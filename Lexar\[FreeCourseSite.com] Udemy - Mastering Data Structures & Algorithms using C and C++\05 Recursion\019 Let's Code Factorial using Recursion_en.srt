1
00:00:00,570 --> 00:00:04,330
In this video, we will see a function for finding factual of a number.

2
00:00:05,400 --> 00:00:08,430
I will show you recursive function as well as iterative function.

3
00:00:08,470 --> 00:00:11,730
So first, let's try to catch a function for finding factorial.

4
00:00:12,270 --> 00:00:18,300
Function takes parameter and if and is equal to zero, then return one.

5
00:00:24,860 --> 00:00:30,050
Else written factorial of and minus one.

6
00:00:32,830 --> 00:00:33,580
But an.

7
00:00:40,920 --> 00:00:47,280
Inside main function, I will take a variable R to get the result of that factorial function, let us

8
00:00:47,280 --> 00:00:49,600
find the factorial of number five.

9
00:00:49,620 --> 00:00:50,940
The answer is 120.

10
00:00:50,970 --> 00:00:51,980
We already know the answer.

11
00:00:51,990 --> 00:00:53,270
We should get the same result.

12
00:00:57,440 --> 00:01:03,470
Display art, I will run the program, we should get out, put 120.

13
00:01:07,000 --> 00:01:11,380
Yes, the output is 120 is a factor of five.

14
00:01:14,720 --> 00:01:17,520
Let us check factory love for it should be 24.

15
00:01:20,150 --> 00:01:21,890
Yes, it is correct.

16
00:01:24,410 --> 00:01:26,840
Let us check factory love zero.

17
00:01:27,170 --> 00:01:28,310
What the result will be.

18
00:01:30,690 --> 00:01:31,410
It is one.

19
00:01:34,830 --> 00:01:38,020
Let us use a negative number here and see what the function will do.

20
00:01:38,850 --> 00:01:40,440
Factory love minus one.

21
00:01:44,400 --> 00:01:52,410
See then the value of an S minus one, then it's not zero, so it will be calling itself again and again,

22
00:01:52,890 --> 00:01:56,970
minus one, minus one, minus two and minus three, minus four goes on.

23
00:01:58,800 --> 00:02:02,360
So actually it has gone into infinite calling and terminated.

24
00:02:02,820 --> 00:02:05,340
So you can see total how many calls it has done.

25
00:02:09,979 --> 00:02:16,740
Two six two zero five four calls it has done, then why did it stop because of stack overflow?

26
00:02:17,870 --> 00:02:23,300
So if a recursive function is going into infinite recursive calls, at one point it will terminate because

27
00:02:23,300 --> 00:02:28,310
of stack overflow system stack has overflow and so it has made so many calls.

28
00:02:29,630 --> 00:02:31,710
Factorial is not defined for negative number.

29
00:02:31,730 --> 00:02:38,000
So if you want to avoid this recursive call, so then here you can write a condition to stop the function

30
00:02:38,000 --> 00:02:39,140
if the number is negative.

31
00:02:42,070 --> 00:02:49,150
Now, I will write a detective version of factorial, I will call it as I fact.

32
00:02:51,670 --> 00:02:59,260
It takes a barometer and for finding factorial, I will take if that is initialized with one and also

33
00:02:59,260 --> 00:03:11,080
we need a variable ifour iteration using for loop, I assign one on words and I less than equal to n

34
00:03:11,890 --> 00:03:17,560
i plus plus then factor it is multiplied by I.

35
00:03:20,750 --> 00:03:26,020
So I will be taking different values from one to end and it will be getting multiplied by every time.

36
00:03:26,030 --> 00:03:33,800
So this will be multiplying just like one to two and three and so on 2002 and then finally return F

37
00:03:33,940 --> 00:03:35,260
F is having that result.

38
00:03:36,800 --> 00:03:42,320
And here inside main function, I will call this a factorial function and I will pass the parameter

39
00:03:42,320 --> 00:03:47,560
as five letters in the program and see what the output will be.

40
00:03:47,570 --> 00:03:49,490
It should give output 120.

41
00:03:53,070 --> 00:04:01,980
Yes, output is 120, let us debug the program quickly and see how the values of F and I are changing

42
00:04:01,980 --> 00:04:03,330
in this iterative version.

43
00:04:06,460 --> 00:04:16,089
So you can observe this watch here was this portion where the values of F I will be changing, I will

44
00:04:16,089 --> 00:04:18,800
continue execution, it will enter into factorial function.

45
00:04:19,209 --> 00:04:22,250
Now, the variables I and F board are there.

46
00:04:23,540 --> 00:04:24,440
I use one.

47
00:04:24,760 --> 00:04:27,160
So one multiplied by F F is also one.

48
00:04:27,550 --> 00:04:33,700
Then I becomes two multiplied by five becomes to the night becomes three multiplied by f f becomes six.

49
00:04:34,130 --> 00:04:35,440
Then I use the four.

50
00:04:35,440 --> 00:04:38,170
Four is multiplied twenty four I use five.

51
00:04:38,170 --> 00:04:39,580
Five is multiplied 120.

52
00:04:39,910 --> 00:04:40,920
I become six.

53
00:04:40,930 --> 00:04:41,770
Then it will stop

54
00:04:45,340 --> 00:04:47,830
and come back to main function and print the result.

55
00:04:48,440 --> 00:04:49,900
So out of program.

56
00:04:51,430 --> 00:04:57,040
So that's all in this video we have seen a recursive as well as iterative version of factorial function.

