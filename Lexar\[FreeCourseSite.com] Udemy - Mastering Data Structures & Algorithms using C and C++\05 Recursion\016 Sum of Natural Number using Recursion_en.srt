1
00:00:00,210 --> 00:00:07,150
Now, let us look at an example, recursive function for finding some off first and natural number.

2
00:00:07,170 --> 00:00:10,830
So, first of all, explaining what does it mean by someone of an actual number?

3
00:00:11,160 --> 00:00:16,530
Then I will be wiser because the function then we will write a recursive procedure that is function

4
00:00:16,530 --> 00:00:18,440
using C or C++ language.

5
00:00:19,320 --> 00:00:22,800
Let us understand what is what does it mean by some of the astonishing numbers?

6
00:00:23,130 --> 00:00:31,350
So if I take some national numbers like one, two, three, four, five, six, seven, simple, they

7
00:00:31,350 --> 00:00:35,130
want to take up to seven, then I want the sum of all these.

8
00:00:35,130 --> 00:00:36,210
Sum of all these.

9
00:00:38,030 --> 00:00:40,080
Submission of forest and natural no.

10
00:00:40,100 --> 00:00:41,960
So how many natural no.

11
00:00:42,050 --> 00:00:42,980
Fuster seven.

12
00:00:43,010 --> 00:00:45,440
So for seven months, one, two, seven.

13
00:00:46,130 --> 00:00:53,000
See, if I say forest and the natural numbers, then it this one plus two plus three plus four plus

14
00:00:53,000 --> 00:01:00,740
goes on to last Thomas and and can be anything like in this previous example it was seven.

15
00:01:01,200 --> 00:01:02,260
It can be anything.

16
00:01:02,870 --> 00:01:07,210
Now for this submission let us write a recursive definition.

17
00:01:07,220 --> 00:01:16,370
So for recursive definition I will say some of the tomes as one plus two plus three plus four plus goes

18
00:01:16,370 --> 00:01:22,340
on to now before end the time there will be any minus one then.

19
00:01:22,340 --> 00:01:25,100
And so having to read one more second last.

20
00:01:25,350 --> 00:01:27,020
I'm shooting it now.

21
00:01:27,140 --> 00:01:31,160
If I take the submission down here this is nothing but.

22
00:01:33,030 --> 00:01:42,720
Some of tendencies, some of this is Emma minus one dance and dance first, one, two, and then and

23
00:01:42,780 --> 00:01:49,540
minus 10 will be from one book at minus one, then one, two and minus one will be from here to here.

24
00:01:49,680 --> 00:01:51,000
That is till this point.

25
00:01:51,060 --> 00:01:51,810
Till this point.

26
00:01:52,170 --> 00:01:54,270
Then what more is remaining plus.

27
00:01:54,510 --> 00:01:58,500
And then if I am good at it, it gives me up too.

28
00:01:58,530 --> 00:02:01,650
And so this is a recursive definition.

29
00:02:02,070 --> 00:02:04,050
Now this I can write it like this.

30
00:02:04,620 --> 00:02:12,930
Some off and doms can be defined as some off the minus one plus.

31
00:02:13,080 --> 00:02:19,200
And if and as the greater than zero if any is equal to zero then it's not a natural.

32
00:02:19,200 --> 00:02:19,940
No national.

33
00:02:20,000 --> 00:02:22,440
What starts from one on word so and is zero.

34
00:02:22,470 --> 00:02:23,730
So let us say answer this.

35
00:02:23,730 --> 00:02:31,020
You see, remember whenever you define any recurrence relation or recursive function, then you must

36
00:02:31,020 --> 00:02:33,840
have two types of statements.

37
00:02:33,870 --> 00:02:36,050
One is for the large value often.

38
00:02:36,390 --> 00:02:40,680
What is the formula then for the small value of and what is the answer.

39
00:02:40,710 --> 00:02:43,420
So usually for small value we give a direct answer.

40
00:02:43,440 --> 00:02:47,220
So if anything goes to zero, there is no formula that is there.

41
00:02:47,910 --> 00:02:53,580
So based on the same thing, I can write a recursive function in C or C++.

42
00:02:54,000 --> 00:03:08,310
Let us see in sum, which takes integer parameter N and if and is equal to zero rigdon zero otherwise

43
00:03:08,550 --> 00:03:09,360
ripton.

44
00:03:11,370 --> 00:03:21,060
Some off and minus one plus and so here what I want to explain to you here is if you have a recursive

45
00:03:21,060 --> 00:03:27,900
definition of any problem, then that you can convert into a recursive function in any programming language

46
00:03:27,900 --> 00:03:29,040
which supports recursion.

47
00:03:29,070 --> 00:03:35,190
So in C C++, you have recursion so you can directly convert this fancy some off and some of.

48
00:03:35,960 --> 00:03:40,830
And if it is equal to zero, return zero, if anything close to zero, return zero.

49
00:03:41,040 --> 00:03:45,490
Otherwise if friend is better than zero than rate on some off and minus one percent.

50
00:03:45,510 --> 00:03:47,290
So some off and minus one percent.

51
00:03:48,690 --> 00:03:49,500
So that's it.

52
00:03:49,800 --> 00:03:53,550
That is a recursive function for finding some of those definition.

53
00:03:53,550 --> 00:03:53,970
No.

54
00:03:54,850 --> 00:03:59,620
Now, finally, I have one more thing to show you that for finding the sum of a national number, there

55
00:03:59,620 --> 00:04:03,820
is a formula that and in the end plus one by two.

56
00:04:04,670 --> 00:04:05,940
So this is the formula.

57
00:04:05,960 --> 00:04:11,960
So it means I don't have to write a function for that one function will consume time and it's a recursive

58
00:04:11,960 --> 00:04:17,779
function and it will take time for making number of calls and stuff that I can simply use a formula

59
00:04:17,779 --> 00:04:19,170
and write a program.

60
00:04:19,579 --> 00:04:25,550
So usually we write just a formula whenever we have to find out some of a national number instead of

61
00:04:25,550 --> 00:04:26,390
writing a function.

62
00:04:26,690 --> 00:04:34,040
But for experiencing or learning recursive functions, I have taken this example even the same function.

63
00:04:34,040 --> 00:04:41,380
We can write it using iteration that is using loop recursion will be costly because it uses stack internally.

64
00:04:41,690 --> 00:04:45,290
So I will write on two more versions of the same function.

65
00:04:45,290 --> 00:04:50,510
Instead of recursion, I will use loop and also I will directly use the formula and show you.

66
00:04:51,770 --> 00:04:54,050
So I will remove this definition, then I will.

67
00:04:54,080 --> 00:04:59,210
I don't put more functions here, I have a function for some which is directly using the formula if

68
00:04:59,210 --> 00:05:04,820
you pass the value and then it will take the formula using the formula revolvers and in to end this

69
00:05:04,850 --> 00:05:05,880
one by 200.

70
00:05:06,560 --> 00:05:12,200
So the simplest function, and it will take very less time or just just constant time because it has

71
00:05:12,200 --> 00:05:14,450
to evaluate the simple expression.

72
00:05:14,450 --> 00:05:18,110
So I can say that the time taken by this function will be constant.

73
00:05:19,120 --> 00:05:24,940
Few number of operations like one operation to operation and three operations, so total performing,

74
00:05:24,940 --> 00:05:26,360
three operation, that is constant.

75
00:05:26,500 --> 00:05:28,050
So this is the fastest method.

76
00:05:28,540 --> 00:05:29,810
This is using formula.

77
00:05:30,280 --> 00:05:35,830
Now, here I have a function for finding the sum of just a natural number using loop.

78
00:05:37,000 --> 00:05:42,040
Now, this function, it takes the value of an and it will start from one to end.

79
00:05:42,310 --> 00:05:46,930
So I will start the values from one, two and eventually zero.

80
00:05:46,960 --> 00:05:51,760
So this I value from one to end will be keepon added to that as.

81
00:05:51,760 --> 00:05:58,750
So first one is that the next time I becomes to and booza then 300 then for up to when all numbers are

82
00:05:58,780 --> 00:06:00,310
added and this is done.

83
00:06:01,240 --> 00:06:02,920
So this is using a loop.

84
00:06:02,920 --> 00:06:06,680
I have it on the function if I find the time complexity of dysfunction.

85
00:06:06,700 --> 00:06:12,820
So one of the major important statement is this one, and this statement is repeating four and times

86
00:06:13,390 --> 00:06:15,550
as I is taking the values from one point.

87
00:06:15,570 --> 00:06:17,330
So this will be repeated for enflames.

88
00:06:17,710 --> 00:06:20,590
This is statement one time only and this is statement one time.

89
00:06:20,770 --> 00:06:22,930
And if I can see the for loop mostly deserted.

90
00:06:23,260 --> 00:06:24,140
And this one time.

91
00:06:24,580 --> 00:06:26,400
So the main statement is this one.

92
00:06:26,410 --> 00:06:28,150
So this is executing for End-Time.

93
00:06:28,150 --> 00:06:34,330
So I should say that the time taken by this algorithms or any time taken this out of.

94
00:06:34,330 --> 00:06:39,070
And I have found all the frequency of execution of each statement.

95
00:06:39,340 --> 00:06:45,070
And does the mean statement is this one with the addition of Sadun and that's going to execute for End-Time.

96
00:06:45,340 --> 00:06:49,420
So our focus is on that statement of Antine.

97
00:06:50,590 --> 00:06:58,660
And how much memory it takes, just three variables, that is, I guess, and that is part of barometer,

98
00:06:58,990 --> 00:07:00,310
only three variables Hodor.

99
00:07:01,860 --> 00:07:08,340
But what about recursive function, recursive function internally, use stack, whatever the value of

100
00:07:08,640 --> 00:07:14,540
the pass to that, it will make those many calls and it will create those many activation records.

101
00:07:14,850 --> 00:07:21,450
So it's having just one variable NP so that any variable will be created many times, as many times,

102
00:07:21,600 --> 00:07:28,370
depending on the value that we are passing, takes more space, but the time will be outdraw and only.

103
00:07:28,560 --> 00:07:32,250
So let me trace this function and show you for the value five.

104
00:07:32,760 --> 00:07:41,760
If I call this function some form, I do five, then what it will do if it was five, five is not zero

105
00:07:41,970 --> 00:07:49,500
then it will call some of and minus one plus and so it will call itself for some of the four and the

106
00:07:49,500 --> 00:07:52,410
value of and is what five that is added.

107
00:07:53,610 --> 00:07:58,050
So it's making one more call that in this call again the same thing, it is not zero.

108
00:07:58,050 --> 00:08:05,190
So it will make a call for some of three and then also add four, then it will make a call for some

109
00:08:05,190 --> 00:08:12,390
of two and also the three, then some off one and also add.

110
00:08:15,160 --> 00:08:20,800
To then sum of zero and also add one.

111
00:08:22,070 --> 00:08:25,310
Now, finally, someone zero as it is zero, return zero.

112
00:08:25,540 --> 00:08:29,100
So this is zero zero zero plus one as one.

113
00:08:29,440 --> 00:08:30,920
And this is written here.

114
00:08:31,360 --> 00:08:32,919
So this is one plus two.

115
00:08:32,950 --> 00:08:33,760
That is three.

116
00:08:34,030 --> 00:08:35,419
So three written here.

117
00:08:35,679 --> 00:08:36,970
So this is three plus three.

118
00:08:36,970 --> 00:08:37,780
That is six.

119
00:08:37,780 --> 00:08:39,110
Six is written here.

120
00:08:39,460 --> 00:08:43,309
So six plus four is ten then is written here.

121
00:08:43,630 --> 00:08:46,390
So 10 plus five is 15.

122
00:08:46,720 --> 00:08:48,100
15 is written here.

123
00:08:48,310 --> 00:08:49,550
So there is in the 15.

124
00:08:50,080 --> 00:08:54,930
So while returning, the addition is done and you can see that how many calls it has made for five.

125
00:08:54,940 --> 00:08:56,420
It has made total six calls.

126
00:08:56,420 --> 00:09:02,440
So the size of the stack is six and the variable is created for six time.

127
00:09:02,860 --> 00:09:03,900
So memory is more.

128
00:09:04,720 --> 00:09:06,280
But how much time it is taking?

129
00:09:06,580 --> 00:09:09,060
There are total six calls for value of five.

130
00:09:09,070 --> 00:09:12,460
There are six calls for value and there will be endless phone calls.

131
00:09:12,470 --> 00:09:13,710
So it is four of them.

132
00:09:14,080 --> 00:09:17,010
So the time taken by this function is out of hand.

133
00:09:17,020 --> 00:09:20,450
But space is also six time for the value of five.

134
00:09:20,770 --> 00:09:23,080
So the space is also and plus one.

135
00:09:23,320 --> 00:09:25,510
So space is also in plus one here.

136
00:09:28,120 --> 00:09:37,420
This is strain, so their regulation is costly, but it was easy for converting mathematical definition

137
00:09:37,420 --> 00:09:39,760
that is recursive definition into a function.

138
00:09:40,360 --> 00:09:45,690
That's how recursions are easy to write compared to loops for loop.

139
00:09:45,700 --> 00:09:51,220
We have to devise from logic, work on the problem, understand the problem, devise some logic, then

140
00:09:51,430 --> 00:09:57,130
it may work faster compared to recursion that takes lesser space compared to recursion.

141
00:09:57,130 --> 00:09:58,780
So mostly in a lot of applications.

142
00:09:58,790 --> 00:10:03,820
In our program we write loops only instead of using recursions.

143
00:10:04,000 --> 00:10:08,950
But actually the problems are solved using recursions in mathematics.

144
00:10:09,520 --> 00:10:15,400
So I have taken a very simple example and I have shown you how we can define the recursive function

145
00:10:15,400 --> 00:10:20,570
for finding some of the numbers in the coming videos will see more examples on.

