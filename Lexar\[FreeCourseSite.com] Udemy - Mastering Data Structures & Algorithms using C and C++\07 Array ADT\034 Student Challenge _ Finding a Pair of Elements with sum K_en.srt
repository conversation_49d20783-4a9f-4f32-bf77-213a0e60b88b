1
00:00:00,970 --> 00:00:07,450
And this video will learn about a problem, a challenge that is if a list of elements are given, then

2
00:00:07,450 --> 00:00:10,250
we have to find out a bit of elements.

3
00:00:10,300 --> 00:00:14,680
That is two elements such that there are some as equal to some given.

4
00:00:14,680 --> 00:00:15,030
No.

5
00:00:15,460 --> 00:00:20,650
For example, we want to eliminate and be such that the total is equal to them.

6
00:00:20,650 --> 00:00:22,630
As per my example, I have taken 10.

7
00:00:23,760 --> 00:00:28,950
So we have to check in this area, are there any two elements who sum is equal to 10?

8
00:00:29,670 --> 00:00:31,020
So let us check it directly.

9
00:00:32,280 --> 00:00:35,790
Six is there, so I need four so that it becomes ten.

10
00:00:36,420 --> 00:00:40,110
Is there any for no then three.

11
00:00:41,610 --> 00:00:48,090
I need seven to get back to the last 10 check for seven years found, so we have a bit of elements that

12
00:00:48,090 --> 00:00:52,490
are three and seven, which is forming to them ten, then eight.

13
00:00:52,890 --> 00:00:54,540
I need two for this one.

14
00:00:54,540 --> 00:00:56,190
So check for two just found.

15
00:00:56,460 --> 00:01:00,900
So we have one more pair of elements who some ten then 10.

16
00:01:01,140 --> 00:01:02,340
So already it's ten.

17
00:01:02,340 --> 00:01:06,110
So I should have zero four zero in this remaining list.

18
00:01:06,120 --> 00:01:07,230
No, there is not foam.

19
00:01:07,710 --> 00:01:09,720
No one thing why I'm not checking the site.

20
00:01:10,080 --> 00:01:13,050
See, despite all that you have compared with the rest of the elements.

21
00:01:14,750 --> 00:01:15,210
Right.

22
00:01:15,260 --> 00:01:16,860
So I have to check forward only.

23
00:01:17,270 --> 00:01:18,530
So now 16.

24
00:01:18,920 --> 00:01:19,490
This is great.

25
00:01:19,760 --> 00:01:24,750
So anyway, if I check, there is no Fairphone, so this is not equal to 10.

26
00:01:24,770 --> 00:01:28,100
There is no peer if there is any number, but it should be negative number.

27
00:01:28,100 --> 00:01:29,180
That is minus six.

28
00:01:29,180 --> 00:01:29,540
Right.

29
00:01:29,900 --> 00:01:30,680
Then seven.

30
00:01:30,920 --> 00:01:32,510
There is nothing here than five.

31
00:01:32,510 --> 00:01:33,860
There is nothing to nothing.

32
00:01:33,910 --> 00:01:34,160
So.

33
00:01:35,090 --> 00:01:40,220
So this is all I have found out that there are two pair of elements who Sommer's equal to 10.

34
00:01:41,600 --> 00:01:46,150
Now, we need an algorithm on this one, so I will show you first method.

35
00:01:46,370 --> 00:01:47,740
Let's look at the first method.

36
00:01:48,590 --> 00:01:50,210
See, I have to scan this.

37
00:01:50,660 --> 00:01:54,940
So for this, I will take one variable, like not for this element six.

38
00:01:54,950 --> 00:01:58,200
I need a pair, so I have to check the rest of the area.

39
00:01:58,430 --> 00:02:04,700
So for checking in the rest of the area, I should use a G so G will help me find out an element such

40
00:02:04,700 --> 00:02:06,870
that whose total is equal to 10.

41
00:02:07,310 --> 00:02:09,580
So this G I have to go on incremented.

42
00:02:10,650 --> 00:02:12,890
So using this G I will check this total.

43
00:02:12,890 --> 00:02:16,100
It's not ten then moved to the next element and check.

44
00:02:16,100 --> 00:02:17,710
Is it equal to ten.

45
00:02:17,720 --> 00:02:19,840
No Max is equal to ten.

46
00:02:19,880 --> 00:02:20,300
No.

47
00:02:20,570 --> 00:02:22,740
So in this way I can go on checking the elements.

48
00:02:22,940 --> 00:02:26,540
So this procedure is same as how we were finding duplicate elements.

49
00:02:26,570 --> 00:02:31,860
Yes, it seems only the task that time we were checking, is it a duplicate element?

50
00:02:32,270 --> 00:02:34,710
Now you have to check whether the sum is equal to ten.

51
00:02:35,440 --> 00:02:36,010
That's it.

52
00:02:36,530 --> 00:02:38,870
It's a simple and one more thing.

53
00:02:38,870 --> 00:02:42,430
I have taken unique elements, so there are no duplicate elements here.

54
00:02:42,440 --> 00:02:46,400
Like if I have three, then one seven, then one more seven.

55
00:02:46,400 --> 00:02:47,870
I have no there are no duplicate.

56
00:02:47,870 --> 00:02:49,100
All elements are unique.

57
00:02:49,100 --> 00:02:53,630
So you don't have to worry about a duplicate elements, if at all there are duplicates, then you should

58
00:02:53,630 --> 00:02:56,000
remove the duplicates and perform this procedure.

59
00:02:57,420 --> 00:03:00,370
I will quickly write on the program, called for doing this one.

60
00:03:00,390 --> 00:03:03,860
So let us see for scanning through this element.

61
00:03:03,870 --> 00:03:05,010
I need a follow up.

62
00:03:05,010 --> 00:03:11,610
So I like to follow before I assign Zettl and I should go up to Lasley because I don't have to check

63
00:03:11,610 --> 00:03:12,180
this element.

64
00:03:12,180 --> 00:03:12,390
Right.

65
00:03:12,390 --> 00:03:13,500
There is nothing beyond this.

66
00:03:13,770 --> 00:03:17,310
So I use less than minus one and I less plus.

67
00:03:18,780 --> 00:03:23,340
Then for every element I should try to find a pairing element such that total is equal to 10.

68
00:03:23,340 --> 00:03:26,690
So for that I need a G starting from the next element onwards.

69
00:03:26,940 --> 00:03:35,250
So for that I will start g assign I plus one on word then just less than the last element I should go

70
00:03:35,460 --> 00:03:41,480
less than and that is g c++ c remember here and value is ten.

71
00:03:41,490 --> 00:03:41,910
Right.

72
00:03:41,940 --> 00:03:45,230
There are two total elements for the last element of the next nine.

73
00:03:45,960 --> 00:03:55,220
Now what I should do every time if if I plus eight of G is equals to given them.

74
00:03:55,220 --> 00:03:56,940
But let us say given them what is key.

75
00:03:57,300 --> 00:04:05,130
If it is so then Brainbow the element c the printer itself I value of G value like suppose I is here

76
00:04:05,130 --> 00:04:07,710
right now, I is here and D here.

77
00:04:07,920 --> 00:04:14,220
So if I value that three you have divided that the seven, three and seven so it will be the three plus

78
00:04:14,220 --> 00:04:16,670
seven equals two K so that is equal to ten.

79
00:04:16,690 --> 00:04:17,140
Yes.

80
00:04:17,310 --> 00:04:23,880
So we got the pair of element who some is equal to K thus it does the end of loop and is there enough

81
00:04:23,890 --> 00:04:24,180
loop.

82
00:04:24,900 --> 00:04:25,650
So that's all.

83
00:04:25,650 --> 00:04:28,800
This is the code for finding a pair of elements.

84
00:04:29,750 --> 00:04:36,440
Now, analysis, how much work we are doing, see amount of work we are doing is similar to finding

85
00:04:36,440 --> 00:04:42,990
duplicates, right, for being here on I on this place, we are checking the rest of all the elements.

86
00:04:42,990 --> 00:04:48,590
So how many elements we are taking let's and minus one elements then keeping eye here we are checking

87
00:04:48,590 --> 00:04:49,480
the rest of the elements.

88
00:04:49,480 --> 00:04:50,630
So this is in minus two.

89
00:04:50,900 --> 00:04:53,820
So is the same way this is going to one.

90
00:04:54,260 --> 00:04:57,110
So this is and then two and minus one by two.

91
00:04:57,380 --> 00:04:59,860
And this is Outdraw and Square.

92
00:05:00,320 --> 00:05:00,520
Yes.

93
00:05:00,710 --> 00:05:02,060
Times Square.

94
00:05:02,720 --> 00:05:10,880
So this is quadratic time taking procedure right and square with we say quadratic quadratic time ticking

95
00:05:10,880 --> 00:05:11,450
procedure.

96
00:05:12,390 --> 00:05:12,710
Right.

97
00:05:14,250 --> 00:05:18,360
Here and square doesn't mean exactly 10 in 10.

98
00:05:18,390 --> 00:05:23,330
That is a hundred, it doesn't mean that as end square, that is quadratic timelike.

99
00:05:24,060 --> 00:05:27,570
So if you look at the code, also here is a follow up.

100
00:05:27,570 --> 00:05:28,830
The follow up is there.

101
00:05:28,830 --> 00:05:29,760
So I can see it is.

102
00:05:29,770 --> 00:05:35,210
And because it is going starting from zero and ending at N minus one, then inside that one will follow.

103
00:05:35,550 --> 00:05:36,960
That is also going to land.

104
00:05:36,960 --> 00:05:40,640
But some very disturbing just beside Ironwood.

105
00:05:40,660 --> 00:05:40,880
Right.

106
00:05:41,280 --> 00:05:44,890
So it's almost and if you say no, no, it's already using.

107
00:05:44,910 --> 00:05:46,400
So this was the analysis.

108
00:05:46,590 --> 00:05:46,980
Yes.

109
00:05:47,070 --> 00:05:48,530
And minus one and minus two.

110
00:05:48,540 --> 00:05:51,810
Therefore it is reducing and the total is again and square.

111
00:05:52,140 --> 00:05:53,780
See the degree of this polynomial.

112
00:05:53,790 --> 00:05:58,960
If we take the degree of the polynomial, this is N squared minus and by two degrees and square.

113
00:05:58,980 --> 00:06:00,480
So we are seeing this is quadratic.

114
00:06:01,500 --> 00:06:05,900
So even if I do detailed analysis for this one, it will be in square only.

115
00:06:05,910 --> 00:06:12,960
So I'm assuming roughly as not one to assume this followed by then or even ignore it, because in the

116
00:06:12,960 --> 00:06:17,990
previous example I said that I wrote while Loop and I said that it's negligible, we can ignore it.

117
00:06:18,120 --> 00:06:23,220
But here I am saying it should be considered so far that you should have the good understanding of the

118
00:06:23,220 --> 00:06:25,220
procedure that is being done here.

119
00:06:25,560 --> 00:06:31,020
If you understand the procedure, then you can decide whether it is considerable or not.

120
00:06:31,170 --> 00:06:32,610
You can judge that one.

121
00:06:32,610 --> 00:06:32,850
Right.

122
00:06:33,090 --> 00:06:34,500
So this comes at practice.

123
00:06:34,530 --> 00:06:35,550
There is no formula.

124
00:06:35,550 --> 00:06:40,470
I cannot say that if this is starting from here to here, C and otherwise to like this.

125
00:06:40,470 --> 00:06:42,690
No, it's all based on the procedure.

126
00:06:42,810 --> 00:06:44,480
So there are a lot of things to learn.

127
00:06:44,490 --> 00:06:44,810
Right.

128
00:06:45,000 --> 00:06:48,980
So meanwhile, you'll understand how to analyze a procedure based on the code.

129
00:06:49,420 --> 00:06:49,670
Right.

130
00:06:49,950 --> 00:06:54,410
So now it is an insight that there is one more and so it is sort of and square.

131
00:06:54,840 --> 00:06:59,680
So by reading the code also, we have found that it's taking outdraw and square time.

132
00:07:00,360 --> 00:07:02,410
So this is a time consuming method.

133
00:07:02,790 --> 00:07:05,600
I want a faster method for faster method.

134
00:07:05,610 --> 00:07:12,570
Again, the solution is hashing hash table, so I'll show you how it is possible so quickly.

135
00:07:12,570 --> 00:07:13,680
I will show you that procedure.

136
00:07:14,400 --> 00:07:20,100
Now let us look at the second method for finding a bit of elements whose total is equal to give a number

137
00:07:20,100 --> 00:07:20,760
that is ten.

138
00:07:22,340 --> 00:07:28,630
This method uses hashing so far, I have created a table which is already initialized with the Zeitels,

139
00:07:29,120 --> 00:07:33,080
the side of the table is 16 because the largest number present here is 16.

140
00:07:33,890 --> 00:07:38,150
So you already have this discussed that when you are using harshing, then you need a lot of memory

141
00:07:38,510 --> 00:07:41,700
and it depends on the maximum element that you are dealing with.

142
00:07:43,220 --> 00:07:44,850
Now, what should be the procedure?

143
00:07:45,110 --> 00:07:51,710
So simply, we will scan for this list of elements and we will use this hash table, increment the value

144
00:07:51,710 --> 00:07:52,690
or market the value.

145
00:07:53,060 --> 00:07:53,810
Let us do it.

146
00:07:54,020 --> 00:07:55,160
And while marking.

147
00:07:55,160 --> 00:07:57,990
While scanning what all we have to do, let us check it.

148
00:07:58,580 --> 00:07:59,710
So let to this.

149
00:08:01,010 --> 00:08:01,940
This is six.

150
00:08:02,060 --> 00:08:03,950
Right, Mark six.

151
00:08:04,140 --> 00:08:05,300
OK, this is Mark.

152
00:08:05,720 --> 00:08:09,890
And for six, which number is required to make a double standard.

153
00:08:10,400 --> 00:08:11,950
So 10 minus six is four.

154
00:08:12,350 --> 00:08:15,530
Look for four for is there.

155
00:08:15,800 --> 00:08:16,520
It's not there.

156
00:08:17,240 --> 00:08:18,740
So there's no matching for six.

157
00:08:19,410 --> 00:08:22,110
Then let us take eight on the first element.

158
00:08:22,550 --> 00:08:23,630
Let us take a year.

159
00:08:23,750 --> 00:08:27,650
This number is six now for making up the number ten.

160
00:08:27,660 --> 00:08:28,610
We need four.

161
00:08:28,820 --> 00:08:31,370
So check on index four as it mark No.

162
00:08:31,520 --> 00:08:32,919
Four is not there in the list.

163
00:08:32,929 --> 00:08:33,799
It's not yet found.

164
00:08:33,809 --> 00:08:34,669
Maybe it's there.

165
00:08:34,669 --> 00:08:36,880
We don't know, but it's not yet Fonthill here.

166
00:08:37,100 --> 00:08:40,640
So six, it cannot be a page of element.

167
00:08:40,640 --> 00:08:40,830
Right.

168
00:08:41,120 --> 00:08:41,900
So what six.

169
00:08:41,900 --> 00:08:42,620
Is there some.

170
00:08:42,860 --> 00:08:45,650
It does one then move to the next element.

171
00:08:45,830 --> 00:08:47,300
Three normalises element.

172
00:08:47,360 --> 00:08:49,100
We need for making it as a ten.

173
00:08:49,100 --> 00:08:50,950
So we need seven go to the next seven.

174
00:08:50,950 --> 00:08:55,700
No seven is not there on the list but we found the three so mark three as one.

175
00:08:55,700 --> 00:09:00,410
So I'm thinking I'm just incrementing the one right then.

176
00:09:00,560 --> 00:09:03,710
Nix's eight four four eight which number I need.

177
00:09:03,920 --> 00:09:04,670
That is two.

178
00:09:04,850 --> 00:09:06,080
So go to index two.

179
00:09:06,080 --> 00:09:07,790
So two is not that right.

180
00:09:08,120 --> 00:09:10,300
So two is not there is not found.

181
00:09:10,310 --> 00:09:13,270
So there is no matching pair and we got this eight.

182
00:09:13,280 --> 00:09:17,300
So increment this market as one then ten.

183
00:09:17,480 --> 00:09:19,010
So for ten I need zero.

184
00:09:19,160 --> 00:09:20,240
Zero is not there.

185
00:09:20,450 --> 00:09:22,240
But have you got ten for my goodness.

186
00:09:22,240 --> 00:09:25,700
And one then sixteen for sixteen.

187
00:09:25,700 --> 00:09:26,690
There should be minus six.

188
00:09:26,990 --> 00:09:28,370
There is no such index.

189
00:09:30,680 --> 00:09:37,730
The next seven, four, seven, I need a three year freeze on the demand, so it means the three and

190
00:09:37,730 --> 00:09:40,830
seven make a pair whose total is some 10.

191
00:09:41,240 --> 00:09:42,500
So, yes, we got the result.

192
00:09:42,500 --> 00:09:47,920
We will display this vessel and also will mark this as one, because this is already there in the phone.

193
00:09:48,810 --> 00:09:51,320
Then go to five five as long as one.

194
00:09:52,820 --> 00:09:54,700
Then what do we have to check it?

195
00:09:54,770 --> 00:10:00,340
Yes, it is there, too, and it makes a bear and also Mark it has one then for nine, check for one.

196
00:10:00,350 --> 00:10:01,290
One is not there.

197
00:10:01,310 --> 00:10:07,250
So anyway, the phone for me does one, then for 14, check for minus four, minus four is out of index.

198
00:10:07,550 --> 00:10:09,670
So anyway, this is so market as one.

199
00:10:10,160 --> 00:10:16,100
So this how we can go on marking their limits and finding out whether there is an element who is equal

200
00:10:16,100 --> 00:10:16,320
to.

201
00:10:17,420 --> 00:10:18,970
So that's all this is a procedure.

202
00:10:18,990 --> 00:10:20,440
Now what about analysis?

203
00:10:20,810 --> 00:10:22,100
I will not do analysis.

204
00:10:22,100 --> 00:10:25,250
You know, the result already because already we have used hash table.

205
00:10:25,250 --> 00:10:27,460
Right in the previous examples also.

206
00:10:27,650 --> 00:10:29,690
So the time taken is order of hand.

207
00:10:29,870 --> 00:10:30,280
Right.

208
00:10:30,290 --> 00:10:31,000
And four percent.

209
00:10:31,640 --> 00:10:38,210
Now, let me write on the code for this five dollar program code, which will use this hash table and

210
00:10:38,210 --> 00:10:39,710
find out a pair of elements.

211
00:10:39,710 --> 00:10:42,620
So here I will write on so what I have to do.

212
00:10:42,620 --> 00:10:44,750
Basically, I don't have any work here.

213
00:10:44,750 --> 00:10:53,450
I have to scan for this adeus for I find zero and I if less than that is last element N and a plus plus

214
00:10:54,380 --> 00:10:59,660
then what I have to do every time now being here I should check here whether food is marked or not.

215
00:11:00,160 --> 00:11:03,230
So check whether in the hash table at four.

216
00:11:03,260 --> 00:11:05,890
So right now this is six so I have to check for four.

217
00:11:05,900 --> 00:11:14,270
So that is nothing but ten then minus the six I have to do so then the minus six means ten is a K and

218
00:11:14,270 --> 00:11:15,230
minus six means.

219
00:11:15,230 --> 00:11:18,020
If I write it off I.

220
00:11:20,110 --> 00:11:24,350
See, 10 minus, for example, four to six gifts, what for?

221
00:11:24,610 --> 00:11:31,090
So this will be each offer for each of four we are looking at at your forward if at your food is not

222
00:11:31,090 --> 00:11:34,620
equal to zero, it's already found it for the reform.

223
00:11:34,840 --> 00:11:36,250
That means there is a bear.

224
00:11:36,460 --> 00:11:37,420
So what is the bear?

225
00:11:37,420 --> 00:11:38,980
So I can bring these elements.

226
00:11:39,880 --> 00:11:41,260
So I'm bringing these elements.

227
00:11:41,260 --> 00:11:43,050
The elements are that is elfy.

228
00:11:43,090 --> 00:11:44,140
That is the first element.

229
00:11:44,440 --> 00:11:46,920
Then next is the difference between them.

230
00:11:46,930 --> 00:11:47,140
Right.

231
00:11:47,170 --> 00:11:48,160
We have checked that for.

232
00:11:48,160 --> 00:11:48,960
So that is different.

233
00:11:48,970 --> 00:11:53,760
So K minus E of I, that is the difference and also key.

234
00:11:54,100 --> 00:11:59,050
So the output is first element plus second element is equal to that is ten for example.

235
00:11:59,050 --> 00:11:59,530
It is ten.

236
00:11:59,860 --> 00:12:03,040
So this will be printed if this is not equal.

237
00:12:03,040 --> 00:12:03,450
Right.

238
00:12:03,460 --> 00:12:04,300
Equal to zero.

239
00:12:04,660 --> 00:12:09,220
Anyway, after checking this, if it is found then we will use this then afterwards.

240
00:12:09,520 --> 00:12:13,870
Definitely we have to make this element that is six as one.

241
00:12:13,870 --> 00:12:16,570
I will increment it so that as each of successive one.

242
00:12:16,900 --> 00:12:18,070
So each of six.

243
00:12:18,070 --> 00:12:18,730
What is six.

244
00:12:18,730 --> 00:12:22,860
If I, if I as one.

245
00:12:23,830 --> 00:12:26,880
So for one I will simply C++.

246
00:12:26,890 --> 00:12:27,950
So this will increment.

247
00:12:28,540 --> 00:12:29,260
So that's all.

248
00:12:30,310 --> 00:12:34,790
So this is the procedure for finding a pair of elements with the help of hash table.

249
00:12:36,160 --> 00:12:40,970
Right, so this algorithm takes out of or oftentime.

250
00:12:41,260 --> 00:12:43,460
This is getting to these elements only once.

251
00:12:43,480 --> 00:12:48,580
Yes, it's faster, but the space taken is more so always remember this.

252
00:12:48,580 --> 00:12:49,450
There is a tradeoff.

253
00:12:49,780 --> 00:12:52,900
If you want to save the space, then time will be more.

254
00:12:52,900 --> 00:12:55,350
If you want to save time than space will be more.

255
00:12:55,480 --> 00:12:56,590
That's all in this video.

256
00:12:56,590 --> 00:13:02,700
In the next video, I'll show you how to find a pair of elements in a thought that if the arrays sorted.

