1
00:00:00,570 --> 00:00:08,130
In this video, I'm going to show you how black trees are related to two, three, four trees bought

2
00:00:08,130 --> 00:00:08,730
me for trees.

3
00:00:08,730 --> 00:00:14,250
Already we have studied, we have learned how to create a tree by inserting the keys and also how to

4
00:00:14,400 --> 00:00:14,750
it.

5
00:00:14,770 --> 00:00:15,750
So two, three, tree.

6
00:00:15,900 --> 00:00:21,840
If you have not seen, I suggest you go back and check the video on two, three, four trees.

7
00:00:21,840 --> 00:00:23,350
Two, three, four, please.

8
00:00:23,910 --> 00:00:27,110
So let us see how they are related to red black a click.

9
00:00:27,420 --> 00:00:29,150
So here I have some examples.

10
00:00:29,640 --> 00:00:32,520
See, this is two, three, four, three, two, three, four months.

11
00:00:32,910 --> 00:00:39,770
Or every node in their tree can have one key, two children, two children.

12
00:00:39,780 --> 00:00:45,680
That means two are are not can have two kids and three children.

13
00:00:45,840 --> 00:00:52,020
So that is tree or a node can have three kids and four children.

14
00:00:52,230 --> 00:00:53,150
So that is four.

15
00:00:53,790 --> 00:00:58,260
So that is the degree of each node can be either two or three or four.

16
00:00:58,560 --> 00:01:01,650
So the number of kids can be either one or two or three.

17
00:01:02,490 --> 00:01:05,730
So I have examples of these two, three, four trees.

18
00:01:06,570 --> 00:01:12,240
Now here I have similar red to black, a tree for two or three for tree.

19
00:01:12,390 --> 00:01:13,710
So let us compare them.

20
00:01:14,250 --> 00:01:15,450
So this is a red black tree.

21
00:01:15,450 --> 00:01:16,950
There are no red nodes in this one.

22
00:01:17,700 --> 00:01:18,750
How it is related.

23
00:01:19,020 --> 00:01:22,680
See, there is a node and it is having just one key.

24
00:01:23,430 --> 00:01:26,250
And next note that is on the left side is having ten.

25
00:01:26,670 --> 00:01:29,070
Then one more node is having thirty.

26
00:01:29,070 --> 00:01:30,630
That is the right side of Pindi.

27
00:01:30,960 --> 00:01:32,220
So there are three nodes.

28
00:01:32,220 --> 00:01:35,490
So these are three Norns and this is the sustenance of thirty.

29
00:01:36,150 --> 00:01:38,310
Now next this a tree.

30
00:01:38,310 --> 00:01:42,480
If you see first node is having two kids, 20 and 40.

31
00:01:42,720 --> 00:01:44,910
So single node is having two keys.

32
00:01:45,180 --> 00:01:49,770
So if you look here, single node is having just one key as it is binary.

33
00:01:49,830 --> 00:01:50,080
Right.

34
00:01:50,100 --> 00:01:50,970
So just one key.

35
00:01:51,240 --> 00:01:53,640
And this 40 is a Shawna's red.

36
00:01:53,970 --> 00:02:00,090
That means 40 is inside the node vitamin D just like this.

37
00:02:00,090 --> 00:02:07,350
Just like this means it's a part of being the doctor node twenty four node is nothing.

38
00:02:07,350 --> 00:02:13,350
But as a part of this node, a key part of this not done on the right side we find final ten for ten

39
00:02:13,350 --> 00:02:16,330
is a new not separate node, something more separate.

40
00:02:16,380 --> 00:02:24,140
And also Blacula supplements for Blackbelt 15 not for example, 30, 40, 60.

41
00:02:24,370 --> 00:02:26,280
So single node is having three keys.

42
00:02:26,700 --> 00:02:33,090
Middle key is Blackner and for this Blackmond containing twenty and sixty also.

43
00:02:33,270 --> 00:02:34,780
So they are right now.

44
00:02:34,980 --> 00:02:38,460
So they are not in separate node in their two, three, four, three.

45
00:02:38,460 --> 00:02:39,870
They are inside the same node.

46
00:02:39,870 --> 00:02:42,210
So that's why they are having red color.

47
00:02:42,390 --> 00:02:48,780
So if you combine this twenty and sixty together in forty, it becomes the same node, it becomes the

48
00:02:48,780 --> 00:02:49,410
same node.

49
00:02:49,770 --> 00:02:53,730
So it means that the regular nodes are related to experience.

50
00:02:53,730 --> 00:02:54,960
The black node.

51
00:02:55,200 --> 00:02:55,710
Yes.

52
00:02:56,130 --> 00:03:00,270
Then there are four n a separate laws for all are black.

53
00:03:00,270 --> 00:03:01,170
So these are black.

54
00:03:02,240 --> 00:03:03,900
Then I have one more example here.

55
00:03:04,380 --> 00:03:11,880
See this is one node, Blacula, Blacula, one node black color black and fifteen is also the inside

56
00:03:11,880 --> 00:03:12,480
the same.

57
00:03:12,480 --> 00:03:17,220
So that is right Collamore on the right side of ten then one more note.

58
00:03:17,220 --> 00:03:18,750
Thirty forty black.

59
00:03:19,330 --> 00:03:23,370
Today's black color forty should be red because its not having its own node.

60
00:03:23,370 --> 00:03:24,060
Separate node.

61
00:03:24,060 --> 00:03:26,740
It's along with thirty so that's why it is the right color.

62
00:03:27,480 --> 00:03:30,060
Now finally we can take it as a general formula.

63
00:03:30,060 --> 00:03:34,500
Like just look at this one, see if there is only one key and two children.

64
00:03:34,500 --> 00:03:38,130
So this is one key key black color and two children.

65
00:03:38,130 --> 00:03:41,400
Black color if there are two keys and three children.

66
00:03:41,580 --> 00:03:48,090
So out of two keys, one keys black and this Keys red and these three children are black.

67
00:03:48,660 --> 00:03:51,090
So if a new node is there, then it's a black.

68
00:03:51,570 --> 00:03:57,000
If a key is within an existing node, then it is rather than three keys are there.

69
00:03:57,000 --> 00:03:58,530
So this middle one is black.

70
00:03:58,950 --> 00:03:59,880
Left side is red.

71
00:03:59,880 --> 00:04:00,120
Right.

72
00:04:00,120 --> 00:04:04,320
So this is because all three together, these three together are in a single node.

73
00:04:04,590 --> 00:04:09,510
There are there are four new North children, also all the black.

74
00:04:10,500 --> 00:04:17,250
So this is all you can take it as a gentle form as compared to two, three, four trees that are the

75
00:04:17,279 --> 00:04:18,029
black trees.

76
00:04:18,300 --> 00:04:22,060
So you should draw this once, right?

77
00:04:22,290 --> 00:04:23,700
Just watching will not help you.

78
00:04:24,090 --> 00:04:25,520
You have to draw this once.

79
00:04:25,710 --> 00:04:29,010
So copy this and go by yourself once again.

80
00:04:29,010 --> 00:04:31,410
Have a look at it then do it by yourself.

81
00:04:31,860 --> 00:04:32,180
Alright.

82
00:04:32,850 --> 00:04:39,270
Now, next, I'm going to take these keys and generate the BlackBerry simultaneously.

83
00:04:39,270 --> 00:04:46,230
I will show you two, three, four, three is also how we are inserting BlackBerry and insertion is

84
00:04:46,230 --> 00:04:51,570
causing that he coloring or rotation and how they are related to two, three, four threes.

85
00:04:51,720 --> 00:04:56,130
So let us do it for the set of keys which already have shown in the previous example.

86
00:04:56,490 --> 00:04:59,850
So let us relate red BlackBerry with two, three, four, three.

87
00:04:59,920 --> 00:05:01,250
So I'll remove this and it.

