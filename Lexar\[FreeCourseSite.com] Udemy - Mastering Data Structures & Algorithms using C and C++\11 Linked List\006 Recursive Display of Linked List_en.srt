1
00:00:01,620 --> 00:00:09,120
Recursive function for displaying a link that is a traversal as well as a displaying of inkless.

2
00:00:09,510 --> 00:00:16,530
So I remove this functional right of recursive function here function, name, display, it takes a

3
00:00:16,530 --> 00:00:20,200
barometer of type, this node structure, node pointer.

4
00:00:21,090 --> 00:00:27,660
Let us call the point on the MLP and it sticking to the point to somebody passing first as it pointed

5
00:00:27,660 --> 00:00:29,070
to this one then.

6
00:00:31,050 --> 00:00:36,600
As this is the court, so we should try it on the best condition, this condition may be for continuation

7
00:00:36,900 --> 00:00:38,040
or termination.

8
00:00:38,040 --> 00:00:45,180
So we will write the condition for continuation so that P is not a null, then we will print.

9
00:00:46,430 --> 00:00:55,460
If BP's not equal to null, then print BP's data, that is data inside each node, so pretty tough piece

10
00:00:55,460 --> 00:01:00,830
of data after printing data, it should go to the next node recursively.

11
00:01:01,100 --> 00:01:07,330
So display will call itself again, bypassing next nodes pointer.

12
00:01:09,200 --> 00:01:11,350
So display BP's next.

13
00:01:11,780 --> 00:01:14,480
That's all close to the bracket and close this one.

14
00:01:16,220 --> 00:01:20,970
So this is a recursive function for displaying all the elements of a link list one by one.

15
00:01:21,650 --> 00:01:25,190
So it is printing and calling itself and moving on to the next node.

16
00:01:26,120 --> 00:01:30,780
So for this example, Lincolnesque, how it works, let us look at it.

17
00:01:31,250 --> 00:01:36,080
So if I am calling this upon first node pointer, that is first if I pass.

18
00:01:38,270 --> 00:01:46,620
Suppose the call is display first, then let us see how it works.

19
00:01:47,660 --> 00:01:53,570
So I will that function and show you this is important because a lot of things that we can do up on

20
00:01:53,570 --> 00:01:56,400
linguists and we can write down recursive functions also.

21
00:01:57,140 --> 00:01:58,710
So let us see the tracing.

22
00:01:59,450 --> 00:02:04,910
So first time when I call this function by passing first, that is the point of the first node that

23
00:02:04,910 --> 00:02:05,620
is 200.

24
00:02:05,870 --> 00:02:09,139
So it will be called upon first.

25
00:02:09,500 --> 00:02:10,410
That is 200.

26
00:02:10,820 --> 00:02:13,120
I'm not writing the complete function name display.

27
00:02:13,130 --> 00:02:15,520
I'm just writing B, that is a B for display.

28
00:02:16,070 --> 00:02:17,540
Let us see what it does.

29
00:02:17,780 --> 00:02:21,830
C 200 is past 200 is not null.

30
00:02:22,100 --> 00:02:23,180
B's not null.

31
00:02:23,180 --> 00:02:28,730
PS 200 so will print and then it will call itself on next node.

32
00:02:28,970 --> 00:02:32,870
So first thing it does is it will print eight P's here.

33
00:02:32,880 --> 00:02:33,940
Right BS here.

34
00:02:34,190 --> 00:02:37,850
So it will print eight and call itself up on next node.

35
00:02:37,850 --> 00:02:39,330
That is to count this one.

36
00:02:39,590 --> 00:02:41,510
So this will be called upon book.

37
00:02:41,540 --> 00:02:47,020
And so I'm generating a tracing three simultaneously.

38
00:02:47,270 --> 00:02:50,600
I will also show you stack contents.

39
00:02:51,140 --> 00:02:55,490
Let us look at stack contents also because recursion uses stack.

40
00:02:56,330 --> 00:02:58,910
Suppose this is a stack system stack.

41
00:02:59,900 --> 00:03:03,140
First time when I have called the address was to 200.

42
00:03:03,350 --> 00:03:09,800
So activation of the code is created and the pointer B is two hundred and two hundred is not known.

43
00:03:09,800 --> 00:03:14,970
So it has printed eight and it has called again itself for the next address.

44
00:03:14,990 --> 00:03:15,770
That is 210.

45
00:03:17,020 --> 00:03:22,690
So for the next call, again, the activation, the court will be created and the Pointer B will have

46
00:03:22,690 --> 00:03:24,270
the value to ten.

47
00:03:25,420 --> 00:03:30,020
Now in this call, B, the 210 B is not null.

48
00:03:30,280 --> 00:03:31,260
So it will print.

49
00:03:31,540 --> 00:03:33,840
So print the date of present here.

50
00:03:33,850 --> 00:03:34,600
That is three.

51
00:03:35,530 --> 00:03:39,730
And then the next step is call it again for B's next.

52
00:03:39,760 --> 00:03:44,280
So it will call again itself for the next node that is 270.

53
00:03:44,290 --> 00:03:46,420
So it will take this value actually 270.

54
00:03:46,810 --> 00:03:48,650
So it will call itself 270.

55
00:03:48,850 --> 00:03:50,660
And this is that because of this note.

56
00:03:51,130 --> 00:03:53,130
So that our point that is not null.

57
00:03:53,140 --> 00:04:00,850
It is performing these two steps for focus on these two steps now D of 270 is call again.

58
00:04:00,850 --> 00:04:02,680
The new activation of the code is created.

59
00:04:02,980 --> 00:04:11,080
So again, a new activation of the card is a fresh call and the P value is 270, 270.

60
00:04:12,450 --> 00:04:23,310
Enter insight, BP's nocturnal 270 is not it's not zero print, but in the value 270, this note is

61
00:04:23,310 --> 00:04:27,940
having about seven then second step God itself for the next NORDO.

62
00:04:28,140 --> 00:04:29,680
Call itself for the next note.

63
00:04:29,700 --> 00:04:30,690
So what is next here?

64
00:04:31,020 --> 00:04:32,210
Three hundred or so.

65
00:04:32,250 --> 00:04:32,940
Three hundred.

66
00:04:32,940 --> 00:04:34,200
Call upon 300.

67
00:04:34,470 --> 00:04:36,400
This 300 is that address of the next node.

68
00:04:37,260 --> 00:04:38,830
So again, there's a fresh call.

69
00:04:39,000 --> 00:04:40,560
So again, a new activation.

70
00:04:40,560 --> 00:04:43,800
The code is created and the Pointer B will have the address.

71
00:04:44,010 --> 00:04:44,760
Three hundred.

72
00:04:46,820 --> 00:04:57,050
Now, in this call, B is A 300, 300 is not known, then what is the value inside this one 12?

73
00:04:58,870 --> 00:05:03,350
The next call, the next Naude call, the next Nordea.

74
00:05:03,670 --> 00:05:08,650
So what is the address here, 350, 350 years past?

75
00:05:09,670 --> 00:05:14,320
Again, it's a new call for new call again inactivation that God has created.

76
00:05:14,590 --> 00:05:18,460
This time, P-value is 350 350.

77
00:05:20,540 --> 00:05:26,280
Now, fresh called bee bees, not 350, is not the trend.

78
00:05:26,900 --> 00:05:33,760
What is the value in 358 or 350 I remove this value is nine nine.

79
00:05:35,030 --> 00:05:37,310
Then next step is called cell phone.

80
00:05:37,310 --> 00:05:38,530
Next call, it's the phone.

81
00:05:38,530 --> 00:05:39,580
Next that is zero.

82
00:05:40,100 --> 00:05:43,700
So it will call itself the upon zettl.

83
00:05:44,390 --> 00:05:47,920
Now, again, you call that zero is nothing but null.

84
00:05:48,290 --> 00:05:50,660
So I get an activation of the code is created.

85
00:05:50,930 --> 00:05:54,050
And in this activation of the code P value is zero.

86
00:05:54,410 --> 00:06:01,040
So again, a fresh call B B is not equal to no no it is equal to null.

87
00:06:01,370 --> 00:06:02,750
B is equal to null.

88
00:06:03,320 --> 00:06:05,050
It will not enter inside.

89
00:06:05,120 --> 00:06:10,040
So please equal to false false means don't enter inside exit.

90
00:06:10,820 --> 00:06:12,260
So function exits.

91
00:06:12,620 --> 00:06:14,330
This function will terminate.

92
00:06:15,660 --> 00:06:18,110
This call will terminate without doing anything.

93
00:06:19,330 --> 00:06:26,290
Now, as this is recursion, when this call has terminated its activation, the code is deleted, then

94
00:06:26,650 --> 00:06:28,330
it will go back to the previous caller.

95
00:06:28,720 --> 00:06:32,440
Previous caller was for this node nine, this node.

96
00:06:32,650 --> 00:06:36,220
So the value was printed and it went on the next node also.

97
00:06:36,220 --> 00:06:37,890
So the printing was also done.

98
00:06:37,900 --> 00:06:39,250
It went on to the next node.

99
00:06:39,250 --> 00:06:40,270
Also, work is over.

100
00:06:40,630 --> 00:06:42,160
So this function has finished.

101
00:06:42,460 --> 00:06:44,410
So its activation, the code is deleted.

102
00:06:44,680 --> 00:06:47,690
It will go back for this also, but the steps are over.

103
00:06:47,890 --> 00:06:54,000
So again, this work is completed, so its activation code is deleted and likewise all of them are deleted.

104
00:06:54,190 --> 00:06:56,260
So function is returning back.

105
00:06:56,500 --> 00:07:02,020
So the recursive calls are made one by one and then it will go on returning back.

106
00:07:02,380 --> 00:07:04,600
So that is all about display function.

107
00:07:04,990 --> 00:07:06,990
Now, let us analyze this one.

108
00:07:07,840 --> 00:07:12,960
We have already written a function using loopholes and that one and this one.

109
00:07:13,420 --> 00:07:16,850
First of all, how much time it takes time.

110
00:07:17,200 --> 00:07:20,110
What is the work it is doing printing Nottingham's?

111
00:07:21,350 --> 00:07:29,780
Then printing takes how much time, Konstantine for one order, then how many times it is printing depends

112
00:07:29,780 --> 00:07:30,960
on a number of nodes.

113
00:07:31,610 --> 00:07:35,660
So what is the time taken end times, printing times.

114
00:07:35,690 --> 00:07:44,420
And so the timers are off and whether you use loop or recursion, so the loop loop will repeat for any

115
00:07:44,430 --> 00:07:44,710
time.

116
00:07:44,720 --> 00:07:48,190
So it is out of hand, but in recursion the code is here.

117
00:07:48,230 --> 00:07:51,290
So it is printing and calling itself printing and calling it so.

118
00:07:51,320 --> 00:07:55,700
So how much time are the sticking print called print called then call.

119
00:07:55,730 --> 00:08:00,930
So how many times spending is done and calling is done depends on a number of nodes.

120
00:08:01,040 --> 00:08:03,250
So again it is also outdraw.

121
00:08:03,260 --> 00:08:10,040
And so the time taken by this function is and you can remember that if there is any procedure, which

122
00:08:10,040 --> 00:08:17,330
is a traversing the link list once and then time will be Arbroath and only the next space.

123
00:08:18,840 --> 00:08:26,490
Look, function was a simple function, there was no extra space, but this is recursion that uses STAC.

124
00:08:26,670 --> 00:08:28,160
So what is the size of the stack?

125
00:08:28,500 --> 00:08:30,580
One, two, three, four, five, six.

126
00:08:30,840 --> 00:08:31,820
How many of those are there?

127
00:08:31,830 --> 00:08:38,850
One, two, three, four, five, five nodes are there, but six activation because why one for null.

128
00:08:38,860 --> 00:08:46,140
Also for total six activation records are activated so total and plus one activation records are created.

129
00:08:46,320 --> 00:08:51,720
And if you count the calls also one, two, three, four, five, six and plus one calls.

130
00:08:52,080 --> 00:08:52,830
So one more thing.

131
00:08:52,830 --> 00:09:00,060
Remember, if any procedure or a recursive function as written for traversing a link list just once,

132
00:09:00,390 --> 00:09:04,500
then it will making endless phone calls for the entire linked list.

133
00:09:04,650 --> 00:09:07,780
And also the stack size will be endless fun.

134
00:09:08,910 --> 00:09:13,050
So asymptotically answer is outdraw and that is out.

135
00:09:13,230 --> 00:09:17,310
And so the space is also ultrathin and time is also order.

136
00:09:17,490 --> 00:09:20,260
And so that's all about the function.

137
00:09:20,670 --> 00:09:23,430
Now I will make changes in this display function.

138
00:09:24,240 --> 00:09:27,390
It is printing, then calling itself recursively.

139
00:09:27,690 --> 00:09:29,730
I'll just change the order first.

140
00:09:29,730 --> 00:09:33,900
Let's call recursively, then print, then let us see what happens.

141
00:09:35,750 --> 00:09:42,440
Now, this is a different function, Fosset is calling itself recursively, then printing the element

142
00:09:42,440 --> 00:09:43,070
in a..

143
00:09:44,000 --> 00:09:46,790
So this is the first step and the second step.

144
00:09:47,860 --> 00:09:55,530
What happens if this changes mean let us praise this one, and as it is recursive, it will also use

145
00:09:55,540 --> 00:09:58,810
a strike, so maybe I'll get the same stock.

146
00:09:59,380 --> 00:10:00,820
So I have not removed the stack.

147
00:10:01,150 --> 00:10:04,090
Let us trace that one and see how it works.

148
00:10:05,230 --> 00:10:07,110
First time when the function is called.

149
00:10:07,120 --> 00:10:09,320
It is called the address to Honberg.

150
00:10:09,400 --> 00:10:10,480
So this is the first call.

151
00:10:10,870 --> 00:10:12,130
Yes, this is the first call.

152
00:10:13,050 --> 00:10:20,490
Activation records created two hundred apiece, two hundred bees, not nearly as it does not call itself

153
00:10:20,490 --> 00:10:20,880
again.

154
00:10:21,270 --> 00:10:23,160
What about printing afterwards?

155
00:10:23,190 --> 00:10:24,800
First of all, call it self again.

156
00:10:25,110 --> 00:10:30,190
So first, it will make a new call upon next, nor the bees next.

157
00:10:30,210 --> 00:10:33,990
So what is the next nor two can this address see?

158
00:10:33,990 --> 00:10:37,160
It will take the address from this not only to then.

159
00:10:37,650 --> 00:10:39,750
And what about printing afterwards.

160
00:10:39,960 --> 00:10:42,270
So first let us finish this again.

161
00:10:42,270 --> 00:10:46,560
Afresh can be assigned to 10 bees to the bees not.

162
00:10:46,890 --> 00:10:47,330
Yes.

163
00:10:47,850 --> 00:10:50,980
Call again for the next node and printing afterwards.

164
00:10:51,180 --> 00:11:00,000
So again, this will make a call upon next node upon this next node to 70, so to 70 and print afterwards.

165
00:11:00,420 --> 00:11:02,010
So likewise it will continue.

166
00:11:02,160 --> 00:11:08,070
It will call for four seven 271 when it is called and it will call again for three hundred.

167
00:11:08,280 --> 00:11:13,820
So again, a new call for three hundred and printing will be done afterwards.

168
00:11:14,040 --> 00:11:21,570
So for three hundred it will call up one next node that is 350 350 and printing will be done afterwards.

169
00:11:21,790 --> 00:11:26,820
Again it will call upon next node that is null and printing will be done afterwards.

170
00:11:27,180 --> 00:11:30,180
Now this is at the last Soapies zero.

171
00:11:30,510 --> 00:11:36,160
This nodal condition fails because bees null so it will not enter inside and terminates.

172
00:11:36,480 --> 00:11:38,160
So this function terminates.

173
00:11:38,190 --> 00:11:40,030
So its activation, the code is deleted.

174
00:11:40,230 --> 00:11:41,300
So this is over.

175
00:11:42,310 --> 00:11:44,220
And then it will go back to the previous call.

176
00:11:44,220 --> 00:11:45,750
In that previous call.

177
00:11:45,750 --> 00:11:50,330
In that previous call, it has finished the first step that is go to the next node.

178
00:11:50,640 --> 00:11:52,520
But the printing it has to do so.

179
00:11:52,530 --> 00:11:53,700
First step is completed.

180
00:11:53,850 --> 00:11:56,130
Printing is remaining, so it will print.

181
00:11:56,180 --> 00:12:02,770
Now, so what is the value of this 350 350 node is having value nine nine is printed.

182
00:12:03,780 --> 00:12:06,000
So this function is over its activation.

183
00:12:06,000 --> 00:12:06,940
That card is deleted.

184
00:12:07,140 --> 00:12:12,380
It will go back to the previous call for this is the node is twelve, so it will print to its activation

185
00:12:12,390 --> 00:12:13,260
record is deleted.

186
00:12:13,560 --> 00:12:14,890
Go back for this note.

187
00:12:14,910 --> 00:12:16,590
Print seven seven.

188
00:12:16,590 --> 00:12:18,210
Its activation record is deleted.

189
00:12:18,510 --> 00:12:24,210
Go back for this node to then it's value is three 3D printed, its activation records deleted.

190
00:12:24,570 --> 00:12:26,370
Go back for this node eight.

191
00:12:26,550 --> 00:12:33,120
It is printed activation record is deleted so you can see that it is operating while returning.

192
00:12:33,130 --> 00:12:38,820
So what is the output output is nine twelve seven three eight.

193
00:12:38,970 --> 00:12:42,570
That is nine twelve seven three eight.

194
00:12:42,870 --> 00:12:44,240
So these are the values printed.

195
00:12:44,790 --> 00:12:46,920
So these values are in reverse.

196
00:12:46,920 --> 00:12:49,080
Nine twelve seven three eight.

197
00:12:49,590 --> 00:12:57,750
Yes, it is displaying a Linklaters in reverse c the previous function was printing in the same order,

198
00:12:58,020 --> 00:13:03,390
but this is printing in reverse order, although the difference is the print was before the call.

199
00:13:04,050 --> 00:13:06,330
Now the plaintiffs after the call.

200
00:13:06,510 --> 00:13:12,810
So the order has changed because it is printing while retaining recursion will call itself call itself

201
00:13:12,810 --> 00:13:14,810
again and again and also it will return.

202
00:13:15,060 --> 00:13:16,830
So while returning to the spending.

203
00:13:17,100 --> 00:13:21,180
So if you are doing anything at recutting time, then it will be done in three words.

204
00:13:22,050 --> 00:13:24,870
Then what about the time and the space taken by the function?

205
00:13:25,000 --> 00:13:27,810
Let the same amount of time for five notes.

206
00:13:27,810 --> 00:13:32,750
One, two, three, four, five, six calls are there and the size of the stack is also six.

207
00:13:32,760 --> 00:13:35,310
So it is taking the same amount of space and time.

208
00:13:35,670 --> 00:13:38,250
So the time and the spaces are of and only.

209
00:13:38,760 --> 00:13:46,040
So that's all about traversing and displaying linguistic recursively will see more operations on legalist.

