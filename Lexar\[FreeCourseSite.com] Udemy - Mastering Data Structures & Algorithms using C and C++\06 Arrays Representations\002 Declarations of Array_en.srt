1
00:00:00,210 --> 00:00:08,400
First, motorists, if I am declaring an array with the name of size of five, then this will allocate

2
00:00:08,400 --> 00:00:11,010
the memory space for five integers.

3
00:00:13,080 --> 00:00:22,650
Indices are zero to four, and this is just a declaration, so the values inside this one will be garbage

4
00:00:22,650 --> 00:00:29,070
values because nothing is initialized for the values will be garbage and unknown values, random values

5
00:00:29,520 --> 00:00:31,760
that are not initialized by us.

6
00:00:31,770 --> 00:00:33,240
So they are not useful for us.

7
00:00:33,250 --> 00:00:34,980
So we say garbage values.

8
00:00:35,770 --> 00:00:36,450
Next method.

9
00:00:36,660 --> 00:00:41,700
If I want to initialize the values, then I should say this is a part of declaration.

10
00:00:41,880 --> 00:00:47,190
And here I can initialize the values like two, four, six, eight and 10.

11
00:00:47,820 --> 00:00:54,510
Some of them initializing also then are able to be created during runtime and all the values will be

12
00:00:54,510 --> 00:00:55,860
initialized in this one.

13
00:00:57,840 --> 00:01:02,950
So this looks like two, four, six, eight and 10 does the second method.

14
00:01:03,180 --> 00:01:05,640
So this is the declaration plus initialization.

15
00:01:05,650 --> 00:01:10,140
So if you want to have some values initially in Inari, then you can mention all of us.

16
00:01:10,710 --> 00:01:17,640
Now, third method, you can mention the size, but if you don't want to initialize all the values,

17
00:01:17,640 --> 00:01:25,650
just few values you want to initialize, then an array of size of five will be created within this is

18
00:01:26,260 --> 00:01:29,160
zero four as only two values are mentioned.

19
00:01:29,160 --> 00:01:36,180
So only two four will be initialized and the remaining elements will be initialized with the C2 because

20
00:01:36,180 --> 00:01:41,580
once the initialization process starts, it will try to initialize all the elements or remaining values

21
00:01:41,580 --> 00:01:42,410
are not given.

22
00:01:42,420 --> 00:01:44,020
So it will initialize them one zero.

23
00:01:44,970 --> 00:01:50,910
So even I can select this integer of size of five and just zero.

24
00:01:51,210 --> 00:01:53,100
Then this will create an array

25
00:01:59,010 --> 00:02:04,320
and initialize first one with zero as it will continue initializing rest of them also.

26
00:02:04,380 --> 00:02:07,020
So it will initialize all of them with zeros.

27
00:02:08,699 --> 00:02:16,260
And one more method is therefore creating an adding just you can mention the values two, four, six,

28
00:02:16,260 --> 00:02:17,870
eight and 10.

29
00:02:18,480 --> 00:02:25,080
Then depending on the number of elements you have mentioned in the initialization list, the array size

30
00:02:25,080 --> 00:02:26,530
will be the same as that one.

31
00:02:26,550 --> 00:02:33,070
So an array of this file size will be created and it will be initialized with all these elements.

32
00:02:33,690 --> 00:02:39,120
So if I add one more element to this one, legacy 12, then total sizes, one, two, three, four,

33
00:02:39,120 --> 00:02:39,750
five, six.

34
00:02:39,970 --> 00:02:45,480
So orioles' five, the six will be created and this will be initialized with these elements.

35
00:02:49,070 --> 00:02:54,850
So these are the various ways of declaration and initialization of an array.

36
00:02:55,940 --> 00:02:59,770
This is there in both the sea as well as C++.

37
00:03:00,830 --> 00:03:06,740
If you write like this inside the program that during runtime, this is how it will work inside the

38
00:03:06,740 --> 00:03:07,490
main memory.

39
00:03:07,830 --> 00:03:11,810
Now, next, we will see how we can access all the elements of an array.

40
00:03:11,960 --> 00:03:16,660
Here I have an array of sci fi which is already initialized with these elements.

41
00:03:17,060 --> 00:03:23,150
So during runtime the array will be created like this and the elements will be stored in these locations

42
00:03:23,150 --> 00:03:23,960
and their addresses.

43
00:03:23,990 --> 00:03:29,180
Also, I have mentioned that this is will be contiguous, know how to access those elements.

44
00:03:29,210 --> 00:03:37,150
So if I want to bring any value so I can see print if all they want to print a value, that is at index

45
00:03:37,160 --> 00:03:37,650
zero.

46
00:03:37,670 --> 00:03:46,760
So then I can say Person Tildy eight of WSDL, this will print the value at index a zero sum.

47
00:03:46,790 --> 00:03:50,080
What they want to print the value one, then I should make it as one.

48
00:03:50,900 --> 00:03:57,290
So it means I can access all those values by just changing the index of one of two or three anything.

49
00:03:58,250 --> 00:04:04,370
Now if I have to traverse through an array, so traversing means visiting all the elements once.

50
00:04:05,060 --> 00:04:11,240
So if I have to traverse all the elements, then I can take the help of Falu, which will help me change

51
00:04:11,240 --> 00:04:14,190
these indices from zero to four.

52
00:04:14,420 --> 00:04:17,519
So this can be changing from zero to four so far from zero.

53
00:04:17,540 --> 00:04:20,860
This will be the element that makes one, then two, then three and four.

54
00:04:21,079 --> 00:04:25,730
So it will allow me to access all the elements by traversing through those elements.

55
00:04:26,160 --> 00:04:29,390
So for Loop is used for accessing the elements in an array.

56
00:04:29,720 --> 00:04:36,080
So far I assign zero eight is less than five and a plus plus.

57
00:04:36,350 --> 00:04:41,690
And here I can mention I know I can access all those elements.

58
00:04:43,370 --> 00:04:50,630
So for loop is used for traversing through all the elements of energy and the element can be accessed

59
00:04:50,630 --> 00:04:53,580
with the name of an array and index.

60
00:04:54,170 --> 00:04:59,130
Now next, let us see what are the ways of accessing a particular element in an array.

61
00:04:59,720 --> 00:05:01,440
If I want to bring this element.

62
00:05:01,470 --> 00:05:09,410
Then I can see printf person Tildy eight of two.

63
00:05:10,100 --> 00:05:19,700
This is one method then the same thing, but indef I can even see two of a this another method so even

64
00:05:19,700 --> 00:05:27,800
you can use index outside and the name of an array inside subscript then using the pointer Artomatic.

65
00:05:27,800 --> 00:05:30,380
Also we can access that element, that same element.

66
00:05:30,380 --> 00:05:41,630
If I want to access then bring so and so I can say star a plus to the plus two.

67
00:05:41,630 --> 00:05:45,460
Must be inside the bracket before the entire thing.

68
00:05:45,470 --> 00:05:46,660
That is a plus two.

69
00:05:47,030 --> 00:05:54,860
So I can mention any index here and I can start here and I can access that particular location so other

70
00:05:54,860 --> 00:06:01,790
elements can be accessed by using the subscript that is index as well as they can be accessed with the

71
00:06:01,790 --> 00:06:04,030
help of Pointer Automatic.

72
00:06:04,820 --> 00:06:07,120
So this is where all the basic things about ANELI.

73
00:06:07,130 --> 00:06:09,770
So we learn more about this in the next video.

