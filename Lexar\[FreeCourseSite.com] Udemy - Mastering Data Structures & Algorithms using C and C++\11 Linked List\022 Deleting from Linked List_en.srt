1
00:00:00,540 --> 00:00:07,680
Let us look at the delicate operation that is deleting a particular node from unlink list so far, deleting

2
00:00:07,680 --> 00:00:09,090
a node, there are two cases.

3
00:00:10,000 --> 00:00:15,820
First one is delivering fast north and the second one is deleting a note at a given position, so the

4
00:00:16,420 --> 00:00:18,520
first is a pointer pointing on the first order.

5
00:00:18,940 --> 00:00:24,400
So if you are deleting first note, that is a special case, because if this node is gone, then the

6
00:00:24,400 --> 00:00:26,170
next node should become first node.

7
00:00:26,320 --> 00:00:28,650
That is the extra thing that you have to take care of.

8
00:00:28,660 --> 00:00:33,850
And we are deleting Facebook, then deleting any other notes that they want to delete for node, then

9
00:00:34,090 --> 00:00:36,700
simply delete the node first will not be disturbed.

10
00:00:37,180 --> 00:00:39,020
First remains from the same node.

11
00:00:39,190 --> 00:00:44,890
Let us look at case one deleting first node how to delete first log deleting first node.

12
00:00:45,040 --> 00:00:49,810
So for deleting first node we should move this pointer faster to the next node.

13
00:00:50,110 --> 00:00:56,650
If suppose I'm simply moving the pointer here on the first node, then this becomes the first law when

14
00:00:56,680 --> 00:00:59,590
the first is moved here of to start from here.

15
00:01:00,040 --> 00:01:04,239
So if you go to file suit against the letters of this node, then you can access all these nodes one

16
00:01:04,239 --> 00:01:04,660
by one.

17
00:01:05,230 --> 00:01:06,490
Then what about this node?

18
00:01:06,670 --> 00:01:10,380
There is no first pointer here, so this node is not reachable.

19
00:01:10,390 --> 00:01:12,060
You cannot go to this note at all.

20
00:01:12,310 --> 00:01:13,980
Nobody is having this address.

21
00:01:14,200 --> 00:01:18,940
So this node became useless, but it is still there in the main memory.

22
00:01:19,300 --> 00:01:21,520
So now listen, very important thing.

23
00:01:21,700 --> 00:01:27,220
Whenever you are deleting a node, make sure that you are dislocating the memory for that node also

24
00:01:27,640 --> 00:01:33,340
because when you are creating a node, we are saying new and creating a new node in a heap.

25
00:01:33,610 --> 00:01:35,680
This is how we are creating a new node in him.

26
00:01:36,130 --> 00:01:42,190
Then we should also say delete for deleting a node that is a location from heap and dislocation from

27
00:01:42,190 --> 00:01:42,490
him.

28
00:01:42,850 --> 00:01:45,910
So when the node is not in use, it must be deleted.

29
00:01:46,150 --> 00:01:48,810
Now let us see how to delete first node.

30
00:01:49,390 --> 00:01:52,740
I'll bring back first on the node eight.

31
00:01:53,200 --> 00:01:58,540
Now we have to delete this node for deleting this node should move to the next node.

32
00:01:58,720 --> 00:02:02,440
Then who should delete this node physically from the heap.

33
00:02:02,590 --> 00:02:04,220
Who should use delete on this one.

34
00:02:04,450 --> 00:02:10,750
So for deleting this morning we will take one point to be on Fassnacht, take up on it, be on first

35
00:02:10,750 --> 00:02:14,360
and then move on to the next node.

36
00:02:15,010 --> 00:02:17,380
Now our link starts from here.

37
00:02:17,410 --> 00:02:19,150
Just the starting point of willingness.

38
00:02:19,460 --> 00:02:22,270
This is no more a part of Lincolnesque.

39
00:02:22,600 --> 00:02:25,170
Then delete this note from the mean.

40
00:02:26,140 --> 00:02:26,660
That's it.

41
00:02:27,190 --> 00:02:32,710
So by using force, we cannot delete it because if we delete, we will lose that as of the next node.

42
00:02:32,950 --> 00:02:35,790
So we need one extra points for deletion of this node.

43
00:02:36,130 --> 00:02:37,780
So first we'll move to the next node.

44
00:02:37,780 --> 00:02:40,450
And the other point that will take care of deletion of this one.

45
00:02:41,340 --> 00:02:46,710
So let us right on the program code and see how deletion is done, so I will do it once again.

46
00:02:47,160 --> 00:02:55,640
So the first step is we need a pointer B, so I declare it node B, that should point on first to node.

47
00:02:55,950 --> 00:03:00,290
So Pointer B is pointing on first and then move to the next node.

48
00:03:00,540 --> 00:03:06,790
So for movement of a pointer on a link list, we use this statement first assigned for SCAP Snakes.

49
00:03:06,810 --> 00:03:09,660
So first we'll move to the next node.

50
00:03:10,230 --> 00:03:12,950
First comes here, then linguist's starts from here.

51
00:03:13,170 --> 00:03:18,360
This northers outside the Linklaters now delete the node delete.

52
00:03:19,450 --> 00:03:22,760
Maybe that's all this inaudible be deleted.

53
00:03:23,710 --> 00:03:26,540
It will be removed from the memory, so everything will be gone.

54
00:03:27,040 --> 00:03:33,340
One more thing, deletion means not throwing away the value, taking all the value from the linguists

55
00:03:33,610 --> 00:03:34,660
for what purpose?

56
00:03:34,660 --> 00:03:35,980
You want to use the value.

57
00:03:36,400 --> 00:03:38,940
So if you want to use the value, take out the value.

58
00:03:39,280 --> 00:03:43,980
So let us take the value in some variable X before deleting the load.

59
00:03:44,170 --> 00:03:47,340
So please let us take it in some variable X.

60
00:03:47,500 --> 00:03:50,810
So these are four steps for deleting first node.

61
00:03:51,670 --> 00:03:52,780
Now analysis.

62
00:03:52,780 --> 00:03:55,510
What is the time taken for the leading first node.

63
00:03:55,720 --> 00:03:57,560
One, two, three, four, four steps.

64
00:03:57,580 --> 00:04:03,570
So the time is constant time for deleting this first noticed constant order of one.

65
00:04:03,730 --> 00:04:04,600
Then one more thing.

66
00:04:04,630 --> 00:04:06,730
Do you need extra points for deletion?

67
00:04:06,760 --> 00:04:07,660
Yes, definitely.

68
00:04:07,660 --> 00:04:11,830
We need extra pointer for deletion that the speed with the first.

69
00:04:11,830 --> 00:04:12,830
We cannot do that.

70
00:04:13,330 --> 00:04:14,140
Let me show you.

71
00:04:14,530 --> 00:04:16,420
See, suppose Foster's here.

72
00:04:17,680 --> 00:04:23,950
First us here, then if I say possibility, then I'll go there, so you should leave, this note is

73
00:04:23,950 --> 00:04:24,350
gone.

74
00:04:24,520 --> 00:04:25,690
How do you go to the snow?

75
00:04:25,750 --> 00:04:27,390
You cannot get out of the snow.

76
00:04:27,610 --> 00:04:29,960
So you need the dose of that note that is kept here.

77
00:04:30,310 --> 00:04:33,000
So before deletion, you need that address.

78
00:04:33,250 --> 00:04:36,310
So definitely you need the help of another pointer.

79
00:04:37,600 --> 00:04:40,480
So that's all about deleting Facebook now.

80
00:04:40,510 --> 00:04:46,810
Next, let us look at the deletion of any other log, deleting a note from given position, I will say

81
00:04:47,470 --> 00:04:51,290
bullishness for that is I want to delete this new order.

82
00:04:52,150 --> 00:04:53,290
What should the procedure?

83
00:04:53,350 --> 00:04:55,120
First, let us look at the procedure.

84
00:04:55,390 --> 00:04:58,840
Then I will write on the code deleting fourth node.

85
00:04:58,990 --> 00:05:06,520
That is this one for deleting this node from the link list means this node three that is nine should

86
00:05:06,520 --> 00:05:08,350
point on six.

87
00:05:08,350 --> 00:05:13,050
If this note is pointing on six, then automatically this node is gone from the Lincolnesque.

88
00:05:13,090 --> 00:05:15,100
So this link is pointing here now.

89
00:05:15,280 --> 00:05:16,770
It's no more pointing on seven.

90
00:05:17,020 --> 00:05:18,760
So this node is not reachable.

91
00:05:19,060 --> 00:05:22,870
So if you start from first eight, there is first node gets the rest of this note.

92
00:05:22,870 --> 00:05:26,080
This node gives that this node does not give that the six.

93
00:05:26,230 --> 00:05:27,700
So this node is not reachable.

94
00:05:27,910 --> 00:05:29,640
So in this way, the law is deleted.

95
00:05:30,160 --> 00:05:35,920
Then also we know that this node should be physically deleted from the memory, so I should delete it

96
00:05:35,920 --> 00:05:36,210
there.

97
00:05:36,220 --> 00:05:41,860
Look at the memory for the smoke that followed this what I have to do so now for deletion, what I have

98
00:05:41,860 --> 00:05:46,200
to do, I have to modify the link of this node.

99
00:05:46,210 --> 00:05:50,670
So if you want to delete fourth node, you should modify the link of a node before that.

100
00:05:50,930 --> 00:05:51,760
That is code node.

101
00:05:52,180 --> 00:05:55,420
And the second thing is you must delete this node physically.

102
00:05:56,390 --> 00:06:01,790
How many point is required to point a slick like you need a pointer on this note for modifying its length

103
00:06:01,940 --> 00:06:07,970
and you need a pointer on this note for dear looking at it from the memory so to point us needed, let

104
00:06:07,970 --> 00:06:12,470
us see how we can have a two point as one point that we really want on this note and one point that

105
00:06:12,470 --> 00:06:14,140
we want on previous note.

106
00:06:14,270 --> 00:06:15,400
So it makes one point.

107
00:06:15,410 --> 00:06:20,060
US reaching on this more than the other pointer can be following that pointer and reach there.

108
00:06:20,360 --> 00:06:23,420
So we will take a falling pointer tail pointer.

109
00:06:23,570 --> 00:06:27,650
So I will take a pointer B here and Q here.

110
00:06:27,750 --> 00:06:28,150
No.

111
00:06:29,240 --> 00:06:30,390
Is it a footnote?

112
00:06:30,510 --> 00:06:30,960
No.

113
00:06:31,250 --> 00:06:36,230
So move Q here and move B to next normal.

114
00:06:37,410 --> 00:06:41,460
Then we are not on the fourth nor see, we have done it for one time.

115
00:06:42,060 --> 00:06:46,470
Then again, bring two on B and move people next node.

116
00:06:46,980 --> 00:06:53,160
So two times I have done so, we have still not done for the position, then bring the Q on, nor the

117
00:06:53,160 --> 00:06:56,240
P and the move to next door.

118
00:06:56,520 --> 00:06:58,830
So we have reached node four.

119
00:06:58,980 --> 00:07:03,630
So please on the note to be deleted and Qs on the node before that node.

120
00:07:03,780 --> 00:07:09,330
So how many times I have moved P three times because I'm deleting fourth node position forward.

121
00:07:09,660 --> 00:07:14,360
So for any position position I should move it for K minus one time.

122
00:07:14,520 --> 00:07:19,910
So for purist position I should move it for B or minus one time.

123
00:07:20,460 --> 00:07:21,720
It is just like an insert.

124
00:07:21,720 --> 00:07:27,080
We have done so people breaching that node and you will be helping P for deleting that node.

125
00:07:27,510 --> 00:07:30,240
So let us let us write on the code still here.

126
00:07:30,540 --> 00:07:36,420
We won't be on first and node pointer.

127
00:07:36,420 --> 00:07:38,090
Q Should be none.

128
00:07:38,670 --> 00:07:41,150
So initially P was here and Q was null.

129
00:07:41,520 --> 00:07:44,100
Now going moving them for how many times.

130
00:07:44,100 --> 00:07:45,480
Position minus one times.

131
00:07:45,480 --> 00:07:48,180
I can do it using Falu so far.

132
00:07:48,810 --> 00:07:52,950
So far I assign zero to position minus one times what I should do.

133
00:07:53,250 --> 00:07:57,510
Q should come on be and B should move to the next node.

134
00:07:57,780 --> 00:08:02,250
Let's all be on fourth position and two is on third position.

135
00:08:02,250 --> 00:08:03,090
No, no.

136
00:08:03,090 --> 00:08:08,400
What other things I have to do make this cu point on this node.

137
00:08:08,580 --> 00:08:11,370
From where do you get that is from this node that is PS next.

138
00:08:11,700 --> 00:08:18,440
So modify Qs next to PS next modify Qs next to two B's next.

139
00:08:18,930 --> 00:08:19,920
This is modified.

140
00:08:20,490 --> 00:08:23,610
Now that node is free, it's not reachable.

141
00:08:23,940 --> 00:08:28,530
So simply delete the node once before division you take the value and then delete it.

142
00:08:28,800 --> 00:08:37,280
So I think the value in variable X and deleted accessing these data delete node B that's all for naught

143
00:08:37,289 --> 00:08:38,030
will be deleted.

144
00:08:38,490 --> 00:08:42,210
So this is the procedure for deleting a note from any position.

145
00:08:42,690 --> 00:08:47,940
It will delete any node starting from second position onwards, because for the first position, first

146
00:08:47,940 --> 00:08:52,500
node, this is the procedure we have seen late for any other position.

147
00:08:52,500 --> 00:08:54,600
Even the last node also gets deleted.

148
00:08:54,600 --> 00:09:00,420
With this one, you can verify this code, even the last node catalytic null analysis.

149
00:09:01,580 --> 00:09:09,370
How many links are modified, just one longest modified U.S., one longest modified this link, change

150
00:09:09,650 --> 00:09:11,910
this link actually in order for the deleted.

151
00:09:11,970 --> 00:09:13,610
So there's no modification of link.

152
00:09:14,360 --> 00:09:15,530
One link is modified.

153
00:09:16,200 --> 00:09:23,210
How many point required extra point to point this out, plan B and Q that are for deleting in order

154
00:09:23,420 --> 00:09:24,780
to support this request?

155
00:09:25,460 --> 00:09:26,690
What is the time taken?

156
00:09:27,690 --> 00:09:35,190
The time span here is only in this for loop, this loop will move P and Q, so follow up is used for

157
00:09:35,430 --> 00:09:39,840
reaching a given position, so it will take you to that position.

158
00:09:40,170 --> 00:09:43,590
So the time taken by this loop depends on the position that you are giving.

159
00:09:43,860 --> 00:09:49,350
If you are giving second position than the time is constant, PDCA will not move at all if you are giving

160
00:09:49,350 --> 00:09:56,730
last position that the time is maximum and so the time taken by this procedure is minimum constant.

161
00:09:58,070 --> 00:10:03,360
And maximum, and so the time taken is minimum, one maximum and.

162
00:10:05,070 --> 00:10:06,430
So that's all about deletion.

163
00:10:07,000 --> 00:10:12,150
No, I will write a single function which will take the index and delete that note, whether it may

164
00:10:12,150 --> 00:10:16,020
be a footnote or any of the more I will write a single function in.

165
00:10:17,740 --> 00:10:25,090
Now, let us see the function for deletion function is delete, it takes a position and returns are

166
00:10:25,090 --> 00:10:28,220
deleted value that is integer because all these values are integer.

167
00:10:29,110 --> 00:10:37,030
I need to point this point cuz I have taken PACU and variable X and I if position is one then delete

168
00:10:37,030 --> 00:10:37,670
Fassnacht.

169
00:10:37,960 --> 00:10:40,360
So these are the steps that we are already familiar with.

170
00:10:40,360 --> 00:10:41,350
Just what we have discussed.

171
00:10:41,350 --> 00:10:47,260
How to delete does not take out the data from first and make point on first move us to next more and

172
00:10:47,260 --> 00:10:55,540
delete B otherwise if it is not for Snoad means if position is not one then we have to delete a node

173
00:10:55,540 --> 00:10:56,980
from any other position.

174
00:10:56,980 --> 00:11:03,760
So we need to point USPI on first node and to a null and void kubel reach on the particular index and

175
00:11:03,760 --> 00:11:08,920
longboarding and checking that Weatherby's becoming the any time seeing this list.

176
00:11:08,920 --> 00:11:10,450
I have only five nodes.

177
00:11:10,600 --> 00:11:13,510
If I say position as a ten then what happens.

178
00:11:14,530 --> 00:11:20,810
This P should move to the line times so it will move for one time two times three four.

179
00:11:21,070 --> 00:11:23,200
At the fifth time B becomes null.

180
00:11:23,620 --> 00:11:27,600
So there is no intent index so it cannot reach there so P becomes null.

181
00:11:27,910 --> 00:11:30,610
So I'm also checking whether PE's not null or not.

182
00:11:30,610 --> 00:11:32,320
If it is null then it will stop.

183
00:11:33,040 --> 00:11:39,460
And after coming out, I am confirming whether P has reached a valid node or not or it has become null

184
00:11:39,460 --> 00:11:42,160
if it has become known since it was invalid index.

185
00:11:42,550 --> 00:11:47,080
If it is not null, then these are the three steps that are required for deletion.

186
00:11:47,080 --> 00:11:50,350
That is, we have to modify a link that is make it kills.

187
00:11:50,350 --> 00:11:53,050
Next point on PS next on the site.

188
00:11:53,320 --> 00:11:55,450
Like then delete the node.

189
00:11:55,450 --> 00:12:00,700
If suppose you want to delete footnote then delete the node at last, return the value that you have

190
00:12:00,700 --> 00:12:01,290
deleted.

191
00:12:01,660 --> 00:12:03,820
So that's all about deletion.

