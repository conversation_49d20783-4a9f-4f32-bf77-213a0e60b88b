1
00:00:00,150 --> 00:00:06,860
And this video will look at insert operation inserting in a circular linked list.

2
00:00:08,170 --> 00:00:14,260
So already in linear language, we have learned how to insert, so let us recall a few things from there.

3
00:00:14,950 --> 00:00:17,290
So first of all, I will label the notes.

4
00:00:17,440 --> 00:00:24,670
I'll give the indices to the north, let us call this known as one first or second, third, fourth

5
00:00:24,670 --> 00:00:25,960
and fifth node.

6
00:00:26,350 --> 00:00:29,890
So what are the positions I have for inserting a new node?

7
00:00:29,890 --> 00:00:33,010
I can insert it before the first node and that is had node.

8
00:00:33,020 --> 00:00:38,120
So this is zeroth index and this is first this is in between second and third.

9
00:00:38,180 --> 00:00:39,850
The second and this is third.

10
00:00:40,420 --> 00:00:43,910
And this is food and this is fifth.

11
00:00:44,140 --> 00:00:46,600
So I have total six positions for insertion.

12
00:00:46,600 --> 00:00:51,060
I can insert a new node anywhere out of the sixth position.

13
00:00:51,700 --> 00:00:58,930
If you remember in Linear Linguist's, we had two cases inserting before first node or inserting at

14
00:00:58,930 --> 00:01:00,460
any other given position.

15
00:01:00,490 --> 00:01:06,760
So here we have two cases inserting before and inserting at any other given position.

16
00:01:07,150 --> 00:01:09,430
Whatever the position you want, you can insert there.

17
00:01:10,060 --> 00:01:15,720
So two cases are that let us look at second case for inserting at any other position.

18
00:01:16,150 --> 00:01:17,280
What is the procedure?

19
00:01:18,060 --> 00:01:22,680
Suppose I want to insert a new node at our position for months here.

20
00:01:22,720 --> 00:01:24,010
I want to insert a new north.

21
00:01:24,580 --> 00:01:26,380
Let us in, you know, there.

22
00:01:27,650 --> 00:01:28,610
And remove this.

23
00:01:29,830 --> 00:01:37,300
First of all, create a new law with the help of a point of and insert Data No.14 sitting in order here,

24
00:01:37,600 --> 00:01:41,040
I should link this one to Pfiffner and thought no one should point on this one.

25
00:01:41,380 --> 00:01:46,930
So I have to manipulate this effort and also change that one so far that I need a pointer here.

26
00:01:47,200 --> 00:01:52,840
So that point that I can bring it from Fastenal that is held in order and I should move the pointer

27
00:01:52,840 --> 00:01:55,990
one, two, three times than the pointer comes here.

28
00:01:56,440 --> 00:02:03,040
Once the pointer come here, I can make a D next point on PS next PS next to the Swift node and also

29
00:02:03,040 --> 00:02:07,000
I can modify this pointer that is make this piece next point on new.

30
00:02:07,000 --> 00:02:10,120
Nor does the method for insertion.

31
00:02:10,840 --> 00:02:16,270
So this procedure the same as what we have seen in inserting in a linear link.

32
00:02:16,690 --> 00:02:20,230
So same procedure as the let us have a look at the code once again.

33
00:02:20,230 --> 00:02:22,660
I'll just fade on the code here.

34
00:02:22,660 --> 00:02:23,560
I have written the code.

35
00:02:23,740 --> 00:02:29,950
I need a pointer p up on hadnot then with the follow up move P for the position minus one time.

36
00:02:29,950 --> 00:02:32,830
If the position is false then move P four three times.

37
00:02:33,920 --> 00:02:38,800
Then create a new node, I'm creating a new node and filling the data is the data.

38
00:02:38,960 --> 00:02:39,940
So data is filled.

39
00:02:40,220 --> 00:02:45,860
Then these next Espy's next sorties next is pointing on peaceniks, which was already pointing here,

40
00:02:46,280 --> 00:02:47,570
then peaceniks jesty.

41
00:02:47,810 --> 00:02:49,010
So Peaceniks is pointing.

42
00:02:50,150 --> 00:02:55,010
So this is a code for inserting at a given position, the same as what we have signed Línea.

43
00:02:55,760 --> 00:02:58,490
So you are already familiar with the code then?

44
00:02:58,490 --> 00:03:00,670
One more important analysis.

45
00:03:01,790 --> 00:03:08,930
This procedure takes a minimum time when you want to insert here after Fassnacht and it takes maximum

46
00:03:08,930 --> 00:03:12,500
time when you want to insert after last node if this is the last word.

47
00:03:13,620 --> 00:03:20,580
Right, so after had a. if you insert it takes minimum time, so many times Konstantine, so this procedure

48
00:03:20,580 --> 00:03:26,670
takes constant time when you want to insert here and it takes a maximum order of time when you want

49
00:03:26,670 --> 00:03:28,970
to insert after the last load.

50
00:03:30,630 --> 00:03:30,970
Right.

51
00:03:31,050 --> 00:03:36,930
So in a circle of linguists, there is one position where we can insert an Konstantine and that position

52
00:03:36,930 --> 00:03:38,580
is after HADNOT.

53
00:03:39,800 --> 00:03:43,390
Now we will see how to insert a new note before heading north.

54
00:03:44,430 --> 00:03:49,340
Before inserting before had known what other things I have to do, let us see the procedure.

55
00:03:50,460 --> 00:03:54,780
First of all, I should create a new node with the help of some pointy.

56
00:03:56,350 --> 00:04:00,080
Then insert data that the nexus should point on.

57
00:04:00,660 --> 00:04:09,880
OK, this is done, but one more thing I have to do, this last node should point upon being so I have

58
00:04:09,880 --> 00:04:12,370
to manipulate or modify this last node.

59
00:04:12,790 --> 00:04:14,490
So I should reach last node.

60
00:04:15,070 --> 00:04:22,840
So how I can reach last node, take a pointer B from the north and go on doing it, go on moving it

61
00:04:23,080 --> 00:04:24,020
and stop here.

62
00:04:24,340 --> 00:04:29,250
On what basis I can stop there because its next is equal to head.

63
00:04:29,590 --> 00:04:33,030
So stop on the node whose next is equal to head.

64
00:04:33,280 --> 00:04:37,660
So stop and Degnan so p will move and stop at this node.

65
00:04:38,840 --> 00:04:47,060
Then what I have to do, these next should point on new Nordea soapies, next to that link is coming

66
00:04:47,070 --> 00:04:50,520
still here, you can see so extend it and make it point.

67
00:04:50,520 --> 00:04:57,530
Don t know if you start from head, this aid will stand on three three one zero nine nine will send

68
00:04:57,530 --> 00:05:04,030
us on six six will send us on two and two will send us on 10 and 10 is linked to eight.

69
00:05:04,340 --> 00:05:08,980
So this note is inserted inside circular Lincolnesque.

70
00:05:09,200 --> 00:05:10,160
No one last time.

71
00:05:10,760 --> 00:05:15,440
Shall I move ahead and call this as new head.

72
00:05:16,250 --> 00:05:23,240
So if somebody comes on the left hand side of her and sits there, will you make him had no need not

73
00:05:23,240 --> 00:05:24,430
be harmed.

74
00:05:24,440 --> 00:05:30,080
If somebody comes before the first and sits before us, then you can call him first to new first.

75
00:05:30,440 --> 00:05:32,300
But a new head is not necessary.

76
00:05:32,600 --> 00:05:34,640
So logically, you don't have to move.

77
00:05:34,640 --> 00:05:39,710
Her head will be that same node, if at all you want to change, you can move that head.

78
00:05:40,430 --> 00:05:43,340
I prefer let the heartbeat seem normal.

79
00:05:43,460 --> 00:05:44,930
Don't move it just.

80
00:05:44,930 --> 00:05:47,260
We have inserted a new node before head north.

81
00:05:47,870 --> 00:05:51,650
This is the same as in setting a new node after last node.

82
00:05:51,650 --> 00:05:55,010
What was the last known to then after that to who is there.

83
00:05:55,010 --> 00:05:58,790
And so this is the last node and then it is connecting to head.

84
00:05:59,060 --> 00:06:03,290
So inserting before head or after last known as the same.

85
00:06:03,710 --> 00:06:07,910
Although the difference is whether you want more head there on a new node or not.

86
00:06:08,420 --> 00:06:10,610
I like him so hard.

87
00:06:10,610 --> 00:06:11,420
It is your choice.

88
00:06:11,420 --> 00:06:12,710
Moving ahead is your choice.

89
00:06:13,070 --> 00:06:17,330
So inserting before her or inserting after last bothers him.

90
00:06:17,780 --> 00:06:20,240
So let us right on the procedure for doing all this.

91
00:06:20,570 --> 00:06:21,800
I learned on the court here.

92
00:06:22,100 --> 00:06:25,850
So what I need is I will I will create a new node and fill the data.

93
00:06:25,870 --> 00:06:29,960
So first I will create a new node notice created.

94
00:06:30,290 --> 00:06:35,360
Let us fill the data, then let it point on had in order, so be it.

95
00:06:35,360 --> 00:06:38,930
I is filled with the value and D next is pointing on had no.

96
00:06:38,990 --> 00:06:43,850
What I have to do is I should go on the last note and make it point on the new order.

97
00:06:43,850 --> 00:06:47,960
The T so far that I should take a pointer P from here and make it move.

98
00:06:47,960 --> 00:06:54,290
Glazner so far that I will have one more pointer node to be assigned had.

99
00:06:54,590 --> 00:06:57,110
Now that should move to last node.

100
00:06:57,200 --> 00:07:08,010
So while these next is not equal to header, so it will stop and business is equal to ahead.

101
00:07:08,060 --> 00:07:11,030
So right now business is equal to had, so it will stop.

102
00:07:11,180 --> 00:07:17,770
And every time what I should do move the assign PS next so P will be moving on from next door.

103
00:07:18,020 --> 00:07:21,380
So this loop will move P till the last node.

104
00:07:22,890 --> 00:07:31,790
Then that last note should point on new order to be an extra point on D, then if you want to move ahead,

105
00:07:31,800 --> 00:07:35,750
you can move ahead on new Naude predefine T.

106
00:07:36,180 --> 00:07:38,160
So this had Northville Point on New York.

107
00:07:39,100 --> 00:07:45,700
So this is the procedure for inserting before death or also after last.

108
00:07:45,850 --> 00:07:48,640
So even this procedure is also inserting after last.

109
00:07:48,670 --> 00:07:50,940
But this is based on the position index.

110
00:07:51,250 --> 00:07:55,250
So there are two cases inserting before her or inserting at any other position.

111
00:07:55,810 --> 00:08:00,810
So this second caller does inserting at index zero and this is any other index.

112
00:08:01,030 --> 00:08:03,040
So let us combine these two and.

113
00:08:03,040 --> 00:08:03,280
Right.

114
00:08:03,360 --> 00:08:05,130
A single insert function.

115
00:08:05,140 --> 00:08:09,910
So I will write a single insert function, then I'll explain that one here.

116
00:08:09,910 --> 00:08:11,230
I have to insert function.

117
00:08:11,230 --> 00:08:16,510
Let us quickly look at this one, see an insert function that's taking two parameters at what position

118
00:08:16,510 --> 00:08:21,520
you want to insert, what index you want to insert and what value you want to insert.

119
00:08:22,150 --> 00:08:27,730
Then here, first condition, if position is zero means you want to insert before had a..

120
00:08:28,030 --> 00:08:30,430
Then already we have seen what other teams you have to do.

121
00:08:30,700 --> 00:08:33,250
But here I have taken care of one more condition.

122
00:08:33,490 --> 00:08:38,409
See here have created a new node then in this statement condition and checking.

123
00:08:38,770 --> 00:08:40,049
Is it the first node.

124
00:08:40,360 --> 00:08:42,299
It will be first notified is annulments.

125
00:08:42,429 --> 00:08:43,270
It is the first node.

126
00:08:43,539 --> 00:08:47,650
If this is the first node then that should point on that note and heads.

127
00:08:47,650 --> 00:08:52,870
The next should point on itself the quality we have seen in previous video, if there is one single

128
00:08:52,870 --> 00:08:55,470
node and had inaudible depending on itself.

129
00:08:55,750 --> 00:08:56,830
So that's what I have done.

130
00:08:57,010 --> 00:09:01,270
If that is another means this is the first node, so I have taken care of that one.

131
00:09:01,690 --> 00:09:05,330
Otherwise, the procedure already have shown you there's more space.

132
00:09:05,330 --> 00:09:09,010
So I have to mine more than one statements in a single line.

133
00:09:09,430 --> 00:09:11,170
So already you have seen the procedure.

134
00:09:11,170 --> 00:09:17,200
You should take Peterle the last known and this note should point on head and that last note should

135
00:09:17,200 --> 00:09:18,760
point on, you know, the T.

136
00:09:18,880 --> 00:09:25,090
So all that procedure I have shown here ls if there is any other position, then this is a regular code

137
00:09:25,090 --> 00:09:27,460
for inserting in the order at any given position.

138
00:09:27,460 --> 00:09:29,020
So the same code is there.

139
00:09:29,980 --> 00:09:32,260
But one thing is missing in this code here.

140
00:09:32,260 --> 00:09:34,720
I'm not checking whether the position is valid or not.

141
00:09:34,870 --> 00:09:40,900
Like suppose one, two, three, four, five nodes are there so I can give this from zero to five,

142
00:09:40,900 --> 00:09:41,920
not beyond that.

143
00:09:42,460 --> 00:09:45,580
So I'm not checking here whether the position is going beyond that one.

144
00:09:46,090 --> 00:09:51,410
If I'm not checking what happens in this code, you try to analyze this one right?

145
00:09:51,640 --> 00:09:54,640
When I write the program that time, I will include that condition.

146
00:09:54,640 --> 00:09:56,410
Also, I will check that console.

147
00:09:57,220 --> 00:10:04,450
Finally, analysis of insertions that if you insert a new node after header, that is after all the

148
00:10:04,450 --> 00:10:10,420
time is constant and it is growing and growing at the last note it is and or even before the head node

149
00:10:10,420 --> 00:10:11,260
also edges.

150
00:10:11,380 --> 00:10:14,830
And so there is one position where the time is constant.

151
00:10:19,840 --> 00:10:21,100
That's all about insert.

