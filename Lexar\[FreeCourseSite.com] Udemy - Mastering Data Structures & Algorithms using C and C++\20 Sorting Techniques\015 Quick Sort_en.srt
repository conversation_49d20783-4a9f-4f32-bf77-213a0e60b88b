1
00:00:00,540 --> 00:00:08,189
I have taken the list of elements here and I want to sort the list, so let us see the procedure applied,

2
00:00:08,189 --> 00:00:11,930
the procedure of quicksort and thought one element first.

3
00:00:12,480 --> 00:00:13,700
So let us do it.

4
00:00:14,280 --> 00:00:22,030
See the procedure, this first element and the list we selected as Pimlott Birute or whatever you say.

5
00:00:22,410 --> 00:00:25,530
So this is what element first element of spirit.

6
00:00:25,680 --> 00:00:26,680
So what is the spirit?

7
00:00:26,760 --> 00:00:31,290
See, I have given the example of students and you are finding your place.

8
00:00:31,650 --> 00:00:33,500
So yourself was pivot.

9
00:00:33,570 --> 00:00:38,480
So who is going to find out the place now 50 has to find its place.

10
00:00:38,610 --> 00:00:40,180
So that is spirit element.

11
00:00:40,230 --> 00:00:45,960
So we have to find the position of 50 so far that all the elements are smaller than 50 should be on

12
00:00:45,960 --> 00:00:46,460
the site.

13
00:00:46,680 --> 00:00:48,510
All the elements are greater than 50.

14
00:00:48,510 --> 00:00:53,580
Should be on that side, on the right hand side, left hand side, smaller elements.

15
00:00:54,760 --> 00:01:00,790
So for that, we will see if there are any larger elements on this site and from that side we will see,

16
00:01:00,790 --> 00:01:02,380
are there any shorter elements?

17
00:01:03,100 --> 00:01:06,640
If so, we will interchange them so far that we will take.

18
00:01:06,790 --> 00:01:10,300
I hear that we look for greater elements.

19
00:01:10,300 --> 00:01:14,170
So if any greater element is found, it will stop and then try to change.

20
00:01:14,650 --> 00:01:16,560
That will take it from here.

21
00:01:17,020 --> 00:01:20,770
That will be looking for any smaller elements.

22
00:01:21,040 --> 00:01:24,670
So if any smaller element found that site, it will interchange with the element.

23
00:01:25,510 --> 00:01:30,820
I will be looking for any element that is a greater than the pivot.

24
00:01:32,650 --> 00:01:39,220
So it must get some element that is greater than it, right, that only it will stop and the GAO will

25
00:01:39,220 --> 00:01:47,580
be looking for an element smaller than or equal to pivot so that at least it will come and that the

26
00:01:47,710 --> 00:01:49,750
pivoted pivot is equal to it.

27
00:01:49,870 --> 00:01:51,060
And so it will stop there.

28
00:01:52,050 --> 00:01:58,030
I will once again, I will be looking for a greater element, leadership will be looking for smaller

29
00:01:58,030 --> 00:02:02,250
or equally equality nominees, looking for greater and greater support.

30
00:02:02,310 --> 00:02:04,500
No element is greater than pivot.

31
00:02:04,740 --> 00:02:06,930
Then there should be something to stop.

32
00:02:06,960 --> 00:02:10,889
I so far that I will take infinity at that.

33
00:02:12,140 --> 00:02:15,660
Without this, all you can do, I'm showing him a third of it, infinity.

34
00:02:15,920 --> 00:02:22,250
So what does that infinity and awfullest marker, some maximum integer maximum number, you can take

35
00:02:22,370 --> 00:02:23,440
out some marker.

36
00:02:23,750 --> 00:02:29,310
So largest number like a four digit to whites, then three, two, seven, six, seven.

37
00:02:29,330 --> 00:02:31,370
That is the largest integer you can take.

38
00:02:33,480 --> 00:02:42,420
Let us stop, so we selected private eye and also initialise let us start using I find out an element

39
00:02:42,420 --> 00:02:44,090
which is greater than 50.

40
00:02:44,400 --> 00:02:44,970
Is it good?

41
00:02:44,980 --> 00:02:45,090
And.

42
00:02:45,480 --> 00:02:51,630
Yes, we got the very first element, greater than 50 using G find the element that is smaller than

43
00:02:51,630 --> 00:02:51,920
people.

44
00:02:51,960 --> 00:02:53,990
That is 50 DiClemente.

45
00:02:54,180 --> 00:02:57,210
Yes, we got an element that is smaller than 50.

46
00:02:57,420 --> 00:03:02,880
So what that small element is doing here, what the other person is doing here as them going to change

47
00:03:02,880 --> 00:03:03,680
their places.

48
00:03:03,900 --> 00:03:09,990
OK, now the next list will be 50 and here comes the 30.

49
00:03:10,230 --> 00:03:12,660
This is 60 and 90.

50
00:03:14,200 --> 00:03:21,880
Forty, this is 80 and guarantee the 70 will come and displace.

51
00:03:24,180 --> 00:03:26,130
Infinity's Aspies.

52
00:03:27,660 --> 00:03:28,960
I is still here.

53
00:03:29,150 --> 00:03:33,570
Jesus is still here, let us perform the next step again.

54
00:03:33,570 --> 00:03:35,400
Continue, continue I.

55
00:03:36,990 --> 00:03:38,610
This is great to stop.

56
00:03:40,460 --> 00:03:41,530
This is smaller.

57
00:03:41,660 --> 00:03:42,300
Yes, it is.

58
00:03:42,650 --> 00:03:47,570
See, I have taken the elements, most of the greater element decided the smaller that site so we can

59
00:03:47,570 --> 00:03:48,980
see more number of wrapping's.

60
00:03:50,640 --> 00:03:56,040
We got a great element here, we got a smaller element to what we are doing here, you come and stand

61
00:03:56,160 --> 00:03:59,130
this person, please go this side because you're taller than me.

62
00:03:59,310 --> 00:04:00,590
I am below 50.

63
00:04:01,620 --> 00:04:02,820
OK, Interchange.

64
00:04:04,380 --> 00:04:08,520
This is 50 and 30, don't even come here.

65
00:04:09,640 --> 00:04:10,990
Remaining as it is.

66
00:04:12,040 --> 00:04:18,130
60 will take this place, Infinity IIs pointing here, just pointing in.

67
00:04:19,380 --> 00:04:23,470
Continuum move, i.e., this is greater than pivot.

68
00:04:23,580 --> 00:04:32,640
Yes, move the G, this is smaller than pivot to smaller than 50, then interchange them 15, 20, 20

69
00:04:32,640 --> 00:04:36,170
as it is at this place, 10 will come here.

70
00:04:36,900 --> 00:04:41,190
These two as it is nine, they will take the place of 10 infinity.

71
00:04:41,190 --> 00:04:44,950
And then this is pivot I use here.

72
00:04:46,500 --> 00:04:50,220
Continue to find the element which is greater than this one.

73
00:04:50,580 --> 00:04:51,480
Is it greater?

74
00:04:51,960 --> 00:04:53,840
No, this is not greater move.

75
00:04:54,150 --> 00:04:55,710
So I should move.

76
00:04:55,710 --> 00:05:00,720
If it is a smaller I should stop if it is a greater thing should continue.

77
00:05:00,900 --> 00:05:03,870
If it is smaller or equally, it should continue right.

78
00:05:04,200 --> 00:05:05,890
To stop when it is greater.

79
00:05:05,910 --> 00:05:11,550
Remember, this is a confusing point sometimes for the students then simply using G, I have to find

80
00:05:11,550 --> 00:05:13,920
out an element that is smaller than this one.

81
00:05:14,220 --> 00:05:16,980
So Javor decrement, is it smaller?

82
00:05:17,280 --> 00:05:17,750
No.

83
00:05:18,330 --> 00:05:20,550
So if it is a greater continuum.

84
00:05:22,350 --> 00:05:23,250
Is it smaller?

85
00:05:23,550 --> 00:05:24,210
Yes.

86
00:05:25,270 --> 00:05:30,300
Stop if it is a smaller or equal stop, if it is great, that it should continue.

87
00:05:31,870 --> 00:05:39,610
Then now, if you observe, I became greater than she means, we have checked the entire list of elements,

88
00:05:39,880 --> 00:05:40,780
nothing is left.

89
00:05:40,810 --> 00:05:41,980
We have checked thoroughly.

90
00:05:42,220 --> 00:05:44,500
So we have to change all the elements.

91
00:05:45,190 --> 00:05:49,320
See, I haven't started from this site and Jay has started from this site.

92
00:05:49,570 --> 00:05:51,770
Now, they board met at some place.

93
00:05:51,880 --> 00:05:53,990
So how I became of a..

94
00:05:54,010 --> 00:05:57,430
So if you have checked the entire list now, do what interchange.

95
00:05:57,430 --> 00:05:58,660
Pivot element of it.

96
00:05:59,470 --> 00:06:01,270
Jide element.

97
00:06:02,450 --> 00:06:10,010
So 40 will come to this site today, 2010, as it is, this is 50 and remaining element.

98
00:06:11,390 --> 00:06:12,490
This is infinity.

99
00:06:12,770 --> 00:06:15,050
This is sorted at G.

100
00:06:17,220 --> 00:06:23,190
So one element that is 50, we got it sorted and the rest of the elements, if you see all the small

101
00:06:23,190 --> 00:06:25,450
elements of the site all together, element that site.

102
00:06:25,800 --> 00:06:27,000
So 50 sorted.

103
00:06:27,940 --> 00:06:29,760
So now one element, 50 sorted.

104
00:06:30,130 --> 00:06:31,040
What about the rest?

105
00:06:31,330 --> 00:06:33,730
We have to sort this list still.

106
00:06:33,730 --> 00:06:37,840
We have to sort this list and still we have to sort this list.

107
00:06:38,290 --> 00:06:41,270
So list is a partition here splitted here.

108
00:06:41,560 --> 00:06:45,820
So this position, we call it as partitioning position.

109
00:06:46,180 --> 00:06:48,130
Lissa's getting partition here.

110
00:06:48,380 --> 00:06:51,210
So some elements on this side, some elements on this side.

111
00:06:51,220 --> 00:06:53,830
In my example, I got four four elements on board.

112
00:06:53,830 --> 00:06:57,250
So it's not necessary that it is always in the middle.

113
00:06:57,250 --> 00:06:58,150
It can be anywhere.

114
00:06:58,480 --> 00:07:00,280
But the list is partition into two.

115
00:07:00,490 --> 00:07:03,360
And we have to sort this one also this one also.

116
00:07:03,640 --> 00:07:06,120
How will you sort this quicksort again.

117
00:07:06,250 --> 00:07:07,270
Quicksort on this.

118
00:07:07,270 --> 00:07:10,380
Quicksort on this on board will perform quicksort.

119
00:07:10,840 --> 00:07:16,290
So it means quicksort is recursively applied on left hand side and right hand side.

120
00:07:16,810 --> 00:07:17,340
Yes.

121
00:07:17,860 --> 00:07:19,900
So can quicksort work on few elements.

122
00:07:19,950 --> 00:07:20,320
Yes.

123
00:07:20,830 --> 00:07:21,800
Two elements are there.

124
00:07:21,800 --> 00:07:22,360
It can work.

125
00:07:22,360 --> 00:07:24,850
One element Luneta for many of them.

126
00:07:24,850 --> 00:07:27,090
Two elements must be there for performing quicksort.

127
00:07:27,610 --> 00:07:28,450
I'll show you that.

128
00:07:29,200 --> 00:07:30,100
And one more point.

129
00:07:30,430 --> 00:07:31,860
Who is infinity for this one?

130
00:07:31,880 --> 00:07:32,330
This one.

131
00:07:32,340 --> 00:07:32,680
This one.

132
00:07:32,730 --> 00:07:37,870
The largest value that we have added at the end of this marker, then who isn't ready for this list

133
00:07:38,320 --> 00:07:39,710
as ending here is infinity.

134
00:07:40,060 --> 00:07:44,820
So this saudade element 50 will act as infinity for this left hand cycliste.

135
00:07:47,700 --> 00:07:55,440
So that's what we will see this time, no one lasting this entire procedure of selecting pivot and taking

136
00:07:55,440 --> 00:08:01,830
and exchanging the element that is smaller and larger elements, then finally bringing pivot at positioning

137
00:08:01,830 --> 00:08:02,400
position.

138
00:08:02,850 --> 00:08:07,270
This entire procedure is called a partitioning procedure.

139
00:08:07,920 --> 00:08:09,380
So it's partitioning procedure.

140
00:08:09,660 --> 00:08:15,900
So Quicksort uses partitioning procedure for something that elements and also that is recursive.

141
00:08:16,560 --> 00:08:22,140
So Fosterville right on partitioning procedure, the one that we have seen done right on the quixotry,

142
00:08:22,330 --> 00:08:22,940
civil guard.

