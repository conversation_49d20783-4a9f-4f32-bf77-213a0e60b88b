1
00:00:00,270 --> 00:00:07,980
In previous white lectures, I have shown the sea style programming that I have adopted in my course

2
00:00:08,130 --> 00:00:11,850
and also I have shown you how to convert a C language code to C++.

3
00:00:12,840 --> 00:00:14,630
Not in this demonstration lectures.

4
00:00:14,880 --> 00:00:21,230
I will show you various styles of programming, writing a simple monolithic program to an object oriented

5
00:00:21,240 --> 00:00:21,720
program.

6
00:00:21,730 --> 00:00:29,070
So you watch all these demo lectures step by step, one by one in the same order and also practice these

7
00:00:29,070 --> 00:00:33,280
things like this will give you the idea of various styles of programming.

8
00:00:33,690 --> 00:00:39,870
So in this demo lecture, I will show you the fossa style that is monolithic style of programming.

9
00:00:40,260 --> 00:00:47,010
The program that I am going to write is to finding the area and perimeter of a rectangle and I will

10
00:00:47,010 --> 00:00:50,260
be taking <PERSON><PERSON>'s input from the user.

11
00:00:50,730 --> 00:00:55,900
Again, I repeat, I am going to find area and perimeter of a rectangle.

12
00:00:56,580 --> 00:00:58,620
So let us start the program.

13
00:00:58,740 --> 00:01:04,950
See as it is a monolithic program, everything I will write inside mean function so far a rectangle.

14
00:01:04,950 --> 00:01:10,290
I need two variables integer length as well as breath.

15
00:01:10,800 --> 00:01:15,840
OK, and these I will initialize them to zero write initial values.

16
00:01:15,840 --> 00:01:21,510
I will keep them at zero that I should take input, length and input.

17
00:01:21,510 --> 00:01:24,870
But so I will use print f.

18
00:01:26,230 --> 00:01:29,110
Enter land and.

19
00:01:31,810 --> 00:01:36,580
Does the language function now seen lend?

20
00:01:37,550 --> 00:01:44,480
But see, I have mixed Aussie language and C++ code because both the libraries are including I Will

21
00:01:44,480 --> 00:01:50,260
Stream and Studio and that type of program, the project is C++ project.

22
00:01:50,390 --> 00:01:56,570
And so I'm writing like this because you feel comfortable with both the C and C++.

23
00:01:56,960 --> 00:02:00,470
OK, so I am taking length and breadth, so reading.

24
00:02:00,730 --> 00:02:04,810
And then after that I should calculate area and perimeter.

25
00:02:04,830 --> 00:02:10,250
So I will declare a variable area and in this I will calculate lente.

26
00:02:11,520 --> 00:02:13,530
Multiply by breadth.

27
00:02:14,250 --> 00:02:16,640
So this will give it here.

28
00:02:16,950 --> 00:02:22,250
All right, then also I will calculate perimeter, so I will give the short name Betty.

29
00:02:22,350 --> 00:02:26,400
And this is to into Lent plus.

30
00:02:28,190 --> 00:02:36,910
But I liked then I will print the result, so printf area is equal to percentile.

31
00:02:37,760 --> 00:02:50,650
And again then perimeter equal to percentile D and slashing and here I will pass ADA as well as pediment

32
00:02:50,660 --> 00:02:51,710
transparent with them.

33
00:02:51,740 --> 00:02:55,130
So this will be printing area and the value of area.

34
00:02:55,140 --> 00:03:01,020
Then it will go to next line, write new line perimeter, then it will show the perimeter and go to

35
00:03:01,030 --> 00:03:01,520
next slide.

36
00:03:01,700 --> 00:03:02,250
That's it.

37
00:03:02,270 --> 00:03:03,410
There's a simple program.

38
00:03:03,410 --> 00:03:06,880
I will run the program and show you the entire length and breadth.

39
00:03:07,040 --> 00:03:08,900
So clandestine and breathless.

40
00:03:08,900 --> 00:03:09,430
Five.

41
00:03:09,440 --> 00:03:16,730
So I got Area Swiftie and Perimeter's 30 that does a simple program and I can finish the program within

42
00:03:16,730 --> 00:03:17,890
a few lines of code.

43
00:03:18,200 --> 00:03:24,050
I have to get a very simple task so that you can observe the style of programming rather than focusing

44
00:03:24,050 --> 00:03:24,830
on the problem.

45
00:03:25,070 --> 00:03:27,050
You can focus on style of programming.

46
00:03:27,420 --> 00:03:27,820
All right.

47
00:03:28,160 --> 00:03:30,390
So this this is a very simple task.

48
00:03:30,530 --> 00:03:31,640
Now, one question may arise.

49
00:03:31,640 --> 00:03:33,080
Some student will get confused.

50
00:03:33,080 --> 00:03:36,920
Already you are reading and prettier than what you are initializing them.

51
00:03:36,920 --> 00:03:38,060
So it's a good practice.

52
00:03:38,060 --> 00:03:44,210
Whenever you declare a variable, you initialize them as a good practice so that you can avoid any problems

53
00:03:44,210 --> 00:03:49,450
that may cause due to mistyping or carelessness of a programmer.

54
00:03:49,880 --> 00:03:50,280
Right.

55
00:03:50,660 --> 00:03:52,720
So initialize it beforehand.

56
00:03:52,910 --> 00:03:55,100
No doubt we are reading them right.

57
00:03:55,290 --> 00:03:59,060
It is just like you have cooked and prepared an omelet.

58
00:03:59,810 --> 00:04:02,900
Then you will take it in a serving plate and you will eat it, right.

59
00:04:03,350 --> 00:04:07,150
So eating plate is different than cooking plate is a difference anyway.

60
00:04:07,160 --> 00:04:10,430
You are going to eat so you can directly from the cooking pan.

61
00:04:10,430 --> 00:04:10,790
Right?

62
00:04:11,220 --> 00:04:13,540
So the question is just as simple as that.

63
00:04:13,580 --> 00:04:15,070
Like this is similar to that.

64
00:04:15,530 --> 00:04:17,899
So better initialize it beforehand.

65
00:04:18,290 --> 00:04:19,760
Then after that you read the values.

66
00:04:19,760 --> 00:04:24,490
If you're not reading then what are the values there and inside they will have garbage values so better

67
00:04:24,500 --> 00:04:25,720
there should be something in that.

68
00:04:26,000 --> 00:04:31,850
So a big seismograms, you may face problems if you don't initialize the variables, that's it.

69
00:04:32,150 --> 00:04:34,460
So this is a monolithic program that I have done.

70
00:04:35,060 --> 00:04:36,020
In next video.

71
00:04:36,020 --> 00:04:43,550
I will use the same program and I will modify it and I will show you model programming or programming

72
00:04:43,550 --> 00:04:44,650
using functions.

73
00:04:45,080 --> 00:04:47,330
So that's all in this video, right, on this program.

74
00:04:47,330 --> 00:04:49,790
And be ready to watch the next lecture.

