1
00:00:00,150 --> 00:00:05,290
In this video, we will have the demonstration for Double-dealing, so we will create and display a

2
00:00:05,360 --> 00:00:07,710
willingness using C language program.

3
00:00:08,660 --> 00:00:13,250
So I'm starting a new project for an interest in politics doubling.

4
00:00:14,210 --> 00:00:19,850
Allan, that is for linguist and the language selected to see language next three to project.

5
00:00:21,180 --> 00:00:23,970
Projects really go to the main function.

6
00:00:25,750 --> 00:00:31,900
Clear all these comments project is ready now for implementing the blindingly first of all, we have

7
00:00:31,900 --> 00:00:33,910
to define a structure for a node.

8
00:00:34,360 --> 00:00:37,410
So as we have already seen that it will have three members.

9
00:00:37,420 --> 00:00:45,610
First one has struck node and that is a pointer for the previous node and next to the data in type picking

10
00:00:45,920 --> 00:00:50,110
next is a struct node that is pointed to next to North.

11
00:00:50,260 --> 00:00:56,440
Then here I will take a pointer for first node that is first and initially assigned as null now as we

12
00:00:56,440 --> 00:01:00,970
have done for linear link cluster and so will include same way I will try to function for creating a

13
00:01:00,970 --> 00:01:01,200
link.

14
00:01:01,450 --> 00:01:06,040
It will take an array of elements and create a link list.

15
00:01:06,730 --> 00:01:08,280
So first array of elements.

16
00:01:08,320 --> 00:01:10,050
Then the second one is number of elements.

17
00:01:10,060 --> 00:01:17,440
Now here for creating a new node, I should have a pointer that is ten point thirty and also for last

18
00:01:17,440 --> 00:01:19,200
node I will take one pointer.

19
00:01:19,210 --> 00:01:24,280
Then I will need a variable like I for scanning through an array and if any more variables are required,

20
00:01:24,280 --> 00:01:25,540
then I will declare them on.

21
00:01:25,540 --> 00:01:30,670
The first thing I have to do is create a first node by using first pointer.

22
00:01:30,790 --> 00:01:33,550
So struct node for this node.

23
00:01:33,550 --> 00:01:41,840
Also I have given the function Ima's struct node, so size of struct nor a node will be created using

24
00:01:41,840 --> 00:01:42,730
mellark function.

25
00:01:43,030 --> 00:01:49,880
And one more thing I have to do is that I should include header file that is still not each.

26
00:01:49,960 --> 00:01:57,150
Then coming back to the create function, a node is created and enforced to data that in first data

27
00:01:57,160 --> 00:02:02,650
I should have elements from first location of an array that is in existence.

28
00:02:02,770 --> 00:02:11,620
Then I should set the pointer like previous as well as first point that is next to null.

29
00:02:12,790 --> 00:02:17,590
Because this is the only note and one more thing is I should set last as first.

30
00:02:17,740 --> 00:02:23,860
So last is also pointing out one footnote now with the help of this last point that I can continue creation

31
00:02:23,860 --> 00:02:29,890
on the rest of the north and go on joining them in the link list that I can do it using followed so

32
00:02:29,890 --> 00:02:37,120
far, I assign one onwards because already we have taken then I plus plus then inside for a loop.

33
00:02:37,120 --> 00:02:38,670
I should create a node every time.

34
00:02:38,680 --> 00:02:41,500
So all of the code is here for creating a new note.

35
00:02:41,500 --> 00:02:44,710
I will copy that code and pasted here.

36
00:02:44,950 --> 00:02:52,360
Then I should send the data of this new node as E of I and as I have to always insert a new node after

37
00:02:52,360 --> 00:03:00,850
the last node, then I should say these next should be same as last next and the previous should be

38
00:03:00,850 --> 00:03:02,260
same as last.

39
00:03:02,410 --> 00:03:09,130
Then last pointers next should be pointing upon P and after that I should make the last point upon,

40
00:03:09,130 --> 00:03:10,750
you know the E and that's all.

41
00:03:10,750 --> 00:03:14,230
These are the steps for adding a new node every time at the end of a link list.

42
00:03:14,350 --> 00:03:17,470
Then next I should have a function for displaying a linked list.

43
00:03:17,470 --> 00:03:24,310
So I will have a function display which will take struct, nor the pointer that is up on the first node.

44
00:03:24,310 --> 00:03:25,150
Let us be.

45
00:03:26,070 --> 00:03:34,200
And while he is not equal to none, it will be displaying the elements who display an element percentile

46
00:03:34,620 --> 00:03:36,120
and piece of data.

47
00:03:36,150 --> 00:03:39,180
Then he should be keep on moving up on Nixonland.

48
00:03:39,180 --> 00:03:43,800
And after printing all the elements, I should give a line gap so that I get some formatted output.

49
00:03:43,800 --> 00:03:46,780
I get some free space after the next one.

50
00:03:47,220 --> 00:03:52,020
Then likewise, I will also write on a function for finding the length of a list that is lent.

51
00:03:52,440 --> 00:03:54,000
It will get struck.

52
00:03:54,150 --> 00:03:56,610
Naude pointed to first node.

53
00:03:57,060 --> 00:04:00,830
Then here I will take one length variable that is initially zero.

54
00:04:01,290 --> 00:04:08,110
Now using my loop, I can travel through a link list by every time, increment England and also moving

55
00:04:08,340 --> 00:04:09,810
to the next normal.

56
00:04:09,900 --> 00:04:14,240
Then finally, at the end of the function, I should return lente.

57
00:04:14,250 --> 00:04:19,500
So yes, all the functions that we have been writing for Linell English and Circler linguists, we have

58
00:04:19,510 --> 00:04:20,459
written here also.

59
00:04:20,490 --> 00:04:25,230
Now inside the main function, I will create a link list and display its length and also display its

60
00:04:25,230 --> 00:04:25,890
elements.

61
00:04:25,920 --> 00:04:32,340
So first of all, I will take an array of elements like 10, 20, 30, 40 and 50.

62
00:04:32,340 --> 00:04:37,080
Then I will call a function created by passing Uhry and the number of elements are five.

63
00:04:37,080 --> 00:04:39,390
To create function should create all the elements.

64
00:04:39,410 --> 00:04:42,180
Then I will print the length of a linguist.

65
00:04:42,180 --> 00:04:50,580
So first and then I will say is what's in Tildy then also slash and then here I will call the function

66
00:04:50,580 --> 00:04:53,300
lenth bypassing first pointer.

67
00:04:53,420 --> 00:04:59,100
Then I will also call display function for displaying all the elements of wrinklies, bypassing first

68
00:04:59,100 --> 00:04:59,400
point.

69
00:04:59,700 --> 00:05:00,180
That's all.

70
00:05:00,180 --> 00:05:05,670
Main function is very little from the program here lenders' five and it is displaying all the elements

71
00:05:05,670 --> 00:05:08,030
that is and or indeed 30, 40 to 50.

72
00:05:08,040 --> 00:05:09,450
So everything is perfect.

73
00:05:10,440 --> 00:05:18,240
So back to this program, I'm providing the same program code as a PDF below this video, you can download

74
00:05:18,270 --> 00:05:24,060
this and if any help, you can look into this one or if you face any problem, then you can contact

75
00:05:24,060 --> 00:05:26,040
me and push it on suspicion, that's all.

