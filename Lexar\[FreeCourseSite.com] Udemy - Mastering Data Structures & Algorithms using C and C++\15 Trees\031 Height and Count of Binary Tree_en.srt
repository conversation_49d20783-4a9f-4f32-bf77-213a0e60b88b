1
00:00:00,630 --> 00:00:08,039
In this video, we learn about a function for counting lords in a binary tree, so already I have an

2
00:00:08,039 --> 00:00:13,310
example by <PERSON><PERSON><PERSON> and also a function available and this function is recursive.

3
00:00:13,830 --> 00:00:16,710
So just I will explain how this function works.

4
00:00:17,250 --> 00:00:22,980
Let us trace the function to understand the working of a function so that you can write down similar

5
00:00:22,980 --> 00:00:24,450
functions if you require.

6
00:00:24,630 --> 00:00:27,890
Let me read out this function and explain a few things.

7
00:00:27,900 --> 00:00:29,190
Then I will show you placing.

8
00:00:30,790 --> 00:00:36,630
Function name is Count, and I have taken some temporary variables, X and Y, these are local to a

9
00:00:36,640 --> 00:00:37,150
function.

10
00:00:39,100 --> 00:00:45,390
Point on the A.P., that should be a pointer if <PERSON>'s not null.

11
00:00:46,840 --> 00:00:48,640
If he's not killed, then.

12
00:00:50,410 --> 00:00:57,340
Calling itself upon the child, count itself upon right child, get the result of left Charlie next,

13
00:00:57,370 --> 00:01:04,989
get the result of right Charlie invite so to function calls and left shall also write also done for

14
00:01:04,989 --> 00:01:09,220
counting every Naude plus one should be added for every note like.

15
00:01:10,470 --> 00:01:15,830
One first or second or third note, one by one, you have to go on adding, so we're adding it's done

16
00:01:16,230 --> 00:01:21,270
whatever the left hand side, plus whatever the result of right hand side plus one.

17
00:01:22,290 --> 00:01:23,010
This Naude.

18
00:01:23,810 --> 00:01:30,430
Number of nodes on that site, number of nodes on side, then plus one, this not so, I have just read

19
00:01:30,430 --> 00:01:30,790
it out.

20
00:01:30,790 --> 00:01:36,430
So it makes sense that this is counting the number of nodes and it is doing recursively.

21
00:01:36,430 --> 00:01:40,130
It is calling itself Espy's not null.

22
00:01:40,270 --> 00:01:42,720
Do all these things if it is nothing else.

23
00:01:42,730 --> 00:01:47,440
But this is at this point, I don't have the right answer because if it's such as having written so

24
00:01:47,440 --> 00:01:53,250
I don't have to write even you can write Elveden inside delicateness return zero minutes.

25
00:01:53,350 --> 00:01:58,480
If there is no node, if it is none other than zero, then one important thing I have to show you.

26
00:01:58,480 --> 00:02:01,840
Counting of a node is done in this line and this statement.

27
00:02:01,840 --> 00:02:02,700
Just look at this.

28
00:02:03,830 --> 00:02:06,570
And before counting, it is going on left and right.

29
00:02:07,610 --> 00:02:10,870
So first left, then right, then count.

30
00:02:11,360 --> 00:02:12,090
I'll change it.

31
00:02:12,110 --> 00:02:12,740
Just listen.

32
00:02:13,220 --> 00:02:15,580
First left, then right, then print.

33
00:02:16,310 --> 00:02:17,030
What is this?

34
00:02:17,570 --> 00:02:18,770
First left, then right.

35
00:02:18,770 --> 00:02:19,300
Then print.

36
00:02:19,580 --> 00:02:20,590
This is post starter.

37
00:02:20,930 --> 00:02:21,560
Yes.

38
00:02:21,770 --> 00:02:25,250
This procedure of this function is working in post started form.

39
00:02:26,250 --> 00:02:28,030
This is working in post starter form.

40
00:02:28,910 --> 00:02:35,720
And one thing I will tell you what, all the processing you want to do upon binary trees, most of the

41
00:02:35,720 --> 00:02:38,960
time you have to use your starter form.

42
00:02:40,290 --> 00:02:45,540
So, for starters, more commonly used upon by Americans for doing any type of processing.

43
00:02:46,410 --> 00:02:50,880
So now let me raise the censure you so I'll you know, how recursive function works.

44
00:02:50,910 --> 00:02:53,950
I will not spend much time quickly having to dressing.

45
00:02:54,270 --> 00:02:56,490
So I'm showing you dressing up on three directly.

46
00:02:56,640 --> 00:03:01,560
So the root function is called upon root, then root is not gnarliest.

47
00:03:01,560 --> 00:03:08,670
Not one of the steps when it is not three steps are did find X find way then add them plus one and they're

48
00:03:08,670 --> 00:03:08,970
done.

49
00:03:09,300 --> 00:03:10,530
So first find X.

50
00:03:10,650 --> 00:03:11,550
Find X.

51
00:03:12,680 --> 00:03:19,340
Karl on the left child first finished first step, then you go on second step, right, first finish.

52
00:03:19,340 --> 00:03:23,900
First a step, first step going left chairmanship will come on the snored upon this note.

53
00:03:23,900 --> 00:03:29,240
This is not so again, exercising, exercising, collarbone, left child.

54
00:03:29,360 --> 00:03:31,100
Whatever the result you get, you pick it up.

55
00:03:31,160 --> 00:03:32,240
It's OK.

56
00:03:32,270 --> 00:03:33,580
I'll give you this card here.

57
00:03:33,890 --> 00:03:37,340
So this is not null on the left side exercise.

58
00:03:39,310 --> 00:03:46,020
Call upon left side, left side is what Nullum here, left side is not here, next side annulments.

59
00:03:46,030 --> 00:03:48,570
What is the result from this one zero?

60
00:03:48,730 --> 00:03:50,650
If it is not null, it is entering here.

61
00:03:50,830 --> 00:03:52,860
If it is not, then it will return zero.

62
00:03:52,990 --> 00:03:55,300
So from the side the result will be zero.

63
00:03:55,320 --> 00:03:58,330
I'll just extend and show you the side result will be zero.

64
00:03:58,510 --> 00:04:00,060
So this X will continue.

65
00:04:01,990 --> 00:04:07,540
It has scarred itself on the left side, which was none, so the value of X is zero for which note,

66
00:04:07,720 --> 00:04:08,640
nor the 12.

67
00:04:09,410 --> 00:04:18,100
So first step is finished fourth in order to well, then perform second step Y equals to right y equals

68
00:04:18,100 --> 00:04:19,390
to right chain.

69
00:04:19,690 --> 00:04:20,570
There is a node.

70
00:04:20,709 --> 00:04:23,560
So for this new conference call.

71
00:04:23,650 --> 00:04:31,040
So again, for this node X X A left Chaiten, none for this left child.

72
00:04:31,280 --> 00:04:32,610
Again it's not known.

73
00:04:32,770 --> 00:04:35,310
So X assign left child.

74
00:04:35,530 --> 00:04:37,420
So for this left nothing is there.

75
00:04:37,420 --> 00:04:38,370
NULL zero.

76
00:04:38,380 --> 00:04:40,000
So this will be zero then four.

77
00:04:40,000 --> 00:04:41,520
Right for right.

78
00:04:41,530 --> 00:04:42,520
Also it is zero.

79
00:04:42,910 --> 00:04:46,180
Now for this node nine to step are our first step.

80
00:04:46,180 --> 00:04:50,020
Second step then third step X plus plus one.

81
00:04:51,640 --> 00:04:59,980
Zero plus zero plus one is turn, it will go to Disney World, in which variable X because it was called

82
00:04:59,980 --> 00:05:06,350
on the left side one, then on the side Y for the side X, there is nothing, none.

83
00:05:06,370 --> 00:05:13,630
So zero five zero zero zero plus one one is written not for this nor the left hand side X value.

84
00:05:13,630 --> 00:05:16,600
Also it knows y also n to see.

85
00:05:16,600 --> 00:05:18,390
I said it is Infostrada right.

86
00:05:18,580 --> 00:05:28,000
So this left, right completed then route one plus one plus one three C this is giving the number of

87
00:05:28,000 --> 00:05:29,080
nodes below this one.

88
00:05:30,130 --> 00:05:36,520
Now this is really zero plus the three three plus one for the for the right side.

89
00:05:36,610 --> 00:05:38,130
Why this is zero.

90
00:05:38,140 --> 00:05:39,370
There is nothing on the side.

91
00:05:39,520 --> 00:05:41,350
None of them go back.

92
00:05:41,350 --> 00:05:47,160
Four plus one equals zero plus one five five C below this check.

93
00:05:47,290 --> 00:05:53,980
One, two, three, four, five five nodes are there then go on the right hand side, then up on this

94
00:05:53,980 --> 00:06:00,490
one left hand side up on this one left hand side, null zero eight eight zero zero zero plus one one

95
00:06:00,490 --> 00:06:01,810
goes back to this one.

96
00:06:03,200 --> 00:06:12,770
Then on the side of my left side X left side X zero one zero zero zero one zero zero zero plus one goes

97
00:06:12,770 --> 00:06:14,720
back one this side.

98
00:06:14,720 --> 00:06:15,650
Why a zero?

99
00:06:15,980 --> 00:06:22,700
Now these two are added and return one for one plus one plus one, two, two, one, two, three, three

100
00:06:22,700 --> 00:06:28,910
plus one four nine five plus four nine plus one 10 Sadun said it's ten.

101
00:06:29,180 --> 00:06:30,620
So that's all this is working.

102
00:06:30,620 --> 00:06:37,910
Infostrada I'll make one small change in the score right then you have to tell me what this function

103
00:06:37,910 --> 00:06:39,310
will do here.

104
00:06:39,500 --> 00:06:43,430
I will write on the statement if I pick up a child.

105
00:06:45,050 --> 00:06:53,600
And pick up our child, this piece, the left childless there is not null and void.

106
00:06:53,630 --> 00:06:59,690
Child is also not null, then written explicitly plus one.

107
00:07:01,420 --> 00:07:12,590
And it's written explicitly then this offense, this if and at last read on the zero.

108
00:07:14,150 --> 00:07:21,540
Yes, what is this condition, this condition is for both left and right are dead.

109
00:07:21,860 --> 00:07:25,310
So it means if both the children are there, then only it will count one.

110
00:07:25,520 --> 00:07:27,200
Otherwise, one is not counted.

111
00:07:27,680 --> 00:07:28,830
That is not counted.

112
00:07:28,850 --> 00:07:33,020
So it will count only those Norns who are having both the children.

113
00:07:33,410 --> 00:07:37,430
So Nords, with the degree to from this tree with notes, will be counted.

114
00:07:37,790 --> 00:07:40,460
Eight is having both the children for is having the children.

115
00:07:40,760 --> 00:07:42,220
Five is having both the children.

116
00:07:42,230 --> 00:07:44,540
So only three notes will be counted.

117
00:07:44,720 --> 00:07:50,270
So this will count the number of nodes which are having both the children left side as well as the right

118
00:07:50,280 --> 00:07:50,620
side.

119
00:07:51,140 --> 00:07:57,080
I are introduced one condition and it became a condition for counting and also the degree to change

120
00:07:57,080 --> 00:08:01,970
the condition can leave, change the condition to count nodes with the degree one.

121
00:08:03,600 --> 00:08:08,540
Whatever you like, you can go next, I will make one more change written.

122
00:08:09,090 --> 00:08:12,060
There are no else explores why.

123
00:08:12,070 --> 00:08:13,920
Plus visa data.

124
00:08:14,130 --> 00:08:15,450
Can you tell me what will happen?

125
00:08:15,610 --> 00:08:17,280
These data is that it is.

126
00:08:17,280 --> 00:08:24,570
It will add all these elements from we will get the sum of all the elements in the train, then student

127
00:08:24,570 --> 00:08:29,310
exercise here on modifying the code so we don't know what the function is doing.

128
00:08:29,520 --> 00:08:29,940
OK.

129
00:08:31,110 --> 00:08:38,039
Function name, I will call it as fun, and this is fun, is calling itself fun, is calling itself

130
00:08:38,039 --> 00:08:48,510
I'm modifying it effects is greater than Y written X plus one else written Y plus one.

131
00:08:49,200 --> 00:08:52,500
Take this and find out what this function is doing.

132
00:08:53,520 --> 00:08:53,910
Like.

133
00:08:54,810 --> 00:09:01,150
What this function is doing is not adding X and Y, but either explicit one or lightless one.

134
00:09:01,530 --> 00:09:07,410
So for understanding or knowing what that function is doing, you have to place it upon a tree and observe

135
00:09:07,410 --> 00:09:07,530
you.

136
00:09:07,930 --> 00:09:08,870
Then you can find out.

137
00:09:08,880 --> 00:09:10,950
So, for example, you can take this tree that.

138
00:09:12,290 --> 00:09:17,570
If you take just two or three loans, you may not be able to figure out what it is doing this type of

139
00:09:17,570 --> 00:09:20,270
tree and trace that one and find out.

140
00:09:20,600 --> 00:09:24,160
So that's all we have seen various functions upon a tree.

141
00:09:24,710 --> 00:09:29,420
I have started with accounting, but have also shown you different functions on this one.

