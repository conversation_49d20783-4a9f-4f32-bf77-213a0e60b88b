1
00:00:00,570 --> 00:00:02,740
This topic is about expanding trees.

2
00:00:02,760 --> 00:00:07,680
So in this video, I'll explain to you, what does it mean by expanding tree and what does it mean by

3
00:00:08,010 --> 00:00:09,690
minimum conscious planning tree?

4
00:00:10,170 --> 00:00:16,680
Then further, we have the topics like Sprint's algorithm for finding minimum cost planning and Kruskal

5
00:00:16,680 --> 00:00:19,140
algorithm for finding minimum cost of spending.

6
00:00:19,680 --> 00:00:21,650
We will study this in the next videos.

7
00:00:22,680 --> 00:00:30,690
So in this video, let us understand what is spanning, spanning trees, a graph of a graph having all

8
00:00:30,690 --> 00:00:35,850
what is as of a graph like here I have for what this is all for what it says, but.

9
00:00:36,800 --> 00:00:43,940
And the minus one, it just means the number of words is minus one, it just so that there is no cycle

10
00:00:44,420 --> 00:00:49,730
formed by those edges and growth must be connected to define it formally.

11
00:00:50,120 --> 00:00:51,200
Like this is a graph.

12
00:00:51,620 --> 00:00:58,890
G is very difficult to read and this is the number of words, as we call it.

13
00:00:59,090 --> 00:01:02,650
And and this is a number of edges, we call it as E.

14
00:01:04,430 --> 00:01:06,340
Now what is this planning to be spanning three?

15
00:01:06,340 --> 00:01:09,020
If I say this is the spanning tree, then.

16
00:01:10,300 --> 00:01:13,000
It is a graph of a graph.

17
00:01:14,210 --> 00:01:21,530
We can find a spinal injury only if is connected, then this spanning tree should have set off vertices

18
00:01:22,940 --> 00:01:31,670
and edges where number of what says are Seema's number of this disease and the number of edges.

19
00:01:33,550 --> 00:01:41,110
Should be equal to number of it, minus one number of what is this total versus minus one, and this

20
00:01:41,110 --> 00:01:42,210
should not be in this cycle.

21
00:01:43,690 --> 00:01:48,010
So this will all formal definitions now let us understand it in simple terms.

22
00:01:48,340 --> 00:01:52,180
So for this graph, I will show you what does it mean by expanding trade?

23
00:01:53,020 --> 00:01:55,140
This graph is having for it us.

24
00:01:55,210 --> 00:02:02,590
So I must take all for what it says, OK, for what the society can then other for what it says.

25
00:02:02,590 --> 00:02:04,620
I should take only three X.

26
00:02:06,030 --> 00:02:08,960
One, two, three, total.

27
00:02:09,000 --> 00:02:09,680
There are six.

28
00:02:09,729 --> 00:02:13,200
I just have to go and only three does a spinal injury.

29
00:02:14,370 --> 00:02:15,650
I'll take one more example.

30
00:02:16,050 --> 00:02:19,020
One, two, three and four.

31
00:02:20,080 --> 00:02:21,600
I'll connect them like this.

32
00:02:22,560 --> 00:02:25,840
These three images have taken is it also apparently.

33
00:02:25,860 --> 00:02:29,300
Yes, this is a stunning photograph, him even.

34
00:02:29,330 --> 00:02:30,660
This is also spanning three.

35
00:02:30,980 --> 00:02:33,260
It means more than one spanning desert possible.

36
00:02:33,570 --> 00:02:34,080
Yes.

37
00:02:34,080 --> 00:02:35,900
More than one spanning trees are possible.

38
00:02:36,210 --> 00:02:37,260
I will draw one more.

39
00:02:42,230 --> 00:02:50,270
For what is this, I have taken three and just one, two, three, yes, as it is, but we know it is

40
00:02:50,270 --> 00:02:51,100
having a cycle.

41
00:02:51,320 --> 00:02:52,310
It's not responding.

42
00:02:52,880 --> 00:02:56,210
And you can see that there is a vortex three, which is not connected.

43
00:02:56,690 --> 00:03:00,070
So it should be connected and it should not have any cycle.

44
00:03:00,320 --> 00:03:01,810
Then we say it's a spanning tree.

45
00:03:02,270 --> 00:03:03,620
This is not a spanning tree.

46
00:03:04,310 --> 00:03:05,420
Then I draw a few more.

47
00:03:07,290 --> 00:03:12,900
See, these are also spinal injuries, so various spinal injuries are possible for a given graph.

48
00:03:13,620 --> 00:03:16,880
Can we calculate how many spinal injuries are possible photograph?

49
00:03:17,310 --> 00:03:22,530
Let us see how many what is out there for what then?

50
00:03:22,530 --> 00:03:23,640
How many are just out there?

51
00:03:24,900 --> 00:03:27,690
Six inches, then for four more.

52
00:03:28,060 --> 00:03:32,710
How many are this must be there just three or just must be there for minus one.

53
00:03:33,030 --> 00:03:39,960
So from the set of trenches that a set of edges I should select for minus one inches.

54
00:03:40,230 --> 00:03:41,040
So see.

55
00:03:42,150 --> 00:03:45,480
B minus one, that is four, minus one.

56
00:03:46,620 --> 00:03:50,050
How many ways I can select that many spinal injuries are possible.

57
00:03:50,460 --> 00:03:54,750
So out of six for the first three quarters, this can be selected in how many ways?

58
00:03:54,900 --> 00:03:58,360
So six C three weeks.

59
00:03:58,380 --> 00:04:03,090
So, for example, graph it is six C three is.

60
00:04:03,960 --> 00:04:04,370
Yes.

61
00:04:04,390 --> 00:04:08,520
With this I can get answer, but let us see something.

62
00:04:10,070 --> 00:04:17,480
If I take like this without labels, I'm showing here, this is also three inches, but this is forming

63
00:04:17,480 --> 00:04:18,050
a cycle.

64
00:04:20,089 --> 00:04:24,990
This is also a just by forming a cycle two cycles from this one.

65
00:04:25,010 --> 00:04:27,050
If I take this one, two and three.

66
00:04:27,050 --> 00:04:28,360
So this is one, two and three.

67
00:04:28,670 --> 00:04:30,140
This is one, two and four.

68
00:04:30,920 --> 00:04:34,360
Then if I take this, this will also form a cycle.

69
00:04:34,790 --> 00:04:35,450
Yes.

70
00:04:35,990 --> 00:04:38,150
And even this will form a cycle.

71
00:04:39,590 --> 00:04:45,920
So one, two, three, four, four cycles are possible, I should not take those set of ideas which

72
00:04:45,920 --> 00:04:49,580
are forming cycles so total for I could find out.

73
00:04:49,910 --> 00:04:50,230
Right.

74
00:04:50,480 --> 00:04:52,400
And there are four cycles in this one.

75
00:04:52,700 --> 00:04:57,470
And those cycles are formed by three edges, not more than three.

76
00:04:57,510 --> 00:05:02,540
Just because we are selecting maximum three ages, whatever the number of ideas we have selecting,

77
00:05:02,540 --> 00:05:05,840
we should see that a cycle is formed using those.

78
00:05:05,840 --> 00:05:08,010
Many are just for less number of factors also.

79
00:05:08,510 --> 00:05:13,860
So find out all those cycles, not more than three or just and subtract.

80
00:05:14,360 --> 00:05:16,340
So minus cycles.

81
00:05:17,180 --> 00:05:18,440
So minus four.

82
00:05:19,280 --> 00:05:20,910
Twenty minus four.

83
00:05:20,930 --> 00:05:24,140
So this gives 16 spanning threes.

84
00:05:24,300 --> 00:05:28,310
So yes, for this graph, 16 spanning trees are possible.

85
00:05:28,730 --> 00:05:32,300
So once again, I repeat, how many are we have then?

86
00:05:32,300 --> 00:05:37,370
How many are just we have to select so they can be selected in sixty three ways for us.

87
00:05:37,370 --> 00:05:45,070
But our example, minus how many cycles are formed by using three or less are just so out of three adults

88
00:05:45,080 --> 00:05:48,020
we found there after three cycles possible.

89
00:05:48,170 --> 00:05:52,460
So that four I have subtracted and I got 16 different spy entries.

90
00:05:52,730 --> 00:05:56,240
So if I generate all I can generate 16 different spanning entries.

91
00:05:56,420 --> 00:05:57,800
I have taken only four here.

92
00:05:58,340 --> 00:06:00,290
This was not a Spanish no.

93
00:06:00,590 --> 00:06:03,860
Next thing I will explain, what does it mean by minimum cost?

94
00:06:03,870 --> 00:06:04,530
Spangly.

95
00:06:04,730 --> 00:06:09,560
So for explaining minimum cost Manningtree, I have given rates to the edges of a graph.

96
00:06:10,830 --> 00:06:16,680
The weight for age from one to two is two and two to four one, so I have given the weight.

97
00:06:17,660 --> 00:06:25,400
Now, let us understand the problem we want the spanning three like this, such that the cost of total

98
00:06:25,400 --> 00:06:26,890
Spanish entry must be minimal.

99
00:06:27,260 --> 00:06:33,410
So cost means the total wage of all the edges that are included in expanding pay.

100
00:06:33,860 --> 00:06:35,330
So already you have few Spanish.

101
00:06:35,690 --> 00:06:39,630
Let us give those visas to this and check what is the cost.

102
00:06:40,400 --> 00:06:41,960
So this cost us too.

103
00:06:42,120 --> 00:06:43,100
And this is fine.

104
00:06:43,410 --> 00:06:44,360
And this is three.

105
00:06:44,570 --> 00:06:46,460
So total cost to stand for.

106
00:06:46,460 --> 00:06:47,300
This is three.

107
00:06:47,660 --> 00:06:49,090
And for this one, this is five.

108
00:06:49,100 --> 00:06:50,420
This is six and three.

109
00:06:50,810 --> 00:06:51,920
So this is 14.

110
00:06:54,290 --> 00:07:02,740
And for this, it is fine, and this are just for and just six, so this is 15, total cost is 15.

111
00:07:02,750 --> 00:07:04,010
So we are getting different cost.

112
00:07:04,040 --> 00:07:05,240
You can see Sawtell here.

113
00:07:05,540 --> 00:07:09,610
This looks like smallest then this one, too.

114
00:07:09,950 --> 00:07:10,880
And this is one.

115
00:07:11,480 --> 00:07:12,260
This is one.

116
00:07:12,260 --> 00:07:13,090
And this is three.

117
00:07:13,550 --> 00:07:15,220
So this is six.

118
00:07:15,230 --> 00:07:16,610
Oh, this is more smaller.

119
00:07:18,630 --> 00:07:25,200
So I did not try all 16 spanning trees just have taken four and out of these four, I got a minimum

120
00:07:25,210 --> 00:07:26,750
quarter spanning three years, this one.

121
00:07:27,180 --> 00:07:29,400
So maybe this is the minimum cost or.

122
00:07:30,430 --> 00:07:35,100
12 more are possible, I should try out all of them and select the best one, right.

123
00:07:35,440 --> 00:07:36,790
So that's the problem.

124
00:07:36,850 --> 00:07:39,010
We want a minimum cost spanning.

125
00:07:40,120 --> 00:07:46,070
So there are some algorithms like provincial government algorithm for finding minimum cost, apparently

126
00:07:46,120 --> 00:07:46,930
that we will learn.

127
00:07:46,930 --> 00:07:48,580
And next, videos, deals.

128
00:07:48,910 --> 00:07:50,130
So that's all in this video.

