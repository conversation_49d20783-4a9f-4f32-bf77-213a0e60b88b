1
00:00:00,520 --> 00:00:06,840
Now, let us write a function for finding the sum of all these elements.

2
00:00:06,880 --> 00:00:12,960
I want to add all these elements one by one, eight plus three plus seven plus 12 and so on.

3
00:00:13,690 --> 00:00:18,610
So I don't have to explain much because already we have learned in the previous videos just we have

4
00:00:18,610 --> 00:00:19,640
to see the function.

5
00:00:20,470 --> 00:00:26,590
So let us write a function for adding all these values so far, adding I should traverse all these numbers.

6
00:00:26,620 --> 00:00:30,790
So we know very well that we need a pointer for traversing all the notes.

7
00:00:30,790 --> 00:00:36,910
That point I should start from first node, so I will call the function name as ADD and it takes a pointer

8
00:00:36,910 --> 00:00:39,880
to know the type structure node.

9
00:00:40,930 --> 00:00:48,730
<PERSON>boy, so this must be first norder let the return diabete integer, so it means I'm going to call

10
00:00:48,730 --> 00:00:57,230
this function by passing IG first so it will be taking a pointer to <PERSON><PERSON><PERSON> to be appointed to do not

11
00:00:58,630 --> 00:00:59,970
know what I should do here.

12
00:01:00,130 --> 00:01:05,470
I should travel through all the laws and I'll go on adding the value for adding the value.

13
00:01:05,560 --> 00:01:10,900
I should have one variable, let us call it as some and some is zero.

14
00:01:12,100 --> 00:01:13,790
How to traverse all these.

15
00:01:13,840 --> 00:01:19,920
So using a loop we have been doing this will be so I can write like this.

16
00:01:19,970 --> 00:01:29,830
This means be not equal to nul and every time what I should do some assign some plus B capsid data or

17
00:01:29,830 --> 00:01:30,900
piece of data.

18
00:01:30,970 --> 00:01:36,640
So I should add first time it will be added to this one and next time three will be added for like that.

19
00:01:36,640 --> 00:01:38,040
I want all of them to be added.

20
00:01:38,050 --> 00:01:42,640
So for that I should move to the next note, be assigned B's next.

21
00:01:43,420 --> 00:01:49,450
So if you remember in the previous examples also we have seen that while Loop and this is statement

22
00:01:49,810 --> 00:01:53,980
assigned peaceniks, this will be making a move on all the N one by one.

23
00:01:54,290 --> 00:01:56,530
So it will help us to traverse the.

24
00:01:57,310 --> 00:02:04,210
So what I am doing while traversing just go on adding the data in some so I will get the sum of all

25
00:02:04,210 --> 00:02:06,610
the elements in this variable sum.

26
00:02:06,970 --> 00:02:09,610
So finally return some.

27
00:02:11,310 --> 00:02:16,560
The analysis of dysfunction function the same, it is Bodrov and because it's visiting all the nodes

28
00:02:16,560 --> 00:02:17,310
just once.

29
00:02:18,030 --> 00:02:22,740
Now let me lay down the same function for finding the sum of all the elements using recursion.

30
00:02:23,430 --> 00:02:29,850
I will call the function names the same function I am picking and sticking a barometer.

31
00:02:29,850 --> 00:02:38,130
Struct Nord Point B, then it should return integer reason because all these elements are integer in

32
00:02:38,130 --> 00:02:38,910
our example.

33
00:02:40,920 --> 00:02:46,110
Now, in the recursive methodology we have seen in counting in the previous video, similar approach

34
00:02:46,110 --> 00:02:46,810
we have to take.

35
00:02:47,070 --> 00:02:49,770
So here I was right on the termination condition.

36
00:02:50,070 --> 00:02:55,270
If a B is equal to zero, that is null, then return zero.

37
00:02:55,290 --> 00:02:57,990
So if it is null means we don't have to add anything.

38
00:02:58,320 --> 00:03:08,700
If these elements nothing is there, there is no element so zero otherwise else written and function

39
00:03:08,700 --> 00:03:12,270
will call itself up on next node.

40
00:03:12,750 --> 00:03:17,600
So this is next node plus the data, also the current node.

41
00:03:17,850 --> 00:03:21,680
So plus piece of data B's data.

42
00:03:24,530 --> 00:03:31,670
So this is a recursive function, it is similar to Gowland function only, except instead of adding

43
00:03:31,670 --> 00:03:39,160
one here, I'm adding pieces of data so that of every node will be added in the column function.

44
00:03:39,170 --> 00:03:43,990
It was adding one every time, but here it is adding data so the working will be the same.

45
00:03:44,390 --> 00:03:46,430
And already we are familiar with recursion.

46
00:03:46,730 --> 00:03:47,930
It uses a stack.

47
00:03:49,040 --> 00:03:54,280
So it's going through all the rules, so time is order and the stock space is also out of hand.

48
00:03:54,590 --> 00:03:57,400
So the time and space of this function sort of and.

