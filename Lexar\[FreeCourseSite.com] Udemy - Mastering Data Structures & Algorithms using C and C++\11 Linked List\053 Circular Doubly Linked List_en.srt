1
00:00:00,700 --> 00:00:04,090
In this video, we learn about circle of little interest.

2
00:00:04,330 --> 00:00:07,740
This is a double increase and also it is circular.

3
00:00:08,200 --> 00:00:12,970
That is this first not our head, nor this pointing on the last note or last note is pointing on the

4
00:00:12,970 --> 00:00:13,490
first note.

5
00:00:13,780 --> 00:00:20,050
So these are a collection of DoubleLine Ling the N, which are circularly connected.

6
00:00:21,390 --> 00:00:28,290
So already we saw circular similar interests, this is w linguist's, so the benefit of this link is

7
00:00:28,300 --> 00:00:33,300
this you can access in either direction by direction as well as circularly.

8
00:00:33,300 --> 00:00:34,140
We can access.

9
00:00:35,310 --> 00:00:38,950
So so far, we have seen various types of Linkous competitions.

10
00:00:38,970 --> 00:00:44,340
We will do it in some other medium, so let us learn a few things about <PERSON><PERSON><PERSON>.

11
00:00:45,390 --> 00:00:49,170
So this is basically a DoubleLine recently, so I don't have to explain much.

12
00:00:49,440 --> 00:00:51,660
I'll just give you a few important points.

13
00:00:52,080 --> 00:00:54,610
And rest is a student exercise.

14
00:00:55,020 --> 00:00:56,790
So let us look at some operations.

15
00:00:57,780 --> 00:01:00,150
Like, first of all, I want to traverse this.

16
00:01:00,390 --> 00:01:07,380
So for Transversing, I may be starting from Hadnot, then I can go to next and next and next Noad and

17
00:01:07,380 --> 00:01:07,980
next note.

18
00:01:08,370 --> 00:01:10,770
And again, if I say next, I'll be on head.

19
00:01:12,380 --> 00:01:22,010
So how we have handled singly, so the same way we should handle it, we have to scan it all the nodes

20
00:01:22,280 --> 00:01:26,200
and once we reach to head once again, then we should stop.

21
00:01:27,020 --> 00:01:31,190
If I want to display legalist, how the code should look like, let us see.

22
00:01:33,780 --> 00:01:45,260
I should take a pointed upon head and hold them for printing print the data move pointed to next note.

23
00:01:46,560 --> 00:01:50,670
So printing and moving, I have to do it repeatedly using the white.

24
00:01:52,650 --> 00:01:55,820
Do while P is not equal to her.

25
00:01:58,030 --> 00:02:02,560
So if you remember in subclassing little list, also we have yours.

26
00:02:02,600 --> 00:02:04,730
Do I look because we're using my look?

27
00:02:04,750 --> 00:02:05,760
It was not possible.

28
00:02:06,220 --> 00:02:08,080
So the same thing we have to do here.

29
00:02:08,090 --> 00:02:10,780
Also notice from the operations are similar.

30
00:02:11,290 --> 00:02:15,420
Now, let me tell you that few important things related to insert operation.

31
00:02:16,240 --> 00:02:18,070
So let us look at insert operation.

32
00:02:19,420 --> 00:02:25,090
If they want to insert any Naude at a given position, let us say this is Forslund second or third,

33
00:02:25,100 --> 00:02:27,960
not fourth, nor do I want to insert a new note after footnote.

34
00:02:28,420 --> 00:02:35,340
Then the procedure, the same as how we have inserted in the that is bring your pointer upon this node,

35
00:02:35,770 --> 00:02:43,880
create a new Norm Filaret to make this point on this one and this previous point on this one.

36
00:02:44,200 --> 00:02:48,150
So this next should point here and this previous should point here.

37
00:02:48,520 --> 00:02:52,410
So for links, I have commodify that I am inserting any node again in place.

38
00:02:52,420 --> 00:02:55,060
So the procedure, the same procedure.

39
00:02:57,010 --> 00:03:00,760
Then how about inserting a new note before heading north?

40
00:03:01,000 --> 00:03:06,190
So for this, I should take a pointer and create a new note and fill the data here.

41
00:03:06,880 --> 00:03:11,950
Then what are the modifications I have to do in a double inkless?

42
00:03:12,220 --> 00:03:16,890
I was supposed in English, we were making only three changes.

43
00:03:16,900 --> 00:03:18,250
These feelings would change.

44
00:03:18,580 --> 00:03:25,510
But here I have to modify this last note link also, instead of pointing on header here, it should

45
00:03:25,510 --> 00:03:26,350
point on this one.

46
00:03:27,370 --> 00:03:28,680
So there's the thing you have to do.

47
00:03:30,030 --> 00:03:36,660
So it means I should modify for links, so, yes, the important thing here is whenever you insert in

48
00:03:36,660 --> 00:03:41,060
the circle or the willingness you have to modify for links.

49
00:03:41,520 --> 00:03:44,730
So let us see how I can do that and how much time it takes.

50
00:03:46,020 --> 00:03:47,340
This point on had.

51
00:03:49,200 --> 00:03:55,490
And this should point, Don, had the previous months that not eight, so I will draw a link from here.

52
00:03:58,070 --> 00:04:03,210
So how I can get there less of that, nor do I have to traverse the whole linked list?

53
00:04:03,240 --> 00:04:10,900
No, I can say heads the previous that is has previous so I can get the gist of that node in Constanta

54
00:04:10,910 --> 00:04:11,410
time.

55
00:04:11,810 --> 00:04:15,280
I don't have to scan through this link and reach there.

56
00:04:15,770 --> 00:04:17,500
That's the important point here.

57
00:04:17,750 --> 00:04:23,270
If you want to reach that node, that node is actually we are calling it s last node.

58
00:04:23,450 --> 00:04:29,190
So we can also call it as a node previous to had a. a..

59
00:04:29,210 --> 00:04:30,290
Previous to her node.

60
00:04:30,560 --> 00:04:35,240
Because if you take a previous pointer, you will get the idea of that norm so that we can get it in

61
00:04:35,240 --> 00:04:36,060
Konstantine.

62
00:04:37,040 --> 00:04:43,280
So for adding a new node before had node, the time will be constant because you don't have to scan

63
00:04:43,280 --> 00:04:45,320
through the linked list for reaching that last point.

64
00:04:45,740 --> 00:04:47,330
I will complete other links also.

65
00:04:47,600 --> 00:04:53,300
So this is pointing on this one and the previous is pointing on that note and heads the next this.

66
00:04:54,230 --> 00:04:55,460
Should be pointing on.

67
00:05:00,330 --> 00:05:08,210
The snowed and under eight hurricanes each on that note, these previous already I have a little bit,

68
00:05:08,460 --> 00:05:10,500
so that should be pointing on these.

69
00:05:13,230 --> 00:05:19,590
So now you can see that from day I'll be going to this node six, then six to nine nine two two two

70
00:05:19,590 --> 00:05:23,970
two seven seven to eight, eight to two again.

71
00:05:24,780 --> 00:05:27,530
So, yes, this is connected circularly.

72
00:05:27,900 --> 00:05:33,070
So how much time it has taken, just create a node and modify the links so the time is constant.

73
00:05:33,390 --> 00:05:38,520
So if you are in setting a new node before had node, time is constant and one important property we

74
00:05:38,530 --> 00:05:43,050
learn is that to go on that node, we don't have to scan for the link list.

75
00:05:43,950 --> 00:05:47,130
Just take a previous pointer and you can go there, that all.

76
00:05:47,370 --> 00:05:51,610
Similarly, you can try out delete operation by yourself.

77
00:05:51,780 --> 00:05:56,540
I have flown in such an operation that is sufficient delay to work on it.

78
00:05:56,970 --> 00:05:59,300
It's very easy then few more important things.

79
00:05:59,310 --> 00:06:07,170
I'll tell you if suppose there is a single node header, there's a single node, suppose the value is

80
00:06:07,170 --> 00:06:12,750
six, then how it should look like this next should also point on some previous should also point on

81
00:06:12,750 --> 00:06:13,320
itself.

82
00:06:14,290 --> 00:06:15,730
So then it is circular.

83
00:06:17,560 --> 00:06:23,200
And if there is a no, no, not at all, then Lincolnesque is empty, then it should be no.

84
00:06:24,390 --> 00:06:30,540
If you remember from subclassing living there, the problem was, if it is not also we wanted it to

85
00:06:30,540 --> 00:06:31,420
be circular.

86
00:06:31,680 --> 00:06:36,710
So if that is the case, then you should have one more domino that is heading empty node.

87
00:06:37,260 --> 00:06:39,590
So anyway, I don't want to include that one here.

88
00:06:40,830 --> 00:06:46,590
So this topic remains as an exercise for you, so you prepare the complete code for all the options

89
00:06:46,590 --> 00:06:47,110
on this one.

90
00:06:47,370 --> 00:06:53,960
So basically many important operations are insert and delete, complete insert operation and delete

91
00:06:53,970 --> 00:06:54,540
operation.

92
00:06:58,210 --> 00:07:00,940
These operations, you must write on complete code.

93
00:07:01,150 --> 00:07:05,710
So what are the cases in setting beforehand or inserting after any other coalition?

94
00:07:05,720 --> 00:07:07,240
All those cases you should take care.

95
00:07:07,900 --> 00:07:10,370
So that's all about circular delinquents.

96
00:07:10,900 --> 00:07:18,310
So in one of the upcoming videos, I'll compare all the lists and I will discuss the advantages and

97
00:07:18,310 --> 00:07:20,320
disadvantages of those linguist.

