1
00:00:01,320 --> 00:00:05,939
And this video will write a program for a creative version of much thought.

2
00:00:07,460 --> 00:00:08,900
That is using Loopt.

3
00:00:10,940 --> 00:00:17,210
So already we have used a project called Shotting and we have written all the sorting procedures inside

4
00:00:17,210 --> 00:00:17,940
the same project.

5
00:00:18,320 --> 00:00:20,170
So in the same project, I will write on the.

6
00:00:21,130 --> 00:00:27,820
Functions for much thought, so for merging already we have seen that we have to write a function for

7
00:00:27,820 --> 00:00:28,980
Marje separately.

8
00:00:29,260 --> 00:00:36,580
So first of all, I will write a function for March that is merging to list from a single uhry.

9
00:00:36,760 --> 00:00:40,360
So it needs the three parameters that is low, mid.

10
00:00:41,490 --> 00:00:48,630
And height, these are the three indexes, and the first parameter is the study that I need, the variables

11
00:00:48,630 --> 00:00:51,060
like <PERSON>J<PERSON> and Key.

12
00:00:53,950 --> 00:01:02,110
And also, we need Uhry be that is auxillary Oraibi of some size, so instead of deciding how much I

13
00:01:02,110 --> 00:01:05,760
need and all, I will take some maximum size temporarily.

14
00:01:06,430 --> 00:01:12,580
So if at all you want to decide the size, then you can up if at all.

15
00:01:12,580 --> 00:01:17,830
You want to judge what the size required and you can declare the area of required size.

16
00:01:18,490 --> 00:01:19,120
Not here.

17
00:01:19,120 --> 00:01:20,890
Just to make it easy and simple.

18
00:01:20,890 --> 00:01:22,960
I have made it 100 size.

19
00:01:24,480 --> 00:01:31,590
Now we have to run a while loop that is reaching, that is comparing and copying the elements while

20
00:01:31,590 --> 00:01:33,450
I use less than or equal to.

21
00:01:34,750 --> 00:01:36,760
Mader and.

22
00:01:38,510 --> 00:01:41,210
Jay is less than or equal to hide.

23
00:01:42,770 --> 00:01:51,020
And every time we should check the elements, that is if if I if it is less than E of G, then.

24
00:01:52,280 --> 00:01:58,520
In the B of K plus plus, we should copy an element from AOF, I love.

25
00:01:58,580 --> 00:01:59,150
Plus.

26
00:02:01,560 --> 00:02:08,880
Otherwise, it would be of Kate Plus plus we should copy the element from AOF shapelessness.

27
00:02:10,660 --> 00:02:16,810
This process should continue as long as the elements are available to campaign, and once one of the

28
00:02:16,810 --> 00:02:22,310
list ends, we should stop and copy the remaining elements either from the first list.

29
00:02:22,930 --> 00:02:32,320
So if I is not less than equal to murder, it should continue and copy the elements so it be of gay

30
00:02:32,650 --> 00:02:32,980
plus.

31
00:02:32,980 --> 00:02:36,130
Plus it should covid elements from each of.

32
00:02:37,480 --> 00:02:37,930
I.

33
00:02:38,980 --> 00:02:39,430
I.

34
00:02:40,670 --> 00:02:43,880
And see if there are remaining elements in the second list, that is.

35
00:02:44,970 --> 00:02:54,600
Just less than high school to high and C++, it should popular elements and B of K plus plus from E

36
00:02:54,600 --> 00:03:02,910
of G and after copying all the remaining elements, we should transfer all the elements from low to

37
00:03:02,910 --> 00:03:07,590
high, from a B to A back again.

38
00:03:08,160 --> 00:03:11,840
So isolated and equal to high and eight plus plus.

39
00:03:12,180 --> 00:03:14,640
So every time I.

40
00:03:16,210 --> 00:03:18,280
Escapade with BofI.

41
00:03:19,290 --> 00:03:24,120
This is for transferring the elements, so this functionality we have discussed, we have seen it so

42
00:03:24,120 --> 00:03:26,430
the same function I have written then.

43
00:03:27,780 --> 00:03:34,140
Here I will write on a function form just saw that this emerged, thought it pretty much thought that

44
00:03:34,140 --> 00:03:38,490
it should take the parameters that this idea of elements and a number of elements and.

45
00:03:40,500 --> 00:03:45,390
That inside this, we need fewer variables like I p and the low.

46
00:03:46,510 --> 00:03:48,240
High and mid.

47
00:03:50,080 --> 00:03:57,010
Then phospholipids four passes E, that is fastpass, that is the gap in between the that italicize

48
00:03:57,010 --> 00:04:03,070
will be too and will continue while P is less than equal to and.

49
00:04:05,310 --> 00:04:12,780
And he will be moving by feet into do so always, the left side will be getting double done for inner

50
00:04:12,780 --> 00:04:13,470
for loop.

51
00:04:13,830 --> 00:04:14,760
This is for.

52
00:04:15,880 --> 00:04:20,209
Merging all the list in a single pass, so I should start from zero.

53
00:04:20,709 --> 00:04:30,700
I guess I must be minus one is less than and that's all that we have discussed then I should being reminded

54
00:04:30,700 --> 00:04:31,120
by.

55
00:04:32,750 --> 00:04:39,680
I speak then here we will find out low that is high and high, that is.

56
00:04:40,850 --> 00:04:42,830
I plus B, minus one.

57
00:04:44,420 --> 00:04:45,640
And then we will find out.

58
00:04:46,130 --> 00:04:49,100
That is the floor value, so floor value anyway.

59
00:04:49,110 --> 00:04:50,030
These are integers.

60
00:04:50,030 --> 00:04:55,730
So we will get floor value, low plus high divided by two.

61
00:04:57,830 --> 00:05:03,060
So if you got low, mid and high, then we will call March function, which we have already written

62
00:05:03,060 --> 00:05:03,290
there.

63
00:05:03,350 --> 00:05:08,000
That is Uhry and the low and the mid value and high value.

64
00:05:10,240 --> 00:05:17,890
Except so this fall, Lou will perform one boss and it will merge all those small list of sizes two

65
00:05:17,890 --> 00:05:21,610
to each, and then it will become four, four elements each and goes on.

66
00:05:22,880 --> 00:05:30,280
Then at last, I should see that if by two is less than 10 minutes, if there are some more elements

67
00:05:30,280 --> 00:05:38,050
remaining, then we should merge array of elements starting from Zero and Aebi to come up and the minus

68
00:05:38,050 --> 00:05:38,380
one.

69
00:05:39,410 --> 00:05:44,960
Nexon So that's all we have discussed and I have explained you entered into all the same code I'm writing

70
00:05:44,960 --> 00:05:51,560
here now let us call much sort that is emerge sought from the main function.

71
00:05:58,560 --> 00:06:04,260
It's a do not show the list properly, it has not started, there are some warnings.

72
00:06:04,290 --> 00:06:05,270
OK, let us check.

73
00:06:05,280 --> 00:06:06,150
What is the error?

74
00:06:06,300 --> 00:06:07,170
What is the problem?

75
00:06:10,280 --> 00:06:12,470
Oh, I beg the pardon.

76
00:06:12,890 --> 00:06:22,060
Oh, yes, I have not initialize them, I should be L and G should be made a plus one on words and K

77
00:06:22,100 --> 00:06:22,730
should also be.

78
00:06:22,730 --> 00:06:25,110
And now they are initialized.

79
00:06:25,640 --> 00:06:26,330
Let us run.

80
00:06:29,570 --> 00:06:36,830
Let us take the elements, they are solid threes, so all three is missing here, threes at the last.

81
00:06:37,250 --> 00:06:40,300
So it means three is not included in the list.

82
00:06:41,150 --> 00:06:42,920
So there is one more problem in this one.

83
00:06:42,920 --> 00:06:44,350
So why three is not included?

84
00:06:44,900 --> 00:06:45,710
Let us check.

85
00:06:50,030 --> 00:06:57,890
B by two, minus one, yes, C, we are having ENDESA starting from zero onwards.

86
00:06:58,840 --> 00:07:05,890
So if there are eight elements, zero to seven, so it should be minus one, if I do maybe eight, but

87
00:07:05,890 --> 00:07:08,140
minus one, I should get letters from now.

88
00:07:10,680 --> 00:07:11,800
Yes, perfect.

89
00:07:12,630 --> 00:07:14,820
So it has sorted all the elements.

90
00:07:16,180 --> 00:07:24,460
So that's all it really version of much thought anyway, mercedez it to them suddenly, but because

91
00:07:24,460 --> 00:07:30,550
this is iterative and one is recursive, so this one is called as much sort.

92
00:07:33,920 --> 00:07:39,710
The next we'll be looking at recursive Marzak and also you'll see the program for that one.

