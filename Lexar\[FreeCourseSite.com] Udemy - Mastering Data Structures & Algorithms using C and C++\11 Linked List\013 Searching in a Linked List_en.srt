1
00:00:00,590 --> 00:00:07,820
Now, let us look at searching in our list, we know to search procedures that are Leanyer search and

2
00:00:07,820 --> 00:00:12,710
binary search, Lenie search sequential will check for the element one by one.

3
00:00:13,130 --> 00:00:17,870
Then binary search will work only on Sartorialist and it will check in the middle of a list.

4
00:00:18,440 --> 00:00:20,360
If the element is found, it's OK.

5
00:00:20,720 --> 00:00:22,790
If it is small, it will check on the left side.

6
00:00:23,090 --> 00:00:24,920
Otherwise it will check on the right side.

7
00:00:24,980 --> 00:00:28,370
We know this one, that procedure, binary search.

8
00:00:28,370 --> 00:00:34,850
We cannot perform upon <PERSON><PERSON><PERSON>'s because we cannot directly go in the middle of all inkless.

9
00:00:34,860 --> 00:00:36,670
We have to traverse from here.

10
00:00:37,430 --> 00:00:39,710
So traversing takes out of time.

11
00:00:40,640 --> 00:00:43,060
We cannot reach in the middle in Konstantine.

12
00:00:43,370 --> 00:00:46,620
So that is the reason binary search is not suitable.

13
00:00:46,940 --> 00:00:49,760
So we look at only linear search.

14
00:00:50,420 --> 00:00:53,180
So let us understand Línea search for linear.

15
00:00:53,420 --> 00:00:59,330
Suppose I'm searching for a key to wealth then how to search.

16
00:00:59,690 --> 00:01:02,900
Go on looking for the value 2L starting from the first.

17
00:01:02,900 --> 00:01:03,580
Nor is it.

18
00:01:03,590 --> 00:01:04,830
Well, not as it were.

19
00:01:04,849 --> 00:01:05,880
No as it were.

20
00:01:05,900 --> 00:01:07,060
No, as it were.

21
00:01:07,090 --> 00:01:07,390
Yes.

22
00:01:07,460 --> 00:01:08,320
Found strong.

23
00:01:09,620 --> 00:01:14,630
So it means I have to traverse the inkless and go looking for the key element.

24
00:01:15,110 --> 00:01:15,880
So transversing.

25
00:01:15,890 --> 00:01:18,020
We know where it is as simple as traversing.

26
00:01:18,320 --> 00:01:25,460
Just the thing is if the keys don't stop there only if suppose I'm searching for a value that is twenty

27
00:01:25,460 --> 00:01:25,880
five.

28
00:01:26,060 --> 00:01:29,720
Twenty five is not here so go on looking for it is not twenty five.

29
00:01:29,720 --> 00:01:31,000
Not twenty five, not twenty five.

30
00:01:31,340 --> 00:01:37,070
And so we reach the null value so the key is not found so directly.

31
00:01:37,070 --> 00:01:44,780
Let's write a function for searching, let us call a function name as search and this is taking struct

32
00:01:44,780 --> 00:01:50,000
node pointer B so this should also take a key as parameter.

33
00:01:50,000 --> 00:01:55,070
The key that we are searching, we may be searching for a value 2L or who may be searching for value.

34
00:01:55,070 --> 00:01:58,130
Twenty five, whatever it is the key also we have to take parameter.

35
00:01:58,850 --> 00:02:02,090
So then as I said, we have to traverse so directly.

36
00:02:02,090 --> 00:02:04,130
I will write on the code for traversing.

37
00:02:04,130 --> 00:02:07,340
So whatever thing we say will be is not equal to null.

38
00:02:08,960 --> 00:02:16,850
And every time we move P to Nixon, ok, this is the code for traversing, going through all the nodes,

39
00:02:17,150 --> 00:02:18,100
my wife, everything.

40
00:02:18,100 --> 00:02:18,890
What I have to do.

41
00:02:19,130 --> 00:02:30,190
Check if key is equal to B's data, if on any node, if key is equal to data like suppose B's here.

42
00:02:30,200 --> 00:02:33,110
So check if a key that is let's say it is too.

43
00:02:33,110 --> 00:02:37,280
And so it's this to equal to piece data eight.

44
00:02:38,120 --> 00:02:38,540
No.

45
00:02:38,600 --> 00:02:46,820
So then move to next node then again check if this key is equal to piece data, no to the move to next

46
00:02:46,820 --> 00:02:47,090
node.

47
00:02:47,450 --> 00:02:49,400
If it is equal then what we should do.

48
00:02:49,830 --> 00:02:54,680
Return the address of that note be I mean suppose when.

49
00:02:54,680 --> 00:02:56,150
PS here now.

50
00:02:56,150 --> 00:02:59,180
PS Equal to data keys equal to data.

51
00:02:59,570 --> 00:03:02,810
The return P means we want the address of a..

52
00:03:05,420 --> 00:03:08,450
Return type is no longer.

53
00:03:10,510 --> 00:03:18,160
So this is for searching, and if the key is not found, then it will become null and come outside.

54
00:03:18,490 --> 00:03:22,570
So Ripton null, Minsky's not found.

55
00:03:22,780 --> 00:03:27,280
Well, this is the code for search, so this is the simplest function.

56
00:03:27,790 --> 00:03:30,220
I relied on a recursive version also.

57
00:03:31,830 --> 00:03:41,730
Directly over the raid on Loadstar function, name search, same signature, same type note and pointer,

58
00:03:42,420 --> 00:03:48,240
known pointer and key, no condition for null.

59
00:03:48,630 --> 00:03:55,510
If these is means there is no Naude, there is no nobleman's right and nothing else.

60
00:03:55,530 --> 00:04:01,120
Do what check if key is equal to be the data.

61
00:04:01,590 --> 00:04:04,440
If data is matching, then return B.

62
00:04:06,540 --> 00:04:09,090
If it is not matching then return.

63
00:04:09,330 --> 00:04:11,830
Call itself search.

64
00:04:11,880 --> 00:04:15,960
This is recursive search the next.

65
00:04:17,670 --> 00:04:25,410
And he also Fossett will check if the data is matching with the key, it will return the address of

66
00:04:25,410 --> 00:04:30,200
a. otherwise it will call itself again upon next node.

67
00:04:31,020 --> 00:04:33,370
So we know very well how recursive function works.

68
00:04:33,390 --> 00:04:37,110
I don't have to trace this financial year, so I have simply written the code.

