1
00:00:00,510 --> 00:00:08,189
In this video, I will compare deletions from red to black, a tree with the deletion from two, three,

2
00:00:08,189 --> 00:00:08,930
14.

3
00:00:09,780 --> 00:00:14,760
If you have not seen two, three, four, three, I suggest you go back and go to the video.

4
00:00:14,760 --> 00:00:15,750
Two, three, four, three.

5
00:00:16,050 --> 00:00:21,000
Then you can understand it's clearly in one of the video already you have explained how black trees

6
00:00:21,000 --> 00:00:24,030
are like two, two, three, four trees from the previous video.

7
00:00:24,030 --> 00:00:24,960
We saw deletion.

8
00:00:25,020 --> 00:00:25,290
Right.

9
00:00:25,590 --> 00:00:31,920
So now let us compare that deletion of the black tree with the deletion of two, three, four, three.

10
00:00:31,950 --> 00:00:33,320
So I have some examples.

11
00:00:33,330 --> 00:00:35,100
Automatically I will show you some examples.

12
00:00:35,580 --> 00:00:37,260
Let us look at this first example.

13
00:00:37,770 --> 00:00:40,260
Here is a red BlackBerry check.

14
00:00:40,260 --> 00:00:46,200
Whether this perfectly having black balance or not means the number of black along the bat.

15
00:00:46,590 --> 00:00:49,920
One, two, three, one, two, three.

16
00:00:49,920 --> 00:00:52,940
And it's also three and one, two and three.

17
00:00:53,280 --> 00:00:54,330
One, two, three.

18
00:00:54,540 --> 00:00:55,330
It is perfect.

19
00:00:56,490 --> 00:01:00,200
Now, how the same team is represented in two, three, four, three.

20
00:01:00,570 --> 00:01:03,030
So for every black node, there is a separate note.

21
00:01:03,180 --> 00:01:09,180
And if there is a red node where the child is there, that is along with the black node inside the same

22
00:01:09,180 --> 00:01:09,780
black node.

23
00:01:10,050 --> 00:01:13,520
So it means that a child of color child often.

24
00:01:13,800 --> 00:01:15,600
So it will be along with the back then.

25
00:01:15,810 --> 00:01:16,950
So let us look at him.

26
00:01:17,820 --> 00:01:19,080
Today is a node.

27
00:01:19,290 --> 00:01:20,730
We'll get to the node then.

28
00:01:20,730 --> 00:01:24,890
What it is doing here, 50 is the red color child all for today.

29
00:01:24,900 --> 00:01:26,580
So it's present there then.

30
00:01:26,940 --> 00:01:27,810
Then the node.

31
00:01:27,810 --> 00:01:30,060
Yes, birthday is the right color child.

32
00:01:30,060 --> 00:01:35,690
So 20th along with that same 10, then 40 is there for a separate note.

33
00:01:35,790 --> 00:01:36,390
Forty five.

34
00:01:36,390 --> 00:01:37,140
Is that a child.

35
00:01:37,380 --> 00:01:38,260
Sixty eight.

36
00:01:38,420 --> 00:01:39,330
Not now.

37
00:01:39,330 --> 00:01:42,720
I have compared the red black trevitt to three forty nine.

38
00:01:42,720 --> 00:01:46,500
Let us delete so I will try to delete radical elements.

39
00:01:46,840 --> 00:01:48,450
Let's say I want to delete twenty.

40
00:01:48,630 --> 00:01:51,660
So black research for 2020 is found here.

41
00:01:51,990 --> 00:01:54,060
A different color simply deleted.

42
00:01:55,220 --> 00:01:55,580
Right.

43
00:01:55,700 --> 00:01:58,920
So you'd still have same black balance, right?

44
00:01:59,450 --> 00:02:02,350
Black color, boxers of same number, same size.

45
00:02:02,990 --> 00:02:07,420
So the NRA directly did not check it here in two, three, four, three.

46
00:02:07,700 --> 00:02:10,280
So for twenty two, it is less than 30, 26.

47
00:02:11,180 --> 00:02:12,380
So they need this one.

48
00:02:12,690 --> 00:02:18,020
So if you delete this, nothing will happen because that note is having one kid that is ten and that

49
00:02:18,020 --> 00:02:18,700
is perfect.

50
00:02:19,280 --> 00:02:24,020
If kids are not there in a no no becomes empty, then you have to perform revisioning two, three,

51
00:02:24,020 --> 00:02:25,310
four, three, four, balancing it.

52
00:02:25,670 --> 00:02:26,860
But it is having a key.

53
00:02:27,230 --> 00:02:28,070
So that's all.

54
00:02:28,220 --> 00:02:31,490
A regular mortar doesn't affect two, three, four threes.

55
00:02:32,120 --> 00:02:33,730
So I'll write back that greatly.

56
00:02:33,740 --> 00:02:34,940
I'll take another example.

57
00:02:35,580 --> 00:02:36,860
Now listen carefully.

58
00:02:37,040 --> 00:02:39,770
I'm building one more and I will show you two cases in that one.

59
00:02:40,070 --> 00:02:40,670
Laodicean.

60
00:02:41,420 --> 00:02:43,340
I want to delete Turkey.

61
00:02:44,690 --> 00:02:46,190
So Indrek Black three.

62
00:02:46,190 --> 00:02:50,210
If you delete the turkey, then you should take its place in order.

63
00:02:50,210 --> 00:02:52,820
Predecessor or in order.

64
00:02:52,820 --> 00:02:56,000
Successor either should go that far.

65
00:02:56,000 --> 00:02:56,810
They should go down.

66
00:02:57,740 --> 00:03:00,130
So if Turkey goes there, it is right.

67
00:03:00,320 --> 00:03:02,750
So deletion of it will not make any difference.

68
00:03:03,020 --> 00:03:04,250
The twenty goes here.

69
00:03:04,400 --> 00:03:07,010
I'm not changing this, just I'm writing below this one.

70
00:03:07,280 --> 00:03:08,990
So Turkey is gone and Burundi is there.

71
00:03:09,230 --> 00:03:11,270
So this twenty, not twenty will be deleted.

72
00:03:11,290 --> 00:03:12,070
It clear.

73
00:03:12,580 --> 00:03:13,580
So simple.

74
00:03:14,600 --> 00:03:17,810
Now let us see the same thing in that two, three forty.

75
00:03:18,320 --> 00:03:21,170
If I delete Turkey either I should board.

76
00:03:21,170 --> 00:03:25,340
This becomes empty, vacant so I should borrow a key from this side of the side.

77
00:03:25,550 --> 00:03:29,330
So from the side if you see two and he will come up from the side if you see forty will come up.

78
00:03:29,330 --> 00:03:29,600
Right.

79
00:03:29,870 --> 00:03:36,530
So first I will show twenty, thirty goes up and if you delete twenty from here then there is no problem.

80
00:03:37,610 --> 00:03:38,970
So that's all right.

81
00:03:39,020 --> 00:03:45,550
BlackBerry also behave in the same be no put back the it is then I'll show you another possibility like

82
00:03:45,620 --> 00:03:48,980
deleting 14 or delete 40 from here.

83
00:03:49,250 --> 00:03:53,340
If I delete 40, 48 is of black color, but it is having red color change.

84
00:03:53,660 --> 00:03:55,300
So forty five will come here.

85
00:03:55,580 --> 00:03:58,360
Forty five will come here and it will become black.

86
00:03:58,580 --> 00:04:04,870
So this is 40 for 40 that nobody's is gone and 40 will be copied at this place.

87
00:04:05,900 --> 00:04:11,810
So once again, today was here, so far he has taken place, 40 was black, but it was having a black

88
00:04:11,840 --> 00:04:13,740
child, so the child became black.

89
00:04:14,480 --> 00:04:18,740
So if you're deleting a black note and is having a child and the child will take its place and it will

90
00:04:18,740 --> 00:04:19,660
become a black note.

91
00:04:20,390 --> 00:04:21,940
So let us see the same thing here.

92
00:04:22,610 --> 00:04:26,650
If I am deleting 30, I can borrow a key from here, 20 or 40.

93
00:04:26,660 --> 00:04:29,380
So if I send 40 now this this weekend.

94
00:04:29,390 --> 00:04:30,800
But what is the key present there?

95
00:04:30,820 --> 00:04:31,450
Forty five.

96
00:04:31,610 --> 00:04:32,930
So that note belongs to home.

97
00:04:32,930 --> 00:04:34,520
Forty five because that is the only key.

98
00:04:34,730 --> 00:04:36,170
So it is here in the cell.

99
00:04:36,170 --> 00:04:37,990
So you can just move it on the site.

100
00:04:39,180 --> 00:04:39,410
Right.

101
00:04:39,770 --> 00:04:44,570
So this node, black color, not just black color, not so perfect.

102
00:04:44,930 --> 00:04:48,080
So it's perfectly matching with two, three 40s.

103
00:04:48,470 --> 00:04:50,510
So I have shown you only red color.

104
00:04:51,020 --> 00:04:55,760
Now let us see if you are deleting ignored, which is black color, then what happens?

105
00:04:56,240 --> 00:04:59,940
This is one example from here I will delete 50.

106
00:05:00,350 --> 00:05:03,800
So first of all, search for 50 50 is this sun.

107
00:05:04,250 --> 00:05:09,340
And if this note is deleted, then its place will be taken by none.

108
00:05:09,380 --> 00:05:12,590
So that becomes what, double black reason?

109
00:05:12,860 --> 00:05:15,260
Because the note that we have deleted was black.

110
00:05:15,290 --> 00:05:16,920
So in this place we have double black.

111
00:05:17,300 --> 00:05:19,250
So what is the rule for double black?

112
00:05:19,280 --> 00:05:21,920
We have seen the cases check for the sibling.

113
00:05:22,070 --> 00:05:23,350
Sibling is black.

114
00:05:23,660 --> 00:05:24,910
Then what are its children?

115
00:05:24,920 --> 00:05:26,470
You have to see the children of siblings.

116
00:05:27,290 --> 00:05:28,770
There are no children, actually.

117
00:05:28,790 --> 00:05:31,500
So those are nuns and those are black?

118
00:05:31,820 --> 00:05:33,140
Yes, siblings.

119
00:05:33,140 --> 00:05:35,090
Black children are also black.

120
00:05:35,120 --> 00:05:36,240
So what is the procedure?

121
00:05:36,260 --> 00:05:42,050
What we do in this case, if a sibling and children both are black, the recolor, if any one of them

122
00:05:42,050 --> 00:05:43,660
is written performed rotation.

123
00:05:43,940 --> 00:05:45,320
So this is recovering.

124
00:05:45,320 --> 00:05:50,030
So how we treat color this, we change it to red and it's a parent.

125
00:05:50,060 --> 00:05:53,730
We change it to black, that's all.

126
00:05:54,860 --> 00:05:56,780
Now check whether it is balanced or not.

127
00:05:56,780 --> 00:05:57,740
Black balance.

128
00:05:57,740 --> 00:05:59,900
One, two, three.

129
00:06:00,230 --> 00:06:01,370
That is not on the side.

130
00:06:01,420 --> 00:06:04,190
So, yes, one, two, three.

131
00:06:04,490 --> 00:06:05,600
Right, then.

132
00:06:05,600 --> 00:06:07,570
One, two, three.

133
00:06:07,580 --> 00:06:08,650
That is black on the side.

134
00:06:08,960 --> 00:06:09,920
One, two.

135
00:06:09,980 --> 00:06:10,540
This is red.

136
00:06:10,700 --> 00:06:12,550
So black on this side three.

137
00:06:12,770 --> 00:06:14,190
So it's perfectly balanced.

138
00:06:15,230 --> 00:06:16,970
So I'll put back the things as it is.

139
00:06:17,180 --> 00:06:20,210
Then I will show you there in that two, three, four trees.

140
00:06:20,240 --> 00:06:20,390
Yeah.

141
00:06:20,420 --> 00:06:25,370
The trees back I want to delete 50 50 50 is in a separate node.

142
00:06:25,730 --> 00:06:29,380
If I remove this 50, then this node becomes vacant.

143
00:06:29,600 --> 00:06:30,590
Then what I should do.

144
00:06:31,280 --> 00:06:33,790
Can I borrow any key from the side of the site?

145
00:06:33,830 --> 00:06:34,070
No.

146
00:06:34,090 --> 00:06:36,170
They are also having same single key.

147
00:06:36,350 --> 00:06:44,240
Then what to do much more, in which case I should much together 60 I should bring here and 70 should

148
00:06:44,240 --> 00:06:45,990
be on its right side.

149
00:06:46,400 --> 00:06:48,100
So this is March.

150
00:06:48,110 --> 00:06:49,370
It is March with the parent.

151
00:06:49,390 --> 00:06:53,310
So this is 60 and this is 70 in a single load this.

152
00:06:53,360 --> 00:06:54,230
Nor does it delete it.

153
00:06:54,650 --> 00:06:56,270
So same thing is happening here.

154
00:06:56,270 --> 00:07:00,280
When 50 is gone, then what we did, 60 came here.

155
00:07:00,290 --> 00:07:01,630
So it means this is black.

156
00:07:01,700 --> 00:07:05,170
Yes, this is black and seventy came along with this one.

157
00:07:05,180 --> 00:07:08,060
So this becomes later this became red.

158
00:07:09,410 --> 00:07:11,210
So the same procedure procedure.

159
00:07:11,360 --> 00:07:13,130
Same as a two, three, four, three.

160
00:07:13,550 --> 00:07:20,090
So swiping the color is nothing but just like merging the N, merging the N, No.

161
00:07:20,090 --> 00:07:25,130
One more cases remaining sibling s and its children are black.

162
00:07:25,430 --> 00:07:30,980
All siblings, black children attacked anyone and both will perform probation.

163
00:07:30,980 --> 00:07:34,880
So I have two examples, two examples and there are two, three, four.

164
00:07:34,880 --> 00:07:36,280
PS let us do it.

165
00:07:36,560 --> 00:07:40,220
Suppose I want to delete ten then is gone.

166
00:07:40,610 --> 00:07:45,050
So this is null and it becomes double black because ten was black.

167
00:07:45,890 --> 00:07:46,990
Then what to do here.

168
00:07:47,100 --> 00:07:48,170
Perform rotation.

169
00:07:48,170 --> 00:07:53,990
Right, because sibling is red and if children are black perform rotation along the spinning around

170
00:07:53,990 --> 00:07:54,380
parent.

171
00:07:54,590 --> 00:07:56,660
So I could parent we get to 40.

172
00:07:56,900 --> 00:08:02,990
That is Black Knight and Billy comes here and this will be 50.

173
00:08:03,200 --> 00:08:03,680
Right.

174
00:08:04,100 --> 00:08:08,600
And this child, this was black so it will be to here.

175
00:08:08,990 --> 00:08:10,040
So that is thirty.

176
00:08:12,520 --> 00:08:18,130
We have already seen this right, when the Chinese are being slapped, but it is coming from one side

177
00:08:18,130 --> 00:08:21,670
to another side of a tree, that if it is ready, it will become black.

178
00:08:21,670 --> 00:08:22,890
If it is black, it will become right.

179
00:08:23,020 --> 00:08:24,460
So this has become clear.

180
00:08:24,790 --> 00:08:27,310
It was black, not the black colors.

181
00:08:27,520 --> 00:08:28,620
One, two, three.

182
00:08:29,050 --> 00:08:30,260
OK, one, two, three.

183
00:08:30,550 --> 00:08:32,970
So in all our examples, we are getting three.

184
00:08:32,990 --> 00:08:34,900
Doesn't mean that always the number of blacks.

185
00:08:34,900 --> 00:08:35,600
Salvatore, right.

186
00:08:35,710 --> 00:08:37,530
In our example, we have small examples.

187
00:08:37,530 --> 00:08:39,909
So the number of black history here.

188
00:08:40,450 --> 00:08:41,500
So one, two.

189
00:08:41,799 --> 00:08:43,570
And that is three.

190
00:08:44,020 --> 00:08:44,870
So it's perfect.

191
00:08:45,580 --> 00:08:48,480
So let us see what is happening in this one when ten is gone.

192
00:08:49,960 --> 00:08:51,430
Can we borrow anything from here?

193
00:08:51,700 --> 00:08:53,440
No, I cannot borrow anything from you.

194
00:08:53,470 --> 00:08:54,130
Then what to do?

195
00:08:54,340 --> 00:08:55,300
Mostly the school.

196
00:08:55,480 --> 00:08:58,180
So baby comes here today will be followed by that.

197
00:08:58,390 --> 00:08:59,530
And this 40.

198
00:08:59,530 --> 00:09:00,670
I should move this site.

199
00:09:00,820 --> 00:09:04,240
So 40 and 50 should come to this Maunsell.

200
00:09:05,380 --> 00:09:06,810
We should not have blank space here.

201
00:09:06,820 --> 00:09:08,050
So I have just shifted them.

202
00:09:08,350 --> 00:09:10,090
So 20 and 30 are much.

203
00:09:10,090 --> 00:09:15,890
So that's what it is happening here with rotation also it is merging or bordering whatever it is.

204
00:09:16,000 --> 00:09:19,920
So this is much so now you can see that 40 year old black.

205
00:09:19,930 --> 00:09:25,890
Yes, 50s and yes, black and Bridie's and old black studies along with that sort of thing.

206
00:09:26,620 --> 00:09:29,020
So this what we got not last example.

207
00:09:29,020 --> 00:09:36,940
I have Naude as black and children are here and they want to delete and let us see that also for this

208
00:09:36,940 --> 00:09:40,900
already I have this right, I want to delete them so can is gone.

209
00:09:40,900 --> 00:09:43,450
So it is known as the Lord was black.

210
00:09:43,450 --> 00:09:45,190
So this is black heavy.

211
00:09:45,670 --> 00:09:45,890
Right.

212
00:09:46,060 --> 00:09:48,400
This is double black lowlanders.

213
00:09:48,400 --> 00:09:48,940
Double black.

214
00:09:48,940 --> 00:09:49,750
Check the sibling.

215
00:09:49,750 --> 00:09:50,710
Sibling is black.

216
00:09:50,740 --> 00:09:51,670
What are these children.

217
00:09:51,670 --> 00:09:52,080
Children.

218
00:09:52,080 --> 00:09:53,380
Novotna here.

219
00:09:53,380 --> 00:09:54,580
Sublingual stepchildren.

220
00:09:54,580 --> 00:09:59,080
They're black, their sibling is black and children are just contrast to that one and the situation

221
00:09:59,120 --> 00:09:59,530
the same.

222
00:09:59,530 --> 00:10:02,520
We have to perform probation then the suspension.

223
00:10:02,890 --> 00:10:06,010
We have to perform rotation, probation around this one.

224
00:10:06,010 --> 00:10:07,660
But we will perform.

225
00:10:07,870 --> 00:10:12,820
We can perform a zigzaggy rotation that is right left probation.

226
00:10:12,820 --> 00:10:16,480
We can perform right, left, right, left probation.

227
00:10:16,720 --> 00:10:18,760
This norm will become routine.

228
00:10:18,970 --> 00:10:22,420
Tutty will move up the ante as it is black.

229
00:10:22,420 --> 00:10:23,740
It will remain same.

230
00:10:23,890 --> 00:10:24,940
It will come on this site.

231
00:10:25,330 --> 00:10:27,700
Forty will remain cemented in place.

232
00:10:27,850 --> 00:10:28,630
And fifty.

233
00:10:28,870 --> 00:10:29,570
That is right.

234
00:10:29,590 --> 00:10:30,390
It remains ready.

235
00:10:30,400 --> 00:10:31,870
It's not disturbed at all.

236
00:10:32,260 --> 00:10:35,350
So this just thirty has gone up and it is now.

237
00:10:35,350 --> 00:10:36,370
So it became black.

238
00:10:37,270 --> 00:10:38,920
So that's all this little station.

239
00:10:39,550 --> 00:10:48,000
So when the sibling is black and children are heard and you can perform a lot or exact rotation like

240
00:10:48,280 --> 00:10:49,570
is having there are children.

241
00:10:50,200 --> 00:10:52,380
No, let us look at their hair.

242
00:10:52,390 --> 00:10:54,490
Time is gone, then is deleted.

243
00:10:54,740 --> 00:10:55,090
Right.

244
00:10:55,420 --> 00:10:59,020
Then come the site then who will move up today.

245
00:10:59,020 --> 00:11:02,530
Will move up right then this place this weekend.

246
00:11:02,710 --> 00:11:03,670
So this is weekend.

247
00:11:03,670 --> 00:11:05,270
So just move fourteen fifteen.

248
00:11:05,290 --> 00:11:11,200
This site for goodness sake this is boring so we don't have too much.

249
00:11:11,200 --> 00:11:14,230
We have to borrow here so we can go boring.

250
00:11:14,230 --> 00:11:17,440
Also we can do much also depends boring possible.

251
00:11:17,440 --> 00:11:18,090
We do boring.

252
00:11:18,490 --> 00:11:20,260
Now see who is route thirty.

253
00:11:20,290 --> 00:11:20,980
That is black.

254
00:11:21,280 --> 00:11:21,760
Don't do this.

255
00:11:22,130 --> 00:11:22,860
All that is black.

256
00:11:22,870 --> 00:11:25,950
What do you know that is black fifties along with the forty.

257
00:11:25,960 --> 00:11:27,160
So it isn't that color.

258
00:11:28,330 --> 00:11:31,410
That's all this is matching with two, three, four fortress.

259
00:11:31,660 --> 00:11:37,660
So black trees are perfectly a with the two, three, four trees they are designed keeping in mind two,

260
00:11:37,660 --> 00:11:40,750
three, four trees, beautiful trees can have more than one.

261
00:11:40,750 --> 00:11:43,060
Keyes's binary trees can have just one key.

262
00:11:43,330 --> 00:11:49,420
Solid black tree is a binary tree with the one key but behaving like two, three, four tree.

263
00:11:49,990 --> 00:11:51,610
So this means practice.

264
00:11:51,610 --> 00:11:57,340
So you practice it once and observe the things by yourself and this makes you perfect topic.

265
00:11:58,090 --> 00:11:59,410
So that's all in this video.

