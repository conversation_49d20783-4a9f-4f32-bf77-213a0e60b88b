1
00:00:00,150 --> 00:00:06,840
And this video will look at the demonstration for concatenation of cool English, as well as merging

2
00:00:06,840 --> 00:00:12,930
of two linguist, so I should already have two linguist with me and this program, which I have been

3
00:00:12,930 --> 00:00:14,970
using as having one Linklaters.

4
00:00:16,120 --> 00:00:18,670
That's already created using create function.

5
00:00:18,700 --> 00:00:24,640
If we look back to the create function, this was the first function we have in it and this is creating

6
00:00:24,650 --> 00:00:28,730
firstly, it is directly accessing point the first and it is creating facilities.

7
00:00:28,870 --> 00:00:34,870
So as I need one more Linklaters, I will have a new pointer that is second and one more point that

8
00:00:34,870 --> 00:00:35,500
I may need.

9
00:00:35,500 --> 00:00:43,420
That is code pointer for total interest and I will initialize these pointers also as Nonno as we already

10
00:00:43,420 --> 00:00:48,370
have a function for creating first limitlessness, for creating second lintels, I should have separate

11
00:00:48,370 --> 00:00:48,910
functions.

12
00:00:48,930 --> 00:00:53,170
So what I'll do is I'll copy the same function and pasted here.

13
00:00:53,170 --> 00:00:58,460
I listed here and I will call this function name as created to the second function.

14
00:00:58,480 --> 00:01:02,110
Now here, instead of using pointer first I will use pointer.

15
00:01:02,410 --> 00:01:04,400
Second, I'll modify this.

16
00:01:04,420 --> 00:01:05,620
Yes, this is enough.

17
00:01:06,040 --> 00:01:08,140
It will create second link list.

18
00:01:08,470 --> 00:01:14,560
First of all, let us try to create bool inkless that is first and second and display them individually.

19
00:01:14,560 --> 00:01:17,730
I should create a link using this created to function.

20
00:01:17,890 --> 00:01:19,780
Let us go back to the main function here.

21
00:01:19,780 --> 00:01:21,880
Create function is creating Faslane list.

22
00:01:22,450 --> 00:01:29,440
Then I will have one more array that is B and in this I will have some elements, five elements I have

23
00:01:29,440 --> 00:01:29,830
taken.

24
00:01:30,730 --> 00:01:37,080
I will call function created two by passing a B and the number of elements are five.

25
00:01:37,090 --> 00:01:39,400
So it should create a second linguist.

26
00:01:39,580 --> 00:01:45,460
Then here I'm displaying futsal English, so let us call it as first new line and for the second link

27
00:01:45,470 --> 00:01:51,710
list also I should add it so for displaying, I will copy the scope and pasted here.

28
00:01:52,270 --> 00:01:57,010
Now this code, I will make it for second longest and I will pass the pointer.

29
00:01:57,010 --> 00:01:57,860
That is second.

30
00:01:57,880 --> 00:01:58,990
It's a global pointer.

31
00:01:59,060 --> 00:02:03,670
Now let us run this program and see I should get the Booligal first linked list then followed by that

32
00:02:03,670 --> 00:02:10,539
I should get second interest is first Linklaters 50 for the N30 and Banty and second Linklaters one

33
00:02:10,539 --> 00:02:11,560
two three four five.

34
00:02:11,920 --> 00:02:17,140
Yes it's perfect so I'll remove the scope for display I have to link is created here.

35
00:02:17,140 --> 00:02:20,680
That is first and second request by using two different functions.

36
00:02:20,770 --> 00:02:26,140
Now I have to write on the code for concatenation of two linked list so I will write function name that

37
00:02:26,140 --> 00:02:29,080
is contact which will take two pointers.

38
00:02:29,080 --> 00:02:31,540
That is first and second link is pointer.

39
00:02:32,140 --> 00:02:40,090
I will at first point B and second point arise and second point as Q so four concatenation.

40
00:02:40,090 --> 00:02:45,670
I should scan through the first link list that is using pointer B and I should reach the end and from

41
00:02:45,670 --> 00:02:49,240
there I should connect the links to the second link that is to.

42
00:02:49,240 --> 00:02:51,850
Q So it's a simple process, easy process.

43
00:02:52,210 --> 00:02:58,760
But first of all I should make the point, a third point on E that is up on the facilities now concatenated.

44
00:02:59,240 --> 00:03:04,890
We will have it in pointer code nine should scandal fossil Englis and I should stop at last.

45
00:03:04,890 --> 00:03:10,060
And so I should say these next is not equal to null.

46
00:03:10,270 --> 00:03:18,580
So it will stop upon last node and everytime I should move b b assign these next once or twice for each

47
00:03:18,580 --> 00:03:24,180
on the last node then I should say E next assign Q it will be concatenating NetSol.

48
00:03:24,190 --> 00:03:28,510
The procedure is very simple, so I'll get the turtling disappointed by that third.

49
00:03:28,630 --> 00:03:35,710
Now here inside the main function after creating Boolean I should call function concat by passing first

50
00:03:35,710 --> 00:03:39,370
Linklaters that is first and the second longest and that is second.

51
00:03:39,790 --> 00:03:43,720
So this should concatenate and the result should be in third linguist.

52
00:03:43,870 --> 00:03:44,920
Then I should display this.

53
00:03:44,920 --> 00:03:47,770
So I will write on the code quickly for displaying yes.

54
00:03:48,120 --> 00:03:50,290
If concatenated lintels that is displayed.

55
00:03:50,290 --> 00:03:54,120
Hollinghurst So it will be a pointer pointing to concatenated.

56
00:03:54,490 --> 00:03:55,300
Let us run this.

57
00:03:55,300 --> 00:03:57,330
I should get concatenated least.

58
00:03:57,370 --> 00:03:57,940
Yes.

59
00:03:58,390 --> 00:04:02,710
Fifty it and 32 and then one, two, three, four, five so far.

60
00:04:02,710 --> 00:04:08,350
Linkous then followed by that as a second language I tried to just change the order instead of signalling

61
00:04:08,350 --> 00:04:08,680
first.

62
00:04:08,680 --> 00:04:14,860
Linkous I'll give first second one the next futsal inkless so I should get one, two, three, four

63
00:04:14,860 --> 00:04:16,170
five then fifty two one.

64
00:04:16,180 --> 00:04:16,570
Yes.

65
00:04:16,570 --> 00:04:17,490
One, two, three, four, five.

66
00:04:17,829 --> 00:04:22,850
Now this is the first is the second longest and followed by that as a first linguist's.

67
00:04:23,020 --> 00:04:24,940
So that's all the concatenation.

68
00:04:24,940 --> 00:04:26,050
It's working perfectly.

69
00:04:26,230 --> 00:04:32,020
I'll remove this now let us write a function for merging two Linkous So for merging.

70
00:04:32,020 --> 00:04:34,480
First of all I will modify this linked list first.

71
00:04:34,480 --> 00:04:39,670
Linkous I will take the values like the and or indeed 30, 40 and 50.

72
00:04:40,060 --> 00:04:46,540
And for the second linked list I'll take the elements like five fifteen, twenty five, thirty five

73
00:04:46,570 --> 00:04:47,970
and forty five.

74
00:04:48,070 --> 00:04:54,160
Now let us write on the code for merging bool in place that already we have seen much so it should be

75
00:04:54,160 --> 00:05:01,600
pointed to first Linkous that is B and also pointed to second link that is Q so Pintu I'm taking asked

76
00:05:01,600 --> 00:05:07,900
point us to first and second guessed now here I should merge to link and make the third point their

77
00:05:07,900 --> 00:05:10,900
point on March four for merging process.

78
00:05:10,900 --> 00:05:15,600
I need a pointer that is last point that is useful for merging I declare.

79
00:05:15,810 --> 00:05:21,240
The first thing I should make point or put point, either on your cue, whoever's element is a smaller

80
00:05:21,240 --> 00:05:23,130
like futsal, English or second linguist.

81
00:05:23,490 --> 00:05:29,940
So for that I should check the condition, that is if the data is smaller than CUSA data.

82
00:05:29,970 --> 00:05:38,190
And third, as well as last, bullet should point on E and next B should move to the next node and turd's

83
00:05:38,190 --> 00:05:38,600
next.

84
00:05:38,610 --> 00:05:41,040
It should be made as otherwise.

85
00:05:41,040 --> 00:05:44,510
If Kyuss data is smaller than the same, thing should be done upon you.

86
00:05:44,550 --> 00:05:46,140
So I will copy this.

87
00:05:46,290 --> 00:05:47,950
This I should make it for you.

88
00:05:48,220 --> 00:05:48,690
Yes.

89
00:05:48,840 --> 00:05:49,980
Here it is done now.

90
00:05:49,980 --> 00:05:51,870
The processes rebidding process.

91
00:05:51,870 --> 00:05:56,770
I should keep on checking the elements of P and Q which of it is smaller that should be linked with

92
00:05:56,770 --> 00:05:59,670
the total English with the help of pointer last.

93
00:05:59,700 --> 00:06:06,450
So while P and Q every time check if E data is smaller than Qs data.

94
00:06:06,480 --> 00:06:10,610
If so, then I should make proper links like last.

95
00:06:10,890 --> 00:06:18,300
Next should point on E and P should move to the next node and the last next should be made as null.

96
00:06:18,330 --> 00:06:20,580
Otherwise I should do the same thing.

97
00:06:20,580 --> 00:06:28,590
But last night should point on E and lasta should and should work on P then P should move to next node

98
00:06:28,590 --> 00:06:35,220
and last to next should be made as null and otherwise I should perform same sort of operation upon.

99
00:06:35,220 --> 00:06:37,920
Q So I will copied and pasted here.

100
00:06:37,930 --> 00:06:39,540
Now the same process should be done for.

101
00:06:39,540 --> 00:06:41,550
Q That I should point on.

102
00:06:41,550 --> 00:06:46,360
Q and Q should move on to the next node and the last name should be made as null.

103
00:06:46,380 --> 00:06:51,510
So this y look will keep on copying the elements, whichever is smaller and at the end of the line,

104
00:06:51,810 --> 00:06:55,110
whichever is remaining, I should link to that one.

105
00:06:55,110 --> 00:07:05,460
If it's not null if p then last to next should point on be then last the next should point on e f q

106
00:07:05,490 --> 00:07:07,740
then last next should point on.

107
00:07:07,740 --> 00:07:11,660
Q That's on the slot we have already discussed on Lightbourne.

108
00:07:11,760 --> 00:07:19,470
Now let us go to main function and the most two linked list merge by passing pointer first comma pointer

109
00:07:19,500 --> 00:07:23,160
second, then the third linguist's will be form and let us display third.

110
00:07:23,160 --> 00:07:25,010
Linkous let us run the program.

111
00:07:25,020 --> 00:07:25,560
Yes.

112
00:07:25,920 --> 00:07:29,790
Five, 10, 15, 20, 25, 30, 35, 40, 45, 50.

113
00:07:30,240 --> 00:07:31,760
All elements are merged.

114
00:07:31,890 --> 00:07:34,530
Let us change a few elements and try again.

115
00:07:34,770 --> 00:07:38,220
I'll give all these elements that are greater than vasilenko.

116
00:07:38,280 --> 00:07:41,130
So fifty two and this is one fifty two.

117
00:07:41,130 --> 00:07:43,940
This is 252 to fifty two 452.

118
00:07:44,280 --> 00:07:46,680
So all these elements are greater than first.

119
00:07:47,050 --> 00:07:49,850
So let the civilians manage or not.

120
00:07:49,870 --> 00:07:51,210
Let it smudge or not.

121
00:07:51,210 --> 00:07:56,730
Yes, it is merging, it's working perfectly even in this worst case that is all small elements and

122
00:07:56,730 --> 00:07:59,560
facilities then all large element in the second lingley.

123
00:07:59,580 --> 00:08:03,570
So they are being just like concatenated and it is merging perfectly.

124
00:08:04,260 --> 00:08:08,250
I don't think I'll make this first element as five and the rest of them are greater.

125
00:08:08,460 --> 00:08:13,800
So the five should come before and the rest of these elements should come after fifty.

126
00:08:14,250 --> 00:08:14,940
Let us run.

127
00:08:15,060 --> 00:08:21,060
Yes, five, then 10, 20, 30, 40, 50, then followed by that 150 to 32, so on.

128
00:08:21,080 --> 00:08:24,630
So yes, this much function is working perfectly, that's all.

129
00:08:24,630 --> 00:08:25,800
Without much function.

130
00:08:25,800 --> 00:08:29,640
I have shown you how to concatenate and how to merge the coldest little lendee.

131
00:08:29,640 --> 00:08:35,730
What is the interesting function that is marginal interest to try it by yourself, write a program and

132
00:08:35,730 --> 00:08:36,690
practice this one.

