1
00:00:01,910 --> 00:00:07,100
And this video will write few functions upon Linklaters that is counting the number of new and finding

2
00:00:07,100 --> 00:00:13,810
the sum of all the elements in our list and also we will write a function for search.

3
00:00:13,820 --> 00:00:17,990
So all these functions, we can write them iterative as well as recursive.

4
00:00:18,350 --> 00:00:23,300
Already I have discussed these functions up on whiteboards or just I have to write the code and give

5
00:00:23,300 --> 00:00:24,230
you a demonstration.

6
00:00:25,440 --> 00:00:32,490
So let us right first function for counting the number of nodes in a nucleus, so here already I have

7
00:00:32,490 --> 00:00:36,060
an array of elements and I have already created a link list.

8
00:00:36,600 --> 00:00:39,750
This is the same program which we have already seen in previous videos.

9
00:00:39,990 --> 00:00:41,220
It's clearly a function already.

10
00:00:41,220 --> 00:00:42,840
I have explained the previous videos.

11
00:00:43,380 --> 00:00:44,700
This is a create function.

12
00:00:44,700 --> 00:00:50,370
Here you can see some updating the same program, all of these structures already defined and the pointer

13
00:00:50,370 --> 00:00:51,470
first is also dead.

14
00:00:51,840 --> 00:00:54,030
So I'm adding functions to the same program.

15
00:00:54,060 --> 00:00:57,690
So finally, it becomes a single big program for the linked list.

16
00:00:58,830 --> 00:01:04,440
Now, here I will write a function for counting number of nodes that is finding land of our linked list.

17
00:01:04,980 --> 00:01:07,560
So function should return our length of a link.

18
00:01:07,680 --> 00:01:11,760
So the return APIs integer and let us call the function as count.

19
00:01:12,570 --> 00:01:16,860
Let it take node pointer to the first node.

20
00:01:17,370 --> 00:01:19,230
Let us call the pointer ISP.

21
00:01:20,500 --> 00:01:26,520
I'm here for conking lente are finding the land, I will take a variable, and that is initially WSDL,

22
00:01:27,310 --> 00:01:33,340
not the I should scan through the entire links and on the Norns until I reach the end of a interest.

23
00:01:33,550 --> 00:01:36,730
So this can be done using available widely.

24
00:01:38,900 --> 00:01:43,730
And plus plus, this will be counting on the move to next north.

25
00:01:45,930 --> 00:01:50,550
That's all at the end, it should return as that is lente.

26
00:01:52,320 --> 00:01:53,400
So this is the function.

27
00:01:55,990 --> 00:02:01,780
And here inside main function, I will simply call that function inside princess.

28
00:02:03,560 --> 00:02:04,250
Printf.

29
00:02:05,340 --> 00:02:06,570
Lent is.

30
00:02:08,330 --> 00:02:16,010
Is first indicted and here I will call the function down by passing Winterfest.

31
00:02:19,160 --> 00:02:24,700
That's all I should get the lint from the program, I should get the land through the lenses.

32
00:02:24,710 --> 00:02:26,810
Five, yes, it is five.

33
00:02:28,760 --> 00:02:36,060
I'll just give a line up and run it again and show you a new line or give one more new line lenders',

34
00:02:36,070 --> 00:02:38,120
then there's five different.

35
00:02:39,490 --> 00:02:42,130
Yes, I got a message that is five.

36
00:02:44,300 --> 00:02:46,170
Let us add a few more elements here.

37
00:02:46,220 --> 00:02:48,350
Eight, 12.

38
00:02:49,410 --> 00:02:55,100
Twenty and a number of elements are increased from eight elements, I have not I should get Delinda's

39
00:02:55,110 --> 00:02:55,560
eight.

40
00:02:58,240 --> 00:03:00,970
Yes, Landesa, so it's working perfect.

41
00:03:02,060 --> 00:03:07,380
The next thing I can write it as a recursive function, so just above main function, I will write on

42
00:03:07,390 --> 00:03:09,700
a function, call our account.

43
00:03:10,120 --> 00:03:17,720
That should take a structure known and pointer that is pointed to the fact known as it is recursive.

44
00:03:17,740 --> 00:03:28,270
So I should check, if BP's not equal to null, then it can pull itself by counting and return or count

45
00:03:29,140 --> 00:03:30,460
on BP's next.

46
00:03:31,810 --> 00:03:33,700
And for each node, add one.

47
00:03:35,840 --> 00:03:39,530
Otherwise, return zero.

48
00:03:41,600 --> 00:03:44,630
Next fall, just a few lines of code for.

49
00:03:45,850 --> 00:03:52,180
Recursive count, so here inside the main function at this place, I will just modify this function

50
00:03:52,180 --> 00:03:53,090
as our count.

51
00:03:53,500 --> 00:03:59,950
Now it will be calling this function Aurecon function and this account should return us lente eight.

52
00:04:03,080 --> 00:04:04,370
Yes, it is returning.

53
00:04:05,940 --> 00:04:06,510
Perfect.

54
00:04:07,510 --> 00:04:14,680
The next function we will see finding some I will write a function that is a function for finding some

55
00:04:14,680 --> 00:04:22,360
blood type should be integer and the function name of some and it should be struck Naude pointer upon

56
00:04:22,660 --> 00:04:23,980
Fastenal that is be.

57
00:04:25,190 --> 00:04:31,900
For finding some, I should have some variables I'll take as an initial zero now control the intelligence

58
00:04:31,940 --> 00:04:35,140
that is travels through this link will be not equal to none.

59
00:04:36,440 --> 00:04:42,220
And every time it's a plus, assign these data, these data is added.

60
00:04:42,800 --> 00:04:46,670
So he he moves to the next normal.

61
00:04:48,460 --> 00:04:55,480
That's all, instead of displaying, we are adding them to variable X and finally return as that is

62
00:04:55,480 --> 00:04:56,650
sum of all the elements.

63
00:04:58,600 --> 00:05:03,880
And here I will remove the sprint and I will write a function for finding some.

64
00:05:05,040 --> 00:05:06,060
So printf.

65
00:05:09,700 --> 00:05:10,810
Some as.

66
00:05:13,050 --> 00:05:15,600
Person Tildy, then Newlon.

67
00:05:20,130 --> 00:05:23,430
I will call the function some by passing first.

68
00:05:25,750 --> 00:05:31,170
So I should get the sum of all the elements, so I have a few elements here, eight elements are there,

69
00:05:31,180 --> 00:05:34,630
so I should get the total value of all these elements.

70
00:05:35,160 --> 00:05:36,010
Let us run it.

71
00:05:38,410 --> 00:05:46,960
Yes, it seems it is 80, so, yes, it is the sum of all these elements and let us look at recursive,

72
00:05:46,960 --> 00:05:55,630
some function, I will call it, as are some unpicks struck nought point or two for Snoad.

73
00:05:56,860 --> 00:06:03,040
And here inside the function, I will check if P is equal to Nudelman speech, not pointing on any note

74
00:06:03,370 --> 00:06:07,630
written or otherwise written.

75
00:06:08,730 --> 00:06:15,810
All autism itself, that is on Next Nixonland and also at the date of Naude.

76
00:06:20,330 --> 00:06:26,560
Then that solved a very simple function, recursive function is just in two, three lines, it has finished

77
00:06:26,870 --> 00:06:29,340
now here instead of some, I will call add some.

78
00:06:29,720 --> 00:06:31,420
Now, this will be calling this function.

79
00:06:32,540 --> 00:06:33,340
Let us run.

80
00:06:33,350 --> 00:06:34,310
I should get the same.

81
00:06:35,720 --> 00:06:40,610
Something is wrong, here are some 800 CAPITALIS is.

82
00:06:41,580 --> 00:06:42,360
Let us run it.

83
00:06:45,000 --> 00:06:48,240
Yes, Eddie, it is displaying some.

84
00:06:49,200 --> 00:06:50,670
It is a disciplined team, resolute.

85
00:06:52,290 --> 00:06:55,710
So that's all we have seen, town function announcements.

86
00:06:55,740 --> 00:07:00,090
That is a number of notes that is land of a linguist and the second and we saw.

87
00:07:01,750 --> 00:07:10,150
So this was a creative town and we saw a recursive and then some effective some then also we have seen

88
00:07:10,450 --> 00:07:11,410
recordset some.

89
00:07:12,550 --> 00:07:17,160
That's all in this video, and so we will look at it in the next video.

