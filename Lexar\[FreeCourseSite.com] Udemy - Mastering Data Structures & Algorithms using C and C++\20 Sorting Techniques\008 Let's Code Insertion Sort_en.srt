1
00:00:01,050 --> 00:00:10,200
And this video I will show you insertion sort algorithm already we have seen in our program or algorithm,

2
00:00:10,500 --> 00:00:12,160
I will read of the function for that one.

3
00:00:12,720 --> 00:00:16,910
So this is a project for sorting already have started at the time of talk.

4
00:00:16,950 --> 00:00:19,200
So I'm using the same project already.

5
00:00:19,200 --> 00:00:24,140
I have the list of elements here and a number of elements again, and this luger's for displaying all

6
00:00:24,140 --> 00:00:24,720
the elements.

7
00:00:25,750 --> 00:00:29,500
Now, here, I will ride on a function for Infusionsoft.

8
00:00:30,990 --> 00:00:38,730
So as discussed, function returns, void insertion, alcoholic's insertion, just.

9
00:00:40,950 --> 00:00:48,840
A parameter A. and number of elements now inside this, I need a few variables like IJ<PERSON> and X.

10
00:00:51,040 --> 00:00:58,420
Now, I should run a loop for the process or for that I should perform in minus one percent, so actually

11
00:00:58,420 --> 00:01:02,410
I start from one on works and is less than an.

12
00:01:03,390 --> 00:01:08,850
I plus plus and minus one out there, so that's the reason I'm starting from an onwards and societal.

13
00:01:09,830 --> 00:01:17,360
That initially should be assigned as a minus one and X, I should take it off G the element that I have

14
00:01:17,360 --> 00:01:17,570
to.

15
00:01:19,870 --> 00:01:27,850
If I that is the element that I have to insert then for shifting the elements we have use a while loop

16
00:01:27,850 --> 00:01:31,630
line is greater than minus one.

17
00:01:32,670 --> 00:01:33,150
And.

18
00:01:35,490 --> 00:01:36,810
AOF G is.

19
00:01:38,790 --> 00:01:40,020
Greater than X.

20
00:01:41,680 --> 00:01:47,170
And every time we are shifting an element of Gippsland that is at the next location, I'll copy the

21
00:01:47,170 --> 00:01:48,590
element of change.

22
00:01:49,180 --> 00:01:51,910
So if the elements then G minus minus.

23
00:01:54,210 --> 00:02:00,360
Then after the end of Lub at AOF plus one, I should copy the element that is taken an X.

24
00:02:02,010 --> 00:02:07,890
Next, this day in session sort let us call this from here main function and see that it sorts all the

25
00:02:07,890 --> 00:02:08,810
elements or not.

26
00:02:11,550 --> 00:02:14,790
Insertion of elements and a number of elements and.

27
00:02:15,830 --> 00:02:17,060
Later in the program.

28
00:02:20,170 --> 00:02:24,010
Two, three, four, five, six, seven, nine, 10, 11, 13 years it has started.

29
00:02:26,350 --> 00:02:27,980
That's it, it's working perfectly.

30
00:02:28,480 --> 00:02:31,840
So that's all in this video, you can write on this function and write by yourself.

