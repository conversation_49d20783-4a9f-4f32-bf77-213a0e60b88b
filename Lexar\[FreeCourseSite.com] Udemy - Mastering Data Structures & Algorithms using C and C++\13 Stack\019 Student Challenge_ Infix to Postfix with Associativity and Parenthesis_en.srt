1
00:00:00,450 --> 00:00:08,580
And this video will learn how to convert infix expression into postfix expression if infix expression

2
00:00:08,580 --> 00:00:15,900
is having parentheses and those operators whose associativity is right to left.

3
00:00:16,560 --> 00:00:21,660
In the previous video we have seen, there was only addition, subtraction, multiplication, division.

4
00:00:21,660 --> 00:00:26,430
Only four operators are taken and they're associativity, was left to right.

5
00:00:26,580 --> 00:00:34,240
But we did not handle any operators who associate DVDs right to left and even we did not consider brackets.

6
00:00:34,920 --> 00:00:40,530
So if you consider brackets and the associativity both left to right and right to left, then this procedure

7
00:00:40,530 --> 00:00:44,390
works for any number of operators, any type of operators.

8
00:00:44,460 --> 00:00:49,630
So what we are learning with a limited set of operators for understanding the procedure.

9
00:00:50,100 --> 00:00:56,370
So I have the operators here, addition, subtraction, multiplication, division over what is right

10
00:00:56,370 --> 00:00:57,740
to left associative.

11
00:00:57,750 --> 00:01:04,129
These are left to right associative and I have opening bracket as well as closing bracket.

12
00:01:04,890 --> 00:01:09,410
Then there is something written here that is old stack precedence in stack precedence.

13
00:01:10,050 --> 00:01:10,890
I will explain.

14
00:01:10,890 --> 00:01:11,520
What is that?

15
00:01:13,090 --> 00:01:18,340
I been through the procedure and writing a program is student exercise, so you have to write a program

16
00:01:18,340 --> 00:01:18,960
for this thing.

17
00:01:19,210 --> 00:01:23,470
So I will explain the procedure so far, explaining the procedure to have taken an expression which

18
00:01:23,470 --> 00:01:28,380
is also having parentheses and right to left associative operator.

19
00:01:28,510 --> 00:01:29,410
But are there.

20
00:01:30,310 --> 00:01:36,710
So this expression, let us first understand how to convert it into postfix using pen and paper.

21
00:01:36,760 --> 00:01:39,980
First, let us get the result once we know the result.

22
00:01:40,030 --> 00:01:45,050
Then from the procedure, we can confirm overexerted, let us do it manually.

23
00:01:45,770 --> 00:01:48,620
So before doing it, I will add one more parenthesis here.

24
00:01:48,880 --> 00:01:53,920
This one, I will enclose it for converting into postfix who is having highest precedence.

25
00:01:54,130 --> 00:01:59,420
Obviously brackets are having highest precedence, rate brackets are having highest precedence.

26
00:01:59,710 --> 00:02:04,630
So in this bracket, we should we should convert that into postfix, but insert that one more bracket

27
00:02:04,630 --> 00:02:07,480
as that come this one first into postfix.

28
00:02:07,480 --> 00:02:12,520
So this is a B plus, put it into the bracket, then start C again.

29
00:02:12,520 --> 00:02:15,490
Bracket is then minus Deepavali for S.

30
00:02:16,480 --> 00:02:22,140
Then which one, this bracket, this bracket, so this we have finished this bracket, so left side,

31
00:02:22,150 --> 00:02:26,420
right side, then stop left side, right side, then start.

32
00:02:26,620 --> 00:02:33,870
This is gunwalking minus deep E as that this is completed minus Poveda Power.

33
00:02:34,480 --> 00:02:38,100
How you precedences power but associativity right to left.

34
00:02:38,110 --> 00:02:39,200
So convert this one.

35
00:02:39,490 --> 00:02:48,130
So this will be a B plus a C start minus deep e f power in bracket.

36
00:02:49,550 --> 00:02:57,380
Now, an explanation, this power, so let's say this is Rightside, so this is a B plus seastar, minus

37
00:02:57,830 --> 00:03:01,520
BBF of power and this power goes at the end.

38
00:03:01,790 --> 00:03:04,760
Now this is completed, is completed only necessary meaning.

39
00:03:05,000 --> 00:03:08,180
So final answer is A, B plus C star.

40
00:03:09,520 --> 00:03:17,410
DPF power, power minus does the postfix, I don't know the answer there, so that when we finish the

41
00:03:17,410 --> 00:03:19,110
procedure we should get the same answer.

42
00:03:20,150 --> 00:03:23,280
So when we convert the expression, we should get this answer.

43
00:03:24,050 --> 00:03:25,520
Now let us see the procedure.

44
00:03:27,380 --> 00:03:32,990
Now let us see the procedure, how it takes a stack and convert it to postfix with the help of this

45
00:03:33,020 --> 00:03:33,950
president's table.

46
00:03:34,430 --> 00:03:35,800
Let us see the procedure.

47
00:03:36,050 --> 00:03:37,580
So why are you showing me the procedure?

48
00:03:37,700 --> 00:03:42,530
Explain why we have a whole stack of precedences and instead precedences.

49
00:03:42,860 --> 00:03:43,640
I'll explain.

50
00:03:44,060 --> 00:03:47,260
And the procedure is similar to the one we have seen earlier.

51
00:03:47,270 --> 00:03:52,790
Same procedure, but the little bit difference, let us see a state of the state going to have to scan

52
00:03:52,790 --> 00:03:55,560
for this and fix the expression by taking one simple at a time.

53
00:03:55,880 --> 00:03:59,430
So what is the first assembly opening bracket?

54
00:03:59,690 --> 00:04:02,240
What is the president's seven?

55
00:04:03,590 --> 00:04:04,400
Why not zero?

56
00:04:05,030 --> 00:04:10,430
This is old, said the stack, and it's not inside the stack, so I'll check the statements but seven.

57
00:04:10,460 --> 00:04:13,930
OK, now is there anything in the stack?

58
00:04:13,940 --> 00:04:14,690
Nothing is there.

59
00:04:14,690 --> 00:04:15,790
That is of how you present.

60
00:04:15,980 --> 00:04:17,779
So push that into the bracket.

61
00:04:18,079 --> 00:04:21,100
So once it has came in, this has came in.

62
00:04:21,440 --> 00:04:26,060
What is the precedent of this now inside the stack in stack precedences zero.

63
00:04:26,420 --> 00:04:27,610
OK, this is the.

64
00:04:28,560 --> 00:04:30,030
Then move to the next symbol.

65
00:04:31,260 --> 00:04:33,520
What is the next symbol again, looping back?

66
00:04:33,810 --> 00:04:38,190
What is the president's seven seven, what is there in the stack?

67
00:04:38,310 --> 00:04:40,260
This one, what is the president's now?

68
00:04:40,470 --> 00:04:41,090
Zero.

69
00:04:41,460 --> 00:04:42,750
So that is seven.

70
00:04:42,780 --> 00:04:44,910
This is seven and this is zero.

71
00:04:44,910 --> 00:04:47,400
So seven is a greater push it into the stack.

72
00:04:47,910 --> 00:04:51,720
Once it came in, said the Stockett's president became Zettl a.

73
00:04:53,020 --> 00:04:56,890
This is open and open and send it to postfix.

74
00:04:57,930 --> 00:04:58,750
Next, a symbol.

75
00:04:59,370 --> 00:05:03,270
Plus, what is the president's precedence, it is old, say.

76
00:05:03,300 --> 00:05:06,630
So this one and what is the president's stacked top?

77
00:05:06,630 --> 00:05:07,320
This is zero.

78
00:05:07,680 --> 00:05:08,240
Push it.

79
00:05:08,850 --> 00:05:11,070
Not once it came inside presidents.

80
00:05:11,070 --> 00:05:16,410
We came to Nixon, but this B B is an open Senate postfix.

81
00:05:17,560 --> 00:05:24,970
The Nixon bill is closing bracket, so what is this president's zero C o stack, president zero in stack.

82
00:05:25,330 --> 00:05:27,100
It is never pushed into the stack.

83
00:05:27,120 --> 00:05:28,840
Why we should push closing bracket.

84
00:05:29,320 --> 00:05:30,790
That's why it's presidents.

85
00:05:30,790 --> 00:05:33,780
It's not that only old sales tax.

86
00:05:33,790 --> 00:05:35,440
It's all said only it's not pushed.

87
00:05:35,470 --> 00:05:37,790
We don't push it and it's zero.

88
00:05:38,110 --> 00:05:38,850
What to do now.

89
00:05:39,250 --> 00:05:41,410
This is zero but this is two.

90
00:05:42,100 --> 00:05:42,950
This is greater.

91
00:05:43,180 --> 00:05:44,820
This is greater about.

92
00:05:46,180 --> 00:05:51,230
And Annika Postfix check next, this is zero, this is zero.

93
00:05:51,580 --> 00:05:53,630
Oh, dear, President Assad equal.

94
00:05:53,950 --> 00:05:59,640
So this is the only situation where we get precedences equal when opening and closing bracket.

95
00:05:59,650 --> 00:06:00,070
Then what?

96
00:06:00,070 --> 00:06:02,260
We should go pop out.

97
00:06:03,280 --> 00:06:07,060
Don't agree to postfix that they don't happen.

98
00:06:07,210 --> 00:06:12,780
Simply skip it and move to next because we got a match move to next.

99
00:06:15,100 --> 00:06:19,660
That's what you have to do when the president says are equal and they will be equal only in those situations

100
00:06:19,660 --> 00:06:24,850
where you have a closing bracket here, an opening bracket here otherwise, and this president says

101
00:06:25,270 --> 00:06:28,760
that is instead of an old stack, precedences will never be equal.

102
00:06:29,230 --> 00:06:30,070
Let us continue.

103
00:06:31,180 --> 00:06:41,590
Star outside presidents three in STAC zero, Bush once Bush, what is the president's for?

104
00:06:43,210 --> 00:06:49,930
S. O'Brien, Senate closing bracket precedent's zero, this one for

105
00:06:52,990 --> 00:06:56,200
president zero is also zero pop out.

106
00:06:56,530 --> 00:07:03,880
Don't target that move to make Simbel minus precedences one energy in the stack.

107
00:07:03,880 --> 00:07:05,620
No, push it now.

108
00:07:05,620 --> 00:07:07,270
Precedences how much do.

109
00:07:08,860 --> 00:07:15,310
Next, somebody, the next symbol, power, what is this president's six?

110
00:07:17,450 --> 00:07:27,920
This is two, so that is great, push it, push once it becomes five oh, see if you observe in nonparticipants

111
00:07:27,920 --> 00:07:34,120
was increasing when it was coming in, but not decreasing reason it is right to lift associative.

112
00:07:34,400 --> 00:07:41,090
So the purpose of giving two presidencies is that if it is left to right, let it increase.

113
00:07:41,090 --> 00:07:45,320
When it is going inside the stack, if it is right to left, let it decrease.

114
00:07:45,710 --> 00:07:52,850
So if you want to add more operators and this one you can add and depending on the associativity you

115
00:07:52,850 --> 00:07:58,020
can increase or decrease the precedences c power is having right to left associative.

116
00:07:58,310 --> 00:07:59,420
So this is decreasing.

117
00:07:59,580 --> 00:08:02,090
It should be five six, but it is written in reverse.

118
00:08:02,090 --> 00:08:03,730
That is five there and six here.

119
00:08:04,160 --> 00:08:07,910
So that's so when it came inside, its precedent's became five.

120
00:08:09,580 --> 00:08:17,050
Mixed symbol, e operand, mixed symbol, this is homage six six and what is the presidency of five

121
00:08:17,290 --> 00:08:18,270
that is greater?

122
00:08:18,310 --> 00:08:22,090
It will come inside after pushing what is the president's five.

123
00:08:22,760 --> 00:08:23,100
Right.

124
00:08:23,530 --> 00:08:27,960
So before pushing me, check the condition that after that it is Bush now the president's five.

125
00:08:28,810 --> 00:08:38,950
Right next symbol as s open, it makes the symbol finish end of this expression of the symbol from the

126
00:08:38,950 --> 00:08:46,750
stack power, power minus power, power minus a B plus see a B plus.

127
00:08:46,750 --> 00:08:52,330
He started the F, started the F power over minus PowerCore minus.

128
00:08:53,400 --> 00:08:55,780
We got the so end of the procedure.

129
00:08:56,160 --> 00:09:01,650
Let me once again tell you here, we have included the brackets, brackets having highest precedence.

130
00:09:01,920 --> 00:09:05,190
If you want more operators, you can add them and give the precedences.

131
00:09:05,190 --> 00:09:08,250
As you know, you give the precedences that are your operators.

132
00:09:09,000 --> 00:09:15,570
If any operators left-to-right associated, then increase the president from old students stack, if

133
00:09:15,570 --> 00:09:17,720
it is right to left, then decrease the president.

134
00:09:17,740 --> 00:09:21,240
So in this way you can add as many operators as you want in this one.

135
00:09:21,750 --> 00:09:25,950
So this has given a complete idea by using limited set of operators.

136
00:09:26,890 --> 00:09:30,580
So you have to write on the program, as I said, this is student exercise.

