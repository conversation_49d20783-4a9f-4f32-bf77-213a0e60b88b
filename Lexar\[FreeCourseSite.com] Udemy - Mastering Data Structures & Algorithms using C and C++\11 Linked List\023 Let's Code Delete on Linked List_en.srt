1
00:00:00,120 --> 00:00:05,730
And this video will write a function for deleting <PERSON><PERSON><PERSON> from McGivern linked list at a given position.

2
00:00:05,730 --> 00:00:07,800
So I'm using the same project here.

3
00:00:07,810 --> 00:00:09,990
Main function is already creating a link list.

4
00:00:10,320 --> 00:00:13,570
Now, before the main function, I will write on a function for deleting A..

5
00:00:14,100 --> 00:00:22,230
So let us write a function delete and it should take a pointer to the first node and index from which

6
00:00:22,230 --> 00:00:23,510
we want to delete an element.

7
00:00:23,520 --> 00:00:31,140
So inside this function I may need a few variables like a pointer to a node that is a pointer and also

8
00:00:31,140 --> 00:00:32,680
to store a deleted element.

9
00:00:32,700 --> 00:00:37,760
I will take a variable X and then that I will assign minus one before deleting an element.

10
00:00:37,770 --> 00:00:40,370
I will check whether the indices are given valid or not.

11
00:00:40,650 --> 00:00:46,020
So the possible indices are starting from one onwards, as we have discussed on whiteboard, and this

12
00:00:46,020 --> 00:00:51,960
is starting from one on works and that will go up to the length of a class so I can give the index from

13
00:00:51,960 --> 00:00:52,830
one to length.

14
00:00:53,220 --> 00:01:00,180
So here I will write on the condition that if index is less than one done, it is invalid, or if the

15
00:01:00,180 --> 00:01:04,650
index is greater than the following list that is following the following list.

16
00:01:04,650 --> 00:01:06,690
We have a function called down to that.

17
00:01:06,690 --> 00:01:10,270
We will send a pointer B, which is a pointer to first note.

18
00:01:10,770 --> 00:01:18,610
So if this is the case, that is either index is less than one or indexes greater than down then at

19
00:01:18,670 --> 00:01:19,710
the invalid index.

20
00:01:19,720 --> 00:01:21,930
So I should simply return minus one.

21
00:01:21,930 --> 00:01:23,730
That is element is not deleted.

22
00:01:23,730 --> 00:01:26,000
So or else I can say X also here.

23
00:01:26,580 --> 00:01:30,400
So element is not deleted, otherwise we can insert an element.

24
00:01:30,420 --> 00:01:32,550
So this is the extra condition I'm showing here.

25
00:01:32,550 --> 00:01:34,640
This I have not discussed on whiteboard.

26
00:01:34,650 --> 00:01:40,410
Now as part of a discussion here, we have two cases that is inserting Fastenal or inserting any other.

27
00:01:40,680 --> 00:01:47,070
So first of all, if the given index is equal to one, then we have to handle it, especially that this

28
00:01:47,070 --> 00:01:47,920
is the first node.

29
00:01:47,940 --> 00:01:53,640
So if it is the first node, then I should make a Q point upon the first node, as does the first index.

30
00:01:53,640 --> 00:01:59,190
Then I should take the data from first node and I should move the first point to the next node.

31
00:01:59,190 --> 00:02:03,450
Then I should delete the previous first node that is.

32
00:02:03,450 --> 00:02:04,890
Q where he was pointing.

33
00:02:04,890 --> 00:02:09,289
Then after this I should return X the deleted element as an X.

34
00:02:09,300 --> 00:02:12,900
So this is the first case though the pointer here is maybe different.

35
00:02:12,900 --> 00:02:15,120
So be or do whatever the point that is use.

36
00:02:15,390 --> 00:02:16,830
So logic is same next.

37
00:02:16,830 --> 00:02:23,020
Otherwise means if it is not first index then any other index, then if it is any other index then I'm

38
00:02:23,070 --> 00:02:23,970
using a follow up.

39
00:02:23,970 --> 00:02:30,790
I should use NQ to move them and make a point on the exact node which we want to delete.

40
00:02:30,810 --> 00:02:34,020
So for that, using a follow up, I need one more variable.

41
00:02:34,020 --> 00:02:36,330
I so here I will declare a variable.

42
00:02:36,330 --> 00:02:45,510
I then using a follow I will move this pointer point you I assign zero, I use less than index minus

43
00:02:45,510 --> 00:02:47,790
one and I placeless.

44
00:02:47,790 --> 00:02:52,110
So already I have checked for valid index so I don't have to check anything extra here.

45
00:02:52,110 --> 00:02:58,140
And every time you will move moved up on E and we will move to next node.

46
00:02:58,290 --> 00:03:03,210
Then after moving the end queue for the minus one time he will be pointing on the node which we want

47
00:03:03,210 --> 00:03:03,740
to delete.

48
00:03:03,750 --> 00:03:09,810
So as we have already seen, the steps that kills next will be pointing on PS next.

49
00:03:10,000 --> 00:03:13,200
So now P will be logically removed from a link list.

50
00:03:13,500 --> 00:03:23,160
Then in X we will take the data from these data and then delete P and finally set it on X, the deleted

51
00:03:23,600 --> 00:03:24,170
NetSol.

52
00:03:24,180 --> 00:03:27,170
So this is the portion of the code that we have already seen.

53
00:03:27,180 --> 00:03:33,660
Then before this I have added extra line that is checking for validity of index delete function is ready.

54
00:03:34,060 --> 00:03:38,790
Now let me check it once or see the return type as integer type.

55
00:03:38,790 --> 00:03:42,150
Initially I wrote it as void delete return type of integer.

56
00:03:42,150 --> 00:03:46,500
The value we are returning, the deleted value we are returning and this pointer.

57
00:03:46,500 --> 00:03:54,840
Q is a telling point so it can be initialize to zero and for deleting n I should use a function free

58
00:03:55,170 --> 00:03:56,940
instead of calling delete.

59
00:03:57,300 --> 00:04:03,360
Delete is a keyword that is in C++, but here we should call a function.

60
00:04:03,360 --> 00:04:05,150
Free is everything is perfect.

61
00:04:05,160 --> 00:04:08,370
Now let us go back to main function inside the main function already.

62
00:04:08,370 --> 00:04:11,280
I have a link list with the set of elements here.

63
00:04:11,280 --> 00:04:12,370
That is ten to 50.

64
00:04:12,390 --> 00:04:14,400
Let us delete forty and checked.

65
00:04:14,580 --> 00:04:20,610
So after creating a link list I will call the delete function and I will give the first pointer as a

66
00:04:20,610 --> 00:04:22,440
parameter and indexes.

67
00:04:22,460 --> 00:04:24,860
If I want to delete forty then it is fortunate.

68
00:04:24,870 --> 00:04:28,520
Lexie, this is first and second and third and fourth node.

69
00:04:28,530 --> 00:04:32,250
Yes, I should give them access for forty should be deleted.

70
00:04:32,640 --> 00:04:35,860
And after that when I display a link I should not get forty.

71
00:04:36,140 --> 00:04:37,680
Let us run the program and see.

72
00:04:37,920 --> 00:04:43,780
Yes, I'm getting the N that is 10, 20, 30 and 50 40 is deleted.

73
00:04:44,730 --> 00:04:47,550
Let us check whether it is deleting fifth element or not.

74
00:04:47,850 --> 00:04:49,980
Yes, it is deleting fifth element also.

75
00:04:50,280 --> 00:04:51,530
What about the first element.

76
00:04:51,660 --> 00:04:55,320
Yes it is deleting and also that is 20, 30, 40, 50 Vegard.

77
00:04:55,320 --> 00:04:57,630
If I give it next to zero then what happens.

78
00:04:57,660 --> 00:04:58,860
Nothing is deleted.

79
00:04:58,860 --> 00:04:59,970
That is ten to fifteen.

80
00:05:00,090 --> 00:05:05,130
All the elements are as it is, nothing is deleted if I give the next size eight.

81
00:05:05,160 --> 00:05:08,550
There are no eight elements here and it is invalid index.

82
00:05:09,060 --> 00:05:10,700
So it's not deleting anything.

83
00:05:10,710 --> 00:05:12,310
All the elements are as it is.

84
00:05:12,330 --> 00:05:15,530
I'll make one more change here that I'm calling delete function.

85
00:05:15,540 --> 00:05:22,380
I will directly print its result that is deleted element person Tildy and slash.

86
00:05:22,380 --> 00:05:26,270
And so whatever the element is deleted, it will display that element.

87
00:05:26,280 --> 00:05:27,960
Also let us run it again.

88
00:05:27,960 --> 00:05:29,730
Delete element is minus fundaments.

89
00:05:29,730 --> 00:05:33,630
It is invalid index and I'm getting all the elements as it is in the link lists.

90
00:05:33,930 --> 00:05:39,150
So instead of eight I will give the index as to so 30 should be deleted is deleted.

91
00:05:39,150 --> 00:05:42,330
Element is 20 then I'm having 10, 30, 40 and 50.

92
00:05:42,360 --> 00:05:43,650
Yes, it is perfect.

93
00:05:43,660 --> 00:05:47,490
So that's all in this video we have completed delete function.

94
00:05:47,490 --> 00:05:50,510
See here we are writing all the functions up on a link list.

95
00:05:50,550 --> 00:05:54,000
So it is becoming a single big program useful for links.

96
00:05:54,000 --> 00:05:59,340
So further in our subject, in our course, whenever we require a link list, we will be using this

97
00:05:59,340 --> 00:06:04,650
as a header file and we'll be including it and we'll be calling on the functions, whatever we need.

