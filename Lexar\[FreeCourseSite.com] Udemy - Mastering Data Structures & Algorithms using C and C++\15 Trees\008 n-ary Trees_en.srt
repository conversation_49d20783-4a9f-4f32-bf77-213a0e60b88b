1
00:00:00,270 --> 00:00:08,630
And this topic will learn about water and trees and what are street energy trees just like struck by

2
00:00:08,710 --> 00:00:08,980
trees.

3
00:00:09,000 --> 00:00:17,670
You can also have energy trees, then height versus north of strictly trees, then internal for external

4
00:00:17,670 --> 00:00:18,140
laws.

5
00:00:18,390 --> 00:00:20,760
So here, one thing you can observe, these are energy trees.

6
00:00:20,760 --> 00:00:27,000
But I have given the heading as <PERSON><PERSON> and <PERSON><PERSON><PERSON> are calling the number of nodes, number of

7
00:00:27,000 --> 00:00:27,330
nodes.

8
00:00:27,330 --> 00:00:27,530
Right.

9
00:00:27,570 --> 00:00:32,000
In previous videos we have called and as nodes so it can create confusion.

10
00:00:32,340 --> 00:00:38,940
So for degree I gave the names and let us learn what our energy is.

11
00:00:38,970 --> 00:00:47,220
Just video we learn about and the trees and the tree and the degree of our tree.

12
00:00:48,310 --> 00:00:54,700
Degree means every node in a tree can have at most and children, not more than 10 children.

13
00:00:54,850 --> 00:00:57,160
Let us take the examples and study.

14
00:00:57,640 --> 00:01:01,960
See here the example is a tree, a three hour tree with a degree tree.

15
00:01:02,200 --> 00:01:07,820
So every node can have a zero one, two and three children, any of these.

16
00:01:08,290 --> 00:01:12,000
So the capacity of each node is maximum tree.

17
00:01:12,340 --> 00:01:16,270
It can be from zero to three, but not more than three.

18
00:01:16,780 --> 00:01:18,880
So if it is less also, you should accept it.

19
00:01:19,630 --> 00:01:21,130
These are my examples.

20
00:01:21,130 --> 00:01:24,930
I have drawn them and seeing that these are all three trees.

21
00:01:26,020 --> 00:01:28,100
So I have declared that these are three.

22
00:01:28,480 --> 00:01:33,130
So let us check really the tree or you're not the first one degree.

23
00:01:33,140 --> 00:01:34,270
Three degrees.

24
00:01:34,270 --> 00:01:35,170
Two degrees.

25
00:01:35,170 --> 00:01:36,280
Three degrees.

26
00:01:36,280 --> 00:01:37,000
Zero zero.

27
00:01:37,450 --> 00:01:37,860
Yes.

28
00:01:38,040 --> 00:01:45,430
No node is having degree more than three years, percent or so 383.

29
00:01:46,210 --> 00:01:47,110
What about this?

30
00:01:48,330 --> 00:01:55,020
Degrees, two degrees, three degrees, one degrees, zero degrees zero, yes, no one is having a more

31
00:01:55,020 --> 00:02:02,490
than three years, 33 degrees, two degrees, two degrees, zero zero zero.

32
00:02:03,510 --> 00:02:05,240
No notice having degrees.

33
00:02:05,520 --> 00:02:06,630
More than three.

34
00:02:06,900 --> 00:02:08,110
Yes, sir.

35
00:02:08,479 --> 00:02:09,289
383.

36
00:02:09,930 --> 00:02:15,400
It's looking like a binary tree, is it not a binary tree if it is also a tree.

37
00:02:15,510 --> 00:02:22,980
But I said that every node have the capacity to have three children if they have less than no problem.

38
00:02:24,030 --> 00:02:26,410
But I said that this is theory.

39
00:02:26,460 --> 00:02:27,260
So this theory.

40
00:02:27,840 --> 00:02:31,330
So who will decide the degree of a tree by looking at the tree?

41
00:02:31,350 --> 00:02:35,750
We cannot decide the degree right degree of a tree is a pre decided.

42
00:02:35,940 --> 00:02:37,470
So that's what I'm drawing the trees.

43
00:02:37,470 --> 00:02:39,030
I said that these are three.

44
00:02:39,480 --> 00:02:42,590
So based on the condition that said.

45
00:02:43,610 --> 00:02:46,880
So from that, you cannot judge its degree.

46
00:02:47,040 --> 00:02:50,060
Now I have one more example, let us look at this for.

47
00:02:50,570 --> 00:02:56,000
So every note can have zero one, two, three, four children, not more than four children right now.

48
00:02:56,000 --> 00:02:58,370
This are three, two children.

49
00:02:58,370 --> 00:02:59,920
Four children, two children.

50
00:02:59,930 --> 00:03:00,790
Yes, perfect.

51
00:03:01,070 --> 00:03:02,410
Two children, three children.

52
00:03:02,810 --> 00:03:03,250
Perfect.

53
00:03:03,410 --> 00:03:09,770
See, this also is looking like a theory, but I said that every note is having capacity of four and

54
00:03:09,770 --> 00:03:11,840
I am drawing it as a forgery.

55
00:03:12,350 --> 00:03:15,100
As for the conditions, yes, I just for it.

56
00:03:15,770 --> 00:03:16,640
What about this one?

57
00:03:17,700 --> 00:03:23,160
One, two, three, four, five, no notes cannot have the degree, more than four.

58
00:03:23,550 --> 00:03:25,620
This is not a territory.

59
00:03:25,770 --> 00:03:26,770
So what does it mean?

60
00:03:26,790 --> 00:03:28,230
I'll explain you a little bit.

61
00:03:30,710 --> 00:03:36,400
See if you have the idea, I'm assuming that you already know this type of thing, so no structure,

62
00:03:36,650 --> 00:03:38,150
so all the value in the year is.

63
00:03:39,140 --> 00:03:39,950
Now this a..

64
00:03:40,220 --> 00:03:43,820
How many children it can have for children it can have.

65
00:03:44,420 --> 00:03:48,250
So what is the degree of a normal having four children?

66
00:03:48,920 --> 00:03:55,400
So all the laws in our degree for will have the capacity of having four children.

67
00:03:56,030 --> 00:03:58,940
Suppose this node is not having all four.

68
00:03:59,300 --> 00:04:00,080
This is null.

69
00:04:00,320 --> 00:04:02,120
Then what is the degree of a..

70
00:04:02,330 --> 00:04:03,040
Just a three.

71
00:04:03,290 --> 00:04:05,420
But what is the capacity for.

72
00:04:07,300 --> 00:04:13,840
If the degree is a three, we will not say this node is belonging to 383, no see the capacity that

73
00:04:13,840 --> 00:04:18,589
is another that if suppose this node is also not dead, then it's binary.

74
00:04:19,000 --> 00:04:25,530
No, based on the node, we cannot see the capacity this node belongs to for three.

75
00:04:25,840 --> 00:04:26,190
Right.

76
00:04:26,500 --> 00:04:29,200
No doubt a node is not having all four children.

77
00:04:29,500 --> 00:04:33,820
That's why I said if I'm making a tree, then I will decide the degree of our time.

78
00:04:34,480 --> 00:04:37,480
Next, we'll be looking at strict and trees.

79
00:04:37,900 --> 00:04:50,820
Now, strict tree emery tree is one in which every node can have either a zero children or exactly children.

80
00:04:51,340 --> 00:04:52,120
For example.

81
00:04:52,120 --> 00:04:54,520
I have a stick the three year tree here.

82
00:04:55,210 --> 00:04:56,700
These are some example trees.

83
00:04:57,070 --> 00:05:00,700
Let us check which of these are strictly trees?

84
00:05:01,900 --> 00:05:08,230
Students really means every node can have either zero children or exactly three children.

85
00:05:09,380 --> 00:05:10,940
Not less, not more.

86
00:05:11,090 --> 00:05:17,240
Let us look at the first example degree of this is three, OK, perfect degree of this is three.

87
00:05:17,240 --> 00:05:17,710
Perfect.

88
00:05:17,840 --> 00:05:18,920
So these two are over.

89
00:05:19,200 --> 00:05:21,920
Degree of this is zero zero zero degree.

90
00:05:21,920 --> 00:05:22,860
Zero degrees zero.

91
00:05:22,880 --> 00:05:23,870
Yes, perfect.

92
00:05:24,230 --> 00:05:26,060
Degrees zero or three.

93
00:05:26,060 --> 00:05:26,380
Right.

94
00:05:26,600 --> 00:05:28,580
So only zero or three.

95
00:05:28,580 --> 00:05:31,160
Only two degrees are allowed degrees.

96
00:05:31,160 --> 00:05:35,240
Three degrees, three degrees, three degrees zero zero.

97
00:05:35,240 --> 00:05:36,160
All these are zero.

98
00:05:36,470 --> 00:05:37,010
Yes.

99
00:05:37,200 --> 00:05:40,550
This is also strictly three degrees.

100
00:05:40,550 --> 00:05:43,640
Three degrees zero zero degrees.

101
00:05:43,640 --> 00:05:44,740
Two wrong.

102
00:05:45,380 --> 00:05:47,030
This is not a strict binary.

103
00:05:47,030 --> 00:05:48,910
One of the node is having a degree too.

104
00:05:48,920 --> 00:05:53,540
So it's not a strictly three degrees.

105
00:05:53,540 --> 00:05:56,540
Three degrees do no wrong.

106
00:05:57,350 --> 00:05:58,100
That's enough.

107
00:05:58,100 --> 00:06:00,290
One node is violating that condition.

108
00:06:00,680 --> 00:06:02,840
Then I don't have to check the rest of the nodes.

109
00:06:03,260 --> 00:06:06,790
So this is not strictly a training.

110
00:06:07,040 --> 00:06:10,340
So these are the two examples of strictly adultry.

