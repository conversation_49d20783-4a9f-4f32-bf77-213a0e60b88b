1
00:00:00,360 --> 00:00:06,420
Let us look at insertions hard for explaining in session thought I have taken an array of elements of

2
00:00:06,420 --> 00:00:08,700
five elements out there to reduce water.

3
00:00:08,730 --> 00:00:15,570
OK, I have just avoided indices and even they are not required to let us perform in such thought.

4
00:00:16,050 --> 00:00:19,280
The very first thing is I have five elements here.

5
00:00:19,560 --> 00:00:23,680
So we assume that the first element is what it is socket.

6
00:00:24,390 --> 00:00:27,140
If there is only one element, then obviously it is sorted.

7
00:00:27,480 --> 00:00:32,580
So we assume that just the first element, then the remaining elements are not sorted.

8
00:00:32,880 --> 00:00:38,480
So remaining elements will take hold from another and insert them on the site in the socket list.

9
00:00:38,760 --> 00:00:42,330
So take out an element inside the site, take out an element and to decide.

10
00:00:42,630 --> 00:00:46,100
So right now in an array, one element is sorted.

11
00:00:46,320 --> 00:00:48,300
Four elements are not sorted.

12
00:00:50,040 --> 00:00:56,190
One element that is eight assorting, so from the unsorted list will take out an element and insert

13
00:00:56,190 --> 00:01:00,750
the site, take out an element and insert the site before the procedures.

14
00:01:01,110 --> 00:01:02,670
So let us start the process.

15
00:01:03,270 --> 00:01:12,180
First step, I will insert five, insert five so far inserting five C first element eight is as it is,

16
00:01:12,180 --> 00:01:13,420
I'm inserting five.

17
00:01:13,710 --> 00:01:19,050
So the remaining elements as it is now, the list will be extending to two elements before inserting

18
00:01:19,050 --> 00:01:22,370
five check with this element eight is greater than five.

19
00:01:22,380 --> 00:01:23,170
So shifted.

20
00:01:23,780 --> 00:01:26,520
Now there is nothing, there's nothing to check.

21
00:01:26,790 --> 00:01:28,370
So we have reached the first place.

22
00:01:28,650 --> 00:01:32,330
So insert five at this place after inserting five.

23
00:01:32,340 --> 00:01:33,270
The result is.

24
00:01:34,320 --> 00:01:40,970
Five than eight still here, the list is remaining seven, three, two, they are not yet sorted.

25
00:01:41,280 --> 00:01:47,250
So I have shown here how to insert five hoggish of Deliman and this derivate for one element we have

26
00:01:47,250 --> 00:01:47,800
inserted.

27
00:01:48,030 --> 00:01:50,670
So this let us call it as a first pass.

28
00:01:52,410 --> 00:01:53,640
First pass.

29
00:01:53,640 --> 00:01:55,950
Then second pass in second pass.

30
00:01:55,950 --> 00:01:57,330
Let us insert seven.

31
00:02:00,940 --> 00:02:06,970
In the list already, five and eight are no, we are trying to insert seven, so list will extend till

32
00:02:06,970 --> 00:02:10,979
here and the remaining elements are these these are not yet sorted.

33
00:02:10,990 --> 00:02:16,990
So seven, we are going to insert, so seven, take it outside, not for inserting seven starts shifting

34
00:02:16,990 --> 00:02:21,010
the elements which are larger than seven from right hand side eight.

35
00:02:21,100 --> 00:02:25,840
It is great to find it's not a creator, so seven should be inserted here.

36
00:02:26,560 --> 00:02:29,230
So the result after inserting seven is.

37
00:02:30,490 --> 00:02:35,680
Five, seven, eight and three to kill three elements listed.

38
00:02:36,250 --> 00:02:37,990
We will also analyze this one.

39
00:02:39,090 --> 00:02:46,500
See how many elements were compared, just one comparison, how many fleischmann's, how much it.

40
00:02:46,860 --> 00:02:48,560
Only one element was shifted.

41
00:02:48,570 --> 00:02:51,750
So let let's say one slap left in the second box.

42
00:02:52,080 --> 00:02:58,150
How many elements there compared in our example, only one element was compared, but at most how many?

43
00:02:58,170 --> 00:03:01,160
Both eight and five can move possible.

44
00:03:01,170 --> 00:03:04,240
If the number is a smaller than five, then both can move.

45
00:03:04,530 --> 00:03:12,600
So let us write on maximum competitions and the maximum slaps for maximum competitions are two competitions

46
00:03:12,930 --> 00:03:16,140
and the maximum slaps are to slap.

47
00:03:17,010 --> 00:03:20,850
Now we have to insert two more elements and sort two more elements.

48
00:03:20,880 --> 00:03:24,150
Let me continue with the rest of the elements.

49
00:03:24,660 --> 00:03:25,620
The resolution today.

50
00:03:25,650 --> 00:03:26,520
I will take it here.

51
00:03:29,500 --> 00:03:32,980
Not hard pass inside the three, so.

52
00:03:35,050 --> 00:03:38,680
Insert three, safwat inserting three, take out the three.

53
00:03:39,960 --> 00:03:42,970
And the list of sites will be still here.

54
00:03:43,830 --> 00:03:47,340
Does the freespace now start shifting the element?

55
00:03:47,340 --> 00:03:54,180
Age is greater, shifted seven as greater shifted three is greater shift to the three here.

56
00:03:58,070 --> 00:04:02,540
Foster shifting and inserting three, the list looks like this.

57
00:04:03,940 --> 00:04:07,900
Still here, the list of Sodor three five, seven eight, then this is to.

58
00:04:08,970 --> 00:04:12,270
Then here, how many competitions and how many?

59
00:04:12,450 --> 00:04:18,339
I've done three, three competitions and three slaps, right?

60
00:04:18,690 --> 00:04:19,690
These are maximum.

61
00:04:19,920 --> 00:04:21,880
Now, one last element is remaining two.

62
00:04:22,050 --> 00:04:24,240
So this is the fourth pass we will perform.

63
00:04:26,380 --> 00:04:33,100
And so the two were willing to do so in this area when we inserted two from the side will take all two

64
00:04:33,320 --> 00:04:36,270
and the list of sites will be maximum.

65
00:04:36,280 --> 00:04:41,290
Now, the last element presently, certain elements are these.

66
00:04:41,300 --> 00:04:42,590
That is three five, seven, eight.

67
00:04:42,940 --> 00:04:46,360
This is a free freespace and we'll start competing and shifting the elements.

68
00:04:46,360 --> 00:04:47,470
Eight is greater than two.

69
00:04:47,470 --> 00:04:50,800
It will shift to seven also and five also and three also.

70
00:04:51,100 --> 00:04:55,720
Then two will come at this place after shifting and copying the elements.

71
00:04:59,110 --> 00:05:03,980
Final array looks like this two, three, five, seven, eight.

72
00:05:04,120 --> 00:05:04,920
So that's it.

73
00:05:04,960 --> 00:05:09,360
This is sort of you got to sort it out every time from right hand side.

74
00:05:09,370 --> 00:05:12,970
We are taking on an element and inserting it on the left hand side.

75
00:05:13,270 --> 00:05:16,560
Left-Hand side is always the lastly.

76
00:05:16,630 --> 00:05:18,340
What are the number of combinations?

77
00:05:19,350 --> 00:05:25,170
One, two, three, four, four competitions we have done and maximum for Fraps.

78
00:05:27,410 --> 00:05:34,700
No, let us do some analysis, so first of all, how many buses are required, number of buses, number

79
00:05:34,700 --> 00:05:37,130
of buses, one to.

80
00:05:38,220 --> 00:05:39,750
Three, four.

81
00:05:41,030 --> 00:05:48,290
Four passes for one, two, three, four, five elements, five element four passes, 10 elements,

82
00:05:48,290 --> 00:05:54,740
nine percent so far, and elements how many passes and the minus one passes and the minus one passes

83
00:05:54,740 --> 00:05:57,170
required and how many competitions?

84
00:05:57,170 --> 00:05:58,520
Number of competitions.

85
00:05:58,680 --> 00:06:00,550
So final number of competitions.

86
00:06:00,830 --> 00:06:01,820
Let us check here.

87
00:06:02,090 --> 00:06:07,070
In the first past, one competition was done and second pass to comparisons were done.

88
00:06:07,520 --> 00:06:09,820
Third Pass three and the fourth pass four.

89
00:06:10,580 --> 00:06:19,910
So I like them one comparison two plus three plus four as there are five elements for the process.

90
00:06:19,910 --> 00:06:27,460
So up to four and elements and minus one passes up to and the minus one.

91
00:06:27,830 --> 00:06:31,100
So this is and then two and minus one by two.

92
00:06:33,150 --> 00:06:41,610
So this polynomial is of degree, square, outdraw and square as the number of comparisons are taken

93
00:06:41,610 --> 00:06:48,180
as the time, complexity of algorithms for time, complexity of information sorters and square maximum

94
00:06:48,180 --> 00:06:50,460
time, they can make incisions in square.

95
00:06:51,360 --> 00:06:58,140
And what about the slabs here, one slab maximum, two slaps, right, then three, four, five and

96
00:06:58,140 --> 00:06:58,740
four, five.

97
00:06:58,780 --> 00:07:06,330
So four slabs, also same as number of competitions for a number of swaps for any elements, that will

98
00:07:06,330 --> 00:07:10,220
be one plus two plus three plus goes on to the minus one.

99
00:07:10,530 --> 00:07:13,360
And this is an enduring minus one by two.

100
00:07:13,920 --> 00:07:16,490
So this is and square swaps.

101
00:07:18,750 --> 00:07:21,580
So minimum slabs are rough and square.

102
00:07:22,410 --> 00:07:28,310
So this is about comparisons and swaps, we have to check whether it is adaptive and stable or not.

103
00:07:28,320 --> 00:07:32,850
But before that, I will tell you a few important things about inflation hog.

104
00:07:34,050 --> 00:07:40,260
First thing, if I perform just one pass, what is the result, see, after the one after the first

105
00:07:40,260 --> 00:07:42,270
pass, I go to five and eight.

106
00:07:43,220 --> 00:07:44,520
Is it useful anyway?

107
00:07:45,110 --> 00:07:48,220
Can I say that the first element will be the smallest element?

108
00:07:48,260 --> 00:07:51,440
No, we cannot say that that is not the smallest element.

109
00:07:51,900 --> 00:07:56,680
If suppose I have one more element of nine here, can I say eight is the largest element?

110
00:07:56,730 --> 00:07:57,120
No.

111
00:07:57,620 --> 00:08:04,930
So intermediate Rozelle's that is just one or two parts will not give you any useful result in insertions

112
00:08:04,940 --> 00:08:05,180
or.

113
00:08:06,220 --> 00:08:11,590
Like in bubble thought, if you perform one pass, you get the largest element to pass us to larger.

114
00:08:12,370 --> 00:08:16,410
But inflation, you will not get anything useful then.

115
00:08:16,430 --> 00:08:18,160
Second important thing about inflation.

116
00:08:19,360 --> 00:08:24,280
We have seen inflation in an area as well as inflation in a linguist.

117
00:08:24,700 --> 00:08:30,030
So in an array, we have to shift the elements, but we don't have to shift anything.

118
00:08:30,520 --> 00:08:37,240
So the benefit of inflation thought upon Linklaters that you don't have to shift the elements without

119
00:08:37,240 --> 00:08:37,690
shifting.

120
00:08:37,690 --> 00:08:40,030
You can insert so inflation.

121
00:08:40,030 --> 00:08:48,310
So that's more useful or more compatible with the linked list than what I should say that inflation

122
00:08:48,320 --> 00:08:50,080
sorters are designed for.

123
00:08:50,200 --> 00:08:50,860
Linguist.

124
00:08:51,640 --> 00:08:53,830
Yes, it is designed for linguists.

125
00:08:54,760 --> 00:08:58,210
So remember this point for studying linguists intuition.

126
00:08:58,220 --> 00:09:00,090
So it is better knowledge.

127
00:09:00,130 --> 00:09:04,000
I will write on the algorithm for insertions than later.

128
00:09:04,000 --> 00:09:05,780
We will analyze the rest of the team.

