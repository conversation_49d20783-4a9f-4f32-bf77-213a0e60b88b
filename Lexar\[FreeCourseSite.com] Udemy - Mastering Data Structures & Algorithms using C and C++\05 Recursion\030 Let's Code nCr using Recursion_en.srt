1
00:00:00,430 --> 00:00:06,939
And this video, we will see a demo for combination that is NCAR formula, first of all, I will show

2
00:00:06,939 --> 00:00:11,820
a simple function for finding NCAR value, which uses factorial.

3
00:00:11,860 --> 00:00:15,490
So first of all, I will write on a function for finding factorial.

4
00:00:17,050 --> 00:00:18,540
Which we have already seen that.

5
00:00:19,780 --> 00:00:26,940
So fact function, if it's is zero, then right on one otherwise written.

6
00:00:30,880 --> 00:00:37,210
Factorial of and minus one in two and the DE will function.

7
00:00:39,080 --> 00:00:43,160
I will write a function for NPR and see ARE.

8
00:00:44,290 --> 00:00:47,980
Which takes two parameters that is known and are.

9
00:00:51,510 --> 00:00:55,080
So inside this function, we have to find out numerator and denominator.

10
00:00:57,380 --> 00:01:03,410
First of all, I will write a report on zero here, so literally avoid this error.

11
00:01:04,099 --> 00:01:09,950
Litoral NAVILLE Modified inside the function we need numerator and denominator.

12
00:01:10,490 --> 00:01:13,660
So numerator common denominator.

13
00:01:13,670 --> 00:01:20,720
I would take two variables then the numerator is assign that factorial off and.

14
00:01:23,300 --> 00:01:24,620
Then denominator's.

15
00:01:25,960 --> 00:01:29,620
A sign that factory of R.

16
00:01:30,800 --> 00:01:35,690
Multiplied by factorial of and minus.

17
00:01:38,090 --> 00:01:44,660
Then we have the numerator as well as the denominator, the numerator divided by denominator, all these

18
00:01:44,660 --> 00:01:47,230
are integer value, so division is also integer.

19
00:01:47,540 --> 00:01:50,230
The result of inside is integer type only.

20
00:01:50,900 --> 00:01:53,480
So we don't have to worry about any floating points.

21
00:01:55,110 --> 00:02:00,780
So let us look at this function once again, that is Numerati, this factory, Laffan and Denominator's

22
00:02:00,780 --> 00:02:03,900
factory of our and factory, Laffan minus are.

23
00:02:06,370 --> 00:02:11,790
No, inside main function, I will print the result of that function directly F.

24
00:02:13,280 --> 00:02:15,260
Person daily, a new line.

25
00:02:21,930 --> 00:02:25,290
Called the function NCAR by passing.

26
00:02:26,310 --> 00:02:30,630
Five, comma one five, commonness five.

27
00:02:31,970 --> 00:02:33,870
Let us run and see the desert.

28
00:02:36,130 --> 00:02:37,450
Yes, it is five.

29
00:02:40,690 --> 00:02:42,250
Five to 10.

30
00:02:45,040 --> 00:02:49,030
Yes, we got five, commentary's also 10, I have modified that.

31
00:02:50,340 --> 00:02:52,830
So it's also 10:00, so it's working perfectly.

32
00:02:55,590 --> 00:02:57,750
For almost two years, six.

33
00:03:00,050 --> 00:03:06,560
Yes, four to six, so this was a simple function, just using factorial and we have few statements

34
00:03:06,560 --> 00:03:07,550
here to get the result.

35
00:03:08,330 --> 00:03:13,280
Factorial is recursive, but defensed is not recursive nonetheless, right.

36
00:03:13,520 --> 00:03:16,430
Recursive function for finding value of NCI.

37
00:03:20,440 --> 00:03:22,210
So here I read the function.

38
00:03:24,070 --> 00:03:28,090
Let us call it guys, all captains, NCAR.

39
00:03:29,370 --> 00:03:30,420
It's a recursive.

40
00:03:31,990 --> 00:03:35,370
I should write on something written done here to avoid this error.

41
00:03:36,440 --> 00:03:41,120
Next, if anything close to R r.

42
00:03:43,180 --> 00:03:48,100
Are as equals to zero, then return one.

43
00:03:49,210 --> 00:03:50,680
Otherwise, Rickon.

44
00:03:52,350 --> 00:03:55,710
NCAR of and minus one Gummow.

45
00:03:57,420 --> 00:04:04,140
Out of minus one, plus NC, out of and minus one comma on.

46
00:04:06,140 --> 00:04:09,390
That's all here inside the main function.

47
00:04:09,410 --> 00:04:16,790
I will make a function call for this recursive one capital, Scott, let us find the value of Clamato.

48
00:04:19,260 --> 00:04:21,680
For commercial six, yes, it's perfect.

49
00:04:22,870 --> 00:04:26,980
Here I will modify the values and stuff for I will say five or more to.

50
00:04:28,170 --> 00:04:29,070
It should be 10.

51
00:04:30,150 --> 00:04:34,080
Yes, it is 10 five commentary, it should be 10.

52
00:04:35,380 --> 00:04:35,870
Yes.

53
00:04:38,050 --> 00:04:45,610
So that's all in this video, we have seen a simple function using factorial and also a complete recursive

54
00:04:45,610 --> 00:04:49,000
function for finding NCAR values.

