1
00:00:00,630 --> 00:00:04,700
Let us look at the solutions for the questions and quiz, too.

2
00:00:05,430 --> 00:00:07,180
So there are four questions in question.

3
00:00:07,260 --> 00:00:08,710
This is related to us.

4
00:00:09,180 --> 00:00:12,920
So based on the topic, I have given the question, so let us see the solutions.

5
00:00:13,320 --> 00:00:14,230
So the first question.

6
00:00:14,260 --> 00:00:21,090
So first question is that if there is an array of integers and the dimensions, two dimensions or the

7
00:00:21,090 --> 00:00:29,100
dimensions are one to 10 and one to 15 and the basis of an array is 100 and the size of integer is one

8
00:00:29,100 --> 00:00:32,740
byte worksite, one bite it is given in the question.

9
00:00:32,759 --> 00:00:34,230
So I have written it like this.

10
00:00:35,400 --> 00:00:41,910
Then we have to find out the address of any location and what will be the formula, because the formula

11
00:00:41,910 --> 00:00:45,860
is known formula and in that we already know a lot.

12
00:00:45,880 --> 00:00:47,540
That is location zero N.W..

13
00:00:47,580 --> 00:00:50,300
So we can use that and still it will be a formula.

14
00:00:50,310 --> 00:00:52,810
We will not get a single value because we don't know.

15
00:00:52,840 --> 00:01:06,120
And so what is the Formula C formula is a lot plus i.e. minus one in two and plus J minus one.

16
00:01:06,450 --> 00:01:08,790
The Y minus one engine minus one.

17
00:01:08,790 --> 00:01:15,540
Because the indices are starting from one onwards, like C++ or Java, the initial starts from zero

18
00:01:15,540 --> 00:01:17,920
onwards, but here it is starting from one.

19
00:01:17,940 --> 00:01:22,500
So in all languages it was allowed to start index from one also.

20
00:01:22,500 --> 00:01:28,380
So that when it is one, so we have to write a minus one and also G minus one if it is zero, just so

21
00:01:28,380 --> 00:01:30,680
we can take I know what is.

22
00:01:30,690 --> 00:01:33,500
And so the dimensions of engross.

23
00:01:33,510 --> 00:01:35,700
And so this is and like this is.

24
00:01:35,730 --> 00:01:40,980
And so now let us put the values and what we know 100 right.

25
00:01:41,580 --> 00:01:43,170
Then I minus one.

26
00:01:43,740 --> 00:01:44,420
Minus one.

27
00:01:44,880 --> 00:01:47,190
And this is and that is 15.

28
00:01:47,850 --> 00:01:48,750
This is one of 15.

29
00:01:48,760 --> 00:01:53,520
So it is 15 to 15 then plus minus one.

30
00:01:53,820 --> 00:01:55,890
So two minus one will be as it is.

31
00:01:55,900 --> 00:01:56,900
So I'll keep it open.

32
00:01:57,060 --> 00:01:58,170
Know how much this is.

33
00:01:58,620 --> 00:02:07,800
This is hundred plus fifty nine minus 15 plus G minus one.

34
00:02:08,250 --> 00:02:14,880
So this is 15 I plus the G and this one is 100, minus 15 and minus one.

35
00:02:15,150 --> 00:02:16,050
So minus 16.

36
00:02:16,050 --> 00:02:18,070
If it is done then it is eighty four.

37
00:02:19,170 --> 00:02:20,640
So this is the correct answer.

38
00:02:21,030 --> 00:02:24,270
Fifty nine plus plus eighty four is the correct answer.

39
00:02:24,870 --> 00:02:26,150
So this is the first question.

40
00:02:26,610 --> 00:02:30,540
Now second question, this one, this is a little difficult for the student.

41
00:02:30,990 --> 00:02:33,000
So I'll explain in detail was this one.

42
00:02:33,450 --> 00:02:38,760
There is a two dimensional array of four dimensions, four and three that is for rows and three columns.

43
00:02:38,770 --> 00:02:41,730
So here I have drawn it here for zero.

44
00:02:41,730 --> 00:02:47,450
One, two, three, four rows and columns are zero one two are is already initialized with values.

45
00:02:47,460 --> 00:02:50,130
One, two, three, four, five, six, seven, eight, nine, 10, 11, 12.

46
00:02:50,400 --> 00:02:51,710
So one, two, three, four, five, six.

47
00:02:51,720 --> 00:02:54,360
All these numbers are there and the base address is given.

48
00:02:54,480 --> 00:02:59,490
That is 2000 and this is integer type and the size of India is a four.

49
00:02:59,790 --> 00:03:05,990
So this is given in the question so that you have drawn in the diagram here and have filled the values.

50
00:03:06,900 --> 00:03:11,130
Now let us go towards the solution based addresses.

51
00:03:11,130 --> 00:03:13,740
2000, an integer sticking for whites.

52
00:03:14,220 --> 00:03:15,840
So first addresses two thousand.

53
00:03:15,840 --> 00:03:17,120
So I wrote zero for that.

54
00:03:17,130 --> 00:03:20,970
And this is 2004, 2008, 2000.

55
00:03:21,810 --> 00:03:23,430
Twelve plus four.

56
00:03:23,430 --> 00:03:25,880
Right plus forty six plus four is twenty.

57
00:03:26,100 --> 00:03:28,620
So I have written the addresses of all these locations.

58
00:03:28,830 --> 00:03:33,930
So you know very well that in the section we have studied that two dimensional elements actually the

59
00:03:33,930 --> 00:03:36,150
memory will be allocated in a single dimension form.

60
00:03:36,690 --> 00:03:38,460
So that is a start from 2000.

61
00:03:38,460 --> 00:03:41,730
So this is 2004 because Integer is four and eight.

62
00:03:41,850 --> 00:03:43,690
The next is this is 2012.

63
00:03:43,920 --> 00:03:47,900
So likewise this is 2026, forty and forty four.

64
00:03:48,900 --> 00:03:50,040
So these are their addresses.

65
00:03:50,490 --> 00:03:56,650
Not on this important thing we should know for getting the solution is how to access the elements using

66
00:03:56,670 --> 00:03:58,190
pointer arithmetic.

67
00:03:58,710 --> 00:04:01,020
Suppose I want to get the value eight.

68
00:04:01,140 --> 00:04:06,330
How to get the value eight C eight is rule number two and column number one.

69
00:04:06,480 --> 00:04:14,190
So how to get that one X plus rule number two X plus two then here start.

70
00:04:15,290 --> 00:04:23,280
OK, one stop, then column number is one plus one, then this whole thing should be inside stock.

71
00:04:23,980 --> 00:04:31,220
This is how we can access any location or any data inside any location at any given location.

72
00:04:31,230 --> 00:04:36,620
So to come up when I'm accessing if you want to access one comma to, then try this as well.

73
00:04:36,620 --> 00:04:37,300
And this first two.

74
00:04:38,150 --> 00:04:44,820
So important in this one is we need to start for accessing data, accessing data.

75
00:04:45,380 --> 00:04:52,370
If I remove one star, then what I get I get the address of that data address of the data.

76
00:04:52,580 --> 00:04:55,570
I don't get the data, I get the address of.

77
00:04:55,850 --> 00:04:58,960
So what is the address of eight to twenty eight.

78
00:04:59,240 --> 00:05:00,960
So this is 2028.

79
00:05:01,100 --> 00:05:02,330
So starting is 2000.

80
00:05:02,340 --> 00:05:10,550
So I have not returned to tell you just on detailed list 2028 next in this, if I remove this plus one

81
00:05:10,790 --> 00:05:12,230
if I remove this plus one.

82
00:05:12,500 --> 00:05:16,550
So it means it will not go to this rule one then whose address I get.

83
00:05:16,700 --> 00:05:20,010
I get the beginning address of this second row.

84
00:05:20,290 --> 00:05:26,690
Plus Trumans, this is second row X plus two, so first plus as Foro and second plus was for column.

85
00:05:26,960 --> 00:05:27,300
Right.

86
00:05:27,560 --> 00:05:31,190
So if I say explicit, well get the basic beginning address of the second row.

87
00:05:31,400 --> 00:05:33,440
That is two thousand twenty four.

88
00:05:33,590 --> 00:05:34,510
That's what I get.

89
00:05:35,360 --> 00:05:36,200
And one more thing.

90
00:05:36,650 --> 00:05:43,610
If you remove this to start also, then also it means the beginning address of the second row that is

91
00:05:43,610 --> 00:05:44,750
2034.

92
00:05:44,780 --> 00:05:49,680
It means seem very right to start or don't start because you will not be getting the value.

93
00:05:49,790 --> 00:05:51,920
So definitely address for value.

94
00:05:51,920 --> 00:05:53,120
You should have two start.

95
00:05:53,410 --> 00:05:54,160
Remember this.

96
00:05:55,040 --> 00:05:56,740
Now we are able to get the solution.

97
00:05:56,750 --> 00:05:57,950
So what is ask here?

98
00:05:58,280 --> 00:06:04,430
First thing is what is explicitly percentile you is use that for building the address.

99
00:06:04,440 --> 00:06:08,210
So what is three X plus treatments.

100
00:06:08,210 --> 00:06:11,360
Third, beginning a list of third or what is the thirty six.

101
00:06:11,630 --> 00:06:12,950
So 2026.

102
00:06:12,980 --> 00:06:14,570
So this is 2026.

103
00:06:15,740 --> 00:06:23,570
Right next, if I write Star, I'll not get the data, it is explicitly third only same address only

104
00:06:23,570 --> 00:06:23,890
Algate.

105
00:06:24,080 --> 00:06:26,090
So what is the address of the third or beginning address?

106
00:06:26,090 --> 00:06:30,050
That is 2036 2036 only same thing.

107
00:06:31,130 --> 00:06:35,490
Now, next Star explores to some beginning address of the second row.

108
00:06:36,260 --> 00:06:40,960
So this one 2024 plus the three plus treatments.

109
00:06:41,150 --> 00:06:43,450
One, two, three.

110
00:06:44,390 --> 00:06:48,020
If I get him address, this is also 236.

111
00:06:48,320 --> 00:06:54,020
Again, this is also 2036 X plus two star mints.

112
00:06:54,050 --> 00:06:56,150
This address 2034.

113
00:06:56,270 --> 00:06:57,830
Right then plus three.

114
00:06:57,830 --> 00:06:59,090
One, two, three.

115
00:06:59,090 --> 00:07:00,470
So 2036.

116
00:07:01,220 --> 00:07:01,670
That said.

117
00:07:01,880 --> 00:07:07,400
So the answer for this 2036, 2036 and 2036, three times that has given.

118
00:07:08,350 --> 00:07:09,880
So this is the answer for this question.

119
00:07:10,690 --> 00:07:13,240
Now, the next question is third question.

120
00:07:14,340 --> 00:07:22,030
Here the question is, there are two madrassahs, Inbee Madisons can be represented either using the

121
00:07:22,110 --> 00:07:29,940
major mapping or major mapping and by a compiler, by a company that they can be represented in anything

122
00:07:30,990 --> 00:07:32,610
but C language in C++.

123
00:07:32,610 --> 00:07:34,800
Compiler follows rule major mapping.

124
00:07:34,800 --> 00:07:35,190
Right.

125
00:07:35,220 --> 00:07:36,750
We have already discussed this one.

126
00:07:37,080 --> 00:07:43,470
Now, coming to the question, it says that if A and B are being multiplied, we have to perform such

127
00:07:43,470 --> 00:07:44,120
an operation.

128
00:07:44,770 --> 00:07:47,730
Then if you are a compiler, you are writing a compiler.

129
00:07:47,750 --> 00:07:51,580
So in your compiler for such a situation, what do you prefer?

130
00:07:52,080 --> 00:07:58,110
If and B are multiply somatics A multiplied by Beemans if you have two Matisses.

131
00:07:58,260 --> 00:07:58,660
Right.

132
00:07:58,950 --> 00:08:02,910
So if you know the matrix multiplication rules are multiplied with columns.

133
00:08:02,910 --> 00:08:03,330
Right.

134
00:08:03,330 --> 00:08:04,430
Elements in the column.

135
00:08:04,740 --> 00:08:11,280
But all that column we get the product of two Matisses a row with columns we multiply and get each element.

136
00:08:11,290 --> 00:08:13,330
So, you know the matrix multiplication procedure.

137
00:08:13,860 --> 00:08:15,060
So you should know the procedure.

138
00:08:15,690 --> 00:08:20,620
Now the question is, if I'm multiplying these two, then which mapping is better?

139
00:08:21,390 --> 00:08:27,560
So the options are given that first one is Romelio, a second column because rules are multiplied with

140
00:08:27,570 --> 00:08:28,050
column.

141
00:08:28,050 --> 00:08:34,190
So the first idea that you're getting is first one, it should be Rumaila and B should be column Midgette.

142
00:08:34,559 --> 00:08:35,809
That is the wrong answer.

143
00:08:36,750 --> 00:08:42,580
The right answer is any representation can be use reason which representation is better.

144
00:08:43,020 --> 00:08:47,990
So if you see the wrong major formula I have just written, the formula here column is a formula.

145
00:08:48,420 --> 00:08:49,710
How many operations are there?

146
00:08:49,790 --> 00:08:53,130
Two editions, one multiplication, two editions, one multiplication.

147
00:08:53,520 --> 00:08:54,930
Both are equally efficient.

148
00:08:55,110 --> 00:08:59,760
So efficiency is based on the operations for how many operations we have to perform to calculate this

149
00:08:59,760 --> 00:09:00,290
formula.

150
00:09:00,570 --> 00:09:02,210
And for this one, they are simple.

151
00:09:02,880 --> 00:09:04,710
So both are equally efficient.

152
00:09:05,670 --> 00:09:08,690
So the answer is it's independent, both are equally efficient.

153
00:09:08,700 --> 00:09:10,890
You can use one, right?

154
00:09:11,020 --> 00:09:11,900
Not specific.

155
00:09:11,910 --> 00:09:14,940
First one is from one column, but can be ruminative.

156
00:09:14,940 --> 00:09:18,410
Both can be column in any combination for combinations are possible.

157
00:09:18,420 --> 00:09:22,160
Anyone can be declared independent of any representation.

158
00:09:22,440 --> 00:09:27,530
Right now you may be thinking that if the values are smaller than the number of multiplications and

159
00:09:27,570 --> 00:09:31,620
also less than I will say and value may be small also.

160
00:09:31,920 --> 00:09:35,360
So it's not built on value, it's based on just a number of operations.

161
00:09:35,490 --> 00:09:36,870
So both are equally efficient.

162
00:09:37,230 --> 00:09:42,220
So you can use any method independent of representation is the right answer.

163
00:09:42,240 --> 00:09:44,960
Now, the next question is fourth question.

164
00:09:45,480 --> 00:09:49,770
So this is little and I'll remove this and I will do the work for this one.

165
00:09:50,740 --> 00:09:59,080
The question is, there is a three dimensional name of DeSales X and there are three dimensions and

166
00:09:59,380 --> 00:10:00,830
we don't know what are the dimensions.

167
00:10:01,420 --> 00:10:02,740
We don't know the data type.

168
00:10:04,750 --> 00:10:10,360
And if the size of an integer is too wide and the size of load is four bytes, this is given in the

169
00:10:10,360 --> 00:10:10,870
question.

170
00:10:11,680 --> 00:10:19,930
And the question says that for evaluating the address for a three dimensional compiler is performing

171
00:10:19,930 --> 00:10:21,060
following steps.

172
00:10:21,730 --> 00:10:23,080
And these are the steps given.

173
00:10:24,350 --> 00:10:30,110
Then from these steps, can you find out what other dimensions and what is the data type?

174
00:10:30,620 --> 00:10:31,500
This is the question.

175
00:10:32,390 --> 00:10:37,280
So when the company is performing operations, it is using the Romeijn representation.

176
00:10:37,560 --> 00:10:41,490
OK, Romeijn representations followed and these are the intermediate steps.

177
00:10:42,170 --> 00:10:47,610
So now we have to find out what are the values of these dimensions and the data type.

178
00:10:48,440 --> 00:10:50,540
So for the solution, I have taken it here.

179
00:10:51,620 --> 00:10:52,790
Data type, we don't know.

180
00:10:53,090 --> 00:10:56,570
Dimensions are L.M. and three dimensions.

181
00:10:56,570 --> 00:10:57,370
One, two, three.

182
00:10:58,130 --> 00:10:59,370
Then what is it?

183
00:10:59,370 --> 00:11:04,160
The Romijn formula for three dimensionality and resolve itself?

184
00:11:04,790 --> 00:11:13,700
It is a lot plus I women plus Jane plus Leskie and this entire thing multiplied by Dubberly does the

185
00:11:13,700 --> 00:11:17,480
formula and in this one thing observe service.

186
00:11:18,970 --> 00:11:25,690
We have men fight, and I think it is that and W's there, so we don't have.

187
00:11:26,290 --> 00:11:29,150
So yes, we cannot find out, we cannot find it out.

188
00:11:29,440 --> 00:11:35,440
But if we can find out, Ammon and W. these are the values that are variables that are used here.

189
00:11:35,650 --> 00:11:35,970
Right.

190
00:11:36,790 --> 00:11:40,290
Then if I expand this formula, it will be like this.

191
00:11:41,230 --> 00:11:44,150
Knowing if you reached in here, then you know the answer from you.

192
00:11:44,890 --> 00:11:45,370
Let us see.

193
00:11:45,370 --> 00:11:52,330
Does the formula this completing multiplied by revolución, multiplied by little bit the value so have

194
00:11:52,340 --> 00:11:53,770
multiplied every time with W..

195
00:11:54,310 --> 00:12:00,610
Not see this compiler will not execute the entire formula at once.

196
00:12:01,310 --> 00:12:04,860
It has to do step by step like multiplication has done.

197
00:12:04,870 --> 00:12:06,560
The Nix's done, the X is done.

198
00:12:06,560 --> 00:12:07,570
Then addition is done.

199
00:12:07,570 --> 00:12:11,160
So each operation it takes one machine instruction.

200
00:12:11,170 --> 00:12:13,180
So step by step it is doing so.

201
00:12:13,180 --> 00:12:14,380
That's it is shown here.

202
00:12:14,830 --> 00:12:15,640
Steps are there.

203
00:12:16,120 --> 00:12:17,800
The standard formula cannot be done.

204
00:12:17,800 --> 00:12:22,780
If I give you a pen and paper and I'll give you values and ask you to solve it, you cannot do it at

205
00:12:22,780 --> 00:12:23,080
once.

206
00:12:23,110 --> 00:12:30,700
For example, if I say three to four Deepu plus nine into 73 just to do it.

207
00:12:31,390 --> 00:12:33,310
So in a single glance, can you give dancer.

208
00:12:33,370 --> 00:12:36,130
No facil multiply this then multiply this then added.

209
00:12:36,460 --> 00:12:40,810
So the same with compiler is also performing them a step by step.

210
00:12:41,900 --> 00:12:48,880
Now, solution in the formula, what is multiplied with the K, K and W gain to the value?

211
00:12:49,160 --> 00:12:53,900
So here in the steps K into four, does that so can go forward.

212
00:12:53,900 --> 00:12:57,260
Is Theremins what is the value of the value for.

213
00:12:57,530 --> 00:12:59,270
So W is for.

214
00:13:00,790 --> 00:13:03,040
That's it, so it's so simple.

215
00:13:03,220 --> 00:13:05,350
We know the value of the value.

216
00:13:05,460 --> 00:13:10,700
Now, then, if you see this, I started for the smaller one, not this one.

217
00:13:10,720 --> 00:13:13,190
This is having three things, multiple four things multiplied.

218
00:13:13,210 --> 00:13:19,490
So I took the simple term, then G and then W, so sedges multiplied by and then to the value.

219
00:13:19,900 --> 00:13:21,980
So what is the value of the G 32.

220
00:13:22,750 --> 00:13:24,160
So that is 32 minutes.

221
00:13:24,190 --> 00:13:26,020
It is actually an intruder value.

222
00:13:26,320 --> 00:13:30,190
So an end to the value is 32.

223
00:13:30,490 --> 00:13:34,780
Then what is enough and is 32 by Ford.

224
00:13:35,050 --> 00:13:36,070
That is eight.

225
00:13:36,610 --> 00:13:38,740
So nominal and is eight.

226
00:13:40,830 --> 00:13:48,760
Then the last thing I is multiplied by Emmental and include a value, so here it is, one zero before.

227
00:13:49,650 --> 00:13:52,110
So this is a means to an end, to the value.

228
00:13:52,200 --> 00:13:55,800
So I'm into and into the value.

229
00:13:56,190 --> 00:14:02,190
This is one zero four one zero before an incredible you already know that was 32.

230
00:14:02,230 --> 00:14:06,810
So this is as equals to one zero two four by 32.

231
00:14:07,260 --> 00:14:12,220
So if you divide this, this is 32, that is 32 into 32 is one single form.

232
00:14:12,390 --> 00:14:16,120
So we know the value of also that is 32.

233
00:14:17,130 --> 00:14:20,460
So now we know the values W is for formants.

234
00:14:20,470 --> 00:14:22,850
This is data type data type float.

235
00:14:23,190 --> 00:14:24,270
So this is flawed.

236
00:14:24,420 --> 00:14:25,640
Data type is flawed.

237
00:14:26,550 --> 00:14:26,970
Right.

238
00:14:27,270 --> 00:14:31,710
And X, as I said, the first dimension, we cannot know right.

239
00:14:32,100 --> 00:14:38,620
Then what is M M 32 and triple and enis eight let's say.

240
00:14:39,000 --> 00:14:40,000
So whatever it may be.

241
00:14:40,410 --> 00:14:47,470
So if you check the options among the options, only one option is having float here and 32 an eight

242
00:14:47,470 --> 00:14:47,780
here.

243
00:14:48,360 --> 00:14:54,540
So whatever it is toilers return here to or anything, whatever the value may be, it will be torn because

244
00:14:54,540 --> 00:14:55,650
this one and this one.

245
00:14:55,650 --> 00:14:57,420
And this one I've seen.

246
00:14:57,720 --> 00:14:58,920
So there only one option.

247
00:14:58,920 --> 00:14:59,470
That is true.

248
00:15:00,600 --> 00:15:01,440
So that's all.

249
00:15:01,620 --> 00:15:04,860
And these are the solution for questions in question.

