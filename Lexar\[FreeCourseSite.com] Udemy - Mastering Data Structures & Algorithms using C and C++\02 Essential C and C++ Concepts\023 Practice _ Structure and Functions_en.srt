1
00:00:00,400 --> 00:00:07,000
In the previous video I have read on this program, the program is written using functions like they

2
00:00:07,000 --> 00:00:10,520
said, that we should combine length and breadth and define a structure.

3
00:00:11,140 --> 00:00:15,600
Now, in this lecture, we will see structure and functions.

4
00:00:15,850 --> 00:00:18,820
So let us define a structure here.

5
00:00:19,450 --> 00:00:22,120
St<PERSON>ck a rectangle.

6
00:00:24,420 --> 00:00:34,410
And inside this land and inside us, <PERSON>, so how do you find the structure which is combining length

7
00:00:34,410 --> 00:00:37,700
and breadth, so inside main function?

8
00:00:37,710 --> 00:00:44,220
I will not be declaring length and breadth, but I will be declaring a variable of type rectangle.

9
00:00:44,220 --> 00:00:44,670
Right.

10
00:00:44,820 --> 00:00:45,600
So rectangle.

11
00:00:45,600 --> 00:00:49,380
Ah, I will initialize it to zero zero initially.

12
00:00:49,380 --> 00:00:49,710
Right.

13
00:00:49,830 --> 00:00:51,520
So this is initialized to zero zero.

14
00:00:51,780 --> 00:00:57,350
Now here I am taking length and breadth so I cannot directly access lintang, but I should say I bought

15
00:00:57,360 --> 00:00:59,600
land and are not.

16
00:00:59,610 --> 00:01:04,530
But I liked then what about this ETN perimeter.

17
00:01:04,950 --> 00:01:07,770
These are meant for this rectangle.

18
00:01:07,770 --> 00:01:11,100
Right, so they should not take length and breadth separately.

19
00:01:11,400 --> 00:01:14,490
They should take a rectangle as a parameter.

20
00:01:14,520 --> 00:01:19,520
So I will write struck this as a rectangle as parameter.

21
00:01:19,860 --> 00:01:26,930
So are then inside this they should have our dot land and I got it.

22
00:01:27,000 --> 00:01:28,890
So I'm just modifying the code.

23
00:01:28,950 --> 00:01:30,120
See the same program.

24
00:01:30,570 --> 00:01:32,600
But the way it is written is different.

25
00:01:32,940 --> 00:01:37,140
So struck a rectangle and are.

26
00:01:38,280 --> 00:01:43,740
And here, I should say, our daughter a lot, but so frustrating, it changed to our daughter better

27
00:01:44,220 --> 00:01:48,480
than our daughter and then to this area, I should not pass Lindenwood.

28
00:01:48,480 --> 00:01:50,260
I should pass a complete rectangle.

29
00:01:50,520 --> 00:01:51,870
This is complete and rectangle.

30
00:01:51,990 --> 00:01:56,430
See, the benefit here is that we were passing two things to area and perimeter.

31
00:01:56,760 --> 00:02:01,950
But now those two things are combined and put together inside a box called rectangle.

32
00:02:02,400 --> 00:02:09,720
Now from here we are passing a box to this area, the box to this perimeter and the boxes containing.

33
00:02:10,110 --> 00:02:15,550
But so ADA can access Lindenwood and Perimeter can also access Lindenwood.

34
00:02:16,080 --> 00:02:22,560
And the parameter passing method that we are using is called the value and the project of C++ type so

35
00:02:22,560 --> 00:02:24,110
strict is not mandatory.

36
00:02:24,570 --> 00:02:28,200
So if you give or don't give doesn't make any difference.

37
00:02:28,500 --> 00:02:35,850
So in one function I have given struct other function I have removed at a date so I can export cnci

38
00:02:35,970 --> 00:02:37,640
C++ style of programming.

39
00:02:37,650 --> 00:02:39,600
There's a minor difference here right now.

40
00:02:39,600 --> 00:02:40,680
Let us on the program.

41
00:02:41,550 --> 00:02:44,610
Lundestad and with the same result.

42
00:02:44,940 --> 00:02:52,230
See the working for programmers not changing, but the way program has been written is different, right.

43
00:02:52,260 --> 00:02:56,270
The preparation is different, but the final rules are the same, right.

44
00:02:56,550 --> 00:02:58,410
The way this period is different.

45
00:02:59,310 --> 00:03:02,160
So this is structure and function said.

46
00:03:02,160 --> 00:03:05,820
This is the style of programming that I have adopted in my class.

47
00:03:06,550 --> 00:03:08,750
And this is the C style of program.

48
00:03:08,760 --> 00:03:09,100
Right?

49
00:03:09,330 --> 00:03:15,540
If you convert into class, then it is a C++ style and so it is easy to convert it to it into a class.

50
00:03:15,760 --> 00:03:19,330
So I will also show you how to convert this into a class in the next lecture.

51
00:03:19,500 --> 00:03:24,860
Before leaving, I will also write one function for initializing and initialize function will take a

52
00:03:24,870 --> 00:03:28,170
structured rectangle by address.

53
00:03:28,200 --> 00:03:31,440
So this is address and it should also take Landen.

54
00:03:31,440 --> 00:03:35,060
This also Cakebread does already have explaining on whiteboard.

55
00:03:35,070 --> 00:03:35,380
Right?

56
00:03:35,400 --> 00:03:44,100
So I am just directly writing the code lenders L and Rs, but assign B then from main function.

57
00:03:44,100 --> 00:03:48,410
Instead of directly accessing Lindenberg, you can call the function initialize.

58
00:03:48,720 --> 00:03:56,660
So then here you should have temporary variables, Allenby and you should read the values in L and B

59
00:03:57,210 --> 00:03:57,740
I like.

60
00:03:58,200 --> 00:03:59,400
I will remove this one.

61
00:03:59,970 --> 00:04:00,810
I'm making changes.

62
00:04:00,810 --> 00:04:02,100
You have to be very careful.

63
00:04:02,130 --> 00:04:04,770
You watch it carefully and try to practice this one.

64
00:04:04,920 --> 00:04:05,260
Right.

65
00:04:05,310 --> 00:04:06,150
I'm making changes.

66
00:04:06,150 --> 00:04:13,740
I'm not writing a fresh code so I will call this function initialize which will take address of R,

67
00:04:14,220 --> 00:04:19,230
then lente as L and but this B not.

68
00:04:19,230 --> 00:04:25,830
Instead of directly accessing lindenberg of a rectangle, it is initialized by calling initialize function.

69
00:04:26,100 --> 00:04:30,120
And this LMB are temporary variable for user interaction.

70
00:04:30,330 --> 00:04:31,500
I will run the program.

71
00:04:32,160 --> 00:04:34,350
It works in the same way again.

72
00:04:34,350 --> 00:04:35,360
And five.

73
00:04:35,370 --> 00:04:36,840
Yes, everything's the same.

74
00:04:36,990 --> 00:04:45,510
So this program as written using structure and functions are like the one which we have seen on whiteboard.

75
00:04:45,660 --> 00:04:47,580
The first program we saw monolithic.

76
00:04:47,970 --> 00:04:49,620
Later we made functions.

77
00:04:49,620 --> 00:04:51,720
Now we made structure and functions.

78
00:04:52,140 --> 00:04:55,200
Now the next move is for object orientation.

79
00:04:55,530 --> 00:04:58,650
So I will convert this program into class.

80
00:04:59,160 --> 00:05:01,500
So the next lecture is on object orientation.

81
00:05:01,590 --> 00:05:08,010
So practiced this and be written here so that you can follow me and convert the program into object

82
00:05:08,010 --> 00:05:08,790
oriented program.

