1
00:00:00,400 --> 00:00:06,030
In the previous video, already we have studied how a program utilizes the main memory by dividing

2
00:00:06,030 --> 00:00:13,200
the memory into sections, like stack and heap. And, we have also understood what is static memory

3
00:00:13,200 --> 00:00:16,470
allocation and what is dynamic memory allocation.

4
00:00:16,470 --> 00:00:22,290
Now let us move to the topic, that is, Introduction to Data Structures. In this video,

5
00:00:22,360 --> 00:00:26,220
I will be giving introduction to various data structures.

6
00:00:26,220 --> 00:00:31,650
I have categorized them here as physical data structures and logical data structures.

7
00:00:31,680 --> 00:00:34,370
So first, I'll explain what are physical data structures,

8
00:00:34,410 --> 00:00:40,230
just the introduction. Then, introduction to various Logical Data Structures.

9
00:00:40,230 --> 00:00:43,770
Now, let us look at Physical Data Structures.

10
00:00:43,920 --> 00:00:49,040
These are the two physical data structures, Array and Linked List.

11
00:00:49,410 --> 00:00:54,120
We can have more physical data structures, by taking the combination of these, that is Array

12
00:00:54,460 --> 00:00:57,060
and Linked List, we can have some variations in them.

13
00:00:57,960 --> 00:00:59,730
Basically these are two.

14
00:00:59,930 --> 00:01:03,270
The first thing, why I'm calling them as physical?

15
00:01:03,420 --> 00:01:10,370
The reason is, these data structure decides or defines how the memory is organized, how the memory is

16
00:01:10,380 --> 00:01:11,130
allocated.

17
00:01:12,090 --> 00:01:13,860
So, let us look at them one by one.

18
00:01:15,000 --> 00:01:16,100
This is an Array.

19
00:01:16,140 --> 00:01:21,420
This is directly supported by programming languages, like it is there in C language, in C++, and even

20
00:01:21,420 --> 00:01:22,290
in Java.

21
00:01:22,290 --> 00:01:23,590
This is directly supported.

22
00:01:24,480 --> 00:01:29,130
This is a collection of contiguous memory locations, all these locations are side by side.

23
00:01:29,610 --> 00:01:35,610
If I have an array for seven integers, then all these places for seven integers are together,

24
00:01:35,840 --> 00:01:38,200
They are at one place.

25
00:01:38,550 --> 00:01:47,640
This array will have fixed size, once it is created of some size, then that size cannot be increased or

26
00:01:47,640 --> 00:01:48,290
decreased.

27
00:01:49,530 --> 00:01:54,940
So, it is a fixed size. So, the size of an array is static.

28
00:01:55,290 --> 00:02:03,780
Where this array can be created? An array can be created either inside stack or it can be created inside

29
00:02:03,820 --> 00:02:07,230
heap.

30
00:02:07,230 --> 00:02:15,150
We can have a pointer, pointing to this array. So, array can be created either inside stack or inside heap,

31
00:02:15,410 --> 00:02:19,030
any where it can be created. When to use this data structure?

32
00:02:19,080 --> 00:02:24,900
When you are sure, what is the maximum number of elements that you are going to store, if you know the

33
00:02:24,900 --> 00:02:32,350
length of the list, then you can go for array. Now, second data structure, Linked List.

34
00:02:32,370 --> 00:02:38,840
This is a complete dynamic data structure, and it is a collection of nodes, where each node contains data

35
00:02:38,970 --> 00:02:42,300
and is linked to the next node.

36
00:02:42,450 --> 00:02:50,100
The length of this list can grow and reduce, dynamically. So, it is having variable length.

37
00:02:50,850 --> 00:02:52,790
So, as per your requirement,

38
00:02:52,800 --> 00:02:57,180
You can go on adding more and more nodes and add more elements, or

39
00:02:57,390 --> 00:02:59,570
You can reduce the size.

40
00:02:59,770 --> 00:03:03,410
This Linked List is always created in heap.

41
00:03:05,530 --> 00:03:12,580
Collection of nodes are created always in heap, like head may be a pointer, that is pointing there,

42
00:03:12,880 --> 00:03:15,430
So the head pointer may be inside the stack.

43
00:03:17,530 --> 00:03:20,830
So Linked List is always created in heap.

44
00:03:20,830 --> 00:03:25,610
We go with this one, if you know the limit of list, or the size of the list.

45
00:03:25,630 --> 00:03:28,220
If it is fixed. And, we go with this,

46
00:03:28,230 --> 00:03:31,070
If the size of the list is not known.

47
00:03:31,120 --> 00:03:38,950
So these two are physical because they define how the memory should be organized for storing the elements

48
00:03:38,980 --> 00:03:40,620
or for storing the data.

49
00:03:40,630 --> 00:03:43,160
So these are more related to memory.

50
00:03:43,240 --> 00:03:49,230
So, I've just introduced these two data structures to you, as this is a separate topic in our subject.

51
00:03:49,720 --> 00:03:55,450
Now, let us move on to the next type of data structures, that is Logical Data Structures.

52
00:03:55,550 --> 00:04:03,220
Now, let us look at logical data structures. See, here is the list of logical data structures, that are Stack,

53
00:04:03,250 --> 00:04:07,370
Queues, Trees, Graphs and Hash Table. And, these are physical data structures,

54
00:04:07,380 --> 00:04:12,200
Already we have seen. Now, let us look at the differences between them.

55
00:04:12,310 --> 00:04:17,829
Physical data structures are actually meant for storing the data, they will hold the data, they will actually

56
00:04:17,829 --> 00:04:19,870
store the data in the memory.

57
00:04:19,870 --> 00:04:25,090
Then, when you have the list of values you may be performing operations like, inserting more values, or

58
00:04:25,090 --> 00:04:30,820
deleting existing values, or searching for the values, and many more operations.

59
00:04:30,910 --> 00:04:38,210
Now the question is, How you want to utilize those values? How you will be performing insertion and deletion?

60
00:04:38,230 --> 00:04:45,470
What is the discipline that you are going to follow? That discipline is defined by these data structures,

61
00:04:45,730 --> 00:04:51,770
that is, stack, queues, trees, graphs and hash table.

62
00:04:52,090 --> 00:05:03,880
These are linear data structures, and these are non-linear, and this may be linear or tabular data structure.

63
00:05:04,590 --> 00:05:08,640
Hash Table, so it is tabular. So, it is a tabular data structure. Stack,

64
00:05:08,650 --> 00:05:11,480
This works on discipline that is, LIFO,

65
00:05:11,500 --> 00:05:12,660
Last In First Out.

66
00:05:15,580 --> 00:05:18,720
Queue works on the discipline that is, FIFO.

67
00:05:18,760 --> 00:05:21,550
This is a non-linear data structure,

68
00:05:21,550 --> 00:05:26,590
This will be organized like a hierarchy, and this is collection of nodes and the links between the

69
00:05:26,590 --> 00:05:27,070
nodes.

70
00:05:27,460 --> 00:05:31,300
So these data structures are actually used in applications.

71
00:05:31,300 --> 00:05:38,040
These are data structures are actually used in algorithms. And for implementing these data structures,

72
00:05:38,200 --> 00:05:45,160
We either use array or Linked List. So, this is the important point that we have to learn in this topic, that

73
00:05:45,160 --> 00:05:52,750
is, these logical data structures are implemented using any of these physical data structures, either array,

74
00:05:52,810 --> 00:05:56,810
or linked list, or combination of array and linked list.

75
00:05:57,580 --> 00:06:02,430
So that's all, I have given the introduction of various types of data structures.

76
00:06:02,440 --> 00:06:03,430
I have categorized them.

77
00:06:03,430 --> 00:06:08,470
This was just the introduction, to give you awareness. So, the conclusion of this topic is, I wanted

78
00:06:08,470 --> 00:06:14,140
to differentiate types of data structures that is, physical data structures, arrays and linked lists, and these

79
00:06:14,140 --> 00:06:20,170
are logical, and these logical data structures are implemented using physical data structures, either

80
00:06:20,170 --> 00:06:27,880
using array and linked lists. So, through out our course, we will learn about each data structures and we will implement

81
00:06:27,880 --> 00:06:31,870
them using array, as well as we'll implement them using linked lists.

82
00:06:31,870 --> 00:06:37,450
So we have to learn these. Here, I have given just names of the data structures, some of the data structures.

83
00:06:37,450 --> 00:06:42,370
If you pick up each topic, there are lot of sub topics in them, like there are different types of queues, there

84
00:06:42,370 --> 00:06:45,880
are different types of trees, and there are different types of graphs.

85
00:06:45,880 --> 00:06:48,640
So, each and everything, we'll learn all those things in detail.

86
00:06:49,520 --> 00:06:56,920
So, in our course, we will be first learning in detail about this arrays and linked lists data structures. We will

87
00:06:56,920 --> 00:07:03,360
implement them, we will write the programs for these, then we'll start learning about these data structures.

88
00:07:03,500 --> 00:07:11,170
Every data structure, we will implement using array as well as linked list. So, in the next video, I'll

89
00:07:11,170 --> 00:07:15,820
explain, what is ADT? And, what are the various types of lists?

