1
00:00:00,660 --> 00:00:08,370
This is a diagonal matrix I have begun to dangle in matrix of order, five by five, that is five rows

2
00:00:08,850 --> 00:00:10,420
and five columns.

3
00:00:11,100 --> 00:00:17,850
And here you can see that most of the numbers are zeroes and only the elements in their diagonal are

4
00:00:17,850 --> 00:00:18,740
nonserious.

5
00:00:19,920 --> 00:00:26,010
So the important thing is, other than diagonal, all elements must be zero then only we say it's a

6
00:00:26,010 --> 00:00:26,430
diagonal.

7
00:00:27,330 --> 00:00:29,370
Suppose I have some element here.

8
00:00:29,380 --> 00:00:33,600
Let us say five, that it's not a diagonal matrix.

9
00:00:33,990 --> 00:00:35,010
This must be zero.

10
00:00:35,580 --> 00:00:38,700
So the important condition is all those elements must be zero.

11
00:00:39,000 --> 00:00:41,010
So let us see how we can define this one.

12
00:00:41,430 --> 00:00:49,830
See if this is a matrix m then I'm off I g.

13
00:00:50,870 --> 00:00:53,970
Esposito if.

14
00:00:55,180 --> 00:00:57,220
I is not equal to Gina.

15
00:00:59,480 --> 00:01:08,270
And, Wolf, I should be zero if I is not equal to then it is TitleMax if they are equal than their

16
00:01:08,300 --> 00:01:12,530
limits, maybe 090 We don't bother about that, we bother about rest of the elements.

17
00:01:12,530 --> 00:01:15,530
All these elements must be seasonal and these elements are suitable.

18
00:01:16,010 --> 00:01:21,460
So these elements are non-zero elements that are of the elements are Zeitels.

19
00:01:21,830 --> 00:01:24,860
So I have given a definition of diagonal mattocks.

20
00:01:25,700 --> 00:01:32,630
Now if I have to represent or diagonal matrix in a program then.

21
00:01:33,690 --> 00:01:40,830
For a matrix, I have to take two dimensional five rows and five columns, but if I take a two dimensional

22
00:01:40,830 --> 00:01:45,040
array of sci fi by five, most of the elements will be Zeitels.

23
00:01:45,870 --> 00:01:46,700
So I'll shoot.

24
00:01:47,750 --> 00:01:54,410
So like this, if I take our two dimensional array for storing this matrix, then most of the elements

25
00:01:54,410 --> 00:01:55,130
are zeros.

26
00:01:56,570 --> 00:02:01,970
If these are individuals and individuals taking two bites, then total, how many bytes of memory?

27
00:02:02,000 --> 00:02:06,110
This is a consuming there are five to five elements out there.

28
00:02:06,120 --> 00:02:07,730
That is twenty five elements out there.

29
00:02:07,970 --> 00:02:11,810
And each element is taking two bites support for an integer.

30
00:02:11,810 --> 00:02:16,080
Then it is taking 50 bytes, 50 bites of memory is consuming.

31
00:02:17,060 --> 00:02:24,270
So if I'm storing this in 50 bytes of memory, then storage of zero elements is unnecessary.

32
00:02:24,650 --> 00:02:29,340
So it is wasting space as well as the processing on Diebold matter.

33
00:02:29,400 --> 00:02:34,430
So it's like if I'm adding to diagonal madras's, then adding more zeros is of no use.

34
00:02:34,790 --> 00:02:39,530
If I'm multiplying to diagonal mitosis, then multiplication with zeroes is of no use.

35
00:02:39,570 --> 00:02:43,200
So I will be wasting time and processing upon zeros.

36
00:02:43,580 --> 00:02:51,560
So the idea here is that we want to store only non-zero elements so how we can store only non-zero elements.

37
00:02:51,560 --> 00:02:53,240
So far, only non-zero elements.

38
00:02:53,240 --> 00:02:57,360
We can take just a single dimensionality and store these elements.

39
00:02:58,130 --> 00:02:59,610
So let us see how we can do that.

40
00:03:00,020 --> 00:03:04,330
Now let us see how we can represent our diagonal matrix and adjust a single dimensionality.

41
00:03:04,400 --> 00:03:11,500
So I would take a single dimension in the display of size of five because only I have five non-zero

42
00:03:11,510 --> 00:03:12,050
elements.

43
00:03:12,410 --> 00:03:14,930
So I have an idea of size five.

44
00:03:15,380 --> 00:03:17,300
This are starting next to zero.

45
00:03:17,300 --> 00:03:20,600
But here, if you observe I have taken then this is from one onwards.

46
00:03:20,930 --> 00:03:23,870
So in the mathematics we started, this is from one on one.

47
00:03:23,900 --> 00:03:25,240
So that's what I'm following.

48
00:03:25,250 --> 00:03:29,240
So if you want, you can change the universe from zero also.

49
00:03:30,230 --> 00:03:35,390
Then this Uhry that is in programming, it is in C programming or in C++ programming.

50
00:03:35,570 --> 00:03:37,050
The array starts from zero.

51
00:03:37,070 --> 00:03:38,830
So I have taken an array from zero.

52
00:03:39,620 --> 00:03:42,520
Now I want to store only non-zero elements.

53
00:03:42,520 --> 00:03:44,150
So let us store those elements.

54
00:03:44,630 --> 00:03:48,800
Three, seven, four, nine and six.

55
00:03:49,940 --> 00:03:56,680
So I have only non-zero elements, no, let us see how we can access these elements from a single dimension,

56
00:03:56,900 --> 00:03:57,090
right.

57
00:03:58,150 --> 00:04:03,270
If they want to access em off one common element, then it is at zero.

58
00:04:03,880 --> 00:04:08,830
If we want to access em off to common to this element to commit to it is that one.

59
00:04:09,910 --> 00:04:11,900
So only the elements are there.

60
00:04:12,130 --> 00:04:19,990
So if I want to access any element from Matics and I Comanche this, I'm calling it as M, but it is

61
00:04:19,990 --> 00:04:23,320
stored in an attic then from where I can get the element.

62
00:04:23,560 --> 00:04:30,540
First of all, if I is equal to G, if I is equal to G, that only we have an element.

63
00:04:31,390 --> 00:04:31,610
Right.

64
00:04:31,870 --> 00:04:40,210
So if I is equal to G then to come to this present that one three commentary's do so element is present

65
00:04:40,210 --> 00:04:44,950
at either minus one or even I can say G minus one.

66
00:04:45,960 --> 00:04:47,160
So this is very simple.

67
00:04:48,030 --> 00:04:52,050
Now we'll see how we can write a C program code for representing this one.

68
00:04:53,140 --> 00:05:00,350
So I relied on just the required code for implementing this diagonal matrix, using C language.

69
00:05:00,730 --> 00:05:06,070
So for that, first of all, I need an E of what size?

70
00:05:06,610 --> 00:05:08,350
For my example, it is five.

71
00:05:09,160 --> 00:05:09,790
If you want.

72
00:05:09,800 --> 00:05:14,740
You can also take a dynamic array of variable size, whichever size you want at runtime.

73
00:05:14,740 --> 00:05:16,050
You can create a double McMurry.

74
00:05:16,450 --> 00:05:23,020
So I'm not writing a complete program, just so I'm showing you the pieces of code know for storing

75
00:05:23,020 --> 00:05:24,270
data in the matrix.

76
00:05:24,280 --> 00:05:26,970
That is the semantics or retrieving the data in The Matrix.

77
00:05:26,980 --> 00:05:28,830
That is actually one theory.

78
00:05:29,140 --> 00:05:30,920
I need two functions.

79
00:05:30,950 --> 00:05:36,120
That is for setting the data and getting the data so certain get functions I need.

80
00:05:36,400 --> 00:05:42,760
So let us right first set function for saving data on storing data in this one.

81
00:05:43,150 --> 00:05:45,330
So first of all, it means ody.

82
00:05:46,760 --> 00:05:53,990
First parameters on which you're seeing my program, I may be having many madrassas which are diagrammatic,

83
00:05:53,990 --> 00:06:01,070
so Vejvoda Dykeman Matrixes I, Compositors Paramatta the next it needs, which element supports this

84
00:06:01,070 --> 00:06:05,410
element along with this, it also needs room number and column number.

85
00:06:05,420 --> 00:06:06,830
That is I Anchee.

86
00:06:07,160 --> 00:06:09,190
So three more parameters are required.

87
00:06:09,590 --> 00:06:17,300
That is I G and X is the element that they want us to.

88
00:06:18,290 --> 00:06:23,960
So this are the parameters of a function, first of all, an area in which you want to store what is

89
00:06:23,960 --> 00:06:26,450
the rule number of what is the column number and what is the element?

90
00:06:27,620 --> 00:06:32,870
Now, if I'm giving any element for storing this one, then actually it should be stored in this area

91
00:06:33,140 --> 00:06:35,300
that is being parsed as parameter to this one.

92
00:06:36,080 --> 00:06:43,250
So before storing first of all, check whether I angotti equal or not so stored only if they are equal.

93
00:06:43,340 --> 00:06:51,980
So if I is equal to G, then only set function will store the element that will store the element and

94
00:06:51,980 --> 00:06:58,550
then it will store the element that I'm minus one that is assigned the X that's on.

95
00:07:00,000 --> 00:07:04,490
If I is not equal to do nothing, no need to do anything, so close this one.

96
00:07:05,700 --> 00:07:09,930
See, this is the formula we have seen that how we can access the elements.

97
00:07:09,940 --> 00:07:11,080
That is a minus one.

98
00:07:12,060 --> 00:07:14,190
So this is set to function.

99
00:07:15,400 --> 00:07:21,420
Now, I need a function for reading the data or retrieving the data from some rule number and column

100
00:07:21,440 --> 00:07:24,310
number, so for that, I will write a function called.

101
00:07:25,510 --> 00:07:26,020
Get.

102
00:07:27,300 --> 00:07:29,300
And this also means an early.

103
00:07:31,450 --> 00:07:39,880
And it needs inbusiness I and that is rule number and column number, and it has to return the element

104
00:07:39,880 --> 00:07:43,540
from whatever the element, if you want to retrieve it, has to return the elements of the return type

105
00:07:43,540 --> 00:07:44,170
percentage of.

106
00:07:45,610 --> 00:07:52,240
Now, inside this, I have to retrieve an element, so whatever the angle values are given from there,

107
00:07:52,240 --> 00:07:53,560
it has to retrieve an element.

108
00:07:53,570 --> 00:07:58,270
But first of all, it should check with Anjar, equal or not, because we have the elements.

109
00:07:58,270 --> 00:08:00,130
Only four is equal to that.

110
00:08:00,130 --> 00:08:00,760
It is Degnan.

111
00:08:00,850 --> 00:08:02,260
So check if.

112
00:08:03,590 --> 00:08:09,980
I is equal to Jay, so I will in the same line, if it is equal, then the rate on.

113
00:08:11,340 --> 00:08:18,630
Eight of eight minus one seeds at the same place where I have stored it, otherwise, if it is not equal,

114
00:08:18,840 --> 00:08:22,180
then definitely the element of zero three one zero.

115
00:08:22,860 --> 00:08:29,790
These are getting set functions we can write on and call them from our main function or from anywhere

116
00:08:29,790 --> 00:08:30,570
in the program.

117
00:08:30,870 --> 00:08:36,100
But we also have to pass the order in which the diagonal mattocks are stored.

118
00:08:36,990 --> 00:08:40,260
So while writing the program adulterating a complete program and you.

