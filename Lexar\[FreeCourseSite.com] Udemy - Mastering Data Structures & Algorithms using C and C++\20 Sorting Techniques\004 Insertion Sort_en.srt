1
00:00:00,330 --> 00:00:02,340
The topic is inflation thought.

2
00:00:03,780 --> 00:00:09,030
This is one of the important sorting technique for understanding in social thought, we must understand

3
00:00:09,030 --> 00:00:10,860
why the name insert is taken.

4
00:00:11,430 --> 00:00:17,580
So insolation means what if we understand back then, the sorting becomes very easy and the same idea

5
00:00:17,580 --> 00:00:19,680
is followed by such an outcome.

6
00:00:19,710 --> 00:00:22,920
So let us understand, what does it mean by insertion here?

7
00:00:23,030 --> 00:00:28,980
Before understanding insertion, I have taken an array list of elements and these elements are already

8
00:00:28,980 --> 00:00:29,510
sorted.

9
00:00:30,590 --> 00:00:37,610
And in this list, I want to insert a tool in a solid position, so it should be installed at a place

10
00:00:37,610 --> 00:00:39,390
where it should come in sorted order.

11
00:00:40,040 --> 00:00:46,120
So here insertion means inserting an element in a Saturday at a solid position.

12
00:00:46,610 --> 00:00:48,100
So it should be inserted at sorted.

13
00:00:49,340 --> 00:00:53,720
Let us see how we can insert this element in a solid position in an array.

14
00:00:54,230 --> 00:00:57,710
Now, listen carefully where this tool should come.

15
00:00:59,240 --> 00:01:05,360
After two, after six, after 10, before 15, it should come here.

16
00:01:06,500 --> 00:01:12,860
Doyle should come here, then insert 2L here for inciting to here what I have to do, I have to shift

17
00:01:12,860 --> 00:01:17,810
all these elements on the right hand side, make a free space here, then insert to.

18
00:01:19,440 --> 00:01:23,580
So if you observe what I have done, I first found out where early should come.

19
00:01:23,910 --> 00:01:26,070
Then I said, I'll be shifting the Olympics.

20
00:01:27,410 --> 00:01:30,830
So the important thing is you don't have to find out when it should come.

21
00:01:31,820 --> 00:01:36,050
Directly, you can stop shifting the elements, you don't have to find its position.

22
00:01:36,430 --> 00:01:41,060
Now, I will show you exact procedure how this insertion should work.

23
00:01:41,720 --> 00:01:42,430
Let us stop.

24
00:01:42,710 --> 00:01:44,270
I want to insert a tool.

25
00:01:44,780 --> 00:01:46,760
The last element is at this index.

26
00:01:46,760 --> 00:01:51,230
I don't have any Facebook I will vote on in this last element of that index.

27
00:01:51,230 --> 00:01:51,670
Six.

28
00:01:52,100 --> 00:01:53,630
So is it greater than twelve?

29
00:01:53,700 --> 00:01:54,170
Yes.

30
00:01:54,170 --> 00:01:56,330
Shifting move to the next element.

31
00:01:56,330 --> 00:01:57,200
25 is good.

32
00:01:57,230 --> 00:02:00,700
Then 12 shifted a mixed element.

33
00:02:00,710 --> 00:02:02,960
20 is greater than 12 shifted.

34
00:02:03,530 --> 00:02:06,440
Next element 15 is greater than 12 shifted.

35
00:02:06,890 --> 00:02:08,770
Then ten is not greater than twelve.

36
00:02:08,780 --> 00:02:09,759
So I am here now.

37
00:02:09,770 --> 00:02:10,340
Right now.

38
00:02:10,650 --> 00:02:12,740
OK, I have moved all the elements now.

39
00:02:12,740 --> 00:02:14,930
Right now here there is not greater than twelve.

40
00:02:15,200 --> 00:02:16,760
So this is place is free.

41
00:02:16,760 --> 00:02:18,820
So this place stored element to it.

42
00:02:19,070 --> 00:02:22,460
So all these elements will be shifted and twelve will be inserted here.

43
00:02:23,620 --> 00:02:29,800
So I don't have to search and find out which position from the last go on shifting larger elements than

44
00:02:29,800 --> 00:02:34,380
when you get in the elements smaller than the element that you are inserting insert element there.

45
00:02:34,960 --> 00:02:38,490
So all the larger elements are shifted on the right hand side, that's all.

46
00:02:39,540 --> 00:02:47,770
This is information then let us also see insertion in our linked list, so I have taken a linear, singly

47
00:02:47,790 --> 00:02:51,900
linked list and this is a list of sorted elements.

48
00:02:52,110 --> 00:02:57,190
And I want to insert 18 in a link with we don't have to shift the elements.

49
00:02:57,210 --> 00:02:58,550
Shifting is not required.

50
00:02:58,830 --> 00:03:02,310
Then we have to find out where this 18 should come.

51
00:03:02,820 --> 00:03:07,370
Like in this link lists, 18 should come after 15 or before 20.

52
00:03:07,600 --> 00:03:11,560
So I should find out its position and inserted that fault so far.

53
00:03:11,560 --> 00:03:15,810
To find an explanation, I will take a pointer to Travelers link lists.

54
00:03:16,050 --> 00:03:18,170
Then also I will take one pill pointer.

55
00:03:18,360 --> 00:03:24,380
I am sure you the procedure how to insert that check a piece of data that is smaller than this one.

56
00:03:24,660 --> 00:03:30,900
So move to next, but before that to bring Q upon P and then move B.

57
00:03:32,820 --> 00:03:36,060
This is smaller than 18, so again, move be.

58
00:03:37,320 --> 00:03:46,590
But first, bring the Q upon P, then move P Nobbys data is a smaller than 18 again move, be in queue

59
00:03:46,770 --> 00:03:56,160
for first Q then move B this is still smaller, is smaller than the eighteen so move Q upon B and move

60
00:03:56,370 --> 00:03:56,960
to next.

61
00:03:57,600 --> 00:04:00,480
Not is A greater than the eighteen.

62
00:04:00,480 --> 00:04:04,770
So we have found the first element which is greater than the key element that we are inserting.

63
00:04:05,130 --> 00:04:06,450
So we found its position.

64
00:04:06,720 --> 00:04:07,890
So it should be after.

65
00:04:07,890 --> 00:04:15,810
Q So far that take one more temporary pointer and create a new node, insert this value eighteen, then

66
00:04:16,079 --> 00:04:21,690
make this a D next point on P and Qs next to point on P NetSol.

67
00:04:21,690 --> 00:04:28,410
This is insert my if I start from here to two, six, six to ten, ten to 15, 15 to 18, 18 to 20.

68
00:04:28,410 --> 00:04:32,640
So 18 is inserted in the position for a link list.

69
00:04:32,670 --> 00:04:38,700
We have to find out its position and then insert, whereas in another we don't have to find the position,

70
00:04:38,940 --> 00:04:40,650
simply shift the elements.

71
00:04:41,070 --> 00:04:47,430
And once you get a smaller element, then insert and once you have finished shifting all larger elements,

72
00:04:47,430 --> 00:04:49,230
then insert freespace.

73
00:04:49,590 --> 00:04:54,930
So what is the time taken for insertion in an array shifting of elements?

74
00:04:54,930 --> 00:04:56,070
How many elements?

75
00:04:56,460 --> 00:05:01,140
Some elements are shifted forever depends on the number of elements.

76
00:05:01,410 --> 00:05:06,120
Maybe a C two elements are shifted, maybe all elements have shifted.

77
00:05:06,120 --> 00:05:07,960
So it may be a minimum constraint.

78
00:05:07,960 --> 00:05:11,460
The maximum and the minimum, constant and maximum.

79
00:05:11,460 --> 00:05:21,780
And then here find out the position using P and Q So traversing takes time, insertion, constant time.

80
00:05:22,050 --> 00:05:26,160
Just we have to create a node and make links for searching for a position.

81
00:05:26,160 --> 00:05:30,960
Takes time how much time it takes to traverse a few elements.

82
00:05:30,960 --> 00:05:31,890
So let us see.

83
00:05:31,920 --> 00:05:37,860
And so it may either traverse all elements or just one element.

84
00:05:37,860 --> 00:05:40,290
So it may be inserted before too also.

85
00:05:40,290 --> 00:05:40,560
Right.

86
00:05:40,890 --> 00:05:44,280
So time may be minimum, constant, maximum.

87
00:05:44,640 --> 00:05:53,940
And so inserting an element in a surplus takes minimum constant time, maximum order of time.

88
00:05:54,060 --> 00:05:55,700
So that's all I have explained to you.

89
00:05:55,710 --> 00:05:57,470
What does it mean by insertion?

90
00:05:58,260 --> 00:06:00,950
No, I will explain you insertion side.

91
00:06:01,140 --> 00:06:03,180
That will be very easy for understanding.

