1
00:00:00,840 --> 00:00:08,460
In this video, I will show you how to download Dev C++, how to set it up and how to use it.

2
00:00:09,390 --> 00:00:13,230
So this is an idea used for writing C++ program.

3
00:00:13,800 --> 00:00:16,900
Most of the students use Dev C++.

4
00:00:16,920 --> 00:00:21,080
So let us learn how to download and set up.

5
00:00:21,480 --> 00:00:32,900
So open Google Chrome and Google Chrome, search for download dev C++ Vitamin GWM already.

6
00:00:33,570 --> 00:00:34,880
I have searched for it already.

7
00:00:35,310 --> 00:00:37,500
So this is the thing that you have to type.

8
00:00:37,900 --> 00:00:45,390
Just not don't make sure you are searching for the same thing, same string download dev, C++, vitamin

9
00:00:45,400 --> 00:00:53,700
GW mingy W is a compiler so when you download Dev C++, it's an ID along with that compiler will also

10
00:00:53,700 --> 00:00:57,080
get downloaded and installed on your machine.

11
00:00:57,690 --> 00:00:59,220
So just hit enter.

12
00:01:01,240 --> 00:01:08,030
Then you'll will find various links, click on the first link, that is from SourceForge Dot Net.

13
00:01:08,590 --> 00:01:10,190
So click on this one.

14
00:01:11,200 --> 00:01:16,230
Now, here we are on a download page on SourceForge website.

15
00:01:16,810 --> 00:01:18,760
Click on this green button.

16
00:01:19,510 --> 00:01:28,450
You can read it, download the latest version of C++ five point one one TDM GCT four point ninety nine

17
00:01:28,540 --> 00:01:32,800
to set up to make sure you have reached the same blink rate.

18
00:01:32,830 --> 00:01:35,500
You should read it and check you are on the same link.

19
00:01:36,100 --> 00:01:37,030
Click on this one.

20
00:01:37,950 --> 00:01:44,760
It will start downloading after downloading install C++.

21
00:01:50,690 --> 00:01:51,250
OK.

22
00:02:09,419 --> 00:02:11,110
It has finished installation.

23
00:02:11,940 --> 00:02:15,730
If I show you the folder there in C++ installed.

24
00:02:16,730 --> 00:02:20,100
This is a C and program files.

25
00:02:21,290 --> 00:02:30,570
And here is the CPB, so you can see that it is also install mingy W that is Jianyu compiler for Windows.

26
00:02:30,590 --> 00:02:35,330
This is a minimal compiler for windows that have started C++.

27
00:02:36,310 --> 00:02:39,960
So English language selected the next then.

28
00:02:40,040 --> 00:02:48,340
OK, is that C++ is ready now before we start using it, we have to make some important settings and

29
00:02:48,340 --> 00:02:50,270
these settings we have to do only once.

30
00:02:50,770 --> 00:02:52,660
So let me show you what settings you have to do.

31
00:02:53,440 --> 00:02:55,390
Go to the menu and the menu.

32
00:02:55,780 --> 00:03:00,910
You'll find pools on the top, then select compiler options.

33
00:03:02,530 --> 00:03:04,150
And here, a few tabs out there.

34
00:03:04,270 --> 00:03:05,980
That is general tab inside.

35
00:03:06,000 --> 00:03:08,460
Insiders mention hyphen G.

36
00:03:09,700 --> 00:03:14,150
This will allow the compiler or the debugger to debate the program.

37
00:03:14,710 --> 00:03:19,630
So there's a flag for the compiler which will allow the debugger to reboot the program.

38
00:03:19,660 --> 00:03:22,890
So what is debugging and all we will see later in the next videos.

39
00:03:23,890 --> 00:03:30,730
You have to mention this hyphen G or minus G, then go to another tab that is programs.

40
00:03:34,500 --> 00:03:43,680
Here you'll D.C. So for that just to is given and this compiler, by default, it will be C++ 98 Walsham,

41
00:03:44,400 --> 00:03:47,280
but will be using the features of C++.

42
00:03:47,930 --> 00:03:49,290
That is the later version.

43
00:03:49,680 --> 00:03:53,440
So to use C++ Leverne, we have to mention here.

44
00:03:53,910 --> 00:03:55,980
So this is what you are supposed to write.

45
00:03:56,550 --> 00:04:00,210
Hyphen SDD, that is minus Usted.

46
00:04:00,280 --> 00:04:10,320
You can read us then equal to C++ Leverne then do the same thing in the next field.

47
00:04:10,320 --> 00:04:15,810
Also write C++ level nested C++ Leverne.

48
00:04:17,790 --> 00:04:20,010
It can be capitaland small C++.

49
00:04:20,529 --> 00:04:27,670
That doesn't make a difference by mentioning this now it will be using C++ Leverne compiler.

50
00:04:28,800 --> 00:04:33,830
Now we are using the word Cowsill will be using the features of C++ Levonne also.

51
00:04:34,200 --> 00:04:37,530
So if this does not mention those features will not work.

52
00:04:38,220 --> 00:04:42,060
So this is the very important thing that you have to do in the settings.

53
00:04:42,720 --> 00:04:46,940
Nonlinearity just say, OK, now the settings are done.

54
00:04:48,000 --> 00:04:52,710
Now we are ready to do any project or write over C++ programs.

55
00:04:53,910 --> 00:04:56,790
So let me show you how to create a new project.

56
00:04:59,400 --> 00:05:00,750
Say a new project.

57
00:05:01,880 --> 00:05:07,250
And select an application, let the project name as my first.

58
00:05:10,100 --> 00:05:12,020
And it should be created in documents.

59
00:05:13,130 --> 00:05:14,420
Now the project is ready.

60
00:05:15,080 --> 00:05:17,870
This is meant to be file inside the project.

61
00:05:17,880 --> 00:05:19,770
So here, I'll just give a message.

62
00:05:20,420 --> 00:05:21,370
Hello, world.

63
00:05:22,380 --> 00:05:28,370
And then it should use namespace for setout as well as.

64
00:05:28,500 --> 00:05:34,550
And then let us build a project and execute.

65
00:05:35,840 --> 00:05:42,140
HelloWallet has appeared, so that's all this is how you can open a new project for every program or

66
00:05:42,140 --> 00:05:45,890
application, you can start a new project or else for practicing in the same project.

67
00:05:45,890 --> 00:05:50,060
You can go on writing new code there and you can recompile and run it.

