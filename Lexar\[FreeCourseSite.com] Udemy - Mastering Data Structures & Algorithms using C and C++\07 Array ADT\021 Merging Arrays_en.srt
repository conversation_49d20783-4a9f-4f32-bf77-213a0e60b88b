1
00:00:00,540 --> 00:00:08,780
Yeah, in this video, we'll talk about merging that is combining two sorted lists into a single sorted

2
00:00:08,790 --> 00:00:09,150
list.

3
00:00:09,990 --> 00:00:11,720
Merging is a binary operation.

4
00:00:11,730 --> 00:00:16,790
It needs more than one array so that it can combine them and go single.

5
00:00:18,600 --> 00:00:20,160
Likewise, merging.

6
00:00:20,160 --> 00:00:25,790
There are more binary operations on an update quickly, I will tell about them.

7
00:00:27,090 --> 00:00:32,790
One is a contact can be called B.

8
00:00:33,540 --> 00:00:36,510
These are some of the binary operations on on that.

9
00:00:36,510 --> 00:00:41,910
If you need here, you need more than one party like appending in that.

10
00:00:42,120 --> 00:00:45,330
Already we have an idea and there is supposed free space.

11
00:00:45,600 --> 00:00:48,510
We want to add these elements are followed by this one.

12
00:00:49,320 --> 00:00:57,110
That is a contact, that same thing, combining these two, what is a concatenation?

13
00:00:57,390 --> 00:01:03,330
So it may be done in totality, like all the elements of it, then all the elements of it be followed

14
00:01:03,330 --> 00:01:03,860
by that.

15
00:01:04,970 --> 00:01:13,190
Spending means that today's updated contract means that maybe in some other areas, the same thing concatenated

16
00:01:13,200 --> 00:01:18,490
pronounced him, comparing to is comparing whether they have a same set of elements or not.

17
00:01:18,780 --> 00:01:23,710
So one by one, we have to compare the elements from Gourdes then copying.

18
00:01:24,090 --> 00:01:29,280
So if you have an array of elements and there is a free year here, we want to copy all these elements

19
00:01:29,280 --> 00:01:29,830
in this one.

20
00:01:30,510 --> 00:01:33,660
So this is copying the likewise.

21
00:01:33,660 --> 00:01:36,030
We have much now.

22
00:01:36,030 --> 00:01:38,190
Let us see what is merging.

23
00:01:38,520 --> 00:01:41,380
Merging can be done only on sorted list.

24
00:01:42,000 --> 00:01:46,860
So first is the sorted and the second arrays also socket and.

25
00:01:49,220 --> 00:01:53,720
We have to combine these two arrays and get the single sorted.

26
00:01:55,400 --> 00:01:58,590
And for emerging, we need a definitely third.

27
00:01:59,930 --> 00:02:07,550
We cannot combine and store only in A or B, we need one moderately to say see now how to combine them

28
00:02:07,880 --> 00:02:09,289
before looking at the procedure.

29
00:02:09,289 --> 00:02:09,949
Let us see.

30
00:02:10,190 --> 00:02:16,690
The number of elements in this area are M elements and this are an element.

31
00:02:16,700 --> 00:02:19,950
So that is having M elements and this is having an element.

32
00:02:20,690 --> 00:02:27,890
Now we will combine these two and store them here such that they are sorted so far that we will take

33
00:02:28,070 --> 00:02:39,110
index point igy and I start from first index of A and start from first index of B and case from here.

34
00:02:41,400 --> 00:02:48,860
Now, what the procedure we perform as a repeating procedure for what we have to do compared AOF I would

35
00:02:49,080 --> 00:02:53,660
be of G, which is a smaller copy that from here.

36
00:02:54,210 --> 00:02:59,160
So if Alfi is a smaller Kopit, if a biology is a smaller copy, that's fine.

37
00:02:59,640 --> 00:03:07,170
So right now, if either smaller sockpuppet is Suderman, then move it to the next element and move

38
00:03:07,680 --> 00:03:08,310
to the next.

39
00:03:10,080 --> 00:03:18,020
Then compare, if I were to be offered, Jane, be of the smaller, so copy the sentiment for an increment

40
00:03:18,810 --> 00:03:19,200
and.

41
00:03:20,370 --> 00:03:20,890
Increment.

42
00:03:22,050 --> 00:03:28,450
So what we are doing here, I will write on pseudocode for that one initially I is equal to zero and

43
00:03:28,450 --> 00:03:32,580
GS also zero and Keisel so zero that was initial one.

44
00:03:34,730 --> 00:03:49,390
Then we are comparing if each eye is smaller than B of G, if so, then in C of K we will copy AOF Iron

45
00:03:50,510 --> 00:03:56,900
and we increment K as well as I otherwise n C of K plus.

46
00:03:56,900 --> 00:04:02,030
Plus we will copy B of G plus plus.

47
00:04:03,910 --> 00:04:05,150
This is what we are doing.

48
00:04:07,030 --> 00:04:15,190
So let us continue IAFIS eight biology's tenso eight is copied here I is moved further and Kayes moved

49
00:04:15,190 --> 00:04:15,670
further.

50
00:04:16,510 --> 00:04:25,060
IAFIS 16, the object for 10 is copied, then Jesmond for further IAFIS 16, biology's 12 for 12 is

51
00:04:25,060 --> 00:04:29,920
copied and JS move further and also move further.

52
00:04:30,790 --> 00:04:31,960
IAFIS 16.

53
00:04:31,960 --> 00:04:33,210
Biology's 22.

54
00:04:33,220 --> 00:04:33,910
This is 16.

55
00:04:33,920 --> 00:04:34,600
This is 22.

56
00:04:34,600 --> 00:04:35,650
So copy 16.

57
00:04:36,700 --> 00:04:37,630
Move next.

58
00:04:37,990 --> 00:04:38,860
Move next.

59
00:04:40,320 --> 00:04:44,550
Yafai 20, and this is 22 for 22 for Prendes Company.

60
00:04:45,870 --> 00:04:48,060
I moved, case moved.

61
00:04:49,450 --> 00:04:57,730
Now, this is 25, this is 20 to 22 is escapade, this is 25 and this is 23.

62
00:04:57,730 --> 00:04:59,200
So 23 is Kopit.

63
00:05:00,250 --> 00:05:01,600
Then C++.

64
00:05:02,660 --> 00:05:03,500
Case here.

65
00:05:04,820 --> 00:05:11,160
So Jay became five, no means it has gone outside the size of another.

66
00:05:11,540 --> 00:05:13,140
There are total five elements.

67
00:05:13,460 --> 00:05:14,590
So Jay became five.

68
00:05:14,600 --> 00:05:17,830
No, it means the second list B has finished.

69
00:05:18,710 --> 00:05:19,480
We should stop.

70
00:05:19,490 --> 00:05:20,550
We cannot compare.

71
00:05:20,570 --> 00:05:23,480
Now, if I would be of G, then what to do?

72
00:05:23,750 --> 00:05:30,500
Definitely there are some elements left over in a copy them from wherever I is from that same place

73
00:05:30,500 --> 00:05:33,260
to start copying the remaining elements because I have more elements.

74
00:05:33,260 --> 00:05:37,870
Copy all of them and definitely at least one element will be left over.

75
00:05:38,300 --> 00:05:42,290
So in our example here, we got one element left.

76
00:05:42,770 --> 00:05:48,380
If suppose this is not 23, this is twenty eight, then this is left over, that twenty five will be

77
00:05:48,380 --> 00:05:48,800
copied.

78
00:05:49,580 --> 00:05:52,990
So any one of the list, at least one element will be left over.

79
00:05:53,000 --> 00:05:54,620
So we have to copy that element.

80
00:05:55,040 --> 00:05:55,730
So copy that.

81
00:05:55,730 --> 00:05:56,630
Twenty five here.

82
00:05:58,470 --> 00:06:00,280
That's all this is the merging process.

83
00:06:01,200 --> 00:06:06,450
Now let me complete the code, see, I was comparing and copying, comparing and copying every time

84
00:06:06,750 --> 00:06:08,790
repeatedly I was doing so.

85
00:06:08,790 --> 00:06:09,870
This was happening.

86
00:06:10,350 --> 00:06:16,830
Why I is less than M and G is less than an.

87
00:06:19,140 --> 00:06:26,220
Compare and copy, compare and copy, but we have stopped then when we became equal to him, so it is

88
00:06:26,220 --> 00:06:27,800
less than then we were continuing.

89
00:06:27,860 --> 00:06:29,310
When it is equal to when we stop.

90
00:06:29,730 --> 00:06:36,690
We have to stop either if the fossilised ends or second list ends means I become equal to him or becomes

91
00:06:36,690 --> 00:06:38,120
equal to when we have to stop.

92
00:06:38,370 --> 00:06:39,420
OK, we have stopped.

93
00:06:40,080 --> 00:06:46,290
Then what we have to do, copy the remaining elements in the fossilised capillary, meaning if in second

94
00:06:46,290 --> 00:06:54,180
list, then also copied elements so far that I would write on the code here for whenever I let it be

95
00:06:54,180 --> 00:06:54,420
there.

96
00:06:54,420 --> 00:07:06,420
Only I less than m I placeless c of plus plus a sign of i.e. this will be the remaining element from

97
00:07:06,420 --> 00:07:07,170
first list.

98
00:07:07,770 --> 00:07:14,670
If suppose there are some remaining elements in the second list then copy gaolers then n g plus plus.

99
00:07:15,700 --> 00:07:21,190
And CEO of Cabelas, plus Kopi B of Jay.

100
00:07:23,050 --> 00:07:25,840
So this is the code for merging.

101
00:07:26,990 --> 00:07:33,710
So at last, I will copy that 25 also here and move I and Keyon, so stop.

102
00:07:34,370 --> 00:07:39,550
We have combined to sartorialist and do a single sartorialist and the single sorted list.

103
00:07:39,550 --> 00:07:44,690
Lister's having total emblems and elements, no analysis.

104
00:07:45,970 --> 00:07:47,250
What is the work done?

105
00:07:48,650 --> 00:07:55,370
Comparing the elements and copying the elements here, we prefer copying of elements for how many elements

106
00:07:55,370 --> 00:07:59,630
are copied and elements from here and elements from here are copied.

107
00:07:59,930 --> 00:08:08,980
So total time taken s m plus and so it is written, has teed off and left him C so far.

108
00:08:08,990 --> 00:08:14,360
I was writing outdraw, but this is a known one we use of ttr.

109
00:08:14,570 --> 00:08:21,530
We can use a big word or mingering thing also, but usually time is used for Vicryl explaining what

110
00:08:21,530 --> 00:08:22,790
is teater afterwards.

111
00:08:24,230 --> 00:08:31,580
So the time taken here is take off and plus and total implosion elements are copied, and this is the

112
00:08:31,580 --> 00:08:36,360
first time we are giving the time, complexity in two variables and and variables.

113
00:08:36,740 --> 00:08:40,130
So this is a notation for Margine, wherever you see.

114
00:08:40,130 --> 00:08:47,170
And plus and then definitely there is merging news there, symbols and information.

115
00:08:47,510 --> 00:08:49,370
So that's all about March.

