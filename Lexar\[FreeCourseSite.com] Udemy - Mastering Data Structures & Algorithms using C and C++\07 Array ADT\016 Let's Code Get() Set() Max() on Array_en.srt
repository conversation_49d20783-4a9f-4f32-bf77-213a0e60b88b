1
00:00:00,240 --> 00:00:07,050
In the city of right, all those small functions like get set are finding maximum and minimum, finding

2
00:00:07,050 --> 00:00:09,840
some of all the elements and also calculate an average.

3
00:00:10,810 --> 00:00:16,870
So let us start with the first function, that is get it will take the index and returns the element,

4
00:00:17,470 --> 00:00:22,900
so the function return type should be integer type because our error is integer, then it should be

5
00:00:23,890 --> 00:00:24,550
index.

6
00:00:25,000 --> 00:00:27,950
But along with the index, it should also take an array.

7
00:00:27,970 --> 00:00:34,450
So the first parameter is struck early and it should be called by value because it's not going to modify

8
00:00:34,750 --> 00:00:35,120
UTI.

9
00:00:37,310 --> 00:00:43,340
Now, there's a simple function, if the index is valid, then we should return the element at that

10
00:00:43,340 --> 00:00:44,300
particular index.

11
00:00:44,480 --> 00:00:48,080
So first check whether the index given is valid or not.

12
00:00:48,260 --> 00:00:57,440
So if index is greater than or equal to zero and if index is less than.

13
00:00:58,680 --> 00:01:01,320
Adis Lente done it is a valid index.

14
00:01:03,340 --> 00:01:05,379
If yes, then simply written.

15
00:01:06,450 --> 00:01:07,440
Array of.

16
00:01:09,060 --> 00:01:16,830
AOF index, whatever the index is given, otherwise written, it's an invalid index, so we can return

17
00:01:16,830 --> 00:01:18,210
a value that is minus one.

18
00:01:19,070 --> 00:01:22,170
So there's a simple forget function now.

19
00:01:22,220 --> 00:01:27,140
I will also write on set function and we will try all of them inside main function.

20
00:01:28,960 --> 00:01:34,400
Said function will change the value at a given index, so it needs Uhry itself.

21
00:01:34,420 --> 00:01:37,360
That is by address.

22
00:01:38,200 --> 00:01:39,250
By address.

23
00:01:40,380 --> 00:01:41,760
And the index.

24
00:01:43,440 --> 00:01:51,000
And the new value that we want to change, so it will set the value at a given index, so before setting

25
00:01:51,000 --> 00:01:53,780
first of all, check that the index given is valid or not.

26
00:01:54,180 --> 00:01:55,410
So same thing.

27
00:01:55,680 --> 00:02:02,790
Whether index is valid or not, index is greater than equal to zero and index should be less than.

28
00:02:04,320 --> 00:02:13,830
Iraj Lent, Odie's Lent, then it is valid, if it is valid, then in an urry we will change the value

29
00:02:13,830 --> 00:02:18,150
at a given index to new value X.

30
00:02:19,280 --> 00:02:19,850
Nexon.

31
00:02:21,180 --> 00:02:27,630
There's a search function now, let us write a function for finding maximum element, Max, it should

32
00:02:27,630 --> 00:02:33,480
take out a structure and again, it is called by value a sufficient.

33
00:02:34,790 --> 00:02:39,330
Then here I should have one variable for finding maximum, so Max assign.

34
00:02:40,440 --> 00:02:46,290
First of zero value will assign, so first of all, we will take it then also we need a variable ifour

35
00:02:46,290 --> 00:02:47,160
running the following.

36
00:02:47,730 --> 00:02:54,540
So for I assign one on works, I is less than is Lent.

37
00:02:55,800 --> 00:02:57,180
And if.

38
00:03:01,260 --> 00:03:04,170
Elfy value is greater than Max.

39
00:03:04,190 --> 00:03:06,080
Then we will change Max to.

40
00:03:07,810 --> 00:03:08,590
Yafai.

41
00:03:11,910 --> 00:03:14,790
So let us enclose this inside a bracket.

42
00:03:16,820 --> 00:03:20,400
And the end, we will return whatever the max value that we got.

43
00:03:21,590 --> 00:03:23,810
So this is a function for finding maximum.

44
00:03:24,360 --> 00:03:26,770
Now let us write a function for finding minimum.

45
00:03:26,780 --> 00:03:31,760
So I will copy dysfunction and I will simply modify this fund to make a function for finding minimum.

46
00:03:34,620 --> 00:03:36,120
Change the name to mean.

47
00:03:38,060 --> 00:03:41,000
And variable name also will change Tasmin.

48
00:03:43,950 --> 00:03:49,290
Then if your father is less than men, then change men to.

49
00:03:51,260 --> 00:03:53,600
Elfy and Min.

50
00:03:55,900 --> 00:03:58,030
So I have modified the function for finding Min.

51
00:03:59,260 --> 00:04:03,340
So I don't get an sec functions as well as Max and Min.

52
00:04:04,630 --> 00:04:11,860
Now, let's write a function for finding the sum of all the elements sum it should take at structure.

53
00:04:17,740 --> 00:04:19,810
So it's going to be a very simple function.

54
00:04:20,779 --> 00:04:30,830
And I need a variable eye for running for look for ISIS, zero is less than eight hours land and I plus

55
00:04:30,830 --> 00:04:31,730
plus every time.

56
00:04:32,150 --> 00:04:36,080
And in this variable we will add yafai values.

57
00:04:41,580 --> 00:04:47,370
So that's all, whatever the values are, they will be added, then we will return as the result is

58
00:04:47,390 --> 00:04:48,360
a variable.

59
00:04:48,490 --> 00:04:48,830
Yes.

60
00:04:49,410 --> 00:04:51,900
So this is a function for some finding some.

61
00:04:53,150 --> 00:04:55,710
Then at last, we need a function for finding Avery.

62
00:04:55,730 --> 00:04:58,120
So I will take float a Viji.

63
00:05:00,880 --> 00:05:01,720
Which takes.

64
00:05:02,640 --> 00:05:05,190
Uhry Aitarak.

65
00:05:10,440 --> 00:05:16,410
I don't have to add all the elements already, I have a function for finding some, so let us call that

66
00:05:16,410 --> 00:05:22,590
function some by passing UT and divided by Aeronaut Lente.

67
00:05:24,190 --> 00:05:31,090
This will give the average and this value should be of Diepsloot, so I will make it as float a basket

68
00:05:31,090 --> 00:05:31,620
as float.

69
00:05:32,860 --> 00:05:34,750
So average function becomes symbol.

70
00:05:38,780 --> 00:05:45,440
Now, let us try all these functions and see so the functions which directly return, the result I will

71
00:05:45,440 --> 00:05:47,240
use have to first function.

72
00:05:47,240 --> 00:05:58,910
I will call this get percentile and and here I will call a function get bypassing Uhry and indexes do

73
00:05:59,120 --> 00:06:01,950
so at indexed to value for this present.

74
00:06:01,970 --> 00:06:03,140
So it should return for.

75
00:06:06,790 --> 00:06:07,420
Yes.

76
00:06:09,340 --> 00:06:10,420
As left for.

77
00:06:11,970 --> 00:06:14,220
I will send in zero, the value should be to.

78
00:06:16,030 --> 00:06:17,170
Yes, values, too.

79
00:06:18,540 --> 00:06:22,660
I'll give you the index nine index is not there, so it should return minus one.

80
00:06:23,680 --> 00:06:25,660
Yes, it has returned minus one.

81
00:06:28,610 --> 00:06:31,520
So perfect, it's perfect, so I will remove this.

82
00:06:33,540 --> 00:06:34,890
Next, let us try a.

83
00:06:36,980 --> 00:06:47,860
Said function, I will call, said function by passing address of a body and index is zero zero the

84
00:06:47,870 --> 00:06:48,230
index.

85
00:06:48,260 --> 00:06:53,110
I want to write a value 15 so this rule should be changed to 15.

86
00:06:53,150 --> 00:06:54,420
They should be changed to 15.

87
00:06:55,130 --> 00:06:55,940
Let us run it.

88
00:06:57,260 --> 00:07:01,070
Anyway, afterwards, they are displaying all the elements, so you can see that the elements are changed

89
00:07:01,070 --> 00:07:03,680
for the first element was to and has become 15.

90
00:07:04,610 --> 00:07:06,640
Yes, it is changing the value.

91
00:07:06,660 --> 00:07:08,410
So it's not inserting an element.

92
00:07:08,420 --> 00:07:12,980
It is just changing element at a given index, replacing an element.

93
00:07:14,790 --> 00:07:21,510
Now, let us call now, let us call Max function, so I will directly print the result, percentile

94
00:07:21,510 --> 00:07:23,280
the slash and.

95
00:07:26,260 --> 00:07:34,080
Max function, I will pass Uhry on here inside, but I will change one element to 14, so I should get

96
00:07:34,100 --> 00:07:35,500
maximum, that's 14.

97
00:07:37,840 --> 00:07:42,340
Let us run the program, its maximum limit is 14.

98
00:07:44,200 --> 00:07:45,850
I'll make the sacrifice.

99
00:07:48,660 --> 00:07:50,490
A maximum of 35.

100
00:07:52,680 --> 00:07:58,740
I just changed its function to men and let us see what is the minimum value I will give the value added

101
00:07:58,740 --> 00:07:59,130
to.

102
00:08:01,930 --> 00:08:04,330
Minimum value values to, yes, resolve this to.

103
00:08:07,830 --> 00:08:09,150
I'll change the value to.

104
00:08:10,380 --> 00:08:14,340
Twenty three, so now the minimum value is a three, so I should get the other three.

105
00:08:14,940 --> 00:08:16,500
Yes, it is industry.

106
00:08:18,610 --> 00:08:20,370
I will change this function to some.

107
00:08:23,860 --> 00:08:30,700
Total sum of all the elements of Seventy-one, so if you add them, you may get Seventy-one then average

108
00:08:30,970 --> 00:08:31,920
for this Uhry.

109
00:08:35,980 --> 00:08:41,020
So it should be a float type, so I will change this to float.

110
00:08:43,549 --> 00:08:45,320
So the average is fourteen point to.

111
00:08:46,770 --> 00:08:51,210
Perfect, so that's all we have implemented, all the functions.

112
00:08:53,210 --> 00:08:54,460
So that's all in this video.

