1
00:00:00,300 --> 00:00:09,030
Now, this section is about <PERSON><PERSON>, so in this video, we will learn why we need linguist so for covering

2
00:00:09,030 --> 00:00:15,060
the topic, I'll be discussing about these two things, problems that arise and the difference between

3
00:00:15,220 --> 00:00:16,500
ADI and linguist.

4
00:00:17,040 --> 00:00:22,140
So foresting problem with the other is of a fixed size.

5
00:00:22,140 --> 00:00:27,240
Whenever you are creating an error, you have to mention the size and later on you cannot increase or

6
00:00:27,240 --> 00:00:28,500
decrease the size of an array.

7
00:00:29,040 --> 00:00:31,210
So once created, it remains the same.

8
00:00:31,920 --> 00:00:32,850
That is the problem.

9
00:00:33,420 --> 00:00:34,910
Why fixie the problem?

10
00:00:35,340 --> 00:00:40,710
See, unless we are sure that we are going to store some number of elements, if we know the size,

11
00:00:40,710 --> 00:00:44,880
the number of elements, then we can create an array of that particular size.

12
00:00:45,390 --> 00:00:51,300
If we don't know how many elements we are going to store during the runtime of a program, then we cannot

13
00:00:51,300 --> 00:00:52,850
decide the size of an array.

14
00:00:53,550 --> 00:00:59,520
If we take some random size, then that size may be either insufficient or it may be.

15
00:01:00,660 --> 00:01:03,840
For example, I'm not sure how many elements I'm going to store.

16
00:01:04,120 --> 00:01:12,750
So in the program, if I declare an array of size of 10, then in this I cannot store more than 10 elements

17
00:01:14,130 --> 00:01:14,500
support.

18
00:01:14,520 --> 00:01:19,010
I have more than 10 elements during runtime, then I cannot store them.

19
00:01:19,830 --> 00:01:26,040
And if I am just having two three elements, then lot of space in that area will be wasted.

20
00:01:27,520 --> 00:01:29,980
So I think a lot five hundred.

21
00:01:31,770 --> 00:01:38,280
Now, again, I cannot store the elements more than 100 or so during runtime of a program, don't know

22
00:01:38,280 --> 00:01:40,010
how many elements will be required.

23
00:01:41,190 --> 00:01:45,210
See, one thing I'll make you clear, we are programmers.

24
00:01:45,420 --> 00:01:51,980
We write programs, applications for home for users, people who use our programs.

25
00:01:52,710 --> 00:01:58,600
So people who use our programs will decide how many elements they are going to enter into the program.

26
00:01:59,040 --> 00:02:05,640
So there what will be the list size or array size we don't call for at runtime.

27
00:02:05,910 --> 00:02:11,600
The list of sites will be decided, but at coding time or compile time, we have to give the files.

28
00:02:12,090 --> 00:02:13,000
This is the problem.

29
00:02:13,500 --> 00:02:19,480
So this is the problem with the idea that next let us see the difference between a real inkless.

30
00:02:19,480 --> 00:02:26,960
So I'll discuss more about it and then I'll come to linguist, not a little bit of a little bit of revision.

31
00:02:26,970 --> 00:02:32,390
We already know that we can create an array either inside stack or we can create an inside heap.

32
00:02:32,700 --> 00:02:38,490
So for creating an array inside the stack, we see integer, a size that sometimes I'm thinking a small

33
00:02:38,490 --> 00:02:38,960
size here.

34
00:02:39,270 --> 00:02:43,630
So this will be created inside activation record of mean function.

35
00:02:44,070 --> 00:02:46,390
So idea of five five will be created there.

36
00:02:48,050 --> 00:02:53,560
Now, if you want to create an uhry in heap, then we should take a pointer.

37
00:02:53,740 --> 00:02:59,830
So four pointer, I'll take the variable name B, then we should say new, whatever the size you want.

38
00:03:00,110 --> 00:03:03,890
So this size can be taken at runtime and mention here that is the benefit.

39
00:03:04,160 --> 00:03:06,530
But directly I'm writing here as an example.

40
00:03:06,830 --> 00:03:09,050
So Pointer P will be available here.

41
00:03:09,590 --> 00:03:12,770
An array of the size of five will be created inside.

42
00:03:12,860 --> 00:03:15,590
Keep this pointer pointing on that one.

43
00:03:17,660 --> 00:03:20,870
So this is inside stock and this is inside keep.

44
00:03:22,110 --> 00:03:27,750
Another, the benefit of an array is that all these locations will be contiguous, there will be side

45
00:03:27,750 --> 00:03:33,210
by side so you can access them with the index that this is zero Telemann first elements again, third

46
00:03:33,210 --> 00:03:34,020
and fourth element.

47
00:03:35,230 --> 00:03:40,810
So randomly you can reach on any element, so you imagine just like a bench.

48
00:03:42,070 --> 00:03:48,060
There is a bench of capacity five, five people can sit on that bench, so when you're making a bench

49
00:03:48,060 --> 00:03:51,990
at that time, only decide what size you want, then that capacity is fixed.

50
00:03:52,750 --> 00:03:54,940
Not only those many people can sit on that bench.

51
00:03:55,180 --> 00:03:59,460
If more people there's more space, it's less people, then space is wasted.

52
00:04:00,010 --> 00:04:01,240
So this is like a bench.

53
00:04:04,740 --> 00:04:11,940
Now, coming up next here with what we want this, we don't want a fixed sized bench like this because

54
00:04:11,940 --> 00:04:16,860
we don't know the size who is using our program and what is this requirement?

55
00:04:16,870 --> 00:04:18,029
How many elements is having?

56
00:04:18,060 --> 00:04:18,560
We don't know.

57
00:04:18,930 --> 00:04:22,460
Let him go on giving the elements as many elements as you want.

58
00:04:22,680 --> 00:04:28,290
So the let the user give as many elements as you want, then we cannot go for a nanny.

59
00:04:28,980 --> 00:04:29,760
And one more thing.

60
00:04:30,000 --> 00:04:33,320
Let the user add more elements and also remove the element.

61
00:04:33,340 --> 00:04:38,690
So we want some data structure which should grow and reduce in size.

62
00:04:38,700 --> 00:04:41,540
So we want a variable size structure.

63
00:04:42,540 --> 00:04:49,350
So for that, I'll just share the idea about legalist, so for that, we must create a memory in the

64
00:04:49,350 --> 00:04:50,970
heap during that time.

65
00:04:52,170 --> 00:04:57,460
And we should use linking for making another link list.

66
00:04:58,050 --> 00:05:01,440
So how let us see, I'm just sharing the idea here.

67
00:05:02,460 --> 00:05:08,720
See, for allocating memory inside the other inside, we have taken the pointer and with one pointer,

68
00:05:08,730 --> 00:05:13,980
I have a look at that, five spaces with one point, I have to look at it, five spaces again, made

69
00:05:13,980 --> 00:05:14,850
it like a bench.

70
00:05:16,140 --> 00:05:19,020
But now I will take one pointer.

71
00:05:19,100 --> 00:05:21,320
Let me call it guys first.

72
00:05:22,320 --> 00:05:23,900
I just keep a pointer with me.

73
00:05:24,600 --> 00:05:25,680
Now, watch this carefully.

74
00:05:25,680 --> 00:05:28,850
I'm shooting the idea of linguist's not showing.

75
00:05:28,870 --> 00:05:30,510
What is Linkous just shooting an idea.

76
00:05:30,900 --> 00:05:32,350
I have a pointer now.

77
00:05:32,480 --> 00:05:34,950
I want to store one element, just one element.

78
00:05:35,160 --> 00:05:39,480
Then for this I look at the space at this point.

79
00:05:39,480 --> 00:05:41,070
The first will be pointing on this one.

80
00:05:41,880 --> 00:05:43,860
So that is for storing just one element.

81
00:05:44,070 --> 00:05:45,780
That is the eight is the element.

82
00:05:47,400 --> 00:05:50,030
So at this point that we can point only on one element.

83
00:05:50,160 --> 00:05:52,080
What about next if I want more?

84
00:05:52,380 --> 00:05:59,250
So along with this element, we also look at the space for a pointer to the next element, if there

85
00:05:59,250 --> 00:06:01,260
is anything, Suleymanov pointing on that one.

86
00:06:01,980 --> 00:06:03,390
So right now there is nothing solid.

87
00:06:05,110 --> 00:06:05,930
So that's it.

88
00:06:06,360 --> 00:06:07,410
We made a node.

89
00:06:07,800 --> 00:06:09,600
So first pointer was there.

90
00:06:09,870 --> 00:06:15,690
When we have a look at it, the memory for just one element and we have a look at that memory for element

91
00:06:15,690 --> 00:06:17,460
as well as pointed to the next one.

92
00:06:18,880 --> 00:06:25,420
Now, we have one more point that if we want one more element, then again, I look at the memory like

93
00:06:25,420 --> 00:06:32,020
this here, you stole the element, then this will be pointing on this one, like this pointer was pointing

94
00:06:32,020 --> 00:06:34,060
on this or this point of reporting on this node.

95
00:06:34,360 --> 00:06:36,640
So we can call that block as a node now.

96
00:06:38,720 --> 00:06:40,740
The next I have one more element, 12.

97
00:06:41,000 --> 00:06:46,630
So, again, create a. Northville will contain value to it and the pointer to next and this point really

98
00:06:46,640 --> 00:06:47,590
pointing on this one.

99
00:06:48,570 --> 00:06:51,570
So there is no known beyond this one from it be known.

100
00:06:53,040 --> 00:06:57,270
So I'm creating a new one whenever I want to store an element.

101
00:06:59,390 --> 00:07:06,860
So it is just like it is not a bench, imagine that we have free space, whoever want to come and sit.

102
00:07:06,890 --> 00:07:09,910
He should bring his chair over the next person come.

103
00:07:09,920 --> 00:07:11,900
The first person will link to that person.

104
00:07:11,900 --> 00:07:13,830
Then that will link to the next person.

105
00:07:14,060 --> 00:07:22,010
So in this world, forming a chain or length of chairs and if anybody is leaving, he can take a chair

106
00:07:22,010 --> 00:07:22,430
with him.

107
00:07:22,910 --> 00:07:25,650
So whoever is coming in will bring a chair.

108
00:07:25,820 --> 00:07:31,250
So every element will bring its own memory and point to the next element.

109
00:07:31,580 --> 00:07:37,080
So together, element space and the pointer space together we call it as Norder.

110
00:07:37,790 --> 00:07:41,760
So this is how we Follmer Linklaters inside heap.

111
00:07:43,850 --> 00:07:49,580
So now you can see that it's suppose you want to remove three, you can easily remove three this eight

112
00:07:49,580 --> 00:07:55,850
and stop pointing on three this morning on 12 that season, and you can remove this one.

113
00:07:56,780 --> 00:07:59,630
I'll bring it back as it is now.

114
00:07:59,630 --> 00:08:04,760
Suppose you want to bring some new element in between three and two and then you can simply create a..

115
00:08:04,760 --> 00:08:09,870
Let's element seven seven will be pointing on two and three will be pointing on seven.

116
00:08:10,490 --> 00:08:14,660
Now, if you start from here, you'll go on eight and some me you can go on three from three.

117
00:08:14,660 --> 00:08:16,880
You can go on seven seven you can go on 12.

118
00:08:17,420 --> 00:08:20,300
So it's easy to insert something in between also.

119
00:08:22,270 --> 00:08:30,730
By just changing links, so this data structure is more flexible than Uhry, and the size can grow and

120
00:08:30,730 --> 00:08:38,520
size can really and you can easily insert new elements or remove the existing elements from the legris.

121
00:08:40,370 --> 00:08:41,640
So that's it.

122
00:08:41,659 --> 00:08:43,020
I have given the introduction.

123
00:08:43,340 --> 00:08:44,169
What does it mean?

124
00:08:44,720 --> 00:08:45,280
What does it mean?

125
00:08:45,860 --> 00:08:50,510
So remember, this points once again that it didn't say it's tagaris created inside.

126
00:08:50,510 --> 00:08:53,630
He using appointer and the linguist's.

127
00:08:53,780 --> 00:08:56,390
There is no meaning of creating a language inside STAC.

128
00:08:56,510 --> 00:08:59,810
We create linguists always inside keep so dynamically.

129
00:08:59,810 --> 00:09:02,830
We look at the memory for each element separately.

130
00:09:02,840 --> 00:09:03,350
Look at it.

131
00:09:03,500 --> 00:09:05,470
If Elementalist remove, the memory is gone.

132
00:09:06,620 --> 00:09:08,090
So the size is variable.

133
00:09:09,640 --> 00:09:15,190
So in the next video, we will have the details of a Lintas, this will just I was sharing the idea

134
00:09:15,190 --> 00:09:16,170
about linguist.

