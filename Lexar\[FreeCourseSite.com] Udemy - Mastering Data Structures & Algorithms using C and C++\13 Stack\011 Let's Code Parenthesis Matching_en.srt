1
00:00:00,210 --> 00:00:05,070
In this video, we will develop a function for checking parties as matching already I have discussed

2
00:00:05,070 --> 00:00:09,790
that function on whiteboard, so let us write a function for checking parameters and matching.

3
00:00:10,200 --> 00:00:16,340
So I'm using the program, which I already have written, using C language for start using Linkous,

4
00:00:16,360 --> 00:00:18,990
Solidere, have Bush Poppen display functions.

5
00:00:20,340 --> 00:00:22,800
And this note structure is having data.

6
00:00:22,830 --> 00:00:28,230
This data should not be of tiepin digit, it should be of type character because we have characters

7
00:00:28,230 --> 00:00:29,760
in expression.

8
00:00:30,870 --> 00:00:37,260
Then in the pub function, your push function should also take a character, it should not be an integer

9
00:00:37,290 --> 00:00:38,090
in C language.

10
00:00:38,100 --> 00:00:40,890
We do not have templates, so we should modify the data type everywhere.

11
00:00:41,430 --> 00:00:46,930
Now, this is of type character and the function should return a character type value.

12
00:00:48,060 --> 00:00:51,280
That's all I think I have made all the changes.

13
00:00:51,380 --> 00:00:56,910
Now let us write a function for checking whether the parentheses, balance or not.

14
00:00:57,270 --> 00:01:04,620
So for that inside the main function, first of all, I should take an array that is expression XP and

15
00:01:04,620 --> 00:01:05,220
do this.

16
00:01:05,220 --> 00:01:11,400
I will make it as a pointer so that I'll take it as a string and I will give expression.

17
00:01:12,350 --> 00:01:13,430
A plus B.

18
00:01:15,950 --> 00:01:17,120
Multiplied by.

19
00:01:19,450 --> 00:01:22,280
C minus D now partners are closed.

20
00:01:22,300 --> 00:01:28,810
I have given a balanced one, not this I to a function, so as a string that is array of characters.

21
00:01:28,870 --> 00:01:31,900
So I should have a function which will take a.

22
00:01:34,950 --> 00:01:40,890
Array of characters, that is a strength and a certain type of integer to return, whether true or false.

23
00:01:40,920 --> 00:01:42,040
This is balanced or not.

24
00:01:42,040 --> 00:01:49,970
So I will call the function names is balanced and it should take a character type pointer to an expression.

25
00:01:49,980 --> 00:01:51,060
So does the string.

26
00:01:53,440 --> 00:02:00,370
Now, I have to use a follow for scanning through this expression, so I will take I then using follow

27
00:02:00,370 --> 00:02:06,790
up, I will scan through it, for I is assigned Zettl an expression of a.

28
00:02:07,860 --> 00:02:16,620
As not equal to zero, so until it reaches zero, should continue then I plus.

29
00:02:18,880 --> 00:02:25,420
Then inside the followup, I should check whether a symbol and expression of eye is equal to.

30
00:02:26,480 --> 00:02:33,170
Opening bracket, if it is opening bracket, if it is opening bracket, then I should push it into the

31
00:02:33,170 --> 00:02:34,430
stack of.

32
00:02:35,320 --> 00:02:36,640
Expression of I.

33
00:02:38,070 --> 00:02:44,250
As I should check, if it is a closing bracket expression of I if it is a closing bracket.

34
00:02:45,630 --> 00:02:47,890
Then I should check if the stack is empty.

35
00:02:48,060 --> 00:02:49,510
How to check it, the stack is empty.

36
00:02:49,530 --> 00:02:51,510
I did not write any function for the stack empty.

37
00:02:51,510 --> 00:02:55,690
So I will say if copies equals to another, it means the stack is empty.

38
00:02:55,710 --> 00:03:00,210
So there is nothing in the stacks I should return false a false WSDL.

39
00:03:00,990 --> 00:03:03,480
Otherwise I should put an element from the stack.

40
00:03:03,480 --> 00:03:06,180
And I don't need that element and I should continue.

41
00:03:08,160 --> 00:03:09,720
That's all at the end of the loop.

42
00:03:11,170 --> 00:03:18,280
I should check if the stack is empty, so if OP is equal to another, there is nothing in the statute

43
00:03:18,280 --> 00:03:19,460
then I should return to.

44
00:03:20,270 --> 00:03:21,190
Yes, it is.

45
00:03:22,700 --> 00:03:27,680
Balance parenthesized otherwise, if there is something remaining in the stack, they should return

46
00:03:27,680 --> 00:03:28,250
false.

47
00:03:29,980 --> 00:03:32,110
That fell out of the function.

48
00:03:34,140 --> 00:03:40,200
Here from the main function, I will call and bring this output directly percentile.

49
00:03:41,940 --> 00:03:42,340
De.

50
00:03:44,900 --> 00:03:46,420
All is balanced.

51
00:03:48,210 --> 00:03:51,720
By passing an expression, it should return through.

52
00:03:52,640 --> 00:03:55,500
Yes, it has given one that is true.

53
00:03:56,570 --> 00:03:59,570
I try to remove one bracket and let us run this.

54
00:04:02,230 --> 00:04:04,010
Falls, it has certain faults.

55
00:04:04,720 --> 00:04:06,460
I'll give two extra brackets.

56
00:04:07,570 --> 00:04:11,410
Let us run this a little false, yes, false.

57
00:04:11,860 --> 00:04:13,930
That's all you can implement its function by your.

