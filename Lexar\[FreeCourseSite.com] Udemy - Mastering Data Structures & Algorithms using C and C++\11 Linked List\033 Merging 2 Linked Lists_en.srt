1
00:00:00,240 --> 00:00:04,440
And this video will look at merging of two linguist.

2
00:00:06,200 --> 00:00:11,210
If you look into the section arrays there, we have seen how to manage two arrays.

3
00:00:11,870 --> 00:00:14,340
Now we are learning how to manage to link list.

4
00:00:14,750 --> 00:00:22,760
So let me tell you briefly about merge process merging is the process of combining two sorted list into

5
00:00:22,760 --> 00:00:24,180
a single sorted list.

6
00:00:24,530 --> 00:00:31,040
So I have to sort of list first, Linklaters sorted to wait 10, 15 seconds for that for seven, 12,

7
00:00:31,040 --> 00:00:32,439
14, buther for.

8
00:00:33,170 --> 00:00:38,210
So combine them and we will combine these two into a single sorted list.

9
00:00:38,780 --> 00:00:44,670
The next thing, if you remember merging to arrays, you require one more today.

10
00:00:45,020 --> 00:00:47,090
But in Linklaters it's not necessary.

11
00:00:47,300 --> 00:00:49,330
If you want, you can take third lengths.

12
00:00:49,340 --> 00:00:56,660
Otherwise you can combine these two and make it as a single linked list one single because you don't

13
00:00:56,660 --> 00:00:58,700
need extra linguist.

14
00:00:59,980 --> 00:01:06,520
I'll explain the procedure, how merging is done over Linklaters and also simultaneously in Maryland

15
00:01:06,820 --> 00:01:08,260
program called also.

16
00:01:09,250 --> 00:01:16,050
So let us see, this is first English, the second language for merging with LA, one more point to

17
00:01:16,070 --> 00:01:17,460
let us call it as a third.

18
00:01:17,710 --> 00:01:20,620
I will take one point or call third point that hard.

19
00:01:21,310 --> 00:01:27,880
And for helping that point, I will I will take one more point up that is lost.

20
00:01:28,660 --> 00:01:36,520
I need to point this one is a term that will be binding on the first node of total English that is marginal

21
00:01:36,700 --> 00:01:36,970
list.

22
00:01:37,240 --> 00:01:43,810
And also I will use one more point of call last, literally pointing on the last node of that merged

23
00:01:43,990 --> 00:01:44,700
Legris.

24
00:01:45,880 --> 00:01:47,950
So I have to use two pointers.

25
00:01:48,950 --> 00:01:50,400
Let us start the procedure.

26
00:01:50,650 --> 00:01:51,690
What is the procedure?

27
00:01:52,330 --> 00:01:59,660
Compare the element of first node and first link list an element of first node and second list.

28
00:02:00,010 --> 00:02:01,270
Which one is a smaller.

29
00:02:01,810 --> 00:02:04,120
This is a smaller one.

30
00:02:04,120 --> 00:02:05,010
This is smaller.

31
00:02:05,110 --> 00:02:08,320
Bring last as well as third upon.

32
00:02:08,500 --> 00:02:17,920
This node will bring Todd as well as last on this node because this is the first to know that we are

33
00:02:17,980 --> 00:02:19,110
starting from here.

34
00:02:19,570 --> 00:02:25,630
So make it point on that one, then move the first to next node first.

35
00:02:25,630 --> 00:02:32,350
We'll move to next node and just write F instead of first, then make this has none.

36
00:02:34,030 --> 00:02:38,980
So now you can see that the first link lists as having military nodes and is the first node.

37
00:02:39,160 --> 00:02:43,840
Then what about the previous note that was already there in the link last night belongs to Merge Linguist

38
00:02:43,840 --> 00:02:48,190
where both thought and last supporting them in the first step.

39
00:02:48,190 --> 00:02:51,550
We have to bring both third and last on same node.

40
00:02:53,280 --> 00:02:56,310
Next, again, continue compar.

41
00:02:57,880 --> 00:03:02,000
Eight and four, who is a small second one, is this one.

42
00:03:02,440 --> 00:03:09,730
So now the steps are repeating watch, this is what I should do last to make sure the point on the small.

43
00:03:09,880 --> 00:03:16,800
OK, last and extra point on this morning and bring last on this note.

44
00:03:17,830 --> 00:03:24,700
OK, then the move was second to the next node sickness moved on to the next morning.

45
00:03:25,120 --> 00:03:31,160
So let us call just as ever for a second and make this last next as a nun.

46
00:03:32,170 --> 00:03:35,280
Now there are two nodes in this third linked list.

47
00:03:35,620 --> 00:03:37,930
This is the starting node and the next part.

48
00:03:39,730 --> 00:03:47,290
They'll continue the same procedure, compare first and second value, so this is a smaller so make

49
00:03:47,290 --> 00:03:54,970
this last to extra point on the Snoad and the move will last on this Norn, I'll call it guys just end

50
00:03:55,390 --> 00:03:59,420
and move seconded to next to Naude and make the final.

51
00:04:00,370 --> 00:04:02,320
So now the procedure will be repeating.

52
00:04:02,650 --> 00:04:08,710
I will continue but in here let us right on the call so I'll bring back the things a..

53
00:04:08,710 --> 00:04:10,330
And again to show you the code.

54
00:04:11,500 --> 00:04:14,560
Now, back to the same thing, what was the first step?

55
00:04:15,010 --> 00:04:19,000
First step is a check, who is a smaller, better first one or second one?

56
00:04:19,269 --> 00:04:22,230
Whoever is the smaller bring 2.0.

57
00:04:22,420 --> 00:04:24,780
That is the third and last on that node.

58
00:04:25,060 --> 00:04:30,590
So I will write down that code if forced to data.

59
00:04:32,750 --> 00:04:33,950
It's less than.

60
00:04:35,470 --> 00:04:36,850
Second, Siddharta.

61
00:04:38,670 --> 00:04:39,060
Then.

62
00:04:40,280 --> 00:04:40,790
Bring.

63
00:04:43,810 --> 00:04:44,920
Bring Todd.

64
00:04:46,930 --> 00:04:53,860
As well as last up on first, that is bringing Todd.

65
00:04:56,150 --> 00:05:02,840
And last up on the same note, we did this one, then move fast to make Snoad.

66
00:05:02,990 --> 00:05:08,080
So take us to next Naude first we'll move to next model.

67
00:05:09,230 --> 00:05:13,700
First should move to first next Naude.

68
00:05:15,280 --> 00:05:25,600
Then after that, this last to next should be made as null, so last next, as mad as not, this is

69
00:05:25,600 --> 00:05:27,490
what we did for the first time.

70
00:05:28,690 --> 00:05:32,730
We did this the first link, not because this was smaller.

71
00:05:32,920 --> 00:05:38,490
If suppose this was smaller, we would have done the same thing on this node.

72
00:05:38,620 --> 00:05:41,110
So I should trade on inside Hellespont.

73
00:05:42,220 --> 00:05:55,000
For the second same steps toward our last should point on second and second, move on to the next second

74
00:05:55,000 --> 00:06:06,850
should move on seconds next Naude and last next should be made as none as we did upon first link Lismore's.

75
00:06:06,850 --> 00:06:09,650
We should do the same thing for second linguist's also.

76
00:06:09,670 --> 00:06:12,240
So this is the initial step that we have to do.

77
00:06:13,150 --> 00:06:15,820
Then after this we were doing repeating steps.

78
00:06:15,820 --> 00:06:17,140
What were the rebidding steps.

79
00:06:17,500 --> 00:06:21,640
Compare the date of first and second, which is a smaller.

80
00:06:21,640 --> 00:06:22,660
Now this is smaller.

81
00:06:22,810 --> 00:06:26,290
So make last an extra point on this one.

82
00:06:27,010 --> 00:06:27,970
This is one step.

83
00:06:28,690 --> 00:06:31,600
Then bring last on this note.

84
00:06:31,840 --> 00:06:33,150
This is the second step.

85
00:06:34,030 --> 00:06:38,900
Then if it is the first or second, we don't know whoever it may be, it should move to the next node.

86
00:06:38,930 --> 00:06:44,510
OK, second is moving to the next node, the next middle last to next to us.

87
00:06:46,000 --> 00:06:47,740
These are the steps that we are doing.

88
00:06:48,400 --> 00:06:53,830
Let me repeat once again, compared foster data with second data, which is a smaller that is eight

89
00:06:53,830 --> 00:06:54,510
and this is seven.

90
00:06:54,520 --> 00:06:55,300
So this smaller.

91
00:06:55,660 --> 00:07:05,350
So make last an extra point on this node and bring last upon this node and move again to the next node.

92
00:07:08,280 --> 00:07:10,760
And make last as null.

93
00:07:11,970 --> 00:07:12,920
That's what we are doing.

94
00:07:14,280 --> 00:07:19,800
Again, I do one more time compare first date with the second set of first, the data is smaller, so

95
00:07:19,800 --> 00:07:27,450
make last an extra point on the data and move last up to that point, whether it is first or second,

96
00:07:27,910 --> 00:07:33,890
then the move to the next node first is move to the next node and the last to make sure we made.

97
00:07:35,160 --> 00:07:36,410
This what I have to do.

98
00:07:36,630 --> 00:07:38,970
Whoever said data is smaller.

99
00:07:39,270 --> 00:07:41,430
So again, I will write on the code here.

100
00:07:42,150 --> 00:07:54,040
If first data is less, then seconds later this is the repeating part.

101
00:07:54,270 --> 00:07:57,200
So here we will be having loop.

102
00:07:57,330 --> 00:07:57,820
Right.

103
00:07:57,990 --> 00:08:01,800
So this is a part of loop as we are going on repeating this one.

104
00:08:02,040 --> 00:08:03,820
So what should be the condition in loop?

105
00:08:03,840 --> 00:08:06,630
I will show you afterwards, but let us see this one.

106
00:08:06,810 --> 00:08:09,250
Now, let us compare no first to data.

107
00:08:09,250 --> 00:08:11,710
Is the tenants against data is the 12th, right.

108
00:08:11,730 --> 00:08:12,510
So that is smaller.

109
00:08:12,540 --> 00:08:15,990
So what I should do last and they should point on first.

110
00:08:17,580 --> 00:08:22,410
Last to next to should the point on first.

111
00:08:24,590 --> 00:08:24,980
Ben.

112
00:08:26,350 --> 00:08:35,919
Lance should be pointing up on first and got in the same line, Lastra should be same as the first,

113
00:08:36,820 --> 00:08:46,120
then move fast to make small move first to next Naude move us to next note how to move us to next more

114
00:08:46,480 --> 00:08:50,630
first to find fault caps and then make last to make stars null.

115
00:08:50,830 --> 00:08:53,980
Make last nicks has none so we'll continue here.

116
00:08:53,980 --> 00:08:59,540
Only Lance to next four should be made as no lack of space.

117
00:08:59,540 --> 00:09:01,330
So I haven't done a single line.

118
00:09:01,330 --> 00:09:03,730
You can see that these are the statements.

119
00:09:04,750 --> 00:09:10,420
This is one that makes a statement the next and next subtotal.

120
00:09:10,450 --> 00:09:13,990
One, two, three, four, four steps and performing.

121
00:09:14,170 --> 00:09:17,230
If this is smaller, faster the smaller.

122
00:09:17,710 --> 00:09:23,500
Otherwise, if second one is a smaller, then for the second same steps should be performed.

123
00:09:23,710 --> 00:09:25,330
So that is last to next.

124
00:09:25,330 --> 00:09:28,330
Should be made to point on second.

125
00:09:30,280 --> 00:09:37,900
Then last year, the point on second, I'm going to read the steps, the same step just of it will be

126
00:09:37,900 --> 00:09:40,690
second, I have written the same steps.

127
00:09:40,870 --> 00:09:46,750
If Fuster data is a small do this, if second data is more, Dodi's steps are same, only the point

128
00:09:46,750 --> 00:09:48,640
as a different point.

129
00:09:48,650 --> 00:09:50,390
I see the first point to see the second.

130
00:09:50,770 --> 00:09:52,730
So this we have to go on doing it.

131
00:09:52,750 --> 00:09:56,890
So let me continue for some more time and see next.

132
00:09:57,730 --> 00:09:58,600
Compare the data.

133
00:09:58,780 --> 00:09:59,270
This one.

134
00:09:59,590 --> 00:10:02,620
This one is a smaller one is a smaller so last.

135
00:10:02,620 --> 00:10:04,450
Annexure should be pointing on this one.

136
00:10:04,720 --> 00:10:08,770
And the last issue to come here and second should move on this one.

137
00:10:08,770 --> 00:10:11,890
I just tried this and last and there should be no.

138
00:10:13,710 --> 00:10:17,880
Then again, compare fossil data, 15 seconds to 14.

139
00:10:17,910 --> 00:10:20,730
This is smaller, so last night should point here.

140
00:10:20,970 --> 00:10:25,700
And the last point on this one and the second should move to the next node.

141
00:10:25,890 --> 00:10:30,520
So the second becomes null and the last null nexus should be made.

142
00:10:30,520 --> 00:10:31,090
That's another.

143
00:10:33,350 --> 00:10:38,960
Now, I have finished all the elements and one of the linguists that the second linguist and second

144
00:10:38,960 --> 00:10:43,010
linguist, there are new elements because I'm not sure I should stop.

145
00:10:43,520 --> 00:10:50,390
So it means this reputation they can do as long as there are some nodes in either fossilised or S.E

146
00:10:50,600 --> 00:10:53,060
If anyone even on the list ends, I should stop.

147
00:10:53,750 --> 00:10:58,990
If it becomes null or second become null, whoever becomes null, I should stop.

148
00:11:00,110 --> 00:11:01,340
So I should continue.

149
00:11:01,340 --> 00:11:04,910
If both are not, no phosphors should also be not null.

150
00:11:05,270 --> 00:11:11,300
Second should also be not so while first.

151
00:11:12,500 --> 00:11:13,430
Not equal to.

152
00:11:15,980 --> 00:11:18,440
And second.

153
00:11:19,460 --> 00:11:20,710
Not equal to none.

154
00:11:22,060 --> 00:11:25,710
This is the condition, so I'll just highlight this condition.

155
00:11:26,490 --> 00:11:28,360
See, this is the condition.

156
00:11:28,610 --> 00:11:32,230
So as long as none of them is none, I should continue.

157
00:11:32,840 --> 00:11:33,260
Right.

158
00:11:33,830 --> 00:11:40,130
So this is a loop loop is having these two things like those four steps, either for the first nor first

159
00:11:40,140 --> 00:11:42,180
linguist's, odd for the second linguist's.

160
00:11:42,200 --> 00:11:42,740
They are there.

161
00:11:44,270 --> 00:11:49,850
Now, at last, what is remaining see, one of the linguists has to finish and last disappointing on

162
00:11:49,850 --> 00:11:50,330
this note.

163
00:11:50,720 --> 00:11:53,050
Now, there are some notes remaining in first.

164
00:11:53,780 --> 00:11:59,900
So if there are some nodes remaining in first, then last of this one should point on first NetSol.

165
00:12:02,830 --> 00:12:08,290
Last from the extra point on first, if suppose there are no words remaining in second English, then

166
00:12:08,290 --> 00:12:08,960
it should be done.

167
00:12:09,160 --> 00:12:11,920
Second, so here I was right on that part.

168
00:12:12,520 --> 00:12:19,330
If justice not none after this loop, anyone is not null because one of them has become known.

169
00:12:19,360 --> 00:12:20,290
So it came out.

170
00:12:20,590 --> 00:12:21,970
The first one is not null.

171
00:12:22,210 --> 00:12:25,750
Then make a loss to make the first.

172
00:12:26,690 --> 00:12:27,900
That's what I have done here.

173
00:12:27,910 --> 00:12:29,640
C last is pointing on this one.

174
00:12:31,710 --> 00:12:41,940
Else, definitely second one will be not so lost to next year, the point on second, so wherever the

175
00:12:41,940 --> 00:12:44,110
NRA meaning it should point on that one.

176
00:12:44,640 --> 00:12:47,600
So this is the code for merging the code, this little.

177
00:12:48,630 --> 00:12:52,570
So if you see the third linked list, let me trace this one third.

178
00:12:52,680 --> 00:12:56,760
This gives the address of two to give the address of four for give that is of seven.

179
00:12:57,060 --> 00:13:02,520
Seven gives the address of eight, eight gives the address of ten, then gives the idea of two and two

180
00:13:02,640 --> 00:13:04,260
is leading us to 14.

181
00:13:04,260 --> 00:13:08,550
14 is leading us to 15 and after 15 there is none.

182
00:13:09,120 --> 00:13:12,760
So this has form or one single interest.

183
00:13:13,350 --> 00:13:14,550
Next, all this is much.

184
00:13:16,320 --> 00:13:18,960
Now let us do some analysis and analysis.

185
00:13:19,200 --> 00:13:24,630
There is no extra link lists required here, so that is the benefit of merging upon linked list.

186
00:13:24,930 --> 00:13:31,140
So you can see that merging is suitable for link lists because it doesn't require any extra space.

187
00:13:31,380 --> 00:13:34,560
In case of arrays, you need extra space.

188
00:13:35,610 --> 00:13:39,350
Then second thing is, what is the time taken by this process?

189
00:13:39,600 --> 00:13:45,540
So if we say there are elements and Forslund list then and elements and second linguist's, so the time

190
00:13:45,540 --> 00:13:51,810
instead of let's end this analysis we have already done in merging in a hurry.

191
00:13:51,990 --> 00:13:55,590
So you can watch that video if you want more than answers and answers.

192
00:13:55,590 --> 00:13:57,430
The same here, emplacing.

193
00:13:57,450 --> 00:14:01,410
So whenever we see emerging, we should mention the times emplace.

194
00:14:01,410 --> 00:14:07,080
And so Emrah, the number of elements and first link or another number of elements from the second nickless,

195
00:14:07,350 --> 00:14:13,170
usually we don't take to variables, but to show that this is merging, we are making it as a notation

196
00:14:13,170 --> 00:14:13,890
for merging.

197
00:14:14,070 --> 00:14:18,310
So emblazoned means, OK, this is merging, merging is used here.

198
00:14:18,840 --> 00:14:20,910
So it has become a location for much.

199
00:14:21,780 --> 00:14:23,760
So that's all about merging two linguistic.

