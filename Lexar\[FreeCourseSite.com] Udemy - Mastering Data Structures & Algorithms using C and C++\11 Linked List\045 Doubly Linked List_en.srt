1
00:00:00,480 --> 00:00:07,710
And this video, I'll give you introduction to the list, this is an example, missed the previous link

2
00:00:07,710 --> 00:00:09,050
is what we have seen.

3
00:00:09,180 --> 00:00:11,730
Now we can call them a single interest linguist.

4
00:00:12,030 --> 00:00:17,540
So this is linked with so what is the single or let us look at this one single list.

5
00:00:17,550 --> 00:00:21,330
Every node will have a pointer to next node in a single event.

6
00:00:21,570 --> 00:00:27,340
But here you can see that a node is having a pointer to next node as well as previously.

7
00:00:27,660 --> 00:00:35,010
So it means from one node, I can go forward as far as I can come back so I can access this list of

8
00:00:35,010 --> 00:00:37,250
elements bidirectional.

9
00:00:37,410 --> 00:00:39,830
I can traverse them in either direction.

10
00:00:40,500 --> 00:00:45,920
Suppose I'm starting from first node that is number six, then I can go to nine, then three.

11
00:00:46,100 --> 00:00:51,420
So they want to go back previous so I can come back here or I want to go to next.

12
00:00:51,570 --> 00:00:55,040
I can go to next like inside browser.

13
00:00:55,170 --> 00:01:00,600
If you have a visitor from that and you have clicked some links and you have gone deeper and deeper,

14
00:01:00,930 --> 00:01:03,980
then you can go forward and backward afterwards.

15
00:01:04,110 --> 00:01:08,820
So if you come back, you will be back on the previous page if you say I want to go forward again,

16
00:01:08,830 --> 00:01:10,260
so then you can go to the speech.

17
00:01:10,500 --> 00:01:17,280
So every new page will be added to the list like structure so that you can move in between the pages

18
00:01:17,280 --> 00:01:18,000
if you want.

19
00:01:18,690 --> 00:01:24,990
If you take the example of a contact list in our mobile phone, then you can scroll up and down.

20
00:01:24,990 --> 00:01:27,000
You can move in both the direction.

21
00:01:27,360 --> 00:01:30,220
You can go to next contact or the previous contact.

22
00:01:30,510 --> 00:01:34,230
So that's what it is possible using the interest.

23
00:01:34,560 --> 00:01:42,630
So there are now let us see how to define a node of a particular node, try to show the node structure.

24
00:01:44,050 --> 00:01:52,720
So this is an old structure as having three elements or three members, one is the data and one is pointed

25
00:01:52,720 --> 00:01:56,630
to a previous node and nexus point to go next door.

26
00:01:56,650 --> 00:01:57,600
So there are three things.

27
00:01:57,970 --> 00:01:59,590
So every note is containing three things.

28
00:01:59,590 --> 00:02:05,830
The data pointed to the previous node pointer go next node and I will define the structure for this

29
00:02:05,830 --> 00:02:07,810
node using C language.

30
00:02:08,509 --> 00:02:10,240
There's a structure in C language.

31
00:02:11,080 --> 00:02:18,340
First the point of pipestem node, the next integer type data, and again the winter of type node that

32
00:02:18,340 --> 00:02:19,330
is next pointer.

33
00:02:20,500 --> 00:02:23,470
Not how to create this node and how to fill up the node.

34
00:02:23,470 --> 00:02:28,750
We already know we don't have to learn anything new are the same as single linguists.

35
00:02:29,670 --> 00:02:33,400
So just as a sample, I will create one note and fill some data and show you.

36
00:02:34,140 --> 00:02:35,280
So for creating a..

37
00:02:35,280 --> 00:02:37,610
I should have a point so I will take a pointer.

38
00:02:37,800 --> 00:02:42,750
Struck deep disappointment, I suppose, to the pointer.

39
00:02:44,730 --> 00:02:49,920
Now, to that day, I will create a new law and assignment for creating a new law in language, we should

40
00:02:49,920 --> 00:02:53,580
use Mellark function, but that will be lendee.

41
00:02:53,580 --> 00:02:57,690
So I'm using C++ syntax that is new for creating a new node.

42
00:02:57,990 --> 00:03:00,540
So new node A. will be created.

43
00:03:03,000 --> 00:03:05,490
This will create a new node having three members.

44
00:03:05,820 --> 00:03:07,810
Now, this is data and this is previous point.

45
00:03:07,860 --> 00:03:08,450
The next point.

46
00:03:08,520 --> 00:03:13,770
So I'll fill up all three previous then data and the next D previous is.

47
00:03:14,160 --> 00:03:17,190
So this is not easy.

48
00:03:17,190 --> 00:03:18,250
Data is the 10.

49
00:03:18,510 --> 00:03:20,450
So the value can be written here.

50
00:03:22,380 --> 00:03:23,640
D next is null.

51
00:03:23,880 --> 00:03:24,750
So this is not.

52
00:03:25,740 --> 00:03:29,540
So this is how we can create a new node and feel the values.

53
00:03:30,270 --> 00:03:37,410
And if you want you can link this node and this link list also on existing one of the operations that

54
00:03:37,410 --> 00:03:44,800
we can perform on the same as whatever we have done on a single interest, like displaying the linguists,

55
00:03:44,890 --> 00:03:46,950
counting the number of nodes in the link with.

56
00:03:48,170 --> 00:03:50,390
Finding maximum, finding minimum.

57
00:03:51,870 --> 00:03:57,970
Searching various operations for the same set of operations, but only the data structure is different.

58
00:03:57,990 --> 00:04:04,980
The difference is that you can move in the direction that for the benefit of using Inglis's, we want

59
00:04:04,980 --> 00:04:06,620
to access in either direction.

60
00:04:07,060 --> 00:04:08,430
That's nothing more than that.

61
00:04:08,730 --> 00:04:12,580
In the next video, we will see few operations on this list.

