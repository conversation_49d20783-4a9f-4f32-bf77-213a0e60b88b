1
00:00:00,120 --> 00:00:05,689
And this we do have a look at iterative procedure for preorder in order traversal, which I already

2
00:00:05,700 --> 00:00:06,480
have discussed.

3
00:00:08,060 --> 00:00:13,430
So I have already written a program, C language program for creating a tree, if you remember in the

4
00:00:13,430 --> 00:00:19,610
previous video, that same program I'm getting on and already I have written recursive functions for

5
00:00:19,610 --> 00:00:21,200
preordering and tostada.

6
00:00:22,790 --> 00:00:29,150
Now we will right iterative procedures that is using loop so far loop, I also need a stack.

7
00:00:29,160 --> 00:00:33,110
So already I have taken a stack header file, I have created a header file.

8
00:00:33,680 --> 00:00:36,200
Just how I have created for Kusakabe.

9
00:00:36,200 --> 00:00:36,830
I have done it.

10
00:00:37,280 --> 00:00:40,930
This is not the type of pointer double pointer that is for update.

11
00:00:41,720 --> 00:00:45,160
Then I have written a function for creating a stack which is creating a stack here.

12
00:00:45,800 --> 00:00:52,070
These are the functions already we have seen in the stack using Uhry that I have already implemented

13
00:00:52,070 --> 00:00:52,310
them.

14
00:00:52,640 --> 00:00:56,780
So this push ApoB is empty, is full, all these functions already there.

15
00:00:57,080 --> 00:00:59,300
So this is a stack using this.

16
00:00:59,300 --> 00:01:01,580
I'll be using it in the iterative process.

17
00:01:02,780 --> 00:01:08,330
Now, let us come back to the program that is main function program here, I will vote on a preorder

18
00:01:08,330 --> 00:01:11,530
traversal function that is using loop iterative version.

19
00:01:12,050 --> 00:01:21,400
So let us write a function, I will call it as I preorder, and it should take struct node pointer to.

20
00:01:23,760 --> 00:01:29,580
And here it needs a stack, so create an object of type stack STK.

21
00:01:30,560 --> 00:01:38,570
Then I should call a function that is stock to create for initializing a stack, so I will send this

22
00:01:38,990 --> 00:01:42,930
address of the stack as well as I will create a very bigger size so that that is sufficient.

23
00:01:42,950 --> 00:01:44,540
So that is the size of the stack.

24
00:01:47,210 --> 00:01:50,930
Now, the procedure is why look, wimpy are.

25
00:01:52,130 --> 00:01:56,040
Not is empty, this is empty.

26
00:01:56,180 --> 00:02:01,250
There in the queue, also in the stack, also so far I have changed the name to his empty stack and

27
00:02:01,250 --> 00:02:02,420
we will pass the stack.

28
00:02:03,230 --> 00:02:05,210
It will check whether the stack is empty or not.

29
00:02:06,190 --> 00:02:08,180
And here, F.P. is not null.

30
00:02:09,009 --> 00:02:18,460
We will bring the data from E percent daily is data, then I should push this into a stack.

31
00:02:18,460 --> 00:02:26,890
So address of stack and point a B, if it is not null, then B should move on to left child ill child.

32
00:02:28,410 --> 00:02:30,300
Then else, if BP's null.

33
00:02:31,450 --> 00:02:39,790
If he's null, then we should pop out the address from the start that is unperson STK unticketed and

34
00:02:39,790 --> 00:02:42,940
B then P should move on to Rachel child.

35
00:02:45,230 --> 00:02:52,100
That's the very simple procedure, that iterative procedure for finding preorder traversal now here,

36
00:02:52,150 --> 00:02:55,130
the main function already I have the site recreate function.

37
00:02:55,130 --> 00:03:00,500
Then instead of post order, I'll just change it to preorder and I will call this function.

38
00:03:02,460 --> 00:03:04,110
I pre order function.

39
00:03:05,790 --> 00:03:07,080
Let us on the program.

40
00:03:10,610 --> 00:03:12,930
Or function in that small letter.

41
00:03:12,950 --> 00:03:14,090
I have to change this one.

42
00:03:14,510 --> 00:03:17,450
This is Stack small Cypriot.

43
00:03:18,970 --> 00:03:19,810
Let us run it.

44
00:03:21,570 --> 00:03:27,960
Now asking for the root value and its left child is 20, Rachel is 30, then I don't have any other

45
00:03:27,960 --> 00:03:28,400
children.

46
00:03:28,410 --> 00:03:29,880
So all these are minus one.

47
00:03:30,760 --> 00:03:34,500
Yes, preorder is 10, 20, 30, it's working.

48
00:03:35,370 --> 00:03:36,690
Let us run it once again.

49
00:03:39,570 --> 00:03:47,640
Rudest child is 20, right, Chad is 30 and it's left child is 40, that inflation is 50 to 60, 70,

50
00:03:48,300 --> 00:03:51,080
then I don't have any notes at all.

51
00:03:51,090 --> 00:03:55,320
So all I have to mention them as another.

52
00:03:56,640 --> 00:03:59,130
Yes, preorder is entering the.

53
00:04:00,300 --> 00:04:07,570
40 and 50, 30, 60, 70 or so left hand side is 20, 40, 50, and right hand side is 36 to 70.

54
00:04:07,980 --> 00:04:09,490
So here is the preorder.

55
00:04:09,510 --> 00:04:10,830
So it's like perfectly.

56
00:04:12,420 --> 00:04:16,709
Now, how about in order, so I will copy this function as it is.

57
00:04:19,010 --> 00:04:20,930
The preorder function I will copied.

58
00:04:24,090 --> 00:04:24,630
Kopi.

59
00:04:27,000 --> 00:04:32,550
Then based instead of I preorder, I will call I in order.

60
00:04:34,490 --> 00:04:41,360
And instead of spreading the ball here, I should call it, before going on the right trail.

61
00:04:45,380 --> 00:04:49,010
Then instead of preorder, I will call in order.

62
00:04:51,070 --> 00:04:57,520
Let us call a function I in order here, NetSol, see if you remember, just a change of location of

63
00:04:57,760 --> 00:05:02,470
the function will change you to in order traversal let us run the program.

64
00:05:05,040 --> 00:05:12,540
Asking for the route and left is 20, Rachel is 30, then I don't have any other data, so I know this

65
00:05:12,540 --> 00:05:15,500
one is in order is 32 and 30.

66
00:05:15,880 --> 00:05:16,610
It's perfect.

67
00:05:19,300 --> 00:05:25,180
So this program is little Andy, because all of these should have saved this duen stock and you can

68
00:05:25,180 --> 00:05:26,400
utilize those things here.

69
00:05:27,100 --> 00:05:28,360
So that's all in this video.

