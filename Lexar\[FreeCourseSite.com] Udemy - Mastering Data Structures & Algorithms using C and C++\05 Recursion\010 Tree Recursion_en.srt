1
00:00:00,720 --> 00:00:06,280
And this video will learn about the treaty question, so first of all, I will compare what is linear

2
00:00:06,280 --> 00:00:08,400
recursion and prediction.

3
00:00:08,430 --> 00:00:13,560
Let us see see a linear regression function that is recursive function.

4
00:00:13,740 --> 00:00:19,950
If it is calling itself only one time, you can see that the function is calling itself only one time,

5
00:00:21,090 --> 00:00:23,340
then it is a linear regression.

6
00:00:23,730 --> 00:00:28,590
So already we have seen in the previous video that the functions which are calling just one time and

7
00:00:28,590 --> 00:00:34,740
we have seen recursion and recursion, but in this example it is having something to process before

8
00:00:34,740 --> 00:00:36,490
the call as well as after the call.

9
00:00:36,750 --> 00:00:39,180
So it is just a recursive function.

10
00:00:39,930 --> 00:00:43,200
We cannot name it as head or tail recursion.

11
00:00:44,130 --> 00:00:47,060
And this code is not a C or C++ code.

12
00:00:47,070 --> 00:00:51,560
I have just written a pseudo code here, so it's not a complete function like an integer.

13
00:00:51,570 --> 00:00:52,470
Everything is not there.

14
00:00:52,480 --> 00:00:59,780
So don't try to read it as a C language code, just so I'm sure you structure than here prediction.

15
00:01:00,120 --> 00:01:07,470
So what is prediction if a function that is recursive function is calling itself more than one time.

16
00:01:07,680 --> 00:01:13,200
So here you can see it is calling itself first time and here it is calling itself second time.

17
00:01:13,440 --> 00:01:18,150
So all of a recursive function is calling itself more than one time.

18
00:01:18,370 --> 00:01:20,860
Then it has three recursion.

19
00:01:22,170 --> 00:01:25,170
So in my example, I have shown it as two times.

20
00:01:25,170 --> 00:01:29,370
So it may be more than two times also, so that we call it a straight recursion.

21
00:01:29,700 --> 00:01:35,790
So here I have one example of three recursion, which is similar to the previous examples, only the

22
00:01:35,790 --> 00:01:39,190
difference that is calling itself more than one time.

23
00:01:39,840 --> 00:01:45,230
So I will trace this one and show you how it will work and what will be the output.

24
00:01:45,420 --> 00:01:50,160
And also I will show you how the stack is created in this one.

25
00:01:50,820 --> 00:01:52,910
Let us look at the tracing of dysfunction.

26
00:01:52,920 --> 00:01:54,920
The function is caused by passing value.

27
00:01:54,940 --> 00:01:58,140
Three, I have taken a piece of stack.

28
00:01:58,140 --> 00:02:00,400
I'm not taking complete memory and data memory.

29
00:02:00,430 --> 00:02:07,230
Just I'm showing you how it is used by this recursive function, which is a three recursion letter.

30
00:02:07,230 --> 00:02:11,320
Strix Foster scarred by passing function with value three.

31
00:02:11,820 --> 00:02:18,900
So the first call as one of three for this function activation, Vegard is created and the value of

32
00:02:18,900 --> 00:02:20,230
N will be three.

33
00:02:20,640 --> 00:02:27,480
So this is the first record created and then one of those steps it has to perform first, print the

34
00:02:27,480 --> 00:02:33,980
value, then make two columns so it will bring the value, then it will make two columns.

35
00:02:34,530 --> 00:02:35,740
Let us print the value.

36
00:02:36,570 --> 00:02:43,920
So it will print the value three and make first a function call fun four and a minus one.

37
00:02:44,040 --> 00:02:47,920
That is two and second call.

38
00:02:47,940 --> 00:02:52,520
It has to mean that it will be doing once this call has finished.

39
00:02:53,370 --> 00:03:00,750
So let us first finish first column so far, this call and again activation record is created where

40
00:03:00,750 --> 00:03:04,500
the value of end will be to two.

41
00:03:05,280 --> 00:03:07,890
Now for this call, let us see what happens.

42
00:03:08,220 --> 00:03:10,740
And that is two is greater than zero.

43
00:03:10,740 --> 00:03:14,670
Yes, two is greater than zero print and then make two calls.

44
00:03:15,300 --> 00:03:16,200
So let us do it.

45
00:03:17,560 --> 00:03:27,100
But two and make one call that and minus one, that is, and minus one to two, minus one one, then

46
00:03:27,100 --> 00:03:30,520
second call it will make after finishing this one.

47
00:03:31,270 --> 00:03:35,580
So so far we have two value sprinters.

48
00:03:35,590 --> 00:03:38,040
So I'll also be writing the output here.

49
00:03:38,350 --> 00:03:44,180
So the output of the function so far is three and two.

50
00:03:46,630 --> 00:03:48,340
Now, let us continue with this call.

51
00:03:48,670 --> 00:03:57,790
This call again, activation record is created and the value of end will be one not for the value of

52
00:03:57,790 --> 00:03:58,840
an as one.

53
00:03:59,050 --> 00:04:00,590
One is greater than zero.

54
00:04:00,610 --> 00:04:01,990
Yes, it is greater than zero.

55
00:04:02,200 --> 00:04:06,610
Then it will perform three steps, first one, then make two calls.

56
00:04:06,620 --> 00:04:13,740
So first it will bring the value that is one, then it will make first call that reduced the value of

57
00:04:13,750 --> 00:04:14,110
N.

58
00:04:14,110 --> 00:04:14,980
So this is one.

59
00:04:14,980 --> 00:04:18,890
So it will be zero and Schenkkan it will make afterwards.

60
00:04:19,750 --> 00:04:23,520
So for this call again, a fresh call is made from here.

61
00:04:23,710 --> 00:04:24,910
So a new activation.

62
00:04:24,910 --> 00:04:28,690
The code is created where the value of end will be zero.

63
00:04:31,140 --> 00:04:39,900
Now, this time fan of zero, this is zero zero greater than zero, no, so it will not end in sight

64
00:04:40,050 --> 00:04:42,120
and it will not perform these steps.

65
00:04:43,050 --> 00:04:48,780
So the function call terminates, not a function calls terminates.

66
00:04:48,990 --> 00:04:52,740
This activation record is deleted from the stack.

67
00:04:54,210 --> 00:04:59,880
So before the election, if you count one, two, three, four, four, Activision recalls how many

68
00:04:59,880 --> 00:05:04,810
calls one, two, three, four, four calls for Activision records out there.

69
00:05:05,100 --> 00:05:06,270
So this was deleted.

70
00:05:07,830 --> 00:05:11,760
Not once it is deleted, it will go back to the previous call.

71
00:05:11,760 --> 00:05:13,320
It means this has finished.

72
00:05:13,680 --> 00:05:18,120
It will go back to the previous call in previous call for performing three steps.

73
00:05:18,120 --> 00:05:19,380
First was completed.

74
00:05:20,070 --> 00:05:21,600
Second is also complicated.

75
00:05:21,600 --> 00:05:24,440
The slow third one to actually perform.

76
00:05:24,750 --> 00:05:31,440
So it will make a call here four and the minus one next call and minus one.

77
00:05:31,680 --> 00:05:34,560
So N minus one, one minus one is again zero.

78
00:05:34,770 --> 00:05:38,340
So again, it will make a call with value of zero.

79
00:05:38,700 --> 00:05:41,210
So again, the activation record is created.

80
00:05:41,220 --> 00:05:42,290
Actually this was gone.

81
00:05:42,540 --> 00:05:44,770
So new activation, the code is again created.

82
00:05:44,790 --> 00:05:46,020
So I have it is this one.

83
00:05:46,020 --> 00:05:48,300
So let us assume again it is created here.

84
00:05:50,470 --> 00:05:52,780
So, again, the fate of the Steiger's for.

85
00:05:54,840 --> 00:06:02,580
Next, four four four zero, this is zero zero is not greater than zero, so it's not going to enter

86
00:06:02,580 --> 00:06:03,560
inside function.

87
00:06:03,570 --> 00:06:08,100
And so this activation, the card is deleted and this function terminates.

88
00:06:09,090 --> 00:06:16,350
So now you can see that for fun of one, all three steps are completed printing, calling itself two

89
00:06:16,350 --> 00:06:23,910
times with reduced value of and that is one a sprinter and zero zero as call.

90
00:06:24,120 --> 00:06:26,330
And they have finished and then the output.

91
00:06:26,340 --> 00:06:27,330
We also have one

92
00:06:30,630 --> 00:06:32,820
now this call is completed.

93
00:06:32,910 --> 00:06:34,480
All three steps are over.

94
00:06:34,920 --> 00:06:36,750
So it will go back to the previous call.

95
00:06:36,960 --> 00:06:41,430
So go back to the previous comments of this call has finished this activation.

96
00:06:41,430 --> 00:06:42,420
The code is deleted.

97
00:06:42,990 --> 00:06:45,980
So right now there are only two activation records, the cards in the stack.

98
00:06:46,230 --> 00:06:49,650
One is for value of another three and value of those two.

99
00:06:50,910 --> 00:06:58,140
Note is back on this call where and there's two and in that out of three steps, it has already performed

100
00:06:58,860 --> 00:07:03,370
first printing of two and then the next.

101
00:07:03,390 --> 00:07:06,250
Again, it has to make a call that is at minus one.

102
00:07:06,540 --> 00:07:10,320
So now it will make a call for fun of and minus one.

103
00:07:10,830 --> 00:07:13,920
So again, fun of and minus one.

104
00:07:14,250 --> 00:07:14,970
That is two.

105
00:07:14,970 --> 00:07:15,570
Minus one.

106
00:07:15,570 --> 00:07:16,290
That is one.

107
00:07:17,950 --> 00:07:19,460
Now again, a fresh call.

108
00:07:19,650 --> 00:07:22,490
So new activation record is creating.

109
00:07:26,960 --> 00:07:32,750
Now, for this, it is one, so it will bring to one now all the things that happens will be similar

110
00:07:32,750 --> 00:07:33,400
to this one.

111
00:07:33,830 --> 00:07:38,070
So it means it will print one and then it will call itself for.

112
00:07:38,120 --> 00:07:38,840
No, I'm not right.

113
00:07:38,870 --> 00:07:40,010
Complete name fun.

114
00:07:40,010 --> 00:07:44,290
I'm just using F because of lack of space, F of zero.

115
00:07:44,540 --> 00:07:49,370
So this Activision record is created again that this ends.

116
00:07:49,610 --> 00:07:50,840
So this is deleted.

117
00:07:51,230 --> 00:07:52,910
Then goes back again.

118
00:07:53,180 --> 00:07:54,300
Call for zero.

119
00:07:54,590 --> 00:08:00,410
So again, inactivation record is created for zero and this ends and this is deleted.

120
00:08:01,280 --> 00:08:02,890
Now control goes back to this.

121
00:08:03,020 --> 00:08:04,730
This is finished, goes back to this.

122
00:08:04,970 --> 00:08:08,900
All three steps are finished, then goes back to one of three.

123
00:08:09,110 --> 00:08:12,570
In this, it has to perform one more step.

124
00:08:12,580 --> 00:08:15,190
That is one of this was two.

125
00:08:15,200 --> 00:08:16,340
So this is also two.

126
00:08:16,580 --> 00:08:19,340
So three minus one, three minus one.

127
00:08:19,880 --> 00:08:22,040
So again, two calls are made.

128
00:08:23,030 --> 00:08:26,150
So already this portion, we have seen it completely.

129
00:08:26,450 --> 00:08:29,120
Same thing happens here also.

130
00:08:30,400 --> 00:08:37,480
He also seemed happens now this I have taken enough space for explaining this one, now this, I have

131
00:08:37,480 --> 00:08:41,970
to write it in small size, but it will happen as it is what has happened here.

132
00:08:42,370 --> 00:08:43,919
So let me finish it quickly.

133
00:08:44,260 --> 00:08:48,900
So the first thing happened is value to spend it, to spend it.

134
00:08:49,210 --> 00:08:51,790
And here activation record has created.

135
00:08:53,850 --> 00:08:59,820
Then call for the of one new activation record for this was gone.

136
00:09:00,450 --> 00:09:11,310
So one is created then for this value, one is printed value one is printed, then called for fun of

137
00:09:11,310 --> 00:09:18,030
Siedel and the sense activation of the card one more is created with value zero and this ends.

138
00:09:18,510 --> 00:09:23,960
And again, a call for fan of zero again and zero.

139
00:09:24,180 --> 00:09:25,680
And this also ends.

140
00:09:25,690 --> 00:09:32,490
This also goes back here fond of one for this one is printed.

141
00:09:33,030 --> 00:09:34,470
So output is one.

142
00:09:36,430 --> 00:09:40,690
Then Khilafah fan of zero call for phone of zero, right?

143
00:09:41,080 --> 00:09:45,260
So, again, two more activation records than double to finish the finish.

144
00:09:45,550 --> 00:09:47,710
And finally, also finish up.

145
00:09:47,950 --> 00:09:49,810
This is the output that we get.

146
00:09:51,100 --> 00:09:55,480
So I have simultaneously shown you how the activation record works.

147
00:09:55,840 --> 00:10:01,030
So you can see that maximum size of the stack was how much?

148
00:10:01,750 --> 00:10:03,580
One, two, three, four.

149
00:10:03,580 --> 00:10:04,810
Maximum was four.

150
00:10:04,810 --> 00:10:05,920
One, two, three, four.

151
00:10:06,250 --> 00:10:08,890
And total, how many activation of records are created?

152
00:10:09,100 --> 00:10:15,460
And many calls out there, like, I did not draw all just I have given the idea I did not draw each

153
00:10:15,460 --> 00:10:16,330
and every call here.

154
00:10:16,660 --> 00:10:23,290
So if I count them one, two, three, four, five, six, seven, eight, nine, 10, 11, 12, 13,

155
00:10:23,290 --> 00:10:24,070
14, 15.

156
00:10:24,280 --> 00:10:25,950
So total 15 calls are there.

157
00:10:26,140 --> 00:10:29,620
So 15 times activation records are created and deleted.

158
00:10:30,580 --> 00:10:34,210
Nine will remove the stack and redraw this tree once again.

159
00:10:34,390 --> 00:10:36,870
And we will analyze more on the tree.

160
00:10:37,540 --> 00:10:39,430
I have read on the tree once again.

161
00:10:39,790 --> 00:10:45,880
Let us analyze the tree so by which we can analyze the working of this recursive function.

162
00:10:46,510 --> 00:10:47,690
So here is the tree.

163
00:10:48,820 --> 00:10:53,290
Now, first of all, let me label the calls in which order they are.

164
00:10:53,290 --> 00:11:00,190
Karda see the force function call as this one one, then second call.

165
00:11:00,700 --> 00:11:03,050
This will be done once this has finished.

166
00:11:03,370 --> 00:11:04,380
It has to finish.

167
00:11:04,390 --> 00:11:06,130
So I know that there are three things.

168
00:11:06,130 --> 00:11:08,460
Again, the second call printing is done.

169
00:11:08,710 --> 00:11:09,790
Then third called.

170
00:11:11,450 --> 00:11:13,670
Then this one will be done once this has finished.

171
00:11:13,840 --> 00:11:17,850
So another sprinting, then this gone forward, gone.

172
00:11:18,200 --> 00:11:22,010
Now this is fun with the value zero, this will terminate.

173
00:11:22,430 --> 00:11:25,460
Then it will go to Discon.

174
00:11:25,670 --> 00:11:28,010
So this is fifth one, fifth call.

175
00:11:28,490 --> 00:11:32,780
Then as this has finished, it will go back and then it will make this call.

176
00:11:33,170 --> 00:11:37,250
So this is sixth con then under this seven and eight.

177
00:11:37,700 --> 00:11:39,660
Now these are also finish.

178
00:11:39,920 --> 00:11:45,120
So under this, if you see all the calls are completed now, it will go to this one.

179
00:11:45,230 --> 00:11:46,280
So this is the ninth.

180
00:11:48,080 --> 00:11:51,290
So that is the ninth call, ninth activation.

181
00:11:51,290 --> 00:11:54,500
The code to be created in the stack now, Heidi, is how much?

182
00:11:54,500 --> 00:11:55,140
One, two.

183
00:11:55,310 --> 00:11:57,350
So only two will be there in the stack.

184
00:11:57,770 --> 00:12:00,640
But the count, if you take this, the ninth one.

185
00:12:00,650 --> 00:12:04,790
So there's a ninth call, the end of the sprinting is done and this call is made.

186
00:12:04,970 --> 00:12:10,400
So this is the tenth call and then printing is done and this is mid level call.

187
00:12:10,640 --> 00:12:16,390
And this will finish now because value of this is zero, then this call to call.

188
00:12:16,790 --> 00:12:19,180
So this is also finished because the value of zero.

189
00:12:19,400 --> 00:12:20,870
So this will also complete.

190
00:12:21,050 --> 00:12:22,550
All three steps are completed.

191
00:12:22,550 --> 00:12:30,920
And this one, the world can then go back for this one right side, 13th gone, then 14 call and 15.

192
00:12:32,540 --> 00:12:34,840
So that's what all of you have counted them.

193
00:12:35,090 --> 00:12:42,670
So total 15 calls are made in this particular function when we pass the value as a three.

194
00:12:44,090 --> 00:12:46,800
So I have shown you the order in which the calls are made.

195
00:12:46,820 --> 00:12:54,920
This is very important for you to visualize or trace how these type of functions work, which are calling

196
00:12:54,920 --> 00:12:56,180
themselves more than one time.

197
00:12:57,020 --> 00:12:59,210
Now we will analyze the time, complexity.

198
00:12:59,210 --> 00:13:02,050
So time complexity depends on the number of calls.

199
00:13:02,090 --> 00:13:02,570
Yes.

200
00:13:02,840 --> 00:13:06,970
In each call it is printing except in the calls where the value is zero.

201
00:13:07,160 --> 00:13:11,660
So otherwise it is remaining almost for every call that.

202
00:13:12,740 --> 00:13:15,740
Now let us see how many calls are made.

203
00:13:15,740 --> 00:13:20,240
If the value is three so far and how many calls will be made with that.

204
00:13:20,240 --> 00:13:22,500
We can decide the time complexity of this one.

205
00:13:22,640 --> 00:13:24,120
So let us analyze this one.

206
00:13:24,470 --> 00:13:32,470
So if I count the number of calls level twice then and this level, just one call is made.

207
00:13:33,680 --> 00:13:41,270
And in this level, if I see two calls are made to and in this level, one, two, three, four calls

208
00:13:41,270 --> 00:13:42,710
are made for.

209
00:13:43,250 --> 00:13:48,680
And this level, one, two, three, four, five, six, seven, eight, eight calls are made.

210
00:13:49,640 --> 00:13:55,400
So it means when they have passed the value as a three, then in the first level, one call, next level

211
00:13:55,400 --> 00:14:00,250
to call, next level for the next eight, four, three, there are four levels.

212
00:14:00,770 --> 00:14:09,260
And if the value of PN that I am passing here, this value n if it is a four then there will be five

213
00:14:09,260 --> 00:14:09,950
levels.

214
00:14:09,980 --> 00:14:15,410
It means the three will be more bigger than this one and how many calls it may be having.

215
00:14:16,370 --> 00:14:17,850
So let us analyze this one.

216
00:14:17,870 --> 00:14:20,910
So for any value, often how many calls it will be.

217
00:14:21,230 --> 00:14:29,370
So let us sum up all this so I will take the sum of all those one plus two plus four plus eight.

218
00:14:29,870 --> 00:14:31,470
This is total 15.

219
00:14:32,060 --> 00:14:33,680
Yes, we know.

220
00:14:33,680 --> 00:14:35,090
Discount all of them.

221
00:14:35,100 --> 00:14:35,780
That is 15.

222
00:14:35,780 --> 00:14:38,010
Or you add all these, you get 15.

223
00:14:38,360 --> 00:14:40,760
So this is nothing but dupa zero plus two.

224
00:14:40,760 --> 00:14:42,020
Bar one plus two.

225
00:14:42,030 --> 00:14:44,540
Bovver two plus two, three.

226
00:14:46,160 --> 00:14:55,430
So this is nothing but some of the terms of that, the summation of terms of cities, that is CBDs.

227
00:14:55,850 --> 00:14:58,910
That is some of geometric progression.

228
00:14:58,910 --> 00:15:02,080
Tome's what is all the adjectives and all.

229
00:15:02,120 --> 00:15:05,970
We will learn some other time, but right now I'll just ride on the formula.

230
00:15:06,290 --> 00:15:09,590
So this will be nothing but two power.

231
00:15:10,040 --> 00:15:11,360
Hairston was three.

232
00:15:11,360 --> 00:15:13,660
So this is three plus one minus one.

233
00:15:14,750 --> 00:15:21,500
So it means if I have dupa zero plus two, power one plus two, power two goes on two.

234
00:15:21,770 --> 00:15:27,010
If this is then then this will go tail to power and then how much that is.

235
00:15:27,380 --> 00:15:30,620
This is dupa and plus one.

236
00:15:30,620 --> 00:15:31,510
Minus one.

237
00:15:32,600 --> 00:15:34,370
So that many calls it will make.

238
00:15:35,030 --> 00:15:36,660
So what is that equal do.

239
00:15:36,860 --> 00:15:40,730
So this is Order of Tuborg and.

240
00:15:42,840 --> 00:15:46,240
So total dupa and calls are made.

241
00:15:46,560 --> 00:15:52,800
So if I substitute the value of three here, so two or three plus one for to perform minus one, that

242
00:15:52,800 --> 00:15:54,340
is sixteen, minus one is 15.

243
00:15:54,750 --> 00:16:00,330
So for any value of N, the number of calls will be outdraw dupa.

244
00:16:00,340 --> 00:16:04,080
And so that's all we have seen.

245
00:16:04,080 --> 00:16:05,280
What is truly recursion.

246
00:16:05,280 --> 00:16:10,830
And we have taken one example and we have seen how the treating tree is generated and how the calls

247
00:16:10,830 --> 00:16:14,700
are made, how the stock is used and what is the time complexity.

248
00:16:14,970 --> 00:16:17,110
So for this example function, it was too.

249
00:16:17,430 --> 00:16:20,850
And so it depends on the function, what time complexity may be getting.

250
00:16:20,850 --> 00:16:25,760
It's not that every tree recursion will have to Boran for this function is too boring.

251
00:16:26,190 --> 00:16:27,210
So the time is.

252
00:16:29,310 --> 00:16:36,280
Order of dupa and then what is the space, what is the space complexity?

253
00:16:36,630 --> 00:16:44,520
So it depends on what is the maximum height of the stack, because total activity of the course depends

254
00:16:44,520 --> 00:16:49,700
on a number of calls, but how much space it was occupying inside the stack.

255
00:16:49,980 --> 00:16:54,900
So you have observed that same space was reduced inside the stack.

256
00:16:55,260 --> 00:16:59,920
One activation on the court was going in the same place, another activation record was created.

257
00:17:00,330 --> 00:17:07,050
So we don't require much space, just like Boran, but it required the space equal to the height of

258
00:17:07,050 --> 00:17:08,510
a tree that we got here.

259
00:17:08,730 --> 00:17:10,609
So one, two, three, four.

260
00:17:11,040 --> 00:17:12,760
If it is three, four is the space.

261
00:17:12,780 --> 00:17:20,190
So if it is an endless fun little bit of space or this order of any space complexities of and so maximum

262
00:17:20,190 --> 00:17:24,010
stack size that we need, and that will be sufficient.

263
00:17:24,720 --> 00:17:26,730
So that's all about prediction.

