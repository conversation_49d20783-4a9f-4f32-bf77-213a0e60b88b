1
00:00:00,540 --> 00:00:06,060
Here already I have an area of five five that is created in heap and pointers pointing to that.

2
00:00:06,840 --> 00:00:09,490
So this is point to be and this is in style.

3
00:00:09,510 --> 00:00:11,260
This is in a heap we know very well now.

4
00:00:12,270 --> 00:00:17,790
So this is pointing then I suppose all of it is having some elements that size of five is not sufficient

5
00:00:17,790 --> 00:00:18,300
for me.

6
00:00:18,600 --> 00:00:27,960
I want a larger size urte than one method for increasing the size is take one more point, and that

7
00:00:28,260 --> 00:00:35,030
is called Ice Cube and create a new array of required larger size.

8
00:00:35,040 --> 00:00:42,660
Let us say I want an area of size ten, then point out you will be pointing up on an array with the

9
00:00:42,930 --> 00:00:45,840
larger size zero.

10
00:00:45,840 --> 00:00:46,720
One, two, three.

11
00:00:46,740 --> 00:00:50,700
So on this cue is pointing on the largest Hazari.

12
00:00:52,000 --> 00:00:58,770
So I cannot increase the size of the seam every other day, setback, so alternative, could a bigger

13
00:00:58,780 --> 00:01:03,250
factory not transfer all those elements from B to Q?

14
00:01:03,520 --> 00:01:06,230
So we know the size of that side is fine.

15
00:01:06,520 --> 00:01:09,640
So far I find zero.

16
00:01:10,000 --> 00:01:19,180
I is less than five I plus plus then in the queue of my computer elements of the line.

17
00:01:20,680 --> 00:01:25,240
So I'm copying them using Falu so all these values will be copied again.

18
00:01:25,240 --> 00:01:27,430
Five eight nine six four four.

19
00:01:27,430 --> 00:01:33,370
The values are copied there and even there is one function in C language that is called as a meme copy.

20
00:01:33,370 --> 00:01:34,420
That is memory copy.

21
00:01:34,720 --> 00:01:39,070
You can use that and mention these stories so those contents will be copied here.

22
00:01:40,180 --> 00:01:44,170
So using for loop, I'm showing them next.

23
00:01:44,740 --> 00:01:50,490
We want to increase the size of this be actually be the pointer piece pointing once and so Ariano.

24
00:01:50,950 --> 00:01:57,400
Now you have to make this point on this one, but before making any point on that one, let us delete

25
00:01:57,400 --> 00:01:57,600
that.

26
00:01:58,240 --> 00:02:01,210
So here say delete be.

27
00:02:02,990 --> 00:02:10,280
As I said, if you don't delete a news memory, then it will cause memory leak means the shortage of

28
00:02:10,280 --> 00:02:12,370
memory reduction in the size of the memory.

29
00:02:12,680 --> 00:02:14,180
So it is unused news now.

30
00:02:14,190 --> 00:02:15,340
We don't need not.

31
00:02:15,380 --> 00:02:16,640
So delete that one.

32
00:02:16,940 --> 00:02:21,520
So this memory is deleted then make a B point on cue.

33
00:02:22,430 --> 00:02:26,600
So wherever cue is pointing, B will also point to then.

34
00:02:26,600 --> 00:02:29,470
So no new location where these findings.

35
00:02:29,480 --> 00:02:29,960
This one.

36
00:02:31,620 --> 00:02:36,630
Then now this array is appointed by board to be as well as secure.

37
00:02:36,930 --> 00:02:39,120
Now let us remove a cue from their.

38
00:02:41,720 --> 00:02:43,280
Do assign another.

39
00:02:45,340 --> 00:02:47,990
So, Tuval, no more point on that one.

40
00:02:48,010 --> 00:02:48,710
This is none.

41
00:02:49,030 --> 00:02:50,920
So this is not pointing on this one.

42
00:02:51,110 --> 00:02:52,790
It's pointing on this be.

43
00:02:53,230 --> 00:02:53,470
No.

44
00:02:53,560 --> 00:02:56,190
If I say be, this is Lorino.

45
00:02:56,740 --> 00:02:57,910
That it is gone.

46
00:02:57,940 --> 00:02:58,770
It is deleted.

47
00:02:59,140 --> 00:03:03,340
So it is removed from the memory and B array sizes, not 10.

48
00:03:04,600 --> 00:03:06,410
Now let us take the example like this.

49
00:03:06,430 --> 00:03:08,320
Like the P was pointing on the sorry.

50
00:03:08,320 --> 00:03:11,080
So P is having car now.

51
00:03:11,440 --> 00:03:15,130
B is having a small car knowing that God is not sufficient.

52
00:03:15,370 --> 00:03:19,680
B needs a bigger safe car, so B produces a new car.

53
00:03:19,930 --> 00:03:22,750
So Q help in purchasing a new car.

54
00:03:23,020 --> 00:03:25,060
Not B was having motor cars at that time.

55
00:03:25,330 --> 00:03:32,380
Then all these things are transferred in a new car and that is sold out now because owning this car.

56
00:03:33,770 --> 00:03:41,940
So for shifting from one car to another, car has helped because he has to sell that car also, or else

57
00:03:41,940 --> 00:03:42,980
you can take the example.

58
00:03:43,010 --> 00:03:47,630
This room is not sufficient for conducting a class and the students are growing in number.

59
00:03:47,660 --> 00:03:51,320
We need a bigger room so we cannot increase the size of the current room.

60
00:03:51,500 --> 00:03:54,710
So we have to shift it to a new, bigger room.

61
00:03:54,710 --> 00:04:00,320
So there's a new, bigger room and all the students from that class are moved to this class.

62
00:04:00,590 --> 00:04:03,830
And there is a lot of respect for more students to sit in the class.

63
00:04:04,730 --> 00:04:08,480
So that's the idea I followed for increasing the size of a fanatic.

64
00:04:08,840 --> 00:04:15,980
So if you are not doing it, if some API is doing it inside programming, then definitely it is following

65
00:04:15,980 --> 00:04:19,130
this method because the same size, but it cannot be grown.

66
00:04:19,760 --> 00:04:26,650
Not tell you the reason why the size cannot be grown, because the memory for the area should be contiguous.

67
00:04:27,170 --> 00:04:32,510
For example, the beginning and resolve this is five hundred and find not one.

68
00:04:32,750 --> 00:04:37,160
Then there should be two, three, four, five, six, seven, eight, nine.

69
00:04:37,360 --> 00:04:39,950
Then the next location must be ten, eleven.

70
00:04:40,430 --> 00:04:46,670
Now we don't know whether that location is freely available or not, or it is used by some other object

71
00:04:46,670 --> 00:04:47,890
inside the same program.

72
00:04:48,110 --> 00:04:54,410
So there is no guarantee that the next consecutive locations are free or not if the consecutive locations

73
00:04:54,410 --> 00:04:57,260
are not free, but it cannot be increase.

74
00:04:57,470 --> 00:05:00,700
That is the reason we say that a device cannot be increased.

75
00:05:01,040 --> 00:05:04,940
So rather than that, we will have another larger area and shift elements.

76
00:05:06,470 --> 00:05:09,130
So this is about static and dynamic.

