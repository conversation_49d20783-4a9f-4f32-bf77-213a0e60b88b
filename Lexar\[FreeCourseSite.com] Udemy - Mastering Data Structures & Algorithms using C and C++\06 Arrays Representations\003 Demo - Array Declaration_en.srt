1
00:00:03,440 --> 00:00:09,170
In this video, we will look at the demonstration of declaration and initialization of.

2
00:00:10,000 --> 00:00:14,690
We have already seen in previous video there are various methods of declaration and initial addition.

3
00:00:15,200 --> 00:00:16,600
So we'll try all of them here.

4
00:00:17,580 --> 00:00:23,610
First method, a clearer idea of some size, I have declared an area of size of five and I'm not initializing

5
00:00:23,610 --> 00:00:23,770
it.

6
00:00:24,510 --> 00:00:29,550
Next, I will declare an area of some size five and also I will initialize the values.

7
00:00:31,220 --> 00:00:39,530
I have an five values terminated, I will declare an array of size again, but I will initialize only

8
00:00:39,540 --> 00:00:40,340
few values.

9
00:00:42,930 --> 00:00:47,960
Average lives, only three remaining seven values will be made as Zeitels.

10
00:00:49,920 --> 00:00:52,110
The next I will declare an early.

11
00:00:53,620 --> 00:01:00,430
Of size five, and I will assign only evil, and as the first element is zero, the rest of the elements

12
00:01:00,430 --> 00:01:01,840
will also be filled with zeros.

13
00:01:03,530 --> 00:01:09,170
Then one more method I will not mention any size, I will write on the elements.

14
00:01:14,320 --> 00:01:15,880
I have given six elements.

15
00:01:19,870 --> 00:01:22,000
Now, here, right, return zero.

16
00:01:25,650 --> 00:01:26,970
I'll put a bookmark.

17
00:01:31,690 --> 00:01:36,520
See, I'm getting here warnings that all these variables, I'm not using them anywhere in the program,

18
00:01:37,060 --> 00:01:41,320
so if the variables are not used, then Compiler is giving you a warning.

19
00:01:41,680 --> 00:01:45,590
So the warnings are instant and the errors are also you have observed their intent.

20
00:01:45,610 --> 00:01:50,170
So that is the benefit of using this Ed, this Xcode Idy.

21
00:01:51,250 --> 00:01:54,790
I will run the program and show you how these areas are created in the memory.

22
00:01:55,330 --> 00:02:00,670
I'll put a breakpoint here, then I will run the program.

23
00:02:05,880 --> 00:02:08,530
You can see in the area that is in the watch.

24
00:02:10,090 --> 00:02:16,570
All the variables are shown here, a is of size five, and all these values are zero and the last value

25
00:02:16,570 --> 00:02:17,630
is some garbage.

26
00:02:17,650 --> 00:02:19,740
So actually these zeros are also garbage.

27
00:02:20,020 --> 00:02:21,130
It can be any number.

28
00:02:22,060 --> 00:02:25,400
Our next hour, if you look at it, is filled with values, one, two, five.

29
00:02:25,870 --> 00:02:28,680
So here we have initialize array with one to five.

30
00:02:29,440 --> 00:02:30,250
I'll close them.

31
00:02:33,600 --> 00:02:38,400
Now, RLC, I have taken an array of Siza 10, but I have initialise only three numbers.

32
00:02:38,760 --> 00:02:41,130
Rest of the numbers will be Zeitels.

33
00:02:41,160 --> 00:02:42,970
Automatically they are initialized with zero.

34
00:02:42,990 --> 00:02:47,700
They are not garbage and the de la initialised.

35
00:02:48,120 --> 00:02:49,920
There is no garbage value here.

36
00:02:49,930 --> 00:02:51,330
These are the values that we have.

37
00:02:51,330 --> 00:02:52,170
Initialize them.

38
00:02:53,470 --> 00:02:59,950
And you can see I have not mentioned the size, but I have given six numbers here, so an area of size

39
00:02:59,950 --> 00:03:00,850
six is created.

40
00:03:01,180 --> 00:03:04,930
It is showing an area of size here inside this bracket.

41
00:03:05,260 --> 00:03:07,720
And there are six elements, zero through five.

42
00:03:09,640 --> 00:03:10,540
I'll stop this.

43
00:03:11,900 --> 00:03:17,600
I will remove these declarations and I will show you how the addresses of relocations are contagious.

44
00:03:19,100 --> 00:03:23,530
I will bring the addresses of every so far that I need to follow.

45
00:03:23,990 --> 00:03:25,750
So using follow.

46
00:03:33,490 --> 00:03:41,620
Here are the addresses of all the locations so far, printing addresses I should use W and space.

47
00:03:45,090 --> 00:03:45,840
New line.

48
00:03:48,110 --> 00:03:51,430
Then I will bring the address of AOF I.

49
00:03:53,910 --> 00:03:58,440
Ampersand will bring the address for printing addresses I should use you.

50
00:04:00,570 --> 00:04:01,980
I'll remove the breakpoint.

51
00:04:03,500 --> 00:04:04,880
Now, let us run the program.

52
00:04:07,630 --> 00:04:12,250
See, these are the addresses of all the locations, there are total five locations or first address

53
00:04:12,250 --> 00:04:19,570
that is first integer addresses, 56 and hearing digits taking four bytes or plus four to 60, the next

54
00:04:19,570 --> 00:04:24,200
plus four to 64, the next plus four to 68, the next plus four to 72.

55
00:04:24,760 --> 00:04:26,830
So all these addresses are contiguous.

56
00:04:27,280 --> 00:04:33,010
All the location that is a zero to a for the locations are side by side and.

57
00:04:34,220 --> 00:04:39,670
In this compiler, indigenous taking four bites, but Evernham discussing on a whiteboard.

58
00:04:39,680 --> 00:04:42,170
I'm assuming that indigenous taking two bites.

59
00:04:42,170 --> 00:04:44,870
So that is easy for me for explaining the things.

60
00:04:48,250 --> 00:04:53,470
So that's all in the video we have seen how we can declare an initialise Edy's.

