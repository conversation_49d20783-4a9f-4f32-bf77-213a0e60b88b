1
00:00:00,480 --> 00:00:06,170
Here is an student exercise similar to <PERSON><PERSON><PERSON>'s matching the same problem, a little bit extended.

2
00:00:06,480 --> 00:00:11,010
So let us and let me explain you what the problem is.

3
00:00:11,490 --> 00:00:16,410
See, this expression is parenthesized, but these are not just parenthesis brackets.

4
00:00:16,560 --> 00:00:24,240
They're so different types of brackets are the slower brackets or they are called as <PERSON><PERSON>'s round brackets.

5
00:00:24,810 --> 00:00:29,130
They're also called parentheses and square brackets.

6
00:00:29,130 --> 00:00:30,790
So different types of brackets are there.

7
00:00:30,930 --> 00:00:36,080
So the problem is we have to check whether the brackets given are in balance.

8
00:00:36,900 --> 00:00:38,260
So let us check this one.

9
00:00:38,310 --> 00:00:41,350
So this problem can also be solved using stack.

10
00:00:41,440 --> 00:00:46,670
So let me take a step back and trace this and show you how it should work here.

11
00:00:46,800 --> 00:00:49,610
The stack let us control the expression.

12
00:00:50,070 --> 00:00:58,920
First one is slower bracket braces included, opening bracket included opening square record included

13
00:00:59,310 --> 00:01:04,069
a skip plus skip, b skip closing bracket.

14
00:01:04,230 --> 00:01:10,200
So for this closing bracket, when I propose something from the stack, it must be a match for that

15
00:01:10,470 --> 00:01:11,760
square bracket only.

16
00:01:11,970 --> 00:01:13,740
So yes, it's the opening bracket.

17
00:01:13,740 --> 00:01:16,220
Perfect square opening bracket six.

18
00:01:16,230 --> 00:01:16,650
Perfect.

19
00:01:17,640 --> 00:01:18,960
Then don't do anything.

20
00:01:18,960 --> 00:01:21,630
Just continue asterisk.

21
00:01:21,690 --> 00:01:22,350
Ignore it.

22
00:01:22,980 --> 00:01:30,390
Opening a square bracket, push it into the stack c ignore minus ignore the square bracket.

23
00:01:30,480 --> 00:01:35,000
So so what we get from the stamp is the opening close brackets.

24
00:01:35,000 --> 00:01:36,990
So yes it is matching matches form.

25
00:01:37,200 --> 00:01:39,600
So continue then closing brackets.

26
00:01:39,600 --> 00:01:43,980
So for disclosing bracket support from the stack we got this opening bracket.

27
00:01:43,990 --> 00:01:45,870
So yes it is match for this fun.

28
00:01:46,230 --> 00:01:46,830
Perfect.

29
00:01:48,290 --> 00:01:54,440
Then just ignore and continue next, ignore the symbol, ignore the symbol, then this is the closing

30
00:01:54,440 --> 00:01:57,310
record purpose symbol from the strike.

31
00:01:57,530 --> 00:01:58,840
So it's a flawed record.

32
00:01:58,850 --> 00:02:01,460
So it's the opening record for this song is smashing.

33
00:02:02,060 --> 00:02:05,710
Everything is matching perfectly at the end, stack is empty.

34
00:02:05,720 --> 00:02:07,970
So it means the brackets are balanced.

35
00:02:09,039 --> 00:02:15,310
Let us see if they are not balanced, then what happens, I'll change the symbols, I'll change the

36
00:02:15,310 --> 00:02:20,920
expression, let us trace this one and see how it works and what problem arises.

37
00:02:22,160 --> 00:02:28,540
Opening bracket, Bush opening bracket, pushing, opening brackets, square bracket, pushing, ignore,

38
00:02:28,550 --> 00:02:31,540
plus ignore, we ignore closing brackets.

39
00:02:31,540 --> 00:02:33,400
So for this closing bracket, pop out.

40
00:02:33,530 --> 00:02:35,100
So what is the opening bracket?

41
00:02:35,120 --> 00:02:36,880
We are getting a square bracket.

42
00:02:36,900 --> 00:02:37,800
It's not matching.

43
00:02:38,240 --> 00:02:40,250
It should be a square closing bracket.

44
00:02:40,250 --> 00:02:44,930
But actually this round bracket and what we got from the strike is square brackets.

45
00:02:44,960 --> 00:02:45,820
It's not matching.

46
00:02:46,130 --> 00:02:48,000
So that symbol should also match.

47
00:02:48,320 --> 00:02:52,610
This is not just having one type of bracket that is round brackets or parentheses.

48
00:02:52,860 --> 00:02:54,590
It's having different type of brackets.

49
00:02:54,830 --> 00:02:57,920
So you also have to check whether the brackets are matching or not.

50
00:02:58,160 --> 00:03:02,990
So what you have to do in the procedure, let me explain a little bit what are the things that we have

51
00:03:02,990 --> 00:03:07,560
to focus on, not explain you how this different from the previous procedure.

52
00:03:07,760 --> 00:03:13,550
So what is the extra thing that you have to take care for writing a program for this fun little C?

53
00:03:14,840 --> 00:03:21,470
This is a strain and the character type stack and we have to scan for this expression by taking one

54
00:03:21,470 --> 00:03:23,630
symbol at a time for symbol.

55
00:03:24,110 --> 00:03:28,290
Now, one of the things I have to check is that the flower market, is it around record?

56
00:03:28,310 --> 00:03:29,430
Is it a square record?

57
00:03:30,020 --> 00:03:36,110
Earlier we were just checking if it was the opening bracket, just one parenthesis opening bracket around

58
00:03:36,110 --> 00:03:36,470
bracket.

59
00:03:36,830 --> 00:03:38,210
But three things you have to check.

60
00:03:38,630 --> 00:03:42,830
If it is any of these three, then push it into the stack.

61
00:03:42,950 --> 00:03:49,140
Just flawed bracket, push it down or push it square bracket or push it then.

62
00:03:49,160 --> 00:03:54,420
Similarly, I should also check whether it is a closing bracket of lower bracket or square back door

63
00:03:54,440 --> 00:03:54,970
on record.

64
00:03:55,460 --> 00:03:58,400
So instead of checking just one symbol, I have to check through symbols.

65
00:03:58,790 --> 00:04:01,010
If it is any of those three, push it.

66
00:04:02,090 --> 00:04:08,890
If it is any of these three that is closing bracket or on bracket or the slope, then pop up about.

67
00:04:10,140 --> 00:04:17,339
So extra conditions you have to ride on, so I'll just give you the sample, if expression of eye is

68
00:04:17,339 --> 00:04:28,590
equal to flower market or expression of eye is equal to a round bracket and that the square bracket

69
00:04:29,040 --> 00:04:37,500
push it into the stock as an expression of I so have not written all three conditions.

70
00:04:37,500 --> 00:04:44,470
So I give you the idea, this part or this one or next one floor bracket around bracket or square bracket.

71
00:04:44,490 --> 00:04:47,630
Also, you have to write down if these are matching, push it.

72
00:04:48,920 --> 00:04:49,600
OTHERLY.

73
00:04:51,360 --> 00:04:58,260
If it is a closing bracket or closing bracket or square bracket, then I should pop out and take it

74
00:04:58,260 --> 00:05:00,440
in some variable than this.

75
00:05:00,450 --> 00:05:07,770
You have to check whether it is exactly matching with the type of bracket or not so that you have to

76
00:05:07,770 --> 00:05:08,160
write on.

77
00:05:08,160 --> 00:05:09,810
That is the extra work you have to do.

78
00:05:11,100 --> 00:05:16,890
So it's a student exercise to do it, not one hint I can give you because you have to check so many

79
00:05:16,890 --> 00:05:23,220
things that for the slower bracket, closing record should be flower opening back for around closing

80
00:05:23,220 --> 00:05:25,020
brackets should be round opening brackets.

81
00:05:25,020 --> 00:05:26,340
So three things we have to check.

82
00:05:26,760 --> 00:05:28,920
I think you can make a little symbol.

83
00:05:28,920 --> 00:05:32,120
If you know this hint, I'm giving you a hint.

84
00:05:32,790 --> 00:05:40,260
So the Henders I have the ASCII codes, ASCII code for those brackets, see for the brackets as code

85
00:05:40,260 --> 00:05:41,330
is 40 and 41.

86
00:05:41,910 --> 00:05:43,410
So these codes are below 50.

87
00:05:44,530 --> 00:05:51,900
Then four square brackets, the codes are 91 in ninety three and four because it's 123 and 125.

88
00:05:52,930 --> 00:05:57,010
So if the codes are less than 40, the difference is of one 40 and 41.

89
00:05:57,520 --> 00:06:03,090
If they are greater than 90, then the difference is off to ninety one in ninety three, one, two,

90
00:06:03,260 --> 00:06:04,300
three and one twenty five.

91
00:06:04,310 --> 00:06:05,440
There's a difference of two.

92
00:06:07,940 --> 00:06:14,450
So by using this, if you can reduce the code, the size of the code for you don't have to write so

93
00:06:14,450 --> 00:06:20,960
many examples by using this, you can write some tricky code to reduce the number of conditional statements

94
00:06:21,380 --> 00:06:23,750
anyway if you're writing many conditional statements.

95
00:06:23,780 --> 00:06:25,070
Also, no problem.

96
00:06:25,100 --> 00:06:26,450
Write the program for this one.

