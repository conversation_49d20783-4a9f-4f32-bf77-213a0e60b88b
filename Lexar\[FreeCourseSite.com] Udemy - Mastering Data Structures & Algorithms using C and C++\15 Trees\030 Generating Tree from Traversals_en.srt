1
00:00:00,480 --> 00:00:04,350
So the topic is a generating three from <PERSON><PERSON>.

2
00:00:05,280 --> 00:00:11,250
So already we have learned that any two travel are compulsory out of that one of their drivers must

3
00:00:11,250 --> 00:00:12,600
be in order.

4
00:00:13,020 --> 00:00:20,160
So I have a pre order and in order and one more possible is forced order and in order to let us learn

5
00:00:20,160 --> 00:00:23,090
how to generate free from preordering in order.

6
00:00:23,970 --> 00:00:25,370
So I'll show you the procedure.

7
00:00:26,730 --> 00:00:27,690
Create a..

8
00:00:30,670 --> 00:00:38,200
Take all the elements off in order to in order as it is in the north, don't change all the right elements

9
00:00:38,200 --> 00:00:45,340
in the same order seven, six, nine, three, four, five, eight to one.

10
00:00:46,930 --> 00:00:49,960
As it is, I have taken this is initial work.

11
00:00:51,220 --> 00:00:59,560
Now, repeating procedure means the steps will be repeating what to do, scan through pre order, scan

12
00:00:59,560 --> 00:01:04,180
through preorder by taking one element at a time and then what to do with each element.

13
00:01:04,190 --> 00:01:04,750
Let us see.

14
00:01:04,870 --> 00:01:05,580
First step.

15
00:01:05,590 --> 00:01:08,850
I'm sure you take the first element from left to right.

16
00:01:09,130 --> 00:01:11,970
So first element is for so route.

17
00:01:12,010 --> 00:01:16,270
So you remember what will be the first element in preorder rugosa three.

18
00:01:16,330 --> 00:01:17,920
So Ford is the root of a tree.

19
00:01:18,430 --> 00:01:20,290
So search for four.

20
00:01:20,740 --> 00:01:22,270
Search it is found.

21
00:01:23,230 --> 00:01:27,760
Observe once again, search for four in this list of elements.

22
00:01:27,800 --> 00:01:28,830
Yes, it is found here.

23
00:01:29,230 --> 00:01:32,660
So take four in one note.

24
00:01:33,400 --> 00:01:34,210
This is Ruth.

25
00:01:35,210 --> 00:01:40,940
Then what about the rest of the elements, seven, six, nine, three on the left hand side of four,

26
00:01:42,320 --> 00:01:49,300
seven, six, nine, three on the left hand side, and these elements on the right hand side, five

27
00:01:49,310 --> 00:01:49,900
to one.

28
00:01:49,910 --> 00:01:54,230
So five eight to one on the right hand side.

29
00:01:55,790 --> 00:01:59,940
Yes, we got first to know that this first true north.

30
00:02:00,470 --> 00:02:08,419
If I tell you this trip once again, first Telemann surge, then split, move to the next element seven,

31
00:02:08,660 --> 00:02:13,040
search for seven, then seven when it will be on the left side or right side.

32
00:02:13,340 --> 00:02:16,580
See ask for preorder will be going on left hand side.

33
00:02:16,580 --> 00:02:19,160
If it is not found any longer I'd say so.

34
00:02:19,160 --> 00:02:23,030
Search and left side seven is found, seven is found.

35
00:02:23,270 --> 00:02:28,940
Then split four was already there then left hand side left child seven.

36
00:02:29,750 --> 00:02:30,680
No other elements.

37
00:02:30,740 --> 00:02:31,580
Six, nine, three.

38
00:02:31,580 --> 00:02:33,980
All of them are on the right hand side of seven.

39
00:02:34,220 --> 00:02:38,660
So we'll take them on in a single node on the right hand side of seven.

40
00:02:39,290 --> 00:02:42,890
Despite this as it is for searching and splitting.

41
00:02:43,610 --> 00:02:46,010
No, let me do the rest of the nodes quickly.

42
00:02:47,410 --> 00:02:55,470
Next element named Search, it has found either in this list or this list, this list found split.

43
00:02:56,200 --> 00:03:02,350
So here I will draw four on the side, then seven on the left hand side, then nine is on the right

44
00:03:02,350 --> 00:03:03,400
hand side of seven.

45
00:03:03,790 --> 00:03:05,920
Then what about the remaining element?

46
00:03:05,920 --> 00:03:06,580
Six entry?

47
00:03:06,580 --> 00:03:08,380
They are on the different sides of nine.

48
00:03:08,390 --> 00:03:12,190
So left hand side take six right hand side take.

49
00:03:13,090 --> 00:03:20,320
Three, yes, six on the side, three on this side, this is completed, then the right hand side elements

50
00:03:20,320 --> 00:03:28,610
are, as it is, five eight two one five eight to one, then move to the next element six search.

51
00:03:28,610 --> 00:03:29,560
It is found here.

52
00:03:30,400 --> 00:03:31,660
And that is a single element.

53
00:03:31,660 --> 00:03:33,340
Yes, with one single element only.

54
00:03:33,370 --> 00:03:34,830
So it's perfect.

55
00:03:34,840 --> 00:03:36,550
Don't do anything next.

56
00:03:36,970 --> 00:03:38,510
Three three.

57
00:03:38,510 --> 00:03:39,380
Search for three.

58
00:03:39,380 --> 00:03:41,740
You found here under the single element.

59
00:03:42,160 --> 00:03:43,120
Don't do anything.

60
00:03:44,990 --> 00:03:51,600
Next element to search, it is found here, found here, so there are more than one element suspect.

61
00:03:51,800 --> 00:03:53,020
So I will draw here.

62
00:03:53,510 --> 00:03:54,800
So this site is.

63
00:03:57,080 --> 00:03:58,200
Already completed.

64
00:03:58,320 --> 00:04:05,470
I will take it as it is, and here NeuroPace to do so, five and eight on the left hand side.

65
00:04:07,010 --> 00:04:09,980
Five and eight on the right hand side one.

66
00:04:12,010 --> 00:04:17,589
Its next move on to the next element, that is five, search for five.

67
00:04:17,620 --> 00:04:21,660
It is found here is two elements are there, so split I will draw it here.

68
00:04:22,360 --> 00:04:28,720
So four, so this left hand side is austerities, right hand side two and one as it is.

69
00:04:30,940 --> 00:04:32,830
Then this is what I have to take five.

70
00:04:33,130 --> 00:04:34,030
OK, take five.

71
00:04:35,080 --> 00:04:38,580
What about eight, which should be is on the right hand side.

72
00:04:38,590 --> 00:04:40,520
And then also it will go on the right hand side only.

73
00:04:41,080 --> 00:04:42,880
So this is on the right hand side.

74
00:04:44,200 --> 00:04:47,680
Continue eight search for eight single element.

75
00:04:48,820 --> 00:04:49,780
Don't do anything.

76
00:04:50,060 --> 00:04:53,380
Next one search for one found single element.

77
00:04:54,520 --> 00:04:56,570
That's all this is a tree.

78
00:04:57,980 --> 00:05:02,920
This is a unique tree, just one single tree for this tree.

79
00:05:03,310 --> 00:05:05,900
This is the preorder and this is the in order.

80
00:05:06,400 --> 00:05:09,910
There is no other tree possible which gives same preordering in order.

81
00:05:09,940 --> 00:05:11,280
So there's a unique tree.

82
00:05:11,740 --> 00:05:15,770
So you are able to generate a unique tree for preorder anything that is given.

83
00:05:16,060 --> 00:05:17,260
And this is the procedure.

84
00:05:17,560 --> 00:05:21,190
So this was the first step and the second step we have taken for.

85
00:05:21,610 --> 00:05:27,330
And the third step, we have split at seven because we're moving along from left to right.

86
00:05:27,490 --> 00:05:27,690
Side.

87
00:05:27,700 --> 00:05:29,740
We were scanning preorder from left to right.

88
00:05:31,760 --> 00:05:38,900
Then for the step, we have taken nine, so I automatically got six and three also, so this also completed

89
00:05:39,380 --> 00:05:41,570
the next here we selected two.

90
00:05:41,750 --> 00:05:45,710
This was for the fifth six then this is seventh step, I should say.

91
00:05:46,730 --> 00:05:51,470
So in this a step towards Splitter, then eight to step in.

92
00:05:51,470 --> 00:05:52,770
This five was a splitter.

93
00:05:53,000 --> 00:05:55,190
So even eight was also stated.

94
00:05:56,440 --> 00:06:03,520
We got a treaty, this is a procedure that analysis, what is the time, complexity, how much time

95
00:06:03,520 --> 00:06:04,150
it is taking?

96
00:06:04,480 --> 00:06:06,100
So actually, we have done both work.

97
00:06:06,310 --> 00:06:12,250
We have not done any good or the pseudocode or algorithm just based on the procedure.

98
00:06:12,250 --> 00:06:13,450
Only we have to give the time.

99
00:06:13,480 --> 00:06:14,730
So what was the work done?

100
00:06:15,070 --> 00:06:17,830
We have Scantlebury order all the elements.

101
00:06:17,830 --> 00:06:19,700
We have scanned through them once.

102
00:06:19,720 --> 00:06:20,650
So this is an.

103
00:06:22,080 --> 00:06:27,780
Yes, but while scanning for every element of what we were doing, searching for that element here to

104
00:06:27,780 --> 00:06:32,810
make it as root or make it as the new node, so we were searching for the element every time.

105
00:06:33,090 --> 00:06:35,610
So for every element we were searching.

106
00:06:35,610 --> 00:06:37,880
So searching takes how much time and time.

107
00:06:38,100 --> 00:06:41,190
So how many times we have to for every element we have searched.

108
00:06:41,190 --> 00:06:43,590
So and in doing right.

109
00:06:43,890 --> 00:06:46,300
Searching constant digging and things right.

110
00:06:46,380 --> 00:06:47,780
Search takes and andin.

111
00:06:49,070 --> 00:06:55,100
And how many times we have done for any elements we have done, so there are any elements, so an element

112
00:06:55,110 --> 00:06:56,510
search for anytime.

113
00:06:56,520 --> 00:06:57,800
So this is an square.

114
00:06:58,820 --> 00:07:01,820
So the time taken by this procedure is in square.

115
00:07:04,420 --> 00:07:10,360
So that's all about this procedure, so generating a from private cells is very important.

116
00:07:11,710 --> 00:07:13,540
I will show you the program for this one.

