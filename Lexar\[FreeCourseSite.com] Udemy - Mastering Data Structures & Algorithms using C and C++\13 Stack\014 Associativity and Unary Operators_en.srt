1
00:00:00,630 --> 00:00:07,570
In this video, we learn about associativity and unity operators for two things we will discuss.

2
00:00:08,310 --> 00:00:15,720
What does it mean when associativity and how to handle unary operators while converting infix to perfect

3
00:00:15,720 --> 00:00:20,340
for some of the seawaters, associativity to discuss associativity.

4
00:00:20,340 --> 00:00:25,860
I have taken a few operators here, some of the operators this we have already used them in the previous

5
00:00:25,860 --> 00:00:28,410
example, addition, subtraction, multiplication, division.

6
00:00:28,680 --> 00:00:30,720
And this is a new one hour operation.

7
00:00:31,050 --> 00:00:34,470
And this is unary minus the unity minus.

8
00:00:34,470 --> 00:00:35,320
I'll show you what is it.

9
00:00:35,790 --> 00:00:38,290
So these are the new operators I have introduce.

10
00:00:38,310 --> 00:00:41,970
Now, if you see the presidencies, this is one, then this is two.

11
00:00:41,970 --> 00:00:49,380
So the higher than that is power and higher than this unity minus then highest one is <PERSON><PERSON><PERSON>'s and

12
00:00:49,380 --> 00:00:54,840
associativity, if you see a few of them is having Left-to-right and few of them is having right to

13
00:00:54,840 --> 00:00:55,200
left.

14
00:00:56,130 --> 00:00:58,060
So let us understand what these are.

15
00:00:59,190 --> 00:01:06,570
See, already I said that presidencies and associativity are meant for parenthesized often expression.

16
00:01:06,870 --> 00:01:14,490
If you don't parenthesized it in your program, then compiler will logically parenthesized them based

17
00:01:14,490 --> 00:01:15,640
on these precedences.

18
00:01:15,930 --> 00:01:19,530
Associativity now what is associativity?

19
00:01:19,650 --> 00:01:27,510
Let us see this an expression I have more than one operators having same precedence, plus, plus,

20
00:01:27,510 --> 00:01:28,050
minus.

21
00:01:28,290 --> 00:01:30,780
All of them are having the same precedence.

22
00:01:31,260 --> 00:01:37,800
Then which one we should select first check the associativity left-to-right so you must select them

23
00:01:37,800 --> 00:01:38,800
from left to right.

24
00:01:39,210 --> 00:01:45,810
If this expression is properly parenthesized, then it looks like this first A plus B is put into the

25
00:01:45,810 --> 00:01:48,850
bracket, then second plus is included.

26
00:01:48,870 --> 00:01:53,440
So this whole thing is kept in the bracket, then next to minus is taken.

27
00:01:53,460 --> 00:01:55,110
So this whole thing is kept in the back.

28
00:01:55,740 --> 00:01:57,840
So parenticide is done like this.

29
00:01:57,990 --> 00:01:59,880
So it means Faustus of this.

30
00:01:59,880 --> 00:02:03,170
Plus the second one is this one and the third one is this one.

31
00:02:03,180 --> 00:02:06,180
So we have to take them from left to right.

32
00:02:07,560 --> 00:02:12,600
Now, I'll give you one more example, see this type of statements we can write on in the program that

33
00:02:12,600 --> 00:02:18,840
assigned with the B A sign that C and Cosine would fight so hard, August 1st five is a sign to see

34
00:02:19,170 --> 00:02:25,170
that that five is also assigned to B and also assigned to A, so you can observe that it is working

35
00:02:25,170 --> 00:02:26,110
from right to left.

36
00:02:27,000 --> 00:02:31,290
So there are more than one operators which are having the same precedence.

37
00:02:31,290 --> 00:02:33,620
All that assignment, they are having same precedence.

38
00:02:33,910 --> 00:02:37,780
No doubt it's not there on the table, but we can see that they are all the same.

39
00:02:37,800 --> 00:02:40,980
So the president is equal then, which one you should take first?

40
00:02:41,280 --> 00:02:47,560
So as I just explained, that first value is assigned to see that that values assigned to be working

41
00:02:47,640 --> 00:02:48,450
from right to left.

42
00:02:48,480 --> 00:02:51,070
Yes, it's associativity is right to left.

43
00:02:51,360 --> 00:02:56,800
So if I am parenthesized it, then based on the associated with you have to do because is the same.

44
00:02:57,120 --> 00:03:00,130
So let us see how parenticide is done first.

45
00:03:00,150 --> 00:03:03,800
C assign five is put in the bracket, then this assignment is taken.

46
00:03:03,810 --> 00:03:07,200
So this B this is kept in the bracket, then this assignment.

47
00:03:07,230 --> 00:03:09,990
So this is a this whole thing is kept in the bracket.

48
00:03:10,260 --> 00:03:13,710
So Barondess additions will happen from right to left.

49
00:03:14,220 --> 00:03:21,450
So from these two examples, we can understand that there are two types of associativity that are left

50
00:03:21,450 --> 00:03:24,590
to right, then right to left here.

51
00:03:24,870 --> 00:03:29,230
This is left to right associative and this is a right to left associate.

52
00:03:29,460 --> 00:03:32,850
So if presidents are equal, then we look at associates.

53
00:03:33,090 --> 00:03:36,480
Let me show you the postfix form of first one.

54
00:03:37,020 --> 00:03:38,750
Then also I want to show for second one.

55
00:03:39,060 --> 00:03:40,890
Let us convert this into postfix.

56
00:03:41,430 --> 00:03:45,180
If I convert this into postfix first, this part will get converted.

57
00:03:45,180 --> 00:03:49,320
So this becomes a B plus, then this bracket, next bracket.

58
00:03:49,560 --> 00:03:51,510
So left hand side is only become bigger.

59
00:03:51,520 --> 00:03:58,860
So C then plus goes after C, so C plus then this is complete and this whole bracket is completed from

60
00:03:58,860 --> 00:03:59,420
here to here.

61
00:03:59,790 --> 00:04:06,390
Then this minus minus is upon this hole complete left side and B so left side is already there, then

62
00:04:06,390 --> 00:04:07,690
B then minus.

63
00:04:08,010 --> 00:04:09,280
So this is the postfix form.

64
00:04:10,110 --> 00:04:12,030
Liquidate the sixth form of this one.

65
00:04:12,240 --> 00:04:13,860
So first this has to be converted.

66
00:04:13,860 --> 00:04:21,420
So C five then assignment, then this bracket to have to take so be assigned this complete right hand

67
00:04:21,420 --> 00:04:21,720
side.

68
00:04:21,899 --> 00:04:27,780
So B as it is right hand side as it is, then assignment goes after this, then come to this bracket

69
00:04:27,790 --> 00:04:28,800
outermost bracket.

70
00:04:28,860 --> 00:04:33,110
So because this portion is already completed, so we assign this whole thing.

71
00:04:33,120 --> 00:04:34,340
So this whole thing is already there.

72
00:04:34,500 --> 00:04:38,080
So e then this completing the bracket then assignment.

73
00:04:39,060 --> 00:04:43,190
So this is the sixth form of this expansion and this is the postfix form on this expression.

74
00:04:43,530 --> 00:04:50,100
So postfix is dependent on the politicization and the parentheses are dependent on precedence and associativity.

75
00:04:51,020 --> 00:04:57,590
Let us look at a few more examples up on the right to left associativity operators, I may be using

76
00:04:57,590 --> 00:04:59,820
some operators which are even not there in the table.

77
00:05:00,980 --> 00:05:04,100
So let us see the examples on this example.

78
00:05:04,100 --> 00:05:07,870
APower people would see this one power operator.

79
00:05:08,330 --> 00:05:11,060
There's a famous one, mostly far right associativity.

80
00:05:11,060 --> 00:05:17,960
Most of the actors take this operator, power operator to explain right associativity this symbolists

81
00:05:17,960 --> 00:05:19,160
caps caps.

82
00:05:19,310 --> 00:05:23,450
And so instead of using caps, some authors use a dollar symbol also.

83
00:05:23,930 --> 00:05:25,470
So I'm using caps here.

84
00:05:25,490 --> 00:05:26,340
So this is power.

85
00:05:27,020 --> 00:05:29,810
This is right to left associative.

86
00:05:30,140 --> 00:05:35,000
So it means there are more than one operators in the same expression.

87
00:05:35,000 --> 00:05:40,360
They are having same precedence then how they should be pardoned decides they should be parenthesized

88
00:05:40,380 --> 00:05:40,610
right.

89
00:05:40,610 --> 00:05:40,940
To live.

90
00:05:41,330 --> 00:05:45,610
So if I parenthesized this one, then it looks like this first.

91
00:05:45,710 --> 00:05:51,540
This is in the bracket, then this one is in the bracket and I will convert that into postfix.

92
00:05:51,540 --> 00:05:58,720
So I will write the same form of expression and convert it into postfix, see which one should be converted

93
00:05:58,720 --> 00:05:59,530
into postfix.

94
00:06:00,020 --> 00:06:04,400
So already I have kept in the brackets, but let us without brackets, let us follow it.

95
00:06:04,790 --> 00:06:05,730
How to follow this one.

96
00:06:05,990 --> 00:06:09,900
There are two Povero operators, so which one should be converted into postfix first.

97
00:06:10,160 --> 00:06:10,760
This one.

98
00:06:11,000 --> 00:06:11,540
This one.

99
00:06:12,270 --> 00:06:12,860
OK, Gunvor.

100
00:06:12,880 --> 00:06:13,210
This one.

101
00:06:13,220 --> 00:06:14,840
So this becomes B C power.

102
00:06:15,260 --> 00:06:20,030
Put it in the square brackets then apower as it is now in this power.

103
00:06:20,300 --> 00:06:23,680
So for this power, this is the left hand side is the right hand side.

104
00:06:23,900 --> 00:06:29,660
So take left hand side, take the right hand side that is bkk power and then take the operator at the

105
00:06:29,720 --> 00:06:30,100
end.

106
00:06:30,170 --> 00:06:31,020
So this is power.

107
00:06:31,430 --> 00:06:33,450
So this is a C power power.

108
00:06:33,710 --> 00:06:34,460
Now one more thing.

109
00:06:34,850 --> 00:06:39,230
If we look at this mathematically, what does it mean, a power?

110
00:06:41,220 --> 00:06:46,390
B, then see the power of it, see the power of B.

111
00:06:46,770 --> 00:06:51,680
So this is how it looks like if you say no, no, this is left to right associative.

112
00:06:51,690 --> 00:06:58,800
If you want to try that one, then left-to-right associative means first AIPA would be so first apower

113
00:06:58,800 --> 00:07:02,750
B, then this entire thing is given with policy.

114
00:07:02,770 --> 00:07:08,040
So the entire thing would see it will look like this if the associativity is from right to left.

115
00:07:08,430 --> 00:07:11,880
No povero better is having the right to left associativity.

116
00:07:12,210 --> 00:07:13,590
This operator is not there.

117
00:07:13,590 --> 00:07:14,430
And C language.

118
00:07:14,430 --> 00:07:17,560
This used to be there in previous languages, older languages.

119
00:07:18,000 --> 00:07:23,040
Now let's take some unary operators and convert them into prefix and postfix.

120
00:07:24,450 --> 00:07:25,450
Minus eight.

121
00:07:25,800 --> 00:07:34,500
This is an allegation of a unity minus unity, minus precedences, HIAS just less than bracket highest

122
00:07:34,500 --> 00:07:39,590
precedence and associativity is a right to left, so frustrated and left.

123
00:07:39,780 --> 00:07:43,100
It means if there are more than one, then we have to do it from right to left.

124
00:07:43,320 --> 00:07:44,250
So we will see that.

125
00:07:44,430 --> 00:07:46,420
First of all, let us write this prefix.

126
00:07:46,760 --> 00:07:50,850
So if I write it in prefix, then it looks like this is minus.

127
00:07:50,880 --> 00:07:53,700
Only then what is the postfix form of this one?

128
00:07:54,030 --> 00:07:56,550
This is a minus.

129
00:07:57,640 --> 00:08:05,550
Then if suppose I have minus minus eight, then minus should be put in the bracket first right to left,

130
00:08:05,560 --> 00:08:06,160
as I said.

131
00:08:06,370 --> 00:08:11,260
So this minus goes in the bracket first, then this one, obviously.

132
00:08:11,740 --> 00:08:13,720
So that is minus of it.

133
00:08:13,840 --> 00:08:19,210
And it's minus so first right hand side, minus six taken then left hand side minus.

134
00:08:19,630 --> 00:08:21,940
You can convert this into perfect postfix.

135
00:08:23,130 --> 00:08:29,220
The next operator I will take this is the reference operator in sign language, this is used for accessing

136
00:08:29,220 --> 00:08:31,370
the data very point that is pointing.

137
00:08:32,220 --> 00:08:33,840
This is the regular phone.

138
00:08:33,960 --> 00:08:38,039
So I can say this, the infix form, then what is the prefix form?

139
00:08:38,250 --> 00:08:40,039
So prefix will also be seen.

140
00:08:40,230 --> 00:08:43,940
It will be Starboy only because it's not a binary operation.

141
00:08:43,950 --> 00:08:45,060
This is a unity operation.

142
00:08:45,060 --> 00:08:46,310
We have only one option.

143
00:08:46,950 --> 00:08:50,550
So either it has to come before operand or after.

144
00:08:51,030 --> 00:08:55,630
So this is before opening then postfix form of this one.

145
00:08:55,650 --> 00:09:03,330
So it may look like B staff that if there are multiple operators, start starboy means what?

146
00:09:03,900 --> 00:09:05,040
Which is done first.

147
00:09:05,310 --> 00:09:06,990
How they must be parenthesized.

148
00:09:07,290 --> 00:09:08,110
Right to left.

149
00:09:08,310 --> 00:09:11,850
So first ERP then again start of that whole Transat.

150
00:09:12,240 --> 00:09:15,600
Yes, we know it well that it goes from right to left.

151
00:09:15,630 --> 00:09:18,660
We have done it like this, but now we are observing it.

152
00:09:19,140 --> 00:09:23,100
Now I'll take one more example of unity operator and factorial.

153
00:09:23,340 --> 00:09:28,150
What is the prefix form of this one prefix form on this one is first factorial then.

154
00:09:28,150 --> 00:09:31,080
And what is the postfix form of expression?

155
00:09:31,410 --> 00:09:33,300
This is N factorial only.

156
00:09:33,570 --> 00:09:35,130
This is the same normal form.

157
00:09:35,130 --> 00:09:37,620
So normal form is this is nothing but infix I should say.

158
00:09:37,890 --> 00:09:41,010
So the specifics are same as infix here.

159
00:09:41,010 --> 00:09:47,820
Prefix was same as infix prefix for same as infix but here postfix the same as infix.

160
00:09:48,210 --> 00:09:53,160
Now one more operator log x, log X.

161
00:09:53,160 --> 00:09:58,650
What is the prefix of this one operator name is log so it will be long x only then.

162
00:09:58,650 --> 00:10:00,450
What is the postfix form of this one.

163
00:10:00,750 --> 00:10:02,940
It will be X then log.

164
00:10:04,680 --> 00:10:06,830
Postfix Sabaneta comes afterwards.

165
00:10:06,870 --> 00:10:12,960
I have to read it afterwards, upgrader comes before perfect, so it's already before only, so I should

166
00:10:12,960 --> 00:10:13,900
take it as it is.

167
00:10:14,790 --> 00:10:21,120
So there's a few examples of unity operators I have shown you unity over the same languages usually

168
00:10:21,120 --> 00:10:25,450
will have highest precedence and they will have right to left associativity next.

169
00:10:25,470 --> 00:10:28,410
I'll take one example and we will convert it into postfix.

170
00:10:28,680 --> 00:10:33,210
This is an expression based on the precedents and associativity.

171
00:10:33,210 --> 00:10:37,230
If I directly convert it into postfix, who is having higher precedence?

172
00:10:37,230 --> 00:10:46,230
Unary operators who are, you know, the operators here minus log factorial, they all have the same

173
00:10:46,230 --> 00:10:46,800
precedence.

174
00:10:46,800 --> 00:10:49,110
We are fuming that they all have same precedence.

175
00:10:49,500 --> 00:10:52,730
But what is the associativity right to left?

176
00:10:52,770 --> 00:10:56,550
You know, the operator I just wrote minus here, but include all of them here.

177
00:10:56,850 --> 00:11:01,570
So right to left, associativity if it is right to left that which one I should convert into postfix

178
00:11:01,570 --> 00:11:02,010
first.

179
00:11:02,430 --> 00:11:04,350
This one factorial.

180
00:11:04,380 --> 00:11:11,550
I really like the expression minus eight plus bs start log postfix off and sartorialist.

181
00:11:11,550 --> 00:11:18,390
Similarly in fact that one is not ex who are remaining minus and log.

182
00:11:18,540 --> 00:11:20,800
They have higher precedence right to left.

183
00:11:20,820 --> 00:11:22,290
So first convert that log.

184
00:11:22,560 --> 00:11:26,220
So minus A plus B star Gunma.

185
00:11:26,220 --> 00:11:30,790
This log of this one supposed fixes and factorial longbows afterward.

186
00:11:31,110 --> 00:11:33,900
Now I will include the C to compute then which one.

187
00:11:34,230 --> 00:11:35,600
This is the unity operator.

188
00:11:35,640 --> 00:11:37,500
How you complete this first.

189
00:11:37,650 --> 00:11:39,430
So a minus.

190
00:11:39,570 --> 00:11:40,800
Put it into the brackets.

191
00:11:40,800 --> 00:11:42,330
So a minus.

192
00:11:42,330 --> 00:11:47,580
Put it into the bracket to show that this is completed plus as it is B as it is Tarah as it is.

193
00:11:47,580 --> 00:11:48,480
This is complicated.

194
00:11:48,480 --> 00:11:55,670
So N factorial and log maheshwari meaning plus and multiplication.

195
00:11:55,680 --> 00:11:57,180
So who is having higher precedence.

196
00:11:57,180 --> 00:11:57,980
Multiplication.

197
00:11:58,230 --> 00:12:01,620
So this multiplication is upon what Left-Hand side is.

198
00:12:01,620 --> 00:12:03,270
B right inside this whole thing.

199
00:12:03,630 --> 00:12:06,390
So this I will write as it is then.

200
00:12:06,390 --> 00:12:14,700
Plus I have to convert this into postfix so far left hand side B right hand side and factorial log and

201
00:12:14,700 --> 00:12:18,210
this should be inside the last one remaining S plus.

202
00:12:18,420 --> 00:12:22,190
So this plus this up on the left hand side and the right hand side.

203
00:12:22,470 --> 00:12:30,840
So first right left hand side with brackets Lydersen us get a minus then return side B and factorial

204
00:12:30,840 --> 00:12:37,050
analogue return side is completed then right plus so all are completed so I will not bracket.

205
00:12:37,410 --> 00:12:38,470
So this is the answer.

206
00:12:38,850 --> 00:12:42,270
So this is the postfix form of this expression.

207
00:12:42,570 --> 00:12:45,480
Let me show you what at all Priddis I perform first.

208
00:12:45,480 --> 00:12:46,530
I have performed this way.

209
00:12:46,530 --> 00:12:49,080
I got this result, then I have performed this one.

210
00:12:49,080 --> 00:12:50,130
So I got this result.

211
00:12:50,340 --> 00:12:51,720
Then I have performed this one.

212
00:12:51,720 --> 00:12:52,770
So I got this result.

213
00:12:53,040 --> 00:12:54,630
Then I perform multiplication.

214
00:12:54,630 --> 00:12:55,600
So I got this result.

215
00:12:55,620 --> 00:12:56,850
This was the multiplication.

216
00:12:57,090 --> 00:12:58,550
Then I perform addition.

217
00:12:58,560 --> 00:12:59,960
So this was the result.

218
00:13:00,870 --> 00:13:05,520
So first factorial the log, then minus, then multiplication, then plus.

219
00:13:05,880 --> 00:13:09,870
So that's all about associativity and unary operator.

220
00:13:10,050 --> 00:13:14,160
How to handle them while converting into postfix form.

221
00:13:14,340 --> 00:13:17,370
I have explained you prefix is an exercise for you.

222
00:13:17,670 --> 00:13:18,970
You can try that one.

