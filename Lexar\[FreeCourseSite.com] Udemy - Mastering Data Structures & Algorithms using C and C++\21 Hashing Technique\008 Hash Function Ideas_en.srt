1
00:00:00,150 --> 00:00:06,180
And this medium, actually, we're talking about the different hashing functions through so this hashing

2
00:00:06,180 --> 00:00:11,190
function to look at the different ideas for hashing functions, models, hash function, which we have

3
00:00:11,190 --> 00:00:17,340
already used it, a mixed of mixed square hash function and folding hash function.

4
00:00:17,370 --> 00:00:25,770
So I'll explain about those hash functions and see the first and foremost requirement are the properties

5
00:00:25,770 --> 00:00:32,580
of hash function as the hash function should be selected such that the values in the hash table must

6
00:00:32,580 --> 00:00:34,700
be uniformly distributed.

7
00:00:34,830 --> 00:00:40,230
Whether you are using chaining or you are using open addressing that is linear probing or quadratic

8
00:00:40,230 --> 00:00:40,560
probing.

9
00:00:40,590 --> 00:00:45,810
Whatever the method is, use the values in a hash table must be uniformly distributed.

10
00:00:45,810 --> 00:00:50,940
All the value should not be concentrated or collected at the same place every time.

11
00:00:51,090 --> 00:00:52,710
So this is the first and foremost purpose.

12
00:00:53,580 --> 00:00:56,360
So this is the important properties of a hash function.

13
00:00:56,370 --> 00:01:02,380
So selection of hash function depends on a programmer or designer who is designing a system.

14
00:01:02,400 --> 00:01:06,960
So once you add all that already have the idea about the data that you are going to insert in this one.

15
00:01:07,290 --> 00:01:14,100
Based on that, you can select the size of the hash table as well as the type of hash function so that

16
00:01:14,340 --> 00:01:19,630
the collusions are avoided if conditions are wider than the values will be uniformly distributed.

17
00:01:19,920 --> 00:01:25,020
And the next thing, if you are using chaining, then the hash table size can be anything.

18
00:01:25,440 --> 00:01:33,750
But if you are using linear probing, then the hash table size should be double the number of elements

19
00:01:33,750 --> 00:01:35,040
that you are going to insert.

20
00:01:35,280 --> 00:01:40,800
Suppose you are going to insert 10 elements then the size of the hash table must be 20 or should be

21
00:01:40,800 --> 00:01:41,180
double.

22
00:01:41,730 --> 00:01:45,750
If you're using linear probing that you are going to insert very much only inside the hash.

23
00:01:46,140 --> 00:01:50,940
So it means the loading factor of hash table must be less than or equal.

24
00:01:50,940 --> 00:01:52,840
Two point five half a figure.

25
00:01:52,920 --> 00:01:54,100
Yes, definitely.

26
00:01:54,100 --> 00:01:56,630
You have to give extra space for achieving hashing.

27
00:01:57,570 --> 00:02:00,120
Now let us talk about these hash function.

28
00:02:00,420 --> 00:02:01,550
So first hash function.

29
00:02:01,560 --> 00:02:07,770
We have already seen that we have been writing this hash function of X equals X more than whatever the

30
00:02:07,770 --> 00:02:09,180
size of the hash tables.

31
00:02:09,180 --> 00:02:12,490
So this mark function will give the values of starting from zero onwards.

32
00:02:12,780 --> 00:02:17,220
Some of you don't want the values of starting from zero and then you can add a plus one sometime.

33
00:02:17,220 --> 00:02:23,010
Logistical like this also is suppose the other is a starting from index one on works, then we don't

34
00:02:23,010 --> 00:02:23,940
want zero.

35
00:02:23,940 --> 00:02:25,970
We want minimal value as one.

36
00:02:26,100 --> 00:02:29,640
So after that we can add one so that minimal value will get as well.

37
00:02:29,670 --> 00:02:34,470
So any plus one plus one, you can define a model as hash function, not next.

38
00:02:34,470 --> 00:02:35,150
Important thing.

39
00:02:35,820 --> 00:02:37,790
What should be the size of the hash table?

40
00:02:37,980 --> 00:02:43,560
As I said, that it should be double the number of the keys, but it is the preferred that the size

41
00:02:43,560 --> 00:02:49,310
is a prime number so that the collusions already because prime number division, because the prime numbers

42
00:02:49,320 --> 00:02:50,520
will have on the two factors.

43
00:02:50,520 --> 00:02:55,830
That is one and the number itself, that's the reason it is suggested the size of the hash table must

44
00:02:55,830 --> 00:02:57,840
be a prime number like here.

45
00:02:57,840 --> 00:03:01,350
Just for example, African hash, typical size nine.

46
00:03:01,620 --> 00:03:07,320
But it is better for this level that is prime number of 13 or 17.

47
00:03:07,680 --> 00:03:12,960
So it's that we take up time them, whereas the size of a hash that we don't have to discuss much about

48
00:03:12,960 --> 00:03:13,360
this one.

49
00:03:13,400 --> 00:03:16,500
Then the second method let us look at that is MC squared method.

50
00:03:16,590 --> 00:03:23,550
So that method suggests that whatever the keys you do square that key and take the middle digit, for

51
00:03:23,550 --> 00:03:24,090
example.

52
00:03:24,300 --> 00:03:29,430
I think a small key that is just 11 keys that I want to insert.

53
00:03:29,970 --> 00:03:33,270
So they should be inserted, first of all, from square of this.

54
00:03:33,270 --> 00:03:33,270
This.

55
00:03:33,720 --> 00:03:35,190
This gives one twenty one.

56
00:03:36,570 --> 00:03:42,490
Then take it to so this level should be stored up next to one story.

57
00:03:42,510 --> 00:03:48,510
We follow this and while searching also when the number is 11, then squared it so you get one thirty

58
00:03:48,510 --> 00:03:52,380
one, then take the middle aged and go to that place live on the phone.

59
00:03:52,500 --> 00:04:01,230
So I've taken a small number that is 11 is suppose case study then from 13 square that is one sixty

60
00:04:01,230 --> 00:04:03,840
nine then the aged six.

61
00:04:03,870 --> 00:04:05,620
It should go on six.

62
00:04:05,760 --> 00:04:10,050
So in my example I have taken two digits somewhere so square up that I'm getting three digits.

63
00:04:10,290 --> 00:04:15,100
Some of the keys bigger than this one, then you may get more number of digits.

64
00:04:15,140 --> 00:04:17,070
Suppose you have five digits.

65
00:04:18,399 --> 00:04:23,890
Then you can take this middle digit out if you have six digits after performing square, if you are

66
00:04:23,890 --> 00:04:27,380
getting six digits, then we can take two digits.

67
00:04:27,400 --> 00:04:34,180
Also, suppose this two digits are not in the hash table, then you can perform more on that one.

68
00:04:34,480 --> 00:04:35,750
So Mark is always there.

69
00:04:35,980 --> 00:04:43,570
So even after taking off a square of key, you can still perform more so that you can get a position

70
00:04:43,570 --> 00:04:45,640
within the size of a hash tag.

71
00:04:46,690 --> 00:04:52,050
So this is the idea of mega square taking a square of a key, then taking middle digits.

72
00:04:52,360 --> 00:04:54,680
You can take one digit or you can take more of it.

73
00:04:54,970 --> 00:04:55,670
That's up to you.

74
00:04:56,470 --> 00:04:58,930
Then let us look at fording method.

75
00:04:59,110 --> 00:05:00,640
Suppose Altius.

76
00:05:00,820 --> 00:05:04,120
One, two, three, three, four, seven.

77
00:05:05,230 --> 00:05:07,630
I have a six digit keep.

78
00:05:07,870 --> 00:05:11,980
We can partition the key into more than one partitions by taking some number of digits.

79
00:05:12,010 --> 00:05:18,160
So here I will take pooh-pooh digits to two digits, then fuster digits number two.

80
00:05:18,520 --> 00:05:21,040
The next two digit is number thirty three.

81
00:05:21,280 --> 00:05:23,800
The next two digits is forty seven.

82
00:05:26,450 --> 00:05:27,740
R d these digits.

83
00:05:27,980 --> 00:05:29,250
Twelfth Night.

84
00:05:29,600 --> 00:05:31,760
So we got a number ninety to.

85
00:05:32,790 --> 00:05:36,000
This 92 should be an index and hash table.

86
00:05:37,370 --> 00:05:43,880
So for us, we are taking digits and it is just like we are folding these digits and making them as

87
00:05:43,880 --> 00:05:49,400
a single number, that this number can be used as an index for hash table.

88
00:05:49,580 --> 00:05:55,370
And again, if you think that this number is larger, then you can perform more or else you can add

89
00:05:55,370 --> 00:05:59,090
these two digits also like nine plus to 11.

90
00:06:00,360 --> 00:06:05,360
We can still add those two digits also, but I also want should perform the same procedure.

91
00:06:07,860 --> 00:06:14,160
If the index is valid, you can store the element there, otherwise you can again add those digits and

92
00:06:14,160 --> 00:06:20,040
you can again, you can fold those digits and you can generate a new number that is mapping inside the

93
00:06:20,040 --> 00:06:22,770
hash table or within the range of size of the hash to.

94
00:06:23,920 --> 00:06:31,300
So this is about numerary keys, not if some of our keys, a string, I have a string as a key.

95
00:06:33,560 --> 00:06:41,000
ABC supports this key ABC, it can be a name, name of a person, name of a product or anything it can

96
00:06:41,000 --> 00:06:41,210
be.

97
00:06:41,450 --> 00:06:47,860
So I'm just taking Alphabet's ABC, not how this can be converted or indexed in a hash table.

98
00:06:48,110 --> 00:06:55,490
So for this, we can take ASCII quotes of these alphabets like A, B, C, sixty five.

99
00:06:55,490 --> 00:06:57,670
Sixty six, sixty seven.

100
00:06:58,490 --> 00:07:02,570
This is forming three different numbers so you can make it as a single number.

101
00:07:02,600 --> 00:07:08,570
Now this is like a single number and this we can perform folding by taking sixty five plus six to six

102
00:07:08,570 --> 00:07:09,920
plus sixty seven.

103
00:07:10,310 --> 00:07:12,890
We can add all these eight.

104
00:07:12,890 --> 00:07:13,580
Carry one.

105
00:07:15,950 --> 00:07:22,230
One ninety eight now, this is from intelligence, suppose this is going out of range again for more

106
00:07:22,470 --> 00:07:25,020
others, you can skip the scatty.

107
00:07:25,020 --> 00:07:27,570
If you want just two digits, that's up to you.

108
00:07:27,900 --> 00:07:32,850
So there's a whole string can be converted into an index with the help of their ASCII quotes.

109
00:07:34,100 --> 00:07:41,420
Now, finally, conclusion is you can design your own hash function, whatever the hash function you

110
00:07:41,420 --> 00:07:48,470
like, you can design it, make sure that all of it gives the same result whenever you are passing a

111
00:07:48,470 --> 00:07:53,420
key one, two, three, four, seven, that always you should get the lemon.

112
00:07:53,420 --> 00:07:55,720
Suppose if you're getting lemon, you should always get liver.

113
00:07:56,750 --> 00:08:00,740
It's not that one time would have given some numbers or some other time or just giving some other time.

114
00:08:00,740 --> 00:08:03,580
But that is not a suitable hash function.

115
00:08:04,010 --> 00:08:08,810
So hash function should always give you the same index, not while sorting it has given the index.

116
00:08:08,810 --> 00:08:10,910
We will store it while retrieving again.

117
00:08:10,910 --> 00:08:11,660
It gives the index.

118
00:08:11,660 --> 00:08:13,330
We go there and find it, that's all.

119
00:08:15,620 --> 00:08:21,290
So designing hash function is up to a designer or a programmer so you can choose anything you want.

120
00:08:21,440 --> 00:08:26,390
Even a programmer can have his own idea and he can introduce his own hash function.

121
00:08:26,660 --> 00:08:28,100
These are just the ideas.

122
00:08:28,130 --> 00:08:33,620
So depending on the data, you can think over it and you should design some hash function which avoids

123
00:08:33,620 --> 00:08:34,070
collision.

124
00:08:34,190 --> 00:08:40,659
Then one more thing that languages like Java and Darknet these languages provides in hashing like Java.

125
00:08:40,669 --> 00:08:46,460
Whenever you create an object, a hash code is given for that object that has code is automatically

126
00:08:46,460 --> 00:08:49,550
generated by JVM on a system.

127
00:08:49,940 --> 00:08:56,240
So it may be using data and time and a location and various other values as a parameter and generate

128
00:08:56,310 --> 00:09:03,230
some high school so that that code will uniquely identify an object created so universally.

129
00:09:03,230 --> 00:09:05,630
It will be unique and it will be uniquely identified.

130
00:09:05,990 --> 00:09:08,150
So hash codes are more important in Java.

131
00:09:08,300 --> 00:09:10,820
You can identify any object with its hash code.

132
00:09:11,120 --> 00:09:14,990
So those high scores are generated by using some parameters.

133
00:09:15,860 --> 00:09:21,530
So even we can design our own system where we can generate the hash code for every value that we want

134
00:09:21,530 --> 00:09:23,030
to store and hash table.

135
00:09:23,270 --> 00:09:28,130
And we can have our own hash function, which is uniformly distributing the values.

136
00:09:28,340 --> 00:09:31,160
But in these examples we have been using the key.

137
00:09:31,160 --> 00:09:32,360
There is a block of data.

138
00:09:32,390 --> 00:09:33,470
It is not a generator.

139
00:09:33,480 --> 00:09:35,680
The key, the keys, apocrypha data.

140
00:09:35,680 --> 00:09:37,640
I suppose this is the key or this is a key.

141
00:09:38,000 --> 00:09:40,670
Houghton's we can have our own keys for the objects.

142
00:09:40,880 --> 00:09:42,530
So that's all the hashing technique.

143
00:09:42,710 --> 00:09:46,520
So we have discussed various topics and hashing technically practice these programs.

144
00:09:46,550 --> 00:09:49,820
This will develop your programming skills and analytical skills.

