1
00:00:00,720 --> 00:00:07,470
And this video, we look at a function for <PERSON><PERSON><PERSON><PERSON>'s kids, first of all, I will show you a charity

2
00:00:07,480 --> 00:00:10,320
function that I have explained you in the previous video.

3
00:00:12,720 --> 00:00:19,050
I was going to function, I will the function, the mass index parameter, and that is the item number

4
00:00:19,050 --> 00:00:23,340
that we want to find out in the cities inside the function.

5
00:00:23,340 --> 00:00:28,650
First of all, I will write a return zero because if you see this error message, function is having

6
00:00:28,650 --> 00:00:30,890
any return date, but it is not returning anything.

7
00:00:30,900 --> 00:00:35,450
So that's what data is controlled regions and of nonvote function.

8
00:00:35,470 --> 00:00:39,400
So to avoid this error for timing, I will write down return zero.

9
00:00:39,420 --> 00:00:41,340
Then later we'll see what we have to return.

10
00:00:43,030 --> 00:00:44,220
So this threat is gone.

11
00:00:46,670 --> 00:00:54,530
No, I learned on the function, I've taken some variables like easy to initialize with the zero and

12
00:00:54,560 --> 00:00:56,190
even initialized with one.

13
00:00:56,210 --> 00:01:04,010
And as for finding some and also we need variable ifour for loop that the very first thing inside a

14
00:01:04,019 --> 00:01:11,180
function is if and is less than or equal to one, then return same number and.

15
00:01:13,810 --> 00:01:20,740
Otherwise, we'll find out the term using follow ups, so follow will start I from two onwards and I

16
00:01:20,770 --> 00:01:25,180
reaches less than a week or two and then I switched.

17
00:01:25,480 --> 00:01:29,860
So it will start from two and reaches up to the total number and the Tom.

18
00:01:30,550 --> 00:01:33,190
So every time we have to.

19
00:01:34,510 --> 00:01:45,310
Find out some that is by adding zero, and even then, DS3 should get the value of four to one and B

20
00:01:45,310 --> 00:01:51,590
one should get the value of something that is new that we have obtained by repeating this loop for end

21
00:01:51,610 --> 00:01:52,030
times.

22
00:01:52,030 --> 00:01:53,950
So we get the result in s.

23
00:01:54,760 --> 00:02:02,420
So then lots of statements should be written as earlier I wrote it at zero to avoid error return.

24
00:02:02,440 --> 00:02:04,780
S here.

25
00:02:04,780 --> 00:02:08,139
I'm getting a warning that s may be only initialized when used here.

26
00:02:08,440 --> 00:02:13,320
If it doesn't enter into for loop s will not be modified and the US is having some garbage value.

27
00:02:13,330 --> 00:02:15,580
So let us initialize as with the Zettl.

28
00:02:15,910 --> 00:02:17,260
So this warning will be gone.

29
00:02:18,100 --> 00:02:20,200
Will not get any wrong answers due to this.

30
00:02:20,200 --> 00:02:25,480
But to avoid this one I have just initialize Esposito not inside mean function.

31
00:02:25,480 --> 00:02:31,900
I will directly print the result obtained by that function, but it's entirely and new line.

32
00:02:33,340 --> 00:02:40,110
Then I will call the function fizbo but passing then that's OK.

33
00:02:41,020 --> 00:02:42,550
So now the whole program is ready.

34
00:02:43,030 --> 00:02:44,290
I will run the program.

35
00:02:50,010 --> 00:02:59,130
Yes, 22 as 55, let us see, 50, Tom answered is five for 50, Tom.

36
00:03:00,480 --> 00:03:04,510
Yes, the fifth item is five, then six, the term is eight.

37
00:03:04,670 --> 00:03:06,000
Let us try that also.

38
00:03:08,350 --> 00:03:15,250
Yes, I got the result, eight search for perfect normal right recursive function for finding Fibonacci

39
00:03:15,250 --> 00:03:23,770
Dom, let me call the function name are Fab Four recursive then parameters.

40
00:03:23,770 --> 00:03:32,890
And that is the term number, as we have already seen the code if and as less than or equal to one then.

41
00:03:35,690 --> 00:03:50,700
Written and itself, otherwise written are of and the minus two plus are of an A minus one, that's

42
00:03:50,700 --> 00:03:50,880
all.

43
00:03:50,880 --> 00:03:55,560
The function is so simple because it's recursive, it has finished in just two lines.

44
00:03:57,590 --> 00:04:02,600
Instead of calling a fib, I will call r fib here inside the main function.

45
00:04:03,140 --> 00:04:06,980
Now let us run program and see what the output is for this.

46
00:04:06,980 --> 00:04:08,050
It should get eight.

47
00:04:08,510 --> 00:04:10,760
Yes, our afterwards was also giving eight.

48
00:04:13,000 --> 00:04:21,990
Not to reduce this number of calls we have seen memorisation, so I will write on a function using memorisation,

49
00:04:22,000 --> 00:04:28,870
so already I have one function that is a utility function called then recursive function for Fibonacci.

50
00:04:29,290 --> 00:04:34,210
Then next I will write a memoir position function for Fibonacci.

51
00:04:37,320 --> 00:04:44,550
For memorisation function, we need one global F, so I will take the array size as a 10 limited size

52
00:04:45,120 --> 00:04:46,020
if you want larger.

53
00:04:46,050 --> 00:04:46,800
You can take it.

54
00:04:47,520 --> 00:04:51,780
And inside the main function, I will first initialize this array.

55
00:04:55,370 --> 00:05:04,880
Using for a loop, I will initialise, for I signed zero, I use less than 10 I plus plus I remember

56
00:05:04,880 --> 00:05:07,300
the array should be initialized with minus one.

57
00:05:07,310 --> 00:05:13,000
So all these locations, I will fill them with minus one because zero is also if you will not see Tom.

58
00:05:13,020 --> 00:05:14,410
So we should have the Tom.

59
00:05:15,230 --> 00:05:18,980
So you should have a number, not a Fibonacci tone.

60
00:05:18,980 --> 00:05:21,160
So minus one is not a Fibonacci term.

61
00:05:22,070 --> 00:05:25,180
So I have initialized this array with minus one.

62
00:05:26,090 --> 00:05:29,240
Now here I will write on memorisation function.

63
00:05:29,510 --> 00:05:36,620
That is a function for finding Fibonacci dom using memorisation integer is the return type.

64
00:05:36,620 --> 00:05:44,900
So I will call the function Ima's moussab for memorisation then it sticking dom number and as parameter

65
00:05:46,310 --> 00:05:53,750
fustiness if any, is less than or equal to one then in as of an assignment itself.

66
00:05:56,140 --> 00:06:04,840
And also right on and so these are two statements inside if condition, so I will write them in flowback

67
00:06:04,840 --> 00:06:04,990
at.

68
00:06:08,450 --> 00:06:09,750
So this is the body of.

69
00:06:13,040 --> 00:06:25,490
Then else, if the number is greater than one, then check if that's of any minus two is equals to minus

70
00:06:25,490 --> 00:06:25,810
one.

71
00:06:25,820 --> 00:06:31,680
If it is equal to minus one, then call em Saib of A..

72
00:06:31,820 --> 00:06:33,980
Minus to call this function.

73
00:06:34,400 --> 00:06:39,880
And whatever the result we obtain, take it in F off and the minus two.

74
00:06:40,190 --> 00:06:41,660
So I'm writing in a single line.

75
00:06:42,170 --> 00:06:47,090
I will take it in the next line then next one again four and the minus one.

76
00:06:47,090 --> 00:06:48,080
Also I have to check it.

77
00:06:48,440 --> 00:06:58,190
If F of and minus one is equal to minus one then call the function for and minus one and take its result

78
00:06:58,190 --> 00:07:00,570
and F of and minus one.

79
00:07:02,090 --> 00:07:05,060
So call method of and minus one.

80
00:07:06,680 --> 00:07:13,380
Once we got this result, whether the function is called or not call we get the results here then return.

81
00:07:16,470 --> 00:07:26,160
And fourth and minus two, plus F off and minus one, because the results are kept in a global area

82
00:07:26,170 --> 00:07:31,010
F, so that's all it is, a function using memorization.

83
00:07:31,290 --> 00:07:39,690
So F is a global area that will help this function to store the results of the function to avoid excessive

84
00:07:39,690 --> 00:07:40,250
costs.

85
00:07:43,550 --> 00:07:48,980
Now, here inside main function is of calling Ares, I will call em Fab Four or five and I should get

86
00:07:48,980 --> 00:07:49,450
the result.

87
00:07:49,450 --> 00:07:50,150
That's five.

88
00:07:53,410 --> 00:07:56,380
I have deleted the breakpoint now let us run the program.

89
00:07:58,920 --> 00:08:03,750
Yes, the answer is five, if we call it four, six, then let us see.

90
00:08:06,340 --> 00:08:14,500
Aid, it's looking now, will put a break point and debug the function, so you have to count how many

91
00:08:14,500 --> 00:08:17,620
function calls it is making for value.

92
00:08:17,650 --> 00:08:19,890
Five, it should make six calls.

93
00:08:19,900 --> 00:08:21,270
I have made the value as five.

94
00:08:21,280 --> 00:08:24,900
So it should make total six calls, run the program.

95
00:08:25,870 --> 00:08:28,630
So it has game on this printer function.

96
00:08:29,140 --> 00:08:35,480
So you have to watch this watch window and debug navigation to count the number of calls it is making.

97
00:08:35,830 --> 00:08:36,580
Let us stop.

98
00:08:39,830 --> 00:08:42,380
First call value of an EF five.

99
00:08:44,410 --> 00:08:49,960
Second, gold value of an as a three, so here you can see the value of an industry and here at this

100
00:08:49,960 --> 00:08:51,940
place you can see there are two calls.

101
00:08:56,170 --> 00:09:00,640
So in the previous call, it is in this statement Cedar's and this statement.

102
00:09:04,310 --> 00:09:12,140
So to call so far then, for three, 2013 Seidel's blog, then again, it is going to call itself for

103
00:09:12,140 --> 00:09:13,580
one, that is three minus two.

104
00:09:14,450 --> 00:09:17,780
So this is the third call for one.

105
00:09:17,780 --> 00:09:22,620
This conditional statement will be true and it will come out of the function after excluding this safe

106
00:09:22,680 --> 00:09:23,110
block.

107
00:09:23,930 --> 00:09:25,400
So we are on the third call.

108
00:09:27,710 --> 00:09:30,180
Third call has finish back on the second call.

109
00:09:30,710 --> 00:09:33,800
Now it is entering into second if part of this one.

110
00:09:33,800 --> 00:09:40,370
So it will make a call for and minus one that is three is that is and is a three to three minus one

111
00:09:40,370 --> 00:09:40,680
too.

112
00:09:41,060 --> 00:09:43,280
So for two it is a fourth call.

113
00:09:44,420 --> 00:09:46,220
For two, it will enter into its part.

114
00:09:48,840 --> 00:09:51,060
Then first if fifth call.

115
00:09:55,290 --> 00:09:56,340
Then second, if.

116
00:10:01,640 --> 00:10:12,280
It has kept because already the value of one is found so total five calls so far, then back on three.

117
00:10:12,290 --> 00:10:18,900
So it is making a second, but so far three this it has finished now is going to call this one.

118
00:10:19,850 --> 00:10:21,470
So already for of two is known.

119
00:10:21,480 --> 00:10:25,160
So it doesn't make a call then back on five.

120
00:10:25,940 --> 00:10:26,960
No second part.

121
00:10:28,180 --> 00:10:28,930
A five.

122
00:10:30,470 --> 00:10:37,910
Now, it is the sixth call for four and four for already feeble four, two was known, that is four

123
00:10:37,910 --> 00:10:38,480
minus two.

124
00:10:38,480 --> 00:10:42,320
So that's why it didn't call this one the next.

125
00:10:42,620 --> 00:10:46,310
Again, this four three, although that is four minus one is a three so far.

126
00:10:46,310 --> 00:10:47,510
This also devalues.

127
00:10:47,540 --> 00:10:49,610
No, I did not call this one.

128
00:10:50,390 --> 00:10:52,010
So total six calls so far.

129
00:10:52,130 --> 00:10:53,690
Now from forward it will come out.

130
00:10:53,840 --> 00:10:59,930
See it has not expanded the function call with four because all the values were found on back on 5th.

131
00:11:00,320 --> 00:11:01,460
That is value five.

132
00:11:01,460 --> 00:11:04,190
So it is the first call and it will terminate.

133
00:11:04,340 --> 00:11:08,900
So total calls are six, only six calls it has made and got the result.

134
00:11:09,560 --> 00:11:16,490
And if I expand this Fibonacci array, you can see that the values are filled in this one zero one one,

135
00:11:16,760 --> 00:11:19,850
two, three, four, four and five.

136
00:11:19,850 --> 00:11:28,010
The values are not there because one thing I have missed here that is inside this before returning the

137
00:11:28,010 --> 00:11:36,170
result, I should fill up the value that is fizbo and should be filled with five of and the minus two

138
00:11:37,220 --> 00:11:40,290
plus five of and the minus one.

139
00:11:40,970 --> 00:11:46,100
Now let us run the program once again and see how the value of global RF are changing.

140
00:11:54,010 --> 00:12:01,180
You can see that the global array is all filled with minus one, if I continue execution, see first

141
00:12:01,510 --> 00:12:02,850
if one is found.

142
00:12:12,510 --> 00:12:14,210
Then EF zero is found.

143
00:12:18,740 --> 00:12:20,510
Then ever to as one now.

144
00:12:22,580 --> 00:12:23,930
F of is to.

145
00:12:27,870 --> 00:12:38,550
I for forty three, then four, five years, five that fall, so this is how the values are being stored

146
00:12:38,550 --> 00:12:42,120
and global, and if so, that's all in this video.

147
00:12:42,120 --> 00:12:45,650
We have seen three different versions of Fibonacci function.

148
00:12:45,960 --> 00:12:52,320
One is iterative version and recursive function, and the third one was recursive function with memorization

149
00:12:52,320 --> 00:12:54,210
to avoid excessive calls.

