1
00:00:00,550 --> 00:00:05,470
And this video revealed by the sea language program for generating Aviel three.

2
00:00:06,520 --> 00:00:13,570
So we will create a balanced binary search team that is able to see by inserting the elements, by performing

3
00:00:13,570 --> 00:00:19,060
rotations, so let us create a project and call the project as Aviel Tres.

4
00:00:20,300 --> 00:00:21,890
And the C language program.

5
00:00:23,140 --> 00:00:24,580
So let us create this one.

6
00:00:26,980 --> 00:00:32,980
Let us start see the first thing I should define a structure Naude for a tree.

7
00:00:33,240 --> 00:00:35,260
So in this one, we will have a.

8
00:00:36,310 --> 00:00:45,560
Pointed to a left child that is stuck north star and child, then integer data.

9
00:00:45,580 --> 00:00:49,960
That is the key element, then struck north.

10
00:00:51,630 --> 00:00:53,160
Star child.

11
00:00:54,460 --> 00:01:00,670
Then for Aviad, we should maintain balance factor for every Norder, so balance factors are calculated

12
00:01:00,670 --> 00:01:05,489
by the height of a tree, that type of livestock minus height of right supply.

13
00:01:06,000 --> 00:01:09,890
So here we will maintain height for every Norder.

14
00:01:10,950 --> 00:01:15,720
So in this way, we can get the height of left and right subtree and calculate the balance factor.

15
00:01:18,040 --> 00:01:22,570
So, yes, this is hype, so I'm introducing one member that is hype.

16
00:01:23,740 --> 00:01:25,190
Of a Naude.

17
00:01:26,250 --> 00:01:26,670
The.

18
00:01:27,860 --> 00:01:33,830
I should have a pointer for a root node of our aviary, so I will directly declare a pointer here.

19
00:01:33,860 --> 00:01:37,190
That is rude and I will initialize it to null.

20
00:01:40,360 --> 00:01:47,230
Then no, we should perform in third, so for insertion already we have written a function that is recursive

21
00:01:47,230 --> 00:01:53,080
function for inserting in a binary search tree, it will be almost the same function.

22
00:01:53,500 --> 00:01:55,810
Just we will say take it off, hide.

23
00:01:56,050 --> 00:02:03,640
And if it is becoming imbalanced, we perform the so I will copy the code for insert from binary search.

24
00:02:03,820 --> 00:02:06,220
So all we have seen that function, that same function.

25
00:02:06,220 --> 00:02:07,210
I will heavily populated here.

26
00:02:07,840 --> 00:02:08,430
Yes.

27
00:02:08,440 --> 00:02:14,950
Here is a function for recursively inserting in a binary search tree and give a gap so that you can

28
00:02:14,950 --> 00:02:17,950
see the complete function before the function we have written.

29
00:02:18,940 --> 00:02:23,890
Now, while insolation, we have to take care of one thing that we should also said behind and we know

30
00:02:24,070 --> 00:02:27,880
that in invited is always an element is inserted in the lymph node.

31
00:02:28,240 --> 00:02:31,720
So when it is in the lymph node and the height of that node will be one.

32
00:02:34,100 --> 00:02:41,510
So inside this recursive insert, this is the place where I'm creating a new node and inserting it so

33
00:02:41,510 --> 00:02:44,940
after Satinsky, I will also set the height.

34
00:02:45,130 --> 00:02:46,200
That is that's fun.

35
00:02:46,870 --> 00:02:49,580
See, one thing, if you remember, we have this because of the height.

36
00:02:49,760 --> 00:02:55,910
If there is just one single node and the height of Siedel, but I am taking it as one on words or else

37
00:02:55,910 --> 00:02:56,910
you can make it at zero.

38
00:02:57,030 --> 00:02:58,300
Also, there is a choice.

39
00:02:58,610 --> 00:03:00,830
So I'm starting from one on works.

40
00:03:01,880 --> 00:03:05,240
For example, I'll show you see there is only one single.

41
00:03:06,020 --> 00:03:13,370
Then how does one then if it is having a child, then the height of a tree is oh I'll be digging as

42
00:03:13,370 --> 00:03:18,640
to this is one and this is two, I will know that we can start from zero.

43
00:03:18,680 --> 00:03:20,300
But here I'm starting from on.

44
00:03:21,710 --> 00:03:26,420
OK, so if there is a single law, then it is how it will be run, so then obviously the height of this

45
00:03:26,420 --> 00:03:28,280
one will become from here.

46
00:03:28,610 --> 00:03:33,500
From the beginning, it will become too see if there's a single launch, then the height will be one.

47
00:03:33,770 --> 00:03:36,240
And then above this one, this node is there.

48
00:03:36,250 --> 00:03:39,500
So the height from here to here is to do so for this node.

49
00:03:39,500 --> 00:03:41,810
I should say the height from here is to.

50
00:03:43,260 --> 00:03:45,900
Does the one thing then second thing?

51
00:03:48,190 --> 00:03:54,160
For every note, I should update the height so reclusive leader's calling so in the negotiation process

52
00:03:54,160 --> 00:03:56,620
and returning to see this all that.

53
00:03:56,620 --> 00:04:03,070
Also excessive calls are over at the reading in time, I should update the height of every node.

54
00:04:03,370 --> 00:04:11,020
So these height, that is height of every Naude should be equal to the maximum height of left subfield

55
00:04:11,020 --> 00:04:13,900
right subtlely, whichever's subthemes.

56
00:04:13,900 --> 00:04:15,910
Height is greater than height.

57
00:04:15,910 --> 00:04:16,600
We should peak.

58
00:04:17,050 --> 00:04:21,279
So I have to check the left subtree height as well as the right height and take it.

59
00:04:21,579 --> 00:04:26,440
And I should take the maximum one so far that I should write on some order here.

60
00:04:26,620 --> 00:04:32,050
Instead of expanding the code here itself, I will write a function about here for calculating the height

61
00:04:32,350 --> 00:04:35,400
so that function I will directly use it here.

62
00:04:35,410 --> 00:04:38,830
Let us call that function as Naude height.

63
00:04:38,980 --> 00:04:40,030
So I will apologize.

64
00:04:40,660 --> 00:04:41,200
What height.

65
00:04:41,620 --> 00:04:42,410
Height of A..

66
00:04:42,670 --> 00:04:44,050
So far, no height.

67
00:04:44,410 --> 00:04:48,800
I should send a pointer B then I will write on the function for finding the height of A..

68
00:04:49,150 --> 00:04:51,160
So here about this are insert.

69
00:04:51,160 --> 00:04:53,650
I will first of all try the function for finding the height.

70
00:04:54,100 --> 00:05:00,220
So function Naude height uninstructed struck norvig.

71
00:05:02,810 --> 00:05:04,070
And here I should check.

72
00:05:05,450 --> 00:05:12,260
I should find out the height of subtree and also height of right subtree and for the height of left

73
00:05:12,260 --> 00:05:18,800
subtree, I should check that first of all, please not just be as well as these left Charla's that

74
00:05:18,980 --> 00:05:24,290
these alkyl if it is there, then I should pick the height of Bee's left child.

75
00:05:25,880 --> 00:05:28,010
Otherwise, the heights should be taken at zero.

76
00:05:28,610 --> 00:05:35,830
Now, see, maybe I should also take height of Rachel that I will copy this one and pasted here all

77
00:05:35,900 --> 00:05:37,880
of the differences it is for a child.

78
00:05:40,990 --> 00:05:44,920
These are child, then this is our child's height.

79
00:05:46,980 --> 00:05:54,150
Then after this, whichever is greater in that I should add one and set the height of P, so here that

80
00:05:54,170 --> 00:06:02,670
I think I will ride on in written that if X is actually greater than they are, then.

81
00:06:04,140 --> 00:06:06,540
All right, Don Etchells, plus one.

82
00:06:07,440 --> 00:06:09,870
Otherwise, our last one.

83
00:06:10,930 --> 00:06:16,380
That's it, so I should check the height of the left shoulder and the height of Rachell, and in that

84
00:06:16,380 --> 00:06:20,960
it will be plus one, just I'll show you see supporters in the street, the speed.

85
00:06:21,450 --> 00:06:23,040
I want to know the height of this one.

86
00:06:23,290 --> 00:06:28,680
So height of this should be two and the height of this should be one, because there is only one note

87
00:06:28,680 --> 00:06:35,730
and these are two nodes in the height of OK, so these height should be whichever the value is greater

88
00:06:35,730 --> 00:06:36,400
to is greater.

89
00:06:36,420 --> 00:06:37,450
So plus one three.

90
00:06:37,710 --> 00:06:42,930
So yes, height of P is a tree that is one, then two and then three.

91
00:06:43,500 --> 00:06:48,060
So this is all I am comparing and setting the height of B so here.

92
00:06:48,060 --> 00:06:49,230
Whatever the height of.

93
00:06:50,450 --> 00:06:57,440
No dissent by the function, I will take that in to be so in this way, I'll be updating highlights

94
00:06:57,440 --> 00:07:02,280
of every Naude, NetSol Illiad have completed the code.

95
00:07:02,510 --> 00:07:09,110
So what I'm doing is after inserting I pretending I am, I'm updating the heights of each and every

96
00:07:09,110 --> 00:07:09,470
node.

97
00:07:10,070 --> 00:07:14,780
Now here I should write on the code for performing rotation, so far performing rotations.

98
00:07:14,780 --> 00:07:21,170
I should check the balance factor of each node, then perform appropriate rotation.

99
00:07:21,560 --> 00:07:26,600
So for this I should know the balance factor off order to be after updating the height.

100
00:07:26,600 --> 00:07:29,020
I should know the balance factor of note.

101
00:07:29,030 --> 00:07:37,160
B I will write on the rotation function for two cases that is and allow the remaining two are similar

102
00:07:37,160 --> 00:07:37,540
only.

103
00:07:37,850 --> 00:07:39,350
So let us write on the function.

104
00:07:39,380 --> 00:07:42,400
So first of all, I should have a function for finding the balance factor.

105
00:07:42,770 --> 00:07:49,880
So here I will write a function for returning balance factor that is called the function Ima's balance

106
00:07:50,030 --> 00:07:50,870
factor.

107
00:07:51,590 --> 00:07:54,500
Balance factor of struct node.

108
00:07:56,070 --> 00:08:01,830
I should take an order and find this balance factor now, but in fact, there is height of levels of

109
00:08:01,880 --> 00:08:02,950
tremendous height of right.

110
00:08:03,720 --> 00:08:05,870
So, first of all, I should know the height of subtree.

111
00:08:06,090 --> 00:08:07,480
So the height of olive trees.

112
00:08:07,530 --> 00:08:13,230
This one I can copy the scope and the height of just one already have used it here.

113
00:08:13,590 --> 00:08:16,470
So copy these two things then.

114
00:08:17,410 --> 00:08:22,150
No, I should return height of love subtree minus high growth rates.

115
00:08:22,570 --> 00:08:23,290
Yes.

116
00:08:24,630 --> 00:08:26,220
Cha cha cha cha cha.

117
00:08:26,580 --> 00:08:29,510
So the court is similar to finding the height of a..

118
00:08:29,970 --> 00:08:34,630
There we were adding one, but now we are subtracting height of love, some tremendous height of rights.

119
00:08:35,549 --> 00:08:37,650
Now, here, I should check in.

120
00:08:38,250 --> 00:08:44,760
Now, back inside this recursive insert function sitting here, we have already seen just new line.

121
00:08:44,770 --> 00:08:46,320
This is the only new line I have added.

122
00:08:46,860 --> 00:08:52,710
Now let us write on the code for checking balance factors and deciding whether to perform a little or

123
00:08:52,710 --> 00:08:53,130
a lot.

124
00:08:53,310 --> 00:08:56,450
So let us start using that balance factor and perform rotation.

125
00:08:56,480 --> 00:08:57,660
So if.

126
00:08:58,880 --> 00:09:03,140
The balance factor of a. that is balance factor.

127
00:09:05,610 --> 00:09:10,050
Of AP, if it is equal to two months, the notice imbalance.

128
00:09:11,040 --> 00:09:16,710
Then there is imbalance on which side, left hand side, because it's balance that is positive, that

129
00:09:16,710 --> 00:09:24,840
is plus two and I should check the balance factor of left subtree or the left child of a B. So I will

130
00:09:25,140 --> 00:09:27,330
balance a factor of.

131
00:09:29,220 --> 00:09:30,480
These left child.

132
00:09:31,890 --> 00:09:33,840
Here is balance factor small.

133
00:09:34,590 --> 00:09:38,520
And if it is equal to one means, it is on the left hand side.

134
00:09:38,550 --> 00:09:41,730
So I should perform a rotation.

135
00:09:41,760 --> 00:09:43,980
Yes, I should perform a little rotation.

136
00:09:44,220 --> 00:09:47,670
So I should call a function for L'il rotation.

137
00:09:49,240 --> 00:09:53,860
So this logic I have to ride on separately, this will be more than one statement.

138
00:09:53,890 --> 00:09:57,990
So let us make it as a function so I will perform a little rotation upon me.

139
00:09:58,600 --> 00:10:02,190
And also I should report on whatever the address that I'm getting.

140
00:10:02,920 --> 00:10:06,490
Then similarly, I should perform all type of rotation.

141
00:10:06,490 --> 00:10:10,120
So I should check the balance factors and perform rotation.

142
00:10:10,120 --> 00:10:12,280
So I will write on the rest of them.

143
00:10:12,560 --> 00:10:13,870
So I will copy this.

144
00:10:16,200 --> 00:10:17,370
And posted here.

145
00:10:19,620 --> 00:10:25,680
Now, the fact that it's true and the balance factor of this left child minus one, then I should call

146
00:10:26,760 --> 00:10:27,990
our rotation.

147
00:10:30,370 --> 00:10:32,020
Then if.

148
00:10:33,080 --> 00:10:38,540
If the balance factor is minus two, that is it, it's negative, so it is heavy on the right hand side,

149
00:10:39,510 --> 00:10:41,750
then the balance factor of right, I should check.

150
00:10:41,750 --> 00:10:44,510
And if that is also minus one, that is heavy on.

151
00:10:44,720 --> 00:10:45,230
Right.

152
00:10:45,230 --> 00:10:45,680
Right.

153
00:10:45,890 --> 00:10:49,370
So I should call our rotation.

154
00:10:51,740 --> 00:10:57,240
Upon feet, then at last, I copied and pasted here.

155
00:10:57,830 --> 00:11:00,980
Now this is for the right child and the balance factor is minus two.

156
00:11:01,280 --> 00:11:05,540
And the the factor of Rachell is one that is heavy on the left hand side.

157
00:11:05,550 --> 00:11:08,210
So I should call ALAH rotation.

158
00:11:08,880 --> 00:11:13,550
Then I will implement all these functions and show you how these rotation's looks.

159
00:11:14,690 --> 00:11:21,050
So first, let us write a little rotation, so here about this insert function, I will write down a

160
00:11:21,050 --> 00:11:21,980
little rotation.

161
00:11:23,500 --> 00:11:30,880
So function will return a pointer to a. and let us call it a little rotation.

162
00:11:31,090 --> 00:11:36,220
The functionality we are using there, it will take a pointer to an order struck, known to be.

163
00:11:38,780 --> 00:11:46,760
Now, how to handle this one, so I have an example I will show you there, so rotation is being performed

164
00:11:46,760 --> 00:11:54,230
upon nor the P, C, C, suppose this is B, this notice B and the rotation that is a little rotation

165
00:11:54,230 --> 00:11:55,590
is performed over this one.

166
00:11:55,970 --> 00:11:58,010
So what are the changes that have to do?

167
00:11:59,530 --> 00:12:03,310
I should make this known that this left child of the speech.

168
00:12:03,370 --> 00:12:05,620
So let us call this as being.

169
00:12:07,020 --> 00:12:13,770
So the spill should become rude and it's the right child should become that is beet ulcerate child.

170
00:12:13,830 --> 00:12:19,310
So let's call it as a people are let us call it as BLR, these left children right.

171
00:12:19,410 --> 00:12:25,740
And should become a left child of B so that after rotation the N it should look like this.

172
00:12:25,740 --> 00:12:26,490
I will draw them.

173
00:12:28,030 --> 00:12:32,200
This should be on the should be, Paul.

174
00:12:33,740 --> 00:12:42,230
And this should be the and are so I can use a three pointers or just by using two pointers, I can do,

175
00:12:42,360 --> 00:12:44,000
but let us make it simple.

176
00:12:44,240 --> 00:12:48,710
I will take three pointers already pointing here on this note, up on this note.

177
00:12:48,860 --> 00:12:55,400
So I'll take a pill and be an art and I will modify these note about the height.

178
00:12:55,550 --> 00:12:59,330
See, the height of the spillar remains the same because height we are counting down.

179
00:12:59,610 --> 00:13:04,340
So it will remain the same only and the height of A and the P will change.

180
00:13:04,340 --> 00:13:05,510
So we will calculate it.

181
00:13:06,860 --> 00:13:10,620
So let us take three pointers and make the necessary assignments.

182
00:13:10,880 --> 00:13:17,680
So what are the changes I have to do is that I should become E so this is coming on the right hand side,

183
00:13:18,070 --> 00:13:22,550
then the left side should become ELR.

184
00:13:23,090 --> 00:13:26,140
These are the two assignments I have to make and update their heights.

185
00:13:26,480 --> 00:13:29,720
So first of all, let let us take these two extra pointers.

186
00:13:30,830 --> 00:13:32,810
Let us take stock Naude.

187
00:13:33,890 --> 00:13:37,100
Feel that if these left child.

188
00:13:40,290 --> 00:13:42,210
Then struck north.

189
00:13:44,480 --> 00:13:53,600
BLR, that is the right child, so Peel's our child is perfect, nor are the assignments I have to make

190
00:13:54,320 --> 00:13:55,130
deals.

191
00:13:55,130 --> 00:13:57,570
Our child should be made aspy.

192
00:13:58,110 --> 00:13:58,640
Yes.

193
00:14:01,190 --> 00:14:06,140
Then the piece left child should be made as BLR.

194
00:14:07,430 --> 00:14:07,910
OK.

195
00:14:09,640 --> 00:14:12,640
Then I should modify the height, I should find out the.

196
00:14:15,680 --> 00:14:25,040
Heights of A, B and L, because a lot of men seem so let us call function that is not height and change

197
00:14:25,040 --> 00:14:26,330
the height of a b.

198
00:14:27,560 --> 00:14:33,410
BS height is a sign that Naude hide from BP.

199
00:14:34,780 --> 00:14:38,720
Then the people's height, I should change people's height.

200
00:14:38,980 --> 00:14:42,100
Should we change as Naude height?

201
00:14:44,660 --> 00:14:45,680
From being.

202
00:14:46,650 --> 00:14:53,590
That's all done at last, if suppose the rotation was performed about Ruden or for three months, if

203
00:14:53,630 --> 00:15:00,980
this B was equal to Rudman's ruthless means, if this route was equal to be made through Denpa same,

204
00:15:01,250 --> 00:15:09,170
then a rule should be changed to new that this new rule that is being Nexon after this Sayeret on.

205
00:15:11,370 --> 00:15:13,370
Being that is the new rule.

206
00:15:15,340 --> 00:15:17,510
That's all this is a little rotation.

207
00:15:18,050 --> 00:15:26,260
Yes, now let us check by inserting a few notes and see whether automatically that's a little traditionally

208
00:15:26,260 --> 00:15:28,030
performed up on a tree or not.

209
00:15:28,690 --> 00:15:32,920
So here inside the main function, I'll be calling.

210
00:15:33,910 --> 00:15:43,810
Are insert, so first of all, I should say root a sign, ah, insert bypassing Rude and the keys and.

211
00:15:45,020 --> 00:15:46,550
Then our insert.

212
00:15:48,400 --> 00:15:50,440
Ruled and the keys.

213
00:15:53,220 --> 00:15:57,810
Five will come on the left hand side, then are insert.

214
00:16:00,320 --> 00:16:05,030
Root, then do so, this will be on the left left.

215
00:16:05,600 --> 00:16:12,440
So if I draw the diagram and show you this is how the tree will look like, so this will be in balance

216
00:16:12,440 --> 00:16:15,170
and the rotation should be performed over this and.

217
00:16:16,500 --> 00:16:19,640
So who should become the new rule five should become new rule?

218
00:16:20,250 --> 00:16:21,860
Let us check whether it works.

219
00:16:26,000 --> 00:16:29,570
Here I will put a bookmark and run and show you.

220
00:16:34,750 --> 00:16:35,080
Oops.

221
00:16:35,480 --> 00:16:41,980
There are warnings because I have not implemented the rest of the functions, so I'll make them comments,

222
00:16:42,480 --> 00:16:47,400
OK, instead of comments, I'll write down dummy functions, see for a little.

223
00:16:47,720 --> 00:16:53,210
And also it's giving an error because in a little rotation I have taken a pointer.

224
00:16:53,210 --> 00:16:54,460
Yes, a little rotation.

225
00:16:54,500 --> 00:17:01,010
Odd is not capitalized is a small so I have changed that one then the rest of the functions I'll just

226
00:17:01,010 --> 00:17:06,770
write down dummy function so that it doesn't show any warning.

227
00:17:07,160 --> 00:17:12,500
So this is one more and this I will call it as a lot of patient.

228
00:17:14,480 --> 00:17:15,349
And one more.

229
00:17:16,690 --> 00:17:18,520
And this, I will call it as.

230
00:17:19,589 --> 00:17:20,760
Our rotation.

231
00:17:23,260 --> 00:17:26,349
And one more, this, I will call it as.

232
00:17:28,950 --> 00:17:33,120
Then each function, I should write on something so I should have done something otherwise it will,

233
00:17:33,120 --> 00:17:35,340
given that all the functions are not written anything.

234
00:17:35,340 --> 00:17:41,910
So write it down for each three three of these functions, I will write on null for all of them.

235
00:17:42,720 --> 00:17:45,420
Now let us compile and see if there is any error.

236
00:17:45,420 --> 00:17:46,250
I will remove it.

237
00:17:47,160 --> 00:17:48,000
There is no error.

238
00:17:48,910 --> 00:17:54,460
Now, let us see the node structure that is tree structure formed here, let us expand this.

239
00:17:54,980 --> 00:17:56,830
Oh, Rudi's five.

240
00:17:57,780 --> 00:18:00,270
Sea route was 10, I have given 10 here.

241
00:18:01,480 --> 00:18:05,710
But here you can see that is fine at its height is also to.

242
00:18:07,670 --> 00:18:13,610
And it's left childless to yes, perfect, and the right child is 10, yes, rotation is perfect.

243
00:18:17,030 --> 00:18:17,660
NetSol.

244
00:18:18,600 --> 00:18:20,040
As they the big point.

245
00:18:25,330 --> 00:18:29,290
Now, I have to show you one more rotation that is a lot of rotation.

