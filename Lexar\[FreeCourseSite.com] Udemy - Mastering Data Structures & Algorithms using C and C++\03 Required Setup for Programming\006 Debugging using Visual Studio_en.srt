1
00:00:00,060 --> 00:00:07,800
In this video, we will see how to do debugging in Visual Studio City Building is the process of executing

2
00:00:07,800 --> 00:00:11,570
the program line by line and tracing the program.

3
00:00:12,690 --> 00:00:16,800
So tracing will help us to find out any bugs in the program.

4
00:00:17,280 --> 00:00:23,820
A program, if a program is executing but not giving perfect results, then there may be something gone

5
00:00:23,820 --> 00:00:24,990
wrong in the program.

6
00:00:25,590 --> 00:00:29,880
To find out that mistake, we have to trace the program and check it.

7
00:00:30,360 --> 00:00:32,740
So for tracing debugger will help us.

8
00:00:33,660 --> 00:00:35,130
So how to do debugging?

9
00:00:35,130 --> 00:00:36,120
We will see here.

10
00:00:36,120 --> 00:00:40,560
You don't have to do any special settings for debugging, so it's very easy.

11
00:00:40,980 --> 00:00:42,140
And Visual Studio.

12
00:00:42,810 --> 00:00:44,750
I'll show you how to do debugging.

13
00:00:44,760 --> 00:00:47,470
So already I have one program written here.

14
00:00:47,970 --> 00:00:53,440
This program is already available for finding the sum of all the elements in an array.

15
00:00:53,490 --> 00:01:00,190
So I have some array of elements and the program will find the addition of all these elements.

16
00:01:01,050 --> 00:01:02,490
So let us do debugging.

17
00:01:03,150 --> 00:01:07,920
So for debugging, you have to set some breakpoint in any statement in the program.

18
00:01:09,150 --> 00:01:13,130
So till the break point, the program will execute normally and it will stop there.

19
00:01:13,500 --> 00:01:15,290
Then you can trace it line by line.

20
00:01:15,300 --> 00:01:19,610
So I'll put a breakpoint on the first line itself, first instruction itself.

21
00:01:20,400 --> 00:01:23,280
So the method of putting a breakpoint is on the left hand side.

22
00:01:23,640 --> 00:01:26,460
That is before the line number and there's a gray area.

23
00:01:26,580 --> 00:01:30,830
Just click so it will insert a breakpoint.

24
00:01:30,840 --> 00:01:33,670
And if you want to remove, just click it again, it will be gone.

25
00:01:34,470 --> 00:01:36,570
So it is just like toggling on and off.

26
00:01:36,600 --> 00:01:37,870
So just click it once.

27
00:01:38,010 --> 00:01:42,980
That said, we go to break, but you can have more than one breakpoints also in the program.

28
00:01:43,230 --> 00:01:47,970
So if the program is very lendee, you may require that you can have multiple breakpoints.

29
00:01:47,970 --> 00:01:50,890
So here I have first statement as a big point.

30
00:01:51,420 --> 00:01:57,990
Now let us run, go to the back and say start debugging so that you can press F5.

31
00:01:57,990 --> 00:02:02,700
Also to just click on this one is on the first line.

32
00:02:03,930 --> 00:02:10,080
Yes, it is really so you can see that in this line, some variable is declared, but the line is not

33
00:02:10,080 --> 00:02:10,840
yet executed.

34
00:02:10,860 --> 00:02:12,300
So here is a watch window.

35
00:02:13,110 --> 00:02:14,430
Here is a watch window.

36
00:02:14,460 --> 00:02:18,900
It will automatically pop up and here you can find some and it's having garbage.

37
00:02:19,350 --> 00:02:20,870
It's not yet initialized.

38
00:02:21,300 --> 00:02:24,060
So this variable, this line is about to be executed at night.

39
00:02:24,360 --> 00:02:31,260
So if I take the cursor also the showing that the value in some is some garbage value, Knoller's execute

40
00:02:31,260 --> 00:02:34,680
this line so far that what I have to just go to debug.

41
00:02:35,070 --> 00:02:44,430
And here you can find the statements like step in to step over or step out so you can follow F 10 four

42
00:02:44,700 --> 00:02:45,540
step over.

43
00:02:45,840 --> 00:02:52,110
If it is a function and you want to go inside that function, then you can say step in, do so for this

44
00:02:52,110 --> 00:02:53,010
simple program.

45
00:02:53,040 --> 00:02:55,320
Afghani's OK, so you can press a button.

46
00:02:55,830 --> 00:02:59,400
So come back and press often find pressing.

47
00:02:59,410 --> 00:03:01,850
Efkan Yes.

48
00:03:01,980 --> 00:03:03,810
No, you can see that variable.

49
00:03:03,810 --> 00:03:06,360
Some is already created and it is initialized to zero.

50
00:03:06,360 --> 00:03:12,000
If I move the cursor here it shows value at zero and in the watch window now you can see a new variable

51
00:03:12,000 --> 00:03:13,980
A which is having garbage.

52
00:03:13,980 --> 00:03:15,780
That is not right.

53
00:03:15,780 --> 00:03:19,650
It's not there initialized because this line is about to be executed.

54
00:03:19,650 --> 00:03:20,760
It's not yet executed.

55
00:03:21,300 --> 00:03:28,020
If I press after it again, then now you can see that this is initialized with these values.

56
00:03:28,020 --> 00:03:32,160
So here in the watch window, you can see go four, six, seven, nine.

57
00:03:32,460 --> 00:03:39,090
All these values, these values are there in another even in the watch window, I can expand and see

58
00:03:39,090 --> 00:03:42,090
the values here for all these values represented here.

59
00:03:43,170 --> 00:03:46,470
Right now, it's about to enter into the for loop.

60
00:03:46,900 --> 00:03:53,190
You have to observe this watch window, how the value of X is taking these elements from it and they

61
00:03:53,190 --> 00:03:54,690
are getting added to some.

62
00:03:54,690 --> 00:03:57,030
So the values are two four six seven nine.

63
00:03:57,450 --> 00:03:59,670
So I'll keep on pressing aften.

64
00:04:02,740 --> 00:04:10,380
Now, you can see X is also defined now and it's values to know Nix's, some are saying some plus X.

65
00:04:10,420 --> 00:04:16,950
So now two is added to some and this is a displayed who is displayed on the screen.

66
00:04:17,320 --> 00:04:20,170
So if you want to see that window, just you can click here.

67
00:04:20,589 --> 00:04:24,970
Now you can see that two is displayed here in the console window.

68
00:04:25,900 --> 00:04:32,920
Now, again, often not continue, not X is gone because it has again finished the for loop noggin about

69
00:04:32,920 --> 00:04:34,390
to start to fall once again.

70
00:04:34,390 --> 00:04:36,520
So it's not dead in the watch window here.

71
00:04:36,520 --> 00:04:43,150
You can see life again often now X is there again and it's having for.

72
00:04:43,180 --> 00:04:50,620
So if we keep the cursor on X we can find values for and keep the cursor on some the values to now when

73
00:04:50,620 --> 00:04:53,950
this statement is executed now some becomes six.

74
00:04:54,250 --> 00:04:55,900
So that is two plus four to six.

75
00:04:56,560 --> 00:05:02,980
Now it will print four also then again next Wednesday can actually become six and some becomes 12,

76
00:05:03,970 --> 00:05:07,030
X becomes seven and some becomes nineteen.

77
00:05:08,230 --> 00:05:18,050
Then X becomes nine and Sam becomes 28, and that's all it has came out of the loop this time, no print

78
00:05:18,050 --> 00:05:18,640
Daxam.

79
00:05:19,480 --> 00:05:21,630
So that program ends.

80
00:05:22,540 --> 00:05:28,420
So if I see the scandal that is console window, I can see all the values that are printed and also

81
00:05:28,420 --> 00:05:29,560
some is printed here.

82
00:05:30,610 --> 00:05:32,550
So this is how you can with the programs.

83
00:05:33,220 --> 00:05:38,070
So wherever you want to understand the workings of a program and you want to see it's working clearly.

84
00:05:38,440 --> 00:05:44,440
So I suggest you follow this debugging process so that it will give you a clear idea about the work

85
00:05:44,500 --> 00:05:50,470
of a program so that all the concepts you are learning, you can check them how the things are happening

86
00:05:50,470 --> 00:05:51,670
with the help of thieberger.

87
00:05:52,650 --> 00:05:59,040
So DeBacker is a very powerful tool for learning also and also developing applications for removing

88
00:05:59,040 --> 00:05:59,620
books.

89
00:06:00,360 --> 00:06:01,610
So that's all in this video.

