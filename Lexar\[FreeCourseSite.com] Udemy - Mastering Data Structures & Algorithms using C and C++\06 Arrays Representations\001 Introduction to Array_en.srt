1
00:00:00,210 --> 00:00:07,040
And this really will learn about an Audi, what is an Audi and how to declare an Audi and initialize

2
00:00:07,140 --> 00:00:12,150
that, then what are the methods of accessing elements of an Audi?

3
00:00:12,720 --> 00:00:15,030
So let us start with an introduction to an Audi.

4
00:00:16,120 --> 00:00:23,110
Before talking about <PERSON><PERSON>'s, let me explain about simple variables, variables are supported by every

5
00:00:23,110 --> 00:00:26,060
programming language, and the variables will have some data type.

6
00:00:26,440 --> 00:00:32,920
For example, if I declare a variable of type integer and whose name is <PERSON>, and in this I want to store

7
00:00:32,920 --> 00:00:39,280
the value 10, then we know very well that at one time Variable X will get some memory.

8
00:00:39,820 --> 00:00:45,180
If we assume that integer takes two bytes, then two bytes of memory will be allocated for X legacy.

9
00:00:45,370 --> 00:00:48,970
Fassbinder this is 100 and the next Bidart assessment, not one.

10
00:00:49,180 --> 00:00:52,510
And together in these two bytes, the value ten will be stored.

11
00:00:54,010 --> 00:00:55,530
The we are already familiar of it.

12
00:00:55,930 --> 00:01:02,280
So this type of variable is a single valued variable which can store just one single value and a single

13
00:01:02,290 --> 00:01:03,040
valid variable.

14
00:01:03,050 --> 00:01:05,990
We can also call it as scalar variable.

15
00:01:06,340 --> 00:01:07,670
So there's this killer variable.

16
00:01:09,080 --> 00:01:13,970
Sillerman is having just magnitude, so this is magnitude, the value is a 10 on.

17
00:01:15,650 --> 00:01:24,250
Then what is if we can store multiple values, that is a list of values or a set of values, so it is

18
00:01:24,260 --> 00:01:28,270
a collection of elements and all the elements are of the same type.

19
00:01:28,580 --> 00:01:34,380
So Array is a collection of a similar data elements grouped under one name.

20
00:01:34,760 --> 00:01:42,170
Suppose instead of just one value, if I have to store a list of values, then I can declare and let

21
00:01:42,200 --> 00:01:47,080
us see integer type I want and the name is E and the size of five.

22
00:01:47,450 --> 00:01:54,260
So this will give the array of size of five where I can store five integers.

23
00:01:57,230 --> 00:02:05,000
Five individuals I can store and inbusiness zero, one, two, three, four, now here does this killer

24
00:02:05,000 --> 00:02:07,280
variable where I can store just one value.

25
00:02:07,490 --> 00:02:11,660
This is a vector variable, which is also having a dimension as a single dimension.

26
00:02:11,900 --> 00:02:18,830
So I can store total five values in this one so we can call this variable as a vector variable.

27
00:02:20,360 --> 00:02:22,580
So arrays are vector variable.

28
00:02:23,970 --> 00:02:29,310
Does a single integer underneath makes these are five integers underneath a.

29
00:02:30,990 --> 00:02:38,560
The memory allocated is hundred and will not run for five integers, total 10 bytes of memory will be

30
00:02:38,560 --> 00:02:45,090
allocated if the integer sticking to bytes, for example, the first byte addresses 200, then the next

31
00:02:45,090 --> 00:02:46,050
byte is two, not one.

32
00:02:46,060 --> 00:02:49,560
So this is two, not two and three.

33
00:02:49,560 --> 00:02:51,030
Two, not four and five.

34
00:02:51,510 --> 00:02:51,750
Two.

35
00:02:51,750 --> 00:02:53,580
Not six and seven.

36
00:02:54,030 --> 00:02:56,090
Two, not eight and nine.

37
00:02:56,520 --> 00:02:59,360
So the memory a look at will be contiguously.

38
00:02:59,730 --> 00:03:01,980
So all the locations will be side by side.

39
00:03:02,280 --> 00:03:05,880
They are contiguous, four, five integers total.

40
00:03:05,880 --> 00:03:09,260
Ten bytes of memories are located together as a single block.

41
00:03:09,960 --> 00:03:12,510
Not all those five individuals are having the same name.

42
00:03:12,510 --> 00:03:18,180
That is a then how to differentiate those five integers so we can differentiate them with their indices,

43
00:03:18,630 --> 00:03:19,790
not eight of zero.

44
00:03:20,070 --> 00:03:23,070
This is first integer second and third and fourth and fifth.

45
00:03:23,070 --> 00:03:27,120
So I can access them or differentiate them with the help of index.

46
00:03:27,900 --> 00:03:31,290
So I want to store of value 15 here.

47
00:03:31,530 --> 00:03:38,570
Then I can see eight of two that is indexed to assign value 15.

48
00:03:38,850 --> 00:03:40,730
So this will store them under 15 here.

49
00:03:41,280 --> 00:03:48,140
So using the name and the index, we can access any of those integers, any of those elements.

50
00:03:48,450 --> 00:03:54,870
So that's a whole array is a collection of elements or a list of elements which are of the same type

51
00:03:54,870 --> 00:03:56,070
order of type integer.

52
00:03:56,310 --> 00:04:01,020
And this is supported by every programming language as a basic feature of a language.

53
00:04:01,410 --> 00:04:04,400
This is there in C language as well as C++.

54
00:04:04,920 --> 00:04:10,590
Now let us see what are the different methods of declaration and what are the various ways of initializing

55
00:04:10,590 --> 00:04:10,950
our.

