1
00:00:00,900 --> 00:00:03,690
Let us look at such methods in an odd.

2
00:00:04,019 --> 00:00:05,640
There are two search methods.

3
00:00:06,150 --> 00:00:09,450
One is <PERSON><PERSON> search and the second one this binary search.

4
00:00:09,840 --> 00:00:11,490
So in this, we do have a look at <PERSON><PERSON>.

5
00:00:11,570 --> 00:00:14,820
So our next video, we will talk about binary search.

6
00:00:15,240 --> 00:00:18,410
So let us look at Lenio Search here.

7
00:00:18,420 --> 00:00:19,860
I have a list of elements.

8
00:00:19,860 --> 00:00:24,400
I have taken a list of sites or RFI that's 10 and the list of sites is also 10.

9
00:00:25,170 --> 00:00:28,830
Now, here I have taken unique elements so far performing search.

10
00:00:28,980 --> 00:00:30,690
The elements must be unique.

11
00:00:30,720 --> 00:00:34,650
There should not be any duplicate elements if there are multiple copies done.

12
00:00:34,650 --> 00:00:37,350
If you are searching, we may be getting any one of the copy.

13
00:00:37,350 --> 00:00:39,960
We may not be able to reach another copy of it.

14
00:00:40,110 --> 00:00:47,660
So this should be a unique set of elements then in a list if I want to search for any key element.

15
00:00:47,940 --> 00:00:50,820
So the value that we are searching, usually we call it that's key.

16
00:00:51,300 --> 00:00:53,520
So suppose I'm searching for a key.

17
00:00:54,210 --> 00:00:55,920
That is fine.

18
00:00:56,970 --> 00:01:01,500
Let us look at linear search method in media search.

19
00:01:01,500 --> 00:01:09,000
We search for the key in the list linearly by checking the elements one by one for searching five.

20
00:01:09,000 --> 00:01:12,990
We will start comparing from the first element on what is it, five?

21
00:01:12,990 --> 00:01:14,910
No, next go to the next element.

22
00:01:14,910 --> 00:01:15,600
Is it five?

23
00:01:15,600 --> 00:01:15,960
No.

24
00:01:16,260 --> 00:01:16,930
Go to the next.

25
00:01:17,280 --> 00:01:17,930
Is it five?

26
00:01:17,940 --> 00:01:19,910
No, no, no.

27
00:01:20,370 --> 00:01:21,840
Yes, it is found here.

28
00:01:22,090 --> 00:01:24,360
We already know the element that we are looking for.

29
00:01:24,360 --> 00:01:27,690
That is five and we want to know where it's present.

30
00:01:28,170 --> 00:01:30,450
So we got the index that is seven.

31
00:01:31,230 --> 00:01:39,150
So usually the result of a search is the location where the element is found so that we can access that

32
00:01:39,150 --> 00:01:39,500
element.

33
00:01:40,310 --> 00:01:43,080
Now, let us take one more key element and search it.

34
00:01:43,230 --> 00:01:47,030
So this time I think the key element is twelth.

35
00:01:48,760 --> 00:01:52,630
Let us find out whether Google is present or not, so we'll follow the same procedure.

36
00:01:52,660 --> 00:01:59,710
We'll start from the very first element in the list and compare 12 as it will not go to the next element,

37
00:01:59,710 --> 00:02:03,960
as it will not go to the next element, the next, next, next, next.

38
00:02:03,970 --> 00:02:05,500
And even this is not the.

39
00:02:05,500 --> 00:02:06,460
Well, this is not.

40
00:02:06,490 --> 00:02:09,030
Well, then we have reached the end of our list.

41
00:02:09,310 --> 00:02:15,550
So if you have reached the end of our list, once element is not found, if the element is not found,

42
00:02:15,550 --> 00:02:18,010
searches unsuccessful.

43
00:02:18,880 --> 00:02:22,800
So if the key element is found, we say search is successful.

44
00:02:23,020 --> 00:02:26,030
If it is not found and we say it is unsuccessful.

45
00:02:26,500 --> 00:02:28,470
So this is successful.

46
00:02:29,410 --> 00:02:30,990
This is unsuccessful.

47
00:02:32,300 --> 00:02:38,740
So key element is found the search for successful killing, but it's not found, so the search was unsuccessful.

48
00:02:38,750 --> 00:02:42,530
So when you're searching, you have to take care of other things, whether the element is found or not

49
00:02:42,530 --> 00:02:42,830
found.

50
00:02:43,760 --> 00:02:47,570
Now, let us write a pseudocode for performing linear search.

51
00:02:47,960 --> 00:02:54,430
So for searching for a key element, we start comparing the element from the zeroth index onwards.

52
00:02:54,440 --> 00:02:59,780
And every time we move on to the next element, sort of just like traversing a list, this can be done

53
00:02:59,780 --> 00:03:03,580
using for loop or even if you want, you can use while loop.

54
00:03:03,800 --> 00:03:05,400
So I'll show using follow.

55
00:03:06,110 --> 00:03:16,040
So for all you start from index zero, so I should start from index zero and tell where I should go

56
00:03:16,310 --> 00:03:18,980
to the end of a list and the Nexus nine.

57
00:03:19,280 --> 00:03:20,720
So that is Lent.

58
00:03:20,720 --> 00:03:23,750
Lent is ten so it just lent to minus one.

59
00:03:24,050 --> 00:03:31,010
So I can see I less than Lent so I should traverse the whole length of a list and every time I should

60
00:03:31,010 --> 00:03:37,280
be moving forward five percent C++ then what I have to do every time, wherever I is I should check

61
00:03:37,280 --> 00:03:40,420
that is the key element as equal to of.

62
00:03:41,540 --> 00:03:51,240
So if key is equal to eight of i.e. if it is equal then searches successful.

63
00:03:51,560 --> 00:03:57,050
So if you got the element, so once you got the element, what we want, we want the index element is

64
00:03:57,050 --> 00:03:57,450
found.

65
00:03:57,770 --> 00:04:01,480
So here we can see a written index.

66
00:04:02,180 --> 00:04:03,750
So who is giving index.

67
00:04:03,850 --> 00:04:09,590
I submit it is better than that is index return i.e. that's all.

68
00:04:10,430 --> 00:04:12,200
So let us see how it works.

69
00:04:12,410 --> 00:04:14,210
I initialised also holds.

70
00:04:14,210 --> 00:04:16,420
The key value that I'm looking for is a five.

71
00:04:16,670 --> 00:04:17,540
Then it will check.

72
00:04:17,779 --> 00:04:18,910
I use less than that.

73
00:04:18,920 --> 00:04:19,310
Yes.

74
00:04:19,610 --> 00:04:20,000
Key.

75
00:04:20,000 --> 00:04:21,480
Is it equal to 85.

76
00:04:21,950 --> 00:04:23,060
That is key five.

77
00:04:23,070 --> 00:04:25,240
Is it equal to zero eight.

78
00:04:25,490 --> 00:04:25,900
No.

79
00:04:26,210 --> 00:04:32,570
So it will continue and say I plus I will move to the next index then here checks gives it equal to

80
00:04:32,840 --> 00:04:33,170
fight.

81
00:04:33,470 --> 00:04:33,970
Fight.

82
00:04:33,980 --> 00:04:36,870
Is it equal to that of one nine.

83
00:04:37,250 --> 00:04:37,650
No.

84
00:04:37,700 --> 00:04:43,410
So it will go to the next so it will continue and once it reaches here.

85
00:04:43,790 --> 00:04:46,620
Now this time key is equal to five years.

86
00:04:46,640 --> 00:04:50,060
Five is equal to each of the seven then written seven.

87
00:04:51,420 --> 00:04:54,000
So this is a case of successful search.

88
00:04:55,370 --> 00:05:01,460
Now, what happens in case of unsuccessful search, let us see, initially, I suppose we are searching

89
00:05:01,460 --> 00:05:06,910
for 12, Fossett will check that Keays equals to eight zero to a lizard equal to this one.

90
00:05:06,920 --> 00:05:07,340
No.

91
00:05:07,520 --> 00:05:09,170
Then I will move to the next element.

92
00:05:09,500 --> 00:05:11,600
Then check is equal to F1.

93
00:05:11,640 --> 00:05:12,650
F1 no.

94
00:05:12,840 --> 00:05:13,620
Then again, I.

95
00:05:14,180 --> 00:05:17,510
I move to the next element, then again, take 12.

96
00:05:17,510 --> 00:05:18,960
Is it equal to any of this.

97
00:05:18,980 --> 00:05:19,310
No.

98
00:05:19,610 --> 00:05:23,660
So in this way it will continue and this condition will never be true.

99
00:05:23,840 --> 00:05:25,730
So I will move to next to the next.

100
00:05:25,730 --> 00:05:26,240
The next.

101
00:05:26,270 --> 00:05:31,950
And finally let us take this eye is less than land use land the system and I use the name.

102
00:05:31,970 --> 00:05:34,370
Yes, it is less so key.

103
00:05:34,400 --> 00:05:36,110
Is it equal to fight two.

104
00:05:36,320 --> 00:05:43,880
Is it equal to do not then continue I placeless I will move to the next location now.

105
00:05:43,880 --> 00:05:51,680
I became actually ten, I became ten and ten is not less than ten so it will come out of the loop.

106
00:05:51,830 --> 00:05:58,600
So if it is not finding a key element then it will come out of the loop if at all kilometers is found

107
00:05:58,610 --> 00:06:00,140
and it will return.

108
00:06:00,470 --> 00:06:02,170
Stop the procedure here itself.

109
00:06:02,870 --> 00:06:05,630
So if the key is not found, then it will come out of the loop.

110
00:06:05,630 --> 00:06:08,780
Then outside the loop we can say return.

111
00:06:11,330 --> 00:06:15,830
Not if the key element is not found, then you have to return something to show that element is not

112
00:06:15,830 --> 00:06:16,190
found.

113
00:06:16,490 --> 00:06:22,160
So the invalid index, that is minus one, so we can return minus one.

114
00:06:22,160 --> 00:06:23,930
So usually we do it like this.

115
00:06:24,230 --> 00:06:27,700
If the element is not found, then return indexes minus one.

116
00:06:28,070 --> 00:06:34,310
So if you are calling this procedure, then if you go to minus fundaments element, it's not fun if

117
00:06:34,310 --> 00:06:36,500
you got a valid index element is found.

118
00:06:36,510 --> 00:06:41,450
So that's how you can know whether the search was successful or unsuccessful.

119
00:06:41,960 --> 00:06:43,460
So this is about Línea such.

120
00:06:43,760 --> 00:06:48,860
Let us analyze, find all the time complexity of linear search.

121
00:06:49,190 --> 00:06:53,030
So one thing I will tell you about time complexity for finding the time complexity.

122
00:06:53,030 --> 00:06:57,710
Either you can observe the work that you have done or else you can read the code.

123
00:06:59,210 --> 00:07:03,230
So let us see from the procedure what we are doing for searching.

124
00:07:03,680 --> 00:07:05,940
Key element checking next.

125
00:07:05,940 --> 00:07:07,570
Check next, next, next.

126
00:07:07,850 --> 00:07:13,310
So we are comparing the elements, how many elements we are comparing at the most, how many.

127
00:07:13,500 --> 00:07:19,850
If I'm searching for two, then I may be performing on and compare notes depending on the number of

128
00:07:19,850 --> 00:07:24,260
elements or if I'm searching for eight, then I remember getting eliminated.

129
00:07:24,260 --> 00:07:25,580
Just one competition.

130
00:07:26,610 --> 00:07:35,020
So if you look at the code now, this loop may repeat up to the end of a list, or if at all the conditions

131
00:07:35,040 --> 00:07:37,710
satisfied in the beginning itself, it may terminate.

132
00:07:38,220 --> 00:07:45,940
So this loop may repeat the minimum for one time and maximum for any time.

133
00:07:46,350 --> 00:07:53,160
So the best case is that if the element that we are searching is found at first place and the time order

134
00:07:53,160 --> 00:07:59,550
of one, which gives timers one and the worst cases, if you're searching for an element which is present

135
00:07:59,550 --> 00:08:03,060
at the last index and the time is outdraw.

136
00:08:03,210 --> 00:08:12,120
And so worst case is searching for lost element or the time taken for worst case, which outdraw and

137
00:08:12,540 --> 00:08:15,130
this is a case of successful search.

138
00:08:15,690 --> 00:08:17,460
What about unsuccessful search?

139
00:08:17,670 --> 00:08:23,460
If the key element is not found, then always it will check all the elements, then only it can inform

140
00:08:23,460 --> 00:08:24,810
you that element is not there.

141
00:08:25,050 --> 00:08:29,640
So far, unsuccessful search timers are always outdraw and.

142
00:08:31,410 --> 00:08:33,940
It's maximum time, always maximum time.

143
00:08:33,960 --> 00:08:38,070
So unless you have reached the end of the list, you cannot say that the element is not present in the

144
00:08:38,070 --> 00:08:38,280
list.

145
00:08:38,280 --> 00:08:39,559
So the time taken this order.

146
00:08:39,809 --> 00:08:44,970
And so this was the best case and worst case I have shown, you know, one more time.

147
00:08:45,270 --> 00:08:47,790
Let us look at average a case a time.

148
00:08:47,790 --> 00:08:51,810
Also, average case time may not be easy always.

149
00:08:51,810 --> 00:08:57,440
So we may not be performing it always because it means a lot of efforts for doing analysis.

150
00:08:57,660 --> 00:09:01,580
So mostly we are dependent on worst case or also best case.

151
00:09:02,310 --> 00:09:05,040
Let's see, average case, little take.

152
00:09:05,520 --> 00:09:08,700
So best case is searching for an element present.

153
00:09:08,700 --> 00:09:11,940
That one was his or searching element.

154
00:09:12,180 --> 00:09:13,350
But I think that last.

155
00:09:14,350 --> 00:09:20,070
Then these are two cases, what are the other cases searching for an element present at the next one

156
00:09:20,080 --> 00:09:23,350
that is second element or the third element or the fourth element or the fifth element?

157
00:09:23,680 --> 00:09:26,110
So these are all possible cases then?

158
00:09:26,110 --> 00:09:29,590
What is the number of competition four done for checking?

159
00:09:29,590 --> 00:09:33,040
If the element is present, then what is the number of competition required?

160
00:09:33,040 --> 00:09:38,920
If you're searching for first element, just one competition required, then what about the element,

161
00:09:38,920 --> 00:09:42,720
that second location, that is the next one to competitions required?

162
00:09:43,030 --> 00:09:47,110
So the case, if you're searching for this, it needs to competition in case if you're searching for

163
00:09:47,110 --> 00:09:49,630
this, it needs to be competition.

164
00:09:49,630 --> 00:09:50,380
So on.

165
00:09:50,680 --> 00:09:53,510
Then if you're searching for lost element and competition.

166
00:09:53,540 --> 00:09:53,810
Sorry.

167
00:09:54,760 --> 00:10:01,180
So I have taken the time taken in all possible cases and I have taken the total time taken in all possible

168
00:10:01,180 --> 00:10:01,630
cases.

169
00:10:01,990 --> 00:10:07,510
So some of the times have taken in all possible cases then total, how many cases are there and elements

170
00:10:07,510 --> 00:10:07,870
out there.

171
00:10:07,960 --> 00:10:09,560
So any guesses out there.

172
00:10:09,580 --> 00:10:17,290
So this is in so some of the time taken in all possible cases divided by number of cases, this is equal

173
00:10:17,290 --> 00:10:19,390
to and in the end.

174
00:10:19,390 --> 00:10:20,740
Plus one by two.

175
00:10:22,940 --> 00:10:25,820
Hall divided between and gets canceled.

176
00:10:26,120 --> 00:10:34,100
So this is endless one way to so this polynomial degree, one that is Bodrov and for the average case,

177
00:10:34,100 --> 00:10:34,970
time is.

178
00:10:36,150 --> 00:10:37,130
Outdraw and.

179
00:10:39,020 --> 00:10:42,620
So you have found the average kids also now.

180
00:10:43,660 --> 00:10:44,900
Let me say something here.

181
00:10:45,430 --> 00:10:50,370
She usually finding average case time needs a lot of analysis.

182
00:10:50,370 --> 00:10:56,560
So you have to consider all possible cases, identify them, take all the time and are there times and

183
00:10:56,560 --> 00:10:58,060
divide them by a number of cases.

184
00:10:58,350 --> 00:11:03,170
A little tedious work, and sometimes we may not be able to get some proper solution.

185
00:11:03,850 --> 00:11:12,160
So mostly we are dependent on worst case because usually worst case and the average case will take the

186
00:11:12,160 --> 00:11:14,370
same amount of time, almost the amount of time.

187
00:11:14,710 --> 00:11:16,540
So worst case is easy to analyze.

188
00:11:16,780 --> 00:11:19,520
So mostly we do worst case analysis.

189
00:11:19,990 --> 00:11:23,850
So this was the analysis of Línea search?

190
00:11:25,060 --> 00:11:25,930
No, let us see.

191
00:11:26,320 --> 00:11:28,310
Improvement in Línea search.

192
00:11:28,840 --> 00:11:30,440
Is there any improvement possible?

193
00:11:31,010 --> 00:11:31,720
Let us check.

