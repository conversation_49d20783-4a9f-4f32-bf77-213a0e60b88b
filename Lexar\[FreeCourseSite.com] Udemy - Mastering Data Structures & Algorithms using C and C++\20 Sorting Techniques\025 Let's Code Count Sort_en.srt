1
00:00:01,030 --> 00:00:08,440
And this video will write a concert function, so already you have a main function and a list of elements

2
00:00:08,440 --> 00:00:09,040
available.

3
00:00:09,520 --> 00:00:12,820
Now here I will write on a function for concert.

4
00:00:14,210 --> 00:00:19,550
Consort, it takes an array of elements as well as the number of elements.

5
00:00:21,540 --> 00:00:27,750
Now, here, inside this, I need two variables, IJ<PERSON> and <PERSON>, for finding the maximum element and

6
00:00:27,750 --> 00:00:30,580
also funding the elements, I need boundaries.

7
00:00:30,580 --> 00:00:31,950
So for that, I'll take appointer.

8
00:00:33,560 --> 00:00:38,750
Now, the first thing is I should find on the maximum elements in Element in the list so far that I

9
00:00:38,750 --> 00:00:45,580
will try to function to find find Max, 80 percent UTI and a number of elements.

10
00:00:45,590 --> 00:00:52,450
And so let us write on the function here for finding a maximum elements element.

11
00:00:52,460 --> 00:00:54,950
I should have a minimum element.

12
00:00:54,950 --> 00:01:01,790
So into Tudi to underscore Minne, this will be a minimum element, integer element.

13
00:01:02,920 --> 00:01:04,060
Then I need.

14
00:01:05,060 --> 00:01:13,310
I foreshadowing to this list so far, I assign zero I is less than an eight plus plus.

15
00:01:15,530 --> 00:01:20,600
If any element from an uhry is greater than Max.

16
00:01:26,060 --> 00:01:33,360
Then we will change Max to that element, so already we have the smallest element so we can find out

17
00:01:33,380 --> 00:01:42,940
maximum element return Max, so dysfunctional, find out the maximum element from now here inside longsword,

18
00:01:42,940 --> 00:01:44,950
we will build an area of that size.

19
00:01:45,460 --> 00:01:48,250
So CSI and CSI and.

20
00:01:50,050 --> 00:01:53,560
Integer type array mellark function.

21
00:01:55,920 --> 00:01:58,320
And this should be size of.

22
00:01:59,390 --> 00:02:07,460
Integer, and this should be multiplied by Max plus one as we need index up to that number, that is

23
00:02:07,460 --> 00:02:10,060
maximum number because they need to start from zero.

24
00:02:10,070 --> 00:02:15,580
So we should take one extra, like if you want, and index and then it should take sides of Urías 11.

25
00:02:15,590 --> 00:02:17,440
So you get any sense from the European.

26
00:02:19,500 --> 00:02:25,980
Then this area should be initialise, so I assign zero itis less than an.

27
00:02:28,460 --> 00:02:31,740
Sorry, Max, plus one and I plus plus.

28
00:02:32,090 --> 00:02:36,590
So the theory should be initialise with Zettl.

29
00:02:39,080 --> 00:02:42,050
All the elements are initialized, count is initialized to zero.

30
00:02:44,300 --> 00:02:50,690
Now we have to scan through this area that is a that is being passed, so we will scan through this

31
00:02:50,690 --> 00:02:58,130
one for I assign zero, I guess, less than 10 for an element, a plus plus and whatever the element

32
00:02:58,130 --> 00:02:58,700
is found.

33
00:02:58,700 --> 00:03:01,490
So in Sea of AOF I.

34
00:03:02,520 --> 00:03:05,960
They will do plus plus increment if that is found.

35
00:03:08,440 --> 00:03:15,940
Then we have to scan through border is I, that is a and see, so I assign zero and also assign Siedel

36
00:03:16,210 --> 00:03:17,140
Nevine.

37
00:03:19,710 --> 00:03:26,410
G is less than Max plus one at the end of the series.

38
00:03:26,550 --> 00:03:28,530
We will check if.

39
00:03:29,980 --> 00:03:33,570
Of if CEO of G.

40
00:03:34,660 --> 00:03:36,010
It's greater than zero.

41
00:03:36,260 --> 00:03:37,810
It is not zero minutes.

42
00:03:37,840 --> 00:03:48,970
There are definitely some element, sort of, if I will copy that element and also will decrement account

43
00:03:49,080 --> 00:03:51,970
of that G so minus Pinus.

44
00:03:52,960 --> 00:03:58,760
So I have used AI for our E and G for the C.

45
00:03:59,270 --> 00:04:01,810
OK, so variable names.

46
00:04:02,700 --> 00:04:07,600
And if it is greater than zero, we will do this, otherwise we will increment J.

47
00:04:08,400 --> 00:04:08,970
So J.

48
00:04:08,970 --> 00:04:09,720
Plus plus.

49
00:04:10,590 --> 00:04:11,370
And that's on.

50
00:04:12,710 --> 00:04:15,260
This will sort a.

51
00:04:16,209 --> 00:04:23,890
So from here, I will call consort function by passing and a number of elements are in.

52
00:04:25,880 --> 00:04:31,280
Yes, let us run the program and check, oops, we got another.

53
00:04:32,370 --> 00:04:40,950
Let us see what is the reason of the letter C is none, C is none, and this is having element OK effect,

54
00:04:41,460 --> 00:04:45,330
then this is a zero and this is none that is giving null.

55
00:04:47,360 --> 00:04:51,920
OK, so it means C is not sign any hurry, we are creating an.

56
00:04:52,280 --> 00:04:56,960
So let us see what the problem is there in that area creation process.

57
00:04:58,050 --> 00:05:03,390
OK, Marlock, we have not included a warning, so it will implicitly declare so we need not go and

58
00:05:03,570 --> 00:05:05,170
go there and write honestly deliver.

59
00:05:06,060 --> 00:05:13,800
How Max plus one that should be inside bracket, then what is the value in Max or some large value?

60
00:05:14,100 --> 00:05:14,940
So OK.

61
00:05:14,970 --> 00:05:19,600
Yes, I have written a function for finding Max, but I did not call that function.

62
00:05:19,920 --> 00:05:21,690
Yes, I missed that statement.

63
00:05:21,990 --> 00:05:25,560
So fine, Max, I should send an update and a number of elements.

64
00:05:25,560 --> 00:05:30,180
And so, Max, I found out let us check the code once again before executing it.

65
00:05:31,560 --> 00:05:37,890
This is initializing an array and this is called incrementing all the values than I use for ages four.

66
00:05:38,970 --> 00:05:40,020
That is Kountry.

67
00:05:40,710 --> 00:05:43,280
And one element is found.

68
00:05:43,300 --> 00:05:48,420
Value is greater than zero and copied element of a oh plus plus.

69
00:05:48,420 --> 00:05:55,590
Also, I should do that is copied competency of these variables so that it becomes zero and we continue

70
00:05:55,590 --> 00:05:56,750
upon the next element.

71
00:05:56,760 --> 00:05:58,200
Yes, everything is perfect.

72
00:05:58,260 --> 00:05:59,400
Yeah I see.

73
00:05:59,460 --> 00:06:00,300
It was missing.

74
00:06:01,380 --> 00:06:03,000
Therefore, let us run this now.

75
00:06:05,720 --> 00:06:12,680
Yes, perfect, three five seven nine, 10, 11, 12, 13, 16 and 24, so it's working perfectly.

76
00:06:14,250 --> 00:06:16,060
So this is a town sort of.

77
00:06:17,990 --> 00:06:23,630
So this is a faster sorting algorithm that takes a lot of time, but it takes a lot of space.

78
00:06:24,620 --> 00:06:25,720
That's all in the studio.

