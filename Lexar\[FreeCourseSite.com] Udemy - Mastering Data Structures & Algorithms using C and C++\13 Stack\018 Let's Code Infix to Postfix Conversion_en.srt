1
00:00:00,150 --> 00:00:07,140
In this video, we will write a function for converting infix expression to Portuguese expression as

2
00:00:07,140 --> 00:00:08,790
the procedure utilizes the stacks.

3
00:00:08,790 --> 00:00:13,440
Already I have the order for <PERSON><PERSON><PERSON>, so that is using Linklaters.

4
00:00:13,620 --> 00:00:15,030
So this is the same program.

5
00:00:15,300 --> 00:00:20,290
Start using <PERSON><PERSON><PERSON><PERSON> and the Solidere have uses for Barondess matching.

6
00:00:20,310 --> 00:00:25,890
Now I will use the same program or similar function for infix to specific dunwich.

7
00:00:28,000 --> 00:00:33,730
But forensics reports, Reconversion, I need a few functions, like the checking the precedences or

8
00:00:33,730 --> 00:00:36,400
for that I will write on function for precedences.

9
00:00:38,610 --> 00:00:43,950
Function return type as integer and let us call the function <PERSON><PERSON>'s body for precedence, and it should

10
00:00:43,950 --> 00:00:48,300
take a character type value that is X.

11
00:00:49,750 --> 00:00:58,450
And here, if X is equal to plus operator, minus operator, it's precedences one, so I should return

12
00:00:58,450 --> 00:00:59,920
one else.

13
00:00:59,920 --> 00:01:03,770
If it is multiplication or division, then I should return precedence as to.

14
00:01:04,239 --> 00:01:15,640
So if X is equal to multiplication, that is star or if it is Division X is equals to division, then

15
00:01:15,640 --> 00:01:16,510
I should return.

16
00:01:17,620 --> 00:01:19,540
Too, because the president is, too.

17
00:01:21,340 --> 00:01:26,400
That's all we have seen only for operators based on that, we will convert the expression into politics

18
00:01:26,430 --> 00:01:26,950
expression.

19
00:01:27,690 --> 00:01:28,870
Also, there is no precedent.

20
00:01:28,880 --> 00:01:32,430
So I should return something that I will return to the next.

21
00:01:32,440 --> 00:01:37,600
I also need a function to check whether symbol is a operator or operator.

22
00:01:37,630 --> 00:01:44,410
So I will have integer type return type and the function name I will recognize is operand.

23
00:01:45,250 --> 00:01:47,830
And data type is character.

24
00:01:48,880 --> 00:01:54,280
Here it is, plus minus multiply or divide, and it is an operator, otherwise it's an opening.

25
00:01:54,310 --> 00:01:55,840
So here I have a word on the condition.

26
00:01:55,840 --> 00:02:02,770
If Character X is equal to this operator or it is minus, I will read all these things.

27
00:02:03,670 --> 00:02:08,289
Here are written addition, subtraction, multiplication, division, then it is not an option, so

28
00:02:08,289 --> 00:02:09,380
I will return false.

29
00:02:09,400 --> 00:02:15,160
That is a written zino, otherwise I will return true return true if an open.

30
00:02:16,440 --> 00:02:22,080
So that's all these functions are sufficient for me to write up and fix Pacific Tunnel vision function.

31
00:02:23,350 --> 00:02:28,960
Now, here, inside the main function, I will take an expression that is infix, so character.

32
00:02:30,050 --> 00:02:36,560
Pointed out by Bill Gates and insects, I will already initialise this one, so I will take E-Plus be

33
00:02:37,040 --> 00:02:44,950
multiplied by see a simple expression, then I take one more pointer for postfix expression.

34
00:02:45,470 --> 00:02:48,690
So the sportsplex expression will be done by the function.

35
00:02:48,710 --> 00:02:52,790
So here I will write on a function for infix to postfix.

36
00:02:53,390 --> 00:02:55,400
So return type of a function is correct.

37
00:02:55,490 --> 00:03:03,950
A pointer and two or I will polygons infix sportsbook so I will say interposed and the parameters are

38
00:03:04,430 --> 00:03:06,470
pointer of Tife infix.

39
00:03:07,100 --> 00:03:16,490
Then inside the function I need a character type array or postfix and I have taken a pointer for this.

40
00:03:16,490 --> 00:03:17,990
I should allocate the memory.

41
00:03:18,230 --> 00:03:21,440
So first of all I should know the length of a string lente.

42
00:03:21,830 --> 00:03:23,930
So Estie are Lenn.

43
00:03:23,930 --> 00:03:29,480
I can call the function that is Stenglein function to find out the size of infix expression.

44
00:03:29,840 --> 00:03:33,560
And depending on that I should allocate the space for this postfix expression.

45
00:03:33,890 --> 00:03:39,760
So postfix I will create an array inside heap of type character.

46
00:03:40,400 --> 00:03:48,520
So star character, mellark function and whatever the Lantis plus one because I need to store zero also.

47
00:03:48,740 --> 00:03:56,510
And this should be multiplied by size of character, multiplied by size of character.

48
00:03:58,210 --> 00:04:03,530
On a Botox experience created inside, he so that I can return it to the main function.

49
00:04:04,390 --> 00:04:09,520
Now I need a few more things like this I n g that are both initialized to zero.

50
00:04:10,470 --> 00:04:18,480
Now, using a while loop, I will scan through and fix expression, so while infix of eye is not equal

51
00:04:18,480 --> 00:04:20,339
to single.

52
00:04:21,660 --> 00:04:27,060
Then every time I should check whether it's an open is operand.

53
00:04:29,350 --> 00:04:37,270
I should pass this infix, a symbol to that function is open operand function, and if it returns true,

54
00:04:37,270 --> 00:04:38,650
then I should add it to.

55
00:04:39,820 --> 00:04:41,880
Postfix expression, right?

56
00:04:42,410 --> 00:04:45,580
Plus plus a sign infix of.

57
00:04:46,780 --> 00:04:47,980
I placeless.

58
00:04:49,440 --> 00:04:52,830
It's the first thing else, if it is not an.

59
00:04:53,830 --> 00:04:58,600
Operand, then I should check the precedences, check the presidents of.

60
00:04:59,850 --> 00:05:00,960
Infixes, symbol.

61
00:05:01,930 --> 00:05:02,410
I.

62
00:05:04,140 --> 00:05:07,110
If the presidents of inflexible is greater than.

63
00:05:08,600 --> 00:05:15,230
Residents of a symbol inside the stock, then I should call residents of stopgaps, Gedda.

64
00:05:16,200 --> 00:05:17,280
I should send this one.

65
00:05:18,280 --> 00:05:24,760
So the point that is pointing that the topmost element is stacked up and if the Sensex exhibition symbol

66
00:05:24,760 --> 00:05:32,200
is greater, then we will push a symbol in the stock that is Insects' of a plus plus.

67
00:05:34,120 --> 00:05:40,090
If the symbol is smaller, then they will pop out of value from the stack.

68
00:05:41,660 --> 00:05:52,110
And added to postfix, so postfix of G plus plus will be assigned with about value from the stack XOL

69
00:05:52,670 --> 00:05:59,840
before it all happens inside a line loop, then once you come out of the loop, while the stack is not

70
00:05:59,840 --> 00:06:06,560
empty, while top is not equal to null, I should continue and I should support all the values from

71
00:06:06,560 --> 00:06:11,240
the stack and add them to postfix expression or six of G plus.

72
00:06:11,240 --> 00:06:16,260
Plus I should assign the value from the stack up out from the stack.

73
00:06:16,820 --> 00:06:25,370
Then after this at the postfix end of the postfix, I should also add Snel character that is zero so

74
00:06:25,370 --> 00:06:26,930
that it becomes a complete string.

75
00:06:27,830 --> 00:06:28,410
NetSol.

76
00:06:29,030 --> 00:06:31,760
This is really not one thing that I'm finding.

77
00:06:31,760 --> 00:06:35,430
The precedents of a post element initially stack be empty.

78
00:06:35,630 --> 00:06:36,780
So this may give error.

79
00:06:37,100 --> 00:06:44,560
So what I do initially, I will push something in the stack, so push I will push a symbol that is a

80
00:06:44,570 --> 00:06:45,110
hash.

81
00:06:45,290 --> 00:06:47,330
So initial push this one.

82
00:06:49,030 --> 00:06:56,050
Now, inside the main function, we will call this function into post by passing this infix expression

83
00:06:56,320 --> 00:07:04,540
and it should return the postfix expression and then we will print this f percentile as.

84
00:07:06,730 --> 00:07:09,220
Expression, we will paint, that is postfix.

85
00:07:12,850 --> 00:07:20,520
That's all and one more thing, I'll increase the size of this Uhry that is POSIX, I'll add to install

86
00:07:20,530 --> 00:07:21,970
and plus one plus two.

87
00:07:22,660 --> 00:07:28,490
And one thing inside the function, I should also add return postfix extra.

88
00:07:28,540 --> 00:07:29,900
I should add that on postfix.

89
00:07:29,900 --> 00:07:35,990
So it should return the expression and it is taken in this variable that is a pointer and it is displayed.

90
00:07:36,610 --> 00:07:38,330
Now let us run the program and check.

91
00:07:41,550 --> 00:07:48,560
C plus B star plus hash C, as we have also already included a hash inside the stock.

92
00:07:48,570 --> 00:07:50,460
So that is also popped out and out around here.

93
00:07:50,790 --> 00:07:54,300
That is the reason I have taken the size Ousland plus two.

94
00:07:55,640 --> 00:08:02,330
Here showing me a warning that actually England returns long type of data, but I'm thinking as integer,

95
00:08:02,330 --> 00:08:03,950
so I may be losing some data.

96
00:08:04,730 --> 00:08:07,490
So anyway, it is sufficient for me because the value is very small.

97
00:08:09,940 --> 00:08:13,000
To remove this warning, I should make this as long.

98
00:08:14,770 --> 00:08:21,100
Anyway, the function is working perfectly, let us add a few more things into the expression than here,

99
00:08:21,100 --> 00:08:26,040
I will write A plus B or C minus A D by E.

100
00:08:26,500 --> 00:08:35,650
So the sixth form of this one is A, B, C star plus the slash slash minus.

101
00:08:35,830 --> 00:08:36,850
So let us run.

102
00:08:40,679 --> 00:08:44,880
ABC staff plus D minus.

103
00:08:44,910 --> 00:08:45,590
Yes, correct.

104
00:08:45,630 --> 00:08:46,790
We got the result.

105
00:08:47,800 --> 00:08:53,020
So that's all the coming videos, you'll find one more method of converting and for political expression,

106
00:08:53,320 --> 00:08:57,190
so you have to write on that program by yourself and practice this program.

107
00:08:58,740 --> 00:08:59,870
That's all in this video.

