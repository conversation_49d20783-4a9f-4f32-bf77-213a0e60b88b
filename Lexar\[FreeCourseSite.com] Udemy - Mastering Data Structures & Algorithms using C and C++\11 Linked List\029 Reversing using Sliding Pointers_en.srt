1
00:00:01,330 --> 00:00:09,400
Now, let us reverse a linguist by reversing <PERSON><PERSON>'s reversing <PERSON><PERSON>'s means, this node for instead of

2
00:00:09,400 --> 00:00:14,290
pointing on Node six, it should point on two and six should point on four and eight should point on

3
00:00:14,290 --> 00:00:16,309
six and two should become null.

4
00:00:16,870 --> 00:00:22,660
Now, before showing you how to reverse, I'll show you one small concept that is sliding pointers,

5
00:00:22,780 --> 00:00:25,750
sliding pointers and sliding pointers.

6
00:00:26,200 --> 00:00:28,010
What I mean by sliding pointers.

7
00:00:28,030 --> 00:00:28,540
Let us see.

8
00:00:29,200 --> 00:00:30,600
I take three pointers.

9
00:00:30,670 --> 00:00:38,650
One is pointer B and the pointer Q and pointer are initially pointer piece pointing.

10
00:00:38,650 --> 00:00:41,710
One for Snork and Q is null.

11
00:00:41,890 --> 00:00:44,790
That is before it and out is also null.

12
00:00:45,130 --> 00:00:45,970
That is before it.

13
00:00:46,000 --> 00:00:48,880
I'm assuming that are before Fastenal or actually they are not.

14
00:00:49,090 --> 00:00:54,120
They are no better actually but assume that they are here now this is the initial state.

15
00:00:54,340 --> 00:00:58,780
So what they want is these three pointers should slide, slide, move.

16
00:00:59,020 --> 00:01:00,960
It is just like a telling point pointer.

17
00:01:01,390 --> 00:01:04,720
If you remember one pointer P and Q was following it.

18
00:01:04,900 --> 00:01:06,040
Now R is following.

19
00:01:06,040 --> 00:01:08,710
Q So all three together are moving.

20
00:01:08,710 --> 00:01:09,470
So I'm calling it.

21
00:01:09,470 --> 00:01:10,390
That's a sliding.

22
00:01:10,900 --> 00:01:13,600
So let us see how I want them to work.

23
00:01:14,120 --> 00:01:16,960
See first R should come on.

24
00:01:17,320 --> 00:01:23,900
Q and Q should move on to B and we should move to mix Nixonland.

25
00:01:25,370 --> 00:01:35,570
This is one moment, one step, then continue, I should come on cue, you should move on to be and

26
00:01:35,570 --> 00:01:38,020
we should move to next North Sea.

27
00:01:38,030 --> 00:01:39,740
All three are moving together.

28
00:01:39,860 --> 00:01:42,070
So that's why I'm calling them a sliding point.

29
00:01:42,100 --> 00:01:45,440
And then again, I should move on.

30
00:01:46,520 --> 00:01:51,270
You should move on B B should move to the next room.

31
00:01:52,340 --> 00:01:57,860
So in this way, we can move three pointers together so far that I will ride on the court here initially,

32
00:01:57,860 --> 00:02:00,730
BS on first and KUNR both are null.

33
00:02:01,750 --> 00:02:11,760
Then in each step, what I am doing odd is coming over cu, CU is coming over PE and PS moving to next

34
00:02:11,770 --> 00:02:12,160
north.

35
00:02:14,160 --> 00:02:22,170
I want this to continue until peace becomes null so I can write a condition that will be not equal to

36
00:02:22,170 --> 00:02:22,520
no.

37
00:02:22,590 --> 00:02:24,690
So it will stop, then becomes null.

38
00:02:26,430 --> 00:02:29,760
So these are the steps before sliding windows.

39
00:02:29,880 --> 00:02:30,870
So now you know what?

40
00:02:30,870 --> 00:02:32,100
I was sliding pointers.

41
00:02:32,940 --> 00:02:35,580
Now let us see how we can reverse the links.

42
00:02:36,120 --> 00:02:41,190
So I take a small example and show you what are the requirements for reversing the links.

43
00:02:41,910 --> 00:02:43,290
So I take three notes.

44
00:02:48,260 --> 00:02:53,940
I assume that this is already pointing on the previous note, then I have to reverse the lingo of this

45
00:02:53,940 --> 00:02:56,320
note so that it points on this previous note.

46
00:02:56,460 --> 00:02:58,230
That's what we want right for.

47
00:02:58,230 --> 00:03:03,300
Should point on to your point on formants this note, should point on the previous note now have to

48
00:03:03,300 --> 00:03:04,220
modify this one.

49
00:03:04,410 --> 00:03:05,630
So let us take a pointer.

50
00:03:05,640 --> 00:03:06,570
Q On this one.

51
00:03:08,440 --> 00:03:14,060
Then for changing cues, link to this Naude, I should know the address of this note also, so I have

52
00:03:14,060 --> 00:03:15,460
one pointer here on.

53
00:03:17,260 --> 00:03:24,220
Then when this link is modified, this will be pointing here, then where is the address of the next

54
00:03:24,220 --> 00:03:24,570
node?

55
00:03:24,730 --> 00:03:26,060
We lost the address.

56
00:03:26,080 --> 00:03:30,970
We don't have the address, just not even having the address of this, nor WiFi modify it and make it

57
00:03:30,970 --> 00:03:31,500
point here.

58
00:03:31,870 --> 00:03:37,510
We will not get the address of the next node so far that we should also have a pointer on the next node,

59
00:03:37,600 --> 00:03:38,830
let us call it as being.

60
00:03:39,280 --> 00:03:45,730
So we need three pointers, the node which is being modified and the previous node and the next not

61
00:03:45,850 --> 00:03:47,830
from taking the names of SPQR.

62
00:03:48,190 --> 00:03:53,390
So what I should do Fuseli should point on are this modification.

63
00:03:53,410 --> 00:03:54,430
I should do so.

64
00:03:54,430 --> 00:03:55,620
This has reversed the link.

65
00:03:55,930 --> 00:03:59,110
No, I have to modify the link of this node.

66
00:03:59,120 --> 00:04:00,700
Beyond that, there are few more nodes.

67
00:04:00,700 --> 00:04:01,330
Assume that.

68
00:04:01,330 --> 00:04:03,300
I assume that there are more nodes ahead.

69
00:04:03,580 --> 00:04:09,690
So I have to modify this node so far that we should go there, then I should come here.

70
00:04:09,700 --> 00:04:16,180
So I should come in and you should move to the smaller and the B should move to next.

71
00:04:16,180 --> 00:04:18,450
More like there is some node again.

72
00:04:19,390 --> 00:04:20,050
So that's it.

73
00:04:20,290 --> 00:04:23,160
So it means the speed you are sliding.

74
00:04:24,040 --> 00:04:26,250
So that is the reason already I have explained you.

75
00:04:26,260 --> 00:04:27,580
What does it mean by sliding.

76
00:04:28,040 --> 00:04:37,500
So how to reverse this slide and reverse the link they have slated then say Qs next assign are not set.

77
00:04:38,110 --> 00:04:44,050
So we need to do two things slide three pointers and with the help of Q reverse the link.

78
00:04:44,860 --> 00:04:49,750
So already I have the code here for sliding, so only one step is remaining.

79
00:04:49,750 --> 00:04:51,620
That is Kucinich should point or not.

80
00:04:51,850 --> 00:04:52,980
So I will write on that code.

81
00:04:52,990 --> 00:04:59,530
Also, Kucinich, at this point on this, the extra line in here, everything was for sliding, but

82
00:04:59,530 --> 00:05:07,120
now I have added Kucinich next, as are that's all this is the procedure for reversing this by reversing

83
00:05:07,120 --> 00:05:08,520
their links.

84
00:05:09,460 --> 00:05:13,830
Let me trace this and show you really it works on this example.

85
00:05:13,840 --> 00:05:25,060
Englishtown not so I'm placing it be upon first node or get B upon first node given r null Q and r r

86
00:05:25,120 --> 00:05:29,800
null while p not equal to another Espy's not null.

87
00:05:30,610 --> 00:05:31,500
These are the steps.

88
00:05:31,960 --> 00:05:39,940
So the first three steps are for sliding Audobon Cucu upon PNB on next node Audobon.

89
00:05:39,940 --> 00:05:42,430
Q So odds against null.

90
00:05:42,940 --> 00:05:47,920
Q Upon this node then beyond next node.

91
00:05:48,610 --> 00:05:55,150
So these three steps I have performed slightly then Qs next year are so Kyuss next.

92
00:05:55,280 --> 00:05:57,770
This one is our what is our null.

93
00:05:59,310 --> 00:06:00,440
So this is the first step.

94
00:06:01,850 --> 00:06:10,430
Continue being Jessop's pointing on something and then slide them these three steps so our thumbs on

95
00:06:10,430 --> 00:06:13,100
cue cue goes on the.

96
00:06:14,500 --> 00:06:16,050
We move to Nixonland.

97
00:06:17,440 --> 00:06:25,770
Three steps forward now, Cuba's next, as are Cuba's next, as are others this morning.

98
00:06:25,810 --> 00:06:32,800
So this morning on this note here, that is two hundred will be written only that it was 250, but now

99
00:06:32,800 --> 00:06:33,700
it is 200.

100
00:06:34,540 --> 00:06:43,180
Next again, continue, please not then slide them out of one queue or upon queue, then queue up on

101
00:06:43,180 --> 00:06:43,630
P.

102
00:06:44,230 --> 00:06:45,340
Q upon P.

103
00:06:46,990 --> 00:06:57,390
Then two next to next, then Kuze next on on, so cute, next is this one, this was having 300 on this

104
00:06:57,520 --> 00:06:59,540
point on ah what is ah.

105
00:06:59,560 --> 00:07:05,320
Those to tend to turn out as it wind up one thing on this morning instead of having a student so that

106
00:07:05,320 --> 00:07:08,470
if student is written here continue be not null.

107
00:07:08,470 --> 00:07:08,710
Yes.

108
00:07:08,710 --> 00:07:09,250
Not null.

109
00:07:09,630 --> 00:07:19,720
Audubon queue are up on Q Q upon P Q upon P B's null B's next so B became null.

110
00:07:20,020 --> 00:07:25,090
Next losses step three steps forward cuz next is are so cuz next is on.

111
00:07:25,480 --> 00:07:27,890
So here what they said was null.

112
00:07:27,910 --> 00:07:29,230
So now what will we return to.

113
00:07:29,230 --> 00:07:30,430
Fifty will be done here.

114
00:07:31,630 --> 00:07:32,950
Continue being not equal.

115
00:07:32,950 --> 00:07:34,360
Do not know p became null.

116
00:07:34,360 --> 00:07:39,900
Stop so you can see that Linklaters reverse now who became for Snoad wherever Q is pointing.

117
00:07:40,090 --> 00:07:43,900
So the last step I will add first should be up on.

118
00:07:43,900 --> 00:07:46,840
Q So we should bring this first upon.

119
00:07:46,840 --> 00:07:52,720
Q This is the first node now first node this morning on this note and this is pointing on business and

120
00:07:52,720 --> 00:07:54,130
the last node is none.

121
00:07:54,520 --> 00:07:56,050
So the Linklaters reverse.

122
00:07:56,770 --> 00:07:59,860
So these are the simple steps for reversing a link list.

123
00:08:00,190 --> 00:08:05,560
I have used the concept of sliding pointers and I have reversed it.

124
00:08:06,520 --> 00:08:10,560
So with the help of three pointers, it's possible to reverse Linkous.

125
00:08:10,570 --> 00:08:14,800
Now, as I said, that we prefer modifying links for reversing a link list.

126
00:08:14,800 --> 00:08:18,010
We don't prefer movement of data in the link lists.

127
00:08:18,340 --> 00:08:20,980
We don't prefer that aid is brought in this node.

128
00:08:20,980 --> 00:08:23,480
Two hundred and two is sent in this note.

129
00:08:23,480 --> 00:08:27,100
The three that we don't want to change the contents of node.

130
00:08:27,670 --> 00:08:34,299
We want to reverse the link reason see in the link lists as per the example, for learning purposes,

131
00:08:34,299 --> 00:08:38,020
we are taking only integers, integers to whites, to whites.

132
00:08:38,020 --> 00:08:39,370
We are just taking two bytes.

133
00:08:40,400 --> 00:08:43,470
But that need not be an integer always.

134
00:08:44,000 --> 00:08:45,530
It may be a record.

135
00:08:46,430 --> 00:08:48,270
It may be having a lot of values.

136
00:08:48,290 --> 00:08:57,650
For example, I'll show you there is a node having a rule number of a student name of a student address

137
00:08:57,650 --> 00:09:00,860
of a student here of a student.

138
00:09:03,250 --> 00:09:10,390
Department of Law student, mobile phone number of a student, then there is a link, so this whole

139
00:09:10,390 --> 00:09:11,340
record is there.

140
00:09:11,710 --> 00:09:14,610
It's not just one value, like two, four, six, eight.

141
00:09:14,860 --> 00:09:16,020
This is a complete record.

142
00:09:16,180 --> 00:09:18,220
So it's a long list of records.

143
00:09:18,730 --> 00:09:22,600
Not if you say I want to bring that data here and copy.

144
00:09:23,980 --> 00:09:28,960
Now, if you say I want to swap this data and a lot of contents have to be swapped.

145
00:09:29,900 --> 00:09:36,710
In our example, there are two ways to avoid integers, but here, suppose this is two hundred bytes,

146
00:09:37,730 --> 00:09:39,380
the size of this record is 200.

147
00:09:39,380 --> 00:09:41,490
By then you will be copying 200 bytes.

148
00:09:41,960 --> 00:09:44,120
So what is preferrable copying?

149
00:09:44,120 --> 00:09:50,030
Just links are changing the pointers point how much memory they take the day to widen 16 compiler or

150
00:09:50,030 --> 00:09:52,480
four bytes and targetability compiler.

151
00:09:52,490 --> 00:09:57,770
So again, that depends on the compiler for pointer always takes same amount of memory legacy to wait,

152
00:09:57,770 --> 00:10:02,240
for example, because know what examples we are calling integer as two by four pointer also to bytes.

153
00:10:02,630 --> 00:10:07,070
So how much data you have to modify just two bytes point that we have to reverse that.

154
00:10:08,180 --> 00:10:12,080
So instead of reversing data, we prefer modifying links.

155
00:10:13,360 --> 00:10:16,090
So that's all the procedure is very simple, using sliding point.

156
00:10:16,420 --> 00:10:17,590
We have a reverse.

157
00:10:17,940 --> 00:10:25,480
Linkous Now one more method I'll show you reversing and inkless using recursion, so we'll see that.

