1
00:00:03,070 --> 00:00:09,340
Let us write a program for <PERSON><PERSON>'s algorithm already have started a project, there's a C++ project I

2
00:00:09,340 --> 00:00:10,570
have taken here.

3
00:00:10,580 --> 00:00:13,570
I have has designed for maximum integer.

4
00:00:13,810 --> 00:00:18,970
So you remember I have shown you that I is a constant that is symbolic, constant.

5
00:00:18,970 --> 00:00:20,500
I will use it as infinity.

6
00:00:20,950 --> 00:00:26,110
And this is a cosmetics corsages and cymatics of size eight by eight.

7
00:00:26,260 --> 00:00:27,700
So atras and eight columns.

8
00:00:27,700 --> 00:00:28,110
I do.

9
00:00:28,120 --> 00:00:34,450
Well I'm not using 021 zero column and the actual costs are just systematics starting from one comma,

10
00:00:34,480 --> 00:00:35,200
one onwards.

11
00:00:35,200 --> 00:00:36,370
Right from here onwards.

12
00:00:36,370 --> 00:00:42,780
If starting from skipping zeros that is zero three one zero column.

13
00:00:43,990 --> 00:00:51,580
Now next is nearly that we have already seen on white board that it will find out the vertices are near

14
00:00:51,580 --> 00:00:53,200
two which connected.

15
00:00:53,210 --> 00:00:58,480
What explains the what is are already in solution synthesisers eight again here.

16
00:00:58,480 --> 00:01:04,060
021 I will not be using I'll using from one to seven then this is for the solution.

17
00:01:04,060 --> 00:01:05,770
That is t is for spatting.

18
00:01:05,770 --> 00:01:11,710
Regenerating is apparently true Budos and seven columns that six will also be sufficient.

19
00:01:12,510 --> 00:01:13,660
I will make it six.

20
00:01:14,170 --> 00:01:15,880
Now here is the main function in this.

21
00:01:15,880 --> 00:01:17,980
I will write on Prince algorithm.

22
00:01:17,980 --> 00:01:22,210
So first of all, I will declare some variables that are required that is ejected.

23
00:01:22,390 --> 00:01:29,080
Three variables and you and the other vertices that I need and also n that is number of orders as we

24
00:01:29,080 --> 00:01:34,120
have seven vertices and min that is for finding the minimum every time.

25
00:01:34,120 --> 00:01:37,390
So I will take the value infinity to find smaller value.

26
00:01:37,390 --> 00:01:40,930
We need to have a larger value with just so I as infinity.

27
00:01:41,770 --> 00:01:45,490
Now, if any more variable required, I will come back here and declare them.

28
00:01:46,480 --> 00:01:47,800
Now let us start.

29
00:01:47,800 --> 00:01:54,730
The very first thing in the initial step of Prem's algorithm is to find the minimum cost edge from all

30
00:01:54,730 --> 00:01:55,090
this.

31
00:01:55,390 --> 00:02:00,430
So as I said, I will be taking the upper triangular part only and then find out the minimum one from

32
00:02:00,430 --> 00:02:00,700
there.

33
00:02:00,910 --> 00:02:08,350
So far that I will do for loop for assign one on what's right is less than equal to end and eight plus

34
00:02:08,350 --> 00:02:14,290
plus this will scan through rules and this J will scan through column, but it should start from eight

35
00:02:14,290 --> 00:02:21,130
onwards so that it can access only the upper triangular part then C++.

36
00:02:21,700 --> 00:02:29,950
Then every time we will find out whether a ghost of an edge i.e. Gomaa G.

37
00:02:30,100 --> 00:02:36,280
If it is less than Min then we will change this to cost of that edge.

38
00:02:36,280 --> 00:02:38,530
I Colma J.

39
00:02:39,640 --> 00:02:46,440
And also we will set you as I and V as G.

40
00:02:46,720 --> 00:02:48,760
So here we got an H.

41
00:02:49,450 --> 00:02:50,970
The minimum cost actually got.

42
00:02:51,580 --> 00:02:56,260
So these two four loops will be getting the minimum cost edge in the form of Youko movie.

43
00:02:56,260 --> 00:03:07,930
So that has to be stored in a solution that is to be of zero zero assign you and D of one first row

44
00:03:08,170 --> 00:03:11,140
and zero to column assign V.

45
00:03:11,320 --> 00:03:12,280
So he got this one.

46
00:03:13,090 --> 00:03:19,750
And also we have a call that is near and we have to update that bloody near.

47
00:03:20,050 --> 00:03:20,370
Right.

48
00:03:20,560 --> 00:03:30,970
And before that in the near Uhry at location that is you as well as V I should set them as zero because

49
00:03:30,970 --> 00:03:33,340
already we have included them in the solution.

50
00:03:33,340 --> 00:03:35,500
So these have to be declared as zero.

51
00:03:36,010 --> 00:03:38,710
I've made them initialize them, as you know.

52
00:03:38,710 --> 00:03:47,050
I have to update or initialize, I should say, nearly so far that I will use for look for assign zero

53
00:03:47,050 --> 00:03:51,700
sorry, one onwards and I is less than equal to end and eight plus plus.

54
00:03:52,420 --> 00:03:57,490
And in this one I have to check all of the words as whether they are nearer to you or nearer to me.

55
00:03:57,970 --> 00:04:00,610
I will remember we are doing the initial work.

56
00:04:00,610 --> 00:04:15,220
So if the cost of an edge from I to you, if this is less than the cost of energy from I to V, if this

57
00:04:15,220 --> 00:04:25,240
is small, first one is a small, then we will set near of EI as you, otherwise we will set near of

58
00:04:25,270 --> 00:04:30,270
EI as me and of athlete.

59
00:04:30,790 --> 00:04:38,830
So even if they are equal also it will set aside the right if you use less than invalided you, otherwise

60
00:04:38,830 --> 00:04:42,340
it will set it as v I remove this brackets.

61
00:04:44,230 --> 00:04:47,480
Now this is initialization of near Hoddy.

62
00:04:48,010 --> 00:04:48,880
Now we are ready.

63
00:04:49,960 --> 00:04:52,360
Now we have to include the rest of the edges for this.

64
00:04:52,360 --> 00:04:59,740
The procedure is repeating so far that I will take a for loop and for I start from one onwards because

65
00:04:59,740 --> 00:05:01,450
at the zero location we already have a.

66
00:05:01,740 --> 00:05:08,300
In the solution so far that we have this eye and I should be less than and minus one that just will

67
00:05:08,310 --> 00:05:10,150
be taking so this is a plus plus.

68
00:05:11,250 --> 00:05:16,370
Now we have to get the remaining set of just that is N minus one I just saw one is already done.

69
00:05:16,620 --> 00:05:19,400
So remaining are just we have to take so far this one.

70
00:05:19,410 --> 00:05:23,510
First of all, we'll find out from the library which one is minimum.

71
00:05:23,520 --> 00:05:32,290
So we have to scan through this nearly so far that I will take a loop starting with the G g assign one

72
00:05:32,290 --> 00:05:35,020
onwards because we are using the error rate from one on one.

73
00:05:35,050 --> 00:05:38,300
So let's drink to end and C++.

74
00:05:38,640 --> 00:05:42,580
So what we have to do here is we have to find out which one is minimum.

75
00:05:42,600 --> 00:05:47,640
So remember, before rating the condition, I should have minimum already having an infinity value.

76
00:05:47,650 --> 00:05:49,020
That is the largest value for that.

77
00:05:49,410 --> 00:05:58,870
So set minimum to initialize minimum to I then check here if cost of G comma near of G.

78
00:05:58,890 --> 00:05:59,230
Right.

79
00:05:59,290 --> 00:06:00,390
You remember this code.

80
00:06:00,390 --> 00:06:05,970
I have already explained you on whiteboard and just typing the code and running the program to give

81
00:06:05,970 --> 00:06:06,510
you a demo.

82
00:06:07,110 --> 00:06:08,920
And that is less than a minute.

83
00:06:09,060 --> 00:06:15,660
If so, if at all then we will set K Assange and also we will change Mintu.

84
00:06:15,960 --> 00:06:20,270
Cost of that Chickamaw near of G.

85
00:06:22,140 --> 00:06:27,750
These two things will do so that this will get the key for which we have the minimum value and one more

86
00:06:27,750 --> 00:06:28,770
thing and set the condition.

87
00:06:28,770 --> 00:06:34,490
We have to include only those whose neurology is not equal to zero.

88
00:06:34,800 --> 00:06:35,190
Right.

89
00:06:35,550 --> 00:06:40,160
So this condition is also that we should not come back to the same edge every time, though.

90
00:06:40,470 --> 00:06:42,060
What is this which are already included?

91
00:06:42,060 --> 00:06:43,390
We should not consider them.

92
00:06:43,410 --> 00:06:47,900
So this is the condition and checking the minimum one and finding the minimum.

93
00:06:47,970 --> 00:06:50,760
And so whichever is minimum that will be set in case.

94
00:06:51,840 --> 00:06:56,520
Now, once we are out of this follow, that is Cefalu, we got the answer.

95
00:06:56,520 --> 00:07:06,110
That is minimum one sorte of zero to rule and I Collum assign we have we got an edge.

96
00:07:06,150 --> 00:07:08,100
So what does that edge its key.

97
00:07:08,430 --> 00:07:08,820
Right.

98
00:07:09,090 --> 00:07:16,140
And D of first rule then I eat the column as a sign of it.

99
00:07:16,470 --> 00:07:19,430
Near of key here off key.

100
00:07:19,950 --> 00:07:26,970
So we got an edge and once this is done we have to modify in of a key and set it at zero.

101
00:07:27,060 --> 00:07:30,870
Since this what X is already included we are not going to use it again.

102
00:07:32,520 --> 00:07:34,400
Now we have to modify the neurally.

103
00:07:34,530 --> 00:07:39,900
See the first step is to find out the minimum one that is nearest one and select it, keep it in the

104
00:07:39,900 --> 00:07:41,100
solution then.

105
00:07:41,100 --> 00:07:46,500
Now update near Uhry to check whether other words are nearer to K or not.

106
00:07:46,500 --> 00:07:48,510
So for that are going to have to run a follow up.

107
00:07:48,810 --> 00:07:55,920
So this for Loop again I will use Jihae Jyothi and one on words and she is less than or equal to End

108
00:07:55,920 --> 00:07:57,180
and C++.

109
00:07:57,570 --> 00:08:00,060
And what we do here every time is check.

110
00:08:00,210 --> 00:08:08,070
If first of all one year of that G should not be equal to zero means the results which are already included.

111
00:08:08,070 --> 00:08:14,220
We should not consider them and find out whether Cast-Off G.

112
00:08:14,220 --> 00:08:24,290
Komaki as less than cost of G comma near of G.

113
00:08:25,980 --> 00:08:31,220
If so, then modify near of a G as Cayman's.

114
00:08:31,230 --> 00:08:36,260
This what exists nearer to key that we should modify all these things.

115
00:08:37,409 --> 00:08:40,919
So once this is modified, then again the procedure will repeat.

116
00:08:40,919 --> 00:08:43,350
So does the repetition for rest of the edges.

117
00:08:43,740 --> 00:08:46,190
And this is the code what we have seen on whiteboard.

118
00:08:46,500 --> 00:08:51,090
Now the remaining part is printing the solution, not let us bring the solution.

119
00:08:51,090 --> 00:08:53,370
So far this for loop I can use.

120
00:08:53,370 --> 00:08:59,100
I know because I'm outside that outer loop for assigned zero is less than and minus one.

121
00:08:59,100 --> 00:09:04,210
I just will be having a plus plus and just we have to print Soucy out.

122
00:09:04,230 --> 00:09:15,840
I will print the values first one s t of zero of I and second one is I will put the brackets here,

123
00:09:16,290 --> 00:09:18,020
I will put them inside the bracket.

124
00:09:19,230 --> 00:09:23,940
OK, so this will put the opening bracket then comma.

125
00:09:24,430 --> 00:09:28,950
This is the next thing then after this second one.

126
00:09:28,960 --> 00:09:34,260
So as an edge I'm showing it as an order sort of one comma.

127
00:09:34,290 --> 00:09:43,720
I then let us close the bracket, does the closing packet the next us and then that's all.

128
00:09:45,330 --> 00:09:48,150
So let us compile and see if there are any mistake.

129
00:09:48,150 --> 00:09:50,250
If there are any errors, I will remove the errors.

130
00:09:50,460 --> 00:09:50,720
Yeah.

131
00:09:50,730 --> 00:09:53,570
Key is not initialized and used anyway.

132
00:09:54,240 --> 00:09:57,260
Now let us on the program and see if there are anything left.

133
00:09:57,360 --> 00:09:58,260
If any mistakes.

134
00:09:58,260 --> 00:10:00,390
I will remove those errors first.

135
00:10:00,390 --> 00:10:01,380
Let us on the program.

136
00:10:04,110 --> 00:10:11,490
Yeah, there's doubt, but it is displaying one sixth, then again, one sixth, actually, it should

137
00:10:11,490 --> 00:10:16,320
be five, six, right, five, six, then four, five, five, six is coming here.

138
00:10:16,320 --> 00:10:17,790
Four or five is also coming year.

139
00:10:18,450 --> 00:10:20,670
Then three, four is OK.

140
00:10:20,670 --> 00:10:23,180
Then we have seven to two to three also.

141
00:10:23,670 --> 00:10:24,930
So there is something missed here.

142
00:10:24,930 --> 00:10:25,920
Let us look at it.

143
00:10:27,000 --> 00:10:27,660
Condition.

144
00:10:27,880 --> 00:10:28,090
Yeah.

145
00:10:28,200 --> 00:10:33,060
Here I am checking the conditions on here of jeez.

146
00:10:33,060 --> 00:10:33,900
Not equal to zero.

147
00:10:33,900 --> 00:10:34,350
Yes.

148
00:10:34,380 --> 00:10:36,510
You also oh here.

149
00:10:36,510 --> 00:10:37,680
I have missed the coalition.

150
00:10:37,980 --> 00:10:47,130
I have to rate the coalition here also while initializing the earlier I should say near of I is not

151
00:10:47,130 --> 00:10:51,480
equal to Zettl then only check you or the.

152
00:10:51,480 --> 00:10:54,570
This is the first initial addition of nearly so here.

153
00:10:54,570 --> 00:10:55,050
I missed it.

154
00:10:55,050 --> 00:10:56,550
So I have it on that condition now.

155
00:10:56,880 --> 00:11:01,140
So we know well that, that neither of our it should not be equal to zero four zero minutes.

156
00:11:01,230 --> 00:11:02,520
We have already included the word.

157
00:11:02,730 --> 00:11:04,140
We don't have to include it again.

158
00:11:04,530 --> 00:11:06,690
Let us run it and check once again.

159
00:11:08,880 --> 00:11:14,340
Yes, this is the perimeter that we have seen on the white board, that is one to six and five to six,

160
00:11:14,340 --> 00:11:18,250
then four to five, then three to four, then two to three, then seven to two.

161
00:11:18,270 --> 00:11:19,420
Yes, this is perfect.

162
00:11:20,250 --> 00:11:22,260
So that solves the previous algorithm.

163
00:11:23,460 --> 00:11:27,090
So you can type this program by yourself on your PC and you can test it.

164
00:11:27,450 --> 00:11:31,920
And so easy you can easily rewrite the code once you have done practice on it.

165
00:11:32,520 --> 00:11:39,150
And there is a PDF available after along with this video check for the resource, you will find a folder

166
00:11:39,150 --> 00:11:40,810
on the right hand side panel.

167
00:11:41,250 --> 00:11:42,210
So just click on it.

168
00:11:42,210 --> 00:11:44,750
You can find a PDF and you can download the Prius.

169
00:11:45,990 --> 00:11:48,320
So let's all try this program by yourself.

