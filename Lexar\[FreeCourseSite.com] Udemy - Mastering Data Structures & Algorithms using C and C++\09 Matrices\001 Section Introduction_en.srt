1
00:00:00,360 --> 00:00:06,720
In this section will be learning about special <PERSON>'s special Madison Square Mattocks, that is their

2
00:00:06,720 --> 00:00:10,410
dimensionless and cross end or ENVI.

3
00:00:10,410 --> 00:00:16,430
And for example, if it is five, then it is five cross five, five rows and five columns.

4
00:00:16,860 --> 00:00:20,010
So Madison Square matics does the first thing.

5
00:00:20,340 --> 00:00:23,580
And the second thing is they are having more number of zero elements.

6
00:00:24,720 --> 00:00:31,710
If there are more number of elements than we can handle them specially and only non zero elements and

7
00:00:31,710 --> 00:00:39,890
avoid storage of zero elements so that space can be saved as well as computation time can be saved.

8
00:00:40,800 --> 00:00:43,230
So we'll be learning about all these mattresses one by one.

9
00:00:43,240 --> 00:00:44,540
I'll just read all the names.

10
00:00:44,550 --> 00:00:51,480
That is a diagonal matrix, triangular, upper triangular, symmetric matrix, tri diagonal band Matrix,

11
00:00:51,480 --> 00:00:55,860
<PERSON><PERSON>litz and at will learn about spots matics.

12
00:00:56,940 --> 00:00:59,430
So let us start with Diagonal <PERSON>ocks.

