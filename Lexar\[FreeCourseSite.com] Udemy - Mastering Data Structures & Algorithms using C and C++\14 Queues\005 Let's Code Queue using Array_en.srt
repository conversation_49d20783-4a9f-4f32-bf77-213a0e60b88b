1
00:00:00,650 --> 00:00:06,890
And this video, we'll look at language program for implementation of Kuis using 2.0.

2
00:00:06,930 --> 00:00:10,190
So I will give the project name as DU.

3
00:00:11,080 --> 00:00:15,580
Using Uhry and the language is language.

4
00:00:18,460 --> 00:00:19,420
Create a project.

5
00:00:20,680 --> 00:00:27,340
Our project is really, first of all, for implementation, we need to define our structure, that is.

6
00:00:27,340 --> 00:00:35,610
Q inside this, we need to mention the data members that is size of Q And then we need a pointer that

7
00:00:35,620 --> 00:00:38,530
is front and we need a pointer that is rare.

8
00:00:39,190 --> 00:00:42,340
And also we need some space for storing the set of elements.

9
00:00:42,370 --> 00:00:43,810
I'll take a pointer for that one.

10
00:00:44,750 --> 00:00:47,720
Now, let us create a new object inside the main function.

11
00:00:47,750 --> 00:00:55,940
So here I will say struct cue and variable cue, a first of all, should be initialized for initialization

12
00:00:55,940 --> 00:00:56,450
of a cue.

13
00:00:56,460 --> 00:00:58,560
I will write on a function called create.

14
00:00:58,670 --> 00:01:06,620
So let us try to function create, which will take a structure of a cue as a pointer and it will modify

15
00:01:06,620 --> 00:01:10,200
the cue and also it should know what should be the size of a cube.

16
00:01:10,220 --> 00:01:12,440
So let us take one variable that is size.

17
00:01:14,920 --> 00:01:19,720
That it is taking two parameters, that is for initialization, it needs sites.

18
00:01:21,790 --> 00:01:28,300
So here inside function, first of all, for a cue, we will initialize the as size.

19
00:01:29,300 --> 00:01:38,000
Then we will initialise FrontPoint and also we will initialise a real pointer to minus one, then I

20
00:01:38,000 --> 00:01:41,900
should create an array inside a heap of size, a given size.

21
00:01:41,900 --> 00:01:50,410
So Kuze Q Here I will assign Uhry using my function, I will create an array.

22
00:01:51,380 --> 00:01:57,140
Offer given size, so that should be TEUs size, whatever the size of a queue is into.

23
00:01:58,450 --> 00:02:05,740
Size of integer, so that's all this will create a tube that is initialized to now from the main function,

24
00:02:05,740 --> 00:02:10,449
I should first create a queue and I should pass the address of this queue and size.

25
00:02:10,449 --> 00:02:12,280
I want to see a size five.

26
00:02:13,750 --> 00:02:20,350
So creation process over night, I should implement the functions for NQ dequeue and display for first

27
00:02:20,350 --> 00:02:21,640
function is NQ.

28
00:02:22,860 --> 00:02:27,240
For NQ, first of all, we should check whether the queue is empty so far, that the condition is,

29
00:02:27,810 --> 00:02:34,470
if rare, is equal to dusa size minus one than to the full.

30
00:02:34,830 --> 00:02:39,810
So we can simply say we cannot insert and queue as full.

31
00:02:41,890 --> 00:02:48,190
Otherwise, we can in certain elements or else we can insert an element, so for inserting an element,

32
00:02:48,190 --> 00:02:50,290
Fusari, it is incremented.

33
00:02:51,580 --> 00:02:59,590
Plus, plus, then Cuz Du gabs rare as a sign that X.

34
00:03:01,260 --> 00:03:08,340
I think it should take one more parameter that is structure no to type that is, it should take a pointer

35
00:03:08,350 --> 00:03:10,310
to I missed this one.

36
00:03:10,950 --> 00:03:15,030
So it's sticking to as a parameter as well as the element to be inserted.

37
00:03:15,930 --> 00:03:18,180
Now, let us write a function for dequeue.

38
00:03:21,990 --> 00:03:26,310
It should take a structure cue that is pointed to.

39
00:03:28,240 --> 00:03:29,420
Not here for deletion.

40
00:03:29,440 --> 00:03:30,570
I need some variables.

41
00:03:30,620 --> 00:03:36,680
I will initialize it to minus one X, then first check if it is empty.

42
00:03:36,700 --> 00:03:45,190
So if Q's front is equals to choose rare, then we will we cannot delete it.

43
00:03:46,280 --> 00:03:48,050
Q is MP.

44
00:03:50,690 --> 00:03:51,800
And solution.

45
00:03:53,360 --> 00:03:58,570
Otherwise, we can delete the elements for deletion, first of all, we should move the front pointer

46
00:03:59,090 --> 00:04:05,000
so front plus plus then in X will take the value from the three.

47
00:04:06,070 --> 00:04:09,430
Very well from this point, that is CU's front.

48
00:04:12,410 --> 00:04:13,070
That's it.

49
00:04:13,130 --> 00:04:19,220
So the value is deleted, then returned the deleted element, return X.

50
00:04:21,399 --> 00:04:23,750
Normally, you'd have a function for display.

51
00:04:23,770 --> 00:04:30,010
So here I will write display function and display function should take a structure cue.

52
00:04:33,260 --> 00:04:33,710
To.

53
00:04:35,610 --> 00:04:42,690
Then using a for loop, we can display all elements of our I values from front plus one.

54
00:04:42,690 --> 00:04:51,230
So it is CUSA front plus one, Q's front plus one and I is less than equal to Duse rare.

55
00:04:51,930 --> 00:05:02,910
And every time I plus plus and print the values printf percentile d some space and Qs Q of I.

56
00:05:03,810 --> 00:05:10,650
So from front to rear, it will display all the elements and here I should declare a variable I and

57
00:05:10,650 --> 00:05:16,040
at the end, after displaying all the elements, I should give a line Gazzo print and.

58
00:05:19,010 --> 00:05:26,690
NetSol, so I have implemented all the functions now inside the main function, let us insert a few

59
00:05:26,690 --> 00:05:29,300
elements so I will call the functions NQ.

60
00:05:30,290 --> 00:05:40,130
And in this queue, I will first insert and then I will insert 20 and then I will insert.

61
00:05:41,400 --> 00:05:48,630
Thirty, so I haven't said the three elements let us call display function display ju, it's not taking

62
00:05:48,630 --> 00:05:50,490
my pointer, it's not my address.

63
00:05:51,670 --> 00:05:52,630
Let us run this.

64
00:05:53,890 --> 00:05:56,770
Yes, the queue contains 10, 20 and 30.

65
00:05:58,050 --> 00:06:04,650
And that said, this is sufficient and even I will call delete function here, percentile the I will

66
00:06:04,650 --> 00:06:09,420
display the deleted value and all you.

67
00:06:11,330 --> 00:06:13,310
And send the address of KUB.

68
00:06:17,080 --> 00:06:18,430
CI a. deleted.

69
00:06:20,140 --> 00:06:25,160
That said, you can try all other options, you can try them and you can check whether it is displaying

70
00:06:25,160 --> 00:06:33,610
to follow them all those things, you try them, that's all due program for implementation of Q using

71
00:06:33,610 --> 00:06:34,030
Uhry.

