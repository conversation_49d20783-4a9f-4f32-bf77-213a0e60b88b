1
00:00:00,150 --> 00:00:06,190
In this video, we'll look at implementation of stock using that is stock using a.

2
00:00:07,240 --> 00:00:14,260
We have already seen that in previous video that we saw for STAC definition of stack of required data

3
00:00:14,260 --> 00:00:16,960
representation and then the operations on that data.

4
00:00:17,380 --> 00:00:19,830
So first of all, we need representation of data.

5
00:00:20,320 --> 00:00:24,370
So for implementing this tag, what are the things required then?

6
00:00:24,370 --> 00:00:27,100
You are using an early look at this.

7
00:00:27,500 --> 00:00:31,790
I need a fixed advisory, so I have to get an idea of phase five here.

8
00:00:32,020 --> 00:00:37,360
So this is the size of a UTI, so we need a fixed size area and also we should know what is the size

9
00:00:37,360 --> 00:00:37,850
of that idea.

10
00:00:37,880 --> 00:00:44,230
So I have one variable in which we can store <PERSON><PERSON>, then we need a pointer to point on the

11
00:00:44,230 --> 00:00:45,460
topmost element.

12
00:00:45,640 --> 00:00:47,750
So yes, I have one more thing that is top.

13
00:00:48,250 --> 00:00:50,710
So what should be the data type of this one integer.

14
00:00:50,710 --> 00:00:56,210
Because it is pointing on the index, which is the recently inserted element that it will be pointing.

15
00:00:56,230 --> 00:00:59,660
So that is the index in digit type, integer type.

16
00:00:59,950 --> 00:01:03,560
Then this depends what type of values you are storing in this one.

17
00:01:03,910 --> 00:01:10,360
So we need total ratings, we need an array of some size and we need the size.

18
00:01:10,690 --> 00:01:15,450
And also we need to find the three things we need for implementing a stack.

19
00:01:16,540 --> 00:01:17,380
Then one more thing.

20
00:01:17,770 --> 00:01:24,160
Inside the stack we insert and delete the demon from seeming insertion is also done from top and deletion

21
00:01:24,160 --> 00:01:28,090
is also done from we select this, we select this.

22
00:01:28,630 --> 00:01:30,520
So from the same enemy, insert and delete.

23
00:01:30,790 --> 00:01:32,920
So here an array is having two X.

24
00:01:33,900 --> 00:01:36,510
See from the side also, you can insert the cell also inside.

25
00:01:36,840 --> 00:01:39,530
What does it mean by this inserting from either side?

26
00:01:39,750 --> 00:01:43,260
So I few elements and show you support?

27
00:01:43,290 --> 00:01:47,460
I have a few elements, 10, 15 and seven.

28
00:01:48,120 --> 00:01:50,740
I have few elements that I want to insert.

29
00:01:50,760 --> 00:01:56,400
One more element that is 20 where I should insert next element here or here.

30
00:01:57,270 --> 00:02:00,000
If I'm inserting here, I should shift all these elements.

31
00:02:00,360 --> 00:02:01,710
If I want, I can insert.

32
00:02:02,010 --> 00:02:02,970
But which is better.

33
00:02:03,240 --> 00:02:04,680
Inserting this side is better.

34
00:02:05,010 --> 00:02:10,060
Inserting this side so he can say that this side, the insertion and deletions are done.

35
00:02:10,740 --> 00:02:16,020
So if you remember in one of the videos I have shown you, insertion and deviation from that side is

36
00:02:16,350 --> 00:02:17,240
Konstantine.

37
00:02:17,250 --> 00:02:18,470
It takes constant time.

38
00:02:19,530 --> 00:02:24,720
If you say that new every new element will come here only and we should shift the elements then we are

39
00:02:24,720 --> 00:02:25,860
inserting from the site.

40
00:02:25,860 --> 00:02:29,740
So it will be so butterbean so that even from the side.

41
00:02:30,300 --> 00:02:32,310
So let us quickly see what all we have learned.

42
00:02:32,730 --> 00:02:37,800
We require an array of class size and we require a top pointer and the elements will be inserted from

43
00:02:37,800 --> 00:02:39,960
this and always from this end.

44
00:02:40,710 --> 00:02:43,320
And the top will be pointing up on top most elements.

45
00:02:43,320 --> 00:02:50,160
So we'll stop this one should probably be pointing on this one topmost element then how all the operations

46
00:02:50,160 --> 00:02:50,770
are performed.

47
00:02:50,770 --> 00:02:51,870
We will learn that one.

48
00:02:52,230 --> 00:02:53,040
Then one more thing.

49
00:02:53,460 --> 00:02:59,610
When we are saying stack, we usually imagine that it is vertical, it is vertical like a can of balls

50
00:02:59,610 --> 00:03:04,380
that was vertical or green backs which are piled up one way or another.

51
00:03:04,380 --> 00:03:07,050
That is also vertical or the shunting yard.

52
00:03:07,050 --> 00:03:13,290
That is one railroad that was also vertical and in recursion that in many places we have seen the stack.

53
00:03:13,290 --> 00:03:14,510
So that was also vertical.

54
00:03:14,760 --> 00:03:20,400
So usually we imagine a stack as vertical so that I'm shooting it as vertical.

55
00:03:20,850 --> 00:03:26,100
So we will be drawing vertical, Larry, whenever we say Stack actually is a normal array only.

56
00:03:26,760 --> 00:03:27,070
Right.

57
00:03:27,420 --> 00:03:30,720
But we do to clearly see how it is working.

58
00:03:30,930 --> 00:03:32,360
This orientation is better.

59
00:03:32,370 --> 00:03:34,260
So we will be using disorientation.

60
00:03:34,260 --> 00:03:39,170
Actually itsunori simpler then based on the things required for us.

61
00:03:39,660 --> 00:03:41,670
We require three things, right.

62
00:03:42,000 --> 00:03:44,390
We require three things first, second and third.

63
00:03:44,670 --> 00:03:51,750
So for this we will combine them together and we define a structure like how we have been doing in the

64
00:03:51,750 --> 00:03:52,650
previous topics.

65
00:03:52,830 --> 00:03:55,260
So little things grouped under one name.

66
00:03:55,260 --> 00:03:56,670
We can define a structure.

67
00:03:56,970 --> 00:03:58,980
Let us define a structure for the stack.

68
00:03:59,370 --> 00:04:01,620
I will define a structure for Stracke.

69
00:04:01,620 --> 00:04:03,300
Let us call it down stack.

70
00:04:04,910 --> 00:04:07,130
Then what are the members required?

71
00:04:07,430 --> 00:04:16,209
First, I will take size integer size, then also I will take the pointer also top line.

72
00:04:16,910 --> 00:04:18,560
So there's a stop then.

73
00:04:20,110 --> 00:04:29,350
I need the money, so shall I declare some size directly or shall we decide at one time and create?

74
00:04:30,670 --> 00:04:36,670
We will decide at one time and create that and we will find out what is the maximum expected size of

75
00:04:36,670 --> 00:04:37,270
the stack.

76
00:04:37,270 --> 00:04:39,130
And we create an area of that size.

77
00:04:39,490 --> 00:04:42,120
So then better take it as a pointer.

78
00:04:42,310 --> 00:04:46,990
Don't take a fixed size so that we can dynamically create a structure.

79
00:04:47,680 --> 00:04:50,050
So what are the type of elements that I will be storing?

80
00:04:50,140 --> 00:04:51,610
Mostly for learning purposes?

81
00:04:51,610 --> 00:04:55,420
We use integer type only, so I'll use integer type Uhry.

82
00:04:55,600 --> 00:04:57,290
So for that I will declare a pointer.

83
00:04:57,610 --> 00:05:04,840
So this is integer type pointer, so I'll be using this structure wherever I'm using stack, so I'll

84
00:05:04,840 --> 00:05:05,740
follow this structure.

85
00:05:05,740 --> 00:05:13,150
Structure is having three members as the size and a strong pointer and the pointer to another that will

86
00:05:13,150 --> 00:05:14,350
be created dynamically.

87
00:05:15,010 --> 00:05:20,520
Next, let us see how to create this and initialize the stack inside main function.

88
00:05:20,530 --> 00:05:30,340
I will create the object of this that stack, so I will take a variable simply so struck stack.

89
00:05:31,990 --> 00:05:40,100
Steve, so Steve Astatke, now, once the system is created, there are three variables in this one,

90
00:05:40,370 --> 00:05:51,530
so we'll get an object that is S.T. having three things inside, that is science and top pointer and

91
00:05:51,530 --> 00:05:52,730
as is a pointer.

92
00:05:54,080 --> 00:05:55,730
Three members are dead now.

93
00:05:55,790 --> 00:06:01,970
I should know what is the size, because this is not yet created a day for storing element is not yet

94
00:06:01,970 --> 00:06:02,350
created.

95
00:06:02,360 --> 00:06:04,420
I have to create it so I should know the sites.

96
00:06:05,000 --> 00:06:08,390
So let us take the size of input from the keyboard.

97
00:06:08,390 --> 00:06:15,200
So I will right now, I will say bring this into the stack and I will scan this size value that is stored

98
00:06:15,200 --> 00:06:15,800
sites.

99
00:06:16,750 --> 00:06:22,750
So here I have written in the underside of Shugden scanners, read the value, suppose the size of the

100
00:06:22,750 --> 00:06:29,240
stack is given as five, then this size will be five, this size is five.

101
00:06:29,410 --> 00:06:36,310
So let us assume that this is five, Norvan, these all the size I should create an other offices are

102
00:06:36,310 --> 00:06:38,170
fine for storing the elements.

103
00:06:39,310 --> 00:06:41,380
I should give the money already.

104
00:06:41,380 --> 00:06:45,580
I have a pointer so let us create an array here and assign it to this point.

105
00:06:46,450 --> 00:06:53,850
So here is the code S.T. dot dots to leave this facility, this new entry.

106
00:06:54,130 --> 00:06:59,440
So create a new order from Heap Newman's heap of what size?

107
00:07:00,100 --> 00:07:01,120
A similar size.

108
00:07:01,240 --> 00:07:03,480
So this is standard size five.

109
00:07:03,910 --> 00:07:07,210
So new end of size five, size five.

110
00:07:07,450 --> 00:07:11,570
So an array is created and this address should be stored away here.

111
00:07:11,590 --> 00:07:13,800
So what is this Estie daughters.

112
00:07:14,050 --> 00:07:17,390
So start something new and store size.

113
00:07:17,390 --> 00:07:19,870
So that is created and it is a sign here.

114
00:07:20,770 --> 00:07:25,140
Then one more thing I have to do, we know that top on topmost element.

115
00:07:26,270 --> 00:07:27,590
Where is the topmost element?

116
00:07:27,620 --> 00:07:33,720
There is no topmost element, then they should point it should point out the stack.

117
00:07:33,740 --> 00:07:35,480
It means there are no elements.

118
00:07:35,480 --> 00:07:37,170
If the element comes, then it will come there.

119
00:07:37,430 --> 00:07:42,090
So where the element, first element, mickum and then very top should be before that.

120
00:07:42,320 --> 00:07:44,310
So let us take one more index here.

121
00:07:44,390 --> 00:07:48,560
This is not there in an area, just we labeling it as index minus one.

122
00:07:48,560 --> 00:07:50,600
So Dobb should be at minus one.

123
00:07:50,810 --> 00:07:53,550
So I should initialize this top as minus one.

124
00:07:54,230 --> 00:07:57,500
So we say that top is pointing here at minus one.

125
00:07:58,880 --> 00:08:04,990
So initially, cops should point at minus one, so I should write on that code so Estie, don't stop

126
00:08:05,840 --> 00:08:11,060
Steidl top assign minus one assignment minus one.

127
00:08:11,420 --> 00:08:16,390
So these are the statements that will make our stack ready with this complete structure.

128
00:08:16,400 --> 00:08:18,110
Size is their top pointer, is there?

129
00:08:18,290 --> 00:08:21,020
And an array of required sizes are also created.

130
00:08:22,550 --> 00:08:26,520
So we are using and acting as a structure, there's a structure.

131
00:08:26,990 --> 00:08:34,090
So next we will be looking at how we perform the operations on the stack where there is like this.

132
00:08:34,429 --> 00:08:36,850
So we'll be looking at this diagram every time.

133
00:08:37,100 --> 00:08:40,580
So the simple diagram, we will see that I will show you.

134
00:08:41,299 --> 00:08:44,740
So this is the structure that will be looking at every time whenever we see a stack.

135
00:08:44,750 --> 00:08:49,370
So this is an array of some size I have taken and this is size, size five and top.

136
00:08:49,370 --> 00:08:54,570
Initially it is at minus one as we go on in setting and it will be moving and pointing on the top Muslim.

137
00:08:55,460 --> 00:08:57,170
Let us look at two important things here.

138
00:08:57,470 --> 00:08:59,510
When do you say this stack is empty?

139
00:08:59,790 --> 00:09:00,960
So right now it is empty.

140
00:09:01,520 --> 00:09:05,850
So what is the commission set top as equals to minus one, then stack is empty.

141
00:09:06,110 --> 00:09:12,680
So, yes, condition for empty is if top equals two minus one, then the stack is empty.

142
00:09:13,980 --> 00:09:19,650
Then when I say that stack is full, if all the elements are there and the top is pointing on this location,

143
00:09:19,650 --> 00:09:21,010
beyond this, there is no space.

144
00:09:21,270 --> 00:09:22,440
So what is this location?

145
00:09:22,680 --> 00:09:25,290
Size minus one stack full condition.

146
00:09:25,290 --> 00:09:28,610
This one top is equal to size minus one at this location.

147
00:09:28,950 --> 00:09:30,440
So I will get on full condition.

148
00:09:31,110 --> 00:09:34,940
So top is equal to size minus one as a stack full condition.

149
00:09:35,220 --> 00:09:36,810
So we know the conditions also.

150
00:09:37,930 --> 00:09:40,480
So we'll be using these conditions very, very quiet.

151
00:09:41,320 --> 00:09:44,070
Now, next, let us look at all the operations one by one.

152
00:09:44,250 --> 00:09:46,320
I will explain how the operations perform.

153
00:09:46,320 --> 00:09:51,570
And I will also show you the program called That is the function for doing that.

