1
00:00:00,390 --> 00:00:07,410
Let us write a function for parenthesis matching, here is the expression, I have taken it as an array

2
00:00:07,410 --> 00:00:08,430
of characters.

3
00:00:08,470 --> 00:00:09,570
That is a string.

4
00:00:10,020 --> 00:00:17,460
So array of characters terminated by null character string, so expressions represented as a string.

5
00:00:18,300 --> 00:00:22,680
Next, I have a function called is <PERSON><PERSON>, so it will check whether it is balance or not.

6
00:00:22,890 --> 00:00:25,100
If it is not balance, it will return false.

7
00:00:25,110 --> 00:00:31,830
If it is balance, it will return true, then it takes the parameter that is pointed to a character

8
00:00:31,890 --> 00:00:36,080
so it can access this money that is ideal for characters it can access.

9
00:00:36,960 --> 00:00:40,580
So pointer to an array works just like a name of another.

10
00:00:41,130 --> 00:00:42,950
So I'm thinking of same name expression.

11
00:00:43,590 --> 00:00:45,450
Now let us write on the procedures.

12
00:00:45,540 --> 00:00:50,150
Not first of all, we need a stack, so I will take a stack of type array.

13
00:00:50,610 --> 00:00:53,520
So for that we had a structure, if you remember.

14
00:00:53,790 --> 00:00:55,710
So that I will take.

15
00:00:55,770 --> 00:00:59,220
So let me create a stack struct stack estie.

16
00:00:59,460 --> 00:01:01,160
So Stack is ready.

17
00:01:01,200 --> 00:01:05,040
So this variable is of type, that structure we have seen earlier.

18
00:01:05,310 --> 00:01:09,750
But one change we have to do is the URRY doctor type of an array.

19
00:01:10,080 --> 00:01:17,910
We have use integer, but here we are going to push characters that assemble opening brackets so we

20
00:01:17,910 --> 00:01:21,890
should change the data type of structure as character.

21
00:01:22,350 --> 00:01:24,210
So I will write the structure and show you.

22
00:01:25,110 --> 00:01:31,260
If you remember, the structure for stock was having Cizre to help and appointed to an integer, but

23
00:01:31,260 --> 00:01:33,330
now we have a pointer to character.

24
00:01:33,360 --> 00:01:34,830
So this is structure we'll be using.

25
00:01:34,830 --> 00:01:36,210
You have to modify the structure.

26
00:01:36,660 --> 00:01:41,010
Next, we will see how to initialize or stack how to initialize this one.

27
00:01:41,520 --> 00:01:42,430
So this is Stack.

28
00:01:42,480 --> 00:01:43,770
I have to initialize it.

29
00:01:43,770 --> 00:01:48,110
I have to set the size of the stack and also set the top pointer.

30
00:01:48,120 --> 00:01:49,010
So I will do that.

31
00:01:49,050 --> 00:01:49,940
Just watch it here.

32
00:01:50,370 --> 00:01:53,520
So I will set the size standard size.

33
00:01:53,760 --> 00:01:55,530
So what should be the size of the stack?

34
00:01:55,890 --> 00:02:00,170
It depends on number of opening brackets because we are just pushing opening brackets.

35
00:02:00,450 --> 00:02:03,050
So how do I know how many opening back there?

36
00:02:03,540 --> 00:02:09,289
So I assume that ectomorph I have opening brackets equal to the size of the expression.

37
00:02:09,870 --> 00:02:14,340
So let us take a little bigger slice of stock that is equal to the size of the expression.

38
00:02:14,340 --> 00:02:17,960
So I should know the length of that string and that should be the size.

39
00:02:18,210 --> 00:02:23,460
So I will take the size of a stack that is just as string length of this expression.

40
00:02:23,640 --> 00:02:28,530
String land is a function you can call that one and it will give you the length of a string that is

41
00:02:28,530 --> 00:02:29,140
the size.

42
00:02:29,580 --> 00:02:34,180
Now next thing I have to do is a top pointer, minus one pointer is minus one.

43
00:02:34,620 --> 00:02:43,740
Then I have to create an array of given size so dynamically I will create an array as dot as assign

44
00:02:44,340 --> 00:02:45,030
new.

45
00:02:45,870 --> 00:02:51,510
So here I have created an array of characters because as I said, we have to push characters in the

46
00:02:51,510 --> 00:02:52,800
stacks of the stack type.

47
00:02:52,840 --> 00:03:00,060
I'm taking this character stack, so array of characters created of given size, whatever the size is,

48
00:03:00,300 --> 00:03:02,600
then it's pointless pointing to us.

49
00:03:03,000 --> 00:03:05,490
So let us see the structure also inside the memory.

50
00:03:06,150 --> 00:03:07,670
If you remember, there's a structure.

51
00:03:07,980 --> 00:03:10,890
This is the structure for the first thing size we have said.

52
00:03:11,130 --> 00:03:14,280
So the size of this one is starting to sit here.

53
00:03:14,670 --> 00:03:18,780
Then 2.0 is minus one, top is minus one, then an array of size.

54
00:03:18,780 --> 00:03:19,910
The target is created.

55
00:03:20,670 --> 00:03:24,210
So this is how the structure looks like in the memory.

56
00:03:24,810 --> 00:03:27,600
But we imagine that the stack is like this.

57
00:03:28,500 --> 00:03:30,720
So for our understanding, this is sufficient.

58
00:03:31,050 --> 00:03:33,760
We don't need to see this complete structure every time.

59
00:03:33,960 --> 00:03:37,610
So these are the statements that are required for initializing that stack.

60
00:03:37,650 --> 00:03:39,890
So we need a stack and also we have to initialize it.

61
00:03:40,350 --> 00:03:44,550
I will remove these lines because I need space for writing the remaining piece of code.

62
00:03:44,910 --> 00:03:46,950
Let us like the procedure in the procedure.

63
00:03:46,950 --> 00:03:52,530
We have to scan through this expression going on each symbol at a time, starting from zero until we

64
00:03:52,530 --> 00:03:57,330
reach none so far that we can use for accessing a string.

65
00:03:57,330 --> 00:03:59,040
We do usually using for loop.

66
00:03:59,250 --> 00:04:00,670
So I will write on the following.

67
00:04:01,500 --> 00:04:07,560
So for this followable scan, for this expression, for assigning zero that starts from zero, I will

68
00:04:07,560 --> 00:04:15,060
be going on incrementing C++ until this expression of my expression of expression of pi is not equal

69
00:04:15,060 --> 00:04:17,519
to zero so that it is reaching Slessor.

70
00:04:17,529 --> 00:04:19,649
That is null character string terminator.

71
00:04:19,649 --> 00:04:20,390
It will stop.

72
00:04:20,820 --> 00:04:22,019
So that becomes equal.

73
00:04:22,019 --> 00:04:22,720
It will stop.

74
00:04:23,100 --> 00:04:25,220
So this will scan for this expression.

75
00:04:25,920 --> 00:04:31,780
Now inside this while scanning what we have to do, if it is opening bracket, push it into the stack.

76
00:04:32,310 --> 00:04:34,230
Let me write on that here.

77
00:04:34,320 --> 00:04:41,700
If expression of AI is equal to opening bracket symbolists opening bracket, then push it into the stack.

78
00:04:41,700 --> 00:04:43,770
So stack name is Estie.

79
00:04:43,770 --> 00:04:45,360
I'm sending it Bio-Reference.

80
00:04:45,360 --> 00:04:51,720
That suntanning address of a stack and also a symbol, that same symbol, whatever the symbols.

81
00:04:52,980 --> 00:05:01,830
The next effort is not opening, else it must be closing next to right for closure or else if that expression

82
00:05:01,830 --> 00:05:07,560
of why that particular symbol, wherever I is pointing, if it is closing, then what we have to do,

83
00:05:07,860 --> 00:05:08,960
we have to pop out.

84
00:05:09,210 --> 00:05:13,070
But before popping out, check whether there is something in the stack or not.

85
00:05:13,260 --> 00:05:16,280
If the strike is empty means there is no matching fund.

86
00:05:16,800 --> 00:05:23,660
If you remember, if I have opening bracket, opening bracket and closing, closing, closing was this

87
00:05:23,670 --> 00:05:24,840
this is their insight.

88
00:05:24,840 --> 00:05:25,860
This is their insight.

89
00:05:26,070 --> 00:05:29,610
Then for this one, I pop this one for second one also.

90
00:05:29,610 --> 00:05:30,720
Bob, don't know.

91
00:05:30,720 --> 00:05:35,700
I'm not closing, but the stock is empty, so I should check if the stock is empty, if the stock is

92
00:05:35,700 --> 00:05:38,030
empty, then there is no matching phone.

93
00:05:38,310 --> 00:05:39,810
So first of all, I'll check here.

94
00:05:39,990 --> 00:05:42,450
If a stock is empty, matching is not there.

95
00:05:42,450 --> 00:05:43,430
So little false.

96
00:05:43,770 --> 00:05:46,100
So here are right on the condition I read.

97
00:05:46,110 --> 00:05:50,130
Doubt if is empty if you have already done this function.

98
00:05:50,460 --> 00:05:53,070
Stephen Colbert value point of reference.

99
00:05:53,970 --> 00:05:59,560
If examined, this function will return true if the strike is empty, otherwise it will return false.

100
00:05:59,940 --> 00:06:06,420
So if it is returning true, then if true, then return false, false forward, because there is no

101
00:06:06,420 --> 00:06:09,810
matching bracket in the stack, because stack has become empty.

102
00:06:10,080 --> 00:06:12,390
Still, we need one more opening bracket.

103
00:06:12,930 --> 00:06:19,530
Dismissing that is the meaning so that conditions have become if matching their stack is not empty,

104
00:06:19,860 --> 00:06:21,990
then just pop out and continue.

105
00:06:22,380 --> 00:06:26,400
So this is about public function is taking again the address of a stack.

106
00:06:26,410 --> 00:06:28,400
So it will simply pop out a symbol, that's all.

107
00:06:28,740 --> 00:06:30,610
And we will continue will not do anything.

108
00:06:30,870 --> 00:06:32,630
So this Elzbieta closes here.

109
00:06:32,970 --> 00:06:40,200
So if it is opening bracket boschert, if it is closing bracket for out and before popping check, if

110
00:06:40,200 --> 00:06:44,340
the stack is empty, then we say we cannot continue it on false means.

111
00:06:44,340 --> 00:06:45,150
They are not matching.

112
00:06:45,750 --> 00:06:46,220
That's all.

113
00:06:46,230 --> 00:06:49,440
These are the things I have to do that solve.

114
00:06:49,560 --> 00:06:52,050
If it is opening or closing, I have to react.

115
00:06:52,200 --> 00:06:55,370
Otherwise, let it simply go on moving forward.

116
00:06:55,380 --> 00:07:00,480
So I will be moving forward and once it has reached zero, it will stop.

117
00:07:00,480 --> 00:07:02,400
So I will close this loop.

118
00:07:03,440 --> 00:07:10,250
Now, once it has came out of this whole it has finished the complete expression, not once the expression

119
00:07:10,250 --> 00:07:13,570
is completed, this should not be anything remaining in this tack.

120
00:07:13,670 --> 00:07:14,110
Right.

121
00:07:14,150 --> 00:07:15,820
So if you remember, I'll show it again.

122
00:07:16,400 --> 00:07:19,880
See, I suppose I have two opening brackets, just one closing bracket.

123
00:07:19,880 --> 00:07:20,640
So let us do it.

124
00:07:21,270 --> 00:07:22,640
Push, push.

125
00:07:22,940 --> 00:07:27,780
And for this one, Bob Dole and I have reached the end of the expression, but still staggers.

126
00:07:27,830 --> 00:07:31,960
Having one opening bracket means there is no matching symbol found.

127
00:07:32,270 --> 00:07:33,890
So Stack should be empty.

128
00:07:33,890 --> 00:07:37,270
When you have finished scanning, stacks should be empty.

129
00:07:37,550 --> 00:07:39,980
So I have to check whether the stack is empty.

130
00:07:40,130 --> 00:07:41,900
If it is not empty, then again, false.

131
00:07:41,900 --> 00:07:44,050
It's not matching otherwise true.

132
00:07:44,240 --> 00:07:45,740
So I relied on the code here.

133
00:07:46,430 --> 00:07:53,920
I believe the statement return is empty office t if the stack is empty and C for the sentiments true

134
00:07:54,580 --> 00:08:00,320
there is matching matching to otherwise return false means it's not matching.

135
00:08:00,320 --> 00:08:03,260
If something is remaining leftover in the stack then it's not match.

136
00:08:03,680 --> 00:08:04,330
So that's all.

137
00:08:04,340 --> 00:08:06,050
This is the procedure for part of this machine.

138
00:08:06,050 --> 00:08:08,930
I will write on a program and show you and inside the program.

139
00:08:08,930 --> 00:08:13,310
I need everything related to the stack because this procedure uses a stack.

140
00:08:13,970 --> 00:08:15,500
So I will write this in the program.

