1
00:00:00,150 --> 00:00:05,370
Let us consider a black a tree for the set of keys, and simultaneously I want to show you two, three,

2
00:00:05,370 --> 00:00:05,840
four, three.

3
00:00:06,210 --> 00:00:12,510
So let us insert fussin order as it is a root node, then a new lawn should be right?

4
00:00:12,550 --> 00:00:13,710
Not but this is a root.

5
00:00:13,710 --> 00:00:16,170
And also thus it will be a black delamont.

6
00:00:16,200 --> 00:00:20,040
So 10 then here again, three, four, three.

7
00:00:20,840 --> 00:00:22,830
Now next key twenty.

8
00:00:22,830 --> 00:00:24,120
Twenty is greater than this one.

9
00:00:24,120 --> 00:00:29,010
So it will come as the right child and it is in that color every new red color.

10
00:00:29,310 --> 00:00:34,500
So when the new law is inserted and if parent is black, then no problem in that black tree.

11
00:00:34,620 --> 00:00:40,650
Right then this means what it is on the right side of then in the same law.

12
00:00:40,890 --> 00:00:43,260
Superintendency in the stinky.

13
00:00:44,380 --> 00:00:44,910
Perfect.

14
00:00:45,910 --> 00:00:49,860
The next 30 today is greater than this one today.

15
00:00:50,560 --> 00:00:52,880
So these two we have instead of today and starting now.

16
00:00:53,200 --> 00:00:56,030
So today's insert here as ignored.

17
00:00:56,290 --> 00:01:03,740
Now, this is where the conflict or outright conflict, there's a new norm right under this parent.

18
00:01:03,760 --> 00:01:04,870
And this is grandparent.

19
00:01:04,879 --> 00:01:06,330
This is the uncle.

20
00:01:06,580 --> 00:01:06,930
Right.

21
00:01:07,400 --> 00:01:08,370
So parents.

22
00:01:08,650 --> 00:01:09,710
But uncle is black.

23
00:01:09,730 --> 00:01:10,420
So what to do?

24
00:01:10,660 --> 00:01:11,380
Rotate.

25
00:01:11,770 --> 00:01:12,400
I rotate.

26
00:01:12,700 --> 00:01:20,050
Whichever mission is required are rotation on the left hand side rotation or the signal rotation.

27
00:01:20,050 --> 00:01:22,400
Whatever the name you like, it can take it so hard.

28
00:01:22,400 --> 00:01:28,960
It looks like see this then will come down OK and it will become great color and don't even move up

29
00:01:29,110 --> 00:01:30,550
and it will become black color.

30
00:01:30,910 --> 00:01:32,440
And today comes here.

31
00:01:32,770 --> 00:01:34,220
That is great color.

32
00:01:34,420 --> 00:01:36,190
So these are the changes.

33
00:01:36,490 --> 00:01:37,840
So I'm not drawing a new tree.

34
00:01:37,840 --> 00:01:40,690
No, I'm making modifications to the same tree.

35
00:01:41,680 --> 00:01:44,300
Now this means is inserted here.

36
00:01:44,590 --> 00:01:50,260
So today's incident here already I have shown you that single note is having three values.

37
00:01:50,290 --> 00:01:53,560
So this is middle of this black and left side is red.

38
00:01:53,560 --> 00:01:53,860
Right.

39
00:01:54,460 --> 00:01:59,320
So this means these two reds are as a part of the same Naude same log.

40
00:01:59,780 --> 00:02:02,880
Now, next is 50.

41
00:02:03,250 --> 00:02:08,280
So where it will be inserted here, 50, right.

42
00:02:08,470 --> 00:02:10,780
Where the conflict of hatred conflict.

43
00:02:10,990 --> 00:02:17,220
So what we do here, this is the new node and this is parent and this is a grandparent.

44
00:02:17,230 --> 00:02:17,990
This is uncle.

45
00:02:18,610 --> 00:02:20,440
This is red and uncle is also red.

46
00:02:20,470 --> 00:02:21,210
So what to do.

47
00:02:21,490 --> 00:02:24,750
Change the color of a parent and uncle to black.

48
00:02:24,790 --> 00:02:26,650
So these two should become black.

49
00:02:27,190 --> 00:02:32,810
These two should become black, white and grandparents should become red.

50
00:02:33,100 --> 00:02:39,500
But as it is ruled, after making if I make red also if I make it red, also actually the root.

51
00:02:39,550 --> 00:02:42,010
So again, convert it back to black.

52
00:02:42,010 --> 00:02:44,520
If it was not root, we would have kept it red.

53
00:02:44,530 --> 00:02:48,250
Only does the result after inserting 50.

54
00:02:48,910 --> 00:02:55,450
OK, so we have this note and two new notes and 50 is a part of Turkey.

55
00:02:56,290 --> 00:02:58,790
Let us see how it happens in two, three, four, three.

56
00:02:59,140 --> 00:03:03,880
So when you want to insert 50 50 comes here, there is no space for 50.

57
00:03:03,880 --> 00:03:07,840
Then what we do, we split this node, split this more.

58
00:03:09,250 --> 00:03:09,700
Right.

59
00:03:10,030 --> 00:03:12,860
Than what we can take on the side.

60
00:03:12,880 --> 00:03:19,660
So all of these four, 10 will be on the site, then 30, 50 will be on that site, and the parent will

61
00:03:19,660 --> 00:03:22,540
contain just 20 Bennettsville contain Banty.

62
00:03:22,540 --> 00:03:27,190
So if you know about this insertion in the two, three, four, three, it's better.

63
00:03:27,610 --> 00:03:30,760
So I'll give it to you that if you have not seen it, just go and watch it.

64
00:03:31,060 --> 00:03:32,290
So splitting is done.

65
00:03:33,010 --> 00:03:33,940
We have done splitting.

66
00:03:33,940 --> 00:03:34,240
Right.

67
00:03:34,510 --> 00:03:35,990
So 50 there was no space.

68
00:03:35,990 --> 00:03:40,750
So we have split at one on this site to note that site and the study goes up.

69
00:03:41,900 --> 00:03:42,880
Check it out.

70
00:03:43,090 --> 00:03:50,960
It is a separate black Blacula is a separate note, Blacula tardies, a separate note, Blacula 50 is

71
00:03:50,960 --> 00:03:52,880
a part of same note reticular.

72
00:03:54,400 --> 00:04:01,900
This is matching the two, three, four trays, the next guy is 40, 40, gunsmith, 40 comes on the

73
00:04:02,680 --> 00:04:04,230
left shoulder, 50.

74
00:04:04,540 --> 00:04:08,050
Now, this is red right conflict and there's a new norm.

75
00:04:08,680 --> 00:04:11,410
And this is Birand and this is grandparent.

76
00:04:11,680 --> 00:04:15,550
And this is uncle and parent and uncle.

77
00:04:15,790 --> 00:04:19,300
If you check the colors, this is a red and black support form.

78
00:04:19,310 --> 00:04:21,250
Rotation, which rotation?

79
00:04:21,579 --> 00:04:25,670
This is zigzaggy rotation or our rotation.

80
00:04:25,870 --> 00:04:33,460
So what happens as we know about the rotation, 40 goes up here, 40 goes up here and 30 comes down

81
00:04:33,460 --> 00:04:42,670
as a red color, not 30 and 50 remains as right as not the site looks like.

82
00:04:43,790 --> 00:04:44,100
Right.

83
00:04:44,390 --> 00:04:51,140
So it means when you're inserting 40, where 40 should come in between these two, but it isn't between

84
00:04:51,140 --> 00:04:57,080
these two, just move 50 here and insert 40, insert 40.

85
00:04:57,470 --> 00:05:03,560
Now, what that note says that three keys in a single order means what?

86
00:05:04,760 --> 00:05:06,880
One of them is black and one is black.

87
00:05:06,890 --> 00:05:08,730
And the left side and right side of it.

88
00:05:09,080 --> 00:05:10,710
Yes, left right side of it.

89
00:05:11,540 --> 00:05:17,480
So it's completely matching with that two, three, four, three, all three guys together in one node.

90
00:05:17,630 --> 00:05:20,280
So these two that says that we are part of 40.

91
00:05:20,690 --> 00:05:22,010
So for this in the middle there.

92
00:05:23,130 --> 00:05:24,460
That's it now.

93
00:05:24,480 --> 00:05:33,640
Next, after finishing 40 letters in insert 60, 60 comes here right at conflict ravaged conflict.

94
00:05:34,020 --> 00:05:40,510
So what we did here in that conflict, this we us, because this is an unconditional surrender.

95
00:05:40,800 --> 00:05:50,760
So we color them, make them as black and make this as a fight to make this as a vote for president.

96
00:05:52,660 --> 00:05:55,140
Got it, so we won't know.

97
00:05:55,600 --> 00:05:58,030
Check this one, is there any way that conflict.

98
00:05:58,060 --> 00:05:59,080
No, this is red.

99
00:05:59,080 --> 00:05:59,480
Black.

100
00:05:59,830 --> 00:06:00,660
This is perfect.

101
00:06:01,510 --> 00:06:02,550
So I'll let you know this.

102
00:06:02,920 --> 00:06:05,200
So this is red and this is black.

103
00:06:05,200 --> 00:06:06,280
So there's no conflict.

104
00:06:06,290 --> 00:06:08,550
So this is perfect, right?

105
00:06:08,590 --> 00:06:09,460
This is balanced.

106
00:06:09,760 --> 00:06:10,720
Not what happens here.

107
00:06:10,720 --> 00:06:12,910
Legacy, but I want to insert 60.

108
00:06:12,940 --> 00:06:14,320
There is no space for 60.

109
00:06:14,470 --> 00:06:15,160
Then what to do.

110
00:06:15,400 --> 00:06:16,690
Split attack node.

111
00:06:16,930 --> 00:06:17,770
Okay, split.

112
00:06:18,070 --> 00:06:21,230
Then after splitting, what happens then.

113
00:06:21,230 --> 00:06:23,860
We have split that 40 will go up.

114
00:06:24,550 --> 00:06:29,800
Right and 50 and 60 will go as I love this one.

115
00:06:30,340 --> 00:06:30,700
Right.

116
00:06:30,700 --> 00:06:31,500
Chain of 40.

117
00:06:31,780 --> 00:06:33,630
So this is all splitting will done.

118
00:06:33,670 --> 00:06:35,010
I will veto this 30.

119
00:06:35,020 --> 00:06:36,420
I'll take some space here.

120
00:06:37,280 --> 00:06:41,830
OK, 30, c 30 and 40 and 50 were there.

121
00:06:42,040 --> 00:06:45,580
So when we want to insert 60 also there is no space so we split.

122
00:06:45,580 --> 00:06:48,040
And one key on the site to on that site.

123
00:06:48,250 --> 00:06:53,380
And actually it's not exactly middle, so the right biologist's.

124
00:06:53,440 --> 00:06:54,420
So forty goes up.

125
00:06:54,730 --> 00:07:04,810
So now what this says C first node Blacula forty is a part of that had Blacula rather than this having

126
00:07:04,810 --> 00:07:08,020
left side separate node black separate not black.

127
00:07:08,530 --> 00:07:10,080
This is separate node black.

128
00:07:10,090 --> 00:07:11,650
So this is separate but black.

129
00:07:12,100 --> 00:07:15,250
This is 50 separate node black separate node black.

130
00:07:15,550 --> 00:07:17,700
60 is about 50 60.

131
00:07:17,700 --> 00:07:21,300
The part of 50 now Netsky that is 70.

132
00:07:21,550 --> 00:07:22,810
I will insert 70.

133
00:07:22,810 --> 00:07:24,790
No 70 will come here.

134
00:07:24,940 --> 00:07:25,220
Right.

135
00:07:25,450 --> 00:07:31,350
70 will come as a right channel for 60 and it will be a regular conflict.

136
00:07:31,360 --> 00:07:38,190
So Redbeard conflict since this is right, this is where uncle is black so perform rotation.

137
00:07:38,470 --> 00:07:41,020
So fifty comes here OK.

138
00:07:41,320 --> 00:07:45,310
And sixty moves up and this becomes seventy.

139
00:07:46,270 --> 00:07:47,260
This node is gone.

140
00:07:49,170 --> 00:07:57,300
This Hartzell Kaleck rotations, zigzag rotation are rotation, so Simbi, when I'm inserting Seventy-Seven,

141
00:07:57,300 --> 00:07:58,210
they should come here.

142
00:07:58,480 --> 00:08:00,090
Know what that node says?

143
00:08:00,090 --> 00:08:01,180
There are three keys.

144
00:08:01,530 --> 00:08:06,510
Now that node belongs to 60 furphies left and 70 is right.

145
00:08:06,900 --> 00:08:09,510
So all three in the same place, 60 in the middle.

146
00:08:09,780 --> 00:08:10,390
This is red.

147
00:08:10,410 --> 00:08:10,920
This is red.

148
00:08:10,920 --> 00:08:13,830
Saying that we belong to 60 on the same tree.

149
00:08:14,280 --> 00:08:18,420
Now, next 80 80 should come on the right side of 70.

150
00:08:18,440 --> 00:08:20,420
It is inserted here now.

151
00:08:20,430 --> 00:08:20,720
Right.

152
00:08:20,740 --> 00:08:24,010
Let the conflict of interest concept uncle is also red.

153
00:08:24,300 --> 00:08:32,370
So ching this to black and black then what about grandparent should become red again.

154
00:08:32,400 --> 00:08:33,510
Red red conflict.

155
00:08:33,659 --> 00:08:34,770
Red red conflict.

156
00:08:35,070 --> 00:08:40,710
Then check uncle uncle is black so perform rotation so I will treat right.

157
00:08:40,740 --> 00:08:43,470
So you have to perform rotation over these three notes.

158
00:08:43,470 --> 00:08:44,400
So I will treat right.

159
00:08:44,730 --> 00:08:49,940
40 cousins here, 20 is on the side and 30 should be red.

160
00:08:50,190 --> 00:08:50,640
Right.

161
00:08:50,670 --> 00:08:57,660
British buried under 60 should be a red ten is on the left hand side.

162
00:08:58,290 --> 00:09:04,680
OK, and 60s right side is 70 and 60s left side is 50.

163
00:09:04,770 --> 00:09:05,390
That is black.

164
00:09:05,540 --> 00:09:07,860
That only today was left of forty.

165
00:09:07,860 --> 00:09:13,860
It should come as a right of twenty black only then it is as red.

166
00:09:13,860 --> 00:09:18,780
Only on the right hand side of seventy does the final three.

167
00:09:19,830 --> 00:09:21,390
I will show this there.

168
00:09:21,930 --> 00:09:24,510
I'll show this that not what I'm inserting.

169
00:09:24,720 --> 00:09:25,620
Eighty here.

170
00:09:25,860 --> 00:09:26,610
Eighty here.

171
00:09:26,910 --> 00:09:28,020
There is no space.

172
00:09:28,290 --> 00:09:31,050
When there is more space I have to split that one.

173
00:09:31,290 --> 00:09:32,820
So I will take this as it is.

174
00:09:32,970 --> 00:09:34,230
I'll take this as it is.

175
00:09:34,500 --> 00:09:34,890
Ten.

176
00:09:35,130 --> 00:09:36,780
OK, thirty.

177
00:09:38,040 --> 00:09:38,970
I'll split that.

178
00:09:39,480 --> 00:09:42,510
So splitting this one fifty on this site.

179
00:09:42,780 --> 00:09:43,230
Sixty.

180
00:09:43,230 --> 00:09:47,550
We will send it upwards and these are 70 and 80.

181
00:09:48,900 --> 00:09:50,370
60, I have to take it upwards.

182
00:09:50,490 --> 00:09:53,190
So 60 will go here in this node.

183
00:09:53,430 --> 00:09:54,770
Is there any space available.

184
00:09:54,780 --> 00:09:55,110
Yes.

185
00:09:55,110 --> 00:09:57,210
Sixty can comfortably fit in here.

186
00:09:57,510 --> 00:09:58,530
So sixties here.

187
00:09:58,860 --> 00:10:00,190
This is left of twenty.

188
00:10:00,240 --> 00:10:02,100
This after twenty and before forty.

189
00:10:02,340 --> 00:10:03,030
After forty.

190
00:10:03,030 --> 00:10:03,870
Before sixty.

191
00:10:04,200 --> 00:10:05,070
After sixty.

192
00:10:06,030 --> 00:10:14,340
It's look like not what this dream phase first node three D forty is black.

193
00:10:14,910 --> 00:10:15,870
Forty years black.

194
00:10:16,350 --> 00:10:19,650
Twenty and sixty are part of Sinora right now.

195
00:10:19,830 --> 00:10:21,780
Left and right and left and right.

196
00:10:21,780 --> 00:10:29,850
Right separate node then black color and Blacula all tarde 50 and 60 70.

197
00:10:29,850 --> 00:10:30,960
So this should be thirty.

198
00:10:31,170 --> 00:10:31,920
This was thirty.

199
00:10:32,460 --> 00:10:36,000
I made it forty, 30, 50, 70 today.

200
00:10:36,010 --> 00:10:37,260
50, 70 all black.

201
00:10:37,590 --> 00:10:39,770
Now 80 is along with the 70.

202
00:10:39,810 --> 00:10:42,520
So it's not exactly matching.

203
00:10:42,660 --> 00:10:47,790
So this is how I'm black trees are little two to three 40s.

204
00:10:48,060 --> 00:10:52,290
Now if you practice this once you will never forget that black trees.

205
00:10:52,440 --> 00:10:56,400
Now you don't have to memorize properties of black trees.

206
00:10:56,670 --> 00:10:58,080
You know how they are designed.

207
00:10:58,080 --> 00:11:00,570
You know the main idea behind them.

208
00:11:01,020 --> 00:11:01,470
Right?

209
00:11:01,470 --> 00:11:04,230
So you can gather the concept once again easily.

210
00:11:04,470 --> 00:11:07,820
If you are familiar with the two, three, four trees, that is sufficient.

211
00:11:07,830 --> 00:11:10,830
You can recall everything from about black blackness.

212
00:11:10,830 --> 00:11:13,800
Also not remaining is for I need you do it.

213
00:11:14,070 --> 00:11:17,700
I've shown you sufficient number of keys in session.

214
00:11:18,030 --> 00:11:20,700
Now you know how to create a black tree.

215
00:11:21,030 --> 00:11:23,120
And hard is a little fortress.

216
00:11:23,760 --> 00:11:25,820
So that's all in this video practice.

217
00:11:25,830 --> 00:11:26,930
This one by yourself.

