1
00:00:00,210 --> 00:00:04,990
In the previous whiteboard lecture, I have explained about pointed to a structure.

2
00:00:05,430 --> 00:00:07,790
So let us look at the demonstration for the scene.

3
00:00:08,550 --> 00:00:15,210
So already I have written a structure here like structure, a rectangle with length and breadth.

4
00:00:15,690 --> 00:00:18,810
Now, here I will declare a variable of type structure.

5
00:00:20,320 --> 00:00:21,010
Struck.

6
00:00:22,300 --> 00:00:23,260
Rectangle.

7
00:00:24,240 --> 00:00:31,040
Ah, and I will initialise length and breadth of it, then in five minutes, what I have done on whiteboard,

8
00:00:31,650 --> 00:00:33,330
I will try to compile a program.

9
00:00:33,430 --> 00:00:34,880
I mean, run the program.

10
00:00:34,890 --> 00:00:39,070
There is nothing to display, but it will run without any errors.

11
00:00:39,090 --> 00:00:40,250
Yes, there are no error.

12
00:00:40,260 --> 00:00:42,480
Nothing is displayed right now.

13
00:00:42,510 --> 00:00:43,560
One thing I will show you.

14
00:00:44,830 --> 00:00:48,610
This is a C++ program right inside C++.

15
00:00:49,860 --> 00:00:56,850
If you remove struck, also, it is a valid I'm using C++ compiler inside C compiler.

16
00:00:57,120 --> 00:01:00,110
You must write struct here, right?

17
00:01:00,330 --> 00:01:03,780
But in C++ you can skip struct here.

18
00:01:04,080 --> 00:01:05,690
Yes, you can write rectangle.

19
00:01:05,970 --> 00:01:06,710
Let us run.

20
00:01:07,200 --> 00:01:09,840
If there is an error then it should give an error message.

21
00:01:10,560 --> 00:01:14,120
Undefined rectangle should give a message but no error.

22
00:01:14,460 --> 00:01:18,900
There is nothing to display so nothing is printed and there is no error rate.

23
00:01:19,200 --> 00:01:28,470
So it means in C++ programs struct is not mandatory in the C programs struct as mandatory is the difference.

24
00:01:28,520 --> 00:01:30,700
Alright, now let us proceed further.

25
00:01:31,140 --> 00:01:33,790
This is the object of a rectangle, right?

26
00:01:34,110 --> 00:01:40,830
So if you want to access the length and breadth then you can access C out of are dot.

27
00:01:42,000 --> 00:01:51,320
Lent late, so it will bring Lent and this is Enel then called Artaud Bread.

28
00:01:51,900 --> 00:01:53,580
So this will print bread.

29
00:01:54,400 --> 00:01:56,590
This is a normal excess using dot operator.

30
00:01:57,000 --> 00:02:01,650
So when you have a normal variable, you access them using dot operator.

31
00:02:01,680 --> 00:02:03,050
Remember this one, right?

32
00:02:03,690 --> 00:02:05,820
No, I will take a pointer.

33
00:02:06,180 --> 00:02:07,740
So four pointer again.

34
00:02:07,770 --> 00:02:14,370
I can write just a rectangle and starboy to this.

35
00:02:14,370 --> 00:02:18,690
I will assign the address of an object of type rectangle.

36
00:02:19,470 --> 00:02:24,560
Now when you have a pointer to a structure, then dot operator cannot be used.

37
00:02:24,750 --> 00:02:27,720
You have to use addle, you have to use adle.

38
00:02:27,970 --> 00:02:32,100
Then out I will use arrow the arrow lente.

39
00:02:32,410 --> 00:02:38,870
OK then this is and then then see out the arrow Brett.

40
00:02:39,920 --> 00:02:46,810
And for this also, I will use and let us run this, it should print Lindenberg that Esten five in this

41
00:02:46,820 --> 00:02:54,920
line and 10 five in the slight C for normal variable like that is rectangular use dot operator.

42
00:02:55,250 --> 00:02:59,770
And when you have a pointer pointing to a structure, then use arrow.

43
00:03:00,410 --> 00:03:00,730
Right.

44
00:03:02,200 --> 00:03:07,510
So remember this, don't get confused, when do you start on, when to use that or just a variable,

45
00:03:07,510 --> 00:03:11,520
you start and for pointer use at the point of it.

46
00:03:11,740 --> 00:03:14,020
So you got to values 10 five and then five year.

47
00:03:14,680 --> 00:03:21,040
Then next thing, I will show you how to create an object of rectangle that is this rectangle structure

48
00:03:21,400 --> 00:03:22,220
in a heap.

49
00:03:22,570 --> 00:03:24,130
So I will remove all these things.

50
00:03:24,610 --> 00:03:27,630
OK, I will remove these things now here.

51
00:03:27,880 --> 00:03:31,570
So first of all you need a pointer and then a pointer is declared.

52
00:03:31,570 --> 00:03:37,070
It will be created inside stack right now to create an object on in a heap.

53
00:03:37,330 --> 00:03:40,450
So for that in C language you have to write down.

54
00:03:40,900 --> 00:03:47,770
Struck a rectangle and pointer call mellark function.

55
00:03:48,190 --> 00:03:52,210
Mallott function should take the size of.

56
00:03:54,180 --> 00:03:56,250
Struck a rectangle.

57
00:03:56,880 --> 00:04:01,980
So what does it mean mellark function will allocate the memory, how much memory?

58
00:04:02,490 --> 00:04:08,490
We want total bytes of memory that are consumed by this rectangle object or structure.

59
00:04:08,490 --> 00:04:13,820
So total four plus four, eight bytes light like an on white board.

60
00:04:13,830 --> 00:04:18,420
I was assuming it was taking two whites, but compiler for whites for integer.

61
00:04:18,690 --> 00:04:20,970
So total aid bytes of memory.

62
00:04:21,300 --> 00:04:27,450
So size of operator will take eight bites and Marlock it will become eight light.

63
00:04:27,700 --> 00:04:32,930
You can directly write on eight also here then mellark function returns a pointer.

64
00:04:33,360 --> 00:04:35,060
So it is of type void.

65
00:04:35,160 --> 00:04:37,990
So we should typecasts it as struct rectangle.

66
00:04:38,220 --> 00:04:42,810
So here a rectangle object will be created in a heap and the pointer will be pointing on it.

67
00:04:43,230 --> 00:04:51,270
Now using the pointer I will set the length, I will send the letters 15 and said the bread that is

68
00:04:52,080 --> 00:04:52,590
seven.

69
00:04:53,340 --> 00:04:59,060
Now you can see that I'm using Arrow Operator because I have a pointer to a rectangle structure.

70
00:04:59,070 --> 00:04:59,420
Right.

71
00:04:59,790 --> 00:05:03,130
And again, I'm printing them so I should get the values 15 and seven.

72
00:05:03,450 --> 00:05:06,540
So this is a C language and C++ syntax mixed.

73
00:05:07,290 --> 00:05:11,480
If you write primitives here and this becomes a C language program completely.

74
00:05:11,520 --> 00:05:11,880
Right.

75
00:05:12,270 --> 00:05:17,730
So if you write struct and then here print, this will become a C-code.

76
00:05:18,480 --> 00:05:20,560
She can mix C and C++ code.

77
00:05:21,090 --> 00:05:25,110
So let us run it ciego the values as 15 and seven.

78
00:05:25,110 --> 00:05:31,740
So it is printing 15 and seven so that it this is a dynamic allocation of rectangle structure.

79
00:05:32,130 --> 00:05:36,210
If you want to do it in C++, then just it is easy.

80
00:05:36,210 --> 00:05:40,570
You have to write just a new rectangle, that's all.

81
00:05:41,340 --> 00:05:44,400
You don't have to write my log size of all those things.

82
00:05:44,730 --> 00:05:51,460
Just change this line and it becomes a C++ code and it will create a rectangle, object and heap.

83
00:05:51,920 --> 00:05:52,350
All right.

84
00:05:52,360 --> 00:05:55,070
So there's a difference between C-code and C++ code.

85
00:05:55,770 --> 00:05:59,630
So we have seen what language code as well as C++ code.

86
00:06:00,300 --> 00:06:01,340
I will run this one.

87
00:06:01,770 --> 00:06:02,710
Yeah, it's working.

88
00:06:02,730 --> 00:06:04,830
I got the result as 15 and seven.

89
00:06:05,190 --> 00:06:05,750
That's it.

90
00:06:06,440 --> 00:06:10,380
See, once again, I'm telling you that I am taking a C++ project.

91
00:06:10,500 --> 00:06:17,040
So the type of project to C++ where I can't I don't see language code also C++ code also.

92
00:06:17,310 --> 00:06:19,950
So whichever one you are comfortable, you follow that.

93
00:06:19,950 --> 00:06:26,100
Given all that, if you're comfortable with printf and mellark use that, I feel comfortable with code

94
00:06:26,100 --> 00:06:28,050
or this new land.

95
00:06:28,050 --> 00:06:28,570
Use it.

96
00:06:29,040 --> 00:06:29,510
All right.

97
00:06:30,030 --> 00:06:36,060
So there's a minor difference between the style of code in C and C++ here.

98
00:06:36,420 --> 00:06:39,880
So I suggest you to practice this and see it by yourself.

99
00:06:40,380 --> 00:06:42,170
So this will be helpful for you further.

100
00:06:42,920 --> 00:06:44,100
That's all in this video.

