1
00:00:00,570 --> 00:00:08,270
Tubagus binary search trees, so in this indiscreetly, we learn about what is binary history and the

2
00:00:08,270 --> 00:00:10,490
properties of binary system.

3
00:00:11,030 --> 00:00:13,170
So here is a binary search tree.

4
00:00:13,700 --> 00:00:14,750
Let me define it.

5
00:00:14,960 --> 00:00:21,620
It is a binary tree in which for every node, all the elements and its left subtree are smaller than

6
00:00:21,620 --> 00:00:27,080
that node and all the elements in the right subtree are greater than that node.

7
00:00:27,680 --> 00:00:32,240
So if you look at this also elements in the left, there is only one element that is smaller.

8
00:00:32,360 --> 00:00:35,040
And here the element is greater for this note.

9
00:00:35,180 --> 00:00:36,860
This is smaller and this is greater.

10
00:00:37,100 --> 00:00:40,510
So this binary tree is useful for searching.

11
00:00:40,730 --> 00:00:46,470
That's why it is named as binary search tree harleysville for searching and explain.

12
00:00:46,490 --> 00:00:51,920
Suppose I am searching for a key value 40 then start from route search for 40.

13
00:00:52,460 --> 00:00:53,510
Is this 40?

14
00:00:53,510 --> 00:00:53,820
No.

15
00:00:53,900 --> 00:00:55,380
40 years greater than this one.

16
00:00:55,400 --> 00:00:57,620
So definitely 40 is on the right hand side.

17
00:00:58,070 --> 00:00:59,170
Go right hand side.

18
00:00:59,420 --> 00:01:01,230
Is it 40 or 50?

19
00:01:01,580 --> 00:01:04,379
So 40 must be on the left hand side of 50.

20
00:01:04,610 --> 00:01:07,300
Go on left hand side is anybody's phone.

21
00:01:07,700 --> 00:01:12,290
So you have comparatively few elements in a tree and form the result.

22
00:01:14,390 --> 00:01:20,390
So this is useful for searching an element and less number of competitions, so this is more like a

23
00:01:20,390 --> 00:01:21,560
binary search.

24
00:01:22,340 --> 00:01:27,010
Binary search is applied on a single array in a list of elements.

25
00:01:27,410 --> 00:01:29,780
But this is upon binary tree.

26
00:01:30,920 --> 00:01:33,280
So the behavior is similar to binary search.

27
00:01:33,470 --> 00:01:38,700
So the search team depends on the height of what we will learn about search in more detail in other

28
00:01:38,700 --> 00:01:39,200
videos.

29
00:01:39,380 --> 00:01:44,250
Now, let us learn about the properties first in binary search.

30
00:01:44,270 --> 00:01:45,950
We will not have duplicates.

31
00:01:46,340 --> 00:01:47,790
We do not have duplicates.

32
00:01:48,440 --> 00:01:54,290
Now, the second one, if we take in order traversal of binary search team, we get the list of elements

33
00:01:54,290 --> 00:01:56,320
in sorted order.

34
00:01:56,780 --> 00:01:57,580
Let us check.

35
00:01:57,920 --> 00:02:03,350
I will perform in order traversal of this one start from root.

36
00:02:04,530 --> 00:02:05,980
Move around the boundary.

37
00:02:06,900 --> 00:02:08,789
Yes, we have reached the bottom, Ben.

38
00:02:10,180 --> 00:02:10,570
Then.

39
00:02:11,810 --> 00:02:12,500
Tuffin.

40
00:02:15,130 --> 00:02:15,640
Grindy.

41
00:02:18,970 --> 00:02:19,450
Kourtney.

42
00:02:23,050 --> 00:02:23,620
40.

43
00:02:25,960 --> 00:02:26,530
50.

44
00:02:29,200 --> 00:02:29,890
16.

45
00:02:32,300 --> 00:02:40,460
OK, so if you got the elements in the sorted order, so in order gets sorted out in order to get started

46
00:02:40,460 --> 00:02:43,330
on it, then took property.

47
00:02:44,060 --> 00:02:51,830
If we have some N, then how many different binary searches can be created for the same set of nodes.

48
00:02:52,310 --> 00:02:54,950
So no binary searches for nodes.

49
00:02:55,310 --> 00:03:00,910
So the code and the last one is number of binary searches for N if and nodes are given.

50
00:03:01,070 --> 00:03:05,600
So they move this and show you four and knows how many searches are possible.

51
00:03:06,350 --> 00:03:09,290
See here I have three nodes, 10, 20, 30.

52
00:03:09,800 --> 00:03:15,790
Then I want to generate binary search from those three and also what are the different shapes possible.

53
00:03:16,430 --> 00:03:22,370
So we know very well that if we take the order of binary search tree, we should get the elements in

54
00:03:22,370 --> 00:03:24,360
sorted out the same order.

55
00:03:24,770 --> 00:03:26,460
So all this should be sorted order.

56
00:03:27,050 --> 00:03:31,330
So if you remember for the same preorder, how many trees are possible.

57
00:03:31,340 --> 00:03:32,750
We saw it one of the video.

58
00:03:33,790 --> 00:03:35,080
Not for the same in order.

59
00:03:35,110 --> 00:03:36,320
How many trees are possible?

60
00:03:37,360 --> 00:03:39,130
I will draw them, then we will see.

61
00:03:39,740 --> 00:03:42,460
Yes, five trees are possible.

62
00:03:44,130 --> 00:03:47,290
For the three notes of five, different shapes are possible.

63
00:03:47,310 --> 00:03:53,010
So I have taken five shapes now in each shape, the elements are filled such that if you take that in

64
00:03:53,010 --> 00:03:57,450
order, so you get 10, 20, 30 for all these trees.

65
00:03:57,450 --> 00:04:02,720
If I change the arrangements of these elements, I will not get the same in order.

66
00:04:02,970 --> 00:04:10,660
So each shape, each shape can be filled only in one way to get the same in order.

67
00:04:11,370 --> 00:04:18,450
So you already know the formula for three one five trees are done for and knows how many trees can be

68
00:04:18,450 --> 00:04:19,010
generated.

69
00:04:19,470 --> 00:04:24,990
So two and C and by and plus one piece can be generated.

70
00:04:28,580 --> 00:04:29,420
Catalog number.

71
00:04:30,750 --> 00:04:37,210
So if animals are given various shapes by night, which can be generated and that number is 20 and one

72
00:04:37,290 --> 00:04:43,140
plus one and one more point, we observe again, I repeat the point in one shape, the key elements

73
00:04:43,140 --> 00:04:46,290
can be filled only in one way, one way.

74
00:04:46,560 --> 00:04:50,830
If I write a 20 year, 10 year, I will not get the same in order.

75
00:04:51,510 --> 00:05:00,960
Then let us see how we can represent this binary binary search trees represented using linked representation,

76
00:05:01,360 --> 00:05:02,630
linked representation.

77
00:05:02,850 --> 00:05:04,560
If you want, you can use also.

78
00:05:04,590 --> 00:05:08,040
But mostly this is done using linked representation.

79
00:05:08,430 --> 00:05:10,710
So I will draw a link representation for this one.

80
00:05:10,740 --> 00:05:16,360
So this is a link representation of this binary search tree so that for introduction to by the research

81
00:05:16,360 --> 00:05:18,920
team, we have also learned about its properties.

82
00:05:19,290 --> 00:05:22,050
Now next, we will learn how to search in a binary search.

