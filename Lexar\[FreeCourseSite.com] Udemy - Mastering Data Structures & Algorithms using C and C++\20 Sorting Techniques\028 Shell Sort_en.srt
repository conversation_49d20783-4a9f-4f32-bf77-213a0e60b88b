1
00:00:07,830 --> 00:00:09,570
So a next against Shell shall.

2
00:00:10,760 --> 00:00:18,300
This is not as useful for sorting very large size list, name of disorders upon the name of a person

3
00:00:18,300 --> 00:00:19,740
who has introduced it.

4
00:00:21,150 --> 00:00:28,170
This site is an extension of insertions means it follows the idea of insertion sought only.

5
00:00:29,700 --> 00:00:31,920
So basically, this insertion site.

6
00:00:33,350 --> 00:00:35,830
Why inflation, so it is used in Shell.

7
00:00:36,190 --> 00:00:39,340
Let us understand that one seed for exploding.

8
00:00:39,350 --> 00:00:44,780
I have taken an array of elements and all these elements are already Sakic.

9
00:00:45,350 --> 00:00:52,430
If I try to sort this list of element using insertion sort, then how much time it takes it takes to

10
00:00:52,430 --> 00:00:54,160
just order off and time.

11
00:00:54,410 --> 00:00:56,480
We have already started this way.

12
00:00:56,960 --> 00:01:02,090
It will start inserting the element from right hand side to the left hand side sorted list one by one.

13
00:01:02,510 --> 00:01:05,670
So it will start from the second try to insert five on this side.

14
00:01:06,050 --> 00:01:10,070
So this is the right place for fine and it will stay here only eight.

15
00:01:10,070 --> 00:01:11,080
It will try to insert.

16
00:01:11,090 --> 00:01:14,650
So it will be compared with five and eight stays here only because it is good.

17
00:01:14,810 --> 00:01:18,700
The five 9/11 is compared with the evidence, so it stays here only.

18
00:01:18,710 --> 00:01:21,950
Likewise, every element is compared with just one element.

19
00:01:22,190 --> 00:01:23,540
It will not shift in the element.

20
00:01:23,540 --> 00:01:25,670
Every element will stay in its own place.

21
00:01:25,680 --> 00:01:31,100
So that's also so total outdraw and comparisons are required if the list is already sorted.

22
00:01:32,270 --> 00:01:38,030
So this is an important feature of some sort and that's why we call this in such as adoptive.

23
00:01:39,030 --> 00:01:42,510
No, I will make one change in this one and I'll explain you something.

24
00:01:43,470 --> 00:01:46,130
I will write on 16 here and 11 here.

25
00:01:46,140 --> 00:01:47,850
I have just interchange two elements.

26
00:01:47,850 --> 00:01:53,630
So the rest of the element seems to be sorted only because of 16 11 list is not forget.

27
00:01:54,360 --> 00:01:57,990
No, let us see the behavior of insertions and how it works.

28
00:01:58,810 --> 00:02:00,090
It will start from here.

29
00:02:00,090 --> 00:02:00,840
Insert swordfights.

30
00:02:00,840 --> 00:02:03,480
So fighting means there are only eight that is greater than five.

31
00:02:03,480 --> 00:02:06,210
So it will remain here only 16 greater than eight.

32
00:02:06,210 --> 00:02:07,750
So remain here only tudy.

33
00:02:07,770 --> 00:02:08,430
These are smaller.

34
00:02:08,440 --> 00:02:10,550
So 13 is shifted here.

35
00:02:11,310 --> 00:02:16,340
So 16 comes here, 13 goes there, then begin to leave this compared with this one.

36
00:02:16,350 --> 00:02:17,130
So this is great.

37
00:02:17,130 --> 00:02:23,790
I thought it is better to stay there only then the next and 11 11 is smaller than this one.

38
00:02:23,800 --> 00:02:28,340
So 16 comes here, 11 goes there and 11 is a still smaller than this one.

39
00:02:28,350 --> 00:02:33,450
So everyone comes here and 13 goes there, then 11 compared to the date, nothing will happen.

40
00:02:33,450 --> 00:02:36,630
18 the last element it is compared with sixteen.

41
00:02:36,960 --> 00:02:38,360
Nothing will happen, much of it.

42
00:02:38,940 --> 00:02:42,960
So just a few elements are shifted and the elements are sorted.

43
00:02:43,590 --> 00:02:49,530
So if that is not sorted due to fewer elements, then it will take very lesser time in inflation.

44
00:02:49,530 --> 00:02:56,150
So to arrange them in proper places, it requires very few shifting of elements.

45
00:02:56,940 --> 00:02:59,700
This is the idea used by Shell Swap.

46
00:03:00,790 --> 00:03:08,920
So let us understand how it works, so for exponential thought, I have taken an example list of elements

47
00:03:08,920 --> 00:03:11,790
that are leverne elements in this study.

48
00:03:12,430 --> 00:03:20,170
Now let us see how shell shock works, like an insertion for the elements are inserted one after another

49
00:03:20,500 --> 00:03:23,830
consecutively, that neighboring elements are inserted.

50
00:03:24,340 --> 00:03:30,110
But in shallow thought, the elements are inserted what they are distance apart.

51
00:03:30,130 --> 00:03:32,360
So there is a gap in between the elements.

52
00:03:32,800 --> 00:03:38,670
So let us see what this gap C gap is defined with a number of elements by two.

53
00:03:39,070 --> 00:03:41,270
And we will take a floor dividing here.

54
00:03:41,300 --> 00:03:42,690
The number of elements are 11.

55
00:03:43,060 --> 00:03:45,520
So Gap is 11 by two.

56
00:03:48,020 --> 00:03:51,800
Florida gives five, so initially Gap is five.

57
00:03:53,380 --> 00:03:58,930
So we will sort the elements, not the conservative elements, but the elements with a gap of five or

58
00:03:58,930 --> 00:04:04,750
distance of five, how I will show you at this gap we will again divide it by two.

59
00:04:05,470 --> 00:04:08,910
People from the insertion thought with that gap.

60
00:04:09,130 --> 00:04:12,610
So we'll go on reducing the gap until gap becomes one.

61
00:04:13,900 --> 00:04:15,310
So let us see, what is it?

62
00:04:16,029 --> 00:04:21,730
So let us see how we perform intuition sort in between the elements that the gap of five.

63
00:04:22,270 --> 00:04:29,860
So let perform this one initial element of this fun first element is this one now, which is the element

64
00:04:29,860 --> 00:04:31,020
with the gap of five.

65
00:04:31,030 --> 00:04:33,160
One, two, three, four, five.

66
00:04:34,610 --> 00:04:37,700
So just let us take these two elements, nothing else.

67
00:04:37,880 --> 00:04:43,760
So assume that there are only two elements in a list, the insertion side, we assume that the first

68
00:04:43,760 --> 00:04:44,210
element is.

69
00:04:45,170 --> 00:04:48,450
So we have to insert this element on the left hand side.

70
00:04:48,740 --> 00:04:49,880
So compare this one.

71
00:04:49,880 --> 00:04:52,970
We are inserting six, not six is a smaller than nine.

72
00:04:53,240 --> 00:04:56,120
So shifta nine here and bring six here.

73
00:04:57,440 --> 00:05:00,350
Six is brought here and nine is here.

74
00:05:01,370 --> 00:05:06,740
Now, if you look at just these two elements and don't see anything else, only six, nine, nine,

75
00:05:06,980 --> 00:05:09,670
they are sorted first to six, then Nexus nine.

76
00:05:10,430 --> 00:05:12,690
This is the basic step and the same step.

77
00:05:12,690 --> 00:05:13,490
We will continue.

78
00:05:13,940 --> 00:05:15,400
No, I will slide this one.

79
00:05:15,710 --> 00:05:24,650
So this will point on one and this will point on one, two, three, four, five with a gap five.

80
00:05:24,660 --> 00:05:25,790
So this is on six.

81
00:05:26,820 --> 00:05:33,270
Now we consider these two elements, the survivors sort, and then insert 12 to what is greater than

82
00:05:33,270 --> 00:05:35,190
five, so it remains here only.

83
00:05:35,490 --> 00:05:42,880
So they are perfect or they are already subject just to elements of the slide to the next location.

84
00:05:43,470 --> 00:05:45,770
So one, two, three, four, five.

85
00:05:45,780 --> 00:05:48,360
Anyway, this is also moving to the next element.

86
00:05:48,360 --> 00:05:51,290
So we don't have to count every time this is moving.

87
00:05:51,300 --> 00:05:52,290
So this is also moving.

88
00:05:52,950 --> 00:05:53,980
Compare these two.

89
00:05:54,000 --> 00:05:55,710
This is all it is at 16.

90
00:05:55,710 --> 00:05:57,060
So 10 we will insert.

91
00:05:57,330 --> 00:05:58,530
So 10 is a smaller.

92
00:06:01,090 --> 00:06:05,190
Then comes here, 16 goes there, then again, slide.

93
00:06:05,410 --> 00:06:08,110
So this one, but this one.

94
00:06:09,550 --> 00:06:11,080
Eight is Sa'adat four.

95
00:06:11,110 --> 00:06:15,150
We are inserting so Ford is a small Ford comes here, it goes there.

96
00:06:17,900 --> 00:06:18,740
Ages here.

97
00:06:19,920 --> 00:06:26,490
That, again, slightness move to the next element, this is, again, five elements of a one, two,

98
00:06:26,490 --> 00:06:33,750
three, four, five, compare these to 13 and instead to to the smaller than 13 to comes here.

99
00:06:36,350 --> 00:06:38,000
Then 13 moves here.

100
00:06:39,410 --> 00:06:45,410
So two and three not slapped now before moving to the next element, I want to show you one thing.

101
00:06:45,860 --> 00:06:51,820
See, right now from today, if you see from this side, one, two, three, four, five.

102
00:06:51,830 --> 00:06:57,550
Yes, the gap is five from do you check the gap, the site, one, two, three, four.

103
00:06:57,830 --> 00:07:04,700
So there is no alignment at position five inside the four only area, inside four only so far.

104
00:07:04,700 --> 00:07:09,170
All the elements which I have moved just now for them that it was ending here.

105
00:07:09,530 --> 00:07:12,020
Right now I will move to the next Harlemite.

106
00:07:13,810 --> 00:07:14,440
This one.

107
00:07:15,630 --> 00:07:17,240
One, two, three, four, five.

108
00:07:17,280 --> 00:07:24,570
This one compared these to nine is a three year insetting, so three the smaller, so three comfier

109
00:07:24,570 --> 00:07:25,620
and nine goes there.

110
00:07:27,420 --> 00:07:29,960
Three comes here, mangoes here.

111
00:07:31,540 --> 00:07:37,660
Then from here, if you start one, two, three, four, five, from here, if you start one, two,

112
00:07:37,660 --> 00:07:40,540
three, four, five, oh, this is coming as the fifth element.

113
00:07:40,960 --> 00:07:44,520
See, this should also be included, right?

114
00:07:44,860 --> 00:07:49,360
See, with a gap of five, we have three elements, not just two elements.

115
00:07:49,450 --> 00:07:50,520
Three elements are there.

116
00:07:50,770 --> 00:07:56,370
So actually we should insert three by comparing these three elements.

117
00:07:56,380 --> 00:07:58,030
It was here, so it came here.

118
00:07:58,030 --> 00:07:59,740
So I should include all three elements.

119
00:08:00,010 --> 00:08:02,380
So consider three elements as one list.

120
00:08:02,650 --> 00:08:07,210
So from here I brought the three here, then still can compare with this one.

121
00:08:07,210 --> 00:08:08,920
So trees are still smaller than this.

122
00:08:09,190 --> 00:08:12,550
So three comes at this place and six goes here.

123
00:08:14,420 --> 00:08:21,580
So that's all the last index we have reached, the end of the end of a list, so stop, we have played

124
00:08:21,590 --> 00:08:27,410
the performance of some sort between two or three elements as a list with a gap of five.

125
00:08:27,680 --> 00:08:33,559
So what we have done in this one is just we have scanned through the list once filled, slapping Sadan,

126
00:08:33,740 --> 00:08:37,150
not many slaps, a few slapping Southern feel shiftings.

127
00:08:37,490 --> 00:08:39,770
So that's what they do off Shell Psaltis.

128
00:08:40,190 --> 00:08:44,690
If you try to shuffle a few elements, then they may get it quickly sorted.

129
00:08:45,200 --> 00:08:47,330
So we have seen fastpass.

130
00:08:47,900 --> 00:08:50,660
This is FastPass with a gap of five.

131
00:08:51,320 --> 00:08:58,540
No Gap is five again perform division by five by two floor value.

132
00:08:58,670 --> 00:08:59,720
This is two point five.

133
00:08:59,720 --> 00:09:03,700
So this is to not again we have to do this with a gap of two.

134
00:09:04,820 --> 00:09:10,160
Now with the gap of two in the listed, the number of elements will be more so.

135
00:09:10,160 --> 00:09:12,610
Now I will start with the gap of two.

136
00:09:12,950 --> 00:09:18,220
This is the first element then one to this the next element, then one two.

137
00:09:18,230 --> 00:09:19,490
So this is the next element.

138
00:09:19,490 --> 00:09:22,310
One, two does the next element, one, two.

139
00:09:22,520 --> 00:09:26,060
So there's the element one to so many elements of it.

140
00:09:26,270 --> 00:09:29,810
So we have to perform insertions hard upon these elements.

141
00:09:30,140 --> 00:09:35,450
Three, ten to twelve, eight and nine.

142
00:09:36,050 --> 00:09:39,020
We have to perform start on those sort of elements.

143
00:09:39,080 --> 00:09:44,960
Non-automotive elements in the list has increased, but we will be doing upon only two elements at a

144
00:09:44,960 --> 00:09:45,280
time.

145
00:09:45,440 --> 00:09:49,100
Let me show you how we can proceed and remove these marks.

146
00:09:49,850 --> 00:09:53,980
Let us talk first element is this one one two.

147
00:09:53,990 --> 00:09:57,710
This is the next element number three with the ten.

148
00:09:57,890 --> 00:09:59,570
So ten is greater than three.

149
00:09:59,570 --> 00:10:00,980
So it is in the right place.

150
00:10:00,980 --> 00:10:01,970
So this is sorted.

151
00:10:02,810 --> 00:10:04,280
Then move to the next element.

152
00:10:05,430 --> 00:10:06,090
Five.

153
00:10:07,250 --> 00:10:14,060
Then one, two, so this one for now, four, the five four is a smaller than three, so bring four

154
00:10:14,060 --> 00:10:15,260
years and five there.

155
00:10:19,860 --> 00:10:24,730
Then some here try to move back to elements, one to know we are going out of list.

156
00:10:24,990 --> 00:10:25,870
Don't do anything.

157
00:10:26,400 --> 00:10:28,200
Continue move to the next element.

158
00:10:29,820 --> 00:10:35,490
So this comes on index two and this moves on to index of four over 10.

159
00:10:35,520 --> 00:10:38,460
So two is a smaller sort to comes here.

160
00:10:39,600 --> 00:10:40,640
And goes there.

161
00:10:42,000 --> 00:10:47,650
Then again, from here, see from here, if you see one, two, then from here, one, two, yes.

162
00:10:47,700 --> 00:10:48,860
This is also included.

163
00:10:49,050 --> 00:10:54,610
So compared to the three, so to the smaller scale, right to here and center three there.

164
00:10:55,080 --> 00:10:55,800
So that's it.

165
00:10:56,100 --> 00:11:02,130
So we have to insert the element with those elements with a gap of two as long as it is adjusted.

166
00:11:02,520 --> 00:11:05,830
So we have no more number of elements because the gap is less.

167
00:11:06,360 --> 00:11:07,270
Let us continue.

168
00:11:07,860 --> 00:11:08,750
We are here.

169
00:11:09,360 --> 00:11:13,490
So next we will move on to this and we will move to this one.

170
00:11:13,860 --> 00:11:15,540
Now, compare six with the five.

171
00:11:15,780 --> 00:11:17,520
So five is smaller, six is greater.

172
00:11:17,520 --> 00:11:20,370
So we don't have to shift the move to next.

173
00:11:21,850 --> 00:11:29,410
This this one not too well, is greater than 10, so to illustrate it is in its own position, then

174
00:11:29,410 --> 00:11:30,310
move to the next.

175
00:11:32,500 --> 00:11:38,410
Six and 16, so 16 is greater than six persons proper permission to move to next.

176
00:11:41,100 --> 00:11:44,240
Now, 12 and eight, eight is a smaller than two.

177
00:11:44,670 --> 00:11:46,130
So bring it here.

178
00:11:47,920 --> 00:11:49,180
And sent to all their.

179
00:11:51,610 --> 00:11:57,910
Now, this element should be compared again with the second element, the site, so one to compare it

180
00:11:57,910 --> 00:11:58,880
with this one also.

181
00:11:59,170 --> 00:12:00,940
So 10 and eight.

182
00:12:00,940 --> 00:12:02,410
So it is smaller than 10.

183
00:12:02,410 --> 00:12:03,730
So bring it here.

184
00:12:04,090 --> 00:12:05,280
Send it in there.

185
00:12:06,790 --> 00:12:12,480
Then again, compare this with two elements, so this one, so eight is greater than three to three

186
00:12:12,480 --> 00:12:14,480
years, smaller than eight we do don't have to shift.

187
00:12:14,680 --> 00:12:17,130
So once you get a smaller element, we can stop.

188
00:12:17,140 --> 00:12:18,280
We don't have to go further.

189
00:12:19,060 --> 00:12:21,010
So here we are here now.

190
00:12:21,010 --> 00:12:22,500
Move to the next element.

191
00:12:22,750 --> 00:12:26,890
This is on 16 and the next element here it is on 13.

192
00:12:26,890 --> 00:12:27,820
That is index nine.

193
00:12:28,210 --> 00:12:30,100
13 is a smaller than 16.

194
00:12:30,310 --> 00:12:35,130
So shift here, 13 comes here and 16 goes there.

195
00:12:35,800 --> 00:12:40,340
Then as it has shifted again, checked with the second element and this backward direction.

196
00:12:40,360 --> 00:12:44,230
So this one study is greater than six or six and smaller.

197
00:12:44,530 --> 00:12:46,510
So stop once we got a smaller element.

198
00:12:46,510 --> 00:12:46,990
Stop.

199
00:12:48,870 --> 00:12:52,670
Then I would remove this, we were basically here and this was the second element.

200
00:12:55,520 --> 00:12:57,400
Next is this one with this one.

201
00:12:59,440 --> 00:13:03,910
Twelve and nine nine is a smaller shift to nine here, central their.

202
00:13:07,990 --> 00:13:14,020
As it has shifted, then again, compared with the second element, the site, so 10 and nine, so nine

203
00:13:14,020 --> 00:13:15,760
is smaller than 10 is greater.

204
00:13:15,790 --> 00:13:18,460
So bring nine here, send it there.

205
00:13:21,540 --> 00:13:26,280
Then, as it has shifted again, compared with the second element, the site, so this is it, but this

206
00:13:26,280 --> 00:13:31,950
is mine, so it is a smaller than mine, so we don't have to continue checking at the summit.

207
00:13:32,270 --> 00:13:33,720
So this is the last element.

208
00:13:34,020 --> 00:13:35,790
I will show you all the second elements.

209
00:13:35,850 --> 00:13:37,700
One, two, and one, two.

210
00:13:37,980 --> 00:13:44,070
So if you check this two, three, then eight, then nine, then 10, then 12.

211
00:13:44,370 --> 00:13:46,510
So two, three, eight, nine, 10, 12.

212
00:13:46,650 --> 00:13:47,970
These elements have started.

213
00:13:48,170 --> 00:13:48,570
Yes.

214
00:13:48,820 --> 00:13:51,330
It's forming a single list with the gap of two to.

215
00:13:52,440 --> 00:13:54,390
So we have finished second pass.

216
00:13:54,420 --> 00:13:59,850
It was a little windy because a number of elements were more so that's all the end of the second part.

217
00:14:00,050 --> 00:14:01,310
We have finished second pass.

218
00:14:01,530 --> 00:14:06,690
So when we have finished when the marker reached here, so this we can take it as a primary one, the

219
00:14:06,870 --> 00:14:07,660
primary marker.

220
00:14:07,920 --> 00:14:11,650
So first element of all of this, one second element of all this fun.

221
00:14:11,880 --> 00:14:13,980
So actually, this is the one we have to track.

222
00:14:13,980 --> 00:14:16,800
And once it reaches this end, we should stop.

223
00:14:17,190 --> 00:14:17,480
Right.

224
00:14:17,850 --> 00:14:22,330
So now we will perform one more pass by reducing the gap.

225
00:14:22,350 --> 00:14:23,360
So what is Gap now?

226
00:14:23,370 --> 00:14:25,330
Gap is two, so gap is two.

227
00:14:25,660 --> 00:14:28,150
This is two by two and this is one.

228
00:14:28,590 --> 00:14:32,990
So now finally, this is going to be a last pass because the gap has become one.

229
00:14:33,360 --> 00:14:34,890
So first time gap was five.

230
00:14:34,900 --> 00:14:37,640
The next gap was to know next gap, this one.

231
00:14:37,920 --> 00:14:39,150
So we have to sort it.

232
00:14:39,160 --> 00:14:40,350
But one element.

233
00:14:40,350 --> 00:14:40,860
Gap.

234
00:14:41,760 --> 00:14:43,100
So I remove these marks.

235
00:14:43,540 --> 00:14:49,880
I'm going to perform this incision site with a gap of one, as the official saw this, as we have performed

236
00:14:49,880 --> 00:14:53,790
with a greater gap, all the elements are adjusted.

237
00:14:53,790 --> 00:14:55,470
Most of the elements are registered.

238
00:14:55,860 --> 00:14:58,110
Now, the remaining work is very little.

239
00:14:58,290 --> 00:14:59,940
Very few elements will be shifting.

240
00:15:00,420 --> 00:15:01,080
Let us see.

241
00:15:02,420 --> 00:15:06,820
Now in the spotlight, the gap is one, so it will be seen as thought.

242
00:15:07,160 --> 00:15:09,800
So this is the first element, is the second element.

243
00:15:09,800 --> 00:15:11,530
So I would not take to Marks.

244
00:15:11,540 --> 00:15:12,910
I will just take one mark.

245
00:15:13,100 --> 00:15:14,890
We are going to insert this one now.

246
00:15:14,900 --> 00:15:15,860
Right on which side?

247
00:15:15,860 --> 00:15:18,980
Left hand side forward is greater than two to this one.

248
00:15:19,040 --> 00:15:25,100
So it is Libbrecht then three three if you compare four is greater than three.

249
00:15:25,370 --> 00:15:26,870
So interchange.

250
00:15:26,880 --> 00:15:32,390
So three comes here for goes to see there was one shifting right now.

251
00:15:32,390 --> 00:15:35,820
Twenty five compared with the previous element with a gap of one.

252
00:15:36,140 --> 00:15:38,060
So five is greater than four.

253
00:15:38,060 --> 00:15:39,920
So it is a smaller unit to shift.

254
00:15:40,790 --> 00:15:42,250
Eight is greater than five.

255
00:15:42,260 --> 00:15:43,220
No need to shift.

256
00:15:43,880 --> 00:15:45,420
Six is a smaller than eight.

257
00:15:45,500 --> 00:15:46,620
Sort of shifted.

258
00:15:46,970 --> 00:15:51,980
So six comes here, goes here, then continue from the split.

259
00:15:51,980 --> 00:15:52,630
So stop.

260
00:15:52,700 --> 00:15:58,840
So you're going to have to shift it further than this was the actual mark move to the next element.

261
00:15:58,850 --> 00:16:00,950
Nine is greater than eight, so we don't have to shift.

262
00:16:01,490 --> 00:16:02,710
13 is greater than nine.

263
00:16:02,720 --> 00:16:07,400
So we don't have to shift ten then compared with the previous element.

264
00:16:07,410 --> 00:16:14,510
So 13 is greater than is a smaller for Britain here centered in there then still compared with the previous

265
00:16:14,510 --> 00:16:14,950
element.

266
00:16:14,960 --> 00:16:17,030
So 10 is bigger than nine.

267
00:16:17,300 --> 00:16:18,320
Nine is a smaller.

268
00:16:18,320 --> 00:16:23,570
You don't have to shift but nine now move to the next 16, try to insert 16.

269
00:16:23,570 --> 00:16:24,980
16 is greater than 13.

270
00:16:24,980 --> 00:16:29,270
No need to shift to M is a smaller than 16.

271
00:16:29,540 --> 00:16:35,930
Yes, it has to be shifted to 12 here, 16 here then still continue with the previous element to is

272
00:16:35,930 --> 00:16:37,400
a smaller than 13 also.

273
00:16:37,670 --> 00:16:44,230
So 12 comes here and 13 goes there then still compared with the previous element with one element gap.

274
00:16:44,480 --> 00:16:46,450
So 12 is greater than ten.

275
00:16:46,460 --> 00:16:47,360
Ten is a smaller.

276
00:16:47,840 --> 00:16:51,260
So until it gets adjusted, go on shifting it.

277
00:16:51,740 --> 00:16:54,170
So very few shift things we have done.

278
00:16:54,170 --> 00:16:58,100
If you observe, no element was brought in here.

279
00:16:58,190 --> 00:16:58,640
Right.

280
00:16:59,180 --> 00:17:03,290
If any element was getting shifted one or two places it was getting shifted.

281
00:17:03,500 --> 00:17:09,290
So because we have already done a few shift things in the beginning, so that has reduced large number

282
00:17:09,290 --> 00:17:12,869
of shiftings in the last past with the Gap one.

283
00:17:14,430 --> 00:17:21,900
That's all this is, shall we got the elements sorted, this was the last bus seat for this sort, definitely

284
00:17:21,900 --> 00:17:25,829
you have to work on it to take the same example and you do it by yourself.

285
00:17:26,190 --> 00:17:27,530
Then you can remember it.

286
00:17:27,930 --> 00:17:31,140
The other source, if you have watched them without working.

287
00:17:31,140 --> 00:17:32,670
Also, it's easy to remember.

288
00:17:33,090 --> 00:17:35,100
But at this sort, you have to work on it.

289
00:17:35,640 --> 00:17:38,520
Novela analysis of shell sort.

290
00:17:39,240 --> 00:17:40,920
See what we are doing in the pass.

291
00:17:40,980 --> 00:17:45,740
We are scanning through elements and limiter to shifting fuel elements.

292
00:17:45,750 --> 00:17:48,260
We are shifting just a few elements.

293
00:17:48,270 --> 00:17:50,400
We have shifted that is negligible.

294
00:17:50,790 --> 00:17:57,740
So we can just focus on scanning of elements of how many elements are there any elements we have scattered

295
00:17:58,230 --> 00:17:58,790
but some.

296
00:17:59,890 --> 00:18:06,640
So in each pass, we have skeleton elements, then how many passes we have perform, see for 11 elements

297
00:18:06,640 --> 00:18:09,940
we perform one, two, three passes, just three passes.

298
00:18:10,060 --> 00:18:14,340
How we were getting these passes, how we decided we have to perform only three passes.

299
00:18:15,100 --> 00:18:17,080
Number of elements was divided by two.

300
00:18:17,110 --> 00:18:18,790
The result was again divided by two.

301
00:18:18,790 --> 00:18:20,440
The result was again divided by two.

302
00:18:20,810 --> 00:18:22,510
When we reach one, we have stopped.

303
00:18:22,810 --> 00:18:26,530
So this was nothing but successive division by two.

304
00:18:26,890 --> 00:18:31,770
So successive division is nothing but log so that the log we have applied on.

305
00:18:31,780 --> 00:18:32,310
And now.

306
00:18:32,500 --> 00:18:35,860
So this is log and base to.

307
00:18:37,730 --> 00:18:45,120
So in each pass, we have performed scanning of all the elements and login passes we have performed.

308
00:18:45,440 --> 00:18:46,430
So this is.

309
00:18:48,270 --> 00:18:54,840
Scan a list, and this is a number of forces.

310
00:18:56,300 --> 00:19:00,740
So this is analyzed as analogue.

311
00:19:00,830 --> 00:19:06,600
And so this is the time complexity, no more on the more about analysis.

312
00:19:06,620 --> 00:19:11,990
As I said, the actual site is useful for sorting very large sites list.

313
00:19:13,200 --> 00:19:16,260
And the gaps I have taken by dividing them by two.

314
00:19:17,400 --> 00:19:21,570
Sometimes guards are taken as primary numbers.

315
00:19:22,890 --> 00:19:29,330
So here the number of elements are 11, so smaller than that, 11, the prime number is seven.

316
00:19:29,340 --> 00:19:34,560
So we can have a gap of seven, then have a gap of five, then three, then to.

317
00:19:36,320 --> 00:19:38,430
And finally, we can perform for one.

318
00:19:39,110 --> 00:19:45,440
This is also possible so you can select the gap as the prime number also, or you can select the gap

319
00:19:45,440 --> 00:19:46,680
by dividing it by two.

320
00:19:47,030 --> 00:19:49,470
So there are variations possible in this one.

321
00:19:49,670 --> 00:19:55,680
So it is also some analyzed as outdraw and power three by two.

322
00:19:55,880 --> 00:19:59,000
So this will be less then and square.

323
00:19:59,000 --> 00:20:01,040
That is mPower one point five.

324
00:20:05,150 --> 00:20:09,080
And also, it is analyzed as an over five by three.

325
00:20:10,130 --> 00:20:13,070
So this will be in power one point six six.

326
00:20:14,400 --> 00:20:20,940
So either you can send Logan or you can say and for one point five or all she can say and for one point

327
00:20:20,940 --> 00:20:23,650
six for analytical results are various.

328
00:20:23,880 --> 00:20:29,810
So the way I have explained you from there, you can clearly see that it is an log-in.

329
00:20:30,030 --> 00:20:35,190
So I have not considered shifting of elements and few more things about shock.

330
00:20:35,640 --> 00:20:40,100
See shell sorters, falling insertion sort so insertion.

331
00:20:40,110 --> 00:20:41,160
So it is adaptive.

332
00:20:41,370 --> 00:20:43,950
So shell, it is also adaptive.

333
00:20:44,250 --> 00:20:48,300
The next pointis shell, so it doesn't take any extra space.

334
00:20:48,480 --> 00:20:52,590
So it is in place within the same area we can for the elements.

335
00:20:53,530 --> 00:20:59,080
And it comes under competition because it's under competition based holdings, if you remember, there

336
00:20:59,080 --> 00:21:04,320
was only one thought that was merger thought, which was using extra space.

337
00:21:05,050 --> 00:21:07,360
Otherwise, all the sites are in place.

338
00:21:07,620 --> 00:21:10,180
So it also comes under emplace.

339
00:21:12,370 --> 00:21:14,170
So that's all about the analysis.

340
00:21:15,550 --> 00:21:19,740
I will write on a function for Shelford and I will discuss that.

