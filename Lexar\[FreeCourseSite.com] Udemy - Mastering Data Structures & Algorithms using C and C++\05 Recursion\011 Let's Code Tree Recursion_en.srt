1
00:00:00,900 --> 00:00:05,310
Let us look at three decoction, four that will create a new project.

2
00:00:07,939 --> 00:00:14,780
Project type is command line tool project name, I will give it us three.

3
00:00:18,050 --> 00:00:22,970
Recursion Next created the project.

4
00:00:24,970 --> 00:00:27,070
Your project is really an.

5
00:00:29,550 --> 00:00:34,800
Here is the main function here, I will write down a function for three recursion, the one we have

6
00:00:34,800 --> 00:00:35,730
already discussed.

7
00:00:38,620 --> 00:00:45,010
Function fund, which takes parameter and if and is greater than zero.

8
00:00:47,580 --> 00:00:49,410
Then print of the value of an.

9
00:00:57,810 --> 00:01:02,340
Next, it will call itself by passing in minus one four two times.

10
00:01:06,640 --> 00:01:09,290
Now, this function, I will call it from main

11
00:01:12,290 --> 00:01:14,390
function fun by passing voluntary.

12
00:01:17,620 --> 00:01:19,200
Let us run the program.

13
00:01:23,270 --> 00:01:26,960
Said output is three to one, one, two, one one.

14
00:01:27,260 --> 00:01:28,280
Here is the output.

15
00:01:30,850 --> 00:01:36,910
Three, two, one, one, two, one, one, so the same output we got now let us debug this program

16
00:01:36,910 --> 00:01:41,360
and check how these functions are called and how this output is generated.

17
00:01:42,190 --> 00:01:43,750
So I'll put a break point here.

18
00:01:46,200 --> 00:01:47,580
And I will run the program.

19
00:01:49,920 --> 00:01:56,230
So debuggers stops here at the break point, at the beginning point of the function, call inside the

20
00:01:56,430 --> 00:01:58,550
navigator, we can see just mean function.

21
00:01:58,950 --> 00:02:03,000
Now here we will observe how the function calls are created and destroyed.

22
00:02:03,660 --> 00:02:10,500
You can better watch in this watch window how these function calls are created and how the value of

23
00:02:10,500 --> 00:02:11,470
and are changing.

24
00:02:12,420 --> 00:02:13,800
So let me continue.

25
00:02:16,160 --> 00:02:23,210
The first function call, you can see here that ancestry and said watch ancestry.

26
00:02:27,210 --> 00:02:30,870
But in depth, it will paint the tree that it will call itself for to.

27
00:02:34,300 --> 00:02:39,790
Now this time and as two, and there are two function calls here in the debug navigation.

28
00:02:43,270 --> 00:02:46,540
Bring have to then call itself or function one.

29
00:02:47,900 --> 00:02:54,170
Then printf London College sell for zero, this time we will not execute so total four calls, this

30
00:02:54,170 --> 00:02:58,250
is for three to one and zero from the bottom if you check.

31
00:03:01,330 --> 00:03:06,880
Don't go on these numbers, these are actually the ordering that is zero to call, first call, second

32
00:03:06,880 --> 00:03:07,120
call.

33
00:03:07,120 --> 00:03:15,550
So on this not the values of this function ends and it will go back to previous call with the value

34
00:03:15,550 --> 00:03:15,880
of.

35
00:03:17,660 --> 00:03:21,020
And was one then it will make a second call.

36
00:03:21,050 --> 00:03:23,000
Now you can see that it is on the second line.

37
00:03:23,000 --> 00:03:28,910
The second line is highlighted with green color is going to execute the second call, not the value

38
00:03:28,910 --> 00:03:30,090
of an one.

39
00:03:30,650 --> 00:03:32,270
So, again, one minus one zero.

40
00:03:35,130 --> 00:03:37,090
Yes, the function call is zero.

41
00:03:37,110 --> 00:03:40,080
Here you can see in the wash window value is zero.

42
00:03:41,520 --> 00:03:48,440
And again, the function calls are for this function will not enter into if it will come out, then

43
00:03:48,450 --> 00:03:55,180
it will go back to function, call for one, then back on to Nsofor to first call has finished.

44
00:03:55,210 --> 00:03:58,020
Now it is going to make a second call for one.

45
00:04:00,580 --> 00:04:01,480
And values to.

46
00:04:04,940 --> 00:04:07,470
I, for one, want to spend it once again.

47
00:04:07,490 --> 00:04:08,200
So here we go.

48
00:04:08,510 --> 00:04:08,770
Put.

49
00:04:12,440 --> 00:04:16,350
Then four zero again, zero two calls are over.

50
00:04:17,600 --> 00:04:18,529
Now back to.

51
00:04:20,570 --> 00:04:27,140
And back to three now for three, even the end was a three, this first call has finished.

52
00:04:27,230 --> 00:04:31,970
The second quarter will be done, so it will be three minus one to see the same thing.

53
00:04:31,970 --> 00:04:35,180
Just how we got to one one same way we get to one run.

54
00:04:35,960 --> 00:04:41,140
Now, you can see here inside the debug navigation that is main function and only one function is there

55
00:04:41,500 --> 00:04:45,830
that the value of another three margin, it will expand.

56
00:04:45,830 --> 00:04:47,090
It will be ascending.

57
00:04:51,270 --> 00:04:57,960
But in order to call for one print, one call for zero again, you can see that here full function calls.

58
00:04:58,060 --> 00:05:03,720
I mean, this ends and zero also earns.

59
00:05:06,530 --> 00:05:07,550
One splinted.

60
00:05:10,130 --> 00:05:12,590
Zero score, zero Terminix.

61
00:05:14,490 --> 00:05:22,500
To Germany and Britain, its return back to main function, so the things to observe here are how many

62
00:05:22,770 --> 00:05:28,530
factories and records are created inside the stack, if you will count total calls, total calls, AR

63
00:05:28,530 --> 00:05:32,340
15, but at most at a time, maximum four calls for that.

64
00:05:34,140 --> 00:05:36,210
So that's all about the trade recursion.

