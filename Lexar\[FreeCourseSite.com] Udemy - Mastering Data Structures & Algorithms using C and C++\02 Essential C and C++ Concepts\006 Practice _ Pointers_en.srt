1
00:00:00,240 --> 00:00:04,080
And this lecture, I will give the demonstration four pointers.

2
00:00:04,350 --> 00:00:12,030
So what I have shown you on the whiteboard lecture, let us look at the demonstration for it, for demonstration.

3
00:00:12,060 --> 00:00:14,180
I will go to online GDP.

4
00:00:14,790 --> 00:00:22,680
So here in Google search for online GDB, CBD compilers are available.

5
00:00:22,690 --> 00:00:24,900
So let us select C++ in this.

6
00:00:24,900 --> 00:00:27,910
You can write both C and C++ programs.

7
00:00:28,770 --> 00:00:29,170
Yeah.

8
00:00:30,180 --> 00:00:34,920
So let us remove this one pointer like I have shown you.

9
00:00:35,610 --> 00:00:38,220
A variable is declared here on whiteboard.

10
00:00:38,220 --> 00:00:40,110
I have shown this the same thing I will do.

11
00:00:40,650 --> 00:00:47,250
This variable is of type integer and if you want to print a value so I can say S.O.P and the value of

12
00:00:47,250 --> 00:00:48,270
it will be printed.

13
00:00:49,050 --> 00:00:51,630
So this is the integer variable having value leupen.

14
00:00:52,260 --> 00:00:53,810
Yeah, it has printed the value.

15
00:00:54,630 --> 00:00:59,670
So this is a variable it consumes for whites in the memory, not to this variable.

16
00:00:59,680 --> 00:01:02,390
We can also assign a pointer.

17
00:01:02,610 --> 00:01:10,450
So I have taken the pointer starboy so integer a sinden and start by the pointer.

18
00:01:10,680 --> 00:01:13,440
So this statement is a declaration of a pointer.

19
00:01:14,050 --> 00:01:21,510
Then later on in the next line I can see a sign address of a ampersand name and address of the address

20
00:01:21,510 --> 00:01:26,480
of A the stored in B that is a pointer so that we are assigning the address.

21
00:01:26,490 --> 00:01:32,040
We don't have the right star, we have the right star or asterisked only at the time of declaration.

22
00:01:32,340 --> 00:01:36,350
And the next time we have to do is at the time of the referencing.

23
00:01:36,570 --> 00:01:40,680
So should we use a declaration and the reference in time here?

24
00:01:40,680 --> 00:01:47,820
I'm storing the address of a invariable P, so be the point that a variable so just be you have to write

25
00:01:48,060 --> 00:01:48,810
the syntax.

26
00:01:49,020 --> 00:01:54,740
Syntax means if you write like this compiler will understand right now, don't say that.

27
00:01:54,750 --> 00:01:56,520
So if I write started, what will happen.

28
00:01:56,520 --> 00:01:58,110
Compiler will not understand it.

29
00:01:58,470 --> 00:01:58,650
Right.

30
00:01:58,710 --> 00:02:01,650
If you write start again here compiler will not understand.

31
00:02:01,650 --> 00:02:03,810
You may get some errors so you can try out.

32
00:02:03,810 --> 00:02:07,400
If you are getting some ideas you can experiment them by yourself.

33
00:02:07,560 --> 00:02:08,900
So I have a pointer.

34
00:02:09,150 --> 00:02:14,340
Now let us print the value of a with the help of Pointer B..

35
00:02:14,670 --> 00:02:20,520
So now PS having a less of a solid as a defense it so for that I will use printf.

36
00:02:21,030 --> 00:02:30,540
OK, so just I want to show you that I can use C-code as well as C++ code right as geodetic now of the

37
00:02:30,540 --> 00:02:40,920
printing value of it here I will say print f this is using pointer, I will print percentile the value

38
00:02:40,920 --> 00:02:41,840
of it.

39
00:02:42,750 --> 00:02:46,230
So actually I should give Starboy here.

40
00:02:46,560 --> 00:02:47,040
Yes.

41
00:02:47,040 --> 00:02:52,830
Starboy defensing just Beemans Appointer which is having Atrous start Beemans.

42
00:02:52,830 --> 00:02:57,260
It will go to that address and take the value so that value will be printed.

43
00:02:57,600 --> 00:03:01,320
So let us run the program and see you then.

44
00:03:01,760 --> 00:03:07,680
Then after that the next line is using pointer and so it is coming in the same line.

45
00:03:08,010 --> 00:03:11,510
If I write and then here then it will come in next line.

46
00:03:12,150 --> 00:03:16,260
OK, so if I write until after A it will come in next line.

47
00:03:16,530 --> 00:03:17,810
So it will bring the value then.

48
00:03:17,830 --> 00:03:18,210
Yes.

49
00:03:18,210 --> 00:03:21,600
Using pointer then I if I don't write start here.

50
00:03:21,600 --> 00:03:25,350
If I write just stop just B then what happens.

51
00:03:26,010 --> 00:03:27,480
So let us see what happens.

52
00:03:27,990 --> 00:03:34,270
Yeah it is printing but it is not printing the data, it is spending some because Baeza Point-to-point

53
00:03:34,270 --> 00:03:39,720
that is having some interest, it is giving some warning because we have used percentile D and printing

54
00:03:39,930 --> 00:03:40,920
in digit appointer.

55
00:03:41,280 --> 00:03:42,530
She can ignore this warning.

56
00:03:42,840 --> 00:03:44,490
So it is printing address.

57
00:03:44,520 --> 00:03:44,930
Right.

58
00:03:45,210 --> 00:03:52,280
So there's do one thing along with this I will say percentile for ampersand.

59
00:03:52,290 --> 00:03:57,420
I also see first one is a pointer, second one is an address of a book.

60
00:03:57,430 --> 00:04:04,470
Should be same because both are DURRAS eight ampersand is the address of it and P is pointing to that.

61
00:04:04,490 --> 00:04:06,050
E so what should we seem.

62
00:04:06,270 --> 00:04:07,550
So let us see the output.

63
00:04:07,560 --> 00:04:07,980
Yes.

64
00:04:07,980 --> 00:04:10,590
Batarseh it is showing the same number.

65
00:04:10,770 --> 00:04:12,820
So it is showing the address of a.

66
00:04:13,170 --> 00:04:15,910
So if you write start then only you get the data.

67
00:04:16,260 --> 00:04:22,320
So remember for a declaration use the star for the difference in use star.

68
00:04:22,620 --> 00:04:22,980
Right.

69
00:04:23,400 --> 00:04:30,420
We don't need to see that this is OK then for at initialization time, don't use the star as well.

70
00:04:30,420 --> 00:04:33,260
As for pointed out automatic don't use the star.

71
00:04:33,510 --> 00:04:40,830
So this is about a pointer to some variable a linked not a next thing is I will show you a pointer to

72
00:04:40,830 --> 00:04:43,920
and I will remove all these things.

73
00:04:44,550 --> 00:04:46,130
We can have a pointed to another.

74
00:04:46,540 --> 00:04:50,850
I will create an area of size five and I will initialize it with the values.

75
00:04:50,850 --> 00:04:52,880
Two, four, six, eight and ten.

76
00:04:53,370 --> 00:04:54,990
So five values are initialized.

77
00:04:55,380 --> 00:04:59,010
Then I will take the pointer P and also I will initialize.

78
00:05:00,090 --> 00:05:09,120
OK, no, I will initialize it in the next line, so be assigned a very sampas in the syntaxes, you

79
00:05:09,120 --> 00:05:16,370
don't have to give ampersand when you are giving Uhry name to the pointer, because name of an array

80
00:05:16,650 --> 00:05:22,780
itself is the starting address of the three right soapies a pointer so it can still address.

81
00:05:23,580 --> 00:05:32,550
If you want to use ampersand then you should say of zero means this to you of zero is two and its address

82
00:05:32,550 --> 00:05:33,000
the issue.

83
00:05:33,030 --> 00:05:33,240
Right.

84
00:05:33,280 --> 00:05:36,560
And so you have two options either right.

85
00:05:36,570 --> 00:05:45,120
Ampersand E of zero or just write a OK, just write a don't write and personne it will be wrong.

86
00:05:45,540 --> 00:05:47,340
That is invalid syntax.

87
00:05:47,850 --> 00:05:53,040
Compiler may or may not give any warning for you, so you may not get proper results.

88
00:05:53,160 --> 00:05:57,490
So don't use Ambrosini when you are initializing a pointer with an array.

89
00:05:57,720 --> 00:05:58,090
Right.

90
00:05:58,410 --> 00:06:00,950
So just stick it then.

91
00:06:01,440 --> 00:06:04,240
Can I access all these elements using pointer.

92
00:06:04,260 --> 00:06:07,440
Yes, you can access all these elements using a pointer to an area.

93
00:06:07,860 --> 00:06:11,400
So I will print all the elements using a follow up for I have sinned.

94
00:06:11,400 --> 00:06:22,290
Zero isolated in five plus plus then using C ok I will print on the values aof I so let us see and also

95
00:06:22,290 --> 00:06:25,200
I will give and also that they appear in new line.

96
00:06:26,070 --> 00:06:27,410
So let us see the output.

97
00:06:27,720 --> 00:06:28,110
Yeah.

98
00:06:28,110 --> 00:06:29,340
Here we got the values.

99
00:06:29,340 --> 00:06:30,720
Two, four, six, eight, ten.

100
00:06:31,020 --> 00:06:37,910
I have printed all the values using a now how to access them using Pointer Giuseppi of P of eight.

101
00:06:38,460 --> 00:06:42,870
So in place of arena E can I use a pointer.

102
00:06:42,880 --> 00:06:44,220
Yes you can use it.

103
00:06:44,220 --> 00:06:47,680
It will act just like the name of an array.

104
00:06:48,000 --> 00:06:53,460
So in case of a three pointer access name of another name executing it.

105
00:06:54,830 --> 00:07:02,720
Yes, using B also, it is spending all the values soapies appointer, which can act as a name of an

106
00:07:02,720 --> 00:07:03,350
array.

107
00:07:03,780 --> 00:07:05,930
Yes, this is interesting.

108
00:07:06,230 --> 00:07:09,980
So pointed to an area where the arrays created.

109
00:07:09,980 --> 00:07:11,750
It is created inside the stack.

110
00:07:12,130 --> 00:07:17,840
Remember, once again, I am telling you, whenever you declare anything in your program, it is going

111
00:07:17,840 --> 00:07:22,120
to be created inside the stack frame for that function.

112
00:07:22,130 --> 00:07:24,980
So it will be created inside the stack frame of main function.

113
00:07:25,370 --> 00:07:29,010
Now next thing, let us create an array in a heap.

114
00:07:29,390 --> 00:07:31,460
So using a pointer you can create an array.

115
00:07:31,460 --> 00:07:35,100
And he says, I will remove this and this is also gone.

116
00:07:35,510 --> 00:07:36,950
Now we have a pointer.

117
00:07:37,310 --> 00:07:38,150
Not to this point.

118
00:07:38,330 --> 00:07:41,500
We can assign a tree created in the heap.

119
00:07:41,870 --> 00:07:42,650
So for that.

120
00:07:43,670 --> 00:07:44,390
I should.

121
00:07:45,650 --> 00:07:51,740
Use my function and five into size of.

122
00:07:52,900 --> 00:07:59,020
And this is what I have explained on the whiteboard, so my log function for my log function, I should

123
00:07:59,020 --> 00:08:06,640
include astrally dot and also so my function will allocate the memory for five integers.

124
00:08:06,640 --> 00:08:14,140
So it's an array of integers and it is a single pointer p so you can see that only PE's inside the stack,

125
00:08:14,410 --> 00:08:17,390
but this array of five integers in the heap.

126
00:08:18,040 --> 00:08:20,320
Now I will initialize all the values.

127
00:08:20,320 --> 00:08:27,510
I have only five values, so I will initialize them first values 10 and P of one 15.

128
00:08:28,210 --> 00:08:32,159
Okay then P of two is 14.

129
00:08:32,169 --> 00:08:42,510
I will just randomly assigned some values, B of A three as 21 and B of four as 31.

130
00:08:43,390 --> 00:08:45,470
Then here I'm printing.

131
00:08:45,520 --> 00:08:49,690
So it's an array when we have a look at it, five integer in Cincinnati.

132
00:08:49,990 --> 00:08:53,890
So I'm assigning all five elements that I'm printing them.

133
00:08:54,100 --> 00:08:58,440
So I will get all these values printed and this is created in a heap.

134
00:08:58,990 --> 00:09:00,180
So let us run and check.

135
00:09:00,970 --> 00:09:03,460
Yeah, you can see that all these values are printed here.

136
00:09:04,600 --> 00:09:07,770
So this is a uhry inside heap.

137
00:09:08,390 --> 00:09:10,920
I will show you a C++ method of doing it.

138
00:09:11,200 --> 00:09:17,770
So instead of using a mellark, we can just see a new kind of size five.

139
00:09:17,800 --> 00:09:19,600
This is very simple in C++.

140
00:09:20,290 --> 00:09:22,960
Now, this five five word integers.

141
00:09:23,260 --> 00:09:25,990
So Chinari, five integers.

142
00:09:25,990 --> 00:09:26,320
Right.

143
00:09:26,600 --> 00:09:30,400
And then it will automatically convert into a disappointed and assign it to be.

144
00:09:30,940 --> 00:09:33,130
So it is acting just like log function.

145
00:09:33,640 --> 00:09:36,160
But the syntax is simple in C++.

146
00:09:36,980 --> 00:09:40,790
I like C using mixed code of A C and C++.

147
00:09:40,840 --> 00:09:44,400
You can write on the C language code inside C++ programs also.

148
00:09:45,100 --> 00:09:49,570
And once again, I'm telling you, if you know C++, so you already know C language also.

149
00:09:50,020 --> 00:09:50,530
All right.

150
00:09:50,980 --> 00:09:52,460
So let us run this and check.

151
00:09:52,990 --> 00:09:59,200
So using C++ syntax also, I have created an array and initialize all the elements that I'm painting

152
00:09:59,200 --> 00:09:59,420
them.

153
00:09:59,860 --> 00:10:01,570
So this is C++ syntax.

154
00:10:02,560 --> 00:10:05,890
Now the next thing about dynamic allocation C.

155
00:10:06,910 --> 00:10:11,900
Here we have dynamically created the memory for an array in a heap.

156
00:10:12,280 --> 00:10:18,670
So by using new we made a request for memory and he so then we have finished using it.

157
00:10:19,000 --> 00:10:24,210
We should also delete that memory because we are creating an array here.

158
00:10:24,220 --> 00:10:29,080
So we should use the square brackets opening and closing square, because these are I will give us space

159
00:10:29,080 --> 00:10:36,070
so that you can see it delete opening and closing square bracket and then B, if it is any other type

160
00:10:36,070 --> 00:10:39,160
of variable, then you can just write that variable name.

161
00:10:39,160 --> 00:10:42,370
If it is an array, then you must write down the subscript.

162
00:10:42,940 --> 00:10:43,290
Right.

163
00:10:43,720 --> 00:10:45,430
So remember one thing.

164
00:10:45,610 --> 00:10:53,320
Whenever you are dynamically allocating memory, you must release the memory by the when you have finished

165
00:10:53,320 --> 00:10:54,240
using it.

166
00:10:54,820 --> 00:11:02,200
And if you are using C language, then you have to say freebie free phrase used in C language.

167
00:11:02,440 --> 00:11:05,220
Delete is used in C++.

168
00:11:05,830 --> 00:11:06,280
Right.

169
00:11:06,760 --> 00:11:12,520
So this is for dislocating the memory that is allocated in a heap.

170
00:11:12,970 --> 00:11:14,800
So heap memory should be you look at it.

171
00:11:15,460 --> 00:11:22,270
And one more thing as the program is very small, the moment you run it will execute a few steps and

172
00:11:22,270 --> 00:11:22,740
finish.

173
00:11:23,110 --> 00:11:27,760
So in such programs, if you are skipping this, it's not a big problem.

174
00:11:28,570 --> 00:11:32,840
But if you are skipping the deletion, then it's not a big problem.

175
00:11:33,160 --> 00:11:40,210
So mostly in students SISE programs like the programs that will be doing in this course are small programs.

176
00:11:40,420 --> 00:11:43,270
They are not big projects in the projects.

177
00:11:43,630 --> 00:11:45,430
This is a very crucial thing.

178
00:11:45,640 --> 00:11:47,290
You must handle it very carefully.

179
00:11:47,620 --> 00:11:53,170
When memory is not required, you must delete the memory right for these small practiced programs.

180
00:11:53,320 --> 00:11:57,190
You can skip them back even when you do data structures.

181
00:11:57,430 --> 00:12:04,120
Then there also it is good if you are deleting it and if the program is very short, then if you skip,

182
00:12:04,120 --> 00:12:09,340
that's not a problem because once the program ends, the memory will be automatically deleted.

183
00:12:10,330 --> 00:12:11,150
It will not be there.

184
00:12:11,150 --> 00:12:17,030
And once the program and it's not an important thing about a pointer C, first of all, I will write

185
00:12:17,030 --> 00:12:19,690
down some code here, then I will explain you.

186
00:12:19,690 --> 00:12:20,380
What is that.

187
00:12:20,710 --> 00:12:26,350
So I have removed that could see first I have a pointer of type integer P1 and then I will take a pointer

188
00:12:26,350 --> 00:12:36,520
of type character and also I will take a point of type uh thought then also double before.

189
00:12:36,640 --> 00:12:38,020
OK, and as a pointer.

190
00:12:38,590 --> 00:12:41,320
And also I will take one pointer of type structure.

191
00:12:41,710 --> 00:12:49,720
I have defined a structure here and then I will take a pointer of type structure also struct rectangle

192
00:12:50,440 --> 00:12:52,090
starboy five.

193
00:12:52,100 --> 00:12:54,160
So I have five pointers.

194
00:12:54,790 --> 00:13:05,260
No I will print size of P1 that is pointer p1 size of the pointer then I will do the same for all the

195
00:13:05,260 --> 00:13:05,740
pointer.

196
00:13:05,740 --> 00:13:07,060
So I will copy this.

197
00:13:07,630 --> 00:13:10,060
Now I'm printing the size of all the pointers.

198
00:13:10,090 --> 00:13:17,720
Now the interesting thing is I have different type of pointers and I'm printing their sizes, so integer

199
00:13:17,770 --> 00:13:20,950
pointer should take for my select character.

200
00:13:20,950 --> 00:13:23,260
Pointer should take one bite.

201
00:13:23,260 --> 00:13:28,550
Right, because character this one fallowed pointer should take for a four bytes site because float

202
00:13:28,550 --> 00:13:29,070
takes four.

203
00:13:29,470 --> 00:13:30,190
Let us check.

204
00:13:31,280 --> 00:13:41,570
Oh, it is showing h h h h so every point assizes, each bite, I was thinking for bite two one bite

205
00:13:41,570 --> 00:13:42,110
and for bite.

206
00:13:42,110 --> 00:13:42,570
No.

207
00:13:42,870 --> 00:13:44,930
Yeah, this is the interesting thing.

208
00:13:45,350 --> 00:13:50,150
Whatever the data type of point at is going to take same amount of memory.

209
00:13:50,420 --> 00:13:53,000
Every pointer takes the same amount of memory.

210
00:13:53,000 --> 00:13:57,320
You can see that all these pointers are taking eight bites, eight bites.

211
00:13:57,320 --> 00:13:57,670
Right.

212
00:13:58,160 --> 00:14:03,350
So whatever the type of pointer may be in the latest compilers, the pointers are taking eight bytes.

213
00:14:03,350 --> 00:14:05,330
Earlier, they used to take four bytes.

214
00:14:05,330 --> 00:14:07,430
That was equal to integer sites.

215
00:14:08,540 --> 00:14:12,610
But in latest compilers, they are taking eight bites and 64 bit machines.

216
00:14:13,400 --> 00:14:21,050
So whatever the type of pointer is, it is independent size of a point that is independent of its data

217
00:14:21,050 --> 00:14:21,740
type.

218
00:14:22,370 --> 00:14:23,010
That's it.

219
00:14:23,660 --> 00:14:24,830
This is the important thing.

220
00:14:24,830 --> 00:14:28,460
And the interesting thing about pointer, so that's all in this video.

221
00:14:28,460 --> 00:14:32,490
I suggest you practice all these things so that you get used to it.

222
00:14:32,510 --> 00:14:37,760
These things don't simply watch the lectures, try to practice everything right.

223
00:14:37,790 --> 00:14:39,710
So this will help you further in the course.

224
00:14:40,610 --> 00:14:41,330
So that's on.

