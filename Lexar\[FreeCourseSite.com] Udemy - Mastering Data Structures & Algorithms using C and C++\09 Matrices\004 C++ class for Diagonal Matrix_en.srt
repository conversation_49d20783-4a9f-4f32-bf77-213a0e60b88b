1
00:00:00,660 --> 00:00:08,370
This is a diagonal matrix I have begun to dangle in matrix of order, five by five, that is five rows

2
00:00:08,850 --> 00:00:10,420
and five columns.

3
00:00:11,100 --> 00:00:17,850
And here you can see that most of the numbers are zeroes and only the elements in their diagonal are

4
00:00:17,850 --> 00:00:18,740
nonserious.

5
00:00:19,920 --> 00:00:26,010
So the important thing is, other than diagonal, all elements must be zero then only we say it's a

6
00:00:26,010 --> 00:00:26,430
diagonal.

7
00:00:27,330 --> 00:00:29,370
Suppose I have some element here.

8
00:00:29,380 --> 00:00:33,600
Let us say five, that it's not a diagonal matrix.

9
00:00:33,990 --> 00:00:35,010
This must be zero.

10
00:00:35,580 --> 00:00:38,700
So the important condition is all those elements must be zero.

11
00:00:39,000 --> 00:00:41,010
So let us see how we can define this one.

12
00:00:41,430 --> 00:00:49,830
See if this is a matrix m then I'm off I g.

13
00:00:50,870 --> 00:00:53,970
Esposito if.

14
00:00:55,180 --> 00:00:57,220
I is not equal to Gina.

15
00:00:59,480 --> 00:01:08,270
And, Wolf, I should be zero if I is not equal to then it is TitleMax if they are equal than their

16
00:01:08,300 --> 00:01:12,530
limits, maybe 090 We don't bother about that, we bother about rest of the elements.

17
00:01:12,530 --> 00:01:15,530
All these elements must be seasonal and these elements are suitable.

18
00:01:16,010 --> 00:01:21,460
So these elements are non-zero elements that are still the elements of Zeitels.

19
00:01:21,830 --> 00:01:24,860
So I have given a definition of diagonal mattocks.

20
00:01:25,700 --> 00:01:32,630
Now, if I have to represent or diagonal matrix in a program, then.

21
00:01:33,690 --> 00:01:40,830
For a matrix, I have to take two dimensional five rows and five columns, but if I take a two dimensional

22
00:01:40,830 --> 00:01:45,040
array of sci fi by five, most of the elements will be Zeitels.

23
00:01:45,870 --> 00:01:46,700
So I'll shoot.

24
00:01:47,750 --> 00:01:54,410
So like this, if I take our two dimensional array for storing this matrix, then most of the elements

25
00:01:54,410 --> 00:01:55,130
are zeros.

26
00:01:56,580 --> 00:02:01,980
If these are indigenous and indigenous taking two bites, then total, how many bites of memory?

27
00:02:02,010 --> 00:02:06,110
This is a consuming there are five to five elements out there.

28
00:02:06,130 --> 00:02:07,700
That is twenty five elements out there.

29
00:02:07,950 --> 00:02:10,380
And each element is taking two bites.

30
00:02:10,380 --> 00:02:15,390
Suppose for an integer, then it is taking 50 bites, 50 bites of memory.

31
00:02:15,390 --> 00:02:16,080
It's consuming.

32
00:02:17,070 --> 00:02:24,250
So if I'm storing this in 15 bytes of memory, then storage of zero elements is unnecessary.

33
00:02:24,660 --> 00:02:29,570
So it is wasting space as well as the processing on diagonal mitosis.

34
00:02:29,580 --> 00:02:34,440
Like if I'm adding to diagonal madrases, then adding of Zeitels is of no use.

35
00:02:34,800 --> 00:02:39,510
If I'm multiplying to diagonal mitosis, then multiplication with zeroes is of no use.

36
00:02:39,520 --> 00:02:43,210
So I will be wasting time and processing upon zeros.

37
00:02:43,590 --> 00:02:51,570
So the idea here is that we want to store only non-zero elements so how we can store only non-zero elements.

38
00:02:51,570 --> 00:02:53,220
So far, only non-zero elements.

39
00:02:53,220 --> 00:02:57,370
We can take just a single dimensionality and store these elements.

40
00:02:58,140 --> 00:02:59,590
So let us see how we can do that.

41
00:03:00,000 --> 00:03:04,380
Now let's see how we can represent our dynamite's in just a single dimension area.

42
00:03:04,410 --> 00:03:12,060
So I will take a single dimension in a display of size of five because only I have five non-zero elements.

43
00:03:12,400 --> 00:03:14,940
So I have an array of size five.

44
00:03:15,390 --> 00:03:17,310
This three starting index is zero.

45
00:03:17,310 --> 00:03:20,620
But here, if you observe I have taken, then this is from one onwards.

46
00:03:20,940 --> 00:03:23,940
So in the mathematics we started, this is from one onwards.

47
00:03:23,940 --> 00:03:25,250
So that's what I'm following.

48
00:03:25,260 --> 00:03:29,250
So if you want, you can change the universe from zero also.

49
00:03:30,210 --> 00:03:36,540
Then this array that is in programming, it is in C programming or in C++ programming, the array starts

50
00:03:36,540 --> 00:03:37,050
from zero.

51
00:03:37,080 --> 00:03:38,820
So I have taken an array from zero.

52
00:03:39,600 --> 00:03:42,470
Now I want to store only non zero element.

53
00:03:42,500 --> 00:03:44,160
So let us store those elements.

54
00:03:44,610 --> 00:03:48,780
Three seven, four, nine and six.

55
00:03:49,930 --> 00:03:56,690
So I have only non-zero elements now let us see how we can access these elements from a single dimension,

56
00:03:56,890 --> 00:03:57,110
right?

57
00:03:58,160 --> 00:04:03,250
If they want to access em off one common element, then it is at zero.

58
00:04:03,860 --> 00:04:08,810
If we want to access em off to commit to this element, to commit to it is that one.

59
00:04:09,920 --> 00:04:11,900
So only the elements are there.

60
00:04:12,140 --> 00:04:19,910
So if I want to access any element from Matics and Malfi Comanche, this I'm calling it as, but it

61
00:04:19,910 --> 00:04:23,330
is stored in an attic then from where I can get the element.

62
00:04:23,570 --> 00:04:30,530
First of all, if I is equal to G, if I is equal to that only we have an element.

63
00:04:31,370 --> 00:04:31,630
Right.

64
00:04:31,880 --> 00:04:40,140
So if it is equal to G, then to come to this present that one three commentary's do so element is present

65
00:04:40,140 --> 00:04:44,960
that I minus one or even I can say G minus one.

66
00:04:45,960 --> 00:04:50,830
So this is very simple, no, let us write C++ class for a diagonal.

67
00:04:51,570 --> 00:05:00,420
So let us take a class name as diagonal gloss diagonal, one of the inverse of the properties required

68
00:05:00,420 --> 00:05:02,150
for a diagonal mattocks.

69
00:05:02,460 --> 00:05:08,230
I need to dimension what is the damage and other space for storing the element.

70
00:05:08,430 --> 00:05:13,860
So let us make the data as a private private building.

71
00:05:13,960 --> 00:05:18,330
I need one is demolition and as is a square matrix.

72
00:05:18,330 --> 00:05:19,410
So just and there's enough.

73
00:05:19,410 --> 00:05:24,600
We don't need engross and we don't need just one dimension that the same square matrix.

74
00:05:25,620 --> 00:05:31,170
If I fire this five into five then we need an array for this uhry.

75
00:05:31,380 --> 00:05:37,890
I will not create an array, but I will take a pointer so that I can dynamically create an array.

76
00:05:40,090 --> 00:05:47,080
That's all these are the two things I need, the next thing I need, the next thing I should write on

77
00:05:47,080 --> 00:05:54,940
a constructor so constructive inside public, I was be constructive for diagonal.

78
00:05:56,590 --> 00:06:02,260
So when you are creating an object of this one, then what we need, we need the dimension, whether

79
00:06:02,260 --> 00:06:04,330
it is of size of five or four or whatever it is.

80
00:06:04,360 --> 00:06:06,250
So I need a dimension.

81
00:06:06,250 --> 00:06:07,990
So let us take in.

82
00:06:08,800 --> 00:06:11,350
And so this will take a dimension.

83
00:06:12,520 --> 00:06:20,260
Then inside the constructor, I should set this value end to this one and also create an idea.

84
00:06:20,620 --> 00:06:28,840
So here this and so this addle an assignment.

85
00:06:29,900 --> 00:06:38,350
This barometer and to this day, I should create an area of the same size, this size, so new, if

86
00:06:38,360 --> 00:06:42,560
I am taking integer type here, then it is integer only integer of size.

87
00:06:42,980 --> 00:06:46,670
I can simply write and also so I can use it this one on this one.

88
00:06:47,480 --> 00:06:49,430
So this is how arrays are created.

89
00:06:51,090 --> 00:06:56,400
So we will get on a single dimensionally off required size, that is Fineman's area of size five.

90
00:06:56,430 --> 00:06:57,930
Let us say this is the five size three.

91
00:06:59,280 --> 00:07:01,110
Then what of the other functions I need?

92
00:07:01,260 --> 00:07:07,440
I need functions for storing the data and retrieving the data so far that I need functions like setting

93
00:07:07,440 --> 00:07:09,280
the data and getting the data.

94
00:07:09,600 --> 00:07:12,690
So I relied on those two functions wide.

95
00:07:14,150 --> 00:07:14,680
SEC.

96
00:07:15,470 --> 00:07:21,560
So this set of store, the data in this area, in this area that is for this matter, for the metrics,

97
00:07:21,560 --> 00:07:23,380
I need to know the rule number and column.

98
00:07:23,660 --> 00:07:25,580
No, no, no.

99
00:07:25,820 --> 00:07:28,750
So rule number and column number as well as element.

100
00:07:28,760 --> 00:07:34,840
So it meets the three parameters at which rule and activity column.

101
00:07:35,570 --> 00:07:39,650
What is the element that you want to store X then?

102
00:07:39,650 --> 00:07:41,840
We need a function for retrieving the data.

103
00:07:41,850 --> 00:07:48,800
So the function name, let us call it as get then it also needs to from which rule number and which

104
00:07:48,800 --> 00:07:50,360
column number you want to retrieve.

105
00:07:50,360 --> 00:07:58,220
The data and return type should be integer because it's going to return the element at that rule number.

106
00:07:58,340 --> 00:08:03,080
And then we will also have one function for displaying a matrix.

107
00:08:03,350 --> 00:08:06,530
So I learned a function called void display.

108
00:08:07,100 --> 00:08:12,530
So instead of a display function, we can also overload in such an operator that I will show you when

109
00:08:12,530 --> 00:08:13,500
I write the program.

110
00:08:14,720 --> 00:08:20,610
The last thing is we should have a destructo for destroying this dynamically created an array.

111
00:08:20,960 --> 00:08:28,590
So here I should also have a destructive diagonal that's on and of a class.

112
00:08:29,020 --> 00:08:33,309
Next, I will implement these functions outside the class using scope resolution.

113
00:08:33,620 --> 00:08:40,190
So here I will write on those functions first function this set so wide.

114
00:08:41,150 --> 00:08:51,560
Class name diagonal set three parameters IJI and X, then what are you supposed to do then.

115
00:08:51,560 --> 00:08:55,380
Three parameters are given rule number and column number on the element.

116
00:08:55,400 --> 00:08:59,240
So if I'm giving suppos nine so I'll be giving four, four and nine.

117
00:08:59,540 --> 00:09:06,200
If I'm giving seven then two to seven even I may give three to that is zero.

118
00:09:06,470 --> 00:09:07,760
So it should not zero zero.

119
00:09:08,090 --> 00:09:12,020
So first of all it should check whether the indices are for diagonal only or not.

120
00:09:12,440 --> 00:09:22,310
So if I is equal to G then it should store the element in aof i.e. minus one is the formula we have

121
00:09:22,310 --> 00:09:22,610
seen.

122
00:09:22,940 --> 00:09:26,060
Like this is one comma one as a store at zero.

123
00:09:26,360 --> 00:09:30,560
So three commentary is a at two so I minus one.

124
00:09:30,560 --> 00:09:32,840
I want to store that I minus one.

125
00:09:33,080 --> 00:09:35,900
If they both are equal store the element.

126
00:09:37,450 --> 00:09:43,030
That's all this is said function if they are not equal.

127
00:09:43,060 --> 00:09:44,940
Don't do anything, don't store anything.

128
00:09:45,250 --> 00:09:46,500
We don't have to store anything.

129
00:09:46,510 --> 00:09:49,750
And then second one, get to function.

130
00:09:50,910 --> 00:10:00,940
Return type, open the glass name next to us, get to perimeter's and imagine that is which element

131
00:10:00,940 --> 00:10:03,040
you want to read, which element you want to retrieve.

132
00:10:03,100 --> 00:10:05,020
Suppose I want to retrieve trigonometry.

133
00:10:05,200 --> 00:10:09,880
So trigonometry are indexed to let us trigonometry for this one.

134
00:10:10,780 --> 00:10:12,590
So Ikoma is given.

135
00:10:13,060 --> 00:10:17,710
Now here again, I can get the data from there only if I is equal to change.

136
00:10:17,790 --> 00:10:20,230
Just like and said the same thing we have to do.

137
00:10:22,310 --> 00:10:33,580
If I is equal to G, then in the same line, I'm writing on AOF eight minus one and one, wondering

138
00:10:33,590 --> 00:10:36,430
if they are not equal, then definitely the element of zero.

139
00:10:36,470 --> 00:10:38,270
So return zero.

140
00:10:38,960 --> 00:10:41,470
So these are certain methods.

141
00:10:42,050 --> 00:10:49,670
So I have a constructor, I have set a method and get a method completed and I will write on these two

142
00:10:49,670 --> 00:10:50,420
functions.

143
00:10:50,780 --> 00:10:51,770
There is no space.

144
00:10:52,070 --> 00:11:00,140
I will remove this and I will write on those two functions, not display function, class, name and

145
00:11:00,140 --> 00:11:07,340
function, name, display, display means actually we have these elements, but we should display it

146
00:11:07,340 --> 00:11:12,860
like a two dimensional array along with the zeroes so that it looks like a magnox.

147
00:11:13,430 --> 00:11:17,090
So let us display our two dimensional matrix.

148
00:11:17,120 --> 00:11:21,340
So for that we need to for loops, for reassigns zero.

149
00:11:21,650 --> 00:11:33,500
I use less than an eight plus plus that is that and and for GFI in zero js less than n g plus plus.

150
00:11:35,110 --> 00:11:40,900
Then I have to display the elements, but how many elements I have only diagonal elements that are present

151
00:11:40,900 --> 00:11:46,190
in the array, so I should display the element if I is equal to J, otherwise I should display zero.

152
00:11:46,600 --> 00:11:52,750
So if I is equal to G in the same line, I will.

153
00:11:52,810 --> 00:12:01,480
I don't see out of i.e. minus one otherwise seal.

154
00:12:03,400 --> 00:12:04,100
Zero.

155
00:12:05,350 --> 00:12:09,810
So whatever you want to display, you have to give it a double cause if it is not a variable.

156
00:12:09,850 --> 00:12:17,860
So I want to display zero zero in double quotes as a string, then close this one, this wonderful display,

157
00:12:17,860 --> 00:12:20,860
one through and after every row, I should go into the next line.

158
00:12:20,860 --> 00:12:22,810
So see out and then.

159
00:12:24,690 --> 00:12:31,530
Then close followed and closed the function, so this function, using to follow full display are two

160
00:12:31,530 --> 00:12:33,510
dimensional array like this.

161
00:12:34,520 --> 00:12:37,100
Then I have this last function that is.

162
00:12:38,300 --> 00:12:41,000
It should have just a simple statement, so I'll explain.

163
00:12:41,000 --> 00:12:41,660
They're only.

164
00:12:43,030 --> 00:12:51,970
This, I have to say, delete, this is the one that is a dynamically allocated it should be allocated

165
00:12:52,380 --> 00:12:55,700
on so just as a single statement inside that function.

166
00:12:55,720 --> 00:12:57,550
So I have expanded beta cells.

167
00:12:57,880 --> 00:12:59,410
So that's all for our diagonal.

168
00:12:59,410 --> 00:13:06,960
Matrix will be learning about more mitosis, like a lower triangular triangular in all those mattresses.

169
00:13:07,480 --> 00:13:11,640
We will be having the same set of functions in the class.

170
00:13:12,520 --> 00:13:14,920
I will not be writing a complete class every time.

171
00:13:16,150 --> 00:13:20,680
Then we have to see the other Matisses like lower triangular and upper triangular and so on.

172
00:13:21,070 --> 00:13:23,680
For every romantic's the class looks are similar.

173
00:13:23,860 --> 00:13:30,960
We will be having dimension and our square mitosis and will be having array space and will be using

174
00:13:30,970 --> 00:13:32,260
single dimensional only.

175
00:13:32,650 --> 00:13:37,780
And we need a constructor set and get function and display and be destructive.

176
00:13:38,200 --> 00:13:41,710
And while writing a program, I will show you a complete class.

