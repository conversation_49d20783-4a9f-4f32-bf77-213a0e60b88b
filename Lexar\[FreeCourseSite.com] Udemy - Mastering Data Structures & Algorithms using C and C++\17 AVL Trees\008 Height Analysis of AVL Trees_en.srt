1
00:00:00,510 --> 00:00:09,030
The topic is hide versus Naude analysis for aviary trees, similar analysis we have already done for

2
00:00:10,050 --> 00:00:19,640
general binary trees and strict binary trees and also for complete or strict energy trees.

3
00:00:19,650 --> 00:00:20,970
We have done the same analysis.

4
00:00:21,270 --> 00:00:24,450
I we'll do the analysis upon Aviad.

5
00:00:24,750 --> 00:00:31,740
So what we have to do is if height of a Aviel is given, height is given, then find minimum.

6
00:00:31,740 --> 00:00:35,280
How many neurons are possible in that height maximum.

7
00:00:35,280 --> 00:00:37,340
How many ones are possible in that height.

8
00:00:37,830 --> 00:00:42,990
So every tree is a binary tree only basically binary tree than binary search tree.

9
00:00:43,350 --> 00:00:47,390
Then it is hard to balance the binary search for basically this binary tree.

10
00:00:47,670 --> 00:00:53,210
So in any binary tree, if hydrofoil tree is given the maximum, how many nodes can be there.

11
00:00:53,460 --> 00:00:54,930
Poupart Each plus one.

12
00:00:54,930 --> 00:00:55,850
Minus one also.

13
00:00:56,350 --> 00:01:00,030
Yes, we know this one, so I don't have to explain that one.

14
00:01:00,040 --> 00:01:02,360
So this is coming from binary trees, right.

15
00:01:02,610 --> 00:01:05,420
So anyway, I will come across this formula once again.

16
00:01:05,910 --> 00:01:07,890
Now the question is minimum.

17
00:01:07,890 --> 00:01:09,870
How many nodes if hiders given?

18
00:01:11,080 --> 00:01:18,850
So how to get the formula see formula by taking the sample values or examples from the observation,

19
00:01:18,850 --> 00:01:20,190
you can prepare the formula.

20
00:01:20,240 --> 00:01:22,190
So formula comes from observation.

21
00:01:22,420 --> 00:01:28,420
Let us take some examples of some highlights of a Belltrees, then see how many minimum Nords required.

22
00:01:28,480 --> 00:01:29,830
Then we'll come up with the formula.

23
00:01:29,860 --> 00:01:31,470
So let me show you some examples.

24
00:01:31,720 --> 00:01:35,890
First example, let us assume that hiders one.

25
00:01:36,910 --> 00:01:38,280
What about Zettl?

26
00:01:39,340 --> 00:01:42,460
We will start height from one onwards, right?

27
00:01:42,790 --> 00:01:49,040
So here we will start from the same height is one then just one note.

28
00:01:49,150 --> 00:01:51,370
I'm not labelling, I'm not putting any values.

29
00:01:51,610 --> 00:01:53,500
So how many nodes require a minimum.

30
00:01:53,500 --> 00:01:55,150
One only maximum.

31
00:01:55,150 --> 00:02:00,040
Also one minimum one for one height is not zero starting for one the next.

32
00:02:00,370 --> 00:02:05,020
If height is to the minimum, how many laws are required?

33
00:02:06,630 --> 00:02:07,470
Not yes.

34
00:02:07,710 --> 00:02:09,000
I do not.

35
00:02:09,690 --> 00:02:14,460
If Heidi's a three, then the minimum, how many North trainloads?

36
00:02:15,660 --> 00:02:18,420
Three notes require a minimum, three Nords.

37
00:02:19,640 --> 00:02:26,810
No, wrong, this is wrong, is it a try Aviel agreements hide balance, so if you check the balance

38
00:02:26,810 --> 00:02:28,620
factors, this is zero.

39
00:02:28,850 --> 00:02:30,930
This is one and this is a two.

40
00:02:31,250 --> 00:02:32,360
This is not violence.

41
00:02:32,570 --> 00:02:35,870
So I need one more note on this side to make it balance.

42
00:02:36,150 --> 00:02:36,710
Yes.

43
00:02:36,740 --> 00:02:37,850
Not a balance factor.

44
00:02:38,510 --> 00:02:41,380
Zero zero one zero.

45
00:02:41,720 --> 00:02:43,070
And this is one.

46
00:02:43,240 --> 00:02:43,670
Yes.

47
00:02:43,670 --> 00:02:47,270
This is a balance that set minimum nordman.

48
00:02:47,360 --> 00:02:48,380
It must be balanced.

49
00:02:48,410 --> 00:02:56,430
So minimum how many nodes required for nodes required for take four then if the height is for and then

50
00:02:56,430 --> 00:02:58,030
the minimum, how many nodes required.

51
00:02:58,040 --> 00:03:00,730
One, two, three, four.

52
00:03:01,160 --> 00:03:02,120
And the balance.

53
00:03:02,130 --> 00:03:04,730
No, this site I need some nodes.

54
00:03:04,730 --> 00:03:05,330
One node.

55
00:03:05,330 --> 00:03:06,180
Is it sufficient.

56
00:03:06,240 --> 00:03:06,590
No.

57
00:03:06,590 --> 00:03:08,900
Still it is balance one more.

58
00:03:08,900 --> 00:03:10,090
Nor is it balanced now.

59
00:03:10,120 --> 00:03:11,020
Yes, that is valid.

60
00:03:11,270 --> 00:03:12,290
What about this node.

61
00:03:12,500 --> 00:03:13,790
This is not balanced.

62
00:03:14,040 --> 00:03:15,830
How now everything is balance.

63
00:03:16,040 --> 00:03:16,590
Let us check.

64
00:03:16,820 --> 00:03:18,650
Zero one zero.

65
00:03:19,930 --> 00:03:21,590
And this is one this is zero.

66
00:03:21,610 --> 00:03:25,230
This is minus one, and this is one one, two, three, one, two.

67
00:03:25,600 --> 00:03:26,050
Yes.

68
00:03:26,230 --> 00:03:32,420
So total how many nodes require one, two, three, four, five, six, seven, seven nodes require

69
00:03:32,530 --> 00:03:41,430
minimum seven nodes required for height for where the height are starting from one onwards that four

70
00:03:41,440 --> 00:03:43,360
five also I will drive it right here.

71
00:03:43,390 --> 00:03:44,740
There is no space on that site.

72
00:03:45,040 --> 00:03:47,380
If height is five then the minimum.

73
00:03:47,650 --> 00:03:49,140
How many nodes required.

74
00:03:50,020 --> 00:03:50,770
I will try it.

75
00:03:51,220 --> 00:03:54,680
One, two, three, four, five.

76
00:03:54,850 --> 00:03:56,260
No, let's not balance.

77
00:03:56,560 --> 00:04:02,080
If you have five on the side, including this one for nodes must be there then only this balance.

78
00:04:02,110 --> 00:04:04,960
OK, one, two, three.

79
00:04:05,170 --> 00:04:06,730
Total one, two, three, four.

80
00:04:06,760 --> 00:04:07,020
Yes.

81
00:04:07,030 --> 00:04:07,450
Perfect.

82
00:04:07,450 --> 00:04:08,430
Including that group.

83
00:04:08,920 --> 00:04:10,600
Then what about this for this.

84
00:04:10,600 --> 00:04:11,430
Including this one.

85
00:04:11,440 --> 00:04:12,350
One, two, three, four.

86
00:04:12,380 --> 00:04:13,980
You have so you should have one.

87
00:04:15,160 --> 00:04:16,149
One, two, three.

88
00:04:16,180 --> 00:04:17,160
Now it is balance.

89
00:04:17,470 --> 00:04:18,610
Now here you have three.

90
00:04:18,620 --> 00:04:21,040
So here you should have to balance.

91
00:04:21,190 --> 00:04:24,190
Here you have three here you should have to balance.

92
00:04:24,490 --> 00:04:25,750
No, this is a balance.

93
00:04:25,750 --> 00:04:31,000
If you check the balance factor for all the norms, then the three is a balance between how many nodes

94
00:04:31,000 --> 00:04:31,510
are there.

95
00:04:31,750 --> 00:04:35,960
One, two, three, four, five, six, seven, eight, nine, ten, eleven, twelve.

96
00:04:36,310 --> 00:04:38,710
So minimum twelve also are required.

97
00:04:39,130 --> 00:04:39,820
Yes.

98
00:04:41,590 --> 00:04:47,230
Now, we have enough examples for observation and to get the formula.

99
00:04:47,260 --> 00:04:48,910
Now I have some set of values.

100
00:04:48,910 --> 00:04:50,080
Let me be a better table.

101
00:04:50,350 --> 00:04:55,010
If height is given, then how many notes for a given height?

102
00:04:55,120 --> 00:04:56,520
Let us make it of function.

103
00:04:56,980 --> 00:04:58,600
So if height is one one node.

104
00:05:00,300 --> 00:05:11,880
Two, two, three, four, two, two, three, four, four, seven, four, seven, five, 12.

105
00:05:13,300 --> 00:05:16,150
Five to 10, an extended.

106
00:05:18,420 --> 00:05:20,200
Six and seven.

107
00:05:20,520 --> 00:05:21,860
Tell me the answer for this.

108
00:05:21,900 --> 00:05:22,880
I got this tour.

109
00:05:23,010 --> 00:05:30,730
Let us observe four plus seven, 11, 11 plus one, OK, for the seven plus one.

110
00:05:31,450 --> 00:05:33,990
Am I getting out of disvalue also like that?

111
00:05:33,990 --> 00:05:35,800
Only two plus four.

112
00:05:35,940 --> 00:05:36,820
Six plus one.

113
00:05:36,840 --> 00:05:37,730
Yes, seven.

114
00:05:38,190 --> 00:05:39,090
How I got this one.

115
00:05:39,330 --> 00:05:42,800
One plus two three plus one four.

116
00:05:43,140 --> 00:05:46,530
So it means this one is seven plus 12.

117
00:05:46,650 --> 00:05:48,630
19 plus one 20.

118
00:05:49,620 --> 00:05:53,980
Then how to get this one 12 plus 20, 32 plus one.

119
00:05:54,030 --> 00:05:54,630
Thirty three.

120
00:05:54,980 --> 00:05:55,430
Yes.

121
00:05:55,830 --> 00:06:04,440
So the formula is no also N for a given height as previous two values are added and plus one.

122
00:06:04,440 --> 00:06:10,470
So end of action, minus two plus and of minus one.

123
00:06:11,010 --> 00:06:11,820
Plus one.

124
00:06:12,210 --> 00:06:12,830
Plus one.

125
00:06:13,170 --> 00:06:13,450
Yes.

126
00:06:13,570 --> 00:06:18,800
This is the formula then for the very first value, I should have two values with me.

127
00:06:18,990 --> 00:06:25,830
So when height is a zero answer is zero, then hide this one, then the answer is one.

128
00:06:26,100 --> 00:06:26,420
Right.

129
00:06:26,910 --> 00:06:31,500
Otherwise dissident's seven Heidi zero zero.

130
00:06:31,500 --> 00:06:32,990
Hydrous one answer is one.

131
00:06:33,330 --> 00:06:43,160
Otherwise this the formula used the formula for the minimum is this one and for this the table.

132
00:06:44,160 --> 00:06:47,190
So minimum nodes can be obtained by this formula.

133
00:06:47,190 --> 00:06:51,360
So I should write on this formula here for a minimum n so I will not write the formula.

134
00:06:51,660 --> 00:06:53,400
I will say look in the table.

135
00:06:53,700 --> 00:06:55,980
So four minimum one to look into the table.

136
00:06:56,700 --> 00:07:02,770
So if I just fire minimum go alongside a six minimum 20 n like this, then there is one change here.

137
00:07:03,330 --> 00:07:07,080
See I started right from one onwards when I have only one, not just one on.

138
00:07:07,300 --> 00:07:14,310
So if height is three then seven n seven n this height is three.

139
00:07:14,320 --> 00:07:15,330
One, two, three.

140
00:07:15,630 --> 00:07:18,320
So maximum seven Nordström the height is a three.

141
00:07:18,660 --> 00:07:24,030
So this maximum noad formula we will get to Peracha plus one, minus one.

142
00:07:24,030 --> 00:07:30,210
But no we will have the support at minus one formula because the height is the starting from one onwards

143
00:07:30,510 --> 00:07:32,460
for maximum nought for this one.

144
00:07:32,470 --> 00:07:39,300
So if I add this one node and these are two nodes and these are one, two, three, four and also two

145
00:07:39,300 --> 00:07:39,720
square.

146
00:07:39,750 --> 00:07:41,550
So this is one plus two plus two.

147
00:07:41,550 --> 00:07:47,160
Square is equals two to two minus one to minus one is what, eight minus one.

148
00:07:47,170 --> 00:07:47,850
That is seven.

149
00:07:48,300 --> 00:07:49,980
So yes, we have seven nodes.

150
00:07:49,980 --> 00:07:51,690
So this is to cube height is three.

151
00:07:51,940 --> 00:07:53,530
So just to reach minus one.

152
00:07:53,640 --> 00:08:01,080
Now we have the formula as if height is given that the minimum nodes and maximum nodes, we need another

153
00:08:01,080 --> 00:08:01,680
formula.

154
00:08:02,010 --> 00:08:06,070
If nodes are given then what, some minimum height and maximum height.

155
00:08:06,330 --> 00:08:08,070
So let me to remove this.

156
00:08:08,370 --> 00:08:13,300
If nodes are given, then find minimum height and maximum height.

157
00:08:13,860 --> 00:08:20,580
So if you remember in binary trees, what we did is we converted this minimum formula to maximum formula

158
00:08:20,580 --> 00:08:24,350
and maximum formula to minimum formula will do the same thing here.

159
00:08:24,960 --> 00:08:26,820
So we don't have to do any analysis.

160
00:08:27,150 --> 00:08:34,740
C Maximum nodes are any equal to cooperage plus one minus one, then minimum height will be H is equals

161
00:08:34,740 --> 00:08:41,940
two and plus one log base to so log base two and Newlove minus one because in power we don't have plus

162
00:08:41,940 --> 00:08:42,390
one here.

163
00:08:42,840 --> 00:08:44,850
So not missed one plus one then.

164
00:08:44,850 --> 00:08:46,510
What about the maximum height.

165
00:08:46,770 --> 00:08:48,760
This comes from minimum nought formula.

166
00:08:49,020 --> 00:08:49,620
There it is.

167
00:08:49,620 --> 00:08:50,590
Look on the table.

168
00:08:50,610 --> 00:08:52,950
So here also look in the table.

169
00:08:53,100 --> 00:08:54,540
You have to look into the table.

170
00:08:56,010 --> 00:09:03,450
How let me give an example, if I have to overlord's, then what is the maximum height, five if I have

171
00:09:03,450 --> 00:09:04,470
13 Nords.

172
00:09:05,410 --> 00:09:14,200
Tartine is less than 20, so the site only here, only 13 nodes, also five fifteen nodes, five maximum

173
00:09:14,200 --> 00:09:19,210
height, maximum height, five, 19 nodes, height of five maximum.

174
00:09:20,270 --> 00:09:23,450
So these are minimum notes required for getting this height.

175
00:09:24,050 --> 00:09:25,640
Now I have 20 notes.

176
00:09:25,670 --> 00:09:28,820
OK, maximum height is six, maximum height of six.

177
00:09:29,120 --> 00:09:37,150
So from this 12 to 19, maximum height is five and 20 to 30 to maximum height of six.

178
00:09:37,430 --> 00:09:44,460
So you can look into the table and you can read this and stuff and stuff from high towards north.

179
00:09:44,690 --> 00:09:46,640
You have to see from north towards height.

180
00:09:47,610 --> 00:09:52,820
That's it, so you can find out, so you have to practice this little bit bothered by your cell phone

181
00:09:53,130 --> 00:09:56,860
using pen and paper, then you will remember everything, right?

182
00:09:56,970 --> 00:09:58,950
Just practice it once.

183
00:09:58,950 --> 00:09:59,750
You'll remember.

184
00:10:00,180 --> 00:10:10,170
So we have done in-depth analysis of absolutely no one lasting see this formula for minimum number of

185
00:10:10,170 --> 00:10:14,050
notes of formula like this one minimum number of notes formula.

186
00:10:14,520 --> 00:10:17,280
Actually, this formula is meant for this one.

187
00:10:17,280 --> 00:10:25,440
And also useful here, Heidi, is given N minimum N formula, this formalized same as Fibonacci CBDs

188
00:10:25,440 --> 00:10:27,000
formula he has.

189
00:10:27,540 --> 00:10:34,120
Fibonacci series is famous for being on to series balanced, a series.

190
00:10:34,500 --> 00:10:41,460
You take any two down for Fibonacci CCDs like F.I. and as a plus one, any two done.

191
00:10:41,480 --> 00:10:51,960
For example, after 10 and 11 to DOMS, then s 11 by ten if you do or F.I. plus one by if I, if you

192
00:10:51,960 --> 00:10:54,720
do ratio will be one point six.

193
00:10:56,120 --> 00:11:01,050
Approximately, not exactly May maybe one point five nine eight twenty one point fifty seven.

194
00:11:01,070 --> 00:11:06,090
Also, you should accept it as one point six or maybe one point six one six two.

195
00:11:06,110 --> 00:11:08,740
Also approximately one point six.

196
00:11:09,260 --> 00:11:14,150
So any two consecutive numbers, if you take the issue at, is one point six.

197
00:11:14,160 --> 00:11:18,550
So Fibonacci series is believed to be balanced.

198
00:11:18,990 --> 00:11:28,460
So this formula is matching with Fibonacci series means Aviel trees are also balanced, the proof that

199
00:11:28,730 --> 00:11:35,870
they are balance because so their formula for NORDO versus height, same as Fibonacci series, that

200
00:11:36,020 --> 00:11:43,460
one lasting minimum height is log and plus one B's to maximum height.

201
00:11:43,460 --> 00:11:45,950
Look into the table table is from the formula.

202
00:11:46,400 --> 00:11:47,740
So from this formula.

203
00:11:47,790 --> 00:11:49,330
Can't calculate height.

204
00:11:49,640 --> 00:11:51,540
Yes, approximately.

205
00:11:51,560 --> 00:11:54,680
If you calculate from this one, this is the derivation.

206
00:11:54,680 --> 00:12:01,700
If we derive that and get the answer, then approximately, approximately it is equal to one point four

207
00:12:01,700 --> 00:12:05,930
for log base two and plus two.

208
00:12:06,380 --> 00:12:11,520
If you use this formula and convert it in terms of log-in, we get the funds.

209
00:12:11,570 --> 00:12:14,960
So minimum height is also logarithmic.

210
00:12:14,960 --> 00:12:19,470
Maximum height is also multiple of logarithmic, but logarithmic only.

211
00:12:19,520 --> 00:12:24,000
So Aviel tree's height is always in terms of the log.

212
00:12:24,470 --> 00:12:25,160
That's it.

213
00:12:25,250 --> 00:12:29,810
This is enough with the analysis of height or sustenance.

