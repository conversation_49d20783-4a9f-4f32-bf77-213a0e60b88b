1
00:00:02,260 --> 00:00:06,580
In this video, we will see how to use a debugger in code blocks.

2
00:00:09,520 --> 00:00:16,000
Debugger is used for tracing the program's execution line by line, we can trace the program to see

3
00:00:16,030 --> 00:00:20,560
it's working in detail by checking the execution of each line.

4
00:00:21,430 --> 00:00:23,560
It's also tracing the program.

5
00:00:25,140 --> 00:00:32,070
If there is any error or the program is not giving expected results, we can check the execution of

6
00:00:32,070 --> 00:00:35,370
the program line by line and we can catch that error.

7
00:00:37,330 --> 00:00:40,060
Even for understanding the workings of a program.

8
00:00:43,520 --> 00:00:45,410
We can take the help of other.

9
00:00:46,560 --> 00:00:49,290
I have usually debugger in a few programs in my.

10
00:00:50,280 --> 00:00:55,400
So here we will see how to use a debugger for that.

11
00:00:55,740 --> 00:00:59,090
I will create a new project and I will ride on the program.

12
00:01:00,480 --> 00:01:04,140
So create a new project and a console application.

13
00:01:06,130 --> 00:01:10,340
Then there should be C++ at the next here.

14
00:01:10,540 --> 00:01:13,180
I will call it as my book.

15
00:01:18,310 --> 00:01:19,960
Yes, it's really.

16
00:01:20,880 --> 00:01:25,090
Now, he had already I have on programs, I will based the program here directly.

17
00:01:26,580 --> 00:01:27,670
Here is my program.

18
00:01:27,780 --> 00:01:31,950
So the program is to find the sum of all the elements in an array.

19
00:01:31,980 --> 00:01:33,570
These are the elements in an array.

20
00:01:34,590 --> 00:01:40,320
The program will add all these elements and stood the result in some by taking one element at a time

21
00:01:40,320 --> 00:01:41,280
and variable X.

22
00:01:41,700 --> 00:01:42,810
So this type of program.

23
00:01:42,810 --> 00:01:43,700
So you'll see later.

24
00:01:43,710 --> 00:01:46,530
But here the purpose is to show you debugging.

25
00:01:47,600 --> 00:01:52,820
So let us, first of all, compile the program, so go to Bill and say.

26
00:01:54,120 --> 00:02:00,110
Build and run, so it will compile and build a program and run, so I'm getting the result at 35.

27
00:02:00,630 --> 00:02:09,169
That is some of all these elements in an area that is one plus two plus five plus eight plus nine is

28
00:02:09,180 --> 00:02:09,810
25.

29
00:02:10,919 --> 00:02:17,400
Now, the program is already combined, no under under Dibakar, so before running the debugger, I

30
00:02:17,400 --> 00:02:21,770
should put a breakpoint on any one of the statement, so I'll put a breakpoint.

31
00:02:21,780 --> 00:02:27,030
It's better to put a big part of the beginning statement so we can see the execution of all these statements

32
00:02:27,030 --> 00:02:27,640
one by one.

33
00:02:28,140 --> 00:02:30,330
So going this line eight and right.

34
00:02:30,330 --> 00:02:32,920
Click and C add breakpoint.

35
00:02:33,720 --> 00:02:36,690
So here you have to see at Breakpoint and then again.

36
00:02:36,690 --> 00:02:44,410
Right, click on this line to delete it, remove the breakpoint so you can remove breakpoint this.

37
00:02:45,120 --> 00:02:46,470
So I'll add it once again.

38
00:02:46,470 --> 00:02:47,730
They add breakpoint.

39
00:02:48,710 --> 00:02:50,020
Now, Breakpoint is ready.

40
00:02:50,060 --> 00:02:55,010
I have to call debugger, so go to debug option and say start.

41
00:02:56,320 --> 00:03:04,550
Right, so the option is Efate here again, if you want to execute line by line, you can use at seven.

42
00:03:04,960 --> 00:03:07,660
So let us start debuggers started.

43
00:03:07,870 --> 00:03:12,820
Now, if you want to see the contents of the variables, what the values are there and the variables,

44
00:03:13,090 --> 00:03:14,660
you have to add a watch.

45
00:03:15,280 --> 00:03:19,930
So for getting a watch window, this is the method to debug menu.

46
00:03:20,170 --> 00:03:24,880
And inside that select debugging windows menu and click on watch as.

47
00:03:26,740 --> 00:03:33,850
Yeah, here I got watches and this already having variables like some, and so here inside the program

48
00:03:33,850 --> 00:03:35,990
you can see some is there and is there.

49
00:03:36,550 --> 00:03:41,770
So this if I open, then you can see it is having the values one, two and five, eight, nine.

50
00:03:41,770 --> 00:03:45,310
So which are already given here, but it is not showing X.

51
00:03:45,700 --> 00:03:49,060
So if it is not showing any variable you just can select that variable.

52
00:03:49,330 --> 00:03:49,700
Right.

53
00:03:49,700 --> 00:03:51,750
Click and say watch X.

54
00:03:52,660 --> 00:03:54,880
So it's not available in current context.

55
00:03:56,470 --> 00:04:01,170
And even if it is not showing other variables like some or a, you can select it.

56
00:04:02,260 --> 00:04:05,440
Now before I continue, let us see what this program is doing.

57
00:04:05,440 --> 00:04:10,780
This program is going to add all these elements and find the sum of all these elements and variable

58
00:04:10,780 --> 00:04:11,050
X.

59
00:04:11,350 --> 00:04:17,380
So Exel we're taking the values that are presenting every one by one like one, two, five, eight and

60
00:04:17,380 --> 00:04:17,800
nine.

61
00:04:18,250 --> 00:04:20,290
Then it will be going adding them to something.

62
00:04:20,860 --> 00:04:28,510
So here inside the watch, you have to observe how the values of this sum and this X are changing right

63
00:04:29,110 --> 00:04:29,380
now.

64
00:04:29,380 --> 00:04:30,100
Let's proceed.

65
00:04:30,340 --> 00:04:32,350
So I execute the next statement.

66
00:04:32,360 --> 00:04:35,260
Alpers F7 now.

67
00:04:38,400 --> 00:04:42,270
That is all did and exes are still not defined.

68
00:04:43,270 --> 00:04:51,060
Now you can see that exes having value one here and right then some of the still zero and it is on this

69
00:04:51,060 --> 00:04:53,930
14th line where it has not yet added this one.

70
00:04:54,210 --> 00:04:58,060
Once this line ends, the value of X will be added to some.

71
00:04:58,590 --> 00:05:01,800
So if a seven, you can see the value of some will be changing.

72
00:05:07,680 --> 00:05:08,890
Your son became one.

73
00:05:09,450 --> 00:05:15,950
Then again, if I continue, X became too, so you have to watch in the side, X became to know.

74
00:05:15,980 --> 00:05:24,240
Next line the son becomes three to one plus two that the three, then some becomes five and some becomes

75
00:05:24,240 --> 00:05:24,600
eight.

76
00:05:24,600 --> 00:05:31,590
X became five and then some became eight X eight and some become 16 x nine.

77
00:05:31,590 --> 00:05:32,990
Then some becomes 25.

78
00:05:33,750 --> 00:05:35,140
Nobody will come out of the loop.

79
00:05:35,160 --> 00:05:41,550
So here you can see that the line this this current line as shown by this arrow here, no responding

80
00:05:41,550 --> 00:05:42,150
on this one.

81
00:05:42,450 --> 00:05:45,450
If f7, then the value will be printed.

82
00:05:45,450 --> 00:05:46,530
And here is a window.

83
00:05:47,070 --> 00:05:50,010
You can click on this and see the value 25 misprinted.

84
00:05:52,310 --> 00:05:58,880
So that's it, that's how you can use a debugger for executing the program line by line and see how

85
00:05:58,880 --> 00:06:02,450
it is executing how this modifying or updating the variables.

86
00:06:03,560 --> 00:06:07,960
You can take the help of the builder for detailed understanding of execution, of program.

87
00:06:08,630 --> 00:06:12,910
And also, if there are any errors, you can trace it and remove the errors.

88
00:06:14,030 --> 00:06:15,230
So that's all in this video.

