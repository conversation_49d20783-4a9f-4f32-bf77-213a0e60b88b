1
00:00:00,150 --> 00:00:03,870
And this video will have an introduction to kill data structure.

2
00:00:04,950 --> 00:00:12,570
Q is a logical way to structure it works on discipline, FIFO, first in, first out.

3
00:00:13,580 --> 00:00:20,690
Like in daily life, we find various places we form to, like people are standing in the queue to avail

4
00:00:20,690 --> 00:00:26,900
some services or for submitting their application, our cars are standing in the queue at both.

5
00:00:27,230 --> 00:00:30,940
All your application is in queue, even our phone calls.

6
00:00:30,980 --> 00:00:35,360
Sometimes if the lines are busy, we get a message that you are in queue.

7
00:00:35,360 --> 00:00:36,140
Please wait.

8
00:00:37,190 --> 00:00:42,890
Our customer care, if you're calling, then we also get a message that you are in queue, our customer

9
00:00:42,890 --> 00:00:45,100
representatives are busy, please wait.

10
00:00:45,560 --> 00:00:48,020
So we find a queue at various places.

11
00:00:48,260 --> 00:00:55,460
So queue is commonly used in various applications and Simbi in various algorithms, queue is use.

12
00:00:55,460 --> 00:00:58,050
So queue is one of the basic data structure.

13
00:00:58,940 --> 00:01:04,959
So here is an example of a queue showing people are standing in a queue and this is like a counter.

14
00:01:05,090 --> 00:01:10,160
So they are waiting in the queue to get their done to avail some services or do whatever the work they

15
00:01:10,160 --> 00:01:10,370
have.

16
00:01:10,520 --> 00:01:12,020
So this works on discipline.

17
00:01:12,290 --> 00:01:14,330
Fee for that is first in.

18
00:01:14,330 --> 00:01:15,040
First out.

19
00:01:15,820 --> 00:01:26,050
Google have to end like this, and it's called front end, and this is called as red, and if a person

20
00:01:26,050 --> 00:01:30,750
wants to join a queue that he should come and stand at the end of a queue.

21
00:01:30,760 --> 00:01:31,500
That is right.

22
00:01:31,510 --> 00:01:34,960
And so I can call it as insert.

23
00:01:34,960 --> 00:01:37,720
So insertions are done at the rear end.

24
00:01:38,470 --> 00:01:43,470
And if this person has finished his work at the counter, then he can go from the queue.

25
00:01:43,690 --> 00:01:46,850
So this we can call it as a delete and deletion.

26
00:01:46,850 --> 00:01:48,520
This is done from line.

27
00:01:49,330 --> 00:01:54,480
So insertion is done from one end and deletion incident from another end for both.

28
00:01:54,480 --> 00:01:58,630
The ends are used here, whereas if you remember in the stack.

29
00:02:00,560 --> 00:02:06,280
Like, this is a can, so insolation is done from this opening and the deletion is also done from the

30
00:02:06,290 --> 00:02:09,150
same opening if you have some balls in the can.

31
00:02:09,560 --> 00:02:13,850
This is the only end from where you insert and from this and only you can delete.

32
00:02:14,070 --> 00:02:18,560
But here in kill you insert agrarian and delete from.

33
00:02:19,700 --> 00:02:25,290
So let us look at ADT or cube abstract data type of cube.

34
00:02:25,760 --> 00:02:27,260
Let us look at Guity.

35
00:02:27,410 --> 00:02:29,750
First of all, we will look at data.

36
00:02:30,440 --> 00:02:36,050
First of all, we need is space for storing elements or storing objects.

37
00:02:36,970 --> 00:02:40,850
Next, we need a three pointer that is useful for deleting the elements.

38
00:02:40,870 --> 00:02:45,800
It may be pointing up on or before the first element, but it is used for deletion of elements.

39
00:02:46,270 --> 00:02:50,970
The next thing we need is a real pointer that is used for inserting that element.

40
00:02:50,980 --> 00:02:56,560
It may be pointing up on last element or after the last element, but it is used for insertion.

41
00:02:56,950 --> 00:02:59,050
So this is about data that we need.

42
00:02:59,350 --> 00:03:01,630
Space front pointer, Red Pointer.

43
00:03:02,050 --> 00:03:04,900
Now, let us look at the operations on the cube.

44
00:03:05,710 --> 00:03:09,400
First operation is NQ Anchorman's inserting an element in the cube.

45
00:03:09,400 --> 00:03:15,280
So insertions is done from the and like if a person wants to stand in the queue, then he has to come

46
00:03:15,280 --> 00:03:16,090
and stand here.

47
00:03:16,670 --> 00:03:21,010
And then second operation, the A documents are deleting an element.

48
00:03:21,550 --> 00:03:26,980
Deleting an element is done from Fronton means if a person has to leave a queue, then he should go

49
00:03:26,980 --> 00:03:27,690
out from here.

50
00:03:27,700 --> 00:03:33,490
Once his work has finished, then the other simple function like is empty checking would accuse him

51
00:03:33,700 --> 00:03:34,180
of not.

52
00:03:34,630 --> 00:03:40,030
And as a fool, if the queue is a full or not, then if at all, if you want you can write down other

53
00:03:40,030 --> 00:03:40,530
functions.

54
00:03:40,540 --> 00:03:45,060
Also knowing what is the first element in the queue, knowing what is the last element in the queue,

55
00:03:45,130 --> 00:03:45,670
if at all.

56
00:03:45,670 --> 00:03:51,940
You can add them and if you want, you can write more functions like displaying a queue or counting

57
00:03:51,940 --> 00:03:54,070
the number of elements in the queue.

58
00:03:54,790 --> 00:03:58,370
These type of operations are your choice as part of your requirement.

59
00:03:58,690 --> 00:04:01,060
So this is the ability of a queue.

60
00:04:01,840 --> 00:04:07,900
Now, next queue can be implemented using two physical data structures that are.

61
00:04:08,900 --> 00:04:18,440
Ari and Linda, just so you can be implemented using any of these two data structure, Ari, as the

62
00:04:18,440 --> 00:04:25,640
last link, so we have to learn how to implement a using other than how to implement a coup using Lindqvist,

63
00:04:26,060 --> 00:04:27,430
that's all in interaction.

64
00:04:27,740 --> 00:04:31,810
So the next few will learn how to implement a Q using a.

