1
00:00:00,120 --> 00:00:05,340
In this video, we will write a function to check whether Linklaters already sorted or not.

2
00:00:05,370 --> 00:00:11,320
So here I have a main function already written and I have a linked list which is already sorted.

3
00:00:11,340 --> 00:00:15,600
So before the main function, I will write a function to check whether Linklaters sorted or not.

4
00:00:15,630 --> 00:00:16,830
So let us write a function.

5
00:00:16,830 --> 00:00:19,770
Return type as integer means it will return boolean value.

6
00:00:19,770 --> 00:00:20,640
That is integer.

7
00:00:20,850 --> 00:00:22,070
That is true or false.

8
00:00:22,080 --> 00:00:29,160
That is zero or one function NamUs as Sa'adat and let the function take a pointer to Fast<PERSON>l and call

9
00:00:29,160 --> 00:00:30,630
the point and they must be know.

10
00:00:30,630 --> 00:00:34,960
As we have discussed, we need one variable to hold up previous Norks value.

11
00:00:34,980 --> 00:00:39,350
So the initial value in that variable X will be minimum value.

12
00:00:39,360 --> 00:00:44,790
So the minimum value for four byte integer is minus six five five three six.

13
00:00:45,000 --> 00:00:46,360
So this is the smallest number.

14
00:00:46,380 --> 00:00:53,490
Then I have to travel through a linguist by using this pointer B and if any point if the value is smaller

15
00:00:53,490 --> 00:00:55,840
than the previous value, then I should stop.

16
00:00:55,860 --> 00:01:00,780
So already we have discussed the code, so let us write on the code while B is not equal to null.

17
00:01:01,020 --> 00:01:02,820
So this will be our single interest.

18
00:01:02,820 --> 00:01:10,950
And at any time, if a piece of data that is currently N data is less than previous, no data then return

19
00:01:10,950 --> 00:01:12,500
false false zero.

20
00:01:12,540 --> 00:01:19,020
Otherwise, we will copy the data of this current note before moving on to the next node, then be moves

21
00:01:19,020 --> 00:01:21,560
to the next node at the end of a loop.

22
00:01:21,570 --> 00:01:27,060
Once we have reached the end of a link list, we will return one thing that the Linklaters the file.

23
00:01:27,090 --> 00:01:30,330
It's a simple function now here inside the mean function.

24
00:01:30,330 --> 00:01:36,390
Already we have a link which is already sorted, so let us all that function and print its results so

25
00:01:36,390 --> 00:01:37,020
I will directly.

26
00:01:37,050 --> 00:01:40,290
I don't think f but it's untidy and a new line.

27
00:01:40,290 --> 00:01:44,460
Then here I will call function is sorted by passing pointer.

28
00:01:44,460 --> 00:01:46,200
First let us run this.

29
00:01:46,230 --> 00:01:48,480
Yes, it has written the result one.

30
00:01:48,480 --> 00:01:52,900
I'll change a number here and 2013 Megadeath three.

31
00:01:53,430 --> 00:01:56,150
So now the Linklaters not Sodexho should return zettl.

32
00:01:56,190 --> 00:01:59,500
Yes it is returning zettl so it's working perfectly.

33
00:01:59,520 --> 00:02:02,090
So instead of print I'll just write the code here.

34
00:02:02,130 --> 00:02:04,430
I'll display whether that is sorted or not sorted.

35
00:02:04,440 --> 00:02:09,509
So F is a sorted if sorted function will return either true or false.

36
00:02:09,509 --> 00:02:15,060
If it returns true then we will print sorted and nothing else we will print.

37
00:02:15,240 --> 00:02:17,180
Not sorted out a new line.

38
00:02:17,190 --> 00:02:17,730
That's all.

39
00:02:18,300 --> 00:02:19,380
I will hide this display.

40
00:02:19,440 --> 00:02:20,970
Functional government.

41
00:02:21,380 --> 00:02:23,330
This is coming from the previous programme.

42
00:02:23,430 --> 00:02:24,470
Let us run this now.

43
00:02:24,480 --> 00:02:26,030
I should get the message not sorted.

44
00:02:26,040 --> 00:02:28,140
Yes, not sorted is a message.

45
00:02:28,140 --> 00:02:32,150
Al Qaeda's number two thirty and let us run once again.

46
00:02:32,160 --> 00:02:33,450
I should get a message that is.

47
00:02:34,290 --> 00:02:35,090
Yes, it is.

48
00:02:35,550 --> 00:02:38,070
So that's all this function you can to this function.

