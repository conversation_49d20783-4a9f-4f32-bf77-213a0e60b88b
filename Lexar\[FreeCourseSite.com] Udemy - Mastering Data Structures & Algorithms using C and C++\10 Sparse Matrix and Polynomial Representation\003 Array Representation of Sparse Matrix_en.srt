1
00:00:00,210 --> 00:00:04,830
And this video will learn about sports mechanics, so these are the things that I'm going to discuss

2
00:00:05,190 --> 00:00:12,940
how to represent this in our program, then how to create this sports, make sense of this present,

3
00:00:13,080 --> 00:00:19,990
like this stuff to demonstrate how it is presented as a coordinate list or a three column method.

4
00:00:20,440 --> 00:00:23,730
Then next, I will show how to add to my process.

5
00:00:23,760 --> 00:00:26,910
So we will write on the code here for creation.

6
00:00:26,910 --> 00:00:32,610
In addition to the complete program, I will write the program and show you the complete program.

7
00:00:32,640 --> 00:00:34,320
We will look in that material.

8
00:00:34,860 --> 00:00:41,370
Now, let's start with representation in this example, sports mechanics, the soft dimension, four,

9
00:00:41,700 --> 00:00:43,830
five, that is four and five columns.

10
00:00:44,400 --> 00:00:46,230
There are five non-zero elements.

11
00:00:46,230 --> 00:00:48,420
One, two, three, four, five.

12
00:00:49,190 --> 00:00:50,540
So this first.

13
00:00:52,120 --> 00:01:00,760
Topal is about dimension and number of non-zero elements, then these are all non-zero elements, like

14
00:01:00,760 --> 00:01:04,250
the first element of the first row and third column and element is seven.

15
00:01:04,269 --> 00:01:06,550
So first of all, third column and element the seven.

16
00:01:07,470 --> 00:01:14,450
So for each element, we require three things that says Rodenberg column number and the element itself.

17
00:01:14,480 --> 00:01:16,860
I'm calling them as IGY and X.

18
00:01:17,250 --> 00:01:22,650
So for one single element, we need three values so we can combine these three values and we can define

19
00:01:22,650 --> 00:01:24,270
our structure.

20
00:01:26,080 --> 00:01:29,910
Let us define a structure called element struct.

21
00:01:30,750 --> 00:01:33,420
Element that element is having.

22
00:01:34,940 --> 00:01:45,500
I as a rule, no G as a column member and integer the value element itself.

23
00:01:46,010 --> 00:01:51,130
These are the things that defines a single element in sports fanatics.

24
00:01:51,530 --> 00:01:57,040
So for this, we need an array of elements in a sports matrix.

25
00:01:57,050 --> 00:02:00,080
One, two, three, four, five, five elements are there.

26
00:02:00,080 --> 00:02:04,940
So we need an array of elements as well as we need dimensions.

27
00:02:05,090 --> 00:02:09,680
That is M and and and number of non-zero elements.

28
00:02:09,960 --> 00:02:15,640
So let us call this dimension size and cross and and also a number of elements.

29
00:02:16,160 --> 00:02:17,530
So let us define a.

30
00:02:18,770 --> 00:02:25,160
So let us look at again what are the things that we need, we need em and a number of elements and the

31
00:02:25,340 --> 00:02:27,080
list of elements.

32
00:02:28,220 --> 00:02:30,980
So let us define a structure for mattocks.

33
00:02:31,900 --> 00:02:32,650
Struck.

34
00:02:35,860 --> 00:02:36,760
Spa's.

35
00:02:38,240 --> 00:02:48,380
Doesn't having em for a number of rules and for a number of columns, let us call it as a new name for

36
00:02:48,380 --> 00:02:54,890
a number of non-zero elements, that is this fun number of elements fighting on the elements other than.

37
00:02:55,220 --> 00:02:59,150
So these three things are defined here by these three variables.

38
00:02:59,370 --> 00:03:07,400
Then the rest of the elements we can define the array of these elements so struck.

39
00:03:10,910 --> 00:03:14,660
Element E, I need an.

40
00:03:15,770 --> 00:03:23,000
So shall we declare it as an array of some size, if I declare an area of some size, then it will be

41
00:03:23,000 --> 00:03:23,520
limited.

42
00:03:23,540 --> 00:03:25,580
It will be for only those many elements.

43
00:03:25,880 --> 00:03:31,590
Then instead of taking as static size, Uhry, let us take it as a dynamic size.

44
00:03:32,000 --> 00:03:34,640
So for that, I'll just keep it as a pointer.

45
00:03:35,030 --> 00:03:37,900
E is set of elements.

46
00:03:37,910 --> 00:03:38,930
That is a pointer.

47
00:03:39,170 --> 00:03:41,030
Then dynamically we will create this.

48
00:03:41,930 --> 00:03:42,800
So that's all.

49
00:03:43,010 --> 00:03:47,190
This is the structure for Spa's mattocks matrix.

50
00:03:48,230 --> 00:03:49,340
Let us read it again.

51
00:03:49,430 --> 00:03:54,380
Spasmodic structure is containing a number of rules, number of columns, number of non-zero elements

52
00:03:54,380 --> 00:03:58,250
and the array of elements which has to be created dynamically.

53
00:03:58,640 --> 00:04:03,260
Now let us see how to use this structure and create spox somatics.

54
00:04:04,220 --> 00:04:06,300
So I'll remove this and use this space.

55
00:04:07,160 --> 00:04:15,280
Now let us look at how to create the space networks so far that I will try to function so facilities

56
00:04:15,280 --> 00:04:17,959
see that I have a main function.

57
00:04:19,040 --> 00:04:24,800
And inside the main function, I have created a spot matrix object.

58
00:04:25,190 --> 00:04:32,660
So struck sparse as this is one of these spots, Max.

59
00:04:33,260 --> 00:04:35,110
This is one spot, Max.

60
00:04:35,990 --> 00:04:37,010
I like this.

61
00:04:37,010 --> 00:04:39,600
If you want more, you can take more space mattresses.

62
00:04:39,620 --> 00:04:40,750
So I have taken just one.

63
00:04:41,120 --> 00:04:48,290
So as a response to my picks, if you look at it side as one of the things it is having, so is having.

64
00:04:49,460 --> 00:04:57,170
And that is a number of rules and number of columns and none that is number of non-zero elements and

65
00:04:57,320 --> 00:05:01,190
e that is a pointer which can hold a list of elements.

66
00:05:01,490 --> 00:05:06,860
So this is M and number of elements and it is a pointer for a list of elements.

67
00:05:06,860 --> 00:05:10,370
So this is how I got a structure, an object object.

68
00:05:11,480 --> 00:05:14,070
Now I will have a function called create.

69
00:05:14,330 --> 00:05:20,780
So let us call a function as a created, which will send us a structure by reference.

70
00:05:20,810 --> 00:05:24,830
Now here I will write on that create function void.

71
00:05:25,980 --> 00:05:35,910
Create this function, takes a structure type pointer, so the response matrix type pointer struck and

72
00:05:35,910 --> 00:05:38,740
let us call this pointer as s only.

73
00:05:39,120 --> 00:05:43,530
So this function is having a pointer s, which is pointing on this one.

74
00:05:44,550 --> 00:05:51,350
So this function can access this structure, which actually belongs to main function that is sparse

75
00:05:51,360 --> 00:05:52,230
mapping structure.

76
00:05:52,590 --> 00:05:57,810
Now, what I'm going to do in this create function, I'm going to fill all these values that is number

77
00:05:57,810 --> 00:06:00,390
of rows, number of columns and number of non-zero elements.

78
00:06:00,600 --> 00:06:02,660
And I will take all the elements and fill them.

79
00:06:02,940 --> 00:06:04,930
So I read on the code for all those things.

80
00:06:05,790 --> 00:06:11,380
So let us write the very first thing I want to know how many rules and how many columns.

81
00:06:11,520 --> 00:06:13,300
They don't know how many non-zero elements are there.

82
00:06:13,590 --> 00:06:22,980
So let us ask it and take it from keyboard printers and other dimensions, then read the dimensions.

83
00:06:22,980 --> 00:06:25,400
What are those, Emman, and these values?

84
00:06:25,410 --> 00:06:28,840
I want to read them, so I will be reading them through this.

85
00:06:29,040 --> 00:06:30,680
So I will store the values here.

86
00:06:31,380 --> 00:06:35,530
See, I'm reading values and person s i m.

87
00:06:35,730 --> 00:06:44,130
S Arrow and then ampersand s and s and so this actually belongs to me in function, but this is being

88
00:06:44,130 --> 00:06:45,350
filled by this function.

89
00:06:46,320 --> 00:06:49,070
So both values are stored.

90
00:06:49,890 --> 00:06:52,370
Now I need to know how many non-zero elements are there.

91
00:06:52,380 --> 00:06:55,560
So printers and the number of non-zero elements.

92
00:06:55,770 --> 00:06:58,820
So and the number of non zero I have just written on zero.

93
00:06:58,830 --> 00:07:00,300
So we want non-zero elements.

94
00:07:00,660 --> 00:07:04,460
No scanner read the number of non-zero elements and stored in this one.

95
00:07:04,590 --> 00:07:12,140
So scan F unperson s no as I now let us read it as a Kamps.

96
00:07:12,150 --> 00:07:15,870
Actually this is not a cat symbol, but as caps.

97
00:07:15,870 --> 00:07:18,660
No actually caps implements this one.

98
00:07:18,700 --> 00:07:19,500
This is caps.

99
00:07:20,040 --> 00:07:28,260
But this arrow instead of using Tomaro I feel comfortable calling that cats so as caps now so reading

100
00:07:28,260 --> 00:07:35,160
the number of non-zero elements, the source of the values that are filled according to this example,

101
00:07:35,400 --> 00:07:40,620
this is a forward number of rules and this is five number of columns and this is five.

102
00:07:40,620 --> 00:07:42,300
That is number of non-zero elements.

103
00:07:42,630 --> 00:07:44,340
Then what should be the size of this?

104
00:07:44,730 --> 00:07:46,830
I have to create this array dynamically.

105
00:07:47,100 --> 00:07:51,710
I should have an array of those the structure of size of five.

106
00:07:51,900 --> 00:07:56,190
So let us create an array of size, the five elements.

107
00:07:56,520 --> 00:08:00,830
So I have to pick the objects of elements and make an array of elements.

108
00:08:01,170 --> 00:08:05,760
So let us write on the code as Gab's e.

109
00:08:06,870 --> 00:08:14,680
A sign I have to create an array of elements of size of five, and this is pointing there.

110
00:08:14,710 --> 00:08:20,460
So this is I, G and X, I should have an array of size five for creating that.

111
00:08:21,030 --> 00:08:25,490
We have to dynamically look at the memory for that we have to use mellark function.

112
00:08:25,770 --> 00:08:29,310
But I said that I'll be using new Obledo of C++.

113
00:08:29,320 --> 00:08:30,390
That is easy to write.

114
00:08:30,390 --> 00:08:35,130
It will reduce the size of the code, although we have discussed how to write function.

115
00:08:35,730 --> 00:08:39,740
Let us write new new new ideas.

116
00:08:39,750 --> 00:08:46,600
What array of elements of what size the size is here, the number of elements.

117
00:08:46,600 --> 00:08:47,190
So C.

118
00:08:47,450 --> 00:08:51,410
S gabs num whatever the number we have taken.

119
00:08:51,960 --> 00:08:55,930
So this will create this array of elements.

120
00:08:56,430 --> 00:08:58,350
So this is ready then.

121
00:08:58,350 --> 00:09:05,700
I have to fill all these elements one by one so I can see enter all non-zero elements and I can read

122
00:09:05,700 --> 00:09:10,770
all of them one by one so far that I can use a follow up and read all of them one by one.

123
00:09:11,160 --> 00:09:13,050
So let us write the code here.

124
00:09:14,490 --> 00:09:16,440
And there are all non-zero elements.

125
00:09:16,440 --> 00:09:23,280
So print this, enter all elements and these elements, each will be having rule number, column number

126
00:09:23,280 --> 00:09:24,270
and element itself.

127
00:09:24,280 --> 00:09:30,100
So I have to read all three values for each element so I can use a follow up and read all of them so

128
00:09:30,100 --> 00:09:34,170
far that I may need some variable like i.e. for running a follow up.

129
00:09:34,560 --> 00:09:41,550
So far I assign the zero I is less than number of elements.

130
00:09:41,550 --> 00:09:52,010
So this is as gabs num and I plus plus then inside the for loop I have to read all the elements now.

131
00:09:52,090 --> 00:09:58,140
Already we have understood how our main function can create an object of sparse matrix and call it for

132
00:09:58,140 --> 00:10:01,350
creation, the same way for main function.

133
00:10:01,350 --> 00:10:04,380
We call for addition, subtraction, whatever we want to do.

134
00:10:04,770 --> 00:10:08,880
So I will remove this main function and I will finish the code for creation.

135
00:10:09,660 --> 00:10:11,520
So here we have to read three values.

136
00:10:11,520 --> 00:10:12,780
So sketchiness.

137
00:10:13,860 --> 00:10:18,930
See, I have to read revalues askaris percentile, 80 percent, 90 percent, and I have to read all

138
00:10:18,930 --> 00:10:20,300
these values tremendous.

139
00:10:20,880 --> 00:10:25,470
So here I have written just one Ambuhl as the E.

140
00:10:25,870 --> 00:10:29,180
S e of I.

141
00:10:29,340 --> 00:10:31,530
So I starting from zero to none.

142
00:10:31,860 --> 00:10:41,100
So I initially the societal then that I hear we have the wrong number on this one, then ampersand is

143
00:10:41,100 --> 00:10:41,370
there.

144
00:10:41,370 --> 00:10:42,370
So it's address.

145
00:10:42,510 --> 00:10:44,660
So at this place value will be written.

146
00:10:44,970 --> 00:10:48,360
So the first word you enter from the keyboard is for this one, then one will put it on.

147
00:10:49,360 --> 00:10:52,460
Then likewise, I have to read other Tuvalu's also.

148
00:10:52,480 --> 00:10:54,880
So there is no space, I'll just write below this one.

149
00:10:55,120 --> 00:11:03,370
So Scheps e of I don't g ampersand escap e of idot x.

150
00:11:04,870 --> 00:11:07,270
This is how I can scan all Trevelyan's.

151
00:11:07,420 --> 00:11:10,070
So that's all does create function.

152
00:11:10,330 --> 00:11:13,710
Next we will see how to add two spots, Makassar.

153
00:11:13,720 --> 00:11:18,640
So for that I'll just write the function, the code for adding two spots.

154
00:11:18,640 --> 00:11:21,210
Madrassahs using the same representation.

