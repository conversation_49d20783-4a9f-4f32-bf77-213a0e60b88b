1
00:00:00,270 --> 00:00:04,110
In this video, we'll try to C++ class for Splotched Magnox.

2
00:00:06,080 --> 00:00:07,610
I'll give the project Nimmons.

3
00:00:09,050 --> 00:00:11,900
Spartz, Mattocks CBB.

4
00:00:13,640 --> 00:00:17,560
And the language I selected as C++ on next.

5
00:00:18,780 --> 00:00:19,670
Freedom Project.

6
00:00:22,850 --> 00:00:24,230
Here is a main function.

7
00:00:25,350 --> 00:00:27,120
I'll clear of all these comments.

8
00:00:28,800 --> 00:00:29,730
The project is really.

9
00:00:31,580 --> 00:00:38,330
So as we have already seen a program using C language, so did we have user structures, but here we

10
00:00:38,330 --> 00:00:39,710
are going to use classis.

11
00:00:41,000 --> 00:00:46,880
Not a lot to be focusing on the logic, but we'll be focusing on the style of writing C++ program.

12
00:00:48,060 --> 00:00:50,730
So first of all, we need a class for Elliman.

13
00:00:54,190 --> 00:00:59,790
And inside this glass, I'll make everything public Element is having three day members, that is Foster's

14
00:00:59,800 --> 00:01:01,990
or No column No.

15
00:01:04,940 --> 00:01:11,090
Column number and the element itself, so I make them public so that they can be directly accessible.

16
00:01:13,270 --> 00:01:18,580
The next, we will drive to class four spots, matics, al-khalidi spots.

17
00:01:19,760 --> 00:01:24,890
Now, this glass should have the data numbers, I'll make it numbers as private.

18
00:01:25,860 --> 00:01:33,210
First is the dimension, so for that M and next, and and also we should know the number of non-zero

19
00:01:33,210 --> 00:01:36,980
elements, then we need an array of elements.

20
00:01:36,990 --> 00:01:38,670
So I will declare.

21
00:01:39,800 --> 00:01:48,320
Element Eataly, and this we have taken as a pointer so that we can create other Michali in a heap depending

22
00:01:48,320 --> 00:01:49,520
on the required size.

23
00:01:52,100 --> 00:01:58,430
That said, they are over nine, say, public block, we will have all the functions or member functions

24
00:01:58,430 --> 00:01:59,180
of a class.

25
00:01:59,480 --> 00:02:01,310
So first as a constructor.

26
00:02:03,660 --> 00:02:06,720
So in the constructor will create or initialize this as.

27
00:02:07,390 --> 00:02:12,090
So it means dimension number of non-zero elements, three things it needs.

28
00:02:12,510 --> 00:02:14,980
So first, I will take it as a form.

29
00:02:15,000 --> 00:02:20,000
And next and this is four dimension and the number of non-zero elements that is NUM.

30
00:02:21,660 --> 00:02:27,210
Now, constructor is used for initializing these members, so all these members, I will initialize

31
00:02:27,210 --> 00:02:36,210
them here and the names I have taken seem so I have to use this pointer this and assign them and this

32
00:02:36,210 --> 00:02:37,350
and assign.

33
00:02:38,220 --> 00:02:38,670
And.

34
00:02:40,000 --> 00:02:47,290
Then this number should assignment number and numbers the number of non-zero elements so far, that

35
00:02:47,290 --> 00:02:52,070
I should dynamically create an array of elements from heap offices.

36
00:02:52,070 --> 00:02:55,870
And so here element assign new.

37
00:02:57,730 --> 00:03:01,530
Element of surprise, this dart, no.

38
00:03:04,170 --> 00:03:05,160
There's a constructor.

39
00:03:07,370 --> 00:03:12,890
So here I must have a parametrized constructor only because I cannot take any default dimensions or

40
00:03:12,890 --> 00:03:14,390
default number of elements.

41
00:03:15,820 --> 00:03:17,570
So he cannot have known parametrized.

42
00:03:21,090 --> 00:03:22,980
Then I will write this Dr..

43
00:03:26,930 --> 00:03:33,440
The stricter will delete or dynamically created, so just say delete and subscript operator and the

44
00:03:33,440 --> 00:03:35,580
other name is easily erased.

45
00:03:35,690 --> 00:03:36,230
Deleted.

46
00:03:38,640 --> 00:03:39,390
Tetsuro, or.

47
00:03:41,470 --> 00:03:47,020
If you remember, we have written this piece of code as a part of three function that we were creating

48
00:03:47,020 --> 00:03:51,760
a spot matrix and in the creation process, we were also taking all the elements.

49
00:03:52,270 --> 00:03:56,770
But here for reading all the elements, I have to write a separate function.

50
00:03:56,770 --> 00:04:00,460
So I will call the function names read void.

51
00:04:02,570 --> 00:04:09,170
Read Soulet dysfunction, read all the elements, so other dimensions are given just it has to read

52
00:04:09,170 --> 00:04:13,760
non-zero elements so called enter non-zero elements.

53
00:04:19,260 --> 00:04:25,250
So for reading all Longido elements in this, at least, I should use a follow up and read all non-zero

54
00:04:25,290 --> 00:04:31,240
elements and each non-zero element, I need a wrong number, column number and the element itself.

55
00:04:31,590 --> 00:04:34,500
So using followup, I'll do that for integer.

56
00:04:34,500 --> 00:04:38,010
I assign zero I it's less than num.

57
00:04:38,970 --> 00:04:40,290
Then I plus plus.

58
00:04:41,220 --> 00:04:44,070
And I should seen three values, that is.

59
00:04:45,500 --> 00:04:57,410
Eyerly of I thought I that is rule number then element of I thought, gee, that is column number then

60
00:04:57,410 --> 00:04:58,400
element of.

61
00:05:00,130 --> 00:05:00,610
I.

62
00:05:01,980 --> 00:05:04,500
But that is limited since.

63
00:05:06,880 --> 00:05:12,940
See, here I am able to directly say Eyerly of I, because this is a member of the same class that is

64
00:05:13,150 --> 00:05:18,760
Cymatics and the function, Reid is a member of the same class, so it can directly access these members.

65
00:05:22,220 --> 00:05:28,070
This court will read all the elements from other spots, Mattocks, then I will also have a function

66
00:05:28,070 --> 00:05:31,580
for displaying a sports, so I will call the function name as.

67
00:05:32,610 --> 00:05:33,210
Display.

68
00:05:37,210 --> 00:05:43,660
For displaying, I will display other metrics, that is the two dimensions, so for this I will take

69
00:05:43,660 --> 00:05:44,460
to followups.

70
00:05:46,520 --> 00:05:48,650
I think zero is less than.

71
00:05:50,880 --> 00:05:53,070
M and I placeless.

72
00:05:58,230 --> 00:06:06,150
The next followable for G indigency assign zero and GS Lesbian and Angie plus plus.

73
00:06:07,870 --> 00:06:13,330
Then inside this followup, I will display an element if there is non-zero element available for any

74
00:06:13,330 --> 00:06:20,020
Ikoma so far traversing through this past matrix, I need one more variable that will keep track of

75
00:06:20,020 --> 00:06:22,510
non-zero elements in other eyerly.

76
00:06:22,960 --> 00:06:26,530
So if eyerly of key.

77
00:06:28,230 --> 00:06:35,510
I is equal to I as well as Eyerly of gay.

78
00:06:36,780 --> 00:06:45,930
Dot G is equal to G, if they are equal, then I will bring the element from Eyerly so in Element from

79
00:06:45,930 --> 00:06:48,060
Aliev K dot x.

80
00:06:49,060 --> 00:06:50,920
And also, I'll give some space.

81
00:06:53,230 --> 00:06:59,240
And the C++ also I should do as it is traversing a list of non-zero elements.

82
00:06:59,320 --> 00:07:00,640
It should move to the next element.

83
00:07:03,100 --> 00:07:07,600
If this condition is not true, then I should print little.

84
00:07:09,740 --> 00:07:15,830
So I'm getting zero elements also then after the end of the inner loop, I should give a new line that

85
00:07:15,830 --> 00:07:16,490
is then.

86
00:07:18,800 --> 00:07:24,740
That's all, and at the end of a class, I should have a semicolon after the end of the class and all

87
00:07:24,740 --> 00:07:29,900
these functions I have written inside the class itself, I have not used any scope resolution.

88
00:07:31,610 --> 00:07:36,290
Not here to use the scene and scout, I should use namespace, so I will say using.

89
00:07:38,750 --> 00:07:40,460
Namespace astrally.

90
00:07:43,930 --> 00:07:46,090
Now the glass is ready for some extent.

91
00:07:46,990 --> 00:07:48,690
Now the glass is ready to some extent.

92
00:07:49,060 --> 00:07:52,720
Now inside mean function, I will create an object of space mattocks.

93
00:07:53,610 --> 00:07:58,980
And here, the dimensions and the number of non-zero elements, I will directly pass them, I will not

94
00:07:58,980 --> 00:08:00,110
take them from keyboard.

95
00:08:00,450 --> 00:08:04,680
So dimensions are five, comma five and a number of non-zero elements are five.

96
00:08:07,020 --> 00:08:13,560
Then I should read all the elements of S1 that read so read function, read all the elements, then

97
00:08:13,560 --> 00:08:17,860
I will say as standard display, then it will display all elements.

98
00:08:18,720 --> 00:08:21,210
So this is so up to here.

99
00:08:21,210 --> 00:08:26,260
We have finished creating a sports matics, reading its elements, then displaying an element.

100
00:08:26,280 --> 00:08:30,330
So here we are creating it and here we are reading the elements and then we are displaying it.

101
00:08:31,670 --> 00:08:33,950
So, Ilya, let us run the program and check.

102
00:08:36,010 --> 00:08:40,500
And this program, we are not getting a message and dimensions because already we have given the dimensions,

103
00:08:40,530 --> 00:08:44,740
Cyprus and the number of non-zero elements of five to simply have to include elements.

104
00:08:44,800 --> 00:08:46,300
I will enter diagonal elements.

105
00:08:47,370 --> 00:08:53,630
Zero zero element is one one comma, one element, this one, and then to commit to eliminate this one

106
00:08:53,640 --> 00:08:55,980
three commentary is also one four comma.

107
00:08:55,980 --> 00:08:59,630
For one night, I should get a matrix of five by five.

108
00:08:59,640 --> 00:09:01,160
It all diagonal elements as one.

109
00:09:01,890 --> 00:09:03,210
Yes, it's working.

110
00:09:04,420 --> 00:09:08,890
So here we have finished, we got a mattocks display, the display and tarmac's.

