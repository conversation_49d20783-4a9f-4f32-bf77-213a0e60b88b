1
00:00:00,660 --> 00:00:07,470
Now, let us look at an algorithm or a program for performing at for such the same graph, which I have

2
00:00:07,470 --> 00:00:15,180
already explained you for, that I have a different Cymatics seven versus the order from Texas eight

3
00:00:15,180 --> 00:00:15,810
by eight.

4
00:00:15,810 --> 00:00:20,250
Does that to damage the array of size, eight by eight, because the vertex number is starting from

5
00:00:20,250 --> 00:00:20,970
one on, what, four?

6
00:00:20,970 --> 00:00:22,160
I, I'm not using indexes.

7
00:00:22,160 --> 00:00:22,650
Zeitels.

8
00:00:24,220 --> 00:00:28,730
I left them blank, so whatever the values there in them, I don't bother because I'm not using some

9
00:00:28,750 --> 00:00:30,910
that I'm using from one moment on.

10
00:00:31,360 --> 00:00:33,700
So I already have said just semantics.

11
00:00:33,700 --> 00:00:40,300
Wherever there is a niche market as one and remaining are Zeitels for performing, but for search,

12
00:00:40,300 --> 00:00:43,810
I should know which this is already visited and which are yet to be visited.

13
00:00:44,110 --> 00:00:46,850
So far that I have taken a look at that is called visiting.

14
00:00:47,650 --> 00:00:54,420
Then also we need one data structure for Q That is where we insert and delete or incomplete you.

15
00:00:54,730 --> 00:00:55,470
What is this.

16
00:00:55,720 --> 00:00:59,290
So I also I have a Q now let us write on a function.

17
00:00:59,650 --> 00:01:00,640
So for function.

18
00:01:00,640 --> 00:01:06,430
First of all this should be initialized to zero means none of these.

19
00:01:06,440 --> 00:01:14,920
What is obviously for such as you do that for this algorithm or this function, all these are global.

20
00:01:15,310 --> 00:01:20,110
If you think that not only we should pass a test parameter or declare them locally, you can declare

21
00:01:20,110 --> 00:01:21,560
you can pass them as parameter.

22
00:01:21,910 --> 00:01:24,190
So while giving a demo, we will decide that time.

23
00:01:24,670 --> 00:01:28,590
But right now, function just takes integer, i.e. what is this?

24
00:01:28,630 --> 00:01:31,930
This is starting X, whatever the starting what exists.

25
00:01:32,230 --> 00:01:36,940
So I can call this function by passing one or four or five anything as a starting vertex.

26
00:01:36,940 --> 00:01:38,230
So this is the starting vertex.

27
00:01:38,780 --> 00:01:44,410
So if you remember the procedure, the first thing that we did is we have visited that first starting

28
00:01:44,410 --> 00:01:45,550
vertex, this vertex.

29
00:01:45,880 --> 00:01:55,690
So I mean, first of all, print print f percentile D I so whatever the starting number, suppose it

30
00:01:55,690 --> 00:01:57,090
is one that one is printed.

31
00:01:57,100 --> 00:02:01,560
So the output that is breakfast socializr, I will print it on the screen.

32
00:02:01,810 --> 00:02:08,949
So this is first vertex then I should the mark this vertex one as visitor, whichever the vertex is

33
00:02:08,949 --> 00:02:10,449
given I should microdots visitor.

34
00:02:10,690 --> 00:02:12,430
So I'm assuming it is one right.

35
00:02:12,550 --> 00:02:23,500
Let us say this is one then here visiting off of one I should mark it does one so visited of I mark

36
00:02:23,500 --> 00:02:24,220
it as one.

37
00:02:25,210 --> 00:02:26,710
So this is Marquitos one.

38
00:02:26,710 --> 00:02:27,970
So this already visited.

39
00:02:29,910 --> 00:02:36,120
Then I should ask you this, what is in that data structure so new?

40
00:02:38,340 --> 00:02:47,760
In a queue, what I see NQ dequeue and Arae for the coup that we already have learned, we are using

41
00:02:47,770 --> 00:02:51,120
that data structure, so we should have the complete implementation with us.

42
00:02:52,140 --> 00:02:58,610
This may be a ordinary presentation or it may be a link misrepresentation how the Q is implemented.

43
00:02:58,620 --> 00:03:02,070
We don't bother so we can borrow the code of Q here.

44
00:03:02,160 --> 00:03:07,320
So I'm getting a recording function and Q So this vertex one is inserted.

45
00:03:09,060 --> 00:03:16,800
Now the repeating procedure starts, look starts, so I'll try to loop condition, I will write it afterwards.

46
00:03:16,980 --> 00:03:18,510
So I read the condition afterwards.

47
00:03:19,120 --> 00:03:25,860
But in fact, look, what we have to do is take all the words from the Q So you assign.

48
00:03:27,340 --> 00:03:28,170
Dequeue.

49
00:03:30,310 --> 00:03:37,360
From Q So I'm taking what text that is deleted from the Q and A variable you so I will declare a variable.

50
00:03:37,880 --> 00:03:43,350
You so use the word text number that I'm going to explore, I have to explore.

51
00:03:43,570 --> 00:03:45,040
So what is that number one.

52
00:03:45,460 --> 00:03:47,290
So this is you.

53
00:03:49,410 --> 00:03:54,870
I'm calling that's you know, what I'm exploring Vertex one, that I should visit all of this.

54
00:03:54,870 --> 00:03:55,470
And what is this?

55
00:03:55,470 --> 00:03:56,620
What are you seeing here?

56
00:03:56,940 --> 00:04:02,460
See, this is zero one one one two, one, two, three and four other Gissen remaining five, six,

57
00:04:02,460 --> 00:04:02,820
seven.

58
00:04:02,820 --> 00:04:03,620
They are nonexistent.

59
00:04:03,990 --> 00:04:05,400
So I should visit all this.

60
00:04:05,400 --> 00:04:11,490
And what it means if it is marked as one, it is a so for scanning to this whole row.

61
00:04:11,850 --> 00:04:19,980
I should have followed so far that I will take a look for V a sign starting in next one on words.

62
00:04:20,130 --> 00:04:29,370
One V is less than or equal to N and is the number of users and Azuma does global and is global then.

63
00:04:30,430 --> 00:04:36,850
We plus plus the slope will take me through all this, so I'm calling these what is ASV?

64
00:04:37,100 --> 00:04:38,530
So this is me, right?

65
00:04:38,650 --> 00:04:40,030
V zero vs one.

66
00:04:40,030 --> 00:04:48,340
So so wherever it is Magna's one, I should take it as a vertex visited and inserted in a queue.

67
00:04:48,550 --> 00:04:51,760
So while visiting this, I should check if.

68
00:04:53,820 --> 00:04:55,510
Let us say my name is a.

69
00:04:55,860 --> 00:05:08,880
So if any of you and V is equal to one that is adjacent and also one more thing, it should not be restricted

70
00:05:09,180 --> 00:05:11,100
to its not visiting.

71
00:05:11,280 --> 00:05:11,660
Right.

72
00:05:11,910 --> 00:05:17,670
If it is not visited on, I should visit it and visited visited off these equal to zero.

73
00:05:18,300 --> 00:05:19,520
It's not yet visited.

74
00:05:19,980 --> 00:05:24,600
If there is an edge and it is not visited, that I should do two things.

75
00:05:26,820 --> 00:05:31,320
Visit that vortex, so for visiting, I should first bring def.

76
00:05:32,880 --> 00:05:41,790
Percentile Tildy, what x, v and I should mark that word, X has visited, so visited off, we as one

77
00:05:43,050 --> 00:05:44,570
visited off V as one.

78
00:05:44,850 --> 00:05:47,670
And also I should insert that word here.

79
00:05:49,790 --> 00:05:57,080
And you that word next week, so these are the things I should do inside if this process should continue

80
00:05:57,080 --> 00:05:59,330
for this follow and also by loop.

81
00:05:59,720 --> 00:06:01,530
So that follows the repeating procedure.

82
00:06:02,060 --> 00:06:08,510
So you have written three statements and a single line printed market visitor and then drop it in the

83
00:06:08,510 --> 00:06:10,990
queue so that it should be explored next.

84
00:06:12,470 --> 00:06:15,530
So then this loop will visit all the and what it says.

85
00:06:15,530 --> 00:06:17,400
So who are two, three, four.

86
00:06:17,690 --> 00:06:23,390
So three will also be marked here and reasons for this Magda's visited and for this visitor.

87
00:06:23,510 --> 00:06:30,650
So everyone takes this printed, marked, visited and inserted the loop and then come back to why loop

88
00:06:31,070 --> 00:06:36,090
this way loop should again take out a vortex from the queue and start exploring it.

89
00:06:36,110 --> 00:06:37,710
So the next vertex will be two.

90
00:06:38,720 --> 00:06:40,210
So this process will continue.

91
00:06:40,610 --> 00:06:42,410
How long this loop should work?

92
00:06:42,680 --> 00:06:49,130
As long as the queue is not empty, not is empty queue.

93
00:06:50,390 --> 00:06:57,050
So this is excluding all the work us and this is visiting New Waters's.

94
00:06:58,570 --> 00:07:03,940
That's all this is the problem for that first try and give a demo for this one, when I write down the

95
00:07:03,940 --> 00:07:06,220
code, the same thing, I may be writing it.

96
00:07:07,240 --> 00:07:08,530
What, if any, change is required?

97
00:07:08,530 --> 00:07:09,220
I will do that.

98
00:07:10,110 --> 00:07:13,710
And one last thing in this one is how much time wind has taken.

99
00:07:15,220 --> 00:07:16,840
Loop inside that loop.

100
00:07:17,940 --> 00:07:25,440
How many times and what is this and what about the loop, as long as along empty me, how many workers

101
00:07:25,440 --> 00:07:26,400
are still coming in Cuba?

102
00:07:26,700 --> 00:07:27,600
Almost all.

103
00:07:27,870 --> 00:07:28,870
So how many witnesses?

104
00:07:28,890 --> 00:07:31,440
And so this is and this also.

105
00:07:31,490 --> 00:07:34,350
And so the time is how much and square.

106
00:07:35,560 --> 00:07:41,590
Or Dolphin Square, but if you remember in the previous video, I said that the time taken for bed for

107
00:07:41,620 --> 00:07:45,910
search is out of end because we had visiting A.D.s.

108
00:07:47,020 --> 00:07:54,520
Yes, that was analytical time, but really, when you write the program, we realize that we have to

109
00:07:54,760 --> 00:07:58,750
scan through this matrix all the elements and it's taking extra time.

110
00:07:59,980 --> 00:08:02,320
Then one more point I already told you about.

111
00:08:02,350 --> 00:08:07,720
I have discussed about a Edison, cymatics, that if any algorithm is using Edison, cymatics, that

112
00:08:07,720 --> 00:08:09,980
the time is dependent on and square.

113
00:08:10,300 --> 00:08:11,810
So, yes, it is sticking in square.

114
00:08:12,100 --> 00:08:18,900
So if you imagine this program will scan almost all the elements and the Mattocks Robledo.

115
00:08:19,090 --> 00:08:25,300
Yes, depending on whichever Vertex is being explored, it will scan all the elements in the.

116
00:08:26,950 --> 00:08:28,900
So the total Times and Square.

117
00:08:30,220 --> 00:08:36,100
So this is implementation wise, the Times Square, because the data structure we're using is mattocks,

118
00:08:36,159 --> 00:08:41,890
if you're not using matics, if you are using are just on the list, then the time may be different

119
00:08:42,010 --> 00:08:43,630
and it may be shorter.

120
00:08:43,929 --> 00:08:51,610
And because the time will be dependent on number of what it says and number of inches, so we can say

121
00:08:51,610 --> 00:08:52,740
it is ultrafine.

122
00:08:54,490 --> 00:08:59,290
So then the implementation using legal representation, I will directly give you a demo.

123
00:09:00,900 --> 00:09:01,940
That following the spirit.

