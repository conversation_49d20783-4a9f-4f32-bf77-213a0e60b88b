1
00:00:00,360 --> 00:00:01,230
Now, let us see her.

2
00:00:02,280 --> 00:00:09,140
See, this is a structure for her recursion, if there is a function that is calling itself so, it's

3
00:00:09,150 --> 00:00:16,860
a recursive function that the false statement inside the function as a recursive call, it is calling

4
00:00:16,860 --> 00:00:17,920
itself here.

5
00:00:18,360 --> 00:00:18,960
Then what?

6
00:00:18,960 --> 00:00:24,490
All the processing it has to do with is doing it afterwards after the recursive call.

7
00:00:24,750 --> 00:00:29,130
So there is no statement, no operation before the function call.

8
00:00:29,400 --> 00:00:33,840
See if there is something here, then it is not a hard recursion.

9
00:00:34,620 --> 00:00:35,070
Right.

10
00:00:35,100 --> 00:00:38,720
If something is there before the function, call just it is a recursion.

11
00:00:38,730 --> 00:00:41,810
We don't have to give any special name for that one.

12
00:00:42,090 --> 00:00:44,490
So there is nothing before that one.

13
00:00:44,500 --> 00:00:48,470
Then we can see this is a header recursion.

14
00:00:48,660 --> 00:00:50,260
So what do you mean by Haddrick?

15
00:00:50,940 --> 00:00:57,060
It means the function doesn't have to process or perform any operation at the time of calling.

16
00:00:57,360 --> 00:01:01,440
It has to do everything only at the time of returning.

17
00:01:01,740 --> 00:01:04,920
So all the processing is done at returning time.

18
00:01:05,160 --> 00:01:09,050
Then such functions are had recursion.

19
00:01:09,360 --> 00:01:14,820
So the example of this type of recursion already we have seen in the previous video, this is what the

20
00:01:14,820 --> 00:01:15,860
simple example.

21
00:01:16,050 --> 00:01:21,160
So you can see that the function is first calling itself, then the second statement.

22
00:01:21,160 --> 00:01:21,960
It is printing.

23
00:01:22,200 --> 00:01:27,800
So the painting will be done at returning time so the function doesn't do anything at the calling time.

24
00:01:28,620 --> 00:01:30,960
So such recursion had recursion.

25
00:01:31,860 --> 00:01:36,210
Next, we will compare her recursion versus loop.

26
00:01:36,210 --> 00:01:38,150
Can we convert them into loop?

27
00:01:38,490 --> 00:01:39,750
Let us discuss this.

28
00:01:40,260 --> 00:01:49,500
I will try to write the same function using loop, so let me write here wide function file not instead

29
00:01:49,500 --> 00:01:50,910
of conditional statement.

30
00:01:50,910 --> 00:01:56,930
I have to write a loop that I will you have shown you so if any greater than zero.

31
00:01:57,480 --> 00:01:58,770
No here.

32
00:01:58,770 --> 00:02:02,330
First it has to make a recursive call, then it is printing.

33
00:02:02,610 --> 00:02:11,130
So if you remember the output of this one, the output of dysfunction was one, two, three as it is

34
00:02:11,130 --> 00:02:12,980
going to print at any time.

35
00:02:12,990 --> 00:02:17,880
So if you remember Don said in the previous video, what else you can check back the result of this

36
00:02:17,880 --> 00:02:24,990
type of function as one, two, three, because it is spending entertaining time now as it is, I have

37
00:02:24,990 --> 00:02:27,540
tried to write the function with the loop.

38
00:02:27,960 --> 00:02:31,950
Now I want the output to be one, two, three, four.

39
00:02:31,950 --> 00:02:46,050
If I use the printer here, print deaf person daily and then if I am passing function fun with the volume

40
00:02:46,050 --> 00:02:49,410
three, then foster value often will be three.

41
00:02:49,680 --> 00:02:50,880
So three is greater than zero.

42
00:02:50,890 --> 00:02:51,320
Yes.

43
00:02:51,330 --> 00:02:53,190
So it will print what three.

44
00:02:53,400 --> 00:02:57,420
It will not print one first value that will print it will be three.

45
00:02:57,420 --> 00:02:58,580
It is not one.

46
00:02:59,910 --> 00:03:06,540
Then if I have to repeat them just like this one in stock that if I write and the minus minus then that

47
00:03:06,540 --> 00:03:08,360
doesn't work same as that one.

48
00:03:09,120 --> 00:03:11,400
So you cannot easily convert that.

49
00:03:11,400 --> 00:03:14,730
So you have to write it in some other way to get the same output.

50
00:03:15,030 --> 00:03:19,610
But as it is looking at the function, if you try to convert, it's not easy.

51
00:03:20,400 --> 00:03:27,690
We can write some function which spends one, two, three using loop, but looking at this function

52
00:03:27,690 --> 00:03:29,370
we cannot directly created.

53
00:03:29,910 --> 00:03:32,330
So it means I have to write it in a different way.

54
00:03:32,520 --> 00:03:33,720
So let me write it.

55
00:03:33,840 --> 00:03:42,720
Suppose I have to take some variable, i.e. and that is value one then I should say I is less than equal

56
00:03:42,720 --> 00:03:51,810
to N then here I should say I plus plus and I should make this a plus plus after print F so let us try

57
00:03:51,810 --> 00:03:53,040
to run and see this one.

58
00:03:53,370 --> 00:03:56,730
If we pass the value as a three that is and is a three.

59
00:03:56,730 --> 00:03:59,130
So I use one, one is less than three.

60
00:03:59,130 --> 00:04:01,020
So it will print I that is one.

61
00:04:01,020 --> 00:04:04,350
So output is one then it becomes two.

62
00:04:04,350 --> 00:04:06,270
So two, two is less than three.

63
00:04:06,270 --> 00:04:07,410
So it will print two.

64
00:04:07,590 --> 00:04:11,550
So it is to the nine plus, plus I becomes a three.

65
00:04:11,550 --> 00:04:13,320
So three is less than equal to three.

66
00:04:13,530 --> 00:04:14,610
So I use three.

67
00:04:14,610 --> 00:04:18,089
So it will print the three then C++ I becomes four.

68
00:04:18,360 --> 00:04:24,120
Four is not less than equal to three, so it will not enter in and it will finish the loop and it will

69
00:04:24,120 --> 00:04:24,630
come out.

70
00:04:24,960 --> 00:04:27,360
So the output is one, two, three.

71
00:04:27,930 --> 00:04:33,150
So that set, you can see that that function cannot be as it is converted.

72
00:04:33,600 --> 00:04:41,910
So precautions or if a recursive function has to do something at return in time, it cannot be easily

73
00:04:41,910 --> 00:04:45,780
converted in the form of a loop, but it can be converted.

74
00:04:46,020 --> 00:04:47,970
It doesn't look as ities.

75
00:04:49,110 --> 00:04:51,120
So that's all about ID.

76
00:04:51,330 --> 00:04:54,960
And the next video, we will see three recursions.

