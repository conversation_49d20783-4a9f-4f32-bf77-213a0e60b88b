1
00:00:00,350 --> 00:00:08,530
In the video, we will write C++ class for linguist, so I'm starting a new project for C++ class linked

2
00:00:08,670 --> 00:00:09,230
list.

3
00:00:10,230 --> 00:00:18,720
CPB, that is the project name and the language is C++, create a project, yes, create a project.

4
00:00:18,890 --> 00:00:20,430
Let us go to main function.

5
00:00:20,430 --> 00:00:22,220
I'll remove all these comments.

6
00:00:22,500 --> 00:00:22,790
Yeah.

7
00:00:22,800 --> 00:00:25,090
Let us start writing in class for Lincolnesque.

8
00:00:25,110 --> 00:00:29,670
I don't have to discuss much because already we have seen the procedures so so simply I will write on

9
00:00:29,670 --> 00:00:30,170
the code.

10
00:00:30,600 --> 00:00:35,970
So here we will focus on the style of writing the code, how to write on the code.

11
00:00:35,980 --> 00:00:41,460
So let us write few functions and let us define a class and write few functions.

12
00:00:42,150 --> 00:00:48,900
First of all, we need a class for node and in this class everything must be public so that it is directly

13
00:00:48,900 --> 00:00:49,610
accessible.

14
00:00:49,620 --> 00:00:53,730
I should have data and a pointer to next node.

15
00:00:53,850 --> 00:00:56,340
So node next.

16
00:00:56,340 --> 00:00:57,270
That is a pointer.

17
00:00:58,250 --> 00:00:59,360
And of this diskless.

18
00:01:00,990 --> 00:01:06,570
So instead of structure of your writing class, then class Linda list.

19
00:01:09,340 --> 00:01:17,080
That inside this closed up private members, I need a pointer called first, so Naude first.

20
00:01:18,670 --> 00:01:19,430
That's it.

21
00:01:19,450 --> 00:01:24,910
This is sufficient, if anything, more we will see later on the next US.

22
00:01:26,780 --> 00:01:34,190
Public, we should write the functions, so the first function is constructor, so let us call it Linda

23
00:01:34,190 --> 00:01:38,470
List and the constructor is non parametrized.

24
00:01:38,480 --> 00:01:39,540
Nothing should be there.

25
00:01:39,570 --> 00:01:42,620
There are no arguments and the force should be made as null.

26
00:01:43,220 --> 00:01:46,040
This is what we do for initialization of legalist.

27
00:01:49,080 --> 00:01:51,640
The single line got stuck to nothing else.

28
00:01:51,660 --> 00:01:55,270
There is no size, nothing, so it will be initially empty, null.

29
00:01:55,710 --> 00:01:58,470
And as we go on inserting the elements, it will be increasing.

30
00:01:58,480 --> 00:02:01,530
That's why this is a constructor which is making first and none.

31
00:02:01,980 --> 00:02:08,430
The next constructor I will trade on that is we have a redundancy language function for creating a link

32
00:02:08,430 --> 00:02:11,910
list by picking an array of elements and number of elements that same function.

33
00:02:11,910 --> 00:02:14,760
I will like it here, so let us make it as a constructor.

34
00:02:15,060 --> 00:02:20,010
So here for making a Lincolnesque faster, we will send it will create a linguist.

35
00:02:20,970 --> 00:02:26,460
Then I should have a distracter destructor, I will implement it outside all these functions, I'm going

36
00:02:26,460 --> 00:02:32,850
to implement them all, said using SCOP resolution, then the remaining functions void display.

37
00:02:34,830 --> 00:02:41,070
This will display all elements then void insert function, which will take index at which we want to

38
00:02:41,070 --> 00:02:45,260
insert an element and also the element itself, then delete function.

39
00:02:45,510 --> 00:02:51,040
This will delete an element from a given index and it will return the deleted element.

40
00:02:51,510 --> 00:02:56,130
Then also I will write one function for finding Lente of a link list.

41
00:02:56,160 --> 00:03:00,510
That is the count of an endless number of nodes and the list.

42
00:03:01,050 --> 00:03:01,680
That's all.

43
00:03:02,010 --> 00:03:04,740
I will implement these functions and I will explain you.

44
00:03:04,740 --> 00:03:09,870
I will not type them because already we know the procedures, how to write these functions.

45
00:03:10,060 --> 00:03:11,710
You have completed the class.

46
00:03:11,730 --> 00:03:13,170
Let me explain to the class.

47
00:03:13,350 --> 00:03:18,570
See, these are the functions we have written constructor, destructor and display, insert, delete

48
00:03:18,570 --> 00:03:19,460
and land function.

49
00:03:20,160 --> 00:03:22,940
So here using Scopa resolution, I have defined the function.

50
00:03:23,370 --> 00:03:28,200
This is a constructor which is taking array of elements and a number of elements and the same as the

51
00:03:28,200 --> 00:03:31,020
create function that we have written using C language program.

52
00:03:32,950 --> 00:03:38,740
First, I have created for Snowden using follow up, I have created remaining notes by filling the elements

53
00:03:38,740 --> 00:03:42,370
from an update, then this is a deleting of our entire linked list.

54
00:03:42,380 --> 00:03:48,250
So with the help of a pointer B, I'm deleting all the notes and forces moving to the next node and

55
00:03:48,250 --> 00:03:50,050
please keep on deleting ordinance.

56
00:03:51,830 --> 00:03:53,680
Then display function is as usual.

57
00:03:53,830 --> 00:03:59,280
Now one thing to observe that the functions are not taking Parameterize appointed two of us, not first

58
00:03:59,290 --> 00:04:00,860
is already dead inside the glass.

59
00:04:00,910 --> 00:04:06,500
I am taking a look at Pointer P, which is pointing on first node and then displaying all the elements.

60
00:04:06,850 --> 00:04:11,180
So this is the style of code I have already used on whiteboard most of the time.

61
00:04:12,010 --> 00:04:17,690
Then this is the function which is same as Kaun function of C language program.

62
00:04:18,620 --> 00:04:23,860
Then this is insert function having the same procedure, like checking whether the index is correct

63
00:04:23,860 --> 00:04:26,310
or not, then creating a node.

64
00:04:26,320 --> 00:04:29,160
If the node is first and first point, I should point on that one.

65
00:04:29,650 --> 00:04:32,710
That is the index is zero, then four should point on first node.

66
00:04:33,040 --> 00:04:36,520
Otherwise we should go to that index and insert a new node.

67
00:04:38,030 --> 00:04:42,830
Then deleting an order, I'm checking the index, whether it is valid or not, and if the index is one

68
00:04:42,830 --> 00:04:46,760
deleting first note, otherwise deleting a note from a given position.

69
00:04:48,530 --> 00:04:54,140
Then here inside the main functionality I have written Uhry and I'm creating a Lincolnesque, using

70
00:04:54,140 --> 00:04:59,450
that array and displaying it, let us run this program, I should get the output that is one, two,

71
00:04:59,450 --> 00:05:00,290
three, four, five.

72
00:05:00,710 --> 00:05:03,290
Yes, I'm getting the elements that are one, two, three, four, five.

73
00:05:03,690 --> 00:05:08,300
Let me try other functions so I will U.S. out and I will call delete function.

74
00:05:10,550 --> 00:05:15,440
Delete first element, so it should delete one, let us run.

75
00:05:15,970 --> 00:05:19,910
Yes, it has deleted one and the rest of the elements are two, three, four, five.

76
00:05:21,010 --> 00:05:27,100
I delivered an element that is third element and also I will give N.L. so there's that little element,

77
00:05:27,550 --> 00:05:28,590
so I should get the elements.

78
00:05:28,600 --> 00:05:29,620
One, two, four, five.

79
00:05:31,240 --> 00:05:35,140
Yes, three is deleted and the rest of the elements are gone before five.

80
00:05:36,290 --> 00:05:42,560
Now, instructively, let me try a function that is lente if Lenders' five, it is displaying the length

81
00:05:42,560 --> 00:05:43,340
of a list.

82
00:05:44,320 --> 00:05:45,470
Now, I'll remove this.

83
00:05:45,490 --> 00:05:50,590
Let us try insert function eldard, insert and insert.

84
00:05:51,480 --> 00:05:56,400
At index a zero, element 10, so 10 should be considered before the London.

85
00:05:58,880 --> 00:06:01,020
Yes, is inserted before one.

86
00:06:01,460 --> 00:06:04,260
Now let me change and exile in the next three.

87
00:06:04,280 --> 00:06:05,950
So it should be considered after three.

88
00:06:06,620 --> 00:06:08,340
So one, two, three, 10, four or five.

89
00:06:08,360 --> 00:06:08,660
Yes.

90
00:06:08,660 --> 00:06:10,030
One, two, three, 10, four or five.

91
00:06:11,330 --> 00:06:13,730
See the benefit of writing in C++ Classis.

92
00:06:14,000 --> 00:06:20,060
The code is properly organized in the form of a class and the class is having member functions and we

93
00:06:20,060 --> 00:06:24,680
can use the class and call it functions, create object of class and call the functions.

94
00:06:25,190 --> 00:06:30,620
Then already I have shown you how to convert a class into a template class so you can make this class

95
00:06:30,620 --> 00:06:33,080
as a template that is a student exercise.

96
00:06:33,440 --> 00:06:37,270
It should be able to work for any type of data, that's all.

97
00:06:37,520 --> 00:06:39,610
So you can practice this program, right?

98
00:06:39,620 --> 00:06:41,260
A C++ class for Lincolnesque.

