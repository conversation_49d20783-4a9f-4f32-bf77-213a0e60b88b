1
00:00:00,420 --> 00:00:04,860
Now, next thing is, I should write a function for addiction, but before showing that, I will show

2
00:00:04,860 --> 00:00:05,430
you something.

3
00:00:06,720 --> 00:00:12,330
So this read function is taking the elements from keyboard and display function is displaying on the

4
00:00:12,330 --> 00:00:12,990
monitor.

5
00:00:13,680 --> 00:00:19,530
So this read function, I can implement it using extraction operator forseen and display function I

6
00:00:19,530 --> 00:00:24,890
can use implement it using insertion operator that is using seop.

7
00:00:25,410 --> 00:00:31,100
So instead of writing them as display can read, I will convert them into operators.

8
00:00:31,770 --> 00:00:33,600
So I'm going to modify the code.

9
00:00:33,600 --> 00:00:35,000
Just watch closely.

10
00:00:35,670 --> 00:00:37,780
See here is a spasmodically starting.

11
00:00:38,220 --> 00:00:42,390
So for that again I will explain to the class from the beginning, then I will convert both functions

12
00:00:42,580 --> 00:00:43,590
as part of my text.

13
00:00:43,590 --> 00:00:49,810
And these are the data members and this is a constructor that is a constructor and does a destructor.

14
00:00:50,250 --> 00:00:52,140
So here I will close the class.

15
00:00:54,350 --> 00:00:59,720
I have closed the class here, so at the end of the closing bracket, I will remove this, I will remove

16
00:01:00,230 --> 00:01:01,510
so class is closing here.

17
00:01:01,520 --> 00:01:06,380
It says not for implementing insertion and extraction.

18
00:01:06,380 --> 00:01:13,760
Operator, I have to follow the rule for operator overloading that is specific before insertion and

19
00:01:13,760 --> 00:01:14,450
extraction.

20
00:01:14,870 --> 00:01:22,100
First I will write insertion, I should say I stream and written by reference and the function name

21
00:01:22,100 --> 00:01:22,400
is.

22
00:01:24,170 --> 00:01:33,770
Operator extraction, then the parameter first parameter is I stream called by reference, I will call

23
00:01:33,770 --> 00:01:40,040
it as is then poma the spot matrix as a parameter.

24
00:01:40,370 --> 00:01:45,320
So Spartz Matrix Bio-Reference as parameter as.

25
00:01:46,270 --> 00:01:53,360
Yes, this is the signature for extraction operator, that is ice stream operator.

26
00:01:54,880 --> 00:01:58,250
It should be the same, only the class name, whatever the class that you're using.

27
00:01:58,270 --> 00:02:01,220
You have to write on that one here, depending on the class.

28
00:02:01,240 --> 00:02:02,160
This part will change.

29
00:02:02,170 --> 00:02:03,570
Otherwise, everything is same.

30
00:02:04,500 --> 00:02:13,740
Not for insertion operator or stream written by reference, and the operator name is insertion insertion

31
00:02:13,740 --> 00:02:15,150
symbol then.

32
00:02:16,580 --> 00:02:23,000
All Stephen Colbert friends, and I will call it, as always, the first variable name then.

33
00:02:24,090 --> 00:02:28,260
Spots this also by reference that.

34
00:02:28,860 --> 00:02:31,160
So these two functions, I should implement them.

35
00:02:31,320 --> 00:02:34,190
So the first one is for Reed and the second one is for display.

36
00:02:34,470 --> 00:02:36,690
So I will change these functions.

37
00:02:37,020 --> 00:02:39,750
That said, this is for Reed and this is for display.

38
00:02:39,780 --> 00:02:41,370
Now, one more thing I should do here.

39
00:02:41,670 --> 00:02:48,510
I should make the my friend function because this method of implementation is done using friend functions,

40
00:02:49,200 --> 00:02:51,570
NetSol, not these functions.

41
00:02:51,570 --> 00:02:54,930
I should change them as these operators.

42
00:02:55,260 --> 00:02:59,010
And I don't have to use a scope resolution because those are different functions.

43
00:02:59,430 --> 00:03:01,920
So I will write on the same signature here.

44
00:03:03,660 --> 00:03:08,680
This one, and instead of Wall Street, I'll make it as.

45
00:03:10,000 --> 00:03:18,550
That extraction operator then spots is so wherever I'm using the members, I should write a dot and

46
00:03:18,550 --> 00:03:19,710
here as dot num.

47
00:03:20,850 --> 00:03:28,650
An element, Sadaat, and this is also a stock, then instead of the split function, I should use the

48
00:03:28,650 --> 00:03:29,400
signature.

49
00:03:32,130 --> 00:03:39,450
And copy it as it is here, so that signature, you have to remember it right now for extraction.

50
00:03:39,680 --> 00:03:44,640
This is so wherever I'm accessing the members, I should use that as a start.

51
00:03:45,120 --> 00:03:49,910
And here, escort an escort element.

52
00:03:50,550 --> 00:03:54,380
And here also Astarte element here also has a dark element.

53
00:03:54,450 --> 00:03:55,560
See, it was so simple.

54
00:03:55,800 --> 00:04:02,100
So here I am referring to show you how to modify the code and to develop the code from the scratch.

55
00:04:02,700 --> 00:04:08,620
Because if you are familiar with the logic, then only thing is how great the code for here.

56
00:04:08,640 --> 00:04:13,170
All of you have written the one style of code and I modified it to another site.

57
00:04:13,920 --> 00:04:15,510
Now let us compile now.

58
00:04:16,140 --> 00:04:19,850
So that's all it is really my spasmodic class here.

59
00:04:20,910 --> 00:04:26,760
It is having constructed the structure and two friend functions for insertion and extraction operators,

60
00:04:27,150 --> 00:04:29,850
and they are friends, so they are global, so.

61
00:04:31,040 --> 00:04:34,040
Without scope resolution operator, I have written them outside.

62
00:04:36,060 --> 00:04:41,580
Now, everything is really not the same thing should work now instead of the trade, I should say,

63
00:04:42,400 --> 00:04:43,110
see in.

64
00:04:44,780 --> 00:04:50,600
As one uninstall for display, I should say, see out Aslan.

65
00:04:52,420 --> 00:04:57,650
Now, let us compile and see if there are anything, any errors, if anything, I missed an error.

66
00:04:57,970 --> 00:04:58,980
I will remove them.

67
00:04:59,680 --> 00:05:05,830
Yes, there is an error actually at the end of this function, I should return because the return type

68
00:05:05,830 --> 00:05:09,120
of a function is written by reference of ice cream.

69
00:05:09,700 --> 00:05:19,840
So ice cream references is so at the end I should see a return address and here inside or stream I should

70
00:05:19,840 --> 00:05:21,900
write on OS.

71
00:05:23,990 --> 00:05:25,470
Let us compile and run.

72
00:05:26,060 --> 00:05:27,350
Yes, it's working.

73
00:05:28,840 --> 00:05:30,070
Now, the same thing I got.

74
00:05:31,880 --> 00:05:38,220
Democrats are already given, so I will give the elements only in that Agnel so zero zero one one comma

75
00:05:38,240 --> 00:05:39,290
one is also one.

76
00:05:40,400 --> 00:05:45,290
And two is one three commentary's one for gramophones also.

77
00:05:46,160 --> 00:05:47,970
Yes, I got the same output.

78
00:05:48,500 --> 00:05:54,470
See, there is no change in input and output, only the change is internally inside the program, the

79
00:05:54,470 --> 00:05:56,880
style of writing the code has to change.

80
00:05:57,500 --> 00:06:02,150
If you look at the main function, it looks very simple that it looks like as if you have created an

81
00:06:02,150 --> 00:06:08,360
object of a sports matrix, then you are reading the elements using scene and displaying the elements

82
00:06:08,360 --> 00:06:09,170
using code.

83
00:06:09,800 --> 00:06:16,100
And the whole lot of logic is there inside these functions of a class member functions of a class.

84
00:06:18,270 --> 00:06:26,250
Now, the next thing is I have to implement add function, so for add function also I will implement

85
00:06:26,460 --> 00:06:27,300
operator.

86
00:06:28,240 --> 00:06:35,020
So operator overloading in C++ can be done even using memory functions or using friend functions, so

87
00:06:35,020 --> 00:06:37,210
I will implement it using no function.

88
00:06:37,600 --> 00:06:43,140
So here, before these friend functions, I will write on the signature for the function that is an

89
00:06:43,180 --> 00:06:43,780
operator.

90
00:06:45,640 --> 00:06:52,120
So let us write the signature signature spots, it will read on the spot matrix and the function name

91
00:06:52,120 --> 00:06:59,330
is Operator Plus, and this operator actually needs to Spartz, Mattocks or Wanis itself because it's

92
00:06:59,350 --> 00:07:00,370
inside this glass.

93
00:07:00,640 --> 00:07:02,560
And the second one is spa's.

94
00:07:03,730 --> 00:07:10,420
By reference, I will take as one parameter it will take and oneness itself.

95
00:07:11,830 --> 00:07:16,470
So here, just after the class, I'll give a gap and here I will implement that function.

96
00:07:16,870 --> 00:07:18,960
So the same function, I should implement it.

97
00:07:19,300 --> 00:07:23,380
So I will copy this line and pasted here after the class.

98
00:07:23,910 --> 00:07:29,680
Then just before the operator, I should use the last name and scope resolution operator.

99
00:07:32,780 --> 00:07:39,650
I will implement the body of this function, so this function is supposed to add this as that itself.

100
00:07:40,360 --> 00:07:46,210
So one is itself, other one is so already we know the logic how to do so.

101
00:07:46,210 --> 00:07:48,700
Already we know how to to sports smartasses.

102
00:07:48,950 --> 00:07:55,900
I'm not explaining simply the code variables are declared spots mattocks for some.

103
00:07:58,630 --> 00:08:00,040
And it it's created in he.

104
00:08:03,520 --> 00:08:10,990
The dimension should be the same comma and comma number of non-zero elements of Cymatics.

105
00:08:12,860 --> 00:08:15,600
Plus, number of non-zero elements of asthmatics.

106
00:08:15,620 --> 00:08:17,090
That is coming as a barometer.

107
00:08:17,960 --> 00:08:21,590
One more thing I should check first number of rules on number of columns.

108
00:08:21,590 --> 00:08:26,000
I see that the dimensions are similar, not if it's not equal to S m.

109
00:08:27,910 --> 00:08:38,169
Ah, and is not equal to accident and then return, none means it cannot be ordered, otherwise creative

110
00:08:38,169 --> 00:08:39,130
spots might explode.

111
00:08:39,159 --> 00:08:40,960
We have done that is for finding some.

112
00:08:42,780 --> 00:08:46,550
Now, I assign assigned Kate assigned zero.

113
00:08:47,510 --> 00:08:51,900
Get a loop, I use less than a number of non-zero elements.

114
00:08:52,930 --> 00:09:01,660
And Jay is less than X number of non-zero elements, check if your numbers are smaller or greater or

115
00:09:01,660 --> 00:09:02,170
equal.

116
00:09:02,320 --> 00:09:04,840
So Eyerly of I.

117
00:09:06,070 --> 00:09:13,030
But I if it is less than a eyerly of Jadot, I then.

118
00:09:14,440 --> 00:09:15,540
And some thought.

119
00:09:17,030 --> 00:09:20,720
As a disappointed, then, I should use Arrow Eyerly of Key.

120
00:09:22,340 --> 00:09:25,080
A sign that eyerly of a.

121
00:09:26,780 --> 00:09:29,960
And both of these should be implemented plus, plus.

122
00:09:31,050 --> 00:09:34,620
And plus plus no arrest, I will copy paste.

123
00:09:37,740 --> 00:09:42,600
This is greater than this one then in some copy.

124
00:09:43,840 --> 00:09:47,040
Gee, that is a daily of cheap.

125
00:09:49,870 --> 00:09:52,000
Ls the water equal?

126
00:09:53,760 --> 00:09:55,120
Then check balance.

127
00:09:55,200 --> 00:10:01,710
I will copy the same code and make it four columns, the is G is greater than.

128
00:10:02,670 --> 00:10:06,210
Elements of G, then popular elements of a.

129
00:10:07,580 --> 00:10:16,370
If this G is greater than barometer's, the second model, G then copied the element of second matrix

130
00:10:17,160 --> 00:10:18,110
otherwise.

131
00:10:19,300 --> 00:10:23,680
If both are equal, then and some element.

132
00:10:25,100 --> 00:10:35,210
Off-Key copy, the element of AI as it is, so it will copy IJI and X also then X, we will make it

133
00:10:35,210 --> 00:10:45,240
as some of the first mattocks and the second matrix so easily of key plus plus assign eyerly of i.e.

134
00:10:45,950 --> 00:10:46,310
not.

135
00:10:47,380 --> 00:10:56,770
Plus, plus, plus, plus Botox and here also in someone, so it should be X plus as a dot element,

136
00:10:56,770 --> 00:10:58,680
that second matrix G.

137
00:10:58,780 --> 00:11:00,570
Plus plus X.

138
00:11:01,690 --> 00:11:08,020
Yes, this for copy all the elements, then remaining elements, we can use this valuable copy, all

139
00:11:08,020 --> 00:11:16,390
the elements, then the remaining elements will copy them by using followups for wherever I use less

140
00:11:16,390 --> 00:11:18,850
than a number of non-zero elements.

141
00:11:19,390 --> 00:11:26,980
And I plus plus then some of element of K plus plus is assigned with.

142
00:11:27,990 --> 00:11:29,460
Element of I.

143
00:11:31,290 --> 00:11:41,060
Then same thing for second month also, second mattocks also, and this should be G less than Estcourt,

144
00:11:42,790 --> 00:11:44,420
then that's a plus plus.

145
00:11:45,830 --> 00:11:47,690
And this is Astarte.

146
00:11:48,680 --> 00:11:57,770
Jane, then at the end, some number of non-zero elements must be equal to King and right on somatics,

147
00:11:58,250 --> 00:12:01,450
as it is called, by value, so return the difference to value.

148
00:12:01,610 --> 00:12:03,080
So the logic is the same.

149
00:12:03,080 --> 00:12:04,850
Only the style of court is different.

150
00:12:04,860 --> 00:12:08,500
Just you have to watch it and observe few things, then you can write it by yourself.

151
00:12:09,320 --> 00:12:13,310
Inside the main function, I will create the mattresses and I will add them.

152
00:12:13,310 --> 00:12:14,750
So I have clear the main function.

153
00:12:14,760 --> 00:12:17,310
Let us write it from the beginning.

154
00:12:17,570 --> 00:12:20,090
So this is the first Matrix X1, the first matrix.

155
00:12:20,090 --> 00:12:24,590
Let us say dimensions are five by five and number of non-zero elements are also five.

156
00:12:24,920 --> 00:12:27,260
Then the second matrix as aspires to.

157
00:12:27,590 --> 00:12:31,670
And this is also having something that is a number of non-zero elements I find.

158
00:12:32,210 --> 00:12:34,610
Then I will see out.

159
00:12:35,810 --> 00:12:38,760
And the first mattocks, I will take this one.

160
00:12:38,780 --> 00:12:40,250
So this will enter the first matrix.

161
00:12:40,280 --> 00:12:45,170
Then see out I will take the second matics Estherville take the second matrix then.

162
00:12:46,670 --> 00:12:54,670
Then I have to find some of these myself, so I will say spot some assign as one plus as to the CELAC

163
00:12:54,680 --> 00:13:01,340
to madrassahs and the result will be in spots, Mattick, some additional call and the result in some

164
00:13:01,900 --> 00:13:03,710
I have to bring all three Matisses.

165
00:13:03,710 --> 00:13:18,010
So I will display the first Matrix Anel and Fanatic's S1, then Skalp second Matrix and and then I'll

166
00:13:18,020 --> 00:13:24,590
split second matics, then out see some matrix and N.L..

167
00:13:24,890 --> 00:13:30,700
The first somatics that said, oh here I have done a mistake.

168
00:13:31,040 --> 00:13:36,640
It should be seen, I should read the first matrix and I should read Second Matrix and run.

169
00:13:37,280 --> 00:13:37,970
Yes.

170
00:13:41,090 --> 00:13:46,210
I have going to the elements of First Mattocks, so that is I will enter diagonal elements and all the

171
00:13:46,210 --> 00:13:53,230
values will be one zero zero one one comma one is also one to commit to is also one three commentary's,

172
00:13:53,230 --> 00:13:55,000
also one four comma for one.

173
00:13:55,480 --> 00:14:03,460
On the second mattocks elements I will enter row element so zero zero I will enter five then zero comma

174
00:14:03,460 --> 00:14:11,320
one five zero two five zero comma three five zero comma four is five.

175
00:14:13,370 --> 00:14:17,980
Yes, these are the elements of the first markets that are diagonal ones and the second matics first

176
00:14:17,990 --> 00:14:23,510
row is all fives, then the submission is first element is added that was common to the six and the

177
00:14:23,510 --> 00:14:29,490
rest of the elements are taken as it is nupedia for this program, given as a download.

178
00:14:29,510 --> 00:14:34,340
You can download it and check it and try it on your machine back this program.

179
00:14:35,030 --> 00:14:35,690
So that's on.

