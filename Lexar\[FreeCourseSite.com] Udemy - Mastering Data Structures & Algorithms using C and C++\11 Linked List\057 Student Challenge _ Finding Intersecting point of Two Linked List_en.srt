1
00:00:00,330 --> 00:00:05,640
The topic is finding the intersection point of tooling, it's supposed to cool inkless are having some

2
00:00:05,640 --> 00:00:11,340
common collection of neurons like here in the example this Linklaters having these eight nodes and this

3
00:00:11,340 --> 00:00:12,530
is having seven nodes.

4
00:00:12,750 --> 00:00:16,800
So these notes are coming to them like this node is pointing on this one.

5
00:00:16,830 --> 00:00:19,710
So this portion of a link lists belongs to this link.

6
00:00:19,740 --> 00:00:21,030
It's also second link results.

7
00:00:21,660 --> 00:00:23,240
So those sort of nodes are common.

8
00:00:23,640 --> 00:00:28,950
We want to find out the starting point of common nodes that is intersecting point of bullet.

9
00:00:30,570 --> 00:00:31,950
So how do solve this problem?

10
00:00:32,200 --> 00:00:33,000
There's a single link.

11
00:00:33,840 --> 00:00:36,060
We can just move on, lean forward direction.

12
00:00:36,180 --> 00:00:37,930
We cannot move in backward direction.

13
00:00:38,640 --> 00:00:39,950
Shall we start from here?

14
00:00:40,380 --> 00:00:43,350
If we start from here, then how we should move?

15
00:00:43,560 --> 00:00:48,810
Shall we move node at a time when node at a time if we move or not at the time, then this is having

16
00:00:48,810 --> 00:00:53,940
less number of nodes will reach that intersection point early and this having more number of nodes.

17
00:00:54,090 --> 00:00:56,420
So from here you cannot trace from the site.

18
00:00:57,270 --> 00:00:57,480
Right.

19
00:00:57,600 --> 00:01:02,940
How can we know that butat c after time, if you are reaching on the same node then you can see that

20
00:01:02,940 --> 00:01:04,019
OK, this is the same point.

21
00:01:04,440 --> 00:01:07,440
But from this side we cannot reach at the same time.

22
00:01:08,100 --> 00:01:11,540
But from that side, if you come backward you can reach.

23
00:01:12,540 --> 00:01:14,610
Let us go to the end of the first link.

24
00:01:15,030 --> 00:01:18,990
OK, reached then let us go to the end of a second link list.

25
00:01:19,030 --> 00:01:19,350
Yes.

26
00:01:19,350 --> 00:01:20,760
Reach no.

27
00:01:21,600 --> 00:01:22,440
Come backward.

28
00:01:22,440 --> 00:01:23,250
Come backward.

29
00:01:23,550 --> 00:01:25,430
Not this is the point still here.

30
00:01:25,430 --> 00:01:29,590
We will get the same addresses then next n addresses will be different.

31
00:01:30,000 --> 00:01:35,310
So once you get a different result you can say that the previous one was the intersection point.

32
00:01:36,300 --> 00:01:36,900
Yes.

33
00:01:36,900 --> 00:01:38,000
This is a procedure.

34
00:01:38,310 --> 00:01:38,810
Yes.

35
00:01:39,540 --> 00:01:45,870
So the procedure is we have to find the intersecting node by traversing from backward.

36
00:01:46,200 --> 00:01:47,880
How you can traverse from backward.

37
00:01:48,930 --> 00:01:54,850
We cannot do that because it is so singularly implicit, we can move or lean forward direction than

38
00:01:54,930 --> 00:01:56,920
how it is possible to traverse backward.

39
00:01:57,840 --> 00:02:02,220
It is possible using STAC if it is possible using.

40
00:02:02,850 --> 00:02:09,120
So when you cannot traverse backward in a single inkless stack will help you to get back the previous

41
00:02:09,139 --> 00:02:10,020
noroviruses.

42
00:02:10,740 --> 00:02:12,900
So, yes, let us see the procedure.

43
00:02:13,140 --> 00:02:18,320
How intersection point can be found by using tools tracked for tool interest.

44
00:02:18,780 --> 00:02:21,080
So I'll show you the procedure.

45
00:02:21,210 --> 00:02:28,230
Just watch the procedure, see, take a pointer, be upon first node of a nucleus, then scan through

46
00:02:28,230 --> 00:02:32,700
this fossil englis and go on pushing the results of these laws in the stack.

47
00:02:33,120 --> 00:02:38,360
So I will go on simply writing that because we already know fossils handed 110 134.

48
00:02:38,610 --> 00:02:51,790
So I will write on 100, 110, 130, 150, 200, 220 to 40 to 60, 200 to 20 to 40 to 60.

49
00:02:52,140 --> 00:02:58,040
So by taking a pointer upon the first link list, I have gone through all the noise and have put their

50
00:02:58,060 --> 00:02:58,640
addresses.

51
00:02:59,940 --> 00:03:06,240
No, I will take a point of B one second link list, then I will scan through all these nodes and take

52
00:03:06,240 --> 00:03:08,350
their pointers and push them into this stack.

53
00:03:08,820 --> 00:03:20,220
So let us stop 300 310 three thirty three thirty then next after this two hundred thirty to forty two

54
00:03:20,220 --> 00:03:25,790
sixty two hundred to 20 to 40 to 60.

55
00:03:26,460 --> 00:03:32,280
Now I have two stacks of filled with the addresses of the north of two linked list.

56
00:03:32,700 --> 00:03:36,480
Now from the back side I should check from where the ideas are changing.

57
00:03:36,480 --> 00:03:38,970
So I'll do it by popping out the addresses from the stack.

58
00:03:39,300 --> 00:03:45,470
I'll let us start popping all the addresses, pop one and see that this is our C, so delete them.

59
00:03:45,780 --> 00:03:46,310
Yes.

60
00:03:46,320 --> 00:03:50,400
So it means we are coming from last N backward right now.

61
00:03:50,400 --> 00:03:57,090
These two addresses are same in both the stack of these statistics are same about these statistics are

62
00:03:57,090 --> 00:03:58,080
same old.

63
00:03:58,440 --> 00:03:58,770
Right.

64
00:03:59,220 --> 00:04:03,150
And which over the last one you are popping out try to maintain its copy.

65
00:04:03,180 --> 00:04:03,470
Right.

66
00:04:03,510 --> 00:04:05,490
So the last words we have about the student.

67
00:04:06,360 --> 00:04:06,740
Right.

68
00:04:06,930 --> 00:04:10,230
So always first two sixty then to forty that indeed.

69
00:04:10,230 --> 00:04:10,860
And then two hundred.

70
00:04:10,860 --> 00:04:13,360
So that recent one you have to mean the next address.

71
00:04:13,410 --> 00:04:13,980
Check them.

72
00:04:14,310 --> 00:04:15,660
Oh they are different.

73
00:04:15,900 --> 00:04:18,630
So from here the link lists are different.

74
00:04:18,630 --> 00:04:18,950
Right.

75
00:04:19,200 --> 00:04:20,690
So what was the last known.

76
00:04:20,700 --> 00:04:22,840
Having a common address.

77
00:04:22,840 --> 00:04:28,680
So this was so, so what does the intersecting point of two nodes does the intersecting points of two

78
00:04:28,680 --> 00:04:29,460
linked list.

79
00:04:29,680 --> 00:04:32,250
So that's all from this stack.

80
00:04:32,340 --> 00:04:36,890
We can find out which is the intersection point of two link list.

81
00:04:37,080 --> 00:04:45,780
So if I do analysis, the time is order of N and nodes and end also total times and plus and then if

82
00:04:45,780 --> 00:04:48,990
you ask, we are deleting the element from the stack also.

83
00:04:48,990 --> 00:04:51,810
So OK, include that and also to the three.

84
00:04:51,810 --> 00:04:53,580
And so it is linear actually.

85
00:04:55,170 --> 00:04:57,570
So I remove this and show you the program called.

86
00:04:59,120 --> 00:05:00,750
Let us write the code for this one.

87
00:05:01,260 --> 00:05:06,560
See, first of all, I will scan for this fossil inkless and I will put the addresses of all those nodes

88
00:05:06,560 --> 00:05:07,240
in the stack.

89
00:05:07,790 --> 00:05:14,750
So for that logistic point to be binding upon, first this one and I should scan through this entire

90
00:05:14,750 --> 00:05:15,290
link list.

91
00:05:15,290 --> 00:05:22,970
So I should say, while B, that is B, not equal to null or even I can say just P while B, what I

92
00:05:22,970 --> 00:05:23,470
should do.

93
00:05:24,370 --> 00:05:32,530
Push into the stack, let us stay, STK is a variable of type stock which can store the addresses and

94
00:05:32,530 --> 00:05:36,340
I have to push B, so this is called my address, right.

95
00:05:36,880 --> 00:05:39,090
So I have written it in a single line.

96
00:05:39,520 --> 00:05:44,050
Not the same point would be utilized for second link list also.

97
00:05:44,320 --> 00:05:50,790
So be assigned 2nd then while P is not null, I should push all these addresses.

98
00:05:50,800 --> 00:05:58,560
So I'm just I will say B, line B and push into second stack.

99
00:05:58,570 --> 00:06:06,130
So let us call it as stick it to and I will push it out of North B, so let us change this name as one

100
00:06:06,490 --> 00:06:07,410
STK one.

101
00:06:08,080 --> 00:06:14,140
So this is A stored in stack one and this is stored in stack to now I have to order just as from the

102
00:06:14,140 --> 00:06:16,450
stack as long as they are same.

103
00:06:16,840 --> 00:06:20,440
And once I get a different address I should stop.

104
00:06:20,830 --> 00:06:24,130
So let us write on here while.

105
00:06:26,480 --> 00:06:34,910
I should check the topmost element of the stack so I will have a function call stack top, stack top

106
00:06:35,240 --> 00:06:39,590
and I will pass first the stack that is STK one.

107
00:06:41,180 --> 00:06:48,390
Right, and this stock value, I should continue as long as they are equal.

108
00:06:48,560 --> 00:06:52,520
So if it is equal to start top.

109
00:06:54,830 --> 00:07:03,650
STK do so this is STK, too, so as long as the topmost elements from Drew's tax is equal, I should

110
00:07:03,650 --> 00:07:07,670
go on doing what I should do, proper address from two stacks.

111
00:07:07,680 --> 00:07:16,730
So here I will vote on and take appointer B and I will say Bob from Unperson Stack one.

112
00:07:21,370 --> 00:07:28,960
And also in the same line I will write, just published an address from the address of Stack to so I

113
00:07:28,960 --> 00:07:30,620
have to send it by address, right.

114
00:07:30,700 --> 00:07:32,640
So I have to send it by address.

115
00:07:33,010 --> 00:07:36,510
So this impassionate stick it to that all.

116
00:07:37,120 --> 00:07:41,770
See, this loop will purported that this is from Goldstrike, right.

117
00:07:42,080 --> 00:07:43,410
Pop from Stocklands.

118
00:07:43,420 --> 00:07:49,750
Support from start to add any one of the value I can take it and pointer P so pointer P will be having

119
00:07:49,750 --> 00:07:52,120
the last known to delete it from the stack.

120
00:07:53,500 --> 00:07:58,230
So this will be checking and it will stop if the addresses are different.

121
00:07:58,510 --> 00:08:04,900
So the last address that is popped out will be in the variable being so busy, the starting point of

122
00:08:04,900 --> 00:08:05,670
two Lincolnesque.

123
00:08:06,310 --> 00:08:09,910
And if you are preparing these two linked lists then you can print at last.

124
00:08:09,910 --> 00:08:15,400
You can print this one person, Tildy these data.

125
00:08:15,730 --> 00:08:22,690
If you print this data, then you should get this intersecting point of the linked list loop after the

126
00:08:22,690 --> 00:08:28,190
loop you can print so that this is the program called for performing this procedure.

127
00:08:28,510 --> 00:08:33,940
So in this one, you should be ready with these functions like push and pop and the implementation of

128
00:08:33,940 --> 00:08:34,390
Stack.

129
00:08:34,690 --> 00:08:38,510
And that stack should be storing the addresses of these nodes.

130
00:08:38,650 --> 00:08:38,980
Right.

131
00:08:39,280 --> 00:08:43,690
We have already seen the stacks which are storing integer type of values.

132
00:08:43,690 --> 00:08:48,820
But no, you have to store the addresses, so you have to write on the program for this one.

133
00:08:48,820 --> 00:08:50,000
Implement this program.

134
00:08:50,920 --> 00:08:51,460
That's it.

