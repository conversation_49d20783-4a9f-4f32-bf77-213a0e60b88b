1
00:00:00,330 --> 00:00:04,140
Let us look at the demonstration for <PERSON><PERSON> as a parameter.

2
00:00:05,220 --> 00:00:14,370
I have already started online GDP and I have selected C++ program here, so let us assume it.

3
00:00:14,550 --> 00:00:19,940
And here I will show you the program that I have discussed on whiteboard.

4
00:00:20,760 --> 00:00:24,030
See, inside the main function, I will create an array.

5
00:00:24,990 --> 00:00:31,640
If I don't mention the size, then the size will be dependent on the number of elements initialized.

6
00:00:31,920 --> 00:00:32,369
Right.

7
00:00:32,880 --> 00:00:34,640
And and as also there.

8
00:00:34,700 --> 00:00:35,590
And five.

9
00:00:36,300 --> 00:00:42,910
Now let us also display an array using foreach loop in digit X from a.

10
00:00:43,410 --> 00:00:46,930
Then I will see out and I will print X.

11
00:00:47,670 --> 00:00:52,080
So this foreach loop will be printing all the elements of this area.

12
00:00:52,740 --> 00:00:57,630
Let us run it and see if it has displayed all the element.

13
00:00:57,630 --> 00:00:58,920
I have not given spaces.

14
00:00:58,920 --> 00:01:02,490
All the numbers are side by side two four, six, eight, 10.

15
00:01:03,030 --> 00:01:03,410
Right.

16
00:01:04,290 --> 00:01:09,740
If you want spaces then you should write the subspace here or else you can use.

17
00:01:09,750 --> 00:01:17,850
And then now back to the program C I will write on a function which will take this array and print all

18
00:01:17,850 --> 00:01:18,780
the elements.

19
00:01:18,930 --> 00:01:21,390
So I will write function, function, name.

20
00:01:21,390 --> 00:01:22,590
Let us call it as fun.

21
00:01:22,980 --> 00:01:26,040
We should take it as a parameter.

22
00:01:26,040 --> 00:01:28,750
So Arreaza parameters given it like this.

23
00:01:29,100 --> 00:01:31,210
Now one thing, very important thing.

24
00:01:31,260 --> 00:01:32,310
Listen carefully.

25
00:01:32,460 --> 00:01:36,180
Arrays are always parsed by address.

26
00:01:36,180 --> 00:01:39,050
They are never passers-by value.

27
00:01:39,300 --> 00:01:41,580
They are always parsed by address.

28
00:01:41,760 --> 00:01:43,800
And even you cannot pass them by reference.

29
00:01:44,250 --> 00:01:46,080
You have to pass them were address only.

30
00:01:46,500 --> 00:01:53,100
So in your passing in that right to this array, then here for taking it as a parameter, you should

31
00:01:53,100 --> 00:01:56,530
use square bracket and even you can use a starter.

32
00:01:56,640 --> 00:01:57,920
I will show you afterwards.

33
00:01:58,440 --> 00:02:00,880
So I'm calling the names only.

34
00:02:01,050 --> 00:02:02,560
Now let us complete the function.

35
00:02:02,700 --> 00:02:05,160
What should be the return type of this function?

36
00:02:05,160 --> 00:02:05,660
White.

37
00:02:05,670 --> 00:02:07,200
It's not going to return anything.

38
00:02:07,710 --> 00:02:09,060
Not inside this.

39
00:02:09,060 --> 00:02:13,060
What I'll do is I will print it the size of the array.

40
00:02:13,260 --> 00:02:24,750
Let us check the size of the size of a then divided by side of it, divided by size of integer because

41
00:02:24,750 --> 00:02:25,860
it is an integer type.

42
00:02:26,080 --> 00:02:26,520
Right.

43
00:02:26,990 --> 00:02:30,840
Then also I will give endl so that it jumps into next line.

44
00:02:31,470 --> 00:02:34,190
So in this function I am passing an array.

45
00:02:34,600 --> 00:02:37,110
OK, so array will be taken here.

46
00:02:37,560 --> 00:02:39,040
It is BioThrax.

47
00:02:39,060 --> 00:02:42,740
So this is nothing but a pointer to add to this area.

48
00:02:43,140 --> 00:02:45,480
And these two are different, right.

49
00:02:45,840 --> 00:02:50,190
This belongs to me and this belongs to a function fund than here.

50
00:02:50,190 --> 00:02:55,920
I'm trying to print the size of an array divided by side of integer, so I will get the length of the

51
00:02:55,920 --> 00:02:56,180
other.

52
00:02:56,520 --> 00:03:00,510
So inside this main function, let us call function fun and pass it.

53
00:03:00,690 --> 00:03:03,920
Let us run and check what size it will print.

54
00:03:04,350 --> 00:03:07,050
Here you can see that the size is too.

55
00:03:07,470 --> 00:03:14,250
And also there is a warning given here that size of an array function parameter avil return the size

56
00:03:14,250 --> 00:03:17,520
of integer statements integer pointer.

57
00:03:17,670 --> 00:03:24,930
So the result is to see actually it is not giving the size of this at the size of the arrays.

58
00:03:25,260 --> 00:03:26,640
Five a five integer.

59
00:03:26,820 --> 00:03:32,190
It's not giving the size of the sorry that is giving the size of a pointer and the pointer and latest

60
00:03:32,190 --> 00:03:35,640
compiler takes eight bytes and integer for whites.

61
00:03:35,850 --> 00:03:39,450
So this is becoming eight by four.

62
00:03:39,450 --> 00:03:40,740
So the result is two.

63
00:03:41,340 --> 00:03:47,550
So one important thing I can show you that inside the main function we have created an array.

64
00:03:47,850 --> 00:03:48,190
Right.

65
00:03:48,210 --> 00:03:54,060
And when we pass it to a function, function is taking it as a pointer to a right.

66
00:03:54,480 --> 00:03:55,860
Same statement.

67
00:03:56,220 --> 00:04:02,440
I will copy this and I will print it here inside the main function plate.

68
00:04:02,880 --> 00:04:09,650
Let us see what happens, what the size it will print, see the spending the size as five plus one plus

69
00:04:09,660 --> 00:04:09,870
one.

70
00:04:09,870 --> 00:04:10,140
Right.

71
00:04:10,470 --> 00:04:12,510
I'm not labeling this output.

72
00:04:12,780 --> 00:04:16,769
So the first one is the size of this one.

73
00:04:17,070 --> 00:04:19,860
And the second point out is this one that is five.

74
00:04:20,250 --> 00:04:22,590
So this is building two and this is building five.

75
00:04:22,590 --> 00:04:29,760
But once again, I'm telling you that arrays are always parsed by address and the parameter as a pointer.

76
00:04:29,760 --> 00:04:34,800
There's a pointer right at subscript as a pointer to an array which can access.

77
00:04:35,940 --> 00:04:41,370
Now, let us bring the old elements of this array using foreach loop for.

78
00:04:42,620 --> 00:04:43,430
Integer.

79
00:04:45,860 --> 00:04:51,470
Of type from A and C out A and then.

80
00:04:52,640 --> 00:05:01,000
I will remove this, so let us read the code once I have created an array of sci fi elements, right.

81
00:05:01,370 --> 00:05:08,840
And also I have one variable and I'm calling function fun with bypassing a and printing all the elements

82
00:05:08,840 --> 00:05:14,300
of light and function fun, spending all the elements of a using foreach loop.

83
00:05:14,750 --> 00:05:16,140
Let us see what happens.

84
00:05:16,520 --> 00:05:21,340
This is a pointer to this array, a light.

85
00:05:21,680 --> 00:05:25,600
Let us run and see what happens, whether the function brings all the elements or not.

86
00:05:26,420 --> 00:05:27,770
Oops, it's an error.

87
00:05:28,190 --> 00:05:29,390
Why it is an error.

88
00:05:29,890 --> 00:05:32,050
The was not declaring this a scope.

89
00:05:32,270 --> 00:05:39,140
I seeing something else and if I scroll down begin bringing me in ok.

90
00:05:39,140 --> 00:05:42,950
And was not declaring the scope beginning at a small decline in scope.

91
00:05:42,950 --> 00:05:43,550
What is this.

92
00:05:44,120 --> 00:05:51,620
So actually the problem is I cannot use foreach loop upon a pointer we can use for each loop upon an

93
00:05:51,620 --> 00:05:52,100
array.

94
00:05:52,340 --> 00:05:54,550
We cannot use it upon pointer.

95
00:05:54,800 --> 00:06:00,590
So when you pass array to some other function inside that function, you cannot accept that array using

96
00:06:00,590 --> 00:06:03,510
foreach loop because the parameter is a pointer.

97
00:06:03,770 --> 00:06:06,260
So here we should use for loop.

98
00:06:06,590 --> 00:06:10,700
Compiler is not giving a proper message, which is helpful for us to understand.

99
00:06:11,240 --> 00:06:11,690
Right.

100
00:06:11,870 --> 00:06:16,100
So it is saying that Biggin was not given and was not given.

101
00:06:16,130 --> 00:06:21,440
So anyway, I have given this for loop useful loop and stuff.

102
00:06:21,440 --> 00:06:28,490
ForEach loop so foreach loop will not look like it works upon an array which is declared within the

103
00:06:28,490 --> 00:06:32,990
same function because foreach loop works depending on the size of an array.

104
00:06:33,590 --> 00:06:34,030
Yes.

105
00:06:34,040 --> 00:06:35,470
Two, four, six, eight, ten.

106
00:06:35,870 --> 00:06:41,990
These elements are displayed inside function fund and these are displayed inside main function.

107
00:06:42,170 --> 00:06:43,600
This is from main function.

108
00:06:43,610 --> 00:06:43,940
Right.

109
00:06:44,330 --> 00:06:46,010
And this from function function.

110
00:06:46,280 --> 00:06:48,340
Normally we learn one more important thing.

111
00:06:49,100 --> 00:06:51,160
See the array sizes of five.

112
00:06:51,470 --> 00:06:54,710
How does this function will know what is the size of Fanelli.

113
00:06:55,070 --> 00:06:59,230
So usually the pass size also as parameter.

114
00:07:00,350 --> 00:07:02,930
So we will pass it as well.

115
00:07:03,260 --> 00:07:08,960
And then here inside the for loop we will take it as an extra five knive.

116
00:07:08,960 --> 00:07:12,500
The number of elements are more than it will work for more elements.

117
00:07:12,620 --> 00:07:13,060
Right.

118
00:07:13,460 --> 00:07:18,740
So whatever the size you pass from here, this functional work for that much size.

119
00:07:19,990 --> 00:07:27,240
All right, so usually when we pass rate as a barometer to a function, we also pass the size of that

120
00:07:27,250 --> 00:07:29,670
area or the number of elements in that area.

121
00:07:30,460 --> 00:07:32,450
Now, if you run, it will work anyway.

122
00:07:32,470 --> 00:07:33,520
I will not execute it.

123
00:07:34,000 --> 00:07:35,410
Let us see the next point.

124
00:07:35,920 --> 00:07:43,730
See, instead of using square brackets, even you can use Star and you can treat it just like an array.

125
00:07:43,810 --> 00:07:44,610
It works.

126
00:07:45,190 --> 00:07:46,180
See, it is working.

127
00:07:46,180 --> 00:07:47,680
It is spending all the elements.

128
00:07:47,680 --> 00:07:48,990
Two, four, six, eight, 10.

129
00:07:49,300 --> 00:07:50,230
So that's it.

130
00:07:50,260 --> 00:07:54,650
So we have learned about the syntax, how to pass on that and how that is our past.

131
00:07:55,180 --> 00:08:02,680
Now the next important thing I will show you that when this arrays pass by address, then if I change

132
00:08:02,680 --> 00:08:07,240
anything inside this area of zero, I will try on value 15.

133
00:08:07,510 --> 00:08:09,490
Will it change this actual array?

134
00:08:09,730 --> 00:08:11,550
Yes, because it's a pointer.

135
00:08:11,920 --> 00:08:13,810
Why do you write Star Square?

136
00:08:13,810 --> 00:08:15,190
Because it is a pointer only.

137
00:08:15,490 --> 00:08:19,750
So when it is a point when you are changing the value, this value will be change.

138
00:08:20,460 --> 00:08:25,840
Let us check because we are printing all the elements again in the main function after calling function

139
00:08:25,840 --> 00:08:26,180
fun.

140
00:08:26,470 --> 00:08:30,880
So this first element should change to 15 because I have changed it to 15.

141
00:08:30,880 --> 00:08:32,039
Inside function fun.

142
00:08:32,860 --> 00:08:35,289
Let us run so I should get the value.

143
00:08:35,530 --> 00:08:38,000
15, four, six, eight, 10.

144
00:08:38,020 --> 00:08:40,250
Yes, I got 15, four, six, eight, 10.

145
00:08:40,630 --> 00:08:43,210
So actually the elements are two, four, six, eight, 10.

146
00:08:43,600 --> 00:08:47,220
But after calling a function, the first element has changed to 15.

147
00:08:47,650 --> 00:08:55,690
So this is called my address means if I change this, it will reflect into the actual parameter, actual

148
00:08:55,690 --> 00:08:57,110
array that set.

149
00:08:57,520 --> 00:09:00,370
This is what I have explained on white board, the same thing.

150
00:09:00,370 --> 00:09:01,670
I have given a demonstration.

151
00:09:02,110 --> 00:09:08,350
Now, the next thing that I have shown is creating an array inside a function and returning its address

152
00:09:09,010 --> 00:09:11,480
like a function, returning an array.

153
00:09:11,740 --> 00:09:13,860
So for that, I will remove all these things.

154
00:09:14,080 --> 00:09:16,390
Let us write one thresh function.

155
00:09:16,690 --> 00:09:22,680
This function takes and as parameter does, let us call it a size as parameter.

156
00:09:22,690 --> 00:09:27,340
It will take the size and it will create an array and return its pointer.

157
00:09:27,760 --> 00:09:34,690
C square brackets are not supported by the compilers who use Star School because of the old syntax,

158
00:09:34,700 --> 00:09:36,750
so it is not supported in latest compilers.

159
00:09:37,330 --> 00:09:43,990
So use star for return type right now here instead of function, I will take the pointer and I will

160
00:09:44,200 --> 00:09:48,100
create an array so I can create an array using mellark function.

161
00:09:48,520 --> 00:09:52,390
Or I can just created using new new interface.

162
00:09:53,520 --> 00:10:01,770
Sighs Then I will also initialize this area, using follow for individualising zero is less than sighs

163
00:10:02,160 --> 00:10:03,570
and I placeless.

164
00:10:04,260 --> 00:10:11,070
OK, then I will initialize all elements of this array that is dynamically arrays created and heap.

165
00:10:11,070 --> 00:10:11,510
Right.

166
00:10:11,520 --> 00:10:12,380
You know this well.

167
00:10:12,970 --> 00:10:14,040
This is created in him.

168
00:10:14,430 --> 00:10:21,230
Then to this I will assign the value that is a plus one means fust will be one and the second one with

169
00:10:21,240 --> 00:10:23,580
two and three and four like that.

170
00:10:23,880 --> 00:10:26,910
Whatever the size is that many elements I get on.

171
00:10:26,970 --> 00:10:36,060
Right, so it will create 30 and initialised then it should return be better to the point that I like

172
00:10:36,360 --> 00:10:38,430
now inside mean function.

173
00:10:38,820 --> 00:10:46,890
I will create one pointer, I will call it SPQR a length and also I will take one variable size.

174
00:10:47,580 --> 00:10:52,510
OK, I will call it as Ezat and the value is five nine.

175
00:10:52,560 --> 00:10:56,880
I will call that function fun by passing as that that is size.

176
00:10:57,240 --> 00:11:01,830
So it should create an array of size five and return that as I've indicated.

177
00:11:01,840 --> 00:11:09,720
Peja then after doing this I will bring all the elements here in Bidjara assign zero and it's less than

178
00:11:10,440 --> 00:11:15,960
size I plus plus I will print all the elements so I should get Ballymun.

179
00:11:15,960 --> 00:11:19,620
So that is one, two, five.

180
00:11:19,620 --> 00:11:20,040
Right.

181
00:11:20,040 --> 00:11:22,520
One, two, three, four, five pedia.

182
00:11:22,530 --> 00:11:25,010
Rafie and here I will give N.L..

183
00:11:25,290 --> 00:11:28,800
No, you can observe one thing here inside the main function.

184
00:11:28,800 --> 00:11:30,810
I'm just having a pointer and size.

185
00:11:31,290 --> 00:11:36,570
I'm calling a function and taking it further the pointer then using the pointer.

186
00:11:36,570 --> 00:11:39,120
I'm printing an array inside the main function.

187
00:11:39,120 --> 00:11:39,930
There is no array.

188
00:11:40,230 --> 00:11:41,220
Array is not there.

189
00:11:41,550 --> 00:11:44,070
Then who is creating in that function?

190
00:11:44,080 --> 00:11:46,590
One is creating an array of given size.

191
00:11:46,860 --> 00:11:51,660
Here it is creating an array and also initializing all the elements right.

192
00:11:52,170 --> 00:11:54,570
Starting from one onwards and return.

193
00:11:54,840 --> 00:12:01,170
Returning the pointer of that type, which is taken in PDR and main function is using the three.

194
00:12:01,890 --> 00:12:10,140
Yes, this is the benefit of dynamic memory arrays created in the heap and it is created inside function

195
00:12:10,140 --> 00:12:10,560
fun.

196
00:12:11,010 --> 00:12:15,870
But even main function can access it because main function is getting its pointer.

197
00:12:16,080 --> 00:12:21,840
So if you create anything in a heap, then it can be accessed anywhere in the program if pointer is

198
00:12:21,840 --> 00:12:22,740
available on it.

199
00:12:23,100 --> 00:12:24,450
So let us on the program.

200
00:12:24,840 --> 00:12:29,070
The output should be printed as one, two, three, four, five, because the site is five.

201
00:12:31,630 --> 00:12:39,880
Yes, one, two, three, four, five, it is splendid if I change the size to seven, then it will

202
00:12:39,880 --> 00:12:44,580
create an area of size seven and also fill all the elements up to seven.

203
00:12:44,920 --> 00:12:50,530
Then this will bring all seven elements that should be from one to seven.

204
00:12:50,800 --> 00:12:51,500
Let us run.

205
00:12:52,240 --> 00:12:56,920
Yes, the elements are printed from one, two, three, four, five, six and seven.

206
00:12:57,280 --> 00:12:57,870
That's it.

207
00:12:57,970 --> 00:13:00,130
And this is returning of an array.

208
00:13:01,030 --> 00:13:08,020
So this type of thing may we may be using it in other programs that is threatening the address of the

209
00:13:08,020 --> 00:13:14,050
memory, look at inside the function and the main function or some other function is using that memory,

210
00:13:14,050 --> 00:13:15,020
using a pointer.

211
00:13:15,340 --> 00:13:16,150
So that's it.

212
00:13:16,180 --> 00:13:21,620
So we have seen passing of an array and returning an array which was created in a heap.

213
00:13:21,940 --> 00:13:23,470
So that's all in this video.

214
00:13:23,810 --> 00:13:29,950
You practice this by yourself once after watching this, you try to do it by yourself if you struck

215
00:13:29,950 --> 00:13:30,440
anywhere.

216
00:13:30,460 --> 00:13:32,760
Just come back and watch the video once again.

217
00:13:33,290 --> 00:13:33,840
All right.

218
00:13:34,240 --> 00:13:35,520
So that's all in this video.

