1
00:00:00,510 --> 00:00:04,430
In this video, we will learn about Structure as a Parameter.

2
00:00:05,610 --> 00:00:11,580
If you are sending a Structure as Parameter to some function, it may be Call by Value or Call by

3
00:00:11,990 --> 00:00:15,560
Address, Call by Reference is there in C++.

4
00:00:15,570 --> 00:00:17,750
So I will show you Call by Reference also.

5
00:00:18,000 --> 00:00:21,750
So first, let us look at Call by Value method.

6
00:00:21,750 --> 00:00:26,760
See, for that already I have an example this is a structure rectangle having length and breadth as members.

7
00:00:26,760 --> 00:00:32,610
Then I have a main function having a variable of type rectangle and it initialized with it 10 and 5,

8
00:00:32,610 --> 00:00:36,330
so a variable is created with values 10 and 5.

9
00:00:36,370 --> 00:00:43,890
Now I want a function to calculate the area of rectangle by multiplying length and breadth. So I want to write a function

10
00:00:43,940 --> 00:00:49,050
called area, so this function, the selection is already here.

11
00:00:49,170 --> 00:00:50,870
Now what I should pass from here?

12
00:00:50,920 --> 00:00:52,450
Shall I pass length separate, 

13
00:00:52,480 --> 00:00:53,390
breadth separate?

14
00:00:53,400 --> 00:00:56,900
No need, You can send the structure itself.

15
00:00:57,060 --> 00:00:59,430
Yes, that is the benefit of structure.

16
00:00:59,430 --> 00:01:02,990
You can send the structure itself, and the structure is carrying length and breadth.

17
00:01:03,450 --> 00:01:05,000
So both will be passed as one

18
00:01:05,040 --> 00:01:05,680
Parameter.

19
00:01:05,790 --> 00:01:09,770
So it will be passed as a single variable, single data member.

20
00:01:10,020 --> 00:01:11,370
So write r here.

21
00:01:11,610 --> 00:01:13,800
So structure is passed as parameter.

22
00:01:13,940 --> 00:01:16,260
Now what should be the parameter type of this function?

23
00:01:16,260 --> 00:01:21,050
So the argument type should be struct rectangle r

24
00:01:21,640 --> 00:01:22,290
.

25
00:01:22,830 --> 00:01:24,340
So it's rectangle r

26
00:01:24,540 --> 00:01:25,120
.

27
00:01:25,230 --> 00:01:26,650
This is Call by Value.

28
00:01:26,690 --> 00:01:33,720
So instead of r, I will say, r1, just a change of name so you should not feel that they are same,

29
00:01:34,110 --> 00:01:36,190
this variable is different, that variable is different.

30
00:01:36,300 --> 00:01:42,600
So I have taken parameter as r1. Now, that is Call by Value because here I did not write any

31
00:01:42,600 --> 00:01:45,020
* or I did not write &.

32
00:01:45,030 --> 00:01:53,200
This is Call by Value. So, in Call by Value what happens? This object or this variable is passed by value,

33
00:01:53,410 --> 00:01:59,650
So a new variable will be created here, a new variable r1, and it will have its own member that is

34
00:01:59,650 --> 00:02:02,820
length and breadth, right.

35
00:02:03,010 --> 00:02:05,510
So the values will be copied here.

36
00:02:06,160 --> 00:02:08,389
So it means separate object will be created.

37
00:02:08,410 --> 00:02:13,600
Yes, separate object will be created in Call by Value and everything will be copied in the corresponding

38
00:02:13,600 --> 00:02:15,440
members.

39
00:02:15,470 --> 00:02:17,160
This is the benefit of structure.

40
00:02:17,190 --> 00:02:22,060
This is one more thing, one more useful feature. Now, here, what I want to do?

41
00:02:22,070 --> 00:02:23,990
Just calculate area.

42
00:02:24,050 --> 00:02:29,690
So I have to multiply length and breadth. So, directly I will write down, return, I will directly write down in the return

43
00:02:29,690 --> 00:02:32,150
statement r1 . length

44
00:02:32,330 --> 00:02:42,890
So r1's length, right? into r1 . breadth that's all. It will return the result and this printf

45
00:02:42,890 --> 00:02:48,200
will print it. This is Call by Value.

46
00:02:48,530 --> 00:02:56,530
Now one thing, inside this one if I make any changes, if I say
r1 . length ++ , if I modify

47
00:02:56,530 --> 00:02:59,530
length, before returning, so this becomes 11.

48
00:02:59,620 --> 00:03:00,900
So will it change this one?

49
00:03:01,060 --> 00:03:02,880
No it will not change that one.

50
00:03:02,950 --> 00:03:04,090
It is Call by Value.

51
00:03:04,180 --> 00:03:09,320
So the actual parameter will not be modified. This is actual parameter, and this is a formal parameter.

52
00:03:09,370 --> 00:03:12,750
If you're making changes to formal, it will not affect actual.

53
00:03:12,790 --> 00:03:13,750
Right?

54
00:03:14,050 --> 00:03:15,350
This is one thing.

55
00:03:15,640 --> 00:03:15,890
Now,

56
00:03:15,920 --> 00:03:21,460
Next I will show you Call by Reference.

57
00:03:21,550 --> 00:03:24,810
So, for Call by Reference, I don't have to do anything much.

58
00:03:24,850 --> 00:03:33,560
It will be same thing only, and only the thing I have to do here is I should write & r1. Now, that's a 

59
00:03:33,560 --> 00:03:34,330
reference.

60
00:03:34,420 --> 00:03:39,520
So a new object will not be created but the same thing is called as r1 also.

61
00:03:40,420 --> 00:03:43,300
Yes we have already learned about the references.

62
00:03:43,420 --> 00:03:48,220
So the same thing is called as r1 and you know very well that this will not be a separate block

63
00:03:48,250 --> 00:03:52,360
of machine code, it will be pasted at the place of function call.

64
00:03:52,360 --> 00:03:54,490
We have already learned about this.

65
00:03:54,490 --> 00:04:02,560
Only that I have to write & and, rest of the things remain same only, then if I say r1 . length ++

66
00:04:02,560 --> 00:04:03,600
r1 . length ++

67
00:04:03,610 --> 00:04:05,160
Yes this is modified.

68
00:04:05,170 --> 00:04:10,640
This will become 11 that same thing gets modified, right.

69
00:04:10,640 --> 00:04:15,850
So, though for calculating area, I don't need to Call a reference, for calculating area I need to Call

70
00:04:15,860 --> 00:04:19,160
by Value, because it is just supposed to read the values.

71
00:04:19,370 --> 00:04:24,360
So you should know very well, that which function should use which method for.

72
00:04:24,380 --> 00:04:26,930
parameter passing, right.

73
00:04:26,960 --> 00:04:30,620
So this needs Call by Value but also I have explained you by Call by Reference.

74
00:04:30,620 --> 00:04:31,760
How to do that.

75
00:04:31,880 --> 00:04:33,890
In that reference, I can use dot operator.

76
00:04:34,040 --> 00:04:34,420
Yes.

77
00:04:34,420 --> 00:04:34,970
dot operator

78
00:04:35,000 --> 00:04:38,670
Simple as a normal object or normal variable.

79
00:04:39,140 --> 00:04:41,600
Now next, I will show you Call by Address.

80
00:04:41,600 --> 00:04:44,630
So for that I will change the function.

81
00:04:44,870 --> 00:04:49,810
I will write down another function for Call by Address.

82
00:04:49,950 --> 00:04:56,240
Now, for Call by Address, I have an example, I'm calling the function name as change length. Then suppose, I want to

83
00:04:56,240 --> 00:04:58,340
change this length, right.

84
00:04:58,460 --> 00:05:01,340
I want to make it 15, so I can make changes here also

85
00:05:01,340 --> 00:05:03,530
I can make changes also.

86
00:05:03,620 --> 00:05:09,110
No I want it to be done by a function, I don't want to do anything inside main ( ) function, right.

87
00:05:09,120 --> 00:05:14,600
Main function, we should imagine it like a manager and he's having all his assistants and he didn't want

88
00:05:14,600 --> 00:05:16,520
to do anything by himself.

89
00:05:16,520 --> 00:05:19,340
He just want to assign the job to all his assistant.

90
00:05:19,460 --> 00:05:23,060
He knows how to do the work what the work should be done.

91
00:05:23,060 --> 00:05:26,640
So every work he would assign to assistants, so we should imagine it like this.

92
00:05:26,690 --> 00:05:31,500
So main function can change but main function is not doing it, assistant will do it.

93
00:05:31,670 --> 00:05:33,320
So that is change length function.

94
00:05:33,330 --> 00:05:36,590
So we want this function to change this length.

95
00:05:36,740 --> 00:05:41,720
So if you want some function to modify actual parameter then it must be called by the method

96
00:05:41,730 --> 00:05:42,220
Call by Reference.

97
00:05:42,230 --> 00:05:44,230
So we are learning Call by Address

98
00:05:44,240 --> 00:05:46,260
Let us write Call by Address.

99
00:05:46,430 --> 00:05:49,130
First of all, from here I should go on that function.

100
00:05:49,130 --> 00:05:53,060
So yes I should call that function change length, right.

101
00:05:53,240 --> 00:05:57,540
Then what is the parameter I should pass? rectangle,

102
00:05:57,690 --> 00:05:58,210
by Address.

103
00:05:58,220 --> 00:06:01,980
So I should pass &, address, so here what it should take?

104
00:06:02,030 --> 00:06:08,370
It should take it as a pointer, so struct rectangle pointer, right.

105
00:06:08,460 --> 00:06:11,880
* p , then along with this

106
00:06:11,960 --> 00:06:14,100
also, I should pass the new length.

107
00:06:14,150 --> 00:06:19,820
So let us say want to make it as 20, instead of 15 let us say 20 then it should also take new

108
00:06:19,820 --> 00:06:24,530
length, so it is integer l, so it is taking by pointer.

109
00:06:24,620 --> 00:06:30,320
So when you call this function how it looks like, this p is a pointer that belongs to this function and

110
00:06:30,320 --> 00:06:32,860
this is pointing on this one.

111
00:06:33,200 --> 00:06:36,070
Now we have already learned pointer to a structure.

112
00:06:36,400 --> 00:06:41,530
So now you can see that pointer to a structure is useful in  parameter passing also. I want to modify

113
00:06:41,530 --> 00:06:44,280
that length, so say, p ->

114
00:06:44,350 --> 00:06:51,460
Because for accessing this member indirectly using pointer we have to use ->, p -> length = l ;

115
00:06:51,820 --> 00:06:57,720
So this length would be modified to 20. That's it.

116
00:06:58,050 --> 00:06:58,830
So this is how

117
00:06:59,400 --> 00:07:02,750
We can use Call by Address for the structure.

118
00:07:03,120 --> 00:07:06,540
So this type of thing I have used in my course.

119
00:07:06,570 --> 00:07:12,420
So whenever I'm writing structure and function so wherever I have to make changes in a structure

120
00:07:12,720 --> 00:07:17,060
I'm using Call by Address, because I'm writing C language code, right.

121
00:07:17,070 --> 00:07:21,970
So basic code I'm writing in C language only, so I have used Call by Address.

122
00:07:21,980 --> 00:07:27,270
So from here I'm sending an address and then I'm using a pointer and by using arrow, I accessing the

123
00:07:27,270 --> 00:07:28,200
members.

124
00:07:28,230 --> 00:07:32,730
So you should be familiar with this code and if you are comfortable with this one then you can smoothly

125
00:07:32,730 --> 00:07:35,300
follow the rest of the topics.

126
00:07:35,560 --> 00:07:36,310
Right.

127
00:07:36,390 --> 00:07:39,950
So this is the method for Call by Address.

128
00:07:40,000 --> 00:07:41,840
Now one more thing I have to show you.

129
00:07:41,940 --> 00:07:45,900
I'll remove this and they'll explain you.

130
00:07:45,920 --> 00:07:47,590
One more thing that I have to show you.

131
00:07:48,170 --> 00:07:52,130
I have defined a structure here having array as a data member.

132
00:07:52,460 --> 00:07:57,560
And one more integer data member, 2 data members I have taken. Mainly array is important there. So,

133
00:07:57,760 --> 00:08:04,580
we'll focus on array. Here, inside the main function I have created a variable of that type struct test

134
00:08:05,120 --> 00:08:10,270
and the name is t, and I have also initialized it so it will have two members, array and n.

135
00:08:10,350 --> 00:08:15,740
So array, the size of the array is 5 and one more integer variable is there.

136
00:08:15,770 --> 00:08:20,200
So all are initialized. Now, can we send

137
00:08:20,420 --> 00:08:24,140
structure as a parameter in Call by Value?

138
00:08:24,390 --> 00:08:24,780
Yes.

139
00:08:24,790 --> 00:08:28,430
Call by Value, we can send. But can we send an array with Call by Value?

140
00:08:28,550 --> 00:08:34,850
No, array can be sent only by Call by Address, already we have discussed this in previous video, then

141
00:08:35,510 --> 00:08:37,730
but this is structure having an array.

142
00:08:37,789 --> 00:08:39,270
Can you pass this by value?

143
00:08:39,650 --> 00:08:40,190
Yes.

144
00:08:40,200 --> 00:08:44,580
Structure you can pass by value even if it is having an array inside.

145
00:08:44,600 --> 00:08:47,140
So that's the important thing we are learning now.

146
00:08:47,150 --> 00:08:51,950
So here I have a function called fun and I'm taking it as a Call by Value.

147
00:08:51,950 --> 00:08:55,840
So from here on passing t, so whether it works? yes it works.

148
00:08:55,850 --> 00:08:58,280
So what happens when this is passed?

149
00:08:58,610 --> 00:09:02,290
So again a new t1 is created with an array.

150
00:09:02,690 --> 00:09:03,480
Right.

151
00:09:03,500 --> 00:09:08,070
And also n, and every thing from here, it will be copied here.

152
00:09:08,090 --> 00:09:09,830
2, 4, 6, 8, 10.

153
00:09:09,980 --> 00:09:17,820
And also value 5, everything will be copied. So,when a structure is passed by value even if it is having an array

154
00:09:17,940 --> 00:09:21,800
an array will be created separately in the member and it will be filled.

155
00:09:22,100 --> 00:09:26,730
So imagine all these values has to be copied automatically just done by the compiler.

156
00:09:26,990 --> 00:09:28,470
So this is time consuming.

157
00:09:28,640 --> 00:09:33,880
That is the reason the compiler doesn't support array by value, they allow only Call by Reference. But difference is

158
00:09:33,890 --> 00:09:37,220
inside the structure they support it. They have to do it.

159
00:09:37,580 --> 00:09:43,710
So this will be copied. We are not copying it. Internally it is happening means compiler will do this.

160
00:09:43,710 --> 00:09:44,420
.

161
00:09:44,510 --> 00:09:50,720
So yes now if I make any changes, how I can access this one? I can say  t1 . A, because it's own member,

162
00:09:50,720 --> 00:09:50,980
Right?

163
00:09:50,990 --> 00:09:52,080
It's Call by Value.

164
00:09:52,180 --> 00:09:55,800
And I can say, t1 . A[0] = 10;

165
00:09:55,910 --> 00:09:58,610
So this will become 10, right.

166
00:09:58,970 --> 00:10:04,330
So I can change the value, t1 . A[1] = 9;

167
00:10:04,430 --> 00:10:07,000
So this will become 9.

168
00:10:07,970 --> 00:10:10,460
But this will not change, because this is Call by Value.

169
00:10:11,900 --> 00:10:15,590
So I can use dot operator and access the members.

170
00:10:15,590 --> 00:10:19,630
So that's all, We have seen how we can pass structure as parameter.

171
00:10:19,760 --> 00:10:23,810
We have seen Call by Value, We have seen Called by Reference, We have seen Call by Address, and also

172
00:10:23,810 --> 00:10:29,290
we have seen that, even if an array is there inside a structure, that can be passed by value. Structure can

173
00:10:29,290 --> 00:10:30,080
be Passed by Value.

174
00:10:31,440 --> 00:10:32,630
That's all in this video.

