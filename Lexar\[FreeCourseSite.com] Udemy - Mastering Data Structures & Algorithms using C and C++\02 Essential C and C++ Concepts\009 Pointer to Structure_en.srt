1
00:00:00,210 --> 00:00:05,910
In this video, we learn about a pointer to a structure pointed that we have studied now let us see

2
00:00:05,920 --> 00:00:07,930
how to have a pointer up on that structure.

3
00:00:08,340 --> 00:00:13,010
So already we have seen this example of a kind of structure having determined bushland.

4
00:00:13,020 --> 00:00:19,020
And right now, inside the main function, I have a rectangle variable and it is initialized with a

5
00:00:19,020 --> 00:00:19,870
10 and five.

6
00:00:19,890 --> 00:00:24,330
So this variable occupies memory and it will have the value stand and fight.

7
00:00:25,540 --> 00:00:31,960
Now, if I want, I can directly access these members London, but by using variable limb ah so if I

8
00:00:31,960 --> 00:00:39,870
want to print or I want to change so I can say ah not lend a sign chuffing, I can change it like this,

9
00:00:40,690 --> 00:00:43,710
but let us learn now how to access it with the pointer.

10
00:00:43,720 --> 00:00:50,920
So first of all I will declare a pointer, a rectangle pointer.

11
00:00:51,340 --> 00:00:54,400
So I'll give the pointer name as B.

12
00:00:56,130 --> 00:01:01,920
The first time how much memory a tangle consumes, as we know, the stakes to which we are zooming in

13
00:01:01,920 --> 00:01:02,910
digital is to buy.

14
00:01:02,950 --> 00:01:07,830
So this is total four bytes than how much memory it takes.

15
00:01:08,040 --> 00:01:12,090
It takes just two bites every point or whatever the type may be.

16
00:01:12,340 --> 00:01:15,480
Pointer takes two bytes, so let us assume two bytes.

17
00:01:15,960 --> 00:01:18,120
So Pointer doesn't take all four bytes.

18
00:01:18,120 --> 00:01:19,100
It will not have left.

19
00:01:19,230 --> 00:01:22,610
But so let me show you a pointer diagrammatically.

20
00:01:23,250 --> 00:01:25,190
So just like a simple variable.

21
00:01:25,650 --> 00:01:30,990
No, I want to assign the address of this are so disappointed.

22
00:01:30,990 --> 00:01:33,090
I will be pointing on this variable.

23
00:01:33,090 --> 00:01:36,760
Ah, this is initialize, declared and initialize.

24
00:01:37,530 --> 00:01:43,220
Now I want to access these members using that pointer so all I can do that me.

25
00:01:43,260 --> 00:01:43,710
Right.

26
00:01:43,800 --> 00:01:45,660
I want to modify led to 20.

27
00:01:45,660 --> 00:01:46,470
Suppose so.

28
00:01:46,470 --> 00:01:51,480
Can I say paedo to lend assign 20.

29
00:01:52,140 --> 00:01:55,470
No B dot B is a pointer.

30
00:01:55,470 --> 00:01:57,840
It's not a variable, it's not having members.

31
00:01:57,870 --> 00:02:02,400
Then do what I have to come here then I can access dismembers.

32
00:02:02,400 --> 00:02:04,860
So go to the place where pointer is pointing.

33
00:02:04,860 --> 00:02:08,759
Suppose the starting address is 200 then it is having it is 200.

34
00:02:08,789 --> 00:02:10,900
So go to at this 200 and access data.

35
00:02:11,130 --> 00:02:13,300
So ok, so I should write aztek.

36
00:02:13,330 --> 00:02:14,430
OK, drastic.

37
00:02:15,450 --> 00:02:19,720
But this is wrong because the higher precedence is for DOT operators.

38
00:02:19,790 --> 00:02:22,880
Fossett, it will take it as a B dot land again, it is wrong.

39
00:02:23,220 --> 00:02:25,580
So I should enclose this inside the bracket.

40
00:02:25,890 --> 00:02:31,680
So this should be inside the bracket and then not oh, so much I have to write on for accessing that

41
00:02:31,680 --> 00:02:32,120
number.

42
00:02:32,370 --> 00:02:38,930
So instead of following the syntax, see language gives a simple syntax for the members using pointer.

43
00:02:38,940 --> 00:02:44,880
So I can simply say B I'll let assign 20.

44
00:02:45,570 --> 00:02:48,870
So either we can use this or we can use this.

45
00:02:49,530 --> 00:02:58,680
So finally, sort of normal variable use dot operator, this dot operator and four pointer variable

46
00:02:58,950 --> 00:03:03,340
use B I'll write instead of dot we have an arrow.

47
00:03:04,320 --> 00:03:12,340
So this is the method of accessing structure using pointer arrow is used now.

48
00:03:12,360 --> 00:03:13,800
Next one more thing I will do.

49
00:03:14,100 --> 00:03:18,780
I will create the object dynamically in the heap using pointer.

50
00:03:19,140 --> 00:03:23,700
So I will create an object or a variable of type rectangle dynamically.

51
00:03:23,880 --> 00:03:26,070
So I'll remove this and I will write on the code.

52
00:03:27,800 --> 00:03:33,110
Now, let us see how to create it dynamically so far, but first of all, I need a pointer, so I will

53
00:03:33,110 --> 00:03:38,380
take a pointer of type structure struck a rectangle and a pointer.

54
00:03:38,750 --> 00:03:41,490
So I'm using Latapy for a pointer.

55
00:03:41,510 --> 00:03:43,020
So this is a pointer.

56
00:03:43,280 --> 00:03:45,500
So this will be created inside the stack.

57
00:03:45,500 --> 00:03:48,650
And already you are familiar when you declare the variables.

58
00:03:48,650 --> 00:03:54,020
They are created inside the stack as a part of code activation record of function.

59
00:03:54,050 --> 00:03:55,280
So yes, it's a variable.

60
00:03:55,280 --> 00:03:56,460
So it's inside the stack.

61
00:03:56,930 --> 00:04:01,940
Now we want a variable of type structure, object of type, structure in heap.

62
00:04:02,120 --> 00:04:05,140
So how to get the membrane heap mellark function.

63
00:04:05,250 --> 00:04:05,690
Yes.

64
00:04:06,080 --> 00:04:08,230
So I should call log function.

65
00:04:08,270 --> 00:04:08,840
Yes.

66
00:04:09,140 --> 00:04:10,520
OK, function then.

67
00:04:12,010 --> 00:04:14,570
I must give the number of bytes that are required.

68
00:04:14,590 --> 00:04:23,800
So actually the same as this is structure sites, so I can mention four or else I can say size of stock

69
00:04:24,040 --> 00:04:24,930
rectangle.

70
00:04:25,510 --> 00:04:30,330
So rectangle structure stick and right.

71
00:04:30,790 --> 00:04:33,550
That this will look at the memory.

72
00:04:33,850 --> 00:04:40,200
Now we have to type Kostic because Mallott function returns void pointer right.

73
00:04:40,630 --> 00:04:42,730
For WidePoint that it's a generic pointer.

74
00:04:42,760 --> 00:04:44,050
So I have to type cast it.

75
00:04:44,290 --> 00:04:46,510
So for typecasting I should write on here.

76
00:04:46,780 --> 00:04:50,960
Stuck a rectangle asterisk.

77
00:04:51,280 --> 00:04:57,800
So I'm type casting into a rectangle type structure pointer then assign it to be.

78
00:04:58,300 --> 00:05:03,550
This is how we can allocate object of type this rectangle in here.

79
00:05:03,790 --> 00:05:09,100
So this will be created in here and it will be having length and breadth and the pointer will be pointing

80
00:05:09,100 --> 00:05:09,740
on that one.

81
00:05:11,200 --> 00:05:19,180
This doesn't hurt then I want to access the members so I can use it, open it up so Beanland assign

82
00:05:19,600 --> 00:05:24,060
value to them and the piece, but assign value five.

83
00:05:26,230 --> 00:05:27,830
So that's all I can assign.

84
00:05:28,260 --> 00:05:31,150
But so does a written and this will be five.

85
00:05:32,570 --> 00:05:39,260
That's all so more if you want, you can write on, I have to show you how to create a variable of type

86
00:05:39,260 --> 00:05:41,900
structure in heap by using Marlock function.

87
00:05:42,920 --> 00:05:49,370
So that's all about pointed to a structure, so in the previous example, I have shown you an existing

88
00:05:49,370 --> 00:05:56,030
variable to the point I was pointing out, but no, this is a dynamic object created in a heap and the

89
00:05:56,040 --> 00:05:57,080
point is pointing there.

90
00:05:57,350 --> 00:06:00,350
So that was static object and this dynamic object.

91
00:06:01,250 --> 00:06:03,110
That's all about wanted to structure.

