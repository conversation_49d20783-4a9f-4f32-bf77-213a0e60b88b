1
00:00:00,630 --> 00:00:07,410
And this video we will write C++ class for Lord <PERSON><PERSON><PERSON> using both the formless law Romijn

2
00:00:07,410 --> 00:00:09,690
representation and parliamentary presentation.

3
00:00:10,690 --> 00:00:17,440
So I'm using the same project that I have used for diagrammatic so already a class for TitleMax is written

4
00:00:17,440 --> 00:00:17,740
here.

5
00:00:18,900 --> 00:00:25,140
So instead of writing a class right from the scratch, I prefer modifying this class for diagonal as

6
00:00:25,140 --> 00:00:31,560
a class for lower triangular marks because there are only few methods like instructors and instructor

7
00:00:31,560 --> 00:00:33,380
and said get a this limiters.

8
00:00:35,950 --> 00:00:37,460
So the changes are very few.

9
00:00:38,920 --> 00:00:44,470
So it's better you write the complete class from the beginning, but you should also experience how

10
00:00:44,470 --> 00:00:45,870
to modify the code.

11
00:00:46,210 --> 00:00:49,690
So I'm going to modify this class for a lower triangular.

12
00:00:51,000 --> 00:00:54,260
This class named diagonal should be changed to lower triangular.

13
00:00:54,850 --> 00:01:00,520
So then again, constructor will also change and the structure will also change the name.

14
00:01:01,030 --> 00:01:04,790
And wherever the class name is used with school resolution, that should also change.

15
00:01:05,110 --> 00:01:09,580
So I have to make changes at various places so better I'll do what I find and replace.

16
00:01:09,580 --> 00:01:11,980
I will select fine and replace.

17
00:01:13,310 --> 00:01:13,940
Agnel.

18
00:01:14,910 --> 00:01:19,590
Change that signal to lower triangular, so I'm giving you a shot that is lower, right?

19
00:01:24,050 --> 00:01:25,160
Replace all.

20
00:01:30,600 --> 00:01:36,570
See, all these names have changed to lower the class for lower triangular like.

21
00:01:37,770 --> 00:01:44,520
But the mechanism inside these matters is still for Bagnall matics, so let us modify those things.

22
00:01:44,520 --> 00:01:47,370
So let us check everything line by line.

23
00:01:47,910 --> 00:01:48,690
Plus name is.

24
00:01:49,050 --> 00:01:54,830
Right, OK, data members are appointed to an area and dimension and perfect.

25
00:01:55,590 --> 00:01:59,300
Then this is the default constructor of dimension two by two.

26
00:01:59,700 --> 00:02:01,510
And what should be the size of an array.

27
00:02:02,250 --> 00:02:05,570
It should be doing two two plus one.

28
00:02:05,580 --> 00:02:07,620
That is three by two.

29
00:02:07,650 --> 00:02:08,340
Six by two.

30
00:02:08,340 --> 00:02:09,240
That should be three.

31
00:02:09,240 --> 00:02:12,060
So instead of directly writing military, I have used the formula.

32
00:02:14,570 --> 00:02:16,210
That is a parametrized construct.

33
00:02:16,300 --> 00:02:24,380
So what should be the size of this array will be only the size of an array will be entered into and

34
00:02:24,380 --> 00:02:30,320
plus one by two, as we have seen, there are any number of non-zero elements.

35
00:02:34,330 --> 00:02:40,900
Then these are the function definitions, so they are only then does a set function set function.

36
00:02:40,900 --> 00:02:43,960
It will take in excess energy and the value X.

37
00:02:44,990 --> 00:02:45,440
So.

38
00:02:46,420 --> 00:02:54,130
Non-zero values will be present only at the end, he says it is a greater than equal game and the elements

39
00:02:54,130 --> 00:02:57,910
are stored at that place using the formula I into.

40
00:02:59,240 --> 00:03:04,250
I minus one by two, plus J, minus one, yes.

41
00:03:06,120 --> 00:03:11,160
Then coming to a jet function that will return an element of a given index again here, also non-zero

42
00:03:11,160 --> 00:03:19,460
elements are present at Index I to call the G and the elements are retrieved from Location I into.

43
00:03:22,350 --> 00:03:28,230
Eye to eye, minus one by two, plus G, minus one.

44
00:03:31,400 --> 00:03:37,370
The Nexus display function, this function will display in our matrix, including zero and on the elements

45
00:03:37,370 --> 00:03:41,230
and on the elements are present only at, I agree, totally equal to zero.

46
00:03:41,960 --> 00:03:44,660
And the formula for retrieving those elements is.

47
00:03:47,500 --> 00:03:51,250
Eye to eye, minus one eye to.

48
00:03:53,090 --> 00:04:00,080
BITOU, plus minus one, yes, and one more thing, these four loops for a diagonal matrix, I have

49
00:04:00,080 --> 00:04:03,580
taken them from zero onwards, but they should be from one to end.

50
00:04:03,590 --> 00:04:04,300
This is better.

51
00:04:04,760 --> 00:04:08,490
And they also start from one, two, and it's better.

52
00:04:09,290 --> 00:04:12,230
So I have modified this for loop starting and ending index also.

53
00:04:13,560 --> 00:04:14,970
Therefore, all the glass is ready.

54
00:04:16,140 --> 00:04:20,269
Now, let us write on the main function, so for writing mean function, first of all, I should know

55
00:04:20,279 --> 00:04:25,320
what is the dimension of Americans that I want to create so far that I will take the input from the

56
00:04:25,320 --> 00:04:26,780
keyboard and see and.

57
00:04:28,380 --> 00:04:29,340
Dimension's.

58
00:04:30,470 --> 00:04:36,350
And seen the once in all the dimensions, I can create a lower of Matrix.

59
00:04:37,610 --> 00:04:44,540
Alem followed Bangla mattocks of Diamond, so this will be calling parametrized constructor of this

60
00:04:44,540 --> 00:04:50,900
class and he will be creating an area of size and in doing this one, by doing so, the will be set

61
00:04:50,900 --> 00:04:51,590
into any.

62
00:04:55,490 --> 00:04:59,660
And in this matter, I will take all the elements and I will store the elements.

63
00:05:01,750 --> 00:05:07,930
And I will said that elements in the mix, so for this, I will take some variable X, then here I will

64
00:05:07,930 --> 00:05:09,940
say enter all elements.

65
00:05:14,880 --> 00:05:24,600
Then using follow up, I will read all the elements integer, I assign one and I is less than equal

66
00:05:24,600 --> 00:05:30,080
to the dimension that we have given the eight plus plus then next follow.

67
00:05:31,650 --> 00:05:39,570
For indigency, assign one and is less than equal to dimension and plus plus.

68
00:05:43,150 --> 00:05:50,260
Then I will take an element into Variable X and I will call the function M and I will call the function

69
00:05:50,260 --> 00:05:57,180
11. set an element at index icon and the element that I want to insert is X.

70
00:05:58,330 --> 00:06:04,150
Yes, this for loops will take all the elements of a loop triangular matrix that it will take zero as

71
00:06:04,150 --> 00:06:05,330
well as non-zero elements.

72
00:06:06,520 --> 00:06:12,420
And after that, I will say 11. display the celestial elements.

73
00:06:13,600 --> 00:06:16,630
So you just know if you have taken the dimension of an Maciek.

74
00:06:16,640 --> 00:06:19,380
So we should also have one function inside this Maciek.

75
00:06:19,390 --> 00:06:21,390
So I will add it at the end of the display.

76
00:06:21,910 --> 00:06:23,340
We want to know the dimensions.

77
00:06:23,340 --> 00:06:31,120
So say get dimensions should be a function, it should just return.

78
00:06:31,130 --> 00:06:35,650
And so if you were told we want to know the dimension, we can call this function.

79
00:06:35,980 --> 00:06:41,200
But here I have a directly a D because already indeed we have the dimensions I, I'm using the.

80
00:06:42,440 --> 00:06:48,710
Yeah, here is asking for dimensions, so I'll give the dimensions as five now enter all the elements

81
00:06:49,160 --> 00:06:55,340
into the elements as one zero zero zero five, that is one two, then the remaining elements are all

82
00:06:55,340 --> 00:07:02,030
Zeitels, then one, two, three, and the remaining elements are Zeitels, one, two, three, four,

83
00:07:02,030 --> 00:07:03,320
remaining element of zero.

84
00:07:03,680 --> 00:07:05,780
And one, two, three, four, five.

85
00:07:07,650 --> 00:07:12,850
Next one, so it is displaying all the elements, I'm getting back all those elements by display function.

86
00:07:12,880 --> 00:07:15,670
So here you can see this is displaying all the elements.

87
00:07:16,590 --> 00:07:21,460
Now, let us convert this program by using all the major formula.

88
00:07:21,760 --> 00:07:23,520
See, this is for only just formula.

89
00:07:23,790 --> 00:07:30,700
And the formula I have to make changes at a few places is a formula for formula.

90
00:07:30,710 --> 00:07:34,350
Also the size of a Nightengale Besame that is only this one by two.

91
00:07:34,710 --> 00:07:39,640
Then for setting the value and getting the value as well as a display, the formula will change.

92
00:07:40,020 --> 00:07:44,070
So this formula is for all media representation or for the media representation.

93
00:07:44,070 --> 00:07:49,260
If you remember the formula descend into G minus one.

94
00:07:50,470 --> 00:08:00,670
Minus three, minus two into minus one, divided by two plus minus G.

95
00:08:02,690 --> 00:08:08,510
Is the formula and the same formula I should use it in get function as well as display function, so

96
00:08:08,510 --> 00:08:11,480
I will copy this and I will post it here.

97
00:08:16,040 --> 00:08:22,070
And also in this function, we should follow the same formula that said, let us run the program and

98
00:08:22,070 --> 00:08:22,400
see.

99
00:08:25,220 --> 00:08:30,560
Dimensions, it is asking, so I'll give the dimensions for I will enter the elements one zero zero

100
00:08:30,560 --> 00:08:38,110
zero nine one two zero zero one two three and zero one two three four.

101
00:08:39,590 --> 00:08:41,890
Yes, I'm getting back the same elements.

102
00:08:41,900 --> 00:08:43,260
That is wonderful.

103
00:08:44,110 --> 00:08:44,750
That's all.

104
00:08:45,260 --> 00:08:50,330
So I have shown you Romijn as well as Clemenger formula, how easy it is to make changes.

105
00:08:50,330 --> 00:08:56,900
Once you have a good grip on the C++ programming, you can make changes so you can play with the code

106
00:08:56,900 --> 00:08:57,350
like this.

107
00:08:57,350 --> 00:09:02,810
You can make changes at a few places and quickly you can transform a program into some of the program.

108
00:09:04,230 --> 00:09:09,450
Nofollow triangulator have completed rest of the Matisses that support Anglet, automatic matics, you

109
00:09:09,450 --> 00:09:10,470
have to implement them.

110
00:09:15,290 --> 00:09:20,240
Now, for the rest of the Matisses, like Sematic Mattocks and Upper Triangle of Mattocks, our topics,

111
00:09:20,240 --> 00:09:23,900
politics and all matters, you have to implement the programs.

112
00:09:24,050 --> 00:09:24,430
Right.

113
00:09:25,970 --> 00:09:31,310
And if it's any difficulty, you can inform me if I can provide you a help or even I can give you a

114
00:09:31,310 --> 00:09:34,250
program if you are unable to do or you can send your program to me.

115
00:09:34,490 --> 00:09:37,760
If there are any errors, you are unable to remove them.

116
00:09:37,760 --> 00:09:40,010
If there are any survivors, then I can help you.

117
00:09:42,510 --> 00:09:43,530
They followed this.

