1
00:00:00,240 --> 00:00:08,270
And if we do, we will learn how to evaluate our postfix expression, so here an example already I have

2
00:00:08,280 --> 00:00:10,350
a infix expression.

3
00:00:11,100 --> 00:00:13,490
Let us see what is the value of the fund?

4
00:00:13,500 --> 00:00:14,580
What is the answer for the.

5
00:00:16,070 --> 00:00:21,870
Three fights of 15 plus six, <PERSON><PERSON> is three, minus four.

6
00:00:22,520 --> 00:00:24,830
So this is 18 minus four.

7
00:00:26,070 --> 00:00:33,690
14, so the result of this explosion is 14, so I have evaluated and I got the result that 14 women

8
00:00:33,690 --> 00:00:40,200
learn how to evaluate if it isn't postfix of form, we have already learned how to convert infix to

9
00:00:40,200 --> 00:00:40,830
postfix.

10
00:00:41,070 --> 00:00:46,260
After converting into postfix, we must evaluate it and get the result that that's what we are going

11
00:00:46,260 --> 00:00:46,970
to learn now.

12
00:00:47,580 --> 00:00:52,170
So I have an expression in infix form, so I will first convert it into postfix and then I'll show you

13
00:00:52,170 --> 00:00:52,980
the procedure.

14
00:00:54,080 --> 00:00:58,800
Let us convert this into postfix, so this is how you precedent's multiplication and division.

15
00:00:59,030 --> 00:01:07,580
So let us convert this one three five star and this is next how you precedence or six to divide, six,

16
00:01:08,090 --> 00:01:12,620
two and divide then this addition and subtraction to order.

17
00:01:12,650 --> 00:01:18,800
So first addition, this is in between this portion and this portion, support the portions portion

18
00:01:18,800 --> 00:01:20,180
of the left hand side.

19
00:01:20,630 --> 00:01:27,050
So just right plus then this as minus four four four four then minus.

20
00:01:27,980 --> 00:01:32,240
So this is the sixth form directly by reading an expression I have converted into postfix.

21
00:01:32,240 --> 00:01:33,770
So if you practice you can do that.

22
00:01:34,190 --> 00:01:37,180
Now let us see how to value it for evaluation.

23
00:01:37,460 --> 00:01:40,220
We need a stack, so let me take a stack.

24
00:01:40,970 --> 00:01:42,560
So Stack is ready now.

25
00:01:42,560 --> 00:01:44,960
Let us see the procedure in the procedure.

26
00:01:44,960 --> 00:01:51,050
We will control postfix expression by taking one simple at a time and wildcatting.

27
00:01:51,050 --> 00:01:52,010
What we have to do.

28
00:01:52,160 --> 00:01:54,560
Let us learn first symbol.

29
00:01:56,090 --> 00:01:57,470
First symbol three.

30
00:01:57,500 --> 00:01:58,370
It's an option.

31
00:01:58,670 --> 00:02:01,520
If it is an open, push it into the stack.

32
00:02:02,270 --> 00:02:04,100
Next is an open.

33
00:02:04,280 --> 00:02:05,780
Push it into the stack.

34
00:02:06,320 --> 00:02:07,790
Next is operator.

35
00:02:08,389 --> 00:02:13,740
If we get any operator powerboats two symbols from the stack and perform that operation.

36
00:02:14,180 --> 00:02:18,290
So first about five, then about the next one three.

37
00:02:18,620 --> 00:02:21,950
So frustrated that Bob Dole will come on the right hand side.

38
00:02:22,250 --> 00:02:24,110
Second volume, come on the left hand side.

39
00:02:24,110 --> 00:02:29,830
Then we perform this operation, then we perform this operation that is multiplication.

40
00:02:30,020 --> 00:02:31,360
So three five fifteen.

41
00:02:31,370 --> 00:02:32,170
So we got the result.

42
00:02:32,210 --> 00:02:36,410
Fifteen push this result into the stack the next.

43
00:02:38,190 --> 00:02:45,270
Nick Symbolists, six, it's an open push it into the stack, Nick symbolism to it's an open push it

44
00:02:45,270 --> 00:02:46,080
into the stack.

45
00:02:46,380 --> 00:02:53,000
Nick Symbolists divide operator support two symbols for first two, then six.

46
00:02:53,540 --> 00:02:56,760
So both are down and the operation is divide.

47
00:02:57,720 --> 00:02:58,860
So the result is three.

48
00:02:59,190 --> 00:03:00,960
Push that a three into the stack.

49
00:03:01,270 --> 00:03:07,260
Next is a game operator superball to some booth and perform the operation for First Symbolist three

50
00:03:07,740 --> 00:03:14,370
then next symbolists 15 operations plus result is 18.

51
00:03:14,610 --> 00:03:16,430
Push that result into the stack.

52
00:03:17,030 --> 00:03:18,690
Nick Symbolists operand.

53
00:03:18,690 --> 00:03:20,190
So push it into the stack.

54
00:03:20,190 --> 00:03:22,920
And Nick Symbol is operators of two symbols.

55
00:03:22,920 --> 00:03:28,410
Once again, Ford is gone for this Bob down to about 18.

56
00:03:28,710 --> 00:03:32,250
Then the operation is minus performed minus whether third is 14.

57
00:03:32,250 --> 00:03:34,380
Push that result 14 into the stack.

58
00:03:35,900 --> 00:03:39,770
And of expression and the result of the expression is in the stack.

59
00:03:40,190 --> 00:03:45,140
So at the end of expression, we got the result of our expression inside the stack and a few things

60
00:03:45,140 --> 00:03:45,800
to observe.

61
00:03:46,160 --> 00:03:48,800
See, the values that I have taken in my example are indeed your.

62
00:03:49,230 --> 00:03:51,320
So this is tagged as having integer value.

63
00:03:51,340 --> 00:03:53,680
So these operations are performed on integers.

64
00:03:54,020 --> 00:03:56,970
That is multiplication of two integers, three and five.

65
00:03:56,970 --> 00:03:57,720
We've got 15.

66
00:03:57,860 --> 00:03:59,300
So these are integer values.

67
00:03:59,300 --> 00:04:00,310
There's one observation.

68
00:04:00,720 --> 00:04:06,860
Then the second thing, when you are popping out first symbol or first value, that will become second

69
00:04:06,860 --> 00:04:07,700
option here.

70
00:04:07,970 --> 00:04:12,460
And the second value that is popped out from the stack becomes first option.

71
00:04:12,940 --> 00:04:18,019
Otherwise, if you change the order, it will become five, two, three, then this will become two

72
00:04:18,019 --> 00:04:18,760
by six.

73
00:04:18,769 --> 00:04:22,810
So the result is different for try to maintain the same.

74
00:04:25,350 --> 00:04:30,420
And the last thing we can observe here is that the evaluation of an execution is done by just scanning

75
00:04:30,420 --> 00:04:32,550
through an expression only once.

76
00:04:33,430 --> 00:04:37,420
Now, to show you the workings of this procedure in the form of a table, because this is a repeating

77
00:04:37,420 --> 00:04:43,960
procedure, reiterative procedure, Etretat procedures can be traced using a table showing the contents

78
00:04:43,960 --> 00:04:46,610
of data structure for the variables, how they are changing.

79
00:04:47,080 --> 00:04:51,490
Let me trace this procedure once again and let us see the procedure once again.

80
00:04:51,490 --> 00:04:56,570
And I will show you how the values are changing in the form of a table, the symbol for the symbol of

81
00:04:56,620 --> 00:04:57,160
the three.

82
00:04:57,460 --> 00:05:00,820
So push it into the stack for symbol a three.

83
00:05:01,000 --> 00:05:04,870
So push that three into the stack, symbol a five.

84
00:05:05,110 --> 00:05:07,220
So push that into the stacks on the top.

85
00:05:07,240 --> 00:05:12,530
We will have five the next we will have three separating them and then start.

86
00:05:12,970 --> 00:05:15,420
So when you have strong two assemble.

87
00:05:15,440 --> 00:05:21,490
So first to symbolize this one and the second symbol that is this one, multiply and get the results

88
00:05:21,490 --> 00:05:22,930
to the result into the stack.

89
00:05:23,770 --> 00:05:25,210
The next symbol is six.

90
00:05:25,570 --> 00:05:26,680
Push it into the stack.

91
00:05:26,680 --> 00:05:28,150
So six, come on 15.

92
00:05:28,490 --> 00:05:30,640
Next symbol is to push it into the stack.

93
00:05:30,650 --> 00:05:31,720
So two, come on six.

94
00:05:31,720 --> 00:05:33,090
Come on, 15 from the top.

95
00:05:33,100 --> 00:05:36,130
If you see these are the values in this stack, then divide.

96
00:05:36,490 --> 00:05:41,530
So property values, first value of dollars to the second value pop go to six, then divide them and

97
00:05:41,530 --> 00:05:42,250
four is three.

98
00:05:42,550 --> 00:05:46,660
So this is three comma fifteen threes pushed into the stack.

99
00:05:47,050 --> 00:05:50,350
The next symbolist plus two values from the stack.

100
00:05:50,350 --> 00:05:55,720
First values three second value popped out of 15 but form additions and the result is eighteen.

101
00:05:55,930 --> 00:05:57,700
Push that again into the stack.

102
00:05:58,330 --> 00:05:59,650
The next symbol is a four.

103
00:05:59,800 --> 00:06:09,130
So Ford is pushed into the stack for committee next symbolist minus about Walbridge to operands and

104
00:06:09,130 --> 00:06:10,210
perform the operation operations.

105
00:06:10,210 --> 00:06:15,060
So the first symbol is for the Nexus 18 and if you subtract, the result is 14.

106
00:06:15,070 --> 00:06:16,920
So the result is pushed into the stack.

107
00:06:17,500 --> 00:06:18,160
So that's it.

108
00:06:18,550 --> 00:06:24,400
So this is how the procedure is working and scanning through and postfix expression, just one.

109
00:06:24,640 --> 00:06:27,270
And if you want to know the time taken, it is.

110
00:06:27,340 --> 00:06:34,210
And what is in here, the size or length of postfix expression depends on the number of symbols for

111
00:06:34,210 --> 00:06:34,710
each symbol.

112
00:06:34,720 --> 00:06:35,430
We have a step.

113
00:06:35,860 --> 00:06:38,320
So the number of steps are equal to number of symbols.

114
00:06:38,650 --> 00:06:40,600
So the time is sort of even for.

115
00:06:41,350 --> 00:06:44,230
Also, the time was one interesting thing I will show you here.

116
00:06:44,560 --> 00:06:46,750
This is a infix expression.

117
00:06:46,760 --> 00:06:49,770
Suppose this is what I have written in my program.

118
00:06:50,260 --> 00:06:52,450
There are going to have to be stored in some variable.

119
00:06:52,480 --> 00:06:58,690
So if I write this one, then can you tell me which operation will execute the first?

120
00:06:59,080 --> 00:07:00,000
Listen carefully.

121
00:07:00,700 --> 00:07:04,540
Can you tell me which operator will execute first?

122
00:07:04,960 --> 00:07:08,050
So yes, your answer is multiplication.

123
00:07:09,660 --> 00:07:12,180
First, multiplication will execute.

124
00:07:13,310 --> 00:07:16,160
But let us see which one will execute.

125
00:07:16,210 --> 00:07:20,740
First, we know very well that compilers will not evaluate infix expression.

126
00:07:21,080 --> 00:07:24,960
They will convert infix expression, first of all, into postfix expression.

127
00:07:25,400 --> 00:07:28,360
So in one scan, it is converted into postfix.

128
00:07:28,640 --> 00:07:30,730
So let me try postfix form of that one.

129
00:07:31,070 --> 00:07:33,230
First of this multiplication gets converted.

130
00:07:33,260 --> 00:07:37,840
So this is a three four star, then this plus is converted.

131
00:07:37,850 --> 00:07:44,300
So six of five plus the displays will convert plus comes there if you want to include assignment, also

132
00:07:44,300 --> 00:07:45,640
the next assignment.

133
00:07:46,580 --> 00:07:48,860
So this is a complete four six form of expression.

134
00:07:49,160 --> 00:07:52,430
Then we know how the evaluation is done.

135
00:07:52,430 --> 00:07:53,900
Just what we have seen the procedure.

136
00:07:55,270 --> 00:07:56,890
We know how evaluation is done.

137
00:07:57,190 --> 00:07:59,750
Let us take a step back and do evaluation.

138
00:08:00,040 --> 00:08:01,140
So take a step back.

139
00:08:02,170 --> 00:08:09,430
So the first team goes into the strongest X, the Nexus six, the Nexus five, the Nexus and operator,

140
00:08:09,440 --> 00:08:14,980
so purported to operate five is popped out, then six is popped out.

141
00:08:15,310 --> 00:08:18,850
And the result is 11, 11 is pushed into the stack.

142
00:08:20,470 --> 00:08:22,270
Which one was executed first?

143
00:08:24,200 --> 00:08:30,740
This one, this one, so this one got executed first we were believing that this gets executed first.

144
00:08:31,190 --> 00:08:32,150
No wrong.

145
00:08:32,330 --> 00:08:40,340
So that's what I was telling you from the beginning, that precedences and associativity are meant for

146
00:08:40,350 --> 00:08:45,870
parenthesized and they don't decide who will execute Forstchen.

147
00:08:45,890 --> 00:08:48,930
They will decide which operator should execute first.

148
00:08:49,250 --> 00:08:55,070
So you can see that after converting into postfix when the evaluation is done, first operation that

149
00:08:55,070 --> 00:09:00,260
is getting executed is plus not that multiplication for the presidency.

150
00:09:00,260 --> 00:09:04,070
Is that multiplied three and four?

151
00:09:05,240 --> 00:09:08,060
Don't forget to add all these to six of five three.

152
00:09:08,060 --> 00:09:09,200
Then multiply four.

153
00:09:09,380 --> 00:09:10,160
Don't do that.

154
00:09:10,460 --> 00:09:11,810
Multiply three and four.

155
00:09:11,810 --> 00:09:13,160
This is what precedences.

156
00:09:13,430 --> 00:09:15,770
So it shows who should be parenthesized.

157
00:09:15,860 --> 00:09:20,830
This is first parentheses, then the second parentheses, then this is the final one.

158
00:09:21,110 --> 00:09:23,240
Now you see everything is parentheses.

159
00:09:23,480 --> 00:09:26,190
Tell me which parenthesis you will evaluate first.

160
00:09:26,510 --> 00:09:28,000
This one we will evaluate first.

161
00:09:28,170 --> 00:09:28,790
That's all.

162
00:09:28,790 --> 00:09:30,290
That's what happened here.

163
00:09:31,130 --> 00:09:34,220
Plus one was executed, but it is not parenthesized.

164
00:09:34,220 --> 00:09:37,270
We believe that Ohio's procedures that will execute fust.

165
00:09:37,370 --> 00:09:43,580
No, not necessary nor necessary unless, you know, complete parenthesized expression.

166
00:09:43,580 --> 00:09:48,410
Then you see which bakdash to execute first that bracket will be executed or that operation will be

167
00:09:48,410 --> 00:09:48,910
executed.

168
00:09:49,820 --> 00:09:52,370
So this is one interesting fact that.

169
00:09:53,440 --> 00:09:59,030
Precedent's and associativity are meant for parenthesized and not for execution.

