1
00:00:00,390 --> 00:00:04,890
Let us look at recursive insert function for binary search tree.

2
00:00:05,840 --> 00:00:12,110
We can generate an DARBELNET street by inserting keys one by one so we can call this function every

3
00:00:12,110 --> 00:00:16,650
time for inserting a key as the function is recursive.

4
00:00:16,670 --> 00:00:20,810
So already I have written the function, I will trace the function and show you.

5
00:00:21,300 --> 00:00:22,810
But what's from main function?

6
00:00:23,000 --> 00:00:25,220
I have a pointer which is already null.

7
00:00:25,520 --> 00:00:34,930
The first key I will insert, I will call insert function by passing this through and keys 30.

8
00:00:36,380 --> 00:00:41,180
See, I'm quoting in search function for the first time by passing the route, which is not right now,

9
00:00:41,450 --> 00:00:43,370
and I'm passing 30.

10
00:00:43,760 --> 00:00:44,690
So what happens?

11
00:00:44,690 --> 00:00:45,990
How this works, let us see.

12
00:00:46,340 --> 00:00:47,930
So right now, raucus not.

13
00:00:49,100 --> 00:00:54,330
So when they call insert function by passing routes, so that route be here and it is right now none.

14
00:00:54,860 --> 00:00:55,940
So route is null.

15
00:00:56,570 --> 00:01:00,820
So what is the first statement of fees and then please, none right now.

16
00:01:01,130 --> 00:01:09,440
So create a new node, set the key in data and said left and right then and return the city that the

17
00:01:09,440 --> 00:01:09,980
new node.

18
00:01:10,340 --> 00:01:13,160
So create a new node with the help of a temporary pointer.

19
00:01:15,040 --> 00:01:17,470
Insert data today, that is key.

20
00:01:20,140 --> 00:01:28,650
Make this left and right, none, none, none then written to so this Nords Artosis return from there

21
00:01:28,870 --> 00:01:30,970
so that address I can take it in through.

22
00:01:33,380 --> 00:01:35,450
So Rob will be pointing Montagnard.

23
00:01:36,500 --> 00:01:37,620
So this is the first step.

24
00:01:38,510 --> 00:01:41,240
So what I'm creating first, no rules should point on that one.

25
00:01:41,240 --> 00:01:43,300
That is the reason I have a sign it with root.

26
00:01:43,310 --> 00:01:46,230
So Root was not openly pointing on that one.

27
00:01:46,280 --> 00:01:47,180
This is the first step.

28
00:01:47,570 --> 00:01:49,460
Now, let us insert a few more keys.

29
00:01:49,490 --> 00:01:57,590
I will say insert I will pass through disappointing there on that note, root and the value is 20.

30
00:01:59,260 --> 00:02:03,120
I don't have to assign it to anything, though, already I have ruled pointing on that.

31
00:02:03,530 --> 00:02:04,350
That is just normal.

32
00:02:05,050 --> 00:02:05,650
Let us see.

33
00:02:08,160 --> 00:02:10,860
He's up on Route Espy's upon route.

34
00:02:12,210 --> 00:02:17,890
Then please, no, I do not enter into this block then key that I have sinned.

35
00:02:18,190 --> 00:02:20,930
20 is it less than piece of data?

36
00:02:21,180 --> 00:02:22,810
Less than 30?

37
00:02:23,100 --> 00:02:25,210
Yes, it is less so.

38
00:02:25,210 --> 00:02:28,850
Go on with site, insert upon left child.

39
00:02:29,070 --> 00:02:31,250
So it will call itself again.

40
00:02:31,470 --> 00:02:35,060
So again, the search function is called bypassing piece left.

41
00:02:35,380 --> 00:02:36,490
So what is left.

42
00:02:36,560 --> 00:02:39,040
Chinelo be left child none.

43
00:02:39,330 --> 00:02:40,220
I just left.

44
00:02:41,280 --> 00:02:43,050
So this is not call itself.

45
00:02:44,820 --> 00:02:52,230
Right now, BS none, so create a new morgue or create a new law with the help of BP and insert Diski

46
00:02:52,440 --> 00:02:53,190
set designers.

47
00:02:53,470 --> 00:02:57,570
This is what the common work done every time when the point there is no create a..

48
00:02:57,810 --> 00:02:59,820
The data and initialize point to none.

49
00:03:00,090 --> 00:03:03,060
That's where the common steps are and redundant.

50
00:03:03,360 --> 00:03:05,190
So this city is written.

51
00:03:05,490 --> 00:03:07,590
New nodes are necessary written.

52
00:03:07,950 --> 00:03:11,010
So when it returns it goes back to the previous column.

53
00:03:11,220 --> 00:03:17,040
And in previous calls where P was B was here first call P was here.

54
00:03:17,250 --> 00:03:17,580
Right.

55
00:03:17,910 --> 00:03:20,940
So in that call, BS left was null.

56
00:03:21,090 --> 00:03:29,130
Now you can see that big gaps left side or PS PS Left Child is a sign with the result written by this

57
00:03:29,130 --> 00:03:30,050
insert function.

58
00:03:30,360 --> 00:03:32,980
So it will return didas of DNA.

59
00:03:33,000 --> 00:03:35,340
It will be assigned here and it will be pointing.

60
00:03:37,010 --> 00:03:42,440
So from this call, also, it will return, so it will return the same address, be from the snort,

61
00:03:42,770 --> 00:03:44,350
so it will go back to the previous call.

62
00:03:44,360 --> 00:03:45,530
So there was no previous call.

63
00:03:45,880 --> 00:03:47,030
This was the first call.

64
00:03:47,270 --> 00:03:50,870
So this is the only important statement that I have to explain you.

65
00:03:51,110 --> 00:03:53,870
That is a new note is created and assigned as a left.

66
00:03:54,800 --> 00:04:00,220
So when it returns, it will return the new normal sectors that will be assigned to the left side.

67
00:04:00,800 --> 00:04:09,940
I will insert one more key and you insert twenty five and the route this past, insert twenty five route

68
00:04:10,080 --> 00:04:10,730
this past.

69
00:04:11,120 --> 00:04:12,680
So this becomes route.

70
00:04:12,770 --> 00:04:14,540
So P will be pointing on this one.

71
00:04:15,260 --> 00:04:16,310
Is it being done.

72
00:04:16,670 --> 00:04:17,720
B's not null.

73
00:04:18,110 --> 00:04:21,320
It will check key the value that I'm inserting.

74
00:04:21,589 --> 00:04:21,890
Five.

75
00:04:22,310 --> 00:04:23,230
Is it less then.

76
00:04:23,230 --> 00:04:24,080
Piece of data.

77
00:04:24,530 --> 00:04:25,040
Less.

78
00:04:25,190 --> 00:04:29,350
Yes less so call insert again on left chain.

79
00:04:29,660 --> 00:04:37,550
So a new fresh call will be made up on the left and also P-value pointing here again B is not null.

80
00:04:37,760 --> 00:04:40,100
So again, call PS null.

81
00:04:40,250 --> 00:04:42,880
No it's not null then here key.

82
00:04:43,040 --> 00:04:45,380
Is it less then P data twenty five.

83
00:04:45,380 --> 00:04:46,580
Is it less than twenty.

84
00:04:46,850 --> 00:04:51,080
No key is greater than peritta twenty five.

85
00:04:51,320 --> 00:04:53,140
Is it greater than twenty.

86
00:04:53,180 --> 00:04:53,690
Yes.

87
00:04:54,380 --> 00:04:57,260
So call itself again upon the right chain.

88
00:04:57,710 --> 00:05:01,030
So people call it self again upon great chain.

89
00:05:01,370 --> 00:05:02,870
So Rachel was knows right.

90
00:05:02,870 --> 00:05:03,560
Not BS.

91
00:05:03,560 --> 00:05:03,860
No.

92
00:05:08,360 --> 00:05:15,680
So in this Colbys now, so a new node will be created initialise and it addresses written.

93
00:05:16,710 --> 00:05:25,830
Our new law created initialise with the key that we have given these Army national addresses written.

94
00:05:25,830 --> 00:05:28,450
So from when it has came, it has came from the.

95
00:05:29,160 --> 00:05:32,340
So people go back to that note in the previous column.

96
00:05:32,550 --> 00:05:33,940
So what was the statement?

97
00:05:34,260 --> 00:05:35,770
It's not completed right now.

98
00:05:35,820 --> 00:05:37,110
This assignment has to be done.

99
00:05:37,410 --> 00:05:41,160
So please write change will be assigned with that return address.

100
00:05:41,490 --> 00:05:44,920
So right chain will point on this one.

101
00:05:46,410 --> 00:05:47,700
So this call has finished.

102
00:05:47,700 --> 00:05:50,430
So it went back and assigned this link.

103
00:05:51,780 --> 00:05:58,080
Now, this call also finishes, so they don't be so simply it goes back to the previous call right from

104
00:05:58,080 --> 00:06:00,050
here, it has came to the left side.

105
00:06:00,060 --> 00:06:04,910
So it will try to assign the same letters again here and it will go to the previous node.

106
00:06:05,280 --> 00:06:07,990
So this seems like this is the sign here.

107
00:06:08,220 --> 00:06:10,680
So this and also service will also be a sign here.

108
00:06:10,860 --> 00:06:13,350
So it's the same address, only it makes no difference.

109
00:06:13,410 --> 00:06:18,540
So reassignment of the same address will be done then from the school also it finishes.

110
00:06:18,750 --> 00:06:21,580
So there is that because that was the first call.

111
00:06:22,470 --> 00:06:24,390
So this is how the keys will be inserted.

112
00:06:24,390 --> 00:06:30,200
I can go on writing insert function for inserting various keys in binary search tree.

113
00:06:30,600 --> 00:06:33,480
So that's all this is a recursive function.

114
00:06:34,090 --> 00:06:36,120
So I'll show you the demo of dysfunction.

115
00:06:36,120 --> 00:06:39,330
I've been included in the program and show you a demo of this function.

