1
00:00:00,450 --> 00:00:07,890
The topic is representation of binary tree, there are two representation, one is a representation

2
00:00:07,890 --> 00:00:10,980
and second one is linked to representation.

3
00:00:11,100 --> 00:00:13,240
Let us start with representation.

4
00:00:13,260 --> 00:00:16,420
Let us look at a representation of a binary tree.

5
00:00:17,340 --> 00:00:18,270
I have an example.

6
00:00:18,270 --> 00:00:19,260
Binary tree here.

7
00:00:19,260 --> 00:00:22,600
Labels are alphabet, so that's easy for you to understand.

8
00:00:23,010 --> 00:00:24,240
Also, I have an update.

9
00:00:24,810 --> 00:00:31,620
And one thing is our index is starting from an onwards, but the C C++ arena is a start from zero onwards

10
00:00:31,660 --> 00:00:33,350
for storing in C C++.

11
00:00:33,360 --> 00:00:35,580
We can ignore the index if at all.

12
00:00:35,580 --> 00:00:36,870
You want to use that also.

13
00:00:36,870 --> 00:00:37,940
We will see it afterwards.

14
00:00:37,960 --> 00:00:44,610
So right now our index of stock from one onwards, not how to store this binary entry for storing a

15
00:00:44,610 --> 00:00:45,290
binary tree.

16
00:00:45,660 --> 00:00:47,580
I have to store two things.

17
00:00:47,700 --> 00:00:56,610
First one is all the elements that is a 2G and the relationship between those elements, like A, the

18
00:00:56,610 --> 00:01:01,050
parent of B and C or B, the left of C, the right side of it.

19
00:01:01,080 --> 00:01:03,430
Likewise, B and C are also having the children.

20
00:01:03,780 --> 00:01:09,870
So if we are maintaining parent and child relationship, then we can produce a tree back again.

21
00:01:10,300 --> 00:01:12,850
So just storing the elements will not be sufficient.

22
00:01:13,260 --> 00:01:15,770
We should also preserve the relationship.

23
00:01:16,410 --> 00:01:18,660
How I can do this elements.

24
00:01:18,660 --> 00:01:20,560
I can store them in these spaces.

25
00:01:20,670 --> 00:01:23,720
Yes, it's OK, but how come into the relationship.

26
00:01:24,210 --> 00:01:25,840
Maybe this is necessary.

27
00:01:25,850 --> 00:01:26,460
May help me.

28
00:01:26,700 --> 00:01:30,780
The index of each location can help me to preserve the relationship.

29
00:01:31,560 --> 00:01:33,030
Yes, I will use that.

30
00:01:33,540 --> 00:01:35,100
How I will show you.

31
00:01:35,130 --> 00:01:37,320
So first of all, I will fill the elements.

32
00:01:37,560 --> 00:01:40,110
Then I will show you how they are maintaining the relationship.

33
00:01:40,110 --> 00:01:42,360
I will store these elements level by level.

34
00:01:42,490 --> 00:01:50,070
So the first level element is e then this level, second level B and C, so given store B and C, the

35
00:01:50,080 --> 00:01:57,240
third level BFG, so be the F G, I have stored all the elements.

36
00:01:58,170 --> 00:02:02,270
Now I have to see whether the relationships are preserved or not.

37
00:02:02,760 --> 00:02:04,830
Let us observe the indices here.

38
00:02:04,830 --> 00:02:06,690
I will prepare the table of indices.

39
00:02:06,690 --> 00:02:08,340
With that you can understand.

40
00:02:08,699 --> 00:02:13,380
See the first element is a a what is the index of this element.

41
00:02:13,380 --> 00:02:16,050
One then who is the left shell of A.

42
00:02:16,050 --> 00:02:18,780
B there is B attached to it.

43
00:02:18,780 --> 00:02:21,780
Is that two then who is the right shell of a C.

44
00:02:22,050 --> 00:02:23,850
So what is the index of C three.

45
00:02:24,060 --> 00:02:31,680
So right Chetniks is three the next element B what is the index of B two.

46
00:02:32,520 --> 00:02:43,920
Left and right there is a D four and right is that five then the district C C it is an index of three

47
00:02:43,920 --> 00:02:44,360
here.

48
00:02:44,970 --> 00:02:50,340
The index of this element then who is the left shell of C F Verity's six.

49
00:02:51,270 --> 00:02:54,570
Then who is the right shell of C G where it is at seven.

50
00:02:55,560 --> 00:03:01,550
So I think this is sufficient to understand how the relationships are maintained with the help of indices

51
00:03:02,040 --> 00:03:07,410
C element is that index one, the left is a two and I challenge that three.

52
00:03:08,070 --> 00:03:13,020
If the element is that index are to the left child, is that a four and five.

53
00:03:13,920 --> 00:03:15,390
So two into two.

54
00:03:15,870 --> 00:03:16,230
Right.

55
00:03:16,230 --> 00:03:23,370
Four so two two two plus one, five, two, three, six, two three plus one seven four.

56
00:03:23,370 --> 00:03:23,640
Yes.

57
00:03:23,640 --> 00:03:29,220
This is the formula maintain if any element is that index i.e. then is left child.

58
00:03:29,220 --> 00:03:32,160
Is that index a two way and right child.

59
00:03:32,160 --> 00:03:34,770
It is that index to a plus one.

60
00:03:35,370 --> 00:03:38,670
So elements are stored based on this formula.

61
00:03:39,030 --> 00:03:39,360
Right.

62
00:03:39,690 --> 00:03:46,530
If any element is at index I then left child.

63
00:03:47,130 --> 00:03:51,030
Is that index to I then right child.

64
00:03:52,110 --> 00:03:54,140
Is that index to Y plus one.

65
00:03:54,600 --> 00:03:56,250
So these are the formulas use.

66
00:03:57,670 --> 00:03:58,540
No one mooting.

67
00:03:59,520 --> 00:04:06,450
If I take any element, then where is its parent, like, for example, who is the parent of F, C,

68
00:04:06,840 --> 00:04:08,830
so F is that six C's?

69
00:04:08,830 --> 00:04:09,350
That three?

70
00:04:10,560 --> 00:04:17,160
So when this left shoulder, we are getting it by multiplying by two, so we will get it by dividing

71
00:04:17,160 --> 00:04:17,740
it by two.

72
00:04:17,940 --> 00:04:23,040
So the parent of any node will be at eye by two.

73
00:04:23,460 --> 00:04:26,770
When you divide any number by two, you may get a similar cell.

74
00:04:26,790 --> 00:04:27,960
So shall we take it or not?

75
00:04:27,960 --> 00:04:28,560
Let us check.

76
00:04:29,250 --> 00:04:36,310
G is the child of C G that seven, which is its parent at the three.

77
00:04:36,690 --> 00:04:40,190
So this is seven by two is the three point five.

78
00:04:40,380 --> 00:04:41,970
So you should take the floor value.

79
00:04:41,970 --> 00:04:42,640
That is three.

80
00:04:42,820 --> 00:04:47,010
So yes, this is the floor value for any node.

81
00:04:47,670 --> 00:04:53,290
These formulas will help us to obtain its left child by child or parent.

82
00:04:53,760 --> 00:04:58,460
Now, when I was filling these elements, I did not follow this formula.

83
00:04:58,860 --> 00:05:00,480
I just filled them level by level.

84
00:05:01,140 --> 00:05:03,990
And automatically those formulas are applied.

85
00:05:05,050 --> 00:05:12,310
Yes, so fulfilling it, either you follow the formula or fill them level by level, automatically,

86
00:05:12,310 --> 00:05:14,420
the formula will be followed, but that's it.

87
00:05:14,740 --> 00:05:16,770
This is the representation of finality.

88
00:05:17,140 --> 00:05:19,300
And I will take a few more special cases.

