1
00:00:00,950 --> 00:00:07,590
Let us see how to install XCode on Mac OSX operating system.

2
00:00:07,700 --> 00:00:11,340
See, go to App Store, and inside App Store,

3
00:00:11,360 --> 00:00:17,760
Search for XCode, already I have XCode installed on this machine.

4
00:00:17,760 --> 00:00:25,330
See, here is XCode, option is available for updating it, so if I have to update I can update it.

5
00:00:25,340 --> 00:00:31,350
If it is not installed then this button will be for installation of XCode. So, you can click on this one,

6
00:00:31,350 --> 00:00:38,480
and install XCode on your machine. And the other method of installing XCode is, go to terminal, and at

7
00:00:38,570 --> 00:00:43,020
command line, say XCode-select-install

8
00:00:45,730 --> 00:00:47,160
xcode-select-install

9
00:00:47,290 --> 00:00:51,060
See here the message I'm getting is, command line tools are already installed,

10
00:00:51,400 --> 00:00:55,920
use "Software Update" to install updates. So, already XCode is available.

11
00:00:56,110 --> 00:01:03,520
If it is not available, then it will install it. Once it is installed you can start using XCode.

12
00:01:03,850 --> 00:01:07,700
So first of all I will show you how to create a new project for C language.

13
00:01:07,750 --> 00:01:11,170
Then later I will show you how to create a project for C++.

14
00:01:11,170 --> 00:01:15,900
The method is similar just we have to select the language at the time of creating a project.

15
00:01:15,940 --> 00:01:22,830
So let us create a new project. So, click on the file menu and say new, and inside that new option,

16
00:01:22,840 --> 00:01:30,160
There is an option available called Project. So, say new project, and here it opens and it will ask which

17
00:01:30,160 --> 00:01:31,300
type of project you want.

18
00:01:31,300 --> 00:01:32,050
So, here we are

19
00:01:32,050 --> 00:01:38,260
writing command line application. So, select command line tool. Then it will ask for the project name, so

20
00:01:38,260 --> 00:01:43,450
you can mention any project name, like I will say first project is, MyFirst, I will call it as MyFirst,

21
00:01:43,840 --> 00:01:46,970
then I will select the language as C language.

22
00:01:47,020 --> 00:01:51,820
Here you can see in the drop-down, there are more than 1 languages available, so we can select C as well

23
00:01:51,820 --> 00:01:53,420
as C++ also.

24
00:01:53,590 --> 00:01:58,140
So I'll be using the same IDE for writing the programs using C language as well as C++.

25
00:01:58,540 --> 00:02:02,050
So let us select C language and say next.

26
00:02:02,050 --> 00:02:07,540
Now here, it will ask the destination folder, where you have to create a project. So, you can select any folder

27
00:02:07,570 --> 00:02:09,910
where you want your projects to be stored.

28
00:02:10,300 --> 00:02:14,660
So say Create now. New project is created.

29
00:02:15,550 --> 00:02:18,760
So these are the general properties of the projects on the left hand side

30
00:02:18,770 --> 00:02:25,600
bar. Here is the project browser, that is File Explorer, so you can click on the file that is main.c

31
00:02:26,690 --> 00:02:27,130
.

32
00:02:27,130 --> 00:02:32,740
Here is the ready made source code given for writing the basic program and also comments are there.

33
00:02:32,740 --> 00:02:38,200
So if you don't want, you can remove these comments, and inside the main function also, some comments are

34
00:02:38,220 --> 00:02:39,480
there, I'll remove them.

35
00:02:39,890 --> 00:02:40,340
Yeah.

36
00:02:40,360 --> 00:02:44,270
Here is a perfect C language main function.

37
00:02:44,270 --> 00:02:46,150
Now by default some thing is written here.

38
00:02:46,150 --> 00:02:47,470
That is print Hello World.

39
00:02:47,800 --> 00:02:55,370
So if I run the program, it will print Hello World .So, let us run the program and see. And here's the

40
00:02:55,370 --> 00:02:56,110
output window.

41
00:02:56,130 --> 00:03:01,670
Hello World is displayed, and later on, the program ends with exit code 0. So the program has terminated.

42
00:03:02,150 --> 00:03:05,720
Now these, on left hand side, right hand side, we have the bars.

43
00:03:05,720 --> 00:03:11,210
So if you want to remove the bars, you can go on the right top corner of the screen and you can hide

44
00:03:11,210 --> 00:03:17,870
these panels. So, you can hide these panels and even you can hide this output bar, again you can open it

45
00:03:17,870 --> 00:03:20,440
up, you can pop it up, or you can hide it.

46
00:03:20,600 --> 00:03:27,860
So this is how we can start a new project and we can develop the project.

47
00:03:27,860 --> 00:03:35,510
Now, let us go to the project panel. Now inside this, if you want to add new files, like if you have

48
00:03:35,510 --> 00:03:41,480
to include any header files, you can include them here. You can just say, right click and in the pop up, you

49
00:03:41,490 --> 00:03:47,980
can say new file and you can select the file type that you want to include in this project, so sometime

50
00:03:47,990 --> 00:03:54,320
we may be using header files, so we can pop up this header file. And the benefit of using XCode is

51
00:03:54,320 --> 00:04:00,380
that we can easily debug the program and see it's working line by line. So, I can add a break-point here.

52
00:04:00,710 --> 00:04:05,100
So this is a break-point, and if I run the program, it will come down to this line and stop.

53
00:04:05,100 --> 00:04:07,670
So up to here it will run and it will stop.

54
00:04:10,400 --> 00:04:10,990
Yeah.

55
00:04:11,070 --> 00:04:16,860
Here it is a debug area. You can see the contents of the variables that are used inside the function

56
00:04:16,860 --> 00:04:17,870
or inside the program.

57
00:04:18,450 --> 00:04:22,370
So it's easy for me to explain the working of a program.

58
00:04:22,380 --> 00:04:24,030
So for my explanation purpose,

59
00:04:24,150 --> 00:04:25,670
I'm using XCode.

60
00:04:25,920 --> 00:04:29,540
You can use any IDE for developing your applications.

61
00:04:30,120 --> 00:04:31,130
So through out this course,

62
00:04:31,170 --> 00:04:32,870
I'll be using XCode.

63
00:04:32,880 --> 00:04:36,530
The reason I have told you. So, I can explain you the things more clearly.

64
00:04:37,650 --> 00:04:38,570
That's it.

65
00:04:38,630 --> 00:04:44,740
Next, I will show you, how to create a C++ project. Create new XCode project,

66
00:04:47,610 --> 00:04:49,130
and let it be,

67
00:04:49,140 --> 00:04:51,290
Command Line Tool.

68
00:04:51,440 --> 00:04:54,770
Next. And here, select the language as C++.

69
00:04:54,780 --> 00:05:01,150
There are other languages also available. So, I'll give the project name as MyFirst.

70
00:05:01,190 --> 00:05:07,620
And it is asking for the destination that this project should be stored. So, select the destination folder.

71
00:05:08,550 --> 00:05:15,960
Now the project is created, and this is the program file. Here, already Hello World program is created. So

72
00:05:15,960 --> 00:05:19,760
you can remove the comments, and here you can start writing your code.

73
00:05:20,890 --> 00:05:22,870
So let us run this one and check.

74
00:05:25,670 --> 00:05:26,990
Here in this window,

75
00:05:27,030 --> 00:05:28,400
The output is displayed.

76
00:05:28,670 --> 00:05:30,950
So if you want you can remove these panels,

77
00:05:33,830 --> 00:05:39,120
and here is the output. So, you can extend the size of this one.

78
00:05:39,380 --> 00:05:43,110
So this the watch window where you can see the contents of the variables.

79
00:05:43,130 --> 00:05:48,860
If you have any variables in your program and this is the output window. So, you can show and hide this

80
00:05:48,950 --> 00:05:51,390
output window from this toolbar. That's it.

