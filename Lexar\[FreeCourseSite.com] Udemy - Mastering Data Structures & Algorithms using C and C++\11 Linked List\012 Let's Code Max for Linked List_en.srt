1
00:00:00,150 --> 00:00:04,920
In this video, we will see function for finding the maximum element from Lincolnesque.

2
00:00:04,980 --> 00:00:09,960
So already we have a project on the same project continuing.

3
00:00:10,350 --> 00:00:14,790
And here I will write on a function for finding the maximum element.

4
00:00:15,240 --> 00:00:20,280
So as we have discussed on board, I will write on it redivision as well as recursive function.

5
00:00:20,940 --> 00:00:27,740
So Faustus iterative function, let us call it as Max, which takes a pointer to first node.

6
00:00:27,750 --> 00:00:29,350
Let the pointer name BP.

7
00:00:29,370 --> 00:00:33,960
So here I will take a variable for storing maximum numbers for the variable limits.

8
00:00:33,960 --> 00:00:40,860
Max and I should initialize it with some minimum integer and other integers, 32 bit integer so min

9
00:00:40,860 --> 00:00:42,690
into 32 min.

10
00:00:42,840 --> 00:00:46,560
So there is a built-In constant available in this compiler 4c language.

11
00:00:46,980 --> 00:00:48,210
So I'm using that one.

12
00:00:48,460 --> 00:00:49,850
Depends on your compiler.

13
00:00:49,860 --> 00:00:54,240
You can take the value, otherwise you can set a minimum value like minus two, seven, six, seven

14
00:00:54,630 --> 00:00:56,660
or minus six five five three five.

15
00:00:56,700 --> 00:01:04,319
The next is while these not equal to another, we should continue every time check if each data is greater

16
00:01:04,319 --> 00:01:05,450
than Max.

17
00:01:06,030 --> 00:01:09,800
If so, we will modify Max with each data.

18
00:01:09,810 --> 00:01:15,000
So we'll store that data and max variable and the P should move to the next element.

19
00:01:15,000 --> 00:01:16,680
So be assigned piece next.

20
00:01:16,770 --> 00:01:17,280
That's all.

21
00:01:17,280 --> 00:01:23,470
By the end of this loop will be getting a maximum element in the variable Max or LEVEL-HEADED done Max.

22
00:01:23,490 --> 00:01:31,890
So this function I will call inside the main function and The Apprentice to Max as percentile dx and

23
00:01:31,890 --> 00:01:37,190
also again and here I will call a function Max by passing pointer first.

24
00:01:37,200 --> 00:01:39,870
That is a pointer to a first node of a long list.

25
00:01:39,900 --> 00:01:44,490
So already we have created this Lincolnesque with eight elements on the same side that will find the

26
00:01:44,490 --> 00:01:45,340
maximum number.

27
00:01:45,390 --> 00:01:47,670
Here we have maximum number is 20.

28
00:01:47,710 --> 00:01:49,040
I try to change this one.

29
00:01:49,050 --> 00:01:49,980
I'll make it as two.

30
00:01:49,980 --> 00:01:52,110
So now the largest number we have is 15.

31
00:01:52,120 --> 00:01:53,640
So we should get down to 15.

32
00:01:53,670 --> 00:01:54,290
Let us run.

33
00:01:54,300 --> 00:01:55,980
Yes, we got the number 15.

34
00:01:56,140 --> 00:02:00,890
If I change it to 25 and run it again, if the maximum number is 25.

35
00:02:00,940 --> 00:02:06,600
Now, similarly, you can implement a function for finding minimum by yourself that the student exercise

36
00:02:06,630 --> 00:02:07,060
nudniks.

37
00:02:07,080 --> 00:02:14,460
I will write on a recursive version for finding maximum element, so I will palletize amax it will take

38
00:02:14,460 --> 00:02:21,140
a structure node pointer B and here I should have some variable of type integer which is initialized

39
00:02:21,140 --> 00:02:21,920
with the zero.

40
00:02:21,930 --> 00:02:26,310
And as for the code we have seen on board, so the same thing I'm writing.

41
00:02:26,310 --> 00:02:28,100
If B is that is a zero.

42
00:02:28,110 --> 00:02:31,700
I have written written in 32 min min.

43
00:02:31,710 --> 00:02:39,570
Otherwise we will call a function amax by passing these next and whatever the value we receive we compare

44
00:02:39,570 --> 00:02:40,590
and return the value.

45
00:02:40,590 --> 00:02:47,330
If X is greater than B's data and return X otherwise written B's data.

46
00:02:47,490 --> 00:02:49,820
Nachshon, this is the functionality we have seen.

47
00:02:50,190 --> 00:02:51,810
I'll just give a line gap here.

48
00:02:52,170 --> 00:02:58,370
From here our function could start so that the speed zettl then return the minimum number otherwise

49
00:02:58,380 --> 00:03:05,190
called the function recursively and take resulting X and if X is greater than X otherwise written piece

50
00:03:05,190 --> 00:03:05,700
of data.

51
00:03:05,790 --> 00:03:11,070
So I will write to the next line now from here inside main function, I will call Amax function.

52
00:03:11,070 --> 00:03:14,070
Then I should get the same result that the twenty five letters run.

53
00:03:14,100 --> 00:03:15,210
Yes, we are divided.

54
00:03:15,210 --> 00:03:15,930
Twenty five.

55
00:03:15,960 --> 00:03:17,370
I'll try to modify this.

56
00:03:17,370 --> 00:03:20,350
This is twelve so I'll make it as thirty two different.

57
00:03:20,400 --> 00:03:21,990
I should get down to the thirty two.

58
00:03:22,020 --> 00:03:25,890
Yes I got down to a study so that's all in this video.

59
00:03:25,950 --> 00:03:31,410
We have written different functions for finding a maximum that is iterative version as well as recursive

60
00:03:31,410 --> 00:03:31,830
version.

