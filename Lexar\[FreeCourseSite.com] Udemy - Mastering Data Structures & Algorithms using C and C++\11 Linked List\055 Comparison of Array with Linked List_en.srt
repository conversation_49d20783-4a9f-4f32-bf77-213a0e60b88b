1
00:00:00,950 --> 00:00:08,210
And this video will compare a little checklist, basically, these are two physical structures and the

2
00:00:08,210 --> 00:00:12,390
beginning videos, I have explained that there are two physical data structures.

3
00:00:12,410 --> 00:00:15,070
We can use combination of these two data structures.

4
00:00:15,440 --> 00:00:19,640
So basically two physical data structures, Uhry and linkers.

5
00:00:20,300 --> 00:00:24,140
So using these data structure, we implement ideological data structures.

6
00:00:25,060 --> 00:00:27,190
So let us compare them first.

7
00:00:28,240 --> 00:00:35,110
There they are created in the main memory, or they can be created inside track as well as it can be

8
00:00:35,110 --> 00:00:37,830
created in hip Linklaters.

9
00:00:38,560 --> 00:00:40,230
It is always created in hip.

10
00:00:40,660 --> 00:00:43,360
There is no meaning of creating it inside track.

11
00:00:44,080 --> 00:00:46,090
Next, let us compare their sizes.

12
00:00:46,720 --> 00:00:48,010
This size is fixed.

13
00:00:48,490 --> 00:00:53,080
Once you created an array, you cannot increase or decrease the size of Tamani.

14
00:00:53,560 --> 00:00:55,150
You have to create another ready.

15
00:00:55,660 --> 00:00:58,140
Next, Linkous is in variable size.

16
00:00:58,150 --> 00:01:00,250
You can grow as much as you want.

17
00:01:00,610 --> 00:01:02,620
As much as he is available.

18
00:01:02,630 --> 00:01:04,870
It can grow when he is full.

19
00:01:04,870 --> 00:01:06,220
Then you cannot grow further.

20
00:01:07,610 --> 00:01:11,300
So there is this is fixed lamp and this is variable lamp.

21
00:01:12,770 --> 00:01:16,610
An advantage or disadvantage based on land.

22
00:01:17,930 --> 00:01:25,590
This is fixed size, so the chances of getting perfectly utilizers less either.

23
00:01:26,120 --> 00:01:32,370
It would be having some spaces left over or it may not be sufficient for storing all elements.

24
00:01:32,960 --> 00:01:39,800
So this should be used when you are sure that you will not get the number of elements more than this

25
00:01:39,800 --> 00:01:40,470
size.

26
00:01:40,490 --> 00:01:43,570
So if you know the maximum size, then create an area.

27
00:01:44,240 --> 00:01:50,120
It may be wasting space if you are not storing it fully, but linguist's when you don't know what is

28
00:01:50,120 --> 00:01:50,720
the land.

29
00:01:51,050 --> 00:01:55,470
You can go on creating nodes and go on adding it and also you can go on using it.

30
00:01:55,490 --> 00:02:01,010
So when you are not sure how many elements of the list size, what is the size, then you can move on

31
00:02:01,010 --> 00:02:01,480
interest.

32
00:02:03,530 --> 00:02:03,920
Then.

33
00:02:06,520 --> 00:02:16,940
About about space consumed, see, this will occupy space only equal to the size of the data support.

34
00:02:16,960 --> 00:02:17,890
These are integers.

35
00:02:18,370 --> 00:02:21,360
This is the space for an indigenous space for an integer.

36
00:02:22,090 --> 00:02:29,680
But here this is the space for an integer diso space for pointing to the next small space for integer

37
00:02:29,680 --> 00:02:30,760
pointed to an export.

38
00:02:31,270 --> 00:02:37,840
So instead, every node for every data, there is an extra space required for storing the link of next

39
00:02:37,840 --> 00:02:38,110
one.

40
00:02:38,650 --> 00:02:44,760
So linguistics, extra space, next accessibility, accessibility.

41
00:02:45,220 --> 00:02:47,800
This can be accessed randomly sample.

42
00:02:47,860 --> 00:02:52,030
They want to access something here, I can say of six and I can access it.

43
00:02:53,100 --> 00:03:01,350
I can say of three I can reach here, of nine I can reach here, but this can be accessed sequentially

44
00:03:01,590 --> 00:03:06,840
if I have to access something here that I should start from first hand, go on moving and reach here.

45
00:03:07,290 --> 00:03:13,430
If I have to access something here, then I should start from first hand, go on moving and reach next

46
00:03:13,710 --> 00:03:14,760
faster access.

47
00:03:15,120 --> 00:03:16,620
Who is a faster access?

48
00:03:17,100 --> 00:03:18,600
This is inside the strike.

49
00:03:18,600 --> 00:03:25,710
It is faster inside heap to access direct access was always inside heap.

50
00:03:26,040 --> 00:03:28,530
So there's always internet access.

51
00:03:30,260 --> 00:03:36,500
And also, this is random and this is sequential, we already discussed that nudniks insert and delete

52
00:03:36,500 --> 00:03:37,060
operations.

53
00:03:37,060 --> 00:03:40,160
So for that, I will fill up a few elements and then we will discuss about it.

54
00:03:41,140 --> 00:03:48,450
Let us talk about inside, if I'm inserting any element here, here at the site, so I should say something

55
00:03:48,460 --> 00:03:52,480
from the right hand side, so it takes Constantine just I have to write on the element.

56
00:03:53,550 --> 00:03:58,230
If I want to insert any element here, then I have to shift all elements, so it means let us say we

57
00:03:58,230 --> 00:04:01,930
are inserting from left hand side from the side.

58
00:04:02,190 --> 00:04:09,090
So for shifting elements, it takes time out of and so inserting that side as well and inserting the

59
00:04:09,110 --> 00:04:10,820
side as an act.

60
00:04:11,220 --> 00:04:14,600
So this is the minimum to maximum if you insert here.

61
00:04:15,060 --> 00:04:18,180
And so a little less than but still you say normally.

62
00:04:18,360 --> 00:04:18,660
Right.

63
00:04:19,079 --> 00:04:21,240
But here, always Hillman's always constant.

64
00:04:22,050 --> 00:04:22,890
Let us see his.

65
00:04:23,900 --> 00:04:28,690
Let us look at the links inside link, if I want to insert here, just create a..

66
00:04:28,850 --> 00:04:30,170
Link it and move fast.

67
00:04:30,440 --> 00:04:31,540
So this is constant.

68
00:04:31,880 --> 00:04:38,200
If I am sitting on the left hand side, if I want to insert something here, then I should move pointer

69
00:04:38,210 --> 00:04:39,970
B and bring it on some node.

70
00:04:39,970 --> 00:04:41,700
Then I can insert a new node.

71
00:04:42,260 --> 00:04:47,780
So if I'm inserting from that side, so let us see right hand side then the times order of N.

72
00:04:50,460 --> 00:04:56,520
Here it is one, there it is, and because I have to travel to this linguist's, then I can insert it

73
00:04:57,240 --> 00:05:02,310
here, I have to shift the elements, then I can insert an element so that side one this side, but

74
00:05:02,310 --> 00:05:03,740
here the side of that side.

75
00:05:03,790 --> 00:05:13,190
And so if I talk about who is which, even if I say insert operation is efficient on which data structure.

76
00:05:13,620 --> 00:05:13,980
So to.

77
00:05:15,700 --> 00:05:24,960
In our shifting of elements, but a linguistic movement of Poynton, so the time Decider's one there

78
00:05:24,980 --> 00:05:31,120
at the end there also we have one on that side and on this side note depends from which side you want

79
00:05:31,120 --> 00:05:32,260
to insert all these.

80
00:05:32,530 --> 00:05:36,760
If you are inserting always from the right hand side, then they will be faster.

81
00:05:37,030 --> 00:05:41,050
If you are inserting always on the left hand side, the Linklaters will be faster.

82
00:05:41,860 --> 00:05:43,750
But one more important criteria.

83
00:05:44,260 --> 00:05:46,840
See, there is more shifting of data involved here.

84
00:05:47,140 --> 00:05:50,640
Shifting of data is in one hand you have to shift the data.

85
00:05:50,980 --> 00:05:53,110
So my data is indigence to whites.

86
00:05:53,140 --> 00:05:59,700
If I'm assuming that integer sticking provides for two whites, if it is a double that eight bytes if

87
00:05:59,980 --> 00:06:03,690
for a student record of 200 more than 200 miles.

88
00:06:04,030 --> 00:06:05,170
So this is costly.

89
00:06:05,290 --> 00:06:07,210
Movement of data is costly here.

90
00:06:07,210 --> 00:06:08,500
There is no movement of data.

91
00:06:08,800 --> 00:06:14,470
Then when it comes to data movement, this is better than that one because of shifting of the data other

92
00:06:14,470 --> 00:06:18,830
times more data, and otherwise they are equally efficient.

93
00:06:18,850 --> 00:06:20,410
Only the sides are different.

94
00:06:21,710 --> 00:06:22,850
Now, let us delete.

95
00:06:24,430 --> 00:06:29,860
If I'm reading something from decidedly to only, the length of the list will be reduced, length of

96
00:06:29,860 --> 00:06:30,850
an array will be reduced.

97
00:06:30,910 --> 00:06:36,530
So it is fun if I am deleting eight dimensional shift all the elements of the site.

98
00:06:36,790 --> 00:06:40,240
So again, it is order of so deleting from the site is order.

99
00:06:40,810 --> 00:06:46,660
So insertion or deletion both sort of end time here and insertion deletion both takes all of our time

100
00:06:46,660 --> 00:06:46,960
here.

101
00:06:47,900 --> 00:06:52,920
Then come to the linguist's, deleting this Norder deleting first note Konstantine.

102
00:06:52,940 --> 00:06:54,750
So at the same time, I don't read it again.

103
00:06:55,550 --> 00:06:57,060
Then if I want to delete this one.

104
00:06:57,080 --> 00:07:02,450
So first of all, I should reach this node and I can delete this one and modify this link to another.

105
00:07:02,810 --> 00:07:04,850
So if I'm deleting this, then it sort of.

106
00:07:04,850 --> 00:07:11,090
And so it means if I'm deleting from that site, then sort of hidden from this site, I sort of what?

107
00:07:12,480 --> 00:07:15,390
So as far as I'm going, the time will be more.

108
00:07:16,610 --> 00:07:23,050
OK, here, as far as I'm going from in this direction, the time is more if I'm going that direction,

109
00:07:23,060 --> 00:07:27,890
time is less here, that direction is more this direction, it is becoming less.

110
00:07:28,860 --> 00:07:33,780
So only the direction is different, otherwise the time taken for deletion the same boat are equally

111
00:07:33,780 --> 00:07:34,320
efficient.

112
00:07:34,890 --> 00:07:40,940
If data is not an issue, if data is larger than here, shifting of elements will take a lot of time

113
00:07:41,280 --> 00:07:42,900
and there is no shifting in the world here.

114
00:07:42,930 --> 00:07:45,450
So this is better in terms of the government.

115
00:07:46,440 --> 00:07:54,300
The next searching see upon, we can perform linear search as well as a binary search.

116
00:07:55,350 --> 00:07:58,230
The stakes and and the stakes login.

117
00:08:00,250 --> 00:08:07,930
But at least we can perform just linear search, which takes order and we cannot perform binary search,

118
00:08:07,930 --> 00:08:11,420
if we perform, then it will be taking analog in time.

119
00:08:13,020 --> 00:08:20,790
So we say binary search will be inefficient upon linguist's reason, binary search elements must be

120
00:08:20,790 --> 00:08:22,860
sorted and we should go in the middle of a list.

121
00:08:22,890 --> 00:08:23,870
We know it, right?

122
00:08:24,190 --> 00:08:25,520
We should go in the middle of a list.

123
00:08:25,920 --> 00:08:30,630
So then you are going in the middle of a list here that you can go if I want to go into the middle of

124
00:08:30,630 --> 00:08:31,080
this list.

125
00:08:31,090 --> 00:08:33,360
So zero plus five by two.

126
00:08:33,360 --> 00:08:35,520
That is two point five floor values too.

127
00:08:35,520 --> 00:08:42,150
So I can go on to index to directly, but we are going in the middle means I should scan through all

128
00:08:42,150 --> 00:08:43,480
the notes and go in the middle.

129
00:08:44,100 --> 00:08:46,650
So this movement needs End-Time.

130
00:08:48,540 --> 00:08:53,820
Here it is faster because we can go in the middle, so always in the Middle East time.

131
00:08:54,180 --> 00:08:58,830
So here also we will go always in the middle log time, but to reach middle, we have to start from

132
00:08:58,830 --> 00:09:00,180
first and then.

133
00:09:00,930 --> 00:09:03,530
So it will not be long and it will be in Log-in.

134
00:09:03,540 --> 00:09:10,140
So we say binLaden surgical baphomet, but a binary search is famous for its login time, but it will

135
00:09:10,140 --> 00:09:11,020
do more than that.

136
00:09:11,160 --> 00:09:13,860
So we say we can't perform binary search.

137
00:09:14,610 --> 00:09:16,580
We did not learn anything about 40.

138
00:09:16,620 --> 00:09:22,770
But one thing I want to tell you, most of the sorting methods are designed for some of the sorting

139
00:09:22,770 --> 00:09:24,840
methods are designed for linguist.

140
00:09:25,250 --> 00:09:26,130
So Saaz.

141
00:09:28,090 --> 00:09:35,710
Insertion, art and Mozart, they are designed for linguist, they are more friendly to the linguists,

142
00:09:35,710 --> 00:09:37,030
they are suitable for linguists.

143
00:09:37,300 --> 00:09:43,180
So we have this topic we will learn about this one that time will explore new and and saw this friendly

144
00:09:43,180 --> 00:09:44,080
with the linguists.

145
00:09:44,440 --> 00:09:45,880
They are suitable for linguists.

146
00:09:45,880 --> 00:09:47,050
We will see that later.

147
00:09:48,310 --> 00:09:52,420
So that's all I have compared today versus linguists.

148
00:09:52,750 --> 00:09:54,070
Now, who is the winner here?

149
00:09:55,510 --> 00:09:56,880
Depends on the requirement.

150
00:09:57,820 --> 00:09:59,290
Depends on the requirement.

151
00:10:00,470 --> 00:10:00,910
Right.

152
00:10:01,920 --> 00:10:04,500
If a space is the issue, the specter.

153
00:10:06,120 --> 00:10:07,330
Faster access.

154
00:10:08,420 --> 00:10:09,180
This is better.

155
00:10:10,520 --> 00:10:14,090
Variable, and this is better fixed limit.

156
00:10:14,390 --> 00:10:15,280
This is better.

157
00:10:16,630 --> 00:10:18,430
Data is large, we don't want to move.

158
00:10:18,640 --> 00:10:19,120
This is big.

159
00:10:20,410 --> 00:10:22,450
We are always inserting from left site.

160
00:10:22,810 --> 00:10:25,300
This is better inserting all this from right.

161
00:10:25,810 --> 00:10:26,560
This is better.

162
00:10:27,550 --> 00:10:32,800
There are many things that decides, so you have to select the data structure carefully depending on

163
00:10:32,800 --> 00:10:33,670
your requirement.

164
00:10:34,990 --> 00:10:39,730
So study how you're going to use the data structure in your application.

165
00:10:40,120 --> 00:10:42,550
Depending on that, you select a suitable one.

166
00:10:42,670 --> 00:10:48,280
So I have compared them on various criteria that will help you to decide which data structure to use.

167
00:10:49,120 --> 00:10:53,410
So that's all about competition of ideas and legalist.

