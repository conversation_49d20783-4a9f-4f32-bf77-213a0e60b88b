1
00:00:00,420 --> 00:00:05,190
And this video, we will implement power function, we have seen two versions of power function, so

2
00:00:05,190 --> 00:00:06,450
I will show you the first one.

3
00:00:07,350 --> 00:00:10,620
Let us try to function, call by.

4
00:00:12,680 --> 00:00:23,810
Which takes parameters and and it will raise em to the powers of end if and is equal to zero, then

5
00:00:23,810 --> 00:00:33,400
return one otherwise written bar as as it is, but and will be reduced by value one.

6
00:00:33,980 --> 00:00:37,660
And it is multiplied by and this is what the function we have seen.

7
00:00:37,820 --> 00:00:43,190
So the same function I have written here now as usual, inside main function, I will declare a variable

8
00:00:43,190 --> 00:00:50,110
R and to this I will assign part of Tucumán nine.

9
00:00:51,890 --> 00:00:55,160
Then I will print the value of R.

10
00:00:59,750 --> 00:01:02,600
That program is really I will run the program.

11
00:01:08,020 --> 00:01:09,330
So there was a mistake here.

12
00:01:13,340 --> 00:01:18,710
So that's all a function is ready, and I'm calling that function, let us on the program and see the

13
00:01:18,720 --> 00:01:20,100
results up on its fight.

14
00:01:20,120 --> 00:01:21,560
Well, I should get the results right.

15
00:01:21,770 --> 00:01:23,000
Yes, it's perfect.

16
00:01:24,620 --> 00:01:27,200
I will change the values three.

17
00:01:27,240 --> 00:01:28,910
Part four is 81.

18
00:01:31,860 --> 00:01:34,260
Yes, we got 81, so it's looking perfect.

19
00:01:35,850 --> 00:01:40,410
I would like to know the function we have seen to function another function I have shown you would reduce

20
00:01:40,410 --> 00:01:41,870
the number of multiplications.

21
00:01:43,770 --> 00:01:49,320
I will call that function as pouvoir one, which takes M and.

22
00:01:49,320 --> 00:01:49,680
And.

23
00:01:54,600 --> 00:01:59,280
Here, if the value of Annetts zero, then return one.

24
00:02:02,120 --> 00:02:12,380
Then if the number is even that is unmourned, two is equal to zero, if it is even then written called

25
00:02:12,380 --> 00:02:19,850
Power-One by passing, by passing and multiplied by M and and by two.

26
00:02:22,950 --> 00:02:33,980
Else the number is odd, so written and multiplied by four, one of em into a coma.

27
00:02:36,430 --> 00:02:43,870
And minus one by two, that's all this is the function we have seen, this will take less number of

28
00:02:43,870 --> 00:02:45,830
multiplications to find the result.

29
00:02:45,850 --> 00:02:52,030
So I will just change the function name to one and let us run the program and see it should give me

30
00:02:52,030 --> 00:02:52,530
eighty one.

31
00:02:54,010 --> 00:02:55,360
Yes, it's perfect.

32
00:02:55,840 --> 00:02:58,770
Now, if I pass to bar nine, then what's the result.

33
00:03:01,430 --> 00:03:07,970
It's fight vital, it's perfect if I saved Dupa eight here, I have seen the value, let us run.

34
00:03:09,970 --> 00:03:17,470
256, let me try one more value, nine for three.

35
00:03:19,890 --> 00:03:24,460
Yes, it is 739, so that's all power function.

36
00:03:24,480 --> 00:03:28,650
You have seen both the versions so you can try this program, you can write the program.

