1
00:00:00,390 --> 00:00:05,850
Now, the third method for finding traversal without doing any extra work directly, you can tell the

2
00:00:05,850 --> 00:00:06,380
answer.

3
00:00:06,689 --> 00:00:11,790
So the easiest method I'm showing you and you can try this method on all these trees, I'll be shooting

4
00:00:11,790 --> 00:00:13,650
it on one or two examples.

5
00:00:13,650 --> 00:00:17,030
So does the regular tree that I'm picking and every example.

6
00:00:17,040 --> 00:00:19,310
So I will find out on the street.

7
00:00:20,490 --> 00:00:29,550
So first preorder traversal Briona, so start from root and take your finger around the three.

8
00:00:30,800 --> 00:00:37,130
Right, move your finger around the tree, so first lord, that we came across as a C.

9
00:00:37,730 --> 00:00:41,610
This is completely visible for this finger finger directionlessness, right?

10
00:00:41,630 --> 00:00:42,860
So this is completely visible.

11
00:00:43,160 --> 00:00:55,370
Next one is B, bigger than did that get inside and now E we are able to point on e e then get inside

12
00:00:56,120 --> 00:00:56,660
C.

13
00:00:57,870 --> 00:00:58,740
The F.

14
00:01:00,500 --> 00:01:03,200
Then, gee, this is preorder.

15
00:01:05,180 --> 00:01:14,320
So move your finger like this in this direction, a, b, c, d, e, c, f, g, this is preorder.

16
00:01:15,140 --> 00:01:16,420
Then what about in order?

17
00:01:20,030 --> 00:01:21,090
Put your finger here.

18
00:01:21,800 --> 00:01:25,020
So actually, you have to take the bottom right or middle.

19
00:01:26,030 --> 00:01:29,120
So, no, there's nothing, no notice, but look and move around.

20
00:01:29,120 --> 00:01:29,720
Move around.

21
00:01:31,390 --> 00:01:35,020
Yeah, the complete naughties in the focus and get to the bottom of this.

22
00:01:35,530 --> 00:01:38,320
So this is A, B, then B.

23
00:01:40,620 --> 00:01:41,160
A.

24
00:01:44,060 --> 00:01:44,570
A.

25
00:01:47,660 --> 00:01:48,290
F.

26
00:01:50,160 --> 00:01:50,820
S..

27
00:01:52,640 --> 00:01:53,240
Jean.

28
00:01:55,100 --> 00:01:55,550
Over.

29
00:01:57,210 --> 00:01:58,380
So I did once again.

30
00:01:59,870 --> 00:02:08,570
B, b, e, a, f, c, g, then postcoital.

31
00:02:12,940 --> 00:02:19,090
From the site, put their finger like this, there is no order visible or can move along the boundary,

32
00:02:19,090 --> 00:02:20,090
move along the boundary.

33
00:02:20,200 --> 00:02:24,820
Yes, the first one we came across this D I will write them afterwards.

34
00:02:25,690 --> 00:02:27,940
Then came come along the boundary.

35
00:02:28,150 --> 00:02:36,460
Yes e d e then the B I'll stop here and right on BBR got D.

36
00:02:37,710 --> 00:02:41,280
E and B continue.

37
00:02:42,480 --> 00:02:43,960
Move along the boundaries.

38
00:02:44,880 --> 00:02:48,000
Yeah, the complete Lauder's in focus now f.

39
00:02:49,880 --> 00:02:50,510
Je.

40
00:02:51,950 --> 00:02:53,690
See a.

41
00:02:54,890 --> 00:02:55,520
F.

42
00:02:56,900 --> 00:03:05,010
G, c, e, there's the poster, so he got all the traversal, so it's easy just by looking at the tree,

43
00:03:05,030 --> 00:03:11,280
you can turn it on so you don't have to draw or mark anything on a tree right now.

44
00:03:11,300 --> 00:03:18,380
One important observation I will show you here, the first element of preorder and the last element

45
00:03:18,380 --> 00:03:23,470
of tostada will always be seen because first it was the truth.

46
00:03:23,510 --> 00:03:25,330
And here at last, we visit.

47
00:03:27,230 --> 00:03:34,370
Now, let me do it upon one of the three, I'll take this one and let us find preordering other Infostrada

48
00:03:34,380 --> 00:03:35,620
so I will not write on this.

49
00:03:35,630 --> 00:03:36,470
I will read out.

50
00:03:37,010 --> 00:03:43,520
Preorder A, B, C, D, e, f.

51
00:03:45,100 --> 00:03:45,990
Then in order.

52
00:03:47,540 --> 00:03:48,050
A.

53
00:03:49,080 --> 00:03:54,510
C, b, e, f, b.

54
00:03:56,080 --> 00:03:57,000
And then tostada.

55
00:03:59,400 --> 00:03:59,970
S..

56
00:04:02,640 --> 00:04:06,460
F e d be a.

57
00:04:08,470 --> 00:04:13,690
So this is all you can find accommodations for these also there's a student exercise, you can find

58
00:04:13,690 --> 00:04:17,810
all the taxes, so that's all with the traversal just using pen and paper.

59
00:04:17,829 --> 00:04:23,200
We have done it next be the program for displaying traversal of.

