1
00:00:01,030 --> 00:00:07,760
You have a look at that first traversal of a graph as a graph is a nonlinear data structure.

2
00:00:08,760 --> 00:00:14,850
There are two sadistic, powerful methods like brute force search and depth for search.

3
00:00:15,270 --> 00:00:20,220
These are similar to tree traversal methods, how they are similar to tree traversal methods.

4
00:00:20,250 --> 00:00:20,860
I'll show you.

5
00:00:21,270 --> 00:00:23,010
Let us start with brute force search.

6
00:00:23,940 --> 00:00:29,220
See, the breakfast search is similar to level order of a binary tree.

7
00:00:30,630 --> 00:00:33,680
But here we may not get a binary tree just like a tree.

8
00:00:33,920 --> 00:00:40,790
So whatever it is, but the first Sergius, same level order all the same level order.

9
00:00:40,800 --> 00:00:41,940
Let us understand this.

10
00:00:41,970 --> 00:00:43,820
I have an example graph taken here.

11
00:00:44,250 --> 00:00:47,520
This graph, I can convert it in the form of a tree.

12
00:00:48,540 --> 00:00:55,420
How see a graph without any cycle or without any closed edges is great.

13
00:00:56,310 --> 00:00:59,730
So let us prepare a tree for that graph so I'll show you here.

14
00:01:01,190 --> 00:01:06,140
The what is the SA one two seven, so I'll start from what one what this is what one?

15
00:01:07,350 --> 00:01:12,830
Now to a vortex one, the connected versus are two, three and four, so I'll take on.

16
00:01:13,200 --> 00:01:18,300
So Foster's two, then three, then four.

17
00:01:19,690 --> 00:01:26,440
So far, what one, two, three, four are completed now let us see who are the center two, who is

18
00:01:26,440 --> 00:01:27,900
a decent only three.

19
00:01:28,120 --> 00:01:31,000
So actually I'm preparing all three.

20
00:01:31,150 --> 00:01:35,900
So this, if I include it, will form a graph because it will form a cycle.

21
00:01:36,190 --> 00:01:39,680
So what I'll do is I'll put daughter to a disadvantage.

22
00:01:40,390 --> 00:01:44,350
So I have completed these three and just bust this one.

23
00:01:45,380 --> 00:01:49,700
Now, next, we have three also, so who is are just two or three from three?

24
00:01:49,700 --> 00:01:51,750
I can go on five as well as on four.

25
00:01:52,100 --> 00:01:54,440
So a new word, Axis five.

26
00:01:55,420 --> 00:02:04,380
I can go on for a while because footage there that next from forward where I can go one that is already

27
00:02:04,470 --> 00:02:06,020
what we already more within five.

28
00:02:06,370 --> 00:02:08,910
So five all the BS there.

29
00:02:08,919 --> 00:02:10,509
So I will drop down Redditch.

30
00:02:12,670 --> 00:02:21,970
The Nexus five from five, again, one six and seven, so I will draw here six and seven, therefore

31
00:02:22,300 --> 00:02:25,390
I have converted a graph into all three.

32
00:02:25,810 --> 00:02:29,800
I should not say binary tree because this node is having three children.

33
00:02:30,010 --> 00:02:33,480
So it's not a binary, just certainly not industry.

34
00:02:33,490 --> 00:02:40,630
If you observe I have all seven vertices, then if I count the edges, one, two, three, four, five,

35
00:02:40,960 --> 00:02:42,640
six, seven, eight, nine.

36
00:02:43,850 --> 00:02:47,160
One, two, three, four, five, six, seven, eight, nine.

37
00:02:47,480 --> 00:02:53,070
I have all the nine edges and I have dotted those are just which are trying to form a cycle.

38
00:02:54,170 --> 00:02:56,800
Now, if you hide those dots, there just is a tree.

39
00:02:57,620 --> 00:02:59,660
So I formed a tree photograph.

40
00:03:00,170 --> 00:03:02,490
Now take the level order.

41
00:03:03,790 --> 00:03:10,240
First of all, Texas one, the next level is two, three, four, two, three, four, the next level

42
00:03:10,240 --> 00:03:11,740
is five five.

43
00:03:11,980 --> 00:03:14,710
Next level is six and seven, six and seven.

44
00:03:15,320 --> 00:03:22,810
That's all this is a breakfast such that the result is this is the only result possible low when you

45
00:03:22,810 --> 00:03:28,130
perform breakfast so you can get a different result, whichever one you can take that one.

46
00:03:28,480 --> 00:03:30,700
So how different results are possible?

47
00:03:31,040 --> 00:03:31,690
Let's see.

48
00:03:31,930 --> 00:03:34,630
I will again draw three for this one.

49
00:03:34,630 --> 00:03:42,550
So I again draw from one now who are just adjacent to one are two, three and four.

50
00:03:43,450 --> 00:03:45,340
Do I have to take them in same order?

51
00:03:45,730 --> 00:03:49,480
No, you can take them in any order, so I will change the order now.

52
00:03:49,780 --> 00:03:54,550
So first I will take four, then two, then three.

53
00:03:55,870 --> 00:03:58,870
So the objective here is you must visit.

54
00:03:58,870 --> 00:04:03,190
All are just under the surface of what x1 vertex one is the starting vertex.

55
00:04:03,520 --> 00:04:07,870
So I have visited all of them, in which order it doesn't make any difference.

56
00:04:08,050 --> 00:04:11,890
So first time it was two, three, four nodes for two and three.

57
00:04:12,070 --> 00:04:14,020
So yes, this order is also correct.

58
00:04:14,680 --> 00:04:15,990
The holes are different.

59
00:04:16,000 --> 00:04:16,390
What is this.

60
00:04:16,390 --> 00:04:21,490
I should take next force who are adjacent to four, three and five.

61
00:04:21,760 --> 00:04:23,680
Three is already there in the three.

62
00:04:23,860 --> 00:04:26,680
So five I should take so makes this five.

63
00:04:27,960 --> 00:04:30,090
And for three, outdraw Gutteridge.

64
00:04:31,330 --> 00:04:35,850
The next suggestion I should take two so far, too, is just just just three.

65
00:04:36,070 --> 00:04:42,400
So I got an itch then Nexus three, who is are just one, two, three, four, which is already there

66
00:04:42,400 --> 00:04:43,620
and five is already there.

67
00:04:43,660 --> 00:04:48,240
I would rather an inch than next for five who are just six and seven.

68
00:04:48,520 --> 00:04:51,110
So do I have to take them in the same order.

69
00:04:51,130 --> 00:04:51,880
Six and seven.

70
00:04:51,880 --> 00:04:52,800
Oregon change order.

71
00:04:52,810 --> 00:04:53,830
You can change the order.

72
00:04:54,070 --> 00:04:57,370
So I will take the first seven, then six.

73
00:04:58,450 --> 00:05:04,300
Then from seven and six, there are no other charges that fall, then what is the level order of this

74
00:05:04,300 --> 00:05:04,560
one?

75
00:05:04,810 --> 00:05:13,720
So breakfast soldiers, first one, then four, two, three, four, two, three, then five five,

76
00:05:14,140 --> 00:05:15,280
then seven, six.

77
00:05:16,410 --> 00:05:22,230
The stimulus was such so difficult for such is also correct, so you have a photograph that can be very

78
00:05:22,590 --> 00:05:28,440
for such possible, like if you see here instead of four to three, I would have taken four, three,

79
00:05:28,440 --> 00:05:29,310
two also.

80
00:05:29,340 --> 00:05:30,810
Yes, I'll get a different result.

81
00:05:32,360 --> 00:05:36,900
There's a work next who is the starting works in both these trees?

82
00:05:37,100 --> 00:05:40,370
One, is it necessary that you start from Vortex one only?

83
00:05:40,520 --> 00:05:43,220
No, you can start from anywhere you like.

84
00:05:43,940 --> 00:05:45,460
Shall I start from what for?

85
00:05:45,620 --> 00:05:47,180
Yes, you can start from four.

86
00:05:47,390 --> 00:05:50,070
So I will point forward as the starting works.

87
00:05:50,420 --> 00:05:54,560
Who are the center for one, three and five in which order I can take.

88
00:05:54,740 --> 00:05:56,090
I can take them in any order.

89
00:05:56,300 --> 00:05:57,770
So first I will take five.

90
00:05:58,520 --> 00:05:59,870
Then one.

91
00:06:00,230 --> 00:06:01,490
Then three.

92
00:06:02,560 --> 00:06:09,100
Then who's are Justin, I should take five, four, five, three and six and seven are just for three

93
00:06:09,110 --> 00:06:11,800
years already there, so six and seven.

94
00:06:11,810 --> 00:06:13,590
So I would take four, seven and six.

95
00:06:13,600 --> 00:06:14,460
OK, no problem.

96
00:06:14,470 --> 00:06:16,350
I can take it then.

97
00:06:16,360 --> 00:06:16,990
Who's are Justin?

98
00:06:16,990 --> 00:06:18,160
I should take one.

99
00:06:18,400 --> 00:06:21,600
So for one who is a decent two and three so far.

100
00:06:21,610 --> 00:06:23,310
One, two.

101
00:06:23,320 --> 00:06:26,080
I will do it three or four that are decent.

102
00:06:26,830 --> 00:06:28,960
And for the next three who is a Justin.

103
00:06:28,960 --> 00:06:31,300
Four, three, two, five and four.

104
00:06:31,810 --> 00:06:34,480
So four is coming from two.

105
00:06:34,510 --> 00:06:35,590
I should do an edge.

106
00:06:36,460 --> 00:06:44,380
Right fight or that fall next I should go to a Justin versus seven and six so there are no Justin versus

107
00:06:44,380 --> 00:06:45,250
of seven and six.

108
00:06:45,760 --> 00:06:49,660
I got the three different starting what X is four.

109
00:06:49,930 --> 00:06:51,750
So what is the search for this one.

110
00:06:52,000 --> 00:06:53,800
Let us take a level order on this one.

111
00:06:53,800 --> 00:06:56,320
Fosters four then five one three.

112
00:06:59,070 --> 00:07:00,360
Then seven, six to.

113
00:07:04,430 --> 00:07:08,880
This is a level order, so these are the three examples I have shown, you know?

114
00:07:08,900 --> 00:07:13,850
Let us summarize and understand what it's about for such, I'll listen carefully.

115
00:07:14,240 --> 00:07:18,880
But it's sources that you can traversal from any starting what you want.

116
00:07:19,400 --> 00:07:21,450
It should not be on need not be worth X one.

117
00:07:21,470 --> 00:07:22,870
Only you can start from anywhere.

118
00:07:23,300 --> 00:07:24,220
That's the first point.

119
00:07:24,800 --> 00:07:26,570
So I've started for one also for all.

120
00:07:26,570 --> 00:07:29,060
So I can start from Cryos then.

121
00:07:29,060 --> 00:07:34,330
Second thing, when you have visited a vertex, you should explore that about Vertex completely.

122
00:07:34,340 --> 00:07:35,870
What does it mean by exploration?

123
00:07:36,080 --> 00:07:41,780
See, when we have selected for then we have visited all a just watterson's so visiting all the different

124
00:07:41,780 --> 00:07:42,100
worlds.

125
00:07:42,110 --> 00:07:44,420
This is exploration.

126
00:07:45,140 --> 00:07:46,820
So then why are you selecting Vertex?

127
00:07:47,060 --> 00:07:48,920
Explore all its adjustment.

128
00:07:48,920 --> 00:07:56,750
What is this then select the next vertex and explore all of that while exploring what should be the

129
00:07:56,750 --> 00:08:01,820
order of visiting the what you can visit them in any order, like for any vertex.

130
00:08:01,820 --> 00:08:03,470
There are more than one neighbouring whatsits.

131
00:08:03,470 --> 00:08:04,940
You can visit them in any order.

132
00:08:05,180 --> 00:08:07,280
So this is ideal for search.

133
00:08:07,520 --> 00:08:14,540
Now let us learn breakfast search freshly as it is found in their textbooks and everywhere how it is

134
00:08:14,540 --> 00:08:17,000
defined the same way I will define and explain.

135
00:08:17,000 --> 00:08:26,600
You see, Biddeford search can be defined by using two terms for customers visiting, visiting advertisements.

136
00:08:26,600 --> 00:08:27,270
What's going on?

137
00:08:27,270 --> 00:08:33,679
In particular, what does the first point and the second thing we should understand is exploring.

138
00:08:37,220 --> 00:08:39,330
Exploring means exploration of a vertex.

139
00:08:39,350 --> 00:08:46,700
If I say if I'm on vertex for exploding words, for visiting all are just some words for like one,

140
00:08:46,790 --> 00:08:49,070
three and five visiting.

141
00:08:49,310 --> 00:08:51,860
All are just different words of vertex for.

142
00:08:53,320 --> 00:08:56,620
So exploration means visiting neighbors and visiting.

143
00:08:56,850 --> 00:08:58,110
Just going on networks.

144
00:08:59,050 --> 00:09:02,630
So these are the two domes I'll be using for explaining first such.

145
00:09:03,430 --> 00:09:04,990
So let us start performing.

146
00:09:04,990 --> 00:09:10,450
But for search on this graph, first thing from which vortex you want to start, you can start from

147
00:09:10,450 --> 00:09:10,750
anywhere.

148
00:09:11,140 --> 00:09:13,180
I will start from what x1.

149
00:09:13,360 --> 00:09:14,160
What x1.

150
00:09:14,530 --> 00:09:16,260
So what makes one visited.

151
00:09:16,480 --> 00:09:18,750
So here I will write on visited.

152
00:09:18,760 --> 00:09:19,480
What is this.

153
00:09:19,510 --> 00:09:20,220
This is one.

154
00:09:20,560 --> 00:09:23,140
And this was the this in which order they are visited.

155
00:09:23,200 --> 00:09:24,520
That gives birth for such.

156
00:09:25,330 --> 00:09:26,680
Then I need one more thing.

157
00:09:26,710 --> 00:09:32,410
I need Kulunga structure so I will take a cue and I will drop that one in the queue.

158
00:09:33,550 --> 00:09:34,990
This is the initial point.

159
00:09:35,260 --> 00:09:41,470
Select the starting vertex visited and also drop it in the queue no matter what.

160
00:09:41,470 --> 00:09:43,450
I am showing you this repeating.

161
00:09:43,810 --> 00:09:46,650
This was the initial one, but the steps are repeating.

162
00:09:47,050 --> 00:09:47,740
Look at this.

163
00:09:48,290 --> 00:09:53,190
Take all the vertex from Q OK, go to work and explore that one.

164
00:09:53,500 --> 00:09:55,210
So start exploring one.

165
00:09:55,480 --> 00:10:00,100
Who are the center one, two, three and four in which order they can visit.

166
00:10:00,130 --> 00:10:01,660
You can visit them in any order.

167
00:10:01,990 --> 00:10:03,520
So I will first go on to.

168
00:10:04,950 --> 00:10:15,200
Two was inserted into the mix, two, three, three is visited and inserted a Q the next to four for

169
00:10:15,270 --> 00:10:20,620
the visitor, then inserted in the Q No one is completely explored.

170
00:10:21,210 --> 00:10:25,320
So once you select the vertex for exploration, explore it completely.

171
00:10:27,050 --> 00:10:35,240
This we have done now, again, repeat, select the next vertex for exploration from where from Q So

172
00:10:35,240 --> 00:10:41,960
in the Q next vertex is to start exploring to see from the what are the water so that I can visit the

173
00:10:41,960 --> 00:10:43,760
tree which is already visited.

174
00:10:43,880 --> 00:10:45,370
So just draw the red line.

175
00:10:45,920 --> 00:10:47,730
So two is completely explored.

176
00:10:48,050 --> 00:10:53,570
So next select the vertex from Q and start exploring it three from three.

177
00:10:53,570 --> 00:10:55,270
What are the words that I can visit.

178
00:10:55,280 --> 00:10:58,220
I should explore three to four and five.

179
00:10:58,540 --> 00:11:02,270
So two is already done for a Nexus five.

180
00:11:02,420 --> 00:11:03,370
I don't like this.

181
00:11:03,680 --> 00:11:04,850
So five is a new word.

182
00:11:04,850 --> 00:11:06,920
Expositor and insert in the Q.

183
00:11:08,900 --> 00:11:16,700
Now, trees completely explore Selecter next Ford Explorer exploration for who are the four, five and

184
00:11:16,700 --> 00:11:18,260
three that are already visited.

185
00:11:18,590 --> 00:11:21,040
So just five dotted now four.

186
00:11:21,080 --> 00:11:22,880
There are no new words from Ford's.

187
00:11:22,940 --> 00:11:24,390
Ford is completely explored.

188
00:11:24,910 --> 00:11:30,680
Now, next word from the Cuban Five who are determined to five six.

189
00:11:32,550 --> 00:11:41,370
Does it add to the kill seven, visit seven, added to the Q, not five is completely explored.

190
00:11:42,030 --> 00:11:44,570
Next works for Exploration six.

191
00:11:44,970 --> 00:11:49,180
Nobody, nobody's is the six, so six is completely explored.

192
00:11:49,200 --> 00:11:51,120
We don't say it cannot be explored.

193
00:11:51,330 --> 00:11:52,920
We say it is completely explored.

194
00:11:53,220 --> 00:11:56,040
Next Vertex from Q seven seven.

195
00:11:56,050 --> 00:12:00,660
There is nothing I just said to seven so seven is also completely explored.

196
00:12:00,960 --> 00:12:06,030
Kyul became empty but four search ends and the result of that four soldiers.

197
00:12:06,030 --> 00:12:07,680
One, two, three, four, five, six, seven.

198
00:12:09,120 --> 00:12:15,690
This is one of the reasons I said that when I'm exploring Vertex one, two, three, four, even I can

199
00:12:15,690 --> 00:12:16,680
change the order.

200
00:12:16,690 --> 00:12:21,210
So if you change the order, you get a different bill for such and it is amended.

201
00:12:21,480 --> 00:12:24,840
So for a given graph, there can be various breakfast.

202
00:12:24,840 --> 00:12:32,040
Such importance, if not the order in which you are visiting, importances that you are visiting all

203
00:12:32,040 --> 00:12:34,410
the is us that is more important.

204
00:12:35,130 --> 00:12:40,860
So that's all I have given you one example and all of you have shown you how the various examples are

205
00:12:40,860 --> 00:12:45,050
possible and then learn a few things from this one.

206
00:12:45,570 --> 00:12:49,680
This treaty is called US for search spanning three.

207
00:12:56,250 --> 00:13:02,820
It is a bit for such, apparently, and these are dotted are just what I am drawing, these can be called

208
00:13:02,820 --> 00:13:03,360
as.

209
00:13:04,470 --> 00:13:05,850
Cross benches.

210
00:13:09,480 --> 00:13:16,440
These girls are just in for surgery, will be connecting from one vertex to a vertex in the next level,

211
00:13:16,440 --> 00:13:18,360
only not far away.

212
00:13:18,390 --> 00:13:25,670
What you will not find are just connecting from two to six, know if there is any crosshatch.

213
00:13:25,710 --> 00:13:31,320
And then the first, such a spinal injury, it will be connecting to what it says are different levels

214
00:13:31,320 --> 00:13:33,360
like this one level to the next level only.

215
00:13:33,930 --> 00:13:35,060
So that's what's happening.

216
00:13:35,760 --> 00:13:41,910
Now, the next thing, how much time it has taken, let us do analysis, see the time taken for performing

217
00:13:41,910 --> 00:13:42,790
blood for such.

218
00:13:42,960 --> 00:13:45,480
So what is the work we are doing analytically?

219
00:13:45,480 --> 00:13:47,190
We are looking at right analytically.

220
00:13:47,850 --> 00:13:50,620
The work we are doing this we are doing all the work.

221
00:13:50,650 --> 00:13:51,350
Is this once.

222
00:13:51,510 --> 00:13:54,470
So how many bodies is out there and what is inside there.

223
00:13:54,780 --> 00:13:57,960
So the times are rough and that's it.

224
00:13:58,710 --> 00:14:01,260
Then how about inserting and deleting in the queue?

225
00:14:01,260 --> 00:14:04,740
And also we are checking whether the vertex is visited or not.

226
00:14:04,740 --> 00:14:06,510
More than one word are processed here.

227
00:14:06,840 --> 00:14:09,780
We ignore them analytically, we ignore them.

228
00:14:10,500 --> 00:14:12,990
We say the times are tough and.

229
00:14:14,480 --> 00:14:19,970
So analytical time for breakfast, searchers order, and that's all.

230
00:14:20,280 --> 00:14:26,650
Next, I will show you a few valid breakfasts such without drawing any tree and I'll just I will try.

231
00:14:26,650 --> 00:14:30,650
I don't feel bad for sales so that you can understand all these are correct.

232
00:14:31,280 --> 00:14:33,810
First one, I'll start from word one.

233
00:14:33,990 --> 00:14:39,160
OK, what next one then explore my next one, two, three, four.

234
00:14:39,410 --> 00:14:44,960
No, I will take four, three, two, first four, then three, then two.

235
00:14:45,260 --> 00:14:46,730
One is completely explored.

236
00:14:46,880 --> 00:14:50,330
I should explore faunal from four three.

237
00:14:50,330 --> 00:14:51,770
It's already done five.

238
00:14:52,010 --> 00:14:53,150
OK, next just fine.

239
00:14:53,540 --> 00:14:55,280
So four is completely explored.

240
00:14:55,410 --> 00:15:01,580
Now start exploring three from three to five which are already there then three is completely explored.

241
00:15:01,850 --> 00:15:02,900
Explored two.

242
00:15:02,930 --> 00:15:04,250
There is nothing there to do.

243
00:15:04,460 --> 00:15:10,270
So explore then from five, six and seven, six and seven.

244
00:15:10,700 --> 00:15:13,370
No I will visit first seven then six.

245
00:15:13,370 --> 00:15:13,610
Okay.

246
00:15:13,610 --> 00:15:14,210
No problem.

247
00:15:15,160 --> 00:15:20,210
You can take four seven, then six, five is explored, not explored, seven.

248
00:15:20,620 --> 00:15:22,120
There is nothing I just said to seven.

249
00:15:22,140 --> 00:15:23,120
So seven is explored.

250
00:15:23,470 --> 00:15:26,270
There's nothing I just to six or six is also explore.

251
00:15:26,800 --> 00:15:27,700
There's one answer.

252
00:15:28,300 --> 00:15:32,390
That second one I'll start exploring from Vertex five.

253
00:15:32,620 --> 00:15:40,840
OK, start from five visited explored five water are three, four, six and seven.

254
00:15:41,170 --> 00:15:51,460
So I will dig seven three then six for ok six for that five is completely explored.

255
00:15:51,850 --> 00:15:52,810
Explored seven.

256
00:15:53,030 --> 00:15:57,970
There is nothing I the center seven then explored three four three are just not exists.

257
00:15:57,970 --> 00:15:58,870
Two and one.

258
00:15:59,320 --> 00:16:01,480
So two and one.

259
00:16:02,420 --> 00:16:07,960
Now actually I have finished with all the water says I can stop now instead of continuing because total

260
00:16:07,960 --> 00:16:08,320
seven.

261
00:16:08,320 --> 00:16:09,150
What is this other.

262
00:16:09,400 --> 00:16:10,060
All seven.

263
00:16:10,060 --> 00:16:10,930
What is the got.

264
00:16:12,300 --> 00:16:18,000
So I can stop it here, I am doing it manually, I can stop it otherwise for every vertex, it should

265
00:16:18,000 --> 00:16:18,500
be checked.

266
00:16:19,260 --> 00:16:27,570
Next 31, I'll start from what looks to from what x2 first visit Murdocks to start exploring to just.

267
00:16:27,570 --> 00:16:28,620
And what is Asaka?

268
00:16:28,830 --> 00:16:30,240
One and three.

269
00:16:31,620 --> 00:16:35,780
I'll take three first, then one, OK, valid to explore.

270
00:16:36,270 --> 00:16:38,110
Now start exploring next, which one?

271
00:16:38,130 --> 00:16:42,100
Three from three, four and five.

272
00:16:42,270 --> 00:16:46,260
So I will take first five, then four or five, then four.

273
00:16:46,320 --> 00:16:46,950
No problem.

274
00:16:47,280 --> 00:16:51,690
Three is explored, not start excluding one from one, two and four.

275
00:16:51,690 --> 00:16:52,670
They are already visited.

276
00:16:53,070 --> 00:16:54,440
So two and four visited.

277
00:16:54,480 --> 00:16:56,820
Four one is already explored then five.

278
00:16:57,030 --> 00:16:59,610
Who are the three to five, six and seven.

279
00:16:59,820 --> 00:17:01,320
I take seven and six.

280
00:17:01,320 --> 00:17:05,490
OK so five is explored now start exploring for there is nothing.

281
00:17:05,490 --> 00:17:11,700
I just already visited then for seven nothing at six nothing site so I got this answer.

282
00:17:11,910 --> 00:17:16,980
So one, four, three, two, five, seven, six is correct but four search five seven three six four

283
00:17:16,980 --> 00:17:18,210
two one is also correct.

284
00:17:18,839 --> 00:17:22,589
Even two three one five four seven six is also correct.

285
00:17:23,040 --> 00:17:29,100
The differences are the starting vertex is different sometimes and the order in which the words are

286
00:17:29,130 --> 00:17:32,490
explored or visited, that is also changing.

287
00:17:33,570 --> 00:17:35,340
So all these are valid.

288
00:17:35,340 --> 00:17:37,740
But first, search for this graph.

289
00:17:39,000 --> 00:17:40,680
So that's about it for search.

290
00:17:41,430 --> 00:17:44,940
Next, we will look at an algorithm for breakfast search.

