1
00:00:00,320 --> 00:00:03,270
In this video, I will explain the Parameter Passing Methods.

2
00:00:03,290 --> 00:00:09,850
Like, there are 3 Parameter Passing Methods, Pass by Value, Pass by Address and Pass by Reference.

3
00:00:10,030 --> 00:00:15,780
So for explaining all 3, I have taken 1 simple example that is swapping of numbers.

4
00:00:15,840 --> 00:00:17,350
I have a function for swap.

5
00:00:18,240 --> 00:00:20,720
So let us understand all these 3 methods.

6
00:00:20,820 --> 00:00:26,550
So first is, Pass by Value or Call by Value.

7
00:00:26,550 --> 00:00:28,860
This example is based on Call by Value.

8
00:00:28,980 --> 00:00:34,950
Let me explain you the example first of all, See this is the main function having variables a and b with the

9
00:00:34,950 --> 00:00:36,550
values of 10 and 20.

10
00:00:36,600 --> 00:00:43,740
So these are the variables of main function having values 10 and 20. Then, it is calling swap function

11
00:00:44,300 --> 00:00:45,460
swap function.

12
00:00:45,510 --> 00:00:51,660
The swap function is taking 2 parameters, x and y and it is swapping the numbers x and y with the help

13
00:00:51,660 --> 00:00:53,030
of a temporary variable.

14
00:00:53,930 --> 00:01:00,010
And then, when the control comes back to the main function, it will print the values of a and b.

15
00:01:00,340 --> 00:01:04,390
So, this is the code, main ( ) function and swap ( ) function.

16
00:01:04,620 --> 00:01:10,200
So here in the diagram, I'm showing the variables of main function a and b, and these are the variables

17
00:01:10,290 --> 00:01:16,940
of swap function, that is x, y and temp. And inside the main memory, this is the code section wherever

18
00:01:16,940 --> 00:01:24,090
the program is residing, and these are the stack frames or the activation records of main function and

19
00:01:24,090 --> 00:01:29,280
swap function. When the function type is called, this activation record will be created, and deleted when

20
00:01:29,280 --> 00:01:31,110
the function ends.

21
00:01:31,140 --> 00:01:34,670
So, for explaining the working, I have taken these diagrams.

22
00:01:34,740 --> 00:01:39,420
Now, let us understand Call by Value and it's working.

23
00:01:39,870 --> 00:01:41,470
Now, Call by Value.

24
00:01:41,610 --> 00:01:45,270
See, here the main function is passing these parameters.

25
00:01:45,270 --> 00:01:54,730
These are actual parameters, and these are formal parameters x and y. What happens when the function

26
00:01:54,730 --> 00:01:59,700
is called? The value of a and b is copied in x and y.

27
00:02:00,090 --> 00:02:03,580
So, 10 is copied here and 20 is copied here.

28
00:02:05,040 --> 00:02:10,910
So, the formal parameters are normal variables, so they will take the value of these parameters.

29
00:02:11,009 --> 00:02:15,140
They will take the value, right, when the function is called.

30
00:02:15,820 --> 00:02:20,120
Now this function, as I am not returning anything, it is void type.

31
00:02:20,120 --> 00:02:24,710
Now this is the logic for swapping the values. x and y are swapped.

32
00:02:24,860 --> 00:02:26,140
So with the help of temp.

33
00:02:26,260 --> 00:02:28,080
So first line, temp = x;

34
00:02:28,150 --> 00:02:31,160
This will become 10, and then, x = y;

35
00:02:31,160 --> 00:02:32,760
So this will become 20.

36
00:02:33,340 --> 00:02:35,190
And then y = temp;

37
00:02:35,200 --> 00:02:37,700
So this will become 10.

38
00:02:37,780 --> 00:02:45,140
You can see that the formal parameters are modified but actual parameters remain same.

39
00:02:45,160 --> 00:02:52,250
So, the swapping is done inside the variables of swap function only, they are not reflected inside

40
00:02:52,680 --> 00:02:54,200
actual parameters.

41
00:02:54,290 --> 00:02:59,640
So when the function ends after this line, when it comes back here, there is nothing return, it is

42
00:02:59,650 --> 00:03:01,610
void type, then,

43
00:03:02,160 --> 00:03:03,880
So, a and b are printed.

44
00:03:03,910 --> 00:03:05,300
So, what are the values? Still

45
00:03:05,600 --> 00:03:06,480
10 and 20.

46
00:03:06,740 --> 00:03:08,610
So these values are not swapped.

47
00:03:08,720 --> 00:03:12,590
These values are not swapped, so I'll get the same values, 10 and 20.

48
00:03:12,620 --> 00:03:17,930
So if you see here, these 10 and 20 were there, and these 10 and were passed, and with the

49
00:03:17,930 --> 00:03:26,720
help of temp, this became 20 and this became 10. But actual parameters, they are same only. So, this is Pass by Value.

50
00:03:27,290 --> 00:03:28,810
In Pass by Value,

51
00:03:28,880 --> 00:03:34,280
any changes are done to formal parameters will not reflect in actual parameters.

52
00:03:34,550 --> 00:03:40,290
So, when you should use Pass by Value? When you don't have to modify actual parameters, you can use Pass by

53
00:03:40,340 --> 00:03:43,940
Value. And you can use Pass by Value if a function is

54
00:03:43,970 --> 00:03:46,560
Returning some results. So swap

55
00:03:46,550 --> 00:03:52,210
function should not be done using Pass by Value. In the previous video, we saw adding 2 numbers.

56
00:03:52,250 --> 00:03:56,330
So that is suitable for Pass by Value. So, Pass by Value can be used there.

57
00:03:56,450 --> 00:04:01,510
But here Pass by Value is not suitable for swapping two numbers.

58
00:04:01,580 --> 00:04:06,650
So anyway we have learned what is Pass by Value. In Pass by Value, actual parameters will not

59
00:04:06,650 --> 00:04:10,570
be modified if any changes are done formal parameters.

60
00:04:10,610 --> 00:04:16,200
Next, I will show you Call by Address. Now, let us look at Call by Address.

61
00:04:16,290 --> 00:04:18,399
So in Call by Address mechanism,

62
00:04:18,410 --> 00:04:23,090
The addresses of actual parameters are passed to formal  parameters and  formal parameters must

63
00:04:23,140 --> 00:04:24,040
be pointers.

64
00:04:24,590 --> 00:04:31,330
Any changes is done inside a function will modify actual parameters. Now here, we have to learn 2 things,

65
00:04:31,660 --> 00:04:35,800
How to write Call by Address? and, How it works?

66
00:04:35,800 --> 00:04:38,600
So remember, Call by Address uses pointers.

67
00:04:38,650 --> 00:04:43,690
So let me write down the syntax. So you have to observe the syntax very carefully.

68
00:04:43,690 --> 00:04:47,330
This code is same as Call by Value code, right?

69
00:04:47,350 --> 00:04:50,380
Now, How to convert it into Call by Address code.

70
00:04:50,410 --> 00:04:52,360
So what other things I have to do?

71
00:04:52,360 --> 00:04:58,330
So here, inside the main function, when I'm passing these parameters, these are actual parameters, So I should

72
00:04:58,330 --> 00:05:00,400
send their addresses.

73
00:05:00,400 --> 00:05:05,930
So these are the addresses, this is first thing. So it means I'm sending 200 and 202. Now

74
00:05:05,950 --> 00:05:11,230
who can take addresses? We know pointers can take addresses. So these must be pointers.

75
00:05:11,350 --> 00:05:19,740
Yes these are the pointers. So it means when I pass from here the address 200 will be copied

76
00:05:19,740 --> 00:05:23,400
in this one and 202 will be copied in this one.

77
00:05:23,910 --> 00:05:30,630
So when the function is called, so it means this is pointing on this and this is pointing on this one.

78
00:05:30,780 --> 00:05:36,680
Yes those are the pointers. Now, here inside the code, as this is a pointer,

79
00:05:36,720 --> 00:05:38,650
If I say just x it is 200.

80
00:05:38,760 --> 00:05:40,170
So this will be 200.

81
00:05:40,170 --> 00:05:41,710
No no I'm talking about this.

82
00:05:41,730 --> 00:05:43,810
The value here is 10 and 20.

83
00:05:44,010 --> 00:05:45,240
So I'm talking about the value.

84
00:05:45,450 --> 00:05:52,470
So here I should say I *, and *x, and *y, and here also 

85
00:05:53,340 --> 00:05:55,830
So these are the changes we have to make.

86
00:05:55,860 --> 00:05:59,620
So, first I wrote a simple code of Call by Value, then I made it into Call by Address.

87
00:05:59,640 --> 00:06:04,140
So in Call by Address, the formal parameters must be pointers,

88
00:06:04,140 --> 00:06:11,280
And here I have to use de-referencing that is *, for accessing the data of actual parameters. And here,

89
00:06:11,400 --> 00:06:13,390
addresses must be passed.

90
00:06:13,410 --> 00:06:15,150
So these are the changes in the syntax.

91
00:06:15,210 --> 00:06:18,680
So you should observe the syntax very carefully.

92
00:06:18,760 --> 00:06:21,010
Now I will show you the working.

93
00:06:21,210 --> 00:06:23,960
Let us start executing the program. See first,

94
00:06:23,970 --> 00:06:28,140
The main function is having 2 variables, a and b. They are having values 10 and 20.

95
00:06:28,170 --> 00:06:36,520
So these are 10 and 20. Next, the swap function it's called, the addresses are passed.

96
00:06:36,680 --> 00:06:39,280
So, these are the pointers. So they will take the addresses.

97
00:06:39,290 --> 00:06:44,690
So here in the diagram, if you want to see, these are pointing on this one they are having the addresses.

98
00:06:44,750 --> 00:06:49,470
So now, this function can access the variables of  another function.

99
00:06:49,490 --> 00:06:52,950
It's not directly, it is indirectly using pointers.

100
00:06:53,060 --> 00:06:53,770
Yes.

101
00:06:53,780 --> 00:06:59,740
One function cannot access the variables of another function directly but it can access using pointers

102
00:06:59,770 --> 00:07:01,250
so it can access indirectly.

103
00:07:01,280 --> 00:07:08,260
Yes this is indirect access. Now, when the function is called, temp is already there.

104
00:07:08,360 --> 00:07:11,280
temp = *x; So, *x,

105
00:07:11,330 --> 00:07:18,860
So that is 10. If you say x, it is 200. *x? it is 10. So, 10 is stored in temp.

106
00:07:18,860 --> 00:07:22,700
*x =*y; *x is 10, *y is 20.

107
00:07:22,910 --> 00:07:29,720
So this is copied here. 20 is copied here. Then, *y = temp; *y = 10.

108
00:07:30,110 --> 00:07:37,790
So it is 10. Now, you can see that actual variables are modified. So, when the function

109
00:07:37,790 --> 00:07:44,180
ends, control comes back, and when it prints, the value of a is 20 and value of b is 10. So this

110
00:07:44,180 --> 00:07:45,860
is 20 and this is 10.

111
00:07:45,920 --> 00:07:54,860
So yes these are swapped. so Call by Address is suitable mechanism for modifying the actual parameters.

112
00:07:55,070 --> 00:07:59,860
So, we will be using this type of code more frequently in our programs.

113
00:08:00,100 --> 00:08:03,470
So you should be familiar with this one. Call by Address is more useful.

114
00:08:03,650 --> 00:08:05,600
So that's all about Call by Address.

115
00:08:05,720 --> 00:08:09,590
Now the last thing is Call by Reference.

116
00:08:09,640 --> 00:08:12,760
Now let us see all reference. For Call by Reference,

117
00:08:12,980 --> 00:08:18,260
I have written back the same example now, This is Call by Value parameter passing method.

118
00:08:18,260 --> 00:08:20,650
Now what are the changes in this I should do

119
00:08:20,780 --> 00:08:23,660
So that it becomes called by reference?

120
00:08:23,660 --> 00:08:24,630
So, what is a reference

121
00:08:24,630 --> 00:08:30,440
already we have learned in the previous video and references are supported only in C++. This is

122
00:08:30,440 --> 00:08:37,520
not a part of C language, and this is a very useful and powerful mechanism or feature of C++.

123
00:08:37,520 --> 00:08:42,350
So here, we have to learn 2 things, How to write Call by  Reference? and, How it works?

124
00:08:42,350 --> 00:08:45,920
So first of all, How to write call by reference?

125
00:08:46,130 --> 00:08:53,050
Let us look at the code here, inside the main function, I have two variables a and b, a and b are passed.

126
00:08:53,060 --> 00:08:54,740
So this is Call by Value, right?

127
00:08:54,760 --> 00:08:55,880
Nothing is written here.

128
00:08:56,000 --> 00:09:00,030
It is not & or anything, this is Call by Value. Now here,

129
00:09:00,040 --> 00:09:05,320
These are also normal variables, for this is Call by Value, and this is normal code.

130
00:09:05,800 --> 00:09:07,660
So same as Call by Value.

131
00:09:07,660 --> 00:09:09,620
How to make it Call by Reference?

132
00:09:09,730 --> 00:09:15,830
Let us see the changes, here in actual parameters don't do anything.

133
00:09:16,350 --> 00:09:19,370
And inside the body don't do anything.

134
00:09:19,410 --> 00:09:24,070
Same as Call by Value. Only the in parameters, just before

135
00:09:24,270 --> 00:09:25,990
parameter name, or variable name,

136
00:09:26,090 --> 00:09:26,610
write &

137
00:09:26,640 --> 00:09:31,890
So simple syntax. Syntax is very simple.

138
00:09:31,890 --> 00:09:33,380
Just write & there.

139
00:09:33,540 --> 00:09:35,190
Those are references.

140
00:09:35,190 --> 00:09:38,510
This is a reference to a, this  is a reference to b.

141
00:09:38,510 --> 00:09:39,180
y is reference to b.

142
00:09:39,240 --> 00:09:41,260
Those have became references.

143
00:09:41,460 --> 00:09:44,630
And How do you use references? Normally like other variables.

144
00:09:44,640 --> 00:09:49,210
Normal variables, so those are normal variables.

145
00:09:49,640 --> 00:09:55,020
So syntactically, This is Call by Reference. See, it's not a major change. Compared to Call by Value, it's

146
00:09:55,020 --> 00:10:01,740
similar just we are having & there. Now working; How it works?

147
00:10:01,740 --> 00:10:02,650
See this.

148
00:10:02,810 --> 00:10:06,440
Let us start. Program starts from main function, a and b are 2 variables.

149
00:10:06,530 --> 00:10:11,970
Okay. Then, a is having value 10, b is having value 20.

150
00:10:12,380 --> 00:10:19,410
Swap function is called by passing a and b, a and b are passed to x and y. x becomes a reference to a, y becomes a

151
00:10:19,410 --> 00:10:24,450
reference to b; and reference is nothing but alias or another name or a nickname to a variable.

152
00:10:24,450 --> 00:10:30,980
So this itself is x, and that itself is y, right. So does it take any memory?

153
00:10:30,980 --> 00:10:34,680
References doesn't take any memory. Existing variable, another name is given.

154
00:10:35,250 --> 00:10:37,760
Yes so that doesn't take any extra memory.

155
00:10:38,280 --> 00:10:42,550
So, these 4 bytes 2 bytes for integer, 2 bytes for integer, same 4 bytes.

156
00:10:42,570 --> 00:10:44,710
So, these 2 bytes are called as x, and

157
00:10:44,760 --> 00:10:48,280
that 2 bytes are called as y. See, these variables actually belong

158
00:10:48,280 --> 00:10:53,550
to main function. main ( ) is calling them as a b, and swap function is calling them as x y.

159
00:10:54,120 --> 00:10:55,080
Yes.

160
00:10:55,290 --> 00:10:57,050
So swap is calling with another name.

161
00:10:57,390 --> 00:11:04,320
Now, let us continue. Temp variable is declared, okay? Then, now

162
00:11:04,320 --> 00:11:09,630
temp = x; x value is stored in temp. Then x = y;

163
00:11:09,840 --> 00:11:17,320
So this becomes 20. Then, y = temp; so y becomes 10. Function ends.

164
00:11:17,380 --> 00:11:21,800
Control comes back to this line. Print, What are a and b? 20 and 10.

165
00:11:21,930 --> 00:11:29,910
So, 20 and 10. So you can see that, when these formal parameters, x and y are manipulated, the actual

166
00:11:29,910 --> 00:11:33,640
parameters are modified. So that's it.

167
00:11:33,640 --> 00:11:37,760
This is Call by Reference. Now, one important point.

168
00:11:37,840 --> 00:11:45,430
I said that one function cannot access the variables of another function directly, it can access indirectly.

169
00:11:45,730 --> 00:11:52,010
But how it is possible that it is accessing directly? So the answer to this is, if you see inside

170
00:11:52,010 --> 00:11:54,950
the memory, swap is not a separate function.

171
00:11:55,550 --> 00:12:01,370
So if it's not a separate function it has became a part of main function and there is only one activation

172
00:12:01,430 --> 00:12:02,040
activation record.

173
00:12:02,090 --> 00:12:08,120
So if you see the working of the same program here, now as long as the main function code is running,

174
00:12:08,120 --> 00:12:15,920
these are a and b; and once the swap starts these are called as x and y, and also temp variable is created

175
00:12:15,950 --> 00:12:23,720
inside same old activation record of main function, right? Inside the same stack frame And, once the

176
00:12:23,720 --> 00:12:29,550
swap function ends, this is gone, and the values that were 10 and 20, these will change to 20 and 10.

177
00:12:30,140 --> 00:12:36,680
So this swap is not a separate body of a function, it has became a part of the main function. So, it means

178
00:12:36,680 --> 00:12:40,670
that a machine code of the swap function will be pasted here.

179
00:12:41,150 --> 00:12:44,410
So this is more like monolithic program, right?

180
00:12:44,480 --> 00:12:50,780
The entire code inside a single main function only, So the machine code is monolithic. Though the source

181
00:12:50,780 --> 00:12:54,400
code is procedural or modular.

182
00:12:54,510 --> 00:13:03,350
So yes, C++ does this one. C doesn't do this. C++ allows Called by Reference and the code of that function 

183
00:13:03,350 --> 00:13:09,720
will be copied at the place of function call. So, do you think this should be allowed or this should be entertain

184
00:13:09,750 --> 00:13:11,210
that code is copied

185
00:13:11,220 --> 00:13:13,140
there? What is the use of writing function?

186
00:13:13,140 --> 00:13:15,090
So yes, this is not

187
00:13:15,110 --> 00:13:21,420
advisable to use Call by Reference more frequently. You can use Call by Reference for

188
00:13:21,420 --> 00:13:27,120
small functions, one or two lines of function, or the function like swap, you can use Call by Reference

189
00:13:27,360 --> 00:13:35,170
but don't use it for heavy functions which are having loops and are having complex logic. So Call by Reference

190
00:13:35,260 --> 00:13:36,610
should be used carefully.

191
00:13:36,640 --> 00:13:43,870
So it's a more powerful feature of C++ but it should be used carefully, right. It cannot be used always.

192
00:13:44,050 --> 00:13:50,860
So that is the reason C++ also have called Call by Address. So, more commonly used features are Call by Value and

193
00:13:50,910 --> 00:13:53,730
Call by Reference.

194
00:13:53,800 --> 00:13:58,570
So that's all about Parameter Passing Methods. Then, in the coming videos, I'll show you how to passe

195
00:13:58,690 --> 00:14:03,390
arrays to a function and how to pass structures to a function.

