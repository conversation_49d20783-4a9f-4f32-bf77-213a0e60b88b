1
00:00:00,150 --> 00:00:05,190
And if we do, we will look at the demonstration of a function to check whether a Linklaters having

2
00:00:05,190 --> 00:00:07,000
a loop or as a Clelia.

3
00:00:07,020 --> 00:00:11,310
So this is the main function which we have been using, and it is creating a linkage of these.

4
00:00:11,700 --> 00:00:15,030
And as a lineal interest, I will form a loop in this link.

5
00:00:15,190 --> 00:00:16,710
So how to form for that?

6
00:00:16,710 --> 00:00:24,150
I will, first of all, take two pointers, temporary pointers, nor even and people are using these

7
00:00:24,150 --> 00:00:24,740
pointers.

8
00:00:24,750 --> 00:00:30,270
I will form a loop, so I will make it even point upon third node that is 30.

9
00:00:30,300 --> 00:00:31,240
So let us do it.

10
00:00:31,290 --> 00:00:39,930
So even assign first, this will be the first node, then its next means it will be on 20, then its

11
00:00:39,930 --> 00:00:42,720
next means it will be on 30.

12
00:00:42,720 --> 00:00:49,170
So T1 will be pointing on node thirty first maintain order and then its next is not only the next,

13
00:00:49,170 --> 00:00:50,170
next is not 30.

14
00:00:50,310 --> 00:00:54,150
Then I will take point eighty two and make it point upon last node.

15
00:00:54,300 --> 00:01:02,140
So for that first as the first node, the next means second node, the next is hard node, the next

16
00:01:02,160 --> 00:01:05,519
is fourth nor the next fifth node.

17
00:01:05,610 --> 00:01:07,920
Yes, it was pointing on the last note.

18
00:01:08,190 --> 00:01:14,440
Even I can make it move to layer by using loop by loop instead of my loop I am directly adding a single

19
00:01:14,440 --> 00:01:19,110
line not e to next is null because it's binding on node 50.

20
00:01:19,120 --> 00:01:25,080
So it does next is made as even so it becomes a loop.

21
00:01:25,110 --> 00:01:27,270
So it is just like from fifty.

22
00:01:27,270 --> 00:01:29,370
It is pointing on from fifty.

23
00:01:29,370 --> 00:01:30,690
Disappointing on thirty.

24
00:01:30,690 --> 00:01:32,610
So having a loop from the loop.

25
00:01:33,000 --> 00:01:38,580
Now let us write on a function that is a simple function for checking whether Linklaters having a loop

26
00:01:38,580 --> 00:01:39,120
or not.

27
00:01:39,150 --> 00:01:44,280
Now here let's write a function for checking it for this loop so it should return integer type.

28
00:01:44,280 --> 00:01:50,220
That is true or false and the function name is as a loop and it should take a pointer to first node.

29
00:01:50,220 --> 00:01:54,290
So I will call the point on the emails f then for accessing.

30
00:01:54,720 --> 00:02:01,620
I need to point out that is under P and Q so I will declare them point that B and point to Q initially

31
00:02:01,620 --> 00:02:07,890
P as well as Kubota depending on first not the process is repeating process to check whether Linklaters

32
00:02:07,890 --> 00:02:08,630
having a loop.

33
00:02:08,639 --> 00:02:16,020
So using do while I have explained so first pointer PS moved to the next node by just taking one step

34
00:02:16,590 --> 00:02:18,180
and the queue will take two steps.

35
00:02:18,190 --> 00:02:20,040
So first of all, first step is taken.

36
00:02:20,700 --> 00:02:27,300
Then the second step is conditional that if Q is not equal to null then Q will move to the next node.

37
00:02:27,300 --> 00:02:34,710
Otherwise it will remain there only and this process should continue until P and humans, none of them

38
00:02:34,710 --> 00:02:42,500
should be null as well as a P is not equal to Q So it will stop when any one of these condition fails.

39
00:02:42,510 --> 00:02:43,650
So then they become equal.

40
00:02:43,650 --> 00:02:45,620
It means there is a loop.

41
00:02:45,630 --> 00:02:48,120
So I think I have missed this connection up on board.

42
00:02:48,450 --> 00:02:53,640
So we have to write on this condition also that it will stop and both are meeting at one place and if

43
00:02:53,670 --> 00:02:58,290
it is equal to humans at same place, then return.

44
00:02:58,920 --> 00:03:00,420
True, that is a loop.

45
00:03:00,840 --> 00:03:03,620
Otherwise return false.

46
00:03:03,630 --> 00:03:04,860
That is, it's not a loop.

47
00:03:04,860 --> 00:03:09,930
So this condition I have missed on Lightbown when I wrote the code on whiteboard this is missing.

48
00:03:09,930 --> 00:03:11,630
That is B not equal to null.

49
00:03:11,970 --> 00:03:14,760
If if they are equal we should stop that's all.

50
00:03:14,760 --> 00:03:19,350
So already I have created a loop here then I will print f the result of that function.

51
00:03:19,350 --> 00:03:20,580
I will call that function.

52
00:03:20,580 --> 00:03:26,010
Function is as a loop and I will send the pointer to all of us know that said, let us run.

53
00:03:26,010 --> 00:03:26,430
Yes.

54
00:03:26,430 --> 00:03:28,620
One it is having a loop return.

55
00:03:28,620 --> 00:03:28,980
True.

56
00:03:28,980 --> 00:03:30,060
OK, I'll do one thing.

57
00:03:30,240 --> 00:03:34,410
I will make these statements as coming, then I will run it so it should return false.

58
00:03:34,410 --> 00:03:35,490
No it is not a loop.

59
00:03:35,490 --> 00:03:38,670
I got a warning because the Stephen and data are not in use.

60
00:03:38,670 --> 00:03:41,250
So the result of zero and false it's not a loop.

61
00:03:41,760 --> 00:03:43,470
So then I have made it as a loop.

62
00:03:43,470 --> 00:03:44,190
It is returning.

63
00:03:44,190 --> 00:03:44,640
True.

64
00:03:44,970 --> 00:03:46,460
Yes, perfect.

65
00:03:47,340 --> 00:03:48,970
So the code is working perfectly.

66
00:03:48,970 --> 00:03:52,470
It is detecting the Linklaters having a loop or not.

67
00:03:52,650 --> 00:03:53,940
Let me run once more.

68
00:03:53,940 --> 00:03:54,240
Yes.

69
00:03:54,240 --> 00:03:54,940
Return true.

70
00:03:54,960 --> 00:04:03,660
So here even I can write on a message that it's a loop or a linear link by using conditional statement.

71
00:04:03,660 --> 00:04:05,480
But directly I'm printing this result.

72
00:04:05,490 --> 00:04:06,690
That is true or false.

73
00:04:06,690 --> 00:04:07,890
NetSol right.

74
00:04:07,890 --> 00:04:09,810
On this function by yourself and check it.

