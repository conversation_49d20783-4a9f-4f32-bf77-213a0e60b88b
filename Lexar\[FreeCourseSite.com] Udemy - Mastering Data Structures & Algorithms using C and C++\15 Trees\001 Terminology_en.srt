1
00:00:00,740 --> 00:00:07,760
This section is about trees, so we learn about general trees and binary trees and various types of

2
00:00:07,760 --> 00:00:08,690
binary trees.

3
00:00:09,440 --> 00:00:16,000
So in this video we learn about the terminology of trees and introduction to binary trees.

4
00:00:16,309 --> 00:00:18,600
So let us start with terminology.

5
00:00:19,130 --> 00:00:25,220
See here I have taken a general tree and nodes are containing alphabets so that it's easy for you to

6
00:00:25,220 --> 00:00:25,800
understand.

7
00:00:26,270 --> 00:00:27,680
Let us define our tree.

8
00:00:27,970 --> 00:00:32,210
Tree is a collection of nodes or what is this?

9
00:00:32,570 --> 00:00:35,030
And edges, nodes.

10
00:00:35,240 --> 00:00:38,510
And these links are called as edges.

11
00:00:39,020 --> 00:00:46,730
If there are any nodes then there will be and minus one axis, because for each node you can see that

12
00:00:46,730 --> 00:00:48,680
there is the edge coming over that node.

13
00:00:48,860 --> 00:00:51,530
There is an X coming over that not every node.

14
00:00:51,530 --> 00:00:54,950
There is an X coming from its spirit except root.

15
00:00:55,070 --> 00:01:00,450
Root is not having a hedgy coming from any parent because that is the parent of all the nodes.

16
00:01:00,950 --> 00:01:04,640
So if there are any vertices, then there will be any minus one.

17
00:01:05,480 --> 00:01:07,540
This is the root node for the tree.

18
00:01:08,390 --> 00:01:14,720
And if I look at only this portion of the node below B, then this is a subtree.

19
00:01:16,000 --> 00:01:25,690
D and all the descendants of the Ilda n Belote is a subtree, so if I define a tree, tree is a collection

20
00:01:25,690 --> 00:01:26,800
of N.

21
00:01:27,730 --> 00:01:36,020
Among those notes, one more is taken as route north rest of the north side are divided into disjointed

22
00:01:36,040 --> 00:01:39,120
subsets, they are in some sense, this is one subset.

23
00:01:39,120 --> 00:01:39,980
There's another subset.

24
00:01:40,300 --> 00:01:41,860
There is only one node in the subset.

25
00:01:42,430 --> 00:01:46,900
And each subset is a three again or subtree again.

26
00:01:47,410 --> 00:01:52,660
So I repeat, the definition three is a collection of north where one of the node is taken as a root

27
00:01:52,660 --> 00:01:59,980
node and the rest of the north are divided into disjoined subsets and each subset of the three or subquery.

28
00:02:00,960 --> 00:02:07,650
Now, let us look at the terms first route, the very first note on the top as a route.

29
00:02:08,750 --> 00:02:10,100
Parent and child.

30
00:02:11,000 --> 00:02:18,500
A. is a parent to its very next to descendants are those children which are connected with just one

31
00:02:18,510 --> 00:02:18,860
act.

32
00:02:19,750 --> 00:02:22,740
Are those descendants which are connected with one act?

33
00:02:23,420 --> 00:02:32,480
So for BNF, other children, for these are children of the city, the parent of a.

34
00:02:34,130 --> 00:02:41,600
And or children of El Al is a parent of an all, so we have finished these.

35
00:02:42,690 --> 00:02:43,800
Now, siblings.

36
00:02:45,970 --> 00:02:48,370
Siblings are children of the same parent.

37
00:02:49,780 --> 00:02:56,890
Are children of these, so they are sibling to each other, are siblings.

38
00:02:57,700 --> 00:03:05,500
Janki are siblings because they're the same and are siblings next descendants.

39
00:03:07,160 --> 00:03:14,360
For any Naude, the set of laws, which are his children and their children and their children form,

40
00:03:14,480 --> 00:03:20,090
or in other words, the descendants, are all those set of laws which can be read from a particular

41
00:03:20,090 --> 00:03:26,590
morgue or underdiagnosed, for example, for b e, f, g, m.

42
00:03:26,840 --> 00:03:28,910
All these are descendants of B.

43
00:03:30,520 --> 00:03:38,740
For all and then all our descendants, for the IPL and all all the loans that are reachable from the

44
00:03:38,950 --> 00:03:40,180
descendants of the.

45
00:03:41,560 --> 00:03:43,360
Nexxus ancestors.

46
00:03:44,660 --> 00:03:55,730
So for any node, all the nodes along the path from that group node, so for M gfa B A R ancestors for

47
00:03:55,730 --> 00:04:02,180
an L, the A R ancestors, the Nexus degree of A..

48
00:04:02,960 --> 00:04:03,800
Degree of A..

49
00:04:03,810 --> 00:04:09,890
This number of children, it is having direct children, not all descendants that children degree of

50
00:04:09,910 --> 00:04:11,720
Elorza to just having two children.

51
00:04:11,960 --> 00:04:20,290
Nagati over the years a three degree of a three degree of first two degree of Giese one and degree of

52
00:04:20,700 --> 00:04:24,830
zero zero MCO number of children.

53
00:04:24,830 --> 00:04:27,620
Any notice having is the degree of a node.

54
00:04:28,130 --> 00:04:30,020
Then what is the degree of a tree?

55
00:04:32,310 --> 00:04:38,700
From the tree, we cannot tell what is the degree degree is a pretty target, but if you observe what

56
00:04:38,700 --> 00:04:45,270
is the maximum degree of any normal three three say this having three degrees, this is having three

57
00:04:45,270 --> 00:04:45,540
degree.

58
00:04:46,080 --> 00:04:49,740
So the degree of a tree can be minimum three.

59
00:04:51,240 --> 00:04:58,710
Minimum, at least, it is three or it is more than three also, so I can explain you afterwards, then

60
00:04:58,710 --> 00:05:05,880
coming to internal and external laws, internal and external laws are also Carlysle and only if and

61
00:05:05,880 --> 00:05:12,810
leaf nodes are criminal laws for non terminal or what are those nodes with a degree zero or less leaf

62
00:05:12,810 --> 00:05:14,030
nodes degree.

63
00:05:14,100 --> 00:05:14,910
Zero degrees.

64
00:05:14,910 --> 00:05:16,090
Zero degrees zero.

65
00:05:16,500 --> 00:05:20,100
So E, m, k, g, c and or I.

66
00:05:20,250 --> 00:05:27,000
All these are leaf norms or external nodes or criminal laws.

67
00:05:27,450 --> 00:05:35,670
And those laws whose degrees are greater than zero are internal nodes, nonbelief n terminal noncriminals.

68
00:05:36,300 --> 00:05:45,600
Next levels C level of a tree starts from one on word that is root is at level one, dunnarts children

69
00:05:45,600 --> 00:05:50,730
at level two and their children are level three and four and so on.

70
00:05:52,500 --> 00:05:54,720
So level start from one onwards.

71
00:05:54,900 --> 00:05:58,110
So actually, for level, we count Nords.

72
00:05:59,650 --> 00:06:05,070
Here, one more horizontal, if you see there is one node, then at this level, if you come closer

73
00:06:05,110 --> 00:06:07,810
to NRK, either you can't destroy or destroy.

74
00:06:07,960 --> 00:06:09,250
We don't control rooms.

75
00:06:09,850 --> 00:06:10,680
This is the part.

76
00:06:10,780 --> 00:06:16,570
But in the past, we will come to north until here the part from road that will take three nodes.

77
00:06:16,570 --> 00:06:17,420
One, two, three.

78
00:06:17,770 --> 00:06:20,810
Either you come here or you come here anywhere.

79
00:06:20,860 --> 00:06:24,520
At this level, there are three nodes, one, two, three nodes done.

80
00:06:24,520 --> 00:06:27,450
At this level there are four nodes, one, two, three, four nodes.

81
00:06:27,940 --> 00:06:31,810
So we take the part by taking a number of nodes.

82
00:06:31,810 --> 00:06:33,760
So that's how this levels.

83
00:06:35,740 --> 00:06:42,820
The next is height of offertory, tree, height of a travel market here on this side route is that high

84
00:06:42,820 --> 00:06:48,250
at zero and its children are high one, then height to then height three and four.

85
00:06:50,950 --> 00:06:55,180
See, here we go, round edges in here.

86
00:06:55,210 --> 00:07:01,450
There are no edges, not if you come down to this next level or let us say level only or part, either

87
00:07:01,450 --> 00:07:04,690
you go here or here or here, then how many are just out there?

88
00:07:04,810 --> 00:07:06,760
One, just one or just there.

89
00:07:06,790 --> 00:07:11,770
So this is taking one edge to reach here, one inch to reach here at this horizontally at this level

90
00:07:11,890 --> 00:07:16,290
to decide if for one to two it until here, three or four inches.

91
00:07:17,020 --> 00:07:21,190
So level starts from one onwards and the height starts from zero onwards.

92
00:07:21,490 --> 00:07:22,560
Higher level.

93
00:07:22,570 --> 00:07:27,970
Both are useful for analysis not coming to the last term, that is forest.

94
00:07:28,690 --> 00:07:32,740
A collection of trees is called as a forest, for example.

95
00:07:32,980 --> 00:07:34,360
Here I will make a change.

96
00:07:34,810 --> 00:07:37,020
I have removed a root.

97
00:07:37,030 --> 00:07:37,600
I have removed.

98
00:07:37,810 --> 00:07:39,280
How many trees do you see?

99
00:07:39,760 --> 00:07:40,480
This is one.

100
00:07:40,870 --> 00:07:46,000
Then this is just one single node tree and this is another two total three trees are there.

101
00:07:47,620 --> 00:07:52,720
So this is a forest collection of trees of the forest, and if at all you want to make it as a tree

102
00:07:52,720 --> 00:07:57,760
by combining all the forest, then you can introduce a node like this.

103
00:07:58,450 --> 00:08:01,140
So here a forest is converted to a tree.

104
00:08:02,380 --> 00:08:04,270
So forest collection of trees.

105
00:08:04,750 --> 00:08:06,500
So that's all the terminology.

106
00:08:06,750 --> 00:08:10,360
Now I will introduce you to a binary tree.

107
00:08:11,140 --> 00:08:13,600
Now let us learn about Binary Tree.

108
00:08:14,470 --> 00:08:18,190
Our tree offer degree to degree humans.

109
00:08:18,580 --> 00:08:23,770
Every node can have maximum two children, not more than two children.

110
00:08:24,310 --> 00:08:28,890
It can have less than two children, but not more than two children.

111
00:08:29,230 --> 00:08:32,830
So degree of a tree must be two.

112
00:08:33,669 --> 00:08:38,220
If the degree is a two, then it is called binary tree by two.

113
00:08:38,500 --> 00:08:39,340
So two children.

114
00:08:39,580 --> 00:08:47,440
So every node can have two children, is having B and C, B is having A, B and E likewise C having

115
00:08:47,440 --> 00:08:52,020
F and G as there are only two children, we will give the names to the children.

116
00:08:52,360 --> 00:08:57,080
This is left child and the social right child so far be the left and the right.

117
00:08:58,000 --> 00:08:59,920
So we give names left children.

118
00:08:59,950 --> 00:09:08,230
Okay, one more definition of a binary tree that every node can have either zero children or one child

119
00:09:08,500 --> 00:09:10,240
or at most two children.

120
00:09:10,480 --> 00:09:15,670
So children can be either zero, one or two, not more than two.

121
00:09:16,600 --> 00:09:19,840
Let us look at some examples of binary tree.

122
00:09:20,740 --> 00:09:22,510
I will tell somebody trees.

123
00:09:22,750 --> 00:09:24,010
I will not label them.

124
00:09:24,010 --> 00:09:31,180
This is labeling ABCDE just I will draw dominoes and look at the shape and find out whether it is a

125
00:09:31,270 --> 00:09:32,380
binary tree or not.

126
00:09:33,100 --> 00:09:34,960
Here I have some binary trees.

127
00:09:35,140 --> 00:09:37,570
First one is a binary tree.

128
00:09:39,630 --> 00:09:41,040
This is not having any children.

129
00:09:41,400 --> 00:09:45,100
Yes, every note can have zero one or two children.

130
00:09:45,540 --> 00:09:47,260
How many children selling to children?

131
00:09:47,310 --> 00:09:48,110
OK, perfect.

132
00:09:48,390 --> 00:09:49,070
How many children?

133
00:09:49,080 --> 00:09:51,770
Zero percent to children.

134
00:09:51,810 --> 00:09:52,320
Perfect.

135
00:09:52,500 --> 00:09:52,950
Zero.

136
00:09:52,950 --> 00:09:53,270
Zero.

137
00:09:53,700 --> 00:09:54,630
Everything is perfect.

138
00:09:55,350 --> 00:09:56,220
What about this?

139
00:09:57,870 --> 00:10:00,420
This not one child is perfect.

140
00:10:00,570 --> 00:10:01,740
Zero, one or two.

141
00:10:01,800 --> 00:10:03,810
Remember this this not one child.

142
00:10:03,930 --> 00:10:04,620
Okay, perfect.

143
00:10:04,630 --> 00:10:06,670
This is not perfect.

144
00:10:07,230 --> 00:10:08,910
This is also binary tree.

145
00:10:09,180 --> 00:10:13,250
And this type of tree is called left school tree.

146
00:10:14,160 --> 00:10:16,060
So this is left skewed by military.

147
00:10:16,350 --> 00:10:19,920
And this one, there's another one I just put on supposition here.

148
00:10:20,280 --> 00:10:21,780
That is another one third one.

149
00:10:21,960 --> 00:10:23,670
And that is also binary tree.

150
00:10:23,670 --> 00:10:24,690
That is right.

151
00:10:24,690 --> 00:10:28,140
Skewed binary tree, whatever this one.

152
00:10:29,340 --> 00:10:30,090
How many children?

153
00:10:30,360 --> 00:10:30,810
Three.

154
00:10:30,960 --> 00:10:32,460
No, this is not a bunny.

155
00:10:32,970 --> 00:10:34,760
So then you say it's not a bunny tree.

156
00:10:34,980 --> 00:10:40,730
If any note is having more than two children, then you say it's not a anything less than two children.

157
00:10:40,740 --> 00:10:41,590
It's not a problem.

158
00:10:42,720 --> 00:10:43,980
So this is about this.

159
00:10:44,000 --> 00:10:46,380
Apparently, this is not a binary tree.

160
00:10:46,690 --> 00:10:48,150
This one is not a pine tree.

161
00:10:48,690 --> 00:10:49,770
Then what about 51?

162
00:10:50,940 --> 00:10:55,600
So this tree just rotated with Samangan, the same as this one.

163
00:10:56,380 --> 00:10:56,790
Yes.

164
00:10:57,030 --> 00:11:04,250
We draw the tree like this also instead of growing Left-to-right, the child we draw down then.

165
00:11:04,410 --> 00:11:05,400
Right, child?

166
00:11:06,710 --> 00:11:13,010
So this ship is also possible, so we draw a tree like this once, so binary are more commonly used.

167
00:11:13,040 --> 00:11:14,600
There are various forms of pine.

168
00:11:14,960 --> 00:11:16,330
We'll be learning them slowly.

169
00:11:16,820 --> 00:11:19,100
So in this video, we have finished the topic.

170
00:11:19,110 --> 00:11:23,270
I have discussed the terminology and introduction to buy Natalee's.

