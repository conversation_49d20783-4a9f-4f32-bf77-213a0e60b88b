1
00:00:00,890 --> 00:00:10,100
And this way, we will see what the methods of creating that stack are inside heap, we have already

2
00:00:10,100 --> 00:00:10,640
seen this.

3
00:00:10,640 --> 00:00:13,530
We can create an alibi just mentioning the size.

4
00:00:13,560 --> 00:00:15,740
So this will be created inside stack.

5
00:00:17,720 --> 00:00:23,600
Next, for creating an arena here, we must have a pointer to that point that we can create an arena

6
00:00:23,600 --> 00:00:26,840
heap and assign the address of that, <PERSON>, to that point.

7
00:00:27,440 --> 00:00:30,530
So here I have right on the code be assigned for.

8
00:00:33,110 --> 00:00:36,560
Allocating memory, and he the function is mellark function.

9
00:00:37,400 --> 00:00:40,580
And suppose I want to create five integers so we know that.

10
00:00:43,560 --> 00:00:50,530
So five into size of integer, we can use an the size of integer because here in this compiler it is

11
00:00:50,550 --> 00:00:51,970
taking four bytes.

12
00:00:51,970 --> 00:00:55,410
So whatever the number of bytes the compiler is taking, it will take that many bytes.

13
00:00:56,660 --> 00:01:02,590
This Marlock function will return with the type of pointer, so it must be typed, dusted before use.

14
00:01:02,750 --> 00:01:05,720
So I will typecast it as integer type.

15
00:01:06,530 --> 00:01:08,360
This will create an array in heap.

16
00:01:09,480 --> 00:01:13,740
Here, I will change the addresses to five and I will initialized with some numbers.

17
00:01:16,010 --> 00:01:18,750
Two, four, six, eight and 10.

18
00:01:19,550 --> 00:01:24,430
Now, this is what I have created, just not using heat, I will initialize this urry.

19
00:01:25,660 --> 00:01:28,210
That is P of zero, that three.

20
00:01:30,430 --> 00:01:36,970
I will assign all numbers now here I will take one integer variable for displaying the value, so I

21
00:01:36,970 --> 00:01:41,380
will display the values of array as well as arizpe using for loop.

22
00:01:41,800 --> 00:01:44,950
So first for loop, I will print a.

23
00:02:03,830 --> 00:02:08,300
Yeah, the follow up I'm reading all the elements of it from zero to four.

24
00:02:10,889 --> 00:02:17,670
Same followed by be here to print the dynamic that is pointed by so P of I hear.

25
00:02:20,360 --> 00:02:28,550
Between these two, I just want a new line, so bring Afghan so the results are displayed one after

26
00:02:28,550 --> 00:02:29,070
another.

27
00:02:29,820 --> 00:02:35,250
Next thing is, I'm getting a warning here that is unable to find a definition of mellark function,

28
00:02:35,250 --> 00:02:40,020
so for that I should include a header file that is steadily edge.

29
00:02:44,640 --> 00:02:46,740
The function is available in this report.

30
00:02:47,070 --> 00:02:48,060
So this is gone.

31
00:02:52,460 --> 00:02:58,040
I will run the program and show you these are the two followups used for printing the values of the

32
00:02:58,040 --> 00:03:02,930
NRA to conform, the ideas are created and they are holding those values.

33
00:03:08,110 --> 00:03:13,630
Yeah, you can see that two four, six eight are the values that are kept in three and three five seven

34
00:03:13,630 --> 00:03:14,210
nine 11.

35
00:03:14,230 --> 00:03:17,860
These are the values that I have assigned them to be.

36
00:03:20,170 --> 00:03:27,030
So in our programs, whenever we want to allocate money in, he then does the method using Midlake function,

37
00:03:27,040 --> 00:03:28,450
you can educate member and he.

38
00:03:29,620 --> 00:03:35,290
I will put a break point here and debug that program and show you as here is a breakpoint on the written

39
00:03:35,290 --> 00:03:37,130
statement and then I will run the program.

40
00:03:37,870 --> 00:03:39,850
Let us see how these race looks like.

41
00:03:40,240 --> 00:03:47,830
See, this array is having values two, four, seven, eight arrays, having values, two, four, six,

42
00:03:47,830 --> 00:03:49,550
eight, and it is of size five.

43
00:03:49,570 --> 00:03:53,800
So here in the brackets, again, size five, then P is just a pointer.

44
00:03:53,800 --> 00:03:58,030
It's not an array at this point that is having so and so address the status.

45
00:03:58,480 --> 00:04:04,150
Then the P is pointing on the first element, as I have shown you on the board whiteboard, that pointer

46
00:04:04,150 --> 00:04:05,470
really pointing on the first element.

47
00:04:05,470 --> 00:04:08,260
But using that pointer you can access all of the elements.

48
00:04:08,280 --> 00:04:09,540
So this is P of zero.

49
00:04:09,550 --> 00:04:12,400
That is three and plus one.

50
00:04:12,400 --> 00:04:17,320
If you say you can access this one or B of two be of three or four, we can access all these elements

51
00:04:17,320 --> 00:04:18,519
with the help of this pointer.

52
00:04:19,329 --> 00:04:22,029
So it's showing only one value when it is different.

53
00:04:22,040 --> 00:04:24,130
So Starboy gives value three.

54
00:04:26,750 --> 00:04:33,230
The edges of the area in heaps given here in the hexadecimal form, the hexadecimal number.

55
00:04:35,670 --> 00:04:40,110
So that's all this is the demonstration of creating a in stock as well as in he.

