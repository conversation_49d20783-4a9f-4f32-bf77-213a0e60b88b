1
00:00:01,410 --> 00:00:05,380
Now, let us look at internal laws versus external laws.

2
00:00:05,400 --> 00:00:11,340
That is that is not only known for six leaf nodes of a strict binary tree.

3
00:00:12,360 --> 00:00:16,880
So for explanation, all of you have become some examples, celebrities, let us look at them.

4
00:00:17,960 --> 00:00:25,250
So this strict monitoring, how many internal laws, one and two to internal NSA, how many external

5
00:00:25,250 --> 00:00:25,660
laws?

6
00:00:25,670 --> 00:00:26,480
One, two, three.

7
00:00:26,840 --> 00:00:27,830
These are leaf notes.

8
00:00:29,400 --> 00:00:34,800
And this strict binary number of internal laws, one, two, three, external nodes, one, two, three,

9
00:00:34,800 --> 00:00:35,150
four.

10
00:00:35,460 --> 00:00:37,040
So four leaf nodes are there.

11
00:00:37,770 --> 00:00:42,990
And in this one, internal laws, one, two, three, four, five internal laws are five.

12
00:00:43,530 --> 00:00:48,690
Then how many external nodes, leaf nodes, one, two, three, four, five, six, six.

13
00:00:50,240 --> 00:00:56,870
So from these examples, we can see that internal laws are, to an extent a law three, this is three,

14
00:00:56,870 --> 00:01:01,190
so this is four and if it is five, then this is six.

15
00:01:01,760 --> 00:01:09,320
So leave no aside one greater than non-lethal or external laws or one greater than internal laws so

16
00:01:09,320 --> 00:01:13,690
we can come up with the formula is equal to EI plus one.

17
00:01:14,090 --> 00:01:21,760
Yes, this formula is always true in strict binary trees and in stigmata into this formula is more important.

18
00:01:22,190 --> 00:01:23,750
That is a number of internal laws.

19
00:01:23,750 --> 00:01:25,790
Plus one is equal to the more fictional the North.

