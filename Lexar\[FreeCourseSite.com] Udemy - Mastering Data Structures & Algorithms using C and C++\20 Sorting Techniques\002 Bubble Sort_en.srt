1
00:00:00,210 --> 00:00:04,590
The topic is bubble thought, so in this video, we learn about bubbles.

2
00:00:05,460 --> 00:00:08,740
I will show you how it looks up on this list of elements.

3
00:00:09,090 --> 00:00:10,770
Then also I will analyze.

4
00:00:11,370 --> 00:00:11,910
Let us see.

5
00:00:11,910 --> 00:00:12,590
It's working.

6
00:00:12,900 --> 00:00:19,080
I have a list of elements, array of elements, five elements are there in and out now to understand

7
00:00:19,080 --> 00:00:19,940
how it works.

8
00:00:19,950 --> 00:00:22,350
I will draw this vertically.

9
00:00:22,770 --> 00:00:26,400
So I will take these elements vertically and are zero.

10
00:00:26,400 --> 00:00:27,360
One, two, three, four.

11
00:00:27,360 --> 00:00:28,570
I don't need this.

12
00:00:29,890 --> 00:00:31,470
Without any source, we'll work on it.

13
00:00:32,560 --> 00:00:33,460
So let us stop.

14
00:00:34,540 --> 00:00:39,850
So it will compare a conservative pair of elements every time, if the first element is greater than

15
00:00:39,850 --> 00:00:45,310
the second element, then it will interchange the element, said we'll compare these to winning eight

16
00:00:45,310 --> 00:00:46,330
is greater than five.

17
00:00:46,510 --> 00:00:48,430
So five will move up.

18
00:00:48,850 --> 00:00:49,940
It will come down.

19
00:00:50,500 --> 00:00:52,280
The remaining elements as it is.

20
00:00:52,930 --> 00:00:56,850
So this was the first competition and the first element was found greater.

21
00:00:57,130 --> 00:00:59,710
So it has changed continuum.

22
00:00:59,860 --> 00:01:03,760
Second comparison, eight and seven eight is greater than seven.

23
00:01:03,770 --> 00:01:06,100
So five seven comes up.

24
00:01:06,580 --> 00:01:08,350
Eight moves down.

25
00:01:08,830 --> 00:01:09,760
Three to.

26
00:01:10,980 --> 00:01:16,050
The third comparison with the next element, so these are conservative elements or it will compare them

27
00:01:16,860 --> 00:01:29,390
five seven three eight two, because it was great to then compare these two five seven three to eight.

28
00:01:29,700 --> 00:01:35,640
So I have compared all the pain off elements, total five elements are there and for possible pasada

29
00:01:35,640 --> 00:01:35,670
there.

30
00:01:35,670 --> 00:01:38,430
So we have compared all possible pairs of elements.

31
00:01:38,970 --> 00:01:39,310
Right.

32
00:01:39,660 --> 00:01:44,940
So when all elements are compared once, then this is called sparse.

33
00:01:46,020 --> 00:01:49,920
Parchments, we are going through the list, so this is the first time we are going through.

34
00:01:49,950 --> 00:01:51,210
So this is FastPass.

35
00:01:52,610 --> 00:01:54,450
What is the result of the first pass?

36
00:01:54,860 --> 00:02:02,460
One element is certain which element, the largest element is sodic, heaviest element, the socket

37
00:02:02,930 --> 00:02:04,820
that eight assorted in the list.

38
00:02:05,510 --> 00:02:08,320
So we have done one, two, three, four competitions.

39
00:02:08,630 --> 00:02:10,940
So we have performed four competitions.

40
00:02:12,930 --> 00:02:18,210
So there are five elements we have performed for competition, that is if there are any elements, then

41
00:02:18,210 --> 00:02:22,560
we have performed and minus one competition, then how many times swaps are done?

42
00:02:23,310 --> 00:02:25,170
Every competition was done.

43
00:02:25,170 --> 00:02:26,550
One, two, three, four.

44
00:02:26,580 --> 00:02:27,680
This is the final result.

45
00:02:27,990 --> 00:02:29,770
So for swaps are uncertain.

46
00:02:30,330 --> 00:02:33,990
Now, all elements are not sort of just one element that is eight.

47
00:02:34,350 --> 00:02:37,110
Then I have to continue and perform second parts.

48
00:02:37,560 --> 00:02:40,440
So I will take this list of elements once again.

49
00:02:41,810 --> 00:02:48,470
I perform second pass first compare these two elements, see, first one is not greater than the second

50
00:02:48,470 --> 00:02:52,510
one, so they are not slack dreaming as it is.

51
00:02:52,880 --> 00:02:55,810
So if it is greater, we will slap the next bear.

52
00:02:56,300 --> 00:02:57,200
Seven and three.

53
00:02:57,440 --> 00:02:58,470
Seven is greater.

54
00:02:58,490 --> 00:03:08,550
So five three, seven to eight, then seven with the two five three as it is a seven is greater.

55
00:03:08,550 --> 00:03:11,570
So slap it to seven and eight.

56
00:03:12,610 --> 00:03:20,310
Then seven and eight, if I compare also no use because eight is already succored, I can avoid that

57
00:03:20,320 --> 00:03:21,100
competition.

58
00:03:21,370 --> 00:03:25,870
So total two elements are sorted now, seven and eight.

59
00:03:27,330 --> 00:03:33,960
The largest element they got, how many competitions they have performed, three competitions have performed,

60
00:03:34,560 --> 00:03:37,710
then how many swabs are done just to slap?

61
00:03:38,100 --> 00:03:44,460
Only two slaps are performed because here the number was a smaller than seven at most.

62
00:03:44,460 --> 00:03:46,050
How many slots are possible?

63
00:03:46,050 --> 00:03:47,310
Three sides are possible.

64
00:03:47,610 --> 00:03:54,710
So I really don't sense this is the maximum number of fans, not the actual slots.

65
00:03:54,790 --> 00:03:56,170
That has happened.

66
00:03:56,170 --> 00:03:57,420
The maximum.

67
00:03:57,870 --> 00:03:58,660
And here.

68
00:03:59,830 --> 00:04:01,510
Now, this was the second bus.

69
00:04:05,430 --> 00:04:06,730
Two buses are completed.

70
00:04:07,020 --> 00:04:13,310
How many elements we got started, two elements, guards, forty eight and now seven on seven and eight

71
00:04:13,320 --> 00:04:13,800
are on it.

72
00:04:14,220 --> 00:04:19,980
Now I will take the elements once again, continue with this list and perform one more bus, five,

73
00:04:19,980 --> 00:04:22,640
three to seven and eight.

74
00:04:23,250 --> 00:04:25,100
Compare these to five years later.

75
00:04:25,410 --> 00:04:31,100
So three goes up, five comes down to and seven and eight the Dompierre, five with the two.

76
00:04:31,590 --> 00:04:32,730
So five is greater.

77
00:04:32,760 --> 00:04:35,640
So two and five, seven and eight.

78
00:04:36,540 --> 00:04:43,500
That's all stop because if you compare five to the seven on five était also use the remaining as it

79
00:04:43,500 --> 00:04:45,460
is now, three elements are sorted.

80
00:04:45,840 --> 00:04:48,480
How many competitions do comparisons.

81
00:04:49,050 --> 00:04:50,070
How many slaps?

82
00:04:50,070 --> 00:04:51,630
Maximum two slaps.

83
00:04:51,650 --> 00:04:56,160
Yes, this was the third box I will perform one more pass.

84
00:04:56,550 --> 00:05:00,030
Three, two, five, seven, eight.

85
00:05:00,570 --> 00:05:06,000
Compare these two threes greater so two three five seven eight.

86
00:05:06,570 --> 00:05:07,110
That's it.

87
00:05:07,800 --> 00:05:10,610
Comparing three with five is of no use because all it is.

88
00:05:10,990 --> 00:05:13,690
So I'm not comparing with those elements which are already sorted.

89
00:05:13,950 --> 00:05:16,620
So right now how many elements are sorted.

90
00:05:16,890 --> 00:05:21,480
I have sorted one, two, three, four elements, but there is only one element left.

91
00:05:21,480 --> 00:05:22,560
So that is also sorted.

92
00:05:22,560 --> 00:05:24,170
So this whole list is sorted.

93
00:05:24,750 --> 00:05:26,340
How many competitions we perform?

94
00:05:26,670 --> 00:05:28,080
Only one competition.

95
00:05:28,260 --> 00:05:31,200
How many slaps are done, one slap is done.

96
00:05:32,280 --> 00:05:33,990
That follows the list of sodic.

97
00:05:34,410 --> 00:05:35,700
So this is the working of doubles.

98
00:05:35,700 --> 00:05:37,410
Start using five elements.

99
00:05:37,410 --> 00:05:41,760
I have explained you let us analyse this one and study each and every of this one.

100
00:05:42,060 --> 00:05:47,070
First of all, how many of I have performed total for passes?

101
00:05:47,490 --> 00:05:49,620
One, two, three, four.

102
00:05:49,800 --> 00:05:51,060
I have performed for parties.

103
00:05:51,060 --> 00:05:53,550
If I separate them like this, you can see them clearly.

104
00:05:54,240 --> 00:05:58,200
I have performed total four passes and how many elements I have.

105
00:05:58,410 --> 00:06:01,650
I did not write that I have a five element.

106
00:06:01,680 --> 00:06:07,740
So as a five see, actually when we are doing analysis, we should do it for elements, elements, whatever

107
00:06:07,740 --> 00:06:08,580
the number of elements.

108
00:06:08,580 --> 00:06:12,900
Maybe in our example we have only five elements so far, five elements.

109
00:06:12,900 --> 00:06:15,510
Four passes are that then four and elements.

110
00:06:15,510 --> 00:06:18,770
How many passes will be there and the minus one will be there.

111
00:06:18,960 --> 00:06:23,310
So this is and minus one passes.

112
00:06:24,980 --> 00:06:26,340
Yes, this is perfect.

113
00:06:26,810 --> 00:06:30,500
Then let us find out number of competitions.

114
00:06:32,700 --> 00:06:38,040
So if I start from here, four, three, two, one, if I come from that side, one, two, three,

115
00:06:38,040 --> 00:06:44,190
four, so total comparisons are one here then plus two then plus three, then plus four, one plus two,

116
00:06:44,190 --> 00:06:47,550
plus three plus four, then four elements.

117
00:06:47,550 --> 00:06:48,060
How many.

118
00:06:48,600 --> 00:06:56,490
One plus two plus three plus it goes on to see for five it was up to four so four and it will be in

119
00:06:56,490 --> 00:06:57,300
the minus one.

120
00:06:58,410 --> 00:07:02,500
This is some of the national numbers up to and minus one.

121
00:07:02,520 --> 00:07:06,260
So this is an end to end, minus one by two.

122
00:07:06,570 --> 00:07:09,600
So this is all the rough and square.

123
00:07:09,810 --> 00:07:10,230
See this?

124
00:07:10,230 --> 00:07:12,680
I got a formula formalized polynomial.

125
00:07:13,020 --> 00:07:17,780
What is the degree of polynomial and square thyroiditis outdraw and square.

126
00:07:18,120 --> 00:07:21,030
Then next is number of slots.

127
00:07:22,370 --> 00:07:28,760
How many swabs are performed, see, actually, this is maximum number of swabs right here, you can

128
00:07:28,760 --> 00:07:33,140
observe that there were only two swabs, but I rode three, so three where possible.

129
00:07:33,170 --> 00:07:35,270
So I'm writing maximum number of swabs.

130
00:07:35,660 --> 00:07:41,060
So maximum how many swabs are there again, from their effect on one plus two plus three plus four.

131
00:07:41,270 --> 00:07:47,090
So one plus two plus three plus four four elements.

132
00:07:47,090 --> 00:07:50,740
One plus two plus three plus goes on two and minus one.

133
00:07:51,080 --> 00:07:54,030
Then this is all and then two and minus one by two.

134
00:07:54,230 --> 00:07:57,220
So this is also outdraw and square.

135
00:07:57,890 --> 00:08:06,050
See a number of competitions are also in square number of swabs are also and square C from the criteria.

136
00:08:06,050 --> 00:08:08,080
I have checked the two things.

137
00:08:08,090 --> 00:08:15,500
How many comparisons, how many slaps and the time complexity of any sorting algorithm is given based

138
00:08:15,500 --> 00:08:16,870
on a number of competitions.

139
00:08:17,180 --> 00:08:24,950
So the time taken by this algorithm and square now of five criteria that we have seen in our first video

140
00:08:24,950 --> 00:08:31,640
of the section, I have complete two criteria competitions and slots remaining.

141
00:08:31,640 --> 00:08:37,309
Three are there, whether it is adaptive or not, whether it is a stable or not, whether it is taking

142
00:08:37,309 --> 00:08:39,580
extra memory, these things we have to discuss.

143
00:08:40,549 --> 00:08:45,290
So before going to that analysis, let me discuss a few interesting thing about bubbles.

144
00:08:45,360 --> 00:08:52,720
OK, see, first one, why it is called the bubbles fault C, this was the list of elements.

145
00:08:52,730 --> 00:08:54,470
This was demonstrate this one.

146
00:08:54,890 --> 00:08:56,660
It is heaviest element.

147
00:08:56,990 --> 00:09:01,760
So if you drop it from here, it is going down and settling at the bottom.

148
00:09:02,060 --> 00:09:06,740
And the other elements like two, three, seven, five, they are lighter elements.

149
00:09:06,740 --> 00:09:08,300
So they are slowly moving up.

150
00:09:08,510 --> 00:09:09,530
So they are moving up.

151
00:09:10,770 --> 00:09:17,490
So the effect is just like if you throw a stone in water, then stone is heavier, so it will go and

152
00:09:17,490 --> 00:09:21,350
said let the better than the bubbles which are like that will come up.

153
00:09:22,540 --> 00:09:29,470
So that's why the name of this artist bubble, so the effect is just like bubbles in water when this

154
00:09:29,470 --> 00:09:30,880
heavy thing is thrown in water.

155
00:09:31,540 --> 00:09:37,120
I don't know who observed this and given the name, but this is what the observation based on which

156
00:09:37,120 --> 00:09:38,920
the name bubbles are just given.

157
00:09:40,150 --> 00:09:46,960
This is the name one more interesting thing in one pass, I got the largest element, then the second

158
00:09:46,960 --> 00:09:49,180
part, I got to love this element.

159
00:09:49,570 --> 00:09:53,640
Third pass, three largest Salema for so many elements.

160
00:09:53,650 --> 00:09:55,940
If we perform K passes, then what?

161
00:09:55,940 --> 00:09:57,370
They get killed.

162
00:09:57,400 --> 00:09:59,190
Largest elements, they get killed.

163
00:09:59,200 --> 00:10:00,130
Largest element.

164
00:10:01,770 --> 00:10:07,800
Suppose you have a list and you don't want to sort everything, just see one, which is the greatest.

165
00:10:09,430 --> 00:10:15,730
This performance pass, I want FUSA three Gradus, no perform, three passes and stop.

166
00:10:16,690 --> 00:10:23,830
So you can get the help of this procedure, this algorithm for performing selected number of passes

167
00:10:24,250 --> 00:10:27,270
to get the desired result or required Rezac.

168
00:10:27,860 --> 00:10:33,910
I will remove these things, then I will just write on a piece of code chewing bubble, start working

169
00:10:33,910 --> 00:10:35,740
of bubble, sort of remove the.

170
00:10:36,550 --> 00:10:37,540
I have a function.

171
00:10:37,540 --> 00:10:39,070
Probably thought so.

172
00:10:39,070 --> 00:10:40,800
What is the work we are doing in this one?

173
00:10:41,140 --> 00:10:44,460
We are performing and the minus one passes.

174
00:10:45,340 --> 00:10:50,670
So we have to repeat something that is process four and minus one time.

175
00:10:51,010 --> 00:10:52,720
So I will write on for loop directly.

176
00:10:52,960 --> 00:10:57,120
This is what passes for ISIS zero I lasdun and the minus one.

177
00:10:57,130 --> 00:10:59,710
If there are any elements then minus one passes.

178
00:10:59,710 --> 00:11:00,540
We have to perform.

179
00:11:01,180 --> 00:11:08,170
So this will be looking for in minus one time that in each pass what I have to do, I have to compare

180
00:11:08,170 --> 00:11:09,970
this element with the next element.

181
00:11:10,180 --> 00:11:16,840
So if I said this is G, then compared with G plus one, then again bringing G here and G plus one,

182
00:11:16,840 --> 00:11:18,650
then bring it here, then the plus one.

183
00:11:19,000 --> 00:11:24,150
So I need one more loop for comparing all the elements, consecutive pairs of elements every time.

184
00:11:24,460 --> 00:11:31,630
So I would take one more for loop for G not for gasline zero G less than N minus one G plus plus.

185
00:11:31,650 --> 00:11:34,420
So this will be moving along this elements.

186
00:11:35,440 --> 00:11:40,690
Here also wrote and minus one, because if there are five elements, then I should perform only four

187
00:11:40,690 --> 00:11:41,440
combinations.

188
00:11:41,470 --> 00:11:42,360
Yes, correct.

189
00:11:43,090 --> 00:11:47,020
But one moving in the next pass, one company introduced.

190
00:11:48,710 --> 00:11:53,960
Then again, next, postulant competition should reduce soil in every possible competition should reduce,

191
00:11:54,260 --> 00:11:56,960
so not just and minus one minus.

192
00:11:56,960 --> 00:12:00,800
I also say it means first time in FastPass zero.

193
00:12:01,070 --> 00:12:02,330
So nothing is subtracted.

194
00:12:02,720 --> 00:12:04,320
Next pass I will be one.

195
00:12:04,350 --> 00:12:05,560
So one will be subtracted.

196
00:12:05,600 --> 00:12:07,220
So this becomes a minus two.

197
00:12:08,120 --> 00:12:16,640
So G minus and minus one minus eight Y because in every possible competition should reduce next what

198
00:12:16,640 --> 00:12:17,930
I should do inside the slope.

199
00:12:18,140 --> 00:12:24,140
I should compare it often with the object plus one if the object is greater than flat.

200
00:12:24,560 --> 00:12:27,740
So here check if I have written the statement.

201
00:12:28,650 --> 00:12:35,060
If Iraq is greater than the London, then swap them Slatkin's three statements have Greydon, instead

202
00:12:35,100 --> 00:12:37,180
of writing three statement, I just wrote SLAPP.

203
00:12:38,080 --> 00:12:40,740
So subelements the objectivity of G plus one.

204
00:12:40,920 --> 00:12:41,350
That's it.

205
00:12:41,400 --> 00:12:43,530
So logic is very simple, formable thought.

206
00:12:43,770 --> 00:12:48,660
And you can see that there are two nested for loops for Loop is dead and inside that one would fall.

207
00:12:49,410 --> 00:12:55,470
So from the code, if you see if you have to follow that are nested, then the Times and Square and

208
00:12:55,470 --> 00:12:57,930
both are working based on and only and.

209
00:13:00,140 --> 00:13:04,040
So times are rough and square from the court also, you can see this one.

210
00:13:05,830 --> 00:13:06,370
Next.

211
00:13:08,610 --> 00:13:10,380
So I have written the code now.

212
00:13:10,410 --> 00:13:14,310
Next thing, let us understand whether it is adoptive or not.

213
00:13:15,620 --> 00:13:21,260
So for that, I will take a list which is already sorted, so this list is already sorted.

214
00:13:23,590 --> 00:13:31,360
Right, let us performable forward on this one and we will perform first pass, first pass, the first

215
00:13:31,380 --> 00:13:35,180
pass companies to eliminate swopping as required.

216
00:13:35,340 --> 00:13:37,610
No, there's not a greater right.

217
00:13:37,610 --> 00:13:37,620
Right.

218
00:13:37,830 --> 00:13:39,000
Human societies.

219
00:13:39,580 --> 00:13:41,190
Let's compare these two elements.

220
00:13:41,310 --> 00:13:42,420
Is it greater than this one?

221
00:13:42,450 --> 00:13:42,750
No.

222
00:13:43,170 --> 00:13:45,960
So I write down the elements as it is.

223
00:13:47,080 --> 00:13:52,020
Can you give me this competition and slapping where it is happening in order for loquat in the follow

224
00:13:52,030 --> 00:13:52,210
up?

225
00:13:52,990 --> 00:13:54,590
Yes, it is happening inside in the.

226
00:13:55,360 --> 00:13:56,710
This is G c.

227
00:13:56,980 --> 00:13:58,480
This was G was here.

228
00:13:58,870 --> 00:13:59,890
So g.G plus one.

229
00:14:00,130 --> 00:14:02,070
Next time J was here didn't he.

230
00:14:02,080 --> 00:14:02,540
Plus one.

231
00:14:02,560 --> 00:14:08,370
So this happening inside this loop in the fall and this condition is not true.

232
00:14:08,710 --> 00:14:11,500
This condition has a field for Stemmons.

233
00:14:11,500 --> 00:14:14,500
So the condition was false and also condition is false.

234
00:14:14,800 --> 00:14:16,730
That again, check next.

235
00:14:16,750 --> 00:14:18,280
So G should come on this one.

236
00:14:18,280 --> 00:14:18,630
Right.

237
00:14:18,760 --> 00:14:20,050
So this should come on this one.

238
00:14:20,380 --> 00:14:21,400
Compare these two.

239
00:14:21,670 --> 00:14:26,560
So these two are converging plus one plus one is a greater no.

240
00:14:26,740 --> 00:14:28,360
So clopping will not happen.

241
00:14:28,690 --> 00:14:34,270
So elements are as it is the Next G is here on this one.

242
00:14:34,990 --> 00:14:36,640
Compare these two then.

243
00:14:36,640 --> 00:14:37,840
Is it greater than this one.

244
00:14:37,840 --> 00:14:38,950
Is it greater than that one.

245
00:14:38,950 --> 00:14:39,260
No.

246
00:14:39,640 --> 00:14:41,290
So it will not show up.

247
00:14:41,290 --> 00:14:42,880
So the elements are as it is.

248
00:14:44,020 --> 00:14:47,560
So we have completed one pass and then the center pass.

249
00:14:47,890 --> 00:14:50,040
There was no swapping done at all.

250
00:14:50,290 --> 00:14:52,120
So it means inside this for loop.

251
00:14:52,330 --> 00:14:53,890
So that was never done.

252
00:14:54,160 --> 00:14:55,660
Five was not done at all.

253
00:14:55,660 --> 00:14:56,560
No, that was done.

254
00:14:56,830 --> 00:15:01,350
If no swapping is done means we can say that the elements were already sorted.

255
00:15:01,630 --> 00:15:03,570
That is the reason swapping is not done.

256
00:15:04,300 --> 00:15:04,930
Yes.

257
00:15:05,620 --> 00:15:06,160
Yes.

258
00:15:06,460 --> 00:15:13,540
We can identify that if the list is already sorted or not, if there is no swapping, then the list

259
00:15:13,540 --> 00:15:14,370
is already sorted.

260
00:15:14,680 --> 00:15:18,150
So for that I will take one variable called Slide.

261
00:15:20,300 --> 00:15:26,690
This is like variable, I will initialize it to zero before entering into a pass this luger's performing

262
00:15:26,690 --> 00:15:27,360
pass, right.

263
00:15:28,340 --> 00:15:29,960
This is for repeating the process.

264
00:15:29,960 --> 00:15:31,360
This loop is for competition.

265
00:15:31,480 --> 00:15:32,780
You just know what you have done all these.

266
00:15:32,830 --> 00:15:33,990
So this is happening in this one.

267
00:15:34,370 --> 00:15:37,760
So before starting this, make a flag at zero.

268
00:15:38,240 --> 00:15:42,350
And if any swap is happening, make flaggers one.

269
00:15:43,910 --> 00:15:51,450
There is some shopping done then also this for Loop, there is no space here, so I don't hear.

270
00:15:54,050 --> 00:16:02,030
Outside this Falu, outside this for loop check if a flag is still zero, means the swapping was never

271
00:16:02,030 --> 00:16:02,390
done.

272
00:16:02,400 --> 00:16:03,760
We can identify this.

273
00:16:04,340 --> 00:16:05,510
So here I will ride on.

274
00:16:05,990 --> 00:16:14,030
If the flag is still zero, then break or stop or return.

275
00:16:14,030 --> 00:16:14,750
Whatever you want.

276
00:16:14,750 --> 00:16:15,350
You can do it.

277
00:16:17,010 --> 00:16:22,950
Then closing off on the for loop, then closing a function so I can use one flag variable and make the

278
00:16:22,950 --> 00:16:29,280
flag at zero before every pass and inside this for loop, if swapping is done, then I will make flaggers

279
00:16:29,280 --> 00:16:33,770
one outside this for loop, outside this in the for loop.

280
00:16:34,050 --> 00:16:35,970
Once the past has finished check.

281
00:16:35,970 --> 00:16:42,510
If a flag is still zero, then something was not done and break brigman it will come out of this outer

282
00:16:42,510 --> 00:16:46,280
loop also and function stops also consistently written.

283
00:16:47,100 --> 00:16:51,530
So by introducing a flag we can identify whether there was any swopping in the past or not.

284
00:16:51,540 --> 00:16:55,140
If no swiping means list is already sodded.

285
00:16:56,000 --> 00:17:01,100
So this may happen in fastpass or second in any parts, it may happen whenever it is happening, it

286
00:17:01,100 --> 00:17:01,670
will stop.

287
00:17:02,540 --> 00:17:08,380
If the list is already sorted, then to stop after surpassingly no analysis.

288
00:17:08,690 --> 00:17:10,190
What is the time taken?

289
00:17:10,190 --> 00:17:14,930
If the list is already circuit, it is performing just one pass and it will break.

290
00:17:15,200 --> 00:17:17,839
So it must pass how many companies and on how many slots?

291
00:17:18,109 --> 00:17:19,490
Number of competitions.

292
00:17:19,490 --> 00:17:24,220
Ah, and the minus one slaps zero.

293
00:17:25,160 --> 00:17:27,170
So it's not performing any SLAPP zero.

294
00:17:27,180 --> 00:17:28,430
Perhaps so.

295
00:17:28,700 --> 00:17:30,130
And the minus one competition.

296
00:17:30,140 --> 00:17:32,360
And the time is how much outdraw an.

297
00:17:34,310 --> 00:17:42,290
So we can now say that bubble saw the time, minimum time taken by bubbles for the maximum time taken

298
00:17:42,290 --> 00:17:48,980
by bubbles, or this order of and square the sense that we have seen then and there is no we are looking

299
00:17:48,980 --> 00:17:49,970
at it is taking off.

300
00:17:50,180 --> 00:17:56,750
And then if the list is already started to bubble, it is having a minimum time also maximum also.

301
00:17:57,110 --> 00:18:01,070
So if the list is already sorted, then it is taking minimum time.

302
00:18:01,550 --> 00:18:03,080
So bubbles are adaptive.

303
00:18:03,590 --> 00:18:06,110
Yes, bubbles are adoptive.

304
00:18:07,350 --> 00:18:08,970
So this is adoptive.

305
00:18:12,460 --> 00:18:13,540
Yes, through.

306
00:18:15,810 --> 00:18:24,780
So by itself, it was adoptive or we made it by using flag, we made it by using flag so we can make

307
00:18:24,780 --> 00:18:27,510
Bubble sort as adoptive by default.

308
00:18:27,510 --> 00:18:29,530
It is by nature it is not adopted.

309
00:18:29,910 --> 00:18:36,180
We made it so it is possible to make bubbles adoptive and the bubble sort is known as adoptive.

310
00:18:36,180 --> 00:18:42,690
And then one last thing, whether it is stable or not to study, whether it is stable or not, I have

311
00:18:42,690 --> 00:18:44,340
taken a list of elements.

312
00:18:44,550 --> 00:18:45,450
Let us see the list.

313
00:18:45,660 --> 00:18:49,700
See there are duplicate elements, so let us start bubbles on this one.

314
00:18:50,100 --> 00:18:53,370
So first pass, first it will compare the elements, this element.

315
00:18:54,700 --> 00:18:57,280
A first is greater than second one, then interchange.

316
00:18:57,520 --> 00:18:59,900
But first one is not greater than second one.

317
00:19:00,640 --> 00:19:01,870
It will not interchange.

318
00:19:02,170 --> 00:19:03,600
So I will do one thing.

319
00:19:03,610 --> 00:19:07,180
I will write this a right so that you can identify.

320
00:19:08,170 --> 00:19:13,150
So little to change, it will not interchange that blockade remains as it is.

321
00:19:14,640 --> 00:19:15,540
Then eight.

322
00:19:17,210 --> 00:19:18,650
Then three pfeifle.

323
00:19:20,310 --> 00:19:26,100
Then we'll compare these to then the same goes down, so eight, three.

324
00:19:27,980 --> 00:19:37,280
Eight, five, four, yes, so that was Fosset Fosset remains their only like the second aid will come

325
00:19:37,280 --> 00:19:39,940
at the bottom then I believe that Texas state will come.

326
00:19:39,950 --> 00:19:41,450
So the order is not changing.

327
00:19:41,720 --> 00:19:43,010
It's not switching.

328
00:19:43,010 --> 00:19:48,920
The elements like Disarrayed eight is coming faster then this blockade, they are not changing the order.

329
00:19:49,640 --> 00:19:53,870
This will not happen because if this is greater than only too willing to change, so bubble.

330
00:19:53,870 --> 00:19:58,330
So it will change only if the element is first element is significant.

331
00:19:58,730 --> 00:20:01,310
So that's how bubbles are stable also.

332
00:20:01,550 --> 00:20:02,930
So yes, it is stable.

333
00:20:03,680 --> 00:20:06,800
So bubbles are adaptive, bubbles are stable.

334
00:20:07,520 --> 00:20:08,710
That's all about bubbles.

335
00:20:08,720 --> 00:20:08,980
Not.

