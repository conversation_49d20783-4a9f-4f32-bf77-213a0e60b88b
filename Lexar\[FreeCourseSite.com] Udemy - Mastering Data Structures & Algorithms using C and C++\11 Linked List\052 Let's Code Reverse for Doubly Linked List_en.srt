1
00:00:00,150 --> 00:00:07,680
And this video will look at the demonstration for function to reverse a linguist, so already we have

2
00:00:07,680 --> 00:00:12,530
seen the board work and have explained the procedure just like that function and show you that is a

3
00:00:12,540 --> 00:00:15,600
function about this main function won't reverse.

4
00:00:15,750 --> 00:00:18,550
Let it take a pointer to a..

5
00:00:18,570 --> 00:00:19,560
That is just normal.

6
00:00:19,620 --> 00:00:20,730
Procedure is very simple.

7
00:00:20,730 --> 00:00:24,710
Just we have to scan through the entire Linkous and swabbed it behind us.

8
00:00:25,080 --> 00:00:27,960
So for swapping, I should have one more pointer.

9
00:00:27,960 --> 00:00:29,410
That is a temporary pointer.

10
00:00:29,870 --> 00:00:33,180
Then using my loop I will scan to link list.

11
00:00:33,240 --> 00:00:36,620
Why bees B's not equal to none, then swap the pointers.

12
00:00:37,140 --> 00:00:42,630
So first in a temporary point or take these next and then IDP's next.

13
00:00:42,660 --> 00:00:47,270
Assign B the previous than in previous assigned time.

14
00:00:47,470 --> 00:00:55,480
NetSol now pointers previous and next chart interchange not should move to the next node.

15
00:00:55,500 --> 00:01:01,260
So now the pointer is stored in previous so B should move to previous node.

16
00:01:01,410 --> 00:01:04,670
So previous node will take it to the next node.

17
00:01:04,830 --> 00:01:06,870
Then on that new node.

18
00:01:06,870 --> 00:01:12,300
I should also check if that is the last node, if a piece next, this node is not your change.

19
00:01:12,310 --> 00:01:19,650
If it is equal to null means it's the last node, then first pointer should be pointing on that last

20
00:01:19,650 --> 00:01:21,000
note p that's all.

21
00:01:21,080 --> 00:01:28,350
So here already I have a main function which is creating a link list of values, 10, 20, 30, 40,

22
00:01:28,350 --> 00:01:28,800
50.

23
00:01:29,130 --> 00:01:31,190
Then also I'm displaying a link list.

24
00:01:31,350 --> 00:01:37,280
But before displaying a linguist, let us reverse reverse first send appointed to a first.

25
00:01:37,710 --> 00:01:39,050
Now let us run the program.

26
00:01:39,210 --> 00:01:39,810
Oops.

27
00:01:39,810 --> 00:01:40,590
There's an error.

28
00:01:42,210 --> 00:01:46,800
See here when it is reaching the last node there is no next pointer for the last node.

29
00:01:46,800 --> 00:01:47,660
Not not.

30
00:01:47,700 --> 00:01:56,050
So here I should add one more condition that if B is not equal to null and B's next to null then change

31
00:01:56,050 --> 00:01:56,450
this one.

32
00:01:56,460 --> 00:01:58,010
So there's the extra condition decided.

33
00:01:58,010 --> 00:01:59,550
I have not shown you on the whiteboard.

34
00:02:00,060 --> 00:02:06,060
So excarnation I should add, because before checking PS next, I should make sure that PS not not let

35
00:02:06,060 --> 00:02:07,570
us run the program once again.

36
00:02:07,620 --> 00:02:11,330
Yes, Linklaters rewards fifty, forty, three, twenty and ten.

37
00:02:11,820 --> 00:02:17,100
So the elements that we have inserted in the link while creating a link list, those elements are reversed

38
00:02:17,100 --> 00:02:17,410
now.

39
00:02:17,550 --> 00:02:18,180
That's all.

40
00:02:18,180 --> 00:02:20,030
You can try this function by yourself.

