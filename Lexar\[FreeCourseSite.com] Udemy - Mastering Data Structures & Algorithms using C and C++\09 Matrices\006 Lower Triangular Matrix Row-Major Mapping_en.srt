1
00:00:00,450 --> 00:00:07,560
The next topic is lower triangle of markets, a lower triangular maddux's a square matrix in which the

2
00:00:07,560 --> 00:00:11,820
lower triangular part of a matrix is non-zero elements.

3
00:00:12,120 --> 00:00:18,010
And the upper triangle part is all Zeitels and none of them as non-zero.

4
00:00:18,210 --> 00:00:19,320
So you can see this one.

5
00:00:19,770 --> 00:00:22,710
These set of elements are non-zero.

6
00:00:23,730 --> 00:00:25,080
These are forming a triangle.

7
00:00:26,180 --> 00:00:30,310
Now, let us define this and see which elements are zettl which are known to you.

8
00:00:30,700 --> 00:00:38,210
Let us check in business if the name of this matrix is M and these are rules and let us call them as

9
00:00:38,210 --> 00:00:41,510
I and these are columns, let us call them as G.

10
00:00:41,730 --> 00:00:43,670
These are columns and column as G.

11
00:00:44,440 --> 00:00:49,760
Now if I compare I n g this is one one two.

12
00:00:49,760 --> 00:00:52,730
One, two, two, three.

13
00:00:52,730 --> 00:00:54,270
One, three, two, three, three.

14
00:00:54,290 --> 00:01:01,180
So if you check all these indices I value as greater than or equal to value.

15
00:01:02,030 --> 00:01:08,660
So for non-zero elements I value is always greater than what about zero elements I value smaller.

16
00:01:09,440 --> 00:01:15,740
So it means if I value is less than G, that is rule number is less than column number than the element

17
00:01:15,740 --> 00:01:17,030
must be zero.

18
00:01:18,240 --> 00:01:28,640
These must be sealed so we can define a lower triangular matrix as a form of icon, Munshi is zero.

19
00:01:29,040 --> 00:01:36,270
If I is less than Cheam, I'm of Ikoma, G is non-zero.

20
00:01:36,960 --> 00:01:39,700
If I is greater than the to chain.

21
00:01:40,710 --> 00:01:44,190
So these elements are non-zero and those must be zero.

22
00:01:44,670 --> 00:01:51,840
Now let us observe in the matrix of Engross, and that is a five by five total twenty five elements

23
00:01:51,840 --> 00:01:54,430
rather than how many elements are non-zero.

24
00:01:54,960 --> 00:01:56,820
So a number of non-zero elements.

25
00:01:57,600 --> 00:01:59,460
First rule is having one element.

26
00:02:00,240 --> 00:02:02,730
Second row is having two non-zero elements.

27
00:02:03,600 --> 00:02:06,040
Turturro is having three non-zero elements.

28
00:02:06,660 --> 00:02:08,009
So this is four five.

29
00:02:08,160 --> 00:02:10,169
So this is four plus five.

30
00:02:10,940 --> 00:02:17,400
Now for any engross and matrix, it will be one plus two plus three plus goes on to last.

31
00:02:17,400 --> 00:02:17,970
One will be.

32
00:02:18,780 --> 00:02:21,240
So this is an end to end.

33
00:02:21,240 --> 00:02:25,870
Plus one by two number of elements are non-zero.

34
00:02:27,510 --> 00:02:29,040
These many elements are non-zero.

35
00:02:30,430 --> 00:02:38,380
Animatrix, the total and square elements will be there, and square and non-zero elements are anything

36
00:02:38,380 --> 00:02:40,350
to end plus one by two.

37
00:02:41,260 --> 00:02:44,290
So if I subtract this, then I get zero elements.

38
00:02:44,500 --> 00:02:45,820
These are zero elements.

39
00:02:46,240 --> 00:02:53,800
So non-zero elements are an end to end, plus one by two and zero elements are in square minus and in

40
00:02:53,800 --> 00:02:55,020
the end plus one by two.

41
00:02:55,900 --> 00:02:56,920
So this will be.

42
00:02:58,250 --> 00:03:01,130
And in the end, minus one by two.

43
00:03:01,160 --> 00:03:03,110
So if you observe here is one zero.

44
00:03:03,140 --> 00:03:06,680
These are two zeros than three zeros and four zeros.

45
00:03:06,830 --> 00:03:09,100
Dimensions are five girls, five.

46
00:03:09,410 --> 00:03:11,900
But the last number of zeros are four.

47
00:03:11,900 --> 00:03:14,320
So this is going up to one plus two plus three plus four.

48
00:03:14,330 --> 00:03:16,470
So that is an N minus one by two.

49
00:03:16,850 --> 00:03:22,150
So there are an enduring minus one by the number of zero elements and an end to end plus one by the

50
00:03:22,160 --> 00:03:24,050
number of non-zero elements.

51
00:03:24,680 --> 00:03:30,530
Now let us talk about representing these lower triangular mattresses in our program.

52
00:03:30,540 --> 00:03:37,430
So when we want to use them in the program, we want to avoid storing zeros so that we can save memory

53
00:03:37,430 --> 00:03:44,660
space as well as we can save the time in processing zero elements then for storing on the elements,

54
00:03:44,660 --> 00:03:49,910
how much space we need there and into this one by a number of elements.

55
00:03:49,910 --> 00:03:52,960
So we need any amount of space.

56
00:03:53,300 --> 00:03:56,200
So as our example, how much space we need.

57
00:03:56,570 --> 00:03:59,330
So we need one plus two plus three plus four plus five.

58
00:03:59,330 --> 00:04:02,280
That is total 15 spaces, very clear.

59
00:04:02,750 --> 00:04:04,830
So let us see how it can be represented.

60
00:04:04,850 --> 00:04:10,610
So I will take an array of the size of 15 and we will learn how to store these elements.

61
00:04:10,760 --> 00:04:16,070
So here I have an array of 15 spaces of 15 size.

62
00:04:16,399 --> 00:04:18,540
Then I can store all these elements in this one.

63
00:04:19,579 --> 00:04:21,070
Now, how to store these elements?

64
00:04:21,529 --> 00:04:26,570
One method is I can store them around Bitel and second McCurdy's.

65
00:04:26,570 --> 00:04:29,280
I can store them column by column also.

66
00:04:29,900 --> 00:04:32,380
So let us follow Robledo method.

67
00:04:32,390 --> 00:04:34,610
We can call it as a rule, major method.

68
00:04:35,060 --> 00:04:37,660
So first let us see rule major method.

69
00:04:37,670 --> 00:04:43,770
So let us follow Romijn and fill the elements rule by first element.

70
00:04:43,790 --> 00:04:45,760
This is a one one.

71
00:04:46,100 --> 00:04:47,360
So this is a one.

72
00:04:49,200 --> 00:04:57,690
The first one then rode to that is eight to one and eight will do so, this is a two one and a two two.

73
00:04:58,050 --> 00:04:59,670
So this is the road to.

74
00:05:01,780 --> 00:05:05,680
Then three, one, three, two, two, three, four lightweights, I will fill up all the elements that

75
00:05:05,680 --> 00:05:06,190
will battle.

76
00:05:06,970 --> 00:05:13,960
So here I have filled all the non-zero elements in an array, a roll by here, one to observe observers.

77
00:05:14,740 --> 00:05:19,480
And this representation I have in this is starting from one on words.

78
00:05:19,480 --> 00:05:25,020
But as per programming C or C++ programming arrays start from indexes eedle onwards.

79
00:05:25,300 --> 00:05:26,680
So we have to take care of it.

80
00:05:27,160 --> 00:05:28,840
So we have simply stored element.

81
00:05:28,840 --> 00:05:29,380
Robledo.

82
00:05:29,680 --> 00:05:34,960
But is there any formula that is mapping this two-dimensional array that is lower triangular matrix

83
00:05:35,320 --> 00:05:37,600
in a single dimension array?

84
00:05:37,900 --> 00:05:44,320
Let us come up with some formula so that we can access a specific element, so let us see how we can

85
00:05:44,320 --> 00:05:46,520
build up a formula feasible.

86
00:05:46,810 --> 00:05:52,130
I want to access an element that is for commentary, so for commentary as in fourth row.

87
00:05:52,150 --> 00:05:53,050
So this is here.

88
00:05:53,350 --> 00:05:59,890
So this element I want to access, if I want to access this element, reach of this element, then what

89
00:05:59,890 --> 00:06:06,360
should be the index of each of four, comma, three?

90
00:06:07,510 --> 00:06:08,020
What should.

91
00:06:10,640 --> 00:06:12,690
What should be the index for commentary?

92
00:06:12,950 --> 00:06:18,970
So here it is for commentary, but in this index is eight, so I should get eight.

93
00:06:19,550 --> 00:06:25,580
So for that, if I start from index zero, I have to move eight indices to access this one.

94
00:06:25,850 --> 00:06:28,500
So how do you know how many indices we have to move?

95
00:06:28,790 --> 00:06:30,550
See, this element is unfordable.

96
00:06:30,840 --> 00:06:31,840
This is 42.

97
00:06:32,090 --> 00:06:34,540
So this will come after Theodores.

98
00:06:34,790 --> 00:06:37,750
So in foster, how many elements are there second or.

99
00:06:39,790 --> 00:06:45,340
So I have to skip three doors, so Foster is having just one element, then the second group is having

100
00:06:45,340 --> 00:06:46,150
two elements.

101
00:06:46,900 --> 00:06:48,890
The third is having three elements.

102
00:06:49,210 --> 00:06:51,340
So I have skip three rules.

103
00:06:51,580 --> 00:06:52,090
Yes.

104
00:06:52,450 --> 00:06:55,210
Then so I have three rules.

105
00:06:55,660 --> 00:07:01,240
Now, when I have skipped first or second or third row, I am here in the beginning of fourth row,

106
00:07:01,480 --> 00:07:06,010
then how many elements I should move ahead to reach this forward commentary.

107
00:07:06,190 --> 00:07:09,940
So when I'm here, I should move one to two elements.

108
00:07:09,940 --> 00:07:12,550
I should move so plus two.

109
00:07:13,030 --> 00:07:16,090
So if I take total this is eight.

110
00:07:16,250 --> 00:07:17,820
So yes, I got the index.

111
00:07:18,370 --> 00:07:20,100
So this is four for commentary.

112
00:07:20,560 --> 00:07:26,440
Let us take one more example and then we will build a formula so all they want to access are five,

113
00:07:26,710 --> 00:07:27,970
four, four, five.

114
00:07:27,970 --> 00:07:29,760
Coming forward is present at 13.

115
00:07:30,040 --> 00:07:36,260
So what should be the index of five comma four five performance.

116
00:07:36,340 --> 00:07:41,860
I should go to first row, so for that I should cross four roles.

117
00:07:42,070 --> 00:07:46,090
So First Row is having one element and second is two and third is three.

118
00:07:46,090 --> 00:07:48,640
Then fourth rule is having four elements.

119
00:07:48,650 --> 00:07:53,050
I have to first row, second or third or fourth row, then I'm here in the beginning of this one.

120
00:07:53,500 --> 00:07:57,280
Then from here, how many steps I should take to reach five coming forward.

121
00:07:57,280 --> 00:07:59,710
One, two, three, four plus three.

122
00:08:01,720 --> 00:08:04,240
If you are this, this will be 13.

123
00:08:05,590 --> 00:08:15,640
So, yes, I got Dynex, so what is the formula for any icon, Munshi So for any index aof icon, my

124
00:08:18,040 --> 00:08:24,250
formula should be C. This is adding one plus two plus three because before this is adding one plus two

125
00:08:24,250 --> 00:08:26,030
plus three plus four because that's five.

126
00:08:26,350 --> 00:08:32,010
So this means for five it is five to five minus one by two.

127
00:08:32,409 --> 00:08:34,299
That is some of a natural number.

128
00:08:34,570 --> 00:08:37,130
That is less than five one less than five.

129
00:08:37,390 --> 00:08:38,549
So one less than I.

130
00:08:38,650 --> 00:08:40,240
So this will be I in two.

131
00:08:40,240 --> 00:08:45,490
I'm minus one by two then plus C this was three.

132
00:08:45,490 --> 00:08:46,300
So this was two.

133
00:08:46,630 --> 00:08:47,310
This was four.

134
00:08:47,320 --> 00:08:48,060
So this is three.

135
00:08:48,310 --> 00:08:50,860
So if it is a G then it will be G minus one.

136
00:08:52,220 --> 00:08:58,220
So this should be the formula for accessing non-zero elements in a single dimension.

137
00:08:59,270 --> 00:09:05,790
This is a major formula so we can prepare a program for this one using this formula.

138
00:09:06,470 --> 00:09:07,960
We'll be looking at all other markets.

139
00:09:08,150 --> 00:09:11,630
Then finally, I will show you one single program in which I will just change.

140
00:09:11,630 --> 00:09:13,730
The formula for the markets will be changing.

141
00:09:14,330 --> 00:09:16,430
So and we will see the programming part.

142
00:09:17,430 --> 00:09:25,140
Now, let us see how we can represent them in column major, that is if I'm storing the elements column

143
00:09:25,140 --> 00:09:25,710
by column.

