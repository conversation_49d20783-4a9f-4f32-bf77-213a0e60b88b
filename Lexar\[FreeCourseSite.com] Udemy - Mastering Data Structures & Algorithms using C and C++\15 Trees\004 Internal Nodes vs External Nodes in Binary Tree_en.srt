1
00:00:00,300 --> 00:00:07,710
Now we are going to find out the relationship between internal laws and external laws that is not only

2
00:00:07,800 --> 00:00:13,240
North versus Leaf notes so far, finding the relationship already have some examples.

3
00:00:13,560 --> 00:00:18,990
So let us find out the leaf nodes and on leave notes, then we will see if there is any formal line

4
00:00:18,990 --> 00:00:19,560
between them.

5
00:00:19,560 --> 00:00:21,200
Is there any relationship between them?

6
00:00:21,660 --> 00:00:23,280
So let us find out here.

7
00:00:23,280 --> 00:00:30,390
How many nodes are there with the degree to a number of nodes with the degree to this is having two

8
00:00:30,390 --> 00:00:34,980
children sort of agreed to this, also having Goochland so degree to any of them?

9
00:00:35,340 --> 00:00:35,680
No.

10
00:00:36,150 --> 00:00:39,670
So there are two nodes for the degree to then how many nodes to the degree?

11
00:00:39,690 --> 00:00:43,440
One, this is having just one child degree.

12
00:00:43,440 --> 00:00:49,440
One, this also having just one degree, one to two nodes with the degree one that.

13
00:00:49,440 --> 00:00:51,160
How many nodes are there with the degree.

14
00:00:51,180 --> 00:00:51,600
Zero.

15
00:00:52,620 --> 00:00:55,080
One, two, three, three.

16
00:00:55,110 --> 00:00:57,200
Those are there with the degree zero.

17
00:00:57,840 --> 00:01:03,570
So I have found out all the node with the degree to degree one and degree zero.

18
00:01:04,019 --> 00:01:04,340
Right.

19
00:01:04,530 --> 00:01:07,230
So I do the same thing for other two examples also.

20
00:01:07,230 --> 00:01:14,850
Then we will see is that any formula industry number of nodes for the degree to do is having both particular

21
00:01:15,090 --> 00:01:20,480
degree to those having both the children degree to this, having Boudjellal degree to any other node.

22
00:01:20,490 --> 00:01:20,730
No.

23
00:01:20,910 --> 00:01:27,660
So one, two, three, three nodes for the degree to then how many nodes are having degree one?

24
00:01:29,010 --> 00:01:30,110
This is having a degree one.

25
00:01:30,150 --> 00:01:31,400
This also having degree one.

26
00:01:31,420 --> 00:01:32,670
This is also having degree one.

27
00:01:32,670 --> 00:01:33,750
So one, two, three.

28
00:01:34,140 --> 00:01:37,330
And this also having degree one does also having negative and so total.

29
00:01:37,350 --> 00:01:37,710
How many.

30
00:01:37,950 --> 00:01:39,720
Five nodes five.

31
00:01:39,930 --> 00:01:41,850
One, two, three, four, five.

32
00:01:42,360 --> 00:01:44,420
Then how many nodes of the degree zero.

33
00:01:45,870 --> 00:01:48,240
One, two, three, four.

34
00:01:48,270 --> 00:01:51,750
These nodes are not having any children so degree zero.

35
00:01:51,750 --> 00:01:57,880
And also for C these are internal laws and this is external trade.

36
00:01:57,990 --> 00:01:59,620
These are internal and external.

37
00:02:00,180 --> 00:02:05,160
So these two together internal means they must have either two children or one child, no children,

38
00:02:05,160 --> 00:02:07,770
zero children, lymph nodes, external nodes.

39
00:02:08,310 --> 00:02:16,740
Let is observe here how many nodes for the degree to only one is how many nodes with the degree one

40
00:02:17,550 --> 00:02:18,330
does having degree.

41
00:02:18,340 --> 00:02:22,470
One, two, three, four, four and also the degree one.

42
00:02:22,800 --> 00:02:24,840
Then how many nodes with the degree zero.

43
00:02:25,740 --> 00:02:27,780
One and two to.

44
00:02:30,050 --> 00:02:36,560
I have written the values here, so there are some hints I have given in this values, you find out

45
00:02:36,560 --> 00:02:37,580
what is the relationship.

46
00:02:37,640 --> 00:02:41,960
Two, three, three, four, one, two.

47
00:02:43,700 --> 00:02:49,820
So there is a relationship between north of the degree to one degree zero, but not degree one, but

48
00:02:49,820 --> 00:02:53,840
not everyone, the degree to and degree zero there is a relationship.

49
00:02:54,170 --> 00:03:00,950
So what is the relationship number of Norsworthy degrees zero are equal to number of and with the degree

50
00:03:00,950 --> 00:03:02,780
two plus one.

51
00:03:05,040 --> 00:03:09,090
Yes, this is always true in binary tree.

52
00:03:10,070 --> 00:03:16,640
This formula is always true in biometrically, so that's all about the formula for relationship between

53
00:03:17,300 --> 00:03:19,790
internal and external laws we have seen.

