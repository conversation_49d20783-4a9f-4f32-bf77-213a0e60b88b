1
00:00:00,150 --> 00:00:04,019
In this, we do look at the demonstration for nested recursion.

2
00:00:04,050 --> 00:00:10,170
The project is already using the same project and here I will try it on the code for nested recursion.

3
00:00:10,650 --> 00:00:13,170
I've learned on the function that we have already seen.

4
00:00:19,570 --> 00:00:31,090
Function takes parameters and and if and is greater than <PERSON><PERSON>, it will return and minus 10 otherwise

5
00:00:31,090 --> 00:00:38,470
function will return the function of a. plus level.

6
00:00:40,390 --> 00:00:41,710
So there's an asset called.

7
00:00:46,340 --> 00:00:53,460
Then from the main function, I will take some temporary variable Ardern in R, I will assign the result

8
00:00:53,460 --> 00:00:55,950
of the function called by passing 95.

9
00:00:55,950 --> 00:00:58,590
The value that we have seen done here.

10
00:00:58,590 --> 00:01:01,200
I will print the result of R.

11
00:01:03,300 --> 00:01:05,390
New line are.

12
00:01:07,730 --> 00:01:13,670
That said, it's a simple function, the same result we get, we know the result that if 90 days pass,

13
00:01:13,670 --> 00:01:14,520
what is the output?

14
00:01:15,020 --> 00:01:17,170
So let us run the program and see the result.

15
00:01:19,240 --> 00:01:25,930
Ninety one is the result, yes, already we have traced this program and we have seen it, if we possibly

16
00:01:25,930 --> 00:01:28,290
value ninety nine, let us see what will be the result.

17
00:01:29,980 --> 00:01:30,910
Still 91.

18
00:01:33,030 --> 00:01:36,290
If it was the result last hundred, then let us see what is the result.

19
00:01:38,650 --> 00:01:41,320
Still, even if it was 200.

20
00:01:43,790 --> 00:01:46,590
One ninety seven is greater than zero.

21
00:01:47,180 --> 00:01:49,020
It is just returning and minus 10.

22
00:01:49,040 --> 00:01:50,360
So what is the value of a..

23
00:01:50,390 --> 00:01:52,570
Minus 10 otherwise these calls are made.

24
00:01:52,820 --> 00:01:56,870
If it was the value to the then to see what is the result.

25
00:01:58,040 --> 00:02:00,560
So again, answer is ninety one cities study.

26
00:02:00,710 --> 00:02:06,260
The value today will go up to the value of not one and then from a not one one, then subtract that

27
00:02:06,260 --> 00:02:07,100
it gets 91.

28
00:02:07,130 --> 00:02:10,280
So for any value, this function is going to return value.

29
00:02:10,280 --> 00:02:13,390
Ninety-One so you can debug dysfunction and check.

30
00:02:13,400 --> 00:02:14,780
So I'm not debugging now.

31
00:02:14,840 --> 00:02:17,050
Already I have shown you how to reboot the program.

32
00:02:17,810 --> 00:02:23,150
So whichever editor you are using, you can try debugger that will help you clearly understand the flaw

33
00:02:23,150 --> 00:02:24,770
or the working off program.

34
00:02:26,480 --> 00:02:27,860
So that's all in this video.

