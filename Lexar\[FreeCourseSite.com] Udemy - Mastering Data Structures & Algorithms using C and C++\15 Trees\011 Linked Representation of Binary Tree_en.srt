1
00:00:01,100 --> 00:00:08,680
Now, let us look at the representation of a binary tree, so the same example, binary trees have taken

2
00:00:09,170 --> 00:00:09,710
no link.

3
00:00:09,710 --> 00:00:13,980
Representation means we will be using nodes like nodes of interest.

4
00:00:14,360 --> 00:00:21,380
So, yes, we will use a node, nor structure will be containing data and two pointers left and right.

5
00:00:22,130 --> 00:00:29,960
So this node structure is the same as the node of double dealing the list and linguists will have previous

6
00:00:29,960 --> 00:00:30,420
pointer.

7
00:00:30,450 --> 00:00:31,160
Next pointer.

8
00:00:31,190 --> 00:00:33,710
Here we are taking left Charlon right.

9
00:00:33,720 --> 00:00:35,360
Check this node.

10
00:00:35,360 --> 00:00:42,770
Can we define using self-referential structure node having two pointers, self-referential pointer,

11
00:00:43,130 --> 00:00:47,220
alkyl and rightside and in-between data is also there.

12
00:00:47,930 --> 00:00:49,700
So we say link the representation.

13
00:00:49,710 --> 00:00:51,980
We don't say link the list to representation list.

14
00:00:52,250 --> 00:00:55,740
It is Lilium, but a tree is a nonlinear data structure.

15
00:00:56,330 --> 00:00:57,760
So nomics.

16
00:00:57,770 --> 00:01:02,010
Let us see how we can utilize these nodes and represent this one.

17
00:01:03,140 --> 00:01:07,340
So for every node in a binary tree, we will create this structure node.

18
00:01:09,570 --> 00:01:20,340
So I have kept the structure ready for the street, so this is a. for A and B, C, D, E, F and G,

19
00:01:20,940 --> 00:01:26,910
this is having two children, B and C B's, having two children, and C, also having two children.

20
00:01:26,940 --> 00:01:30,550
What about B, there are no children for the there are no children for E..

21
00:01:31,680 --> 00:01:33,180
OK, so these are all known.

22
00:01:34,590 --> 00:01:36,780
So if there is no child I have made it as no.

23
00:01:37,470 --> 00:01:40,290
So this structure will be dynamically creating.

24
00:01:40,290 --> 00:01:46,020
So it will be definitely created in heap, whereas already presentation or they can be created in the

25
00:01:46,030 --> 00:01:48,150
stack also or heap also.

26
00:01:48,150 --> 00:01:50,150
So it can be static as well as dynamic.

27
00:01:50,430 --> 00:01:52,730
But this structure is definitely dynamic.

28
00:01:52,740 --> 00:01:53,490
So it is created.

29
00:01:53,490 --> 00:01:57,600
And he now let us study something about this one.

30
00:01:58,810 --> 00:02:04,930
There are nodes and nodes are seven node because these are seven nodes in this graphical tree.

31
00:02:05,170 --> 00:02:10,030
So here also in the link position, we have seven nodes then.

32
00:02:10,940 --> 00:02:12,620
How many pointers are there?

33
00:02:13,660 --> 00:02:16,540
One, two, three, four, five, six, seven, eight.

34
00:02:17,540 --> 00:02:18,870
Well, there are seven north.

35
00:02:19,160 --> 00:02:24,950
There are eight pointers and plus one null.

36
00:02:26,330 --> 00:02:34,030
And final point, as always, for any unshaved binary tree you represented using link representation,

37
00:02:34,550 --> 00:02:38,080
then if there are no winners, then definitely there will be.

38
00:02:38,090 --> 00:02:40,190
And plus, one final point does.

39
00:02:41,410 --> 00:02:48,340
So if you remember this formula that is Defendor, the personal point of this is similar to Ezequiel's

40
00:02:48,340 --> 00:02:56,310
to E-Plus one, this we have studied in strictly binary tree strict can we studied that number of external

41
00:02:56,310 --> 00:02:58,060
nodes will be internal nodes plus one.

42
00:02:58,630 --> 00:03:00,850
So similar formula if and knows that.

43
00:03:00,850 --> 00:03:02,310
And plus one final point.

44
00:03:02,320 --> 00:03:07,690
As I remove one node and show you if I am removing this node, then this should be not.

45
00:03:08,990 --> 00:03:10,080
How many laws are there?

46
00:03:10,100 --> 00:03:11,940
One, two, three, four, five, six.

47
00:03:11,990 --> 00:03:13,500
How many will point of father?

48
00:03:13,760 --> 00:03:16,310
One, two, three, four, five, six, seven.

49
00:03:16,460 --> 00:03:18,390
Yes, seven point.

50
00:03:19,220 --> 00:03:21,500
So that's all about legal representation.

51
00:03:21,800 --> 00:03:28,880
Mostly we will be using this representation for writing programs or algorithms over a binary tree.

