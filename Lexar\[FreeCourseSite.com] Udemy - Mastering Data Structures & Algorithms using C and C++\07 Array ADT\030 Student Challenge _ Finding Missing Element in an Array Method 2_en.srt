1
00:00:00,570 --> 00:00:05,850
Now, let us see another method, which may be a faster method for finding a missing element in sequence

2
00:00:05,850 --> 00:00:06,620
of elements.

3
00:00:06,960 --> 00:00:12,810
So the same example I have set of elements that are starting from one and ending at well for one to

4
00:00:12,810 --> 00:00:16,210
12 and some elements are missing that are five and eight.

5
00:00:16,230 --> 00:00:17,160
We already know that.

6
00:00:17,520 --> 00:00:19,840
And the previous method was taking ultrafine square.

7
00:00:19,860 --> 00:00:22,180
Time now for making it faster.

8
00:00:22,200 --> 00:00:27,550
I have taken an edit here that is from zero to 12.

9
00:00:28,740 --> 00:00:36,540
The last indexes at Qestrel for the maximum limit is 12, I have taken an at a tilt well, so first

10
00:00:36,540 --> 00:00:42,030
of all, <PERSON><PERSON><PERSON>, equal to the size, that is the maximum limit you have in the sequence.

11
00:00:42,660 --> 00:00:45,010
Then initialize the array with the zero.

12
00:00:45,430 --> 00:00:47,990
OK, initialise know what to do with this.

13
00:00:48,390 --> 00:00:49,610
I'll show you the procedure.

14
00:00:50,600 --> 00:00:58,100
See, scan through this array one by one, not let us see the procedure, see scan through this single

15
00:00:58,100 --> 00:01:03,710
dimensional picking one element at a time, what to do for each element.

16
00:01:04,280 --> 00:01:05,750
First element is three.

17
00:01:05,750 --> 00:01:08,750
Go to index three and increment it.

18
00:01:09,830 --> 00:01:15,350
Maybe that's one, so it was zero, just make it us one, the next seven, go to the next seven and

19
00:01:15,350 --> 00:01:22,610
make it as one incremented for go to index, then incremented nine, go to index nine and incrementing,

20
00:01:22,970 --> 00:01:27,060
go to the next 12 and incremented then go to the next six.

21
00:01:27,230 --> 00:01:27,920
Same thing.

22
00:01:27,920 --> 00:01:31,370
One increment level increment.

23
00:01:31,880 --> 00:01:34,010
Then two or increment.

24
00:01:34,280 --> 00:01:36,740
Then 10 increment.

25
00:01:37,630 --> 00:01:43,720
So have scanned through this area and for that particular element, I have gone into the same index

26
00:01:43,720 --> 00:01:47,740
like 411, I went to index level and incremented that value.

27
00:01:49,340 --> 00:01:56,040
So now in this area, I have all those location montages, one for the elements that are present in

28
00:01:56,040 --> 00:02:02,210
the nutty and the missing element, they have still zeros like zero zero zero anyway.

29
00:02:02,360 --> 00:02:04,820
The zero is not there in our sequence.

30
00:02:04,820 --> 00:02:06,520
Our sequence starts from one onwards.

31
00:02:06,770 --> 00:02:07,830
So ignore that one.

32
00:02:08,030 --> 00:02:13,500
So from that point, from one to two, well, if you see there are two places that are still zeros.

33
00:02:13,780 --> 00:02:15,470
I mean, the elements are missing.

34
00:02:16,810 --> 00:02:19,160
So this procedure looks simple and faster.

35
00:02:19,760 --> 00:02:21,950
Yes, what is the work we have done?

36
00:02:22,030 --> 00:02:27,010
Let us analyze the time, then I will show you the code so facilities analyze the time.

37
00:02:27,010 --> 00:02:30,660
So the time taken is we are scanning the list of elements.

38
00:02:30,670 --> 00:02:31,090
Yes.

39
00:02:31,360 --> 00:02:34,960
Then for every element, what we are doing here are the same index.

40
00:02:34,960 --> 00:02:36,250
We are implementing it.

41
00:02:36,280 --> 00:02:42,640
So how much time it takes for incrementing it and the same index constant time because I don't have

42
00:02:42,640 --> 00:02:43,600
to search anything.

43
00:02:43,900 --> 00:02:50,470
Like if it is like if it is one, go to index one and increment it like if it is to go to the next will

44
00:02:50,710 --> 00:02:51,610
and increment it.

45
00:02:51,670 --> 00:02:53,280
So the time taken is constant.

46
00:02:53,590 --> 00:02:55,810
So majorly where the time is spent.

47
00:02:56,050 --> 00:02:58,390
We are scanning through this list of elements.

48
00:02:58,390 --> 00:02:59,370
So that is order.

49
00:02:59,560 --> 00:03:05,360
And so yes, the time figure is out and so does the fast track method.

50
00:03:05,680 --> 00:03:11,920
Yes, this is the estimate that No one this is one of the additions which is faster.

51
00:03:12,370 --> 00:03:13,780
Not one more thing in analysis.

52
00:03:14,080 --> 00:03:15,160
What do you call this?

53
00:03:15,160 --> 00:03:16,150
Such an array?

54
00:03:16,330 --> 00:03:18,310
Not one thing in analysis.

55
00:03:18,610 --> 00:03:19,690
What do you call to this?

56
00:03:20,050 --> 00:03:21,630
So you have given an enormous edge.

57
00:03:22,000 --> 00:03:24,760
So usually this is hash table.

58
00:03:26,080 --> 00:03:27,610
Hash table we learned later.

59
00:03:27,820 --> 00:03:33,430
But the simple implementation of hash table hash, we are saying because whatever the element is, we

60
00:03:33,430 --> 00:03:35,980
are going to the same index and accessing it.

61
00:03:36,250 --> 00:03:40,780
So this takes Constantine, remember, that hash table takes constant time.

62
00:03:40,960 --> 00:03:42,070
That is ideal.

63
00:03:42,070 --> 00:03:44,190
Time for hashing is constant.

64
00:03:44,530 --> 00:03:48,330
So this is taking constant time for storing those elements here.

65
00:03:49,330 --> 00:03:54,820
So this is hash table or else we can call it as Wizzit.

66
00:03:56,350 --> 00:03:59,620
So we are taking the set of bits for each number and we are marking them.

67
00:03:59,830 --> 00:04:02,480
And whichever are zeros, those elements are missing.

68
00:04:03,190 --> 00:04:10,360
Yes, this bits that we have not implemented, the complete mechanism of hashing technique like using

69
00:04:10,360 --> 00:04:13,840
hash function, all those things, but does the simplest form of hash.

70
00:04:14,590 --> 00:04:19,930
Remember, whenever you have to search, you can apply hashing if possible.

71
00:04:21,450 --> 00:04:29,610
Then one important thing about hashing, when you are using this as a hash table and storing the elements,

72
00:04:29,610 --> 00:04:34,460
then you need an array space equal to the largest element.

73
00:04:34,470 --> 00:04:35,430
So it is one to.

74
00:04:37,760 --> 00:04:44,450
So this may take a lot of space, so space, if it is a constraint, then you cannot use this hashing

75
00:04:44,660 --> 00:04:47,870
means if you have limited space, then you cannot think of hasheem.

76
00:04:48,260 --> 00:04:52,880
But nowadays, the machines that we are using, they have abandoned the memory space.

77
00:04:52,880 --> 00:04:56,510
So we hardly bother about memory space.

78
00:04:56,780 --> 00:05:02,060
So harshing is the best solution whenever you are searching something.

79
00:05:02,480 --> 00:05:07,640
So yes, we have used harshing and that is the time to remember in the previous matter the time was

80
00:05:07,640 --> 00:05:12,410
outdraw and Square has has it to order off and it's a big change.

81
00:05:12,500 --> 00:05:16,640
And using this approach, you can solve various problems, various problems.

82
00:05:16,640 --> 00:05:21,380
So this you can take it as a basic idea, not just a solution to a problem.

83
00:05:22,010 --> 00:05:26,930
Now I will write on a piece of program code and I will explain before writing a program.

84
00:05:26,930 --> 00:05:31,670
First of all, I must have an array of elements, list of elements starting and learning index and the

85
00:05:31,670 --> 00:05:32,420
number of elements.

86
00:05:32,660 --> 00:05:35,510
And also I should have an array dynamically.

87
00:05:35,510 --> 00:05:41,210
You can declare an array of that given maximum size and you can initialize it will also assume that

88
00:05:41,210 --> 00:05:42,370
that part is already there.

89
00:05:42,650 --> 00:05:45,950
Just I will scan for this list for scanning through this area.

90
00:05:45,950 --> 00:05:53,900
I will take a look for I assign starting from zero and I is less than number of elements that are n

91
00:05:54,080 --> 00:06:00,860
so less than a plus less than what I should do every time I see the number is three.

92
00:06:01,130 --> 00:06:05,660
If I initially zero if I is a three.

93
00:06:05,810 --> 00:06:13,340
So I should go to index three and mark it as one so incremented so aof i.e. which is a three.

94
00:06:13,610 --> 00:06:20,020
So I should go to that index in each so atrophy of IHI that I should incremented.

95
00:06:21,050 --> 00:06:21,630
That's it.

96
00:06:22,250 --> 00:06:27,090
So it will scan through this early and for every element in that particular index it will increment

97
00:06:27,090 --> 00:06:27,290
it.

98
00:06:29,740 --> 00:06:37,270
So this followable, incremented, wherever that is found, then I have to scan through this early and

99
00:06:37,270 --> 00:06:40,880
find out missing element, wherever it is zero, that element is missing.

100
00:06:40,900 --> 00:06:41,400
Simple.

101
00:06:41,680 --> 00:06:45,360
So I would lead on one more loop this summer right after this.

102
00:06:45,370 --> 00:06:46,030
The next fall.

103
00:06:46,030 --> 00:06:49,900
Look for I sign I should start from which element?

104
00:06:50,080 --> 00:06:57,720
This element of this element that is low and I use less than equal to high I plus plus light.

105
00:06:58,600 --> 00:07:00,640
So I should start from one and that to it.

106
00:07:01,000 --> 00:07:03,280
Then every time I should check if.

107
00:07:04,470 --> 00:07:13,650
Edge of IHI is still zero, element is not so simply I will print, but indef which element like a four

108
00:07:13,660 --> 00:07:16,100
five zero four five is not that right.

109
00:07:16,320 --> 00:07:24,290
So bring deaf person Tildy new line and I will say I saw that index so that's all in a single line.

110
00:07:24,300 --> 00:07:24,960
I have written it.

111
00:07:25,260 --> 00:07:30,760
So this volume is for scanning through this area and this volume is for scanning through this area.

112
00:07:31,050 --> 00:07:34,770
So this is also order of M and this for loop.

113
00:07:34,770 --> 00:07:42,130
Also a number of elements are in total it is a tuen so I can say it's outdraw of any degree of a polynomial.

114
00:07:42,240 --> 00:07:49,240
And so this fall times so n elements and elements so to N and time are often.

115
00:07:50,500 --> 00:07:51,130
So that's it.

116
00:07:51,400 --> 00:07:56,920
So you can write on this program and check it so you can directly write inside main function, main

117
00:07:56,920 --> 00:07:59,440
function and you can execute this program.

118
00:07:59,470 --> 00:08:00,990
So this is a student exercise.

119
00:08:01,000 --> 00:08:02,920
So you have to do this one at.

