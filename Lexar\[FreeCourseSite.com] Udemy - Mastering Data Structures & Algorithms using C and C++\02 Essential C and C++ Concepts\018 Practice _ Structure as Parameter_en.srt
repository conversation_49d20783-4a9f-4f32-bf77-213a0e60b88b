1
00:00:00,360 --> 00:00:05,430
Let us look at the demonstration for structure as parameter.

2
00:00:06,560 --> 00:00:16,250
Already I have taken a C++ program and online GDP and also how it had structure for Rectangle, but

3
00:00:16,309 --> 00:00:17,670
just having length and breadth.

4
00:00:18,260 --> 00:00:22,700
So let us have a demo of the things that we have learned on whiteboard.

5
00:00:23,360 --> 00:00:32,090
See, inside the main function, I will create an object of rectangle, rectangle, r and also I will

6
00:00:32,090 --> 00:00:37,780
initialize its Lintas 10 and but as five we can directly initialize any structure.

7
00:00:38,150 --> 00:00:44,450
And in C++, strict is not monetary, but in C language it is mandatory that I think what struck is

8
00:00:44,450 --> 00:00:45,770
mandatory here.

9
00:00:45,770 --> 00:00:51,990
When you are declaring OK, anything now here I will print the length and breadth.

10
00:00:52,130 --> 00:00:53,840
So I will use print F.

11
00:00:54,810 --> 00:01:07,500
And I will say <PERSON><PERSON> was entitled Slash and then <PERSON> was entitled and again, I will give Slash and

12
00:01:08,010 --> 00:01:12,410
then he and I will print out our land and Cuma our daughter.

13
00:01:12,750 --> 00:01:15,180
But I will print land and read.

14
00:01:16,340 --> 00:01:20,250
So I will run this and show you then after that, we will look at function.

15
00:01:21,410 --> 00:01:26,720
I'm getting clandestine and Bertus five like Lindstrand and Bertus five.

16
00:01:27,860 --> 00:01:36,590
No, let us write a function function name is fun, which will take struck a rectangle as a parameter

17
00:01:37,160 --> 00:01:43,950
and the parameter type is called by value and the return type is void.

18
00:01:43,970 --> 00:01:51,480
It is not going to return anything that inside this function I will print length and breadth.

19
00:01:51,500 --> 00:01:53,050
So I will use C out.

20
00:01:53,330 --> 00:01:53,720
OK.

21
00:01:55,680 --> 00:02:05,130
I'm mixing the code, you can do that if the project is of type C++, Aagaard, Lente, as well as New

22
00:02:05,130 --> 00:02:05,550
Line.

23
00:02:07,200 --> 00:02:08,910
Next is Brett.

24
00:02:10,199 --> 00:02:12,510
Then I will bring our dog.

25
00:02:14,620 --> 00:02:15,160
Brett.

26
00:02:16,100 --> 00:02:23,930
All right, then after this, I will give new line now this function will take a rectangle as parameter

27
00:02:23,960 --> 00:02:28,810
and it will print its length and but not from the main function.

28
00:02:28,820 --> 00:02:34,430
I will call that function fund and I will pass on those rectangles.

29
00:02:35,660 --> 00:02:41,060
This is the method of putting a rectangle structure I like to see in this function.

30
00:02:41,060 --> 00:02:43,930
I'm just printing London but doing nothing else.

31
00:02:44,480 --> 00:02:46,940
Let us run if I run London.

32
00:02:46,940 --> 00:02:48,680
But Birdman's we print that two times.

33
00:02:49,400 --> 00:02:54,770
One is by the function fun and the other is inside the main function.

34
00:02:55,250 --> 00:02:56,330
There is a mistake here.

35
00:02:56,730 --> 00:02:58,150
This should be Steve.

36
00:02:58,200 --> 00:02:59,730
Are you OK?

37
00:02:59,780 --> 00:03:00,860
Struck a rectangle.

38
00:03:01,530 --> 00:03:02,360
Let us run it.

39
00:03:02,810 --> 00:03:05,390
Yes, I got less than that two times.

40
00:03:05,400 --> 00:03:06,940
Let the but this fight.

41
00:03:07,010 --> 00:03:14,720
So the important thing to learn here is that when we pass a rectangle structuralist parameter, then

42
00:03:14,720 --> 00:03:18,080
both the values, length and breadth, both of them will be copied here.

43
00:03:18,350 --> 00:03:22,250
Our new structure will be created like I have shown on whiteboard.

44
00:03:22,520 --> 00:03:22,920
Right.

45
00:03:23,270 --> 00:03:28,630
So this is called value write how to write on callback value.

46
00:03:28,640 --> 00:03:30,040
We have learned the syntax.

47
00:03:30,740 --> 00:03:33,920
Now let us learn one more thing called value mechanism.

48
00:03:34,190 --> 00:03:38,830
If any changes are done to this rectangle, that will not change this rectangle.

49
00:03:38,990 --> 00:03:39,300
Right.

50
00:03:39,740 --> 00:03:46,670
So what I will do is I will change the length are not Lento's 920, I will make it 20.

51
00:03:47,180 --> 00:03:49,910
So I should get Clandestinity and Bertus five.

52
00:03:50,270 --> 00:03:52,480
But the actual parameter is this one.

53
00:03:52,490 --> 00:03:59,090
This is the actual variable and it lends itself and will remain Sainte-Anne ten and it will be five

54
00:03:59,090 --> 00:03:59,250
one.

55
00:03:59,270 --> 00:04:00,290
And this will not change.

56
00:04:00,560 --> 00:04:02,090
It will change only inside.

57
00:04:02,090 --> 00:04:04,730
The function for this rectangle are right.

58
00:04:05,060 --> 00:04:08,390
And remember this are and this are not same.

59
00:04:08,720 --> 00:04:09,560
They are different.

60
00:04:09,710 --> 00:04:13,030
If you are getting confused then you can change its name durect.

61
00:04:13,280 --> 00:04:14,570
OK, they are different.

62
00:04:15,080 --> 00:04:17,180
This is different from this one now.

63
00:04:17,180 --> 00:04:18,370
Only this will change.

64
00:04:18,380 --> 00:04:21,110
Actual variable are will not change.

65
00:04:21,589 --> 00:04:22,670
Let us run and see.

66
00:04:22,670 --> 00:04:26,990
We should get the result as twenty here and five here.

67
00:04:27,400 --> 00:04:30,290
Then here back again Lindstrand and five.

68
00:04:30,500 --> 00:04:31,610
This will not change.

69
00:04:32,120 --> 00:04:34,400
That side is proof that it will not change.

70
00:04:34,740 --> 00:04:41,890
See the result is lenders 20 and Betties five and the lending side main function remains and five it.

71
00:04:41,970 --> 00:04:43,050
This is called by value.

72
00:04:43,340 --> 00:04:48,220
Now let us look at Golby address see before Shinkolobwe address.

73
00:04:48,260 --> 00:04:50,390
Let us look at the syntax once again.

74
00:04:50,930 --> 00:04:53,600
See a structure rectangle that is declared here.

75
00:04:53,600 --> 00:04:56,210
So then you have a variable of type structure.

76
00:04:56,640 --> 00:05:03,620
We use dot operator for accessing the members and here this is called the value and this is also variable

77
00:05:03,620 --> 00:05:05,780
of type structure rectangle.

78
00:05:06,020 --> 00:05:08,930
So use dot operator for accessing the members.

79
00:05:09,080 --> 00:05:14,190
But now in called address from here we will pass the address of a rectangle.

80
00:05:14,690 --> 00:05:18,130
So here we should take a pointer of type rectangle.

81
00:05:18,380 --> 00:05:23,110
So I will call it a spa or just be a A to speak then.

82
00:05:23,300 --> 00:05:27,670
Now this is not a variable of type rectangle, it is a point of type rectangle.

83
00:05:28,070 --> 00:05:33,620
So for a pointer we use arrow, we do not you start, we use arrow.

84
00:05:34,070 --> 00:05:37,700
And right now, wherever this is written, I should change it to be.

85
00:05:38,000 --> 00:05:42,190
And also I should remove DOT and change it with Arrow.

86
00:05:42,410 --> 00:05:47,090
Alright, so I don't know, let us read the code once again.

87
00:05:47,570 --> 00:05:53,420
See I have a structured type variable having value and five in the length and breadth and passing it

88
00:05:53,420 --> 00:05:57,170
to function and the function is modifying its length printing.

89
00:05:57,170 --> 00:06:04,460
Newlon then but then next when it come back to the main function it will print Lindenwood here.

90
00:06:04,550 --> 00:06:08,090
So will it change the length of this rectangle.

91
00:06:08,430 --> 00:06:14,420
Yes, this will change the length of the rectangle because this is called the adjust parameter.

92
00:06:14,570 --> 00:06:16,550
Twenty is the new length.

93
00:06:16,820 --> 00:06:19,550
So what will be printed on the screen first?

94
00:06:19,550 --> 00:06:24,500
Lindenberg will be twenty five, then again inside the main function.

95
00:06:24,500 --> 00:06:30,320
When it comes out of function fun it will print twenty and five only because the stand will be modified

96
00:06:30,320 --> 00:06:32,680
frequently by this function font.

97
00:06:32,900 --> 00:06:33,980
Let us on NC.

98
00:06:33,980 --> 00:06:36,560
We should get twenty and five good times.

99
00:06:36,950 --> 00:06:40,610
Yes we got twenty five and again twenty and then five.

100
00:06:40,880 --> 00:06:46,310
So it means that this function is modifying this rectangle structure.

101
00:06:47,000 --> 00:06:47,780
So that's it.

102
00:06:47,810 --> 00:06:51,170
I've shown you called value and Colbert told us one more thing.

103
00:06:51,170 --> 00:06:56,780
I have shown you on white board that if a structure is containing an array then array will be copied

104
00:06:56,780 --> 00:06:57,260
here.

105
00:06:57,590 --> 00:07:01,880
Array will be copied into a formal parameter here.

106
00:07:01,910 --> 00:07:04,280
Right, so that you can try it by yourself.

107
00:07:04,580 --> 00:07:06,620
I will show you one more new thing here.

108
00:07:07,580 --> 00:07:15,190
I will create a variable of type structure on heap inside a function and return, etc..

109
00:07:16,360 --> 00:07:21,730
So let us remove everything and write it as a fresh program.

110
00:07:22,180 --> 00:07:25,650
OK, I will remove everything and write it as a first program.

111
00:07:26,080 --> 00:07:27,940
I have a rectangle structure.

112
00:07:27,950 --> 00:07:30,150
Let it be as it is now.

113
00:07:30,160 --> 00:07:36,460
I will write one function which will return a pointer of type rectangle.

114
00:07:37,950 --> 00:07:39,430
Point of type rectangle.

115
00:07:39,840 --> 00:07:48,000
So what it will do, it will create an object of type rectangle on heap plate using a pointer.

116
00:07:48,240 --> 00:07:49,740
Let us create a total right.

117
00:07:50,010 --> 00:07:53,130
We assign new rectangle.

118
00:07:53,610 --> 00:07:55,560
This is in C++ syntax.

119
00:07:55,900 --> 00:08:01,820
If you want to do it and see language, then you have to write down struct sorry.

120
00:08:02,220 --> 00:08:07,200
Struct Rectangle, Pointer and mellark.

121
00:08:07,810 --> 00:08:16,320
OK, just Nordon if you want to do it and see language, this is all you can write it in C language

122
00:08:16,560 --> 00:08:17,460
in C++.

123
00:08:17,460 --> 00:08:18,240
It's very simple.

124
00:08:18,240 --> 00:08:21,280
Just say we assign new rectangle.

125
00:08:21,680 --> 00:08:22,070
All right.

126
00:08:22,470 --> 00:08:25,050
This is in C language so I will remove this line.

127
00:08:25,380 --> 00:08:28,670
OK, I will do Thomond ok, let it be there.

128
00:08:29,460 --> 00:08:35,179
Now I have created an object of type rectangle using a pointer and it will be created in here.

129
00:08:35,309 --> 00:08:37,440
When you say new it will be created in heap.

130
00:08:37,919 --> 00:08:39,330
Then please lend.

131
00:08:39,330 --> 00:08:43,440
I will set it as 15 and B's breath.

132
00:08:43,860 --> 00:08:50,500
I will set it as seven landen but I have said then I will return B.

133
00:08:51,510 --> 00:08:53,870
Now let us read the function once again.

134
00:08:54,300 --> 00:09:00,060
There is a function which is creating an object of type rectangle using a pointer and it is creating

135
00:09:00,060 --> 00:09:07,210
an object in a heap and it is initializing and to 15 and seven and returning the address of that structure.

136
00:09:07,590 --> 00:09:13,830
So this is structure object is created in Hebrew and its address is written by this function inside

137
00:09:13,830 --> 00:09:14,780
the main function.

138
00:09:15,030 --> 00:09:18,090
I will take the pointer struct.

139
00:09:20,030 --> 00:09:28,400
Rectangle starboy, OK, I will call it Expedia, I will call the function thubten function is not taking

140
00:09:28,400 --> 00:09:29,210
any parameters.

141
00:09:29,210 --> 00:09:29,830
Nothing is there.

142
00:09:29,840 --> 00:09:31,610
I don't have to pass any parameters.

143
00:09:31,910 --> 00:09:35,930
OK, this is just creating an object and returning etcs.

144
00:09:36,410 --> 00:09:39,200
This function will create an object and return the address.

145
00:09:39,500 --> 00:09:48,170
Now here using Siao I will print the length and breadth length that Lantis now BTR is a pointer so I

146
00:09:48,170 --> 00:09:52,460
should use arrow like lente then N.L..

147
00:09:54,240 --> 00:09:57,660
Brett BTR, Brett.

148
00:09:59,380 --> 00:09:59,680
And.

149
00:10:01,070 --> 00:10:01,800
That's it.

150
00:10:01,820 --> 00:10:09,020
So this program is demonstrating how function can return a pointer to an object created in a heap and

151
00:10:09,020 --> 00:10:14,010
that can be accessed using mean function also it can be accessed by any function of that point.

152
00:10:14,030 --> 00:10:14,710
It is available.

153
00:10:15,320 --> 00:10:15,980
Let us run.

154
00:10:15,980 --> 00:10:18,680
We should get the result as 15 and seven.

155
00:10:19,580 --> 00:10:23,440
Yes, 15 and seven seat object is not created inside.

156
00:10:23,450 --> 00:10:29,840
Mean function object is created by one function and it is taken by mean and it is printing.

157
00:10:30,080 --> 00:10:35,310
So we who created this one functions one has created in the heap and initialized object.

158
00:10:36,110 --> 00:10:39,650
So this is a returning appointer to a structure.

159
00:10:39,860 --> 00:10:46,730
This is getting your point, a structure and the job of function is to create an object of structure

160
00:10:46,730 --> 00:10:48,190
and initialize and return it.

161
00:10:48,380 --> 00:10:51,840
So we have seen passing Parseghian structure where value passing is structure.

162
00:10:51,840 --> 00:10:56,150
Better address then returning the address of a structure.

163
00:10:56,660 --> 00:10:58,190
So that's all in this video.

164
00:10:58,550 --> 00:11:02,580
You practice this one and also you try array inside a structure.

165
00:11:03,080 --> 00:11:03,580
All right.

166
00:11:04,310 --> 00:11:05,180
So that's it.

