1
00:00:01,140 --> 00:00:06,190
In this video, I will show you a recursive function for finding some, and also I will write an iterative

2
00:00:06,210 --> 00:00:09,180
version of some of the natural numbers.

3
00:00:11,960 --> 00:00:15,350
So let us write the program that we have discussed in the previous video.

4
00:00:17,570 --> 00:00:21,290
I will write a function for finding some of a natural number using recursion.

5
00:00:24,940 --> 00:00:32,320
In some which takes parameter pgn and returns to indigent, if any value is zero,

6
00:00:35,200 --> 00:00:36,280
return zero.

7
00:00:36,700 --> 00:00:42,830
Otherwise return some of and minus one plus.

8
00:00:42,850 --> 00:00:46,120
And so that's the function that we have already seen.

9
00:00:46,870 --> 00:00:52,240
So that you have written that function not here and said the main function, let us take a variable,

10
00:00:52,240 --> 00:00:56,400
are for getting the result of that function sum and I will pass the value fine.

11
00:00:57,460 --> 00:00:58,840
Then I will print this result.

12
00:01:05,519 --> 00:01:10,740
That's it, function is really a recursive function, a function is very small because it is recursively

13
00:01:10,740 --> 00:01:11,240
defined.

14
00:01:13,050 --> 00:01:16,210
I will run the program and show you the result.

15
00:01:16,230 --> 00:01:17,560
We know it should be 15.

16
00:01:17,580 --> 00:01:18,970
So, yes, that is all we got.

17
00:01:19,150 --> 00:01:19,680
15.

18
00:01:25,600 --> 00:01:27,770
You can debug this function and check it already.

19
00:01:27,790 --> 00:01:32,620
I have shown you how to deal with it so you can see how many function calls are made and how the values

20
00:01:32,620 --> 00:01:33,730
of end are changing.

21
00:01:35,830 --> 00:01:44,410
Not the function item that is for iterative, some of the natural numbers, so here I will take a temporary

22
00:01:44,410 --> 00:01:51,520
variable is and also I assess initialized to zero in which I will store the sum of all the numbers I

23
00:01:51,520 --> 00:01:53,970
start from one eye is less than or equal to.

24
00:01:53,980 --> 00:02:00,640
And so there's a full loop I plus plus some assign some plus I every time.

25
00:02:00,640 --> 00:02:08,229
So I will be taking the values one through end and all those values are added to some then finally return

26
00:02:08,229 --> 00:02:15,790
some that is as variable as insert the main function instead of recursive sum I will call iterative

27
00:02:15,790 --> 00:02:20,080
some item then let us see Nargeolet on the program.

28
00:02:20,080 --> 00:02:21,610
We should get the output 15.

29
00:02:24,700 --> 00:02:29,360
Yes, again, we got 15, so the result, the same, that function is recursive or iterative.

30
00:02:30,790 --> 00:02:35,580
Let us debug this function and check how the iterative function is working, how this loop is working.

31
00:02:36,340 --> 00:02:41,230
I will put a breakpoint here on the function called Inside Mean, then I will run the program.

32
00:02:44,110 --> 00:02:48,590
So it has stopped at this function call, I will continue execution, it will enter it.

33
00:02:49,050 --> 00:02:55,030
I know it has entered into ice and now you have to look at this window with the value of any of a five

34
00:02:55,030 --> 00:02:59,910
and SS zero and this inside this function.

35
00:03:00,340 --> 00:03:07,060
So we have variables and S and I so you have to look at the values of these three variables here inside

36
00:03:07,060 --> 00:03:09,610
the window, see how they are changing in a loop.

37
00:03:10,300 --> 00:03:11,580
Now I is also zero.

38
00:03:11,590 --> 00:03:14,020
So it is upon the for loop now.

39
00:03:14,020 --> 00:03:16,050
It is up on the for loop now.

40
00:03:18,160 --> 00:03:25,870
Now I'm continuing I is zero then this I will be added to as here I will be added to s..

41
00:03:26,650 --> 00:03:28,500
So as also became one.

42
00:03:28,840 --> 00:03:33,730
So instead of what you can see that as has became one then continue.

43
00:03:33,730 --> 00:03:36,310
I became to just keep watching here.

44
00:03:38,990 --> 00:03:44,990
Then those are the two, as we became three, then I became three trees are dead, so has become six,

45
00:03:44,990 --> 00:03:50,870
then I become for four days added so I become S becomes 10, then I becomes five.

46
00:03:50,870 --> 00:03:55,490
Five is added to S, then S becomes 15 and I become six.

47
00:03:55,490 --> 00:03:57,280
So it terminates follow.

48
00:03:57,290 --> 00:03:58,620
Then it will return the result.

49
00:03:58,640 --> 00:04:00,530
So that result is an Arnold.

50
00:04:02,420 --> 00:04:04,970
Now, August 15, so 15 dispended.

51
00:04:07,650 --> 00:04:14,320
So we have to observe how this up is repeatedly executing and generating some of the national numbers.

52
00:04:15,060 --> 00:04:20,070
So what the functions are for the same purpose, but the approach of solving a problem or writing a

53
00:04:20,070 --> 00:04:21,070
function is different.

54
00:04:21,540 --> 00:04:23,870
This is recursive and this is iterative.

55
00:04:25,830 --> 00:04:27,390
So that's all in this video.

