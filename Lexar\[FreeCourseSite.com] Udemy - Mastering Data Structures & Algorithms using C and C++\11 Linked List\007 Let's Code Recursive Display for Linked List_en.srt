1
00:00:00,150 --> 00:00:07,320
In this video, we'll look at recursive function for displaying a list, so I'm using the previous project

2
00:00:07,500 --> 00:00:09,730
that already we have seen in the previous video.

3
00:00:09,750 --> 00:00:15,240
I have an array of elements and creating a linked list of these elements, how to create.

4
00:00:15,240 --> 00:00:16,890
Already I have written a function.

5
00:00:16,920 --> 00:00:19,840
This function will create a linked list, how it does working.

6
00:00:19,840 --> 00:00:25,800
You will come to know the incoming videos and also vetoed a function for displaying this was an iterative

7
00:00:25,800 --> 00:00:26,490
function.

8
00:00:26,520 --> 00:00:29,530
Below this I will write a recursive function for display.

9
00:00:29,580 --> 00:00:31,170
So let us write recursive function.

10
00:00:31,170 --> 00:00:37,920
It's a small function void or display and this will take a node structure as parameter B then as the

11
00:00:37,920 --> 00:00:39,240
logic we have already seen.

12
00:00:39,240 --> 00:00:48,830
That is, if E is not equal to null, then printf percentile e-space the value inside a node that is

13
00:00:48,830 --> 00:00:57,120
the data then call itself our display by passing these next NetSol as a small function on here inside

14
00:00:57,120 --> 00:00:57,990
mean function.

15
00:00:57,990 --> 00:01:02,570
I will call this our display by passing on the first letter from the program.

16
00:01:02,910 --> 00:01:08,510
It should display all the elements and the list is three, five, seven, 10, 15 years.

17
00:01:08,520 --> 00:01:11,180
These elements are displayed by this function.

18
00:01:11,220 --> 00:01:16,570
Let us debug dysfunction and see how this recursive function is working and how the stack is utilized.

19
00:01:16,590 --> 00:01:19,260
I'll put a break point on the program here.

20
00:01:19,260 --> 00:01:24,570
As we have seen in the previous video array and are linked lists, you can see complete links is already

21
00:01:24,570 --> 00:01:27,180
there because the create function was already executed.

22
00:01:27,450 --> 00:01:29,280
Linklaters created on here.

23
00:01:29,280 --> 00:01:34,740
Inside this panel you can see the function calls how the functions are being made.

24
00:01:35,070 --> 00:01:38,670
First function this mean right now and the number is zero now.

25
00:01:38,760 --> 00:01:42,420
Then it will start calling out display and it will begin calling itself.

26
00:01:42,420 --> 00:01:44,010
So this stack size will grow.

27
00:01:44,010 --> 00:01:47,460
And here also you can see how the value of B are changing.

28
00:01:47,470 --> 00:01:48,870
I will continue execution.

29
00:01:48,880 --> 00:01:51,930
Yes, it has to reach our display, our display right now.

30
00:01:51,960 --> 00:01:53,450
Ease up on fastener.

31
00:01:53,490 --> 00:01:55,110
You can see that the data is three.

32
00:01:55,110 --> 00:02:01,290
And here inside this debug panel, you can see our displays call the next equally split element three.

33
00:02:01,680 --> 00:02:04,530
Then it will call upon next node recursively.

34
00:02:04,740 --> 00:02:11,430
And you can see that the second call and the pointer is now seven seven zero and the data is five continue

35
00:02:11,820 --> 00:02:12,360
five.

36
00:02:12,780 --> 00:02:16,440
Call itself up on next node, not a node as having a seven.

37
00:02:16,440 --> 00:02:19,580
And here you can see there are three calls so far.

38
00:02:19,620 --> 00:02:21,360
So in the stop moskal it is here.

39
00:02:21,360 --> 00:02:25,230
And if and in the previous call it has call itself from this line.

40
00:02:25,230 --> 00:02:30,750
So it is doing this line and the previous call also it is in the same line now continue in seven the

41
00:02:30,750 --> 00:02:35,400
next note and go to next print fifteen and go to the next node.

42
00:02:35,400 --> 00:02:36,240
Now it is none.

43
00:02:36,240 --> 00:02:39,090
So inside this watch you can see p value is null.

44
00:02:39,420 --> 00:02:40,980
Now it will start returning back.

45
00:02:40,980 --> 00:02:42,450
It will go back to the previous node.

46
00:02:42,450 --> 00:02:43,530
Just watch the data.

47
00:02:43,800 --> 00:02:50,130
So it will be going back to previous node that is fifteen now go back to fifteen then go back to the

48
00:02:50,130 --> 00:02:50,610
end.

49
00:02:50,610 --> 00:02:56,800
Here you can watch, then go back to seven and go back to five, then go back to three and end.

50
00:02:56,880 --> 00:03:02,120
So it has call itself again and again and also it has returned back along those same nodes.

51
00:03:02,490 --> 00:03:04,110
So this is recursive display.

52
00:03:04,110 --> 00:03:10,350
Now I'll make one change here inside this recursive display instead of faceprint and display, I will

53
00:03:10,770 --> 00:03:14,610
remove the screen and I will write it after recursive call.

54
00:03:14,610 --> 00:03:19,710
Then it should print the elements while returning function should be the elements while returning.

55
00:03:19,800 --> 00:03:20,460
Let us run.

56
00:03:20,460 --> 00:03:21,630
Yes, while returning.

57
00:03:21,630 --> 00:03:24,120
When it is printing, the elements are printed in reverse.

58
00:03:24,480 --> 00:03:27,030
First fifteen and then seven.

59
00:03:27,030 --> 00:03:27,840
Five three.

60
00:03:27,840 --> 00:03:30,330
See, these elements are displayed in reverse.

61
00:03:30,330 --> 00:03:31,170
So that's it.

62
00:03:31,170 --> 00:03:34,260
In this video I have shown you recursive function.

63
00:03:34,500 --> 00:03:40,560
It is printing the elements in forward as well as in reverse while returning and backward.

64
00:03:40,560 --> 00:03:42,450
It is displaying the same interest.

65
00:03:42,450 --> 00:03:47,520
So I have shown you are display function, which is displaying the elements from left to right as well

66
00:03:47,520 --> 00:03:48,630
as from right to left.

67
00:03:48,630 --> 00:03:49,490
That is backward.

68
00:03:49,500 --> 00:03:51,470
It is spending them while they're running, that's all.

