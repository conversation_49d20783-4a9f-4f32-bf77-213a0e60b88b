1
00:00:00,960 --> 00:00:08,940
In this video, we will write a function for a recursive much sort, this project is a sorting project,

2
00:00:08,940 --> 00:00:12,170
all the sorting functions I am writing in the same project.

3
00:00:12,630 --> 00:00:17,220
So right from the Bible for the elements, we have same project.

4
00:00:17,490 --> 00:00:25,250
So if I scroll up, you can see that already I have a function for merging to list from a single urry

5
00:00:25,770 --> 00:00:28,820
as a recursive sort utilizes this function.

6
00:00:28,830 --> 00:00:30,510
So I will be directly using this one.

7
00:00:30,540 --> 00:00:32,610
We don't have to write the function once again.

8
00:00:33,600 --> 00:00:35,700
And this was the innovative Mozart.

9
00:00:35,700 --> 00:00:36,660
We already don't.

10
00:00:37,750 --> 00:00:43,750
Now, here, above the main function, I will write on recursive MARSOC, so let us write, let us call

11
00:00:43,750 --> 00:00:50,560
it as more Dussart and it should make an array of elements and low and high.

12
00:00:52,760 --> 00:00:59,690
Then if there are more than one elements, then it should sort them if law is less than high, then

13
00:00:59,690 --> 00:01:00,680
it should sort them.

14
00:01:01,100 --> 00:01:02,570
So first of all, it should find out.

15
00:01:02,930 --> 00:01:06,620
So for that, I will declare a variable that is made.

16
00:01:08,080 --> 00:01:15,760
And here inside, I will find out mid equals low plus high divided by two.

17
00:01:17,970 --> 00:01:19,590
They should be inside the brackets.

18
00:01:21,660 --> 00:01:23,190
Then perform.

19
00:01:24,380 --> 00:01:25,520
Margie, sort.

20
00:01:26,580 --> 00:01:29,520
Upon an uhry from low to mid.

21
00:01:30,960 --> 00:01:34,200
And perform much sought from.

22
00:01:36,150 --> 00:01:39,210
Upon an entry from MIT plus one to high.

23
00:01:40,640 --> 00:01:49,500
Then perform much upon an array from low to mid and mid, plus one too high, so just mentioned high.

24
00:01:50,570 --> 00:01:51,260
That's on.

25
00:01:52,320 --> 00:01:56,940
A very small program for Fireline program, so it's a recovery function.

26
00:01:58,270 --> 00:02:05,170
Now, let us call this function from main function by passing this array of elements and Luisito and

27
00:02:05,170 --> 00:02:07,780
there are elements and loss indexes nine.

28
00:02:09,639 --> 00:02:10,630
Let us run this.

29
00:02:14,320 --> 00:02:17,190
Yes, the elements are solid, yes, total.

30
00:02:18,200 --> 00:02:20,000
And elements and all of them are subject.

31
00:02:21,190 --> 00:02:26,380
And here I'm getting a warning that I have not used any so yes, I wrote nine directly in of that I

32
00:02:26,380 --> 00:02:30,340
can send minus one if I run again and the warning is gone.

33
00:02:32,140 --> 00:02:34,120
So that's all in this much sort.

34
00:02:36,340 --> 00:02:41,890
You can practice this function right on the function by yourself, by law, after watching this video,

35
00:02:42,310 --> 00:02:45,820
or you can post this video and you can type the program simultaneously.

