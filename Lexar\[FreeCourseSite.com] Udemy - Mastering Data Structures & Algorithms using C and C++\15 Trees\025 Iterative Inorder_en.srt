1
00:00:00,990 --> 00:00:08,100
Next to the small change I can convert this procedure for in order to traversal, so let us look at

2
00:00:08,109 --> 00:00:15,810
in order, let us see in order traversal in order traversal says first go to the left Chike, then print,

3
00:00:16,200 --> 00:00:16,990
then go to right.

4
00:00:17,700 --> 00:00:20,100
So left the route right.

5
00:00:20,170 --> 00:00:21,130
That's how we read it.

6
00:00:21,300 --> 00:00:23,190
Let me quickly trace the essential you.

7
00:00:24,900 --> 00:00:32,840
We need stock and we start from route then, so initially that is not done and the stack is empty.

8
00:00:33,390 --> 00:00:37,010
Now let us look at this preorder see preorder first.

9
00:00:37,020 --> 00:00:43,580
If it is not obvious, first step is printing, but then in order first we go to left side.

10
00:00:45,320 --> 00:00:46,620
Then we should not print.

11
00:00:46,880 --> 00:00:52,730
OK, let us move to left China, but ask for preorder Facebook status and go to live chat, we'll get

12
00:00:52,730 --> 00:00:56,000
pushed out of the gate and go to left China.

13
00:00:56,330 --> 00:00:58,250
OK, we will go do this.

14
00:00:58,790 --> 00:01:00,990
That again, this is not null print.

15
00:01:01,130 --> 00:01:01,990
We will not print.

16
00:01:02,150 --> 00:01:03,500
This is not perform.

17
00:01:03,800 --> 00:01:07,520
Bush and Google left China pushed the address of three and move to left.

18
00:01:07,520 --> 00:01:12,440
China can move to left China then this is not null again.

19
00:01:12,740 --> 00:01:17,900
So we should resolve this before into the stock and go to left China.

20
00:01:20,270 --> 00:01:21,470
No, this is not an.

21
00:01:22,730 --> 00:01:30,650
If it is not all these steps that are performing, we will not performing the sprints we were performing

22
00:01:30,650 --> 00:01:33,060
to step, nor does nothing else.

23
00:01:33,560 --> 00:01:37,790
So in this order, what we are doing about go to right.

24
00:01:37,790 --> 00:01:40,610
Chain washin biopunk.

25
00:01:41,930 --> 00:01:50,590
We'll get that for now before going to write childish print, before going to print in an order.

26
00:01:51,170 --> 00:01:52,740
So printing should be done now.

27
00:01:53,180 --> 00:01:54,510
We are about to go on, right?

28
00:01:54,580 --> 00:01:56,400
Chelsea is about to go on, Rachel.

29
00:01:56,690 --> 00:01:58,280
So before that faceprint.

30
00:01:58,580 --> 00:02:01,210
So it means I should print here first.

31
00:02:01,400 --> 00:02:05,410
So print this first, then move to our child.

32
00:02:06,320 --> 00:02:08,530
So print that was removed from here.

33
00:02:10,000 --> 00:02:10,660
NetSol.

34
00:02:12,120 --> 00:02:18,120
So with a little bit of observation, I can say that in the preorder before going on left child, we

35
00:02:18,120 --> 00:02:21,750
were printing, but in in order before going on, right.

36
00:02:21,750 --> 00:02:22,650
Child printed.

37
00:02:22,920 --> 00:02:24,570
So this function becomes.

38
00:02:26,050 --> 00:02:30,560
In order for a minor, change has made it in order.

39
00:02:32,160 --> 00:02:37,780
That's one thing I want to show you, what is the complexity of dysfunction in preorder?

40
00:02:38,040 --> 00:02:44,020
We saw in order also same function, just a change of position of print function.

41
00:02:44,310 --> 00:02:45,470
This is minor changes there.

42
00:02:45,900 --> 00:02:49,020
So what is the time complexity loop there loop?

43
00:02:49,500 --> 00:02:51,660
How long as many nodes are there?

44
00:02:51,660 --> 00:02:53,150
That many times it will repeat.

45
00:02:53,220 --> 00:03:00,150
So what is the time, complexity, order of an order and and what was the size of the stack.

46
00:03:00,450 --> 00:03:03,740
Depends on the height of a three year stack.

47
00:03:03,750 --> 00:03:05,010
Depends on the height of a tree.

48
00:03:05,550 --> 00:03:08,730
Now both started, for starters, little complex.

49
00:03:08,880 --> 00:03:10,200
So I'll keep the same code.

50
00:03:10,500 --> 00:03:11,190
Let us see.

51
00:03:11,340 --> 00:03:17,840
What about both legacy post starter for starters, since that first go on left side, then right.

52
00:03:18,540 --> 00:03:19,680
Then print the data.

53
00:03:20,720 --> 00:03:22,170
Go to left, also right.

54
00:03:22,190 --> 00:03:24,180
Also means you should not bring it here.

55
00:03:24,800 --> 00:03:27,400
So after finishing both left and right, we should print.

56
00:03:27,860 --> 00:03:29,720
So let us take us to our country.

57
00:03:29,720 --> 00:03:30,290
Is this fun?

58
00:03:30,320 --> 00:03:33,030
So I'll take a stack, start from here.

59
00:03:33,140 --> 00:03:36,820
See, the procedure will be similar to this one only with some changes.

60
00:03:37,100 --> 00:03:39,950
So what changes are quite there to study that?

61
00:03:40,130 --> 00:03:42,670
We will trace it with the help of that functionally.

62
00:03:42,680 --> 00:03:44,390
Actually, the function is for in order.

63
00:03:44,870 --> 00:03:47,990
But where we require changes, we will make the changes there.

64
00:03:48,320 --> 00:03:50,480
So I will follow that procedure only in traffic.

65
00:03:50,930 --> 00:03:52,840
So let us start at ease on this one.

66
00:03:52,850 --> 00:03:53,960
So please not.

67
00:03:54,200 --> 00:03:55,150
What is the first step?

68
00:03:55,430 --> 00:04:02,110
Push the rest of that note into the stack eight, then move to lift change or move to China.

69
00:04:03,560 --> 00:04:06,820
So please not so if it is not push and move, lift.

70
00:04:06,950 --> 00:04:10,430
So push that as of three and move left.

71
00:04:12,460 --> 00:04:13,810
Posters of for.

72
00:04:15,930 --> 00:04:26,700
And move left no additional, so no means what we should do else, pop out, print and go to.

73
00:04:26,700 --> 00:04:26,930
Right.

74
00:04:27,330 --> 00:04:29,390
So let us do it pop out.

75
00:04:29,640 --> 00:04:31,540
So this is deleted.

76
00:04:32,610 --> 00:04:33,450
These here.

77
00:04:35,430 --> 00:04:43,440
Print can go to right China no, don't print postretirement first, go to writing afterwards, print

78
00:04:43,590 --> 00:04:46,510
left, overate, go Google Ritcher, then print.

79
00:04:46,830 --> 00:04:48,490
So we should not print this one.

80
00:04:48,510 --> 00:04:50,340
We should not print it.

81
00:04:50,340 --> 00:04:51,270
Should not be printed.

82
00:04:52,090 --> 00:04:54,120
Pop out and go to write check.

83
00:04:54,580 --> 00:04:56,220
OK, I will go to rachet.

84
00:04:56,790 --> 00:04:57,930
I'll go to Rachel.

85
00:04:58,500 --> 00:04:59,820
So Rachel is what not.

86
00:05:00,210 --> 00:05:02,650
If it is not popular then go to Rachel.

87
00:05:02,910 --> 00:05:05,900
So which one I get at this of three or four.

88
00:05:05,910 --> 00:05:06,900
I did not print.

89
00:05:07,260 --> 00:05:08,900
I should print that for also.

90
00:05:10,170 --> 00:05:10,860
That's it.

91
00:05:11,160 --> 00:05:12,240
So the differences.

92
00:05:12,600 --> 00:05:15,320
I should push that address off for once again.

93
00:05:15,690 --> 00:05:22,500
First time it was pushed so that we can go on right side, second time we need so that we can print

94
00:05:22,500 --> 00:05:22,650
it.

95
00:05:23,040 --> 00:05:31,620
So before going to Rachel instead of that for once again into the stack, once again, push it, then

96
00:05:31,620 --> 00:05:35,900
go to Rachel so I could write on the code afterwards.

97
00:05:36,030 --> 00:05:43,320
So push it again and then go to Rachel, not d not once again about whose address we get.

98
00:05:44,160 --> 00:05:48,720
Falsettos now see the second problem, second problem.

99
00:05:49,110 --> 00:05:54,240
First time we spoke that was for said this time we are popping out.

100
00:05:54,690 --> 00:05:55,890
This is footprinting.

101
00:05:55,890 --> 00:05:57,470
We are back on for now.

102
00:05:57,480 --> 00:06:03,750
We should print now how to know that a address for Rightside and this service is for printing.

103
00:06:03,900 --> 00:06:05,070
There should be some difference.

104
00:06:06,230 --> 00:06:07,850
Yes, that's the problem.

105
00:06:08,570 --> 00:06:14,560
So what we should do when we are pushing it a second time, we should push negativities.

106
00:06:15,560 --> 00:06:17,070
I just cannot be negative.

107
00:06:17,210 --> 00:06:25,370
No, no, you convert that into an integer and store negative integer when you are using converting

108
00:06:25,370 --> 00:06:26,500
to an positive.

109
00:06:27,130 --> 00:06:28,510
OK, we can do that.

110
00:06:28,910 --> 00:06:31,010
So push didas once again.

111
00:06:31,010 --> 00:06:36,250
So put the time, first time when you're pushing push positive, second time when you're pushing this

112
00:06:36,320 --> 00:06:37,010
negative.

113
00:06:38,460 --> 00:06:45,510
So positive adjustments go to Rachel, negative response, but the data, so I will directly modify

114
00:06:45,510 --> 00:06:45,960
that one.

115
00:06:46,410 --> 00:06:50,060
And you have to trace the complete working on this example.

116
00:06:51,180 --> 00:06:52,430
OK, this is sufficient.

117
00:06:52,680 --> 00:06:54,720
If I explain, it will be confusing.

118
00:06:55,860 --> 00:07:00,270
So you take pen and paper and trace this function hypothetically right on the phone.

119
00:07:00,300 --> 00:07:04,580
So I have explained you important points and this one that we have to push that as a two time.

120
00:07:04,590 --> 00:07:05,750
So I was right on the call.

121
00:07:06,660 --> 00:07:07,390
That's all that I.

122
00:07:07,410 --> 00:07:09,300
I'll finish tracing you.

123
00:07:09,300 --> 00:07:14,950
Do you took an example three and you trace everything and the complete all by yourself.

124
00:07:15,510 --> 00:07:17,010
So there is a story in exercise.

125
00:07:17,550 --> 00:07:21,240
I'm writing the code purportedly from the stack.

126
00:07:21,390 --> 00:07:23,940
As I said, the stack will be often digitized.

127
00:07:24,210 --> 00:07:29,970
So I will take one long integer or temporary value BMB.

128
00:07:30,180 --> 00:07:37,550
OK, so I will pop out a value from the stack Bob Estie.

129
00:07:39,150 --> 00:07:44,670
So I'll get integer value in a temporary variable, not check whether it is positive or negative.

130
00:07:45,510 --> 00:07:49,290
If it is positive means we have to push again and go to write check.

131
00:07:49,590 --> 00:07:51,330
If it is negative means we should print.

132
00:07:51,480 --> 00:07:53,820
So first check if it is positive.

133
00:07:54,210 --> 00:08:02,220
If a temp is greater than zero, positive if it is positive, then do what pushes the negative value

134
00:08:02,220 --> 00:08:08,970
of temp into the stack, push the negative value into the stack, then move to right side.

135
00:08:09,360 --> 00:08:12,480
Temp is having an address but actually it is integer value.

136
00:08:12,840 --> 00:08:18,840
I type costed it into an address, then gets outside that is moving on to like.

137
00:08:20,500 --> 00:08:22,130
OK, the syntax I have written.

138
00:08:22,330 --> 00:08:24,640
Let us see how it works, OK?

139
00:08:24,660 --> 00:08:31,630
While writing the program and once again, if that is positive or negative, address once again and

140
00:08:31,780 --> 00:08:33,100
move to write.

141
00:08:33,580 --> 00:08:41,669
If it is not greater than zero else, else it is less than equal to zero and it will not be zero here.

142
00:08:41,980 --> 00:08:48,160
So it is less negative if it is negative than what we should do, print the data and again to make a

143
00:08:48,170 --> 00:08:49,050
decent number.

144
00:08:50,050 --> 00:08:57,160
So print the data, so print the data by typecasting that numeric value to address.

145
00:08:57,430 --> 00:09:00,010
So print the data and then make its numbers.

146
00:09:00,940 --> 00:09:04,120
So this is the extra work required for for Strogoff.

147
00:09:04,480 --> 00:09:09,040
We have to push the data says two times one for going on the right side, one for printing.

148
00:09:09,040 --> 00:09:10,590
So positive and negative addresses.

149
00:09:10,600 --> 00:09:13,960
We are pushing this the extra work that we required and both our.

150
00:09:15,570 --> 00:09:19,500
So that's all with the iterative version of Traversal.

