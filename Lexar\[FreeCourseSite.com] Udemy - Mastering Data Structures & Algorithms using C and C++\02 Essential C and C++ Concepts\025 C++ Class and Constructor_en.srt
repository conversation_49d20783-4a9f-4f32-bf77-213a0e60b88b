1
00:00:01,760 --> 00:00:09,170
In the previous video, I have shown you how to convert C language program to C++ program using a rectangle

2
00:00:09,170 --> 00:00:12,320
class. Now, the same program or same class.

3
00:00:12,410 --> 00:00:17,080
I will try it once again freshly as a new C++ program.

4
00:00:17,570 --> 00:00:25,330
So let us write a class for rectangle using C++. So here, first of all we should include a header file

5
00:00:25,340 --> 00:00:31,570
#include

6
00:00:34,290 --> 00:00:39,800
It maybe iostream or iostream.h, depending on the compiler you are using, and if

7
00:00:39,800 --> 00:00:47,780
you have to write suppose iostream only, then you have to say
using namespace std; So that you 

8
00:00:47,780 --> 00:00:53,660
can use the cin and cout objects for display or reading.

9
00:00:53,720 --> 00:00:57,740
I may or may not be using them but this is the thing that you have to write down in the beginning.

10
00:00:57,740 --> 00:01:00,410
If you have cin and cout in your program.

11
00:01:00,410 --> 00:01:06,580
Now let us write a class for rectangle, class rectangle.

12
00:01:07,370 --> 00:01:09,980
Inside the class, first of all, I need data remembers.

13
00:01:09,980 --> 00:01:11,470
So let us declare them as

14
00:01:11,480 --> 00:01:12,350
Private.

15
00:01:12,350 --> 00:01:13,730
So I need 2 data members.

16
00:01:13,730 --> 00:01:14,890
One is length,

17
00:01:15,080 --> 00:01:17,880
And the second one is breadth.

18
00:01:17,990 --> 00:01:19,890
So this is integer breadth.

19
00:01:20,120 --> 00:01:21,900
I'm taking both of them as integer.

20
00:01:21,920 --> 00:01:25,000
So if you want to change the data type you can change that.

21
00:01:25,380 --> 00:01:31,370
Then, the next thing I will define member functions, so I will write all of them inside public block.

22
00:01:31,520 --> 00:01:36,740
So the first thing we should have a constructor, constructor will not have any return type, but it

23
00:01:36,740 --> 00:01:38,640
may take or may not take parameter.

24
00:01:38,640 --> 00:01:44,510
So first I'm writing non-argument constructor, which doesn't take any argument. So, I should initialize

25
00:01:44,510 --> 00:01:50,480
length and breadth to some value, so I will assign both length as well as breadth equal to 1.

26
00:01:50,810 --> 00:01:54,620
So this, we can call it as a default constructor.

27
00:01:54,740 --> 00:01:58,300
So I have written them in a single line, because I need more space here.

28
00:01:58,430 --> 00:02:01,050
Then next, I will write down a parameterized constructor.

29
00:02:01,070 --> 00:02:06,280
This will be called as constructor overloading, this takes length,

30
00:02:06,350 --> 00:02:12,200
and also breadth, this I will be defining it outside using scope resolution operator.

31
00:02:12,830 --> 00:02:18,650
So this is just a prototype or a signature of a function, or the header of a function.

32
00:02:19,040 --> 00:02:21,540
So this is a constructor member function.

33
00:02:21,660 --> 00:02:28,600
Now next upon a rectangle, I may need the functions like calculating area, or calculating perimeter.

34
00:02:28,700 --> 00:02:33,880
Now, next function I should have this area function, which should return a result,

35
00:02:34,070 --> 00:02:35,480
that is integer type.

36
00:02:35,480 --> 00:02:43,340
Then one more, integer perimeter, which returns perimeter of a rectangle.

37
00:02:43,460 --> 00:02:46,090
Now these two type of functions are there.

38
00:02:46,290 --> 00:02:50,790
If you want to have other functions like get, set functions, you can write them.

39
00:02:50,990 --> 00:02:52,590
So I will show only for length,

40
00:02:52,910 --> 00:02:54,840
and breadth you can add them.

41
00:02:55,110 --> 00:02:56,140
So only for length

42
00:02:56,150 --> 00:03:02,350
I will have one function, that is a getLength, which will return

43
00:03:02,390 --> 00:03:13,340
length. So here itself I will and expanded and then I will say, return length, then next is, set length,

44
00:03:13,490 --> 00:03:14,570
to change the length.

45
00:03:14,630 --> 00:03:19,460
So in the previous video, I was calling it as changeLength, call it as

46
00:03:19,490 --> 00:03:26,010
setLength, now, void setLength function.

47
00:03:26,100 --> 00:03:30,020
Now this should change the length to a new given length.

48
00:03:30,320 --> 00:03:35,010
So directly I will write in a single line, because it has to just change the length.

49
00:03:35,080 --> 00:03:42,290
I'm changing, length = l; so these are the functions that I need and I don't have any destructor, I

50
00:03:42,290 --> 00:03:44,190
don't have to destroy anything.

51
00:03:44,300 --> 00:03:49,310
If you have to destroy anything and if you have any dynamic memory or location inside the constructor

52
00:03:49,730 --> 00:03:53,280
then you can release that memory inside a destructor.

53
00:03:53,330 --> 00:03:55,680
So, How destructor is written? It is written,

54
00:03:55,790 --> 00:03:59,060
Just say using a class name with a ~ symbol.

55
00:03:59,600 --> 00:04:05,480
So it should have a name that is rectangle, with the ~ symbol and no arguments at all.

56
00:04:06,410 --> 00:04:11,370
So these are member functions of a rectangle class, these two are constructors.

57
00:04:11,400 --> 00:04:16,079
These are the overloaded constructors, one is default constructor, the other one is parameterized, and

58
00:04:16,130 --> 00:04:22,160
these type of functions which actually perform some operations on data members of an object, we call

59
00:04:22,160 --> 00:04:23,950
them as facilitators.

60
00:04:24,230 --> 00:04:30,270
And then, this is accessor, that is, get function or getter function.

61
00:04:30,540 --> 00:04:34,040
And this is mutator that is setter function.

62
00:04:34,170 --> 00:04:36,370
And this is a destructor.

63
00:04:37,250 --> 00:04:38,840
These type of functions are there,

64
00:04:39,020 --> 00:04:42,480
I have written all types of function inside a class.

65
00:04:42,500 --> 00:04:45,060
Now this is already implemented.

66
00:04:45,230 --> 00:04:49,780
These three functions, I have to implement them, so I'll implement them outside the class.

67
00:04:49,790 --> 00:04:51,940
So after this I have to write them.

68
00:04:51,990 --> 00:04:52,290
.

69
00:04:52,580 --> 00:04:54,680
So here I will write down those functions.

70
00:04:54,680 --> 00:04:58,680
So let us write down the first function, that is constructor parameterized constructor.

71
00:04:58,940 --> 00:05:06,440
So here for implementation, first of all I should write down the class name rectangle and scope resolution

72
00:05:06,560 --> 00:05:08,740
constructor name rectangle,

73
00:05:09,050 --> 00:05:15,320
And it is taking 2 parameters, integer l and integer b.

74
00:05:15,550 --> 00:05:17,050
And there is no return type.

75
00:05:17,200 --> 00:05:18,210
So directly it starts from here

76
00:05:18,220 --> 00:05:18,420
.

77
00:05:18,710 --> 00:05:23,670
So the only thing I have to do is
length = l; and, breadth = b;

78
00:05:23,690 --> 00:05:28,910
So if at all, you have any validations, you can do them here, checking whether length is valid or not,

79
00:05:28,910 --> 00:05:36,140
breadth is valid or not. Now, the next function is area function, so I will write down implementation of the body

80
00:05:36,140 --> 00:05:37,250
of that function.

81
00:05:37,250 --> 00:05:38,450
This is the return type.

82
00:05:38,570 --> 00:05:43,810
Then, the class name rectangle, and scope resolution, the function name is area.

83
00:05:43,940 --> 00:05:47,450
It doesn't take any argument so it's also a single line function.

84
00:05:47,450 --> 00:05:53,030
So, just I will say, return length*breadth;

85
00:05:55,450 --> 00:06:02,740
Now next function is the perimeter function, so I will implement it outside, int Rectangle is the class name,

86
00:06:02,750 --> 00:06:07,100
So I have to use scope resolution, because this function is not an independent function,

87
00:06:07,100 --> 00:06:13,870
this is a part of our rectangle class, so function name is Perimeter.

88
00:06:13,970 --> 00:06:17,290
It doesn't take any argument, and it's also simple formula,

89
00:06:17,290 --> 00:06:23,970
a single line function. So just say, return 2(length + breadth);

90
00:06:24,290 --> 00:06:26,680
So that's the end of a function.

91
00:06:26,690 --> 00:06:33,110
Now the last function that is a destructor, though I don't have anything to de-allocate here, if at all

92
00:06:33,110 --> 00:06:38,900
any de-allocation is required, you can do that inside destructor and you can implement that destructor; destructor

93
00:06:38,900 --> 00:06:46,440
will not have any return type, so directly, class name and scope resolution and this is a destructor.

94
00:06:47,640 --> 00:06:48,990
So inside the destructor,.

95
00:06:48,990 --> 00:06:51,590
I don't have anything to do so I'll just leave it blank.

96
00:06:52,980 --> 00:06:53,690
That's all.

97
00:06:53,810 --> 00:06:58,950
These are the functions, I have implemented them using scope resolution outside the class.

98
00:06:59,030 --> 00:07:02,720
No I will write down the main function and use these functions.

99
00:07:02,870 --> 00:07:12,530
Let us write the main function here, int main, now the first thing I create an object of rectangle, say rectangle

100
00:07:13,190 --> 00:07:16,960
r (10,5); length and breadth.

101
00:07:17,120 --> 00:07:19,910
So this will create a rectangle with length 10,

102
00:07:20,020 --> 00:07:23,790
And breadth 5. Then, next I will call the function.

103
00:07:23,810 --> 00:07:27,060
So I need the function that is area and perimeter, I can call them.

104
00:07:27,170 --> 00:07:33,500
and I will directly display their results, so, cout<< r . area( );

105
00:07:33,650 --> 00:07:40,680
This will display area, cout<< r . perimeter( );

106
00:07:41,690 --> 00:07:50,420
This will display perimeter. If I want to change the length, I can say setLength function, I can call and

107
00:07:50,460 --> 00:07:53,270
I can change the length to 20.

108
00:07:53,380 --> 00:07:59,630
Now if I wanted to display the length then I can say, cout<< r . getLength( );

109
00:08:02,230 --> 00:08:03,650
Length will be  displayed.

110
00:08:03,650 --> 00:08:07,200
This function will return the length, and cout will display it.

111
00:08:08,210 --> 00:08:10,470
So this is the end of the main function.

112
00:08:10,530 --> 00:08:12,410
Now once the main function ends.

113
00:08:12,410 --> 00:08:17,780
This destructor will be automatically called, because the object is going outside the scope.

114
00:08:17,980 --> 00:08:19,020
Function ends.

115
00:08:19,040 --> 00:08:22,320
So a destructor will be called because the object will be destroyed.

116
00:08:23,270 --> 00:08:30,020
So this is a fresh C++ class I have written, right, from the beginning I have written everything. So in the

117
00:08:30,020 --> 00:08:31,890
previous video, I have converted,

118
00:08:32,000 --> 00:08:39,010
Now in this video I have freshly written it. Now, in my course, at very few places I have written C++ code,

119
00:08:39,049 --> 00:08:43,840
because most of the time I have written C language code, and when I have finished the topic of data structure

120
00:08:43,850 --> 00:08:46,950
like for example, array, at the end of the topic of arrays,

121
00:08:47,000 --> 00:08:52,640
I have written a C++ class for arrays. Then, linked lists, at the end of the topic, I have written a class for

122
00:08:53,300 --> 00:08:58,410
linked list, that is C++ class for linked list. So I have collected all the operations of a linked list and I

123
00:08:58,430 --> 00:09:04,040
have put them in a single class and I have defined it as a class, each and every function I have discussed

124
00:09:04,040 --> 00:09:10,430
them separately just like C language program, because if you see, in this example, if I have to discuss

125
00:09:10,510 --> 00:09:16,820
area of rectangle, then I have to discuss only a function, that is the function area of a rectangle,

126
00:09:16,820 --> 00:09:20,200
this function, I have to discuss I don't have to discuss the entire class.

127
00:09:20,480 --> 00:09:26,140
So like this, insertion in a linked list, So I have discussed this separately and I have discussed only a function,

128
00:09:26,460 --> 00:09:28,780
then later I have converted it into a class.

129
00:09:28,820 --> 00:09:31,630
So at the end of the topics you'll find C++ code.

130
00:09:31,760 --> 00:09:37,160
So there sometimes, I have converted a C language code to C++, and sometimes I have freshly written a

131
00:09:37,160 --> 00:09:38,120
class like this.

132
00:09:38,630 --> 00:09:42,890
So now you're familiar with this one, so you can easily follow the rest of the course.

133
00:09:42,980 --> 00:09:46,190
So, I hope you get a lot of benefit from this course.

134
00:09:46,210 --> 00:09:48,700
So, one more thing, I have to show you that is templates.

135
00:09:48,710 --> 00:09:50,890
How to use templates? What are templates?

136
00:09:50,940 --> 00:09:52,520
I'll discuss that in the next video.

