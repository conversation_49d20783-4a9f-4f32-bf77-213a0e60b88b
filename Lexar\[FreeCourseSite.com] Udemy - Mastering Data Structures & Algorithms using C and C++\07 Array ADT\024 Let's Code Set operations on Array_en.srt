1
00:00:00,120 --> 00:00:07,710
And the video I will show you union intersection and difference operations upon set of elements.

2
00:00:08,920 --> 00:00:16,120
On Vilvoorde, I have discussed two methods, one method for unsorted today, and one method was for

3
00:00:16,120 --> 00:00:19,680
soccer daddy and soccer RIMM was using much.

4
00:00:20,350 --> 00:00:23,410
So I will show you a method of using Marjo that is for soccer.

5
00:00:24,430 --> 00:00:29,950
So this is a program already we have written for merging and it's a single project that I'm using for

6
00:00:29,950 --> 00:00:33,010
all the functions of the operations on another.

7
00:00:34,060 --> 00:00:40,150
This is the same project, what I'm using it for operations upon, and so in the same project, I will

8
00:00:40,150 --> 00:00:45,030
use that Marjo function and I will convert it to union intersection and difference.

9
00:00:45,520 --> 00:00:48,510
So for that, first of all, let me get some set of elements.

10
00:00:50,860 --> 00:00:56,050
Facade is having to six, ten, fifteen and twenty five, so in the second set, I will ride on a few

11
00:00:56,380 --> 00:00:58,630
common values like six US Common.

12
00:01:01,570 --> 00:01:02,650
Fluffiness common.

13
00:01:03,550 --> 00:01:08,530
Then you perform union, we should not have duplicates, so for performing union, I can take the help

14
00:01:08,530 --> 00:01:14,220
of multi-function, I will probably that same function and I will change it for union.

15
00:01:14,890 --> 00:01:19,420
So here I will copy that function and I will call it as union.

16
00:01:20,510 --> 00:01:27,230
So you knew for certain that is same as emerging in Mooresboro says this is a veritable combat elements

17
00:01:27,230 --> 00:01:33,830
of 81 and 82 and copied elements in 33 and the sorted order.

18
00:01:35,800 --> 00:01:40,270
So the logic for performing union will also be the same, only the difference is that if the elements

19
00:01:40,270 --> 00:01:45,510
are same, then we should take only one copy and increment in Buderus.

20
00:01:45,530 --> 00:01:47,350
So I will modify this.

21
00:01:49,260 --> 00:01:50,790
See, this is if a.

22
00:01:52,860 --> 00:01:59,970
So this is if a raven element is a smaller than else, so I will modify this and here I will ride on

23
00:01:59,970 --> 00:02:03,030
if I retools element is a smaller.

24
00:02:04,200 --> 00:02:16,260
That is E of G is smaller than a everyone of if I then copy this otherwise, otherwise if both are equal,

25
00:02:17,370 --> 00:02:19,980
if both are equal, then added three.

26
00:02:21,400 --> 00:02:22,210
Of a.

27
00:02:23,290 --> 00:02:24,880
Of C++.

28
00:02:26,160 --> 00:02:29,460
Poppy, any one of the elements, so I will, Poppy, an element from everyone.

29
00:02:32,300 --> 00:02:33,620
And eight plus plus.

30
00:02:34,800 --> 00:02:40,080
And followed by this, I will also perform plus plus and I should increment both.

31
00:02:44,840 --> 00:02:49,820
So this should be inside the bracket because I have more than one statement inside ELT's block.

32
00:02:51,450 --> 00:02:52,350
Yes.

33
00:02:53,430 --> 00:02:58,680
See, this is if everyone celebrated the smaller and the second one is if a retools element is a smaller

34
00:02:58,920 --> 00:03:00,630
than third one, if both are equal.

35
00:03:00,660 --> 00:03:04,590
And Poppy, any one element and increment both I and Jane.

36
00:03:05,770 --> 00:03:12,490
And one more thing I have to modify here is that land of towed array will not be equal to some of lence

37
00:03:12,490 --> 00:03:13,150
of anyone.

38
00:03:13,150 --> 00:03:16,090
And I do what I can take it as key.

39
00:03:16,180 --> 00:03:24,450
Whatever the value of case, that is going to be a land of body and size, then sizes are 10.

40
00:03:25,150 --> 00:03:28,150
So that said, let us try this and stuff.

41
00:03:29,920 --> 00:03:30,670
So that's it.

42
00:03:30,700 --> 00:03:35,320
This has became a function for union of solid bodies.

43
00:03:35,800 --> 00:03:38,980
If the arrays are sorted, I will change the function name.

44
00:03:39,010 --> 00:03:40,750
I will call it as union.

45
00:03:41,970 --> 00:03:48,480
Now, let us run the program, I should get a union of these two arrays so there are no duplicates.

46
00:03:48,870 --> 00:03:54,590
So the elements that I expect are two, three, six, seven, 10, 15, 20 and 25.

47
00:03:54,960 --> 00:03:56,790
So six and 15 are repeating.

48
00:03:57,180 --> 00:03:58,890
So they should be taken only one time.

49
00:04:00,060 --> 00:04:00,690
Let us run.

50
00:04:05,970 --> 00:04:09,370
Yes, two, three, six, seven, 10, 15, 20 and 25.

51
00:04:09,390 --> 00:04:11,130
So there are no duplicates in this one.

52
00:04:12,160 --> 00:04:13,780
So this is working perfect.

53
00:04:15,500 --> 00:04:16,850
So this was for union.

54
00:04:20,180 --> 00:04:26,690
Then I will copy this function for union, I will make the changes and I will make it as a function

55
00:04:26,690 --> 00:04:28,490
for intersection.

56
00:04:34,610 --> 00:04:36,550
Here, I will paste it then.

57
00:04:37,040 --> 00:04:40,580
First of all, I will change the function name to intersection.

58
00:04:43,330 --> 00:04:44,830
Function name was intersection.

59
00:04:47,680 --> 00:04:53,230
So in your compiler, if you are unable to give long names, you can short name an intersection.

60
00:04:53,230 --> 00:04:55,790
We should copy an element only if they are equal.

61
00:04:56,290 --> 00:04:58,660
So I should not copy these elements.

62
00:04:59,650 --> 00:05:06,260
So instead of copping an element, I should simply say I just move to the next element, don't copy.

63
00:05:07,260 --> 00:05:08,310
And in this case.

64
00:05:11,710 --> 00:05:18,760
A second element is smaller than I should just increment G if the secondary element is a smaller than

65
00:05:19,000 --> 00:05:23,140
elements, both are equal C, this one are even smaller.

66
00:05:23,170 --> 00:05:28,270
This one is for array to element is smaller if both are equal than probably any one of the elements,

67
00:05:28,270 --> 00:05:32,630
either from marijuana or added to an increment all iji and K.

68
00:05:33,730 --> 00:05:36,540
And here we should not copy the remaining elements.

69
00:05:36,550 --> 00:05:38,290
We should take only the common element.

70
00:05:38,300 --> 00:05:40,870
So remove the code for the remaining elements.

71
00:05:42,580 --> 00:05:46,260
Ufologist before popping the remaining elements either from Larry, one or two.

72
00:05:48,240 --> 00:05:56,130
That's all stopping only common elements, so this condition is if I write the condition, then everyone's

73
00:05:56,820 --> 00:05:59,970
element of fine is equal to.

74
00:06:01,150 --> 00:06:02,200
I retools.

75
00:06:03,180 --> 00:06:08,260
Aw, gee, I have it the conditional, so otherwise this just eltis sufficient.

76
00:06:08,790 --> 00:06:10,170
Now the code is readable.

77
00:06:11,220 --> 00:06:17,380
You can read every condition here, that is if there's a smaller Rezaee, if this is smaller than English.

78
00:06:17,680 --> 00:06:19,290
If both are equal and copy them.

79
00:06:22,600 --> 00:06:27,490
So if you come back to the main function I have to present and the common elements are six and 15,

80
00:06:27,910 --> 00:06:33,400
so if I call a function that is intersection, then I should get down to six and 15.

81
00:06:37,870 --> 00:06:42,420
And falling into intersection function, then display totally.

82
00:06:45,030 --> 00:06:45,960
Let us run this.

83
00:06:49,290 --> 00:06:53,970
Yes, we got the result of six and 15 that is in the fiction of these two sets.

84
00:06:55,510 --> 00:07:01,210
As these two sides are sorted, so we are able to use the logic, same as more logic, and we are able

85
00:07:01,210 --> 00:07:02,320
to find intersection.

86
00:07:03,890 --> 00:07:09,440
Now, the cooperation that we have to show you is the difference that is at even minus Arietta, we

87
00:07:09,440 --> 00:07:10,580
will do so.

88
00:07:10,580 --> 00:07:18,230
I will copy the Code of Union, so I will paste it and I will modify that one for a different operation

89
00:07:19,440 --> 00:07:20,290
for the union.

90
00:07:20,630 --> 00:07:23,210
So I will make it as a difference function.

91
00:07:25,670 --> 00:07:30,960
Difference, mindset, difference, I should copy the element only from first.

92
00:07:31,730 --> 00:07:39,140
So this part is OK for first and second element, I should not copy simply I should incremented shapelessness.

93
00:07:40,590 --> 00:07:44,760
And when the elements are equal, I should not copy the element at all.

94
00:07:44,790 --> 00:07:46,080
That's what the differences are.

95
00:07:46,440 --> 00:07:54,060
So here I should not copy any agreement, but I should implement I as well as a G when they are equal

96
00:07:54,270 --> 00:07:55,560
and both I should implement.

97
00:07:57,560 --> 00:08:03,110
And I should copy the remaining elements only from everyone, I should not copy the remaining elements

98
00:08:03,120 --> 00:08:07,370
from to even if there are any remaining elements that we should not copy them.

99
00:08:09,640 --> 00:08:10,160
That's it.

100
00:08:10,480 --> 00:08:15,310
So here are the elements of the story are copied, secondary elements are kept.

101
00:08:15,310 --> 00:08:18,630
If both are equal, then skip both of them.

102
00:08:20,860 --> 00:08:26,050
So here inside the main function, if I take a difference, A minus B, then six will not be there,

103
00:08:26,050 --> 00:08:27,090
15 a lot better.

104
00:08:27,400 --> 00:08:29,440
I should get to 10 and 25.

105
00:08:31,230 --> 00:08:33,780
So let us call this function difference.

106
00:08:39,900 --> 00:08:41,850
Yes, to ten and twenty five.

107
00:08:46,530 --> 00:08:47,400
So that's it.

108
00:08:47,430 --> 00:08:51,540
These are the three functions we have seen and there is one more function I have shown you that this

109
00:08:52,200 --> 00:08:54,980
set of membership operation that is nothing but search.

110
00:08:54,990 --> 00:09:00,510
So either you can use a linear search or binary search, same function, you can probably once again

111
00:09:00,510 --> 00:09:04,830
and you can change the name as a set of membership function.

112
00:09:07,240 --> 00:09:12,610
So using my algorithm, I have shown you three different operations like union in the fictional difference.

113
00:09:14,710 --> 00:09:20,830
And the first method for unsorted that is you have to implement those functions so there is a student

114
00:09:20,830 --> 00:09:21,430
exercise.

