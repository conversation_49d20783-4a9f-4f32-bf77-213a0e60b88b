1
00:00:00,490 --> 00:00:05,830
Now, let's talk about two dimensionality, see, already in the previous video, we have seen a damage

2
00:00:05,830 --> 00:00:10,490
dimension UTI in programming languages, we can have a multidimensional.

3
00:00:11,500 --> 00:00:15,520
So basically we use a one dimensional, two dimensional rightmost.

4
00:00:15,520 --> 00:00:22,240
We go for three dimensions, but languages allow us to declare and dimension arrays so commonly use

5
00:00:22,240 --> 00:00:29,200
one second one as to really this is mostly useful for implementing mattresses or for table or data.

6
00:00:30,310 --> 00:00:35,400
Now let us see how to create a two dimensional array in C and C++.

7
00:00:35,740 --> 00:00:40,390
There are three methods of creating a two dimensional UTI, so let us look at them.

8
00:00:42,750 --> 00:00:51,750
First method is a normal declaration of a two dimensional body along that name of another type of energy

9
00:00:52,050 --> 00:00:56,950
and dimensions, let us say I want a body of size, a three by four.

10
00:00:57,780 --> 00:01:04,349
So we visualize that idea of a three by four or five will be created inside me memory with the three

11
00:01:04,349 --> 00:01:06,600
rules and four columns.

12
00:01:09,110 --> 00:01:16,100
The end result will be zero one two four rows and zero one, two, three, four columns.

13
00:01:17,130 --> 00:01:23,670
We represent a two dimensional array and rectangular form that is like a box, but really the memory,

14
00:01:23,670 --> 00:01:25,150
a look at it will be linear.

15
00:01:25,290 --> 00:01:31,200
For example, if you want to look at the addresses, let us assume that the first Byatt's address is

16
00:01:31,200 --> 00:01:33,310
two hundred and two, not one.

17
00:01:33,600 --> 00:01:37,870
Then this is two and three, four and five, six and seven.

18
00:01:38,310 --> 00:01:44,190
So this is a memory for each integer as we are assuming that integer take two weeks or two to buy it

19
00:01:44,190 --> 00:01:44,730
for each.

20
00:01:45,120 --> 00:01:52,750
This is two hundred and six and seven and this will be eight and nine, 10, 11, 12 and 13.

21
00:01:53,250 --> 00:01:59,520
So this means that in real, the memory will be allocated like a single dimension.

22
00:01:59,520 --> 00:02:00,120
Uhry.

23
00:02:01,520 --> 00:02:05,000
The three to four, well, indigenous.

24
00:02:08,310 --> 00:02:15,020
So memory will be a look at it like a single dimensionally, but the compilers allow us to access data,

25
00:02:15,270 --> 00:02:20,870
single dimensional UTI as a tool, the study with the rule number and column number.

26
00:02:21,240 --> 00:02:25,290
So any location, if we want to access, then we can see aof.

27
00:02:25,290 --> 00:02:30,710
Let us suppose here I want to try to value 15, then I can see of one comma two.

28
00:02:31,050 --> 00:02:35,790
This is one rule number two column no value 15.

29
00:02:37,140 --> 00:02:40,980
So we can access to the array with the two indices that serial number and column number.

30
00:02:41,970 --> 00:02:47,270
How compiler will allow us to access other Shandor inside the main memory of a single dimension.

31
00:02:47,610 --> 00:02:50,160
We will learn it in the coming radius.

32
00:02:51,730 --> 00:02:58,470
So if we want to initialize the studio, then we can directly mention the list of elements here, like

33
00:02:58,510 --> 00:03:03,000
three rows and four columns or four stories, having four elements one, two, three, four.

34
00:03:03,310 --> 00:03:08,050
And the second row is having elements, two, four, six, eight, four elements are there.

35
00:03:08,410 --> 00:03:13,480
And Turturro is having the elements, three, five, seven, nine.

36
00:03:15,700 --> 00:03:24,850
So today they will be initialise, so declaration and the initialise, this is common in C and C++,

37
00:03:24,850 --> 00:03:32,350
the same syntax, its use, and they will be created inside STAC because it is just like a variable.

38
00:03:32,380 --> 00:03:35,020
There is no new operator use use.

39
00:03:35,230 --> 00:03:39,040
So once you use a new operator, then only the array will be created in a heap.

40
00:03:39,460 --> 00:03:42,730
So this is the first method of creating an inside stack.

41
00:03:43,480 --> 00:03:47,400
Now let us look at the second method, how we can create a two dimensional early.

42
00:03:48,880 --> 00:03:57,610
We can take an array of pointers, so this is a pointer and does an array of size of three, so they

43
00:03:57,610 --> 00:03:59,520
want the same size three by four.

44
00:03:59,530 --> 00:04:05,930
So I have taken array of size three as this is an array, so normal variable it will be created inside

45
00:04:05,930 --> 00:04:06,460
the stack.

46
00:04:06,610 --> 00:04:11,580
But this is not an array of integers, this array of integer pointers.

47
00:04:12,010 --> 00:04:13,930
So I will draw that picture for that one.

48
00:04:14,200 --> 00:04:16,930
So this is a will be created like this.

49
00:04:19,240 --> 00:04:20,690
And these are all pointers.

50
00:04:21,040 --> 00:04:24,520
So this is index a zero, index one, an index to.

51
00:04:26,020 --> 00:04:27,130
I have just a.

52
00:04:28,100 --> 00:04:35,840
Three of four, three pointers, no words to damage the levee, so I will create two dimensionality

53
00:04:35,900 --> 00:04:36,820
in Hape.

54
00:04:37,160 --> 00:04:45,280
So what actually I want I want an array of a size four so that this pointing to this pointing there.

55
00:04:45,530 --> 00:04:50,570
Then again, an array of five, four, not again an array of size four.

56
00:04:50,840 --> 00:04:56,020
So I should have three arrays of size of four created in Hape.

57
00:04:59,080 --> 00:05:07,030
So this is nothing but array of array of arrays, so there are three arrays and they have this array

58
00:05:07,030 --> 00:05:13,510
pointing to those three arrays and together they form the same two dimensional URTE.

59
00:05:14,800 --> 00:05:17,320
So this is the second method of creating two dimensional.

60
00:05:18,010 --> 00:05:24,930
Let us see the syntax how I can create these arrays so I have an array of size three.

61
00:05:25,150 --> 00:05:28,480
So continuation of this one, I should say of a zero.

62
00:05:28,480 --> 00:05:37,450
Assign new interfaces for such an array of size four will be created whose addresses will be stored

63
00:05:37,450 --> 00:05:38,270
in here of zero.

64
00:05:38,560 --> 00:05:40,930
Similarly, I should do it for each of one.

65
00:05:40,930 --> 00:05:47,260
Assign new end of size of four and I should do it for next location.

66
00:05:47,320 --> 00:05:50,860
Enough to assign new ingolf signs for.

67
00:05:53,090 --> 00:06:00,550
So I should create memory in Haiti before all those theories and this array will be present in the stack

68
00:06:00,560 --> 00:06:06,770
sort of pointing there, not even this structure I can access just like a normal I suppose I want to

69
00:06:06,770 --> 00:06:15,620
store a value here, 15 one gunman to then I can see that Erni, which is a pointer aof one minute this

70
00:06:15,620 --> 00:06:15,890
one.

71
00:06:16,850 --> 00:06:18,110
It's a two minutes.

72
00:06:18,150 --> 00:06:20,570
This one I can store value 15.

73
00:06:21,710 --> 00:06:23,500
So in the same way I can access it.

74
00:06:24,720 --> 00:06:26,160
So these are the two methods.

75
00:06:26,550 --> 00:06:27,930
I'll show you third method.

76
00:06:28,620 --> 00:06:30,180
Now let us look at third method.

77
00:06:31,170 --> 00:06:32,330
See third method.

78
00:06:32,580 --> 00:06:39,030
I can take a double pointer, see one was to demonstrate it indirectly inside the stack.

79
00:06:39,420 --> 00:06:44,520
Now, this is an array of pointers inside the stack, but actually is in the heap.

80
00:06:44,880 --> 00:06:47,440
Now, almost everything in the heap now.

81
00:06:47,700 --> 00:06:50,430
So for that, I will use a double pointer.

82
00:06:50,850 --> 00:06:58,120
So declaration of double pointer, integer star star eight as thick as any.

83
00:06:58,530 --> 00:07:03,780
So this a is a pointer double pointer and this is like a variable.

84
00:07:03,810 --> 00:07:05,890
There is no new operator here.

85
00:07:05,910 --> 00:07:06,980
New is not used.

86
00:07:07,290 --> 00:07:09,960
So this will be created inside STAC.

87
00:07:11,190 --> 00:07:23,040
No, I need this as well as Deaver's for for this fostoria, I should say, a fine neun integer of size

88
00:07:23,310 --> 00:07:23,850
three.

89
00:07:24,150 --> 00:07:25,410
I'm creating this uhry.

90
00:07:25,500 --> 00:07:29,190
So you remember this array was nothing but array of integer pointers.

91
00:07:29,430 --> 00:07:32,010
So, yes, I should mention start here.

92
00:07:32,280 --> 00:07:35,720
So this is an array of pointers of type integer.

93
00:07:36,000 --> 00:07:41,790
So an array of appointers of type integer will be created of size three and this will be pointing on

94
00:07:41,790 --> 00:07:42,240
this one.

95
00:07:42,600 --> 00:07:45,510
So if I take this as these are zero one and two.

96
00:07:47,380 --> 00:07:51,750
So this itself is created an array of pointers created in him.

97
00:07:52,150 --> 00:08:00,130
Now I will prepare other areas, so I have to create these three arrays and assign them to these three

98
00:08:00,130 --> 00:08:00,900
pointers.

99
00:08:02,630 --> 00:08:03,680
So the indices are.

100
00:08:05,160 --> 00:08:06,120
023.

101
00:08:09,250 --> 00:08:17,780
The how to create those three, so same method as I did here, I should say, of zero, assign new ingolf

102
00:08:17,800 --> 00:08:24,390
size four, then aof one have to assign new.

103
00:08:25,270 --> 00:08:28,730
So that's all the entire structure is in a heap.

104
00:08:28,930 --> 00:08:32,169
Now only this double pointer is inside stack.

105
00:08:32,169 --> 00:08:33,250
Because this is new.

106
00:08:33,260 --> 00:08:33,870
This is new.

107
00:08:33,909 --> 00:08:34,400
This is new.

108
00:08:34,690 --> 00:08:37,570
So this is created with new and all three are created with new.

109
00:08:37,809 --> 00:08:41,140
Everything is in him so that all the treatment.

110
00:08:42,730 --> 00:08:48,220
So these are the three methods of creating a two dimensionality completely inside the stack, partial

111
00:08:48,230 --> 00:08:51,830
in the stack, partially making everything in a heap now.

112
00:08:52,450 --> 00:08:53,550
Now, one more thing.

113
00:08:53,780 --> 00:08:55,930
See, every time I have used new operator.

114
00:08:55,960 --> 00:09:01,750
So if you want to know how it is done in C language, then you know very well that mellark function

115
00:09:01,750 --> 00:09:03,610
is used for allocating the memory.

116
00:09:03,880 --> 00:09:10,180
So variable we have use new, we have to use mellark function there because in C language mellark means

117
00:09:10,180 --> 00:09:15,100
memory in hape not lasting how to access this two-dimensional arrays.

118
00:09:15,430 --> 00:09:21,100
These two-dimensional arrays can be accessed using nested to for loops.

119
00:09:21,460 --> 00:09:30,160
So for accessing this or any of these arrays I can use indices i.e. let's say starts from zero in less

120
00:09:30,160 --> 00:09:38,410
than three because there are three rows and next to one more in the for loop Jyothi in zero G is less

121
00:09:38,410 --> 00:09:41,490
than four because there are four columns C++.

122
00:09:41,800 --> 00:09:46,470
So in all my examples have taken three rules and four columns or three rows and four columns.

123
00:09:46,870 --> 00:09:56,620
So accessing all these elements so I can see if I g whatever I want to do here, but I want to print

124
00:09:56,620 --> 00:09:59,440
them or x and modify them or initialize them.

125
00:09:59,770 --> 00:10:01,870
Whatever I want to do, I can do it.

126
00:10:02,110 --> 00:10:05,500
But this will help me traverse through all the elements.

127
00:10:05,500 --> 00:10:08,050
And the problem is not that they are all vital.

128
00:10:08,380 --> 00:10:09,760
So it will access like this.

129
00:10:10,510 --> 00:10:12,850
First of the sedimented next, next, next.

130
00:10:13,090 --> 00:10:18,210
Then it will come to make through all the elements and all the next all the elements in the room now

131
00:10:18,280 --> 00:10:20,380
for accessing all the elements in it all.

132
00:10:20,950 --> 00:10:24,280
This is the loop for different rules, for changing the rules.

133
00:10:24,610 --> 00:10:26,770
This is the loop, the loop of there.

134
00:10:27,280 --> 00:10:28,120
So that's all.

135
00:10:28,120 --> 00:10:31,750
These are the few basic things that one must know about a two dimensional ludi.

136
00:10:32,050 --> 00:10:33,210
So I have explained them.

137
00:10:33,490 --> 00:10:36,510
Now, let us continue with the other topics in an array.

