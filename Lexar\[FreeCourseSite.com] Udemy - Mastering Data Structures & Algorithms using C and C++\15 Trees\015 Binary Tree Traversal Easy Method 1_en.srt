1
00:00:00,510 --> 00:00:05,250
Let us look at the easy method for finding the traversal of a binary tree.

2
00:00:06,430 --> 00:00:11,350
There are more than one easy methods out there, so I will show you that first method, so I'm going

3
00:00:11,350 --> 00:00:15,180
to show for all three traversal so I have to consume three, three times.

4
00:00:15,790 --> 00:00:16,480
That is fine.

5
00:00:16,480 --> 00:00:21,940
Preorder up on the street, preorder for finding a preorder, draw a line at the bottom of a tree,

6
00:00:22,270 --> 00:00:25,030
then from every note from the left hand side of A..

7
00:00:25,300 --> 00:00:26,520
So this is the left hand side.

8
00:00:26,530 --> 00:00:29,490
This is bottom or the middle and this is the right hand side.

9
00:00:30,340 --> 00:00:37,270
So from left hand side, stretch a line and connect to this bottom line from every note you do that

10
00:00:37,540 --> 00:00:43,620
and these lines should not overlap or intersect, need not be a straight line just to connect them.

11
00:00:43,660 --> 00:00:46,270
That's enough connected like this.

12
00:00:48,640 --> 00:00:54,900
Connected, connected, all nodes are connected from left hand side to this bottom line.

13
00:00:56,810 --> 00:01:02,660
Now, scan this line from left to right and see which notes are connecting this first node is a OK.

14
00:01:03,020 --> 00:01:10,440
The next is B, this line belongs to be OK, B, then B, then this belongs to E.

15
00:01:11,390 --> 00:01:12,710
This belongs to see.

16
00:01:13,910 --> 00:01:15,110
This belongs to F.

17
00:01:15,870 --> 00:01:17,110
This belongs to G.

18
00:01:17,660 --> 00:01:20,210
So the scan from left to right and take all the elements.

19
00:01:20,480 --> 00:01:22,220
This is the pre order of a tree.

20
00:01:24,150 --> 00:01:31,950
So simple, now let us look at in order up on the screen, in order to draw a line at the bottom, not

21
00:01:31,950 --> 00:01:33,910
from the middle or the bottom of the north.

22
00:01:34,560 --> 00:01:35,940
Connect the line to this one.

23
00:01:36,570 --> 00:01:38,950
Connect the line from the middle.

24
00:01:39,120 --> 00:01:41,110
That is in between left and right.

25
00:01:41,640 --> 00:01:42,020
Right.

26
00:01:42,510 --> 00:01:48,840
So there are no left right to draw a line by line to try to scan this line and take the elements which

27
00:01:48,840 --> 00:01:50,940
are what are connecting from left to right.

28
00:01:51,270 --> 00:01:57,750
So first of these, then B is A, F, C, g.

29
00:01:59,790 --> 00:02:00,770
This is in order.

30
00:02:02,150 --> 00:02:03,160
Then tostada.

31
00:02:05,650 --> 00:02:07,720
I guess you have a list of what you have to do.

32
00:02:07,990 --> 00:02:11,630
Yes, Ganek line from right inside of a..

33
00:02:11,830 --> 00:02:13,530
So from right hand side of A..

34
00:02:14,020 --> 00:02:15,400
Then this right hand side.

35
00:02:15,580 --> 00:02:16,570
Right hand side.

36
00:02:17,620 --> 00:02:23,470
And this right side, right side of the north and central, this line and take the north, which are

37
00:02:23,470 --> 00:02:28,480
connecting from left to right, b, e next.

38
00:02:28,690 --> 00:02:38,200
This is B, F, G, C, a, c, a, this is Fostoria.

39
00:02:40,780 --> 00:02:44,890
For anything, you can do this and you can easily find the travels.

40
00:02:45,830 --> 00:02:48,950
Next, I want to show you one more method, simple method for finding.

