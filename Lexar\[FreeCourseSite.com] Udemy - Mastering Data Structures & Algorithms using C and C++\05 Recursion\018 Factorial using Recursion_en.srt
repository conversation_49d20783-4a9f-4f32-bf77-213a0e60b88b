1
00:00:00,300 --> 00:00:05,580
Now, let us discuss the problem, finding factorial of a given number, so for this also we can define

2
00:00:05,580 --> 00:00:10,610
a recursive function and even using iteration that is using <PERSON><PERSON><PERSON>, also reconverted.

3
00:00:11,190 --> 00:00:15,840
Let us, first of all, devise a recursive function for this one.

4
00:00:16,200 --> 00:00:23,310
So first, I will define what does it mean by a factor of a number C factorial is representative of

5
00:00:23,310 --> 00:00:23,940
the symbol.

6
00:00:24,240 --> 00:00:27,900
So this means factor the love and the means.

7
00:00:28,230 --> 00:00:39,190
One in Bhuto into three in two goes on two and so it means factorial of five is one and two, two and

8
00:00:39,190 --> 00:00:43,360
two, three in the four and multiplied by five.

9
00:00:43,920 --> 00:00:45,770
So this will be 120.

10
00:00:46,140 --> 00:00:49,420
If I want to play all these things, I'll get down to 120.

11
00:00:50,040 --> 00:00:57,160
So for n I have to continue to n so for five continue till five or ten, I should continue until ten.

12
00:00:57,690 --> 00:01:03,060
So just like some of the national numbers, this is multiplication of first unnatural numbers.

13
00:01:03,570 --> 00:01:04,860
That is called as factorial.

14
00:01:05,730 --> 00:01:07,380
And even I'll tell you one more thing.

15
00:01:07,620 --> 00:01:11,710
That zero factorial is also possible and its answer is one.

16
00:01:12,090 --> 00:01:17,060
So even one factorial is also one and zero factorial is also one.

17
00:01:17,460 --> 00:01:19,120
So both the results are one.

18
00:01:19,890 --> 00:01:22,610
Now, let us define this recursively.

19
00:01:23,160 --> 00:01:33,240
If I say fact of a number, N is one in two, two and two, three goes on up one more time.

20
00:01:33,240 --> 00:01:37,300
I will include that this one more number and minus one in the end.

21
00:01:38,310 --> 00:01:47,130
So in this, if I observe factor of a number from one to end this portion, if I take this is factorial

22
00:01:47,130 --> 00:01:48,630
off and the minus one.

23
00:01:49,200 --> 00:01:51,390
Yes, this can be the math factor.

24
00:01:51,390 --> 00:01:52,430
Eleven minus one.

25
00:01:52,710 --> 00:02:00,330
So factorial off and as a factorial of and the minus one multiplied by any.

26
00:02:00,390 --> 00:02:04,380
If I multiply that with and then I get that factor in.

27
00:02:06,050 --> 00:02:17,660
So this can be defined recursively factorial of any number and as factorial of and the minus one multiplied

28
00:02:17,660 --> 00:02:26,120
by and one end is greater than zero when anything close to zero four zero also answer is one.

29
00:02:26,450 --> 00:02:27,920
So return one.

30
00:02:30,110 --> 00:02:30,670
That's it.

31
00:02:31,520 --> 00:02:38,150
So this is the recursive definition, and once you have a recursive relation or mathematical formula,

32
00:02:38,520 --> 00:02:43,460
then you can convert that recursive formula into C C++ function.

33
00:02:43,490 --> 00:02:45,470
So let me write on a function for this one.

34
00:02:46,070 --> 00:02:50,450
Integer factorial of M.

35
00:02:53,190 --> 00:02:57,720
If and is equal to zero right on.

36
00:02:59,480 --> 00:03:06,360
When it is zero, return one, return one and return this one.

37
00:03:06,620 --> 00:03:14,860
So I return factorial off and the minus one in the end.

38
00:03:15,470 --> 00:03:22,190
So the function of writing a recursive function is so easy you can directly convert a formula into recursive

39
00:03:22,190 --> 00:03:24,380
function, not same function.

40
00:03:24,380 --> 00:03:26,770
Even we can write using iteration.

41
00:03:26,780 --> 00:03:27,770
That is a loop.

42
00:03:28,680 --> 00:03:30,960
Right, and this is using recursion.

43
00:03:31,670 --> 00:03:36,830
Now, it is more similar to some of us to a natural number, if you remember in the previous example,

44
00:03:36,830 --> 00:03:38,090
we saw a similar function.

45
00:03:38,450 --> 00:03:42,080
Only the difference was here it was plus and one more difference.

46
00:03:42,080 --> 00:03:45,850
Rigdon Zero was dead instead of one because the multiplication is there.

47
00:03:46,130 --> 00:03:47,060
So it should be one.

48
00:03:48,200 --> 00:03:49,550
So the function is similar.

49
00:03:49,580 --> 00:03:52,490
Now, if you do analysis, that analysis will be the same.

50
00:03:52,940 --> 00:03:57,140
The size of the memory required depends on the value that are passing.

51
00:03:57,140 --> 00:04:03,800
If you find functional of five that is factorial of five, then it will be making six calls.

52
00:04:03,920 --> 00:04:05,680
So the height of the stack will be six.

53
00:04:06,110 --> 00:04:12,800
So the amount of memory consumed as part of N and the time number of calls depends on the value that

54
00:04:12,800 --> 00:04:15,440
you're passing like four or five, which will making six calls.

55
00:04:15,740 --> 00:04:20,959
So again, it is out of ends just in the previous video for finding some of us to national and what

56
00:04:20,959 --> 00:04:22,580
we have done, similar analysis.

57
00:04:22,590 --> 00:04:27,260
So I want to avoid analysis here because all that we have done analysis for this one.

58
00:04:27,560 --> 00:04:33,230
So announcers are seen now writing a function for this one is a student exercise.

59
00:04:33,240 --> 00:04:35,620
So you can write on the function by yourself.

60
00:04:36,530 --> 00:04:39,860
So that's all about factorial of a given number.

