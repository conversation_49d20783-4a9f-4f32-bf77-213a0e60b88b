1
00:00:00,180 --> 00:00:03,640
And this video will look at the lead operation on our list.

2
00:00:03,930 --> 00:00:09,660
There are two cases for deletion, just like a linear linguist that if you have seen so to get us out

3
00:00:09,660 --> 00:00:14,660
there, that is deleting first note or deleting from a given index, deleting a note from a given index

4
00:00:14,670 --> 00:00:19,340
or deleting first as a special case and deleting any other known as a common case.

5
00:00:19,350 --> 00:00:20,560
And it's another case.

6
00:00:21,650 --> 00:00:26,200
So first we will see how to delete first, nor what are the steps required.

7
00:00:27,090 --> 00:00:30,100
So you want to delete this after deleting this note?

8
00:00:30,270 --> 00:00:33,530
This should become the first node first, just pointing here.

9
00:00:33,540 --> 00:00:35,100
So I should move to the next node.

10
00:00:35,680 --> 00:00:37,520
So I should move first.

11
00:00:37,530 --> 00:00:43,080
But before moving first, let us take a pointer upon this node and then move fast.

12
00:00:43,140 --> 00:00:45,510
So first we'll move to next node.

13
00:00:46,140 --> 00:00:49,630
So take this one first and move us to the next node.

14
00:00:50,190 --> 00:00:56,610
So this is the next move now with the help of Pointer to take out the value if you want, take out the

15
00:00:56,610 --> 00:00:58,470
value and then delete the note.

16
00:00:59,040 --> 00:01:00,300
So this note is deleted.

17
00:01:01,140 --> 00:01:08,490
And first previous this should point as null and void.

18
00:01:08,490 --> 00:01:13,950
Doing this, you should be careful whether first is really pointing on some node.

19
00:01:14,670 --> 00:01:21,330
Suppose this was just a single launch, then Fosterville become known so you cannot make first previous

20
00:01:21,330 --> 00:01:22,260
as null.

21
00:01:22,260 --> 00:01:23,450
There is no first previous.

22
00:01:23,820 --> 00:01:28,290
So I have to do this little carefully that check whether frosties not null.

23
00:01:28,290 --> 00:01:32,460
If not, then make it as null for first previous Megadeath null.

24
00:01:33,210 --> 00:01:34,380
Let me write on the steps.

25
00:01:35,520 --> 00:01:38,250
First step is bring B upon first.

26
00:01:38,250 --> 00:01:40,260
So be upon first.

27
00:01:42,410 --> 00:01:52,190
Then move us to next north, first, a sign, first catch snakes, first to sign, first cap snakes.

28
00:01:53,260 --> 00:02:00,120
Then take out the value and delete them, all these data is taken in ex and delete the old.

29
00:02:01,760 --> 00:02:05,440
Nor deleted them when this note is deleted.

30
00:02:05,690 --> 00:02:07,080
This is gone, right?

31
00:02:07,280 --> 00:02:12,790
So I should make this has known so that I should check if really was is pointing upon some Naude.

32
00:02:13,130 --> 00:02:21,130
So if a first minute was just not known, then the same line I will write on make previous has none.

33
00:02:21,130 --> 00:02:23,960
Hasanuddin, so I have to make this happen and remove the link from here.

34
00:02:24,470 --> 00:02:26,120
Follows the previous estimate as well.

35
00:02:26,130 --> 00:02:27,110
So this is M.F. not.

36
00:02:28,700 --> 00:02:30,200
So these are the steps required.

37
00:02:31,200 --> 00:02:35,920
And these are constant steps, one, two, three, four, five, few steps.

38
00:02:36,300 --> 00:02:39,300
So five steps, so the time taken will be constant.

39
00:02:40,230 --> 00:02:45,140
Now let us see the procedure for deleting a note from given index.

40
00:02:45,660 --> 00:02:51,870
Suppose I want to delete fort nor position is for whose position is for.

41
00:02:53,200 --> 00:03:01,780
How to delete that note, let us see the procedure if I have to delete Fort Nord, then this is a third

42
00:03:01,780 --> 00:03:07,570
Northshore point on fifth node and the fifth node should point on third node.

43
00:03:08,080 --> 00:03:10,700
Then this node is not reachable.

44
00:03:11,020 --> 00:03:12,370
You can delete the node.

45
00:03:13,810 --> 00:03:22,510
So for this, I have to modify TrueNorth and delete one word, delete footnote, modify 5th northern

46
00:03:22,510 --> 00:03:26,530
terminal for to I should modify so total.

47
00:03:26,530 --> 00:03:28,180
How many nodes are involved here.

48
00:03:28,450 --> 00:03:31,450
Three nodes are in more than how many pointers we need.

49
00:03:31,450 --> 00:03:32,380
Three pointers.

50
00:03:33,310 --> 00:03:40,150
Is it possible using a single pointer if I have a pointer here, if I can see it next and I can access

51
00:03:40,150 --> 00:03:45,970
this node again the previous and I can access this node, so let us do it using single pointer.

52
00:03:46,600 --> 00:03:46,800
So.

53
00:03:46,810 --> 00:03:48,040
Well, I should have a pointer.

54
00:03:48,190 --> 00:03:49,860
I should have a pointer upon this.

55
00:03:50,560 --> 00:03:54,400
So let us bring a pointer B from first to that node.

56
00:03:54,640 --> 00:03:57,470
So if I have a pointer P and bring it here.

57
00:03:57,820 --> 00:04:00,310
So how many times I should move it to bring it on.

58
00:04:00,310 --> 00:04:00,940
Fourth node.

59
00:04:01,270 --> 00:04:02,600
One, two, three.

60
00:04:03,100 --> 00:04:10,330
So that code I will write on here be that one first and the move B for position minus one times so that

61
00:04:10,330 --> 00:04:11,100
it comes here.

62
00:04:12,520 --> 00:04:15,610
So this follow P will come on to this node.

63
00:04:17,209 --> 00:04:19,220
Now, what are the links I have commodifying?

64
00:04:21,300 --> 00:04:32,010
These previous be the previous ex should point on business this morning, so this us, so be the previous

65
00:04:32,340 --> 00:04:36,320
next should point on these next this morning.

66
00:04:36,750 --> 00:04:38,790
So be the previous.

67
00:04:40,470 --> 00:04:45,030
Next should point on these next.

68
00:04:47,810 --> 00:04:48,200
Then.

69
00:04:50,120 --> 00:04:53,160
Then this link will be pointing on this one.

70
00:04:53,180 --> 00:04:58,760
So if you come from here, six cents or two, nine nine will send you two three, three will send you

71
00:04:58,760 --> 00:04:59,300
to two.

72
00:04:59,750 --> 00:05:02,840
So I'm not able to reach on the snood.

73
00:05:03,410 --> 00:05:05,260
So one link is removed now.

74
00:05:05,270 --> 00:05:06,770
Next next link.

75
00:05:06,770 --> 00:05:10,760
I have to remove these next medicinals.

76
00:05:11,210 --> 00:05:15,350
Its previous should point on this so that Espy's previous.

77
00:05:15,680 --> 00:05:19,990
So here I should write on dieties of this and also that this of this note is available here.

78
00:05:20,210 --> 00:05:22,570
So it means this value should be copied here.

79
00:05:22,940 --> 00:05:27,170
So please, NEC's previous begats next.

80
00:05:27,170 --> 00:05:31,010
Next catch previous should point on gaps previous.

81
00:05:32,310 --> 00:05:40,020
So but before I do this, one important thing, if suppose there is no Naude after Ford nor Ford is

82
00:05:40,020 --> 00:05:45,960
the last known, there is no Naude on the right side next to the original, nor then I cannot access

83
00:05:45,960 --> 00:05:46,160
it.

84
00:05:46,500 --> 00:05:47,250
It's an error.

85
00:05:47,430 --> 00:05:52,280
If you try to access nulls next door months previous, that's an error.

86
00:05:52,530 --> 00:05:55,940
So I can access that ignored if really for this present.

87
00:05:55,950 --> 00:06:00,360
So I should check the condition Weatherby's nexus there or not.

88
00:06:00,360 --> 00:06:04,140
There is no or not and so it is not null.

89
00:06:04,140 --> 00:06:04,780
I should do it.

90
00:06:05,280 --> 00:06:07,410
So first of all, chickpeas next.

91
00:06:07,530 --> 00:06:08,670
Is it null or not?

92
00:06:10,020 --> 00:06:15,180
If BP's next, then BP's next previous.

93
00:06:17,660 --> 00:06:23,630
Peaceniks previous should be a sign that peace previous this north settlers that is present here.

94
00:06:25,800 --> 00:06:27,480
So I find that to be previous.

95
00:06:28,390 --> 00:06:30,520
So this link will also modify.

96
00:06:32,380 --> 00:06:39,250
Now, this A.P. is not reachable from here, also, you cannot be from there also you cannot reach be

97
00:06:39,490 --> 00:06:41,800
from five theoretically coming on three from three.

98
00:06:41,830 --> 00:06:43,160
We are going directly to five.

99
00:06:43,840 --> 00:06:45,400
Now, this note can be deleted.

100
00:06:46,510 --> 00:06:50,670
So pick up the value and delete the exact same piece of data.

101
00:06:50,830 --> 00:06:52,980
Take on the data and delete the note.

102
00:06:55,050 --> 00:06:57,180
Will it be related or not?

103
00:06:58,300 --> 00:07:03,970
So this is a procedure for deleting a note from given position, so we have seen both the procedure

104
00:07:04,210 --> 00:07:07,090
building first note, deleting a note from any given index.

105
00:07:08,320 --> 00:07:13,870
Now, a little bit of analysis, how many except one does require just one extra point at B in both

106
00:07:13,980 --> 00:07:17,350
cases we have used just be here also you have USB here.

107
00:07:17,370 --> 00:07:21,220
Also, if you read the code, you don't find any other point that apart from P.

108
00:07:23,210 --> 00:07:26,510
Second one analysis, this was constant.

109
00:07:26,930 --> 00:07:33,340
Now, this depends on how much B is being moved, so this it depends on the follow up.

110
00:07:33,740 --> 00:07:39,080
This will move be so different from the position if the position of this last position, so that time

111
00:07:39,080 --> 00:07:46,370
is maximum Sahadevan or else if the position is second position, this node, then the time is minimum.

112
00:07:46,730 --> 00:07:47,800
That is constant.

113
00:07:48,550 --> 00:07:51,140
This loop will not execute at all.

114
00:07:51,410 --> 00:07:55,000
Just these statements will execute if you are deleting psychonaut.

115
00:07:56,780 --> 00:08:03,050
So that's all about insert, not a complete function and using the program, I will do it as we are

116
00:08:03,050 --> 00:08:06,130
used to with the previously implicit of Similary.

117
00:08:06,170 --> 00:08:08,900
I will combine this and I will read it as a single function.

