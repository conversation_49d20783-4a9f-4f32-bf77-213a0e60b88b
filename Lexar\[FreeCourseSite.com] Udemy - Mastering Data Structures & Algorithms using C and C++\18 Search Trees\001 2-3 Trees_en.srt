1
00:00:00,580 --> 00:00:03,130
Our next topic is two, three, three.

2
00:00:03,640 --> 00:00:09,670
So in this video we're discussing about what our country treats its properties, we learned and how

3
00:00:09,670 --> 00:00:14,560
to insult our creator to try to treat that deletion, then I'll do analysis.

4
00:00:14,950 --> 00:00:18,520
Then at last, I can explain to you why we need to treat this.

5
00:00:18,520 --> 00:00:21,010
What is the use or what is the purpose of treat this.

6
00:00:21,460 --> 00:00:31,030
So let us start with what are to treat this introduction to to treat this, to treat, resod circuitries

7
00:00:31,300 --> 00:00:33,700
like we have binary search tree.

8
00:00:34,210 --> 00:00:39,580
Binary Search Street can contain one key and two children like this.

9
00:00:39,610 --> 00:00:44,690
We can have a value here like <PERSON><PERSON><PERSON> and it can have two children on this side.

10
00:00:44,690 --> 00:00:48,420
We can have a smaller value and this can have larger value.

11
00:00:48,730 --> 00:00:50,950
So this is a binary search tree.

12
00:00:51,550 --> 00:00:58,990
So same concept is extended for multiple keys or multiple children know this can have only two children

13
00:00:59,170 --> 00:01:00,880
because one one key value.

14
00:01:01,210 --> 00:01:04,599
If you increase the number of keys, the number of children can also increase.

15
00:01:04,660 --> 00:01:07,720
The number of keys are two here.

16
00:01:07,930 --> 00:01:09,490
So you can have three children now.

17
00:01:09,790 --> 00:01:18,790
So this type of trees are basically called us multi research trees and also they are called m the search

18
00:01:18,790 --> 00:01:19,120
tree.

19
00:01:19,660 --> 00:01:21,040
So what is M here?

20
00:01:21,580 --> 00:01:27,460
Amines for my TV and the other meaning of M here is see the degree of a binary tree.

21
00:01:27,460 --> 00:01:31,510
If I say M as to if it is more than two, it can be anything.

22
00:01:31,520 --> 00:01:35,300
So we say m m degree of a tree is M.

23
00:01:35,800 --> 00:01:42,940
So now I can say that two three trees are search tree Victor Diggory tree.

24
00:01:43,120 --> 00:01:45,790
Yes they are with the degree tree.

25
00:01:46,240 --> 00:01:50,040
So two three trees are multi research trees with a degree tree.

26
00:01:50,980 --> 00:01:58,270
The next two, three trees are balanced, search trees hide balanced searches.

27
00:01:58,510 --> 00:02:01,810
So they are height balance which is called us Bitlis.

28
00:02:02,050 --> 00:02:04,030
This betrays a general concept.

29
00:02:04,030 --> 00:02:05,620
This is for any degree.

30
00:02:06,010 --> 00:02:07,960
But here the degree is a tree.

31
00:02:08,199 --> 00:02:13,300
So you can call to tree trees are big trees all four degree tree.

32
00:02:13,540 --> 00:02:14,860
Yes, that's also true.

33
00:02:15,520 --> 00:02:17,650
So what are the rules of big trees?

34
00:02:17,650 --> 00:02:19,090
How it can balance the height.

35
00:02:19,330 --> 00:02:22,690
So there are two rules that are applied on to three trees.

36
00:02:22,690 --> 00:02:24,190
Also, let us look at the rule.

37
00:02:24,580 --> 00:02:28,450
All leaf nodes should be at the same level as the first rule.

38
00:02:29,380 --> 00:02:34,390
Then second rule is that every node must have at least half of the children.

39
00:02:34,780 --> 00:02:36,880
Half of the children here degree degrees.

40
00:02:36,880 --> 00:02:40,270
A three three by two is seen value.

41
00:02:40,270 --> 00:02:40,930
Is it only.

42
00:02:40,960 --> 00:02:44,980
So yes, obviously we can have two children, so every note must have a minimum.

43
00:02:45,370 --> 00:02:46,180
Two children.

44
00:02:46,240 --> 00:02:47,170
Yes, definitely.

45
00:02:47,170 --> 00:02:49,210
We will have minimum two children here.

46
00:02:49,510 --> 00:02:52,840
But this is for the General Battley for any degree.

47
00:02:52,960 --> 00:02:54,610
Ruelas Ambika.

48
00:02:54,610 --> 00:02:55,490
Children must be there.

49
00:02:55,540 --> 00:02:59,440
If a degree is five, then five by two, 2.5, 2.5.

50
00:02:59,480 --> 00:03:00,850
Sylvana is three.

51
00:03:00,850 --> 00:03:02,740
It must have three children.

52
00:03:02,740 --> 00:03:04,420
So this is about to be three.

53
00:03:04,870 --> 00:03:08,710
So we are learning Beatrice of degree three and two.

54
00:03:08,710 --> 00:03:13,690
Three trees are famous mostly for learning about Beatrice.

55
00:03:13,810 --> 00:03:18,190
We studied two, three, three, three to try to understand Beatrice also.

56
00:03:18,610 --> 00:03:20,190
No, let us look at the structure.

57
00:03:20,560 --> 00:03:26,980
See here, the degree of every node is a three so every node can have three children left child, middle

58
00:03:26,980 --> 00:03:27,760
child, right child.

59
00:03:27,910 --> 00:03:30,540
You can name them like this right, left, the middle and right.

60
00:03:31,390 --> 00:03:34,180
Then three children are possible and two kids are possible.

61
00:03:34,180 --> 00:03:38,080
Then how those two should be given should be less than two.

62
00:03:38,200 --> 00:03:41,170
So it means the keys inside a node must be sorted.

63
00:03:41,540 --> 00:03:48,280
The first point about the structure then second thing, if you take the left child that is left child.

64
00:03:48,280 --> 00:03:51,100
So what are the keys are present here on the keys.

65
00:03:51,100 --> 00:03:53,920
That must be less than key one night.

66
00:03:54,400 --> 00:04:03,570
And and if you look at the keys inside and then all the keys there should be greater than key to Minsky's.

67
00:04:03,580 --> 00:04:05,520
We should be less than all the keys present here.

68
00:04:05,560 --> 00:04:07,050
So I'm calling it as Enzo.

69
00:04:07,390 --> 00:04:08,350
And what about M.

70
00:04:08,560 --> 00:04:09,910
M should have the keys.

71
00:04:09,910 --> 00:04:11,740
This note should should have the keys.

72
00:04:11,740 --> 00:04:15,760
That should be greater than Caven and less than Kaidu.

73
00:04:15,940 --> 00:04:20,140
So M should be greater than Caven and less than Katou.

74
00:04:20,970 --> 00:04:25,990
NetSol, these are the conditions, how the keys must be arranged, so there's a simple thing, the

75
00:04:25,990 --> 00:04:30,340
keys must be original sorted order and here you will find all the keys that are greater than Caven and

76
00:04:30,340 --> 00:04:31,260
less than Kaidu.

77
00:04:31,660 --> 00:04:33,450
And these are all less than Kevin.

78
00:04:33,460 --> 00:04:40,190
Likewise then and one more important thing, as does the surgery, it cannot have duplicates.

79
00:04:40,210 --> 00:04:41,860
We cannot have duplicate elements.

80
00:04:41,870 --> 00:04:42,050
Right.

81
00:04:42,310 --> 00:04:44,960
If you try to insert them, if it is already there.

82
00:04:44,980 --> 00:04:45,810
We don't insert.

83
00:04:46,920 --> 00:04:54,630
Now, let us see how to insert how to how to create a two, three tree, so I take some keys and I will

84
00:04:54,750 --> 00:04:59,620
show you how to insert and I will be creating it by inserting in the elements one by one.

85
00:05:00,210 --> 00:05:03,710
So let us start creation of a set of keys here.

86
00:05:03,960 --> 00:05:08,600
So I will show you how to create a two three tree by inserting these keys one by one.

87
00:05:09,360 --> 00:05:12,400
I already told you that all leaf nodes must be at the same level.

88
00:05:12,420 --> 00:05:14,730
So let us see how that is managed.

89
00:05:14,850 --> 00:05:15,130
Right.

90
00:05:15,420 --> 00:05:16,500
And how high it is.

91
00:05:16,500 --> 00:05:18,070
Balanced literacy.

92
00:05:18,310 --> 00:05:19,870
Let us start inserting.

93
00:05:19,890 --> 00:05:21,720
So first of all, I will insert.

94
00:05:23,560 --> 00:05:29,110
Grindy, so for that create, nor does the LoopNet, OK, right now this is the only note.

95
00:05:29,120 --> 00:05:30,770
So we have Rubner 20.

96
00:05:31,110 --> 00:05:39,960
So 20, I have inserted insert 20 greater Fastenal and insecurity that mixed here only I will insert.

97
00:05:40,030 --> 00:05:41,510
I just wanted this one right.

98
00:05:41,800 --> 00:05:44,730
So today it comes here only it comes after 20.

99
00:05:44,740 --> 00:05:46,190
So today is also inserted.

100
00:05:46,840 --> 00:05:48,930
So you draw a line here separating them.

101
00:05:48,980 --> 00:05:51,180
Well, I have to squeeze in a single log.

102
00:05:52,420 --> 00:05:54,200
20 and 30 is over.

103
00:05:54,460 --> 00:06:00,100
Now let us insert 40, so here I will write insert 40.

104
00:06:00,850 --> 00:06:01,990
Not in this one.

105
00:06:02,290 --> 00:06:04,110
In this one I would insert 40.

106
00:06:04,120 --> 00:06:04,860
So watch this.

107
00:06:04,870 --> 00:06:06,540
Now, this is important.

108
00:06:06,580 --> 00:06:09,100
So if you understand this is then everything is perfect.

109
00:06:10,160 --> 00:06:14,220
Indian tradition there, 40 comes after 30, but there is no space.

110
00:06:15,030 --> 00:06:15,810
There is more space.

111
00:06:16,230 --> 00:06:21,140
No degree is a three keys are truly Salukis.

112
00:06:21,150 --> 00:06:25,560
I just know I've shown you the structure that Kevin Ankita book is out there.

113
00:06:25,590 --> 00:06:30,400
There is no space for Key 40 then what to do splitted the.

114
00:06:31,620 --> 00:06:35,290
If does fail and there is more space for the new node than split the node.

115
00:06:35,340 --> 00:06:36,390
OK, split the node.

116
00:06:36,570 --> 00:06:38,790
So I will create two nodes.

117
00:06:38,940 --> 00:06:43,620
We have 20, 30 and 40 is not in there but consider that 40 also.

118
00:06:43,830 --> 00:06:46,740
So including that 40 if you see there are three keys.

119
00:06:46,740 --> 00:06:54,000
No thought of that one key on the left hand side, one guy on the right hand side, then tatties remaining.

120
00:06:54,270 --> 00:06:57,050
That goes in another node as a parent.

121
00:06:57,060 --> 00:06:58,800
So Turkey will come here.

122
00:06:59,160 --> 00:07:01,590
And this is the left side for cabi.

123
00:07:01,920 --> 00:07:05,500
This is the right child for 30, our middle child.

124
00:07:05,510 --> 00:07:07,580
This there is another case along there.

125
00:07:07,590 --> 00:07:10,350
So we will have the right child also.

126
00:07:10,620 --> 00:07:12,650
Right, so left and middle child.

127
00:07:12,960 --> 00:07:17,280
So we have space empty here, space empty here and also this empty.

128
00:07:17,530 --> 00:07:18,710
So this is the split.

129
00:07:18,990 --> 00:07:25,110
So I'm going to have splitter how many and also created two new ones I created until I have inserted

130
00:07:25,110 --> 00:07:25,860
up to forty four.

131
00:07:25,860 --> 00:07:27,260
How many splits happen?

132
00:07:27,600 --> 00:07:32,520
See, this is the first split four suspect and two new numbers are created in this split.

133
00:07:33,180 --> 00:07:35,690
And so this is in No.

134
00:07:35,760 --> 00:07:36,750
One important thing.

135
00:07:37,380 --> 00:07:42,990
We have seen binary Suchi binary search tree goes downwards, right?

136
00:07:43,200 --> 00:07:46,710
We find where the elements should come and we leave node.

137
00:07:46,710 --> 00:07:48,780
So it goes goes downwards.

138
00:07:48,990 --> 00:07:51,330
But here it is going upwards.

139
00:07:51,540 --> 00:07:53,010
See, already this note was there.

140
00:07:53,220 --> 00:07:56,880
So we have added this node above this one, above this one.

141
00:07:57,210 --> 00:07:59,250
So two, three trees arbitrary.

142
00:07:59,430 --> 00:08:00,640
They go up for.

143
00:08:01,500 --> 00:08:01,890
Right.

144
00:08:02,640 --> 00:08:04,640
That's how they maintain themselves balance.

145
00:08:04,710 --> 00:08:06,930
So the procedure for insertion is this one.

146
00:08:06,930 --> 00:08:10,720
So that at the balance, the high is now next.

147
00:08:10,740 --> 00:08:13,800
I have to insert Merkies now let us insert 50.

148
00:08:14,040 --> 00:08:17,330
So here I will say insert 50.

149
00:08:17,850 --> 00:08:20,130
So in this one I have to insert fifty.

150
00:08:20,130 --> 00:08:22,050
So I will try this one once again.

151
00:08:23,220 --> 00:08:29,670
On this side we have twenty and for peace, I feel like he's not doing ten today.

152
00:08:29,670 --> 00:08:32,789
That is twenty and this is 40 now 50.

153
00:08:33,270 --> 00:08:35,419
Czechia how to insert is the root.

154
00:08:35,429 --> 00:08:36,950
So I'm not marking it every time.

155
00:08:37,049 --> 00:08:37,799
So this is the root.

156
00:08:37,809 --> 00:08:46,500
We can understand that fifty cheekier is greater than thirty and Netsky, it's not there so it should

157
00:08:46,500 --> 00:08:48,840
be in this note only go to this note.

158
00:08:48,990 --> 00:08:51,150
This is forty fifty must be next.

159
00:08:51,390 --> 00:08:52,800
So search for fifty.

160
00:08:52,800 --> 00:08:54,840
First of all it's not there.

161
00:08:54,840 --> 00:08:55,530
That is Witkin.

162
00:08:55,740 --> 00:08:57,090
So philosophy here.

163
00:08:57,450 --> 00:08:58,980
OK, this is perfect.

164
00:09:00,220 --> 00:09:06,190
So I don't have to do anything, simply the keys in certain nomics, 60 Silentium, 60 year olds in

165
00:09:06,190 --> 00:09:10,880
the same step, I now 60 Minutes should come check here for 60.

166
00:09:10,900 --> 00:09:14,020
First of all, 60 is greater than 30 next.

167
00:09:14,150 --> 00:09:14,860
There's no key.

168
00:09:14,860 --> 00:09:16,760
So check in this child, right?

169
00:09:17,110 --> 00:09:18,490
60 is greater than 40.

170
00:09:18,670 --> 00:09:21,580
60 is greater than 50 and is over.

171
00:09:21,850 --> 00:09:23,460
So there is no space here.

172
00:09:23,470 --> 00:09:24,880
Actually, it should come here.

173
00:09:24,880 --> 00:09:25,960
But there is more space.

174
00:09:26,350 --> 00:09:27,700
60 should come here.

175
00:09:28,090 --> 00:09:29,020
There is no space.

176
00:09:29,050 --> 00:09:31,000
Then what to do when there is no space?

177
00:09:31,000 --> 00:09:31,480
Just no.

178
00:09:31,480 --> 00:09:32,780
We have done something here.

179
00:09:32,890 --> 00:09:34,400
Same thing there is split.

180
00:09:34,960 --> 00:09:38,560
So take this Lugazi D then this Noris 20.

181
00:09:38,740 --> 00:09:39,190
Right.

182
00:09:39,550 --> 00:09:40,540
And this node.

183
00:09:40,900 --> 00:09:42,790
We are going to split this node.

184
00:09:43,120 --> 00:09:44,170
Split this node.

185
00:09:44,350 --> 00:09:44,730
Right.

186
00:09:45,040 --> 00:09:46,420
So split this one.

187
00:09:46,420 --> 00:09:51,130
So how split 40 on this side and 60 on that side.

188
00:09:51,430 --> 00:09:59,010
The middle Milliman 50 will go in the root in the parent right here parodist openly so it will go there.

189
00:09:59,560 --> 00:10:01,050
So 50 will go here.

190
00:10:01,060 --> 00:10:02,410
And this was already 30.

191
00:10:02,590 --> 00:10:03,760
So let's be positive.

192
00:10:04,840 --> 00:10:09,970
I don't have to create a separate nor the 450, because all the inaudible did in the period and the

193
00:10:10,210 --> 00:10:12,760
blank space for that, so I stored 50 there.

194
00:10:13,270 --> 00:10:17,500
So this is how 60s inserted into of 50 was easy.

195
00:10:17,500 --> 00:10:22,320
Directly inserted insertion of 60 caused a split, one more split.

196
00:10:22,570 --> 00:10:23,980
How many new laws are created?

197
00:10:24,070 --> 00:10:29,740
Only one Leonora's created because the parent was already there and 50 got a place there.

198
00:10:30,490 --> 00:10:35,200
Now I have to insert more keys like Nexus 10 and 15.

199
00:10:35,380 --> 00:10:36,550
This I'm going to insert.

200
00:10:36,820 --> 00:10:40,270
So here I will do insert an.

201
00:10:41,510 --> 00:10:49,330
So for inserting time, so first of all, I would draw the same thing as it is there, insert 10 ten,

202
00:10:49,670 --> 00:10:55,880
very true top tennis, start searching for ten, tennis less than 30 and go to the left side, then

203
00:10:56,060 --> 00:10:57,910
tennis less than 20.

204
00:10:57,980 --> 00:10:59,030
So it should be here.

205
00:10:59,420 --> 00:11:01,370
So what to do with 20 grand?

206
00:11:01,430 --> 00:11:02,410
Are you there?

207
00:11:02,720 --> 00:11:03,810
Is there any space?

208
00:11:03,830 --> 00:11:04,090
Yes.

209
00:11:05,120 --> 00:11:09,270
So shift 20 there and insert 10 here.

210
00:11:09,660 --> 00:11:14,700
OK, no split required next 15.

211
00:11:14,870 --> 00:11:17,510
So I, I'm inserted in this structure only here.

212
00:11:17,510 --> 00:11:23,630
Only evidence 15 15 should it be 15 is less than 30 and greater than 10 and less than 20.

213
00:11:23,630 --> 00:11:24,790
So it should come in between.

214
00:11:24,980 --> 00:11:26,120
But there is no space.

215
00:11:26,120 --> 00:11:29,350
Actually this node should have 10, 15 and 20.

216
00:11:29,510 --> 00:11:31,400
But there is more space then to what?

217
00:11:31,700 --> 00:11:32,210
Split.

218
00:11:32,390 --> 00:11:33,230
So let us do it.

219
00:11:33,410 --> 00:11:35,280
So here I'm doing separately.

220
00:11:36,440 --> 00:11:36,790
Right.

221
00:11:37,070 --> 00:11:37,760
So split.

222
00:11:37,880 --> 00:11:38,810
So splitting.

223
00:11:38,810 --> 00:11:43,780
I get two nodes, 10 on this side and 20 on this side.

224
00:11:43,790 --> 00:11:49,400
See, considering 15 together, I'm splitting 15 should go up, 15 should go up.

225
00:11:49,640 --> 00:11:50,990
So 15 will come here.

226
00:11:51,170 --> 00:12:00,050
OK, then the rest of the nodes here I have a 40 and here I have 16 and in the root game we have 30

227
00:12:00,050 --> 00:12:01,170
and 50.

228
00:12:01,640 --> 00:12:06,440
So this 50 was pointing on this one and this middle challenge, this one now this left child, we have

229
00:12:06,440 --> 00:12:06,980
split it.

230
00:12:07,460 --> 00:12:11,450
There's more space for 15, just like we have done it for 50/50.

231
00:12:11,450 --> 00:12:12,950
Went up and there was the free space.

232
00:12:13,310 --> 00:12:15,170
There is no free space for 15.

233
00:12:15,170 --> 00:12:15,890
Then what to do?

234
00:12:16,100 --> 00:12:17,730
Split that route also.

235
00:12:17,900 --> 00:12:20,750
So if we split, then we get one more note here.

236
00:12:20,960 --> 00:12:26,220
OK, one more note and one node as a parent, one known as a parent.

237
00:12:26,450 --> 00:12:27,440
So what are the keys?

238
00:12:27,590 --> 00:12:29,830
15, 30 and 50.

239
00:12:30,110 --> 00:12:37,240
So 15 will be on the site, 50 will be on that side and the route will contain 30.

240
00:12:37,580 --> 00:12:38,290
Yes.

241
00:12:38,660 --> 00:12:44,390
Then this will point on this one, this point on this one, not the new keys that we got 15 less than

242
00:12:44,390 --> 00:12:46,330
15 greater than 15.

243
00:12:46,340 --> 00:12:47,120
This is 20.

244
00:12:47,960 --> 00:12:48,740
That is the key.

245
00:12:49,220 --> 00:12:54,710
So this insertion insertion of 15 has caused those splits.

246
00:12:55,070 --> 00:12:56,380
One split here.

247
00:12:56,510 --> 00:12:56,810
Right.

248
00:12:56,870 --> 00:12:57,700
There was no space.

249
00:12:57,710 --> 00:12:58,690
So we have split this.

250
00:12:59,000 --> 00:13:00,670
Then 15 went up in the pool.

251
00:13:00,950 --> 00:13:04,250
So, again, this also split, so to speak, for cost.

252
00:13:04,790 --> 00:13:10,790
So if you count so far, I have done four splits, like four splits under and this is split.

253
00:13:10,790 --> 00:13:12,060
How many new ones are created?

254
00:13:12,350 --> 00:13:14,770
This is one of the node and not anymore.

255
00:13:14,780 --> 00:13:15,230
Not so.

256
00:13:15,230 --> 00:13:20,540
Two new novels are created in this one, Tony, on unknowns and two splits are done.

257
00:13:20,630 --> 00:13:22,760
So this is one of the criteria for analysis.

258
00:13:23,700 --> 00:13:30,630
Now, you can take this one if you're doing paperwork, do this one and you create a tree up to here

259
00:13:30,630 --> 00:13:33,620
by yourself once, then you can always remember it.

260
00:13:34,290 --> 00:13:37,300
Just watching the Lord help you take the keys, right?

261
00:13:37,470 --> 00:13:38,670
You do it by yourself.

262
00:13:39,210 --> 00:13:40,800
Then I have a few more keys remaining.

263
00:13:40,800 --> 00:13:41,720
That is 78.

264
00:13:41,730 --> 00:13:42,450
I will fill them.

265
00:13:42,720 --> 00:13:44,940
OK, so just take up to here.

266
00:13:44,940 --> 00:13:47,820
I will remove everything and I will do it right here.

267
00:13:47,820 --> 00:13:52,380
Then I will insert 70 and 80 so you can take a snapshot for this one.

268
00:13:52,620 --> 00:13:55,620
All the information I have shown you, this is the first one and four.

269
00:13:55,620 --> 00:13:58,890
This is the result for this is the result for this.

270
00:13:59,110 --> 00:14:01,440
The So final reads that one.

271
00:14:02,180 --> 00:14:04,590
I read all that one here and we will continue.

272
00:14:05,490 --> 00:14:06,270
So this is a tree.

273
00:14:06,270 --> 00:14:10,920
We have up to 15 now next to 70 where somebody should come.

274
00:14:10,920 --> 00:14:13,650
70 is greater than 30 and 50 greater than 60.

275
00:14:14,040 --> 00:14:15,090
70 comes here.

276
00:14:15,540 --> 00:14:17,880
The next 80 we really should come.

277
00:14:18,030 --> 00:14:19,290
It is greater than all this.

278
00:14:19,290 --> 00:14:22,710
It should come off to 70, but there is no space then split.

279
00:14:23,070 --> 00:14:29,310
So create one more note right now, including this 60, 70 and 80 also.

280
00:14:29,310 --> 00:14:30,530
So it will go there.

281
00:14:31,050 --> 00:14:35,580
And the 70 that is middle element will go up and this will be right.

282
00:14:37,230 --> 00:14:43,680
That's for all the kids are in soccer now, one more thing, I will insert one more key that is ninety

283
00:14:44,250 --> 00:14:45,360
nine BS insert.

284
00:14:45,390 --> 00:14:47,340
Here are two three.

285
00:14:47,340 --> 00:14:48,400
Three is a form.

286
00:14:48,420 --> 00:14:54,560
So this is the approach for creating are two, three, three by inserting the keys one by one.

287
00:14:54,570 --> 00:15:00,180
So you have seen that it was pulling upwards Sematary follow for between also people plus students.

288
00:15:00,360 --> 00:15:03,750
Now next let us learn a deletion for deletion.

289
00:15:03,750 --> 00:15:06,570
I use the same three to retrieve from this.

290
00:15:06,570 --> 00:15:10,170
I will select the keys and I'll show you how to delete them.

291
00:15:10,560 --> 00:15:12,920
So let us look at the cases in this one.

292
00:15:12,930 --> 00:15:15,840
So first case, let us delete case one.

293
00:15:16,200 --> 00:15:22,080
OK, so I will not be mentioning here you can no longer case as a case one I want to delete.

294
00:15:22,230 --> 00:15:25,620
Ninety delete ninety case one.

295
00:15:25,860 --> 00:15:26,610
Watch it here.

296
00:15:27,330 --> 00:15:29,580
Search for nineteen ninety is greater than today.

297
00:15:29,580 --> 00:15:32,700
There is one key here so it must be in this middle node then.

298
00:15:32,700 --> 00:15:35,730
It is greater than fifty greater than seventy so it must be in the right channel.

299
00:15:35,730 --> 00:15:36,870
So it is greater than eighty.

300
00:15:37,140 --> 00:15:37,470
Yes.

301
00:15:37,470 --> 00:15:43,230
We found nineties font then deleted for nineties gone.

302
00:15:44,400 --> 00:15:45,480
Name is deleted.

303
00:15:46,430 --> 00:15:53,300
Any changes we have to do, no more changes, so does the case, one, we have searched for a key and

304
00:15:53,300 --> 00:15:59,240
the key was in the leave, it is simply deleted and there is no effect upon the two, three, three

305
00:15:59,240 --> 00:16:01,320
or three after deletion.

306
00:16:01,640 --> 00:16:02,710
This is the first case.

307
00:16:02,720 --> 00:16:11,960
Simply delete the key that these two guys, too, I am deleting and delete it.

308
00:16:13,000 --> 00:16:14,100
Let us search for 80.

309
00:16:15,640 --> 00:16:18,210
It is greater than this, greater than this, greater than this.

310
00:16:18,320 --> 00:16:23,390
So come on, this idea, say these phone, delete it, it is gone.

311
00:16:23,410 --> 00:16:25,290
See, we have deleted Eddie already.

312
00:16:25,300 --> 00:16:26,290
We have deleted 90.

313
00:16:26,300 --> 00:16:27,430
So I'm writing them on site.

314
00:16:27,760 --> 00:16:29,080
Not Eddie is gone.

315
00:16:29,230 --> 00:16:30,130
Munadi is gone.

316
00:16:30,150 --> 00:16:35,590
Then what this note is doing, this note has we can we cannot we should not have empty n then what to

317
00:16:35,590 --> 00:16:36,670
do more.

318
00:16:36,670 --> 00:16:41,120
Is this with the sibling either the left sibling or right sibling.

319
00:16:41,320 --> 00:16:44,200
So how do Marge see in time?

320
00:16:44,200 --> 00:16:48,880
At the time of inception we were splitting at the time of the we will march join it.

321
00:16:49,180 --> 00:16:50,520
So join these two.

322
00:16:50,680 --> 00:16:54,330
So joining these to remove this node which has became vacant.

323
00:16:54,340 --> 00:16:58,300
So just leave it and bring 70 here in this one.

324
00:17:00,460 --> 00:17:01,440
This is marching.

325
00:17:01,720 --> 00:17:05,310
So you bring the key from the parent and join these TrueNorth.

326
00:17:05,319 --> 00:17:13,540
So I'll show it separately here, see if suppose you have them here and tutee here and grindy here was

327
00:17:13,540 --> 00:17:14,589
this step carefully.

328
00:17:14,589 --> 00:17:15,650
This will give you the idea.

329
00:17:15,670 --> 00:17:22,569
Merging means for some of you are the leading Tudi that you have to join this to n join these two n

330
00:17:23,109 --> 00:17:26,680
then is already there then copy that key value also here.

331
00:17:26,920 --> 00:17:29,410
Copy that key also here, that key.

332
00:17:29,680 --> 00:17:31,350
So this key is also gone.

333
00:17:31,360 --> 00:17:36,010
So as this one is vacant, so this becomes Lukins so you have to delete this note also.

334
00:17:36,310 --> 00:17:39,460
So when you are deleting today, you have to delete this more as I love this note.

335
00:17:39,730 --> 00:17:44,100
But here we don't have to delete the norm and you don't have to delete the node because already one

336
00:17:44,110 --> 00:17:44,860
more keys there.

337
00:17:45,070 --> 00:17:46,310
So just we have copied that.

338
00:17:46,420 --> 00:17:48,160
So this was case two.

339
00:17:48,400 --> 00:17:53,440
Now I will show you cards to once again for another key that is 60.

340
00:17:53,800 --> 00:17:55,870
So I will write on the links back again.

341
00:17:55,870 --> 00:18:00,090
This was 70 and this is 50 80.

342
00:18:00,130 --> 00:18:00,980
This was 80.

343
00:18:01,660 --> 00:18:02,910
90 is already gone.

344
00:18:02,920 --> 00:18:05,220
So now delete 60.

345
00:18:05,230 --> 00:18:10,090
So if you are deleting 60, search for 60, 60 is found here, delete 60.

346
00:18:10,270 --> 00:18:12,390
Now this note is vacant, then what to do?

347
00:18:12,640 --> 00:18:16,760
You can join with either left sibling or sibling, anyone.

348
00:18:16,760 --> 00:18:18,570
You can join anyone.

349
00:18:19,090 --> 00:18:21,550
So let us join the right sibling.

350
00:18:21,790 --> 00:18:27,730
If you join the right sibling, then 70 will come down and maybe they both together will be in this

351
00:18:27,730 --> 00:18:28,090
node.

352
00:18:28,210 --> 00:18:30,360
So it will be 70 and 80.

353
00:18:30,730 --> 00:18:32,290
And this note is gone, right?

354
00:18:32,410 --> 00:18:34,960
This node is gone and 70s also gone.

355
00:18:35,350 --> 00:18:36,280
It looks like this.

356
00:18:36,760 --> 00:18:38,080
So this note is gone.

357
00:18:38,860 --> 00:18:39,580
Now, one more thing.

358
00:18:39,700 --> 00:18:41,980
I said that this can be Margareth, right?

359
00:18:41,980 --> 00:18:43,410
Sibling as well as the sibling.

360
00:18:43,690 --> 00:18:45,240
Let us do it with the left sibling.

361
00:18:45,400 --> 00:18:52,510
Let us do it so I will write on 70 and ideia that is not deleting 60.

362
00:18:53,050 --> 00:18:55,330
So this should be much less sibling.

363
00:18:55,540 --> 00:18:58,150
So 50 should come down, 50 should come down.

364
00:18:58,150 --> 00:18:59,770
So you can bring it into this node.

365
00:19:00,070 --> 00:19:01,750
An additional is deleted.

366
00:19:02,080 --> 00:19:03,570
Then what about this.

367
00:19:03,940 --> 00:19:05,980
So you have to shift to seventy also.

368
00:19:06,340 --> 00:19:09,580
And instead of Rachell this becomes a middle child.

369
00:19:09,880 --> 00:19:11,380
These are the changes you have to do.

370
00:19:12,430 --> 00:19:15,250
So you do more with less sibling or sibling.

371
00:19:15,250 --> 00:19:18,100
So here merging with the right sibling was better.

372
00:19:18,610 --> 00:19:19,420
It was better.

373
00:19:20,330 --> 00:19:24,790
But if you don't have rights, sibling, then you have to go with the discipline, so you have an option

374
00:19:25,490 --> 00:19:26,850
Nabulsi next case.

375
00:19:26,870 --> 00:19:31,700
So for that, I will draw the tree back again as it is, and then I will do it.

376
00:19:33,160 --> 00:19:37,840
Now, next case, I want to delete 16 letters, delete 16.

377
00:19:38,970 --> 00:19:42,150
Search for 60 and deleted, so 60 ago.

378
00:19:43,560 --> 00:19:50,640
60S gone, this mall is vacant, so you remember what we were doing, we were marching that I have shown,

379
00:19:50,640 --> 00:19:53,160
you ask kids to write, so we were marching.

380
00:19:53,490 --> 00:19:55,950
But before marching, there is one option.

381
00:19:56,130 --> 00:19:56,950
There's one option.

382
00:19:56,970 --> 00:19:57,540
Let us see.

383
00:19:58,170 --> 00:20:00,620
Can we borrow the key here?

384
00:20:01,170 --> 00:20:01,800
Let us check.

385
00:20:01,800 --> 00:20:05,250
On the left hand side is having only one kid cannot share it with you.

386
00:20:05,640 --> 00:20:07,150
Then let us check on the right side.

387
00:20:07,320 --> 00:20:09,060
Yes, it is having two keys.

388
00:20:09,060 --> 00:20:10,620
It can share one key with us.

389
00:20:11,040 --> 00:20:12,630
So let us borrow a key.

390
00:20:12,870 --> 00:20:16,860
So borrowing we have to borrow it to buy our parent.

391
00:20:16,950 --> 00:20:25,650
So 70 should come this side and it should move in to root and 90 should be shifted here.

392
00:20:26,460 --> 00:20:31,510
This is boring, so Turkey, as I have shown you, that is bottle.

393
00:20:32,760 --> 00:20:39,510
So so far we have seen the cases simply delete the note, then delete and march and the third on this

394
00:20:39,510 --> 00:20:39,970
bottle.

395
00:20:40,290 --> 00:20:42,570
So actually, this should be the first step.

396
00:20:42,570 --> 00:20:43,980
Borane should be the first step.

397
00:20:44,160 --> 00:20:46,470
If boring is not possible, then you can march.

398
00:20:46,860 --> 00:20:48,720
So I have shown you bordering at the last.

399
00:20:48,960 --> 00:20:51,510
So let me show you a bottle here once again.

400
00:20:51,900 --> 00:20:57,760
See, this was having two keys and here is one key, let us say down.

401
00:20:57,780 --> 00:21:00,630
And this is 20 and 30 and 40.

402
00:21:00,960 --> 00:21:02,170
Some will stand is gone.

403
00:21:02,190 --> 00:21:05,210
So if 10 is gone, then who should take its place?

404
00:21:05,220 --> 00:21:07,810
20 should come here and 30 should move up.

405
00:21:08,040 --> 00:21:09,360
So this all you can bottle.

406
00:21:10,320 --> 00:21:11,820
Right party should go.

407
00:21:12,360 --> 00:21:16,140
So I'll read write in the next level that 1920 and this was 10.

408
00:21:16,140 --> 00:21:23,150
So if 10 is gone, then here comes 30 on here it will be drinking and here it will be 40.

409
00:21:23,880 --> 00:21:29,450
So this whole boring and boring is done by a group where I didn't miss it on the map and.

410
00:21:30,320 --> 00:21:38,510
So this other cases now, three things either simply deleted or bought it or mar it, so other you have

411
00:21:38,510 --> 00:21:39,540
deleted a few keys.

412
00:21:39,830 --> 00:21:44,260
Now let us delete a few more and see how we can work out with them.

413
00:21:44,990 --> 00:21:47,800
Suppose I want to delete 20 if 20 is gone.

414
00:21:48,140 --> 00:21:48,700
So check.

415
00:21:48,720 --> 00:21:49,470
Can you borrow.

416
00:21:49,850 --> 00:21:51,680
There is no key on this left hand side.

417
00:21:51,710 --> 00:21:52,460
Then do what?

418
00:21:52,460 --> 00:21:53,000
March.

419
00:21:53,390 --> 00:21:57,110
If I march then I should take a single node and remove this.

420
00:21:57,380 --> 00:21:59,450
I should have 10 and 15 here.

421
00:21:59,840 --> 00:22:00,980
Then what about this?

422
00:22:01,280 --> 00:22:03,400
This is empty when it is empty.

423
00:22:03,770 --> 00:22:05,140
Can we borrow from here?

424
00:22:05,190 --> 00:22:06,350
Let us delete 20.

425
00:22:06,430 --> 00:22:09,080
So if I have to delete 20, search for 20.

426
00:22:09,470 --> 00:22:11,750
So 20 is found here then delete it.

427
00:22:12,200 --> 00:22:13,460
If this is deleted.

428
00:22:13,940 --> 00:22:14,610
First a check.

429
00:22:14,630 --> 00:22:17,920
Can you borrow something borrowed from the left sibling.

430
00:22:18,530 --> 00:22:22,270
There is an attempt to share it cannot give a key then to what march.

431
00:22:22,550 --> 00:22:23,750
So first try to borrow.

432
00:22:23,750 --> 00:22:26,060
If you cannot borrow then much, what can market.

433
00:22:26,300 --> 00:22:28,610
So I'll bring this 15 here.

434
00:22:28,850 --> 00:22:31,610
OK, 15 this year and this note is gone.

435
00:22:31,920 --> 00:22:32,920
OK, perfect.

436
00:22:33,320 --> 00:22:34,270
Then what about this?

437
00:22:34,700 --> 00:22:37,850
This became vacant so it is just cascading.

438
00:22:37,850 --> 00:22:43,090
If you delete one and you have merged one, then again you may have to check again the parent.

439
00:22:43,370 --> 00:22:46,360
So again though you have an option bottle or much.

440
00:22:46,700 --> 00:22:48,110
So can you bottle.

441
00:22:48,410 --> 00:22:48,740
Yes.

442
00:22:48,740 --> 00:22:51,890
From the right side you can bottle water boarding.

443
00:22:52,070 --> 00:22:52,720
Just check.

444
00:22:52,720 --> 00:22:55,370
Can you watch what you have to combine.

445
00:22:55,370 --> 00:22:59,000
This today should come down so all the little kids are there.

446
00:22:59,000 --> 00:23:00,440
You cannot bring cutie here.

447
00:23:00,440 --> 00:23:04,280
So yes, it is having more number of kids so you cannot march but you can bottle.

448
00:23:04,670 --> 00:23:05,900
So bottle from there.

449
00:23:06,170 --> 00:23:07,070
So bottle somewhere.

450
00:23:07,070 --> 00:23:07,690
Right chain.

451
00:23:07,730 --> 00:23:08,660
So we should call.

452
00:23:08,660 --> 00:23:09,280
Should be done.

453
00:23:09,860 --> 00:23:11,180
See this is the first key.

454
00:23:11,480 --> 00:23:13,580
This can go up OK.

455
00:23:13,580 --> 00:23:15,550
Fifty goes up no matter what.

456
00:23:15,580 --> 00:23:19,070
Thirty three comes here then one fifty is gone means.

457
00:23:19,070 --> 00:23:20,150
What about its child.

458
00:23:20,390 --> 00:23:22,280
So that child will come on this site.

459
00:23:22,400 --> 00:23:23,480
It will come on the site.

460
00:23:23,480 --> 00:23:25,160
So you have to modify these links.

461
00:23:25,550 --> 00:23:29,660
So fatigue came here now the motorboat that you shift them.

462
00:23:30,050 --> 00:23:32,570
So here you bring seventy.

463
00:23:32,570 --> 00:23:33,740
Actually I'm redoing.

464
00:23:33,770 --> 00:23:38,810
OK, just so you have to shift this, so remove this ninety and bring it here.

465
00:23:39,200 --> 00:23:42,380
So these are the changes I made more multiple changes.

466
00:23:42,380 --> 00:23:46,520
So you have to watch it once again and watch it slowly, then you can understand everything.

467
00:23:46,820 --> 00:23:50,090
So I have borrowed a key from Ritcher.

468
00:23:50,100 --> 00:23:51,730
So fifty came here today.

469
00:23:51,740 --> 00:23:52,430
Came here.

470
00:23:52,610 --> 00:23:54,860
So this is this was perfect then.

471
00:23:54,860 --> 00:23:56,370
What about forty, forty one on the left.

472
00:23:56,370 --> 00:23:59,900
Chinita became right in here then it was here.

473
00:23:59,900 --> 00:24:05,150
So I have shifted these values because we cannot have first location in the foskey is not the second

474
00:24:05,160 --> 00:24:05,620
keys there.

475
00:24:05,630 --> 00:24:06,770
So we cannot have like this.

476
00:24:06,800 --> 00:24:07,850
We just shifted them.

477
00:24:08,330 --> 00:24:10,590
So this eighty seventy nine dividend.

478
00:24:10,620 --> 00:24:11,810
So I have shifted them here.

479
00:24:11,810 --> 00:24:12,530
Nothing else.

480
00:24:12,530 --> 00:24:13,400
I have just shifted.

481
00:24:14,150 --> 00:24:15,860
So this is how deletion is done.

482
00:24:16,160 --> 00:24:17,360
Now one more key.

483
00:24:17,360 --> 00:24:19,550
I really need one more to keep from this one.

484
00:24:20,120 --> 00:24:22,040
I want to delete fifty.

485
00:24:23,520 --> 00:24:32,820
57 who will take its place, so if you remember in the binary search trees, when you delete any node,

486
00:24:33,120 --> 00:24:34,010
it's in order.

487
00:24:34,020 --> 00:24:38,520
Predecessor in order, a successor will take its place in binary search team.

488
00:24:38,520 --> 00:24:39,260
You have learned this.

489
00:24:39,270 --> 00:24:40,500
So same thing you have to do.

490
00:24:40,970 --> 00:24:45,960
Still, all I was showing, only deletion of leaf nodes, but not leaf nodes.

491
00:24:46,230 --> 00:24:49,500
I'm showing you I'm deleting 50 50 is gone.

492
00:24:49,830 --> 00:24:51,300
That should take its place.

493
00:24:51,750 --> 00:24:55,480
Either 40 should take its place or 70 should take its place.

494
00:24:55,830 --> 00:24:56,470
Watch this.

495
00:24:56,490 --> 00:24:58,580
This will have multiple steps, just one.

496
00:24:59,250 --> 00:25:00,690
So I want to send 40.

497
00:25:00,720 --> 00:25:02,910
OK, forty goes here then.

498
00:25:02,910 --> 00:25:03,740
What about this note.

499
00:25:04,170 --> 00:25:07,220
This is weekend so this is weekend bottom.

500
00:25:07,470 --> 00:25:12,090
So bring Tarty here and send 15 in the route.

501
00:25:12,420 --> 00:25:16,940
So we have Bordelais, our parent or grandparent, in the right direction.

502
00:25:17,340 --> 00:25:18,990
So this has changed.

503
00:25:19,920 --> 00:25:29,030
Now, let us lead one more I want to delete 40 once again, root, root, so delete 40, 40 is gone.

504
00:25:29,370 --> 00:25:34,410
Who will take its place in order, predecessor in order successor.

505
00:25:34,440 --> 00:25:35,070
So what is that?

506
00:25:35,070 --> 00:25:38,220
In order for this as a means, go to left then.

507
00:25:38,460 --> 00:25:39,240
Right, right.

508
00:25:39,240 --> 00:25:40,080
Right, right, right.

509
00:25:40,080 --> 00:25:43,320
Most key out here go to right and left.

510
00:25:43,320 --> 00:25:44,280
The most key.

511
00:25:44,520 --> 00:25:46,490
So here I will send Tucky up.

512
00:25:46,830 --> 00:25:48,300
So today has gone up.

513
00:25:48,670 --> 00:25:50,640
This, this is weekend then.

514
00:25:51,360 --> 00:25:52,860
Can we borrow from this child.

515
00:25:53,100 --> 00:25:53,640
No.

516
00:25:53,970 --> 00:25:54,750
Then do what.

517
00:25:54,870 --> 00:26:00,330
Much so I will use this more than 10 and 15 hour much then.

518
00:26:00,340 --> 00:26:01,170
What about this.

519
00:26:01,770 --> 00:26:02,580
This is empty.

520
00:26:02,850 --> 00:26:03,450
Does empty.

521
00:26:03,750 --> 00:26:05,040
Can we borrow from there.

522
00:26:05,250 --> 00:26:09,810
No we cannot borrow then more of these also these also.

523
00:26:09,810 --> 00:26:13,110
So what we will do I will write one note OK.

524
00:26:13,530 --> 00:26:14,940
And decide what's this fund.

525
00:26:14,950 --> 00:26:18,540
So in this study will come down and this eighty.

526
00:26:18,540 --> 00:26:19,230
I will show it.

527
00:26:19,230 --> 00:26:20,970
Here it is here.

528
00:26:21,860 --> 00:26:28,410
Then this is Maryland and this is right north, so this note is also gone, and this is also related

529
00:26:28,410 --> 00:26:28,520
to.

530
00:26:29,670 --> 00:26:37,200
This hearts look like, again, you have to watch this slowly, step by step, you watch it how I have

531
00:26:37,200 --> 00:26:42,050
looked like this one once again, played at a slow speed and watch it.

532
00:26:42,870 --> 00:26:44,580
So this whole deletion is done.

533
00:26:45,120 --> 00:26:49,710
So intelligent primarily there were three cases so far I can simply delete.

534
00:26:49,710 --> 00:26:56,890
It is not at all affecting three and second one delete and March or Nexus, delete and bottom if you

535
00:26:56,910 --> 00:26:58,140
get on board in March.

536
00:26:58,260 --> 00:27:03,460
So I have shown you all guesses, like I have grown that we then have reduced the tree.

537
00:27:04,040 --> 00:27:09,900
OK, so that's all about creation and deletion of keys from two, three, three.

538
00:27:10,270 --> 00:27:14,910
We will do some analysis, then I will tell you why or how to treat.

539
00:27:14,910 --> 00:27:17,370
These are used for analysis.

540
00:27:17,370 --> 00:27:21,120
I have taken two examples of four to three traits.

541
00:27:21,630 --> 00:27:28,320
If you see this example, this is having a minimum number of nodes and minimum number of keys, and

542
00:27:28,320 --> 00:27:35,220
this example is having a maximum number of nodes and maximum number of keys for a given height height

543
00:27:35,220 --> 00:27:39,460
I have taken as to that is zero one two and is also height.

544
00:27:39,960 --> 00:27:45,450
So within the height of two, we have minimum keys and minimum nodes and here also maximum keys and

545
00:27:45,450 --> 00:27:47,310
maximum N no.

546
00:27:47,490 --> 00:27:54,240
We are interested in moving in to three to be of height H minimum.

547
00:27:54,240 --> 00:27:55,590
How many nodes are possible.

548
00:27:55,590 --> 00:27:57,430
Maximum hominine also possible.

549
00:27:57,450 --> 00:28:00,120
So let us see if you observe this.

550
00:28:01,290 --> 00:28:08,100
In height two, if you count on the first level, right at its height zero, there's only one note and

551
00:28:08,100 --> 00:28:12,460
here claymores and half full, not so it's looking like a binary tree.

552
00:28:12,870 --> 00:28:16,230
So the formulas for binary tree can work here.

553
00:28:16,260 --> 00:28:16,580
Yes.

554
00:28:16,860 --> 00:28:25,980
So if you observe minimum number of N n equals first level one, not the next level N the Nix's to square.

555
00:28:26,370 --> 00:28:30,380
So this is how it will be going on, increasing up to a given height.

556
00:28:30,450 --> 00:28:38,850
So it will be dupa X plus one, minus one number of N, so minimum number of nodes are Poupart X plus

557
00:28:38,850 --> 00:28:39,930
one, minus one.

558
00:28:40,710 --> 00:28:43,440
Then let us check maximum number of nodes.

559
00:28:43,950 --> 00:28:46,290
Not in this example first level.

560
00:28:46,610 --> 00:28:47,700
We said this is level.

561
00:28:47,970 --> 00:28:55,080
So level one, notice that then in this level three nodes are there then in this level three squared,

562
00:28:55,080 --> 00:28:56,320
nine nodes out there.

563
00:28:56,340 --> 00:28:57,180
So three squared.

564
00:28:57,180 --> 00:29:02,550
So up to any given height there will be three for each maximum number of nodes.

565
00:29:02,850 --> 00:29:03,710
So total.

566
00:29:03,720 --> 00:29:05,610
How many nodes are there total.

567
00:29:06,060 --> 00:29:09,850
Three X plus one minus one by three minus one.

568
00:29:09,870 --> 00:29:16,610
So if you know the sum of the terms of G.P.S., this is the formula formula for some of the terms of

569
00:29:16,990 --> 00:29:22,300
this is geometry progression every time the term is multiplied by three to get that next term.

570
00:29:22,560 --> 00:29:24,670
So that's all three is a common ratio.

571
00:29:24,690 --> 00:29:27,310
So this the formula for the formula.

572
00:29:27,330 --> 00:29:30,960
You can search it and find out and Google some of the items of it.

573
00:29:32,070 --> 00:29:35,350
So this is how maximum number of nodes are possible.

574
00:29:35,370 --> 00:29:41,220
So anything close to three parts, plus one, minus one by three, minus one.

575
00:29:42,090 --> 00:29:43,650
Now, these are minimum nodes.

576
00:29:43,860 --> 00:29:45,540
These are maximum nodes.

577
00:29:45,870 --> 00:29:48,600
Not actually degree of this is three.

578
00:29:48,930 --> 00:29:50,340
Two are the number of keys.

579
00:29:50,550 --> 00:29:51,470
Degree is a three.

580
00:29:51,720 --> 00:29:53,130
But here we go to two.

581
00:29:53,310 --> 00:29:56,010
So that is two is half of that tree.

582
00:29:56,400 --> 00:29:57,820
Not a single value of three.

583
00:29:58,110 --> 00:30:02,230
So if you remember, three by two seen value is what, two?

584
00:30:02,460 --> 00:30:03,390
So that's what we got.

585
00:30:03,390 --> 00:30:09,540
Two here means if this is not a three, if this is any other thing, then it will be divided by two

586
00:30:09,540 --> 00:30:12,090
and the single value that many nodes you will have.

587
00:30:12,420 --> 00:30:14,460
Right for any other degree.

588
00:30:14,640 --> 00:30:20,520
We are analysing only four degree three, but for any other degree, that's how it will be half of the

589
00:30:20,520 --> 00:30:21,000
degree.

590
00:30:21,000 --> 00:30:22,140
This is half of the degree.

591
00:30:23,160 --> 00:30:29,910
Now I have the minimum norson maximum N now from this form I can find out maximum height.

592
00:30:30,450 --> 00:30:35,260
So from this formula, if it can work equal to how much Synagis minus one here.

593
00:30:35,280 --> 00:30:40,390
So this will be a minus one and bring this base to here.

594
00:30:40,410 --> 00:30:45,420
So if you bring it here, it will be long based and that plus one comes here minus one.

595
00:30:45,690 --> 00:30:47,270
So this is logarithmic.

596
00:30:47,460 --> 00:30:50,850
So big of log and base two.

597
00:30:50,850 --> 00:30:51,540
I can see.

598
00:30:52,080 --> 00:30:55,290
So maximum height is asymptotically.

599
00:30:55,320 --> 00:30:55,710
It is.

600
00:30:56,040 --> 00:31:04,110
And mistal now similarly from this formula that is maximum n I can find out minimum height, minimum

601
00:31:04,110 --> 00:31:11,490
height hiders if I can this if I take out height this will be and in two, three, minus one I'm keeping

602
00:31:11,490 --> 00:31:15,890
industry minus one actually is two so three minus one because a degree minus one.

603
00:31:15,900 --> 00:31:17,850
So I'm keeping that three minus one.

604
00:31:18,330 --> 00:31:19,860
That's minus one come the side.

605
00:31:19,860 --> 00:31:26,910
So it becomes plus one then bring this three base three here so it will be low base three and this whole

606
00:31:26,910 --> 00:31:29,770
thing and this plus one comes here minus one.

607
00:31:30,000 --> 00:31:34,460
So this is log base three and Begal.

608
00:31:34,800 --> 00:31:36,640
So this is also log.

609
00:31:36,930 --> 00:31:38,050
This is also log.

610
00:31:38,520 --> 00:31:45,270
So now from this we can say that the minimum height of the three trees for logging as well as a maximum

611
00:31:45,270 --> 00:31:48,450
height is also log-in only basis difference.

612
00:31:48,780 --> 00:31:51,290
But it is a logarithmic logarithmic.

613
00:31:51,750 --> 00:31:59,280
So this is the proof that height of our two three tree are in general height of a big tree is always

614
00:31:59,280 --> 00:31:59,800
log in.

615
00:31:59,970 --> 00:32:02,120
So this is the conclusion of this analysis.

616
00:32:02,550 --> 00:32:04,230
So that's all about the analysis.

617
00:32:04,230 --> 00:32:12,050
We found our maximum minimum Northan maximum minimum height olate not the last thing as where this two

618
00:32:12,060 --> 00:32:13,220
three trees are useful.

619
00:32:13,680 --> 00:32:19,530
Actually it's A B tree or B plus a tree, B tree or B plus tree.

620
00:32:19,830 --> 00:32:27,100
These are used in database's DBMS suffix like like Softworks, like Oracle, SQL Server or MySQL.

621
00:32:27,510 --> 00:32:33,090
These softwares internally uses these things like if you are developing such a software then you have

622
00:32:33,090 --> 00:32:33,870
to use this one.

623
00:32:34,230 --> 00:32:35,700
So these are useful there.

624
00:32:35,880 --> 00:32:39,600
And this is a type of bigotry.

625
00:32:39,960 --> 00:32:45,300
It's a type of arbitrary for studying arbitrary, but it is used as an example.

626
00:32:45,540 --> 00:32:50,520
Right, because if you take a bigger degree or larger sized degree, it is very complex to do some board

627
00:32:50,550 --> 00:32:51,970
work or understand the concept.

628
00:32:52,290 --> 00:32:55,680
So it is understood at a small level by taking only the gretry.

629
00:32:56,190 --> 00:33:00,840
This is one reason then why B, B plus trees are used in databases because.

630
00:33:01,440 --> 00:33:07,470
At once, when the axis and Lord, we can get more than one that is at once, if it is a binary search

631
00:33:07,470 --> 00:33:09,720
tree, then each node can have just one value.

632
00:33:10,080 --> 00:33:15,210
So when you are fishing alone, you get only one that much here in one single load, more than one that

633
00:33:15,210 --> 00:33:15,660
is out there.

634
00:33:15,690 --> 00:33:18,920
So once you have reach at any node, you can access to values.

635
00:33:19,110 --> 00:33:23,850
So when you have reached a particular node, suppose here then when you are accessing this one, then

636
00:33:23,850 --> 00:33:27,240
Nick's address is the next slide and so you can easily access it.

637
00:33:27,720 --> 00:33:36,570
So if you have the idea about direc accessing and indirect accessing, so in the node, these values,

638
00:33:36,570 --> 00:33:40,850
both of them can be accessed using direct access method that is in a single step.

639
00:33:41,520 --> 00:33:46,000
And if you want to access this more than from here, you'll be taking an address and going there.

640
00:33:46,230 --> 00:33:47,800
So that is indirect access.

641
00:33:48,090 --> 00:33:53,640
So going to the next node is indirect access then within that same node, direct access.

642
00:33:54,030 --> 00:33:58,020
So the benefit of the doubt and direct access, you can get the multiple values at one.

643
00:33:58,020 --> 00:34:01,390
So it is little faster than using binary search team.

644
00:34:02,010 --> 00:34:03,210
And one last point.

645
00:34:03,510 --> 00:34:08,659
If you are using binary search for these keys than the height of a tree will be very big.

646
00:34:09,270 --> 00:34:11,850
But here the height is smaller one.

647
00:34:12,360 --> 00:34:17,260
So if you talk about height, this height is smaller than binary search.

648
00:34:17,670 --> 00:34:19,110
So that's all in this video.

649
00:34:19,230 --> 00:34:21,170
We have learned about two, three, three.

