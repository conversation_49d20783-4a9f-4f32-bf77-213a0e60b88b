1
00:00:00,600 --> 00:00:05,140
Now, let us learn how our compiler handles or manage.

2
00:00:06,330 --> 00:00:08,950
So what are the issues that it has to deal with?

3
00:00:09,150 --> 00:00:16,379
So for explaining that, I would take an example of simple variable that Skidder variable C in our programs,

4
00:00:16,379 --> 00:00:21,540
we use variables as names for representing some data.

5
00:00:22,770 --> 00:00:29,850
But the compiler conversion Tymoshenko machine code will not have variable names during execution for

6
00:00:29,850 --> 00:00:30,740
this variable.

7
00:00:31,080 --> 00:00:37,890
Let us say to bytes of memory, I look at it and the addresses are 100 and one, not one supples during

8
00:00:37,890 --> 00:00:39,540
execution is the memory allocated.

9
00:00:41,530 --> 00:00:44,260
This variable X represents this address.

10
00:00:45,950 --> 00:00:51,590
And the Vatican has to be stored at that location at this address 100.

11
00:00:52,880 --> 00:01:00,320
So basically, machine code should refer further location with the protesters, not by a name, then

12
00:01:00,320 --> 00:01:02,780
<PERSON><PERSON><PERSON> has to convert name into an address.

13
00:01:03,890 --> 00:01:09,640
One that can be known when the memories are located, when the memories are located during execution,

14
00:01:09,920 --> 00:01:14,660
so it means the memory for the variable is allocated at execution, then only the address can be known,

15
00:01:14,900 --> 00:01:18,620
then the whole compiler will write down the address at compile time.

16
00:01:19,160 --> 00:01:23,030
So that's what we will learn about this issue in Uhry.

17
00:01:23,150 --> 00:01:29,980
That is of the variable here we have an example array which is already initialize in my program.

18
00:01:30,320 --> 00:01:35,840
Then during execution time, memory will be allocated for the array and the values will be stored in

19
00:01:35,840 --> 00:01:37,040
corresponding locations.

20
00:01:37,370 --> 00:01:41,210
Assuming that these are their addresses, not in my program.

21
00:01:41,210 --> 00:01:49,250
If I have written, I want to access eight of three and I want to store the value 10.

22
00:01:49,580 --> 00:01:51,890
So it means I want to store the value ten here.

23
00:01:52,770 --> 00:01:54,020
Then this.

24
00:01:54,070 --> 00:01:59,810
A statement in my program has to be converted into machine code, which is referring to this address.

25
00:01:59,810 --> 00:02:01,270
That is 206.

26
00:02:01,820 --> 00:02:03,380
The address is 206.

27
00:02:03,770 --> 00:02:12,230
So Compiler has to refer to that location that is 206 or how this code of our program will get converted

28
00:02:12,230 --> 00:02:13,180
into machine code.

29
00:02:13,640 --> 00:02:20,070
So actually, compiler needs address of our location, eight of three.

30
00:02:20,690 --> 00:02:24,830
So how it this obtain so item by item that this cannot be known.

31
00:02:24,980 --> 00:02:28,330
So Compiler relied on a formula for obtaining their address.

32
00:02:28,940 --> 00:02:30,920
So how that formula looks like laodicean.

33
00:02:31,530 --> 00:02:35,430
See, this is the name of an array and this is the first address of a.

34
00:02:36,080 --> 00:02:40,230
So this first address we can call it does not or locations evil.

35
00:02:40,580 --> 00:02:44,230
And the name of an array is called US based address of an array.

36
00:02:44,540 --> 00:02:49,510
So any location in an array indexing in that it can be accessed with the help of visitors.

37
00:02:49,520 --> 00:02:53,260
Let us and not so alone is the best address that is two hundred.

38
00:02:53,750 --> 00:02:55,850
So this is the starting of the finale then.

39
00:02:55,850 --> 00:02:56,360
Which index.

40
00:02:56,360 --> 00:02:57,920
I want to access this one.

41
00:02:57,920 --> 00:02:58,990
This addresses how much.

42
00:02:59,000 --> 00:02:59,450
Six.

43
00:02:59,780 --> 00:03:04,890
So this address I want to access that is three into two.

44
00:03:06,290 --> 00:03:08,510
This gives me two hundred and six.

45
00:03:09,650 --> 00:03:13,850
So Y three, this is the index that I want to access then why two.

46
00:03:14,060 --> 00:03:17,630
Because each integer takes two bytes to whites.

47
00:03:18,110 --> 00:03:21,530
So it means from the base of an array location zero.

48
00:03:21,770 --> 00:03:28,490
I want to access the third index where each index it takes two whites so the final addresses will not

49
00:03:28,490 --> 00:03:28,880
six.

50
00:03:29,120 --> 00:03:32,630
So using this formula, the address to not six can be obtained.

51
00:03:32,840 --> 00:03:34,860
So I will convert that into a formula.

52
00:03:35,180 --> 00:03:41,840
This is L.A. plus three into size of an integer.

53
00:03:42,890 --> 00:03:44,120
So address of.

54
00:03:45,580 --> 00:03:51,360
Eight of three, if often this now for adults of any location.

55
00:03:53,310 --> 00:04:00,260
If I hold, can we obtain a lot plus I mean, index into.

56
00:04:00,660 --> 00:04:03,210
Now this is an example of integer.

57
00:04:03,550 --> 00:04:09,050
So two bytes if it is floating rate, maybe for the white character, they may be won by Doubler eight,

58
00:04:09,060 --> 00:04:09,900
maybe eight byte.

59
00:04:10,170 --> 00:04:15,610
So depending on the data type, the number of bytes taken by each element may vary.

60
00:04:16,079 --> 00:04:18,810
So I have to take the size of the date at night.

61
00:04:19,140 --> 00:04:22,000
So we use a variable that is W.

62
00:04:23,010 --> 00:04:24,620
So this is the address.

63
00:04:26,550 --> 00:04:27,840
This is index.

64
00:04:29,970 --> 00:04:40,590
And this is the size of a data type, so this is the formula used by COMPILER for converting any index

65
00:04:40,950 --> 00:04:45,490
to address for obtaining the actual address during runtime.

66
00:04:45,750 --> 00:04:53,340
Our program must know the basis of an array, so based basters of an array will be updated once the

67
00:04:53,340 --> 00:04:58,470
program starts running and once the memory for array is allocated.

68
00:04:58,620 --> 00:05:03,930
So the address of this location is known during runtime and it is updated at one time and it is called

69
00:05:03,930 --> 00:05:04,990
as data binding.

70
00:05:05,460 --> 00:05:11,730
So if you want to study about this one, you can study about systems programming or assembly language

71
00:05:11,730 --> 00:05:14,230
programming, then you can learn about these things.

72
00:05:14,880 --> 00:05:18,690
So here we have learn how our compiler generates the formula.

73
00:05:18,990 --> 00:05:23,770
So this is a formula for address and this is not actual address.

74
00:05:24,030 --> 00:05:25,470
So this is the logic of letters.

75
00:05:25,890 --> 00:05:27,690
And this is not a static address.

76
00:05:27,720 --> 00:05:29,550
This is related to base headers.

77
00:05:29,880 --> 00:05:32,720
So this is also a relative address.

78
00:05:32,910 --> 00:05:41,160
So the formula is a relative formula based on base letters of an array from compilers, all declaration

79
00:05:41,160 --> 00:05:44,060
of an array from starting index one also.

80
00:05:44,310 --> 00:05:46,230
Then how the formula will change.

81
00:05:46,260 --> 00:05:47,250
Let us look at it.

82
00:05:48,120 --> 00:05:54,780
I have modified the declaration, suppose in some X, Y, Z language other can have in this starting

83
00:05:54,780 --> 00:06:00,060
from one to five, then this will be the next one and the index will be five.

84
00:06:01,020 --> 00:06:06,870
Now if I want to access any location in an area that is of three dislocation, I want to modify to ten,

85
00:06:07,230 --> 00:06:10,360
then how the formula is written by a compiler.

86
00:06:10,740 --> 00:06:22,620
So for the address of a location, eight of the three, the base address that is 200 plus total index

87
00:06:22,620 --> 00:06:23,130
is this one.

88
00:06:23,140 --> 00:06:25,680
So it has reached that is two to four.

89
00:06:26,010 --> 00:06:33,150
So it should be three minus one, one less because the indices are starting from one onwards into two.

90
00:06:33,390 --> 00:06:35,800
So this will be two hundred and four.

91
00:06:36,270 --> 00:06:47,160
So for any index address can be obtained by E of I as this is location zero and not and this is Index

92
00:06:47,310 --> 00:06:49,500
I minus one in two.

93
00:06:49,710 --> 00:06:51,290
This is the world size.

94
00:06:51,300 --> 00:06:52,950
That is the data type size.

95
00:06:52,950 --> 00:06:53,790
That is a double.

96
00:06:54,480 --> 00:06:58,620
So this is the formula indices are starting from one onwards.

97
00:06:59,640 --> 00:07:00,480
So this is not there.

98
00:07:00,480 --> 00:07:04,710
And C C++, but this used to be there in all languages.

99
00:07:05,580 --> 00:07:11,730
They used to allow other declaration from any starting index so programmer can have an array start from

100
00:07:11,730 --> 00:07:13,500
zero also one also.

101
00:07:14,790 --> 00:07:20,370
But in C C++ it is strictly from Z only reason why.

102
00:07:20,550 --> 00:07:24,540
Why in C C++ we cannot have attained if starting from one onwards.

103
00:07:25,050 --> 00:07:25,950
Let us see this.

104
00:07:26,640 --> 00:07:30,340
We have seen the formula for array indexes starting from zero also.

105
00:07:30,600 --> 00:07:37,460
So just I will recall that formula that was I input W and this is indexed starting from one onwards.

106
00:07:37,460 --> 00:07:38,440
So this is the formula.

107
00:07:38,700 --> 00:07:44,800
So the difference is there is one ultimate expression extra that is minus one is extra.

108
00:07:45,060 --> 00:07:47,850
So if you count the number of operations, one, two, three.

109
00:07:48,570 --> 00:07:50,610
And here there are only two operations.

110
00:07:51,060 --> 00:07:52,540
So one operation is external.

111
00:07:53,220 --> 00:07:58,960
So as this is an early for accessing each location, I have to use the formula.

112
00:07:59,400 --> 00:08:06,270
So if there are any location, then this operation will be performed for ten times if I'm accessing

113
00:08:06,270 --> 00:08:07,120
all the locations.

114
00:08:07,590 --> 00:08:13,740
So those are a matter of a single operation that is just one operation extra, but it will be slower

115
00:08:13,950 --> 00:08:18,240
depending on the size of an array of the sizes, very large of them accessing all the elements.

116
00:08:18,280 --> 00:08:19,500
Then it will be really slow.

117
00:08:19,890 --> 00:08:26,250
It will be affecting the time taken by a program that is the reason c C++ languages.

118
00:08:26,320 --> 00:08:33,780
They allow Arain before starting from zero only because the formula have just less number of operations

119
00:08:33,780 --> 00:08:36,440
and it is faster than this formula.

120
00:08:38,049 --> 00:08:43,260
So this all about single dimensionally, not even known about two dimensional, Larry.

