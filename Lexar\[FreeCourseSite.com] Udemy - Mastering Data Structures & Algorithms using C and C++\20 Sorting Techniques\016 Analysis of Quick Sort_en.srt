1
00:00:00,340 --> 00:00:05,700
Novita analysis of a quicksort before discussing time and the space complexity.

2
00:00:06,470 --> 00:00:08,160
I want to discuss some example.

3
00:00:08,730 --> 00:00:10,680
See, mostly people get confused.

4
00:00:10,680 --> 00:00:15,540
If there are too few elements like two or three elements out there, then how quicksort can work on

5
00:00:15,540 --> 00:00:15,680
it.

6
00:00:15,990 --> 00:00:21,680
So I take a small list of three elements and show you how quicksort can work on it and also two elements

7
00:00:21,690 --> 00:00:22,210
and show you.

8
00:00:23,010 --> 00:00:24,530
So let us take three elements.

9
00:00:24,540 --> 00:00:28,180
Suppose the elements are 2010 and today.

10
00:00:28,290 --> 00:00:29,520
This is infinity.

11
00:00:29,700 --> 00:00:30,750
This is Pivot.

12
00:00:31,110 --> 00:00:32,040
This is AI.

13
00:00:32,189 --> 00:00:34,830
And this is the initial setup.

14
00:00:35,310 --> 00:00:37,830
We have to find a solid position of 20.

15
00:00:38,980 --> 00:00:45,820
Like, so let us start using I find out in the element that is greater than grindy, is it greater?

16
00:00:46,150 --> 00:00:47,620
Is it has found?

17
00:00:49,220 --> 00:00:56,390
Using do you find out any element that is smaller than or equal to 20, is it smaller nor is it smaller?

18
00:00:56,420 --> 00:01:02,830
Yes, we've got the potential for energy, but now it has became greater than if I go to the gym.

19
00:01:02,840 --> 00:01:06,870
And so the sport will get slapped with the element at the gym.

20
00:01:07,430 --> 00:01:12,740
So then comes here today, goes there and this site 20, that site today.

21
00:01:12,740 --> 00:01:16,850
And it's one place this Infinity's from here, the list is partitioned.

22
00:01:17,150 --> 00:01:18,680
There is only one element beside.

23
00:01:18,680 --> 00:01:21,830
There is only one element in this infinity's ignorable.

24
00:01:21,870 --> 00:01:22,090
Right.

25
00:01:22,430 --> 00:01:24,560
So there is only one element, that's all.

26
00:01:25,070 --> 00:01:26,600
Now this is one element is sorted.

27
00:01:26,690 --> 00:01:28,930
And on the other side we've got one one element.

28
00:01:29,060 --> 00:01:30,990
So this list is sorted.

29
00:01:31,190 --> 00:01:36,290
If you perform quicksort on this, quicksort on this, quicksort will not work upon them because it

30
00:01:36,290 --> 00:01:36,770
will work.

31
00:01:36,770 --> 00:01:38,660
At least there are two elements.

32
00:01:39,200 --> 00:01:41,180
So this was the example of three elements.

33
00:01:41,600 --> 00:01:43,590
Now, let's take the example of two elements.

34
00:01:43,590 --> 00:01:47,330
Suppose I have 10 and 20 that's on.

35
00:01:47,340 --> 00:01:49,380
This is infinitely hogweed.

36
00:01:49,700 --> 00:01:50,630
It will work on this.

37
00:01:51,200 --> 00:01:57,960
First element is pivot iis on first element on infinity using the final element that is greater than

38
00:01:57,960 --> 00:01:58,250
ten.

39
00:01:59,090 --> 00:02:03,410
This is fun using the final element that is smaller than or equal to.

40
00:02:03,650 --> 00:02:05,480
Is it smaller or is it smaller.

41
00:02:05,510 --> 00:02:06,440
No, but it is equal.

42
00:02:06,770 --> 00:02:07,720
OK, stop here.

43
00:02:08,270 --> 00:02:09,410
I became greater than.

44
00:02:09,919 --> 00:02:17,390
So we will not stop by increment but we will swap a P word of it to be what is also same g also same.

45
00:02:17,630 --> 00:02:19,260
So it is swap fit itself.

46
00:02:19,640 --> 00:02:27,410
So like if this is low and this is high, so e low is swapped with f g so same element will swap that

47
00:02:27,770 --> 00:02:30,500
nothing will happen then remain so there only.

48
00:02:31,010 --> 00:02:32,680
So now this is partition here.

49
00:02:32,720 --> 00:02:34,190
So this again is sorted.

50
00:02:34,640 --> 00:02:38,030
Then on that side we have just twenty ignored infinity.

51
00:02:38,390 --> 00:02:40,140
So there is a single element no need to solve.

52
00:02:40,550 --> 00:02:42,140
So that's all I want.

53
00:02:42,140 --> 00:02:43,020
Two elements also.

54
00:02:43,310 --> 00:02:44,780
No, I'll take one more example.

55
00:02:44,780 --> 00:02:49,340
If the elements are already in ascending order then how quicksort works.

56
00:02:49,590 --> 00:02:52,310
So here is a list of elements which are already sorted.

57
00:02:52,310 --> 00:02:55,950
And if we run quicksort on this, let us see what happens first.

58
00:02:55,970 --> 00:02:56,540
It will select.

59
00:02:56,540 --> 00:03:02,170
This has been what I hear and G here using I find out any element that is greater than 10.

60
00:03:02,180 --> 00:03:04,200
So this is found using J.

61
00:03:04,220 --> 00:03:07,250
Find out any element that is a smaller than or equal to ten.

62
00:03:07,550 --> 00:03:08,660
Is it smaller than that.

63
00:03:08,690 --> 00:03:10,750
No, no, no, no.

64
00:03:10,760 --> 00:03:11,720
Yes, equal.

65
00:03:12,170 --> 00:03:13,670
This is low and this is high.

66
00:03:13,730 --> 00:03:14,150
Right.

67
00:03:14,180 --> 00:03:22,820
So J and I so I became greater than J so interchange P what element of it J that if the change element

68
00:03:22,820 --> 00:03:26,300
that L j so it is in the change itself.

69
00:03:27,320 --> 00:03:28,000
Then the list.

70
00:03:29,030 --> 00:03:34,130
How many elements do you get on this site, nothing, and then how many elements on this site?

71
00:03:34,370 --> 00:03:39,290
We get all these elements 20, 30, 40, 50 and infinity.

72
00:03:40,860 --> 00:03:43,740
So from here, partitioning is done right?

73
00:03:43,770 --> 00:03:49,560
You've done the procedure partitioning the left hand side, nothing, right hand side, all the remaining

74
00:03:49,560 --> 00:03:50,210
elements.

75
00:03:50,220 --> 00:03:55,770
That is, if you say these were amendments, then these are and the minus one elements the next.

76
00:03:56,160 --> 00:03:57,380
We have to continue sorting.

77
00:03:57,570 --> 00:03:58,800
Select this Aspinwall.

78
00:03:58,830 --> 00:03:59,460
This is law.

79
00:03:59,460 --> 00:04:01,360
And this is why I is here.

80
00:04:01,360 --> 00:04:06,300
The Jews here using I find out in element that is going to be found using.

81
00:04:06,300 --> 00:04:08,900
Do you find any element that is smaller than or equal to 20.

82
00:04:09,030 --> 00:04:11,010
Not smaller, not smaller, not smaller.

83
00:04:11,010 --> 00:04:13,530
Equal phone eye is greater than G.

84
00:04:13,770 --> 00:04:19,620
So if you got a partitioning position interchange element, that law that G will be what elements will

85
00:04:19,620 --> 00:04:22,089
add to the G C the subject itself.

86
00:04:22,620 --> 00:04:24,330
So no change element remains there.

87
00:04:24,330 --> 00:04:26,370
Only partitioning is done.

88
00:04:26,760 --> 00:04:28,750
You remember J partitioning is done.

89
00:04:29,130 --> 00:04:30,710
So how many elements on the left side.

90
00:04:30,900 --> 00:04:34,410
Nothing is there on the left hand side remaining all on the right hand side.

91
00:04:34,420 --> 00:04:38,190
So the remaining elements are 30, 40, 50 and infinity.

92
00:04:38,400 --> 00:04:39,840
Now how many elements are there?

93
00:04:39,840 --> 00:04:46,230
And minus two elements rather than select B word, I hear J here using, I find out any element that

94
00:04:46,230 --> 00:04:46,600
is good to.

95
00:04:47,070 --> 00:04:53,060
So it is formed using different elements smaller than or equal to not smaller, not smaller is equal.

96
00:04:53,310 --> 00:04:55,620
So stop here engaging with itself.

97
00:04:55,620 --> 00:04:56,540
Partition the list.

98
00:04:56,760 --> 00:04:57,770
So today is gone.

99
00:04:58,060 --> 00:05:01,400
The remaining 40 and 50 are infinity remaining.

100
00:05:02,310 --> 00:05:03,420
Know how many elements?

101
00:05:03,420 --> 00:05:04,230
Two elements.

102
00:05:04,850 --> 00:05:05,910
Then select this aspect.

103
00:05:05,920 --> 00:05:09,480
What I hear here mu I do find greater element.

104
00:05:09,480 --> 00:05:13,320
Geophone moved here to find smaller or equal C.M.A.

105
00:05:14,280 --> 00:05:15,390
Partitioning is done here.

106
00:05:15,390 --> 00:05:21,110
What is fifty and the one element left for one element of it.

107
00:05:21,660 --> 00:05:26,070
So you can see this is how quicksort is working on a solid list.

108
00:05:26,430 --> 00:05:31,440
It means the first time when an elements forgiver next time nothing was there on this side and minus

109
00:05:31,440 --> 00:05:32,430
one element of that site.

110
00:05:32,790 --> 00:05:35,790
Then again, nothing on the side and minus two elements the site.

111
00:05:36,030 --> 00:05:42,840
Then this has gone that using then finally two elements of the site, new elements, the site, one

112
00:05:42,840 --> 00:05:45,210
element, the site and zero element the site.

113
00:05:45,240 --> 00:05:48,240
No, let us find out the time, see what it is doing.

114
00:05:48,570 --> 00:05:50,820
First time and elements were compared.

115
00:05:50,910 --> 00:05:54,180
I was moved here, but the G was brought from here.

116
00:05:54,480 --> 00:05:55,110
Bill here.

117
00:05:56,370 --> 00:06:02,710
And competitions again here also was brought from infinity till this one and the minus one competition.

118
00:06:02,970 --> 00:06:07,530
So as many elements are there, that many competitions are done for the first time and competitions

119
00:06:07,530 --> 00:06:11,610
are done then and minus one competition then and minus two, so on.

120
00:06:11,610 --> 00:06:13,110
Then two, then finally one.

121
00:06:13,440 --> 00:06:14,160
So total.

122
00:06:14,160 --> 00:06:14,750
How many.

123
00:06:14,760 --> 00:06:19,190
One plus two plus three plus goes on to any competitions.

124
00:06:19,200 --> 00:06:25,160
I'm adding all these competitions so this is how much and they'll go and plus one by two.

125
00:06:25,470 --> 00:06:27,780
So this is outdraw and square.

126
00:06:27,810 --> 00:06:36,010
So this is the time, complexity of quicksort format sorted list if the list is already sodic.

127
00:06:36,300 --> 00:06:38,880
So actually this is the worst case.

128
00:06:38,880 --> 00:06:40,230
Time off quicksort.

129
00:06:40,590 --> 00:06:41,760
This is the worst case.

130
00:06:42,030 --> 00:06:47,970
If the list is already sorted, it is sticking and square time and the list is sorted in a certainly

131
00:06:47,970 --> 00:06:49,410
more than one conclusion.

132
00:06:49,410 --> 00:06:56,790
If the list is sorted, ascending order and we are trying to sort using quicksort, it performs these

133
00:06:56,790 --> 00:07:00,060
many competitions so that hard and square competition.

134
00:07:00,330 --> 00:07:04,290
So the time, complexity of quicksort and square are removed.

135
00:07:04,620 --> 00:07:05,280
Then we will see.

136
00:07:05,370 --> 00:07:08,600
Second case, I have taken a list in descending order.

137
00:07:08,760 --> 00:07:11,270
Let us understand how quicksort works on this one.

138
00:07:12,060 --> 00:07:14,030
Let us sort through elements of it.

139
00:07:14,070 --> 00:07:18,540
So this is selected as people I use here JS here using.

140
00:07:18,540 --> 00:07:21,630
I find out any element that is greater than fifty.

141
00:07:22,650 --> 00:07:23,370
Is it greater.

142
00:07:23,400 --> 00:07:24,930
No, not a greater than 50.

143
00:07:24,960 --> 00:07:26,140
No, no, no.

144
00:07:26,400 --> 00:07:26,940
Yes.

145
00:07:26,940 --> 00:07:30,070
This is a great infinity is greater than using.

146
00:07:30,110 --> 00:07:32,970
You find any element that is smaller than 50.

147
00:07:33,990 --> 00:07:34,740
Is it smaller?

148
00:07:34,770 --> 00:07:35,790
Yes, this is smaller.

149
00:07:37,430 --> 00:07:43,920
But I became greater than Jay, so interchange be what element of it, Jay?

150
00:07:44,390 --> 00:07:53,330
So if you send it here, then 10, 40, 30, 20 and 50 and infinity.

151
00:07:55,210 --> 00:07:58,450
This element is that these elements are remaining to be sorted out.

152
00:07:59,570 --> 00:08:00,510
Fifty sorted.

153
00:08:00,740 --> 00:08:06,140
How many elements on the right side, nothing is there is not an element, so nothing is there on the

154
00:08:06,140 --> 00:08:06,920
right hand side.

155
00:08:08,270 --> 00:08:15,080
So if you see now the previously I have shown you a three, so this and not the side and minus one element

156
00:08:15,080 --> 00:08:16,270
this side, nothing is there.

157
00:08:17,240 --> 00:08:21,980
So as the list is partition, it is partition into two support, one of the list is empty.

158
00:08:22,010 --> 00:08:22,760
Nothing is there.

159
00:08:23,570 --> 00:08:29,570
So on these elements, if you perform, then 10, 40, 30 and 25 perform.

160
00:08:29,840 --> 00:08:35,659
This is selected as people I use here assume that in cities, the Jews here, OK, that 50 will act

161
00:08:35,659 --> 00:08:36,250
as infinity.

162
00:08:36,260 --> 00:08:37,220
So I will not show 50.

163
00:08:37,220 --> 00:08:43,159
I will show infinity only using I find out an element that is greater using the final element that is

164
00:08:43,309 --> 00:08:44,780
smaller than or equal to 10.

165
00:08:44,840 --> 00:08:45,470
Is it smaller.

166
00:08:45,500 --> 00:08:46,970
No, no, no.

167
00:08:47,570 --> 00:08:51,380
Found I became greater than 10 so we would eliminate.

168
00:08:51,510 --> 00:08:53,150
Let's start with the jade.

169
00:08:53,460 --> 00:08:57,320
So one element is the remaining elements are these.

170
00:08:59,250 --> 00:09:01,470
So, no, this time left Penn State nothing.

171
00:09:01,670 --> 00:09:06,750
Everything is on the right hand side, so on this side, nothing, and the minus two elements on this

172
00:09:06,750 --> 00:09:07,130
side.

173
00:09:07,770 --> 00:09:14,180
So if it is in descending order that one time partitioning was done at the end of the right hand side,

174
00:09:14,530 --> 00:09:17,170
then the next time it was done on the left hand side.

175
00:09:17,610 --> 00:09:19,710
So if partitioning is happening on.

176
00:09:20,910 --> 00:09:28,230
Leftmost position or rightmost position, then the time taken by quicksort will be.

177
00:09:29,880 --> 00:09:37,110
If I continue this, you know the answer and square, yes, so the time complexity of quicksort will

178
00:09:37,110 --> 00:09:38,670
be in square.

179
00:09:40,250 --> 00:09:46,340
So this is the worst case time of quicksort, because the partitioning is happening on any one end of

180
00:09:46,340 --> 00:09:51,860
our list and one of the list is being empty, all the rest of the elements are coming in one single

181
00:09:51,860 --> 00:09:52,180
list.

182
00:09:52,610 --> 00:09:56,120
So that's why times are rough and square.

183
00:09:57,020 --> 00:10:04,280
And this is worst case time of quicksort, so finally now conclusion now of the conclusion again.

184
00:10:05,430 --> 00:10:11,700
Elements, if they are in ascending order or descending order, quicksort will take any nucleotide.

