1
00:00:00,510 --> 00:00:06,510
So it is observed that when you are searching for any key element, then there is a possibility that

2
00:00:06,510 --> 00:00:09,200
you search for the same key element again.

3
00:00:09,810 --> 00:00:14,430
For example, if you have a box, if you have a collection of books in the shelf, that is your personal

4
00:00:14,430 --> 00:00:15,060
collection.

5
00:00:15,390 --> 00:00:18,720
You have search for a particular book and you take out the book and read it again.

6
00:00:18,730 --> 00:00:19,330
You keep it back.

7
00:00:19,560 --> 00:00:24,160
What is the possibility that again tomorrow you will search for the same book, the possibility of height?

8
00:00:24,750 --> 00:00:30,570
So that's what in the search, the behavior of searches that when we search for something, a possibility

9
00:00:30,570 --> 00:00:37,950
that we search for it again, then when we are searching for five, how many competition it has taken

10
00:00:38,400 --> 00:00:43,490
total eight competitions we had performed to reach five, and that is triple seven.

11
00:00:44,070 --> 00:00:49,680
Now, if we are searching for five again, then what we want is it should be search faster.

12
00:00:50,660 --> 00:00:51,870
So there are two options.

13
00:00:52,280 --> 00:00:58,620
One option is you can move a five to the previous location, just one location.

14
00:00:59,210 --> 00:01:01,740
You can swap five with the previous element.

15
00:01:01,750 --> 00:01:04,160
So it will reduce by one step.

16
00:01:04,220 --> 00:01:07,790
So second time when you're searching, you have to perform one less competition.

17
00:01:08,250 --> 00:01:10,080
Then again, five research.

18
00:01:10,100 --> 00:01:11,070
Then again, it will move.

19
00:01:11,090 --> 00:01:15,890
So every time, whenever you are searching five, then it will be coming closer and closer.

20
00:01:16,160 --> 00:01:18,790
Now five is moved here and Dennis moved here.

21
00:01:18,810 --> 00:01:20,690
So to this fight.

22
00:01:21,080 --> 00:01:27,380
And then again now again, if you're searching for five, then three, we'll go there and five will

23
00:01:27,380 --> 00:01:27,960
come here.

24
00:01:28,490 --> 00:01:35,570
So if you again search, it will again move closer so you can move a key element, which is repeatedly

25
00:01:35,570 --> 00:01:36,230
search.

26
00:01:36,230 --> 00:01:38,730
You can move it one step forward.

27
00:01:39,950 --> 00:01:45,860
So this method is called transposition, so that transportation, you can do it here.

28
00:01:46,100 --> 00:01:56,900
If that key element is found, then you can swap that key element that if I would if I minus one and

29
00:01:56,900 --> 00:02:02,900
know your key element index, i.e. minus one, then you can return ie minus one.

30
00:02:04,460 --> 00:02:09,720
So this one statement you can add inside search procedure.

31
00:02:09,949 --> 00:02:15,830
So if the element is found, so you're just moving one step forward so that next time it can be searched

32
00:02:15,830 --> 00:02:16,370
faster.

33
00:02:16,970 --> 00:02:19,280
This is one method, no second method.

34
00:02:19,280 --> 00:02:19,840
I'll show you.

35
00:02:19,840 --> 00:02:25,640
The second method is if you are searching for a key element which is found here after performing eight

36
00:02:25,640 --> 00:02:29,990
competitions, then once it is found, you bring it in the beginning.

37
00:02:31,610 --> 00:02:35,930
Swap it with the first element, if you're swapping it, the first element.

38
00:02:35,960 --> 00:02:39,740
The next time when you're searching, it will be found just in constant time.

39
00:02:40,280 --> 00:02:43,580
So this method is called move to front.

40
00:02:45,860 --> 00:02:52,570
Or move to help the old guard and move ahead, move to friend or move to head.

41
00:02:53,430 --> 00:02:59,210
So another suggestion or another guidelines for improving Leanyer such.

42
00:02:59,480 --> 00:03:05,720
So here in the school, if the key elements found, then we can swap the element with index zero.

43
00:03:07,350 --> 00:03:13,170
And now the new index is zero for the elements of five years brought here, let us assume five years

44
00:03:13,170 --> 00:03:14,380
here and eight is here.

45
00:03:14,640 --> 00:03:20,880
So the key element that we have, such as broad in front, so you can on index zero, that element that's

46
00:03:20,910 --> 00:03:21,510
invisible.

47
00:03:22,080 --> 00:03:25,350
So there's another matter in transportation.

48
00:03:25,350 --> 00:03:30,900
The elements will be moving slowly forward, or if it is not sorted, then it will be slowly moving

49
00:03:30,900 --> 00:03:31,290
back.

50
00:03:33,540 --> 00:03:39,270
This is how it behaves, then move to front, if the limit is surge, then directly just broke into

51
00:03:39,270 --> 00:03:40,110
the first position.

52
00:03:40,620 --> 00:03:46,810
Suppose now if you search for two, then five will be sent on that next nine and two is brought here.

53
00:03:47,040 --> 00:03:52,560
So we don't know where the present element like eight will be thrown away, but the new element will

54
00:03:52,560 --> 00:03:53,660
be brought out again next.

55
00:03:53,820 --> 00:03:55,820
So then again, this may be thrown out somewhere else.

56
00:03:55,830 --> 00:04:02,220
If you're searching for some other value in transportation, there is a slow reduction in the time taken

57
00:04:02,220 --> 00:04:04,530
for searching and moved to.

58
00:04:04,870 --> 00:04:07,510
There is a certain reduction in the time to come for searching.

59
00:04:07,530 --> 00:04:11,280
Next time, if you're keeping on searching, it will be done only in constant time.

60
00:04:13,690 --> 00:04:18,250
So that's all about linear search and its analysis.

