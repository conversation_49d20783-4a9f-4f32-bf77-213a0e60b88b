1
00:00:00,330 --> 00:00:06,240
And this video, we will see how to change the case of Alphabet's, that is Cephus from lower case to

2
00:00:06,240 --> 00:00:10,410
upper case application to lower case of characters in a string.

3
00:00:11,240 --> 00:00:17,630
So here I have an example, I have a string that is welcome, this is all in capital letters or upper

4
00:00:17,630 --> 00:00:18,190
cases.

5
00:00:18,680 --> 00:00:22,480
I want to change those alphabets to lower cases.

6
00:00:22,970 --> 00:00:24,190
So it is possible.

7
00:00:24,500 --> 00:00:25,120
Let us see.

8
00:00:25,580 --> 00:00:33,530
See, already we have seen the ASCII code for Alphabet's capital ASCII code is sixty five and B's as

9
00:00:33,530 --> 00:00:35,570
you go to six to six and so on.

10
00:00:35,900 --> 00:00:38,720
And capitals that ASCII code is nine P.

11
00:00:39,730 --> 00:00:46,390
Similarly, we have seen as he called for <PERSON>, give us also that is 97 and this is 98.

12
00:00:47,330 --> 00:00:55,550
And so on up the lower case, that is one going to win all these cases, so it means one of is capital

13
00:00:55,550 --> 00:01:04,459
W. I have to change it to lowercase, to W. So if suppose it is uppercase e I want to convert it to

14
00:01:04,459 --> 00:01:07,890
lowercase A for how it is possible.

15
00:01:08,180 --> 00:01:09,680
So actually the code is there.

16
00:01:09,680 --> 00:01:10,770
That is sixty five.

17
00:01:11,120 --> 00:01:12,130
This is ninety seven.

18
00:01:12,230 --> 00:01:13,210
What is the difference.

19
00:01:13,550 --> 00:01:18,050
Ninety seven minus sixty five is 32.

20
00:01:19,120 --> 00:01:20,030
This is the difference.

21
00:01:20,290 --> 00:01:29,200
What was the difference between these two alphabets, 98 minus 66 is, again, 32 or so.

22
00:01:29,200 --> 00:01:34,450
It means that uppercase and lowercase letters differences 32.

23
00:01:35,160 --> 00:01:35,610
Yes.

24
00:01:35,980 --> 00:01:46,120
So it means to similarly capitalized and small letters that if I take that is 120 to minus 90 is three

25
00:01:46,120 --> 00:01:47,730
to four years.

26
00:01:47,950 --> 00:01:52,650
So it means that the difference between uppercase to lowercase is of thirty two.

27
00:01:53,260 --> 00:01:59,440
If it is uppercase in that if I add thirty two then I'll get a lowercase ASCII code.

28
00:01:59,980 --> 00:02:03,010
So is it possible to add numbers to alphabets.

29
00:02:03,040 --> 00:02:08,020
Yes, actually the alphabets are letters are nothing but codes.

30
00:02:08,169 --> 00:02:12,820
They are numeric codes for a programmer or for the user.

31
00:02:12,830 --> 00:02:18,530
It is visible as W but actually it is ASCII code inside the main memory.

32
00:02:19,030 --> 00:02:22,460
So yes, I can modify it by adding some number.

33
00:02:22,750 --> 00:02:27,700
So how to convert from uppercase to lowercase, add 32.

34
00:02:28,630 --> 00:02:29,860
Let us give it a try.

35
00:02:30,310 --> 00:02:35,270
I would like to touch it with all these alphabets starting from index zero on watch.

36
00:02:36,520 --> 00:02:41,790
So these are the indices, right, from starting from index zero up to the end of a string.

37
00:02:41,800 --> 00:02:43,410
I will add all of them.

38
00:02:43,720 --> 00:02:44,850
I already do.

39
00:02:45,130 --> 00:02:46,540
Those are in uppercase.

40
00:02:46,610 --> 00:02:51,080
So already you should know what is the present case, then you can change the cases.

41
00:02:51,280 --> 00:02:55,480
So let me write on the procedure for converting the cases.

42
00:02:55,840 --> 00:02:57,970
I should scan through all these alphabets.

43
00:02:57,970 --> 00:03:01,210
So for that, I need a follow up so I will declare a variable.

44
00:03:01,220 --> 00:03:07,620
I so far I assign zero and then should stop when it is zero.

45
00:03:07,630 --> 00:03:16,780
I have given the name of a string as a so as long as e of is not equal to null character, I should

46
00:03:16,780 --> 00:03:24,730
increment a double what I should do every time, every time at each place here I should add 32 so that

47
00:03:24,730 --> 00:03:30,400
if I, I should add if I plus 32.

48
00:03:32,230 --> 00:03:39,580
So political insiders, it will become a lower case then after finishing this, I will bring the stream.

49
00:03:39,850 --> 00:03:40,540
So print.

50
00:03:42,270 --> 00:03:45,690
Versatile s I will string a.

51
00:03:46,910 --> 00:03:54,890
That said, I can get a display of case alphabets for a string welcome, we have already seen how to

52
00:03:54,890 --> 00:03:59,780
scan through a string or how to traverse a string using a for loop.

53
00:03:59,960 --> 00:04:05,240
We have already seen in the previous videos, so I'm scanning through and while going through all the

54
00:04:05,240 --> 00:04:08,510
characters, only thing I am doing is adding 32.

55
00:04:08,850 --> 00:04:10,460
So it is becoming lowercase.

56
00:04:11,450 --> 00:04:19,279
So similarly, if I subtract 32, it will become upper case if the string was already in lower.

57
00:04:20,519 --> 00:04:24,710
So, yes, you can change the case by adding or subtracting 32.

58
00:04:26,020 --> 00:04:32,020
Now, next thing, I'll show you how to toggle the cases, toggle the casement's, if it is lower case

59
00:04:32,020 --> 00:04:34,400
and change or uppercase if it is upper.

60
00:04:34,420 --> 00:04:36,040
I will change it lowercase.

61
00:04:37,030 --> 00:04:43,840
So let us look at how the cases of Alphabet's in insisting I have these Alphabeat, some are in the

62
00:04:43,840 --> 00:04:50,350
works for some are, and I put cases like NCAR in upper case, the rest of them are in lower cases.

63
00:04:50,620 --> 00:04:55,540
So whichever are in lower case, I will change them into application, uppercase to lowercase.

64
00:04:56,320 --> 00:05:02,700
So it is simple, if it is up, I have to hour to 32, if it is lower, I have to subtract 32.

65
00:05:03,160 --> 00:05:05,790
So for that I should scan for this whole string.

66
00:05:06,100 --> 00:05:09,130
So for scanning drawstring, I need to follow already.

67
00:05:09,130 --> 00:05:12,820
I have a follow up now and there's a string inside this for loop.

68
00:05:13,090 --> 00:05:14,800
I will modify the alphabets.

69
00:05:14,980 --> 00:05:19,340
So here what I have to do, check for this upper case.

70
00:05:19,410 --> 00:05:21,180
So first check if it is upper case.

71
00:05:21,460 --> 00:05:32,440
So I can condition here that if I is when it is upper case from sixty five to ninety if if I is greater

72
00:05:32,440 --> 00:05:36,670
than or equal to I can write sixty five and.

73
00:05:37,630 --> 00:05:47,830
If I is less than or equal to 90, so this means it is upper case for change it to Lorqess by adding

74
00:05:47,860 --> 00:05:50,160
32, I can change it to lower case.

75
00:05:50,170 --> 00:05:53,350
So for addition, I can use this symbol also.

76
00:05:53,750 --> 00:05:58,450
That is assignment, additional operation and I will add 32.

77
00:06:00,420 --> 00:06:03,420
If it is not upper case ls.

78
00:06:04,640 --> 00:06:05,030
Al.

79
00:06:06,470 --> 00:06:13,030
Check if it is a lower case, if it is lower case, then change it up by subtracting 32.

80
00:06:13,310 --> 00:06:20,000
So I should not simply dreidels, I should check the condition that if it is lower case so that it is

81
00:06:20,000 --> 00:06:25,900
lower case, if it is in between 97 to one day to do so great of equal to 97.

82
00:06:26,120 --> 00:06:30,580
So instead of writing 97, I can write alphabet also and single codes.

83
00:06:30,740 --> 00:06:33,860
So it is ninety seven and.

84
00:06:35,780 --> 00:06:39,800
If I is less than or equal to 120 to.

85
00:06:41,170 --> 00:06:51,640
If so, then I should change this by subtracting 32 years, this loop will toggle the cases if it is

86
00:06:51,640 --> 00:06:54,820
uppercase, lowercase, but don't change it again.

87
00:06:55,360 --> 00:06:59,470
If it is not uppercase, check if it is lowercase and change the case.

88
00:07:00,410 --> 00:07:06,010
Now, one more important thing, and this one is that if there are other symbols other than alphabets,

89
00:07:06,250 --> 00:07:07,700
they will be untouched.

90
00:07:07,720 --> 00:07:14,340
They will not be modified like symbols here instead of E, I have little symbol.

91
00:07:14,360 --> 00:07:14,810
Fine.

92
00:07:15,220 --> 00:07:15,880
That is fine.

93
00:07:16,120 --> 00:07:18,410
So it remains five will need will not be changed.

94
00:07:18,610 --> 00:07:20,050
So if it is.

95
00:07:21,010 --> 00:07:26,110
Five, then it remains five only because it doesn't come under this range of this range.

96
00:07:27,030 --> 00:07:32,490
At last, I should bring this straight, so I will simply bring them here, so you know very well how

97
00:07:32,490 --> 00:07:35,570
different I would just be if so, you know the syntax.

98
00:07:35,580 --> 00:07:38,490
So that's all I have shown you two things here.

99
00:07:38,490 --> 00:07:44,680
How to change uppercase to lowercase and toggling and lowercase uppercase you can write.

100
00:07:44,880 --> 00:07:48,780
So all these programs you have to write down on your machine.

101
00:07:49,120 --> 00:07:51,720
These are all simple programs simply inside the main function.

102
00:07:51,750 --> 00:07:53,650
You can try this program by yourself.

103
00:07:54,180 --> 00:07:56,550
So this is a practice program or a student challenge.

104
00:07:56,550 --> 00:07:57,910
You have to do it by yourself.

105
00:07:59,150 --> 00:08:03,110
So we will see more function for more operations on stream incoming video.

