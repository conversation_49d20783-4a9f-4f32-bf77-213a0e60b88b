1
00:00:00,480 --> 00:00:06,750
And this video we will see by Navy search functions, I will write a corrective version as well as recursive

2
00:00:06,750 --> 00:00:07,200
version.

3
00:00:09,440 --> 00:00:12,860
So first of all, let us write a gritty version.

4
00:00:16,250 --> 00:00:20,720
Binary search, it should take a parameter that is Uhry.

5
00:00:21,970 --> 00:00:24,350
And it's going to just access the elements.

6
00:00:24,370 --> 00:00:30,010
It's not going to modify them, so all the value is sufficient and the key that we want to search.

7
00:00:32,759 --> 00:00:39,720
Not for performing binary search, we need following variables low, mid and high.

8
00:00:42,070 --> 00:00:49,120
Loans should be initialized with zero and high should be initialized with the lender minus one, because

9
00:00:49,120 --> 00:00:53,470
the last element that's present at Len<PERSON>, so <PERSON><PERSON> minus one.

10
00:00:55,290 --> 00:00:59,860
So these are, initialise, Ivin, right, to return zero to avoid this error.

11
00:01:01,750 --> 00:01:07,090
Then the procedure is using a loop while law is less than or equal to high.

12
00:01:08,350 --> 00:01:08,960
Find out.

13
00:01:11,150 --> 00:01:14,140
Is low plus high, divided by two.

14
00:01:14,520 --> 00:01:18,060
Then check if the key is same as.

15
00:01:19,920 --> 00:01:25,880
Eight of murder, if the same, then we got the element and return its index, the index murder.

16
00:01:29,560 --> 00:01:30,250
Ancef.

17
00:01:32,860 --> 00:01:34,180
He is less than.

18
00:01:35,970 --> 00:01:42,900
If not, then we will modify high value to mid minus one.

19
00:01:45,220 --> 00:01:45,850
Ls.

20
00:01:47,240 --> 00:01:53,360
And so definitely it is on the right hand side, so will change low value to mid plus one.

21
00:01:59,090 --> 00:02:04,130
That's all in this loop, it will check for the element, if it is found, it will retain its index.

22
00:02:04,670 --> 00:02:09,590
Otherwise, if it is outside the loop, we should return minus one, saying that the key element is

23
00:02:09,590 --> 00:02:10,280
not found.

24
00:02:11,560 --> 00:02:16,690
So this is the same court, what we have discussed on Lightbown, few changes are like barometer and

25
00:02:16,690 --> 00:02:20,200
these low, middle and high, which I have declared them.

26
00:02:21,370 --> 00:02:26,680
Now let us call this function inside main function so I will directly show its results.

27
00:02:26,680 --> 00:02:30,480
So I will use printf percentile the slashing.

28
00:02:31,460 --> 00:02:35,510
And I will call binary search function by passing.

29
00:02:36,260 --> 00:02:38,450
And the key that I want to search is five.

30
00:02:40,770 --> 00:02:41,730
Let us run this.

31
00:02:44,740 --> 00:02:47,480
I got the next three, yes.

32
00:02:47,650 --> 00:02:50,110
See, this is zero and one.

33
00:02:51,110 --> 00:02:52,610
Do under the three.

34
00:02:54,880 --> 00:02:59,860
I will change the quality of research, I will give the value as a tool, let us run this.

35
00:03:01,350 --> 00:03:02,390
This index is Eedle.

36
00:03:02,520 --> 00:03:03,500
Yes, correct.

37
00:03:05,820 --> 00:03:12,900
I'll give the key element as then that is not there in the audit, so I should get index minus one.

38
00:03:13,320 --> 00:03:14,760
Yes, index is minus one.

39
00:03:14,770 --> 00:03:15,270
That is key.

40
00:03:15,270 --> 00:03:16,380
Element is not found.

41
00:03:18,210 --> 00:03:24,240
I'll give the element as 15, let us run the element is not there, so it should return index minus

42
00:03:24,240 --> 00:03:24,500
one.

43
00:03:24,510 --> 00:03:26,040
Yes, in Nexxus minus one.

44
00:03:28,590 --> 00:03:30,600
So that following this binary search.

45
00:03:33,080 --> 00:03:38,770
Let us try to recursive binary search function, which takes parameters low, high and the key value,

46
00:03:39,170 --> 00:03:47,560
but here I will also pass an utting, so let me write on the function as being search Arbain search.

47
00:03:48,080 --> 00:03:52,180
It takes three parameters, Faustus and Urry.

48
00:03:53,330 --> 00:03:53,990
LOL.

49
00:03:55,050 --> 00:03:59,400
High and the key value, these are the parameters will be passing.

50
00:04:00,350 --> 00:04:02,870
And here I will write on written minus one.

51
00:04:04,180 --> 00:04:05,260
To avoid the error.

52
00:04:06,510 --> 00:04:09,330
Now, inside the function, we also need a mega.

53
00:04:10,370 --> 00:04:16,550
Then I will vote on the procedure that we have seen, if law is less than or equal to Hyde, then only

54
00:04:16,550 --> 00:04:18,140
we will check for the key.

55
00:04:19,290 --> 00:04:25,830
And the method for checking the keys and the method for checking Aggie's first find out that is as low

56
00:04:25,860 --> 00:04:28,620
plus high divided by two.

57
00:04:30,790 --> 00:04:34,420
And check if G is equal to.

58
00:04:35,420 --> 00:04:38,710
Eight of murder, the murder location.

59
00:04:38,900 --> 00:04:41,350
If so, then return murder.

60
00:04:43,620 --> 00:04:47,500
Else, if key is less than.

61
00:04:51,090 --> 00:04:58,710
Ahmed, then we will check on the left hand side, so I will try to return our urban search.

62
00:04:59,930 --> 00:05:07,670
I will pass a law and the law as it is, instead of high, we will pass bill minus one and the key as

63
00:05:07,670 --> 00:05:08,210
it is.

64
00:05:09,990 --> 00:05:16,830
Else we will return now we have to check on the right hand side, so I will say Arbain, Serge.

65
00:05:19,740 --> 00:05:25,180
Right as it is, Lou, I will tell you two plus one, because we have to check on the right hand side

66
00:05:25,210 --> 00:05:27,610
high as it is under Foskey.

67
00:05:29,250 --> 00:05:32,010
This is the recursive function for binary search.

68
00:05:33,100 --> 00:05:40,060
Now, let us call this urban research from the main function, I will change this urban search.

69
00:05:45,190 --> 00:05:54,760
We have to pass Uhry so we are not a threat then law is initially Zettl definitely then hires it at

70
00:05:54,790 --> 00:05:59,020
arm's length and the key that I want to search is five.

71
00:06:01,240 --> 00:06:05,050
So I have said that it's sufficient parameters for this urban search.

72
00:06:05,080 --> 00:06:06,280
Let us run the program.

73
00:06:08,090 --> 00:06:09,740
Yes, we got the next three.

74
00:06:13,310 --> 00:06:16,100
I'll take the key people and run it.

75
00:06:18,330 --> 00:06:20,550
Yes, index zero is working.

76
00:06:22,740 --> 00:06:28,520
I'll tell you, the key to nine nine is not there, so I should get the index has minus one.

77
00:06:29,190 --> 00:06:30,720
Yes, it is minus one.

78
00:06:32,030 --> 00:06:32,900
So it's working.

79
00:06:34,880 --> 00:06:43,550
That's all that binary search functions I have shown you both iterate evolution and recursive versions

80
00:06:44,210 --> 00:06:50,930
and you can be with these programs and check how the low and high values are changing and how is is

81
00:06:50,930 --> 00:06:52,730
finding calibrating every time.

82
00:06:56,370 --> 00:06:57,590
So that's all in the studio.

