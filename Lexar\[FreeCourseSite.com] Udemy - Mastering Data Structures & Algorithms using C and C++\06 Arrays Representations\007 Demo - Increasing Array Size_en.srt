1
00:00:00,150 --> 00:00:06,190
And as we do, we will see how we can increase the size of an army when it is created inside him.

2
00:00:06,420 --> 00:00:08,350
So for that, I will take one point at.

3
00:00:09,370 --> 00:00:11,920
B as well as I will keep it at one point.

4
00:00:12,000 --> 00:00:17,850
Q First of all, using B, I will create a UTI of size of five.

5
00:00:18,760 --> 00:00:25,330
So already we have seen how to create an inside heap using B will create an array inside.

6
00:00:25,330 --> 00:00:27,970
Keep using Madlock function already.

7
00:00:27,970 --> 00:00:29,320
We have seen how to do that.

8
00:00:32,009 --> 00:00:35,190
Mellark and a size five.

9
00:00:36,940 --> 00:00:38,560
Size of indigent.

10
00:00:39,560 --> 00:00:42,080
So this will create an area of size five.

11
00:00:43,460 --> 00:00:50,540
In that period, I will fill up the values, I will insert odd numbers, we have zero, assign three,

12
00:00:50,720 --> 00:00:54,350
then B of one, assign five.

13
00:00:54,380 --> 00:00:56,000
Likewise, I will fill up all of them.

14
00:00:58,110 --> 00:01:03,600
No display that repeat so far that I will pick one variable I for running a follow.

15
00:01:05,099 --> 00:01:10,850
Here I will bring all the values of an RFP just to confirm the values are stored in an early.

16
00:01:11,880 --> 00:01:15,800
I less than five I plus plus now didn't have.

17
00:01:18,370 --> 00:01:20,830
What's interesting, the next line.

18
00:01:22,420 --> 00:01:27,640
I'll bring the values from B of eight, so they should bring all the values from zero to.

19
00:01:28,620 --> 00:01:30,570
For that is three to 11.

20
00:01:32,060 --> 00:01:33,260
Let us run the program.

21
00:01:35,920 --> 00:01:38,830
If they got the values from three to 11.

22
00:01:41,060 --> 00:01:48,080
Now, suppose this raises five is not sufficient and I want a larger area than simply, I cannot extend

23
00:01:48,080 --> 00:01:53,180
its size than I should it under the point of the rescue, with the help of another point, that I should

24
00:01:53,180 --> 00:01:55,450
create an array of larger size.

25
00:01:55,460 --> 00:01:58,640
So here I will create an area of larger size mellark.

26
00:01:59,300 --> 00:02:01,070
I will create an area of size 10.

27
00:02:01,460 --> 00:02:02,600
So size of.

28
00:02:04,300 --> 00:02:04,930
Indigent.

29
00:02:06,520 --> 00:02:11,470
So Kuvan, I look at memory and heap for an idea of size and.

30
00:02:12,480 --> 00:02:18,350
But the values are already there inside Iraq, those values, I want them to be transferred to Cuba,

31
00:02:18,930 --> 00:02:22,290
so using follow, I can do this for ISIS zero.

32
00:02:23,600 --> 00:02:26,480
I is less than five lifelessness.

33
00:02:28,570 --> 00:02:32,830
Then, in view of I, I should copy the values from B of I.

34
00:02:34,750 --> 00:02:38,440
The silk copy, all elements took, you know, this for loop, that is.

35
00:02:39,770 --> 00:02:46,400
Use for bending the elements from E, I will just change it to you, so let us see whether the elements

36
00:02:46,400 --> 00:02:47,720
are copied in Cuba or not.

37
00:02:47,840 --> 00:02:48,960
I will run the program.

38
00:02:49,580 --> 00:02:55,000
I should get all the elements that are three five seven nine 11 that are copied.

39
00:02:55,010 --> 00:02:57,230
And Q Also, those elements should be printed.

40
00:03:01,140 --> 00:03:03,000
If I'm getting those elements.

41
00:03:04,570 --> 00:03:10,930
Now, after copping at this line, I will say the assigned Q not pointing on cue.

42
00:03:13,690 --> 00:03:17,830
And I will make due as none that you are a.

43
00:03:19,350 --> 00:03:22,760
That two pointer is made and also accused not pointing anywhere.

44
00:03:23,220 --> 00:03:25,430
See, right now is not pointing anywhere.

45
00:03:25,440 --> 00:03:30,830
But if I try to bring the uhry that is pointed by CU, then see what happens.

46
00:03:34,010 --> 00:03:40,580
I got an error because CU is null pointer, so here inside the debugger, you can see that the queue

47
00:03:40,580 --> 00:03:41,890
is null right now.

48
00:03:43,280 --> 00:03:45,470
And he's pointing on some other location.

49
00:03:46,520 --> 00:03:48,200
So I cannot use Kunal.

50
00:03:55,450 --> 00:03:57,510
Now, let us bring the values using the.

51
00:04:02,440 --> 00:04:09,640
Yes, the values are printed, but one thing we have missed that that the before is pointing on cue,

52
00:04:09,850 --> 00:04:11,700
we should delete that previous study.

53
00:04:12,040 --> 00:04:14,380
So for deleting free E!

54
00:04:20,910 --> 00:04:26,430
So after freeing that memory, we should point on dude, and you should be mad as hell, then they can

55
00:04:26,430 --> 00:04:31,740
access the array of size and with the help of dewpoint and so go, I don't have extra values.

56
00:04:31,740 --> 00:04:33,300
I have only five gallons of stored.

57
00:04:34,530 --> 00:04:38,190
In the new, they also start displaying only five elements.

58
00:04:39,420 --> 00:04:45,300
Let us put a big point here in the beginning of the program and debate this program, so let us trace

59
00:04:45,300 --> 00:04:48,030
it line by line and see how things are working.

60
00:04:51,320 --> 00:04:58,670
AP is pointer pointing to address that is last for three digits, if you see if you look into the city

61
00:04:58,670 --> 00:05:04,850
block area that this watch pointer P is having address ending with four F eight.

62
00:05:04,880 --> 00:05:12,360
So it is pointing out this address right now, pointer is garbage, is not having any address validators

63
00:05:12,410 --> 00:05:16,130
because this is a statement of greeting of memory from here.

64
00:05:16,220 --> 00:05:17,330
It's not yet executed.

65
00:05:18,260 --> 00:05:19,730
Let us continue execution.

66
00:05:24,190 --> 00:05:29,260
Now, you can see that pointer is pointing at an address that is ending nine to be zero.

67
00:05:33,230 --> 00:05:33,980
I'll continue.

68
00:05:35,340 --> 00:05:39,780
Now, Cuba is not right, no Jews not excuse not pointing anywhere.

69
00:05:41,410 --> 00:05:45,940
Continue, Not You is pointing out an address ending with four seven zero.

70
00:05:48,300 --> 00:05:54,450
And the value present at the first address where he was pointing is zero and the piece pointing out

71
00:05:54,450 --> 00:05:58,620
an address where the value is a three because we have initialized this value three.

72
00:06:00,040 --> 00:06:06,280
Q is just not created using this statement, so Q is having new, but there are no values.

73
00:06:07,120 --> 00:06:08,380
No, let us run this loop.

74
00:06:09,990 --> 00:06:11,400
The values are being copied.

75
00:06:14,000 --> 00:06:19,370
Now, you can see that he was also having the first value that the three and the address of you, you

76
00:06:19,370 --> 00:06:25,770
remember that is four seven zero eight four seven zero and the P is nine zero.

77
00:06:26,480 --> 00:06:28,580
Now, it is at this line that is free.

78
00:06:29,240 --> 00:06:32,120
Let us finish this line, so please go on.

79
00:06:32,600 --> 00:06:34,550
The address is the same in the.

80
00:06:34,820 --> 00:06:38,240
But that address is invalid because the memory is already allocated.

81
00:06:39,610 --> 00:06:41,980
Now, next line, this is a very important line.

82
00:06:44,180 --> 00:06:49,770
Ursine Q So the address of Q will be given BP, that is 470 will be given to Pete.

83
00:06:52,130 --> 00:07:00,050
Yes, he is having he is having a rest for 70 and also having at us 470.

84
00:07:01,320 --> 00:07:02,270
Our next line.

85
00:07:03,610 --> 00:07:08,950
Do becomes null and be the still binding on the same letter soapies binding on a new one.

86
00:07:09,790 --> 00:07:15,150
So when I bring these values, they are printed, they are printed using pointer.

87
00:07:16,900 --> 00:07:19,240
So that's all I have demonstrated.

88
00:07:19,240 --> 00:07:26,320
How precise can be Ingres I have written the code and I have shown, you see, I have done woodwork

89
00:07:26,320 --> 00:07:27,510
and that same board.

90
00:07:27,530 --> 00:07:30,310
Look, I have given a demonstration on that one.

91
00:07:31,730 --> 00:07:36,680
So whatever we saw on white board, that same thing I have demonstrated and I have shown you.

92
00:07:38,060 --> 00:07:42,480
Now, wherever you require, you have to follow these steps for increasing the size of.

93
00:07:45,110 --> 00:07:46,150
That's all in the studio.

