1
00:00:00,360 --> 00:00:08,430
And if you look at a program or algorithm for Delta Force, such as we have already seen that at that

2
00:00:08,430 --> 00:00:10,440
first search users a stock.

3
00:00:11,900 --> 00:00:19,190
So if we write a recursive function, it automatically uses a stack, so for writing the first search,

4
00:00:19,190 --> 00:00:23,180
all of you have taken our data structure for this graph.

5
00:00:23,660 --> 00:00:26,690
This graph is represented as a different semantics.

6
00:00:27,610 --> 00:00:28,750
See, there are seven murders.

7
00:00:28,800 --> 00:00:34,160
So I have taken eight in the office, so eight by eight to demonstrate.

8
00:00:34,540 --> 00:00:36,540
What are the zero three one zero column?

9
00:00:36,560 --> 00:00:37,530
I'm not using it.

10
00:00:37,840 --> 00:00:40,510
And then this is a restricted area.

11
00:00:42,070 --> 00:00:44,940
All are zero since no one was attacked.

12
00:00:46,010 --> 00:00:47,270
Then what is the procedure?

13
00:00:47,660 --> 00:00:54,260
This is the first search, suppose a vortex that uses the past as of starting point, starting with

14
00:00:54,290 --> 00:00:55,820
X, can be anything that I say.

15
00:00:55,830 --> 00:01:01,010
One, if the starting with X is one, then what I have to do.

16
00:01:03,090 --> 00:01:09,420
First of all, check whether that vortex has visited or not, a resident of that vortex is equal to

17
00:01:09,420 --> 00:01:13,050
zero, means it is not visited, then visited.

18
00:01:13,140 --> 00:01:16,050
So before visiting printed that vertex.

19
00:01:17,400 --> 00:01:25,800
Print version, Tildy, what you print this one, this is printed the market as we see assume that this

20
00:01:25,800 --> 00:01:27,690
is one market that's visited.

21
00:01:33,890 --> 00:01:44,140
Visited of you is one, so Mark, the first one visited, then start exploding at recursively.

22
00:01:44,480 --> 00:01:46,310
So for that I need a for loop.

23
00:01:46,490 --> 00:01:47,540
So for.

24
00:01:48,630 --> 00:01:58,080
We assign one V is left then only to end as the number of witnesses, V plus plus, then this up will

25
00:01:58,080 --> 00:02:03,780
go through all these elements and this Futral and every element we should check whether it is zero or

26
00:02:03,780 --> 00:02:06,000
one, if it is, it's alleged.

27
00:02:06,210 --> 00:02:10,320
So here I, I don't know if it is an edge in a vortex.

28
00:02:11,320 --> 00:02:15,550
And the Tulear that is Matrixx at location, you Conemaugh V.

29
00:02:17,220 --> 00:02:22,140
If it is equal to one means there's an edge like you two.

30
00:02:23,400 --> 00:02:31,860
We let us say the two, so there's an edge, so perform depth first search on to perform that first

31
00:02:31,860 --> 00:02:33,240
search on two.

32
00:02:34,500 --> 00:02:44,400
That is we that's on and off end of this one, so this will call itself again on Vortex two, not a

33
00:02:44,400 --> 00:02:52,890
silver two and two is not visited, not visited PRENDA two and Margaret visited Margarita's was again,

34
00:02:53,400 --> 00:02:55,920
then scanned through all the different words.

35
00:02:56,010 --> 00:02:56,400
Software, software.

36
00:02:56,640 --> 00:02:56,910
True.

37
00:02:57,270 --> 00:02:58,980
So now this is you.

38
00:03:00,400 --> 00:03:04,000
This is you because you is thrown out, so it will scan for this one.

39
00:03:05,190 --> 00:03:07,340
Just one week before that, he visited.

40
00:03:08,350 --> 00:03:11,530
So nothing will happen next zero.

41
00:03:11,710 --> 00:03:18,840
It's not one, so it will not call upon this country, it will cost, so it will call upon three again.

42
00:03:19,450 --> 00:03:22,180
So this heart will continue to avoid the call.

43
00:03:22,180 --> 00:03:23,340
If it is already visited.

44
00:03:23,350 --> 00:03:28,030
I can add one more condition that and visited of.

45
00:03:29,960 --> 00:03:31,850
V is.

46
00:03:32,810 --> 00:03:39,410
Not equal to zero, then only God, if it is already visited, it will not call the function anyway.

47
00:03:39,410 --> 00:03:45,940
If it calls also this condition will stop and that whole stack is used and all.

48
00:03:45,950 --> 00:03:48,770
I will show you then how recursion work.

49
00:03:48,770 --> 00:03:52,810
You know very well you can trace this and check up on any small graph.

50
00:03:53,270 --> 00:03:55,240
Those are sufficient small graph.

51
00:03:55,400 --> 00:03:57,080
It's not too large or too small.

52
00:03:57,500 --> 00:04:03,200
So up on this graph, you can try this one and and see how it is calling itself.

53
00:04:04,010 --> 00:04:07,520
That's all about a program on that first search.

54
00:04:07,910 --> 00:04:10,250
We will see the demonstration for this one.

