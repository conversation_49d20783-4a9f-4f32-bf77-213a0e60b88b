1
00:00:00,390 --> 00:00:08,250
Now, let us look at a second procedure for finding duplicates in a sorted already I have a solidary

2
00:00:08,430 --> 00:00:12,610
and there are some duplicate elements, like eight is duplicated and 15 is duplicated.

3
00:00:13,020 --> 00:00:15,510
We want to know how many duplicates are there.

4
00:00:15,510 --> 00:00:22,530
And also we want to know count, like if eight is duplicated, how many times it is duplicated, if

5
00:00:22,530 --> 00:00:25,340
15 is duplicated and how many times it's duplicated.

6
00:00:25,830 --> 00:00:29,570
Now, for this second method, I'm using hash table.

7
00:00:29,910 --> 00:00:31,880
So does the simple form of hash table.

8
00:00:31,980 --> 00:00:32,299
Right.

9
00:00:32,640 --> 00:00:34,190
Though we don't know what is harshing.

10
00:00:34,200 --> 00:00:39,260
We have it in the last section, but just we are using a simple form of it.

11
00:00:39,660 --> 00:00:41,760
So this is a uhry actually.

12
00:00:41,970 --> 00:00:43,770
And the array size is 20.

13
00:00:44,070 --> 00:00:48,500
Why I have taken a this 20 because the largest element in this area is a 20.

14
00:00:48,810 --> 00:00:51,550
So just you can go at the end of a list because it's so sorted.

15
00:00:51,780 --> 00:00:54,410
So the last element gives you the side of the say.

16
00:00:54,570 --> 00:00:57,010
We should have an array equal to that size.

17
00:00:57,030 --> 00:01:00,920
So if it is 20, I must have index to remember this.

18
00:01:01,170 --> 00:01:03,570
So I should get an idea of size twenty one.

19
00:01:03,930 --> 00:01:04,260
Right.

20
00:01:04,530 --> 00:01:07,920
Then I get the starting index has zero and the last index has twenty.

21
00:01:09,130 --> 00:01:14,620
The next list should be filled with Zeitels, yes, then what is the procedure?

22
00:01:14,800 --> 00:01:16,080
Let us look at the procedure.

23
00:01:16,690 --> 00:01:19,890
So let us scan through this early by taking one element at a time.

24
00:01:19,900 --> 00:01:22,390
So for that, I will take our index pointer.

25
00:01:22,390 --> 00:01:26,830
That is, I'm starting from here, not while scanning check.

26
00:01:26,830 --> 00:01:28,420
The element element is three.

27
00:01:28,750 --> 00:01:34,270
So here, go to index three and mark it as one incremented right.

28
00:01:34,600 --> 00:01:36,310
Then move it to the next element.

29
00:01:36,310 --> 00:01:42,940
Go to index six and that's one C going to the next six Ann-Margaret as one takes just Konstantine I

30
00:01:42,940 --> 00:01:46,000
have to say each of six plus plus that's it.

31
00:01:46,540 --> 00:01:53,470
A simple then go to the next element eight Margarita's one increment that right.

32
00:01:53,710 --> 00:01:55,220
The next increment.

33
00:01:55,510 --> 00:01:58,600
So actually this one 04 I have -- one again.

34
00:01:58,600 --> 00:02:08,830
Edmans make it as two incremented then 10 incremented this one then to an increment this one 15 increment.

35
00:02:09,130 --> 00:02:10,240
So again 15.

36
00:02:10,600 --> 00:02:12,610
So meagerness two again 15.

37
00:02:12,610 --> 00:02:13,600
So Megadeath three.

38
00:02:13,840 --> 00:02:16,120
So I finally had all three, then 20.

39
00:02:16,510 --> 00:02:18,430
Meagerness one incremented.

40
00:02:19,840 --> 00:02:26,070
So to scan through this area of an element once and Martys or increment the count.

41
00:02:26,680 --> 00:02:26,930
Right.

42
00:02:27,310 --> 00:02:32,410
So now in the hash table you can find that which element is appearing for how many times.

43
00:02:32,620 --> 00:02:34,150
So here we got the results.

44
00:02:35,280 --> 00:02:41,370
Now, before showing the court, let us do some analysis how much work we are doing, we are doing nothing

45
00:02:41,370 --> 00:02:42,240
but scanning today.

46
00:02:42,240 --> 00:02:44,370
Sorry for how much and.

47
00:02:45,650 --> 00:02:48,570
And right then, how many times?

48
00:02:48,590 --> 00:02:49,920
Only one time she is coming.

49
00:02:50,310 --> 00:02:51,960
Then where is the result?

50
00:02:52,490 --> 00:02:56,870
And while scanning, we are marking this that is incrementing the counts.

51
00:02:57,350 --> 00:02:58,640
How much time this is ticking?

52
00:02:59,120 --> 00:03:00,020
Like four, ten.

53
00:03:00,020 --> 00:03:05,450
I directly meant to index a ten and have incremented like four fifty nine directly in the index 15 and

54
00:03:05,450 --> 00:03:06,050
incremented.

55
00:03:06,470 --> 00:03:07,880
So incrementing the stick.

56
00:03:08,210 --> 00:03:10,400
Constanta time Konstantine.

57
00:03:10,700 --> 00:03:17,810
So what is the major time we have spent and accessing this the next what we have done we have to find

58
00:03:17,810 --> 00:03:22,220
out what are the duplicate elements from this hash table so we can scan for this one.

59
00:03:22,250 --> 00:03:23,290
This is not the not there.

60
00:03:23,310 --> 00:03:24,070
This is one time.

61
00:03:24,500 --> 00:03:25,310
Okay, one time.

62
00:03:25,550 --> 00:03:26,570
This is two times.

63
00:03:26,720 --> 00:03:28,060
Then this is three times.

64
00:03:28,190 --> 00:03:32,330
So I have to scan through this area also how much time it takes.

65
00:03:33,660 --> 00:03:34,380
Plus an.

66
00:03:35,830 --> 00:03:39,010
How it is and see that are only 10 elements.

67
00:03:39,040 --> 00:03:47,290
These are 21 elements, so 10 means here not count when we and is the size of the input and insight

68
00:03:47,290 --> 00:03:47,840
of the input.

69
00:03:47,940 --> 00:03:48,790
All right.

70
00:03:49,090 --> 00:03:55,000
So in this procedure and is the size OK, who size the size of this size, whatever it may be, we are

71
00:03:55,000 --> 00:03:57,970
not bothered about exact figures here.

72
00:03:58,000 --> 00:03:59,020
Now listen carefully.

73
00:03:59,470 --> 00:04:01,420
And the means línea.

74
00:04:02,110 --> 00:04:02,620
Yes.

75
00:04:03,010 --> 00:04:05,470
Then what about this linear only.

76
00:04:05,500 --> 00:04:09,460
So what I should say and only if you say no, no, this is larger.

77
00:04:09,680 --> 00:04:13,510
OK, you take it as two lines off and no problem.

78
00:04:13,840 --> 00:04:14,230
No, no.

79
00:04:14,230 --> 00:04:15,190
Still larger.

80
00:04:15,220 --> 00:04:17,380
So whatever you want to write on K of a.

81
00:04:18,579 --> 00:04:26,350
So this is all for Línea that if I take it as only and then I say to and so what is true in the end

82
00:04:26,350 --> 00:04:28,120
is also Línea right.

83
00:04:28,420 --> 00:04:31,000
So finally, it is outdraw.

84
00:04:31,000 --> 00:04:35,310
And yes, it takes a lot of time.

85
00:04:36,430 --> 00:04:39,550
So does the analysis novel right.

86
00:04:39,580 --> 00:04:44,380
On the procedure and show you how we can make these elements or count them.

87
00:04:44,380 --> 00:04:45,820
And also they split the isn't.

88
00:04:46,150 --> 00:04:48,700
So the procedure is I have to scan for this Uhry.

89
00:04:48,940 --> 00:04:54,630
So for that I will take a look for Irfan's zero.

90
00:04:54,850 --> 00:05:00,450
I should go and suppos number of elements and then I plus.

91
00:05:00,460 --> 00:05:00,880
Plus.

92
00:05:02,590 --> 00:05:11,680
Then for each element like five, four, three to four aof, I like to see three, so I should go to

93
00:05:11,690 --> 00:05:12,550
third index.

94
00:05:12,670 --> 00:05:15,010
So that is each of.

95
00:05:15,830 --> 00:05:23,390
Ye of time, yes, Alfi three, so that at three I should find the indexing, right, so each of three

96
00:05:23,690 --> 00:05:29,750
sets of three plus plus the full increment so that for this whole I can mark them or increment them

97
00:05:30,320 --> 00:05:30,670
over.

98
00:05:30,890 --> 00:05:38,930
Now the next step is go through this hash table and wherever we have count more than one print element.

99
00:05:39,200 --> 00:05:47,130
So here for Loop once again, for again, I think I said this is not inside the fault of this independent

100
00:05:47,130 --> 00:05:47,710
fault, right?

101
00:05:48,080 --> 00:05:52,850
For assigned zero, I it's less than whatever the maximum number is.

102
00:05:52,850 --> 00:05:58,520
So let's denote I is less than or equal to whatever the maximum number is like.

103
00:05:58,520 --> 00:06:00,440
Here we have maximum is 20.

104
00:06:00,470 --> 00:06:06,500
So between D and I plus plus what we should do every time.

105
00:06:06,980 --> 00:06:10,550
If that value here the value is greater than one.

106
00:06:10,940 --> 00:06:16,400
If each of i.e. is greater than one then we will print.

107
00:06:16,460 --> 00:06:19,610
So in the same line I will write on printf here.

108
00:06:19,610 --> 00:06:22,250
Printf percentile depersonalised.

109
00:06:22,610 --> 00:06:26,240
I give the number and I shall fight.

110
00:06:26,250 --> 00:06:29,000
Ashrafi gives the count how many times the disappearing.

111
00:06:30,210 --> 00:06:30,820
That's it.

112
00:06:31,980 --> 00:06:38,130
It's a simple procedure, let us do the analysis, see this for Loop is repeating for some number of

113
00:06:38,130 --> 00:06:38,540
times.

114
00:06:38,540 --> 00:06:41,830
So it is end and this is independent for loop.

115
00:06:41,850 --> 00:06:43,020
It's not a part of that one.

116
00:06:43,050 --> 00:06:48,500
See, this was not just an independent follow and it's also repeating for some number of times.

117
00:06:48,510 --> 00:06:48,890
So it is.

118
00:06:48,900 --> 00:06:54,830
And so when you have a single follow up, it is end if you have followed inside to follow this and square.

119
00:06:55,260 --> 00:06:56,280
So this is endless.

120
00:06:56,280 --> 00:07:03,230
And so this is to end and time is order of any right or be off.

121
00:07:03,240 --> 00:07:07,640
And so the time order of an hour, big Wolf.

122
00:07:07,650 --> 00:07:11,000
And so we have done the analysis from the court also.

123
00:07:11,490 --> 00:07:16,430
So that's all finding duplicates and counting how many times it's duplicated.

124
00:07:17,040 --> 00:07:18,480
You can write on this procedure.

125
00:07:18,750 --> 00:07:24,000
You can write a simple program, take an array and one more ready for hash table and write on this code

126
00:07:24,000 --> 00:07:24,660
and see.

127
00:07:24,980 --> 00:07:25,150
Right.

128
00:07:25,620 --> 00:07:27,090
So it's an exercise.

129
00:07:28,420 --> 00:07:28,980
That's it.

