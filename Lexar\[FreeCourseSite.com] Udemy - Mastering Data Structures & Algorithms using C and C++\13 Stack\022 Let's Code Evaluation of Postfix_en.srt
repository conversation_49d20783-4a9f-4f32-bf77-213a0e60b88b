1
00:00:00,480 --> 00:00:04,150
This video will look at a function for evaluation of postfix expression.

2
00:00:04,950 --> 00:00:07,170
This is the same program that is programmed for.

3
00:00:08,150 --> 00:00:14,540
Stop using <PERSON><PERSON><PERSON><PERSON>, I have used it for parenthesis matching and also I have used it for INFIX to

4
00:00:14,540 --> 00:00:15,570
post conversion.

5
00:00:16,309 --> 00:00:20,420
Now I'll be using it for validation of postfix and an evaluation.

6
00:00:20,420 --> 00:00:22,550
I need one function that is is operands.

7
00:00:22,550 --> 00:00:25,640
Already I have dysfunction which will return true if it is an option.

8
00:00:26,180 --> 00:00:31,660
No, I have to make some changes inside the school because I'm going to use integer type stacks.

9
00:00:31,680 --> 00:00:38,750
I should change this note type as integer and a push function should get an integer then POF function

10
00:00:38,750 --> 00:00:46,640
should also return a type integer and here is also an integer, so I have to use integer type stack.

11
00:00:46,790 --> 00:00:48,560
So I have made some changes.

12
00:00:50,250 --> 00:00:50,740
That's it.

13
00:00:50,760 --> 00:00:55,350
Now, let us go to the main function and our eval function also.

14
00:00:55,530 --> 00:01:01,350
So inside the main function, first of all, I will declare postfix expression.

15
00:01:03,430 --> 00:01:09,910
Let us take a postfix expression as two, three, four star plus.

16
00:01:11,270 --> 00:01:17,180
This will be for the industry for 12 to 14 and eight to.

17
00:01:18,260 --> 00:01:19,610
Slash minus.

18
00:01:19,640 --> 00:01:24,040
So this will be four minus 14, minus four should be.

19
00:01:24,800 --> 00:01:26,540
So I should get the result as a 10.

20
00:01:26,870 --> 00:01:32,450
So now for this postfix expression here, I will write on a function for evaluation of Postfix.

21
00:01:32,840 --> 00:01:34,840
Let us call the function Ima's eval.

22
00:01:35,210 --> 00:01:38,270
Then it takes a pointer to oppose fixed expression.

23
00:01:38,840 --> 00:01:46,280
And inside this, I need a few variables like I for tracking through this postfix expression.

24
00:01:46,700 --> 00:01:53,810
And also I need some variables like X1 and Extra and for storing the result, not using a follow.

25
00:01:53,810 --> 00:01:55,430
We can scan through this expression.

26
00:01:55,430 --> 00:02:03,170
So I assign zero as long as postfix of AI is not equal to slash acetyl.

27
00:02:05,270 --> 00:02:06,280
It should continue.

28
00:02:08,360 --> 00:02:10,280
And every time I blast plus.

29
00:02:11,920 --> 00:02:17,050
Now, every time we should check whether it's an operation so I should say is operand.

30
00:02:19,770 --> 00:02:23,070
Bypassing postfix symbol I.

31
00:02:24,750 --> 00:02:26,370
If it is an option, then I should.

32
00:02:27,740 --> 00:02:32,270
Push it into the stack, push that postfix symbol I.

33
00:02:36,000 --> 00:02:41,890
Else, if it is not an option, then it's an operator, then for operator, I should be able to operate

34
00:02:41,890 --> 00:02:48,640
from the stacks of first one as X2, then second one is X1.

35
00:02:50,600 --> 00:02:54,560
Then, depending on the operator, I should perform the operation, so.

36
00:02:56,210 --> 00:02:59,480
Such case I can use upon postfix of.

37
00:03:00,840 --> 00:03:02,130
I simbel.

38
00:03:04,130 --> 00:03:11,180
Now, inside the case, first case, if the symbolist plus operator, then a result should be X one

39
00:03:11,180 --> 00:03:15,440
plus X to then give break.

40
00:03:16,910 --> 00:03:23,030
Then see, maybe I should write for all, so I will copy this one and I will paste it for all other

41
00:03:23,030 --> 00:03:23,780
operations.

42
00:03:29,990 --> 00:03:37,370
For all four operations, I have written this plus Denis's minus, and this is multiplication and division.

43
00:03:38,740 --> 00:03:41,200
Now, here, operations also I should change.

44
00:03:41,960 --> 00:03:45,730
This is multiply and next one is divide.

45
00:03:46,740 --> 00:03:52,530
Then once they get the result, I should push that result into the stack, so push ah, that is rather

46
00:03:52,530 --> 00:03:53,760
just pushed into the stack.

47
00:03:54,790 --> 00:03:59,530
Then at the end of the for loop, I should return the topmost element from the stack, so say.

48
00:04:00,840 --> 00:04:01,650
Retcon.

49
00:04:02,630 --> 00:04:09,470
Topmost element of the stack of data, that's one and one more thing, as I have explained you, that

50
00:04:09,470 --> 00:04:15,700
from the 06 exhibition, we will be getting characters we should convert into an integer.

51
00:04:15,710 --> 00:04:19,589
So I should subtract ASCII code of zero or I can subtract 48 also.

52
00:04:20,029 --> 00:04:23,330
So this will subtract ASCII code of zero.

53
00:04:23,420 --> 00:04:26,510
So zero is subtracted and it is pushed that fall.

54
00:04:26,830 --> 00:04:28,870
So is the function which we have already seen it.

55
00:04:29,270 --> 00:04:32,870
Now here I will directly print the result written by that function.

56
00:04:32,870 --> 00:04:43,790
Percentile result is a person Tildy and then slash and also so I should call the function that is eval

57
00:04:43,790 --> 00:04:45,770
by sending postfix expression.

58
00:04:50,260 --> 00:04:52,470
That's all there is, run this.

59
00:04:53,340 --> 00:04:58,930
That's all we got a result that is done, as I told you, the result of this experience and we got that

60
00:04:59,260 --> 00:05:03,850
and you can try with the different values and the different expression and you can check your answers,

61
00:05:04,660 --> 00:05:06,510
implement this program by yourself.

62
00:05:07,390 --> 00:05:08,470
That's all in this video.

