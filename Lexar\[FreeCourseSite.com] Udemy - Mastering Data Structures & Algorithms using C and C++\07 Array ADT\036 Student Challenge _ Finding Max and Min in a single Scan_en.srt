1
00:00:00,060 --> 00:00:06,420
Now, let us look at a procedure for finding maximum, as well as a minimum element from a given list

2
00:00:06,420 --> 00:00:13,230
of elements, we have already seen how to find the maximum element and even we can vary the procedure.

3
00:00:13,260 --> 00:00:15,740
Similarly, we can order the procedure for finding minimum.

4
00:00:16,079 --> 00:00:20,880
But the question here is we want to do it simultaneously in a single scan.

5
00:00:21,940 --> 00:00:24,160
So let us see how it is possible.

6
00:00:24,610 --> 00:00:31,000
I have a given area here with some elements and the maximum element here is a 10 and the minimum element

7
00:00:31,120 --> 00:00:31,720
minus one.

8
00:00:31,730 --> 00:00:34,750
So that's what I want now, how to find Max.

9
00:00:35,140 --> 00:00:37,960
So I have taken minimum and maximum two variables.

10
00:00:38,290 --> 00:00:39,670
So let us start the procedure.

11
00:00:39,670 --> 00:00:42,010
The very first thing what I do is the first element.

12
00:00:42,010 --> 00:00:48,320
Five, I will assign a keyboard for both minimum and maximum of five now to scan the hole.

13
00:00:48,640 --> 00:00:51,220
So for scanning, I will take a starting from here.

14
00:00:51,760 --> 00:00:54,520
Not every time I will check what I will check.

15
00:00:55,510 --> 00:00:58,880
Is it smaller than eight is not less than five.

16
00:00:58,990 --> 00:01:00,550
OK then check.

17
00:01:00,550 --> 00:01:01,790
Is it greater than Max?

18
00:01:01,810 --> 00:01:03,130
Yes, it is greater than Max.

19
00:01:03,130 --> 00:01:08,350
Eight is greater than Max or modify max to eight then go to the next element.

20
00:01:08,560 --> 00:01:10,120
Now watch this carefully.

21
00:01:11,080 --> 00:01:11,950
Check this element.

22
00:01:11,950 --> 00:01:13,110
Is it less than men?

23
00:01:13,150 --> 00:01:14,440
Yes, it is less than men.

24
00:01:14,440 --> 00:01:20,090
So modify this now when it is less than men, you don't have to check it with the max because the number

25
00:01:20,090 --> 00:01:21,430
of it is smaller than minimum.

26
00:01:21,700 --> 00:01:23,080
Cannot be greater than Max.

27
00:01:23,350 --> 00:01:25,810
So you don't have to check with Max.

28
00:01:25,810 --> 00:01:26,120
Right.

29
00:01:26,440 --> 00:01:28,420
So you can save one condition like this.

30
00:01:28,780 --> 00:01:33,690
That next element, is it smaller than no.

31
00:01:33,700 --> 00:01:34,350
Then check.

32
00:01:34,360 --> 00:01:35,380
Is it greater than Max?

33
00:01:35,380 --> 00:01:37,510
Yes, it is going to the max on it.

34
00:01:37,510 --> 00:01:43,510
If this one makes it smaller than no check, is it greater to the max?

35
00:01:43,510 --> 00:01:44,940
No, that is six.

36
00:01:44,950 --> 00:01:50,680
It is neither minimum nor maximum right next to the max element to it is smaller than.

37
00:01:50,950 --> 00:01:51,460
Yes.

38
00:01:51,460 --> 00:01:52,240
Modified.

39
00:01:53,370 --> 00:01:59,670
The next element, then, is it smaller than men, no, then check is it greater than Max of ten is

40
00:01:59,670 --> 00:02:00,450
greater than nine.

41
00:02:00,480 --> 00:02:01,770
Yes, modified.

42
00:02:02,720 --> 00:02:09,180
Then seven, is it less than men, no check here, that is it greater than ten?

43
00:02:09,289 --> 00:02:09,740
No.

44
00:02:10,310 --> 00:02:12,920
Then go to the next element as it smaller than men.

45
00:02:12,990 --> 00:02:14,150
Yes, minus one.

46
00:02:14,150 --> 00:02:18,650
Modify to minus one, then makes this one a little smaller than men.

47
00:02:18,980 --> 00:02:19,940
No check.

48
00:02:19,940 --> 00:02:21,080
Is it greater than Max?

49
00:02:21,090 --> 00:02:21,430
No.

50
00:02:21,770 --> 00:02:29,720
So I have four and so I have executed the procedure on the entire list and we found the minimum as well

51
00:02:29,720 --> 00:02:30,870
as maximum elements.

52
00:02:30,870 --> 00:02:36,440
So only the important thing that was to observe here is we can find it in a single scan.

53
00:02:36,800 --> 00:02:43,370
And if the number is not less than minimum, then we will check whether it is greater than maximum effort

54
00:02:43,370 --> 00:02:47,480
is less than minimum, then we don't check the maximum element.

55
00:02:47,480 --> 00:02:47,760
Right.

56
00:02:48,530 --> 00:02:53,510
So for this, I will write on a piece of code here that is a loop that is scanning through a list of

57
00:02:53,510 --> 00:03:00,560
elements and finding maximum and minimum so quickly, minimum as of zero.

58
00:03:00,860 --> 00:03:03,760
And the maximum is also E of a zero.

59
00:03:04,760 --> 00:03:11,160
Then I need to follow that for loop will start from index one onwards and the number of elements, as

60
00:03:11,420 --> 00:03:13,280
I have example of ten elements.

61
00:03:13,280 --> 00:03:16,890
So I is less than that is ten and I plus plus.

62
00:03:17,120 --> 00:03:25,670
So what I have to do every time check if element of I if it is less than minimum then modify mean that

63
00:03:25,970 --> 00:03:32,390
if I, if the element is not less else then check.

64
00:03:32,390 --> 00:03:40,430
If the element is greater than Max, if it is greater than Max then change Max.

65
00:03:42,980 --> 00:03:44,070
Two, if I

66
00:03:46,910 --> 00:03:48,270
follow the procedure.

67
00:03:49,900 --> 00:03:56,190
Procedure is simple now you can see that this is an Elsipogtog, if this is true, this is done, otherwise

68
00:03:56,200 --> 00:03:56,830
this was done.

69
00:03:58,330 --> 00:04:06,610
Now, analysis, analysis of this procedure, first thing, how much time it is ticking time depends

70
00:04:06,610 --> 00:04:07,450
on the work done.

71
00:04:07,480 --> 00:04:08,480
So what is the work done?

72
00:04:08,500 --> 00:04:11,800
We are scanning through the list of elements for how many elements are there?

73
00:04:12,040 --> 00:04:13,810
Some elements, other elements.

74
00:04:13,820 --> 00:04:15,690
So the time is linear?

75
00:04:15,730 --> 00:04:17,519
Yes, it is of end.

76
00:04:17,950 --> 00:04:20,269
So first of all, the time is outdraw and.

77
00:04:21,940 --> 00:04:28,090
Now, one more analysis, I will do it here, one more analysis, I want to know, Total, how many

78
00:04:28,090 --> 00:04:32,890
competitions are performed in total, how many competitions are performed?

79
00:04:32,890 --> 00:04:34,570
Right time we have done it.

80
00:04:34,900 --> 00:04:38,380
So what was actually done when we said the time is N?

81
00:04:38,650 --> 00:04:40,220
So in that time, what was done?

82
00:04:40,480 --> 00:04:42,650
The competition and the assignment was done.

83
00:04:42,670 --> 00:04:44,120
Competition and assignment.

84
00:04:44,140 --> 00:04:44,580
Right.

85
00:04:44,620 --> 00:04:49,750
OK, so now I want to find out total how many competitions are to see.

86
00:04:49,750 --> 00:04:51,360
Actually there are two competitions.

87
00:04:51,370 --> 00:04:54,850
If this is true then it will not go here.

88
00:04:55,510 --> 00:04:57,570
If it is false, then it will go here.

89
00:04:58,360 --> 00:05:00,370
So it means there are cases.

90
00:05:00,590 --> 00:05:02,440
Yes, there are cases.

91
00:05:02,680 --> 00:05:03,880
That is the minimum time.

92
00:05:04,390 --> 00:05:05,420
Maximum time.

93
00:05:05,680 --> 00:05:06,820
What is the minimum time?

94
00:05:07,100 --> 00:05:09,850
Assume that always it is checking only this condition.

95
00:05:09,850 --> 00:05:11,140
It is not coming to this one.

96
00:05:11,380 --> 00:05:12,540
Always this is true.

97
00:05:12,850 --> 00:05:16,710
So then it will happen if the list is in descending order.

98
00:05:16,720 --> 00:05:22,020
Like if you have the numbers like ten, nine, eight, seven, two and one.

99
00:05:22,210 --> 00:05:27,830
So if you have a list and the decreasing order, then every time this condition we do nine is a smaller

100
00:05:27,830 --> 00:05:31,120
right, it is a smaller some of the smaller always this will be true.

101
00:05:31,420 --> 00:05:37,010
If the list is in descending order then only this condition will be executed for how many times.

102
00:05:37,010 --> 00:05:42,550
So the commission will be executed as we are starting from two and stopping at and minus one, that

103
00:05:42,550 --> 00:05:42,690
is.

104
00:05:42,700 --> 00:05:45,190
And so except one element.

105
00:05:45,190 --> 00:05:47,860
So total number of competitions for this case.

106
00:05:48,100 --> 00:05:51,310
The number of competitions will be in the minus one.

107
00:05:51,430 --> 00:05:51,850
Right.

108
00:05:52,690 --> 00:05:53,470
One less.

109
00:05:54,470 --> 00:05:56,880
Then what about this one?

110
00:05:57,740 --> 00:06:03,670
See, if this is always the falls, then always it will enter into this one and check this condition.

111
00:06:03,680 --> 00:06:07,100
So in what situation it will happen that this is always fall.

112
00:06:07,110 --> 00:06:08,650
So let us take the list.

113
00:06:08,660 --> 00:06:13,240
It is one, two, three, five, eight, nine, 10, 12.

114
00:06:13,280 --> 00:06:18,400
And it's just like this, OK, if the list is going like this, that is an increasing order.

115
00:06:18,590 --> 00:06:20,910
So if I show I don't like this, this is decreasing.

116
00:06:21,260 --> 00:06:25,910
This is increasing order C, this is minimum and maximum rate, initially minimum and maximum.

117
00:06:26,450 --> 00:06:31,010
Every time I get an element, greater than minimum, greater than minimum credit and always it is greater

118
00:06:31,010 --> 00:06:34,150
than minimum, then always this condition will be checked.

119
00:06:34,400 --> 00:06:35,510
So how many conditions?

120
00:06:35,630 --> 00:06:37,610
Two conditions will be checked each time.

121
00:06:37,910 --> 00:06:42,440
So total number of competitions will be what to do.

122
00:06:42,440 --> 00:06:45,530
And minus one like this condition will check.

123
00:06:45,530 --> 00:06:47,590
It fails, then this condition is checked.

124
00:06:47,960 --> 00:06:51,160
So two conditions will be checked for and minus one time.

125
00:06:51,410 --> 00:06:57,350
So the minimum time is and at the maximum time is also out of hand.

126
00:06:57,590 --> 00:07:01,850
So in the best case, the number of competitions are and the minus one.

127
00:07:02,090 --> 00:07:06,550
And in the worst case that is maximum number of competitions are these.

128
00:07:06,920 --> 00:07:08,350
So I'll show you something here.

129
00:07:08,930 --> 00:07:11,840
We have found out the best case and worst case.

130
00:07:12,320 --> 00:07:21,680
See, best case is Lister's in descending order and the best case time as other often.

131
00:07:22,100 --> 00:07:26,120
And if you are specific about the number of competitions then and minus one.

132
00:07:27,260 --> 00:07:32,080
The second thing was case is different, time is different.

133
00:07:32,110 --> 00:07:37,990
I remember this case, the final case was a case in which case the time will be more the case is that

134
00:07:37,990 --> 00:07:40,610
the list is an increasing order.

135
00:07:40,720 --> 00:07:41,110
Right.

136
00:07:41,560 --> 00:07:42,640
This is the case.

137
00:07:42,790 --> 00:07:43,810
Then what is the time?

138
00:07:44,200 --> 00:07:46,510
It is to in two and minus one.

139
00:07:46,540 --> 00:07:48,640
So it is sort of this all sort of.

140
00:07:48,640 --> 00:07:52,420
And then we take the degree of disaster and this is all sort of end.

141
00:07:52,900 --> 00:07:55,500
But being specific, we want to know the competition.

142
00:07:55,510 --> 00:07:57,640
So that is two and minus one.

143
00:07:58,240 --> 00:07:59,660
These many competitions for the.

144
00:08:00,910 --> 00:08:06,960
All right, so time we were seeing a lot of yes, in baskets, also baskets, also sort of anomaly,

145
00:08:07,270 --> 00:08:09,680
but the number of competitions are different, right?

146
00:08:10,000 --> 00:08:13,480
So if you're concerned about this, then you can do the analysis.

147
00:08:13,480 --> 00:08:17,350
You can find how many competitions are, how much movement is done.

148
00:08:17,650 --> 00:08:18,980
So that depends on you.

149
00:08:19,360 --> 00:08:21,990
So this type of analysis can also be.

150
00:08:22,180 --> 00:08:24,580
So that's all the best case and worst case.

151
00:08:24,580 --> 00:08:30,550
I'm not sure the complete program, even I will not write on the code and I will not give you a demonstration

152
00:08:30,550 --> 00:08:31,060
on this one.

153
00:08:31,540 --> 00:08:37,870
This will have to do it by yourself as a student exercise declaring that and write on this code by yourself

154
00:08:37,870 --> 00:08:38,549
and check it.

155
00:08:39,340 --> 00:08:45,850
And one time you take this decreasing array at one time increasing array and debug it line by line and

156
00:08:45,850 --> 00:08:48,250
check how the competitions are done.

157
00:08:48,550 --> 00:08:51,510
But in this case, only this competition will be executed.

158
00:08:51,520 --> 00:08:53,890
It will not enter a second case.

159
00:08:54,190 --> 00:08:55,300
It will execute both.

160
00:08:55,570 --> 00:08:55,900
Right.

161
00:08:56,290 --> 00:08:59,620
So you can count them by yourself and you can see it by debugging it.

162
00:08:59,950 --> 00:09:01,180
So that's all in the studio.

