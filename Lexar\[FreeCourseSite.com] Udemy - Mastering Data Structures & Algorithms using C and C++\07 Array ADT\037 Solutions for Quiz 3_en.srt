1
00:00:00,680 --> 00:00:03,650
I'll give the solutions for his training.

2
00:00:05,040 --> 00:00:09,540
There are total five questions in custody, so let us see the solutions one by one.

3
00:00:10,110 --> 00:00:10,860
First question.

4
00:00:12,180 --> 00:00:18,660
There is an array and on that array of sites, one point four on that reverse operation is performed

5
00:00:18,660 --> 00:00:22,670
from one to K, then from K plus one, two, and then from one, two.

6
00:00:22,740 --> 00:00:24,230
And so what will happen to <PERSON><PERSON>?

7
00:00:25,470 --> 00:00:26,430
So let us see.

8
00:00:26,430 --> 00:00:28,290
I have an example right here.

9
00:00:28,590 --> 00:00:37,440
So I is from one to seven and seven and give something like a three K Street if reverse from one to

10
00:00:37,620 --> 00:00:39,560
case perform that is one, two, three.

11
00:00:39,570 --> 00:00:41,210
So it is two, three, four.

12
00:00:41,370 --> 00:00:42,180
So it will be.

13
00:00:44,390 --> 00:00:52,280
Two, three, four, so it reversed, then reversed from K plus one, two and is performed so K plus

14
00:00:52,280 --> 00:00:54,960
one, two and so five, six, seven, eight.

15
00:00:55,310 --> 00:00:56,660
So this will be.

16
00:00:58,340 --> 00:01:01,830
Five, six, seven, eight.

17
00:01:02,390 --> 00:01:08,450
So this portion is reversed and again, that portion is also reversed, the reverse from one, two and

18
00:01:09,140 --> 00:01:09,610
one, two.

19
00:01:09,640 --> 00:01:12,740
And so there's entirety from one point and is reversed.

20
00:01:12,920 --> 00:01:17,570
So it's four, three, two, eight, seven, six, five.

21
00:01:17,750 --> 00:01:18,220
Right.

22
00:01:18,290 --> 00:01:25,180
So I'll remove all this and write them four, three, two, eight, seven, six, five.

23
00:01:25,790 --> 00:01:26,770
This is that isn't.

24
00:01:27,680 --> 00:01:29,600
So what has happened to an array?

25
00:01:30,080 --> 00:01:37,130
If you observe this is an operation that is called as left rotate or right rotate.

26
00:01:37,580 --> 00:01:44,750
So left rotate means what they called five shift all the elements and insert the five at the back.

27
00:01:45,230 --> 00:01:47,870
So that is s left rotation.

28
00:01:49,260 --> 00:01:54,240
So if you are doing one time, then one element that is five will move at the back and all the elements

29
00:01:54,240 --> 00:01:54,940
will shift here.

30
00:01:55,800 --> 00:02:02,610
So if you observe supples, this four of us here, then if you left shift, all the elements will move

31
00:02:02,610 --> 00:02:04,330
here and four will go there.

32
00:02:04,560 --> 00:02:08,930
So, yes, four has gone there because of left shifting one time.

33
00:02:09,350 --> 00:02:11,790
Then what about three is also gone by.

34
00:02:11,790 --> 00:02:12,540
Left shifting.

35
00:02:12,780 --> 00:02:13,570
What about two.

36
00:02:13,590 --> 00:02:14,950
Two has also gone there.

37
00:02:14,970 --> 00:02:20,280
So two was there in the beginning of the train that initially noticed there was first two weeks and

38
00:02:20,430 --> 00:02:25,190
then three percent and four percent and all the time the elements were shifting here.

39
00:02:25,470 --> 00:02:32,420
So if you perform left, rotate four, three times this result.

40
00:02:33,060 --> 00:02:34,170
So what is three here?

41
00:02:34,330 --> 00:02:34,740
Key.

42
00:02:35,130 --> 00:02:37,590
So left rotate four key times.

43
00:02:37,590 --> 00:02:40,100
That is three times in our example it is three.

44
00:02:40,410 --> 00:02:46,080
So if an array is left rotated four, three times the silver, that isn't so.

45
00:02:46,290 --> 00:02:52,100
So once again, the question, Uninor, if these three operations are performed, then how the result

46
00:02:52,140 --> 00:02:59,820
looks like the result looks like as if it is left to rotate at four, three times or key times.

47
00:03:00,350 --> 00:03:01,740
So yes, that is the answer.

48
00:03:02,490 --> 00:03:04,180
Left rotated four times.

49
00:03:05,070 --> 00:03:06,750
Now, let us look at the next question.

50
00:03:07,630 --> 00:03:08,820
This is a simple one.

51
00:03:09,480 --> 00:03:13,550
I have five hundred elements I remember getting from the keyboard.

52
00:03:13,560 --> 00:03:19,140
Imagine I'll be getting the elements from the keyboard as input in my program, so I'll be getting five

53
00:03:19,140 --> 00:03:19,970
hundred elements.

54
00:03:21,330 --> 00:03:22,470
What are the elements?

55
00:03:22,470 --> 00:03:24,060
The range of the elements?

56
00:03:24,060 --> 00:03:26,750
021 That means any element, the cake.

57
00:03:27,150 --> 00:03:28,470
It will not be less than zero.

58
00:03:28,470 --> 00:03:29,850
It will not be greater than Hanneke.

59
00:03:30,060 --> 00:03:34,020
So the elements are like five three seven twenty thirty five.

60
00:03:34,020 --> 00:03:34,710
Forty five.

61
00:03:34,710 --> 00:03:35,340
Thirty five.

62
00:03:35,340 --> 00:03:36,000
Ninety five.

63
00:03:36,420 --> 00:03:37,110
Eighty five.

64
00:03:37,110 --> 00:03:37,680
Eighty five.

65
00:03:37,680 --> 00:03:38,550
Seventy five.

66
00:03:38,550 --> 00:03:39,180
Eighty five.

67
00:03:39,270 --> 00:03:39,750
Like this.

68
00:03:39,750 --> 00:03:40,590
The numbers have it.

69
00:03:40,920 --> 00:03:43,020
How many final numbers are there.

70
00:03:44,030 --> 00:03:45,290
That's it, next.

71
00:03:45,800 --> 00:03:53,600
So in this, if you have observed, elements may be repeating yes, because there are 500 elements supply

72
00:03:53,600 --> 00:03:58,880
demand for five hundred students know many students may be getting the same marks because the marks

73
00:03:58,880 --> 00:04:02,200
are ranging from zero to 100 only and there are 500 students.

74
00:04:02,480 --> 00:04:05,090
So, yes, more than one student may be getting same marks.

75
00:04:05,090 --> 00:04:11,440
Like Ben, the student got 50 marks or toddies student got 90 marks.

76
00:04:11,500 --> 00:04:16,649
It may be there is some more than one student may get the same marks imagining those marks.

77
00:04:17,120 --> 00:04:18,240
Now what we want.

78
00:04:18,920 --> 00:04:27,450
We want the frequencies of those marks, which are greater than 50 means 51 onwards, 51 onwards.

79
00:04:27,890 --> 00:04:31,310
So we want a frequency like how many got 51 marks.

80
00:04:31,340 --> 00:04:32,870
How many got 52 marks?

81
00:04:32,870 --> 00:04:34,970
How many got 53 marks.

82
00:04:35,000 --> 00:04:35,810
That is frequency.

83
00:04:35,810 --> 00:04:36,140
How many?

84
00:04:36,540 --> 00:04:37,430
That is frequency.

85
00:04:37,940 --> 00:04:41,430
Frequency of the elements are greater than 50.

86
00:04:42,050 --> 00:04:47,170
So for the string only the frequencies, not the numbers, just the frequencies.

87
00:04:47,570 --> 00:04:49,080
What side of what you need.

88
00:04:49,670 --> 00:04:52,910
You need an array from 51 to hand that total.

89
00:04:52,910 --> 00:04:53,690
How many elements?

90
00:04:53,720 --> 00:04:54,830
Just 50 elements.

91
00:04:55,170 --> 00:04:56,690
But you need an array of size.

92
00:04:56,690 --> 00:04:59,740
Just 50 array of size should be sufficient.

93
00:05:00,260 --> 00:05:05,390
For example, 10 people got 51 marks Staudenmaier.

94
00:05:07,260 --> 00:05:11,680
Twelve people got 52 marks, so stood 12 here.

95
00:05:11,850 --> 00:05:13,210
So this is just a frequency.

96
00:05:13,740 --> 00:05:19,460
This is representing 151 marks because we know very well that we are taking the frequency from 50 onwards.

97
00:05:19,860 --> 00:05:23,370
So this first is 451 marks, then makes 450 two marks.

98
00:05:23,370 --> 00:05:24,510
Fifty three marks one.

99
00:05:24,960 --> 00:05:27,120
So I have taken up to 50.

100
00:05:27,120 --> 00:05:30,170
If you don't want to start from zero again, start from one also.

101
00:05:30,570 --> 00:05:34,110
But if I remove this that is from zero to forty nine total.

102
00:05:34,110 --> 00:05:39,990
What is the size of another 50, because we want only those frequencies whose values are better than

103
00:05:39,990 --> 00:05:40,370
50.

104
00:05:40,780 --> 00:05:42,690
So the answer is 50.

105
00:05:45,350 --> 00:05:48,170
So if you related with the monks, it's easy to understand.

106
00:05:48,500 --> 00:05:50,760
So let's start with the second question now.

107
00:05:50,780 --> 00:05:52,830
Let us look at the third question here.

108
00:05:54,050 --> 00:05:59,930
The question is, and other is having the elements in increasing order, sorted order, increasing order.

109
00:06:00,470 --> 00:06:09,500
And we have to find out a bit of a distinctly unique element such that there sum total is equal to us.

110
00:06:09,830 --> 00:06:10,700
So what is this?

111
00:06:10,700 --> 00:06:12,120
Let us say this then.

112
00:06:12,860 --> 00:06:17,840
So I want any two numbers whose sum is equal to 10.

113
00:06:18,840 --> 00:06:25,710
All right, so I have not written the code, just I will trace it so you can understand it so far finding

114
00:06:25,710 --> 00:06:31,800
that we have two variables Gundars I n g i is at zero and G that one.

115
00:06:32,820 --> 00:06:35,310
Now we have to find out the numbers.

116
00:06:35,520 --> 00:06:39,770
The difference is that those different systems, for example, tend to have taken.

117
00:06:40,290 --> 00:06:41,240
So let us proceed.

118
00:06:41,880 --> 00:06:42,490
What to do.

119
00:06:43,260 --> 00:06:48,390
Check oesophagectomy of a G minus here if I subtract it.

120
00:06:48,390 --> 00:06:49,830
So four minus two is two.

121
00:06:50,100 --> 00:06:51,270
So difference is small.

122
00:06:51,390 --> 00:06:52,650
It is smaller than 10.

123
00:06:52,920 --> 00:06:53,550
So move.

124
00:06:53,700 --> 00:06:54,240
So move.

125
00:06:54,240 --> 00:06:55,310
Which one move g.

126
00:06:55,690 --> 00:07:02,310
OK, let us move to the next element because the difference was the small less than 10.

127
00:07:02,310 --> 00:07:03,120
Less than 10.

128
00:07:03,810 --> 00:07:05,700
Nozick eight minus two.

129
00:07:07,050 --> 00:07:15,830
A small less than 10, some move which one G nine minus two or seven, it is still less than 10 Muji.

130
00:07:17,380 --> 00:07:22,640
Thirteen minus two is 11 and it is greater than 10.

131
00:07:22,660 --> 00:07:24,820
It's not equal, then move more.

132
00:07:24,850 --> 00:07:25,300
Which one?

133
00:07:25,570 --> 00:07:29,880
If you move Jay now, that difference will be greater than that only.

134
00:07:30,430 --> 00:07:32,230
No, we want equal to ten.

135
00:07:32,230 --> 00:07:35,290
So not as great as from what I saw.

136
00:07:35,290 --> 00:07:41,280
It means if the difference is greater than it's more why if it is less then less than the movie.

137
00:07:41,690 --> 00:07:43,230
It's a movie, ok.

138
00:07:43,720 --> 00:07:45,490
I not check the difference.

139
00:07:45,490 --> 00:07:48,420
13 minus four is nine.

140
00:07:49,090 --> 00:07:52,120
So the difference is less than this movie share.

141
00:07:53,300 --> 00:07:58,940
Sixteen minus four to one difference is greater than this, so change the smaller number.

142
00:07:58,970 --> 00:07:59,620
This is smaller.

143
00:07:59,630 --> 00:08:00,460
This is a bigger number.

144
00:08:00,680 --> 00:08:01,990
So change the smaller number.

145
00:08:02,480 --> 00:08:06,070
Now, six minus eight is eight.

146
00:08:06,080 --> 00:08:07,150
It is less than 10.

147
00:08:07,520 --> 00:08:10,790
So go for a bigger number, 19.

148
00:08:10,790 --> 00:08:13,610
Minus eight is 11.

149
00:08:14,240 --> 00:08:17,240
Now, the number is bigger than is greater than this.

150
00:08:17,510 --> 00:08:23,210
So change the smaller number move, i.e. 19 minus nine, 10.

151
00:08:23,360 --> 00:08:24,290
Yes, we got it.

152
00:08:25,250 --> 00:08:35,450
So the point here is when we were moving, Jay, when we were moving, I if if I saw sorry G is minus

153
00:08:35,450 --> 00:08:39,500
eight of I was less than s then we were moving.

154
00:08:39,500 --> 00:08:42,260
Jay, if they are equal we have to stop.

155
00:08:42,500 --> 00:08:45,760
If it is greater then move i.e. so that's what we were doing.

156
00:08:46,430 --> 00:08:52,370
So in the question the court is given in that I have asked what should be the condition for this one

157
00:08:52,370 --> 00:08:55,370
here to have given as E and what should be there.

158
00:08:55,550 --> 00:09:01,070
So it should be year of the year minus elfy is less than this then moved shapelessness.

159
00:09:01,100 --> 00:09:03,370
So the next statement read on there is plus plus.

160
00:09:03,380 --> 00:09:06,150
So this should happen if this condition is true.

161
00:09:07,100 --> 00:09:09,170
So in options the conditions are given.

162
00:09:09,170 --> 00:09:10,340
So this the right option.

163
00:09:11,590 --> 00:09:17,890
Now, next question is fourth one, there is an array of elements and we have to find out an element

164
00:09:17,890 --> 00:09:23,370
which is neither a minimum nor maximum means in the area.

165
00:09:23,380 --> 00:09:28,450
If you see the minimum element is three maximum elements already.

166
00:09:28,460 --> 00:09:29,470
I have highlighted it.

167
00:09:29,680 --> 00:09:32,230
So we don't want to end goal other than that.

168
00:09:32,890 --> 00:09:34,600
So total, how many elements are there?

169
00:09:34,600 --> 00:09:35,100
Seven.

170
00:09:36,070 --> 00:09:37,360
One element is minimum.

171
00:09:37,360 --> 00:09:38,650
One element is maximum.

172
00:09:38,980 --> 00:09:43,660
Two elements you leave five elements are there, which are neither minimum nor maximum.

173
00:09:44,020 --> 00:09:49,870
What are those eight seven five nine four five elements are there out of that we have to pick up any

174
00:09:49,870 --> 00:09:50,140
one.

175
00:09:51,040 --> 00:09:52,150
So I'll give an example.

176
00:09:52,420 --> 00:09:57,550
Suppose there is a class of hundred students and I ask you to call one of the student.

177
00:09:57,770 --> 00:10:03,160
Right, but don't call Mr A and Mr B, don't call these persons.

178
00:10:03,880 --> 00:10:05,830
Other than that, you call anybody.

179
00:10:06,310 --> 00:10:10,060
So do you have to check all 100 students?

180
00:10:10,060 --> 00:10:15,580
And then you have we will bring the person all just within a few minutes or within a few seconds.

181
00:10:15,580 --> 00:10:18,670
You can catch one person, you can easily catch a person.

182
00:10:18,670 --> 00:10:20,200
It will not take much time for you.

183
00:10:21,310 --> 00:10:26,410
So similarly here, we don't want a minimum element if we want a minimum element and we have to check

184
00:10:26,410 --> 00:10:30,920
the entire area to conform to the minimum number, we don't want maximum number for that.

185
00:10:30,940 --> 00:10:33,940
Also, you have to check the interior and confirm this is the maximum.

186
00:10:34,270 --> 00:10:35,440
We don't want the one.

187
00:10:35,440 --> 00:10:37,130
We just need at a minimum, not the maximum.

188
00:10:37,720 --> 00:10:42,760
So the simple trick here is that you take the first three elements, first three elements.

189
00:10:42,940 --> 00:10:46,430
We don't know what those elements are by chance minimum.

190
00:10:46,450 --> 00:10:52,300
Is there one among them, if by chance maximum element is there in those three elements?

191
00:10:52,420 --> 00:10:57,160
It's supposed to well, was here all three was that was sort of three.

192
00:10:57,700 --> 00:10:58,220
Either one.

193
00:10:58,220 --> 00:11:03,190
This maximum one is minimum we want, which is neither minimum nor maximum.

194
00:11:03,490 --> 00:11:04,750
So out of these three.

195
00:11:04,900 --> 00:11:07,330
Out of these three, this is minimum.

196
00:11:07,870 --> 00:11:08,770
This is maximum.

197
00:11:09,460 --> 00:11:12,090
This one will look like one.

198
00:11:12,370 --> 00:11:14,130
We just need a minimum of maximum.

199
00:11:14,320 --> 00:11:14,860
Yes.

200
00:11:15,520 --> 00:11:17,590
Seven is one of the element.

201
00:11:18,370 --> 00:11:19,240
Eight is also there.

202
00:11:19,250 --> 00:11:21,790
Five year old that what we don't know, we don't want to check all.

203
00:11:22,030 --> 00:11:23,800
We don't want minimum, maximum.

204
00:11:23,830 --> 00:11:29,050
Anyone elements, anyone element means you don't have to check the entire array, just be one one element.

205
00:11:29,050 --> 00:11:32,680
So out of these three elements, we leave the minimum one.

206
00:11:32,980 --> 00:11:34,660
Maybe that is the minimum element in them.

207
00:11:34,660 --> 00:11:39,410
That is really the maximum thinking that maybe that is the maximum element in it.

208
00:11:39,760 --> 00:11:44,030
Only one element, middle one definitely does need a minimum, not maximum.

209
00:11:44,920 --> 00:11:50,500
So how much time it would take to find out element which is neither minimum nor maximum out of just

210
00:11:50,500 --> 00:11:51,220
three elements?

211
00:11:51,490 --> 00:11:54,670
It takes Constantine just Konstantine.

212
00:11:54,910 --> 00:11:56,520
You don't have to scan the whole area.

213
00:11:56,920 --> 00:11:57,450
That's it.

214
00:11:57,730 --> 00:11:59,320
So the time taken is constant.

215
00:12:00,440 --> 00:12:06,310
That's what I call you, you have to call somebody, but not A and B. So just casual person, he's not

216
00:12:06,320 --> 00:12:08,740
oh, he's a Levett next person, huh?

217
00:12:08,790 --> 00:12:11,740
He's not in there in order to bring him next on.

218
00:12:12,620 --> 00:12:16,790
You do not take much time checking all the people out of those 100 people.

219
00:12:17,220 --> 00:12:19,660
That's what we did here, not last question.

220
00:12:20,470 --> 00:12:21,960
The question is there isn't that.

221
00:12:22,420 --> 00:12:28,690
And we have to find out second largest element, how much time it takes so far, finding larger sentiment

222
00:12:28,690 --> 00:12:29,140
like here.

223
00:12:29,140 --> 00:12:30,520
The largest element is 15.

224
00:12:30,850 --> 00:12:31,290
Right.

225
00:12:31,360 --> 00:12:33,450
This one, it takes out of time.

226
00:12:33,820 --> 00:12:38,850
Then second time again, you search for an element which should be largest, but not 15.

227
00:12:39,280 --> 00:12:40,730
So one more time.

228
00:12:40,750 --> 00:12:45,580
So if you scan the entirety for and being taken and then one more time.

229
00:12:45,580 --> 00:12:50,580
And so it is two and six out of 10 Americans out of an.

230
00:12:51,700 --> 00:12:52,300
So that's it.

231
00:12:52,690 --> 00:12:55,500
These are the solution for Cui's three.

