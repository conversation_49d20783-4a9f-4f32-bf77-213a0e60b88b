1
00:00:00,300 --> 00:00:06,689
Now, the topic is quadratics probing, this is one of the collision resolution technique and it comes

2
00:00:06,689 --> 00:00:08,280
under open addressing.

3
00:00:08,790 --> 00:00:15,210
So if there is a collusion, we try to store eliminate at some free space in the hash table and the

4
00:00:15,210 --> 00:00:19,050
harsh cable, whatever the space is given, we try to utilize that same space.

5
00:00:21,130 --> 00:00:26,350
So in the previous we do, we saw <PERSON><PERSON> improving and improving, there was a problem of clustering of

6
00:00:26,350 --> 00:00:28,420
element or primary clustering of element.

7
00:00:28,930 --> 00:00:31,920
That group of elements were forming a single block.

8
00:00:31,930 --> 00:00:36,070
So to avoid that, we have introduced quadratic proving.

9
00:00:36,730 --> 00:00:38,300
So let us learn about this one.

10
00:00:38,710 --> 00:00:40,960
So for explanation, I have taken an example.

11
00:00:40,960 --> 00:00:42,370
Let us look at the example.

12
00:00:42,670 --> 00:00:44,860
I have keys in the key space.

13
00:00:44,860 --> 00:00:48,930
I have just taken four keys here and this is a hash table of size 10.

14
00:00:49,270 --> 00:00:53,110
I have taken keys that are less than half the size of the hash table.

15
00:00:53,350 --> 00:00:56,520
As I already said, that loading factor should not be more than point five.

16
00:00:56,530 --> 00:00:58,390
So I have taken only four keys to explain.

17
00:00:59,110 --> 00:01:00,370
This is the hash function.

18
00:01:00,370 --> 00:01:03,910
Modulars hash function then is the size of the table.

19
00:01:03,910 --> 00:01:05,890
So this is ten then.

20
00:01:05,890 --> 00:01:09,850
This is a modified hash function used for quadratic probing.

21
00:01:10,150 --> 00:01:16,960
So if you see it looks similar only as the actual faces of X plus four, five, more than ten.

22
00:01:16,960 --> 00:01:18,920
You see the same here.

23
00:01:18,940 --> 00:01:19,840
There is a difference.

24
00:01:19,840 --> 00:01:22,060
FFI is equal to a square.

25
00:01:23,470 --> 00:01:30,740
As far as equals two ice crack and it takes 012, so on, so this is the difference between me and quadratic

26
00:01:30,740 --> 00:01:36,900
probing, the next position is found with the quadratic value, not a linear value.

27
00:01:37,460 --> 00:01:40,540
So it means a linear probing if there is a collision.

28
00:01:40,550 --> 00:01:46,850
We were trying to insert an element at next freespace, but here we will be giving a little gap.

29
00:01:46,860 --> 00:01:49,590
We will be storing at a little distant place.

30
00:01:49,970 --> 00:01:52,650
So how insertion is done, let us look at this one.

31
00:01:53,180 --> 00:01:54,700
See, I have keys here.

32
00:01:56,470 --> 00:01:57,160
Twenty three.

33
00:01:58,190 --> 00:02:00,200
Three more to ten, three.

34
00:02:00,220 --> 00:02:02,240
So 33, the story here.

35
00:02:04,170 --> 00:02:09,039
Then for the three more, the 10, this is also mapped here.

36
00:02:09,270 --> 00:02:13,930
So there's a collision so far for will calculate at 43.

37
00:02:14,250 --> 00:02:19,220
This is at forty three plus F of zero.

38
00:02:19,920 --> 00:02:26,790
So first time when it is zero, then this is for this is three plus zero.

39
00:02:27,950 --> 00:02:30,590
More than the three.

40
00:02:31,870 --> 00:02:38,860
But there is a collision the next three plus this should be one, so that it is one minute, this is

41
00:02:38,860 --> 00:02:42,550
one square one, so one more time that is four.

42
00:02:42,820 --> 00:02:44,440
So, yes, there is a free space.

43
00:02:44,650 --> 00:02:48,010
So Forestry's is stored at the next free space here.

44
00:02:49,590 --> 00:02:58,530
Nudniks 13, more than three only Simplist, so now there's also mapping there, let us do it so I will

45
00:02:58,530 --> 00:03:03,570
just change this one instead of writing once again one nada, zero.

46
00:03:04,670 --> 00:03:05,060
Right.

47
00:03:05,330 --> 00:03:06,780
So we get the next three.

48
00:03:07,370 --> 00:03:11,840
There is a collision next time with this one, so we get the next four.

49
00:03:12,170 --> 00:03:13,500
That is again collision.

50
00:03:13,970 --> 00:03:16,190
Next, we will do three plus.

51
00:03:16,490 --> 00:03:20,340
This will become too so to squarest for more than 10.

52
00:03:20,730 --> 00:03:21,580
That is seven.

53
00:03:22,130 --> 00:03:26,870
So, yes, this 13 is a story at this seven and seven.

54
00:03:27,170 --> 00:03:32,770
So you can see that element of the story with a little distance if there is a collision.

55
00:03:33,590 --> 00:03:36,920
So it means in this three first time, we are at zero.

56
00:03:36,950 --> 00:03:38,230
Next time we are that one.

57
00:03:38,230 --> 00:03:41,600
Next time we are done for next time, we'll be adding nine.

58
00:03:42,590 --> 00:03:44,620
So square square quadratic.

59
00:03:44,960 --> 00:03:47,470
So that's all this is quadratic proving.

60
00:03:48,080 --> 00:03:49,640
Now I have a new last element.

61
00:03:49,640 --> 00:03:51,470
Twenty seven this map here.

62
00:03:51,710 --> 00:03:55,010
But there is a collision so fast with the zero it will be collision.

63
00:03:55,680 --> 00:03:57,770
This will be seven and zero.

64
00:03:57,770 --> 00:04:01,400
It is collision then seven plus one eight elastomeric eight.

65
00:04:01,550 --> 00:04:03,770
So twenty seven is historic.

66
00:04:03,770 --> 00:04:04,040
Eight.

67
00:04:05,860 --> 00:04:07,800
That's all about Goderich probing.

68
00:04:09,090 --> 00:04:14,490
So I don't have to explain much in this one and the same as linear probing only the differences, we

69
00:04:14,490 --> 00:04:19,980
are getting the next location with a distance apart that is not close or consecutive locations.

70
00:04:21,890 --> 00:04:27,450
Then about that analysis, I will there is a known formula, I will write on the formula.

71
00:04:27,800 --> 00:04:32,840
So here are the formulas for every successful search and unsuccessful search.

72
00:04:33,350 --> 00:04:36,500
Just you have to accept these formulas and use them for the classic.

73
00:04:38,640 --> 00:04:39,780
That's about.

74
00:04:41,180 --> 00:04:42,150
Quadratic probing.

