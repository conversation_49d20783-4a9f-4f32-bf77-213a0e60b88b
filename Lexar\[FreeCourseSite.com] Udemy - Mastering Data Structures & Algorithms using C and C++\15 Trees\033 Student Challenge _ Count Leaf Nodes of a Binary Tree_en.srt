1
00:00:00,150 --> 00:00:06,660
In this video, we will see how the gold leaf north and nonbelief north of the north, 40 degrees zero

2
00:00:06,960 --> 00:00:11,470
north a degree one or north with the degree to see, for example.

3
00:00:12,450 --> 00:00:14,070
These are live nodes, right?

4
00:00:14,100 --> 00:00:16,990
These are live north and these are all known live nodes.

5
00:00:17,460 --> 00:00:18,940
So these are the degrees zero.

6
00:00:19,230 --> 00:00:22,090
So these are lymph nodes and degree more than zero.

7
00:00:22,260 --> 00:00:23,310
They are non lymph nodes.

8
00:00:23,760 --> 00:00:26,430
And these are with the degree to this degree to.

9
00:00:26,550 --> 00:00:26,990
Right.

10
00:00:27,210 --> 00:00:30,320
And this is a degree to and these nodes, either the degree one.

11
00:00:30,330 --> 00:00:31,020
This is one.

12
00:00:31,170 --> 00:00:31,470
Right.

13
00:00:31,680 --> 00:00:32,439
And this one.

14
00:00:32,490 --> 00:00:33,990
So that's what we want to consider.

15
00:00:35,040 --> 00:00:39,780
We have already seen the recursive function for counting the number of nodes and this was the function

16
00:00:40,080 --> 00:00:40,530
already.

17
00:00:40,530 --> 00:00:43,830
I have shown you the tracing of this one now, the same function.

18
00:00:43,830 --> 00:00:48,200
If I modify, make some changes, I can count lymph nodes.

19
00:00:48,690 --> 00:00:52,020
So let us make changes for various types of nodes.

20
00:00:52,350 --> 00:00:54,990
So first one is leaf nodes.

21
00:00:55,230 --> 00:00:58,980
See, when you say anomalous lymph nodes, if left, chinless, null and right.

22
00:01:00,030 --> 00:01:03,320
So I will remove this line, OK, and remove this line.

23
00:01:03,690 --> 00:01:06,510
And here I will see if.

24
00:01:08,220 --> 00:01:16,590
No child is not as well as Rachel is none, so please, all child is equal to none.

25
00:01:17,190 --> 00:01:24,180
If it is none as well as these are child like is equal to none.

26
00:01:27,460 --> 00:01:36,730
Then if both are null, then I will contact Node, so I will return X plus my plus one otherwise means

27
00:01:36,730 --> 00:01:41,910
if any, if any one of the condition fails, then it will not enter here.

28
00:01:42,370 --> 00:01:42,820
Then what?

29
00:01:42,820 --> 00:01:46,220
I should return, I should return X plus.

30
00:01:46,240 --> 00:01:47,760
Why that song.

31
00:01:48,700 --> 00:01:52,440
See if these are true then add one.

32
00:01:52,450 --> 00:01:53,880
Otherwise you don't add one.

33
00:01:54,460 --> 00:01:59,230
So it will add one only for those nodes whose both left and right child are null.

34
00:01:59,620 --> 00:02:02,110
So this will be counting lymph nodes.

35
00:02:02,470 --> 00:02:08,720
And after this, if we can return zero, if a node is null, return zero.

36
00:02:08,949 --> 00:02:10,130
So this is already there.

37
00:02:10,150 --> 00:02:11,160
We have already seen it.

38
00:02:12,190 --> 00:02:19,420
So just by adding one condition and the same function for count, we can change this function for counting

39
00:02:19,660 --> 00:02:20,550
lymph nodes.

40
00:02:20,860 --> 00:02:24,500
So leave nodes are vector degrees zero.

41
00:02:24,520 --> 00:02:32,610
So this function is for counting nodes for the degree zero next as counting nodes for the degree to.

42
00:02:33,070 --> 00:02:37,030
So what changes have to do so inside this function, I should say.

43
00:02:38,410 --> 00:02:43,980
Bees left child is not equal to none, and the piece of child is also not equal to none.

44
00:02:44,680 --> 00:02:50,800
If both are not null, then these are notes with the degree to sow discord.

45
00:02:50,800 --> 00:02:51,370
On this note.

46
00:02:51,370 --> 00:02:55,300
On this note, so far, this story, I will get the result out of three.

47
00:02:55,300 --> 00:02:56,980
There are three notes with a degree, too.

48
00:02:58,090 --> 00:02:59,920
This is for finding notes for the degree to.

49
00:03:01,180 --> 00:03:07,150
So just change of condition, that is sufficient, so if this is true, both left, shall I tell both

50
00:03:07,150 --> 00:03:09,210
are there, otherwise don't come.

51
00:03:10,590 --> 00:03:18,240
The third one counting north, also the degree to as well as one means, if a note is having any one

52
00:03:18,240 --> 00:03:23,250
child count, it means this note should be counted because this is no, this is not done.

53
00:03:23,820 --> 00:03:25,310
This is not the system.

54
00:03:25,330 --> 00:03:26,100
Both are not.

55
00:03:26,700 --> 00:03:29,520
So for that, I simply have to change this condition.

56
00:03:29,520 --> 00:03:37,800
As of now, this looks for counting n for the degree one and two.

57
00:03:38,100 --> 00:03:42,360
So all internal laws so that there's a sufficient.

58
00:03:42,810 --> 00:03:50,310
Now, next, I will show you how to write the condition for counting all those n whose degree is one.

59
00:03:50,820 --> 00:03:53,090
So only it's a matter of changing the conditions.

60
00:03:53,090 --> 00:03:58,820
So I will remove this function and here I will write on all the conditions for relief, not only for

61
00:03:59,070 --> 00:04:00,470
all the conditions I will write on.

62
00:04:00,930 --> 00:04:02,310
So let us see the conditions.

63
00:04:02,580 --> 00:04:05,480
See first one leaf node condition.

64
00:04:05,490 --> 00:04:06,660
So leave node condition.

65
00:04:06,660 --> 00:04:17,940
If a PS left child right is equal to none so far that I can write, not as well as his right child should

66
00:04:17,940 --> 00:04:20,190
be equal to none so far that I can write not.

67
00:04:20,399 --> 00:04:28,110
This is four leaf node and node with the degree to degree do so here.

68
00:04:28,110 --> 00:04:34,230
Conditioner's F ps and child is not equal to null so I don't have to write anything.

69
00:04:34,230 --> 00:04:38,770
So it is not equal to not only we have already learned about this one right now.

70
00:04:38,790 --> 00:04:40,770
Next PS our child.

71
00:04:41,250 --> 00:04:48,450
If both are true then this is four degrees to degree to then the third one degree.

72
00:04:49,680 --> 00:04:59,550
One or two, so for this, the condition is if Abby's left child is not known or she's right, child

73
00:04:59,660 --> 00:05:02,640
is not the condition.

74
00:05:03,900 --> 00:05:11,610
This is for exactly degree zero, right, exactly degree to business two or one.

75
00:05:12,660 --> 00:05:20,250
Then last one, exactly, the green one, so exactly the government's effort is not having left child,

76
00:05:20,370 --> 00:05:25,340
it must have right child, or if it is having left China, it should not have a right child.

77
00:05:25,770 --> 00:05:27,330
There's the conditions for that.

78
00:05:27,330 --> 00:05:31,440
I right on the condition here that is phot condition for degree.

79
00:05:31,680 --> 00:05:32,460
Exactly.

80
00:05:32,460 --> 00:05:32,820
One.

81
00:05:32,970 --> 00:05:36,510
So the condition as if a piece a child.

82
00:05:37,140 --> 00:05:37,520
Right.

83
00:05:37,800 --> 00:05:41,190
Is not equal to null then.

84
00:05:41,640 --> 00:05:46,560
And definitely these are child should be equal to No.

85
00:05:48,440 --> 00:05:48,860
Right.

86
00:05:49,640 --> 00:05:50,870
This is one condition.

87
00:05:51,020 --> 00:05:58,490
I'll put it in bracket or the second part are if a piece left childless, then this should not be enough.

88
00:05:59,330 --> 00:06:06,350
So I will write on and the next line I will write on B's ill child is equal to none.

89
00:06:06,710 --> 00:06:10,310
See, earlier we have written not equal to null.

90
00:06:10,310 --> 00:06:16,220
Here I have written not equal to none so no but is equal to null and B's.

91
00:06:16,220 --> 00:06:20,540
Our child should not be equal to our child, not equal to none.

92
00:06:23,090 --> 00:06:29,540
This is how we can vote on the condition for exactly one child, that is degree one.

93
00:06:30,640 --> 00:06:34,710
See, this is too much lendee, can't we reduce the size?

94
00:06:35,230 --> 00:06:40,760
See, this is the coalition is a coalition condemnation from here to here as a single condition.

95
00:06:40,930 --> 00:06:42,370
This also should be in the Blackett.

96
00:06:43,890 --> 00:06:50,370
This is very lendee, can we reduce it so I remove this and show you how I can reduce this fund so this

97
00:06:50,370 --> 00:06:56,850
you can take it if you want to, not only you can do it on this one, but this one, I will try it once

98
00:06:56,850 --> 00:06:57,120
again.

99
00:06:57,270 --> 00:07:02,910
This one is just like if left -- there, dendrite change should not be there.

100
00:07:03,030 --> 00:07:09,860
Right this part or are if left chinless not there right.

101
00:07:09,870 --> 00:07:13,270
Shell must be dead left shell is not then I must be dead.

102
00:07:13,860 --> 00:07:14,790
So what is this.

103
00:07:15,000 --> 00:07:17,720
This is nothing but an exclusive.

104
00:07:17,730 --> 00:07:19,320
Ah ah.

105
00:07:20,310 --> 00:07:21,720
An exclusive.

106
00:07:21,720 --> 00:07:23,040
Ah and ah.

107
00:07:23,400 --> 00:07:25,800
So this is with left and right exclusive.

108
00:07:25,800 --> 00:07:26,150
Ah.

109
00:07:26,850 --> 00:07:32,020
So is there any exclusive ah operation in C C++ like languages.

110
00:07:32,040 --> 00:07:32,730
Yes they have.

111
00:07:33,060 --> 00:07:33,680
What is that.

112
00:07:33,870 --> 00:07:36,020
So I will write on this condition once again here.

113
00:07:36,060 --> 00:07:40,140
So here if these Elci Right.

114
00:07:40,440 --> 00:07:41,500
It's not equal to.

115
00:07:41,520 --> 00:07:41,910
No.

116
00:07:42,330 --> 00:07:43,320
This is Al.

117
00:07:44,590 --> 00:07:58,180
XOR fees are not equal to none the less ah yes, this caps is a symbol used for exclusive Parg, so

118
00:07:58,180 --> 00:08:02,760
I have reduced this lendee condition into a simple condition like this.

119
00:08:03,190 --> 00:08:06,190
So this is a condition for degree one.

120
00:08:06,770 --> 00:08:09,870
So that's all we have finished with all various type of conditions.

121
00:08:10,240 --> 00:08:16,510
Now, confirmation I have shown you by using X and Y variables, it can also be done without using those

122
00:08:16,510 --> 00:08:16,990
variables.

123
00:08:16,990 --> 00:08:19,000
So it can be done in a simple form also.

124
00:08:19,420 --> 00:08:23,890
So I will show you the other ways how you can write on the same recursive function.

125
00:08:24,390 --> 00:08:28,540
So I'll write on the function C function NamUs count.

126
00:08:28,540 --> 00:08:35,409
So I will call it a scoundrelly, which is taking a pointer to a. struct nor pointer b t.

127
00:08:35,710 --> 00:08:40,030
I will not take any variables X and Y simply first I will check the condition.

128
00:08:40,030 --> 00:08:45,970
If B is equal to null means if it is null, we know well that if it is not then the return zero.

129
00:08:46,210 --> 00:08:51,890
OK, there's nowhere else I don't have to write because inside the condition there is a written statement.

130
00:08:51,910 --> 00:08:53,320
So if it is true it will return.

131
00:08:53,320 --> 00:08:54,280
Function will stop.

132
00:08:54,280 --> 00:08:55,330
It will never come here.

133
00:08:55,810 --> 00:08:59,070
So I can write on the code without writing else.

134
00:08:59,260 --> 00:09:00,310
So let us see.

135
00:09:00,880 --> 00:09:07,450
Just say written gonged BS and chain.

136
00:09:08,650 --> 00:09:09,130
Right.

137
00:09:09,640 --> 00:09:11,290
Plus gonged.

138
00:09:12,250 --> 00:09:14,010
These are chain.

139
00:09:18,160 --> 00:09:27,820
Plus one, that's all a social simple see left or right, plus one, earlier I was taking these values

140
00:09:27,820 --> 00:09:30,290
in X and Y variables, then I was adding one.

141
00:09:30,310 --> 00:09:32,650
So now it can be done directly.

142
00:09:32,650 --> 00:09:36,250
I'm calling the function and Rachel also calling the function plus one.

143
00:09:36,940 --> 00:09:38,200
So the working will be same.

144
00:09:38,500 --> 00:09:41,320
So there's another way you can make on this one then.

145
00:09:41,650 --> 00:09:47,680
Regarding Leif Nordon on leave node here, I should have a condition that if it is the leaf node condition

146
00:09:48,040 --> 00:09:52,930
plus one, otherwise one more time I should write down count left, right and without writing.

147
00:09:52,930 --> 00:09:54,160
Plus I should try it on.

148
00:09:54,340 --> 00:10:02,710
See, for example, here I will write if we alkyl and B arginine.

149
00:10:04,590 --> 00:10:14,280
Not not so this is for life, not condition, Ardavan, otherwise written, called Peace, Love Child

150
00:10:16,320 --> 00:10:19,570
plus cold peace, right, China.

151
00:10:22,140 --> 00:10:23,130
Let's follow.

152
00:10:24,900 --> 00:10:30,760
So this is a function for we have learned how to find the number of lymph nodes and a number of long-lived

153
00:10:30,780 --> 00:10:32,760
nodes so you can practice this program.

