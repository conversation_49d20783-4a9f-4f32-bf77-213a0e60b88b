1
00:00:00,630 --> 00:00:07,440
In this video I'll give introduction to stock data structure and some of its applications where it is

2
00:00:07,440 --> 00:00:08,370
used or not.

3
00:00:08,610 --> 00:00:17,220
Then I'll be discussing abstract data type of stack stock ADT stock is a data structure which works

4
00:00:17,220 --> 00:00:17,970
on discipline.

5
00:00:17,970 --> 00:00:26,010
Last In First Out actually a stock is a collection of elements set of values collection of elements

6
00:00:26,400 --> 00:00:32,820
and those collection of elements they are inserted and deleted by following this discipline.

7
00:00:32,820 --> 00:00:38,070
So if you are inserting the elements then when you want to delete which one can be deleted only the

8
00:00:38,070 --> 00:00:41,590
last one that was inserted that can be deleted first.

9
00:00:42,760 --> 00:00:49,390
So this type of mechanism we use it in our daily life like many places see whatever we do manually we

10
00:00:49,390 --> 00:00:52,840
want to automate that one that is computerized Dec. 1.

11
00:00:52,930 --> 00:00:55,830
So we like the procedures for the program for those things.

12
00:00:55,840 --> 00:00:58,240
So we want our computers to do that.

13
00:00:58,390 --> 00:01:05,690
If we are using a stack in some application then in our program also stack would be used to let us see

14
00:01:05,690 --> 00:01:09,250
a few examples where stack is used in our daily life.

15
00:01:09,760 --> 00:01:17,870
So one example is suppose there is a dead end Lane and in this lane cars are parked.

16
00:01:17,870 --> 00:01:20,890
Suppose there is a first God.

17
00:01:21,020 --> 00:01:23,240
This is a red color God.

18
00:01:23,320 --> 00:01:24,300
This is black color.

19
00:01:24,310 --> 00:01:27,580
God this is white color.

20
00:01:28,270 --> 00:01:34,180
This is a red line and three cars are parked here so it means they are brought inside this lane from

21
00:01:34,180 --> 00:01:40,180
this addiction that if I want to take out these cars from which direction they should go.

22
00:01:40,830 --> 00:01:44,670
This site is close so they have to go out from the same direction.

23
00:01:45,060 --> 00:01:47,380
So this side of the lane they can go.

24
00:01:48,060 --> 00:01:50,070
So which God was inserted last.

25
00:01:50,130 --> 00:01:52,860
White car which God will glowed first.

26
00:01:52,890 --> 00:01:53,750
White car.

27
00:01:54,030 --> 00:01:57,740
So that soul lost in will go first.

28
00:01:57,750 --> 00:01:59,440
So last in first.

29
00:01:59,460 --> 00:02:01,260
In short we call it Das lethal.

30
00:02:02,370 --> 00:02:03,900
So this is one example.

31
00:02:03,900 --> 00:02:07,220
Now let us take one more example.

32
00:02:07,300 --> 00:02:10,620
There is a can of balls three balls are there.

33
00:02:10,630 --> 00:02:17,090
That is phosphate colored and green and then blue now from Richard Eric should I have inserted these

34
00:02:17,090 --> 00:02:19,810
balls in this can the static and does the opening.

35
00:02:19,820 --> 00:02:22,030
This is closed compartments closed.

36
00:02:22,050 --> 00:02:28,270
Then if I have to take out the balls from this direction only they will come out so which will the last

37
00:02:28,270 --> 00:02:30,020
ball inserted in the scan.

38
00:02:30,040 --> 00:02:30,730
Blue ball.

39
00:02:31,120 --> 00:02:32,740
Then we shall go first.

40
00:02:32,800 --> 00:02:33,900
Blue Ball will go.

41
00:02:34,150 --> 00:02:39,400
If we look into the can which is the first ball blue color ball it's not red.

42
00:02:39,400 --> 00:02:42,090
Red I can't see I can see blue on the top.

43
00:02:42,100 --> 00:02:44,350
So on the whatever is there on the top that is fast.

44
00:02:44,340 --> 00:02:49,630
Now even if you look in through this lane then which card you'll find first white color guard.

45
00:02:49,990 --> 00:02:51,690
So that white guy first.

46
00:02:51,940 --> 00:02:54,270
So the recently inserted one become the first.

47
00:02:54,580 --> 00:02:56,830
So fast forward in what sense.

48
00:02:56,830 --> 00:02:59,920
The first one to come out first one to come out.

49
00:03:00,040 --> 00:03:08,890
So this scan of balls is also a stack even we can take the example of a stack of plates or a stack of

50
00:03:08,890 --> 00:03:16,990
papers stack off of green bags green bags are kept at one level another in the weight holders stocking

51
00:03:17,050 --> 00:03:17,640
is done.

52
00:03:17,650 --> 00:03:19,150
So the products are kept.

53
00:03:19,150 --> 00:03:21,540
One airborne the third is like the stack.

54
00:03:21,640 --> 00:03:26,500
So we cannot take out any product from the middle or from the bottom we have to take all the products

55
00:03:26,500 --> 00:03:27,940
from the top.

56
00:03:27,960 --> 00:03:29,950
I'll give you one more example.

57
00:03:30,220 --> 00:03:34,600
One more example Nick's example of for Stack.

58
00:03:34,620 --> 00:03:42,150
See this is a railroad track railroad track that is track for train track rails track.

59
00:03:42,420 --> 00:03:46,840
Then this is one more track does an extended track.

60
00:03:46,840 --> 00:03:49,870
This is called as shunting yard.

61
00:03:49,900 --> 00:03:52,770
Now let me show you what is the use of this one.

62
00:03:52,780 --> 00:03:55,040
Suppose this is our train.

63
00:03:55,090 --> 00:03:58,280
This is an engine and behind that all cabins are there.

64
00:03:58,300 --> 00:03:59,400
This is the engine.

65
00:03:59,410 --> 00:04:00,680
The point is the engine.

66
00:04:01,150 --> 00:04:03,910
So this is a train it's coming in this direction.

67
00:04:03,920 --> 00:04:05,850
So it is facing in that direction.

68
00:04:06,160 --> 00:04:08,070
So it can go in that direction.

69
00:04:08,170 --> 00:04:18,500
If I take it on this track then what entered first engine entered first and the last cabin supported

70
00:04:18,540 --> 00:04:21,810
the last cabin duck was incident at last.

71
00:04:21,810 --> 00:04:23,350
Now when they take off from this one.

72
00:04:23,360 --> 00:04:24,950
So who's coming out first.

73
00:04:25,070 --> 00:04:28,360
That last cabin then when the engine will come off at the last.

74
00:04:28,360 --> 00:04:29,690
So who will come out first.

75
00:04:29,690 --> 00:04:32,680
The last cabin that was inserted will come out first.

76
00:04:32,680 --> 00:04:33,860
Now watch this.

77
00:04:33,860 --> 00:04:36,760
I'll take it out in this direction.

78
00:04:36,770 --> 00:04:40,830
Now you can see that the direction of train has a change.

79
00:04:41,040 --> 00:04:42,430
It was going in this direction.

80
00:04:42,440 --> 00:04:44,010
So it has changed the direction.

81
00:04:44,360 --> 00:04:45,840
So by.

82
00:04:46,250 --> 00:04:52,060
By sending a train on the strike and bringing it out from the track it has changed the direction.

83
00:04:52,070 --> 00:04:53,450
So I can see that.

84
00:04:53,480 --> 00:04:56,260
So this you can call this we can call it as a stack.

85
00:04:56,270 --> 00:05:01,450
So if anything is inserted in this tag and we take out again we can reverse acting.

86
00:05:01,460 --> 00:05:05,580
So you can see that train is reverse by using this type of track.

87
00:05:05,610 --> 00:05:12,390
So I'm junctions as religion shifts this type of tracks are available to change the direction of a train.

88
00:05:12,440 --> 00:05:20,440
So there's a lot more application of stack no one more a general idea I'll give you see we have seen

89
00:05:20,440 --> 00:05:28,570
the cautions precautions are the functions which call themselves so their behavior is like loop but

90
00:05:28,570 --> 00:05:30,520
they internally as a stack.

91
00:05:30,640 --> 00:05:32,550
We have already studied about this one.

92
00:05:32,620 --> 00:05:40,930
So recursion uses stack can we convert a recursive function or recursive procedure as are iterative

93
00:05:40,930 --> 00:05:41,730
procedure.

94
00:05:41,890 --> 00:05:44,060
That is a procedure using loop.

95
00:05:44,240 --> 00:05:48,760
So from recursion if we want to convert into iteration yes we can.

96
00:05:48,760 --> 00:05:56,060
So even iteration can be converted into recursion it is known that every recursion can be converted

97
00:05:56,060 --> 00:06:00,590
into iteration but then you can work recursion into iteration.

98
00:06:00,600 --> 00:06:09,300
Sometimes we have to use a stack in iteration recursion uses stock we know then that is a system stock

99
00:06:09,330 --> 00:06:14,970
if you call it the system's target that is automatic program or doesn't have to do anything come bilateral

100
00:06:14,970 --> 00:06:16,680
the runtime system will take care of it.

101
00:06:16,710 --> 00:06:18,410
So we don't have to worry about it.

102
00:06:18,600 --> 00:06:24,090
But when you're converting into iteration then we have to put away the stack and that will be called

103
00:06:24,090 --> 00:06:30,540
as a programmer the stocks or program are should create a stack and implement the stack in the program.

104
00:06:30,540 --> 00:06:32,520
So we have to use a stack in our program.

105
00:06:33,600 --> 00:06:38,820
So sometimes when the discussions are gone within in iteration we have to use a stack.

106
00:06:38,850 --> 00:06:45,370
Also not every discussion somebody cautions are directly converted to iteration some recursion V.T.

107
00:06:45,440 --> 00:06:46,940
quite a stack.

108
00:06:47,070 --> 00:06:49,410
So I will discuss this as a separate topic.

109
00:06:49,410 --> 00:06:55,200
I will go pick some examples and show you there the stack is required with the stack is not required.

110
00:06:55,560 --> 00:07:02,110
So we will discuss this as a separate topic but a general idea I gave you that if you have a known procedure

111
00:07:02,160 --> 00:07:09,240
recursion and you want to convert into iteration maybe you required a stack and vice versa.

112
00:07:09,270 --> 00:07:16,440
If you have a creative procedure and you are using a stack means it can be done better using recursion.

113
00:07:16,440 --> 00:07:22,950
So that's sort of what introduction to the stack now legacy a b the office track that is abstract data

114
00:07:22,950 --> 00:07:34,420
type of stack so I data type and contain data a presentation the operations on the stack let us look

115
00:07:34,420 --> 00:07:34,770
at it.

116
00:07:35,320 --> 00:07:38,430
Let us look at ADT off track ADT.

117
00:07:38,440 --> 00:07:45,770
Just give us their definition of US track in terms of data representation and operations.

118
00:07:45,850 --> 00:07:52,800
So let us see what is there in ADT data that is required is we need space for storing the elements.

119
00:07:52,870 --> 00:07:55,860
So as I said a is a collection of elements.

120
00:07:55,960 --> 00:07:59,670
So we need space for storing both collection of elements.

121
00:07:59,670 --> 00:08:04,560
The next thing is we need a top pointer that is pointing on the topmost element in this track because

122
00:08:04,560 --> 00:08:10,610
the important one is the topmost element Richland was recently inserted so Bob is a pointer pointing

123
00:08:10,610 --> 00:08:14,360
on the topmost element then these are the operations in this stack.

124
00:08:14,390 --> 00:08:20,600
Bushman is inserting value Bachmann's deleting a value peak means looking at the value.

125
00:08:20,600 --> 00:08:23,990
So let us see all the operations I have taken an example here.

126
00:08:24,050 --> 00:08:27,350
Suppose there's a stack and this is the topmost Randy Walker have inserted.

127
00:08:27,350 --> 00:08:29,470
And these are the values already present.

128
00:08:29,480 --> 00:08:34,419
So the foster value that I have inserted was 10 then 16 then indeed and Bernie face recently and so

129
00:08:34,419 --> 00:08:37,960
that one is twenty five so appropriate pointing here.

130
00:08:38,039 --> 00:08:43,450
Now what does it mean by Bush inserting a new value somewhere they want to insert today so inserted

131
00:08:43,500 --> 00:08:44,490
from the top.

132
00:08:44,490 --> 00:08:50,640
So as you know that always insertion and deletion is done from top inside the stack that is from semen.

133
00:08:50,670 --> 00:08:51,900
So insert today here.

134
00:08:51,930 --> 00:08:57,780
So now it's the topmost element this is the new top most elements let top point here on position forward.

135
00:08:58,080 --> 00:09:02,960
So this are we can push inserting a value Bushman just inserting one value.

136
00:09:03,120 --> 00:09:03,840
Not all value.

137
00:09:03,840 --> 00:09:09,930
So if you want more than one value called Push more than one dimes the next bop bop means deleting a

138
00:09:09,930 --> 00:09:12,150
value taking all the value from the stack.

139
00:09:12,180 --> 00:09:17,000
So if we take all the value topmost value will go and next adopt most values this one again.

140
00:09:17,260 --> 00:09:24,110
So double point on presently whichever is stopped most valued in point on that one then peak Vincent

141
00:09:24,110 --> 00:09:26,580
the linear value at that particular index.

142
00:09:26,580 --> 00:09:28,770
So which of the foster value inside the stack.

143
00:09:28,880 --> 00:09:33,590
Twenty five for Foster means not based insertion.

144
00:09:33,610 --> 00:09:35,190
The based on based on deletion.

145
00:09:35,220 --> 00:09:37,020
So if this is a stack like it can.

146
00:09:37,020 --> 00:09:44,520
So if I look in the can the topmost that I find is 25 so Foster values 25 second values strangely.

147
00:09:44,530 --> 00:09:46,920
Total value sixteen fourth values 10.

148
00:09:47,490 --> 00:09:52,240
So I wandered over to the total value so I should get down to 16 from the top.

149
00:09:52,320 --> 00:09:58,680
So pigments looking at the value at the given position it's not deleting just some knowing what is there

150
00:09:59,380 --> 00:10:02,490
somewhere they want to know what is there at the top place.

151
00:10:02,490 --> 00:10:03,320
I got 16.

152
00:10:03,600 --> 00:10:05,310
Yes I want to delete 16.

153
00:10:05,490 --> 00:10:11,940
So take out three values so you get twenty five then Brady then 16 then you can take sixteen.

154
00:10:12,660 --> 00:10:14,980
Let's suppose the boxes are kept one of on it.

155
00:10:15,000 --> 00:10:21,000
You want the third box from top then you have to take out topmost boards the next vault then only you

156
00:10:21,000 --> 00:10:26,100
can take them off if you simply pull that box out other boxes and fold on.

157
00:10:26,100 --> 00:10:30,170
So that's what we are imagining we are having same type of data structure.

158
00:10:30,420 --> 00:10:34,410
So we are imagining that these elements are also arranged similarly.

159
00:10:34,410 --> 00:10:41,480
So to take out 16 i should delete or pop out 25 data or indeed in 16 so poppies for deletion then NICS

160
00:10:41,510 --> 00:10:42,930
is stacked up.

161
00:10:42,960 --> 00:10:47,160
This is not for deleting just for knowing what is the topmost value in the stack.

162
00:10:47,170 --> 00:10:50,150
So right in order to post values twenty five.

163
00:10:50,460 --> 00:10:54,920
Just knowing the topmost value then is empty and it's full.

164
00:10:54,930 --> 00:10:58,120
Knowing that the stack is empty or those full.

165
00:10:59,130 --> 00:11:05,130
So right now stag is neither empty not full one more places that if I fill this place than the size

166
00:11:05,130 --> 00:11:06,150
of the stag is fine.

167
00:11:06,150 --> 00:11:07,400
So it will be fair.

168
00:11:07,560 --> 00:11:13,270
So say it stack will before or if I delete all these values then stack will be empty.

169
00:11:13,320 --> 00:11:15,530
So this is ADT I have explained you.

170
00:11:15,540 --> 00:11:17,700
That is what the what is the meaning of all these.

171
00:11:17,700 --> 00:11:23,740
I have explaining we have not seen the implementation now coming to implementation how we can implement

172
00:11:23,740 --> 00:11:24,470
this stack.

173
00:11:24,510 --> 00:11:31,920
Let us recall once again stack is a collection of elements which follows discipline lead for.

174
00:11:32,070 --> 00:11:39,040
So very historic collection of elements Ari or linked list.

175
00:11:39,220 --> 00:11:46,170
Yes these are the two physical data structures that we know so we use these are the structure for implementing

176
00:11:46,170 --> 00:11:52,980
that stack we can implement a stack using any order we can implement a stack using Linked List this

177
00:11:52,990 --> 00:11:54,470
what we have to learn.

178
00:11:54,790 --> 00:11:59,670
So first we will see how do we implement a stack using early.

179
00:11:59,860 --> 00:12:03,530
So when implementing a stack we will implement all these operations.

180
00:12:03,670 --> 00:12:09,280
So that's all about interaction to stack and ADT and the next video will see implementation of stack

181
00:12:09,550 --> 00:12:10,510
using only.

