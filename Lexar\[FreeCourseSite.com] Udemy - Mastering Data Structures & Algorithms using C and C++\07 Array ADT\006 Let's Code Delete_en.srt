1
00:00:00,920 --> 00:00:03,910
And this video, we will see the lead operation upon and a.

2
00:00:05,190 --> 00:00:11,910
So I'm using the same program which we have already used for insert a pen and display the the structure

3
00:00:12,450 --> 00:00:15,880
having a real size and then the size and length.

4
00:00:16,140 --> 00:00:23,610
So here, just off the main function, I will try to delete function, delete function with the elements

5
00:00:23,610 --> 00:00:25,390
from an array, from a given index.

6
00:00:25,410 --> 00:00:29,370
So it should return a deleted element and the function name is delete.

7
00:00:29,880 --> 00:00:35,760
It should take a barometer of type uhry and it should be called backrest because I'm going to modify

8
00:00:35,760 --> 00:00:41,400
an array and the next thing is it should take an index from where we want to delete an element here.

9
00:00:41,410 --> 00:00:46,500
I'm getting an error because function should return something, so I will write it down zero four I

10
00:00:46,500 --> 00:00:48,300
mean before writing the code.

11
00:00:50,730 --> 00:00:56,940
That it is gone, this short instant error, so wherever there is an error that time, it will highlight

12
00:00:56,940 --> 00:00:57,430
the error.

13
00:00:57,900 --> 00:01:05,360
There is the benefit of this idea for deletion and I should take the value deleted value in this variable.

14
00:01:05,370 --> 00:01:10,500
So I will make the variable initially zettl then before deletion.

15
00:01:10,500 --> 00:01:13,770
First of all, I should check whether the index is given is valid or not.

16
00:01:13,770 --> 00:01:21,240
So indexes should be greater than or equal to zero and index should be less than.

17
00:01:23,270 --> 00:01:25,100
Equal to Áras.

18
00:01:26,760 --> 00:01:35,430
Led to minus one, so I don't I can see just indexes less than Aarav land, so because its lenders will

19
00:01:35,430 --> 00:01:37,680
be having the last element at index zero.

20
00:01:41,440 --> 00:01:46,240
So this is the condition I have to check whether the index is valid or not, if the index is valid,

21
00:01:46,240 --> 00:01:49,720
then invariable get the value from it.

22
00:01:50,080 --> 00:01:51,580
I can give an index.

23
00:01:54,130 --> 00:01:58,300
Then I have to shift the rest of the value, so for that I need one more variable.

24
00:01:58,300 --> 00:01:59,740
I have declared the variable.

25
00:01:59,740 --> 00:02:09,220
I now here I will write on Unfollowed for keeping all the elements for I assign from index onwards.

26
00:02:09,220 --> 00:02:12,610
I should start and I should be less than.

27
00:02:15,450 --> 00:02:16,350
Iraj.

28
00:02:18,750 --> 00:02:24,680
Lent minus one, because we are shifting the element, so we should stop at the minus one and a plus

29
00:02:24,690 --> 00:02:25,170
plus.

30
00:02:26,340 --> 00:02:35,940
And every time at airports, I, I should copy an element from a of my plus one.

31
00:02:38,930 --> 00:02:40,460
This will shift all the elements.

32
00:02:41,760 --> 00:02:48,630
Then, after shifting the elements, the length of an artery should be implemented so drastically.

33
00:02:51,010 --> 00:02:58,330
And I should return the value X, whatever the value delivered, and here also I can write X, that

34
00:02:58,330 --> 00:03:00,850
is X will be zero or else I can write just zero.

35
00:03:00,850 --> 00:03:07,110
It also means if the limit is not found, it means the index is invalid and it will return zero.

36
00:03:08,450 --> 00:03:12,320
So this is the extra condition I have written, checking with the index is valid or not, this I did

37
00:03:12,320 --> 00:03:13,880
not discuss in my position.

38
00:03:14,850 --> 00:03:16,860
Now coming back to main function.

39
00:03:18,350 --> 00:03:23,770
Inside the main function, I will call it function delete will return the value that is deleted security,

40
00:03:23,960 --> 00:03:26,120
CBG time, so that value everybody.

41
00:03:27,170 --> 00:03:30,010
So let us write primitive.

42
00:03:31,800 --> 00:03:35,700
What's entirely new line then, Paul?

43
00:03:37,280 --> 00:03:41,610
Delete function by passing across of an index.

44
00:03:41,630 --> 00:03:47,590
I want to pass it in next for so last indexes for and the value person is six.

45
00:03:47,590 --> 00:03:48,940
So I want to delete this value.

46
00:03:49,610 --> 00:03:54,430
And after this, the length of the array will become four and I'll be having only four elements.

47
00:03:54,440 --> 00:03:55,640
That is two to five.

48
00:03:58,460 --> 00:04:00,810
Let us run the program and see what happens.

49
00:04:01,910 --> 00:04:08,840
Yes, value is deleted and now this function is displaying only four months solely for Valadier.

50
00:04:10,240 --> 00:04:13,120
Let us give a different index index, size zero.

51
00:04:13,150 --> 00:04:14,220
I'll give you an example.

52
00:04:14,260 --> 00:04:19,600
Let us run the program so Vallecito should be deleted and the remaining valid in an area are three,

53
00:04:19,600 --> 00:04:21,250
four, five, six.

54
00:04:23,650 --> 00:04:29,680
Let us the people this delete function, I'll put a break point on print and run the program.

55
00:04:33,160 --> 00:04:38,110
Now, watch here that idea give the values are two, three, four, five, six, and the values that

56
00:04:38,110 --> 00:04:39,910
I am building is index is zero.

57
00:04:40,540 --> 00:04:42,690
If I continue running, then what happens?

58
00:04:42,700 --> 00:04:43,450
Just watch it.

59
00:04:44,840 --> 00:04:50,540
It has entered into delete function, I will expand this array object so that we can see the contents

60
00:04:50,540 --> 00:04:51,470
of an array.

61
00:04:52,610 --> 00:04:53,990
See, no fault starts.

62
00:04:54,080 --> 00:04:55,370
It will be shifting the values.

63
00:04:56,590 --> 00:05:02,440
One by one, Seabreeze copied here at destabilises trees there, so the streets copied here, too,

64
00:05:02,440 --> 00:05:02,940
is gone.

65
00:05:03,280 --> 00:05:05,030
The next four will be copied here.

66
00:05:05,410 --> 00:05:08,860
So this 40 minute as it is, unless it is removed, so you can remove it.

67
00:05:09,820 --> 00:05:11,560
So this form will be copied here next.

68
00:05:14,390 --> 00:05:19,530
This fall is complete, then the five is shifted, then six has shifted.

69
00:05:19,550 --> 00:05:20,540
You can check the land.

70
00:05:20,540 --> 00:05:22,610
Also here at the bottom line is five.

71
00:05:24,550 --> 00:05:27,700
Six has shifted, that land became for.

72
00:05:30,690 --> 00:05:33,360
That's all it was, return back to main function.

73
00:05:35,460 --> 00:05:38,700
The sprinter deluded about dispirited athletic functions called.

74
00:05:41,770 --> 00:05:44,860
So therefore, with this delete function.

