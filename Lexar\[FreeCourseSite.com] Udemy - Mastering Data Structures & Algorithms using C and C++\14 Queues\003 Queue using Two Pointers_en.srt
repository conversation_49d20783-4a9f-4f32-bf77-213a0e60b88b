1
00:00:00,360 --> 00:00:03,469
Let us understand the draw back once again through an example.

2
00:00:04,350 --> 00:00:08,690
See, these people are standing in a queue and this person is providing some service.

3
00:00:08,690 --> 00:00:14,880
So he's handling a counter to the counter and the people are coming and going in simple fashion.

4
00:00:14,880 --> 00:00:16,129
That is, they are forming a queue.

5
00:00:16,379 --> 00:00:18,780
So if a new person comes, he will come and stand here.

6
00:00:19,020 --> 00:00:20,310
And who's done this now?

7
00:00:20,310 --> 00:00:20,970
This person.

8
00:00:20,980 --> 00:00:22,910
So when he has finished work, he will leave.

9
00:00:23,370 --> 00:00:26,210
Let us see what happens if this person is leaving the queue.

10
00:00:26,220 --> 00:00:27,360
His work has finished.

11
00:00:27,630 --> 00:00:28,680
So he is leaving the queue.

12
00:00:29,100 --> 00:00:32,610
If the person is going out from the humans, it is a delayed for.

13
00:00:32,610 --> 00:00:35,360
Let us see what happens if this person goes out.

14
00:00:35,370 --> 00:00:40,320
So this is deleted, then this place is free, so this space should be occupied.

15
00:00:40,320 --> 00:00:43,050
So usually what happens if the people are in the queue?

16
00:00:43,320 --> 00:00:48,060
If a person goes out, all people will move one step forward, all will move.

17
00:00:48,360 --> 00:00:53,580
So this person will move here and he will come to his place and he will come to his place and this person

18
00:00:53,580 --> 00:00:54,390
takes his place.

19
00:00:54,900 --> 00:00:57,540
So they all will shift because this person has gone.

20
00:00:58,140 --> 00:00:59,700
This person is not there in the queue.

21
00:00:59,940 --> 00:01:02,480
So this place is free so all will move.

22
00:01:03,000 --> 00:01:09,270
How many steps are taken when the one person has stepped out from the queue, how many steps are taken

23
00:01:09,510 --> 00:01:11,940
and steps are taken, as many people are there in the queue.

24
00:01:12,450 --> 00:01:13,560
So the time for the mission is.

25
00:01:13,560 --> 00:01:16,680
And yes, because people are moving forward.

26
00:01:16,680 --> 00:01:17,490
So they have shifted.

27
00:01:17,880 --> 00:01:26,190
So instead of that, if this counter can be moved or this person can move, if this person just takes

28
00:01:26,190 --> 00:01:31,500
one step and move forward, then we don't have to shift all these people this place this weekend so

29
00:01:31,500 --> 00:01:32,520
he can move forward.

30
00:01:33,180 --> 00:01:39,690
So this is what we will use for implementation of queue using two point guards in one point that we

31
00:01:39,690 --> 00:01:42,720
use for insertion and one point that we use for deletion.

32
00:01:42,720 --> 00:01:45,900
So it means instead of shifting the elements, we will move the pointer.

33
00:01:46,530 --> 00:01:48,330
So let us see the implementation for that.

34
00:01:48,330 --> 00:01:49,920
I already have taken the queue here.

35
00:01:50,310 --> 00:01:52,260
I'll explain how this works.

36
00:01:52,410 --> 00:01:58,950
So here I have taken a fixed size area and the size of an array is a seven and I have two pointers front

37
00:01:58,950 --> 00:01:59,340
for that.

38
00:01:59,340 --> 00:02:01,040
I have written just F and read.

39
00:02:01,170 --> 00:02:06,660
I have written are initially they are active minus one for initially front and rear.

40
00:02:06,660 --> 00:02:08,100
Both are at minus one.

41
00:02:08,490 --> 00:02:13,860
So it means there is nothing in the Q and Qs empty goes empty.

42
00:02:14,280 --> 00:02:19,860
So right now you can see that both are at minus one and also both are equal front is equal to.

43
00:02:19,860 --> 00:02:26,190
Then excuse me, let us see how to insert elements for inserting an element.

44
00:02:26,190 --> 00:02:30,960
I should move rear pointer to the next location and insurgent element.

45
00:02:31,770 --> 00:02:33,720
So it's so simple for inserting an element.

46
00:02:33,930 --> 00:02:35,910
Now I will insert next one more element.

47
00:02:35,920 --> 00:02:42,260
So just increment the pointer, move it to the next location and insert an element.

48
00:02:42,690 --> 00:02:47,650
So how much time it is taking for inserting an element just constant the time it is ticking and insert

49
00:02:47,660 --> 00:02:53,490
a few more elements are inserted, few more elements responding on the last element that is recently

50
00:02:53,490 --> 00:02:53,940
inserted.

51
00:02:53,940 --> 00:03:00,120
Element first element is that index zero but still front disappointing on minus one.

52
00:03:01,110 --> 00:03:08,070
Now, let us see how to delete for deleting an element, first of all, move front to our next location

53
00:03:08,340 --> 00:03:09,810
and take our back element.

54
00:03:10,020 --> 00:03:14,190
Delete that anyone so easy, just move around and take off.

55
00:03:14,220 --> 00:03:19,050
Then let us delete one more element, move on to the next location and delete that.

56
00:03:19,830 --> 00:03:20,750
This was very easy.

57
00:03:21,710 --> 00:03:28,140
How much time redistricting is taking Constantine just move front and delete the element so simple?

58
00:03:29,360 --> 00:03:33,430
Yes, the idea here is same as the example of what I have explained.

59
00:03:33,440 --> 00:03:37,180
You know, that companies is moving, the table is moving.

60
00:03:37,190 --> 00:03:39,090
People are standing in their own places.

61
00:03:39,410 --> 00:03:40,880
This country is moving forward.

62
00:03:41,660 --> 00:03:42,970
And one more thing to observe.

63
00:03:43,130 --> 00:03:49,310
I'm keeping FrontPoint before the first element, not upon the first element, whereas Red is pointing

64
00:03:49,310 --> 00:03:53,050
up on the last element from this pointing before the first element.

65
00:03:53,660 --> 00:03:58,580
So logically, if you see the counter, that counter will be upon the first person.

66
00:03:58,790 --> 00:04:00,610
It will be in front of the first person.

67
00:04:00,620 --> 00:04:02,430
So friend is acting like a counter.

68
00:04:02,870 --> 00:04:04,670
So it is pointing before the first element.

69
00:04:05,390 --> 00:04:11,990
So now both NQ and cooperation that is insert and delete option are taking constant time.

70
00:04:12,170 --> 00:04:14,040
So our cue has to begin faster.

71
00:04:14,690 --> 00:04:20,519
So with the help of two pointers, we can foster support and you and your operations are taking constant

72
00:04:20,600 --> 00:04:20,959
time.

73
00:04:21,750 --> 00:04:25,830
Now, let us look at the condition when you say Cuba is empty or full.

74
00:04:26,310 --> 00:04:30,920
Now, let us look at the condition when the U.S. crew is empty and when you U.S. useful.

75
00:04:31,350 --> 00:04:32,810
So I have a few elements here.

76
00:04:32,820 --> 00:04:34,200
I will give it a few more elements.

77
00:04:34,350 --> 00:04:35,820
I want to read one more element.

78
00:04:35,830 --> 00:04:39,210
So the method is move front and make it point here and delete the element.

79
00:04:39,510 --> 00:04:45,510
I want to delete one more element so the method is moved from the first and delete the element, that's

80
00:04:45,510 --> 00:04:45,700
all.

81
00:04:46,080 --> 00:04:47,540
Now there is nothing in the queue.

82
00:04:48,300 --> 00:04:49,410
Queue is empty.

83
00:04:49,660 --> 00:04:54,120
So what is the condition when queues empty front and where both are equal?

84
00:04:54,360 --> 00:05:00,090
So far, difficult to read is the condition for QM initially also confirm that they're pointing here

85
00:05:00,090 --> 00:05:02,090
at minus one they were equal.

86
00:05:02,190 --> 00:05:05,370
So whenever they are equal means it's empty.

87
00:05:05,850 --> 00:05:08,370
So the condition is a front is equal to that.

88
00:05:08,610 --> 00:05:10,210
So right now it is empty.

89
00:05:10,530 --> 00:05:12,150
We have to see full condition.

90
00:05:12,480 --> 00:05:14,510
But before that, I want to show you one thing.

91
00:05:15,090 --> 00:05:15,940
Just watch here.

92
00:05:16,560 --> 00:05:23,190
Suppose I am having already two elements, 10 and 12 and friend was pointing before the first.

93
00:05:23,630 --> 00:05:23,890
Right.

94
00:05:25,240 --> 00:05:27,790
Now, you would think that why not, it is pointing upon first.

95
00:05:28,100 --> 00:05:29,020
So let me do it.

96
00:05:29,590 --> 00:05:34,210
If it is pointing upon frustrating element, let us see this pointing from the beginning.

97
00:05:34,210 --> 00:05:38,050
It is pointing on first element that is that you might want to delete.

98
00:05:38,500 --> 00:05:39,900
So that element will be deleted.

99
00:05:40,180 --> 00:05:41,380
Then this one should go.

100
00:05:41,650 --> 00:05:44,500
It should go to the next element because this the next element.

101
00:05:44,510 --> 00:05:47,780
But isn't there there is no element present here though so friends should move.

102
00:05:47,920 --> 00:05:49,800
OK, friend has moved now.

103
00:05:49,810 --> 00:05:51,190
Is there any element available?

104
00:05:51,220 --> 00:05:52,750
Yes, there is an element available.

105
00:05:53,500 --> 00:05:53,780
Right.

106
00:05:54,670 --> 00:05:55,820
But is the cube empty?

107
00:05:55,870 --> 00:05:56,940
No, it's not empty.

108
00:05:57,220 --> 00:05:58,420
Then what is the condition.

109
00:05:59,080 --> 00:06:02,170
So therefore it is equal to then how it is not empty.

110
00:06:02,990 --> 00:06:04,180
So this is a problem.

111
00:06:04,780 --> 00:06:10,750
OK, if you say that front is equal to there is not an empty condition, that one more element is there.

112
00:06:10,930 --> 00:06:12,320
OK, delete that also.

113
00:06:12,580 --> 00:06:16,360
So if I do need this element then I should move to the next location.

114
00:06:16,370 --> 00:06:16,800
Yes.

115
00:06:17,470 --> 00:06:22,800
Now what is the conditional front has became greater than Reyher Nargus empty.

116
00:06:23,170 --> 00:06:29,890
So if you say that frontyard point upon first element, then you have to write this as a QM deconditioned.

117
00:06:29,890 --> 00:06:32,690
What does that front is greater than that.

118
00:06:33,490 --> 00:06:38,430
So if you want, you can implement it like this and you have to make a FrontPoint upon the first element.

119
00:06:38,800 --> 00:06:42,880
But I prefer that front pointing before the first element is more comfortable.

120
00:06:43,100 --> 00:06:44,550
So I am following that tag.

121
00:06:45,990 --> 00:06:49,730
Because it gives the meaningful condition that front is equal to.

122
00:06:50,220 --> 00:06:51,130
OK, is empty.

123
00:06:51,930 --> 00:06:56,100
Now, let us insert a few more elements in the queue and see what is the full condition.

124
00:06:57,210 --> 00:06:58,840
I want to insert one more element.

125
00:06:59,060 --> 00:07:01,070
There's no space because it's full.

126
00:07:01,110 --> 00:07:02,400
So how do you think it was full?

127
00:07:02,430 --> 00:07:03,260
What is the condition?

128
00:07:03,600 --> 00:07:07,530
Square is pointing on six and the size of seven or eight is equal to size.

129
00:07:07,530 --> 00:07:09,670
Minus one is a full condition.

130
00:07:09,960 --> 00:07:10,860
So here are right.

131
00:07:11,230 --> 00:07:12,420
Q Full condition.

132
00:07:13,500 --> 00:07:17,100
Skillfull condition is rated as equal to five minus one.

133
00:07:17,880 --> 00:07:23,040
So if you have studied how to implement a Q using three and two pointers, so two pointers initially

134
00:07:23,040 --> 00:07:24,230
they are at minus one.

135
00:07:24,240 --> 00:07:29,670
And we always use their pointer for inserting four pointer for deletion from quarter point before the

136
00:07:29,670 --> 00:07:35,460
first element, that location, we keep it empty and then sundancer both of them and they are the same

137
00:07:35,520 --> 00:07:36,750
place in the Q is empty.

138
00:07:36,750 --> 00:07:39,810
If Rita has reached the end of another then Q is a full.

139
00:07:40,900 --> 00:07:43,980
Now we will look at the implementation that is program code.

140
00:07:44,020 --> 00:07:44,740
We will look at.

