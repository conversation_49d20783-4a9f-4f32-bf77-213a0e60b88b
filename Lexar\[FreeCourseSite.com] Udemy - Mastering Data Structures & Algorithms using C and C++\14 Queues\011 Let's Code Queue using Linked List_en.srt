1
00:00:00,150 --> 00:00:06,420
In this video, let us look at implementation of a using the list so I'll create a new project that

2
00:00:06,420 --> 00:00:11,610
is due using <PERSON><PERSON><PERSON><PERSON> and the language Issie language.

3
00:00:14,100 --> 00:00:16,860
I'll create a project, project is ready.

4
00:00:18,330 --> 00:00:25,260
For implementation of using linked list, I should use Nords of a link, so for that I will define a

5
00:00:25,260 --> 00:00:26,890
structure that is Naude.

6
00:00:27,570 --> 00:00:33,540
So here the node is ready then I should have two pointers that is front and rear, so I will declare

7
00:00:33,540 --> 00:00:33,930
them here.

8
00:00:33,930 --> 00:00:35,370
Excel globally.

9
00:00:35,370 --> 00:00:40,950
Front is initially null as well as red is also initially null.

10
00:00:41,900 --> 00:00:43,730
So 2.5 initialised.

11
00:00:45,800 --> 00:00:52,040
Then I don't have to create a link less relevant than there are initially, no further initialization

12
00:00:52,040 --> 00:00:52,650
is required.

13
00:00:53,220 --> 00:00:57,150
No, I should have the functions like NQ dequeue and display three functions.

14
00:00:57,170 --> 00:01:03,560
I will write on first function and NQ, which takes the value to be inserted.

15
00:01:06,310 --> 00:01:10,140
Before inserting, I should make sure that there is enough space inside here.

16
00:01:10,800 --> 00:01:17,700
So first of all, I should create a node using temporary point at P, so I would write on the code for

17
00:01:18,240 --> 00:01:19,350
node creation.

18
00:01:21,930 --> 00:01:26,310
Using Mellark function, and when you use mellark function, make sure that.

19
00:01:27,980 --> 00:01:29,070
Libraries included.

20
00:01:29,090 --> 00:01:29,870
That is still the.

21
00:01:33,010 --> 00:01:34,090
Norris created.

22
00:01:35,560 --> 00:01:41,620
Then if not is not created, if it is equal to null, then Cure's is full.

23
00:01:41,620 --> 00:01:46,990
We cannot insert any element so we can say printf du is full.

24
00:01:48,220 --> 00:01:49,810
Else we can insert an element.

25
00:01:49,810 --> 00:01:50,860
So I'll give a new line.

26
00:01:51,780 --> 00:01:57,060
As we can insert an element for inserting an element already naughties created, so I should set the

27
00:01:57,060 --> 00:02:00,570
date of that Naude as X the value that we want to insert.

28
00:02:01,800 --> 00:02:07,470
And also, I should make it to the next panel, because this is going to be a last note order, definitely

29
00:02:07,590 --> 00:02:09,580
in a tube then.

30
00:02:09,870 --> 00:02:16,590
First thing I should check that a front is equal to a front, is equal to the first node, then front

31
00:02:16,590 --> 00:02:17,300
and rear.

32
00:02:17,370 --> 00:02:22,920
Both should point on that new order t otherwise this is not the first node.

33
00:02:23,220 --> 00:02:24,900
Then read appointers.

34
00:02:25,230 --> 00:02:31,740
Next point I should point on this new you that E and read should be brought upon this new node.

35
00:02:32,700 --> 00:02:34,140
So this what I have explained you.

36
00:02:34,470 --> 00:02:36,060
So the same thing I have written here.

37
00:02:37,810 --> 00:02:43,160
The necessary to dequeue function, dequeue function will return an element that is deleted.

38
00:02:43,180 --> 00:02:46,510
So for that, I will take a variable that is initialized with minus one.

39
00:02:47,260 --> 00:02:50,020
Then for deletion, I need a temporary pointer.

40
00:02:50,020 --> 00:02:51,790
So I will take a pointer to.

41
00:02:53,670 --> 00:02:59,910
Then before the election, I should check that the front is equal to none, if it is so, then two is

42
00:02:59,910 --> 00:03:00,350
empty.

43
00:03:00,360 --> 00:03:06,810
I should give a message that the queue is empty and also slashing so that I get a line gap.

44
00:03:07,350 --> 00:03:09,090
LSU is not empty.

45
00:03:09,120 --> 00:03:15,530
I can delete an element, so I should delete first elements or X should be assigned with the financial

46
00:03:15,540 --> 00:03:16,050
data.

47
00:03:16,320 --> 00:03:20,880
Then it should be pointing up on front so that we can delete a..

48
00:03:21,180 --> 00:03:24,660
Then FrontPoint or should be moved on the next node.

49
00:03:25,290 --> 00:03:28,100
Then we should free that pointer to.

50
00:03:29,910 --> 00:03:35,520
And at the end, I should return X, that's all this was dequeue.

51
00:03:37,020 --> 00:03:37,410
That.

52
00:03:39,360 --> 00:03:45,660
For display, I should write a function display for displaying, I should scan through a of for that

53
00:03:45,660 --> 00:03:52,980
I will take a temporary pointer P and while P is not equal to null, while e I can see simply.

54
00:03:54,250 --> 00:03:58,540
I should display all the elements of print, if I should display the data of a..

55
00:03:59,830 --> 00:04:01,270
That is BP's data.

56
00:04:02,730 --> 00:04:08,220
And then he should be moved to the next point and at the end I should give a line gap.

57
00:04:08,220 --> 00:04:12,440
So for that I will say again, no, everything is ready.

58
00:04:12,450 --> 00:04:13,740
I have all three functions.

59
00:04:13,740 --> 00:04:18,570
That is a dequeue and display function inside the main function.

60
00:04:18,570 --> 00:04:21,000
I do not have to create any variable for you directly.

61
00:04:21,000 --> 00:04:22,570
Again, start using the function.

62
00:04:22,590 --> 00:04:31,140
So first of all, I will call NQ and I will insert value and and and insert more elements by copy paste

63
00:04:31,290 --> 00:04:31,650
this.

64
00:04:32,720 --> 00:04:41,240
So I'll insert two elements, 20 and 30, then 40, then 50, then after this I will call a function

65
00:04:41,240 --> 00:04:43,970
that is a display which will display all the elements in an enclosed.

66
00:04:45,360 --> 00:04:52,950
Not on the top, I must include a header file that is include a steady lip edge.

67
00:04:53,400 --> 00:04:54,950
So back to the main function.

68
00:04:55,490 --> 00:04:56,850
No, let's run this one.

69
00:04:58,150 --> 00:05:04,270
Yes, it is displaying all elements, 10, 20, 30, 40, 50, see in display, this piece should start

70
00:05:04,270 --> 00:05:04,920
from fun.

71
00:05:05,050 --> 00:05:06,580
So I have a son and just not.

72
00:05:07,540 --> 00:05:11,630
Then let us call equals one directly displayed resolute.

73
00:05:12,040 --> 00:05:14,880
So here I will give you function.

74
00:05:15,850 --> 00:05:16,810
Let us run this.

75
00:05:18,480 --> 00:05:20,880
If dequeue is deleting the first element, then.

76
00:05:23,050 --> 00:05:27,820
So that's always the implementation of Q using Lychner so you can practice this program.

