1
00:00:00,390 --> 00:00:07,620
And this video we learn about rotations at the time of insertion, that is at the time of creation of

2
00:00:07,620 --> 00:00:14,490
a reality, when we are creating an Aviel training, setting any key in the area, then some doors may

3
00:00:14,490 --> 00:00:15,660
become imbalanced.

4
00:00:15,780 --> 00:00:17,270
So trade becomes imbalance.

5
00:00:17,580 --> 00:00:20,340
So to balance that, we perform rotations.

6
00:00:20,580 --> 00:00:25,410
So those rotations are named as a little rotation out of rotation.

7
00:00:25,410 --> 00:00:29,710
A lot and other locations will understand all these rotations one by one.

8
00:00:30,060 --> 00:00:31,630
So let us start with the first one.

9
00:00:31,800 --> 00:00:32,460
First one.

10
00:00:32,700 --> 00:00:33,730
A little rotation.

11
00:00:33,840 --> 00:00:36,900
A little stick of initiatory.

12
00:00:37,230 --> 00:00:39,560
So this is an initial three.

13
00:00:39,990 --> 00:00:41,510
What are the balance factors?

14
00:00:41,790 --> 00:00:46,080
See, this balance factor of this is zero and this is one and zero.

15
00:00:46,110 --> 00:00:49,470
So this is one minus zero is one for balance factor of this.

16
00:00:49,620 --> 00:00:50,100
This one.

17
00:00:55,330 --> 00:01:02,410
Then I want to insert a value 10, so after inserting 10 in this one, so very Pennsville come, it's

18
00:01:02,410 --> 00:01:03,720
less than 30, less than 20.

19
00:01:03,730 --> 00:01:04,819
So it will come on the site.

20
00:01:05,319 --> 00:01:08,080
So 30 grindy.

21
00:01:08,800 --> 00:01:11,380
And then what are the balance factors?

22
00:01:11,410 --> 00:01:12,270
This is zero.

23
00:01:12,640 --> 00:01:14,590
This is one minus zero.

24
00:01:14,620 --> 00:01:18,250
So one, this is a two minus a zero on this site.

25
00:01:18,270 --> 00:01:19,130
So this is two.

26
00:01:20,470 --> 00:01:23,770
So this node became imbalance of the balance factor.

27
00:01:23,770 --> 00:01:25,270
Four should not be more than one.

28
00:01:25,300 --> 00:01:26,830
So what the balance factor is two.

29
00:01:27,070 --> 00:01:30,400
So this node became imbalance now.

30
00:01:31,410 --> 00:01:37,500
We will give you the name for the imbalance when this note became imbalance because of insertion of

31
00:01:37,500 --> 00:01:37,870
10.

32
00:01:38,220 --> 00:01:44,330
So in which direction Dr. 10 inserted left off left El Al.

33
00:01:44,640 --> 00:01:48,830
So I will give Don Imus and Al imbalance.

34
00:01:49,290 --> 00:01:54,110
So this is a matter for LLN balance, how to perform rotation.

35
00:01:54,480 --> 00:01:56,430
Let us take the tree once again here.

36
00:01:56,700 --> 00:02:01,980
So you'll perform rotation around this node will perform rotation in the static.

37
00:02:03,390 --> 00:02:05,910
This direction, so what does it mean by rotation?

38
00:02:06,660 --> 00:02:13,560
Imagine that this is a trader, OK, keep your finger here and pull the trigger on this side, pull

39
00:02:13,560 --> 00:02:14,450
the trigger on the side.

40
00:02:14,670 --> 00:02:17,910
So if you pull the trigger, it will rotate around the finger.

41
00:02:18,180 --> 00:02:18,520
Right.

42
00:02:18,780 --> 00:02:26,670
So when it rotate around this finger, 30 will come on the side and 20 will move up at here and then

43
00:02:26,670 --> 00:02:29,310
will move a little above its position.

44
00:02:30,150 --> 00:02:31,500
So the tree looks like this.

45
00:02:32,710 --> 00:02:33,980
That's the meaning of rotation.

46
00:02:34,360 --> 00:02:41,170
Hold your finger and move this knob, so rotate around your finger, rotate around a fixed point.

47
00:02:41,560 --> 00:02:44,550
OK, so this is the rotated three.

48
00:02:45,700 --> 00:02:47,500
Let us check the balance factors.

49
00:02:47,680 --> 00:02:48,590
This is zero.

50
00:02:48,880 --> 00:02:49,640
This is zero.

51
00:02:49,810 --> 00:02:52,210
This is one minus one zero.

52
00:02:53,280 --> 00:02:59,460
A lot of violence around which, you know, we have rotated this north, so at that place, who is there?

53
00:02:59,460 --> 00:03:00,210
Not 20.

54
00:03:00,360 --> 00:03:01,590
Is it balanced or not?

55
00:03:01,740 --> 00:03:02,960
Yes, it became grindy.

56
00:03:03,150 --> 00:03:05,580
Yes, its balance factor became zero.

57
00:03:05,820 --> 00:03:10,650
So on whichever node you perform rotation, the balance factor should become zero for the next line.

58
00:03:11,670 --> 00:03:15,960
Earlier at that position, the balance factor was to not the same position.

59
00:03:15,960 --> 00:03:17,140
The balance factor is zero.

60
00:03:17,880 --> 00:03:21,270
So this is the procedure for rotation, not few important things.

61
00:03:21,540 --> 00:03:26,220
First, what should be the name of this rotation imbalance?

62
00:03:26,280 --> 00:03:28,700
Explain a little imbalance, right?

63
00:03:29,070 --> 00:03:36,160
So let us call the rotation also a little rotation, so I'll give them the names and then rotation.

64
00:03:36,870 --> 00:03:40,530
So based on the imbalance, I have given the rotation name on 11.

65
00:03:41,460 --> 00:03:42,600
Now you can say that.

66
00:03:42,750 --> 00:03:45,950
No, no, it is rotating on the right hand side.

67
00:03:46,350 --> 00:03:48,050
We are pulling it on the right hand side.

68
00:03:48,540 --> 00:03:49,900
It the right rotation.

69
00:03:50,340 --> 00:03:51,690
OK, that is also correct.

70
00:03:51,810 --> 00:03:57,500
Then somebody said that no, it is rotating like a clock so it is clockwise rotation.

71
00:03:57,930 --> 00:04:00,870
OK, call it as clockwise clockwise rotation.

72
00:04:01,940 --> 00:04:07,250
So you can give anything, there is no standard name for this one, right?

73
00:04:07,910 --> 00:04:09,100
This is just rotation.

74
00:04:09,320 --> 00:04:10,450
There is no standard name.

75
00:04:10,760 --> 00:04:12,380
So if you want, you can give any name.

76
00:04:12,680 --> 00:04:17,890
I have selected the names so that it is easy for understanding even the complex things.

77
00:04:18,260 --> 00:04:24,230
So I'm calling does Adalimumab, if I call it right, rotation or clockwise rotation.

78
00:04:24,230 --> 00:04:27,500
Maybe I may not be able to explain other things more clearly.

79
00:04:27,530 --> 00:04:29,260
That is, follow a little rotation.

80
00:04:29,480 --> 00:04:32,680
So this is after little rotation.

81
00:04:33,170 --> 00:04:35,330
So this is about name no second.

82
00:04:36,810 --> 00:04:42,240
Second important thing, you hear me thinking that I have taken just a small tree just with the tree

83
00:04:42,240 --> 00:04:42,780
nodes.

84
00:04:44,290 --> 00:04:50,110
What I should do with the tree is very big, so don't worry, rotations are always a done with the tree,

85
00:04:50,110 --> 00:04:58,810
not only always between us, even if a tree is having Totonno's nodes, also rotation is performed over

86
00:04:58,810 --> 00:04:59,710
tree, not me.

87
00:05:00,250 --> 00:05:02,440
So understand this clearly then.

88
00:05:02,440 --> 00:05:03,460
Two examples.

89
00:05:03,460 --> 00:05:06,570
I will show you how this is done on bigger psychometricians.

90
00:05:07,240 --> 00:05:11,140
So that's all about force rotation right now.

91
00:05:11,170 --> 00:05:16,140
Next, we will look at our our rotation for our rotation.

92
00:05:16,150 --> 00:05:19,990
This is initiatory balance factor, at least not a zero.

93
00:05:19,990 --> 00:05:22,010
And this one is zero minus one.

94
00:05:22,030 --> 00:05:23,350
So this is minus next.

95
00:05:23,650 --> 00:05:25,680
I will insert 13 this one.

96
00:05:25,840 --> 00:05:29,350
So 10 and 20 are already there.

97
00:05:29,350 --> 00:05:31,720
So 20 is greater than 10 and greater than 20.

98
00:05:31,720 --> 00:05:35,100
So it comes on this side, not a balance factor of this one zero.

99
00:05:35,110 --> 00:05:37,320
This is zero minus one, minus one.

100
00:05:37,570 --> 00:05:38,830
This is zero minus two.

101
00:05:38,830 --> 00:05:39,970
So this is minus two.

102
00:05:40,180 --> 00:05:43,000
Minus two is not a valid balance factor.

103
00:05:43,390 --> 00:05:45,910
Balance factor cannot be less than minus one.

104
00:05:46,180 --> 00:05:47,190
This is minus two.

105
00:05:47,320 --> 00:05:49,360
So this note imbalance.

106
00:05:49,690 --> 00:05:51,340
So why it became imbalanced?

107
00:05:51,340 --> 00:05:55,480
Because we have inserted 30 just now where we have inserted.

108
00:05:55,720 --> 00:05:56,160
Right.

109
00:05:56,170 --> 00:05:56,880
Chernof Right.

110
00:05:57,550 --> 00:05:57,970
Right.

111
00:05:57,970 --> 00:05:59,460
It is Rachel of Rachel.

112
00:05:59,480 --> 00:06:03,910
So Orara so let us call this size are imbalance.

113
00:06:04,420 --> 00:06:06,280
Now let us see how to perform rotation.

114
00:06:06,280 --> 00:06:07,070
So I'll agree.

115
00:06:07,180 --> 00:06:13,030
I'll take a three once again perform rotation around this in this direction.

116
00:06:13,360 --> 00:06:17,620
So it means hold your finger here and pull the stand on the side.

117
00:06:17,770 --> 00:06:24,760
So if you pull down on the side, then ten comes here, twenty will take the place of ten and 30 will

118
00:06:24,760 --> 00:06:26,200
move up a little bit.

119
00:06:26,440 --> 00:06:27,720
Not a balance factor.

120
00:06:27,880 --> 00:06:28,690
This is zero.

121
00:06:28,690 --> 00:06:29,440
This is zero.

122
00:06:29,650 --> 00:06:30,910
And this is also zero.

123
00:06:31,870 --> 00:06:38,260
So it's perfectly balanced now what should be the name of this rotation, as I said, this is our imbalance,

124
00:06:38,260 --> 00:06:41,080
so let us call this as our obligation only.

125
00:06:41,380 --> 00:06:43,640
So this is our vacation.

126
00:06:44,980 --> 00:06:47,610
So this is after our vacation.

127
00:06:48,310 --> 00:06:51,520
If you say no, no, this is left probation, OK?

128
00:06:51,520 --> 00:06:52,120
You call it.

129
00:06:52,360 --> 00:06:56,460
If you say no, no, this is counterclockwise rotation, OK, we can call it.

130
00:06:57,280 --> 00:06:58,630
You can give any name you like.

131
00:06:59,700 --> 00:07:05,550
That's all but one important thing in a little location, the final result was this one previous rotation

132
00:07:05,910 --> 00:07:07,390
and this audition also seemed.

133
00:07:07,990 --> 00:07:13,310
Yes, in all four patients with the three Nords, we get the same result.

134
00:07:13,350 --> 00:07:14,370
That is a balance.

135
00:07:14,400 --> 00:07:20,060
Three now mixed rotation we look at that is allowed for a rotation.

136
00:07:20,460 --> 00:07:21,660
This is the initial treat.

137
00:07:22,440 --> 00:07:24,270
Let us insert in this one.

138
00:07:24,570 --> 00:07:31,100
So right now, three's having 30 and 10, 20 is less than 30.

139
00:07:31,110 --> 00:07:32,370
So it will come on left side.

140
00:07:32,640 --> 00:07:35,550
But it is a great event and so it will come on the right side.

141
00:07:36,150 --> 00:07:38,490
One of the balance factors, this is zero.

142
00:07:38,770 --> 00:07:40,500
This is zero minus one.

143
00:07:40,500 --> 00:07:41,280
So minus one.

144
00:07:41,530 --> 00:07:43,920
This is one two, minus two.

145
00:07:44,160 --> 00:07:45,570
So this is two.

146
00:07:46,820 --> 00:07:47,050
Boom.

147
00:07:48,020 --> 00:07:54,620
This note became imbalance, this is imbalance, which direction inflation is done just now instead

148
00:07:54,620 --> 00:07:55,790
of 20 on which side?

149
00:07:56,120 --> 00:08:02,460
Left, right, so left, right, left and right are imbalance.

150
00:08:02,480 --> 00:08:07,350
So let us call this as an imbalance and how the rotation should be performed.

151
00:08:07,610 --> 00:08:09,160
I will read all three here.

152
00:08:09,680 --> 00:08:14,170
So Phosphine perform rotation around the snored like this.

153
00:08:14,750 --> 00:08:15,940
So what will be the result?

154
00:08:17,290 --> 00:08:21,340
30 grindy and 10.

155
00:08:22,400 --> 00:08:27,780
Right, Bolton holding a finger here, Bolton then comes the side, 20 moves up.

156
00:08:28,010 --> 00:08:36,000
So this is how the tree looks like the balance factors of zero one and two now perform rotation around

157
00:08:36,030 --> 00:08:37,640
this node like this.

158
00:08:38,390 --> 00:08:39,299
What is the result?

159
00:08:40,159 --> 00:08:41,490
Same old result.

160
00:08:41,510 --> 00:08:47,910
So 20 in the road, 10 and 30, c 30 on the side.

161
00:08:47,930 --> 00:08:51,110
So 20 will move up today, will come down and tunnels will move up.

162
00:08:51,380 --> 00:08:54,800
The balance factors are zero zero zero.

163
00:08:56,310 --> 00:09:01,210
What do you call do this, an rotation and aggravation?

164
00:09:02,360 --> 00:09:03,850
This is also an art.

165
00:09:04,380 --> 00:09:09,360
If you want to give the name based on the direction of rotation, just watch it left rotation, then

166
00:09:09,360 --> 00:09:10,150
right rotation.

167
00:09:10,170 --> 00:09:12,670
So this is a lot only with that perspective also.

168
00:09:13,020 --> 00:09:14,910
So this is a large rotation.

169
00:09:15,090 --> 00:09:19,690
See, this is one rotation only, but there are two steps in this one.

170
00:09:20,580 --> 00:09:23,070
So we call this as double rotation.

171
00:09:23,370 --> 00:09:25,000
We call it a double rotation.

172
00:09:25,710 --> 00:09:27,360
Later, I will talk about this.

173
00:09:27,600 --> 00:09:29,790
Looks a little complex how to do this.

174
00:09:29,940 --> 00:09:32,770
So I'll show you a direct method for this one.

175
00:09:33,300 --> 00:09:35,230
Let us take this story once again here.

176
00:09:35,250 --> 00:09:40,860
So this study should move this site route should move this site and this newly inserted node should

177
00:09:40,860 --> 00:09:42,330
go in place of route.

178
00:09:43,340 --> 00:09:45,680
So 20 will sleep on the site, right?

179
00:09:45,710 --> 00:09:48,590
It will come on the site, Turkey will move towards right.

180
00:09:49,400 --> 00:09:49,870
Grindy.

181
00:09:49,880 --> 00:09:52,670
When it went on, the road went on, right?

182
00:09:52,970 --> 00:09:56,970
Well, inside of those steps, we can see it as a single step.

183
00:09:57,200 --> 00:10:00,700
So you have to send this node there and move that node on its side.

184
00:10:01,100 --> 00:10:02,500
So that's all about a lot.

185
00:10:02,780 --> 00:10:04,940
Then I will explain you quickly.

186
00:10:04,940 --> 00:10:08,240
You have understood how these are working for our location.

187
00:10:08,270 --> 00:10:11,800
This is the initial three people in Turkey right now.

188
00:10:11,810 --> 00:10:14,640
We will insert a new element, Brendin, this one.

189
00:10:15,290 --> 00:10:21,920
So if we are going to bring this greater than 10, but less than 30, so it comes to the site, then

190
00:10:21,920 --> 00:10:25,950
the balance factors are zero one and this is minus two.

191
00:10:26,210 --> 00:10:27,580
So this is became imbalance.

192
00:10:27,740 --> 00:10:28,670
Which imbalance?

193
00:10:28,730 --> 00:10:29,670
Right, left.

194
00:10:29,990 --> 00:10:32,940
So are L imbalance.

195
00:10:33,200 --> 00:10:35,090
So in short, they are an imbalance.

196
00:10:35,450 --> 00:10:37,950
And Wilmerding, see, we get that result only.

197
00:10:37,970 --> 00:10:39,310
So that's why I did not remove it.

198
00:10:39,320 --> 00:10:40,940
We get that result only always.

199
00:10:40,940 --> 00:10:43,070
We are getting similar for three nodes anyway.

200
00:10:43,070 --> 00:10:46,650
We have to perform rotation on this one, so I'll take a treat once again.

201
00:10:46,670 --> 00:10:54,130
But in fact inside zero one and minus two, perform rotation, perform rotation over this node first.

202
00:10:54,470 --> 00:11:00,830
So then another display's comes 20 and on the right side it will be 30.

203
00:11:01,250 --> 00:11:07,160
So the balance factor side of zero minus one and minus two then perform rotation around this node,

204
00:11:07,340 --> 00:11:07,970
this node.

205
00:11:08,240 --> 00:11:13,460
We get that project, then the spotlight down twenty comes here today, also moves up.

206
00:11:14,330 --> 00:11:21,500
We get that tree, if you want to see a direct method, then this should be sent as a road or a parent

207
00:11:21,500 --> 00:11:23,450
and they should move on left side.

208
00:11:23,750 --> 00:11:27,330
So, again, this is also the location, right?

209
00:11:27,710 --> 00:11:29,330
Because there are two steps, actually.

210
00:11:29,330 --> 00:11:30,200
These are the steps.

211
00:11:30,200 --> 00:11:32,840
But for understanding, we take that one.

212
00:11:34,580 --> 00:11:37,620
So whichever you are comfortable with, you can follow that one.

213
00:11:37,910 --> 00:11:39,830
That's all about four auditions.

214
00:11:39,960 --> 00:11:46,970
So let me die down the rotations we have seen first as a little rotation and then our other rotation.

215
00:11:46,980 --> 00:11:48,050
These are called us.

216
00:11:49,180 --> 00:11:50,560
Single rotation's.

217
00:11:52,140 --> 00:11:58,380
These were having single step and a lot of are calling us double rotation, but don't count.

218
00:11:58,380 --> 00:12:01,950
Desire's two rotations count as one only.

219
00:12:02,460 --> 00:12:07,370
See, there is a difference between double and two, so don't count them as two rotation.

220
00:12:07,830 --> 00:12:12,210
This rotation, if I take a lot less one rotation, but it is having to step.

221
00:12:12,210 --> 00:12:15,090
So it is double capacity as double sizes.

222
00:12:15,090 --> 00:12:20,760
Double number of steps are double prosodics, this one only, but his size is double.

223
00:12:21,270 --> 00:12:25,220
So that's all about rotations, not out of four rotations.

224
00:12:25,230 --> 00:12:30,330
I'll give you one observation that may be very useful for you that would be logically useful for you

225
00:12:30,330 --> 00:12:31,250
observed this one.

226
00:12:31,470 --> 00:12:34,020
So the four rotations, what is the observation?

227
00:12:34,170 --> 00:12:34,820
Look at here.

228
00:12:35,100 --> 00:12:37,230
So using three keys or three.

229
00:12:37,230 --> 00:12:41,720
Knaus So I have a three notes.

230
00:12:41,730 --> 00:12:44,490
That is three keys using three keys.

231
00:12:44,490 --> 00:12:46,050
How many different binaries.

232
00:12:46,050 --> 00:12:55,580
First teams we can form five how to NCM man plus one number of binary trees we can create out of Nickie's.

233
00:12:55,860 --> 00:12:58,640
So four three five trees we can create.

234
00:12:59,130 --> 00:13:00,060
So what are those.

235
00:13:00,060 --> 00:13:01,050
How they look like.

236
00:13:01,080 --> 00:13:01,920
I will draw them.

237
00:13:02,310 --> 00:13:05,400
See these are the five trees out of three keys.

238
00:13:05,400 --> 00:13:09,990
We can generate five different binary search trees.

239
00:13:10,380 --> 00:13:12,600
Two plus one five are possible.

240
00:13:13,660 --> 00:13:14,590
What is the height?

241
00:13:14,980 --> 00:13:21,300
Zero one two zero one two zero one zero one two zero one oh, that is of minimum height.

242
00:13:23,530 --> 00:13:31,210
So for trees of larger height, and that is of smaller height advertisements, hide, balance, binary

243
00:13:31,210 --> 00:13:33,670
search tree, so which is more height balance.

244
00:13:33,940 --> 00:13:38,540
So when you have three keys, you can draw a binary search of any shape or a lot of money.

245
00:13:38,630 --> 00:13:39,460
So is only.

246
00:13:40,590 --> 00:13:46,420
So if you have this one, why don't you take that one, change this one to that one, how to change?

247
00:13:46,440 --> 00:13:48,380
We are saying that we are performing rotation.

248
00:13:48,900 --> 00:13:56,550
We are seeing that we are performing rotation or this one or this one are double rotation rate first

249
00:13:56,550 --> 00:13:57,130
on this one.

250
00:13:57,150 --> 00:13:59,520
Next on this one, our double rotation.

251
00:13:59,520 --> 00:14:00,390
First on this one.

252
00:14:00,630 --> 00:14:01,570
Then on this one.

253
00:14:02,100 --> 00:14:08,910
So actually, we have transformed these four threes introductory because that is high to balance and

254
00:14:08,910 --> 00:14:09,860
how we transform.

255
00:14:10,050 --> 00:14:12,060
We are seeing that we have performed rotation.

256
00:14:13,300 --> 00:14:19,930
So this is the idea behind Aviatrix, so idea originated from here when you can have five different

257
00:14:19,930 --> 00:14:20,390
shapes.

258
00:14:20,710 --> 00:14:27,430
Why don't you select that ship even if you got this one modified, even if you got this one modified

259
00:14:27,640 --> 00:14:28,380
that ship.

260
00:14:28,390 --> 00:14:33,460
So this all about rotations at the time of creation at the time of insertion.

261
00:14:34,180 --> 00:14:38,920
Next, we will see the examples if Autrey's bigger, how rotation is performed.

262
00:14:39,370 --> 00:14:41,800
I'll discuss various things in that topic.

