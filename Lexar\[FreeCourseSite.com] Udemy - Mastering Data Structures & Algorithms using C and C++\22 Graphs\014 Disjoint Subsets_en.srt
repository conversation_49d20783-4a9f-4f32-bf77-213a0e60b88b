1
00:00:01,350 --> 00:00:07,480
The topic is a disjoined substance, this is useful for detecting cycles in the graph.

2
00:00:07,500 --> 00:00:12,990
So this is useful in cross-cultural gardam studies and uncovering the topic before Elcano.

3
00:00:14,430 --> 00:00:17,590
Let us understand, what does it mean by destroying subsect?

4
00:00:18,030 --> 00:00:25,680
So if you have a universal concept that is 123456, then I have taken an example of a universal collection

5
00:00:25,680 --> 00:00:27,080
of elements on there.

6
00:00:28,020 --> 00:00:29,370
I've heard that subsect.

7
00:00:29,430 --> 00:00:33,110
I have to substitute it and just look at the pics.

8
00:00:33,120 --> 00:00:39,990
What I'm shooting like A and B, if you check the numbers here, three five nine four seven eight 10.

9
00:00:40,290 --> 00:00:43,510
If you take the intersection of these two intersections, fine.

10
00:00:43,890 --> 00:00:51,470
So if you have subsects aid and if you in the section that said we had this funny but destroying subsect

11
00:00:51,480 --> 00:00:56,400
means, if you take the intersection, there's no common element on that.

12
00:00:57,600 --> 00:01:00,690
Now we use this for detecting cycles.

13
00:01:00,690 --> 00:01:02,640
So we need some data structure, 40 percent.

14
00:01:02,940 --> 00:01:10,200
So how does we will see the data structure that is the memory, as well as a diagrammatically presentation

15
00:01:10,230 --> 00:01:11,800
subroutines we have to focus on.

16
00:01:12,300 --> 00:01:12,990
So let us see.

17
00:01:13,680 --> 00:01:15,720
This is a universal set of 10 elements.

18
00:01:15,910 --> 00:01:21,730
I assume that every element is a sick existence except for the fact.

19
00:01:22,080 --> 00:01:30,980
So for each there is no one to draw on here then NATO and how to show that itself as a negative value

20
00:01:31,050 --> 00:01:32,270
because it's a center.

21
00:01:32,310 --> 00:01:37,250
So we use this notation that negative numbers shows that Exocet are ahead of us.

22
00:01:38,520 --> 00:01:42,120
For every number in an array you find it's minus four, right?

23
00:01:42,420 --> 00:01:44,460
So ignore zero because I'm not using zero.

24
00:01:44,460 --> 00:01:46,870
We have this number one on one, so ignore this one.

25
00:01:47,160 --> 00:01:53,160
So all these other minus one, be sure that each element is a separate like who's the head or parent

26
00:01:53,160 --> 00:01:53,610
of one.

27
00:01:53,610 --> 00:01:56,600
It's a two fifths of three and so on.

28
00:01:57,440 --> 00:02:03,030
No one else come to the next ignore from here you can understand but except meaning you can get from

29
00:02:03,030 --> 00:02:03,290
here.

30
00:02:03,870 --> 00:02:08,410
Now I have set and set B look at seven three five nine.

31
00:02:08,550 --> 00:02:12,580
So this means that these three elements together are forming a set.

32
00:02:13,020 --> 00:02:17,650
So in this we have to select one of the agreement by the parent or head of that section.

33
00:02:18,420 --> 00:02:25,140
So it's one element out of this list that I have selected three as a head and a five and a nine are

34
00:02:25,140 --> 00:02:32,490
pointing on that one to show that that spirit or it means we belong to a sect where the three is also

35
00:02:32,490 --> 00:02:34,320
present that is limiting.

36
00:02:35,100 --> 00:02:38,100
So three five nine, they belong to the same set.

37
00:02:38,850 --> 00:02:40,680
Now, here everything was minus one.

38
00:02:40,690 --> 00:02:41,890
How many present here?

39
00:02:42,180 --> 00:02:43,290
So I'll show it here.

40
00:02:43,290 --> 00:02:45,930
I have an empty idea that same right now.

41
00:02:45,930 --> 00:02:53,130
I have to show this set a of for showing that to five and nine are shown as children of three six five.

42
00:02:53,220 --> 00:02:57,840
I will write the three and four nine also I will write to test three.

43
00:02:58,200 --> 00:03:03,260
Then at the three I have to write some negative value to show that this is a head or a bit of a set.

44
00:03:03,570 --> 00:03:03,830
So I.

45
00:03:03,960 --> 00:03:04,290
Right.

46
00:03:04,320 --> 00:03:07,960
Minus three minus this negative.

47
00:03:07,980 --> 00:03:09,180
OK, what is this three.

48
00:03:09,480 --> 00:03:12,260
This three means total elements in this one.

49
00:03:12,270 --> 00:03:12,480
Yes.

50
00:03:12,500 --> 00:03:13,610
There are three elements.

51
00:03:13,950 --> 00:03:19,380
Next, if you look at the set for seven, eight, nine that I put on four elements, I have selected

52
00:03:19,380 --> 00:03:20,460
four as a header.

53
00:03:21,000 --> 00:03:21,320
Right.

54
00:03:21,450 --> 00:03:25,500
That number is four and the seven, eight and ten are pointing one before.

55
00:03:26,490 --> 00:03:31,890
So four is a pattern or head and seven, nine, ten that we belong to select.

56
00:03:31,890 --> 00:03:33,450
Four we belong to set forth.

57
00:03:33,750 --> 00:03:36,360
So seven, eight and ten here.

58
00:03:36,360 --> 00:03:43,710
I will write on four, four, four and therefore I have to write some negative number minus one.

59
00:03:43,980 --> 00:03:46,860
So just writing minus one is not much useful.

60
00:03:46,860 --> 00:03:53,250
So the total number of N in this one now here problem of note are also for survival guide.

61
00:03:53,250 --> 00:03:54,930
For this four doesn't mean this four.

62
00:03:54,930 --> 00:03:55,190
Right.

63
00:03:55,410 --> 00:04:02,460
This four means the number of N so third element of notes are also here for this sake, this is how

64
00:04:02,670 --> 00:04:05,060
we represent sets internally.

65
00:04:05,340 --> 00:04:11,100
So from that universal set, we have to be added one subset, even some say to me, so these elements

66
00:04:11,100 --> 00:04:12,750
are like three, three, three.

67
00:04:12,750 --> 00:04:21,149
These are forming a subset that is eight and this four and the seven and eight and ten are forming separate.

68
00:04:21,149 --> 00:04:21,750
That is me.

69
00:04:22,079 --> 00:04:22,680
Do the talking.

70
00:04:22,710 --> 00:04:25,140
So what about the rest of the elements of the are not.

71
00:04:25,140 --> 00:04:25,770
You didn't do that.

72
00:04:25,770 --> 00:04:28,740
So let us put all of them as minus one, D minus one.

73
00:04:29,640 --> 00:04:36,690
So that's all just the representation of disjoint subsets in the universe on second one this week by

74
00:04:36,690 --> 00:04:44,250
foreign corporations that are union and fine union opposition and final issue.

75
00:04:44,550 --> 00:04:46,590
Let us look at these operations.

76
00:04:46,590 --> 00:04:51,900
And union is also called as the union and the final score that's collapsing.

77
00:04:51,900 --> 00:04:52,350
Fine.

78
00:04:52,590 --> 00:04:53,250
So I'll explain.

79
00:04:53,250 --> 00:04:53,910
What does it mean?

80
00:04:54,150 --> 00:04:55,260
I remove the censure.

81
00:04:55,300 --> 00:04:58,740
You let us say I want to perform in union.

82
00:04:58,740 --> 00:04:59,830
We perform a.

83
00:04:59,990 --> 00:05:01,520
And this is not a operation.

84
00:05:01,910 --> 00:05:09,370
Now, if we perform union, the set of elements will be three, four, five, seven, eight, nine and

85
00:05:09,380 --> 00:05:09,710
10.

86
00:05:10,430 --> 00:05:14,490
These two are coming together and it's not uncommon for nothing.

87
00:05:14,510 --> 00:05:15,260
Is this correct?

88
00:05:15,830 --> 00:05:22,340
Now, how are you short either medically, medically have to make any one assumption that only one as

89
00:05:22,340 --> 00:05:22,730
a parent.

90
00:05:22,760 --> 00:05:23,360
Not good.

91
00:05:23,780 --> 00:05:25,260
So which one shall I make?

92
00:05:25,260 --> 00:05:27,650
The three as a parent for all or for as a parent?

93
00:05:27,650 --> 00:05:31,020
For whoever is having water in my stomach.

94
00:05:31,070 --> 00:05:32,630
That one has been.

95
00:05:33,020 --> 00:05:34,060
That's what they did.

96
00:05:34,110 --> 00:05:35,910
Union that's made the union.

97
00:05:36,200 --> 00:05:37,670
So this is having more elements.

98
00:05:37,670 --> 00:05:38,990
So that is put forward there.

99
00:05:39,170 --> 00:05:45,530
And Ford is only having seven and eight and not 10.

100
00:05:46,370 --> 00:05:52,480
Then the street, along with its children three will be pointing on this one point on this.

101
00:05:52,850 --> 00:05:55,070
So this is five and this is nine.

102
00:05:56,300 --> 00:06:00,560
So all these Norzai joined together, they are looking they are coming back.

103
00:06:01,100 --> 00:06:01,420
All right.

104
00:06:01,580 --> 00:06:05,930
Enough for these very different two different sets they want to take on.

105
00:06:06,170 --> 00:06:08,410
Now, I don't think that every single step.

106
00:06:08,640 --> 00:06:15,000
Now, let's see how this is done in action and performance and position.

107
00:06:15,290 --> 00:06:19,730
So what we do is who is better parent of set a three three.

108
00:06:19,910 --> 00:06:20,180
Right.

109
00:06:20,420 --> 00:06:23,240
Who is the parent of this set be fought for?

110
00:06:23,630 --> 00:06:27,330
So this a three or four, which is a smaller negative number.

111
00:06:27,350 --> 00:06:27,570
Right.

112
00:06:27,650 --> 00:06:28,280
Negative number.

113
00:06:28,490 --> 00:06:29,420
This is minus four.

114
00:06:29,420 --> 00:06:34,520
Is a smaller minister just having more elements or if you take positive, who is the greater effort

115
00:06:34,520 --> 00:06:35,090
is better.

116
00:06:35,090 --> 00:06:36,440
So that is having more elements.

117
00:06:36,440 --> 00:06:41,780
So what we do is the parent of the said that have four here.

118
00:06:42,440 --> 00:06:49,770
So this to me, is that my be this for then what about five, five, six that my better.

119
00:06:49,780 --> 00:06:51,200
It is three and three say two that.

120
00:06:51,200 --> 00:06:51,920
My goodness.

121
00:06:51,920 --> 00:06:55,310
Four now this was minus three and this is minus four.

122
00:06:55,310 --> 00:06:56,990
Totally this minus seven.

123
00:06:57,410 --> 00:06:59,320
So we add all that together.

124
00:06:59,540 --> 00:07:05,570
So this means that four is having seven or seven elements of that set forward.

125
00:07:05,960 --> 00:07:10,140
So if you watch here, one, two, three, four, five, six, seven, eight, they're not.

126
00:07:10,170 --> 00:07:14,780
If you check who is the parent of four, four, five, five, put on the street three Spooner's four.

127
00:07:14,780 --> 00:07:16,700
So the period of five is forward.

128
00:07:16,700 --> 00:07:20,630
Parent of nine is for that is the forward estimates for seven adults.

129
00:07:20,640 --> 00:07:21,050
Four.

130
00:07:21,350 --> 00:07:23,210
So this is how union is done.

131
00:07:23,540 --> 00:07:31,650
So I was writing a simple code for this one here for performing union Union of Head of State.

132
00:07:31,670 --> 00:07:32,510
Let us see you.

133
00:07:32,840 --> 00:07:37,430
And head of that is let us see ahead of us shared history.

134
00:07:37,430 --> 00:07:40,490
And this is for us, the performing union on this.

135
00:07:40,490 --> 00:07:46,550
That is three and four so far performing this one, I should check whose value is the smaller.

136
00:07:46,760 --> 00:07:53,480
Actually, this negative space should check for smaller, if that's all for you.

137
00:07:53,480 --> 00:07:57,590
It's less than as of the that will be compared.

138
00:07:57,890 --> 00:07:58,280
That's off.

139
00:07:58,280 --> 00:08:04,880
You will be comparing, but it's all for you with the ground on the total value of X minus three, minus

140
00:08:04,880 --> 00:08:05,120
four.

141
00:08:05,120 --> 00:08:06,440
We added and brought here.

142
00:08:06,440 --> 00:08:06,850
Right.

143
00:08:06,860 --> 00:08:16,550
So that is S for you plus of and then we will change as Sothebys Barend does not you.

144
00:08:17,000 --> 00:08:18,010
So we'll make you better.

145
00:08:18,060 --> 00:08:19,900
Better if you is a good idea.

146
00:08:20,930 --> 00:08:24,050
Ls s off.

147
00:08:24,090 --> 00:08:29,020
We, we will add these two and right then that's all for you.

148
00:08:29,430 --> 00:08:37,159
You and to get that right then next if s off you will change to me.

149
00:08:38,510 --> 00:08:44,660
So that's what see this won't have any less number of elements of Ford became better otherwise we can

150
00:08:44,660 --> 00:08:45,200
make it there.

151
00:08:45,210 --> 00:08:51,470
We also like the three will be Barrentine for comes out this and under this we will have a seven and

152
00:08:51,470 --> 00:08:58,640
eight and then right and here we will have to that five and nine.

153
00:09:00,410 --> 00:09:04,990
But we won't, we don't do this because who's having more element for us having more overlap.

154
00:09:05,480 --> 00:09:08,840
Otherwise the three will become better if three three more.

155
00:09:09,140 --> 00:09:11,540
So that's how it looks like if three becomes better.

156
00:09:11,540 --> 00:09:12,320
I have shown you.

157
00:09:12,620 --> 00:09:13,580
But this is correct.

158
00:09:13,700 --> 00:09:15,020
So this is wrong.

159
00:09:15,170 --> 00:09:16,430
We don't need this.

160
00:09:17,000 --> 00:09:18,020
Not back on this one.

161
00:09:18,260 --> 00:09:19,550
This is a union operation.

162
00:09:19,550 --> 00:09:19,820
So.

163
00:09:20,060 --> 00:09:22,440
Alisi Farsi language function beginning.

164
00:09:22,460 --> 00:09:24,050
We understand we can see this.

165
00:09:24,050 --> 00:09:30,440
Why the union if you perform union on this one, so one will be made as part of another and the total

166
00:09:30,480 --> 00:09:35,050
loans are stored in the period discussion in operation.

167
00:09:35,540 --> 00:09:36,830
No one important.

168
00:09:37,460 --> 00:09:40,450
How do we know that there is a second system?

169
00:09:40,490 --> 00:09:42,980
Almost nobody says what is says.

170
00:09:43,400 --> 00:09:45,020
Let's say five and ten.

171
00:09:45,620 --> 00:09:50,690
Five along with set it is for ten long period of four.

172
00:09:50,960 --> 00:09:53,360
Oh they already are in a single set.

173
00:09:53,630 --> 00:09:55,890
If you will join them it will form a second.

174
00:09:56,270 --> 00:09:59,300
If there is an age from five to ten then if you are joining.

175
00:09:59,980 --> 00:10:07,210
It follows that is used for the purpose of making success, see, if you did in this case, in this

176
00:10:07,210 --> 00:10:12,070
case, find five to find 10, for they are in different sects.

177
00:10:12,880 --> 00:10:17,770
We can connect them to five to 10 if they belong to the same sect.

178
00:10:17,950 --> 00:10:19,490
Don't collect five and 10.

179
00:10:19,510 --> 00:10:25,120
It was almost like that said, there's a symbol for somebody who did it, whether it was like it or

180
00:10:25,130 --> 00:10:26,740
not, this is huge.

181
00:10:27,190 --> 00:10:31,060
So how do you explain your union not justifying what is this?

182
00:10:31,060 --> 00:10:31,510
Fine.

183
00:10:31,870 --> 00:10:37,810
Fine means you want to find out who is the parent of any more period of five.

184
00:10:38,410 --> 00:10:45,580
Suffice, but it is three days, but it is for sole parent paphitis for parent of nine nine three three

185
00:10:45,580 --> 00:10:52,930
years for a of nice for so find out the parent to stop motion or group more so from here if you see

186
00:10:53,260 --> 00:10:59,830
nine six three three six four Fawzia's minus seven means Ford is the parent minus negative.

187
00:10:59,830 --> 00:11:00,520
So it's a parent.

188
00:11:00,670 --> 00:11:07,330
Oh this is fine operation firing the ten ten says what if you check fence's for them before this is

189
00:11:07,330 --> 00:11:08,830
negative for the parent.

190
00:11:09,160 --> 00:11:12,190
So find out assignments, finding out who is a parent.

191
00:11:12,640 --> 00:11:15,010
Alright, so let us see this operation.

192
00:11:15,100 --> 00:11:19,660
How to find four to find out how to function here.

193
00:11:19,960 --> 00:11:21,280
Find you.

194
00:11:21,490 --> 00:11:25,930
It means find the names are also the researcher before the show.

195
00:11:25,930 --> 00:11:30,820
That name belongs to a set whose leader or parent is four.

196
00:11:31,120 --> 00:11:37,030
So how you than any Google name keep them out of here as often name Google name.

197
00:11:37,030 --> 00:11:43,300
Take this off night and treat as softly go before it gets off four.

198
00:11:43,540 --> 00:11:45,850
And if it is negative, stop and take this one.

199
00:11:45,850 --> 00:11:47,350
Don't pick the biggest one.

200
00:11:47,710 --> 00:11:48,170
That's OK.

201
00:11:48,490 --> 00:11:54,310
So we have to more taking the value of us until we get the negative number right.

202
00:11:55,030 --> 00:11:57,010
As long as we are getting a positive number.

203
00:11:57,550 --> 00:12:03,060
So far that I have a particular variable X and I have a saying that, you know, with the help of extended

204
00:12:03,060 --> 00:12:11,390
do so, this can be done using my loop line as Saffa X is greater than zero positive.

205
00:12:11,830 --> 00:12:14,050
What to do inside X?

206
00:12:14,710 --> 00:12:16,750
I find the value itself.

207
00:12:17,050 --> 00:12:19,320
It's not so simple.

208
00:12:19,780 --> 00:12:26,110
So if you press this now, if I say find a nine, then X is nine.

209
00:12:26,290 --> 00:12:26,680
Right.

210
00:12:27,220 --> 00:12:29,360
A softening it's redundancy.

211
00:12:29,620 --> 00:12:30,430
Yes, three.

212
00:12:30,970 --> 00:12:35,350
Then X assign a suffix as suffixes for three.

213
00:12:35,650 --> 00:12:36,970
So X becomes three.

214
00:12:37,150 --> 00:12:39,100
No next time X becomes three.

215
00:12:39,790 --> 00:12:41,850
So as soft these include zero.

216
00:12:41,890 --> 00:12:45,760
Yes it is for then as Salvatore's.

217
00:12:45,760 --> 00:12:46,490
What for.

218
00:12:46,630 --> 00:12:50,650
So this X becomes four to four then a soft four.

219
00:12:50,650 --> 00:12:52,310
Does it give the Lindsay sofort.

220
00:12:52,360 --> 00:12:53,470
No it will stop.

221
00:12:53,800 --> 00:12:55,120
So what is the value of X.

222
00:12:55,120 --> 00:12:57,910
Not it is for next one.

223
00:12:59,470 --> 00:13:02,080
So it means the final period of any element.

224
00:13:02,110 --> 00:13:08,630
Just we have to follow the hierarchy and we should give the negative number internally simplest.

225
00:13:09,610 --> 00:13:15,370
So we go to the parent now just read on X.

226
00:13:16,120 --> 00:13:16,720
That's it.

227
00:13:17,140 --> 00:13:20,140
Just not find us so you'll fire the parent.

228
00:13:20,560 --> 00:13:20,920
Right.

229
00:13:21,190 --> 00:13:23,470
There's a simple function, little simple function.

230
00:13:23,830 --> 00:13:26,260
Not is one more idea than this one that is collapsing.

231
00:13:26,260 --> 00:13:26,710
Fine.

232
00:13:26,710 --> 00:13:33,610
We want to go back to the words to the parent radically so you can find our expert by yourself.

233
00:13:33,610 --> 00:13:40,360
I have explained you the sufficient required things that is union and fight operation on set that are

234
00:13:40,360 --> 00:13:43,360
useful in what makes is of course some part.

235
00:13:43,770 --> 00:13:45,260
So they there are no explaining.

236
00:13:45,370 --> 00:13:46,090
You will then.

237
00:13:46,090 --> 00:13:47,680
Fine, I'll be using them.

238
00:13:48,580 --> 00:13:51,640
So that's all in this video because them also available on this one.

