1
00:00:00,490 --> 00:00:03,480
We will look at recursive version of MOSSHART.

2
00:00:04,500 --> 00:00:06,120
It is a two way more sort.

3
00:00:07,020 --> 00:00:11,350
And it follows top down approach as it is recursive.

4
00:00:11,970 --> 00:00:15,120
So let us look at the problem and observe a few things.

5
00:00:15,570 --> 00:00:19,950
See a single variable size eight is given and the elements are not sorted.

6
00:00:20,430 --> 00:00:22,480
Let us take half of this array.

7
00:00:22,950 --> 00:00:24,610
Are these four sorted?

8
00:00:24,810 --> 00:00:25,240
No.

9
00:00:26,190 --> 00:00:28,200
So these are also not sorted.

10
00:00:28,710 --> 00:00:36,020
Then let us look at half of this half whether these two elements are not even DNA, not certain.

11
00:00:36,480 --> 00:00:38,300
Then let us look at half of this half.

12
00:00:38,520 --> 00:00:39,840
So just two elements.

13
00:00:40,710 --> 00:00:43,050
So we try to market first half.

14
00:00:44,080 --> 00:00:46,460
This site is a target no next.

15
00:00:46,510 --> 00:00:49,240
This half, is it shot at the spot?

16
00:00:49,450 --> 00:00:49,890
No.

17
00:00:50,230 --> 00:00:51,780
Let us take the half of this one.

18
00:00:52,060 --> 00:00:57,160
No, I've decided yes, when you have it, it is down to one one element and obviously they are sorted.

19
00:00:57,340 --> 00:01:04,030
So in two emerging, we need to sort that list so that we can merge them and make a single sorted list.

20
00:01:04,360 --> 00:01:09,550
So when they have partitioned this one, there is no guarantee that left side and right side are sorted

21
00:01:09,550 --> 00:01:11,230
because the array is not sorted.

22
00:01:11,590 --> 00:01:14,680
Then I have taken the left hand side and again splitting it into half.

23
00:01:14,950 --> 00:01:17,250
Then again, there is no guarantee that these two are sorted.

24
00:01:17,530 --> 00:01:19,960
Then on this one also are going to have taken have.

25
00:01:20,200 --> 00:01:25,700
Now there is a guarantee that these two are sorted because those are single elements.

26
00:01:25,720 --> 00:01:32,590
So yes, this is the idea and modified as more sort of the recursive procedure than a single large array

27
00:01:32,590 --> 00:01:37,990
is given to it, then it will break it into two halves and there is no guarantee that these two are

28
00:01:37,990 --> 00:01:38,360
sorted.

29
00:01:38,380 --> 00:01:43,900
Then again, bring this into half until it reaches one one element, not one one element.

30
00:01:43,920 --> 00:01:48,430
Once they are sorted, then from there it will start merging the elements, it will start merging.

31
00:01:48,430 --> 00:01:52,390
Those are smaller list and into a larger list.

32
00:01:52,540 --> 00:01:54,450
Interesting phase of recursion.

33
00:01:54,850 --> 00:01:59,930
So first I will write on the algorithm, then I will show you the working, how it works.

34
00:02:00,340 --> 00:02:06,760
So this is a more sort and an array and lower index and higher index are given.

35
00:02:07,000 --> 00:02:07,880
Suppose not.

36
00:02:08,410 --> 00:02:10,750
This is low and this is high.

37
00:02:10,930 --> 00:02:13,180
So low is a zero high in the seven.

38
00:02:13,230 --> 00:02:16,610
So this is a low and high three parameters are given to that.

39
00:02:16,630 --> 00:02:24,010
Mosshart that has a complete list is given complete arrays, given a bigger is given which we have to

40
00:02:24,010 --> 00:02:25,570
sort through merging.

41
00:02:25,720 --> 00:02:29,200
So then it should be broken into halves and the most together.

42
00:02:30,360 --> 00:02:33,790
So it will divide the list into two and merge them together.

43
00:02:34,140 --> 00:02:39,450
So if there are more than one elements, if there is only a single element, nothing to do.

44
00:02:39,660 --> 00:02:41,250
So low should be less than high.

45
00:02:41,380 --> 00:02:44,610
So at least two elements should be there then only low will be less than high.

46
00:02:44,880 --> 00:02:52,260
If low is less than high, find out that is a low plus high divided by two.

47
00:02:52,590 --> 00:02:54,600
And we need to the value here.

48
00:02:56,400 --> 00:02:59,640
So Flaubert's if it is integer division that we good floor value only.

49
00:03:00,000 --> 00:03:01,560
So this is this one.

50
00:03:01,710 --> 00:03:05,400
So low gloss, high volume and zero plus seven by two is a three.

51
00:03:05,640 --> 00:03:14,520
So this is so it will divide the list into half then this part should be merged with the spot, but

52
00:03:14,540 --> 00:03:17,410
magic should be done only if these two are sorted.

53
00:03:18,000 --> 00:03:21,120
So let us sort of this first and sort this one also.

54
00:03:21,630 --> 00:03:25,320
How to sort this recursively using more only.

55
00:03:25,680 --> 00:03:29,490
So Fosset will start these two sides, the left side and right side.

56
00:03:29,760 --> 00:03:32,130
Once they are sorted, then it will merge them.

57
00:03:32,370 --> 00:03:40,430
So far Mosshart from low to middle performance sort the left side that is on Uhry from low to from low

58
00:03:40,440 --> 00:03:47,040
to middle then performance side on the right hand side also that is mid plus one to high performance

59
00:03:47,040 --> 00:03:48,000
side on an array.

60
00:03:48,000 --> 00:03:50,730
On the right hand side that is plus one too high.

61
00:03:51,200 --> 00:03:55,050
So it will left hand side and right hand side recursively.

62
00:03:56,380 --> 00:04:03,370
Then when these two are started, it has to merge them so much to assume that deserves this, all of

63
00:04:03,370 --> 00:04:09,710
a sudden we have to merge these two list Sihamoni list in a single letter to list in a single day.

64
00:04:09,740 --> 00:04:18,120
One is from the second one is too high to list in a single single so that we have seen how to manage

65
00:04:18,160 --> 00:04:23,920
to list in a single day with the help of Auxillary or a B and again getting the result back.

66
00:04:23,920 --> 00:04:25,200
And then we have already seen that.

67
00:04:25,210 --> 00:04:27,180
So we'll call that Manch process.

68
00:04:27,700 --> 00:04:36,260
This is much of a fuss index is low then the middle value and also highly trained.

69
00:04:36,580 --> 00:04:42,280
We have already seen this much less on this procedure, recursive end of the procedure.

70
00:04:42,610 --> 00:04:45,300
This is so small, but this is recursion.

71
00:04:45,310 --> 00:04:49,450
So this will expand like anything and start any large industry.

72
00:04:49,930 --> 00:04:52,900
So let us trace the small thought on this example.

73
00:04:53,200 --> 00:04:57,850
If we call that more sort on this array, Luisito High is a seven.

74
00:04:58,360 --> 00:04:59,770
So first call is zero.

75
00:04:59,770 --> 00:05:00,730
Come on seven.

76
00:05:01,880 --> 00:05:07,890
Zero, comma seven, I'm showing only Laura and I are always there, then what it will do?

77
00:05:07,910 --> 00:05:08,480
Find out.

78
00:05:09,080 --> 00:05:15,670
So what is the value of this one zero plus seven by two is a three, then it will split the list index

79
00:05:15,680 --> 00:05:16,190
of three.

80
00:05:16,200 --> 00:05:19,150
So from here the list is split three is.

81
00:05:19,580 --> 00:05:23,740
So the Susmita then call of on the left hand side.

82
00:05:23,960 --> 00:05:26,640
So first one, that is a local matter.

83
00:05:26,780 --> 00:05:29,630
So it will call itself on the left hand side.

84
00:05:30,050 --> 00:05:32,450
That is from zero to three.

85
00:05:34,210 --> 00:05:41,080
This is the first call for a second call afterwards, once this has finished, then it will go to second

86
00:05:41,080 --> 00:05:41,380
call.

87
00:05:41,770 --> 00:05:43,240
I will mark these are steps.

88
00:05:43,240 --> 00:05:46,380
One, two, three, four.

89
00:05:46,750 --> 00:05:47,750
See first find our.

90
00:05:48,310 --> 00:05:50,610
OK, then go on the left hand side.

91
00:05:50,620 --> 00:05:56,470
This is on the left hand side, left right hand side and then emerging as remaining for this portion

92
00:05:56,470 --> 00:05:59,090
right for the second two steps are remaining.

93
00:05:59,350 --> 00:06:01,420
So let us continue with the left hand side.

94
00:06:01,660 --> 00:06:02,920
This is zero to three.

95
00:06:03,340 --> 00:06:06,010
So low is a zero and high is a three.

96
00:06:06,280 --> 00:06:09,610
So now high for this list is still here only.

97
00:06:10,890 --> 00:06:15,780
Then find out zero plus three, why do this one so many will be one.

98
00:06:17,330 --> 00:06:24,620
Then the list is again divided into two, so we found out the list is divided into two.

99
00:06:24,650 --> 00:06:27,370
So first, the left hand side, that is too many.

100
00:06:27,590 --> 00:06:30,620
So, again, a call is made from low to mid.

101
00:06:30,860 --> 00:06:32,360
That is zero to one.

102
00:06:32,840 --> 00:06:33,800
Zero to one.

103
00:06:35,790 --> 00:06:43,000
Then this is a fresh corn 021, Luisito, and high is one, low is less than high.

104
00:06:43,000 --> 00:06:45,190
Yes, a zero is less than one.

105
00:06:45,190 --> 00:06:52,380
Guess then find out what is the motive for this one zero plus one divided by Doer's zero.

106
00:06:52,380 --> 00:06:54,110
Only so many zero.

107
00:06:54,330 --> 00:06:59,470
So now low is a zero, high is one and the middle is zero.

108
00:06:59,490 --> 00:07:01,090
This is murder itself.

109
00:07:01,110 --> 00:07:01,880
This is murder.

110
00:07:02,340 --> 00:07:06,180
Then after finding murder, called upon left side first.

111
00:07:06,360 --> 00:07:09,180
Then on the left hand side it will call itself.

112
00:07:09,480 --> 00:07:12,720
That is low to murder, low to murder.

113
00:07:12,990 --> 00:07:16,300
Low is also zero is also zero zero zero zero.

114
00:07:16,320 --> 00:07:17,100
It is called upon.

115
00:07:17,100 --> 00:07:24,960
This one sees so many calls are made not at this stage when both are zero, zero is not less than high.

116
00:07:25,170 --> 00:07:27,120
It will not enter inside.

117
00:07:27,480 --> 00:07:28,820
It will not enter inside.

118
00:07:29,070 --> 00:07:31,010
So this call terminates here.

119
00:07:31,530 --> 00:07:34,970
So it goes back to the previous call where low was zero.

120
00:07:34,980 --> 00:07:36,450
High was one, low was zero.

121
00:07:36,450 --> 00:07:37,260
High was one.

122
00:07:37,620 --> 00:07:37,870
Right.

123
00:07:37,980 --> 00:07:41,850
Mirrorball zero on this these two steps it has to perform.

124
00:07:41,850 --> 00:07:43,320
Let us go on the right hand side.

125
00:07:43,680 --> 00:07:45,270
So first go on the right hand side.

126
00:07:45,540 --> 00:07:48,060
That is mid plus one too high.

127
00:07:48,390 --> 00:07:53,220
That is zero plus one one highest one one one common one.

128
00:07:53,670 --> 00:07:55,100
Again these are equal.

129
00:07:55,320 --> 00:07:56,640
So there's a single element.

130
00:07:56,790 --> 00:07:58,650
So it will not perform anything.

131
00:07:58,890 --> 00:07:59,060
So.

132
00:07:59,070 --> 00:07:59,280
Right.

133
00:07:59,280 --> 00:08:00,710
No, actually the list is split.

134
00:08:00,840 --> 00:08:01,140
Right.

135
00:08:01,380 --> 00:08:03,790
So first zero element decide one element.

136
00:08:03,810 --> 00:08:09,060
The in order to hear what was happening was a list of some elements are given.

137
00:08:09,060 --> 00:08:11,010
So it is divided again.

138
00:08:11,010 --> 00:08:12,030
It is divided again.

139
00:08:12,030 --> 00:08:14,940
It is divided until it becomes one one element each.

140
00:08:15,180 --> 00:08:16,500
Yes, one one element each.

141
00:08:16,800 --> 00:08:21,240
Now for the score left hand side is finished, right hand side is finished now.

142
00:08:22,700 --> 00:08:28,510
Last, a statement that is emerging has to be done, so calculating, left side, right side completed,

143
00:08:28,520 --> 00:08:31,360
no margin has to be done, so much has to be done.

144
00:08:31,580 --> 00:08:34,010
So the element is zero zero one one.

145
00:08:34,280 --> 00:08:37,770
This is one list of the second list to list are merged.

146
00:08:37,820 --> 00:08:41,210
So now the new result is to come on eight.

147
00:08:41,720 --> 00:08:45,160
And this is the that this becomes one single list.

148
00:08:47,230 --> 00:08:52,900
First two, then eight, then this is finish, this was the first margin.

149
00:08:53,470 --> 00:08:58,300
Now it will go back to previous call because this portion is finished, then it has to pick the right

150
00:08:58,300 --> 00:08:58,650
side.

151
00:08:58,990 --> 00:09:04,450
So on the right side is a plus one too high plus one to height.

152
00:09:04,780 --> 00:09:07,420
So this is to come on three.

153
00:09:08,050 --> 00:09:09,670
So it will go to the side.

154
00:09:11,560 --> 00:09:15,450
Law is a tool and highest tree.

155
00:09:16,810 --> 00:09:25,720
Find out who plus five by two is a only so murders to do so, Lister's divided into two halves.

156
00:09:27,330 --> 00:09:34,860
Yes, on the left hand side to the left hand side to that is two to two single element.

157
00:09:34,860 --> 00:09:38,660
Nothing is done right hand side plus one too high.

158
00:09:38,760 --> 00:09:42,510
That is a three to three right hand side trigonometry.

159
00:09:42,540 --> 00:09:43,950
So here also nothing will happen.

160
00:09:43,950 --> 00:09:47,460
It terminates not left side, completely right side, completely left sitcom.

161
00:09:47,460 --> 00:09:49,050
The right set completed much.

162
00:09:49,380 --> 00:09:55,130
So these two elements are much so two and three are more together like these are much together now.

163
00:09:55,150 --> 00:09:58,290
These two will be marginal, which is smaller six than nine.

164
00:09:58,620 --> 00:10:04,270
So you can see that actually two lists are getting merge and each having just one element.

165
00:10:04,590 --> 00:10:11,400
So this was the second model we perform now coming back to this one now for this side, left hand side

166
00:10:11,400 --> 00:10:13,130
is completely right, hand side is completed.

167
00:10:13,170 --> 00:10:14,040
This is remaining.

168
00:10:14,250 --> 00:10:18,870
So on this left hand side, completely right hand side is remaining.

169
00:10:19,020 --> 00:10:20,880
So up on this smudging will be done.

170
00:10:21,090 --> 00:10:23,580
So for this list, what is low and high?

171
00:10:27,380 --> 00:10:28,640
Luisito.

172
00:10:29,850 --> 00:10:37,410
Higher, the three and the medals won, medals won, so first Lister's from zero to one zero to one

173
00:10:37,710 --> 00:10:40,610
second lasted from two to three, so two to three.

174
00:10:40,860 --> 00:10:45,360
So these two list of the two two elements each day are much together.

175
00:10:45,540 --> 00:10:49,150
And the result is two, six, eight, nine.

176
00:10:49,620 --> 00:10:50,750
This is the list.

177
00:10:50,910 --> 00:10:52,500
So this is the third march.

178
00:10:52,650 --> 00:10:55,290
And finally, it will have total four elements.

179
00:10:56,800 --> 00:11:01,060
So you can see that has called upon this site, called upon right site, then it will march.

180
00:11:02,540 --> 00:11:04,010
It is working recursively.

181
00:11:05,010 --> 00:11:10,740
Now, remaining from the side, I have to complicate, so without explaining, I will simply fill the

182
00:11:10,740 --> 00:11:16,320
values and quickly I will show them for the so-called right hand side is called right hand side.

183
00:11:16,320 --> 00:11:17,850
Is my plus one too high?

184
00:11:18,120 --> 00:11:26,330
So this is four to seven lawyers for HIZA seven then find out made four plus Seven-Eleven by two.

185
00:11:26,340 --> 00:11:28,040
That is five Medders five.

186
00:11:28,050 --> 00:11:33,990
So this is murder and Mr. Splitted don't call up on left hand side.

187
00:11:34,290 --> 00:11:37,170
The left hand side is four to five.

188
00:11:40,190 --> 00:11:47,830
Lawyers for hire is if I find out the murders of four again, murders four, then call up the left hand

189
00:11:47,840 --> 00:11:48,860
side for Khama.

190
00:11:48,870 --> 00:11:51,410
For this, a single element, nothing will happen.

191
00:11:51,410 --> 00:11:54,750
Kollapen right hand side, Finkleman five single element.

192
00:11:54,770 --> 00:11:55,570
Nothing will happen.

193
00:11:55,850 --> 00:12:01,430
Go back and forth, merging so much more for common five.

194
00:12:01,670 --> 00:12:03,590
These two elements are much together.

195
00:12:03,740 --> 00:12:06,590
So this will be three five and this assorted.

196
00:12:08,010 --> 00:12:13,770
Then go on the right hand side of this, one right hand side is five plus one plus one six seven.

197
00:12:13,800 --> 00:12:15,090
The middle here is six.

198
00:12:15,420 --> 00:12:16,830
The left hand side is six.

199
00:12:16,830 --> 00:12:17,520
Come on six.

200
00:12:17,760 --> 00:12:18,770
They're a single element.

201
00:12:18,780 --> 00:12:20,820
Nothing will happen right now.

202
00:12:20,820 --> 00:12:28,270
It is working on this low and high, six and seven and Medders six only right then right hand side seven.

203
00:12:28,270 --> 00:12:29,040
Common seven.

204
00:12:29,040 --> 00:12:30,210
There's a single element.

205
00:12:30,570 --> 00:12:32,670
Then go back and perform.

206
00:12:32,670 --> 00:12:33,270
Last a step.

207
00:12:33,270 --> 00:12:35,260
That is March 5th to March.

208
00:12:35,280 --> 00:12:36,060
So what is that.

209
00:12:36,300 --> 00:12:38,400
These two are more together then.

210
00:12:38,400 --> 00:12:39,150
Seven and four.

211
00:12:39,150 --> 00:12:41,110
Four for the smaller seven is greater.

212
00:12:41,310 --> 00:12:42,830
So this becomes a single list.

213
00:12:43,560 --> 00:12:47,700
So the single list of two elements in the list of two elements in the list of two and single list of

214
00:12:47,700 --> 00:12:49,470
two this two or three.

215
00:12:49,490 --> 00:12:50,790
So go back then.

216
00:12:50,790 --> 00:12:52,550
This is six to merge.

217
00:12:52,830 --> 00:12:55,920
So four to seven elements are much, four to seven.

218
00:12:55,920 --> 00:13:01,310
So that time Lou was here and he was here and this was Miller.

219
00:13:01,650 --> 00:13:03,690
So four, five and six, seven.

220
00:13:04,960 --> 00:13:11,740
These two are merged together, so this becomes three, four, five and seven, these two estamos,

221
00:13:11,800 --> 00:13:13,000
this becomes one last.

222
00:13:14,850 --> 00:13:20,280
Now for this, the left hand side finished, this right hand side finished, now you can see that both

223
00:13:20,280 --> 00:13:22,750
these two sides are sorted.

224
00:13:24,290 --> 00:13:33,350
Recursively using sword only know upon this zero to seven where low is on zero, highest on seven and

225
00:13:33,710 --> 00:13:37,750
is on three so far, this one merging will be done.

226
00:13:37,940 --> 00:13:41,780
So one Lister's from here to here, which is sucking on this ship.

227
00:13:42,110 --> 00:13:44,820
And this is from four to seven.

228
00:13:44,840 --> 00:13:45,590
This is sorted.

229
00:13:45,610 --> 00:13:47,160
That is in the ship right now.

230
00:13:47,180 --> 00:13:48,710
The elements are no more like this.

231
00:13:48,730 --> 00:13:49,850
They have changed with this one.

232
00:13:51,080 --> 00:13:58,790
Not merge these two by using this much low to listen to high than these two statements together.

233
00:13:58,820 --> 00:14:03,680
So this is two, three, four, five, six, seven, eight, nine.

234
00:14:04,070 --> 00:14:05,450
And this is Final Much.

235
00:14:05,750 --> 00:14:07,670
This was seventh March.

236
00:14:08,420 --> 00:14:10,010
So the procedure was recursive.

237
00:14:10,020 --> 00:14:13,400
So I have traced it and I have shown you are also far more tracing.

238
00:14:14,300 --> 00:14:21,620
The way we write is just for statements, but how it works is very huge and it depends on the size of

239
00:14:21,620 --> 00:14:22,550
a list.

240
00:14:23,120 --> 00:14:26,080
Now let us do analysis of much thought.

241
00:14:26,510 --> 00:14:30,150
So instead of looking at the code, let us look at this tracing tree.

242
00:14:30,770 --> 00:14:33,830
So what the work we are doing, we are doing nothing but merging.

243
00:14:34,130 --> 00:14:36,110
So merging of how many elements.

244
00:14:36,440 --> 00:14:43,520
See, if I look at this root, this root, four elements from three or four elements from here or merge.

245
00:14:43,520 --> 00:14:46,060
And we got totally eight elements and elements.

246
00:14:46,640 --> 00:14:50,150
So the result of in here is and elements are merged.

247
00:14:50,540 --> 00:14:55,150
Then we are getting the results by merging these two here, merging these two here.

248
00:14:55,340 --> 00:15:02,180
So total harmony here for four then here also total at this level, if you see at this level total eight

249
00:15:02,180 --> 00:15:08,150
elements, Samaj, that is in elements and at this level also and elements are Manch and last level,

250
00:15:08,150 --> 00:15:11,060
nothing is done because those are representing single elements.

251
00:15:12,090 --> 00:15:13,920
So how many times Mojang is done?

252
00:15:15,500 --> 00:15:22,330
And three times, so that is equal to what depends on the height of the street and the height of this,

253
00:15:23,080 --> 00:15:25,720
and this certainly looks like a full binary tree.

254
00:15:25,750 --> 00:15:27,930
Yes, it is like a full way to your complete.

255
00:15:28,570 --> 00:15:32,320
So the height is how much log and log in.

256
00:15:32,620 --> 00:15:36,900
So this is a done for how many times log and times.

257
00:15:37,900 --> 00:15:40,230
So merging is done for logging times.

258
00:15:41,170 --> 00:15:44,950
So and is the time taken for merging and it is happening for log in time.

259
00:15:44,950 --> 00:15:52,120
So the time complexities is drawn and the logging is the time, complexities and log in.

260
00:15:52,330 --> 00:15:59,000
So the time complexity of Mercedez and log in and there is no best case or worst case.

261
00:15:59,200 --> 00:16:01,010
So this is average case time.

262
00:16:01,330 --> 00:16:03,040
So this was the time analysis.

263
00:16:04,220 --> 00:16:07,080
The more I have to discuss, just let me show you one more thing.

264
00:16:07,730 --> 00:16:10,070
See how the merging was done.

265
00:16:10,610 --> 00:16:14,570
If this is a tree, then this was more than this one than this one.

266
00:16:14,690 --> 00:16:20,480
So left, right, then route, left, right, then route, then left, right and then route.

267
00:16:20,750 --> 00:16:23,110
So, yes, merging is done in post order.

268
00:16:23,630 --> 00:16:27,770
So if you see this recursive call, recursive call, then merge.

269
00:16:27,770 --> 00:16:30,170
So merging is written after two calls.

270
00:16:30,560 --> 00:16:31,480
So it is posted.

271
00:16:31,970 --> 00:16:34,130
So in the post sort of the merging is done.

272
00:16:34,160 --> 00:16:40,190
So if you represent this in the form of a tree, so the post out of a tree merging is can not come to

273
00:16:40,190 --> 00:16:43,430
the space complexity of MARSOC in this.

274
00:16:43,430 --> 00:16:46,430
Most certainly gardam the space that requires a.

275
00:16:47,120 --> 00:16:51,170
So that is of size and depending on a number of elements.

276
00:16:51,650 --> 00:16:59,510
But if you see this Morschel algorithm that uses auxillary URRY Auxillary at a B for cabbing, the is

277
00:16:59,510 --> 00:16:59,680
there.

278
00:16:59,720 --> 00:17:01,340
So that should also be of size.

279
00:17:01,340 --> 00:17:07,470
And that then as this Mercedez recursive and the recursion utilizes a stack.

280
00:17:07,520 --> 00:17:11,690
So what should be the size of the stack depending on the height of the tree.

281
00:17:12,020 --> 00:17:16,310
So size of the stack is log-in for the main.

282
00:17:16,310 --> 00:17:21,740
Two things that we have to highlight here, that as it is recursive it use a stack.

283
00:17:21,740 --> 00:17:22,710
That is the first one.

284
00:17:22,970 --> 00:17:32,090
Second thing is it needs extra extra arrays compulsory for maushart, so total space required us to

285
00:17:32,090 --> 00:17:33,350
and plus log in.

286
00:17:33,950 --> 00:17:40,010
And in this endless logging is the extra space required for Maushart.

287
00:17:41,010 --> 00:17:48,690
So in competition based more, Mozart is the only algorithm which requires extra space, otherwise we

288
00:17:48,690 --> 00:17:54,990
saw insertions or double thought he thought quicksort, none of them uses extra space, must not use

289
00:17:54,990 --> 00:17:57,560
that extra space in comparison base.

290
00:17:58,890 --> 00:18:01,440
So that's all about maushart algorithm.

