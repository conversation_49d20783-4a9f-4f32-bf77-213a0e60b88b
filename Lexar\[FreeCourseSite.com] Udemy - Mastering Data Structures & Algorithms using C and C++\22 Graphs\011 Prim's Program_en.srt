1
00:00:00,390 --> 00:00:05,220
In the previous video, we have already seen primps algorithm, I have just shown you the working,

2
00:00:05,760 --> 00:00:12,360
not for the same working, we have to write a program so based on the working how to convert into a

3
00:00:12,360 --> 00:00:14,330
program, let us learn about it.

4
00:00:15,150 --> 00:00:21,240
And one more thing I remember for converting any algorithm into a program, you need to identify two

5
00:00:21,240 --> 00:00:21,690
things.

6
00:00:22,080 --> 00:00:24,990
First is what is the data structure required?

7
00:00:25,320 --> 00:00:31,050
Unless you know the data structure, unless you represented in the form of data structures, you cannot

8
00:00:31,050 --> 00:00:31,580
solve that.

9
00:00:32,110 --> 00:00:35,760
The next thing is the instructions, instructions, whatever.

10
00:00:35,760 --> 00:00:38,930
The procedure is the same thing you have to write in any language.

11
00:00:39,210 --> 00:00:40,990
So the basic thing is data structure.

12
00:00:41,010 --> 00:00:45,660
Most of the time storing this, they will try to directly go on the instructions.

13
00:00:45,660 --> 00:00:50,490
But unless you know the clear idea of data structure, you should not go on instructions.

14
00:00:50,490 --> 00:00:51,580
So this is important.

15
00:00:52,200 --> 00:00:57,840
So this video should give you an approach for converting an algorithm into a program.

16
00:00:58,670 --> 00:01:01,740
So for writing a program, we need to have two things.

17
00:01:01,740 --> 00:01:08,310
We have to understand what should be the data structure used for the program and what should be the

18
00:01:08,460 --> 00:01:09,830
operations operation?

19
00:01:09,900 --> 00:01:14,160
We have already seen in the previous video that was just bored of work or paper work.

20
00:01:14,160 --> 00:01:15,870
We have seen not the same thing.

21
00:01:15,870 --> 00:01:18,420
We have to write it in the form of C language instructions.

22
00:01:19,200 --> 00:01:21,900
First of all, let us look at data structure that we require.

23
00:01:22,200 --> 00:01:24,330
So far that I have taken an example here.

24
00:01:24,690 --> 00:01:28,110
This is a graph in the previous year to have used the same graph.

25
00:01:28,110 --> 00:01:29,530
Just the values are different here.

26
00:01:30,260 --> 00:01:33,090
Now, how do we present this graph on paper?

27
00:01:33,100 --> 00:01:38,300
You can do it like this, but inside the C program or in any program, how do you represent this?

28
00:01:38,610 --> 00:01:43,730
So a graph is represented as a two dimensional array, like a matrix.

29
00:01:43,740 --> 00:01:49,170
So this is a two dimensional array that is are just semantics for this graph.

30
00:01:49,680 --> 00:01:54,810
If you observe, as it is known directed graph, I have taken to add just one to six as well as six

31
00:01:54,810 --> 00:01:57,730
to one, but I have taken and introduced five.

32
00:01:58,050 --> 00:02:03,990
So if you observe here, one, two, six, it is five as well as six to one.

33
00:02:03,990 --> 00:02:04,970
Also it is five.

34
00:02:04,980 --> 00:02:07,230
So likewise I feel all those values.

35
00:02:07,260 --> 00:02:07,910
This is ready.

36
00:02:08,250 --> 00:02:14,100
So we need a two dimensional area that is a different cymatics that is having the cost of the edges.

37
00:02:14,110 --> 00:02:18,210
So let us call that matrix as a cost adjacency, cymatics.

38
00:02:18,360 --> 00:02:19,150
And one more thing.

39
00:02:19,290 --> 00:02:22,360
The observer here is I have what it says.

40
00:02:22,410 --> 00:02:24,680
One, two, three, four, five, six, seven.

41
00:02:24,690 --> 00:02:27,720
So I have the words is starting from one, ending at seven.

42
00:02:28,050 --> 00:02:32,400
But others in C, C++ or in any language distract from zero onwards.

43
00:02:32,700 --> 00:02:37,950
So I want from one only so that I have taken the size of this eight by eight.

44
00:02:38,130 --> 00:02:39,990
So zero two seven zero seven.

45
00:02:40,260 --> 00:02:42,660
This is zero zero zero column.

46
00:02:42,660 --> 00:02:43,790
I'm not using it.

47
00:02:44,940 --> 00:02:46,140
And one last thing.

48
00:02:46,410 --> 00:02:51,780
So whenever there is no edge, I have Mockett with the hyphens dashes.

49
00:02:51,780 --> 00:02:53,820
I have put the hyphen there.

50
00:02:54,090 --> 00:02:55,860
That means infinity.

51
00:02:56,250 --> 00:02:57,780
That means infinity.

52
00:02:58,200 --> 00:03:03,420
If you are filling an array in a program then put some maximum value.

53
00:03:03,420 --> 00:03:08,580
There is this is integer type but it then put the maximum integer value.

54
00:03:08,940 --> 00:03:11,890
Alright, so those dashes are maximum integer.

55
00:03:12,090 --> 00:03:12,900
We cannot fill them.

56
00:03:12,900 --> 00:03:14,040
It looks very congested.

57
00:03:14,340 --> 00:03:18,720
So to make it easily readable I have just returned the values.

58
00:03:19,950 --> 00:03:24,610
Now, the next thing that we need is whatever the spanning trade that we are getting, we have to select

59
00:03:24,610 --> 00:03:26,910
the edges and we have to store that spending.

60
00:03:27,210 --> 00:03:34,500
So far that I have taken a two dimensional array with the two rules and six columns and I'm calling

61
00:03:34,500 --> 00:03:35,140
D'Asti.

62
00:03:35,160 --> 00:03:37,150
So here I'll be storing spanning three.

63
00:03:37,680 --> 00:03:42,780
So if you remember in the previous week, you saw that in principle them before select the minimum cost.

64
00:03:43,060 --> 00:03:44,540
That is one comma six.

65
00:03:44,790 --> 00:03:48,780
So here I will be writing one six one six next.

66
00:03:49,080 --> 00:03:51,290
If you remember, we have taken this six five.

67
00:03:51,540 --> 00:03:54,520
So liberating six, five, four, five, six, whatever it comes here.

68
00:03:54,840 --> 00:03:58,770
So in this way, I will fill up this with edges, so I just will have to watch.

69
00:03:59,030 --> 00:04:04,770
So for that I have to cut doodles and in a spy ring, three will have six hedges.

70
00:04:04,770 --> 00:04:06,690
So I have taken the size six.

71
00:04:07,500 --> 00:04:11,070
Next, we have one more data structure that is an called near.

72
00:04:11,310 --> 00:04:12,240
What is the use?

73
00:04:12,270 --> 00:04:14,060
I will show you now.

74
00:04:14,070 --> 00:04:21,480
Next thing I will force demonstrate the working of prints algorithm within these data structures.

75
00:04:21,510 --> 00:04:23,400
I will use these data structures.

76
00:04:23,400 --> 00:04:27,020
I will fill them by following the procedures we learned in the previous video.

77
00:04:27,390 --> 00:04:29,410
Then I will write it as a program.

78
00:04:29,790 --> 00:04:33,230
So first, let us see how we can use this data structures.

79
00:04:33,240 --> 00:04:40,480
In the book, Lidstrom Prints Algorithm, Ziprin says that first of all, you find out the minimum cost

80
00:04:41,550 --> 00:04:42,260
that each.

81
00:04:42,810 --> 00:04:49,990
So which is the minimum cost of all these five and a different six system that is initial step of Prince

82
00:04:50,040 --> 00:04:52,140
Select one comma six.

83
00:04:52,350 --> 00:04:53,690
I will select one comma six.

84
00:04:53,700 --> 00:04:55,980
So then I should store that one comma here.

85
00:04:56,340 --> 00:04:56,760
Here.

86
00:04:57,300 --> 00:05:02,760
But before that, how do you find the minimum one where you check and find out the minimum one?

87
00:05:03,030 --> 00:05:05,340
This is on paper for the same thing.

88
00:05:05,340 --> 00:05:07,350
This is there in the memory and the data structure.

89
00:05:07,620 --> 00:05:14,130
So from this you have to find out minimum so you can check all that metrics and our metrics and find

90
00:05:14,130 --> 00:05:14,720
out the minimum.

91
00:05:14,730 --> 00:05:19,410
So if you check all these values, the minimum values five and it is here also.

92
00:05:20,780 --> 00:05:27,130
So what if you observe this is having duplicated values, five to ten, twenty five is two times, so

93
00:05:27,140 --> 00:05:28,930
do you have to check them dogmatics?

94
00:05:29,060 --> 00:05:29,620
No.

95
00:05:30,050 --> 00:05:35,810
If you check only the lower triangular or the upper triangular, that will be sufficient.

96
00:05:35,930 --> 00:05:37,360
So you can check any portion.

97
00:05:37,730 --> 00:05:42,900
So check any part of it, either lower triangular part of the triangle apart and find out the minimum.

98
00:05:42,920 --> 00:05:49,040
So if you look at this one from the middle, if I take a diagonal upward, diagonal back upward, apart

99
00:05:49,250 --> 00:05:53,270
from this minimum one, comma six, this is better one common six.

100
00:05:53,750 --> 00:05:56,370
So final the minimum one that is initial step.

101
00:05:56,540 --> 00:06:01,500
So for this, you can read on the code, you can try to follow up for finding the minimum from a triangle

102
00:06:01,520 --> 00:06:02,810
park that is one comma six.

103
00:06:03,110 --> 00:06:05,480
And right on that one comma six here.

104
00:06:06,620 --> 00:06:13,110
One in six, so this is the first initial attack that we are going to feel that is the minimum cost.

105
00:06:13,790 --> 00:06:22,560
Now, next, what Prince to say is that you have to select any other age or what X that is connected

106
00:06:22,560 --> 00:06:24,950
to a minimum connected and minimum.

107
00:06:25,290 --> 00:06:27,730
If you take minimum, the next minimum is eight.

108
00:06:28,250 --> 00:06:32,060
But we don't take this one and we select the one which is connected.

109
00:06:32,690 --> 00:06:35,600
It should be connected to the selected one.

110
00:06:35,630 --> 00:06:40,410
So what we have selected for one in six, so it should be connected to one or it should be connected

111
00:06:40,410 --> 00:06:40,970
to six.

112
00:06:40,970 --> 00:06:42,650
So I should select the minimum one.

113
00:06:43,070 --> 00:06:45,080
So who is connected to six five.

114
00:06:45,290 --> 00:06:48,330
Who is connected to one or linked to sort of this?

115
00:06:48,350 --> 00:06:49,540
We have to select minimum.

116
00:06:49,970 --> 00:06:50,410
Right?

117
00:06:50,780 --> 00:06:56,700
So now we need to know who is connected and who is minimum.

118
00:06:57,080 --> 00:06:59,450
So for that, I have taken this near.

119
00:07:00,110 --> 00:07:04,460
I will explain about this initially near array's having all infinity.

120
00:07:04,460 --> 00:07:06,400
First of all, you should fill this with infinity.

121
00:07:06,410 --> 00:07:07,210
I did not get it.

122
00:07:07,730 --> 00:07:10,220
Now I'm showing you this should be infinity.

123
00:07:10,370 --> 00:07:16,520
And the size of the array have taken as of eight that zero to seven because I need one two seven only

124
00:07:16,520 --> 00:07:20,900
because I have seven vertices now as I have selected one of my six.

125
00:07:21,170 --> 00:07:28,400
Maqdis has one Zeil and six also has zero because we have already included them.

126
00:07:28,730 --> 00:07:34,400
This zero shows that this is the solution now because we are getting a solution here spanning three.

127
00:07:35,210 --> 00:07:36,740
Then what is the use of this?

128
00:07:36,740 --> 00:07:44,600
Neroni What we will do now is after taking this one Cormie six, what we should do now is find out all

129
00:07:44,600 --> 00:07:49,960
other what it says, whether they are nearer to one or near to six.

130
00:07:50,120 --> 00:07:51,450
This is initial only right.

131
00:07:51,500 --> 00:07:58,340
We are in the initial part check whether they are nearer to one or nearer to six eight one one.

132
00:07:58,340 --> 00:07:59,290
Oh this is zero.

133
00:07:59,480 --> 00:08:00,490
This we don't have to take.

134
00:08:00,710 --> 00:08:02,810
So take the next one too.

135
00:08:03,080 --> 00:08:05,660
Is it nearer to one or nearer to six.

136
00:08:05,960 --> 00:08:08,090
Nearer to senior to one.

137
00:08:08,090 --> 00:08:10,190
The cost is twenty five nearer to six.

138
00:08:10,190 --> 00:08:11,510
The cost is infinity.

139
00:08:11,510 --> 00:08:12,340
There is no edge.

140
00:08:12,710 --> 00:08:19,160
So if you check here two two one two three five two two six infinity.

141
00:08:19,430 --> 00:08:20,630
So which is minimum.

142
00:08:20,630 --> 00:08:24,320
Twenty five is minimum, so two is nearer to one.

143
00:08:24,590 --> 00:08:27,620
So any other market has one.

144
00:08:28,100 --> 00:08:28,900
This is what we do.

145
00:08:29,210 --> 00:08:32,059
This means that what x2 is nearer to one.

146
00:08:33,070 --> 00:08:42,370
The next one, three three, so maybe check here only three to one infinity, three to six infinity,

147
00:08:42,640 --> 00:08:44,890
they're equal only, but are infinity infinity.

148
00:08:44,890 --> 00:08:45,910
So you can read any one.

149
00:08:45,920 --> 00:08:49,440
So I will take the second one that is six because we are taking one and six.

150
00:08:49,450 --> 00:08:54,430
So here are eight six then four, four to one, four to six.

151
00:08:54,430 --> 00:08:55,410
What are infinity.

152
00:08:55,420 --> 00:08:58,420
So right six then five to one.

153
00:08:58,420 --> 00:08:59,550
Five to six.

154
00:08:59,560 --> 00:09:00,370
This is 20.

155
00:09:00,730 --> 00:09:02,410
So this is infinity.

156
00:09:02,410 --> 00:09:05,040
This is twenty five is nearer to six.

157
00:09:05,050 --> 00:09:05,590
So right.

158
00:09:05,590 --> 00:09:07,450
Six here then seven.

159
00:09:07,450 --> 00:09:08,350
Seven to one.

160
00:09:08,350 --> 00:09:11,330
Seven to six butat infinity so.

161
00:09:11,350 --> 00:09:11,560
Right.

162
00:09:11,590 --> 00:09:12,200
Six here.

163
00:09:12,670 --> 00:09:18,520
So this shows that the rest of the world is us are either nearer to one or nearer to six.

164
00:09:18,850 --> 00:09:20,800
This is what we have to do it initially.

165
00:09:21,010 --> 00:09:25,600
Now the repeating steps will start till we were doing initial work.

166
00:09:25,810 --> 00:09:31,270
So if we look at it quickly, first of all, first step, what I did is from this I found the minimum

167
00:09:31,270 --> 00:09:33,550
and I have taken that edge here, one of my six.

168
00:09:33,880 --> 00:09:38,770
Then I have updated this near array and I found the rest of the what is the site near to one on here

169
00:09:38,770 --> 00:09:39,300
to six.

170
00:09:39,970 --> 00:09:41,080
Now the repeating steps.

171
00:09:41,080 --> 00:09:46,390
Know what I'm doing now, whatever I'm studying now will be going on repeating same thing I'll be doing

172
00:09:46,390 --> 00:09:47,020
again and again.

173
00:09:47,020 --> 00:09:52,180
So I observe this now first step and this one is from this.

174
00:09:52,180 --> 00:09:57,970
Find out minimum what minimum two, two, one, three, two, six, four to six.

175
00:09:57,970 --> 00:10:00,130
Five to six, seven to six.

176
00:10:01,360 --> 00:10:08,350
Two, two, one, three, two, six, four, six, five to six, seven to six, which is minimum because

177
00:10:08,350 --> 00:10:12,310
you have to pick other vertex that is connected to this only one and six only.

178
00:10:12,520 --> 00:10:18,580
So this and this, we have only two connect, and that is twenty five and five, five or six or six

179
00:10:18,580 --> 00:10:18,900
to fight.

180
00:10:18,910 --> 00:10:19,590
That is 20.

181
00:10:19,990 --> 00:10:21,290
So we will find out this one.

182
00:10:21,550 --> 00:10:23,620
See this one index.

183
00:10:23,800 --> 00:10:24,190
Right.

184
00:10:24,190 --> 00:10:26,270
Index to come over.

185
00:10:27,280 --> 00:10:37,660
So he took on one one twenty five three six three coma, six infinity, four coma, six for infinity,

186
00:10:38,080 --> 00:10:46,180
five coma six five coma twenty is twenty, then six coma six to seven because there's already zero seven

187
00:10:46,180 --> 00:10:51,640
coma six seven coma success infinity out of this, which is minimum years minimum.

188
00:10:51,640 --> 00:10:53,430
So that actually should include.

189
00:10:53,590 --> 00:11:00,340
So Mark here, five coma six, five coma six, five, six.

190
00:11:00,550 --> 00:11:05,930
The next picture that we are going to include see this is the first one we have included right there.

191
00:11:05,980 --> 00:11:06,670
The first one.

192
00:11:06,680 --> 00:11:07,780
Then the second one.

193
00:11:09,360 --> 00:11:15,960
Now, next thing is we have to update the story, how will it, first of all, Mark, five, location

194
00:11:15,960 --> 00:11:16,860
five zero.

195
00:11:17,220 --> 00:11:18,520
What does it mean by zero?

196
00:11:18,720 --> 00:11:19,970
We have included it.

197
00:11:20,130 --> 00:11:21,230
Don't check it again.

198
00:11:21,510 --> 00:11:22,560
We have included it.

199
00:11:23,160 --> 00:11:25,380
Know how many words we have?

200
00:11:25,980 --> 00:11:28,800
One, six, five.

201
00:11:28,960 --> 00:11:32,760
Rest of the word is us have the nearest fort Stewart is nearer to one.

202
00:11:32,760 --> 00:11:34,980
Three is nearer to six, 640 in the area to six.

203
00:11:35,220 --> 00:11:36,450
Seven is nearer to six.

204
00:11:36,750 --> 00:11:41,760
Now we have to check is it nearer to five than that existing vortex.

205
00:11:42,060 --> 00:11:46,170
So let us take five to the five infinity.

206
00:11:46,860 --> 00:11:50,190
So one is better, three to five.

207
00:11:50,760 --> 00:11:56,280
And right now it is three to six, which is a smaller three to six and three to five.

208
00:11:56,460 --> 00:11:58,850
If you compare this, both are equal.

209
00:11:58,980 --> 00:12:05,970
So as I'm writing the second review, so let us write three to five is infinity and three to six is

210
00:12:05,970 --> 00:12:07,750
also Infinity Batarseh.

211
00:12:08,160 --> 00:12:09,660
So let it be six on DC.

212
00:12:09,690 --> 00:12:12,580
Remember, we are right now looking at this final five.

213
00:12:12,930 --> 00:12:16,590
We are competing with this one, so please let us call Rescate case fight.

214
00:12:16,890 --> 00:12:18,270
So we are checking with the other.

215
00:12:18,270 --> 00:12:19,880
What is on the here to fight Lemarque.

216
00:12:20,280 --> 00:12:21,300
Not next thing forward.

217
00:12:21,420 --> 00:12:31,020
Let us look at four four two five four to five, four to five is sixteen and the four to six, four

218
00:12:31,020 --> 00:12:32,490
to six is infinity.

219
00:12:32,850 --> 00:12:37,140
See this near table is showing that the four is nearer to six.

220
00:12:37,140 --> 00:12:38,040
That is infinity.

221
00:12:38,310 --> 00:12:41,280
But now four to five is sixteen, which is smaller five.

222
00:12:41,610 --> 00:12:50,910
So updated to five then this one zero zero lead them seven seven is nearer to six this year they will

223
00:12:51,150 --> 00:12:53,700
say that seven is near to six.

224
00:12:53,730 --> 00:12:55,950
The reason it was compared with one and six.

225
00:12:55,950 --> 00:12:57,090
So it was nearer to six.

226
00:12:57,210 --> 00:13:01,410
It is infinity but now is five five cheekier.

227
00:13:01,650 --> 00:13:04,380
Seventy five 1876 infinity.

228
00:13:04,560 --> 00:13:06,120
So this is nearer to five.

229
00:13:07,320 --> 00:13:07,980
That's it.

230
00:13:08,010 --> 00:13:14,980
So this is the complete one step, complete one step, not a step is repeating one more time.

231
00:13:15,900 --> 00:13:16,770
Second step.

232
00:13:17,280 --> 00:13:20,510
What was the thing we have done in the first find out?

233
00:13:20,520 --> 00:13:27,330
Minimum wage, minimum, which is minimum age to come, one three six four five seven five.

234
00:13:27,330 --> 00:13:29,490
Living these zeroes to come on.

235
00:13:29,550 --> 00:13:31,380
How much too common one is.

236
00:13:31,380 --> 00:13:40,490
Twenty five and three six three comma six is infinity four comma five, four, five, six and seven

237
00:13:40,770 --> 00:13:42,830
five seven comma five eighteen.

238
00:13:43,200 --> 00:13:44,250
So which is smaller.

239
00:13:44,580 --> 00:13:48,600
This one is a smaller this one four comma five or sixteen.

240
00:13:48,900 --> 00:13:50,990
So Mark here four five.

241
00:13:51,630 --> 00:13:58,040
This is one thing, the next thing we have to update this one right now is selected works for now.

242
00:13:58,050 --> 00:14:03,510
Check all other modifiers, whether they are nearer to four, but before that, maybe sars-cov-2.

243
00:14:03,510 --> 00:14:08,160
So we should not check this one and four and five and six because they are already included.

244
00:14:08,940 --> 00:14:16,250
So rest of the world is if I should update Bhogle one twenty five to go for as infinity Czechia two

245
00:14:16,280 --> 00:14:18,720
to one is thirty five to to four is infinity.

246
00:14:18,960 --> 00:14:19,860
So one is better.

247
00:14:19,860 --> 00:14:23,390
It is nearer to one than three to six and three to four.

248
00:14:23,500 --> 00:14:25,200
Four, three to six is infinity.

249
00:14:25,200 --> 00:14:26,550
Three to four is eight.

250
00:14:26,550 --> 00:14:27,710
Oh this is nearer.

251
00:14:28,140 --> 00:14:31,110
So update this one then.

252
00:14:31,110 --> 00:14:31,890
Four, five, six.

253
00:14:31,890 --> 00:14:35,490
You leave them seven seven two five and seven to four.

254
00:14:35,700 --> 00:14:40,470
Seven to five s eighteen seven to forward is fourteen.

255
00:14:40,800 --> 00:14:45,260
So it's nearer before the second step completed.

256
00:14:45,840 --> 00:14:47,160
No I will do it quickly.

257
00:14:47,310 --> 00:14:48,240
Find out the minimum.

258
00:14:48,240 --> 00:14:55,500
One two comma one is twenty five and the three coming forward is eight and seven coming forward as fourteen.

259
00:14:55,680 --> 00:15:02,070
So eight is a smaller select this one that is three comma four marquetry coming forward and this is

260
00:15:02,070 --> 00:15:09,270
three and make this as zero then check rest of the world is those who are there two and seven other

261
00:15:09,270 --> 00:15:11,910
nearer to three or that same thing is correct.

262
00:15:12,330 --> 00:15:18,080
So two to one, two two one twenty five then two to three to two threes to an open.

263
00:15:18,210 --> 00:15:22,230
This is near the free market at three then seventy four and seventy three.

264
00:15:22,650 --> 00:15:23,040
Several.

265
00:15:23,040 --> 00:15:30,060
The food is fourteen and seventy three is infinity so it's nearer to before only that step is completed.

266
00:15:30,600 --> 00:15:32,970
The next find the minimum of this.

267
00:15:32,970 --> 00:15:35,910
Two to three seven two four two two three is two and seven.

268
00:15:35,910 --> 00:15:37,650
The four is fourteen.

269
00:15:37,950 --> 00:15:41,520
So it is a smaller so two to three selected.

270
00:15:41,760 --> 00:15:43,140
So this is two to three.

271
00:15:43,800 --> 00:15:44,160
Right.

272
00:15:44,400 --> 00:15:51,270
And the key values now what do we select all this and make it at zero and update only one thing.

273
00:15:51,270 --> 00:15:52,500
I have to seven to four.

274
00:15:52,560 --> 00:15:54,570
Seven to two to four is how much.

275
00:15:54,570 --> 00:15:56,200
Fourteen and seventy two.

276
00:15:56,200 --> 00:15:59,430
Oh I missed this one seven two one two two seven ten.

277
00:15:59,430 --> 00:16:02,880
I did not make this so seventy two is ten.

278
00:16:02,880 --> 00:16:07,890
So this should be ten and two to seven is also ten so seventy two.

279
00:16:08,010 --> 00:16:12,210
So seventy two is a ten and seven to four is eighteen which is smaller.

280
00:16:12,210 --> 00:16:14,580
This one is a smaller more defined as one.

281
00:16:15,930 --> 00:16:17,040
Steppers completed.

282
00:16:18,770 --> 00:16:23,990
Now, again, this trip celebrating, find out only this is minimum what it that's to two seven to two

283
00:16:23,990 --> 00:16:28,580
civilians, including update among the societal update.

284
00:16:28,580 --> 00:16:30,980
This nothing will be updated because all of them are.

285
00:16:31,220 --> 00:16:38,390
Zeitels, your procedure ends because we have finished selecting all six edges.

286
00:16:38,750 --> 00:16:47,150
So I just want to fix this one then five or six this one, then four to five this one, then three to

287
00:16:47,150 --> 00:16:51,730
four, the search, then two to three this edge, then two to seven.

288
00:16:51,740 --> 00:16:52,310
The search.

289
00:16:52,670 --> 00:16:58,310
If you look at the spanning three, we got it like this one to six of them, six to five and five to

290
00:16:58,310 --> 00:16:58,610
four.

291
00:16:58,610 --> 00:17:05,030
That is four to five, then four, three to four, then two to three, then two to seven.

292
00:17:05,329 --> 00:17:09,460
And this is five and twenty and sixteen, eight and 12.

293
00:17:09,470 --> 00:17:12,680
And then this is the minimum cost Planetree.

294
00:17:12,859 --> 00:17:15,079
This is showing about this one.

295
00:17:15,290 --> 00:17:17,180
So I have drawn the diagram for this one.

296
00:17:17,569 --> 00:17:20,119
So that's all about the working of the algorithm.

297
00:17:20,390 --> 00:17:22,970
No, I will write on the pieces of code that is required.

298
00:17:22,970 --> 00:17:25,040
I cannot write on the complete program here.

299
00:17:25,190 --> 00:17:28,490
It needs a lot of space, so I will be showing it in pieces.

300
00:17:28,490 --> 00:17:32,510
Then after this, you will have a video showing you the demonstration of this one.

301
00:17:32,510 --> 00:17:37,010
I will write on the program and show you you can see the code and also believe will be available.

302
00:17:37,010 --> 00:17:37,850
You can download that.

303
00:17:38,510 --> 00:17:41,750
So little pieces of code for doing all these things that.

304
00:17:42,200 --> 00:17:45,890
Elgort So here I have taken data structures.

305
00:17:46,370 --> 00:17:50,780
This is, of course a symmetric so I am declaring them as a global variable.

306
00:17:50,780 --> 00:17:53,860
So there is no main function, it's not yet started.

307
00:17:54,290 --> 00:17:57,500
So this is also the main function for me and may start from here.

308
00:17:58,010 --> 00:18:00,710
Let us say white men or any women, whatever it is.

309
00:18:01,010 --> 00:18:05,600
OK, so here to the main function before the main function I have defined, let me show you what is

310
00:18:05,600 --> 00:18:05,910
the thing.

311
00:18:05,930 --> 00:18:09,800
What are the things I have written has defined i.e. three two, seven, six, seven.

312
00:18:10,160 --> 00:18:13,580
This is the maximum integer in sixteen sixteen integer.

313
00:18:13,880 --> 00:18:16,130
And this is a infinity.

314
00:18:16,250 --> 00:18:16,670
Right.

315
00:18:16,670 --> 00:18:17,510
We need infinity.

316
00:18:17,510 --> 00:18:19,730
I said that at many places we are using infinity.

317
00:18:20,540 --> 00:18:25,910
Then I have a cross adjacency matrix of size eight by eight because there are seven vertices and this

318
00:18:25,910 --> 00:18:26,780
is eight by eight.

319
00:18:26,780 --> 00:18:27,070
Right.

320
00:18:27,290 --> 00:18:30,200
So I have initialize all those values.

321
00:18:30,380 --> 00:18:32,660
So initially they should have done directly.

322
00:18:32,900 --> 00:18:33,870
This is the first stop.

323
00:18:33,890 --> 00:18:35,270
Everything is infinity here.

324
00:18:35,450 --> 00:18:37,100
And the second row, infinity.

325
00:18:37,100 --> 00:18:42,200
Infinity twenty five and infinity is five and infinity infinity infinity twenty five three.

326
00:18:42,200 --> 00:18:43,100
Infinity is five.

327
00:18:43,100 --> 00:18:44,390
And infinity as it is.

328
00:18:44,390 --> 00:18:47,540
I have initialized this so I have not written everything.

329
00:18:47,870 --> 00:18:53,900
So now you can see that this is the whole thing I have to write down here so that my text is prepared

330
00:18:53,900 --> 00:18:54,230
here.

331
00:18:54,710 --> 00:18:57,340
No, the next thing that we require is nearer.

332
00:18:57,620 --> 00:19:01,910
And if you remember initially this was infinity already infinity.

333
00:19:02,240 --> 00:19:04,280
This is the first one is still marked as infinity.

334
00:19:04,640 --> 00:19:09,710
So I have taken an idea of size eight Caldonia and this is initialized with infinity.

335
00:19:10,580 --> 00:19:16,520
The next thing that I need is a two dimensional array with the two doors and six columns for storing

336
00:19:16,520 --> 00:19:17,290
spanning three.

337
00:19:17,300 --> 00:19:24,140
That solution of this problem, so that I have to come here to chromatics for all these three data structures

338
00:19:24,140 --> 00:19:26,450
are global for the main function of the main function.

339
00:19:26,450 --> 00:19:30,740
Stotz So just remove your things from here and on the main function.

340
00:19:31,780 --> 00:19:38,530
So he had to have written initial steps in terms of the of steps, so what we did, we have found out

341
00:19:38,530 --> 00:19:42,740
the minimum cost at first from there, the support triangular part.

342
00:19:43,060 --> 00:19:43,330
Right.

343
00:19:43,430 --> 00:19:44,520
Upper triangle apart.

344
00:19:44,860 --> 00:19:47,170
And we have included that edge here.

345
00:19:47,620 --> 00:19:51,680
And we mark that edge as a zero here, right.

346
00:19:51,730 --> 00:19:53,610
For one miles that are zero.

347
00:19:54,220 --> 00:19:57,990
And then we updated Neroni all those steps we have done here.

348
00:19:58,420 --> 00:20:04,810
Let us see this with the declaration and has defined that I have kept it in small size, main function

349
00:20:04,830 --> 00:20:05,800
starting from here.

350
00:20:06,310 --> 00:20:09,100
These are the variables that I may be using in my algorithm.

351
00:20:09,100 --> 00:20:14,440
So I have declared my AUV and minimum as I have to find out the minimum.

352
00:20:14,440 --> 00:20:14,650
Right.

353
00:20:14,660 --> 00:20:15,910
So for that I need a variable.

354
00:20:16,240 --> 00:20:18,100
So I'm assigning it to infinity.

355
00:20:18,170 --> 00:20:28,090
And then these follow up, these follow up finding the minimum cost at minimum value from their cost.

356
00:20:28,900 --> 00:20:29,270
Right.

357
00:20:29,380 --> 00:20:30,940
That is semantics.

358
00:20:31,330 --> 00:20:38,530
So for accessing that mattocks, I need to followups to four Lusardi I n g and for every Kostov I call

359
00:20:38,530 --> 00:20:40,900
my gem checking, is it minimum less than minimum.

360
00:20:41,080 --> 00:20:45,010
If so, I'm changing the new minimum and changing it to a new minimum.

361
00:20:45,010 --> 00:20:52,720
And I'm also recording that you and V as I as you and V so which is the edge that has given me minimum

362
00:20:53,050 --> 00:20:54,380
I'm finding on that one.

363
00:20:54,400 --> 00:20:57,100
So this is a simple thing, finding the minimum of eight.

364
00:20:57,110 --> 00:20:58,150
So observe this one.

365
00:20:58,540 --> 00:20:59,380
And one more thing.

366
00:20:59,380 --> 00:21:01,530
I have started from one stop ratel.

367
00:21:01,870 --> 00:21:06,970
So covering all the rules, governing all rules, but every column I don't have to take.

368
00:21:07,330 --> 00:21:07,660
Right.

369
00:21:07,930 --> 00:21:11,410
So I have to start the column that is beyond this diagonal.

370
00:21:11,410 --> 00:21:13,470
I should start from the diagonal, I should say.

371
00:21:13,810 --> 00:21:15,430
So I should start from this portion.

372
00:21:15,550 --> 00:21:16,290
This portion.

373
00:21:16,300 --> 00:21:16,870
This portion.

374
00:21:17,020 --> 00:21:18,490
I should not start from the beginning.

375
00:21:18,850 --> 00:21:23,760
So columns I'm starting from I onwards do start from Ibram.

376
00:21:23,770 --> 00:21:25,400
So this will take only upward trend.

377
00:21:25,400 --> 00:21:32,530
But this is finding minimum crosshatch so that we will get the minimum concepts, the minimum cost edge

378
00:21:32,530 --> 00:21:39,550
in you and V, now that minimum crosshatch, if you remember, if you've got one comma six.

379
00:21:39,820 --> 00:21:49,960
So I wrote one here and six here and P of zero zero zero zero and T of one zero zero zero, you be off

380
00:21:49,960 --> 00:21:58,510
on Gomaa zero V I feel the next thing I made this one and six as a zero zero one and six zero to show

381
00:21:58,510 --> 00:22:01,870
that these are all included near of you.

382
00:22:01,870 --> 00:22:04,720
And V this is um this is Meenal.

383
00:22:04,750 --> 00:22:05,100
Right.

384
00:22:05,260 --> 00:22:07,780
So that has made us zettl.

385
00:22:08,110 --> 00:22:09,210
These are made at zero.

386
00:22:10,090 --> 00:22:13,690
The next thing I have updated all these vertices.

387
00:22:14,050 --> 00:22:17,700
They are nearer to home, one or six, you or me.

388
00:22:17,950 --> 00:22:20,920
So here you is one and the V is six.

389
00:22:20,920 --> 00:22:27,130
We have checked so we check this one inside this Costeja systematics and find out they are nearer to

390
00:22:27,130 --> 00:22:28,420
one or near the six.

391
00:22:28,420 --> 00:22:30,280
So this is nearer they have updated.

392
00:22:30,550 --> 00:22:37,360
So you remember I wrote like this, I got the value here one and this was six and this was six and this

393
00:22:37,360 --> 00:22:40,600
was also six and this was also six.

394
00:22:40,600 --> 00:22:42,790
I think this was one and the rest of them were six.

395
00:22:42,790 --> 00:22:45,220
That was the initial work of what we have done.

396
00:22:45,220 --> 00:22:52,120
The same thing for you to find one doing some checking on cost of you.

397
00:22:52,120 --> 00:22:56,760
Komla, I it's less than cost of Ikoma.

398
00:22:56,940 --> 00:23:04,900
I come on you and I Komova that is nearer to you on the to if it is smaller that is near you then updated

399
00:23:04,900 --> 00:23:06,460
as you otherwise would be.

400
00:23:06,460 --> 00:23:09,820
So it's nearer to one or six whichever is nearer right on that.

401
00:23:09,970 --> 00:23:12,280
And inside this I should have one more condition.

402
00:23:12,280 --> 00:23:12,910
I miss that.

403
00:23:13,030 --> 00:23:13,960
I was right on that.

404
00:23:14,260 --> 00:23:21,550
I should take only those who near is not equal to zero near of i.e. is not equal to zero.

405
00:23:21,820 --> 00:23:26,720
It should not include this one in six and this one two conditions.

406
00:23:26,720 --> 00:23:27,880
So this should also be there.

407
00:23:28,150 --> 00:23:30,570
I should find out only for those which are not zero.

408
00:23:30,630 --> 00:23:32,770
So leaving the zero, I should do this.

409
00:23:33,400 --> 00:23:38,770
This will initialize so initial step, find out whether they are nearer to one or nearer to six.

410
00:23:39,610 --> 00:23:41,890
Initialize nearer is initialized.

411
00:23:42,280 --> 00:23:44,140
So this entire thing is initial.

412
00:23:44,140 --> 00:23:47,650
Look now next is the repetition work.

413
00:23:48,130 --> 00:23:54,880
Every time we select the minimum vertex from here and the market are zero and right on an edge here

414
00:23:55,210 --> 00:23:56,320
then updating here.

415
00:23:56,500 --> 00:23:58,060
So that is the code I have to write on.

416
00:23:58,240 --> 00:23:59,560
So I'll remove everything.

417
00:23:59,560 --> 00:24:02,050
No, I will write on the repeating procedure for this one.

418
00:24:03,040 --> 00:24:05,970
Here is the remaining part of the algorithm.

419
00:24:06,070 --> 00:24:07,510
The repeating steps.

420
00:24:07,870 --> 00:24:09,100
See one edge already.

421
00:24:09,100 --> 00:24:12,100
We have got it in the initial step, this one.

422
00:24:12,640 --> 00:24:19,360
And because there are seven vertices, we need six adjust for forming minimum classes spanning three.

423
00:24:19,750 --> 00:24:21,070
So one is already over.

424
00:24:21,330 --> 00:24:22,480
Remaining are five.

425
00:24:22,870 --> 00:24:23,350
There are.

426
00:24:23,350 --> 00:24:25,570
And what it says that is and is a seven.

427
00:24:25,930 --> 00:24:28,810
We need six out of that one is taken.

428
00:24:28,810 --> 00:24:31,200
So seven minus one we have to do that is.

429
00:24:32,140 --> 00:24:40,420
So here is a loop, outermost loop, just the loop for finding out, remaining just up to and minus

430
00:24:40,420 --> 00:24:42,110
one zero to one.

431
00:24:42,200 --> 00:24:44,550
Already we have taken so we have to start from one on word.

432
00:24:44,560 --> 00:24:46,140
So this is starting from one onwards.

433
00:24:46,540 --> 00:24:49,180
So limiting it just so from here to here.

434
00:24:49,750 --> 00:24:56,230
This is the complete task, the repeating steps, nine steps, what we were doing, finding the minimum

435
00:24:56,230 --> 00:25:05,380
of the minimum of what to come up one three six four, five, six, seven, comma six out of all these

436
00:25:05,380 --> 00:25:07,560
are just whichever is smaller that we were finding out.

437
00:25:07,930 --> 00:25:10,560
So for finding minimum, here is a loop.

438
00:25:10,900 --> 00:25:14,290
First of all, minimum, we assign a variable minimum assignment to infinity.

439
00:25:14,560 --> 00:25:17,930
Then for loop starts from one to N from one to end.

440
00:25:18,220 --> 00:25:24,070
So this loop is taking variable G so Jesus starting from indexable onwards, this is starting from an

441
00:25:24,070 --> 00:25:24,850
excellent onwards.

442
00:25:25,180 --> 00:25:27,860
Not it will take only those which are not a zero.

443
00:25:28,180 --> 00:25:33,180
So this is the condition, not zero, and it has to find a minimum.

444
00:25:33,430 --> 00:25:35,890
So with this minimum variable will come every time.

445
00:25:35,890 --> 00:25:40,810
If the cost is less than minimum, we will change the minimum and also we will record that which is

446
00:25:40,810 --> 00:25:42,220
giving us minimum value.

447
00:25:42,670 --> 00:25:52,180
OK, OK, so I'll address this and show you that the JS one, this is the energy and Energy Zero take

448
00:25:53,230 --> 00:25:57,460
then this is a G g to understand the energy.

449
00:25:57,460 --> 00:26:03,100
So to cumberbund we have to find out the cost of G and Nero G, Kostov G.

450
00:26:03,100 --> 00:26:03,750
And here.

451
00:26:03,780 --> 00:26:11,430
OK, so this is the meaning Jakon that is G and then it will be finding out the minimum of all these.

452
00:26:11,560 --> 00:26:17,530
So whenever this minimum we will make a point that so we know already that five, almost six is smaller.

453
00:26:17,530 --> 00:26:18,920
So we will be pointing here.

454
00:26:19,390 --> 00:26:25,550
So for the first step, repeating step, we will get it as five K as five.

455
00:26:26,260 --> 00:26:30,710
So does the minimum know likewise in every step it will defining a minimum.

456
00:26:31,420 --> 00:26:32,910
So we have already seen the procedure.

457
00:26:32,930 --> 00:26:38,760
So I will not be tracing it again, just showing the meaning of the code here once that case formed

458
00:26:38,770 --> 00:26:39,580
what we are doing.

459
00:26:39,610 --> 00:26:44,770
You remember we were writing this five and six here, five in this place that is key.

460
00:26:45,280 --> 00:26:54,620
And the near of the key here at this place and this place we were writing K and here in the end of the

461
00:26:54,950 --> 00:27:04,130
of the look on my game of one go my knee and then we were making this as zero, that is near zero zero

462
00:27:04,480 --> 00:27:04,980
zero.

463
00:27:06,010 --> 00:27:11,670
Then we were talking about other services, whether they are nearer to K and we are bringing this in

464
00:27:11,680 --> 00:27:11,950
here.

465
00:27:12,910 --> 00:27:16,910
So this ablation of this loop was finished.

466
00:27:16,950 --> 00:27:23,990
Again, I'm using G here G and the energy cost of G common energy and cost of G.

467
00:27:24,010 --> 00:27:25,960
Komaki, whichever is the smaller.

468
00:27:26,650 --> 00:27:31,150
It will update that if it is, the smaller it will have detected, otherwise it will leave it as it

469
00:27:31,150 --> 00:27:34,000
is this ablation of nearer.

470
00:27:34,300 --> 00:27:37,940
So these are the steps that we were doing here, defining the minimum one.

471
00:27:38,260 --> 00:27:39,760
This is the code for finding minimum.

472
00:27:40,090 --> 00:27:46,900
And we were setting this here and updating the updating the error and that of the for loop.

473
00:27:47,110 --> 00:27:48,050
We will have the result.

474
00:27:48,400 --> 00:27:51,160
So just print this one so I don't have space here.

475
00:27:51,160 --> 00:27:56,700
So I pretty much we have to print this to damage the all the edges we have to print.

476
00:27:57,700 --> 00:28:00,070
So that's all this is the code.

477
00:28:00,070 --> 00:28:06,010
And for the working of a algorithm, once we finish this, I suggest you watch this video once again

478
00:28:06,220 --> 00:28:08,410
and try to do these things by yourself.

479
00:28:08,410 --> 00:28:12,970
If you do it once by yourself, then you can solve any new problem by yourself.

480
00:28:13,600 --> 00:28:18,130
And the main important thing is the structure, deciding the data structure.

481
00:28:18,370 --> 00:28:22,740
You can take the structures also and you can change the logic also a little bit.

482
00:28:22,790 --> 00:28:26,740
But the procedure will be like the way this is done, maybe different.

483
00:28:27,010 --> 00:28:32,440
OK, like here I have taken it as a tool and six columns.

484
00:28:32,590 --> 00:28:36,340
You can make it as six rows and two columns here.

485
00:28:36,340 --> 00:28:40,210
I'm using near array, so I'm just writing near this vertex.

486
00:28:40,330 --> 00:28:44,890
I'm writing you can write on the edge also so you can take a different approach.

487
00:28:44,890 --> 00:28:46,920
So decide what data structure required.

488
00:28:47,320 --> 00:28:51,650
So I suggest you practice it once by yourself using pen and paper.

489
00:28:51,710 --> 00:28:52,390
This portion.

490
00:28:52,390 --> 00:28:53,080
This portion.

491
00:28:53,440 --> 00:28:54,280
This is nothing.

492
00:28:54,610 --> 00:28:55,180
This is nothing.

493
00:28:55,180 --> 00:28:59,530
If you are able to do this, you can write on the logic loops, you know, variable declarations, you

494
00:28:59,530 --> 00:29:01,270
know, everything is simple only.

495
00:29:01,630 --> 00:29:02,860
So that's all in this video.

496
00:29:03,310 --> 00:29:06,820
In the next year, you will find a demonstration of discord.

497
00:29:06,910 --> 00:29:09,280
I relied on the code and right now, you know, everything.

498
00:29:09,280 --> 00:29:13,360
Just I have to run the code and show you and the proof will also be available along with it.

