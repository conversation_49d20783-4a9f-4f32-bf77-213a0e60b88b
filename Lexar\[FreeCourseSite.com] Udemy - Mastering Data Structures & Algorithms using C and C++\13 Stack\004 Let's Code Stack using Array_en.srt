1
00:00:00,150 --> 00:00:03,360
In this video, we'll look at implementation of stock using.

2
00:00:03,900 --> 00:00:10,150
Let us start a project and call it US stock and the language of see language through the project and

3
00:00:10,220 --> 00:00:11,160
the main function.

4
00:00:11,310 --> 00:00:13,160
I'll of all these comments.

5
00:00:13,440 --> 00:00:15,810
First of all, let us define a structure for.

6
00:00:16,830 --> 00:00:23,460
First of all, let us define our structure for stock as we have already seen that for representing a

7
00:00:23,460 --> 00:00:30,390
stock, we need the size of the stack and a top point-to-point on top element in the stock and for storing

8
00:00:30,390 --> 00:00:31,080
elements.

9
00:00:31,260 --> 00:00:34,300
Let us call Arianism as a capital.

10
00:00:34,310 --> 00:00:38,310
Yes, this area will be creating a dynamic 11am, creating a stack.

11
00:00:38,460 --> 00:00:44,130
Then when we create a stack, we have to initialize its size and the top pointer and also create an

12
00:00:44,130 --> 00:00:44,430
audit.

13
00:00:44,520 --> 00:00:47,670
So for that I will have one function called create.

14
00:00:48,120 --> 00:00:54,450
So this create function should a structure strike and call Bio-Reference s-t.

15
00:00:54,600 --> 00:00:59,040
Now here I will take the input from the keyboard, but what is the size of the stack?

16
00:00:59,040 --> 00:01:06,420
And I will create and initialize the stack and the size then sketchiness percentile d I should read

17
00:01:06,420 --> 00:01:11,930
the site so already I have a pointer to a stack so in that I will directly in size.

18
00:01:12,060 --> 00:01:14,300
Now once we know the size we should create an array.

19
00:01:14,640 --> 00:01:19,080
But before creating a tree I will also initialize our top pointer to minus one.

20
00:01:19,110 --> 00:01:22,570
Then this pointer s I should initialize with an array.

21
00:01:23,130 --> 00:01:26,360
So here I should create an array dynamically and heap.

22
00:01:27,060 --> 00:01:33,540
So it's an area of type integers mellark and the size of the array should be same as the size mentioned

23
00:01:33,540 --> 00:01:34,340
for the stack.

24
00:01:34,350 --> 00:01:37,080
And this should be size of integer.

25
00:01:37,230 --> 00:01:45,210
That's a stack that's created sizes set a stop on that is initialized to minus one and it's created

26
00:01:45,210 --> 00:01:47,730
as I'm using mellark function here in the top.

27
00:01:47,730 --> 00:01:54,360
I should include header file that is still in the top, which I will write on a function for displaying

28
00:01:54,360 --> 00:01:55,000
a stack.

29
00:01:55,020 --> 00:02:01,470
So first let us write the function for display which will take a structured stack.

30
00:02:01,710 --> 00:02:02,300
S-t.

31
00:02:02,590 --> 00:02:05,810
This should be called value because it's not going to modify a stack.

32
00:02:05,830 --> 00:02:12,210
This is going to display and for displaying we need a for loops for that I'll take I and should display

33
00:02:12,280 --> 00:02:13,020
from the top.

34
00:02:13,020 --> 00:02:18,750
So I assign I greater than or equal to zero and I minus minus.

35
00:02:18,750 --> 00:02:26,940
I should print all the values so person lead in some space then the values are in inside an object.

36
00:02:27,690 --> 00:02:29,400
So this will print the entire stack.

37
00:02:29,400 --> 00:02:34,860
Then after printing it I should give a new line so I get some formatted output that set the display

38
00:02:34,860 --> 00:02:40,470
function novel right on fuel push and path functions in the stack, right on the actual operations on

39
00:02:40,470 --> 00:02:43,220
the stack that is push and function for first.

40
00:02:43,350 --> 00:02:45,780
So first I will write on push function push.

41
00:02:45,780 --> 00:02:46,590
Why push.

42
00:02:46,590 --> 00:02:51,450
It should take a structure stack and it should take it by address.

43
00:02:51,840 --> 00:02:55,880
And the value that I want to insert here as we have already seen the procedure.

44
00:02:55,890 --> 00:02:59,100
So first of all, I should check that there is some space in the stack or not.

45
00:02:59,100 --> 00:03:05,620
If SD top is equal to Astiz size minus one, then stack is full.

46
00:03:05,640 --> 00:03:07,140
We cannot insert anything.

47
00:03:07,140 --> 00:03:12,760
So print F, stack overflow and C and new line that follows the message I can give.

48
00:03:12,780 --> 00:03:14,360
Else we can insert an element.

49
00:03:14,370 --> 00:03:20,700
So first of all, we should increment the top pointer and then in an array as I should store the value

50
00:03:21,000 --> 00:03:23,720
of location, whatever the value is given X.

51
00:03:23,730 --> 00:03:26,400
So that follows is a symbol of oppression.

52
00:03:26,430 --> 00:03:32,700
Then I will write on pop function of function should return a deleted value return type integer.

53
00:03:32,970 --> 00:03:36,090
It should take a stack Bio-Reference.

54
00:03:36,090 --> 00:03:37,060
That is my address.

55
00:03:37,060 --> 00:03:42,180
So again, Star City and whatever the values deleted, we will take it in some variable.

56
00:03:42,180 --> 00:03:43,830
So initialize it to minus one.

57
00:03:43,950 --> 00:03:48,180
Then before deletion we should confirm whether there are any elements in the stack or not.

58
00:03:48,300 --> 00:03:50,670
That is, we check whether is empty.

59
00:03:51,420 --> 00:03:56,850
So conditions for empty is if OP is equal to minus one, then the stack is empty.

60
00:03:57,150 --> 00:04:04,470
If so, then we will give a message that stack underflow a new line or else we can delete an element.

61
00:04:04,470 --> 00:04:10,740
So for deleting an element of value from array, from location that is stop and also we should decrement

62
00:04:10,740 --> 00:04:14,950
appointers or simultaneously in the same statement I will also write minus minus.

63
00:04:14,950 --> 00:04:21,630
So this will decrease appointed and after deleting after aspart I should say, return X, so this function

64
00:04:21,630 --> 00:04:26,280
will delete the value if it is available, otherwise it will return minus one.

65
00:04:26,280 --> 00:04:29,730
If the stack is empty, the other operations I will write them afterwards.

66
00:04:29,730 --> 00:04:36,630
So first of all, let us use this to push pop and display functions and let us see how the stack is

67
00:04:36,630 --> 00:04:37,080
working.

68
00:04:37,080 --> 00:04:44,260
So first of all, I will create an object of stock as t the stock name is Estie, then I will create

69
00:04:44,260 --> 00:04:46,800
a function bypassing address of stock.

70
00:04:47,340 --> 00:04:55,250
Then I will push fuel values which in the stack the value I want to push first is ten, then push value

71
00:04:55,260 --> 00:04:55,920
twenty.

72
00:04:55,920 --> 00:04:57,440
I'll push a more values.

73
00:04:57,450 --> 00:04:59,940
No let display the contents of.

74
00:05:00,470 --> 00:05:02,640
So for this also, I should send stock.

75
00:05:02,940 --> 00:05:04,350
Steve, let us run this.

76
00:05:04,370 --> 00:05:09,320
I have pushed 10, 20 and 30 so I should get the display from the top of most valuable.

77
00:05:09,720 --> 00:05:11,530
So I should get the values 32 in the end.

78
00:05:11,530 --> 00:05:13,140
And he is asking for the size.

79
00:05:13,140 --> 00:05:17,810
So I will give the size of the five and the values are 30, 20.

80
00:05:17,810 --> 00:05:19,370
And yes, it's working.

81
00:05:19,370 --> 00:05:22,640
The topmost value study recently inserted a value study.

82
00:05:22,800 --> 00:05:26,820
Let us push more than five values and see what happens.

83
00:05:26,860 --> 00:05:31,790
Steve, I push 40 also and then push 50 also.

84
00:05:32,150 --> 00:05:35,560
Then one more push SD 60.

85
00:05:36,320 --> 00:05:38,210
So I'm pushing six rather than the stack.

86
00:05:38,210 --> 00:05:39,230
Let us run it now.

87
00:05:39,320 --> 00:05:44,360
So first of all, I have written the code says that is asking for the size, so I'll give the size of

88
00:05:44,360 --> 00:05:44,860
five.

89
00:05:45,620 --> 00:05:47,730
So only five others are inserted.

90
00:05:47,750 --> 00:05:51,490
You can see 10, 20, 30, 40 and 50, top 50.

91
00:05:51,800 --> 00:05:56,390
And when I was trying to insert 60, it has given a message that stack overflow.

92
00:05:57,270 --> 00:06:01,890
So I got a message that the strike was slow because strike a full week in order to insert more than

93
00:06:01,890 --> 00:06:02,610
five values.

94
00:06:03,180 --> 00:06:07,830
So beyond this, if I tried to insert any more values, I get a message that stack is full, that is

95
00:06:07,830 --> 00:06:08,960
stack overflows.

96
00:06:08,970 --> 00:06:10,260
So I get on inside this.

97
00:06:10,830 --> 00:06:11,990
No, let us strike up.

98
00:06:12,000 --> 00:06:14,880
Function of function is going to return as the result.

99
00:06:14,880 --> 00:06:17,610
So I will directly print X result daily.

100
00:06:17,700 --> 00:06:23,370
I will take a new line then all of a function and send a of this stack.

101
00:06:23,760 --> 00:06:27,100
So whatever the value is deleted it will return and that value will be printed.

102
00:06:27,180 --> 00:06:31,440
So they recently inserted values of fifty so I should get 50 done after that.

103
00:06:31,440 --> 00:06:33,030
When I display I should have the values.

104
00:06:33,540 --> 00:06:36,050
Forty three to enter the site.

105
00:06:36,180 --> 00:06:37,490
So I'm taking the five.

106
00:06:37,740 --> 00:06:38,270
Five.

107
00:06:38,280 --> 00:06:38,730
Yes.

108
00:06:38,730 --> 00:06:43,470
You can see that furphies deleted and the remaining values in the stack are 40, 30, 20.

109
00:06:43,470 --> 00:06:47,250
And then, now let us look at stack underflow message.

110
00:06:47,250 --> 00:06:49,950
So I will remove these three values.

111
00:06:49,980 --> 00:06:51,570
I will delete more than one time.

112
00:06:51,570 --> 00:06:56,170
So print f delete once more bob s.t..

113
00:06:56,460 --> 00:06:58,710
See there are only two values of the stack.

114
00:06:58,830 --> 00:07:03,150
If I delete one more time then I should get a message that is underfloor.

115
00:07:03,810 --> 00:07:09,120
I should not be able to delete and it should return minus one because if the stack is empty it is returning

116
00:07:09,120 --> 00:07:09,850
minus one.

117
00:07:09,870 --> 00:07:12,720
Here you can see its values initialize as minus one.

118
00:07:12,720 --> 00:07:18,690
So it is returning ex non-stock is having only two values that I am pushing and printing, but I am

119
00:07:18,690 --> 00:07:21,060
deleting three values that is three times pop.

120
00:07:21,120 --> 00:07:22,030
So let us run.

121
00:07:22,050 --> 00:07:27,690
I should get a message that Stip underfloor says I'm mentioning 5.5 five only c c.

122
00:07:27,690 --> 00:07:29,850
The values of doubt are deleted.

123
00:07:29,850 --> 00:07:32,160
Values are twenty and ten.

124
00:07:32,310 --> 00:07:39,180
Then after that, when I'm trying to delete stack I the message I got stuck on the floor then the value

125
00:07:39,180 --> 00:07:41,340
that is written is minus one.

126
00:07:41,850 --> 00:07:46,070
Now as both the values are deleted, there is nothing in the stack, so there is nothing to display.

127
00:07:46,110 --> 00:07:49,200
So display function is not ending anything because stack is empty.

128
00:07:49,230 --> 00:07:51,420
That's all we have tried pushing and functions.

129
00:07:51,420 --> 00:07:59,650
Now let us write remaining functions of a stack that is EHP is empty, is a full and Stackpole and in

130
00:07:59,670 --> 00:08:02,660
that we will be completing all the functions of a stack.

131
00:08:02,760 --> 00:08:08,100
So first I will write on each function that will look into a stack at a particular index.

132
00:08:08,100 --> 00:08:10,860
If the element is available then it will return the element.

133
00:08:10,860 --> 00:08:14,790
So it will take a does index, I will call it as index.

134
00:08:15,120 --> 00:08:16,950
So it's taking a stack and the index.

135
00:08:17,100 --> 00:08:19,530
So whatever the value we are getting, we will take it in a variable.

136
00:08:19,530 --> 00:08:24,550
So initially I'll put it at minus one and I should check with the index, given it's valid or not.

137
00:08:24,870 --> 00:08:30,420
So if SD stop that is stop minus index plus one.

138
00:08:30,420 --> 00:08:34,490
If it is less than zero then it is invalid index.

139
00:08:34,500 --> 00:08:40,200
So let us bring the message that invalid index, otherwise we can return the value.

140
00:08:40,390 --> 00:08:42,750
So first of all, let us take the value in the stack.

141
00:08:42,750 --> 00:08:52,080
So estie dot sorry I should take the value from SD stop minus index plus one, then I should return

142
00:08:52,080 --> 00:08:54,060
the value on X.

143
00:08:54,060 --> 00:08:55,980
I will try this function, I will use it.

144
00:08:55,980 --> 00:08:58,170
But before that let me write down other functions.

145
00:08:58,170 --> 00:09:05,100
Also I should have a function called as empty so it should take a stack then it should check whether

146
00:09:05,100 --> 00:09:06,570
top is equal to minus one.

147
00:09:06,570 --> 00:09:12,570
So if it stays at all, is equal to minus one, then return through that.

148
00:09:12,570 --> 00:09:16,530
If it is empty, otherwise return false zettl.

149
00:09:16,650 --> 00:09:17,910
This is empty function.

150
00:09:17,940 --> 00:09:22,770
Then one more function I have to write on that is is a full so I will write function is full which will

151
00:09:22,770 --> 00:09:25,460
take structure stack S.T..

152
00:09:25,530 --> 00:09:28,800
Then here the condition is a physical exercise minus one.

153
00:09:29,160 --> 00:09:36,120
So I will simply say written up sd stop as equals to SD size minus one.

154
00:09:36,120 --> 00:09:40,590
If this condition is true it will return one, otherwise it will return zero.

155
00:09:40,950 --> 00:09:44,970
So if it is full it will return one, otherwise it will return zero is perfect.

156
00:09:44,970 --> 00:09:53,280
Then I should also have a function for stacked up, so I'll give the immense stacked up struct stack

157
00:09:53,520 --> 00:09:54,120
estie.

158
00:09:54,540 --> 00:09:59,310
This is again called the value so it should return topmost element from the stack.

159
00:09:59,550 --> 00:10:03,390
So but before returning it should check whether there are some elements in the stack or not.

160
00:10:03,630 --> 00:10:09,450
If a stack is not empty, it can do that, if not is empty.

161
00:10:10,020 --> 00:10:12,150
So it can call the function that is empty.

162
00:10:12,360 --> 00:10:15,240
If it is not empty, then it will return Estes.

163
00:10:16,170 --> 00:10:18,180
Of value otherwise written.

164
00:10:19,120 --> 00:10:21,370
Minus one, if there is no element.

165
00:10:22,370 --> 00:10:25,560
So it is not empty, then it can return it, otherwise it at minus one.

166
00:10:25,610 --> 00:10:28,520
So that's all we have all the functions ready for.

167
00:10:29,500 --> 00:10:34,190
See, one more thing while explaining one white board, I have not taken care of a stick.

168
00:10:34,190 --> 00:10:36,500
The syntax just for the explanation.

169
00:10:36,500 --> 00:10:37,660
I was writing pseudocode.

170
00:10:37,940 --> 00:10:40,030
Now here you can see the implementation.

171
00:10:40,040 --> 00:10:40,750
Exactly.

172
00:10:41,420 --> 00:10:43,990
So let us use this big function.

173
00:10:44,000 --> 00:10:47,240
So for that I will put a few more values than I will perform.

174
00:10:47,570 --> 00:10:50,300
Expression S.T. 30.

175
00:10:50,300 --> 00:10:53,150
I will put the values what all I have pushed earlier.

176
00:10:53,660 --> 00:10:56,600
So 60 40 is.

177
00:10:58,040 --> 00:11:05,120
Now, instead of pop, I will call it function, so here I will say, OK, so I'm just modifying the

178
00:11:05,120 --> 00:11:05,530
code.

179
00:11:05,540 --> 00:11:08,450
I'm not writing a call once again, not the index.

180
00:11:08,450 --> 00:11:10,730
What I want is one that is the first element.

181
00:11:10,730 --> 00:11:11,720
So I should get 40.

182
00:11:12,560 --> 00:11:14,940
So let us run this is asking for the size.

183
00:11:14,960 --> 00:11:16,580
I have to give the size sizes five.

184
00:11:17,060 --> 00:11:19,250
Yes, uppermost element is 40.

185
00:11:19,310 --> 00:11:20,580
That is in Force One.

186
00:11:20,610 --> 00:11:22,610
Now, I will modify this one here.

187
00:11:23,270 --> 00:11:24,650
I have given index as one.

188
00:11:24,650 --> 00:11:29,450
So that has given access to so I should get element to see the element is not deleted from the stack.

189
00:11:29,450 --> 00:11:32,730
Stack is still having 40 32 Indiantown stack size.

190
00:11:32,750 --> 00:11:33,900
I'll give you the five.

191
00:11:34,310 --> 00:11:36,320
Now the element from the top is two.

192
00:11:36,590 --> 00:11:38,400
Second element is 30.

193
00:11:38,600 --> 00:11:43,660
So yes, the second element from the top and the element are as it is, it's not deleted.

194
00:11:43,760 --> 00:11:44,290
That's all.

195
00:11:44,570 --> 00:11:46,760
This is full and is empty and stacked up.

196
00:11:46,770 --> 00:11:47,610
You can try them.

197
00:11:47,630 --> 00:11:48,910
I have written all functions.

198
00:11:49,400 --> 00:11:55,340
So if at all, anywhere we glad a stock in a C language program, then we will utilize the system.

199
00:11:55,340 --> 00:11:59,090
If at all we require different data type, then we will change the data type of this.

200
00:11:59,600 --> 00:12:01,340
That is the pointer.

201
00:12:01,580 --> 00:12:07,160
And also wherever we are inserting an element there, we will modify it like this, which is taking

202
00:12:07,160 --> 00:12:07,640
an element.

203
00:12:07,640 --> 00:12:09,160
So we will change the data type.

204
00:12:09,590 --> 00:12:15,290
So we have to change the data type of the elementary as well as the other variables which are dealing

205
00:12:15,290 --> 00:12:15,650
with that.

206
00:12:16,010 --> 00:12:19,430
So that's all this is the implementation of start using.

207
00:12:19,820 --> 00:12:23,720
So the program is little lendee and I have not elaborated main function.

208
00:12:23,720 --> 00:12:25,700
I have just tested the functions here.

209
00:12:25,760 --> 00:12:31,750
If you want, you can make a menu driven program for a start with providing all the options like Bush,

210
00:12:31,770 --> 00:12:37,340
Bob IEG and Exemptive Full, all the options so that you can try like one of the beginning programs

211
00:12:37,340 --> 00:12:41,030
I have shown you on many different programs, that same thing you can implement here.

