1
00:00:00,460 --> 00:00:06,700
In this video, we look at the application of <PERSON><PERSON>, one of the application of stack, the parenthesis

2
00:00:06,700 --> 00:00:07,300
matching.

3
00:00:08,430 --> 00:00:15,440
Let me explain what the problem is expression is given or a formula is given, which is parenthesized,

4
00:00:15,690 --> 00:00:20,360
this around brackets are called parentheses or parenthesized.

5
00:00:20,520 --> 00:00:24,480
We have to find out whether the parentheses are balanced or not.

6
00:00:24,990 --> 00:00:30,110
Means for every opening parentheses, there must be a closing parentheses.

7
00:00:30,570 --> 00:00:34,200
This is what we want to check that some problem is very simple.

8
00:00:34,530 --> 00:00:38,110
For every opening bracket, there must be a closing bracket.

9
00:00:38,580 --> 00:00:39,500
So how do we change this?

10
00:00:40,290 --> 00:00:43,420
So for checking, this stack can be used.

11
00:00:43,830 --> 00:00:51,210
So let us see how we can take the help of a stack and find out whether the parameters are balanced or

12
00:00:51,210 --> 00:00:51,600
not.

13
00:00:53,310 --> 00:00:57,780
I'll take a stack here, a stack is ready, so I'm not shooting the point.

14
00:00:57,820 --> 00:00:59,780
It's not like we know how it looks.

15
00:01:00,760 --> 00:01:07,000
Now, let us see the procedure in the procedure, we will scan through this expression by taking one

16
00:01:07,000 --> 00:01:09,590
symbol at a time, one character at a time.

17
00:01:10,090 --> 00:01:10,740
Let us start.

18
00:01:11,050 --> 00:01:12,120
This is the first symbol.

19
00:01:12,430 --> 00:01:13,950
It's the opening bracket.

20
00:01:14,200 --> 00:01:15,550
Push it into the stack.

21
00:01:15,640 --> 00:01:18,160
If it is opening bracket, push it into the stack.

22
00:01:19,440 --> 00:01:20,460
Next symbol.

23
00:01:21,700 --> 00:01:27,010
There's also opening bracket pushed into the stack next to Symbol as a.

24
00:01:28,430 --> 00:01:34,760
So it's an either opening bracket or closing bracket, some other letter, just ignore it and move ahead.

25
00:01:35,630 --> 00:01:40,080
So it means if you're getting opening or closing bracket, then only you have to respond.

26
00:01:40,130 --> 00:01:41,960
Otherwise, just move ahead.

27
00:01:42,530 --> 00:01:43,920
Let us move to the next symbol.

28
00:01:44,450 --> 00:01:46,340
This is plus some move ahead.

29
00:01:46,790 --> 00:01:48,070
Be move ahead.

30
00:01:48,320 --> 00:01:50,120
Now, I got the closing bracket.

31
00:01:50,420 --> 00:01:56,750
So what we should do now, if you are closing bracket, whenever we get the closing bracket bracket

32
00:01:56,760 --> 00:02:00,710
from the stack that set just one symbol from the stack.

33
00:02:01,680 --> 00:02:08,479
Once a blast popped out, but don't push that closing bracket in this tack, it means we found a match.

34
00:02:08,490 --> 00:02:09,930
Let's move ahead.

35
00:02:11,440 --> 00:02:16,650
Next, this is Astrid Schneider opening or closing brackets to move to the next symbol.

36
00:02:16,930 --> 00:02:23,320
This is opening brackets, pushing into the stack, then move to next symbol, the C, just ignore minus,

37
00:02:23,320 --> 00:02:26,210
ignore the ignore closing brackets.

38
00:02:26,220 --> 00:02:29,740
So once we are on the closing bracket, the symbol from the stack.

39
00:02:30,870 --> 00:02:33,420
And move ahead next, a symbol.

40
00:02:34,360 --> 00:02:41,980
Closing bracket, popular symbols from this track move ahead, so we have reached the end of our expression

41
00:02:42,430 --> 00:02:48,190
and check the stack stack is empty, so it means the parentheses are match.

42
00:02:48,700 --> 00:02:51,260
Now, let us take an example there.

43
00:02:51,280 --> 00:02:52,290
It may go wrong.

44
00:02:53,200 --> 00:02:57,320
So let us take an example where the balances are not balanced.

45
00:02:57,370 --> 00:03:00,040
Then what happens here is an expression.

46
00:03:00,040 --> 00:03:03,150
I have added one more extra opening bracket.

47
00:03:03,520 --> 00:03:10,240
So if you count one, two, three, four, opening brackets are there and one, two, three, only three

48
00:03:10,240 --> 00:03:11,430
closing brackets out there.

49
00:03:11,710 --> 00:03:13,580
Let us see what happens in this case.

50
00:03:13,990 --> 00:03:18,000
So one reticular opening bracket I have added that is extra.

51
00:03:18,010 --> 00:03:19,190
So they are not balanced.

52
00:03:19,510 --> 00:03:22,160
So let us see whether this procedure will help us or not.

53
00:03:22,480 --> 00:03:24,400
Let us quickly go through that procedure.

54
00:03:25,330 --> 00:03:26,950
Opening bracket push.

55
00:03:26,950 --> 00:03:28,510
Next one is opening bracket push.

56
00:03:29,020 --> 00:03:30,370
Opening bracket, push it.

57
00:03:30,610 --> 00:03:32,290
A ignored plus ignored.

58
00:03:32,290 --> 00:03:41,770
B, ignore closing bracket symbol move to next, ignore opening bracket, push it then C minus the D

59
00:03:42,640 --> 00:03:44,610
closing bracket out.

60
00:03:45,100 --> 00:03:47,020
Closing bracket pop out.

61
00:03:48,600 --> 00:03:52,630
And of expression, but stack is not empty.

62
00:03:53,070 --> 00:04:00,660
There is something leftover for which there is no match found, so it is not matching parenthesis are

63
00:04:00,660 --> 00:04:01,720
not balanced.

64
00:04:03,090 --> 00:04:08,030
Now, let us take one more example where it may go wrong or it is not balanced.

65
00:04:08,520 --> 00:04:11,710
In this example, I have taken an extra closing bracket.

66
00:04:11,820 --> 00:04:13,420
Let us see what happens in this one.

67
00:04:13,860 --> 00:04:15,120
Let me scan this quickly.

68
00:04:15,540 --> 00:04:21,750
Open bracket, push it into the stock opening which went into the stack, a ignored plus ignored be

69
00:04:21,750 --> 00:04:26,650
ignored closing bracket pop out as to ignore opening bracket.

70
00:04:26,670 --> 00:04:31,530
Push it c ignore minus ignore the ignore closing bracket.

71
00:04:32,640 --> 00:04:34,740
Second closing bracket.

72
00:04:35,310 --> 00:04:38,010
OK, next one more closing bracket.

73
00:04:38,280 --> 00:04:42,510
But the sack is empty so there is no match found for that closing bracket.

74
00:04:42,510 --> 00:04:44,340
There is no opening bracket available.

75
00:04:44,340 --> 00:04:46,380
So that's all from the observation.

76
00:04:46,380 --> 00:04:53,220
We can see that if you are scanning through the expression by pushing opening brackets and popping out

77
00:04:53,220 --> 00:04:59,850
whenever you get closing bracket in this procedure, if at the end the stack is empty, then they are

78
00:04:59,850 --> 00:05:00,870
possibly matching.

79
00:05:01,080 --> 00:05:05,040
If at the end any opening bracket is remaining, it's not matching.

80
00:05:06,990 --> 00:05:13,830
And if we have one closing bracket, but the stack is empty, there is no opening bracket, then also

81
00:05:13,830 --> 00:05:14,520
does not match.

82
00:05:14,640 --> 00:05:19,650
So there are two situations when it is not matching, you have finished the procedure, but there is

83
00:05:19,650 --> 00:05:20,240
something left.

84
00:05:20,310 --> 00:05:25,140
One second situation you are trying to pop out, but the stack is 70.

85
00:05:25,380 --> 00:05:28,120
So let us write a procedure and see this one.

86
00:05:28,650 --> 00:05:31,010
But before that, I'll show you one example.

87
00:05:31,620 --> 00:05:32,740
Look at this example.

88
00:05:33,090 --> 00:05:36,220
This is not a proper politicization.

89
00:05:36,600 --> 00:05:38,280
This is not a proper parenticide mission.

90
00:05:38,760 --> 00:05:43,690
Parenthesized mission is done upon an operator for including the operands that belong to that operator.

91
00:05:43,950 --> 00:05:49,610
So here operand an operator out there that are in the bracket of the second opening.

92
00:05:49,830 --> 00:05:50,930
So it's also the bracket.

93
00:05:51,300 --> 00:05:57,810
So this is not proper patronization, but let us check whether it is a balanced parenthesis or not.

94
00:05:58,170 --> 00:06:04,260
Let us try it opening, but I could push it into the stack a ignore plus ignore closing bracket or the

95
00:06:04,260 --> 00:06:11,040
symbol from the stack, then opening bracket, push it in the stack, start, ignore C minus and ignore

96
00:06:11,040 --> 00:06:12,120
all of them closing bracket.

97
00:06:12,160 --> 00:06:16,410
But the symbols on this tight end of the expression, there is nothing in the stack.

98
00:06:16,420 --> 00:06:18,150
So it means parentheses are matching.

99
00:06:19,100 --> 00:06:25,880
So conclusion is this procedure doesn't check whether the parentheses are properly given or not.

100
00:06:26,210 --> 00:06:30,140
It will check whether the number of patterns are matching or not.

101
00:06:30,140 --> 00:06:32,360
For every opening, there is a closing bracket or not.

102
00:06:33,020 --> 00:06:36,360
So just balancing of parentheses, actual check.

103
00:06:37,160 --> 00:06:39,170
Let me write on the program and show you.

