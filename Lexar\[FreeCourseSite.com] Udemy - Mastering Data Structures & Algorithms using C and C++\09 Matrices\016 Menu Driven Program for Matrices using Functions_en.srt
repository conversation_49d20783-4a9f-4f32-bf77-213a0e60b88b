1
00:00:00,210 --> 00:00:06,960
For writing the program, using functions, I'll give you the idea how a program can be written on the

2
00:00:06,960 --> 00:00:08,660
menu for the program will be the same.

3
00:00:08,670 --> 00:00:16,059
It'll be creating a matrix and changing the human reading the element as well as displaying the element.

4
00:00:16,650 --> 00:00:18,920
So how the program should look like this?

5
00:00:18,970 --> 00:00:25,040
A main function for the program main function is having an UTI for storing the elements.

6
00:00:25,080 --> 00:00:28,260
So for that, I would take a pointer then.

7
00:00:29,930 --> 00:00:31,220
And is the dimension.

8
00:00:32,540 --> 00:00:38,540
The rest of the things, whatever required, we will see them inside this, I should create an array

9
00:00:38,780 --> 00:00:45,080
that I mentioned first of all, then create an area of required size, new kind of size.

10
00:00:46,520 --> 00:00:52,670
And for diagonal markets and for the same England and might do this also in England plus one <PERSON>,

11
00:00:52,670 --> 00:00:58,910
then followed by that other us, whatever the fall, whatever the number of non-zero elements that should

12
00:00:58,920 --> 00:01:05,330
be mentioned, then the program should have a do a loop and menu options and everything.

13
00:01:05,540 --> 00:01:07,490
CASIS here.

14
00:01:07,490 --> 00:01:13,520
We should have a fish case and right on the case for each case we should call a function, then how

15
00:01:13,520 --> 00:01:15,250
to function should be written.

16
00:01:15,260 --> 00:01:21,140
I will write on the prototype of the function and show you first function is void.

17
00:01:21,680 --> 00:01:27,470
Create discrete function needs adding another dimension.

18
00:01:28,550 --> 00:01:31,420
So it needs basically an array where it has to store the elements.

19
00:01:31,430 --> 00:01:34,160
So this main function should pass an array to that one.

20
00:01:34,550 --> 00:01:36,270
So this should take another.

21
00:01:36,500 --> 00:01:39,560
I will take the name as E or X, any name you can take.

22
00:01:39,560 --> 00:01:42,770
So but take the Nimroz if it needs or.

23
00:01:43,760 --> 00:01:46,010
This is all we can take Arreaza parameter.

24
00:01:46,580 --> 00:01:54,950
We can give square brackets also or even we can use a pointer as a staff as lastic the next for creating

25
00:01:54,950 --> 00:01:55,280
an array.

26
00:01:55,290 --> 00:01:56,450
We need the dimension.

27
00:01:56,780 --> 00:01:59,690
Whatever the mattocks may be, we need dimensions.

28
00:01:59,690 --> 00:02:02,150
So based on the dimension we will need that many elements.

29
00:02:02,490 --> 00:02:04,750
So this needs dimension.

30
00:02:04,760 --> 00:02:10,910
So let us Galdikas and only then this will be reading the elements by using two fondles.

31
00:02:11,180 --> 00:02:21,500
So Hoback, all that one is created by a and and not a logic for reading, not a code for reading the

32
00:02:21,500 --> 00:02:22,100
elements.

33
00:02:22,100 --> 00:02:25,100
We will be taking it the next.

34
00:02:25,280 --> 00:02:30,500
We should have a function for getting an element from a given index.

35
00:02:30,860 --> 00:02:39,980
So for that function names get it also needs an array which are OK so that other we have to pass from

36
00:02:39,980 --> 00:02:42,560
here and whatever the name you want you can take it.

37
00:02:42,570 --> 00:02:46,250
So let us take it as X or any name we can give.

38
00:02:46,250 --> 00:02:47,630
That is a pointer pointing to this.

39
00:02:48,290 --> 00:02:49,580
So how it looks like it.

40
00:02:49,590 --> 00:02:53,870
Suppose this is an array is a pointer pointing to it.

41
00:02:55,460 --> 00:03:03,020
Then they pass this as a parameter, so this X will be a pointer pointing to this on that A is a pointer

42
00:03:03,020 --> 00:03:04,180
that is pointing to this one.

43
00:03:04,190 --> 00:03:08,360
So you can use any name that is belonging to that function then.

44
00:03:08,370 --> 00:03:09,220
What more do we need?

45
00:03:10,190 --> 00:03:12,560
What are the indices I.

46
00:03:15,570 --> 00:03:24,390
And Jamie, so this will lead elements from that particular index and a written statement, so it should

47
00:03:24,390 --> 00:03:28,590
have the return type as integer because we have integer elements only.

48
00:03:30,320 --> 00:03:31,160
This is forget.

49
00:03:33,180 --> 00:03:39,180
Then what about the dimension, if you think that the dimensions may be wrong, you want to test them,

50
00:03:39,180 --> 00:03:40,920
but other dimensions are perfect or not.

51
00:03:41,210 --> 00:03:43,130
Then you have to pass and also to that.

52
00:03:45,220 --> 00:03:55,420
The mix's said function Forcett function, it needs a three pointer Glenorie integer ie integer J that

53
00:03:55,420 --> 00:04:01,170
is indices role in column number and also X, the element that you want to set.

54
00:04:01,960 --> 00:04:02,920
And this is void.

55
00:04:04,810 --> 00:04:06,850
So the body, we will be implementing it.

56
00:04:07,390 --> 00:04:12,280
So all of you have the idea how the body should look like, so I'm just riding the prototype of a function.

57
00:04:12,700 --> 00:04:15,390
So while riding the program, I will be expanding that fund.

58
00:04:16,140 --> 00:04:19,990
Then the last function is a display wide display function.

59
00:04:20,170 --> 00:04:28,570
It also needs a ring and it needs to dimension what other dimensions so that it can display and other

60
00:04:28,810 --> 00:04:30,340
of that dimension and cross.

61
00:04:30,340 --> 00:04:36,310
And so this functions fully implement and this will have minimal and it will be calling those functions

62
00:04:36,310 --> 00:04:37,630
whichever function it wants.

63
00:04:38,320 --> 00:04:41,950
So this is how the design of our program looks like.

64
00:04:42,340 --> 00:04:47,230
So in the following videos, you will find the program for all these type of Matisses and all those

65
00:04:47,230 --> 00:04:49,160
programs will be following this approach.

66
00:04:49,180 --> 00:04:53,050
So I have just discussed the approach here, how those programs are working.

