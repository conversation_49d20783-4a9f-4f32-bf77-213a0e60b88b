1
00:00:00,180 --> 00:00:07,640
Now, the topic is implementation of Acuil using two stocks actually to itself as a data structure,

2
00:00:07,650 --> 00:00:15,780
we can implement it using R.E. or else using linguist but if you have two stacks we can make them well

3
00:00:15,780 --> 00:00:17,020
just like a cube.

4
00:00:17,490 --> 00:00:19,050
Let us see how it is possible.

5
00:00:19,410 --> 00:00:26,790
So here I have two stacks, stack one and that stack two and here I have some set of elements that has

6
00:00:26,790 --> 00:00:35,730
to be actually inserted and deleted from a Q and Q or do I have Google but I have stacks now let us

7
00:00:35,740 --> 00:00:37,530
nq and dequeue some elements.

8
00:00:37,860 --> 00:00:39,010
First element six.

9
00:00:39,030 --> 00:00:42,420
I want to nq so I should inserted in stack S1.

10
00:00:44,070 --> 00:00:52,380
<PERSON><PERSON><PERSON><PERSON> is one the next I want to ask you three, supposedly <PERSON><PERSON><PERSON>, I want to kill nine <PERSON><PERSON><PERSON>

11
00:00:52,920 --> 00:00:53,910
and kill five.

12
00:00:54,060 --> 00:00:55,590
So Pushilin, Aslan.

13
00:00:57,520 --> 00:01:02,780
Now I want to delete I have inserted four elements in it, you know, want to delete.

14
00:01:03,250 --> 00:01:07,420
So if I want to delete, I should get Ballymun six as powerful.

15
00:01:07,840 --> 00:01:09,760
But these elements are in stack.

16
00:01:09,790 --> 00:01:13,200
If I delete any modern element, I'll be getting five.

17
00:01:13,540 --> 00:01:14,590
How do I get six?

18
00:01:15,040 --> 00:01:23,980
So transfer all these elements in to stack to the first five will go here then about nine and push it

19
00:01:23,980 --> 00:01:24,310
here.

20
00:01:24,910 --> 00:01:29,310
Bubble three and push it here, bubble six and push it here.

21
00:01:29,770 --> 00:01:31,960
Not all these elements are transferred there.

22
00:01:32,170 --> 00:01:33,940
They are in stack to know.

23
00:01:33,940 --> 00:01:36,120
I can delete an element from Stack too.

24
00:01:36,160 --> 00:01:37,620
So what is the topmost element.

25
00:01:37,690 --> 00:01:39,580
Six years as powerful.

26
00:01:39,790 --> 00:01:41,140
I should get six first.

27
00:01:41,380 --> 00:01:43,870
So yes, six is deleted from the stack.

28
00:01:44,470 --> 00:01:45,820
So the element six is gone?

29
00:01:45,970 --> 00:01:46,440
I think so.

30
00:01:46,450 --> 00:01:47,200
What I should do.

31
00:01:48,240 --> 00:01:52,110
If you remember thinking I should transfer the elements back again to this one.

32
00:01:53,730 --> 00:01:55,170
No, we don't have to.

33
00:01:55,430 --> 00:01:56,260
We don't have to.

34
00:01:57,000 --> 00:02:01,880
That's what our common sense folks that we sent there and Bob Dole not send them back.

35
00:02:01,890 --> 00:02:02,780
No, no need.

36
00:02:03,330 --> 00:02:05,910
See if at all they want to delete one more element.

37
00:02:06,030 --> 00:02:12,120
Which element should be deleted as Bercu as Burcu, the next element was a three certainly should be

38
00:02:12,120 --> 00:02:12,600
deleted.

39
00:02:12,900 --> 00:02:16,950
So there but three topmost elements to survive will bring it here.

40
00:02:17,170 --> 00:02:20,280
Let it be there only will it work like this.

41
00:02:21,000 --> 00:02:25,770
Let us continue insertion and deletion of a few more elements, NQ and dequeue a few more elements.

42
00:02:26,010 --> 00:02:27,460
Next element I want to include.

43
00:02:27,500 --> 00:02:28,860
So the next element is forward.

44
00:02:29,010 --> 00:02:31,710
So this is for next element.

45
00:02:31,710 --> 00:02:32,520
I want to include.

46
00:02:32,520 --> 00:02:34,040
That is two for two.

47
00:02:34,260 --> 00:02:37,710
So it means while anchoring, I'm just pushing an element in this one.

48
00:02:37,710 --> 00:02:38,010
Yes.

49
00:02:38,010 --> 00:02:39,220
Push an element in this one.

50
00:02:39,840 --> 00:02:47,670
Now let us dequeue delete one element three will come out from the Stackpool is delete from here, delete

51
00:02:47,670 --> 00:02:48,420
one more element.

52
00:02:48,420 --> 00:02:52,410
So nine will come out from Stack to see they are coming in the same order.

53
00:02:52,410 --> 00:02:55,940
Six three nine six nine delete one more element.

54
00:02:56,160 --> 00:03:02,930
So five incommode from Stackpool might want to delete actually which element should come out.

55
00:03:02,940 --> 00:03:04,890
Four but stack two is empty.

56
00:03:05,280 --> 00:03:08,310
So what to do now if a stack two is empty.

57
00:03:08,550 --> 00:03:10,950
Trust for the elements from one to two.

58
00:03:11,190 --> 00:03:18,990
So Poppo two and push it in as to pop out forward and push it in as to now delete the element.

59
00:03:19,200 --> 00:03:20,670
So four will be deleted.

60
00:03:20,940 --> 00:03:22,590
Yes, it's worked in perfect.

61
00:03:23,560 --> 00:03:25,150
Now, let us see how they are working.

62
00:03:25,270 --> 00:03:30,190
I will write down some functions or algorithms, I will not write complete executable function.

63
00:03:30,220 --> 00:03:32,280
Now let us see how NQ is done.

64
00:03:32,370 --> 00:03:37,000
Let us start once again and you first eliminate six.

65
00:03:37,420 --> 00:03:40,150
Push it in this one and you next element.

66
00:03:40,570 --> 00:03:44,550
Push it in this one and next Telemann, push it in this one.

67
00:03:44,860 --> 00:03:49,770
So anchorman's always push in as so NQ operation.

68
00:03:50,110 --> 00:03:54,390
So anchorman's insert or just push an element in the stack s1.

69
00:03:54,400 --> 00:03:58,060
This is what we are doing whenever we are anchoring.

70
00:03:59,770 --> 00:04:06,070
Then whenever we are dickering, deleting an element, what we are doing, legacy transfer all the elements

71
00:04:06,070 --> 00:04:10,690
here, so nine, three, six, then delete the element.

72
00:04:10,720 --> 00:04:11,670
So six is gone.

73
00:04:12,850 --> 00:04:16,180
So we are transferring the elements and deleting from as to.

74
00:04:17,360 --> 00:04:24,380
Again, they want to delete, so don't trust the elements directly delete from us to directly delete

75
00:04:24,380 --> 00:04:24,690
from it.

76
00:04:24,710 --> 00:04:32,330
So it means if a screw is having some elements directly delete them, if S2 is not having any elements,

77
00:04:32,330 --> 00:04:35,340
transfer the elements from one to two, then delete.

78
00:04:35,720 --> 00:04:39,770
So I was right on dequeue function so they could function.

79
00:04:39,770 --> 00:04:41,360
I, I'm writing c the first thing.

80
00:04:41,360 --> 00:04:47,130
And Bhiku, what I should do is whether S2 is having some elements or not check it if it is empty trash

81
00:04:47,240 --> 00:04:49,230
for the elements from S1 S2.

82
00:04:49,580 --> 00:04:52,380
So first to check whether S2 is empty or not.

83
00:04:53,210 --> 00:05:01,430
If it is empty as to if S2 is empty then what I should do for the elements from S1 two is to see I'm

84
00:05:01,430 --> 00:05:07,070
taking Esslin also here, whether it is empty, if as one is also empty nesters also and they both are

85
00:05:07,070 --> 00:05:08,270
empty, is empty.

86
00:05:08,270 --> 00:05:11,010
So I will give a message that is empty.

87
00:05:12,080 --> 00:05:14,810
So here, if S2 is empty then check.

88
00:05:14,810 --> 00:05:20,150
If one is all empty, then give a message to empty and return X so that X will be minus fundaments.

89
00:05:20,540 --> 00:05:22,790
Elements are not there is empty.

90
00:05:22,790 --> 00:05:25,640
So that's why we return minus one else.

91
00:05:25,830 --> 00:05:32,330
Otherwise, if it's one is not empty, transfer all the elements from S1 to S2.

92
00:05:32,630 --> 00:05:36,620
So using a loop I will transfer all the elements until STAC one becomes empty.

93
00:05:37,400 --> 00:05:38,620
So I write on the code there.

94
00:05:39,050 --> 00:05:40,160
So here is the code.

95
00:05:40,160 --> 00:05:46,750
While not is empty as fun until S is not empty, property values are much fun and put them into a store

96
00:05:46,880 --> 00:05:48,110
so the stress loading will be.

97
00:05:49,250 --> 00:05:55,360
Softer tone setting, it will come out of health and out if now a returned element.

98
00:05:56,000 --> 00:06:00,560
So I'm returning an element that is popped out from a stool once again, I'll explain you.

99
00:06:00,770 --> 00:06:04,300
If S2 is empty, then translate.

100
00:06:04,550 --> 00:06:10,300
If it is not empty, then it will directly return the element from S2.

101
00:06:10,820 --> 00:06:13,010
If S2 is empty, then it will enter into this.

102
00:06:13,010 --> 00:06:16,210
If otherwise, directly, it will pop out the element from S2.

103
00:06:17,000 --> 00:06:21,040
So we have to check whether was empty or not because we cannot believe if it is empty.

104
00:06:21,620 --> 00:06:25,630
But while pushing, I did not check whether the stack was full or not.

105
00:06:25,880 --> 00:06:29,420
So I assume that these attacks are implemented using Lindqvist.

106
00:06:30,970 --> 00:06:38,200
So that's all you can be implemented using two tax, so there's a student exercise, you have to write

107
00:06:38,200 --> 00:06:43,120
on an application of the program for implementing you using tools tax.

