1
00:00:00,600 --> 00:00:05,880
In this video, we will look at a program for Krus<PERSON> Salgado them in the previous video, we saw the

2
00:00:05,880 --> 00:00:06,990
working of cross-cultural tell.

3
00:00:07,410 --> 00:00:08,130
It's very simple.

4
00:00:08,130 --> 00:00:09,940
Always select the minimum cost edge.

5
00:00:10,230 --> 00:00:12,230
That's the simplest one.

6
00:00:12,810 --> 00:00:15,960
We will write a program for this one for writing a program.

7
00:00:15,970 --> 00:00:18,170
I have taken an example graph here.

8
00:00:18,180 --> 00:00:24,360
This is there already in Portugal and I have seen the same graph now for writing a program to transfer

9
00:00:24,360 --> 00:00:28,720
important data structure and the operations.

10
00:00:28,920 --> 00:00:32,549
So first of all, beside the data structure, every little structure is perfect.

11
00:00:32,820 --> 00:00:37,680
And if you are able to work on it manually using pen and paper, never letting the program become too

12
00:00:37,680 --> 00:00:38,370
-- easy.

13
00:00:38,730 --> 00:00:40,200
So I have a data structure here.

14
00:00:40,230 --> 00:00:41,280
Let us look at this.

15
00:00:41,310 --> 00:00:42,390
What are the data structures?

16
00:00:42,390 --> 00:00:46,190
I have see, I have taken array of edges.

17
00:00:46,590 --> 00:00:51,480
So if you remember in Prem's, I have taken or are just something like this, that is cosmetics I have

18
00:00:51,480 --> 00:00:51,810
taken.

19
00:00:52,110 --> 00:00:57,320
But here I am not using adjacency matrix, but I am using a list of edges.

20
00:00:57,720 --> 00:00:59,180
So let us see what are the contents.

21
00:00:59,210 --> 00:01:00,370
I'll let you have kept it fit.

22
00:01:00,750 --> 00:01:03,840
So this is an array of dimension.

23
00:01:04,200 --> 00:01:07,280
Three rows and nine columns.

24
00:01:07,440 --> 00:01:08,130
Why nine.

25
00:01:08,310 --> 00:01:09,750
Because there are nine edges.

26
00:01:10,260 --> 00:01:13,740
So if you look at just foser just one, two, two, one, two, two.

27
00:01:13,950 --> 00:01:17,910
And the cost is twenty five one to two costis twenty five.

28
00:01:18,540 --> 00:01:18,920
Right.

29
00:01:19,320 --> 00:01:26,610
So all is containing first vertex no then second vertex number and the Nazrul that is to index two is

30
00:01:26,610 --> 00:01:27,930
containing the cost.

31
00:01:28,350 --> 00:01:35,540
If you look at the next one, common six from one that is an extra six, this value is fine, one six

32
00:01:35,550 --> 00:01:36,480
as well as five.

33
00:01:37,120 --> 00:01:38,510
No mistake about X2.

34
00:01:38,900 --> 00:01:43,260
Two, two, three, two, two, two, seven, 10, two, two, three, two, one, two, two, seven,

35
00:01:43,270 --> 00:01:43,490
10.

36
00:01:43,650 --> 00:01:45,240
Likewise, it is all the different.

37
00:01:45,570 --> 00:01:47,640
Then you are getting on the working of the algorithm.

38
00:01:47,660 --> 00:01:50,190
Always find the minimum wage from this.

39
00:01:50,190 --> 00:01:51,370
I have to select minimum wage.

40
00:01:51,390 --> 00:01:57,000
Not a big task because the minimum and included in the solution, the next minimum, including the solution,

41
00:01:57,630 --> 00:02:02,130
but the adjust that I have already included, that I should not include them again.

42
00:02:02,790 --> 00:02:08,340
So for that hotel room, whether it included or not included, when we are selecting anyone, you should

43
00:02:08,340 --> 00:02:09,470
know that they're not included.

44
00:02:09,870 --> 00:02:16,660
So for that, I have an idea of size nine zero to eight to make them included or not included.

45
00:02:16,980 --> 00:02:23,100
So from here, whichever edge selecting Almog, this has one there to show that this is included if

46
00:02:23,100 --> 00:02:24,390
it is zero, which does not include.

47
00:02:24,810 --> 00:02:30,870
So this array will maintain the record of the edges so far included in the solution then for giving

48
00:02:30,870 --> 00:02:31,410
the solution.

49
00:02:31,410 --> 00:02:33,120
Also, I need a data structure.

50
00:02:33,360 --> 00:02:38,900
So that is I will take a data structure here that is, which we have already used this Antrim's algorithm.

51
00:02:39,210 --> 00:02:41,860
So how many are just will be getting six edgett.

52
00:02:42,090 --> 00:02:42,750
So zero.

53
00:02:42,750 --> 00:02:44,610
One, two, three, four, five.

54
00:02:44,640 --> 00:02:47,400
So this is zero two five.

55
00:02:47,400 --> 00:02:48,330
That is total six.

56
00:02:48,720 --> 00:02:52,070
So I'll be storing the result there in that data structure.

57
00:02:52,230 --> 00:02:59,070
Whenever you are picking up a minimum age, you have to make sure that inclusion of that is not forming

58
00:02:59,070 --> 00:02:59,760
a cycle.

59
00:03:00,240 --> 00:03:01,870
How to detect the cycle?

60
00:03:02,430 --> 00:03:11,400
Yes, for detecting a cycle, we will use set operations that are union and find so far that I have

61
00:03:11,550 --> 00:03:15,390
array called sector and I need there are seven.

62
00:03:15,390 --> 00:03:16,080
What is a three.

63
00:03:16,080 --> 00:03:16,710
One, two, seven.

64
00:03:16,710 --> 00:03:18,300
So one, two seven I have taken.

65
00:03:18,630 --> 00:03:20,700
So starting from zero, I'm not using this one.

66
00:03:20,700 --> 00:03:24,330
So I put across my there, I'm not using it, I'll be using it from here.

67
00:03:24,600 --> 00:03:26,520
And all these are filled with minus one.

68
00:03:26,520 --> 00:03:31,560
Initially the whole sector union that is set Operation Union and Find works.

69
00:03:31,560 --> 00:03:33,840
I will show you why we're tracing this one.

70
00:03:33,840 --> 00:03:40,140
So I will trace this cross-cultural gaudium the procedure which we have already seen by using this data

71
00:03:40,140 --> 00:03:42,780
structure, how they will work in this data structure.

72
00:03:42,780 --> 00:03:47,580
Then afterwards I will write a program what all I'm doing here for the same thing.

73
00:03:47,580 --> 00:03:48,510
I will write a program.

74
00:03:49,020 --> 00:03:54,210
So let us see the tracing of Google's algorithm upon this data structure, because we have this graph

75
00:03:54,210 --> 00:03:56,780
in the form now in this form, this is on paper.

76
00:03:56,790 --> 00:03:57,870
This is inside memory.

77
00:03:59,130 --> 00:04:02,550
So let's see the working for tracing algorithm.

78
00:04:02,550 --> 00:04:05,820
We already know that Kruskal doesn't have any initial steps.

79
00:04:05,820 --> 00:04:10,050
Everything is repeating step and following whether there is a cycle or not.

80
00:04:10,050 --> 00:04:17,640
We use sets and already you have blown what is union and find and the weighted union and collapsing

81
00:04:17,640 --> 00:04:18,930
find both we have seen.

82
00:04:19,260 --> 00:04:21,390
So let us follow the same thing here.

83
00:04:21,660 --> 00:04:24,530
We will use that one to check whether there is a cycle or not.

84
00:04:24,930 --> 00:04:26,100
Let us start the procedure.

85
00:04:26,310 --> 00:04:27,200
What is the procedure?

86
00:04:27,510 --> 00:04:29,990
Find out a minimum costs.

87
00:04:30,780 --> 00:04:32,180
Find out a minimum crosshatch.

88
00:04:32,490 --> 00:04:35,340
So from this we will trace this and find out the minimum.

89
00:04:35,550 --> 00:04:37,590
So first minimum is five.

90
00:04:37,920 --> 00:04:41,280
So let us call this as G that is call it as G.

91
00:04:41,850 --> 00:04:42,210
Right.

92
00:04:42,450 --> 00:04:44,790
And here is it included.

93
00:04:44,790 --> 00:04:45,910
No, it's not included.

94
00:04:45,930 --> 00:04:47,640
So now this is minimum.

95
00:04:47,640 --> 00:04:51,660
So find out whether it is forming up cycle or not.

96
00:04:52,020 --> 00:04:54,840
So far, one and six check who is the parent.

97
00:04:55,080 --> 00:04:58,110
If their parent the same, they belong to the same segments.

98
00:04:58,270 --> 00:05:00,000
The cycle doesn't matter.

99
00:05:00,740 --> 00:05:07,890
So let us check, find find one minus one, fine, six minus six, minus one.

100
00:05:08,210 --> 00:05:10,540
So they are parents of themselves, right?

101
00:05:10,790 --> 00:05:14,470
So if I draw it here, this is one under six.

102
00:05:15,500 --> 00:05:16,920
So they are not forming a circle.

103
00:05:17,000 --> 00:05:18,320
Their parents are different ones.

104
00:05:18,320 --> 00:05:21,530
But in this one and six billion to they are not forming a circle.

105
00:05:21,530 --> 00:05:25,040
So beginning with this one, let let us include this in the solution.

106
00:05:25,040 --> 00:05:27,470
So let us put it here for filling this one.

107
00:05:27,800 --> 00:05:32,990
This is one and six and here are the one because it is included magnet as one.

108
00:05:34,190 --> 00:05:36,190
This one you have included credit as one.

109
00:05:36,560 --> 00:05:37,630
So this is over.

110
00:05:38,090 --> 00:05:40,820
No perform union upon them.

111
00:05:40,820 --> 00:05:44,110
Weighted union on one comma six.

112
00:05:44,420 --> 00:05:46,160
So I we have already learned the union.

113
00:05:46,160 --> 00:05:47,600
We will check this is minus one.

114
00:05:47,600 --> 00:05:54,060
That is also minus one who is minimum one is not smaller so make six other as a parent.

115
00:05:54,080 --> 00:05:58,770
So here I will write six and now there are two to do what it says here.

116
00:05:58,790 --> 00:06:00,610
So this will be minus two.

117
00:06:01,190 --> 00:06:03,550
So this comes under this one.

118
00:06:03,650 --> 00:06:06,960
One comes under this and this will be pointing on this one.

119
00:06:07,100 --> 00:06:12,470
This is just like this one says that my parents six and six is that underpin it of myself.

120
00:06:12,470 --> 00:06:16,530
I am the parent of a set and I have two versus minus two.

121
00:06:16,940 --> 00:06:18,290
Next step is over.

122
00:06:18,950 --> 00:06:20,810
Now let us do it repeatedly.

123
00:06:21,020 --> 00:06:22,090
Let us do it repeatedly.

124
00:06:22,400 --> 00:06:24,140
Find the minimum next minimum.

125
00:06:24,140 --> 00:06:26,150
Who is the next me out of all these?

126
00:06:26,150 --> 00:06:27,420
This eight is minimum.

127
00:06:27,440 --> 00:06:30,200
So this one, it is not included.

128
00:06:30,200 --> 00:06:30,440
Right.

129
00:06:30,440 --> 00:06:31,540
Ford is not included.

130
00:06:31,550 --> 00:06:33,640
So that's all this is taken.

131
00:06:33,770 --> 00:06:40,190
And this is minus one not being to the three in four or not find three and find four.

132
00:06:40,280 --> 00:06:41,090
Find three.

133
00:06:41,270 --> 00:06:44,100
Three three is a parent of itself.

134
00:06:45,020 --> 00:06:47,540
Fine for, for the period of itself.

135
00:06:47,760 --> 00:06:48,280
Right.

136
00:06:48,310 --> 00:06:49,200
These are two different.

137
00:06:50,300 --> 00:06:56,570
So there is no cycle, it will not form a cycle then three or four which is a smaller but are equal

138
00:06:56,570 --> 00:06:56,860
only.

139
00:06:57,140 --> 00:07:00,740
So we'll update this one and make that as a parent.

140
00:07:01,070 --> 00:07:08,060
So we will write forward here and this will take this minus to this means that three says that my opinion

141
00:07:08,060 --> 00:07:09,560
is for right.

142
00:07:10,730 --> 00:07:18,650
And forces that I am the leader of the set or head of the set, and there are two what ifs in this set,

143
00:07:18,800 --> 00:07:22,660
that is three and four under three and four are included here.

144
00:07:22,820 --> 00:07:27,080
Three and four are included to perform union also.

145
00:07:27,560 --> 00:07:29,240
And also we have included this one.

146
00:07:30,320 --> 00:07:35,660
That's it's the same thing will repeat again, find out minimum, which is minimum out of all these,

147
00:07:35,660 --> 00:07:40,520
which is not yet included, don't include one and formants don't take this five and eight other than

148
00:07:40,520 --> 00:07:42,240
this, this is the next minimum.

149
00:07:42,590 --> 00:07:51,770
So this is G 10 Asaji find two and seven find to find two minus one.

150
00:07:51,890 --> 00:07:53,370
Fine, seven minus one.

151
00:07:53,690 --> 00:07:58,040
So this means that this is so seven and this is two.

152
00:07:58,040 --> 00:07:59,050
I will fight like this.

153
00:07:59,060 --> 00:08:03,410
They are independent now then it means it's not forming a cycle.

154
00:08:03,410 --> 00:08:09,740
So you can include this one two comma seven, two and seven not included here and this one three.

155
00:08:09,860 --> 00:08:10,970
Esmay does one.

156
00:08:11,210 --> 00:08:12,570
It means it is included.

157
00:08:13,250 --> 00:08:18,820
Now we have to perform union so two one seven Batard minus one minus one.

158
00:08:18,980 --> 00:08:23,680
So the school will be pointing on seven and here I will write seven.

159
00:08:23,990 --> 00:08:27,830
Now seven says that there are two bodices under the set.

160
00:08:28,250 --> 00:08:28,570
Two.

161
00:08:29,000 --> 00:08:31,410
So we've got three different sets.

162
00:08:31,430 --> 00:08:32,600
No, not again.

163
00:08:32,600 --> 00:08:34,309
Repeat same again.

164
00:08:34,330 --> 00:08:39,650
The repeated find out minimum out of all this right after ten.

165
00:08:39,650 --> 00:08:41,490
The next minimum is to end this one.

166
00:08:41,510 --> 00:08:44,840
So this is G is being included two.

167
00:08:44,840 --> 00:08:45,600
Is it included.

168
00:08:45,680 --> 00:08:46,630
No, not included.

169
00:08:46,910 --> 00:08:49,910
Then find two and find three to find out.

170
00:08:49,910 --> 00:08:51,850
But they are forming a cycle.

171
00:08:52,220 --> 00:08:55,490
Find two to who is a parent of two.

172
00:08:55,790 --> 00:08:59,420
Seven find the three who is a parent of three.

173
00:08:59,690 --> 00:09:00,710
That is four.

174
00:09:01,190 --> 00:09:05,720
So yes, they are different so we can join them.

175
00:09:05,730 --> 00:09:07,040
It is not forming a cycle.

176
00:09:07,400 --> 00:09:10,130
So who is a smaller four or seven.

177
00:09:10,270 --> 00:09:11,390
Both are equal only.

178
00:09:11,600 --> 00:09:15,490
So what we do here is at this place we will write seven.

179
00:09:15,860 --> 00:09:20,080
Now this is that the total for what it says under this one.

180
00:09:20,450 --> 00:09:22,520
So this for comes under this.

181
00:09:22,550 --> 00:09:26,150
I will try it once again here four and under that we have three.

182
00:09:26,450 --> 00:09:28,430
And this is included here.

183
00:09:29,950 --> 00:09:36,610
So they are forming a single set of activists to marketers want to show that this is already included

184
00:09:36,610 --> 00:09:40,090
and to come to the United Kingdom is included in the solution.

185
00:09:40,750 --> 00:09:43,870
Google is included not repeat the steps final.

186
00:09:43,880 --> 00:09:49,240
The minimum one after 12, 14 is the next minimum and fined for fined seven.

187
00:09:49,240 --> 00:09:50,890
Fined for fined seven.

188
00:09:50,890 --> 00:09:52,780
Fined for Buranda seven.

189
00:09:52,780 --> 00:09:56,350
Fined seven thirty seven feet here if you check.

190
00:09:56,350 --> 00:09:57,430
Fined for seven.

191
00:09:57,700 --> 00:09:58,940
Fined seven seven.

192
00:09:59,230 --> 00:10:04,690
So both are the same seven seven and the same four is under the seven only.

193
00:10:04,810 --> 00:10:06,670
So this will form a cycle.

194
00:10:06,910 --> 00:10:11,600
Don't include this one, but this is what makes this extra week not included.

195
00:10:11,630 --> 00:10:14,590
That the sixth one market has won next time.

196
00:10:14,590 --> 00:10:15,790
Don't consider that one.

197
00:10:16,400 --> 00:10:18,580
It's not included in the solution.

198
00:10:18,730 --> 00:10:21,970
But this or this is finished, so just market as one.

199
00:10:22,690 --> 00:10:28,780
So that's how we found a cycle that there is a cycle when both parents are saying for four also we got

200
00:10:28,780 --> 00:10:31,580
seven for seven hours, we got seven nonexisting.

201
00:10:31,770 --> 00:10:34,900
Find out the next minimum right after fourteen.

202
00:10:34,900 --> 00:10:37,930
The next minimum of 16 days we get four or five.

203
00:10:38,260 --> 00:10:44,590
But the Fogelman five can be included or not fined for four five four seven seven seven is the parent

204
00:10:44,590 --> 00:10:45,150
in the diagram.

205
00:10:45,210 --> 00:10:47,470
Can see find five of five.

206
00:10:47,470 --> 00:10:48,760
If not, it included.

207
00:10:48,760 --> 00:10:54,430
Five is independent, five is independent, like five is independent of itself.

208
00:10:54,670 --> 00:10:57,070
So yes, this is not forming a cycle.

209
00:10:57,160 --> 00:10:58,750
So it is not finding a cycle.

210
00:10:59,050 --> 00:11:03,250
Include the five major does one here then.

211
00:11:03,760 --> 00:11:06,640
This is for Gomo five four comma five.

212
00:11:07,210 --> 00:11:08,770
Then we have to perform union.

213
00:11:09,040 --> 00:11:13,090
So five should go under this seven because this is the parent of this one.

214
00:11:13,330 --> 00:11:14,460
And how many are there.

215
00:11:14,470 --> 00:11:15,840
Minus four and the six.

216
00:11:15,850 --> 00:11:16,450
Minus one.

217
00:11:16,810 --> 00:11:17,510
So here we are.

218
00:11:17,510 --> 00:11:21,280
Eight, seven and no this is having minus five.

219
00:11:21,490 --> 00:11:24,030
Five, this is how one, two, three, four or five.

220
00:11:24,310 --> 00:11:27,460
So now next check the next one, find the next minimum.

221
00:11:27,460 --> 00:11:28,810
This is over after sixteen.

222
00:11:28,810 --> 00:11:30,070
The next minimum of five.

223
00:11:30,070 --> 00:11:30,850
Come on, seven.

224
00:11:30,850 --> 00:11:35,790
This one that is minimum fine to five in five.

225
00:11:35,800 --> 00:11:37,210
Seven find five.

226
00:11:37,270 --> 00:11:38,050
Who is the parent.

227
00:11:38,050 --> 00:11:39,460
Seven, five and seven.

228
00:11:39,460 --> 00:11:40,090
Who is the parent.

229
00:11:40,090 --> 00:11:41,710
Seven only because this negative.

230
00:11:42,520 --> 00:11:47,800
So both are giving seems so this five and seven they belong to single said this will form a cycle if

231
00:11:47,800 --> 00:11:50,320
they are belonging to same set at forming a cycle.

232
00:11:50,590 --> 00:11:54,460
So don't include this one so don't include in the solution.

233
00:11:54,460 --> 00:11:59,460
But marketers want to show that this is finished, this is completed included.

234
00:11:59,470 --> 00:12:01,120
This are just gone, deleted now.

235
00:12:01,210 --> 00:12:05,350
OK, the next find, the next minimum after meeting.

236
00:12:05,350 --> 00:12:15,460
The next minimum is twenty five comma six five six, five to five, five to five seven five six six

237
00:12:15,970 --> 00:12:20,220
itself as a parent four four and five, seven, five, six, six hundi.

238
00:12:20,620 --> 00:12:23,800
These are different sets so they are not forming a cycle.

239
00:12:24,130 --> 00:12:25,930
So just include this one.

240
00:12:26,320 --> 00:12:35,300
So this is five comma six included in the solution, five common six and Magnis as one, this as one.

241
00:12:35,320 --> 00:12:36,850
So seven to one is one.

242
00:12:37,630 --> 00:12:38,680
This is included.

243
00:12:39,810 --> 00:12:42,780
On that phone, she actually we have finished on six.

244
00:12:42,930 --> 00:12:44,190
We got all six.

245
00:12:45,600 --> 00:12:51,080
That's all we can stop here because we got and minus four inches and we got the solution.

246
00:12:51,750 --> 00:12:52,920
So this is the working.

247
00:12:53,080 --> 00:12:55,150
So in between the cities is also there.

248
00:12:55,170 --> 00:12:59,970
So if you have already learned about sex or if you have not seen this, I suggest you go back and check

249
00:12:59,970 --> 00:13:00,110
it.

250
00:13:00,690 --> 00:13:03,530
This was there other otherwise it's very simple.

251
00:13:03,840 --> 00:13:06,030
Take minimum and take minimum included.

252
00:13:06,390 --> 00:13:07,740
But forming a cycle or not.

253
00:13:07,770 --> 00:13:08,970
That's what we have to detect.

254
00:13:08,970 --> 00:13:11,350
And sex helped us for sex.

255
00:13:11,370 --> 00:13:15,330
We will use collapsing phone and trade union.

256
00:13:15,540 --> 00:13:18,660
So no, I will ride on the skills program.

257
00:13:18,990 --> 00:13:20,580
I will prepare this data structures.

258
00:13:20,970 --> 00:13:22,800
And also I relied on the instruction.

259
00:13:22,920 --> 00:13:24,060
Let us look at a program.

260
00:13:24,060 --> 00:13:26,460
So all of you have written a piece of code for a program.

261
00:13:26,460 --> 00:13:27,840
Let us understand what is there.

262
00:13:28,200 --> 00:13:30,090
See, I have not yet started main function.

263
00:13:30,090 --> 00:13:36,720
I have just taken these data structures this and set and include and B so how I have declared I will

264
00:13:36,720 --> 00:13:37,110
show you.

265
00:13:37,590 --> 00:13:39,570
See this is maybe one minimum.

266
00:13:39,580 --> 00:13:42,880
So we want somebody that is infinity for we have to find a minimum.

267
00:13:42,900 --> 00:13:44,100
So that's what I have written.

268
00:13:44,130 --> 00:13:48,550
The largest number that I this already have shown you in print, Salgado.

269
00:13:49,260 --> 00:13:55,360
Now this is just a two dimensional dimensionality, three doors and nine columns.

270
00:13:55,560 --> 00:14:00,810
So first of all, contain first vertex and then second vertex and the cost of an edge.

271
00:14:01,050 --> 00:14:02,910
So this is forming one single digit.

272
00:14:03,480 --> 00:14:10,140
So I have taken it is always so first off, first vertex no second vertex number.

273
00:14:10,470 --> 00:14:14,250
And the cost of X, this is first rate structure.

274
00:14:14,440 --> 00:14:24,600
This one, the nexus sets all out initialized with minus one for implementing sets for checking cycle

275
00:14:24,600 --> 00:14:25,510
is formed or not.

276
00:14:25,680 --> 00:14:29,160
So initially, if you remember, this was minus one all the way to minus one.

277
00:14:29,960 --> 00:14:35,220
All I have filled with minus one and the sizes eight that is zero to seven.

278
00:14:35,220 --> 00:14:37,100
So we will be using from one to seven.

279
00:14:37,110 --> 00:14:38,610
First one will not be using it.

280
00:14:39,420 --> 00:14:45,330
Then whether defined whether the set should be considered or not considered, deleted or not deleted,

281
00:14:45,330 --> 00:14:46,460
included or not included.

282
00:14:46,710 --> 00:14:48,440
We have an array that is included.

283
00:14:48,720 --> 00:14:50,550
If you remember, this was initialized with zero.

284
00:14:50,550 --> 00:14:53,730
Then later on when I was including them, I was marking them as one.

285
00:14:54,090 --> 00:14:56,100
So which one of these objects are included or not?

286
00:14:56,110 --> 00:15:01,140
For that we need an array of size nine because there are total nine edges and if you just made zero

287
00:15:01,140 --> 00:15:02,850
here, everything will be initialized.

288
00:15:03,690 --> 00:15:05,730
You don't have to put zero nine times.

289
00:15:06,720 --> 00:15:14,850
No next year are two dimensional array with two rows and six columns for a solution that is spanning

290
00:15:14,850 --> 00:15:15,140
three.

291
00:15:15,270 --> 00:15:16,170
I have this one.

292
00:15:17,390 --> 00:15:20,740
Now, this is union already we have discussed this one, right?

293
00:15:20,790 --> 00:15:27,860
So I will not explain the Soviet Union and similarly collapsing fine are also functional so we can trade

294
00:15:27,870 --> 00:15:29,860
on even just Union fine.

295
00:15:29,870 --> 00:15:33,420
I don't have to show you all of it is a topic on this one.

296
00:15:33,950 --> 00:15:38,840
No, let us right on the main function for Kruskal Salvato, the procedure is very simple.

297
00:15:39,050 --> 00:15:42,370
I will get on the court and I will explain you here.

298
00:15:42,380 --> 00:15:44,720
I have written a main function for cross-cultural selca.

299
00:15:44,720 --> 00:15:46,130
Come look at this one.

300
00:15:46,760 --> 00:15:52,940
See, these are the variables that I would be using I for this preparing a solution I was using here

301
00:15:53,360 --> 00:15:54,620
that is starting with zero.

302
00:15:55,070 --> 00:15:59,200
And as we are including the edge, I will be increasing then J.

303
00:15:59,330 --> 00:16:04,400
K and s number of what it says is the number of edges minimum for finding our minimum.

304
00:16:04,400 --> 00:16:11,540
We need some variable unweaned for any age, whichever is like one six four one issue and successfully

305
00:16:11,840 --> 00:16:12,200
so.

306
00:16:12,200 --> 00:16:13,760
For that I have to clear the variables.

307
00:16:14,990 --> 00:16:20,110
Now, if you look at the code, there's a simple loop, everything is repeating, so just a simple loop,

308
00:16:20,780 --> 00:16:22,580
this vital loop.

309
00:16:24,430 --> 00:16:27,310
For I guess I was saying there's already all right.

310
00:16:27,340 --> 00:16:30,100
I didn't minus one because we needed minus one.

311
00:16:30,100 --> 00:16:33,880
And just so this loop will repeat four and minus one times.

312
00:16:34,820 --> 00:16:42,110
That's it, and what it will do every time find out the minimum age out of all these at just whichever

313
00:16:42,400 --> 00:16:45,560
is minimum, and that the value should not be included.

314
00:16:45,770 --> 00:16:46,550
It should not be one.

315
00:16:46,550 --> 00:16:47,410
It should be zero.

316
00:16:47,780 --> 00:16:49,670
So that included should be zero.

317
00:16:50,000 --> 00:16:52,820
And that just minimum, if it is a minimum.

318
00:16:53,000 --> 00:17:00,290
This for loop is finding out this the finding out minimum one, if you got any minimum change minimum

319
00:17:00,740 --> 00:17:03,900
regarded as key she supports this is minimum.

320
00:17:04,190 --> 00:17:05,220
First one is minimum.

321
00:17:05,480 --> 00:17:08,180
We are taking it as key market as key.

322
00:17:08,510 --> 00:17:15,540
While explaining I was saying, gee, look, I was using not here in the code I'm using here, market

323
00:17:15,550 --> 00:17:20,119
desk G is used for scanning, but of it is minimum came a pointing.

324
00:17:20,540 --> 00:17:28,820
And also I am taking there just you and me, whichever is minimum like suppose I have gotten so as one

325
00:17:29,120 --> 00:17:32,630
then this is you disgust me, you envy those towards taking.

326
00:17:33,320 --> 00:17:36,020
So this is portion, this is for finding minimum.

327
00:17:36,380 --> 00:17:39,840
Just observe this one model of explaining the same thing is written here.

328
00:17:40,490 --> 00:17:46,250
So once you got you, when we check find out that there, when you perform fine, they should not be

329
00:17:46,250 --> 00:17:47,800
seen a result of Fineman's.

330
00:17:47,870 --> 00:17:51,900
Their parents should not be seen if parents came and said forming a cycle.

331
00:17:52,580 --> 00:17:59,870
So if the kind of unbe is not equal, then just not forming a cycle included in the solution.

332
00:17:59,870 --> 00:18:08,660
You and V so unweaned site to the city of zero comma I and T of one only because I have used that.

333
00:18:08,900 --> 00:18:14,210
So that lists right on U v and perform union on unbe that is.

334
00:18:14,210 --> 00:18:15,800
Who are you and what we hear.

335
00:18:16,130 --> 00:18:22,340
This one in six are unsweet but form union on this one and six in the set.

336
00:18:22,790 --> 00:18:29,510
So whatever the fine has given the result upon then we have to perform union work not directly union

337
00:18:29,510 --> 00:18:36,350
of you Komova find whatever the parent who disappear and join their parents so you do not find out if

338
00:18:36,350 --> 00:18:37,280
you are in front of it.

339
00:18:37,730 --> 00:18:42,260
Then as we go to match incrementally move to the next freespace in this team.

340
00:18:43,850 --> 00:18:48,340
That and also make this include whichever extra you got.

341
00:18:48,620 --> 00:18:54,380
Make that as one market, as one that access one here we have to market.

342
00:18:54,380 --> 00:18:58,610
That's what I think, whether it is included or not included.

343
00:18:58,700 --> 00:19:02,300
That was the minimum we have finished with this edge, this Megadeath one.

344
00:19:02,570 --> 00:19:08,160
So this is outside, if not inside, if whether to include or not include, if it is forming a cycle,

345
00:19:08,160 --> 00:19:12,860
it is not included, but we will not include in the solution, but it will mark it as one to show that

346
00:19:13,220 --> 00:19:14,780
we are not going to consider again.

347
00:19:15,830 --> 00:19:17,870
That's all this is repeating every time.

348
00:19:18,050 --> 00:19:18,860
And at the end.

349
00:19:19,200 --> 00:19:20,090
Brender third.

350
00:19:21,610 --> 00:19:26,180
So there's a simple algorithm, the coding is very simple, the main function is very small.

351
00:19:26,770 --> 00:19:32,350
I will go to this and I will show you on the program Kodansha you so you can watch the next video for

352
00:19:32,620 --> 00:19:32,920
Demo.

