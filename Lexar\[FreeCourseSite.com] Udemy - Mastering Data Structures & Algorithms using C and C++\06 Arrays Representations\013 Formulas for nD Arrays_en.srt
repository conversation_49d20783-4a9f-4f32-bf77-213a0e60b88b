1
00:00:00,390 --> 00:00:05,600
Let us prepare a major formula and column media formula for four dimension.

2
00:00:06,420 --> 00:00:07,710
I'll show you the approach.

3
00:00:07,980 --> 00:00:13,580
Then, using same approach, we will see a formula column in the formula for three dimensional also.

4
00:00:14,010 --> 00:00:16,910
So here already I have an example of a fourth dimension.

5
00:00:16,920 --> 00:00:23,600
Any other name is a guitar type or something, and the vibrations are given to the three and four.

6
00:00:24,540 --> 00:00:27,210
Now for the address of any location.

7
00:00:27,480 --> 00:00:30,210
Indices are even I do it three four.

8
00:00:30,570 --> 00:00:32,970
Then what should be the formula?

9
00:00:33,540 --> 00:00:35,880
So let me prepare the formula for Romijn.

10
00:00:38,070 --> 00:00:44,540
And two dimensional, that is already I have explained that all major is done from left to right and

11
00:00:44,540 --> 00:00:46,420
column injuries from right to left.

12
00:00:46,460 --> 00:00:48,290
So we will go from left to right.

13
00:00:48,530 --> 00:00:57,590
Syllogistic <PERSON>, I won the first index engo multiplied by leaving first dimension.

14
00:00:57,590 --> 00:00:59,990
Multiply, first of all, the dimensions.

15
00:01:00,010 --> 00:01:02,510
So this is a data into these three.

16
00:01:02,960 --> 00:01:04,610
Into these four.

17
00:01:06,510 --> 00:01:09,990
Now, second, and that is I do, I do.

18
00:01:11,270 --> 00:01:16,820
It's multiplied by after dimension to all the dimensions should be multiplied.

19
00:01:17,120 --> 00:01:24,710
So this is a deeply into the full dimension and dimension for the MIXED-USE Index three three.

20
00:01:25,130 --> 00:01:27,260
So that third index Ivry.

21
00:01:28,700 --> 00:01:34,720
After Tode index, so after third dimension, all dimensions should be multiplied, so we'll before

22
00:01:34,730 --> 00:01:43,750
his death then plus last I thought for index after four, did I mention everything should be multiplied?

23
00:01:43,760 --> 00:01:44,660
So nothing is there.

24
00:01:44,990 --> 00:01:46,880
So multiplied by the.

25
00:01:48,560 --> 00:01:53,100
This is the formula for Romijn mapping of a fourth dimension.

26
00:01:53,990 --> 00:01:57,380
So you might have observed what is the procedure for preparing the formula.

27
00:01:57,920 --> 00:02:04,430
So for every index we take the dimensions after those dimensions for eight, do we take all the dimensions

28
00:02:04,430 --> 00:02:04,880
after?

29
00:02:06,380 --> 00:02:07,340
So this is the approach.

30
00:02:07,340 --> 00:02:16,820
We have taken it from left to right and I will write the formula for column column media formula address

31
00:02:16,820 --> 00:02:21,770
of the four column measure, already a setback for Romeijn.

32
00:02:21,770 --> 00:02:23,950
We go from left to right for column.

33
00:02:23,970 --> 00:02:26,400
Here we go from right to left.

34
00:02:27,170 --> 00:02:31,690
So let us prepare the formula and not plus.

35
00:02:32,150 --> 00:02:34,120
First one is index four.

36
00:02:34,670 --> 00:02:37,430
So I four multiplied by.

37
00:02:38,450 --> 00:02:44,690
See, this is dimension four, so take all those dimensions before Dimension four, so be ready to deploy

38
00:02:45,200 --> 00:02:50,270
division into B2 in two degree plus.

39
00:02:51,050 --> 00:02:54,400
Now this one, we are moving from right to left.

40
00:02:54,500 --> 00:02:57,900
So next is a three index three.

41
00:02:58,370 --> 00:03:02,120
So go to dimension three and take all the dimensions before dimension three.

42
00:03:02,150 --> 00:03:08,990
So this is one and two, so multiplied by D1 and D2 plus.

43
00:03:09,590 --> 00:03:10,190
I do.

44
00:03:11,680 --> 00:03:17,480
I do multiplied by global dimension data and take all the dimensions before that.

45
00:03:17,500 --> 00:03:18,460
So that is Estevan.

46
00:03:20,650 --> 00:03:21,130
Plus.

47
00:03:21,930 --> 00:03:24,960
This is Ivan multiplied by W..

48
00:03:26,540 --> 00:03:27,300
So that's it.

49
00:03:27,770 --> 00:03:34,160
This is the approach for preparing the formula using four dimension, I have shown you how to write

50
00:03:34,160 --> 00:03:37,200
ROMELIO formula and how to write column in formula.

51
00:03:37,710 --> 00:03:43,730
Now, using the same approach, we can write the formula for any dimensions, any number of dimensions.

52
00:03:43,760 --> 00:03:46,850
So even Trudy was following the same approach.

53
00:03:46,850 --> 00:03:48,320
But this is a very small problem.

54
00:03:48,320 --> 00:03:51,590
So we have seen it thoroughly with the diagrams and everything.

55
00:03:51,980 --> 00:03:57,140
But here I'm showing you directly the formula now based on this formula.

56
00:03:57,240 --> 00:04:03,580
By observing this one, we can prepare a general form of this one for elimination.

57
00:04:04,100 --> 00:04:09,730
So let us prepare a general formula for any dimensions for road measure mapping.

58
00:04:09,740 --> 00:04:11,730
So I'm showing it for Romijn mapping.

59
00:04:11,750 --> 00:04:13,790
Let us take the same formula.

60
00:04:13,790 --> 00:04:21,130
I will generalize it for and dimensions and not plus see all these items are added.

61
00:04:21,410 --> 00:04:26,840
See, this is one go then next this is the next step on next Distomo.

62
00:04:27,050 --> 00:04:29,800
So total four times harder four times are added.

63
00:04:30,080 --> 00:04:42,440
So for addition we use Sigma and here I want I do A3 and 842 so I and its subscript that is I, I do

64
00:04:42,440 --> 00:04:49,930
every so I will call let us be so Petey's the values from one to it is going up before.

65
00:04:50,210 --> 00:04:51,240
So it's a four.

66
00:04:51,270 --> 00:04:52,440
Just make it end.

67
00:04:52,730 --> 00:04:55,790
So this will be four and dimensions.

68
00:04:56,390 --> 00:04:59,500
So ptg the values from one, two and four dimensions.

69
00:04:59,510 --> 00:05:04,220
So this will become Iven I do it three iPhone and so on up to and dimension.

70
00:05:05,280 --> 00:05:05,910
Next thing.

71
00:05:07,100 --> 00:05:09,620
Every index is multiplied by dimensions.

72
00:05:09,650 --> 00:05:15,380
This item is multiplied by three and four, so it is multiplied by product of dimensions.

73
00:05:15,400 --> 00:05:18,110
So this is a product of dimensions, product of dimensions.

74
00:05:18,110 --> 00:05:22,700
So product of pipe we can use of these.

75
00:05:23,600 --> 00:05:27,300
Now, these dimensions are picking something from two, three, four.

76
00:05:27,320 --> 00:05:28,500
So this is just three, four.

77
00:05:28,820 --> 00:05:31,200
So whatever I is this is one.

78
00:05:31,220 --> 00:05:32,320
So this is two, three, four.

79
00:05:32,540 --> 00:05:40,310
So this is taking the rounds after B, so this is due and takes the values from people as one, two

80
00:05:40,430 --> 00:05:40,820
and.

81
00:05:42,600 --> 00:05:47,910
So just going from two to four, three to four, so this is two for three to four.

82
00:05:47,940 --> 00:05:54,660
This is three or four before so cute takes the values from this one go forward and this whole thing

83
00:05:54,660 --> 00:05:55,800
should be multiplied.

84
00:05:57,840 --> 00:05:59,100
But W.

85
00:06:00,980 --> 00:06:10,820
So this is the Romijn formula and general form of and damage, so similarly, you can prepare a formula

86
00:06:10,820 --> 00:06:13,960
for column mapping for any dimensions.

87
00:06:14,300 --> 00:06:16,390
So try this formula by yourself.

88
00:06:17,470 --> 00:06:23,680
Now, let us do some analysis on this one, I removed this column, a formula, and we'll do analysis

89
00:06:23,680 --> 00:06:25,390
on ROMELIO formula.

90
00:06:25,600 --> 00:06:30,410
Let us see how many multiplications are performing for getting this result.

91
00:06:31,180 --> 00:06:32,200
Let us count them.

92
00:06:32,470 --> 00:06:35,080
One, two, three, three multiplications here.

93
00:06:35,170 --> 00:06:40,010
One, two, two multiplications here and one multiplication here.

94
00:06:40,420 --> 00:06:42,930
Then that W is commonplace.

95
00:06:42,940 --> 00:06:47,020
That is plus one depending on dimensions.

96
00:06:47,380 --> 00:06:52,780
The number of multiplications inside the brackets mavity of all four dimensions.

97
00:06:52,930 --> 00:06:54,590
How many multiplications are there.

98
00:06:55,060 --> 00:06:59,020
This is a three plus two plus one for forty.

99
00:06:59,860 --> 00:07:01,180
It means four five b.

100
00:07:01,180 --> 00:07:07,630
How many multiplications will be there for four plus three plus two plus one to four and dimensions.

101
00:07:07,630 --> 00:07:09,250
How many multiplications will be there.

102
00:07:09,490 --> 00:07:12,190
And minus one plus and minus two plus goes on.

103
00:07:12,190 --> 00:07:14,210
Two, three plus two plus one.

104
00:07:14,650 --> 00:07:27,220
So this is nothing but an end to end minus one by two so and into and minus one by two is n square multiplications.

105
00:07:28,150 --> 00:07:30,940
Oh these are too many multiplications.

106
00:07:31,600 --> 00:07:38,130
So the time taken for evaluation of this formula is n square multiplication operations.

107
00:07:38,980 --> 00:07:40,840
So this is too much time consuming.

108
00:07:41,950 --> 00:07:46,230
So the Romijn formula used by the compiler, Strathgordon and square time.

109
00:07:46,990 --> 00:07:50,260
So is there any way to reduce the number of multiplications?

110
00:07:50,980 --> 00:07:55,050
Let us see what we can do for reducing the number of multiplications.

111
00:07:55,300 --> 00:08:01,030
I will take only the portion of formula that is inside the brackets and I will write the formula from

112
00:08:01,030 --> 00:08:05,020
right to left this room with a formula I'm just writing it rewards.

113
00:08:05,380 --> 00:08:18,790
So this is ifour plus I three into the four plus I do it to be three in two before class.

114
00:08:19,750 --> 00:08:26,260
I run into these two into the three into default.

115
00:08:27,580 --> 00:08:33,309
So I have taken only the portion of the formula that is inside the brackets and I have written from

116
00:08:33,309 --> 00:08:37,780
the last time onwards so that we can reduce the number of multiplications.

117
00:08:38,049 --> 00:08:44,560
So if you observe this is having this is having four values, multiply three values, multiply two values,

118
00:08:44,560 --> 00:08:46,090
and then there is no multiplication.

119
00:08:46,390 --> 00:08:51,820
So I have taken from the smaller value on the word so far, so smaller then go on, including not in

120
00:08:51,820 --> 00:09:00,310
this if you observe this effort is as it is, plus before default, before default is common among all

121
00:09:00,310 --> 00:09:00,940
the stone.

122
00:09:00,950 --> 00:09:05,050
So I can try to d for multiplied by default is common.

123
00:09:05,050 --> 00:09:10,450
So I t plus I do include deforestation as common.

124
00:09:10,450 --> 00:09:15,130
So the trees there plus I one deforestation and schoenman.

125
00:09:15,130 --> 00:09:18,070
So this is a data and the tree.

126
00:09:20,170 --> 00:09:26,050
OK, so I have taken Coleman, so I got one application here, let us continue on and see if we can

127
00:09:26,050 --> 00:09:27,760
take more common values.

128
00:09:29,770 --> 00:09:39,700
I three plus nine, these two DOMS D3 is common, so I will ride on these three multiplied by and I

129
00:09:39,700 --> 00:09:45,040
do plus I run into the do.

130
00:09:48,080 --> 00:09:53,130
So I have taken before common and common now only two terms are remaining here further.

131
00:09:53,150 --> 00:09:54,650
I cannot take anything common.

132
00:09:55,550 --> 00:10:01,110
This formula can be rewritten like this by taking dimensions of common.

133
00:10:01,640 --> 00:10:03,330
Now, this is the same thing.

134
00:10:03,350 --> 00:10:04,900
What is there inside the brackets?

135
00:10:05,270 --> 00:10:07,560
So let us see how many multiplications are there?

136
00:10:08,000 --> 00:10:11,240
This is one two multiplications.

137
00:10:11,720 --> 00:10:13,040
Three multiplications.

138
00:10:13,370 --> 00:10:15,680
Oh, so far, four dimensions.

139
00:10:15,920 --> 00:10:22,920
Just there are three multiplications for forty three multiplications are there and phosphide.

140
00:10:23,240 --> 00:10:28,790
There will be four multiplication so far endi there will be and minus one multiplication.

141
00:10:29,000 --> 00:10:33,560
So total time for performing multiplication will be outdraw.

142
00:10:33,560 --> 00:10:40,040
And so this formula reduces the number of multiplication from N Square to part of.

143
00:10:40,130 --> 00:10:44,800
And so by taking common I have reduced the number of multiplications.

144
00:10:44,960 --> 00:10:50,740
So this is how we can reduce the number of multiplication in measure as well as in column measure formula.

145
00:10:51,020 --> 00:10:55,670
So the rule that we have applied that is taking commands to reduce the number of multiplication.

146
00:10:55,910 --> 00:10:58,550
This rule is called as Hohnen for all.

147
00:11:00,310 --> 00:11:07,930
So you have applied Hoeness rule to reduce the number of multiplications, so that's all of four dimension

148
00:11:07,930 --> 00:11:10,090
two and dimensions.

