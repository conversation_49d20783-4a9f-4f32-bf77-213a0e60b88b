1
00:00:00,730 --> 00:00:05,770
And this video, I'll give you a demonstration for creating good emotional ads in the previous video

2
00:00:05,770 --> 00:00:10,830
in the white woodwork, I have shown you what are the methods of creating a good emotionality.

3
00:00:11,800 --> 00:00:17,320
So this will look at those methods and verify them here and see how to do that in our remaining part.

4
00:00:17,320 --> 00:00:22,310
Of course, wherever required two dimensional arrays, we may be using any one of these methods.

5
00:00:22,840 --> 00:00:23,880
So let us see the method.

6
00:00:23,980 --> 00:00:27,700
The first move that I've shown you is that we can create an array inside the stack.

7
00:00:27,700 --> 00:00:33,280
So a normal method that is provided by the compiler, if you follow that method, array will be created

8
00:00:33,280 --> 00:00:34,220
inside stack.

9
00:00:34,720 --> 00:00:39,600
Suppose I want an array with three doors and four columns.

10
00:00:39,760 --> 00:00:43,260
So this is a two dimensional array of dimensions, three by four.

11
00:00:43,750 --> 00:00:46,700
If we want to initialize in every door, I should initialize it.

12
00:00:47,200 --> 00:00:49,060
So the first row is having values.

13
00:00:50,150 --> 00:00:50,930
One before.

14
00:00:51,870 --> 00:00:53,820
Second row is having values.

15
00:00:56,320 --> 00:00:58,510
Even worse than third.

16
00:00:58,810 --> 00:01:01,920
I will take the values or numbers.

17
00:01:02,900 --> 00:01:07,910
So there are three rows and each room is having for four elements because there are four columns, so

18
00:01:07,910 --> 00:01:11,060
this is the way we can create a two dimensional array.

19
00:01:11,090 --> 00:01:13,670
This will be created inside STAP.

20
00:01:13,790 --> 00:01:15,710
Remember this then?

21
00:01:15,710 --> 00:01:20,270
The second method is I can take an array of pointers.

22
00:01:21,960 --> 00:01:25,050
Of Sastry, so this three is for Rose.

23
00:01:26,270 --> 00:01:34,520
Columns are not created, so this array of pointers will be created inside, stacked then to each location

24
00:01:34,520 --> 00:01:38,170
in this area can create an array in heap and assign it.

25
00:01:38,930 --> 00:01:42,860
So for each location, I will create an array inside heap and assignment.

26
00:01:45,190 --> 00:01:54,370
Then create an integer array mellark of size of four and multiplied by size of an integer.

27
00:01:55,290 --> 00:02:00,150
So this is for the first location and they have three such locations, so I will.

28
00:02:01,470 --> 00:02:04,980
I will copy this code and based it for the.

29
00:02:06,880 --> 00:02:08,199
Remaining locations.

30
00:02:09,229 --> 00:02:11,090
This is for lunch and this is for the.

31
00:02:13,000 --> 00:02:14,380
This is for one, and it's for.

32
00:02:17,600 --> 00:02:21,740
So this is how to demonstrate that it's created already we have seen the board look for this one.

33
00:02:22,660 --> 00:02:29,800
Then the Podimata have shown you for that I will get a double pointer here, integer double point or

34
00:02:29,800 --> 00:02:33,960
C in C language, we should declare the variables in the beginning.

35
00:02:33,970 --> 00:02:35,550
So I have declared it in the beginning.

36
00:02:36,250 --> 00:02:38,760
Now it is just a double pointer.

37
00:02:38,770 --> 00:02:43,300
So first of all, I should create a three four rolls.

38
00:02:43,300 --> 00:02:44,620
So C assign.

39
00:02:50,750 --> 00:02:52,100
Integer, double pointer.

40
00:02:53,450 --> 00:02:54,140
Midlake.

41
00:02:55,080 --> 00:02:58,170
It's the size of three that is the size of.

42
00:02:59,940 --> 00:03:01,410
Integer pointers.

43
00:03:03,210 --> 00:03:05,430
So first is an array of pointers.

44
00:03:07,670 --> 00:03:15,350
The Nix's Sea of Zero, I should assign Uhry from here, I should create an array in here and I should

45
00:03:15,350 --> 00:03:15,860
assign it.

46
00:03:16,520 --> 00:03:19,100
So arrange he is offside for now.

47
00:03:19,400 --> 00:03:23,720
So this is for first true for into size of.

48
00:03:24,890 --> 00:03:30,790
Indigene, then same boat, I will copy it and use it for the rest of the rules.

49
00:03:35,380 --> 00:03:39,160
Yes, this is for one, and this is for two.

50
00:03:41,230 --> 00:03:49,120
See, this first rule itself, rule itself are created in him and the rest of the Herries theories are

51
00:03:49,120 --> 00:03:51,730
also created in a heap and here.

52
00:03:52,810 --> 00:03:59,430
Roosevelt created inside the stack and these arrays, that is three areas are created in a heap.

53
00:04:01,050 --> 00:04:07,290
So that's all syntactically, I have shown you how the code works, so I have written the code and all

54
00:04:07,290 --> 00:04:08,500
this code is error free.

55
00:04:09,180 --> 00:04:10,320
Now, one thing will do.

56
00:04:10,620 --> 00:04:15,510
I will take for Loop and I will try to print all these arrays one by one.

57
00:04:18,010 --> 00:04:25,000
I would take advantage of I and J using these, I will print the elements of these arrays, though I

58
00:04:25,000 --> 00:04:28,580
have elements only Infostrada, I don't have elements the second and third party.

59
00:04:29,020 --> 00:04:30,970
But just let us print them and see.

60
00:04:31,890 --> 00:04:33,990
Whether it works or gives an error.

61
00:04:34,710 --> 00:04:35,850
So using for loop.

62
00:04:38,200 --> 00:04:40,450
I less than three eye plus plus.

63
00:04:41,470 --> 00:04:41,860
Then.

64
00:04:42,880 --> 00:04:43,480
For.

65
00:04:45,910 --> 00:04:52,150
Geoscience zero GS less than thought, because this is four columns, C++.

66
00:04:53,800 --> 00:04:55,040
Then indef.

67
00:04:56,970 --> 00:04:58,050
Percentile the.

68
00:05:02,100 --> 00:05:04,590
AOF Ikoma Djay.

69
00:05:05,560 --> 00:05:07,620
So results from this study will be printed.

70
00:05:07,630 --> 00:05:16,180
That is a rate that is good and said stock now for a new line, I will say printf and so after the end

71
00:05:16,180 --> 00:05:19,690
of a little, let it take a new line space.

72
00:05:20,610 --> 00:05:21,630
Let us run this one.

73
00:05:24,460 --> 00:05:29,540
Yes, the values are printed, the values that I have given here that seem values up in that are all

74
00:05:29,560 --> 00:05:30,010
Bitel.

75
00:05:33,790 --> 00:05:39,850
Now, the theme for Loop, I will cut it from here and paste it after creation of the.

76
00:05:43,200 --> 00:05:49,350
And I will point out maybe he's not having any elements, but it should get garbage values, but it

77
00:05:49,350 --> 00:05:52,230
should not give any order that invalid memory.

78
00:05:55,330 --> 00:05:58,360
Yes, it is printing garbage that is upended.

79
00:06:04,480 --> 00:06:10,180
Then seem to follow ups and use it for speed to verify whether it will allow us to access that memory

80
00:06:10,180 --> 00:06:15,900
or not, if that is are not treated and it will not allow us to access, this is greater using double

81
00:06:15,910 --> 00:06:16,470
pointers.

82
00:06:16,740 --> 00:06:20,040
So I'm able to access dislocations using sea of common.

83
00:06:21,640 --> 00:06:22,750
Yes, it is working.

84
00:06:22,750 --> 00:06:25,600
Some valves are zeros and some of us are random.

85
00:06:25,870 --> 00:06:27,400
So all those values are garbage.

86
00:06:27,580 --> 00:06:29,500
It's only because we have not initialized.

87
00:06:31,000 --> 00:06:31,480
That's all.

88
00:06:31,480 --> 00:06:35,820
We have seen the methods how to create two dimensionality just for confirmation.

89
00:06:35,830 --> 00:06:36,790
I have written the code.

90
00:06:37,860 --> 00:06:40,100
And we may be using it if required.

91
00:06:44,260 --> 00:06:45,290
That's all in this video.

