1
00:00:00,360 --> 00:00:08,890
In this video, we will see how to detect if Lincolnesque is having a loop or Linklaters linear.

2
00:00:10,320 --> 00:00:11,160
So what is the loop?

3
00:00:11,190 --> 00:00:12,230
I'll explain first.

4
00:00:12,540 --> 00:00:19,670
See, there is a link list and the last node of our link is pointing on some node unrelentless.

5
00:00:20,070 --> 00:00:22,590
It's not the first node or some other node.

6
00:00:23,220 --> 00:00:28,590
So if one last note is pointing on some node of a link, then it is forming a loop.

7
00:00:28,830 --> 00:00:31,800
So this link lists as having not loop.

8
00:00:33,770 --> 00:00:40,200
Then I have one more linguist which is ending that last note is having none.

9
00:00:40,550 --> 00:00:46,940
So this link, Mrs. <PERSON><PERSON><PERSON>, so there is one linguist with the loop and there is one linguist that is

10
00:00:46,940 --> 00:00:47,510
linear.

11
00:00:48,200 --> 00:00:54,200
But the problem is, if a linguist is given to us, we have to find out whether it is Leanyer or it

12
00:00:54,200 --> 00:00:56,840
is having a clue how to detect.

13
00:00:58,050 --> 00:01:04,590
So first, let us see how to detect whether it is a linear or a linguistic millenia, if it is ending

14
00:01:04,590 --> 00:01:09,820
with the last notice, having done the whole thing over the last node is having or not.

15
00:01:10,080 --> 00:01:15,870
So we have to start from Fastenal and reach the last node and check that it is really having no government.

16
00:01:16,230 --> 00:01:22,880
So for that, let us take a pointer B and go on moving upon this one next to next, next, next.

17
00:01:22,890 --> 00:01:25,380
And at one point B becomes null.

18
00:01:25,620 --> 00:01:29,070
So if he becomes null means it is Lenie.

19
00:01:30,870 --> 00:01:37,230
So if you are scanning, trolling plus or traversing a link list and the pointer becomes null means

20
00:01:37,230 --> 00:01:41,340
it is leaning in the hole to know that it's having a loop or not.

21
00:01:41,670 --> 00:01:49,050
So if I take a pointer P and go on scanning the linked list or traversing a link list, then P will

22
00:01:49,050 --> 00:01:50,310
be moving on to this node.

23
00:01:50,310 --> 00:01:53,370
Then this more than this, more than this node and this note.

24
00:01:53,640 --> 00:01:57,680
Then again, it will come to this node, then this will give there to solve this one.

25
00:01:58,050 --> 00:02:06,240
So it will be moving around these set of nodes only then how I should stop and say that this is having

26
00:02:06,240 --> 00:02:06,660
a loop.

27
00:02:07,290 --> 00:02:10,479
How do I know that there is a loop in this link list?

28
00:02:10,770 --> 00:02:18,240
So there are different suggestions like stored the addresses of the N, and if you come across the same

29
00:02:18,240 --> 00:02:22,410
node once again, then there is a loop in this one.

30
00:02:23,640 --> 00:02:29,040
All you can store the elements if the elements are unique, if you are sure that the elements are unique,

31
00:02:29,340 --> 00:02:33,770
then if you come across the same element once again, then you can say there is a loop.

32
00:02:34,510 --> 00:02:37,170
There is one more method using two pointers.

33
00:02:37,320 --> 00:02:38,410
I'll show you that one.

34
00:02:39,030 --> 00:02:41,170
See, we will take two pointers on a link.

35
00:02:41,200 --> 00:02:43,950
This one is A B and other one is Q.

36
00:02:43,950 --> 00:02:52,890
The board start from first and then B will move by one step and the Q will move by two steps.

37
00:02:53,340 --> 00:02:59,830
So I can compare this to point that the one example like suppose there is a race track.

38
00:03:00,570 --> 00:03:02,820
So these are two cars on the track.

39
00:03:03,270 --> 00:03:09,870
This is the starting point and the track is 15 kilometers and all this car is moving with 100 kilometers

40
00:03:09,870 --> 00:03:13,850
per hour speed and this car is moving by 150 kilometers per hour speed.

41
00:03:14,100 --> 00:03:15,480
So this car is very fast.

42
00:03:15,720 --> 00:03:22,470
Now, from the starting point of the start of the race, then, do you think they will meet again at

43
00:03:22,470 --> 00:03:23,140
any place?

44
00:03:23,170 --> 00:03:25,230
No, this car is slow, that God is fast.

45
00:03:25,590 --> 00:03:26,790
Then they will meet again.

46
00:03:28,130 --> 00:03:34,730
Definitely, this will reach first and this will be reaching that slowly now instead of having a straight

47
00:03:34,730 --> 00:03:35,870
linear track.

48
00:03:36,140 --> 00:03:41,810
We can have a circular track also towards this track is off one kilometre.

49
00:03:42,080 --> 00:03:49,040
And these two cars, they have to take laps off of this track 15 times.

50
00:03:49,340 --> 00:03:51,700
They have to move around this one for 15 times.

51
00:03:51,920 --> 00:03:57,080
So if this car is hundred kilometers per hour and this car is 150 kilometers per hour, as they are

52
00:03:57,080 --> 00:04:01,130
going to make 15 rounds of this one, then definitely they will meet at one point.

53
00:04:01,760 --> 00:04:05,330
So it means if one car is slow, one got is fast, then they were made.

54
00:04:05,330 --> 00:04:10,990
If the track is linear, they will meet once again if the track is circular.

55
00:04:11,300 --> 00:04:14,770
So same with the Linklaters having a loop, then these two pointers will meet.

56
00:04:14,780 --> 00:04:16,750
If it is linear, they never meet.

57
00:04:17,690 --> 00:04:20,399
So let us apply the same idea up on the pointers.

58
00:04:20,420 --> 00:04:26,210
One point will move it slowly by one note and one point that we move it fast by two knots.

59
00:04:26,570 --> 00:04:30,140
So I have been killed so let us move by one Naude.

60
00:04:31,500 --> 00:04:35,120
And Kubi do not want to accuse him.

61
00:04:36,650 --> 00:04:44,060
Then move by the north and Cuba do not want to sulcus here.

62
00:04:44,940 --> 00:04:47,220
Then moved by just one known.

63
00:04:48,620 --> 00:04:52,840
Then move Kubi to Nords, one to sulcus here.

64
00:04:54,010 --> 00:04:56,350
Then move by one Naude.

65
00:04:57,730 --> 00:05:05,890
Move two by two nodes, one, two, so they are meeting again, so the two pointers are meeting again

66
00:05:06,130 --> 00:05:08,800
after the starting point of the meet once again.

67
00:05:09,110 --> 00:05:10,120
Then there is a loop.

68
00:05:12,000 --> 00:05:13,710
Let us apply the same thing there.

69
00:05:14,310 --> 00:05:16,290
I have P and Q here.

70
00:05:18,200 --> 00:05:22,910
Now, let us move piece by one node and Kubi to nodes.

71
00:05:24,690 --> 00:05:31,320
Then be by one note and Kubi to n one to send the next step.

72
00:05:31,350 --> 00:05:32,520
Q becomes none.

73
00:05:32,970 --> 00:05:39,600
So if any one pointer becomes null, we should stop and say that this link with this linear linear link

74
00:05:39,620 --> 00:05:41,730
lists one of the pointer will become another.

75
00:05:41,730 --> 00:05:42,390
Definitely.

76
00:05:42,400 --> 00:05:44,720
Q will become null because it is moving faster.

77
00:05:45,500 --> 00:05:48,200
So we can take two pointers and find out.

78
00:05:49,580 --> 00:05:55,450
Whether it is having a loop or a Selenia, if they meet again, then there is a loop.

79
00:05:55,730 --> 00:05:59,180
If one of the pointer becomes null, then they are linear.

80
00:05:59,690 --> 00:06:03,740
Now, let me write on the function for checking whether there is a loop or not.

81
00:06:04,010 --> 00:06:05,750
If no loop, it's linear.

82
00:06:06,680 --> 00:06:12,260
I like to function, let us call the function as is a loop, so this will return true if there is a

83
00:06:12,260 --> 00:06:15,060
loop, if it is linear, it will return false.

84
00:06:15,530 --> 00:06:17,420
So the stakes are pointed to the first to.

85
00:06:18,300 --> 00:06:21,410
The less then it is going to return.

86
00:06:21,410 --> 00:06:22,190
True or false.

87
00:06:22,230 --> 00:06:23,620
So it is in your type.

88
00:06:24,080 --> 00:06:28,640
No, we need to Blinder's node B.

89
00:06:30,010 --> 00:06:35,040
And two viewpoints are needed, and the P and Q both are on first.

90
00:06:35,090 --> 00:06:40,360
This is F F for first, calling the first fossil, passing it as parameter.

91
00:06:41,080 --> 00:06:50,980
Now what they have to do so using do will just move B by one step and move Q by two steps.

92
00:06:51,100 --> 00:06:52,810
So Q assign cues next.

93
00:06:52,850 --> 00:06:55,330
This is the first step and second step.

94
00:06:55,340 --> 00:06:56,620
Don't take it blindly.

95
00:06:56,950 --> 00:07:00,220
If AQ already became null, then there is no next node.

96
00:07:00,340 --> 00:07:02,170
So check if it has become null.

97
00:07:02,560 --> 00:07:04,630
If it is not null, then move to the next.

98
00:07:05,050 --> 00:07:13,190
So if AQ is not equal to null then Q should be assigned that Qs next.

99
00:07:13,900 --> 00:07:20,230
Otherwise, let the QB only now hear inside of mine.

100
00:07:20,230 --> 00:07:25,750
I will write on the condition that while the P and Q continue as long as.

101
00:07:25,750 --> 00:07:27,430
But the point is not null.

102
00:07:28,500 --> 00:07:32,020
But the point is not the end of this.

103
00:07:32,040 --> 00:07:38,090
There is no space, so I will continue here if P is equal to none.

104
00:07:38,430 --> 00:07:41,530
Both are at the same place, then return.

105
00:07:41,880 --> 00:07:43,300
True, true.

106
00:07:43,850 --> 00:07:45,930
I'm riding through here, Trumans one.

107
00:07:45,930 --> 00:07:51,720
I'll be returning one if they are not equal means definitely one of the pointer that is null.

108
00:07:51,990 --> 00:07:57,420
So return false means it is not having loop not having Lubman.

109
00:07:57,540 --> 00:08:00,630
Definitely it is a linear linguist.

110
00:08:01,660 --> 00:08:07,120
So there's the gold, so there is no space, I continued here, just conditional statement I can read

111
00:08:07,120 --> 00:08:18,460
on that condition here also I can say simply written if a B equals two Q return true, else return false

112
00:08:19,180 --> 00:08:23,060
supposed Trumans ones and false and zero for function ends here.

113
00:08:23,080 --> 00:08:28,810
So that all that conditional statement, if else I have written in just one single line I write down

114
00:08:28,810 --> 00:08:32,980
if P is equal to Q return true, otherwise return false.

115
00:08:33,610 --> 00:08:36,270
So this is the procedure for finding whether there is a loop or not.

116
00:08:36,789 --> 00:08:43,740
So analysis, how many point is required to point pointers, what is the time taken order of.

117
00:08:43,750 --> 00:08:45,680
And that depends on a number of nodes.

118
00:08:46,090 --> 00:08:52,000
So this scanning is a done depends on the number of nodes they may be scanning the portion of a loop

119
00:08:52,000 --> 00:08:52,990
more than one time.

120
00:08:53,170 --> 00:08:54,910
That doesn't make much difference.

121
00:08:55,210 --> 00:08:57,500
Even we can say three times.

122
00:08:57,700 --> 00:08:59,570
And also it is out and.

123
00:09:00,990 --> 00:09:07,170
So it is multiple often that is number of nodes, so it's not andirons often, so to call it doesn't

124
00:09:07,170 --> 00:09:08,970
square some number of times.

125
00:09:09,190 --> 00:09:13,050
And so we can call the time as outdraw and.

126
00:09:14,610 --> 00:09:16,280
So that's all about this procedure.

