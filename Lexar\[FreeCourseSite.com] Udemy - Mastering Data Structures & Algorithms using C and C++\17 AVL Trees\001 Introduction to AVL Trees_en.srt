1
00:00:00,360 --> 00:00:09,300
The topic is, Aviatrix, these are high Timbaland's to binary search trees, how the height of four

2
00:00:09,300 --> 00:00:10,440
trees balanced.

3
00:00:10,800 --> 00:00:18,660
It is balanced using balance factor balance factors, height of left subtree minus height of height,

4
00:00:18,660 --> 00:00:20,910
subtree balance factors.

5
00:00:20,910 --> 00:00:23,400
Height of the subtree minus height of right subtree.

6
00:00:23,670 --> 00:00:29,430
In short, I can say that balance factors height of left subtree minus height of right.

7
00:00:29,910 --> 00:00:37,740
So this balance factor we calculate on every nolde of binary search tree or long we will call it aviating.

8
00:00:38,280 --> 00:00:47,100
On every note we calculate this and the balance factor of every Naude must be either minus one zero

9
00:00:47,400 --> 00:00:48,210
or the one.

10
00:00:48,570 --> 00:00:50,160
It should be any one of these.

11
00:00:50,790 --> 00:00:53,220
These are valid balance factors.

12
00:00:53,610 --> 00:01:00,190
So if I take Morde upon this one, <PERSON>'s absolute right model is absolute.

13
00:01:00,630 --> 00:01:07,710
So this absolute value should be less than equal to one that minus one or two decades plus one more

14
00:01:08,040 --> 00:01:08,580
plus one.

15
00:01:10,390 --> 00:01:16,390
So the balance factors should be absolute value of balance factors should be less than or equal to one.

16
00:01:16,990 --> 00:01:25,900
If balance factor is balance, factor of any note is greater than one, then that northers imbalance.

17
00:01:28,880 --> 00:01:36,080
If it is less than or equal to the no notice balanced, if the absolute value of balance factor is less

18
00:01:36,080 --> 00:01:40,880
than equal to one, then the north is balanced if the absolute value of balanced factor is greater than

19
00:01:40,880 --> 00:01:43,200
one than the Nords imbalance.

20
00:01:43,280 --> 00:01:48,410
So we calculate balance factor for each and every in every tree.

21
00:01:48,950 --> 00:01:51,350
Every node should be balanced.

22
00:01:51,620 --> 00:01:55,760
If any one, no one is imbalanced, then we say three is imbalance.

23
00:01:56,000 --> 00:01:58,140
So how do you balance that tree?

24
00:01:58,610 --> 00:02:01,850
We perform rotations for balancing that tree.

25
00:02:03,630 --> 00:02:09,490
All right, so first of all, we will learn how to calculate balance factors.

26
00:02:09,960 --> 00:02:16,150
Let us take some example trees and learn how to calculate balance factors, how to identify a notice

27
00:02:16,170 --> 00:02:17,400
balance or imbalance.

28
00:02:17,970 --> 00:02:19,580
I have some example trees.

29
00:02:19,590 --> 00:02:23,910
Let us find our balance factor for each and every node in our tree.

30
00:02:24,390 --> 00:02:30,600
So first, I will select this tree, this one balance factor of this node, its height of left tree,

31
00:02:30,600 --> 00:02:37,590
minus height of exactly what is the height of left subtlely one to two, which just that actually height

32
00:02:37,590 --> 00:02:40,120
of left suppliments from here I should see the tree.

33
00:02:40,350 --> 00:02:41,340
Now what is the height.

34
00:02:41,610 --> 00:02:43,670
Zero one actually height this one.

35
00:02:44,190 --> 00:02:45,580
Then what is the height of this one.

36
00:02:45,600 --> 00:02:48,510
There's also one but I did not take one.

37
00:02:48,510 --> 00:02:50,310
I said two because I counted.

38
00:02:50,310 --> 00:02:53,990
And just so to decide also I will count to just two.

39
00:02:54,000 --> 00:02:55,680
So I'm getting to do so.

40
00:02:55,680 --> 00:02:56,460
Answer to zero.

41
00:02:56,670 --> 00:03:00,480
And if you take one one or two two, it's the same for finding the high.

42
00:03:00,540 --> 00:03:03,770
You can just count at this or else you can take the level also.

43
00:03:04,230 --> 00:03:09,300
So you should do the same thing on every normal, like make sure whatever you are doing or do the same

44
00:03:09,300 --> 00:03:09,970
thing on every note.

45
00:03:10,470 --> 00:03:14,940
Let us take the balance factor of this one height of left subtree is one height of rights of trees,

46
00:03:14,940 --> 00:03:17,000
one edge, one minus one zero.

47
00:03:17,370 --> 00:03:18,590
This nothing is there.

48
00:03:18,600 --> 00:03:20,560
So it is zero zero zero zero zero.

49
00:03:20,970 --> 00:03:21,340
Right.

50
00:03:21,750 --> 00:03:23,300
This is zero zero.

51
00:03:23,580 --> 00:03:27,570
What about this one on the left side and the zyda on the right side?

52
00:03:27,750 --> 00:03:32,700
So one minus zero is one or the balance factors one.

53
00:03:33,540 --> 00:03:36,000
So zero zero zero zero zero then one.

54
00:03:36,690 --> 00:03:38,190
Let us calculate for this one.

55
00:03:38,550 --> 00:03:42,450
This is one two minus Rightside, one to two.

56
00:03:42,720 --> 00:03:43,650
So this is zero.

57
00:03:44,250 --> 00:03:45,800
Then here I would write the direct value.

58
00:03:45,900 --> 00:03:49,320
This is one and zero one minus one and this is zero only.

59
00:03:49,740 --> 00:03:50,700
This is zero only.

60
00:03:50,940 --> 00:03:53,230
This is zero minus one is minus one.

61
00:03:53,380 --> 00:03:54,210
What about this.

62
00:03:56,540 --> 00:04:04,160
Let us put the values first on leave notes, then for this one, two, three, one three, minus one

63
00:04:04,160 --> 00:04:05,030
as two.

64
00:04:05,480 --> 00:04:07,220
Oh, this is in imbalance.

65
00:04:08,130 --> 00:04:09,030
This is imbalanced.

66
00:04:09,450 --> 00:04:15,630
What about this one, two and zero, the site, so this is to dissolve in balance and this one is one

67
00:04:15,630 --> 00:04:16,130
and zero.

68
00:04:16,140 --> 00:04:16,890
So this is one.

69
00:04:17,310 --> 00:04:20,350
So you can see that there are two nodes which are in balance on this one.

70
00:04:20,940 --> 00:04:21,930
There are two nodes.

71
00:04:22,710 --> 00:04:25,380
Then what about this leaf node?

72
00:04:25,380 --> 00:04:26,250
Zero zero.

73
00:04:26,520 --> 00:04:26,960
OK.

74
00:04:28,270 --> 00:04:30,650
From boredom and start, this is one zero.

75
00:04:30,670 --> 00:04:35,820
So this is one and this is zero on this side and one two.

76
00:04:35,830 --> 00:04:42,080
So this is minus two and this one, one, two, three on the side, zero there.

77
00:04:42,100 --> 00:04:45,940
So three, this is one, two, three, four on the side and one there.

78
00:04:46,210 --> 00:04:48,010
So this is four minus one.

79
00:04:48,040 --> 00:04:48,820
This is three.

80
00:04:50,500 --> 00:04:58,730
This imbalance, this is invalid, actually see one thing in a weird way, whenever you calculate balance

81
00:04:58,730 --> 00:05:01,160
factor, you will never get the balance factor.

82
00:05:01,250 --> 00:05:03,730
Three, you will get to two.

83
00:05:03,860 --> 00:05:05,060
OK, two is OK.

84
00:05:05,070 --> 00:05:06,190
Minus two is OK.

85
00:05:06,410 --> 00:05:09,770
So you have just gone beyond the balance to three, right?

86
00:05:10,040 --> 00:05:11,690
So three months, that is too much.

87
00:05:11,990 --> 00:05:14,540
You'll never get the balance across three.

88
00:05:15,960 --> 00:05:22,800
All right, so there is not proper, so that is invalid example, I'll show you why it will not why

89
00:05:22,800 --> 00:05:24,600
you will not get balance factors.

90
00:05:24,610 --> 00:05:26,590
Three, that one more thing.

91
00:05:27,550 --> 00:05:27,790
No.

92
00:05:27,790 --> 00:05:28,750
One common mistake.

93
00:05:28,750 --> 00:05:35,110
What students do is find the balance factor of this one one N minus one, two, three, four, four

94
00:05:35,110 --> 00:05:36,400
N minus three.

95
00:05:36,940 --> 00:05:40,450
You don't have to count N, you have to count levels or height.

96
00:05:40,590 --> 00:05:40,880
Right.

97
00:05:41,290 --> 00:05:44,860
So one, one, two, three.

98
00:05:44,890 --> 00:05:45,760
So this is three.

99
00:05:47,460 --> 00:05:52,680
So this is my chance to counter hydrate, check the height, don't count the the.

100
00:05:53,400 --> 00:05:54,570
Don't do this mistake.

101
00:05:55,200 --> 00:06:01,050
So I have shown you how to calculate balance factors, 9x if any notice imbalance.

102
00:06:01,410 --> 00:06:04,290
How do you balance a by performing rotations.

103
00:06:04,320 --> 00:06:05,390
We will learn about it.

