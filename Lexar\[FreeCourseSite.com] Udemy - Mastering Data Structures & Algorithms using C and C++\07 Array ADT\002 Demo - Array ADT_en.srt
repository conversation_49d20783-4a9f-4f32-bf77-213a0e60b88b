1
00:00:00,690 --> 00:00:07,380
Yeah, this election is about race and the operations are on, and so I will create a new project for

2
00:00:07,380 --> 00:00:10,330
any and all the operations that we are going to learn.

3
00:00:10,350 --> 00:00:14,700
I will write them inside the same project that is inside the same program.

4
00:00:15,240 --> 00:00:21,150
At the end of the section, we will have a single program containing all the operations on arrays.

5
00:00:21,780 --> 00:00:24,990
So I will start a new project with the name <PERSON><PERSON>.

6
00:00:32,450 --> 00:00:33,080
The project.

7
00:00:35,710 --> 00:00:40,660
And here is the main function, first of all, I will define a structure for.

8
00:00:45,970 --> 00:00:51,570
And this we need to remember, that is an outrage cell for storing the numbers, so I will take integer

9
00:00:51,590 --> 00:00:52,390
type pointers.

10
00:00:52,390 --> 00:00:54,060
So this will be an integer array.

11
00:00:54,970 --> 00:00:57,340
And the second thing is size.

12
00:01:00,260 --> 00:01:06,980
And the second thing is size and then the number of elements inside that, let us call it as a land.

13
00:01:07,880 --> 00:01:10,790
So these are the three members of a structure.

14
00:01:12,050 --> 00:01:18,020
Now, here inside the main function, I will create a variable of type Uhry and also initialize it.

15
00:01:20,170 --> 00:01:21,340
So first of all.

16
00:01:22,760 --> 00:01:23,300
Uhry.

17
00:01:24,310 --> 00:01:26,440
E. R. is the adenine.

18
00:01:30,250 --> 00:01:33,550
Then for creating an I should know what is the size of an.

19
00:01:34,180 --> 00:01:38,470
So let us take it as input from keyboard so I will give a message here.

20
00:01:39,250 --> 00:01:40,600
Underside of another.

21
00:01:51,610 --> 00:01:54,820
Then read input from the keyboard using scan F.

22
00:01:57,400 --> 00:02:06,010
I should read the value in Iraq that is inside this study, the size I should read size, aeronaut size,

23
00:02:06,490 --> 00:02:09,220
and this should be affixed with Amber.

24
00:02:13,350 --> 00:02:16,590
Yeah, but this will be doing the size of scenario then.

25
00:02:19,910 --> 00:02:23,840
Once we know the size, we should create an array for this point.

26
00:02:24,020 --> 00:02:25,850
This is just an integer type pointer.

27
00:02:26,210 --> 00:02:28,210
It can point on an array.

28
00:02:30,850 --> 00:02:34,960
So we create an array inside, he then we will make this a point on that one.

29
00:02:36,490 --> 00:02:41,500
Here we are able to create an array of our required size, so I have taken the size and I will create

30
00:02:41,500 --> 00:02:45,190
an array of same size and I will make a point on that one.

31
00:02:45,200 --> 00:02:46,810
So say a assign.

32
00:02:48,750 --> 00:02:50,400
Integer type pointer.

33
00:02:51,870 --> 00:02:53,790
Then mellark function.

34
00:02:57,650 --> 00:03:01,280
Multiplied by size of integer.

35
00:03:09,390 --> 00:03:10,920
This is inside.

36
00:03:16,510 --> 00:03:21,850
Because the mental function is present in a study, Livedoor, I should include that header file.

37
00:03:28,990 --> 00:03:31,200
So here I have included petrify.

38
00:03:32,570 --> 00:03:38,170
Now, the following things are initialise, I know the size of an and I have created an inside heap

39
00:03:38,180 --> 00:03:44,960
and making point a point on that one, then I also I should set the land to zero because there are no

40
00:03:44,960 --> 00:03:46,010
elements right now.

41
00:03:46,880 --> 00:03:52,890
So this is what we need in initialization, so this array will be created in a heap.

42
00:03:53,660 --> 00:03:59,120
So this type of array structure is more preferable because the size of the dynamic, whatever the required

43
00:03:59,120 --> 00:04:00,620
sizes, you can take the size.

44
00:04:01,490 --> 00:04:05,920
If you want to fill the elements, then you can take the elements from the keyboard or anywhere else

45
00:04:05,930 --> 00:04:08,630
you can bring the elements and fill them in this array.

46
00:04:09,800 --> 00:04:18,050
Let us fill a few elements in an array and then I will write a function for displaying and so for wicking

47
00:04:18,050 --> 00:04:18,529
numbers.

48
00:04:18,529 --> 00:04:23,030
I should know how many numbers are going to be inserted so that I can pick them from keyboard.

49
00:04:23,360 --> 00:04:27,070
So I'm taking variable in and also for for loop.

50
00:04:27,080 --> 00:04:28,310
I need a variable I.

51
00:04:32,550 --> 00:04:35,430
Here, I'll give a message, enter how many numbers.

52
00:04:42,670 --> 00:04:48,460
And the number of numbers then using scanners, I will take the number of numbers in variable and.

53
00:04:55,520 --> 00:05:00,500
Now, using follow up, I will take all the wealth from the keyboard and I will store them in an array,

54
00:05:01,850 --> 00:05:04,750
I find zero I less than 10 and then I plus.

55
00:05:04,750 --> 00:05:07,640
Plus then scan if.

56
00:05:08,960 --> 00:05:17,620
Read the elements in an early so far that I have to say Airaud out of I and I should give its address,

57
00:05:17,630 --> 00:05:19,710
so here should be unperson.

58
00:05:22,830 --> 00:05:27,900
Now, before follow up, it's better if I give a message that under all elements.

59
00:05:36,890 --> 00:05:42,920
Yes, this follows the lead all the elements from keyboard and store them in and array from starting

60
00:05:42,920 --> 00:05:43,990
location to end.

61
00:05:50,130 --> 00:05:55,230
After insisting all the elements, I should set length of an early as an.

62
00:05:58,090 --> 00:06:03,610
Because I'm inserting an element now here, I will write on function for display.

63
00:06:05,180 --> 00:06:06,580
All the elements of an early.

64
00:06:10,720 --> 00:06:16,570
It should take a structured study, let us call parameter name also Iara.

65
00:06:18,370 --> 00:06:24,760
Then it should simply display all the elements of war that it needs a variable, i.e., for using Falu

66
00:06:25,630 --> 00:06:27,100
so it can give a message.

67
00:06:33,060 --> 00:06:34,140
Elements are.

68
00:06:35,490 --> 00:06:37,410
Then again, new line.

69
00:06:43,340 --> 00:06:50,350
Then using follow up, I will display all elements here, I assign zero, I use less than eight are

70
00:06:50,360 --> 00:06:54,500
not lente whatever the lenders then I placeless.

71
00:06:56,270 --> 00:07:00,380
Then being deaf person daily to space.

72
00:07:04,110 --> 00:07:05,770
Sort of I.

73
00:07:11,100 --> 00:07:16,560
And here inside main function, I will call display function by passing Aitarak.

74
00:07:18,600 --> 00:07:20,490
Let us on the program and test it.

75
00:07:28,120 --> 00:07:30,940
Here and to the side of al-Nouri, I will say.

76
00:07:32,820 --> 00:07:40,110
And number of numbers, I want only five numbers and all the elements, so two, three, four, five,

77
00:07:40,560 --> 00:07:41,250
six.

78
00:07:42,210 --> 00:07:47,400
And the display is displaying all the elements here, two, three, four, five, six.

79
00:07:49,590 --> 00:07:53,970
Here I have insulted all the elements then here display the displaying all elements.

80
00:08:03,170 --> 00:08:07,580
Let us debug the program and see how these elements are stored in an update.

81
00:08:08,240 --> 00:08:13,430
I'll put a breakpoint here where I'm inserting the elements in an area that is filling the elements

82
00:08:13,430 --> 00:08:15,830
in an array, and I will run the program.

83
00:08:19,450 --> 00:08:25,610
And under the side of an array side of an array is 10 and the number of number five.

84
00:08:26,470 --> 00:08:28,330
Now it has entered in debugging mode.

85
00:08:29,990 --> 00:08:31,100
If you look into this.

86
00:08:32,419 --> 00:08:41,539
Did Bulgaria I expand this study, it is just showing they object if I expand the study, it is having

87
00:08:41,539 --> 00:08:48,080
a three pointer that is pointing on some valid address and the size of the 10 and Lento's initialized

88
00:08:48,080 --> 00:08:48,660
to zero.

89
00:08:48,680 --> 00:08:51,140
So these are the things already I have finished in here.

90
00:08:54,490 --> 00:09:00,400
Right, then, if I expand this A I cannot find anything is a point very disappointing on the first

91
00:09:00,400 --> 00:09:02,260
element and that element is zero.

92
00:09:03,830 --> 00:09:09,230
If I feel the elements, let us see what happens, I will continue and there are elements.

93
00:09:10,160 --> 00:09:11,090
First element.

94
00:09:12,730 --> 00:09:21,570
As seven, yeah, you can see seven here inside this debug area that is inside watch seven instead.

95
00:09:22,120 --> 00:09:22,920
I'll continue.

96
00:09:25,280 --> 00:09:27,590
Next, element eight.

97
00:09:29,340 --> 00:09:30,930
I cannot see it anywhere.

98
00:09:32,220 --> 00:09:37,860
See is a pointer is pointing on an area in a heap, so it is pointing on the first element and it is

99
00:09:37,860 --> 00:09:39,110
showing the first element.

100
00:09:40,170 --> 00:09:41,810
It is just beside seven.

101
00:09:41,820 --> 00:09:43,050
So I'm unable to see it.

102
00:09:44,170 --> 00:09:49,420
As the reason he said it will not show all the contents of al-Nouri, it is showing a location where

103
00:09:49,420 --> 00:09:51,460
Pointer is pointing.

104
00:09:52,830 --> 00:09:57,690
So I can continue and fill up all the rest of the elements and also display them, so he didn't say

105
00:09:57,690 --> 00:10:00,540
that he was very upset and able to see and are earning.

106
00:10:02,240 --> 00:10:03,710
OK, I will stop the program.

107
00:10:08,120 --> 00:10:09,290
Remove breakpoint.

108
00:10:10,580 --> 00:10:17,240
Now, I have another option for discussing the topics, so instead of taking an array pointer, I will

109
00:10:17,240 --> 00:10:23,650
change it to an array of a size 20 instead of taking a pointer and creating an array in the heap.

110
00:10:23,960 --> 00:10:30,410
I am taking off exercise early, so I will be using this example that is easy for me for explanation.

111
00:10:30,740 --> 00:10:33,070
So I will modify the things here.

112
00:10:33,560 --> 00:10:39,200
See, I do not have to create an array in here now and even I don't have to set all these things.

113
00:10:39,560 --> 00:10:40,730
I will remove this part.

114
00:10:43,030 --> 00:10:48,600
Now that I'm treating al-Nouri at that time, only, I will directly initialize it inside the object,

115
00:10:49,000 --> 00:10:50,910
see the first thing of the elements.

116
00:10:50,980 --> 00:10:53,440
So initially I will fill the elements.

117
00:10:53,470 --> 00:10:55,990
That is two, three, four, five and six.

118
00:10:55,990 --> 00:10:57,070
I have filled elements.

119
00:10:57,430 --> 00:11:04,620
The next aside, the size of the 20 we have given the size of 20 and a number of elements are five feet.

120
00:11:05,050 --> 00:11:09,620
This structure will be initialized with the values that I have given.

121
00:11:09,910 --> 00:11:16,150
So the first part is stored in an array and the second 20 is the size of another because I have already

122
00:11:16,150 --> 00:11:21,790
mentioned the size here, 20 and then five is the length of another eight minutes, the number of elements

123
00:11:21,790 --> 00:11:22,290
in an array.

124
00:11:23,140 --> 00:11:26,490
So directly I have taken a fixator esoterically.

125
00:11:26,500 --> 00:11:30,580
I have filled the values in the declaration and initialization part.

126
00:11:31,060 --> 00:11:34,740
So here even I don't have to keep all the elements.

127
00:11:34,750 --> 00:11:37,130
I will not take all the elements directly.

128
00:11:37,150 --> 00:11:38,470
I will call display function.

129
00:11:42,940 --> 00:11:49,710
I don't need these variables, see, the program code looks very simple so that it's easy for you to

130
00:11:49,710 --> 00:11:50,340
understand.

131
00:11:51,890 --> 00:11:57,650
I have an array already initialised and I'm calling display function, so let us go to this function

132
00:11:57,650 --> 00:12:00,390
display function is displaying all these elements is perfect.

133
00:12:00,410 --> 00:12:02,270
So I don't have to modify this function.

134
00:12:03,860 --> 00:12:05,770
Let us run and see what happens.

135
00:12:07,540 --> 00:12:14,170
Yes, it is displaying all the elements, that is two, three, four, five, six is perfect, and if

136
00:12:14,170 --> 00:12:20,560
I debate this will be showing all these elements that are from two to six letters said, I'll put a

137
00:12:20,560 --> 00:12:23,560
breakpoint on display and I will run the program.

138
00:12:26,510 --> 00:12:33,680
You are here inside this area, you can see that Arada is an object of type or a variable of type A

139
00:12:33,950 --> 00:12:39,600
sizes 20 25 and a array is of size 22.

140
00:12:39,620 --> 00:12:41,330
It is showing all 20 elements.

141
00:12:41,660 --> 00:12:43,030
All the elements are zero.

142
00:12:43,220 --> 00:12:45,270
Only first five elements are initially.

143
00:12:45,650 --> 00:12:48,580
So I'll be using this example for explanation, but.

144
00:12:49,730 --> 00:12:56,930
That's all in this video and the next video, you can find a function for insight and the program will

145
00:12:56,930 --> 00:12:58,040
be a same program.

146
00:12:58,050 --> 00:13:02,860
So inside the same program, I will be writing a function for inserting an element in adding.

