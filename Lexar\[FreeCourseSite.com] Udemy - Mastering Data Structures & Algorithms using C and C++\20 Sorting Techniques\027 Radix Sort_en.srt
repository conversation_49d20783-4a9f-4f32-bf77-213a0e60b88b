1
00:00:00,510 --> 00:00:07,200
Our next topic is a remix of sort for sorting the elements using this method already I have taken an

2
00:00:07,200 --> 00:00:08,610
array of elements.

3
00:00:08,620 --> 00:00:12,820
These are various elements that I'm going to sort them the sorting matter.

4
00:00:12,840 --> 00:00:20,040
The same has been sort of similar to Binstock, but it's been sort of taking array of bins equal to

5
00:00:20,040 --> 00:00:22,950
the largest element present in the light.

6
00:00:23,400 --> 00:00:26,430
So what is the largest element in this one here?

7
00:00:26,910 --> 00:00:28,000
Three forty eight.

8
00:00:28,170 --> 00:00:32,460
So imagine I should have an array of Binzel size three forty eight.

9
00:00:32,759 --> 00:00:35,390
It will require a lot of space to be very big size.

10
00:00:36,300 --> 00:00:40,080
So to make that the betonsports simple, we introduce radix.

11
00:00:41,550 --> 00:00:42,740
So how it is different.

12
00:00:43,140 --> 00:00:44,430
See we will not take.

13
00:00:45,320 --> 00:00:49,440
Three forty eight minutes, but we will take only ten minutes.

14
00:00:49,580 --> 00:00:50,800
Why only ten minutes?

15
00:00:51,230 --> 00:00:54,580
What, these numbers are indigenous, OK?

16
00:00:55,310 --> 00:01:01,850
But this number system, decimal number system, OK, decimal number system will have how many digits?

17
00:01:02,150 --> 00:01:11,960
029 So take the zero to nine because that said, these are enough for sorting those elements using that

18
00:01:12,000 --> 00:01:12,540
exact.

19
00:01:14,070 --> 00:01:21,610
So that's why the name is Radix, it means if these individuals were in the binary form, then how many

20
00:01:21,610 --> 00:01:21,930
beans?

21
00:01:21,930 --> 00:01:23,520
Enough to beans enough.

22
00:01:24,460 --> 00:01:30,990
Yes, if suppose this is awful numbers, these are all octal numbers, I suppose if they're octal,

23
00:01:31,180 --> 00:01:33,550
then only eight wins are enough for 07.

24
00:01:33,910 --> 00:01:36,820
So that's why the name comes Radix Soke.

25
00:01:37,390 --> 00:01:45,630
So for sorting this decimal numbers, I have already taken 10 Obinze and also initialize them to none.

26
00:01:46,450 --> 00:01:47,620
So set up already.

27
00:01:48,730 --> 00:01:51,210
Next, we have to learn how to solve them.

28
00:01:52,180 --> 00:01:54,340
So let us start sorting the numbers.

29
00:01:55,300 --> 00:02:02,950
So let us start the procedure in the procedure, I will scan through this list of elements and every

30
00:02:02,950 --> 00:02:09,070
element example, 237 for 237.

31
00:02:09,310 --> 00:02:11,000
There is no been available here.

32
00:02:11,290 --> 00:02:11,680
No, no.

33
00:02:11,680 --> 00:02:12,850
You do not have to drop it.

34
00:02:12,850 --> 00:02:14,580
And been to 37.

35
00:02:14,890 --> 00:02:17,770
You have to drop and been seven.

36
00:02:17,980 --> 00:02:21,960
So it means I should just check last digit of a number.

37
00:02:22,360 --> 00:02:22,900
Yes.

38
00:02:23,170 --> 00:02:27,360
Just check the last digit and then drop it and the corresponding bill.

39
00:02:27,550 --> 00:02:31,990
So this is 237 will get a drop here in the bill seven.

40
00:02:31,990 --> 00:02:36,380
So I will not draw nodes and on I will just write number to 37.

41
00:02:37,090 --> 00:02:38,050
It is a top tier.

42
00:02:38,590 --> 00:02:40,990
So I hope you can know that the last digit.

43
00:02:41,200 --> 00:02:50,110
So whatever the number is, if it is alive I then Maudet by 10 you get a number so large that you will

44
00:02:50,110 --> 00:02:52,540
get is not the same thing.

45
00:02:52,540 --> 00:02:55,720
I will do it for all one for the six this is six.

46
00:02:55,720 --> 00:02:59,830
It will go and go between six one forty six.

47
00:03:00,640 --> 00:03:03,040
The next is two fifty nine.

48
00:03:03,670 --> 00:03:07,660
It will go to bin nine to fifty nine.

49
00:03:08,950 --> 00:03:20,710
Makes us three forty eight bin eight 152 bin two one six two three bin three to thirty five bin five

50
00:03:21,670 --> 00:03:22,950
forty eight bin.

51
00:03:22,960 --> 00:03:25,510
They all the reason why is there so below that.

52
00:03:25,630 --> 00:03:29,020
Forty eight, thirty six bin six.

53
00:03:29,020 --> 00:03:32,020
Although the number is dead so below that thirty six.

54
00:03:32,410 --> 00:03:39,910
Sixty two is here and bin to all the numbers I have scan and I have filled them in their corresponding

55
00:03:39,910 --> 00:03:42,400
bins based on the last digit.

56
00:03:43,180 --> 00:03:44,140
Are they sorted.

57
00:03:44,710 --> 00:03:45,310
Let us see.

58
00:03:45,640 --> 00:03:51,130
I have to pick out all these elements from all the bins one by one, by scanning through all these bins

59
00:03:52,090 --> 00:03:52,750
one by one.

60
00:03:52,750 --> 00:03:53,830
Take out all the elements.

61
00:03:53,860 --> 00:03:55,000
As I said, these are C4.

62
00:03:55,000 --> 00:03:59,080
Don't go like a stack, don't follow lethal follow fearful.

63
00:03:59,230 --> 00:04:00,550
And this was the first element.

64
00:04:00,550 --> 00:04:02,080
Take it out for the next element.

65
00:04:02,080 --> 00:04:02,920
Take it out mixed.

66
00:04:03,280 --> 00:04:04,480
So let us follow the order.

67
00:04:04,810 --> 00:04:07,750
I will be emptying bins and I will write down the elements here.

68
00:04:07,990 --> 00:04:09,940
So let us tag this has been attempted.

69
00:04:09,980 --> 00:04:10,810
This is empty.

70
00:04:10,810 --> 00:04:11,950
This is 152.

71
00:04:12,220 --> 00:04:17,290
So read on one fifty to the next is 60 to 62.

72
00:04:17,290 --> 00:04:18,579
I will separate them by comma.

73
00:04:18,790 --> 00:04:20,200
So these elements are gone.

74
00:04:21,820 --> 00:04:22,390
Nix's one.

75
00:04:22,450 --> 00:04:23,170
Sixty three.

76
00:04:26,440 --> 00:04:28,840
Next nexus to thirty five.

77
00:04:31,830 --> 00:04:33,600
One forty six and thirty six.

78
00:04:38,190 --> 00:04:39,150
237.

79
00:04:43,240 --> 00:04:44,980
Three forty eight and forty eight.

80
00:04:51,560 --> 00:04:52,450
259.

81
00:05:01,410 --> 00:05:08,360
So have emptied all the bins and also made them null the elements out here, are this sorted?

82
00:05:08,910 --> 00:05:14,060
No, which is the smallest element, 26 that should come in the beginning.

83
00:05:14,490 --> 00:05:15,440
They are not sorted.

84
00:05:15,900 --> 00:05:17,820
They are not sorted then, but.

85
00:05:18,790 --> 00:05:20,590
Again, drop them in bins.

86
00:05:21,310 --> 00:05:27,790
On what basis, on the second digit from the right hand side, second visit first we have dropped them

87
00:05:27,790 --> 00:05:32,900
based on the last digit, no second last digits, second from the right hand side.

88
00:05:33,580 --> 00:05:36,220
Secondly, how to take out the second digit.

89
00:05:36,430 --> 00:05:41,490
So for any element of what I should do is divided by 10.

90
00:05:41,860 --> 00:05:51,280
So if I divide it by 10, like fifty, if I divide it by 10, then I get 15, then more divide than

91
00:05:51,910 --> 00:05:53,380
OK, four more by 10.

92
00:05:53,530 --> 00:05:54,550
Then they get five.

93
00:05:54,940 --> 00:05:55,510
Yes.

94
00:05:55,840 --> 00:05:58,730
So divide by ten and the more to buy it.

95
00:05:58,750 --> 00:06:06,460
And so it remains unclear what we did divide by one and then the more to by then that's what we did.

96
00:06:06,790 --> 00:06:10,390
Now that division number has became 10 next time.

97
00:06:11,300 --> 00:06:13,440
OK, we will follow this now.

98
00:06:13,710 --> 00:06:13,930
Right.

99
00:06:14,200 --> 00:06:17,910
This was the first time we have done and the second time we will do that.

100
00:06:18,130 --> 00:06:22,120
So first pass is completed and this is the result of the first pass.

101
00:06:24,860 --> 00:06:30,840
Now we have to perform same procedure upon second digit and the second pass.

102
00:06:31,640 --> 00:06:32,870
So let me do it now.

103
00:06:33,620 --> 00:06:35,810
One been five.

104
00:06:39,850 --> 00:06:42,100
Sixty to win six.

105
00:06:45,230 --> 00:06:47,880
163 been six.

106
00:06:51,470 --> 00:06:52,790
Two, three, five, three,

107
00:06:55,970 --> 00:06:57,500
146, been four.

108
00:07:01,030 --> 00:07:02,290
Thirty six, Bindaree.

109
00:07:05,520 --> 00:07:06,510
Thirty seven been.

110
00:07:10,470 --> 00:07:11,940
Three forty eight have been for.

111
00:07:15,700 --> 00:07:17,350
48 Benfold.

112
00:07:20,580 --> 00:07:22,050
Fifty nine, been five.

113
00:07:25,900 --> 00:07:30,370
So I have dropped all those elements once again from other incumbents.

114
00:07:31,340 --> 00:07:34,460
Now, empty all these bins one by one.

115
00:07:35,740 --> 00:07:47,820
So I will like the result here, empty, empty, empty, two, 35, 36 to 37 to 35, 36 to 37.

116
00:07:48,700 --> 00:07:49,740
So these are all gone.

117
00:07:52,270 --> 00:07:55,480
One four two six three forty eight 48.

118
00:08:02,400 --> 00:08:03,930
One fifty two to fifty nine.

119
00:08:10,320 --> 00:08:19,020
Sixty to one one sixty three, so I have emptied all these bins and the elements are done here, so

120
00:08:19,050 --> 00:08:21,390
this is the result of the second pass.

121
00:08:23,700 --> 00:08:26,930
So they need to pass a over check, other elements photic.

122
00:08:28,060 --> 00:08:31,020
Smallest element, the statistic that should come first.

123
00:08:31,060 --> 00:08:33,010
It's not that they are not subject.

124
00:08:35,580 --> 00:08:40,140
Then what I repeat, for one more pass, one more time, you do it.

125
00:08:41,470 --> 00:08:46,170
Already two times we have done by the time we did not get them sorted.

126
00:08:46,210 --> 00:08:46,810
Yes.

127
00:08:47,050 --> 00:08:49,090
Then see the digits.

128
00:08:49,090 --> 00:08:54,430
If you see the largest number, that is three forty eight is off a three digit.

129
00:08:55,060 --> 00:08:56,980
So you have to perform three passes.

130
00:08:56,980 --> 00:08:58,760
After three process, they will get sorted.

131
00:08:59,380 --> 00:09:02,230
OK, let us try it for one more pass.

132
00:09:02,560 --> 00:09:08,950
Now, based on which digit we should do, we have finished up on rightmost digit then the second digit

133
00:09:08,950 --> 00:09:09,340
also.

134
00:09:09,580 --> 00:09:11,890
No, we will do it on third digit.

135
00:09:12,100 --> 00:09:14,930
So it means first digit from right side of this third digit.

136
00:09:15,520 --> 00:09:20,190
What about this two digits only then assume this digit zero.

137
00:09:20,410 --> 00:09:25,380
OK, so 448 also this digits zero, 60 go to zero.

138
00:09:25,660 --> 00:09:27,740
So we will perform upon this one.

139
00:09:28,000 --> 00:09:30,760
So how to get this one from two thirty five.

140
00:09:30,760 --> 00:09:34,600
How to get to see Signaler nine the third pass.

141
00:09:34,600 --> 00:09:43,210
What I should do aof I divided by hundred then the more than ten how to thirty five.

142
00:09:43,210 --> 00:09:53,440
If I take divided by a hundred this gives the what result to then more to ten if I do more to ten gives

143
00:09:53,440 --> 00:09:58,090
answer to only see us divided by a hundred and more two.

144
00:09:58,090 --> 00:10:01,300
Then you will get the third digit from the right hand side.

145
00:10:01,900 --> 00:10:05,530
Now based on the third digit from right side that is third place.

146
00:10:05,950 --> 00:10:08,560
Drop the elements and bense so I will do it now.

147
00:10:09,340 --> 00:10:10,240
This is two.

148
00:10:10,330 --> 00:10:13,090
So it should going to be two to thirty five.

149
00:10:13,090 --> 00:10:16,360
Been two to thirty five.

150
00:10:16,870 --> 00:10:17,620
This is zero.

151
00:10:17,740 --> 00:10:19,180
It should go into zero.

152
00:10:19,360 --> 00:10:20,440
That is thirty six.

153
00:10:21,820 --> 00:10:26,260
Then this is two, two, three, seven, it should going to be two to thirty seven.

154
00:10:27,350 --> 00:10:35,750
One four to six, it should go into bin one one four two six three forty eight, it should go to bin

155
00:10:35,750 --> 00:10:40,760
three three forty eight, bin zero forty eight.

156
00:10:40,880 --> 00:10:42,350
So this is forty eight.

157
00:10:43,790 --> 00:10:56,720
Ben one 152 Ben won 152, bindu 259 beento 259, Ben zero 62, Ben zero 62.

158
00:10:58,010 --> 00:10:59,540
Ben won 163.

159
00:10:59,540 --> 00:11:01,400
Ben won 163.

160
00:11:02,720 --> 00:11:10,520
All elements are completed, no, I should empty all these things, let us start from what's been 36,

161
00:11:10,520 --> 00:11:23,250
48, 62, 36, 48, 62, next been 146, 152, 163, so 146, 152, 163, 146, 152, 163.

162
00:11:23,660 --> 00:11:27,900
So these are also gone next to thirty five to thirty seven to fifty nine.

163
00:11:28,430 --> 00:11:31,790
These three elements copied their last one.

164
00:11:31,790 --> 00:11:32,660
Three forty eight.

165
00:11:35,110 --> 00:11:41,320
So this is the end of a third, so the result of box, check out this audit.

166
00:11:41,770 --> 00:11:43,590
Yes, the elements are sorted.

167
00:11:44,020 --> 00:11:45,920
Yes, the elements are sorted.

168
00:11:46,780 --> 00:11:51,460
So we have done it in three process cities similar to confort.

169
00:11:51,460 --> 00:11:58,990
But the number of parcels are more because we are not taking bids equal to the maximum and we have limited

170
00:11:58,990 --> 00:12:02,670
means that slight number of parcels has increased.

171
00:12:02,920 --> 00:12:04,640
So the time has increased.

172
00:12:05,140 --> 00:12:07,280
Yes, time has increased a little bit.

173
00:12:07,540 --> 00:12:09,070
That is the process is repeating.

174
00:12:09,100 --> 00:12:15,020
Same elements are being done multiple times and that time is depend on a number of digits of a number.

175
00:12:15,460 --> 00:12:21,450
So if you have the numbers in less than a thousand, then you have to do it three times less than 10000.

176
00:12:21,460 --> 00:12:22,740
You have to do it four times.

177
00:12:23,170 --> 00:12:26,600
So depending on the largest number that you have, depending on its digits.

178
00:12:26,620 --> 00:12:32,290
You have to do it that many times and how to get the digits last digit, then second loss and the third

179
00:12:32,290 --> 00:12:38,080
loss from the right hand side is divided by one and ten, divided by 10 and more, then divided by hundred

180
00:12:38,080 --> 00:12:38,720
and ten.

181
00:12:38,860 --> 00:12:40,910
So this will give you the digits.

182
00:12:41,230 --> 00:12:42,630
So what is the time taken?

183
00:12:43,420 --> 00:12:46,620
We are coping all the elements and copying them back there.

184
00:12:46,630 --> 00:12:47,100
So it is.

185
00:12:47,110 --> 00:12:53,250
And what doing how many times, depending on the number of digits so Lipsey been doing.

186
00:12:53,740 --> 00:12:56,250
Now this is almost like a constant.

187
00:12:56,260 --> 00:12:58,420
So it's not enough time.

188
00:12:58,420 --> 00:12:59,470
So it's not a square.

189
00:12:59,740 --> 00:13:08,500
So we can say this outdraw and others say it being grenvilles, the maximum number of digits and the

190
00:13:08,500 --> 00:13:10,660
space is not huge.

191
00:13:10,990 --> 00:13:16,780
Just we have attendance and the number of nodes that will be created in business depends on the number

192
00:13:16,780 --> 00:13:17,410
of elements.

193
00:13:17,710 --> 00:13:21,970
So the space is limited and it is enlightenments number of pins.

194
00:13:22,480 --> 00:13:24,120
So this is limited.

195
00:13:24,250 --> 00:13:25,990
It's not a huge space.

196
00:13:26,140 --> 00:13:28,140
So that's all about traffic sort.

197
00:13:28,690 --> 00:13:30,120
The next thing is a function.

198
00:13:30,130 --> 00:13:33,180
So that function, I believe it does student exercise.

199
00:13:33,550 --> 00:13:34,870
So you write on the function.

200
00:13:34,870 --> 00:13:38,440
The function will be almost the same as a business.

201
00:13:38,440 --> 00:13:44,010
Not only only the thing is the copying of these elements here and again back there.

202
00:13:44,290 --> 00:13:50,170
This process you have to repeat for some number of times, multiple times based on this.

203
00:13:50,410 --> 00:13:52,880
Now, this is deviser was one continuous device.

204
00:13:52,900 --> 00:13:56,500
There was a continuum device that was hundred continue.

205
00:13:56,740 --> 00:13:58,540
One deviser become an extra digit.

206
00:13:58,540 --> 00:13:59,410
That is Towsend.

207
00:13:59,560 --> 00:14:03,790
It is greater than any maximum number, so stoppered.

208
00:14:04,390 --> 00:14:09,060
So three times you have to run the loop and every time this will be multiplied by ten ten.

209
00:14:09,070 --> 00:14:12,700
So when it is becoming greater than the largest number, maximum number you have to stop.

210
00:14:13,330 --> 00:14:15,000
So this is a student exercise.

211
00:14:15,010 --> 00:14:16,090
I will not explain it.

212
00:14:16,450 --> 00:14:19,060
You have to write on the program and I will be showing your demo.

213
00:14:19,450 --> 00:14:21,420
That's all about Rydingsvard.

