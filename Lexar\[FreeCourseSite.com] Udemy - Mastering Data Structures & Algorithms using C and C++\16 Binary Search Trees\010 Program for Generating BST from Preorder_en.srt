1
00:00:00,390 --> 00:00:05,740
Let us write a function for generating a binary stream from preorder traversal.

2
00:00:06,030 --> 00:00:09,030
I'm calling the function names to create from preorder.

3
00:00:09,300 --> 00:00:14,490
It takes the bottom array of individuals that is body is an array.

4
00:00:14,790 --> 00:00:18,020
And also I need to size in this procedure.

5
00:00:18,030 --> 00:00:20,700
I need a stack of pointers.

6
00:00:20,700 --> 00:00:25,290
So I take a stack, then I need a temporary pointer also.

7
00:00:25,290 --> 00:00:25,830
So I will.

8
00:00:27,440 --> 00:00:28,880
Create a temporary point.

9
00:00:29,270 --> 00:00:38,090
I also need a visual scan through this array, so initially I signed with <PERSON><PERSON>, OK, required things

10
00:00:38,090 --> 00:00:38,570
are ready.

11
00:00:38,750 --> 00:00:42,050
If anything more, I will declare it and I'll start the procedure.

12
00:00:42,060 --> 00:00:46,070
The first thing was we created a route node, as in that route is global.

13
00:00:46,280 --> 00:00:50,600
So first of all, create a new node using group directly.

14
00:00:51,450 --> 00:00:52,700
Yeah, here I have created.

15
00:00:52,960 --> 00:00:56,520
This is the first route node and I'm using a new operator here.

16
00:00:56,560 --> 00:00:58,430
We can use Mallott function also.

17
00:00:58,860 --> 00:01:06,650
So some time I'm writing new or whatever it is, then send the data from a body and move it to the next

18
00:01:06,650 --> 00:01:07,030
element.

19
00:01:07,040 --> 00:01:11,100
Also, then the set left children rachell both together as this.

20
00:01:11,120 --> 00:01:17,450
Also note that also that was the first thing we have done then to make point on route PS pointing en

21
00:01:17,450 --> 00:01:17,820
route.

22
00:01:18,590 --> 00:01:19,730
So this what we have done.

23
00:01:20,670 --> 00:01:26,640
Now, the procedure was repeating how long until it reaches the end so total, how many elements are

24
00:01:26,640 --> 00:01:26,970
there?

25
00:01:27,510 --> 00:01:28,640
Eight elements are there.

26
00:01:28,860 --> 00:01:30,730
So end is eight, right.

27
00:01:31,230 --> 00:01:37,080
So I should stop when I becomes eight, so I should continue until I is equal to seven.

28
00:01:37,500 --> 00:01:37,990
So I will try.

29
00:01:38,010 --> 00:01:43,920
I don't know why, because this repetition, these steps are repeating depending on the number of elements

30
00:01:43,920 --> 00:01:44,610
in this study.

31
00:01:44,730 --> 00:01:51,260
So while I is less than and then I is equal to N that is eight stop.

32
00:01:52,180 --> 00:01:56,190
So I am not using follow because I is not in agreement always.

33
00:01:58,480 --> 00:02:00,190
Let us recall, what are the steps?

34
00:02:01,350 --> 00:02:02,090
First thing.

35
00:02:03,540 --> 00:02:09,990
If if they're limited in the priorities, is then be data pointing here, if it is a smaller yes, if

36
00:02:09,990 --> 00:02:16,110
it is a smaller, then create a new node with the help of temporary pointer to assign a new node.

37
00:02:18,260 --> 00:02:19,310
Said the data.

38
00:02:20,980 --> 00:02:25,690
If the Lebensraum preorder is less than they are, like if you remember for 20, we did that.

39
00:02:26,730 --> 00:02:33,450
If it is smaller, then create a new node and send the data, make two children as null, both left

40
00:02:33,450 --> 00:02:37,100
and right as null, and make these left child point here.

41
00:02:37,410 --> 00:02:40,770
Left Cherry Point here, then push that address off.

42
00:02:40,780 --> 00:02:42,570
Peter, the study into the stack.

43
00:02:44,490 --> 00:02:47,910
Then be assigned to bring be upon the.

44
00:02:50,440 --> 00:02:53,350
These are the steps we were doing when the data was a small.

45
00:02:54,480 --> 00:03:00,600
Otherwise, there's no space here, so I'll continue here, I will remove your things and show you if

46
00:03:00,600 --> 00:03:05,440
it is not less element is not less than piece of data, if it is greater.

47
00:03:05,790 --> 00:03:06,800
There are two cases.

48
00:03:07,140 --> 00:03:12,080
If it is greater than it should be within range, it is within range.

49
00:03:12,090 --> 00:03:13,620
Then we create a node and link it.

50
00:03:14,650 --> 00:03:19,910
If it is not within range, then report an address from the stack and move based upon that previous

51
00:03:20,670 --> 00:03:21,670
to to those steps.

52
00:03:23,500 --> 00:03:30,900
So here I have it on the steps like element in the pre order is greater than BP's data.

53
00:03:31,930 --> 00:03:37,480
And also, aluminum preorder is a smaller than stack top Siddharta.

54
00:03:38,500 --> 00:03:46,450
Stockpot will return the address, and it's a data that denotes data and when will I have to write something

55
00:03:46,460 --> 00:03:49,720
here so that if the stack is empty, it should be infinity.

56
00:03:49,750 --> 00:03:53,190
So I did not write that piece of code venturing demo.

57
00:03:53,200 --> 00:03:54,040
I will write on that.

58
00:03:55,820 --> 00:04:02,390
Next, a new know notice, greater data is set and the pointers are set as no BS right side, this may

59
00:04:03,320 --> 00:04:07,340
be that Espy's right will be pointing on this one right.

60
00:04:07,970 --> 00:04:12,120
And move on in order to bring peace upon this new team.

61
00:04:14,030 --> 00:04:19,910
Otherwise, if this condition fails, then pop out an address from the stock on the move B upon that

62
00:04:19,910 --> 00:04:20,190
note.

63
00:04:21,019 --> 00:04:21,870
Don't do anything.

64
00:04:22,190 --> 00:04:25,880
If this condition fails, just move to the previous note.

65
00:04:26,060 --> 00:04:27,560
And I is not moving.

66
00:04:27,710 --> 00:04:29,710
It remains on the same element in an anti.

67
00:04:30,680 --> 00:04:37,070
So that's all this is the end of if and the end of my loop until the end of function, this law will

68
00:04:37,070 --> 00:04:39,740
create a binary search tree from just preorder.

69
00:04:39,750 --> 00:04:41,290
We don't need in order.

70
00:04:41,870 --> 00:04:48,500
Now, a little bit of analysis, how much time it has taken, what we have done, we have just to scan

71
00:04:48,500 --> 00:04:49,730
through all these elements.

72
00:04:49,740 --> 00:04:51,050
So how many elements are there?

73
00:04:51,050 --> 00:04:55,850
And so times are from the work that I have done.

74
00:04:56,150 --> 00:05:03,020
Based on that, I told you that time, look at the code Gordis having loop, which loop by loop, how

75
00:05:03,020 --> 00:05:05,930
many times it will repeat is less than 10.

76
00:05:05,930 --> 00:05:11,760
And somewhere here and here I was done so I was moving until then.

77
00:05:11,990 --> 00:05:14,840
So this loop is similar to for loop only.

78
00:05:14,840 --> 00:05:15,830
So end times.

79
00:05:16,820 --> 00:05:23,450
So this loop idea starting from zero and up to and it is going so it is end times, so Loop will repeat

80
00:05:23,450 --> 00:05:28,930
14 times and all the statements are simple statements, only all of them because Konstantine.

81
00:05:29,300 --> 00:05:32,780
So if we count the steps also one, two, three, four, five, six, seven.

82
00:05:34,100 --> 00:05:40,280
Otherwise, this might happen, so one, two, three, four, four, so maximum seven, so I should

83
00:05:40,280 --> 00:05:45,580
say seven and seven, seven steps, seven times.

84
00:05:45,590 --> 00:05:52,480
And so this is nothing but ultrathin degree of polynomial isn't thought of him, that's all.

85
00:05:52,670 --> 00:05:53,080
So.

86
00:05:53,690 --> 00:05:59,740
So finally, a binary society can be created just using preorder or just using post.

87
00:06:01,090 --> 00:06:05,920
So we don't need in order and it can be generated only in order oftentime.

88
00:06:06,830 --> 00:06:10,070
Preordered, I have shown you, Bulstrode is your exercice.

89
00:06:11,210 --> 00:06:14,020
Who won him for the post order, you have to start from Rightside.

90
00:06:15,650 --> 00:06:16,240
That's what.

