1
00:00:00,750 --> 00:00:07,540
In this video, we will learn how to insert a new node in our existing Dubberly linked list.

2
00:00:08,580 --> 00:00:15,540
There are two cases for insertion <PERSON>'s inserting before Fassnacht, before Fastenal, and second

3
00:00:15,540 --> 00:00:19,410
one is inserting at any other given index at any given index.

4
00:00:20,130 --> 00:00:22,680
So right now, in my example, there are five nodes.

5
00:00:23,250 --> 00:00:26,160
What are the positions where I can insert a new node?

6
00:00:26,160 --> 00:00:30,590
I can insert a new node before this, nor it is called that index has zero.

7
00:00:30,600 --> 00:00:34,280
So I can insert an index zero hour after this node does this one.

8
00:00:34,290 --> 00:00:38,910
And this is to an index and this is called index food.

9
00:00:39,660 --> 00:00:43,740
And if I'm inserting at last after the fifth node, then this is fifth index.

10
00:00:44,100 --> 00:00:48,720
So total six positions are possible where I can insert a new node.

11
00:00:49,440 --> 00:00:55,590
So inserting at index zero, I'm calling it as inserting before fussin or so.

12
00:00:55,590 --> 00:00:57,040
Let us look at these cases.

13
00:00:57,150 --> 00:01:03,300
So first let us see inserting before fust nor what should be the procedure.

14
00:01:04,209 --> 00:01:11,950
Let us see what are the steps required for inserting a new law before fussin or at Index zero, first

15
00:01:11,950 --> 00:01:15,150
step is create a new order with the help of some temporary point.

16
00:01:15,280 --> 00:01:16,600
So I will create a new normed.

17
00:01:17,890 --> 00:01:19,870
Then set the value here, so I'm starting the.

18
00:01:22,300 --> 00:01:29,260
Then how many links I have to modify one, two, and this one, total train links I should modify.

19
00:01:30,160 --> 00:01:37,660
So first, I will make this has no previous hazmat suits, and it should point on first and foremost

20
00:01:37,660 --> 00:01:39,940
to previous should the point on this one.

21
00:01:40,390 --> 00:01:42,670
So total one, two, three links.

22
00:01:42,670 --> 00:01:45,720
I have modified that after modifying three links.

23
00:01:46,090 --> 00:01:51,700
This has became a first node then is the first node from then you can go to six nine, three four one

24
00:01:51,970 --> 00:01:52,840
or from six.

25
00:01:52,840 --> 00:01:54,920
If you come back you can come on ten.

26
00:01:55,150 --> 00:01:58,660
So I should bring that on the first upon this new node.

27
00:01:58,870 --> 00:02:01,480
So bring the pointer first up on new node.

28
00:02:02,920 --> 00:02:04,330
So move first from here.

29
00:02:05,420 --> 00:02:07,290
So these are the steps I have to perform.

30
00:02:08,000 --> 00:02:10,650
Let me write down instructions for this procedure.

31
00:02:11,750 --> 00:02:13,340
First of all, create a new NORDER.

32
00:02:14,740 --> 00:02:16,880
New notice is greater than the data.

33
00:02:18,010 --> 00:02:22,390
This is filling the data set of you that make this link as null.

34
00:02:25,100 --> 00:02:32,870
Then make this link point on for us to tease next on first, then make first a previous point on the

35
00:02:34,940 --> 00:02:38,390
first previous on B, then bring first on New Naldi.

36
00:02:40,060 --> 00:02:41,190
First on new.

37
00:02:43,060 --> 00:02:48,110
So these are the steps, three links are modified, one, two, three.

38
00:02:48,370 --> 00:02:53,310
These are the three links modified and I need one extra point a T for creating a new norm.

39
00:02:54,040 --> 00:02:58,700
So the number of steps involved here are one, two, three, four, five, six, six.

40
00:02:58,700 --> 00:02:59,440
The steps are there.

41
00:02:59,450 --> 00:03:04,240
So six is constant for the time taken for inserting a new notice constant.

42
00:03:05,340 --> 00:03:10,230
Now, let us look at another case, second case inserting at any given position.

43
00:03:11,430 --> 00:03:14,340
I want to insert a new note at position for that.

44
00:03:14,350 --> 00:03:15,300
Is that for.

45
00:03:15,780 --> 00:03:17,160
So if position is for.

46
00:03:19,080 --> 00:03:24,240
So here I want to insert a new node, so let us see what other steps required for doing this one.

47
00:03:27,310 --> 00:03:32,710
First of all, create a new mode with a temporary point of safety until the date I have.

48
00:03:34,330 --> 00:03:40,360
Now, this note should come in between fourth and fifth hour after 14 or so, what are the links I have

49
00:03:40,360 --> 00:03:41,080
to modify?

50
00:03:41,350 --> 00:03:46,660
I should make the city's next point on five, previous point on forward.

51
00:03:47,800 --> 00:03:55,810
Then for some extra point twenty five, the previous should also point A. So total one, two, three,

52
00:03:55,810 --> 00:04:00,490
four, four links I have to modify for four modifying these loans.

53
00:04:00,490 --> 00:04:04,180
I should have a pointer over this, nor do I need on both.

54
00:04:04,180 --> 00:04:06,760
And also just one load is sufficient, I think.

55
00:04:06,760 --> 00:04:08,890
One more on one note.

56
00:04:08,890 --> 00:04:09,990
Appointer is sufficient.

57
00:04:10,000 --> 00:04:14,970
So let us take one point and bring it on for ten or so directly.

58
00:04:15,010 --> 00:04:19,209
Cannot bring a pointer there, so I have to bring it from Fassnacht.

59
00:04:20,079 --> 00:04:23,910
So take a pointer B and move it so that it comes on fourth node.

60
00:04:24,070 --> 00:04:25,480
So how many times it should move.

61
00:04:25,750 --> 00:04:27,910
One, two, three.

62
00:04:28,120 --> 00:04:31,910
So it should move forward three times then it will be on fourteen or not.

63
00:04:31,930 --> 00:04:33,820
I have a pointer p up on fourth node.

64
00:04:34,060 --> 00:04:36,430
I will make modifications and links.

65
00:04:37,630 --> 00:04:38,620
Let us do it first.

66
00:04:38,620 --> 00:04:41,740
One is next is D next.

67
00:04:41,800 --> 00:04:43,240
This node address is present here.

68
00:04:43,540 --> 00:04:45,370
Besnik that is D next.

69
00:04:45,820 --> 00:04:47,410
This is the first one I'm modifying.

70
00:04:48,640 --> 00:04:55,570
I'll remove this inaudible positions for them, these are previous periods of this knowledge.

71
00:04:55,820 --> 00:04:57,670
So this is the second one I'm modifying.

72
00:04:59,700 --> 00:05:07,880
Then third one, this is north of previous, so I can read this note now by TS next as the last piece.

73
00:05:07,910 --> 00:05:10,080
Next I can reach here, so please.

74
00:05:10,100 --> 00:05:11,140
Next is this note.

75
00:05:11,450 --> 00:05:17,810
It's the previous this one should point on T so this is the third modification.

76
00:05:19,430 --> 00:05:22,700
The last piece makes should point on the.

77
00:05:23,660 --> 00:05:28,590
So this is forward to modification, so I have change for links.

78
00:05:28,860 --> 00:05:30,580
So that's all this is inserted.

79
00:05:31,010 --> 00:05:37,340
So from no to the seven, if I say next, I'll be going on 10, then next to that again from two if

80
00:05:37,340 --> 00:05:37,640
I can.

81
00:05:37,650 --> 00:05:40,540
Previous it's ten than previous it is seven.

82
00:05:40,940 --> 00:05:46,680
So I'm able to move in either direction and that node is inserted in between fourth and fifth point.

83
00:05:47,540 --> 00:05:49,850
Now let me write on the instructions for this one.

84
00:05:50,600 --> 00:05:57,750
So first of all, create a new order, then fill the data in the node details.

85
00:05:58,700 --> 00:06:01,780
Then I need a pointer B upon the support node.

86
00:06:02,060 --> 00:06:07,460
So as we are already familiar that I should take a pointer B from here and go on moving it for position

87
00:06:07,460 --> 00:06:13,070
minus one time so I can do it using four loops automatically override on the code for moving P so that

88
00:06:13,070 --> 00:06:15,050
it reaches for the node.

89
00:06:16,640 --> 00:06:19,990
So this followable move be so that it'll be just for naught?

90
00:06:20,390 --> 00:06:22,100
No, I have to modify Linc's.

91
00:06:23,170 --> 00:06:29,530
So first link, as I said, is next, should be BP's next, so they should point on this note and whose

92
00:06:29,530 --> 00:06:30,400
addresses permanent?

93
00:06:30,400 --> 00:06:31,150
BP's next.

94
00:06:31,160 --> 00:06:32,650
So it is modified already know.

95
00:06:32,950 --> 00:06:34,120
So it was present here.

96
00:06:34,480 --> 00:06:36,040
Salty's next is.

97
00:06:37,610 --> 00:06:39,620
These next then.

98
00:06:40,790 --> 00:06:49,310
Is the previous this should point on B, D previous should point on be the determining modified was

99
00:06:49,310 --> 00:06:49,840
this one.

100
00:06:50,000 --> 00:06:54,890
So this I have to handle it especially what special in that seat.

101
00:06:55,640 --> 00:07:04,080
This is D next or next previous so that there is an X not available or not.

102
00:07:04,760 --> 00:07:07,870
Five minutes after 5th node then there is no next morning.

103
00:07:08,660 --> 00:07:12,170
So if I say next rocket is null but I cannot say it's previous.

104
00:07:12,530 --> 00:07:17,830
So this third link I can perform only if there is an X nor the present.

105
00:07:18,140 --> 00:07:20,330
So here I should do it conditionally.

106
00:07:20,600 --> 00:07:26,150
I should check that if a PS next is available, this is sufficient.

107
00:07:26,450 --> 00:07:37,340
If PS next, it's not unknown zero, it's not zero then CEP's next previous as a team B's next previous

108
00:07:37,340 --> 00:07:39,240
as a B B's next.

109
00:07:39,380 --> 00:07:41,210
This one, this one means this node.

110
00:07:41,540 --> 00:07:42,460
It's the previous one.

111
00:07:42,500 --> 00:07:44,090
This one is the same with the T..

112
00:07:44,570 --> 00:07:46,190
So I have modified this third one.

113
00:07:46,580 --> 00:07:49,460
The fourth one because the next is eight.

114
00:07:51,920 --> 00:07:55,090
So next 60, so all these steps are what?

115
00:07:56,130 --> 00:07:59,680
So these are the steps for inserting a new node at a given place.

116
00:08:00,940 --> 00:08:06,190
No analysis, this was for insetting before first time was constant.

117
00:08:06,700 --> 00:08:07,560
What about this?

118
00:08:08,230 --> 00:08:10,450
The time depends upon movement of P.

119
00:08:11,320 --> 00:08:14,440
If P moves up to the last node, the time is maximum.

120
00:08:15,610 --> 00:08:21,410
If P remains on this one only means if you are inserting after first node, then time is constant.

121
00:08:21,790 --> 00:08:27,010
So this procedure, minimum time, this constant maximum time and.

122
00:08:28,100 --> 00:08:31,550
For minimum time is constant and maximum titmus and.

123
00:08:32,740 --> 00:08:40,070
Then about pointers, links, how many points are required in this case to point to three if you're

124
00:08:40,210 --> 00:08:43,130
sitting before first and just one extra point that is required.

125
00:08:43,630 --> 00:08:48,700
How many links are modified in setting before the first three links are modified?

126
00:08:49,240 --> 00:08:53,650
If the first node, if the linguist's is null, just stooling, some modified.

127
00:08:56,000 --> 00:09:01,790
And if you're sitting at any other place forming, so mortified, if you're sitting at the last, only

128
00:09:01,790 --> 00:09:03,980
three links will be modified, this one will not be done.

129
00:09:05,540 --> 00:09:08,090
So minimum Trilling's maximum full length.

130
00:09:08,120 --> 00:09:13,610
I said that this third link, we have to handle it especially so it may or may not be performed.

131
00:09:14,240 --> 00:09:17,030
Now, I will write a single function while writing a program.

132
00:09:17,030 --> 00:09:19,160
So I will write a program and show you the time.

133
00:09:19,160 --> 00:09:22,940
I will combine these two and make it as a single function.

134
00:09:23,810 --> 00:09:25,250
That's about insert.

