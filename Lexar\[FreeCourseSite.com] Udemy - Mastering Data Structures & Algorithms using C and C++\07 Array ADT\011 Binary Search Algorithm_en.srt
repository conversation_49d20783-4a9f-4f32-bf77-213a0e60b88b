1
00:00:00,330 --> 00:00:06,810
So I would like the algorithm for binary search, the syntax may be more like C C++ syntax, but I'm

2
00:00:06,810 --> 00:00:07,800
writing pseudocode.

3
00:00:09,470 --> 00:00:13,070
Algorithm, binary search.

4
00:00:14,270 --> 00:00:16,550
So what are the parameters it needs?

5
00:00:16,700 --> 00:00:20,750
It means three parameters low this one and high.

6
00:00:22,050 --> 00:00:26,940
And key that we are searching for, which we are searching for, this is key.

7
00:00:28,540 --> 00:00:36,730
So these were the parameters then what we were doing every time we were finding out many is low plus

8
00:00:36,730 --> 00:00:41,170
high divided by two and we have to take the Floran.

9
00:00:42,880 --> 00:00:50,370
Then we were checking whether the key is the present act of murder, if a Ghys equals to a murder.

10
00:00:52,000 --> 00:00:55,990
If you remember, we were getting the development, this one, so we were thinking that this is the

11
00:00:56,020 --> 00:01:01,690
key element that are searching for if so, suppose we are searching for 27, then it is found.

12
00:01:02,050 --> 00:01:02,770
It is found.

13
00:01:02,770 --> 00:01:07,300
So right on the index murder, that is this is the index that just found.

14
00:01:07,310 --> 00:01:07,960
So they've done it.

15
00:01:09,410 --> 00:01:16,370
No, if the key element is the smaller, then check on the left hand side, so else if.

16
00:01:17,480 --> 00:01:21,250
He is less than he of murder.

17
00:01:21,650 --> 00:01:28,820
So if it is on the left hand side, we are modifying high to murder minus one so high should be changed

18
00:01:28,820 --> 00:01:30,590
to murder minus one.

19
00:01:32,000 --> 00:01:37,130
If it is not equal, if it is not less, then it must be definitely greater.

20
00:01:37,160 --> 00:01:44,230
So we have to check on that side and modify law permit plus find some modified law permit plus funds

21
00:01:44,270 --> 00:01:49,310
or else we will modify law to murder plus one.

22
00:01:50,790 --> 00:01:58,740
This is what we were doing, so we have to go on doing this until the element is found or if the element

23
00:01:58,740 --> 00:02:03,520
is not there, we should stop, then the law becomes a greater than height.

24
00:02:03,780 --> 00:02:06,120
So this means we have to do it repeatedly.

25
00:02:06,360 --> 00:02:07,800
How long we should do this?

26
00:02:07,800 --> 00:02:13,170
While Lou is less than or equal to high, we should continue this.

27
00:02:13,560 --> 00:02:17,150
So we should stop and then law becomes greater than high.

28
00:02:17,820 --> 00:02:22,380
If law became greater than higher than we stop means the element is not found.

29
00:02:22,680 --> 00:02:28,030
So he can return minus one, saying that the key element is not found.

30
00:02:29,070 --> 00:02:34,190
So this is a iterative procedure that is using Leupp for binary search.

31
00:02:34,800 --> 00:02:40,920
I have just shown the algorithm or pseudocode, though it is more like C C++ code, but the perfect

32
00:02:40,920 --> 00:02:41,880
executable code.

33
00:02:42,030 --> 00:02:43,440
I will write on that.

34
00:02:43,440 --> 00:02:44,370
I show you the program.

35
00:02:45,060 --> 00:02:50,680
This is a binary search algorithm as written using loopier that is iterating version.

36
00:02:51,070 --> 00:02:53,430
We can also write recursive function.

37
00:02:53,700 --> 00:02:58,050
So let us write a record subversion for performing binary search.

38
00:02:59,790 --> 00:03:00,660
Algorithm.

39
00:03:04,490 --> 00:03:06,740
Are binary search.

40
00:03:09,130 --> 00:03:18,760
What are the parameters it needs same parameters, Louhi and the key, so low, high and key, these

41
00:03:18,760 --> 00:03:19,960
are the three parameters.

42
00:03:21,110 --> 00:03:27,860
Now, as it is recursive, so it will be calling itself so what is the first thing we should check whether

43
00:03:27,860 --> 00:03:29,420
there are some elements or not?

44
00:03:29,570 --> 00:03:33,470
So we should check that law should be less than or equal to high.

45
00:03:33,830 --> 00:03:36,890
If a law is greater than high, we cannot perform anything.

46
00:03:36,890 --> 00:03:38,210
So we will check first thing.

47
00:03:38,460 --> 00:03:43,940
That is, if law is less than or equal to high, then only we will consider.

48
00:03:44,210 --> 00:03:47,240
So if law is less than equal to higher, at least there is one element.

49
00:03:47,240 --> 00:03:49,730
If law is greater than the one zero element.

50
00:03:49,740 --> 00:03:58,400
So first condition we check, then after that we will find out and is low plus high divided by two.

51
00:03:58,640 --> 00:04:03,740
And that is fluid value and we check if is equals to.

52
00:04:06,140 --> 00:04:15,910
AOF murder, if so, then the element is found, so right on murder, there's almost a similar almost

53
00:04:15,910 --> 00:04:17,959
at a similar ends.

54
00:04:18,440 --> 00:04:24,820
If the element is on the left hand side, check if key is less than a of murder.

55
00:04:25,280 --> 00:04:29,970
If a key is less than optimal, then we have to search on the left hand side.

56
00:04:29,990 --> 00:04:30,700
So this.

57
00:04:32,980 --> 00:04:40,390
So this procedure can call itself recursively for searching on the left hand side, so say we, Don.

58
00:04:42,580 --> 00:04:48,970
Are binary search of bin search, so this is recursive call.

59
00:04:50,850 --> 00:04:52,330
So this is recursive call.

60
00:04:52,800 --> 00:05:00,270
So what it has to pass law as it is, but in place of high, it should be made minus one and the key

61
00:05:00,270 --> 00:05:01,300
as it is.

62
00:05:01,590 --> 00:05:05,770
So this is a recursive call with the modified list or reduced list.

63
00:05:05,790 --> 00:05:07,590
So it is just passing left hand side.

64
00:05:08,190 --> 00:05:12,450
So if Mitt is here from low to mid, minus one every passing.

65
00:05:13,170 --> 00:05:16,440
So if the key is less, it is on the left hand side.

66
00:05:16,800 --> 00:05:19,430
Otherwise keys greater than it is on the right hand side.

67
00:05:19,530 --> 00:05:20,610
So ends.

68
00:05:22,160 --> 00:05:22,940
Ripton.

69
00:05:24,980 --> 00:05:25,520
Ah.

70
00:05:26,550 --> 00:05:34,020
Been surge murder plus one and high as it is, and the key also.

71
00:05:35,590 --> 00:05:43,150
That's what if law is less than equal to how you do all these things, if law is not less than equal

72
00:05:43,150 --> 00:05:49,030
to high, then written minus one Minsky's not phone.

73
00:05:50,270 --> 00:05:56,520
So this is a recursive procedure for performing binary search upon an array, and I assume that there

74
00:05:56,540 --> 00:05:57,330
is global.

75
00:05:57,350 --> 00:05:59,240
So I'm not taking any parameters here.

76
00:05:59,240 --> 00:06:01,760
Otherwise, they should also be taken as parameter.

77
00:06:02,690 --> 00:06:09,440
If it is not, there means we should assume that it is global in algorithms, so these are the iterative

78
00:06:09,440 --> 00:06:12,300
procedure as well as a recursive procedure.

79
00:06:12,890 --> 00:06:18,410
Not if you observe that recursive algorithm that is still recursion.

80
00:06:19,430 --> 00:06:24,560
See, at returning time, there is nothing either but see either.

81
00:06:24,570 --> 00:06:29,640
It performed this because of all of this recursive call and after the recursive call.

82
00:06:29,660 --> 00:06:31,270
So there is nothing it has to perform.

83
00:06:31,550 --> 00:06:34,860
So it has to check for the element that it is checking beforehand only.

84
00:06:35,120 --> 00:06:38,960
So in this portion it is checking for the elements like it is checking for the elements.

85
00:06:38,960 --> 00:06:42,380
Then after that it is calling itself start returning time.

86
00:06:42,390 --> 00:06:43,700
It's not doing anything.

87
00:06:43,820 --> 00:06:44,900
It's not doing anything.

88
00:06:45,170 --> 00:06:51,220
So instead of writing a detailed recursion, you can also write a loop iterative version.

89
00:06:51,650 --> 00:06:55,730
So already we have discussed that regression and loops are similar.

90
00:06:55,760 --> 00:06:58,710
So if you have to write the location, better go for loop.

91
00:06:59,030 --> 00:07:04,910
So this procedure is better because this uses the stack internally to use a stack now.

92
00:07:05,130 --> 00:07:07,640
Next, we will do the analysis of binary search.

