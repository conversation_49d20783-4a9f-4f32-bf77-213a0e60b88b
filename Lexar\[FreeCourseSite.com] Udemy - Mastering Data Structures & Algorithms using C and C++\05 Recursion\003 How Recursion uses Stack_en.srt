1
00:00:00,330 --> 00:00:08,250
Now, let us look at how recursion uses <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, this first example, and explain to show you

2
00:00:08,250 --> 00:00:10,860
how the stack is utilized.

3
00:00:11,700 --> 00:00:12,300
Not already.

4
00:00:12,300 --> 00:00:17,730
We have seen this model of memory that the memory is used by dividing into three sections.

5
00:00:17,730 --> 00:00:22,240
One is called S. And here a short stack and this is heap.

6
00:00:22,920 --> 00:00:26,490
Now, in my program I have two functions that is fun one and mean.

7
00:00:26,760 --> 00:00:30,630
So the machine code of those two functions is already there in code section.

8
00:00:31,470 --> 00:00:35,720
Now let us run the program and see how the stock is created.

9
00:00:36,320 --> 00:00:39,430
See, the program starts executing some main functions.

10
00:00:39,430 --> 00:00:45,120
So the first function, this main function in that main function, this is the first statement that

11
00:00:45,120 --> 00:00:46,650
is X is created.

12
00:00:46,950 --> 00:00:54,990
So activation the code for main function is created and it will have its own variable that is X having

13
00:00:54,990 --> 00:00:55,650
value three.

14
00:00:58,080 --> 00:01:02,140
That the very next statement is fund one, a call to function one.

15
00:01:02,520 --> 00:01:08,520
So one of the functions, one is called it, is having just one variable that is n this is the only

16
00:01:08,520 --> 00:01:09,060
variable.

17
00:01:09,270 --> 00:01:17,400
So activation record for that function is created and it will have that variable N and this value X

18
00:01:17,430 --> 00:01:19,010
is passed on to that one.

19
00:01:19,470 --> 00:01:21,010
So it will be three.

20
00:01:22,440 --> 00:01:26,670
So this is four million and this is for fun one.

21
00:01:29,390 --> 00:01:32,220
I'm showing you line by line how it is working.

22
00:01:32,240 --> 00:01:37,010
Just let us look at it once more, see the program start executing from main function.

23
00:01:37,010 --> 00:01:40,080
The very first thing is X, so activation of the code for me.

24
00:01:40,100 --> 00:01:46,400
And then X has created the values and the function is scart and that function is having just one variable

25
00:01:46,400 --> 00:01:48,530
that is n so activation.

26
00:01:48,530 --> 00:01:54,660
The code is created for fun, one between N and having the military let us continue.

27
00:01:55,070 --> 00:01:56,470
If that is greater than zero.

28
00:01:56,690 --> 00:01:59,090
Yes, the threat is greater than zero.

29
00:02:00,470 --> 00:02:06,850
So as for the three, already we have seen that the 3D printed the next call function fun.

30
00:02:06,860 --> 00:02:12,980
One is called once again with the reduced value of and that is diclemente to value and minus one.

31
00:02:13,250 --> 00:02:14,910
So that function is called again.

32
00:02:14,930 --> 00:02:21,530
So once the function is called, we know that activation record will be created and in that again variable

33
00:02:21,530 --> 00:02:24,680
M and that will have the value to this time.

34
00:02:25,160 --> 00:02:32,340
This is also activation the code for fun one and the second call not in that second call.

35
00:02:32,360 --> 00:02:34,640
Again, it checks, if any, is better than zero.

36
00:02:34,850 --> 00:02:36,600
Yes, two is greater than zero.

37
00:02:36,890 --> 00:02:38,800
So pretty tough, so to speak.

38
00:02:38,810 --> 00:02:42,870
And the next line call again and the minus one.

39
00:02:42,890 --> 00:02:44,310
So right now and there's a two.

40
00:02:44,570 --> 00:02:45,980
So again, a new call.

41
00:02:46,160 --> 00:02:50,980
Again, an activation of the code is created and variable and it's created with value one.

42
00:02:51,320 --> 00:02:54,290
And this is again, a call for fun one.

43
00:02:56,580 --> 00:03:03,900
Now, again, if it's better than zero, yes, one is greater than zero, one one is Sprint.

44
00:03:03,900 --> 00:03:10,830
And then again call for one again, call one one with reduced value, decrement add value and minus

45
00:03:10,830 --> 00:03:11,010
one.

46
00:03:11,310 --> 00:03:13,590
So again, inactivation record for fun.

47
00:03:13,590 --> 00:03:18,070
One is created and it will have the variable end and that value zero.

48
00:03:18,420 --> 00:03:21,120
This is a call again for one.

49
00:03:22,640 --> 00:03:29,730
This time as a zero, when it calls again endoskeleton zero, so no, and it's not greater than zero,

50
00:03:29,930 --> 00:03:34,910
it will not enter inside, it will not print anything here, doesn't print anything.

51
00:03:35,180 --> 00:03:38,130
And it will come out of this conditional statement.

52
00:03:38,390 --> 00:03:40,920
So it doesn't enter into conditional statements.

53
00:03:40,920 --> 00:03:42,250
So it will come out of the function.

54
00:03:42,620 --> 00:03:50,930
And once that call ends, this activation record is deleted and the control goes back to the previous

55
00:03:50,930 --> 00:03:51,380
call.

56
00:03:51,770 --> 00:03:55,780
And in that previous call, what it has done, it has called this one.

57
00:03:56,060 --> 00:04:00,890
So the next is nothing so that Carl will be using this activation record.

58
00:04:01,890 --> 00:04:09,870
And that also ends so again, this is also deleted, it goes back to the previous caller and the previous

59
00:04:09,870 --> 00:04:12,420
call, this line is over, so there's nothing remaining.

60
00:04:12,750 --> 00:04:16,250
And that call uses this activation of the card, but there is nothing to do.

61
00:04:16,500 --> 00:04:19,230
So this also deleted and also deleted.

62
00:04:19,450 --> 00:04:23,580
Then it come back to me inside mean after one, there is nothing remaining.

63
00:04:23,590 --> 00:04:24,810
So this also ends.

64
00:04:25,150 --> 00:04:27,760
So all this activation and one by one.

65
00:04:28,770 --> 00:04:33,300
So this is how stock is created and utilized agriculture.

66
00:04:33,600 --> 00:04:35,270
Now, one more thing.

67
00:04:35,760 --> 00:04:42,480
What is the size of the stack leaving main function if you count the activation, the guards, one,

68
00:04:42,480 --> 00:04:43,610
two, three, four.

69
00:04:43,860 --> 00:04:49,520
So only for that function, for activation of the cards are created for site of the staggers forward

70
00:04:50,490 --> 00:04:52,140
and how much memory is consuming.

71
00:04:52,350 --> 00:04:55,140
Each activation of the card is having just one variable.

72
00:04:55,140 --> 00:04:57,330
And and and and so.

73
00:04:57,480 --> 00:04:58,620
And how many times.

74
00:04:58,620 --> 00:04:59,490
Four times.

75
00:05:00,090 --> 00:05:01,320
So you can see the calls here.

76
00:05:01,320 --> 00:05:02,870
One, two, three fours.

77
00:05:02,940 --> 00:05:07,140
Already we have done one, two, three, four, four.

78
00:05:07,140 --> 00:05:11,560
So already we have done tracing, we have generated a tracing through your recollection.

79
00:05:12,120 --> 00:05:14,580
So there are four calls for activation.

80
00:05:14,580 --> 00:05:15,600
The cards are created.

81
00:05:16,530 --> 00:05:22,170
And at the end of this, it will go back and go back, so activation records are deleted again, so

82
00:05:22,170 --> 00:05:24,920
sight of the memory consumed by this function.

83
00:05:24,930 --> 00:05:33,330
As for India, whatever the size of variable integer is for the value of X that is known as a three

84
00:05:33,330 --> 00:05:33,900
for value.

85
00:05:33,900 --> 00:05:38,700
Of the three, there are four calls for the value of an equal to five.

86
00:05:38,710 --> 00:05:46,260
There will be six calls so far n there will be N plus one Clontz so turtling plus one call.

87
00:05:46,290 --> 00:05:50,300
So how many activation cards and plus one activation records.

88
00:05:50,580 --> 00:05:54,930
So you can see that the activation of the card number of activation of the cards depends on the number

89
00:05:54,930 --> 00:05:55,710
of calls.

90
00:05:56,070 --> 00:05:59,000
So total calls are and plus one.

91
00:06:00,390 --> 00:06:02,220
And what is an actual variable.

92
00:06:02,220 --> 00:06:04,350
How much memory is consuming now.

93
00:06:04,350 --> 00:06:08,810
It is integer type taking to buy it for, for by depending on the compiler.

94
00:06:09,690 --> 00:06:12,540
So we can say just an endless phone call.

95
00:06:12,560 --> 00:06:20,950
So the total memory consumed by this is order off and we can mention it as outdraw and.

96
00:06:22,740 --> 00:06:24,110
Why not plus, one dead?

97
00:06:24,540 --> 00:06:34,200
See, this is a polynomial, a formula, and it one in that degree of the formula is one higher Satomi's

98
00:06:34,380 --> 00:06:39,550
and so we take and so we degree the order of our degree of.

99
00:06:39,570 --> 00:06:45,950
And so we mentioned the space house order of M, but actually if you see this and plus one activity,

100
00:06:46,040 --> 00:06:47,190
the records are created.

101
00:06:47,910 --> 00:06:53,890
So from this discussion we can understand that recursive functions utilize this stack.

102
00:06:54,210 --> 00:06:59,110
So here internally it takes some extra memory for the stack.

103
00:06:59,460 --> 00:07:05,040
So there are memory consuming functions, recursive functions are memory consuming.

104
00:07:05,190 --> 00:07:06,600
So that's all I have explained.

105
00:07:06,600 --> 00:07:07,880
This one not already.

106
00:07:07,890 --> 00:07:14,030
I have a stack then just I will show you how this function looks, how dysfunctional look.

107
00:07:14,050 --> 00:07:17,840
So I will use this program as well as a tree.

108
00:07:18,170 --> 00:07:21,610
Then also I will show inside the stack what happens.

109
00:07:21,660 --> 00:07:22,890
Let us look at it quickly.

110
00:07:23,340 --> 00:07:24,350
There are two functions.

111
00:07:24,370 --> 00:07:28,700
One, two, and mean both these functions are there inside main memory.

112
00:07:29,470 --> 00:07:31,460
Now let's start executing program.

113
00:07:31,480 --> 00:07:36,810
Start from the main functions for the first variable X, so activation the code for me was created and

114
00:07:36,810 --> 00:07:40,770
having the value tree in X and it will call function Fundo.

115
00:07:41,100 --> 00:07:43,740
One of the functions on two is called activation.

116
00:07:43,740 --> 00:07:45,350
The code for two is created.

117
00:07:45,360 --> 00:07:47,720
So it means all these are four two now.

118
00:07:48,030 --> 00:07:49,200
So I just change them.

119
00:07:50,370 --> 00:07:56,790
So the first call, an equal to three, this is created, that is this one, this is created now and

120
00:07:56,790 --> 00:07:57,730
is greater than zero.

121
00:07:57,870 --> 00:07:58,530
Yes.

122
00:07:59,170 --> 00:08:00,410
Then call again.

123
00:08:00,420 --> 00:08:02,760
So again, it will call with and minus one.

124
00:08:03,030 --> 00:08:08,050
So a new call will create this activation record with an equal to two.

125
00:08:08,310 --> 00:08:09,510
Then again, it will call.

126
00:08:09,520 --> 00:08:14,640
Then again, it will call C, because the first statement is called since calling itself every time

127
00:08:15,030 --> 00:08:19,070
when N is equal to zero zero is not greater than zero.

128
00:08:19,260 --> 00:08:22,260
So it will not end if block can come out of the function.

129
00:08:22,500 --> 00:08:28,110
Once it come out of the function, this activation record is deleted because the function with the value

130
00:08:28,130 --> 00:08:30,380
of an R zero has terminated.

131
00:08:30,900 --> 00:08:33,960
It goes back to the previous call in the same function.

132
00:08:33,960 --> 00:08:37,700
It will go back to the previous call where it has finished to this one.

133
00:08:38,130 --> 00:08:39,750
Now it has to perform this.

134
00:08:40,210 --> 00:08:42,080
That is this line in the entry.

135
00:08:42,480 --> 00:08:48,030
So what is the value of and it has to break the current topmost activation of the code is this one where

136
00:08:48,040 --> 00:08:49,350
the value of end is one.

137
00:08:49,590 --> 00:08:50,910
So it will print one.

138
00:08:51,540 --> 00:08:57,660
It will print one, because the value of this one here and once it has finished, it will come out and

139
00:08:57,660 --> 00:08:59,430
this activation record is deleted.

140
00:08:59,670 --> 00:09:04,770
It will go back to the previous column in the previous call again and has finished the first line.

141
00:09:05,040 --> 00:09:05,880
That is this line.

142
00:09:05,880 --> 00:09:09,650
Now it has to do this so print and so what is the value of.

143
00:09:09,660 --> 00:09:14,460
And now so the topmost activation occurred in the stack you can see now and value is two.

144
00:09:14,700 --> 00:09:16,840
So this is printing, so to speak.

145
00:09:17,580 --> 00:09:20,910
Likewise 3D printer and all this activation the code.

146
00:09:20,920 --> 00:09:27,480
And so you can see that these activation records are created when the function was calling itself again

147
00:09:27,480 --> 00:09:27,990
and again.

148
00:09:28,170 --> 00:09:29,790
So this is more like ascending.

149
00:09:30,720 --> 00:09:34,790
Now, once it has reached the last call, that is where is the Theodore?

150
00:09:35,100 --> 00:09:37,560
So it will start returning.

151
00:09:37,830 --> 00:09:39,000
So that is a descending.

152
00:09:39,270 --> 00:09:41,780
So why are they sending it valued like these values?

153
00:09:42,450 --> 00:09:48,420
So all the values of and are there in the stack and the different activation records.

154
00:09:49,290 --> 00:09:52,760
So these values are utilized at returning time.

155
00:09:53,550 --> 00:09:54,450
So that's all.

156
00:09:54,720 --> 00:09:57,790
This is how recursion uses stack.

157
00:09:58,200 --> 00:10:01,920
So both of these functions are almost the same.

158
00:10:01,920 --> 00:10:07,410
Only the difference is that this is first printing and then calling, this is first calling, then printing.

159
00:10:07,560 --> 00:10:11,490
So the activation that God created for these functions will be same only.

160
00:10:11,490 --> 00:10:13,980
So that is the reason to have taken two similar examples.

161
00:10:14,250 --> 00:10:17,220
And I have shown you how the stock is utilized.

162
00:10:17,520 --> 00:10:19,440
It will be the same in both the programs.

163
00:10:19,770 --> 00:10:25,770
The size of the stack and the space consumed by this function is also author of and that is total and

164
00:10:25,770 --> 00:10:26,700
plus one activation.

165
00:10:26,700 --> 00:10:31,680
The cards are created for the function Fundo leaving the site.

166
00:10:31,680 --> 00:10:34,500
I mean, this is all recursion uses a stack.

167
00:10:34,800 --> 00:10:40,890
Now, next thing that we have to study is how to find the time complexity of recursive functions.

168
00:10:41,310 --> 00:10:47,340
So I will take just one example, this example, and show you how to find the time complexity of recursive

169
00:10:47,340 --> 00:10:47,730
function.

