1
00:00:01,030 --> 00:00:07,880
Here I have taken some keys, let us create a right to black tree by inserting each key one by one.

2
00:00:08,170 --> 00:00:13,500
So let us start the first that I have and I have to insert 10.

3
00:00:13,690 --> 00:00:20,480
So remember, the insertion will be done just like a binary search tree, just like by necessity.

4
00:00:20,860 --> 00:00:22,730
So first key is then.

5
00:00:23,020 --> 00:00:25,060
So all of this are newly inserted.

6
00:00:25,060 --> 00:00:26,840
Node will be had a..

7
00:00:27,610 --> 00:00:30,790
So this is inserting 10 insert.

8
00:00:32,380 --> 00:00:37,990
I haven't forgotten, but this is the first node and it is the root node.

9
00:00:38,110 --> 00:00:40,820
So you remember root node must be blackmailed.

10
00:00:40,840 --> 00:00:46,060
So I should change this color and make it as black, not file change it to black.

11
00:00:47,100 --> 00:00:55,980
So the system that is inserted now makes key, grindy, so here I will continue insert grindy.

12
00:00:56,420 --> 00:01:01,310
So this is all the 10 Silvertone they should come into is greater than 10.

13
00:01:01,320 --> 00:01:02,930
So it should come on the right hand side.

14
00:01:03,180 --> 00:01:05,069
So prentis this one.

15
00:01:07,000 --> 00:01:13,210
Now, when you are inserting a new order, it should be Noor and its parent is black, then no problem,

16
00:01:13,420 --> 00:01:17,440
no problem, no problem with the balancing, the height, you don't have to do anything.

17
00:01:17,710 --> 00:01:18,590
It's perfect.

18
00:01:19,300 --> 00:01:24,550
Now, Netsky is 30 30, so we today should come here.

19
00:01:24,550 --> 00:01:32,690
I will say insert Turkey insert in Turkey right now this is 10 and it's a child is 20.

20
00:01:33,400 --> 00:01:35,110
This is going deep, which is right now.

21
00:01:35,440 --> 00:01:36,910
And Turkey when it should come.

22
00:01:37,300 --> 00:01:39,790
Turkey is greater than 10 and greater than 20.

23
00:01:39,790 --> 00:01:42,910
So 30 should come as the right child of grindy.

24
00:01:43,150 --> 00:01:44,430
And it's a newborn.

25
00:01:44,440 --> 00:01:47,310
So remember, all you, the new know, should be pregnant.

26
00:01:47,490 --> 00:01:47,920
Yes.

27
00:01:47,920 --> 00:01:53,080
I haven't I don't know if you see there is a red over the conflict.

28
00:01:53,080 --> 00:02:00,730
So whenever there is a red conflict, then you have to do some adjustment for making it as a balance.

29
00:02:00,730 --> 00:02:01,690
Right, Blakley?

30
00:02:02,260 --> 00:02:05,050
So there are two approach for adjustment.

31
00:02:05,250 --> 00:02:08,169
Remember this one first one is recovering.

32
00:02:09,220 --> 00:02:11,020
Second one is rotation.

33
00:02:11,260 --> 00:02:16,990
Now, I will take some sample example here and I'll show you what does it mean by coloring and what

34
00:02:16,990 --> 00:02:18,760
does it mean by rotation.

35
00:02:19,090 --> 00:02:20,710
Then we will continue with this one.

36
00:02:20,920 --> 00:02:26,400
So now we will on those two separate coloring and rotation here.

37
00:02:26,410 --> 00:02:34,690
I have examples since this is a newly installed norm and it's a parent, Israela the SP's for parent

38
00:02:34,690 --> 00:02:36,160
and it's for newly installed norm.

39
00:02:36,610 --> 00:02:38,470
And this is a grandparent.

40
00:02:38,680 --> 00:02:44,230
So I Vergès here, this is a grandparent and this is uncle for the Snoad.

41
00:02:44,740 --> 00:02:48,690
So it means the sibling of a parent or sibling of parent.

42
00:02:48,760 --> 00:02:49,420
It's uncle.

43
00:02:49,420 --> 00:02:49,750
Right.

44
00:02:49,760 --> 00:02:51,510
So we use uncle here.

45
00:02:51,940 --> 00:02:57,890
So far, a newly inserted nor its parent is Red Annetts uncle is also red.

46
00:02:58,360 --> 00:02:59,680
Then what to do.

47
00:03:00,030 --> 00:03:02,400
Recolour, recolour the norm.

48
00:03:02,650 --> 00:03:05,470
So hope recolour change these to black.

49
00:03:05,620 --> 00:03:09,490
So make this as black and this will remain red.

50
00:03:09,490 --> 00:03:13,780
Only new normal remain red only and this will become black.

51
00:03:13,900 --> 00:03:17,740
And the parent we should make it as red parent should be red.

52
00:03:19,180 --> 00:03:22,270
This is what you have to change the colors.

53
00:03:22,510 --> 00:03:30,040
So this is called Recology and after that it's supposed to be a root naude then you have to make it

54
00:03:30,040 --> 00:03:30,430
black.

55
00:03:30,790 --> 00:03:32,020
So that will also become black.

56
00:03:32,020 --> 00:03:38,170
If it is not root, not then it will recursively, it will check checkerboards that is towards the parents

57
00:03:38,170 --> 00:03:38,740
of B.

58
00:03:39,010 --> 00:03:40,870
So this is the procedure of recovery.

59
00:03:40,990 --> 00:03:45,790
Just I have shown you recolour that you remember how to do while creating this one.

60
00:03:45,790 --> 00:03:48,370
I will explain you so don't have to worry about that.

61
00:03:48,520 --> 00:03:52,060
If you simply try to understand this is confusing, let us use it.

62
00:03:52,060 --> 00:03:53,410
It becomes easy and simple.

63
00:03:53,770 --> 00:03:56,360
Now next thing is suppose newlines.

64
00:03:56,380 --> 00:03:57,340
I did notice this one.

65
00:03:57,340 --> 00:03:57,630
Right.

66
00:03:57,850 --> 00:04:01,120
And this parent is also then only you have to do something right.

67
00:04:01,120 --> 00:04:02,920
If it is black then no need to do anything.

68
00:04:02,920 --> 00:04:04,930
If it is red, then check uncle.

69
00:04:05,560 --> 00:04:07,900
I said nulls are also black.

70
00:04:07,900 --> 00:04:09,210
I'm not showing Eternals.

71
00:04:09,310 --> 00:04:12,670
The diagram looks consistent, so I'm showing only DeSanto.

72
00:04:13,180 --> 00:04:13,870
This is not.

73
00:04:14,050 --> 00:04:15,070
This is also black.

74
00:04:15,220 --> 00:04:18,430
So parent is red but uncle is black.

75
00:04:18,430 --> 00:04:19,930
Then you have to perform rotation.

76
00:04:21,850 --> 00:04:29,770
If a parent is red and uncle is also Ricola, parents, red uncles, black performed rotation, then

77
00:04:29,770 --> 00:04:38,830
if you remember from Aviad, the rotations we saw there order Alah R-AL for types of auditions before

78
00:04:38,830 --> 00:04:40,630
there is a similar one.

79
00:04:40,660 --> 00:04:41,880
We will change the names here.

80
00:04:42,160 --> 00:04:46,130
So this is are so will perform the audition along this one.

81
00:04:46,420 --> 00:04:49,600
So instead of are are left, left or right.

82
00:04:49,600 --> 00:04:49,870
Right.

83
00:04:50,110 --> 00:04:53,410
We have a single name that we can Zizic rotation.

84
00:04:54,160 --> 00:04:55,720
This is out of rotation.

85
00:04:56,020 --> 00:05:02,890
So this Babycham B becomes apparent and it becomes lt C becomes ritcher and this route remains black

86
00:05:02,890 --> 00:05:04,400
and these children will become right.

87
00:05:04,510 --> 00:05:06,100
So you have to give the color likeness.

88
00:05:07,270 --> 00:05:14,770
And otherwise if this is done, you know, this is parent that is right and left, child that is uncle

89
00:05:14,770 --> 00:05:15,730
and it is black.

90
00:05:15,760 --> 00:05:17,710
So parents red uncle is black.

91
00:05:18,010 --> 00:05:21,250
Then we have to perform this are L rotation.

92
00:05:21,250 --> 00:05:23,950
So this is called as zacher rotation.

93
00:05:24,040 --> 00:05:34,690
So Zig-Zag rotation means Aaryn as well as Ella and the Zig-Zag notation means and as well as R so we

94
00:05:34,690 --> 00:05:35,380
have this name.

95
00:05:36,010 --> 00:05:37,810
So this is combining both the rotations.

96
00:05:38,260 --> 00:05:40,360
So only two things you have to pick from here.

97
00:05:40,390 --> 00:05:42,430
What is that newly inserted.

98
00:05:42,430 --> 00:05:45,610
Known if its parent is right then check uncle.

99
00:05:46,030 --> 00:05:48,820
Uncle may be red, uncle may be black.

100
00:05:49,090 --> 00:05:56,490
If uncle is red recolour if uncle is black then perform a rotation that for this.

101
00:05:56,500 --> 00:05:58,870
But this is the point that we have to pick up from here.

102
00:05:59,200 --> 00:05:59,590
Right.

103
00:05:59,590 --> 00:06:02,350
We don't have to go into details, just ignore them.

104
00:06:02,740 --> 00:06:05,170
When we are creating it we can understand everything.

105
00:06:05,500 --> 00:06:07,090
So let us continue.

106
00:06:07,750 --> 00:06:09,850
So here we have conflict then.

107
00:06:09,850 --> 00:06:15,310
What is uncle here uncle is this Norm and this is black, which is none.

108
00:06:15,670 --> 00:06:21,250
So it means parent is red and uncle is black.

109
00:06:21,250 --> 00:06:23,110
Does the neumont right.

110
00:06:24,250 --> 00:06:26,020
So you have to perform rotation.

111
00:06:26,320 --> 00:06:28,660
So let us perform rotation over this.

112
00:06:28,660 --> 00:06:32,070
Grand parents or traditions are performed over grandparents.

113
00:06:32,410 --> 00:06:34,600
So this is just our rotation.

114
00:06:34,600 --> 00:06:40,030
We know we have done this in Aviel tree, so let us perform audition and I will ride on the tree here

115
00:06:40,300 --> 00:06:45,430
so the string will come here that will become black and its left shine.

116
00:06:45,580 --> 00:06:48,580
That is ten and the right child will be thirty.

117
00:06:48,970 --> 00:06:52,930
So this is ten and this is, this is after rotation.

118
00:06:52,960 --> 00:06:59,020
Watch closely right if you are and then we can follow it and just pause and watch it again and you do

119
00:06:59,020 --> 00:07:03,540
it on but paper, just take a pen and paper and practice it also by yourself.

120
00:07:03,550 --> 00:07:05,610
OK, now let us continue.

121
00:07:05,620 --> 00:07:07,030
We have finished in here.

122
00:07:07,480 --> 00:07:09,070
Now let us insert 50.

123
00:07:09,070 --> 00:07:10,750
So here I am right on the top.

124
00:07:11,020 --> 00:07:14,090
Insert fifty there.

125
00:07:14,140 --> 00:07:17,470
Fifty should be start from route search for fifty.

126
00:07:17,680 --> 00:07:20,110
Fifty is greater than twenty and greater than thirty.

127
00:07:20,110 --> 00:07:21,340
So it should come on the right side.

128
00:07:21,340 --> 00:07:21,940
It's not there.

129
00:07:22,090 --> 00:07:27,460
Insert so newly inserted node fifty back will be off color.

130
00:07:28,000 --> 00:07:29,620
This is the new norm now.

131
00:07:29,650 --> 00:07:30,580
This is the new node.

132
00:07:30,580 --> 00:07:34,290
This is parent and this is grandparent and this is uncle.

133
00:07:34,810 --> 00:07:42,490
Now there is a great conflict as I said, if there is a conflict then only you have to perform a or

134
00:07:42,580 --> 00:07:43,180
rotation.

135
00:07:43,180 --> 00:07:47,610
So that conflict of the parent is an uncle is also there.

136
00:07:47,800 --> 00:07:52,540
So if parent and uncle is also Redmon's, change the color, so let us change the color.

137
00:07:52,540 --> 00:08:01,690
So in this tree only I will modify, change the color, make this as black and this also as black and

138
00:08:01,720 --> 00:08:03,270
this as red.

139
00:08:03,520 --> 00:08:07,630
So barington glass black and grandparent.

140
00:08:07,630 --> 00:08:08,680
That's right.

141
00:08:09,280 --> 00:08:15,610
But this grandparent is a root of a tree and the root must be black.

142
00:08:15,790 --> 00:08:17,950
So again, we have to modify this one.

143
00:08:18,190 --> 00:08:20,050
So special you should check this.

144
00:08:20,050 --> 00:08:21,100
That root is black.

145
00:08:21,220 --> 00:08:23,470
Not if it is root then keep it black.

146
00:08:23,770 --> 00:08:26,070
So this is after inserting fifty.

147
00:08:26,080 --> 00:08:26,830
This is the tree.

148
00:08:27,550 --> 00:08:31,290
So once we saw rotation second we saw recovery.

149
00:08:31,510 --> 00:08:31,990
That's it.

150
00:08:31,990 --> 00:08:33,220
We have to continue like this.

151
00:08:33,220 --> 00:08:38,620
Wante let us insert next key that is forty insert forty.

152
00:08:40,510 --> 00:08:47,500
And this one here I have taken the same tree now insert forty four days of twenty four is greater than

153
00:08:47,500 --> 00:08:47,950
thirty.

154
00:08:48,220 --> 00:08:51,160
Forty is less than fifty so it should come on the side.

155
00:08:51,340 --> 00:08:53,290
So here I should insert forty.

156
00:08:53,650 --> 00:08:54,430
This is right.

157
00:08:54,430 --> 00:08:55,870
Not every new Lauder's.

158
00:08:55,870 --> 00:09:03,560
I know this is new node and this is a parent and this is a grandparent and this is uncle uncle.

159
00:09:03,580 --> 00:09:04,150
It's black.

160
00:09:05,140 --> 00:09:10,630
But if you observe parent s right and uncle is black.

161
00:09:10,630 --> 00:09:16,090
So if a parent is right and uncle is black, that is uncle is black, we have to perform the rotation.

162
00:09:16,420 --> 00:09:18,450
So we should we should perform on this one.

163
00:09:18,460 --> 00:09:20,830
This is right and left to build on the grandparent.

164
00:09:21,100 --> 00:09:21,280
So.

165
00:09:21,370 --> 00:09:27,400
Using grandparental, we have to perform vacation, so it is double the rotation rate, if you remember

166
00:09:27,440 --> 00:09:33,780
the validation that is this exact rotation we have to perform after rotation, I will write on the tree

167
00:09:33,850 --> 00:09:34,630
directly here.

168
00:09:34,950 --> 00:09:39,280
Bindi's here and then F here upon this rotation is performed.

169
00:09:39,580 --> 00:09:44,750
So if you perform rotation like this, then 40 will come here directly from each year.

170
00:09:44,970 --> 00:09:46,750
If you remember that, you do that one.

171
00:09:46,750 --> 00:09:46,930
Right.

172
00:09:47,170 --> 00:09:50,680
So don't take two steps that really do it for will come at this place.

173
00:09:50,950 --> 00:09:54,910
So 40 is the right side is 50 and the left side is 30.

174
00:09:55,150 --> 00:09:57,910
So this becomes 30 and this becomes 50.

175
00:09:58,090 --> 00:09:59,920
And grandparents remain, Fred.

176
00:10:00,100 --> 00:10:02,290
And this will become right.

177
00:10:02,890 --> 00:10:06,020
So this is after performing rotation on this one.

178
00:10:06,040 --> 00:10:11,250
So this is the final three we got after rotation now before continuing insertion of remaining keys.

179
00:10:11,420 --> 00:10:13,980
Let me show you the height of black.

180
00:10:14,440 --> 00:10:16,300
How many blacks in this spot?

181
00:10:16,310 --> 00:10:19,000
One, two, and also three.

182
00:10:19,330 --> 00:10:20,110
One, two.

183
00:10:20,110 --> 00:10:21,190
And one is also there.

184
00:10:21,220 --> 00:10:22,110
So I will drive back.

185
00:10:22,800 --> 00:10:23,890
OK, these are black.

186
00:10:24,160 --> 00:10:25,790
So one, two, three, one, two, three.

187
00:10:25,960 --> 00:10:31,240
And in this direction, if you go one, two and this one more black three, one, two and one more black

188
00:10:31,240 --> 00:10:31,540
three.

189
00:10:31,780 --> 00:10:33,640
One, two, black three.

190
00:10:33,640 --> 00:10:34,210
One, two.

191
00:10:34,240 --> 00:10:35,950
This is black three, four.

192
00:10:35,950 --> 00:10:39,430
In every direction you go it three only the same only.

193
00:10:39,700 --> 00:10:43,960
So you don't have to take special care of me and any number of blacks.

194
00:10:43,960 --> 00:10:46,030
You don't have to follow this one.

195
00:10:46,450 --> 00:10:47,170
Follow this one.

196
00:10:47,630 --> 00:10:47,790
Right.

197
00:10:48,000 --> 00:10:54,240
If there is color conflict, either regular or perform rotation that fought so you'll get a red black

198
00:10:54,490 --> 00:10:54,920
five million.

199
00:10:54,950 --> 00:10:56,200
So remaining keys for that.

200
00:10:56,200 --> 00:10:59,710
I will remove the next ski's 16.

201
00:11:00,130 --> 00:11:01,660
So insert sixty.

202
00:11:01,900 --> 00:11:03,840
So this is the final three that I have.

203
00:11:03,850 --> 00:11:05,800
So in this only I will insert sixty.

204
00:11:06,190 --> 00:11:09,550
Sixty is a greater than 30, greater than 40 and greater than 50.

205
00:11:09,910 --> 00:11:11,580
So 60 should come here.

206
00:11:11,860 --> 00:11:15,540
So as a child of 50, this is 60.

207
00:11:16,540 --> 00:11:17,920
Now, this is a newly insert.

208
00:11:17,920 --> 00:11:21,480
Ignore the parent and the uncle and the grandparent.

209
00:11:22,540 --> 00:11:28,910
Now, check this newlines or not, and this is also read right by conflict and check.

210
00:11:28,930 --> 00:11:30,800
Uncle Uncle is also worried.

211
00:11:31,360 --> 00:11:34,090
If parent and uncle both are, then what to do.

212
00:11:34,360 --> 00:11:42,160
Recolour Hobe Ricola, modifieds appearance as a parent as well as Inglês Black.

213
00:11:42,310 --> 00:11:46,060
So these are black then grandparents as right now.

214
00:11:46,060 --> 00:11:47,710
See this interesting from this point.

215
00:11:47,720 --> 00:11:48,990
You see this is very important.

216
00:11:49,720 --> 00:11:53,560
No, again, for grandparent checking their ancestors.

217
00:11:53,920 --> 00:11:54,620
Check this one.

218
00:11:55,180 --> 00:11:57,580
This is a parent that is black.

219
00:11:57,630 --> 00:12:00,130
This is a red flag, red and black.

220
00:12:00,250 --> 00:12:01,360
It is not a conflict.

221
00:12:01,480 --> 00:12:06,770
If no conflict and stop if suppose this was all sorted, then you have to checkerboards.

222
00:12:06,790 --> 00:12:08,710
So we have a story up to here only.

223
00:12:08,950 --> 00:12:10,800
So this is red and black.

224
00:12:10,810 --> 00:12:13,990
So if you have red under this black, then no problem.

225
00:12:14,110 --> 00:12:16,370
If this red red then you have to continue.

226
00:12:16,690 --> 00:12:22,740
So the thing that we learned one more thing we learned here is that after recovering, a grandparent

227
00:12:22,750 --> 00:12:23,360
becomes red.

228
00:12:23,620 --> 00:12:26,490
Then again, you have to check in ancestors.

229
00:12:26,800 --> 00:12:28,100
So there is black.

230
00:12:28,120 --> 00:12:28,930
So no problem.

231
00:12:29,050 --> 00:12:29,650
Stop it.

232
00:12:29,970 --> 00:12:31,500
I will take the same tree here.

233
00:12:31,510 --> 00:12:32,890
I will drink up to 60.

234
00:12:32,900 --> 00:12:33,550
We have finished.

235
00:12:33,820 --> 00:12:35,400
Then I have to insert 70.

236
00:12:35,560 --> 00:12:37,770
So first of all, I will draw the same thing there.

237
00:12:37,780 --> 00:12:39,120
Then I will insert 17.

238
00:12:39,430 --> 00:12:41,080
So does the same tree I have taken.

239
00:12:41,530 --> 00:12:45,800
Black, black, red, black, black, red, black.

240
00:12:45,820 --> 00:12:47,440
A black, red, black, black, red.

241
00:12:47,620 --> 00:12:48,310
So same thing.

242
00:12:48,610 --> 00:12:50,130
Not insert 70.

243
00:12:50,140 --> 00:12:54,360
Inserting 70, 70 is greater and greater and greater and greater.

244
00:12:54,580 --> 00:12:59,020
So 70 comes here as a right chain, so 70 will be here.

245
00:13:00,760 --> 00:13:06,070
See, I'm trying to insert always on the right hand side so we can come across all the possibilities

246
00:13:06,070 --> 00:13:07,300
here if you go on the left side.

247
00:13:07,300 --> 00:13:08,790
Also the same thing.

248
00:13:09,100 --> 00:13:10,940
So we are learning every possibility here.

249
00:13:11,240 --> 00:13:17,140
OK, so now if you observe there is a red red conflict, red red conflict, this is a new node.

250
00:13:17,290 --> 00:13:22,050
This is parent and this is grandparent and uncle is no right.

251
00:13:22,120 --> 00:13:24,190
I'm not showing the all these are not great.

252
00:13:24,190 --> 00:13:27,370
These are also they are not what we are focusing on this one.

253
00:13:27,370 --> 00:13:29,140
So that's why I'm doing only that one.

254
00:13:30,100 --> 00:13:30,970
This is uncle.

255
00:13:31,090 --> 00:13:31,990
Uncle is black.

256
00:13:32,290 --> 00:13:38,470
So this parent, this red and uncle's black, perform rotation around the grandparent.

257
00:13:38,740 --> 00:13:42,760
So perform rotation around this one for after rotation.

258
00:13:43,000 --> 00:13:46,280
It looks like this is twenty which remains same.

259
00:13:46,300 --> 00:13:50,670
The system remains the same and this is a forty, OK.

260
00:13:50,920 --> 00:13:53,700
And this is a thirty which remains the same.

261
00:13:54,280 --> 00:13:56,110
We have to perform an additional 150.

262
00:13:56,380 --> 00:14:02,680
So out of fifty comes what, 60 and left child is 50.

263
00:14:02,980 --> 00:14:05,530
Right child is 70.

264
00:14:06,940 --> 00:14:07,470
This one.

265
00:14:07,810 --> 00:14:10,390
So grandparent was black.

266
00:14:10,390 --> 00:14:11,710
So it remains black only.

267
00:14:11,950 --> 00:14:14,800
And these are children when we perform rotation.

268
00:14:14,800 --> 00:14:19,900
So the sixty will become black and these children will become right now let us confirm whether it is

269
00:14:19,900 --> 00:14:20,760
balanced or not.

270
00:14:20,980 --> 00:14:24,280
So for that, let us count the number of blacks on the part.

271
00:14:25,450 --> 00:14:27,580
One, two, three.

272
00:14:27,940 --> 00:14:29,800
One, two, three.

273
00:14:30,190 --> 00:14:31,930
One, two, three.

274
00:14:32,080 --> 00:14:33,880
One, two, three.

275
00:14:34,030 --> 00:14:35,500
One, two, three.

276
00:14:35,500 --> 00:14:36,490
One, two, three.

277
00:14:36,760 --> 00:14:42,190
All the parts I have check there are all three black notes, like in this example three.

278
00:14:43,980 --> 00:14:51,660
So it is balance number of blocks along the path are seen, so the balance now till 70 have finished,

279
00:14:51,990 --> 00:14:56,510
not have to insert 80, so I have to remove everything from the board.

280
00:14:56,520 --> 00:15:00,410
So if you want to take a snapshot or copy this one, you can copy it.

281
00:15:00,600 --> 00:15:04,650
I will take this tree once again here and I will continue the rest of the keys.

282
00:15:05,250 --> 00:15:09,060
So I have taken that tree here now will insert it.

283
00:15:09,360 --> 00:15:10,380
This is interesting.

284
00:15:10,390 --> 00:15:12,370
We are going to learn one new thing again here.

285
00:15:12,480 --> 00:15:16,050
Let us see this insert Eddie where it should come.

286
00:15:16,050 --> 00:15:20,700
It is greater than greater than 40, greater than 60, greater than 70.

287
00:15:20,910 --> 00:15:24,730
So it should come here as a right shade of 70.

288
00:15:25,290 --> 00:15:29,030
This is right now a very direct conflict.

289
00:15:29,340 --> 00:15:34,110
This isn't you know, this is Birand and this is Uncle Understrength.

290
00:15:34,110 --> 00:15:36,330
Bitter conflict is there.

291
00:15:36,450 --> 00:15:43,740
So check Uncle Collar Uncle is right there and is also red and gold is also then what to do recolored.

292
00:15:43,740 --> 00:15:48,810
Then make a parent an uncle as a black and grandparent as red.

293
00:15:48,960 --> 00:15:49,980
So check this one.

294
00:15:50,490 --> 00:15:57,930
I will change this 50 to black color and 70 also to black color.

295
00:15:58,440 --> 00:15:59,900
And this is 60.

296
00:15:59,910 --> 00:16:02,520
That is grandparent as a red.

297
00:16:04,700 --> 00:16:15,800
Now, remove this and consider this as a new node now, and this is parent, this is grandparent and

298
00:16:15,800 --> 00:16:21,440
this is what I was saying, that when a grandparent becomes rather than you have to check in, it's

299
00:16:21,860 --> 00:16:23,740
parents and ancestors also.

300
00:16:23,750 --> 00:16:25,790
So that's what this was recovering.

301
00:16:25,790 --> 00:16:27,800
The result of recovering this became great.

302
00:16:27,800 --> 00:16:32,570
Again, there is a direct conflict of interest, conflict, and then there is a good conflict.

303
00:16:32,580 --> 00:16:34,300
Check Uncle Uncle is black.

304
00:16:34,310 --> 00:16:41,060
So for this node, parent is red and that is black who performed rotation around grandparent, around

305
00:16:41,060 --> 00:16:41,750
grandparent.

306
00:16:42,020 --> 00:16:44,720
So around this grandparent, we perform vacation.

307
00:16:44,720 --> 00:16:47,570
So we know very well from Aviel trees.

308
00:16:47,570 --> 00:16:48,440
We have seen this.

309
00:16:48,440 --> 00:16:52,240
We will perform rotation in these three nodes and will do that rotation.

310
00:16:52,250 --> 00:16:55,580
So 40 will move up 40 miles up.

311
00:16:55,880 --> 00:16:56,200
Right.

312
00:16:56,570 --> 00:16:59,140
And then the N then comes the site.

313
00:16:59,450 --> 00:17:05,760
So this is four twenty and then as black as it is right.

314
00:17:06,079 --> 00:17:08,930
Grandparent will become black and children will become red.

315
00:17:09,619 --> 00:17:13,220
So the 40 who will come 60, this is 60.

316
00:17:14,710 --> 00:17:21,099
And it's a child of 70 and I will copy them as it is, this is 70 and this is 80.

317
00:17:23,390 --> 00:17:27,770
I have taken these atrocities, no, Toby is black when it should go.

318
00:17:28,069 --> 00:17:35,370
It was left of 48 will come on the side, so it will be a right shade of green tea, this study.

319
00:17:35,780 --> 00:17:37,410
And what about 60s?

320
00:17:37,410 --> 00:17:39,970
The left side, 50 is ADCETRIS.

321
00:17:40,150 --> 00:17:41,630
This is 50 is black.

322
00:17:41,960 --> 00:17:48,030
So you can see that out of these three n this has ventus became a grandparent and this became red color.

323
00:17:48,050 --> 00:17:50,450
This is only and this became black.

324
00:17:50,690 --> 00:17:51,940
So this is black.

325
00:17:51,950 --> 00:17:52,460
This is red.

326
00:17:52,460 --> 00:17:55,340
And this is really this is the final tree.

327
00:17:55,550 --> 00:18:00,370
So you can see that the incision was done here, but the rotations performed here because there was

328
00:18:00,400 --> 00:18:05,920
a conflict at this point now confirm whether it is perfect or not.

329
00:18:06,200 --> 00:18:08,710
So how do I know that count the number of blacks.

330
00:18:08,900 --> 00:18:11,780
So here are black, right, Nelsa?

331
00:18:11,780 --> 00:18:12,490
Also black.

332
00:18:12,620 --> 00:18:16,580
So count the number of blacks n one, two, three.

333
00:18:16,700 --> 00:18:17,840
One, two, three.

334
00:18:17,960 --> 00:18:19,490
One, two, three.

335
00:18:19,910 --> 00:18:22,960
One, two, three, one, two, and three.

336
00:18:23,180 --> 00:18:24,400
One, two, three.

337
00:18:24,870 --> 00:18:25,640
Along the spot.

338
00:18:25,700 --> 00:18:27,110
One, two, three.

339
00:18:27,110 --> 00:18:28,760
Yes, one, two, three.

340
00:18:29,300 --> 00:18:30,220
It's balanced.

341
00:18:30,470 --> 00:18:33,380
So in all that I see, I'm getting three only every time.

342
00:18:33,380 --> 00:18:35,110
So I don't think that always it should be three.

343
00:18:35,660 --> 00:18:37,040
Don't assume this one.

344
00:18:37,370 --> 00:18:39,890
It depends on the height of fuc is only.

345
00:18:40,160 --> 00:18:41,150
So how does a small.

346
00:18:41,150 --> 00:18:42,800
That's why I'm getting on Destry.

347
00:18:42,800 --> 00:18:47,330
Right so as it is going bigger number of black nawfal increase.

348
00:18:47,330 --> 00:18:47,900
Definitely.

349
00:18:48,590 --> 00:18:54,440
So this is the new thing we learn that is first is recovering second rotation.

350
00:18:54,440 --> 00:18:56,000
So we have done the things here.

351
00:18:56,150 --> 00:19:02,330
So newlines so that is notice always leave node and then we have to balance the regulatory Sawtell here

352
00:19:02,330 --> 00:19:03,110
we have finished.

353
00:19:03,470 --> 00:19:06,620
No, I have to insert four and eight, so let us do it quickly.

354
00:19:06,980 --> 00:19:08,150
I will insert Ford.

355
00:19:08,150 --> 00:19:10,700
So Ford is a smaller than this one and smaller than this one.

356
00:19:10,700 --> 00:19:11,630
Smaller than ten.

357
00:19:11,900 --> 00:19:14,390
So Ford should come on the left of this one.

358
00:19:14,570 --> 00:19:16,880
Always been so as a red color.

359
00:19:16,880 --> 00:19:18,950
So Ford is red color check.

360
00:19:20,580 --> 00:19:26,360
I had already the no it is black so no need of any need rotation or any adjustment.

361
00:19:26,660 --> 00:19:27,460
It's perfect.

362
00:19:28,160 --> 00:19:30,170
The next key is eight.

363
00:19:31,040 --> 00:19:32,540
Eight is less than forty.

364
00:19:32,540 --> 00:19:36,410
Less than grindy, less than ten but greater than four.

365
00:19:36,680 --> 00:19:40,130
So it will be added at the right child of four.

366
00:19:40,370 --> 00:19:44,720
So this is eight now check conflict.

367
00:19:44,720 --> 00:19:46,280
Yes there is a conflict.

368
00:19:47,390 --> 00:19:48,020
This is me.

369
00:19:48,590 --> 00:19:49,520
This is parent.

370
00:19:49,550 --> 00:19:51,980
This is grandparent and this is uncle.

371
00:19:52,160 --> 00:19:53,300
I will remove this.

372
00:19:53,480 --> 00:19:55,190
This will be confusing sometimes.

373
00:19:56,150 --> 00:19:58,640
This is uncle only one adult we are looking at.

374
00:19:58,880 --> 00:20:03,320
So uncle as black parents perform rotation.

375
00:20:03,620 --> 00:20:05,120
So which tradition I should perform.

376
00:20:05,420 --> 00:20:11,870
See, this is the exact rotation because this grandparents parent, this child is left and right chain.

377
00:20:12,170 --> 00:20:13,590
So we know this is the limitation.

378
00:20:13,730 --> 00:20:14,950
So I should do it like this.

379
00:20:15,290 --> 00:20:17,360
So what will be the route.

380
00:20:17,600 --> 00:20:25,040
New route this age will go down and then will come the right chain, then will come at the right and

381
00:20:25,040 --> 00:20:29,690
for remains there only this there isn't and grandparent is black.

382
00:20:29,810 --> 00:20:33,940
And if children are all right, that's what this is.

383
00:20:33,990 --> 00:20:35,150
Balance of your performance.

384
00:20:35,150 --> 00:20:37,720
Exact rotation rotations we know already.

385
00:20:37,730 --> 00:20:37,980
Right.

386
00:20:38,000 --> 00:20:39,320
Which relationship perform.

387
00:20:39,320 --> 00:20:40,670
You can clearly see that.

388
00:20:40,940 --> 00:20:47,110
OK, then whichever direction you have a return to conflict, see that it is a la orell.

389
00:20:48,130 --> 00:20:49,160
Ah so far.

390
00:20:49,160 --> 00:20:50,510
Maybe you can borrow that.

391
00:20:50,630 --> 00:20:51,800
Otherwise it's difficult.

392
00:20:51,800 --> 00:20:56,570
If you try to walk in zigzag or so it will be a new thing for you.

393
00:20:56,600 --> 00:20:59,550
So you can just yourself the traditions of reality.

394
00:20:59,900 --> 00:21:04,490
So I perform like a rotation, I perform no let us conform.

395
00:21:04,490 --> 00:21:05,630
But it is perfect or not.

396
00:21:05,870 --> 00:21:12,110
Check the height we can leave notes just count black one to acknowledge that it definitely is always

397
00:21:12,110 --> 00:21:12,320
there.

398
00:21:12,320 --> 00:21:13,010
So ignore it.

399
00:21:13,340 --> 00:21:15,350
One, two, one, two.

400
00:21:15,710 --> 00:21:17,420
One, two, one, two.

401
00:21:17,850 --> 00:21:22,250
In every part we have two two black notes, including three black notes.

402
00:21:22,610 --> 00:21:25,430
So there's the final word black three for the Keys that have taken.

403
00:21:25,730 --> 00:21:31,950
So this is the procedure for creating a black tree by maintaining all the properties we have lawn are

404
00:21:32,000 --> 00:21:32,750
all just black.

405
00:21:32,750 --> 00:21:35,120
The number of blacks along any path are equal.

406
00:21:35,300 --> 00:21:36,800
There are no consecutive red.

407
00:21:36,920 --> 00:21:42,560
And lastly, one more important thing in this a tree, whatever the height is just to ignore the reds,

408
00:21:42,560 --> 00:21:49,970
ignore the hide will be less so actually hired as a logger, but including reds, it's a double.

409
00:21:50,030 --> 00:21:52,370
So it's allowing us to increase up to two logging.

410
00:21:52,370 --> 00:21:54,520
So that's all the hiders to log in.

411
00:21:54,530 --> 00:22:00,590
If you move reds and see this slogan support, then including red, it has to in because colors are

412
00:22:00,590 --> 00:22:01,490
red or black.

413
00:22:01,730 --> 00:22:07,130
OK, if only all blacks this log and only Aldredge, it is logging red and black.

414
00:22:07,130 --> 00:22:08,760
Both are there so to log in.

415
00:22:08,930 --> 00:22:10,850
So that's all about that black tree.

416
00:22:10,850 --> 00:22:15,830
I have shown you how to create a tree and what are the options you have to perform better recovering

417
00:22:15,830 --> 00:22:18,690
and rotations we have done and we have learned how to do it.

418
00:22:18,770 --> 00:22:20,930
So you have to practice this whole thing by yourself.

419
00:22:21,320 --> 00:22:22,250
Do it once by.

420
00:22:22,330 --> 00:22:27,760
Yourself, without that, you cannot remember it and you cannot do it again, so you have to do all

421
00:22:27,760 --> 00:22:31,820
those steps, you do it once, then later on you can add more kids by yourself.

422
00:22:31,840 --> 00:22:32,260
Also.

423
00:22:33,100 --> 00:22:34,960
That's all in this video in the next few days.

424
00:22:34,990 --> 00:22:39,510
I will show you how it is related to two, three, four trees.

425
00:22:39,820 --> 00:22:46,690
And once you see that it's become danese and BlackBerry become so -- easy, if you're comfortable

426
00:22:46,690 --> 00:22:52,480
with two, three, four trees, I'll take the same keys and I will show you how it is related to two,

427
00:22:52,480 --> 00:22:53,500
three, four trees.

