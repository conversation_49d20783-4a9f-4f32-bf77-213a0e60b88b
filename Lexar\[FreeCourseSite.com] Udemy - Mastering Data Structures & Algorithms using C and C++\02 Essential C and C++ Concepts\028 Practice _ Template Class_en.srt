1
00:00:00,470 --> 00:00:05,330
A whiteboard lecture, I have shown a template class, so let us look at the demonstration.

2
00:00:06,060 --> 00:00:10,840
I have already written a class for Artomatic, the same class which I have shown on whiteboard.

3
00:00:11,670 --> 00:00:13,620
So let me explain to the class.

4
00:00:13,650 --> 00:00:21,030
This is a class Artomatic, having two private members of DIPEN Teachers and B, and this is a constructor

5
00:00:21,240 --> 00:00:25,440
which is used for initializing this and B, this in light.

6
00:00:25,920 --> 00:00:30,960
So the parameters are distinct to integers and their names are also N.V..

7
00:00:31,590 --> 00:00:37,640
So here for initializing D in B this and B I should write this.

8
00:00:37,680 --> 00:00:41,490
This means it refers to the members of the class.

9
00:00:41,490 --> 00:00:48,270
If I don't write this here, if I write simply a fine, then this will be this one parameter.

10
00:00:48,270 --> 00:00:50,280
It will be assigned to itself only.

11
00:00:50,550 --> 00:00:52,770
Right if you don't write this.

12
00:00:53,100 --> 00:00:59,990
So compiler will think that a means parameter and this is also parameter.

13
00:01:00,330 --> 00:01:10,620
But when I say this then it means this, this variable a member a unnormal A is a parameter.

14
00:01:10,860 --> 00:01:14,350
So normal A's are stored inside the data.

15
00:01:14,350 --> 00:01:21,230
Remember E then similarly B so this is a pointer to the current object in C++.

16
00:01:21,250 --> 00:01:21,620
Right.

17
00:01:22,050 --> 00:01:22,950
You should know this.

18
00:01:22,950 --> 00:01:24,730
Just revising it for you.

19
00:01:24,900 --> 00:01:29,550
OK, this is a pointer to a current object, current object.

20
00:01:29,850 --> 00:01:31,340
What does it mean by current object.

21
00:01:31,350 --> 00:01:32,570
I'll explain it afterwards.

22
00:01:33,060 --> 00:01:40,470
Then there is the function with just adding to the numbers and B and returning the result fusa distorting

23
00:01:40,470 --> 00:01:46,290
the result residency then redundancy, then subtract A, subtracting them, understanding C and so that

24
00:01:46,290 --> 00:01:49,440
it doesn't enter, not inside the main function.

25
00:01:49,440 --> 00:01:58,080
I will create an object of automatic object Artomatic e r and I will initialize by passing parameters

26
00:01:58,590 --> 00:02:00,510
ten and five.

27
00:02:00,630 --> 00:02:10,650
Right, so the standard five are passed to this a and they assign to the data members and B all object

28
00:02:10,650 --> 00:02:11,240
eight.

29
00:02:11,520 --> 00:02:13,410
So this means current object.

30
00:02:13,410 --> 00:02:16,460
Er we are calling the constructor upon the R right.

31
00:02:16,530 --> 00:02:22,920
So this is not a this not Beemans A and B they are members of E R object.

32
00:02:23,100 --> 00:02:23,850
That's correct.

33
00:02:23,850 --> 00:02:24,260
Object.

34
00:02:24,690 --> 00:02:31,890
If I continue then if I say e r two and here I, I will pass 15 and seven.

35
00:02:32,200 --> 00:02:35,760
Then again the constructor is called the same constructor is called non.

36
00:02:35,760 --> 00:02:38,380
This time this caps emons.

37
00:02:38,430 --> 00:02:43,510
This arrow means a of er two and B of a tool.

38
00:02:43,860 --> 00:02:48,830
So this time it was for Ayhan the the other time it was for AACTA.

39
00:02:49,110 --> 00:02:51,190
So this means the current object.

40
00:02:51,480 --> 00:02:53,970
Alright, so I have explained what does it mean by this.

41
00:02:54,390 --> 00:02:59,740
I will remove it then simply I will print the result of add and subtract.

42
00:03:00,070 --> 00:03:10,920
OK I it and here I will call Irda and I like then also I will give a model so that it moves to the next

43
00:03:10,920 --> 00:03:14,940
line C out rather than subtract.

44
00:03:15,560 --> 00:03:19,580
Then this is E R SuBo and also L.

45
00:03:20,620 --> 00:03:26,620
That said, I have created an object of a class Artomatic inside main function, and I'm calling to

46
00:03:26,650 --> 00:03:35,890
add and subtract methods or functions of automatic glass guzzlers, additional huffin and subtraction

47
00:03:35,890 --> 00:03:36,500
is five.

48
00:03:36,750 --> 00:03:37,360
That's it.

49
00:03:37,990 --> 00:03:38,860
So this is over.

50
00:03:39,460 --> 00:03:41,890
Now, let us make some changes inside the class.

51
00:03:41,890 --> 00:03:44,460
I will implement all these functions outside.

52
00:03:45,100 --> 00:03:46,740
So already I have written them.

53
00:03:46,750 --> 00:03:47,770
So how to do.

54
00:03:47,770 --> 00:03:48,460
I will show you.

55
00:03:49,270 --> 00:03:53,410
I will remove the bracket closing bracket here and I will close it here.

56
00:03:53,620 --> 00:03:59,860
If you are implementing the functions outside the class, then we should have the declaration of those

57
00:03:59,860 --> 00:04:07,930
functions inside the class so copied and pasted their copy, the header and pasted here.

58
00:04:08,170 --> 00:04:13,150
Copy the heading of this function sub and paste it here.

59
00:04:13,180 --> 00:04:13,880
That's it.

60
00:04:14,290 --> 00:04:15,040
This is done.

61
00:04:15,700 --> 00:04:21,519
Not the functions are outside so they should show their scope means they belong to this class, Artomatic.

62
00:04:21,880 --> 00:04:24,010
So this should be written here.

63
00:04:24,760 --> 00:04:27,960
Artomatic then with scope resolution.

64
00:04:28,600 --> 00:04:29,920
All right then.

65
00:04:30,190 --> 00:04:32,400
Same thing beside the function name.

66
00:04:32,650 --> 00:04:35,620
OK, Artomatic scope resolution.

67
00:04:36,130 --> 00:04:40,360
Just beside the function, the automatic scope resolution finished.

68
00:04:41,200 --> 00:04:43,240
See all letters are gone now.

69
00:04:43,240 --> 00:04:46,150
I have implemented the functions outside the class.

70
00:04:46,150 --> 00:04:47,310
They are little indented.

71
00:04:47,470 --> 00:04:49,600
Let them be as it is not the issue.

72
00:04:49,930 --> 00:04:51,880
So same program looks.

73
00:04:52,330 --> 00:04:56,620
Yes, it's [REMOVED] no less than what this class into a template.

74
00:04:56,920 --> 00:04:58,780
Template classes are generally classes.

75
00:04:58,780 --> 00:05:03,940
They support any data type like this, present classes working for integer.

76
00:05:04,270 --> 00:05:10,960
So if you make it as a template it will support flawed or double or CAD or any other users defined it

77
00:05:10,960 --> 00:05:12,870
or type it will work for anything.

78
00:05:13,120 --> 00:05:18,190
So already I have a class, so I will show you how to convert it into a template.

79
00:05:18,730 --> 00:05:23,260
And even I suggest when you write a program, first of all, you finish the program right.

80
00:05:23,590 --> 00:05:29,470
Then later convert into a template because while typing the code along with the template, then you

81
00:05:29,470 --> 00:05:32,200
may face some difficulties, you might do some mistakes.

82
00:05:32,590 --> 00:05:35,200
So make a terrific class run, then check it.

83
00:05:35,200 --> 00:05:38,140
Everything is working, then convert template.

84
00:05:38,380 --> 00:05:44,170
OK, so even after converting the template, if you get any errors, you can easily remove them because

85
00:05:44,170 --> 00:05:46,060
those errors are due to template only.

86
00:05:46,450 --> 00:05:46,840
Right.

87
00:05:47,200 --> 00:05:48,670
So already classes there.

88
00:05:48,670 --> 00:05:57,870
So the method of making it as a template is template class capital t the easy barometer of type template.

89
00:05:58,330 --> 00:06:02,950
Now this should be type V also T type.

90
00:06:03,430 --> 00:06:10,600
OK, now don't convert everything into T, just be careful, see whatever should be converted.

91
00:06:10,600 --> 00:06:12,370
You convert only that one sometime.

92
00:06:12,370 --> 00:06:17,380
Temporary variables are there, they are integer are flawed that they should remain same only sometimes.

93
00:06:17,590 --> 00:06:19,560
So these are the two variables there.

94
00:06:19,570 --> 00:06:20,140
Template.

95
00:06:20,290 --> 00:06:21,310
So what about this.

96
00:06:21,370 --> 00:06:24,730
There should also be a template, there should also be a template.

97
00:06:24,730 --> 00:06:31,550
Right now the functions outside see the scope of template ends here, this template to be made.

98
00:06:32,020 --> 00:06:34,240
So the class has became a template.

99
00:06:34,240 --> 00:06:37,120
The body of class has closed, so the template has finished.

100
00:06:37,660 --> 00:06:49,660
So for this you should again write on class template, template class B, then this should be T, this

101
00:06:49,660 --> 00:06:52,000
should also be A T, that's it.

102
00:06:52,600 --> 00:06:54,370
But still something is missing here.

103
00:06:54,730 --> 00:06:55,420
What is that.

104
00:06:55,750 --> 00:06:59,560
The class Artomatic is a template so you should pass a template.

105
00:06:59,560 --> 00:07:04,450
I recommend to that class along with this coalition here also should mention template.

106
00:07:04,630 --> 00:07:05,440
Remember this.

107
00:07:05,920 --> 00:07:08,470
Alright, so this portion is over.

108
00:07:08,980 --> 00:07:12,050
Not again for this function I should make it as a template.

109
00:07:12,080 --> 00:07:14,050
I will write on template once again here.

110
00:07:14,170 --> 00:07:21,730
I should try it on template class T then return type this template and local variable C which is adding

111
00:07:21,730 --> 00:07:22,450
to integer.

112
00:07:22,450 --> 00:07:30,150
So it should be a template type and the class should also be declared as template write or still is

113
00:07:30,160 --> 00:07:30,640
an error.

114
00:07:30,880 --> 00:07:33,490
So go back to the class inside the class.

115
00:07:33,490 --> 00:07:34,750
I did not change this to the.

116
00:07:35,530 --> 00:07:39,550
Yes, that is how you can easily make changes.

117
00:07:39,550 --> 00:07:41,920
If there are any errors you can easily find them.

118
00:07:42,340 --> 00:07:44,350
Now the threat is gone now.

119
00:07:44,350 --> 00:07:45,940
Subtract is also the same.

120
00:07:46,210 --> 00:07:55,600
So this also I should make it as a template template class B and this end should be at the end and the

121
00:07:55,600 --> 00:08:03,220
class name should also take D and a local variable should also be a T like Justin NetSol nine said the

122
00:08:03,220 --> 00:08:09,070
main function when I'm creating an object of this automatic, I should mention, and now this works

123
00:08:09,070 --> 00:08:09,670
perfectly.

124
00:08:09,790 --> 00:08:12,520
Now this ten and five are integer type, right?

125
00:08:12,520 --> 00:08:15,100
I will make the mass float then.

126
00:08:15,400 --> 00:08:19,660
These values are ten point nine nine five point four four.

127
00:08:20,150 --> 00:08:20,840
Let us run.

128
00:08:21,080 --> 00:08:27,650
Yes, it is working for float, also sixteen point four, three and five point fifty five right after

129
00:08:27,650 --> 00:08:29,290
working third work for a float.

130
00:08:29,300 --> 00:08:31,710
Also, it works for character also.

131
00:08:31,910 --> 00:08:43,909
This letter is a and this is being let us make it as B Nooran does the sum of A sum of and B A 65,

132
00:08:43,909 --> 00:08:44,840
B six to six.

133
00:08:44,850 --> 00:08:51,260
So we got some letter at some ASCII code whose output is like this.

134
00:08:51,710 --> 00:08:54,020
OK, whose output is like this.

135
00:08:54,320 --> 00:09:00,650
If you want that integer value then you can typecast it like this and you will get the type custard

136
00:09:00,650 --> 00:09:03,230
value in granite.

137
00:09:03,530 --> 00:09:07,460
Now this is minus 125 because it's going to be on 127.

138
00:09:07,790 --> 00:09:09,020
Then this is minus one.

139
00:09:09,500 --> 00:09:11,210
A minus B is minus one.

140
00:09:11,210 --> 00:09:12,910
The difference in the ratio, this one.

141
00:09:13,220 --> 00:09:14,370
So this is minus one.

142
00:09:14,390 --> 00:09:21,200
So first one, if I make it as a B and the second one as aid and I get plus one, yes I got plus one

143
00:09:21,290 --> 00:09:24,430
submission is going beyond 127.

144
00:09:24,440 --> 00:09:32,360
So it is becoming overflow and it is giving negative result like sixty five plus 66 six one one.

145
00:09:32,360 --> 00:09:32,570
Right.

146
00:09:32,590 --> 00:09:35,450
131 is greater than 127.

147
00:09:35,450 --> 00:09:37,250
So that's why we got negative number.

148
00:09:37,400 --> 00:09:42,330
Anyway, if you want to know more you should study this why it is becoming a negative number.

149
00:09:42,620 --> 00:09:44,390
This is a basic programming part.

150
00:09:44,610 --> 00:09:49,570
OK, so you can explore it in Google or you should be aware of this one, that's all.

151
00:09:49,580 --> 00:09:52,730
So I have shown you how to write convert a class into a template.

152
00:09:52,730 --> 00:10:00,300
So in the topics like a reading list and Qs and Stack as well as trees there, if you want to convert

153
00:10:00,300 --> 00:10:02,600
the class into a template, you can convert it.

154
00:10:03,410 --> 00:10:08,360
And if you need any help, if you have already done the class and you are unable to convert into a template,

155
00:10:08,600 --> 00:10:12,020
you can post the code in question on U.S. and you can ask me.

156
00:10:12,350 --> 00:10:15,400
I will convert that class into a template and help you all that.

157
00:10:15,740 --> 00:10:17,060
So that's all in this video.

