1
00:00:00,430 --> 00:00:07,370
Now let's see how to evaluate the polynomial already we have a polynomial B of this structure and this

2
00:00:07,390 --> 00:00:08,630
how it is having the data.

3
00:00:09,510 --> 00:00:17,640
Now, how to compute that if we know the value of X, if the value of X is known, then how we can get

4
00:00:17,650 --> 00:00:22,210
the single value that is answer for this polynomial, the result of this polynomial.

5
00:00:22,780 --> 00:00:24,880
So if you know the value of X, what we should do.

6
00:00:26,020 --> 00:00:32,740
Raise to this power, whatever to explain this, then multiply that coefficient, then aggregate next

7
00:00:32,750 --> 00:00:37,890
term, then in this term raise X to the power forward and multiply it coefficient.

8
00:00:38,290 --> 00:00:43,130
So every time we have to raise X to this power and multiply that coefficient.

9
00:00:43,150 --> 00:00:44,950
So for every dollar we have to do that.

10
00:00:45,190 --> 00:00:47,380
And also we have to go on adding the.

11
00:00:48,370 --> 00:00:53,450
So all these stones can be computed and added one by one using a for loop.

12
00:00:53,620 --> 00:00:55,470
So we have to process all of them.

13
00:00:55,480 --> 00:00:57,700
That is depending on the number of atoms.

14
00:00:57,700 --> 00:01:00,450
So using Afolabi can evaluate this one.

15
00:01:00,880 --> 00:01:05,470
So I'll try it on the code for this for iPIX values.

16
00:01:06,450 --> 00:01:11,580
Zero I is less than number of atonce Bédard.

17
00:01:12,790 --> 00:01:19,900
And I placeless not here, variable is not declared, don't worry about that, when I really liked the

18
00:01:19,900 --> 00:01:23,850
program that time, I will declare the variables, I'm just using them.

19
00:01:23,860 --> 00:01:25,180
So focus on the core.

20
00:01:27,070 --> 00:01:33,880
Then every time we have to raise to this power, so this is power.

21
00:01:35,320 --> 00:01:37,990
X. So assume that there is some variable.

22
00:01:38,010 --> 00:01:43,650
We know the value of X percent X is having value was five, the sex is there.

23
00:01:43,960 --> 00:01:46,690
So you want to place five in this polynomial.

24
00:01:46,690 --> 00:01:49,660
So for that X, it should be raised to this power.

25
00:01:49,690 --> 00:01:50,410
So what is this?

26
00:01:51,040 --> 00:01:54,250
B, daughter B of zero.

27
00:01:54,430 --> 00:01:59,130
So we got B of instead of zero.

28
00:01:59,140 --> 00:02:02,680
I will write I so that I can access all of them one by one.

29
00:02:03,100 --> 00:02:07,650
I dot exponent dot expe.

30
00:02:08,889 --> 00:02:14,500
So there is a power function available and the math, the dot each header file which will raise this

31
00:02:14,500 --> 00:02:15,990
X to this power.

32
00:02:16,750 --> 00:02:18,460
So x rays to this bar.

33
00:02:19,240 --> 00:02:24,520
So we got the export of five then that should be multiplied with this coefficient.

34
00:02:24,520 --> 00:02:25,450
So this coefficient.

35
00:02:25,450 --> 00:02:34,000
So this is P P of zero dot coefficient so multiplied by P dot.

36
00:02:35,330 --> 00:02:37,590
B of zero.

37
00:02:37,770 --> 00:02:44,050
So after zero, I have to go on one and two and three, so on, so let us call it as I don't go.

38
00:02:44,540 --> 00:02:46,320
So this is multiplied that coefficient.

39
00:02:47,240 --> 00:02:50,270
This will be for just one tone like this.

40
00:02:50,270 --> 00:02:55,480
We have to do it for all the times in this volume and I have to go on adding them.

41
00:02:55,790 --> 00:02:59,180
So let us take one more variable and call it as a sum.

42
00:02:59,420 --> 00:03:00,690
And this is zero.

43
00:03:01,040 --> 00:03:04,750
So here I will write on some plus assign.

44
00:03:05,090 --> 00:03:06,500
So initially sum is zero.

45
00:03:06,690 --> 00:03:13,580
Now this is first as computer and rationalistic and in some other than some likewise everything will

46
00:03:13,580 --> 00:03:14,750
be added into some.

47
00:03:15,170 --> 00:03:19,770
So this will be some assign some plus this complete product.

48
00:03:20,060 --> 00:03:26,150
So here the multiplication is done and this is power, this coefficient, so that this will get the

49
00:03:26,150 --> 00:03:30,830
result and some now result in some return result.

50
00:03:30,830 --> 00:03:33,170
If it is a function, then return something.

51
00:03:34,370 --> 00:03:38,660
So using a for loop, each item can be evaluated and added.

52
00:03:40,330 --> 00:03:42,380
So this is how evaluation can be done.

53
00:03:42,640 --> 00:03:47,940
This we will be using in of function when I write a program, that time will get a clear picture, just

54
00:03:47,970 --> 00:03:49,400
rambling explanation here.

55
00:03:49,570 --> 00:03:51,230
So that's all about evaluation.

56
00:03:51,730 --> 00:03:54,640
Now, our next topic is addition of a two polynomial.

57
00:03:54,640 --> 00:03:59,470
So I will take two example polynomials and I will have this representation ready.

58
00:03:59,830 --> 00:04:03,940
Then I will show how to polynomials can be added to that.

