1
00:00:00,330 --> 00:00:05,410
The topic is going forward, this is index based sucking.

2
00:00:06,640 --> 00:00:13,790
This is one of the easiest sorting to understand and also the fastest, but it consumes a lot of memory.

3
00:00:14,870 --> 00:00:16,490
Let us see how it works.

4
00:00:17,210 --> 00:00:19,690
I have a list of elements in an array.

5
00:00:19,910 --> 00:00:23,950
These are the elements that I want to sort them for, for sorting these elements.

6
00:00:23,960 --> 00:00:27,350
I have thicker than any other name is Count.

7
00:00:27,950 --> 00:00:33,580
And the size of this Uhry should be equal to the largest element that we have in this.

8
00:00:33,600 --> 00:00:39,740
I see the largest element in this area is this is 50 50 dislodges.

9
00:00:39,980 --> 00:00:46,320
So I have an array of sizes, 16, so that I have index of 15 also available in this country.

10
00:00:46,850 --> 00:00:48,100
So this is the requirement.

11
00:00:48,440 --> 00:00:53,880
The second requirement is I must initialize this array, but to some empty values.

12
00:00:53,900 --> 00:01:00,110
So there is nothing that can be so we can take a 070 or minus one as empty.

13
00:01:00,500 --> 00:01:03,790
So I will take 077 initialized with Zervos.

14
00:01:04,160 --> 00:01:08,720
So let us initialize this array with Zeitels so the counter is ready with zero.

15
00:01:09,140 --> 00:01:14,960
Now we are ready with the setup for sorting this list I have taken and that is equal to the maximum

16
00:01:14,960 --> 00:01:15,680
size element.

17
00:01:15,890 --> 00:01:18,390
And also I have initialize it now.

18
00:01:18,410 --> 00:01:25,260
Let us start the process so the processes scan through all these elements in this one by one.

19
00:01:25,730 --> 00:01:33,910
So if I go up on the first element six, so whatever the element is at the same index in a counter increment

20
00:01:33,920 --> 00:01:39,670
of value, so make it as one, then go to the next element three.

21
00:01:40,280 --> 00:01:42,920
So go to index three here and mark it as one.

22
00:01:44,180 --> 00:01:45,110
What does it mean?

23
00:01:45,290 --> 00:01:51,500
This says that number three is presented there in that area so far, have you found one occurrence?

24
00:01:52,100 --> 00:01:53,440
Yes, continue.

25
00:01:53,450 --> 00:01:55,250
Next element, name from one.

26
00:01:55,280 --> 00:01:56,130
The first one.

27
00:01:56,150 --> 00:01:57,920
So there's one nine available there.

28
00:01:58,250 --> 00:01:59,990
Then mark this as one.

29
00:02:01,210 --> 00:02:09,690
Then 15, so, Mark, this is one that is incremented the Nexus six go to six already, one is there

30
00:02:09,699 --> 00:02:15,640
so incremented so we get to then eight index back-seat and increment it.

31
00:02:15,650 --> 00:02:22,390
So it is one then to go to index to a level increment three, go to index of three.

32
00:02:22,540 --> 00:02:30,460
It's all anyone in agreement to do to the next six, go to index six and increment this one.

33
00:02:30,490 --> 00:02:31,280
So we have three.

34
00:02:31,610 --> 00:02:38,650
So in the commentary actually I got the number of occurrences of each number presented and I saw the

35
00:02:38,650 --> 00:02:40,300
largest number present was 15.

36
00:02:40,300 --> 00:02:42,730
So I must have index 15 also here.

37
00:02:43,420 --> 00:02:48,370
Like if that is the largest number hundred, then this Arasu size, they should be 100.

38
00:02:49,330 --> 00:02:55,130
So how many times each digit is appearing there for that, the corresponding index, we have rhetoric's

39
00:02:55,180 --> 00:02:56,070
occurrences.

40
00:02:56,350 --> 00:02:58,590
So this is one step of a process.

41
00:02:59,020 --> 00:03:04,840
Now we want sorted elements, so assume that these elements are removed, so I'll remove them.

42
00:03:05,230 --> 00:03:06,520
So I have cleared this one.

43
00:03:06,970 --> 00:03:14,980
Now, the next step is I should copy all the elements from going back to a let us do it.

44
00:03:16,150 --> 00:03:24,160
So the procedure is scan through this rate, and if the value is more than zero greater than zero,

45
00:03:24,460 --> 00:03:26,620
then right on the index as a number.

46
00:03:27,190 --> 00:03:32,650
So let's see, this is zero move to next zero zero.

47
00:03:32,650 --> 00:03:34,420
Move to next three.

48
00:03:34,420 --> 00:03:38,490
Is that three is having to mention three years, two times.

49
00:03:38,500 --> 00:03:38,660
So.

50
00:03:38,720 --> 00:03:39,130
Right.

51
00:03:39,130 --> 00:03:40,460
Three, two times.

52
00:03:41,230 --> 00:03:47,980
So this I can write it one by one, also first rate three, then decrement it then again.

53
00:03:48,000 --> 00:03:48,300
Right.

54
00:03:48,340 --> 00:03:50,140
Three, then decrement it.

55
00:03:50,500 --> 00:03:51,450
It becomes zero.

56
00:03:51,880 --> 00:03:56,380
So this is no move to next this all zero, this move to mixology zero.

57
00:03:56,830 --> 00:04:04,270
The six is three times the right six one time decrement it right six one more time and decrement it

58
00:04:05,140 --> 00:04:05,560
right.

59
00:04:05,560 --> 00:04:07,800
Six one more time than DiClemente.

60
00:04:09,010 --> 00:04:14,160
This is completed, go to the next, go to next, this is eight is one so right.

61
00:04:14,200 --> 00:04:18,070
One eight and make it zero nine is one time.

62
00:04:18,220 --> 00:04:19,209
So right now.

63
00:04:19,240 --> 00:04:20,200
And make it zero.

64
00:04:20,829 --> 00:04:21,750
Ten is one time.

65
00:04:21,760 --> 00:04:23,620
So write it down and make it zero.

66
00:04:24,100 --> 00:04:26,000
11 is your time doing this one time.

67
00:04:26,000 --> 00:04:29,600
So write to us and make it zero then zero zero.

68
00:04:29,680 --> 00:04:33,610
This is also one time to write 15 and make this a zero.

69
00:04:34,510 --> 00:04:36,080
So that's all we got.

70
00:04:36,120 --> 00:04:42,790
Elements in sorted order thing here intentionally have taken duplicate elements so that you can understand

71
00:04:42,790 --> 00:04:44,020
what is the meaning of count.

72
00:04:44,260 --> 00:04:48,500
If there is only one copy of an element, then each place we would have got just one one.

73
00:04:48,520 --> 00:04:52,210
So if there are duplicates, we will have the count of that duplicates.

74
00:04:52,210 --> 00:04:53,830
How many duplicate elements are there?

75
00:04:55,020 --> 00:04:56,820
So therefore, the procedures are simple.

76
00:04:58,520 --> 00:05:02,250
Now, analysis, what is the time taken by this procedure?

77
00:05:02,930 --> 00:05:08,900
What we are doing in the procedure, scanning through Uhry, how many elements is having elements that

78
00:05:08,900 --> 00:05:11,420
begin scanning through a recount?

79
00:05:12,230 --> 00:05:17,180
How many elements are there and elements that the size or the sizes are different?

80
00:05:17,180 --> 00:05:18,310
Also doesn't matter.

81
00:05:18,350 --> 00:05:26,060
We say elements of some some elements so famous and plus and so we can say outdraw.

82
00:05:26,060 --> 00:05:32,200
And if you said no, no, this size is and and this size is so OK.

83
00:05:32,210 --> 00:05:37,450
It is endless and endless and can also be the last order of any.

84
00:05:38,030 --> 00:05:45,400
But when you use this gift, the idea that merging is used here, so it's not merging so better candidates.

85
00:05:45,410 --> 00:05:49,850
And so this is linear time caking algorithm.

86
00:05:51,840 --> 00:05:53,130
Then what about the space?

87
00:05:54,250 --> 00:06:01,260
It needs a big size space that is Uhry, depending on the maximum number that you have, an original

88
00:06:01,290 --> 00:06:01,690
list.

89
00:06:02,470 --> 00:06:06,740
So space consumers huge, that depends on the maximum number.

90
00:06:07,180 --> 00:06:13,920
So it takes a lot of space so we can take extra space is also and only but it is larger space.

91
00:06:14,470 --> 00:06:16,270
So this is a space consuming ELGART.

92
00:06:17,140 --> 00:06:22,990
So unless we have the range of numbers in small range, we cannot think of using confort.

93
00:06:23,260 --> 00:06:26,150
If the range is very big, then we need a big size.

94
00:06:27,760 --> 00:06:30,900
Now let me ride on Elgort function for.

95
00:06:32,320 --> 00:06:38,160
I have a function here that is count for taking an array and a number of elements that size of this

96
00:06:38,170 --> 00:06:38,500
list.

97
00:06:38,590 --> 00:06:43,220
Size of this list is 10 and 10 nudniks.

98
00:06:43,240 --> 00:06:45,790
What I need in that count total Gordimer's.

99
00:06:46,120 --> 00:06:52,960
I need a call count for what should be the size equal to the maximum element present in this.

100
00:06:53,710 --> 00:06:54,970
So, Max.

101
00:06:55,050 --> 00:06:56,170
Fine, fine, Max.

102
00:06:56,420 --> 00:07:02,650
So let us write one function to find out the maximum element in an area where it takes array as a parameter

103
00:07:03,190 --> 00:07:04,480
and the size of an array.

104
00:07:04,840 --> 00:07:07,070
Find out maximum and we know the max.

105
00:07:07,090 --> 00:07:10,040
Not like another example, it was 15.

106
00:07:10,510 --> 00:07:12,270
So for this I should declare a variable.

107
00:07:12,280 --> 00:07:13,870
So I will also declare a variable.

108
00:07:14,150 --> 00:07:15,610
I may require more variables.

109
00:07:15,610 --> 00:07:19,030
I declare them now based on the size I need.

110
00:07:19,300 --> 00:07:26,050
And so let us create dynamically so far that I will take an array with the name C and just change the

111
00:07:26,050 --> 00:07:28,560
name to C count Londinium.

112
00:07:28,780 --> 00:07:41,470
I'll just say C, so let us have one pointer of integer type C, so C assign new end of max size max

113
00:07:41,890 --> 00:07:48,820
C from this max I will get 15, but the array sizes should be 16 because it starts from zero one zero

114
00:07:48,910 --> 00:07:49,320
15.

115
00:07:49,600 --> 00:07:51,790
So max plus one size.

116
00:07:52,480 --> 00:07:54,270
So this is an array created.

117
00:07:55,250 --> 00:07:58,060
Next I have to initialize this array with a zero.

118
00:07:58,090 --> 00:08:04,780
So with the for loop I can initialize that with all the zeroes so the serial will initialize it for

119
00:08:04,780 --> 00:08:07,930
loop zero two max plus one C of ISIS zero.

120
00:08:07,940 --> 00:08:10,320
So all these locations have initialized C2.

121
00:08:11,380 --> 00:08:17,320
Now, next, I have to scan through this, and for every number, whatever the number presented here,

122
00:08:17,330 --> 00:08:24,250
any of I at the same index, I should increment the value so that work I can do it by scanning through

123
00:08:24,250 --> 00:08:26,700
this array using for loop.

124
00:08:27,070 --> 00:08:28,420
So I will write on the code here.

125
00:08:29,230 --> 00:08:37,000
Say this for loop as going from zero to N, that is from zero to 10 for this size array then for every

126
00:08:37,000 --> 00:08:40,120
element that should become the index of Kountry.

127
00:08:40,480 --> 00:08:44,169
So if it is a three minutes go to index three C of three.

128
00:08:45,010 --> 00:08:47,100
If this is three then C of three.

129
00:08:47,470 --> 00:08:51,650
So that element should become an index of Siiri and then I'm incrementing one.

130
00:08:52,510 --> 00:08:58,090
So this followable finish the task of counting all the elements just long enough for that.

131
00:08:58,090 --> 00:09:01,860
The commentary was the final word on the values once again.

132
00:09:02,740 --> 00:09:04,090
So I have written the values.

133
00:09:04,600 --> 00:09:06,990
See, these values will be filled with this follow.

134
00:09:09,250 --> 00:09:15,460
Now, the next step is based on these comments, I should fill up the elements and are they so element

135
00:09:15,470 --> 00:09:22,330
should be copied and it should be documented and if it is zero, I should move ahead so far that again,

136
00:09:22,330 --> 00:09:24,070
I will write a loop, but not this time.

137
00:09:24,070 --> 00:09:28,120
I will use a vile loop and I will write on the code for copying all these elements.

138
00:09:28,480 --> 00:09:30,010
So let us start from here.

139
00:09:30,820 --> 00:09:37,120
So this little loop, I start from zero, I reaches up to Max plus one that is zero to 15.

140
00:09:37,240 --> 00:09:38,650
At 16, it will stop.

141
00:09:38,830 --> 00:09:44,650
And also I need an index pointer here on array to copy the element.

142
00:09:44,660 --> 00:09:46,330
So I here and G here.

143
00:09:46,690 --> 00:09:48,340
So I will take one more counter there.

144
00:09:48,670 --> 00:09:51,170
That is G is also initialized to zero.

145
00:09:51,640 --> 00:09:56,900
Now I have to copy all these elements depending on the count and I should fill the array.

146
00:09:57,310 --> 00:10:02,470
So if this value C of a value for the zero, then I should simply move.

147
00:10:02,470 --> 00:10:04,360
I move.

148
00:10:04,360 --> 00:10:04,950
I move.

149
00:10:04,960 --> 00:10:16,000
I suppose it is not zero then I should copy this value three there and decrement this to one and move

150
00:10:16,000 --> 00:10:16,380
G.

151
00:10:16,430 --> 00:10:17,280
But don't move.

152
00:10:18,160 --> 00:10:21,310
So if it is greater than zero, don't move for the zero then move.

153
00:10:22,180 --> 00:10:24,630
So this I will do it inside the loop.

154
00:10:24,970 --> 00:10:26,230
So let me write on the board.

155
00:10:26,680 --> 00:10:27,280
See here.

156
00:10:27,280 --> 00:10:31,220
If sea of value is greater than zero than in any of J.

157
00:10:31,240 --> 00:10:37,180
Copy the value i.e. if I is the three that I should copy three there and this tremendous value.

158
00:10:37,210 --> 00:10:38,950
So C of A minus minus.

159
00:10:40,070 --> 00:10:45,650
Otherwise, if this is zero, if you have copied bought, this is zero, now move to the next.

160
00:10:46,190 --> 00:10:48,320
So Elsmore way to the next.

161
00:10:49,780 --> 00:10:53,800
So the follow loop, depending on the count, the elements will be filled in.

162
00:10:55,570 --> 00:10:57,370
So let us look at the court once again.

163
00:10:59,390 --> 00:11:05,030
Find the maximum elements from this array, create a count of that size.

164
00:11:05,940 --> 00:11:08,370
Initialise counter with all the rules.

165
00:11:10,370 --> 00:11:15,530
For this count, the number of elements present there and right, the count here in Greece, the count.

166
00:11:16,840 --> 00:11:22,900
Then in this loop, copy all the elements from this commentary back to a saw from the cold if you want

167
00:11:22,900 --> 00:11:29,920
to find out the time complexity this fall loop is end and the follow up is also end and this fall also.

168
00:11:29,920 --> 00:11:32,900
And so totally Destrehan, it is coming.

169
00:11:33,130 --> 00:11:36,000
And because the initial initialization part also is included.

170
00:11:36,370 --> 00:11:38,230
So it is outdraw and.

171
00:11:40,070 --> 00:11:43,200
So here I wrote and listen initialization, I did not consider.

172
00:11:43,460 --> 00:11:49,370
So we got to end, but now we got three and because initial edition also included, so whatever it is

173
00:11:49,370 --> 00:11:51,110
to one or the doesn't make a difference.

174
00:11:51,140 --> 00:11:54,730
Finally, we want the degree of a polynomial that is.

175
00:11:54,890 --> 00:11:58,350
And so the time taken by the algorithms outdraw.

176
00:11:58,430 --> 00:12:01,190
And so that's all about constant.

