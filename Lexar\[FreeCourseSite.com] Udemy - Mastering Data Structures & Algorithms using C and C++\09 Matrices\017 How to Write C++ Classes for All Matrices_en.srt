1
00:00:00,210 --> 00:00:03,450
So this is the classic definition I have just reduce the size.

2
00:00:04,410 --> 00:00:11,100
Now we will pick up the functions one by one and implement them for diagonal matrix, then simultaneously

3
00:00:11,100 --> 00:00:15,230
I will show you how changes can be made for writing other matters.

4
00:00:15,270 --> 00:00:20,340
So here, remember that we are just looking at how the program should look like, but writing the actual

5
00:00:20,340 --> 00:00:22,140
code, I will write the code and show you.

6
00:00:23,130 --> 00:00:26,220
So let us pick up the first function that is constructor.

7
00:00:26,610 --> 00:00:34,080
So constructor for implementing it outside the class, then write the class name and the constructively.

8
00:00:36,580 --> 00:00:39,290
The barometer's dimension and.

9
00:00:41,170 --> 00:00:46,890
This will take the dimension of a mattocks and Crosson, so five or six, whatever, the size, as,

10
00:00:46,950 --> 00:00:48,150
for example, it was five.

11
00:00:48,550 --> 00:00:50,770
So this five should be sitting in this one.

12
00:00:50,780 --> 00:00:52,230
This should be set in that one.

13
00:00:52,510 --> 00:00:57,180
So the member of a class can be referred with this operator.

14
00:00:57,340 --> 00:00:59,700
So this I am assigned.

15
00:01:00,190 --> 00:01:04,480
And so this key word we can use for accessing the member of the class.

16
00:01:04,660 --> 00:01:06,290
And this is a parameter.

17
00:01:07,090 --> 00:01:11,020
So if you are aware of this, that this is a pointer to object itself.

18
00:01:11,050 --> 00:01:18,040
So this we can use if there is a ambiguity name, see this the local member is N and the parameters

19
00:01:18,040 --> 00:01:24,320
are also N, so if both the names are N, so I cannot write and assign them, that is meaningless.

20
00:01:24,640 --> 00:01:29,100
So this end means the member and local medium.

21
00:01:30,130 --> 00:01:33,490
So I have taken the sites then for that same size.

22
00:01:33,490 --> 00:01:40,360
I have a pointer here so I should create an array in a heap of that size sample size five, then I should

23
00:01:40,360 --> 00:01:49,750
create an array and him so assign new end of size and so I can simply take either this or not.

24
00:01:50,110 --> 00:01:53,640
So if I want to write this down, I should say this to and or just.

25
00:01:54,340 --> 00:01:58,030
So this will create this the constructor.

26
00:01:59,280 --> 00:02:06,450
So inside the constructor, this is a creator and he put dynamically so simultaneously over to the districted

27
00:02:06,450 --> 00:02:06,910
also.

28
00:02:07,140 --> 00:02:16,410
So if I'm implementing the Destructor class name, destructo name, then what are district should do

29
00:02:16,920 --> 00:02:23,730
if classes acquiring anything at runtime from heap extraordinarily stacked one?

30
00:02:24,060 --> 00:02:28,670
Or if an object is acquiring any resources, it should release the results.

31
00:02:28,740 --> 00:02:30,310
So heat memory is a resource.

32
00:02:30,690 --> 00:02:36,720
So when we said new in the constructor, then we should say delete indestructible.

33
00:02:37,050 --> 00:02:40,600
Delete this a but it is an ugly.

34
00:02:40,680 --> 00:02:45,910
So OK, mention the subscript operator for this what we need.

35
00:02:46,260 --> 00:02:50,990
So I have finished with constructor and district for Dinello.

36
00:02:53,170 --> 00:02:58,510
Now I have to pick up the next function, but before picking up the mess function, I will explain something

37
00:02:58,510 --> 00:02:59,750
about other matters.

38
00:03:00,520 --> 00:03:01,950
If this is triangle of.

39
00:03:02,440 --> 00:03:03,750
Then what will be the change?

40
00:03:04,540 --> 00:03:07,470
Definitely the class name and the construct name will differ.

41
00:03:07,480 --> 00:03:09,740
But apart from that, what will be the change in the code?

42
00:03:10,210 --> 00:03:15,730
See, here we are creating an array for a diagonal matrix that is equal to size.

43
00:03:15,730 --> 00:03:21,940
And what in the lower triangle of Matrix, what will be the size of an Audi?

44
00:03:22,330 --> 00:03:25,660
The size of another is equal to number of non-zero elements.

45
00:03:25,660 --> 00:03:28,000
So the non-zero elements are how many?

46
00:03:28,150 --> 00:03:31,640
And in the end, plus one, why do these elements are there?

47
00:03:31,960 --> 00:03:38,530
So here, if I'm writing the code for lower triangular matrix, then here I should write and into and

48
00:03:38,530 --> 00:03:39,830
list one by two.

49
00:03:41,860 --> 00:03:42,880
This is the change.

50
00:03:43,540 --> 00:03:46,240
Not depending on the matrix that you are writing.

51
00:03:46,570 --> 00:03:50,580
You have to look at the space or use that formula for the space.

52
00:03:51,370 --> 00:03:57,250
That's a simple plus name is related to the matrix and this will change.

53
00:03:57,250 --> 00:03:58,060
And the distractor.

54
00:03:58,060 --> 00:03:59,650
You must write dimension.

55
00:03:59,650 --> 00:04:01,060
You must take everything.

56
00:04:01,060 --> 00:04:03,660
Assam's only here the formula will change.

57
00:04:04,330 --> 00:04:10,210
Now you going a space sufficient for storing all non-zero elements from a lower triangular or upper

58
00:04:10,210 --> 00:04:13,930
triangular autopilots or diagonal animatics.

59
00:04:14,650 --> 00:04:18,200
So you give the size required for storing all of these elements.

60
00:04:18,240 --> 00:04:20,019
So this is the size.

61
00:04:20,019 --> 00:04:23,230
Next I have to implement create function.

62
00:04:23,230 --> 00:04:28,420
So I will pick up, create function, I will remove this and implement, create function for create

63
00:04:28,420 --> 00:04:38,020
function, return type is void and the class name diagonal and then the function name create something

64
00:04:38,020 --> 00:04:41,560
that creates reading all non-zero elements and filling them in the.

65
00:04:43,650 --> 00:04:49,500
But actually, it is for diagonal matrix, diagrammatic somewhat, it is three, two or three, then

66
00:04:49,500 --> 00:04:56,460
this will have some element here, like six and zero zero zero five zero zero zero nine.

67
00:04:56,850 --> 00:04:58,380
So only diagonal elements are there.

68
00:04:58,590 --> 00:04:59,770
What about the rest of them?

69
00:04:59,790 --> 00:05:00,460
They are zero.

70
00:05:00,720 --> 00:05:02,420
Shall we read those also?

71
00:05:03,090 --> 00:05:10,050
So let us read it just like a matrix, a two dimensional matrix and store only those which are known

72
00:05:10,050 --> 00:05:11,120
to the rest of them.

73
00:05:11,130 --> 00:05:12,510
We know they are zero only.

74
00:05:12,870 --> 00:05:14,880
Just ignore them, ignore them and ignore them.

75
00:05:15,750 --> 00:05:19,980
So I will write down the code for reading this entire matics, the sentence.

76
00:05:20,610 --> 00:05:31,380
So for that I need to look for I assign one I use less than equal to and I miss living on words.

77
00:05:31,530 --> 00:05:34,580
She usually in four Luppi start from zero onwards.

78
00:05:34,590 --> 00:05:40,560
But if you remember the maximisers we have taken them from one on what's right for animatics.

79
00:05:40,830 --> 00:05:42,780
We have started Denis's from Ellenwood.

80
00:05:42,790 --> 00:05:45,690
So that's the reason I'm starting from one then.

81
00:05:46,820 --> 00:05:53,780
Next, look for Go Find One J.s less than equal to n g plus plus.

82
00:05:55,820 --> 00:06:01,720
Then read the very value in some variable see in let us take it in some variables.

83
00:06:02,260 --> 00:06:03,760
So I need one variable here.

84
00:06:05,120 --> 00:06:11,440
Know this value will be non-zero only if I are equal otherwise.

85
00:06:12,720 --> 00:06:16,620
It is a zero, so I should check the condition that if.

86
00:06:18,010 --> 00:06:26,680
I is equal to G, then an eighth of a minus one, so this is the formula I'm minus one, is the formula

87
00:06:27,280 --> 00:06:31,990
assigned X, otherwise don't assign anything, that's all.

88
00:06:32,260 --> 00:06:33,640
So this is all I have written.

89
00:06:34,120 --> 00:06:35,470
See one thing here.

90
00:06:36,560 --> 00:06:44,690
I am trying to take all the elements of a diagonal matrix zeros as well as non zeros, so I want the

91
00:06:44,690 --> 00:06:49,960
user of my program to feel as if he's entering into a complete matrix.

92
00:06:49,970 --> 00:06:56,630
All the elements my programmers are deciding whether to store or not to store by checking the condition

93
00:06:56,630 --> 00:07:02,510
that if I could then store the elements otherwise I don't store, just ignore it if value, but don't

94
00:07:02,510 --> 00:07:06,430
store because the other one is that this is all I have from the procedure.

95
00:07:07,040 --> 00:07:13,610
If you feel that, no, we should take only non-zero elements, then just using single for loop, you

96
00:07:13,610 --> 00:07:16,300
can read all non-zero elements from one to.

97
00:07:16,700 --> 00:07:21,260
You can read all the elements so you don't need to follow up and you don't have to check the condition,

98
00:07:22,280 --> 00:07:24,500
simply take only non-zero elements.

99
00:07:24,650 --> 00:07:25,730
So that is your choice.

100
00:07:25,730 --> 00:07:29,290
While writing the program, I will write the code only for reading non-zero elements.

101
00:07:29,300 --> 00:07:29,720
And you.

102
00:07:31,500 --> 00:07:37,040
But here, the fearless one is entering the complete max, then you will be thinking that live on Trent

103
00:07:37,140 --> 00:07:37,800
Xeros also.

104
00:07:37,830 --> 00:07:39,120
Yes, that's what I'm saying.

105
00:07:39,450 --> 00:07:45,060
If I think that I should not waste the time of the user, let us take only known Zeitels.

106
00:07:45,270 --> 00:07:49,650
Now, with this I can explain you other madrassahs easily.

107
00:07:50,100 --> 00:07:54,930
If this create function is followed Tripler, then everything will be same.

108
00:07:55,380 --> 00:07:56,760
No will be non-zero.

109
00:07:56,910 --> 00:08:01,350
Element will be only if I is greater than adequate equal attention.

110
00:08:01,770 --> 00:08:08,640
So only see I get an equal digit and here I should store the element of error in the formula.

111
00:08:08,760 --> 00:08:17,460
And what is the formula for a lower triangular matics ruminative formula that is I into ie minus one

112
00:08:17,610 --> 00:08:21,810
by two plus G minus one assigned X.

113
00:08:22,200 --> 00:08:24,240
So I'm using the formula and storing that.

114
00:08:26,660 --> 00:08:29,410
This is for all major formula, fluid dynamics.

115
00:08:31,000 --> 00:08:31,640
That's it.

116
00:08:31,660 --> 00:08:36,970
So the all the same, only the formula of this court became follower Tandler Mattocks.

117
00:08:38,100 --> 00:08:40,320
Whether its size will be larger or not, this one.

118
00:08:41,360 --> 00:08:47,260
Then change the formula, it becomes a column, so only at some places you have to change the formula,

119
00:08:47,270 --> 00:08:50,030
otherwise rest of the code will be similarly.

120
00:08:51,930 --> 00:08:57,240
No events and functions are simple function just for setting the value and getting the similar approach

121
00:08:57,240 --> 00:09:00,840
so that when coding we will see the display function.

122
00:09:01,440 --> 00:09:05,670
So for displaying, I have I want to display it as a matics only.

123
00:09:07,160 --> 00:09:10,090
So, again, for all the elements of somatics, we need to follow.

124
00:09:11,090 --> 00:09:12,510
So I'll have to follow ups.

125
00:09:13,070 --> 00:09:17,060
So let us change the name of this function as display display.

126
00:09:20,230 --> 00:09:23,890
I do not need this variable that removed this one.

127
00:09:24,860 --> 00:09:34,300
Then to follow up, what is the purpose reading so far that I will associate so far, diplomatics CEO.

128
00:09:35,870 --> 00:09:40,340
AOF am minus one, then if I he goes to jail.

129
00:09:41,240 --> 00:09:49,400
Otherwise, if I is not equal to Jay then else C out zero give some space also.

130
00:09:52,060 --> 00:10:00,640
Here also, I should give some space so so that space is given in between every element that said that

131
00:10:00,640 --> 00:10:04,540
after the first for Loop Sesi out and then.

132
00:10:07,350 --> 00:10:15,420
And of two follow ups and the functional sow discord will bring to the markets and it will bring a complete

133
00:10:15,420 --> 00:10:21,120
mattocks with all the elements Xeros as well as Montero's, everything that will be displayed noggin.

134
00:10:22,520 --> 00:10:27,440
Instead of showing just non-zero element, I'm showing Zeitels also that is coming as a print on the

135
00:10:27,440 --> 00:10:27,870
screen.

136
00:10:29,060 --> 00:10:35,000
This is for diagonal diplomatics, the notable lower triangular ventromedial formulas used.

137
00:10:35,690 --> 00:10:37,910
Everything will be same here.

138
00:10:37,940 --> 00:10:40,010
The condition will be I.

139
00:10:41,980 --> 00:10:50,220
If I is greater than ecology, the elements are non-zero, then footprinting, I have to use the Formula

140
00:10:50,530 --> 00:10:56,830
C E of how to retrieve the elements, how the elements are mapped.

141
00:10:57,200 --> 00:11:05,160
That is, I do a minus one by two plus minus one and some space after the element.

142
00:11:06,380 --> 00:11:11,050
All know the score became a code for lower triangular matics.

143
00:11:11,290 --> 00:11:14,140
So we have implemented all the function except get set.

144
00:11:14,140 --> 00:11:19,450
I did not show that so that the simple function that storing a value and retrieving a value, we will

145
00:11:19,450 --> 00:11:20,870
see that while writing the code.

146
00:11:21,310 --> 00:11:27,540
So in the following variables, you will find the classes and the main function for all the madrassahs

147
00:11:27,550 --> 00:11:28,180
one by one.

148
00:11:28,450 --> 00:11:34,000
Since the following videos, I'll be showing a complete program, a menu driven program which allow

149
00:11:34,000 --> 00:11:41,420
us to access all these options, either create or display or set the value or get the value of a representative

150
00:11:41,530 --> 00:11:44,040
and programs for each and every mattocks.

151
00:11:44,050 --> 00:11:49,490
And you can see that all those programs look so similar, only the formula will be changed.

152
00:11:50,050 --> 00:11:55,400
So I have given an overview of all the classes or the programs that I'm going to show you.

153
00:11:55,690 --> 00:11:56,270
So that's on.

