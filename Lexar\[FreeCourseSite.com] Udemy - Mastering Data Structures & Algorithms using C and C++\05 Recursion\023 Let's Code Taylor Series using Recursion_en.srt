1
00:00:00,930 --> 00:00:06,930
In this video, I will implement a function for LAPD that is finding people that we have already seen

2
00:00:06,930 --> 00:00:07,880
in the previous video.

3
00:00:08,250 --> 00:00:16,400
So just I will write a function and just I will give you a demo of that function function name.

4
00:00:16,410 --> 00:00:21,930
I will call it as E but written I will take it as double while explaining on board.

5
00:00:21,930 --> 00:00:28,350
I have used integer most of the places, but actually the result is in decimal point, so I should either

6
00:00:28,350 --> 00:00:29,150
float or double.

7
00:00:29,760 --> 00:00:39,690
So I'm taking double here and the parameters are X and and C, X is the power of E and, and how many

8
00:00:39,690 --> 00:00:40,970
times we want.

9
00:00:41,310 --> 00:00:47,550
So the number of times we take, we get more position than inside the function.

10
00:00:47,550 --> 00:00:51,270
We are using static variables which I will declare them as double.

11
00:00:52,710 --> 00:00:55,650
That is B assign one as well as F assign one.

12
00:00:58,160 --> 00:01:03,110
Then if and if zero eight on one.

13
00:01:07,200 --> 00:01:15,140
We need one more variable are for storing, there is also a little as double and that are assigned E,

14
00:01:17,520 --> 00:01:28,170
X, comma and the minus one, and every time B is multiplied by X and F is multiplied by N, that is

15
00:01:28,170 --> 00:01:38,520
for denominator and it is a factorial and return R plus P by F, so that's all the function is ready

16
00:01:38,520 --> 00:01:43,320
then for the main function, I will call it directly.

17
00:01:43,350 --> 00:01:54,810
I will write the result inside the print def call function one comma then so apower one it should give

18
00:01:54,810 --> 00:01:58,980
the other people or one and the number of items that we want are ten.

19
00:01:59,790 --> 00:02:04,900
If we want more position than this ten instead of ten I should give more larger number.

20
00:02:05,880 --> 00:02:08,460
Let us run and see what is the output of this function.

21
00:02:09,690 --> 00:02:13,410
Eastport one s two point seven one eight two eight two.

22
00:02:13,890 --> 00:02:15,590
Let us check it on the calculator.

23
00:02:15,960 --> 00:02:18,060
One Epocrates.

24
00:02:18,930 --> 00:02:20,040
Yes, good point.

25
00:02:20,040 --> 00:02:22,490
Seven one eight two eight one.

26
00:02:23,280 --> 00:02:24,710
So I got the correct puzzle.

27
00:02:25,440 --> 00:02:27,450
Let us drive it another value.

28
00:02:27,570 --> 00:02:29,970
I'll give the value three and run it.

29
00:02:33,180 --> 00:02:37,800
So the answer is thirty point all seven nine six six five.

30
00:02:39,460 --> 00:02:47,080
Let us try it here on the calculator, three purex twenty point or eight five five seven.

31
00:02:48,590 --> 00:02:51,240
And here we got all seven nine.

32
00:02:51,580 --> 00:02:53,410
So it is less little less than that.

33
00:02:53,420 --> 00:02:59,920
So if you want more accurate value than increase the value from ten to 15 or 20 so you can give us another

34
00:02:59,920 --> 00:03:00,340
number.

35
00:03:01,510 --> 00:03:02,740
Let us check for four.

36
00:03:06,410 --> 00:03:08,100
At fifty four point forty four.

37
00:03:08,720 --> 00:03:16,380
And here on the calculator for politics, fifty point fifty four point five nine.

38
00:03:16,850 --> 00:03:19,010
So this is little less than that.

39
00:03:19,400 --> 00:03:23,080
If I increase the number of times instead of 10, I will give 15.

40
00:03:23,090 --> 00:03:24,800
Let us see what result we get.

41
00:03:26,270 --> 00:03:31,850
Yes, fifty four point five nine seven fifty four point five nine eight.

42
00:03:32,000 --> 00:03:34,340
So still, our value is little less than that.

43
00:03:34,340 --> 00:03:38,380
So I can still increase the number of items to get more precise value.

44
00:03:41,390 --> 00:03:45,040
So that's all this function is finding it X.

45
00:03:45,050 --> 00:03:50,540
That is the result of Taylor feeds that following this video.

