1
00:00:00,330 --> 00:00:08,490
Now, let us look at a function for counting the number of nodes and interests that is moving the length

2
00:00:08,640 --> 00:00:17,100
of an interest, if it the one, two, three, four, five, five nodes I have so far, this we have

3
00:00:17,100 --> 00:00:24,170
to traverse through the linked list until we reach that last node and every time we should go on counting.

4
00:00:24,480 --> 00:00:27,570
So with this way we can know the number of nodes in the link lists.

5
00:00:28,110 --> 00:00:29,400
So I'll show you the working.

6
00:00:29,790 --> 00:00:34,110
I should have some variability, a count and that count is initially zero.

7
00:00:34,560 --> 00:00:39,600
Then I should have a pointer pointing on this first node that as a student.

8
00:00:39,750 --> 00:00:42,410
So this B will be doing that first step.

9
00:00:44,150 --> 00:00:51,310
Increment count, so add one to it and move to the next and all that again, do the same thing.

10
00:00:51,620 --> 00:00:55,580
I'd want to count and move, be the next node.

11
00:00:56,700 --> 00:01:05,310
I want to go out and move to the next node, same way I'd want to count, move to next node.

12
00:01:07,050 --> 00:01:11,460
Had one to count and move, movie to make north.

13
00:01:11,490 --> 00:01:13,900
So this next N zero, so it is none.

14
00:01:14,280 --> 00:01:16,650
So when P became null, stop that.

15
00:01:17,310 --> 00:01:18,690
So this is how the procedure is.

16
00:01:18,690 --> 00:01:19,700
And it is very simple.

17
00:01:19,710 --> 00:01:24,060
So we have to traverse through the link lists and go on incrementing count.

18
00:01:24,390 --> 00:01:26,280
So for this, I will write on a function.

19
00:01:27,400 --> 00:01:39,940
Let us make it our function integer count, which takes parameter that is struck Naude pointer p then

20
00:01:39,940 --> 00:01:44,740
for counting I need some variable, let us say C which is having value little.

21
00:01:46,420 --> 00:01:54,250
Then and this is key variable, I'll be incrementing, I'll be counting it, so every time C++ counting

22
00:01:54,580 --> 00:02:01,630
for incrementing disvalue and move paedo next more will be assigned these next.

23
00:02:03,480 --> 00:02:09,810
And this was a year I have to go on repeating it, so this should be written in the while loop, so

24
00:02:09,810 --> 00:02:11,250
why and what should be the condition?

25
00:02:11,610 --> 00:02:13,770
I should stop and become someone.

26
00:02:13,950 --> 00:02:20,390
So I should continue when peace not equal to so be is not equal to zero.

27
00:02:20,850 --> 00:02:22,740
So nothing can also be written at zero.

28
00:02:23,700 --> 00:02:30,240
Then at the end of loop we will have the number of n n variable C so written c.

29
00:02:32,510 --> 00:02:33,020
That's all.

30
00:02:33,530 --> 00:02:40,040
Now, let us analyze the time taken by this function, see this function as it travels through all the

31
00:02:40,040 --> 00:02:41,710
nodes and simply counting.

32
00:02:42,050 --> 00:02:47,030
So if I remove this line, then this is the same as a travel thing, which we have seen in the previous

33
00:02:47,030 --> 00:02:47,400
video.

34
00:02:47,840 --> 00:02:51,460
So how much time it takes for traversing all the nodes in a Linkous?

35
00:02:51,740 --> 00:02:53,290
It depends on number of nodes.

36
00:02:53,390 --> 00:02:57,890
So if nodes are there, then we say time is order of N.

37
00:02:59,430 --> 00:03:05,160
Time taken is not often so mostly the functions that are written up on Linklaters, they will be taking

38
00:03:05,160 --> 00:03:08,100
all the time, so we may not be analyzing it every time.

39
00:03:08,460 --> 00:03:10,980
So I'll just mention the time since out of.

40
00:03:11,010 --> 00:03:14,510
And then what about the space taken by this function?

41
00:03:14,910 --> 00:03:19,680
The space complexity depends on the number of variables or determine which it is having.

42
00:03:20,010 --> 00:03:21,320
Like this is one point.

43
00:03:22,740 --> 00:03:29,370
An integer, there's only two things out there, so a pointer takes, let us say, two bytes, an integer

44
00:03:29,370 --> 00:03:33,510
also takes a two whites assume this total for bytes of memory is ticking.

45
00:03:33,750 --> 00:03:41,250
So four bytes means for this constant time and and space is constant.

46
00:03:41,850 --> 00:03:44,170
So we have finished the iterative version of Count.

47
00:03:44,520 --> 00:03:50,040
Now let us write a recursive function for counting, so I will write the recursive function.

48
00:03:50,050 --> 00:03:51,990
Then I will explain how it works.

49
00:03:52,560 --> 00:03:56,700
I will write the recursive function, function, name and take the last count.

50
00:03:56,700 --> 00:03:59,310
Only then it is taking a pointer.

51
00:03:59,310 --> 00:04:02,820
So we will be calling it by passing first n pointer.

52
00:04:02,820 --> 00:04:05,850
That is to handle what it should do.

53
00:04:05,850 --> 00:04:13,470
If he's not, it should count one for each node and if it is another then it should count zero five

54
00:04:13,470 --> 00:04:14,460
alert on the condition.

55
00:04:16,269 --> 00:04:22,470
If peace equals to null, then return zero.

56
00:04:23,510 --> 00:04:25,810
So if there is no, no, then return zero.

57
00:04:26,900 --> 00:04:36,410
Otherwise, if there is a A. then return, count itself for peace the next.

58
00:04:38,640 --> 00:04:47,770
And also add one that's on, there's the function, simple steps are fine if a B is null and void,

59
00:04:47,770 --> 00:04:49,060
return zero, there is no note.

60
00:04:49,080 --> 00:04:53,430
If there is a node, then call for next node and add one.

61
00:04:54,600 --> 00:05:01,050
So let us quickly trace this one and see how it is working when we call this function for first to know

62
00:05:01,060 --> 00:05:05,690
that is two hundred, I'm writing see for Tom then what it does.

63
00:05:05,730 --> 00:05:06,880
This is not none.

64
00:05:06,900 --> 00:05:08,900
So it will always go into the L spot.

65
00:05:09,180 --> 00:05:09,930
It is not known.

66
00:05:09,960 --> 00:05:14,190
So it goes into the El Paso for almost all the nautical been el sparklingly.

67
00:05:14,190 --> 00:05:17,210
So I will simply write down the steps one by one.

68
00:05:17,940 --> 00:05:25,310
So call itself for the next node that is 210 and add one when to add one.

69
00:05:25,740 --> 00:05:29,990
Not now, because unless this answer is known we cannot add one to it.

70
00:05:30,240 --> 00:05:31,850
So one will not be added right now.

71
00:05:32,730 --> 00:05:37,790
The next call C for next node that is 270 and plus one.

72
00:05:37,800 --> 00:05:39,170
This will be done afterwards.

73
00:05:39,180 --> 00:05:48,840
The next call C for 300 and add one adding one will be done afterwards C of 350 and add one adding one

74
00:05:48,840 --> 00:05:49,850
will be done afterwards.

75
00:05:50,130 --> 00:05:54,720
So for 350, C all four zero plus one adding one will be done afterwards.

76
00:05:54,720 --> 00:06:00,680
So you can see that in all these steps it has entered into the L spot only now P became null.

77
00:06:00,780 --> 00:06:01,710
So please no.

78
00:06:01,830 --> 00:06:05,120
So when P is what it returns, it returns to zero.

79
00:06:05,670 --> 00:06:07,300
So this time this is zero.

80
00:06:07,560 --> 00:06:10,040
So the result of this function is zero.

81
00:06:10,260 --> 00:06:11,430
So this will be zero.

82
00:06:11,430 --> 00:06:15,810
Plus one is one and the result of this function is one.

83
00:06:16,740 --> 00:06:18,480
So this will be one plus one.

84
00:06:18,480 --> 00:06:19,220
That is two.

85
00:06:19,470 --> 00:06:21,300
So the result of this function is two.

86
00:06:21,570 --> 00:06:24,720
So this is two plus one is three.

87
00:06:25,050 --> 00:06:27,300
The result of this function is three seven.

88
00:06:27,300 --> 00:06:28,370
This function is called.

89
00:06:28,380 --> 00:06:32,280
So whatever the result that we get return value will be the value of that function only.

90
00:06:32,580 --> 00:06:35,640
So this is a three, so this is three plus one.

91
00:06:35,650 --> 00:06:36,620
So it will be four.

92
00:06:36,630 --> 00:06:41,040
So the result of this function is four, so four plus one as a five.

93
00:06:41,280 --> 00:06:43,330
So the result of this function is five.

94
00:06:44,100 --> 00:06:45,440
So this is how it works.

95
00:06:46,080 --> 00:06:50,100
So we can observe that addition is being done at returning time.

96
00:06:50,340 --> 00:06:52,050
It is not done at calling time.

97
00:06:53,820 --> 00:07:01,120
No, I'll make one small change and show you see, sometimes students believe that if I write a written

98
00:07:01,410 --> 00:07:09,660
one plus government instead of writing plus one afterwards, if I die first, then veloute I had while

99
00:07:09,660 --> 00:07:10,160
calling.

100
00:07:10,470 --> 00:07:12,950
No, see this one for this.

101
00:07:12,960 --> 00:07:20,720
Also see of two hundred one plus plus with what this function call C of 210.

102
00:07:20,970 --> 00:07:23,710
So one will be added with the result of 210.

103
00:07:23,940 --> 00:07:27,240
So unless the result of 210 is known, addition cannot be done.

104
00:07:27,690 --> 00:07:32,250
So still this addition has to be done at written in time only.

105
00:07:33,600 --> 00:07:35,240
So I have done the tracing.

106
00:07:35,460 --> 00:07:41,100
Now, if you see the time taken by this function as one, two, three, four, five, six calls, endless

107
00:07:41,100 --> 00:07:47,840
phone calls, so this order of land and the space definitely uses the stack because it is recursion.

108
00:07:47,850 --> 00:07:51,750
We have already seen in previous video how the stack is constructed.

109
00:07:52,050 --> 00:07:55,770
So the space complexities and plus one that is Bodrov.

110
00:07:55,770 --> 00:08:01,320
And so the time and space for this function is order and only.

111
00:08:02,280 --> 00:08:10,230
So this function is costly in terms of space, in terms of time are the same as the iterative function

112
00:08:10,230 --> 00:08:11,190
that is using loop.

113
00:08:11,190 --> 00:08:14,820
We have written the function of the same as that, but in terms of space, it is costly.

114
00:08:15,670 --> 00:08:21,610
Now I will make some changes in this function and show you what are the other ways I can write.

115
00:08:21,620 --> 00:08:22,730
Don't same function.

116
00:08:23,430 --> 00:08:25,560
Let us see what are the changes we can do in the code.

117
00:08:25,560 --> 00:08:27,570
And the results remain the same.

118
00:08:27,570 --> 00:08:29,220
Only see inside.

119
00:08:29,220 --> 00:08:33,840
If when you have a written statement, then if this is true, it will return so it will not go further.

120
00:08:34,440 --> 00:08:37,380
So events with or else also you can write it.

121
00:08:38,159 --> 00:08:40,650
Instead of writing this termination condition.

122
00:08:40,650 --> 00:08:42,090
I can write it in other way.

123
00:08:42,390 --> 00:08:54,420
Like if a B is not equal to none, that is not equal to zero then written one plus gonged be caps next

124
00:08:54,930 --> 00:08:58,920
ls return zero c here.

125
00:08:58,920 --> 00:09:01,620
I have written this condition first, so I was returning zero.

126
00:09:01,620 --> 00:09:02,820
Otherwise this was there.

127
00:09:03,090 --> 00:09:04,620
But now I have just changed the order.

128
00:09:04,770 --> 00:09:10,160
If he's not null and do this otherwise do this so you can write, you can change the rules.

129
00:09:11,220 --> 00:09:12,270
One more thing I'll show you.

130
00:09:12,810 --> 00:09:19,730
I like the function using some variable I will declare some variable local variable X having values

131
00:09:19,740 --> 00:09:25,860
even if B if Beemans B's not null.

132
00:09:26,430 --> 00:09:27,000
Remember?

133
00:09:27,010 --> 00:09:31,890
And one of the video I have shown you, this one, this is this will be true.

134
00:09:31,890 --> 00:09:36,310
If a piece is pointing on some node, F.P. is another, then it will be false.

135
00:09:36,660 --> 00:09:40,350
So this is for P pointing on some note that is not known.

136
00:09:40,830 --> 00:09:47,630
If it is not null, then exercise called count the next node.

137
00:09:48,450 --> 00:09:55,520
So we'll call this function and get the result in X, then we will return X plus one.

138
00:09:56,520 --> 00:10:00,300
So I have introduced one variable C earlier in the previous one.

139
00:10:00,330 --> 00:10:02,910
This must count B's next then plus one.

140
00:10:03,240 --> 00:10:08,240
So instead of writing count here, I have taken it in some variable then that variable I'm using here

141
00:10:08,250 --> 00:10:09,920
so you can introduce a variable also.

142
00:10:11,760 --> 00:10:12,300
Next.

143
00:10:14,080 --> 00:10:19,730
Then else, if it is none, then return zero, so I will return zero.

144
00:10:20,180 --> 00:10:25,470
So see if it is coming in Sparkman's X's remaining Z only.

145
00:10:25,690 --> 00:10:27,430
So even I consider it an X.

146
00:10:27,760 --> 00:10:28,990
This means a zero only.

147
00:10:30,370 --> 00:10:32,620
So you can introduce a variable also.

