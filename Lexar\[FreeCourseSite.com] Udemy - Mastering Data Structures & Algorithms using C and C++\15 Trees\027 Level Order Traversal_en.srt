1
00:00:00,630 --> 00:00:06,620
The topic is level order traversal of binary tree, here is an example of banditry.

2
00:00:06,630 --> 00:00:15,240
Already we have learned about level order again and repeated level order means traversing all the waters

3
00:00:15,420 --> 00:00:17,220
of our tree level.

4
00:00:18,330 --> 00:00:19,790
So first level is eight.

5
00:00:20,220 --> 00:00:27,810
So if I write the output here, first level is eight, then second level, the first value is three.

6
00:00:27,810 --> 00:00:28,860
Next that is nine.

7
00:00:29,250 --> 00:00:30,540
So three to nine.

8
00:00:31,470 --> 00:00:34,260
The next level code, level seven six four.

9
00:00:37,850 --> 00:00:39,770
The last level, five and two.

10
00:00:42,510 --> 00:00:48,330
This is the output of level order, so how actually this is done, first visit Ruth.

11
00:00:49,610 --> 00:00:57,890
Grootes visited the children, so the first child left child, then right and left, right, then children

12
00:00:57,890 --> 00:01:04,660
of left, China, seven children of China, then its children.

13
00:01:04,910 --> 00:01:06,960
So first left is only left, is there.

14
00:01:07,310 --> 00:01:08,840
So we went on five.

15
00:01:09,130 --> 00:01:10,310
Rachel is not there.

16
00:01:10,670 --> 00:01:13,310
Then the children orphaned nine children.

17
00:01:13,340 --> 00:01:14,530
That is just one sixth.

18
00:01:15,020 --> 00:01:16,440
So sixth child is only two.

19
00:01:16,550 --> 00:01:17,740
Only two is visited.

20
00:01:17,990 --> 00:01:20,000
So this procedure requires a queue.

21
00:01:20,330 --> 00:01:27,370
So I take an example queue and again I will try and show you how you can help us in traversing in level

22
00:01:27,380 --> 00:01:27,730
order.

23
00:01:28,160 --> 00:01:29,900
So I have a queue ready here.

24
00:01:30,260 --> 00:01:32,070
Let us traverse it once again.

25
00:01:32,090 --> 00:01:38,180
So for that I will take a pointer fee first visit route visit route.

26
00:01:38,300 --> 00:01:42,560
So that is eight and insert the address of eight in the queue.

27
00:01:42,890 --> 00:01:44,450
So address of eight.

28
00:01:45,920 --> 00:01:50,940
So this was the first step or initial step, not a step is repeating step.

29
00:01:51,620 --> 00:01:53,180
Let us look at repeating step.

30
00:01:54,530 --> 00:01:59,180
They got an address from the Q and give it to me, so that is the address of eight only.

31
00:01:59,210 --> 00:02:02,090
So people point on eight again on eight.

32
00:02:03,220 --> 00:02:06,670
Then the have left China three.

33
00:02:08,580 --> 00:02:11,190
Insert the address of a three into Q.

34
00:02:14,380 --> 00:02:21,810
Then visit, that's right, child nine instead of nine in the queue, I'll just write nine, that is

35
00:02:21,810 --> 00:02:22,680
the address of nine.

36
00:02:23,020 --> 00:02:27,970
So using B we have visited left China as well as right now is over.

37
00:02:28,000 --> 00:02:28,960
Workers work on.

38
00:02:30,270 --> 00:02:37,000
These are the steps taken that address from the Q was it left China inserted sentence, then Rachel

39
00:02:37,410 --> 00:02:38,550
inserted situs.

40
00:02:39,690 --> 00:02:45,270
No, continue repeating the steps, they got an address from the Q so the next address is of three,

41
00:02:45,510 --> 00:02:52,800
so take a pointer Pierpont decorator's visit Left Child Left Child and insert the address of Node seven

42
00:02:52,800 --> 00:02:53,640
into the Q.

43
00:02:54,480 --> 00:02:56,770
Then with visit right chain, there is no right child.

44
00:02:56,790 --> 00:02:59,910
If it is not, then we cannot visit so it is completed.

45
00:03:00,630 --> 00:03:04,020
Then take our next address from the Q that is nine.

46
00:03:05,300 --> 00:03:16,500
This is the new normal visits with child six adults of six in Tokyo, then visit my child for address

47
00:03:16,500 --> 00:03:26,880
of four in Tokyo because older then the next address from the Q and make a point on that one visit left

48
00:03:26,880 --> 00:03:29,250
child five which is the address.

49
00:03:30,360 --> 00:03:33,240
A fly in Cucu then, right?

50
00:03:33,540 --> 00:03:35,070
There is no right to live it.

51
00:03:36,960 --> 00:03:42,430
Then they go next Exodus six six apiece, pointing on six left, child left child, right child.

52
00:03:42,460 --> 00:03:47,760
Yes, two incidents of two will become this over.

53
00:03:48,140 --> 00:03:52,000
Take Exodus four for left child have child right.

54
00:03:52,250 --> 00:03:53,220
All right then.

55
00:03:53,220 --> 00:03:53,610
Come on.

56
00:03:53,610 --> 00:03:56,100
Five five is not having any children.

57
00:03:56,370 --> 00:04:01,190
Then they go to and move to to visit left and right.

58
00:04:01,190 --> 00:04:01,700
Child of two.

59
00:04:01,830 --> 00:04:03,900
There are no left and right right now.

60
00:04:03,900 --> 00:04:04,920
Cuba is empty.

61
00:04:05,730 --> 00:04:06,450
Give is empty.

62
00:04:06,750 --> 00:04:08,340
Once the queue becomes empty.

63
00:04:08,340 --> 00:04:09,000
Stop.

64
00:04:11,000 --> 00:04:13,710
So take an visit left child, right?

65
00:04:13,980 --> 00:04:19,399
That's what the procedure is, very simple, but initially we have first visit that rule, then we have

66
00:04:19,670 --> 00:04:21,410
inserted didas of fruit.

67
00:04:21,420 --> 00:04:24,410
And let me write on a procedure for this one.

68
00:04:25,530 --> 00:04:25,730
Yeah.

69
00:04:26,150 --> 00:04:32,510
Procedure here is really we have taken the Cucu should be of type addresses of known.

70
00:04:34,050 --> 00:04:41,140
It's not in your character Q against type Q So the data type of an array should be Norder, the type

71
00:04:41,940 --> 00:04:43,830
one writing the program, I will show you that.

72
00:04:44,250 --> 00:04:46,650
And also the skill should be initialized.

73
00:04:46,650 --> 00:04:53,490
So initializing that front pointer and they point out that we have to do next, first step I will perform

74
00:04:53,920 --> 00:04:56,780
might be the pointer that is it is en route.

75
00:04:56,790 --> 00:04:57,010
Right.

76
00:04:57,030 --> 00:05:00,930
This is the route that first of all, print the date off route.

77
00:05:01,440 --> 00:05:03,540
That piece of data will be pointing on route.

78
00:05:03,540 --> 00:05:04,610
So data is printed.

79
00:05:04,980 --> 00:05:10,860
Then insert the address of the P, P in the Q and Q the centers into.

80
00:05:11,970 --> 00:05:19,470
Now, the procedure is a repeating procedure, so I will write on loop while we have to stop when Cuba

81
00:05:19,470 --> 00:05:26,000
comes M.D. or not is empty cue means as long as Cuba is not allowed to continue, plus established,

82
00:05:26,010 --> 00:05:27,370
take orders from Cuba.

83
00:05:27,690 --> 00:05:34,170
So, Sadique, you take orders from Cuba and take it in point of P, then print left China.

84
00:05:34,210 --> 00:05:44,270
If it is there, if peace is there, then Princip's left China data and also insert the soft left child

85
00:05:44,280 --> 00:05:46,070
in Cuba like this.

86
00:05:46,080 --> 00:05:53,020
This is in the old data printed that is 3D printer and the address of trees inserted and Q and a Q.

87
00:05:54,020 --> 00:05:59,600
Similarly, check for the right, Charlie Rangel is there, then printed and inserted in the queue.

88
00:06:00,050 --> 00:06:02,180
That's it here piece, right?

89
00:06:02,840 --> 00:06:12,500
If it is not not print the data and insert this of that right into Q and the end of my loop, this prosecutor

90
00:06:12,560 --> 00:06:16,070
will be repeating and printing all the elements from three.

91
00:06:17,340 --> 00:06:20,860
So that's all this is in order procedure.

