1
00:00:00,960 --> 00:00:07,800
Let us try to function for converting infix expression to postfix expression, function, name I have

2
00:00:07,800 --> 00:00:15,360
given it and convert it will take a character type pointer to an array of characters, and its name

3
00:00:15,360 --> 00:00:15,930
is INFIX.

4
00:00:16,320 --> 00:00:22,710
So that example already I have given here, this is an array of a size 10 having that same expression

5
00:00:23,100 --> 00:00:24,180
and it's a string.

6
00:00:24,180 --> 00:00:31,130
So it is ending by and it is a string, so it is ending by null character that is less evil.

7
00:00:31,830 --> 00:00:32,659
So it's a string.

8
00:00:32,670 --> 00:00:40,560
So this is also a string on character point and it will convert and returns a character pointer.

9
00:00:40,650 --> 00:00:42,150
So that is postfix infiltrate.

10
00:00:43,390 --> 00:00:51,520
For this procedure, I need two more functions, one function as is operand, this function is to check

11
00:00:51,520 --> 00:00:54,430
whether a symbol is operand or not, if it is operating.

12
00:00:54,460 --> 00:00:55,330
We want results.

13
00:00:56,020 --> 00:00:56,630
That is one.

14
00:00:57,190 --> 00:00:58,660
So here I am checking the condition.

15
00:00:58,660 --> 00:01:01,770
If it is plus minus multiply, divide, then it is an operator.

16
00:01:01,790 --> 00:01:02,670
It's not an opening.

17
00:01:02,980 --> 00:01:05,750
So I'm returning WSDL, otherwise it's an opening.

18
00:01:05,900 --> 00:01:07,000
So that one.

19
00:01:08,210 --> 00:01:13,060
The next we need the president is also so if you remember, the president's US plus minus is having

20
00:01:13,060 --> 00:01:16,010
fun and multiplication division is having precedence, too.

21
00:01:16,280 --> 00:01:21,380
So I have a function for precedences, four plus minus return one for multiplication and division,

22
00:01:21,380 --> 00:01:24,200
return two for any other thing, return zero.

23
00:01:24,580 --> 00:01:28,410
And the other thing, if the stack is empty, nothing is there, then we should have the presidential

24
00:01:28,430 --> 00:01:28,790
zero.

25
00:01:29,270 --> 00:01:30,510
So that also have returned.

26
00:01:31,220 --> 00:01:33,200
So these two functions, I'll be using them.

27
00:01:33,200 --> 00:01:36,290
So the court will be very simple and easy to read.

28
00:01:37,100 --> 00:01:39,710
Now let's come back to the function inside the function.

29
00:01:39,710 --> 00:01:40,840
I need few things.

30
00:01:40,850 --> 00:01:42,680
First of all, I need a stack.

31
00:01:43,760 --> 00:01:44,720
I need a stack.

32
00:01:44,990 --> 00:01:45,650
Which type?

33
00:01:45,680 --> 00:01:46,940
Character type stack.

34
00:01:47,300 --> 00:01:49,610
So in parenthesis matching example.

35
00:01:49,610 --> 00:01:55,100
If you watch that video there, I have use a character type stack and also have initialized it.

36
00:01:55,670 --> 00:01:57,200
So same thing we should do here.

37
00:01:57,200 --> 00:02:01,040
I will not explain that I will directly declare a stack.

38
00:02:01,640 --> 00:02:07,250
So a structure type stack that is Estes's stack and that will be holding character.

39
00:02:07,260 --> 00:02:10,840
So it's a character type stack and also I will initialize it.

40
00:02:10,850 --> 00:02:13,330
So I would like to comment here that it is initialized.

41
00:02:14,060 --> 00:02:15,890
So I assume that it is also initialized.

42
00:02:15,890 --> 00:02:22,290
If your statements that you have to set the size of a stack and create an array and initialize stop

43
00:02:22,290 --> 00:02:28,280
pointer minus one or three statements required, I'm avoiding that so you can check it in parentheses

44
00:02:28,280 --> 00:02:29,570
matching the next thing.

45
00:02:29,570 --> 00:02:34,820
What I need is I need a postfix to store the result, of course.

46
00:02:34,980 --> 00:02:36,320
So what should be the size of this one?

47
00:02:36,590 --> 00:02:37,970
Same as infixes.

48
00:02:38,240 --> 00:02:43,640
So I will create an array equal to the size of infix here.

49
00:02:43,640 --> 00:02:49,820
I have taken character to point out that is postfix and also creating a new array from heap and size

50
00:02:49,820 --> 00:02:52,250
of that array is equal to string length.

51
00:02:52,610 --> 00:02:57,590
And as this is a string, we need one more space for null characters zero.

52
00:02:57,830 --> 00:02:59,170
So that's five plus one.

53
00:02:59,720 --> 00:03:05,570
Then I need a few variables for scanning Troedsson six expression and scanning through this postfix

54
00:03:05,570 --> 00:03:06,110
expression.

55
00:03:06,110 --> 00:03:12,610
So I need to integer variables that I n g so I have taken and both are initialized to zero.

56
00:03:12,710 --> 00:03:19,700
Now the procedure in the procedure I have to scan through complete infix expression from first Simbel

57
00:03:19,880 --> 00:03:22,750
till we reach the null character.

58
00:03:22,760 --> 00:03:24,890
So we have to go on scanning all of them.

59
00:03:25,310 --> 00:03:26,690
So shall I use.

60
00:03:27,890 --> 00:03:29,930
So I can use a for loop.

61
00:03:30,260 --> 00:03:36,380
So followable will taking me through all the symbols but in store for loop before using while loop reason.

62
00:03:36,380 --> 00:03:40,060
I will tell you afterwards I will not use for a loop and use Swindal.

63
00:03:40,460 --> 00:03:44,780
So how long that loop should execute till we reach the null character.

64
00:03:45,320 --> 00:03:52,850
While infix so far is not equal to null until you reach null continuum then what I have to do every

65
00:03:52,850 --> 00:03:53,200
time.

66
00:03:53,690 --> 00:03:59,730
So if you remember the procedure, we were checking every single Vollertsen operand or operator.

67
00:04:00,080 --> 00:04:02,600
So if it is an open we were adding into postfix.

68
00:04:02,720 --> 00:04:10,160
So that code I will write on C to check whether that symbol of infix expression, let us say symbol,

69
00:04:10,490 --> 00:04:14,930
is it an option and sending it to this function so this function returns true.

70
00:04:14,930 --> 00:04:19,490
If it is an option and if it is an option, then directly I should send it to postfix.

71
00:04:20,459 --> 00:04:27,720
So the symbol of an expression should be stored in postfix and index and both should be incremented.

72
00:04:28,940 --> 00:04:30,410
So copying that symbol here.

73
00:04:31,740 --> 00:04:37,860
If it is an opening, if it is not an option, then it is an operator, then I have to check the precedences,

74
00:04:38,040 --> 00:04:40,400
so else I will write on that part.

75
00:04:41,680 --> 00:04:49,660
Else, if it is an operator, then I have to check the precedents of symbol from infix expression and

76
00:04:49,660 --> 00:04:54,850
the precedence of a symbol and the topmost position of the stack.

77
00:04:56,100 --> 00:04:59,790
That's what we were doing, so I will ride on Necla and.

78
00:05:01,200 --> 00:05:08,310
If the presidents of infix expression is greater, then push into the stack otherwise pop up.

79
00:05:09,510 --> 00:05:12,760
I will ride on that board now here, I will read it out.

80
00:05:13,170 --> 00:05:19,950
If a president's party this is president's function of infix, a symbol.

81
00:05:21,890 --> 00:05:28,510
A fight infix, a fight, so I may be here or here, wherever I may be on this op ed or this anywhere

82
00:05:28,520 --> 00:05:29,470
else on the op ed.

83
00:05:30,620 --> 00:05:34,440
So the precedents of that operator is greater than Biery.

84
00:05:34,490 --> 00:05:38,870
Precedents of stacked up, stacked of function we already know.

85
00:05:38,900 --> 00:05:41,180
It gives the topmost symbol in the stack.

86
00:05:41,180 --> 00:05:44,300
And we know Steiger's containing only operators.

87
00:05:44,540 --> 00:05:46,250
So check their precedences.

88
00:05:46,730 --> 00:05:51,470
If the precedents of the symbol that is from the infix is greater, we know we will push it into the

89
00:05:51,470 --> 00:05:58,400
stack, push it and go stack s-t that infix six high and also move to the next symbol, Cymbeline infix.

90
00:05:58,400 --> 00:06:00,500
So if you have pushed this one, go to next symbol.

91
00:06:01,480 --> 00:06:10,130
Plus, plus, plus, plus, if it is not great, if this is smaller than that one or equal, then popular,

92
00:06:10,190 --> 00:06:15,830
the symbol purported symbol from the stack and add it to postfix generally post.

93
00:06:16,330 --> 00:06:18,090
So you remember the original postfix.

94
00:06:18,520 --> 00:06:24,660
So General Dakari and INFIX remain on the same symbol only don't move.

95
00:06:24,810 --> 00:06:30,760
I was done because it was pushed into the stack but here I is not done in Hellespont.

96
00:06:31,760 --> 00:06:35,040
So that I will not move always.

97
00:06:35,060 --> 00:06:36,420
It will not move always.

98
00:06:36,500 --> 00:06:43,400
That's why I have not used for Loop for Lippmann's every time to move to next time while I am using

99
00:06:43,400 --> 00:06:45,110
and I am moving it.

100
00:06:45,590 --> 00:06:49,430
If it is an option, goes to postfix move to next symbol.

101
00:06:49,840 --> 00:06:56,260
If it is a move to next, then if the symbol is posted to the stock, then move to next.

102
00:06:56,540 --> 00:07:01,930
Otherwise don't move because you are putting out the symbol from the stack and adding to postfix.

103
00:07:01,940 --> 00:07:07,990
Don't move beyond the same symbol and continue again and check again with the stacked top.

104
00:07:09,410 --> 00:07:10,960
That's the procedure.

105
00:07:12,010 --> 00:07:18,120
Now, the part that is remaining is whatever was remaining inside the strike, we have to copy that

106
00:07:18,130 --> 00:07:20,830
and postfix so that portion, there is no space.

107
00:07:21,220 --> 00:07:24,230
So I will remove this function and I will show it to you.

108
00:07:25,090 --> 00:07:30,280
So here I will write on the code for copying all the contents from the stack so the values from the

109
00:07:30,280 --> 00:07:33,490
stack can go on adding them to postfix until the stack is empty.

110
00:07:36,220 --> 00:07:39,270
Why not is empty.

111
00:07:42,010 --> 00:07:49,360
Stephen Stack, as long as the stack is not empty, means when it is empty, stop, if it is not empty,

112
00:07:49,360 --> 00:07:50,860
continue what to do.

113
00:07:51,220 --> 00:07:58,150
So inside postfix expression, pop out the element from the stack and added to postfix.

114
00:07:59,050 --> 00:08:00,830
So Stack will be containing operators.

115
00:08:00,850 --> 00:08:03,220
Those are Bob Dole and Arietta Postfix.

116
00:08:03,670 --> 00:08:05,830
So this loop will copy all of them.

117
00:08:06,020 --> 00:08:11,200
Then after copying on, I should add, another character at the end of Postfix, because postfix is

118
00:08:11,200 --> 00:08:14,280
also a string of statements, it must be terminated by another character.

119
00:08:14,680 --> 00:08:16,000
So add another character.

120
00:08:16,660 --> 00:08:20,480
That's all we got the result written that postfix NetSol.

121
00:08:21,490 --> 00:08:24,960
So finally Postfix is written and end of the function.

122
00:08:26,020 --> 00:08:29,070
So this function will convert infix postfix.

123
00:08:29,080 --> 00:08:29,530
That's all.

124
00:08:29,540 --> 00:08:30,310
This is the program.

125
00:08:30,490 --> 00:08:32,980
I will try to write and run the program and show you.

