1
00:00:00,240 --> 00:00:09,420
In this video, we will write a program for Free's using C++ for creating a tree, we require that a

2
00:00:09,420 --> 00:00:10,280
structure that is.

3
00:00:11,280 --> 00:00:13,990
We have already done that in our C language program.

4
00:00:14,610 --> 00:00:19,530
So here in C++ program also, we will use tool for creating a tree.

5
00:00:20,560 --> 00:00:25,510
And in this program, I will not be writing the code once again, but I will be copying the code from

6
00:00:25,510 --> 00:00:31,360
C language program, which we have already seen, because the procedure inside the function will be

7
00:00:31,360 --> 00:00:31,810
the same.

8
00:00:32,140 --> 00:00:37,470
Only the thing is we have to write a C++ class for a tree.

9
00:00:38,830 --> 00:00:45,870
So I will copy this code for Q This is a class for Q which we have already implemented in previous videos.

10
00:00:46,300 --> 00:00:51,010
I will use this class in my new project, so I'll copy this one.

11
00:00:52,660 --> 00:00:54,100
Let us start our new project.

12
00:01:03,130 --> 00:01:10,210
I'll call the Project Nim as a tree CBB and let us select the language type as C++.

13
00:01:13,860 --> 00:01:14,880
Project is really.

14
00:01:16,160 --> 00:01:22,460
Now, here in the project, I will insert a new fine, so I will say a new find and let it be a header

15
00:01:22,460 --> 00:01:23,860
files in this header file.

16
00:01:23,870 --> 00:01:26,060
I'll keep a clock for KUB.

17
00:01:27,430 --> 00:01:30,370
Petrify, let us call it as Q CBB.

18
00:01:32,410 --> 00:01:35,770
And this hydrofoil is the code that I have copied their.

19
00:01:37,160 --> 00:01:43,930
So this is a class for you, yeah, here this class is using still defense counsel because I did not

20
00:01:43,930 --> 00:01:44,870
change them to CNN.

21
00:01:45,580 --> 00:01:48,880
So this class needs a city on edge.

22
00:01:48,950 --> 00:01:53,060
So I will include a studio or has include.

23
00:01:56,480 --> 00:02:03,110
Now, this cute glass should be using a node for a tree, so I will define a class for node here itself,

24
00:02:03,470 --> 00:02:07,580
class for node, I'm saying no to this node will be for a tree.

25
00:02:08,060 --> 00:02:10,990
So this node will have three members.

26
00:02:11,030 --> 00:02:17,090
I'll make those numbers as public so that I can easily access them first as a pointed to a left child.

27
00:02:18,320 --> 00:02:26,960
And next is data that they want to store, and third one is a pointer to a right child, so our child

28
00:02:27,560 --> 00:02:30,570
on this glass is for naught.

29
00:02:31,130 --> 00:02:36,470
So there's a node of a tree, not the school should work up on node.

30
00:02:36,470 --> 00:02:42,110
So I should modify the code for this node because I may be storing the addresses of the nodes in the

31
00:02:42,110 --> 00:02:43,980
queue while creating a tree.

32
00:02:45,020 --> 00:02:46,700
So here I will make changes.

33
00:02:46,730 --> 00:02:51,620
This array should be of type node pointer, so node pointer.

34
00:02:51,620 --> 00:02:54,250
So already it's a pointer double-blind that.

35
00:02:55,190 --> 00:03:01,340
Then here inside the constructor, when I'm creating an UTI, I should not send in, I should say,

36
00:03:01,340 --> 00:03:04,870
Naude ointment and one more, please.

37
00:03:04,880 --> 00:03:05,900
I have mentioned this.

38
00:03:05,910 --> 00:03:11,900
This is not the point that this glass should store the addresses of a tree.

39
00:03:12,140 --> 00:03:15,780
So that's why I'm converting into North NQ.

40
00:03:15,800 --> 00:03:19,190
It should not take an integer, but a node pointer.

41
00:03:23,730 --> 00:03:32,360
Here also, and I should change it, so I'm making required changes and you should return a pointer.

42
00:03:36,280 --> 00:03:38,050
You return type of Snoad Pointer.

43
00:03:40,450 --> 00:03:47,420
On this variable, we're taking the data, it should be of no type and I should initialize it to null.

44
00:03:50,170 --> 00:03:53,110
Does display function, though, I don't need it, let it be.

45
00:03:53,140 --> 00:03:53,990
I'll just leave it.

46
00:03:54,190 --> 00:03:56,380
I don't have to make any changes in this function.

47
00:03:58,800 --> 00:04:06,250
So that's all I guess these are the changes what we need in a new class, so we have made required changes.

48
00:04:06,590 --> 00:04:10,370
Now let's go back to the main part of the program, and that is main function.

49
00:04:10,710 --> 00:04:14,730
So here I should write a class for three, class four three.

50
00:04:17,160 --> 00:04:23,620
Inside this, I should have only one member that is rude, pointed to a rude Naude that is rude.

51
00:04:24,170 --> 00:04:26,820
Now the question is, shall I make it as a public or private?

52
00:04:27,120 --> 00:04:29,380
I may be using it from outside the class.

53
00:04:29,400 --> 00:04:32,370
Also, I suggest this should be made public.

54
00:04:32,670 --> 00:04:36,420
If it is made as a private, then I have to make a lot of changes in the code.

55
00:04:36,750 --> 00:04:38,910
So let us declare it as public.

56
00:04:41,140 --> 00:04:49,060
Now, inside is the first thing is constructor, so constructor, class, name, tree and non parametrized

57
00:04:49,060 --> 00:04:57,370
constructor and just I have to initialize it to null Rudi's initialized as null, then no need of any

58
00:04:57,370 --> 00:04:58,110
constructors.

59
00:04:58,120 --> 00:05:00,010
No, I will write down required methods.

60
00:05:00,010 --> 00:05:03,490
First one is create three function.

61
00:05:04,970 --> 00:05:09,260
Which doesn't take any barometer because Rudy is available inside three glass itself.

62
00:05:12,030 --> 00:05:14,670
That next function is preorder.

63
00:05:16,310 --> 00:05:17,870
I'll take Capital B..

64
00:05:19,620 --> 00:05:22,060
I think it means a rude point.

65
00:05:22,270 --> 00:05:27,520
So let us call it the point that the void host order.

66
00:05:30,370 --> 00:05:34,120
And it also takes a pointer to a room that does let us call it USPI.

67
00:05:35,980 --> 00:05:36,400
Then.

68
00:05:37,760 --> 00:05:38,650
In order.

69
00:05:41,130 --> 00:05:42,660
Take the point of aspy.

70
00:05:44,280 --> 00:05:48,570
These functions we have already done in a C language program for the same set of function and making

71
00:05:48,570 --> 00:05:52,470
them as member functions of three class.

72
00:05:53,540 --> 00:05:57,200
Then also, I will on one function for level order.

73
00:05:58,880 --> 00:06:04,110
Level order function, and it also takes a pointer to route.

74
00:06:05,150 --> 00:06:06,740
And hide function.

75
00:06:09,630 --> 00:06:12,480
Height, which takes a pointer to route.

76
00:06:14,360 --> 00:06:19,250
That's all these are the functions I'm going to implement them outside what I said that I will not be

77
00:06:19,250 --> 00:06:23,660
writing the code once again, along with typing the code once again, all these functions.

78
00:06:23,660 --> 00:06:28,000
I'll copy them from my C language program and I'll pass them and use them here.

79
00:06:28,340 --> 00:06:31,750
So let us first bring the function that is created re function.

80
00:06:32,240 --> 00:06:34,590
So I'll already I have that project open here.

81
00:06:34,850 --> 00:06:36,800
So this created three function.

82
00:06:37,070 --> 00:06:38,000
I'll pop it.

83
00:06:42,680 --> 00:06:49,860
First function, I will Poppit and I will use it here and also I will make all the required changes.

84
00:06:49,880 --> 00:06:54,610
So just watch how I'm making changes in this one to recreate function.

85
00:06:54,620 --> 00:06:56,770
Actually, I'm calling it 383.

86
00:06:56,780 --> 00:06:57,900
OK, I'll change the name.

87
00:06:59,030 --> 00:07:05,270
This is inside three glass and this is create three function.

88
00:07:05,480 --> 00:07:05,930
OK.

89
00:07:07,130 --> 00:07:12,440
Then inside this, this is not a structure more so just a..

90
00:07:13,970 --> 00:07:21,980
I need temporary pointers then to all of your classes available, so I have to create an object of queue

91
00:07:22,100 --> 00:07:25,850
so your queue object will be created for this one here on the top.

92
00:07:25,850 --> 00:07:29,390
I should include a header file that is include.

93
00:07:30,950 --> 00:07:35,810
You see BP just fine.

94
00:07:37,330 --> 00:07:45,250
Now I can use that cube, so if you see this header, file this to keep this header file where I have

95
00:07:45,250 --> 00:07:50,680
the cube, as well as the node structure for a tree to define here, all those things, I'll be able

96
00:07:50,680 --> 00:07:55,330
to use them in my main part of a program when I have included this header file.

97
00:07:57,520 --> 00:08:03,580
Now, Line-by-line, let me continue here, I was creating a queue of 500, so instead of calling a

98
00:08:03,580 --> 00:08:07,660
function created, I will just call parameters constructor of Kubla.

99
00:08:07,660 --> 00:08:16,210
So few of 500 will be created and it will be a queue of a point of time between OK then here this goes

100
00:08:16,210 --> 00:08:16,750
regular.

101
00:08:16,960 --> 00:08:24,850
And here, instead of seeing my log and size of all those things, I have to simply say new node.

102
00:08:25,450 --> 00:08:35,500
So I replace this little code that is new node and most of the things are as it is then NQ.

103
00:08:36,220 --> 00:08:43,030
So this is a part of Kunal, Q Dot and Q and I don't have to pass the test parameter, just a Rodust

104
00:08:43,030 --> 00:08:43,630
parameter.

105
00:08:43,679 --> 00:08:49,390
See this is a function and do the function inside the class for Q Dot and Q I have to see.

106
00:08:51,220 --> 00:08:58,090
And here, while not, is empty, oh, I have not implemented exemptive function, so I will be implementing

107
00:08:58,090 --> 00:08:59,420
that assembly function there.

108
00:08:59,440 --> 00:09:07,180
I'll do it afterwards is empty function inside a queue plus then you got dequeue.

109
00:09:07,180 --> 00:09:13,120
I should say I should not pass this anything as a parameter, then let it be as it is.

110
00:09:13,120 --> 00:09:16,540
No problem in different scanners will be using as the as it is.

111
00:09:16,540 --> 00:09:17,530
I will not change them.

112
00:09:19,330 --> 00:09:22,570
So for that, I should include Studio Dot Hetch.

113
00:09:26,460 --> 00:09:27,510
Can you spin defense?

114
00:09:27,840 --> 00:09:31,560
So this is printing and reading the value and the value of so-and-so.

115
00:09:32,840 --> 00:09:38,720
And if the value is minus one, then don't insult anything if it is not equal to minus one and create

116
00:09:38,720 --> 00:09:39,350
a new norm.

117
00:09:39,800 --> 00:09:42,410
So for this I will call it as new node.

118
00:09:42,650 --> 00:09:48,080
The node is created, all the links are set, then call culotte NQ.

119
00:09:49,700 --> 00:09:54,410
Change is one dog in the sky, and if everything is as it is.

120
00:09:56,540 --> 00:10:00,230
Then this partial change that is new and old.

121
00:10:08,380 --> 00:10:13,990
And again, here culotte and you remove this glass parameter.

122
00:10:16,050 --> 00:10:17,700
That's all this function is really.

123
00:10:19,620 --> 00:10:21,140
Recreate function is ready.

124
00:10:22,140 --> 00:10:27,480
One thing I have to write on is empty function, so I'll go to this class and here I will introduce

125
00:10:27,480 --> 00:10:30,270
a function that is empty just after dequeue.

126
00:10:31,170 --> 00:10:32,100
They should return.

127
00:10:32,580 --> 00:10:36,670
True or false is empty, true or false.

128
00:10:36,700 --> 00:10:44,670
So what what is the condition for tumed in front is equal to rather than empty, it should return.

129
00:10:44,670 --> 00:10:49,590
The result of this condition directly written from the second story OUTFRONT is a good story.

130
00:10:49,750 --> 00:10:50,910
It returns empty.

131
00:10:50,910 --> 00:10:51,540
Yes, true.

132
00:10:52,050 --> 00:10:53,130
So this is sufficient.

133
00:10:53,640 --> 00:10:57,660
So I have implemented it as an inline function that is within the class itself.

134
00:10:59,770 --> 00:11:05,650
Now, coming back to the program here, I have implemented a great read function that I need preorder

135
00:11:05,650 --> 00:11:13,610
post order in order, so I will copy those functions and I'll modify them here according to my CRP program.

136
00:11:15,010 --> 00:11:20,160
So here I have those functions, pre order in order and post orders.

137
00:11:20,170 --> 00:11:21,670
I copy all these functions.

138
00:11:29,800 --> 00:11:35,830
You're here inside the main part of the program after to create function, I will write on those functions,

139
00:11:35,830 --> 00:11:39,410
all these three functions, I need them pre order.

140
00:11:39,730 --> 00:11:44,450
This is a part of a we just now have declared it here inside a glass right.

141
00:11:44,530 --> 00:11:45,580
Preorder, right.

142
00:11:45,590 --> 00:11:46,440
Same function.

143
00:11:46,780 --> 00:11:50,070
I'm writing it as a part of a glass.

144
00:11:50,080 --> 00:11:51,880
So I am using SCOP resolution.

145
00:11:52,390 --> 00:11:53,970
Then what is the change inside this.

146
00:11:54,070 --> 00:11:55,040
Just print different stuff.

147
00:11:55,120 --> 00:11:59,860
Print if I can use a C out of print differently so there is no difference.

148
00:11:59,980 --> 00:12:06,190
So just it has become a part of our class now then in order also made it as a part of three glass,

149
00:12:06,730 --> 00:12:10,710
then three are also made it as a part of the glass.

150
00:12:11,320 --> 00:12:12,580
No more changes at all.

151
00:12:12,940 --> 00:12:17,860
See as it is, the code is useful than it was earlier in a C program.

152
00:12:17,860 --> 00:12:22,030
It was an independent function, but now it has become a part of three glass.

153
00:12:22,040 --> 00:12:23,170
That is the difference here.

154
00:12:24,980 --> 00:12:28,310
Let us bring other functions that is level order function and.

155
00:12:30,470 --> 00:12:31,490
Hide function.

156
00:12:37,100 --> 00:12:38,450
There's a level order function.

157
00:12:42,830 --> 00:12:44,880
Gown and height is also there.

158
00:12:45,710 --> 00:12:48,860
So LAPD and paste them here.

159
00:12:54,020 --> 00:13:02,660
So don't I have not declared I'm not using it as they move this one height and level order for height,

160
00:13:02,880 --> 00:13:04,430
that will change this one.

161
00:13:04,610 --> 00:13:06,060
This is height, capital H.

162
00:13:06,080 --> 00:13:09,260
I have taken and this is a part of three.

163
00:13:09,620 --> 00:13:10,780
So just say three.

164
00:13:11,090 --> 00:13:15,080
And here inside this also this is Capital H and Capital H.

165
00:13:15,620 --> 00:13:16,130
Natural.

166
00:13:16,130 --> 00:13:17,050
No more changes.

167
00:13:17,210 --> 00:13:17,920
Same quarter.

168
00:13:17,930 --> 00:13:20,620
Will you work for this class also.

169
00:13:21,230 --> 00:13:21,470
No.

170
00:13:21,470 --> 00:13:23,690
Let us come to level order for level order.

171
00:13:23,690 --> 00:13:26,900
I should call it as a member of the resource revolution.

172
00:13:27,410 --> 00:13:36,260
Then here inside this again a level order function function uses a Q so I have to use that.

173
00:13:36,260 --> 00:13:42,140
Q Plus this class which you have written here inside the header file, this class I have to use for

174
00:13:42,140 --> 00:13:45,980
making use of this one, I should make the required changes.

175
00:13:45,980 --> 00:13:53,210
First of all, I take a Q that is the object of Q class and I will take it as a constructor as hundred.

176
00:13:53,840 --> 00:13:55,280
So I will remove this one.

177
00:13:55,280 --> 00:13:58,730
I will not have create function then.

178
00:13:58,740 --> 00:13:59,540
Printf is there.

179
00:13:59,570 --> 00:14:06,620
Let the FBI as it is then this is and the Q function that was in C language which was taking Q as well

180
00:14:06,620 --> 00:14:11,630
as the value to be inserted, but now it will be a part of Q plus Q Object.

181
00:14:11,640 --> 00:14:12,680
So they killed or.

182
00:14:14,080 --> 00:14:21,680
Kudos and root, no end of any barometer then this is culotte is empty.

183
00:14:22,480 --> 00:14:24,220
Don't pass the test parameter.

184
00:14:25,260 --> 00:14:28,950
Then you got the dequeue that secured or dequeue.

185
00:14:33,630 --> 00:14:41,930
Then as a child, our child, then this is killed NQ and no of this parameter and here also killed.

186
00:14:42,050 --> 00:14:42,710
Thank you.

187
00:14:43,670 --> 00:14:46,640
Note of Q as parameter NetSol.

188
00:14:50,080 --> 00:14:54,530
Now, main function, I have to write off the words, but in here, let us compile and check.

189
00:14:54,880 --> 00:14:56,080
Are there any errors?

190
00:14:56,080 --> 00:14:58,230
If there are any errors, I will remove them.

191
00:14:58,830 --> 00:15:00,490
See, the approach would have taken here.

192
00:15:00,490 --> 00:15:07,240
If I have copied the existing code and I have written to prepare the class for free without writing

193
00:15:07,480 --> 00:15:09,520
even a single line from the scratch.

194
00:15:09,760 --> 00:15:14,980
So only the class I have written and I have implemented all the things that are borrowed from the existing

195
00:15:14,980 --> 00:15:17,430
code, so I may get errors.

196
00:15:17,710 --> 00:15:20,500
So just watch if there are any errors, how to remove them.

197
00:15:21,160 --> 00:15:22,270
I'll compile this one.

198
00:15:25,210 --> 00:15:26,710
Yes, there are four errors.

199
00:15:31,270 --> 00:15:33,810
First one level order.

200
00:15:34,360 --> 00:15:39,980
It is not matching here, see inside this glass, I have given small letter.

201
00:15:40,010 --> 00:15:45,350
All this is capital letter all OK, I will change it to small letter.

202
00:15:45,910 --> 00:15:48,690
Let me compile once again and see how many others I get.

203
00:15:48,700 --> 00:15:52,330
OK, still three errors now coming to this height.

204
00:15:52,480 --> 00:15:54,610
OK, for height I got an error.

205
00:15:54,850 --> 00:15:58,930
So height actually it is running integer type so it is giving an error.

206
00:15:58,930 --> 00:16:01,900
That return type of outline function is different.

207
00:16:01,900 --> 00:16:04,150
So let us go back to the class definition and see.

208
00:16:04,660 --> 00:16:05,470
Oh right.

209
00:16:05,530 --> 00:16:09,310
I have written it as void so it should be an integer.

210
00:16:09,310 --> 00:16:10,600
It will be returning in height.

211
00:16:11,110 --> 00:16:12,640
I'll compile it once again.

212
00:16:15,110 --> 00:16:18,980
You are successful, right, on a main function.

213
00:16:19,920 --> 00:16:28,140
Inside the main function, let us create a tree and Paul traversal functions, so tree e is a tree,

214
00:16:28,680 --> 00:16:31,470
not on tree I will call function.

215
00:16:34,020 --> 00:16:35,340
Create three.

216
00:16:37,640 --> 00:16:43,980
Let us check the function you created three dysfunction I'm calling, which will create a three, then

217
00:16:44,000 --> 00:16:46,940
after this I will call just preorder function.

218
00:16:50,110 --> 00:16:55,050
Now to this preorder, I should pass through the deposits are recursive function.

219
00:16:55,090 --> 00:16:56,950
Definitely need some parameter.

220
00:16:57,790 --> 00:16:59,520
It cannot be without parameter.

221
00:16:59,770 --> 00:17:02,320
So I should pass the DOD rule.

222
00:17:03,470 --> 00:17:07,010
He got rude, rude, as they were, of a class three.

223
00:17:07,030 --> 00:17:10,290
Yes, there's a little member can access this.

224
00:17:10,310 --> 00:17:12,010
Yes, it is kept in public.

225
00:17:12,440 --> 00:17:16,790
That is the reason I have made it as public so that I can access it directly.

226
00:17:17,510 --> 00:17:20,410
Otherwise, I cannot write recursive function directly.

227
00:17:20,660 --> 00:17:25,690
I need some dummy functions which will call these functions indirectly.

228
00:17:25,700 --> 00:17:26,720
I'll show you that one.

229
00:17:29,680 --> 00:17:36,690
And Allan, if I don't make it as a public, then they cannot call these functions that are recursive

230
00:17:36,690 --> 00:17:38,980
function directly for them.

231
00:17:39,030 --> 00:17:41,360
I need some other dummy functions.

232
00:17:42,180 --> 00:17:44,640
I will show you how to do that later.

233
00:17:44,650 --> 00:17:46,730
I'll show you how to make this as private.

234
00:17:47,580 --> 00:17:49,650
So let us go to this.

235
00:17:52,980 --> 00:17:54,090
Main function.

236
00:17:55,110 --> 00:18:00,720
And completed, yes, I'm calling preorder now, let us run the program and see I will create a tree

237
00:18:00,720 --> 00:18:02,700
and it should perform preorder upon it.

238
00:18:08,120 --> 00:18:16,790
Yes, is running, it's asking the root value to valuable, and that is that as an child of 10 is 20,

239
00:18:16,790 --> 00:18:20,270
rachell of 10 is 30 and there are no more children.

240
00:18:23,060 --> 00:18:27,730
Yes, I got a preorder and Bernadi today is a preorder.

241
00:18:28,220 --> 00:18:29,030
It's working.

242
00:18:29,910 --> 00:18:32,310
Let us perform in order also and check.

243
00:18:36,080 --> 00:18:40,400
Edward wrote, I will pass the test parameter, runit.

244
00:18:43,490 --> 00:18:48,580
A program is running, it's asking for the root value as a end, and the left child is 20, Rachel is

245
00:18:48,620 --> 00:18:52,070
30 and no more children beyond this.

246
00:18:55,250 --> 00:19:02,410
Yes, this is the preorder and today is the preorder, and only then today is in order.

247
00:19:02,900 --> 00:19:04,850
So let us make it properly.

248
00:19:05,600 --> 00:19:09,140
Let me give a proper labelling here before this preorder.

249
00:19:09,140 --> 00:19:12,470
I will give you a message that is this is preorder.

250
00:19:15,900 --> 00:19:24,270
Preorder with the gap, then after preorder, also I will give a new line and then after this I will

251
00:19:24,270 --> 00:19:25,970
say in order.

252
00:19:28,450 --> 00:19:36,520
In order then with the gap thereafter, in order again, I will give a new line seal and that is new

253
00:19:36,520 --> 00:19:37,930
line, one more new line.

254
00:19:39,730 --> 00:19:45,790
Yes, then when I'm using setout here, I should use using namespace, I should declare in the beginning

255
00:19:46,030 --> 00:19:48,340
day, I have done it later on the program.

256
00:19:52,970 --> 00:19:59,480
No end to the values root values 10 and it's left 20, 30 and no more children.

257
00:20:01,930 --> 00:20:09,550
Here you can read it, that is preorders 10, 20 and 30, and in all that is 2010 and today.

258
00:20:09,610 --> 00:20:10,810
Yes, it's working.

259
00:20:12,070 --> 00:20:18,490
Therefore, now I just said that I can't even make this route as private so that it's accessible only

260
00:20:18,490 --> 00:20:22,850
inside the closet and how to write this recursive function, how we can pass the parameter.

261
00:20:23,200 --> 00:20:27,130
So for this, I should have one more reorder function.

262
00:20:27,700 --> 00:20:31,990
That is our parameter and it should all this preorder function.

263
00:20:33,320 --> 00:20:35,900
Bypassing route as a barometer.

264
00:20:37,770 --> 00:20:43,110
Now, let us call this function reorder function so you can see that is a non parametrized function

265
00:20:43,410 --> 00:20:46,420
and this is parametrized, so these are overloaded function.

266
00:20:46,710 --> 00:20:51,400
So from this function gambolling, second one dysfunction by passing around.

267
00:20:52,410 --> 00:20:54,520
So from the main function, they cannot pass through.

268
00:20:54,550 --> 00:20:58,690
So I will call this function and this will recursive function by passing rule.

269
00:20:59,190 --> 00:21:02,040
So let us call this preorder function from here and check.

270
00:21:04,490 --> 00:21:06,980
I'm not passing around as parameter.

271
00:21:07,850 --> 00:21:09,880
Now, let us run, it will work.

272
00:21:12,880 --> 00:21:21,040
Yes, it is working, so if I give the values 10, 20 and 30, then minus one, minus one, minus one,

273
00:21:21,040 --> 00:21:22,900
minus one, it's working.

274
00:21:23,500 --> 00:21:24,930
Preorder is executed.

275
00:21:25,150 --> 00:21:27,200
So the same function has executed.

276
00:21:27,460 --> 00:21:31,840
So if you check here, I'm calling the first function that is without any parameters.

277
00:21:32,170 --> 00:21:35,770
And that function is calling a function with parameters in the same way.

278
00:21:36,040 --> 00:21:43,000
For all these function, I should provide a dummy function which without making any parameters, then

279
00:21:43,000 --> 00:21:45,820
I can make this rule as private.

280
00:21:46,570 --> 00:21:49,620
So I will write on all those and I will show you finally.

281
00:21:50,800 --> 00:21:54,770
Yes, I have written dummy function for each and every recursive function.

282
00:21:55,300 --> 00:21:58,980
So these functions are calling both recursive function internally.

283
00:21:59,290 --> 00:22:02,740
I can make this as private now this rule.

284
00:22:02,890 --> 00:22:04,480
I can make it as private.

285
00:22:07,420 --> 00:22:14,370
So I just declared it above public, so it becomes a private now from here, I don't have to call function

286
00:22:14,380 --> 00:22:18,880
by passing ruled directly, I can call the function or function will be called.

287
00:22:18,890 --> 00:22:23,240
That in turn will call a function that is recursive function.

288
00:22:23,620 --> 00:22:27,970
So for all of them, I have given them functions and I am able to make it as private.

289
00:22:28,180 --> 00:22:33,400
This is the approach you have to take if I run it unsuccessfully.

290
00:22:36,490 --> 00:22:42,090
I want to give the values and all that we have seen it, so that's all in this program.

291
00:22:44,800 --> 00:22:47,590
Oh, there is some morning issuing some warning.

292
00:22:50,210 --> 00:22:51,340
Let us check the warning.

293
00:22:51,920 --> 00:22:56,880
OK, here it is, giving a format specified.

294
00:22:57,500 --> 00:23:01,910
Yeah, we are displaying addresses here, but it is a D written here anyway.

295
00:23:01,950 --> 00:23:03,010
This is a display function.

296
00:23:03,020 --> 00:23:04,010
We are not using it.

297
00:23:04,440 --> 00:23:05,450
We don't need it.

298
00:23:05,870 --> 00:23:07,990
So let us ignore it.

299
00:23:08,690 --> 00:23:10,190
That's all in this program.

300
00:23:10,190 --> 00:23:17,650
So you have seen how we can write a C++ class for a tree so you can write on this by yourself.

301
00:23:17,660 --> 00:23:23,420
If you have implemented those functions, you can borrow them here and you can avoid wiping the whole

302
00:23:23,420 --> 00:23:24,430
program once again.

303
00:23:24,440 --> 00:23:31,550
So that is the reason I'm having brain scan if as it is used here, you can change them to CNN Council.

304
00:23:32,900 --> 00:23:34,240
So that's all in the studio.

