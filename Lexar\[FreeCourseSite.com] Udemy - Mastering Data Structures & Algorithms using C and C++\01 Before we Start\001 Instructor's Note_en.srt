1
00:00:00,330 --> 00:00:00,810
Hi.

2
00:00:00,900 --> 00:00:02,270
Welcome to my course.

3
00:00:02,310 --> 00:00:04,810
I'll guide you how to follow this course.

4
00:00:04,860 --> 00:00:07,790
This course is covering around 45 hours.

5
00:00:07,800 --> 00:00:12,260
Then it means you have to spend some extra time for learning this course.

6
00:00:12,300 --> 00:00:16,149
So I suggest you to complete this course in a timely manner.

7
00:00:16,290 --> 00:00:21,810
So you spend at least two hours per day so that you can finish it within a month.

8
00:00:21,840 --> 00:00:26,850
Then while studying you can watch whiteboard session and whatever it is discussion whiteboard session

9
00:00:26,850 --> 00:00:33,870
you can prepare notes if you want and every program is discussed on whiteboard so you can pause the

10
00:00:33,870 --> 00:00:38,430
video or at the end of the video you can start cording the program by yourself.

11
00:00:38,970 --> 00:00:45,590
So everything is discussed on whiteboard so you can do it by yourself. If at all you face any difficulty,

12
00:00:45,600 --> 00:00:50,730
then you can watch a coding video. Coding videos are also available and along with the coding video

13
00:00:50,730 --> 00:00:56,790
a PDF for full program is also available you can verify your program with that PDF content. The most

14
00:00:56,790 --> 00:00:59,730
important thing is analysis.

15
00:00:59,730 --> 00:01:05,239
There is a video on time and space complexity in the beginning of the course so you must watch that

16
00:01:05,250 --> 00:01:06,360
video.

17
00:01:06,360 --> 00:01:11,400
I have done analysis on each and every data structure and operations on the data structure. Now, while watching

18
00:01:11,400 --> 00:01:12,480
a video on analysis,

19
00:01:12,480 --> 00:01:18,480
I suggest you to practice it by yourself and if you are doing the analysis by yourself ones that will

20
00:01:18,480 --> 00:01:22,380
help you remembering the analysis and applying them in the right place.

21
00:01:22,380 --> 00:01:23,400
And one more thing.

22
00:01:23,400 --> 00:01:29,470
Asymptotic notations are used for representing the time complexities like Bigo, Omega and Theta.

23
00:01:29,490 --> 00:01:30,770
These are simple terms,

24
00:01:30,780 --> 00:01:32,160
These are nothing, without

25
00:01:32,160 --> 00:01:35,420
also you can represent the time complexity.

26
00:01:35,520 --> 00:01:41,040
These terms mostly confuse the students so even I have covered about asymptotic notations, You

27
00:01:41,040 --> 00:01:44,860
can watch the video available for asymptotic notations.

28
00:01:44,920 --> 00:01:50,850
I have discussed when to use Bigo, Omega and Theta but during my discussions I have not used these

29
00:01:50,850 --> 00:01:57,030
notations to make topic simple and easy for you to understand.

30
00:01:57,030 --> 00:02:00,080
At any place If you find any difficulty you can leave a message to me.

31
00:02:00,120 --> 00:02:05,460
You can go to question and answer section and then you can leave a message and if you have any trouble

32
00:02:05,460 --> 00:02:12,510
in coding you can send me a screenshot so that I can quickly reply you. So,  am there with you always whenever

33
00:02:12,510 --> 00:02:18,510
you face any difficulty you can put a question in the QnA section. That's all, you can start

34
00:02:18,510 --> 00:02:22,550
learning Data Structure course. Definitely, you will enjoy the course.

