1
00:00:00,660 --> 00:00:05,910
In this video, we learn about the full monetary and complete warranty and also see the difference between

2
00:00:05,910 --> 00:00:06,190
them.

3
00:00:06,420 --> 00:00:07,790
The concept is very simple.

4
00:00:07,800 --> 00:00:09,190
So there you have example.

5
00:00:09,190 --> 00:00:10,980
Please let us look at them.

6
00:00:11,790 --> 00:00:19,610
This is a full binary tree example of full buoyantly, what is full buoyantly of binary tree of height.

7
00:00:19,620 --> 00:00:24,290
Each having maximum number of N is a full battery.

8
00:00:24,720 --> 00:00:27,240
What is the height of this 180 height?

9
00:00:27,290 --> 00:00:33,490
If I take this as a zero and here one and two zero one two hiders two.

10
00:00:33,720 --> 00:00:35,130
So how many nodes are there?

11
00:00:35,400 --> 00:00:36,630
Maximum nodes.

12
00:00:36,900 --> 00:00:44,300
So maximum loan means getting out of one extra node and still the <PERSON><PERSON>mi to know if I had any node anywhere.

13
00:00:44,610 --> 00:00:50,310
The height will increase for up to height to it is having full nodes.

14
00:00:50,310 --> 00:00:51,590
That is maximum nodes.

15
00:00:52,020 --> 00:00:54,540
So are tree of height.

16
00:00:54,540 --> 00:00:58,110
Each can have how many N dupa x plus one.

17
00:00:58,110 --> 00:00:59,370
Minus one n.

18
00:01:00,460 --> 00:01:05,470
So what is at stake here, too, so if I write to here, this is two plus one.

19
00:01:05,470 --> 00:01:06,160
Minus one.

20
00:01:06,460 --> 00:01:07,930
This is eight minus one.

21
00:01:07,930 --> 00:01:08,740
That is seven.

22
00:01:09,010 --> 00:01:11,380
One, two, three, four, five, six, seven.

23
00:01:11,380 --> 00:01:12,700
Yes, seven notes.

24
00:01:13,570 --> 00:01:20,120
If you remember the formula, all three of high tech can have a maximum to borage, plus one minus one

25
00:01:20,120 --> 00:01:20,860
number of also.

26
00:01:20,860 --> 00:01:23,160
Yes, this are having maximum notes.

27
00:01:23,410 --> 00:01:24,400
So it's a full 180.

28
00:01:24,760 --> 00:01:32,140
So that's all this the definition of full by I will Representative Anthony ABCDE PFG.

29
00:01:32,140 --> 00:01:33,450
I have filled them level one level.

30
00:01:33,550 --> 00:01:39,700
So it is a store and I know we have to understand what is complete binary tree.

31
00:01:40,840 --> 00:01:44,260
Now, this concept is important, completed by listen carefully.

32
00:01:45,470 --> 00:01:48,330
For explaining complete minority, I have taken two examples.

33
00:01:49,190 --> 00:01:51,210
So first one, this one is complete.

34
00:01:51,470 --> 00:01:52,690
This is not complete.

35
00:01:53,390 --> 00:01:56,960
How legacy I will represent that in an.

36
00:01:57,590 --> 00:02:06,880
So first one is A, B, then B, C, B, C, then the next level, the F blank.

37
00:02:06,890 --> 00:02:10,729
So F, b, e, f.

38
00:02:11,910 --> 00:02:14,340
If is the last element that's on.

39
00:02:15,450 --> 00:02:23,550
The size of the tree is one two six, only one two six only not from first element to last element.

40
00:02:23,850 --> 00:02:26,670
Are there any blank spaces in between?

41
00:02:27,160 --> 00:02:29,280
No, there are no blank spaces.

42
00:02:29,520 --> 00:02:31,450
So this is a complete by entry nine.

43
00:02:31,500 --> 00:02:32,070
Define it.

44
00:02:32,350 --> 00:02:40,410
If a binary tree is represented in an array, then they should not be any blank spaces in between the

45
00:02:40,410 --> 00:02:41,070
elements.

46
00:02:42,170 --> 00:02:46,800
If there are blank spaces, it's not accompanied by and there are no blank spaces, it is a complete

47
00:02:46,820 --> 00:02:47,320
ban entry.

48
00:02:47,900 --> 00:02:49,310
No, I have one more example.

49
00:02:49,520 --> 00:02:53,470
Let us store this one, then we will see whether it's is complete or not.

50
00:02:53,930 --> 00:02:55,730
Let us store level Ballymun a.

51
00:02:57,220 --> 00:03:04,500
B, C is to N, B, C, D, then here there should be an element here, there should be an element.

52
00:03:04,510 --> 00:03:05,560
These notes are missing.

53
00:03:05,770 --> 00:03:12,190
So the blank blank e so be blank, blank e so I go to blank spaces.

54
00:03:12,190 --> 00:03:17,420
First element of the last element is E in between those two elements, two blank spaces.

55
00:03:17,710 --> 00:03:19,240
This is not a complete binary.

56
00:03:19,450 --> 00:03:20,290
This is a complete.

57
00:03:21,650 --> 00:03:25,790
Now, I'll give a book, a definition for a complete binary tree.

58
00:03:26,700 --> 00:03:35,850
A complete 180 of height edge here, Heidi 012, accompanied by a tree of height edge, will be a full

59
00:03:35,850 --> 00:03:39,290
binary tree, up to actually minus one height.

60
00:03:41,430 --> 00:03:48,240
So it's minus one, Heidi, if you look at this high BS, then just look at this, it's a full 180.

61
00:03:48,400 --> 00:03:48,950
Yes.

62
00:03:49,500 --> 00:03:55,500
And on the last level, the elements will be filled from left to right without skipping any elements.

63
00:03:55,890 --> 00:03:57,910
Left, right, then right, then left.

64
00:03:58,470 --> 00:03:58,950
That's all.

65
00:03:58,980 --> 00:03:59,550
Finish.

66
00:03:59,790 --> 00:04:00,450
No problem.

67
00:04:00,600 --> 00:04:05,280
Finish as many elements we had, we have filled them from left to right without skipping.

68
00:04:05,490 --> 00:04:09,630
But skipping means I'm not writing this right, but taking left and right here.

69
00:04:09,630 --> 00:04:14,910
I suppose this is E and this is F then this is missing.

70
00:04:15,060 --> 00:04:15,780
This is missing.

71
00:04:15,990 --> 00:04:17,339
So it's not a complete binary.

72
00:04:18,510 --> 00:04:21,269
So last level elements was different from left to right.

73
00:04:21,630 --> 00:04:26,070
Let us look at this up to this high to the full but last level.

74
00:04:26,070 --> 00:04:31,410
The elements are not filled from left to right, left to the next one is this one here is missing.

75
00:04:31,620 --> 00:04:34,250
So missing means it's not a complete binary.

76
00:04:35,220 --> 00:04:37,410
So this is the definition of complete binary.

77
00:04:38,200 --> 00:04:45,880
Why why we need complete plan B, C, we have learned A arrays and also we have used arrays for stock

78
00:04:45,880 --> 00:04:46,370
as the last.

79
00:04:46,410 --> 00:04:52,240
Q And every time I was telling you that we should not have blank spaces in between the elements, if

80
00:04:52,240 --> 00:04:58,210
any element is deleted, we should shift the elements and occupy that place so we can have blank spaces

81
00:04:58,360 --> 00:04:59,000
in an array.

82
00:04:59,440 --> 00:05:06,670
So if our tree is such that you are forced to have blank spaces, then it is not suitable for us.

83
00:05:07,630 --> 00:05:10,150
So we say it's not a complete binary tree.

84
00:05:10,870 --> 00:05:15,000
We say complete if it is suitable for not coming back to this.

85
00:05:15,310 --> 00:05:17,480
This is a full binary tree, right?

86
00:05:17,830 --> 00:05:19,500
This is a full one tree.

87
00:05:20,020 --> 00:05:21,050
Is it complete?

88
00:05:21,100 --> 00:05:22,850
Yes, it's a complete 180.

89
00:05:23,140 --> 00:05:25,190
There are no blank spaces in between there.

90
00:05:25,870 --> 00:05:29,680
So full military is always accompanied by military.

91
00:05:30,670 --> 00:05:35,000
There's a well-known statement, but a complete binary tree.

92
00:05:36,310 --> 00:05:37,190
I haven't seen this.

93
00:05:37,720 --> 00:05:41,260
This was E and this was F, OK, back to that.

94
00:05:41,890 --> 00:05:46,030
But a complete 180 degree need not be a full binary tree.

95
00:05:47,390 --> 00:05:49,020
I is completely unnecessary.

96
00:05:49,130 --> 00:05:52,050
It is full, but if it is fundaments, definitely it is complete.

97
00:05:53,040 --> 00:05:57,420
So that's all the differences between full and complete by military.

