1
00:00:00,300 --> 00:00:07,380
In this video, we will learn how to reverse a Lincolnesque reversing a linguist means right now the

2
00:00:07,380 --> 00:00:09,590
contents are two, four, six, eight.

3
00:00:10,140 --> 00:00:15,600
We have to change the order of the elements and make the elements, says eight six four two.

4
00:00:16,379 --> 00:00:18,160
There are two methods of reversing.

5
00:00:18,480 --> 00:00:19,770
I'll show you the methods.

6
00:00:20,550 --> 00:00:22,860
First one is reversing elements.

7
00:00:23,400 --> 00:00:27,180
And this note about it, too, is there and the last known valuate is there.

8
00:00:27,330 --> 00:00:28,640
I should interchange them.

9
00:00:29,040 --> 00:00:33,930
So here it should be two and here I should have eight node will be seen.

10
00:00:34,890 --> 00:00:35,770
What does it mean?

11
00:00:36,360 --> 00:00:41,420
Suppose the address of this note is two hundred and the address of this note is to ten.

12
00:00:41,790 --> 00:00:44,910
And this is 250 and this is three hundred.

13
00:00:45,240 --> 00:00:52,290
Then this node two hundred should contain the value eight and this node three hundred with that three

14
00:00:52,290 --> 00:01:00,420
and it should contain the value to so reversing a linguist by interchanging elements or reversing elements.

15
00:01:01,940 --> 00:01:05,150
Then the second method is reversing length.

16
00:01:05,600 --> 00:01:11,510
So in this one, you can see that two hundred two hundred is pointing on two, then 210 is pointing

17
00:01:11,510 --> 00:01:13,530
on 252 of this pointing on three hundred.

18
00:01:13,970 --> 00:01:20,660
We want to reverse it means three hundred point on 252 fishhook point of entry to 10 and 210 should

19
00:01:20,660 --> 00:01:21,600
point on 200.

20
00:01:21,860 --> 00:01:27,700
So the value to remains in the same node in the X node only it will not change the node.

21
00:01:28,130 --> 00:01:34,910
So every value remains and its own A. Only the links will change and that in order should become first

22
00:01:34,910 --> 00:01:35,270
node.

23
00:01:35,990 --> 00:01:38,240
Right now, node two hundred is first known.

24
00:01:38,510 --> 00:01:42,380
Then after reversing, node 300 should become first node.

25
00:01:42,980 --> 00:01:44,450
So these are the two approaches.

26
00:01:44,450 --> 00:01:50,850
One is reversing elements and once again reversing elements and then reversing links.

27
00:01:51,530 --> 00:01:57,570
So in a list we prefer reversing links rather than reversing elements y.

28
00:01:57,950 --> 00:01:59,210
I'll tell you afterwards.

29
00:01:59,690 --> 00:02:05,870
So first, let us look at the method, how we can reverse the elements, reversing the elements.

30
00:02:06,170 --> 00:02:13,490
One method is I can take an array of size, the same as the number of elements in the link, like there

31
00:02:13,490 --> 00:02:14,360
are four elements.

32
00:02:14,610 --> 00:02:20,120
So I should take an array of size four so I have another ready then what I should do.

33
00:02:20,120 --> 00:02:24,770
I should copy all these elements in this order after copying all the elements.

34
00:02:24,830 --> 00:02:30,800
Then again, a reverse copy, all the elements in that linked list for the elements will be reversed.

35
00:02:31,160 --> 00:02:32,300
So copy those elements.

36
00:02:32,300 --> 00:02:39,230
First, let us run the procedure and C copy this element because the displays and then move B to the

37
00:02:39,230 --> 00:02:42,710
next element and move it to the next index.

38
00:02:43,370 --> 00:02:48,460
Copy BP's data here on the move B and move.

39
00:02:48,470 --> 00:02:51,860
I copy BP's data six here.

40
00:02:52,370 --> 00:02:56,090
Move B and move I.

41
00:02:58,140 --> 00:03:03,250
Then copy that data also here and becomes null and move I.

42
00:03:04,080 --> 00:03:11,100
This is one fact I have copied all the elements after copying all the elements again bring beyond the

43
00:03:11,100 --> 00:03:14,250
first node and decrement I.

44
00:03:15,300 --> 00:03:19,140
I was outside that and I'll bring it back on the last element.

45
00:03:19,820 --> 00:03:20,660
No more to do.

46
00:03:21,330 --> 00:03:22,080
Copy this.

47
00:03:22,080 --> 00:03:33,990
If I in here make it eight and move B decrement I saw in the second degree and copying the other four

48
00:03:33,990 --> 00:03:35,370
six is copied here for this.

49
00:03:35,370 --> 00:03:36,880
Copy that and two is copied.

50
00:03:37,320 --> 00:03:40,590
So this becomes six and this becomes four and this becomes two.

51
00:03:41,160 --> 00:03:42,810
So I don't have to explain the whole thing.

52
00:03:43,320 --> 00:03:44,970
So I guess you have understood.

53
00:03:46,260 --> 00:03:53,910
So by taking on extra auxiliary Xstrata, we can call it as auxiliary urry, then in that we can copy

54
00:03:53,910 --> 00:04:00,240
the elements as it is, then reverse copy them link so you can see that the elements has changed their

55
00:04:00,240 --> 00:04:01,380
laws here.

56
00:04:01,410 --> 00:04:04,940
This note was meant for two, but now eight is copied there.

57
00:04:05,160 --> 00:04:08,430
So the elements, new elements are eight, six, four and two.

58
00:04:09,960 --> 00:04:17,070
So let me ride on the procedure, the code for this one, so I need a pointer be on for Snoad initially

59
00:04:17,370 --> 00:04:21,800
and I assume that are already existing, right?

60
00:04:21,890 --> 00:04:23,930
I'm not saying creation of an array.

61
00:04:24,930 --> 00:04:35,150
Then what I have to do will be is not equal to null until you reach the null copy the element in aof

62
00:04:35,190 --> 00:04:37,510
I frumpies data.

63
00:04:38,910 --> 00:04:45,360
Then afterwards move to the next node and also move it to next index.

64
00:04:47,220 --> 00:04:49,920
Now, this loop will be keeping all the elements.

65
00:04:51,230 --> 00:04:58,910
Then after that, you saw that when P was bring again P on for small, so bring P again on first note,

66
00:05:00,650 --> 00:05:07,490
then DiClemente, I was outside so I said Degremont I so I would write in the same line I minus minus.

67
00:05:07,520 --> 00:05:09,120
So I will come on this last note.

68
00:05:10,510 --> 00:05:13,520
Then I should copy these elements back in this one.

69
00:05:13,560 --> 00:05:14,640
So, again, I need a loop.

70
00:05:14,980 --> 00:05:28,030
So while B is not equal to this time, do what in Picards data, copy the element from a fine and also

71
00:05:28,030 --> 00:05:33,190
Degremont I so I should be DiClemente and B should be moving forward only.

72
00:05:33,400 --> 00:05:35,800
So be assigned B's next.

73
00:05:35,890 --> 00:05:37,570
So P will be moving forward.

74
00:05:38,910 --> 00:05:39,570
This the loop.

75
00:05:40,120 --> 00:05:42,570
So I have written the piece of program code.

76
00:05:42,580 --> 00:05:48,010
It's not a complete function or not a complete program, just the steps that are required for performing

77
00:05:48,010 --> 00:05:48,760
this operation.

78
00:05:49,040 --> 00:05:55,510
So you can see that this loop is copying all the elements from Linklaters to Ari and this loop is copying

79
00:05:55,510 --> 00:06:00,450
all the elements from array to list so the elements will be reversed.

80
00:06:00,760 --> 00:06:01,630
Analysis.

81
00:06:02,170 --> 00:06:08,380
This method needs extra space array equal to the cipher for Lindqvist.

82
00:06:09,400 --> 00:06:13,630
Then time taken by this procedure is first copying all the elements here.

83
00:06:13,810 --> 00:06:19,330
It's outdraw and this is and then again copying all these elements back there.

84
00:06:19,510 --> 00:06:22,000
S and so two times.

85
00:06:22,000 --> 00:06:25,010
And so this is doing it's not N Square.

86
00:06:25,030 --> 00:06:29,650
I remember this first time all the elements are copied from there to this one.

87
00:06:30,840 --> 00:06:34,330
Then second time all the elements are copied from areto linked list.

88
00:06:34,600 --> 00:06:38,200
So this is separate at two different and so total it is two.

89
00:06:38,200 --> 00:06:40,330
And so the times order of N.

90
00:06:42,140 --> 00:06:48,530
First, always or often, it has to access all the elements and willingness was always part of an.

