1
00:00:00,510 --> 00:00:04,650
They learn about inserting in a binary search tree how to insert.

2
00:00:05,480 --> 00:00:11,750
Suppose I want to insert a key value thirty eight, so by using this example, by necessity, let us

3
00:00:11,750 --> 00:00:13,690
learn how to insert target.

4
00:00:14,210 --> 00:00:19,200
So the first step for insertion is search for key value target.

5
00:00:19,820 --> 00:00:24,860
If this 38 is already present in binary search, then we should not insert it.

6
00:00:25,160 --> 00:00:26,200
We should not get started.

7
00:00:26,870 --> 00:00:32,240
So first of all, find out whether it is there or not there so far that we have to search.

8
00:00:32,780 --> 00:00:34,970
So the first step is search.

9
00:00:34,970 --> 00:00:37,330
And already we know how to search for a key.

10
00:00:37,760 --> 00:00:41,130
Let us repeat the process and search for the study.

11
00:00:41,720 --> 00:00:48,140
So we will start searching from years from digging a pointer to and let us start searching for 38 28

12
00:00:48,140 --> 00:00:48,620
key.

13
00:00:48,800 --> 00:00:49,490
Is it equal?

14
00:00:49,530 --> 00:00:51,050
No, it's a greater than this one.

15
00:00:51,080 --> 00:00:53,440
So move beyond the right hand side.

16
00:00:53,960 --> 00:00:57,380
OK, then, 40, 38, 38.

17
00:00:57,380 --> 00:00:58,010
Is it equal?

18
00:00:58,010 --> 00:00:59,830
No, 38 is smaller than that one.

19
00:01:00,140 --> 00:01:02,030
So move to the left hand side.

20
00:01:03,920 --> 00:01:06,710
Thirty five, thirty eight, is it equal No.

21
00:01:06,950 --> 00:01:14,780
38 is greater than this one, so moved up on the right hand side, so he became none, so they became

22
00:01:14,780 --> 00:01:15,100
null.

23
00:01:15,290 --> 00:01:18,620
The element is not there, 38 is not there in binary search.

24
00:01:18,820 --> 00:01:20,120
So we can insert it.

25
00:01:20,480 --> 00:01:22,000
So where it should get inserted.

26
00:01:22,490 --> 00:01:26,030
See, we were searching for thirty eight and we have terminated here.

27
00:01:26,330 --> 00:01:34,340
So it means that this was the place of thirty eight if suppose 38 is there and the link if suppose 38

28
00:01:34,340 --> 00:01:36,830
is there in the exhausted and it must be then.

29
00:01:37,750 --> 00:01:43,160
It is greater than and less than 14, so it should be the right child of 25.

30
00:01:43,930 --> 00:01:48,280
So, yes, inserted at the same place that was inserted.

31
00:01:48,700 --> 00:01:53,530
So I should create a new node and the link, it would write it off you.

32
00:01:53,710 --> 00:01:54,260
That's it.

33
00:01:54,340 --> 00:01:55,540
So it can be inserted.

34
00:01:56,140 --> 00:02:00,170
But for linking with the return of Tardif, I need a pointer on that.

35
00:02:00,190 --> 00:02:01,390
If I do, I have a pointer.

36
00:02:01,390 --> 00:02:06,280
And I know he actually became known for inserting Tuqay.

37
00:02:06,550 --> 00:02:11,810
I should create a new node and link it to the right of thirty five that it will be inserted.

38
00:02:12,220 --> 00:02:14,640
So we need a pointer on thirty five also.

39
00:02:15,280 --> 00:02:20,320
So for this we can have a tail pointer so let us use a little pointer.

40
00:02:20,330 --> 00:02:23,710
So like insurge for thirty eight along with the tail pointer.

41
00:02:23,800 --> 00:02:29,380
Let us start is the pointing here and R is also one more point of that.

42
00:02:29,380 --> 00:02:29,890
Is month.

43
00:02:30,610 --> 00:02:31,560
Is it matching.

44
00:02:31,600 --> 00:02:32,830
No this is great.

45
00:02:33,100 --> 00:02:37,780
So move beyond rightside but before that bring on here and.

46
00:02:39,600 --> 00:02:44,560
Move to your right side, is it matching with 40 no, 38 is less.

47
00:02:44,910 --> 00:02:46,770
So bring are upon P.

48
00:02:48,990 --> 00:02:55,800
And move people left China, is it matching with 35 and 38, they are matching No.

49
00:02:56,130 --> 00:02:57,090
38 is greater.

50
00:02:57,300 --> 00:02:58,320
It should be on the right side.

51
00:02:58,590 --> 00:03:06,390
So bring are upon this node and move to do the right China, not to be given criminal.

52
00:03:06,690 --> 00:03:07,160
They begin.

53
00:03:07,170 --> 00:03:08,880
Another element is not there.

54
00:03:09,040 --> 00:03:10,740
We can insert it there.

55
00:03:12,410 --> 00:03:16,470
Rachel of thirty five, who will help now point it out, will help.

56
00:03:17,060 --> 00:03:22,430
So we need a telling point that otherwise we should again search from the root.

57
00:03:22,730 --> 00:03:24,440
So the procedure is similar to search.

58
00:03:24,660 --> 00:03:31,010
Only thing we need is telling point up and if it became null, create and insert a note, that's all.

59
00:03:31,370 --> 00:03:32,860
So I will try it on the procedure.

60
00:03:33,380 --> 00:03:39,330
So insert function pointer to root to the pointer group and the key that we want to insert.

61
00:03:39,980 --> 00:03:42,240
So for this we need a tailwind.

62
00:03:42,260 --> 00:03:46,550
That's why we declare a pointer on which will be initially null.

63
00:03:47,240 --> 00:03:49,160
I have to just search for the key.

64
00:03:49,190 --> 00:03:49,510
Right.

65
00:03:49,520 --> 00:03:50,390
Search for the key.

66
00:03:50,570 --> 00:03:55,220
So for searching we need a loop that is Ts not equal to no.

67
00:03:55,400 --> 00:03:58,090
Then we have to check and move would be.

68
00:03:58,340 --> 00:04:07,860
But before that I should come up on t then check if key is equal to piece of data, if it is matching

69
00:04:07,880 --> 00:04:09,970
this key element is already there.

70
00:04:10,220 --> 00:04:12,590
So don't do anything, just return.

71
00:04:14,000 --> 00:04:17,899
So even if you have what you can set it on, but it doesn't really just stop the function.

72
00:04:18,880 --> 00:04:22,810
If matches found, I don't have to insert else.

73
00:04:24,880 --> 00:04:36,150
If a kid is less than a day later, then move on left child, let's move Dearborn, right.

74
00:04:37,030 --> 00:04:41,530
This process will continue either if the Keys already found, it will stop.

75
00:04:41,890 --> 00:04:48,280
If the keys are not found, they will become null and stop and virata will be or will be up on the Nord

76
00:04:48,910 --> 00:04:49,850
just before T..

77
00:04:50,380 --> 00:04:52,670
So using are we have to link a new normal.

78
00:04:52,690 --> 00:04:58,360
So first of all, create a new node and inserted there so then we can do it for T has become null and

79
00:04:58,360 --> 00:05:01,330
it has came out of the loop for creating a node.

80
00:05:01,330 --> 00:05:02,500
I need one more pointer.

81
00:05:04,630 --> 00:05:09,520
I need one more point, so I'll take a point on the beat, then here I relied on the.

82
00:05:10,030 --> 00:05:17,110
So here I have created a new node and also set the data that is key value and made a left and right.

83
00:05:18,340 --> 00:05:24,250
So this is all I have done using Pointer P, so I'll just remove P here and create a new node with the

84
00:05:24,250 --> 00:05:28,490
help of pointer P enter the key and make this.

85
00:05:28,490 --> 00:05:29,000
That's none.

86
00:05:29,440 --> 00:05:36,100
So these are the steps I have done that this AP should be to the left side.

87
00:05:36,160 --> 00:05:36,490
Right.

88
00:05:37,300 --> 00:05:38,580
So I should check it again.

89
00:05:38,690 --> 00:05:39,040
Right.

90
00:05:39,250 --> 00:05:43,090
I for my example it comes out if I change, but before linking I should check it.

91
00:05:43,450 --> 00:05:47,310
So if this key is less then these data points should come left.

92
00:05:47,920 --> 00:05:49,690
Otherwise it should come as no change.

93
00:05:50,020 --> 00:05:51,520
So here I will write on the code.

94
00:05:51,880 --> 00:05:58,360
If BP's data is less than honest data, then US left child should be B otherwise.

95
00:05:58,360 --> 00:06:00,830
Else our child should be.

96
00:06:01,990 --> 00:06:03,180
So check once again.

97
00:06:03,190 --> 00:06:08,660
And so this will be connected to this one as I wrote that song.

98
00:06:09,130 --> 00:06:11,920
So inflation is more like searchingly.

99
00:06:12,200 --> 00:06:15,760
So the time for inserting is outdraw login only.

100
00:06:16,120 --> 00:06:23,410
That is height of a binary search for creating and tarby the I should first of all create root.

101
00:06:23,800 --> 00:06:25,240
Then I stop the keys.

102
00:06:25,240 --> 00:06:27,910
I can insert them by using this function.

103
00:06:29,010 --> 00:06:35,890
So next next, let us see, we will take some key values and learn how to create a binary search team

104
00:06:35,910 --> 00:06:38,040
by inserting the keys one by one.

