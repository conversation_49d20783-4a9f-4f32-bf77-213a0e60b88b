1
00:00:00,880 --> 00:00:05,480
We will do analysis of height versus n this type of analysis.

2
00:00:05,500 --> 00:00:10,950
We have already done on binary trees, so I did not do much explanation here directly.

3
00:00:10,960 --> 00:00:12,480
I will try to write on the formula.

4
00:00:13,120 --> 00:00:19,540
We have to get the formula for height versus n of strict retries.

5
00:00:19,840 --> 00:00:20,190
Right.

6
00:00:20,920 --> 00:00:24,430
But as an example, I have taken a strict tree every tree.

7
00:00:25,060 --> 00:00:28,960
So from these example trees, we will get the formula for a tree.

8
00:00:29,470 --> 00:00:35,500
Let us look at this C height of our trees given as a tool and these are a minimum number of nodes it

9
00:00:35,500 --> 00:00:35,980
can have.

10
00:00:35,980 --> 00:00:40,260
These are maximum nodes it can have and the height is here three.

11
00:00:40,480 --> 00:00:42,310
So these are the minimum nodes it can have.

12
00:00:42,310 --> 00:00:44,260
These are the maximum it can have.

13
00:00:44,530 --> 00:00:45,550
These are also nodes.

14
00:00:45,740 --> 00:00:47,980
OK, so I need the formula.

15
00:00:47,980 --> 00:00:51,990
If height is given minimum human nodes, maximum homeloans.

16
00:00:52,240 --> 00:00:56,490
So let us write the formula, let us observe and write the formula.

17
00:00:57,040 --> 00:00:59,110
See how the students how many animals are there.

18
00:00:59,590 --> 00:01:01,870
Three plus three plus one.

19
00:01:02,140 --> 00:01:04,660
So three plus three plus one.

20
00:01:04,930 --> 00:01:07,170
It's nothing but two and two, three plus one.

21
00:01:07,180 --> 00:01:07,480
Right.

22
00:01:07,810 --> 00:01:08,770
Number of nodes.

23
00:01:09,730 --> 00:01:12,460
Then here again it is minimum three plus three plus three.

24
00:01:12,670 --> 00:01:16,350
So this is three and two, three plus one number of nodes are there.

25
00:01:16,660 --> 00:01:22,610
So it means if, if height is given the minimum nodes, how many minimum nodes are there?

26
00:01:22,960 --> 00:01:30,910
See this tree is the degree and two is height, three is a degree and three is HitIer.

27
00:01:31,180 --> 00:01:31,600
So.

28
00:01:32,860 --> 00:01:35,150
Three into two plus one.

29
00:01:35,350 --> 00:01:43,240
So what was a hiatus in the three plus one, but actually this three year degree of a three, so I should

30
00:01:43,240 --> 00:01:46,180
call it as Emma, so I would rewrite the formula.

31
00:01:46,510 --> 00:01:49,360
This is into each plus one.

32
00:01:50,330 --> 00:01:51,200
So that's the formula.

33
00:01:51,250 --> 00:01:57,550
So in that strict military minimum number of notes will be an equal to match plus one.

34
00:01:58,670 --> 00:02:07,490
Then the maximum north, how many, let us observe one, then three, then nine.

35
00:02:08,180 --> 00:02:13,730
What about this one, then three, the nine, then twenty seven.

36
00:02:13,730 --> 00:02:20,000
If I write them, this is one and this is three and this is three squared, this is three.

37
00:02:20,010 --> 00:02:26,470
Q So this is one plus three plus three squared plus three cube.

38
00:02:26,960 --> 00:02:29,680
So these are the terms of ICDs.

39
00:02:29,690 --> 00:02:30,350
Yes.

40
00:02:30,530 --> 00:02:33,290
But what is this three three year degree.

41
00:02:33,530 --> 00:02:42,830
And so I will rewrite it like this one plus and plus M squared plus MQ plus goes on up to whatever the

42
00:02:42,830 --> 00:02:43,520
height is.

43
00:02:43,760 --> 00:02:53,780
So this GPCRs if I add then if you remember the formula for this, I read on the form e r bovver K plus

44
00:02:53,780 --> 00:02:59,360
one, minus one by R minus one is the formula so far this series.

45
00:02:59,390 --> 00:03:02,190
What is the formula is what.

46
00:03:02,190 --> 00:03:03,980
Here it is one.

47
00:03:03,990 --> 00:03:05,480
Okay, don't write it then.

48
00:03:05,480 --> 00:03:08,990
Our common ratio is what m power.

49
00:03:09,320 --> 00:03:10,070
Guess what.

50
00:03:10,070 --> 00:03:11,690
Each plus one.

51
00:03:12,140 --> 00:03:12,980
Minus one.

52
00:03:13,580 --> 00:03:15,230
So mPower plus one.

53
00:03:15,230 --> 00:03:18,920
Minus one divided by M minus one.

54
00:03:19,160 --> 00:03:20,600
This is M minus one.

55
00:03:21,540 --> 00:03:22,910
Yes, this is the formula.

56
00:03:23,830 --> 00:03:30,600
See, once again, I tell you, maximum, how many nodes possible and stick the three or three, the

57
00:03:30,600 --> 00:03:33,660
total number of nodes are forming on this formula.

58
00:03:33,990 --> 00:03:35,220
This is for the grid three.

59
00:03:35,340 --> 00:03:36,480
I converted to a degree.

60
00:03:36,480 --> 00:03:40,890
And then this is the formula for some of the terms of GPCRs.

61
00:03:41,370 --> 00:03:42,660
This is a GPCRs.

62
00:03:42,930 --> 00:03:45,450
Then using the formula, I have added this one.

63
00:03:45,450 --> 00:03:46,920
I got a formula for this one.

64
00:03:47,580 --> 00:03:50,710
So impoverished, plus one, minus one by M minus one.

65
00:03:50,850 --> 00:03:59,410
So the maximum nodes are maximum nodes are an equal to empower each plus one minus one by M minus one.

66
00:03:59,640 --> 00:04:00,720
So that's all we got.

67
00:04:00,720 --> 00:04:04,650
The formula, if you already know the height then the minimum.

68
00:04:04,650 --> 00:04:05,430
How many nodes.

69
00:04:05,550 --> 00:04:07,200
Maximum, how many nodes.

70
00:04:07,620 --> 00:04:09,570
No other formula we have to write on.

71
00:04:09,990 --> 00:04:15,550
If we know the number of nodes, then minimum height and maximum height.

72
00:04:15,930 --> 00:04:18,269
This was minimum nodes, maximum nodes.

73
00:04:18,540 --> 00:04:20,519
So I relied on for height.

74
00:04:20,850 --> 00:04:22,740
Safe nodes are given.

75
00:04:23,640 --> 00:04:26,380
Then what is minimum height and maximum height.

76
00:04:26,700 --> 00:04:32,710
These formulas, I can get them from here, like the minimum nodes can give me maximum height formula.

77
00:04:33,090 --> 00:04:36,270
So here it is an equal to match plus one.

78
00:04:36,510 --> 00:04:37,340
What is each.

79
00:04:38,690 --> 00:04:44,750
That one, if you sign here and the minus one then comes in denominator.

80
00:04:45,320 --> 00:04:48,580
So this is equal to and minus one by M.

81
00:04:48,860 --> 00:04:49,200
Yes.

82
00:04:49,220 --> 00:04:53,600
And minus one by M, then what about this maximum N?

83
00:04:53,600 --> 00:04:55,760
The formula gives the minimum height.

84
00:04:56,120 --> 00:04:58,290
So this is having power.

85
00:04:58,490 --> 00:05:01,000
So definitely this will be in terms of log.

86
00:05:01,250 --> 00:05:04,970
So you can convert this one to paperwork and convert this.

87
00:05:04,970 --> 00:05:06,740
I will directly write on the formula that.

88
00:05:08,240 --> 00:05:16,310
See, this is the formula, lobbyism and into a minus one, plus one, then entero minus one.

89
00:05:17,180 --> 00:05:19,210
So if you convert this, you get that fun.

90
00:05:20,240 --> 00:05:26,420
So these formulas are more helpful when you want to know maximum how many nodes are possible and what

91
00:05:26,420 --> 00:05:28,930
is the minimum height, minimum height possible.

92
00:05:29,240 --> 00:05:34,410
So minimum height, you can see that it is logarithmic and the maximum height is in terms of energy.

93
00:05:34,940 --> 00:05:37,320
So this is logarithmic and this is linear.

94
00:05:38,450 --> 00:05:45,530
The purpose of all this analysis is to know the space and the time taken by trees when we really use

95
00:05:45,530 --> 00:05:47,570
them, when we use the trees.

96
00:05:47,610 --> 00:05:49,520
That time we will use these formulas.

97
00:05:49,700 --> 00:05:50,560
So right.

98
00:05:50,870 --> 00:05:52,330
That time I will not discuss.

99
00:05:52,340 --> 00:05:56,410
I will be discussing how to use them rather than discussing the formulas.

100
00:05:56,840 --> 00:05:59,390
So we are first analyzing and keeping the foreigners ready.

101
00:05:59,700 --> 00:06:01,480
Then we will use them afterwards.

102
00:06:02,420 --> 00:06:08,600
Next, one more formalized remaining, that is number of international forces, external laws, so I'll

103
00:06:08,600 --> 00:06:09,740
take the same example.

104
00:06:10,040 --> 00:06:14,210
I will remove this and let us find our number of internal and external nodes.

105
00:06:15,110 --> 00:06:21,910
So from these examples, let us find out internal and external nodes for stricter 383.

106
00:06:21,920 --> 00:06:25,300
Then we will prepare the format for any military.

107
00:06:25,880 --> 00:06:28,670
Let us observe how many rules are there?

108
00:06:28,670 --> 00:06:32,290
One to internal rules are to externals.

109
00:06:32,300 --> 00:06:33,650
One, two, three, four, five.

110
00:06:36,520 --> 00:06:41,950
Internal downloads, one, two, three, four, four, excellence, one, two, three, four, five,

111
00:06:41,950 --> 00:06:43,030
six, seven, eight, nine.

112
00:06:46,030 --> 00:06:47,800
Into the north, one, two, three.

113
00:06:49,770 --> 00:06:52,620
Extend those three, four, five, six, seven.

114
00:06:55,200 --> 00:06:56,650
Then into the Lords here.

115
00:06:56,760 --> 00:07:00,900
One, two, three, four, five, six, seven, eight, nine, 10, 11, 12, 13.

116
00:07:02,470 --> 00:07:06,430
External boards, these are three of the three that is 27.

117
00:07:08,420 --> 00:07:11,700
So can you find any pattern on the relationship between the values?

118
00:07:11,870 --> 00:07:16,220
Let me check two one five two plus one.

119
00:07:16,700 --> 00:07:23,480
OK, to include two plus one gives me five, then fall to two plus one.

120
00:07:24,020 --> 00:07:26,200
I would two in the four plus one.

121
00:07:26,480 --> 00:07:27,680
Yes, that is nine.

122
00:07:28,210 --> 00:07:30,440
Then what about this two three six plus one.

123
00:07:30,440 --> 00:07:31,130
Seven years.

124
00:07:31,650 --> 00:07:34,210
Two to three plus one gets the seven.

125
00:07:35,330 --> 00:07:36,020
What is this.

126
00:07:36,680 --> 00:07:37,600
Twenty thirteen.

127
00:07:37,610 --> 00:07:38,660
Twenty six plus one.

128
00:07:38,660 --> 00:07:40,120
Yes two into thirteen.

129
00:07:40,520 --> 00:07:43,720
That is twenty six plus one gets twenty seven.

130
00:07:44,060 --> 00:07:52,520
So it means external laws are equal to doing the number of internal laws plus one for our stricter three,

131
00:07:52,880 --> 00:07:58,670
three then for strict MRA three is equal to see.

132
00:07:58,670 --> 00:08:01,130
This was a three so I go to do so.

133
00:08:01,130 --> 00:08:05,210
For me it will be a minus one in two I plus one.

134
00:08:05,630 --> 00:08:14,690
He has this formalized for internal law enforcers, external laws for any state and territory.

