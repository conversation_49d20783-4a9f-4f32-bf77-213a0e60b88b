1
00:00:00,230 --> 00:00:03,690
Let us start with the introduction to data structures.

2
00:00:03,960 --> 00:00:08,940
Data is an integral part of our applications or programs.

3
00:00:08,940 --> 00:00:14,730
If I define a program, program is nothing but set of instructions which perform operations on data to

4
00:00:14,730 --> 00:00:22,150
get some results. So without data there is no need of instruction, no use of instructions, then the data

5
00:00:22,200 --> 00:00:23,500
term, term data,

6
00:00:23,520 --> 00:00:29,880
We use it in many places like Data Structures, databases, data warehouse, big data.

7
00:00:30,300 --> 00:00:34,200
So from this, What is actually data structure?

8
00:00:34,230 --> 00:00:38,960
So for this if we have a little bit knowledge about all other things also that would be better.

9
00:00:39,150 --> 00:00:45,810
So far so I'll start with data structures and we'll also have the brief introduction to databases and

10
00:00:45,810 --> 00:00:48,030
data warehouse, as well as big data.

11
00:00:48,870 --> 00:00:52,670
So first let us start with Introduction to data structures.

12
00:00:52,740 --> 00:00:58,770
First of all let me introduce what is data structure. Data structure can be defined as arrangement of

13
00:00:58,890 --> 00:01:07,470
collection of data items so that they can be utilized efficiently, operations on that data can be done efficiently.

14
00:01:07,470 --> 00:01:14,310
So it's all about the arrangement of data and the operations on the data that are efficient operations

15
00:01:14,310 --> 00:01:14,920
on the data.

16
00:01:15,330 --> 00:01:20,250
But the question is, Where? Inside the main memory during the execution of a program.

17
00:01:21,300 --> 00:01:24,330
So without data structure there cannot be any application.

18
00:01:24,690 --> 00:01:29,820
Every application will have a set of instructions which will perform operations on data, so data

19
00:01:29,820 --> 00:01:37,020
data is mandatory, then where the data is kept? inside the main memory. Where the programs will be? inside the main memory.

20
00:01:37,350 --> 00:01:44,210
So during the execution of a program how the program will manage data inside the main memory and perform

21
00:01:44,280 --> 00:01:45,260
the operations.

22
00:01:45,300 --> 00:01:46,860
That is data structure.

23
00:01:47,400 --> 00:01:55,670
So I'll take an example and explain you in detail how the program utilizes data and how they put the

24
00:01:55,670 --> 00:01:57,720
data inside the main memory.

25
00:01:57,720 --> 00:01:59,810
Let us look at this diagram.

26
00:02:00,420 --> 00:02:05,450
Here is a CPU, processor that is central processing unit that is our microprocessor.

27
00:02:05,680 --> 00:02:07,170
And this is main memory.

28
00:02:07,170 --> 00:02:14,970
Let us say this is a ram; and this is a storage that is hard disk drive or external storage or just call

29
00:02:14,970 --> 00:02:17,580
it storage.

30
00:02:17,580 --> 00:02:19,530
This will execute our programs.

31
00:02:19,560 --> 00:02:23,060
That is, it is a process so it will execute the instructions.

32
00:02:23,830 --> 00:02:27,600
And this is temporary memory that is working memory.

33
00:02:27,610 --> 00:02:34,520
It is also called primary memory, and this is permanent storage.

34
00:02:34,530 --> 00:02:36,040
This is permanent storage.

35
00:02:36,180 --> 00:02:42,630
So if we look into our PC, then we will have a processor, RAM and hard disk, or if we look into a

36
00:02:42,630 --> 00:02:43,520
a mobile phone,

37
00:02:43,550 --> 00:02:49,830
Then there will be a processor and there will be some RAM like 2 GB or 3 GB or 4 GB for RAM, and there

38
00:02:49,830 --> 00:02:54,490
is a storage of 16 GB, 32 GB, or, 128 GB, like this.

39
00:02:54,810 --> 00:03:01,410
Then, where do we keep our programs? when we install any program in our P.C. or on our mobile

40
00:03:01,410 --> 00:03:06,210
phone, a program or an application will get installed on storage.

41
00:03:07,380 --> 00:03:12,540
So here I am showing it as a program file, this is a program file.

42
00:03:13,050 --> 00:03:15,400
Then, where do we keep our data?

43
00:03:15,400 --> 00:03:17,670
That also, will keep it on hard disk.

44
00:03:17,670 --> 00:03:24,650
Suppose you have any pics or photos or videos or any documents that all you keep it on your hard disk

45
00:03:24,850 --> 00:03:27,090
or in your mobile phone you keep it in storage.

46
00:03:28,020 --> 00:03:36,500
So these are data files. So, I'll take one example of a commonly used application like M.S. word.

47
00:03:36,640 --> 00:03:42,820
Most Windows users use M.S. Word, commonly used software, or, NotePad

48
00:03:42,820 --> 00:03:50,980
also you can take. Assume that, this is an MS word program file, and this is document file, or

49
00:03:51,070 --> 00:03:52,550
docx file.

50
00:03:52,870 --> 00:03:56,480
And this is MS word file.

51
00:03:56,710 --> 00:04:02,770
Now through this example, I'll explain you where the data structure comes into picture and how MS word

52
00:04:02,800 --> 00:04:04,420
may need data structure.

53
00:04:04,420 --> 00:04:08,650
Now let us see how our application program runs.

54
00:04:09,250 --> 00:04:14,590
If you want to run MS Word on your P.C., or on your mobile phone, you are touching an icon or double

55
00:04:14,590 --> 00:04:19,190
clicking an icon of a MS Word, then let us see what all happens.

56
00:04:19,390 --> 00:04:24,870
This MS Word program will be brought into the main memory, so it is brought into the main memory.

57
00:04:24,970 --> 00:04:32,500
So let's say, these are all the instructions of this MS Word program, such as I'm showing lines to show

58
00:04:32,500 --> 00:04:37,210
the machine language code, or instructions of MS Word program.

59
00:04:37,210 --> 00:04:41,510
So whenever you want to run an application the application code or the program code has to be brought

60
00:04:41,510 --> 00:04:42,900
into the main memory.

61
00:04:43,790 --> 00:04:49,900
Then once it is brought in CPU will start executing this MS word application so you will see a window

62
00:04:49,930 --> 00:04:55,430
appearing on the screen and then all the menu options everything comes up and you can start using MS Word.

63
00:04:55,530 --> 00:05:04,940
Now, if you want to open a file, document file in your MS Word application,

64
00:05:05,150 --> 00:05:09,480
Suppose this program wants to access this data in this file.

65
00:05:09,490 --> 00:05:14,920
Then this data also has to be brought into the main memory. So let us say,

66
00:05:14,920 --> 00:05:16,140
This is data,

67
00:05:16,210 --> 00:05:18,670
This is some word document file.

68
00:05:19,090 --> 00:05:21,450
So data is also brought into the main memory.

69
00:05:21,470 --> 00:05:27,940
Now, from this I can explain that, I can say that, a program has to be brought into the main memory for

70
00:05:27,940 --> 00:05:34,930
its execution, as well as data has to be brought into the main memory for processing on the data, so that

71
00:05:34,930 --> 00:05:39,790
these instructions can perform operation on the data. Program can not directly process

72
00:05:39,790 --> 00:05:41,500
the data keeping it on the storage,

73
00:05:41,500 --> 00:05:44,740
The data has to be brought into the main memory.

74
00:05:44,970 --> 00:05:53,680
Now, from this we can say that every application deals with some data, whether it is MS Word or if

75
00:05:53,680 --> 00:05:59,350
it is NotePad, then the NotePad file, text file has to be brought into the main memory; if it is chrome,

76
00:05:59,380 --> 00:06:02,460
you are browsing any web page, then that page,

77
00:06:02,460 --> 00:06:04,190
when it is coming from internet,

78
00:06:04,210 --> 00:06:06,040
It has to be in the main memory.

79
00:06:06,040 --> 00:06:13,720
So every application on your computer system or any mobile phone deals with some data, and that data

80
00:06:13,720 --> 00:06:21,400
has to be inside main memory. How you organize data inside the main memory, So that it can be easily

81
00:06:21,400 --> 00:06:26,170
used by this application efficiently, utilized by this application program?

82
00:06:26,710 --> 00:06:28,800
So how you organize the data?

83
00:06:28,990 --> 00:06:36,700
So the arrangement or organizing of the data inside the main memory for efficient utilization by the

84
00:06:36,970 --> 00:06:40,920
application that arrangement is called as a Data Structure.

85
00:06:41,560 --> 00:06:48,070
So data structures are formed in the main memory, during the execution time of a program.

86
00:06:48,070 --> 00:06:50,240
When the program runs it needs the data.

87
00:06:50,290 --> 00:06:55,460
So the question how it will arrange the data in the main memory for performing its operations.

88
00:06:55,510 --> 00:07:02,160
So that arrangement is called as data structures. So a data structure is a part of running program.

89
00:07:02,180 --> 00:07:09,220
You may be knowing different data structures, like arrays or linked lists or trees or hash tables, whatever the

90
00:07:09,220 --> 00:07:14,840
data structure is suitable, application can use that particular data structure here for arranging its

91
00:07:14,880 --> 00:07:15,880
data.

92
00:07:16,070 --> 00:07:21,210
Data may be a text data, or a multi media data like images or videos,

93
00:07:21,280 --> 00:07:26,460
Lot of contents maybe there, in the form of data; so all those contents, how they are organized in the

94
00:07:26,460 --> 00:07:27,520
main memory.

95
00:07:27,520 --> 00:07:35,530
So we have to design some data structure so that the application can use it perfectly or more efficiently

96
00:07:35,530 --> 00:07:39,610
and application should work faster or process faster over that data.

97
00:07:40,780 --> 00:07:41,770
So that's it.

98
00:07:41,770 --> 00:07:43,600
So I have given you the idea,

99
00:07:43,810 --> 00:07:46,390
What is data structure, and

100
00:07:46,480 --> 00:07:48,930
where it will be in your computer system.

101
00:07:49,300 --> 00:07:50,850
So it is not on the disk,

102
00:07:50,950 --> 00:07:52,390
It is inside the main memory.

103
00:07:52,630 --> 00:07:56,930
So without data structure, we cannot develop any application.

104
00:07:57,210 --> 00:08:00,310
Now, let me give you some idea about databases.

105
00:08:00,550 --> 00:08:08,350
See, when the data is larger in size, or commercial data that is used in businesses, like banks

106
00:08:08,410 --> 00:08:14,200
or retail stores or manufacturing farms, they will have a lot of data, and they will have some organized

107
00:08:14,220 --> 00:08:20,380
data in the form of database tables or relational data, and where they keep that relational

108
00:08:20,380 --> 00:08:23,300
data, all that data is stored on the disk.

109
00:08:23,620 --> 00:08:28,270
So I'll just remove this, and show you a relational table that is kept on the disk.

110
00:08:28,270 --> 00:08:34,150
Now you can see here, that on the disk, on the hard disk, there's a table, database table. I'm showing

111
00:08:34,169 --> 00:08:35,280
only one table,

112
00:08:35,289 --> 00:08:36,330
There maybe many tables.

113
00:08:36,330 --> 00:08:41,289
Data maybe spread out across various database tables and is organized in the form of tables and the

114
00:08:41,289 --> 00:08:43,600
tables are having a relationship between them.

115
00:08:43,600 --> 00:08:48,740
So mostly, commercial data is stored in the form of data tables.

116
00:08:48,740 --> 00:08:54,530
Now, if any application program is using that database then data has to be brought into the main memory,

117
00:08:54,540 --> 00:08:55,720
Then only you can use it.

118
00:08:55,960 --> 00:08:57,840
So again you need data structure here.

119
00:08:57,970 --> 00:09:03,960
When you are pulling the data from the hard disk or from the storage to the main memory during execution

120
00:09:04,000 --> 00:09:05,940
then you need definitely data structure here.

121
00:09:06,700 --> 00:09:10,150
So arrangement of data here in the main memory is data structure,

122
00:09:10,270 --> 00:09:14,670
Then how this data is organized in form of table on the disk,

123
00:09:14,680 --> 00:09:16,240
This is database.

124
00:09:16,240 --> 00:09:23,350
Just brief introduction I'm giving you. A database means arranging the data in some model like

125
00:09:23,350 --> 00:09:30,040
say a relational model in the permanent storage, so that it can be retrieved or to accessed by applications

126
00:09:30,190 --> 00:09:32,060
easily,

127
00:09:32,170 --> 00:09:35,180
That arrangement in the hard disk, or in the permanent storage,

128
00:09:35,200 --> 00:09:40,140
It's called as database. Then next,

129
00:09:40,160 --> 00:09:45,430
Let us go to the next term. Like I was talking about commercial data,

130
00:09:45,530 --> 00:09:51,050
That is the data used in businesses, so they will have a huge amount of data daily coming in,

131
00:09:51,170 --> 00:09:56,980
Like many customers are making transactions or lot of goods are manufactured and sold.

132
00:09:57,230 --> 00:10:03,650
So the data size will be growing day by day large and large. So large sized data, or very large data,

133
00:10:03,680 --> 00:10:06,140
that may not be used daily, right?

134
00:10:06,240 --> 00:10:10,290
Like 1 year old data, or 10 year old data, that may not be used now.

135
00:10:10,310 --> 00:10:17,570
So commercial data can be categorized into 2, that is operational data or legacy data, old data.

136
00:10:17,840 --> 00:10:27,130
Operational data that is used daily, and Legacy data can be kept as storage somewhere, if required

137
00:10:27,370 --> 00:10:33,980
we can fetch that data and use it, or you can say historical data. So, that historical data can be kept

138
00:10:34,130 --> 00:10:43,580
on array of disk, just because, imagine 10 years of data, or 50 years of data, is kept on, not just

139
00:10:43,580 --> 00:10:45,650
one disk, an array of disk.

140
00:10:45,650 --> 00:10:52,750
So the large size data which is kept here that is acting as a historical data for any commercial form,

141
00:10:52,820 --> 00:10:55,010
This is data warehouse.

142
00:11:00,170 --> 00:11:04,600
This is a data warehouse. So mostly commercial forms will have their

143
00:11:04,600 --> 00:11:05,780
Data warehouses.

144
00:11:06,080 --> 00:11:14,060
That data warehouse is helpful for analyzing the business, or making the policies, or starting a new trend,

145
00:11:14,510 --> 00:11:21,560
or giving offers to the customers, dealing with the customers, so for that, the previous data, old data will

146
00:11:21,560 --> 00:11:22,500
help in

147
00:11:22,580 --> 00:11:29,040
organization and taking decisions. So, this large size data is data warehouse, and the algorithms written

148
00:11:29,120 --> 00:11:36,410
for analyzing that data are data mining algorithms.
Data Mining Algorithms.

149
00:11:36,740 --> 00:11:41,870
So wherever data is used, I'm explaining about that, see this is database.

150
00:11:41,890 --> 00:11:43,160
Right.

151
00:11:43,910 --> 00:11:45,710
Database.

152
00:11:45,890 --> 00:11:47,470
This is data structure.

153
00:11:50,730 --> 00:11:56,790
So, Data Structure, inside the main memory, during the execution. Database, on the disk and large size

154
00:11:56,790 --> 00:12:03,090
data. And the huge size data, which is inactive now, and when required it is utilized,

155
00:12:03,090 --> 00:12:09,210
That is Data Warehouse. And the last term, that is, Big Data is that term used nowadays,

156
00:12:11,820 --> 00:12:17,190
Big Data. With the start of internet, a huge sized data is accumulating,

157
00:12:17,190 --> 00:12:24,960
day by day in Internet, that data is about things, about people, about places, lot of data is

158
00:12:24,960 --> 00:12:26,200
available in the Internet.

159
00:12:26,250 --> 00:12:34,080
And by analyzing that data we can take a lot of decisions, that is for management, for governance, or for

160
00:12:34,080 --> 00:12:35,190
businesses.

161
00:12:35,250 --> 00:12:38,620
Analysis is very useful about that data.

162
00:12:38,850 --> 00:12:47,450
Storing and utilizing that very large sized data, that study is Big Data. That study is Big Data. Now,

163
00:12:47,580 --> 00:12:51,020
for a beginner, these are maybe confusing terms,

164
00:12:51,050 --> 00:12:56,980
So I have separately defined them, where each is used.

165
00:12:56,980 --> 00:13:02,220
Again I repeat, Data Structure is in main memory, during the execution of a program, arrangement of data

166
00:13:02,220 --> 00:13:09,930
here. And arrangement of data on the disk is Database; Arrangement of data in the array of disk is

167
00:13:10,020 --> 00:13:10,830
Data Warehouse.

168
00:13:10,830 --> 00:13:12,730
That is not operational,

169
00:13:12,810 --> 00:13:17,710
That is not used day to day. And, the Big Data, that is used in Internet.

170
00:13:17,850 --> 00:13:22,380
So that's all, I have given you different places where data is used.

171
00:13:22,380 --> 00:13:24,660
Now our subject is Data Structures.

172
00:13:24,750 --> 00:13:26,780
So let us start with Data Structures in

173
00:13:26,850 --> 00:13:27,960
Next video onwards.

