1
00:00:00,560 --> 00:00:07,280
And this video will see <PERSON><PERSON><PERSON> more a function for selection sort, which we have already discussed

2
00:00:07,280 --> 00:00:14,060
on Lightwood, I'm using the same project that is used for sorting Solidere in such and such is the

3
00:00:14,060 --> 00:00:14,820
same program.

4
00:00:15,290 --> 00:00:20,120
So instead the same project, I will add one more function that is for selection Sarte.

5
00:00:20,360 --> 00:00:21,200
So let us write.

6
00:00:23,440 --> 00:00:28,570
Selection sort, it takes an array of elements and a number of elements and.

7
00:00:29,670 --> 00:00:37,580
Now, I need a few variables like IJI and Key, then for the process, I should have I starts from zero,

8
00:00:38,100 --> 00:00:41,400
I use less than I need and minus one percent.

9
00:00:41,400 --> 00:00:43,170
So I plus plus.

10
00:00:43,170 --> 00:00:44,270
So it is less than ten.

11
00:00:44,280 --> 00:00:44,910
Minus one.

12
00:00:46,030 --> 00:00:54,700
The next follow up that is for each boss as well as key, should start from AI and it should reach the

13
00:00:54,700 --> 00:00:57,850
last element and G plus plus.

14
00:00:59,220 --> 00:01:08,160
And every time we check that if the element of gee is smaller than element that is off key, then we

15
00:01:08,160 --> 00:01:09,990
will bring that position.

16
00:01:10,020 --> 00:01:10,410
Jamie?

17
00:01:11,940 --> 00:01:17,130
And this is not for all of us for the past, after the end of the past, we will swab the elements out

18
00:01:18,180 --> 00:01:21,870
of iron so some functionality is available.

19
00:01:21,870 --> 00:01:24,870
So Busoni of A and ampersand.

20
00:01:27,310 --> 00:01:28,890
Eight of king.

21
00:01:30,780 --> 00:01:35,190
Next on the search, sort all the elements already we have seen it's working.

22
00:01:36,130 --> 00:01:39,490
Now, here, instead, the main function I will call selection sort.

23
00:01:42,580 --> 00:01:49,210
By passing around a number of elements and here it is displaying all elements, let us see whether it

24
00:01:49,210 --> 00:01:50,350
will sort the elements.

25
00:01:53,790 --> 00:01:59,910
Yes, two, three, four, five, six, seven, nine, 10, 11, 13, it has hazarded, let us change

26
00:01:59,910 --> 00:02:05,740
some elements like this is 16 and this is 24 and this is 12.

27
00:02:06,240 --> 00:02:07,530
Now, let us run and see.

28
00:02:07,530 --> 00:02:08,850
I've changed a few elements.

29
00:02:09,990 --> 00:02:14,410
So three five seven nine, 10, 11, 12, 13, 16 and 24.

30
00:02:14,460 --> 00:02:16,050
Yes, it is working perfectly.

31
00:02:17,940 --> 00:02:22,980
So that's all these are all simple functions, that is a simple function procedure for selection.

32
00:02:23,880 --> 00:02:25,020
So that's all in this video.

