1
00:00:01,760 --> 00:00:05,290
The topic in this video is a black actress.

2
00:00:05,890 --> 00:00:13,930
I'm going to explain the properties of red black trees and how to insert and how to generate a red black

3
00:00:13,930 --> 00:00:14,190
tree.

4
00:00:14,560 --> 00:00:20,020
These are the two things we will learn in the studio properties and creation of red BlackBerry.

5
00:00:20,710 --> 00:00:22,390
So let us look at the properties.

6
00:00:22,850 --> 00:00:28,000
See, first of all, red BlackBerry is a hydra balanced binary search tree.

7
00:00:28,100 --> 00:00:31,420
So it's a by necessity, you have already studied about by the tree.

8
00:00:31,810 --> 00:00:39,020
There is a topic available and also we have started about Aviel trees that are highly balanced by necessity.

9
00:00:39,040 --> 00:00:42,160
So this is also highly balanced by the research tree.

10
00:00:43,420 --> 00:00:49,390
If you remember, in Aviel, we were trying to balance the height, but performing rotations, if any

11
00:00:49,390 --> 00:00:56,290
balance factor is related similar to what we do here, we perform rotations if the black conditions

12
00:00:56,290 --> 00:00:56,980
are monitored.

13
00:00:57,250 --> 00:00:58,750
So it's more similar to a.

14
00:01:00,010 --> 00:01:07,170
But this idea of the black tree originates from two, three, four trees.

15
00:01:07,330 --> 00:01:10,060
So we already have a topic called two, three, four trees.

16
00:01:10,060 --> 00:01:14,530
If you are perfect in that one, if you have practiced that one, then this is nothing.

17
00:01:14,530 --> 00:01:17,110
Red Black Tree is very, very simple.

18
00:01:17,350 --> 00:01:22,810
If you have the clear idea of two, three, four trees and even I'm going to relate it with a real black

19
00:01:22,810 --> 00:01:25,240
tree with two, three, four tree in the coming video.

20
00:01:26,080 --> 00:01:27,940
Now let us look at other property.

21
00:01:28,780 --> 00:01:33,940
Every node is either red or black, so nodes will have color, dark red or black.

22
00:01:33,940 --> 00:01:37,680
Some people take it just as color or molds as color.

23
00:01:37,690 --> 00:01:41,730
So we'll follow nodes as color nodes are either red or black.

24
00:01:41,950 --> 00:01:43,780
That's why it is called red electric.

25
00:01:44,470 --> 00:01:49,350
Then a root of a tree is always black, so root for trees, always black.

26
00:01:49,360 --> 00:01:51,980
So here you can see I have taken an example of red blackboy.

27
00:01:52,360 --> 00:01:55,300
The fruit is black, so hold of a tree.

28
00:01:55,480 --> 00:01:57,940
That is the mean nor the first note is always black.

29
00:01:58,630 --> 00:02:01,150
Then nulls are also taken as black.

30
00:02:01,330 --> 00:02:06,220
See here after this node there a leaf node so it doesn't have any child.

31
00:02:06,580 --> 00:02:08,050
So but the null are there.

32
00:02:08,050 --> 00:02:13,240
So if you know the linked representation of a binary tree, if you take the node, you'll have a key

33
00:02:13,240 --> 00:02:13,690
value.

34
00:02:13,690 --> 00:02:16,530
And if it is a leaf node, then these values will be not.

35
00:02:16,540 --> 00:02:17,710
These points would be null.

36
00:02:18,100 --> 00:02:20,970
So those nulls are also treated as black.

37
00:02:21,490 --> 00:02:23,830
So I'm highlighting them as black here.

38
00:02:24,130 --> 00:02:26,550
So nulls are also black now.

39
00:02:26,570 --> 00:02:35,680
Next thing, number of blacks on the path from rule to to are saying let us count black n one, two,

40
00:02:36,340 --> 00:02:41,200
three, three, one, two and go along the spot.

41
00:02:41,380 --> 00:02:44,170
Three, one, two, three.

42
00:02:44,470 --> 00:02:45,670
Let us go in this direction.

43
00:02:45,670 --> 00:02:47,980
One, two and three.

44
00:02:48,280 --> 00:02:49,360
One, two.

45
00:02:49,360 --> 00:02:50,290
And this is three.

46
00:02:50,620 --> 00:02:51,930
One, two, three.

47
00:02:51,940 --> 00:02:53,350
One, two, three.

48
00:02:53,470 --> 00:02:55,760
One, two, three, four.

49
00:02:55,780 --> 00:03:03,910
In every part you go, you should have seen number of black notes that's under the property then.

50
00:03:04,120 --> 00:03:05,440
No go consecutive.

51
00:03:05,710 --> 00:03:06,100
Right.

52
00:03:06,100 --> 00:03:07,120
Nor should be there.

53
00:03:07,540 --> 00:03:07,930
Right.

54
00:03:08,080 --> 00:03:09,840
Like this is red and this is also red.

55
00:03:09,850 --> 00:03:10,810
It should not be there.

56
00:03:11,140 --> 00:03:13,360
Like this is red then this should not be red.

57
00:03:13,780 --> 00:03:15,490
So no to Consuegra difference.

58
00:03:15,640 --> 00:03:18,220
It means if there is any red A..

59
00:03:18,490 --> 00:03:22,150
Then parent will also black and the child will also be black.

60
00:03:22,150 --> 00:03:22,810
Definitely.

61
00:03:22,930 --> 00:03:31,030
So for any node parent and child of red black the next newly installed node, when you are inserting

62
00:03:31,030 --> 00:03:34,870
a new node, always it is inserted as a red node.

63
00:03:35,870 --> 00:03:44,390
Then next and the last thing is the height of our right to block a tree is subminimum log-in maximum

64
00:03:44,390 --> 00:03:46,490
double of log, and that is to log in.

65
00:03:47,030 --> 00:03:51,530
So if you remember the height of a voluntary minimum, it can be lockin maximum.

66
00:03:51,530 --> 00:03:52,040
It can be.

67
00:03:52,050 --> 00:04:00,350
And but when you're trying to make a tight balance in a tree, it is one point four four in log, one

68
00:04:00,350 --> 00:04:02,480
point four for it to log in.

69
00:04:02,750 --> 00:04:06,360
No red black, three f2 into log-in.

70
00:04:06,650 --> 00:04:10,550
So it is this little relaxing compared to a tree.

71
00:04:10,730 --> 00:04:15,410
So we can say that Aviel tree is more strict, but it's is not that much.

72
00:04:16,070 --> 00:04:19,850
So it allows the height to go up to point to Log-in.

73
00:04:20,450 --> 00:04:23,240
So the maximum height of black trees.

74
00:04:23,360 --> 00:04:26,070
Gulaga by looking at all these properties.

75
00:04:26,390 --> 00:04:27,920
What do you hope to remember this?

76
00:04:27,970 --> 00:04:29,330
Do I have to remember all these?

77
00:04:29,660 --> 00:04:34,340
If you are writing academic exam, then you have to remember this, right?

78
00:04:34,500 --> 00:04:38,480
And if you are facing interview also, they may ask questions randomly from these properties.

79
00:04:38,900 --> 00:04:43,430
But do you have to remember these always know you don't have to remember them.

80
00:04:43,670 --> 00:04:47,400
If you remember the tree creation process that I'm going to show you next.

81
00:04:47,750 --> 00:04:51,220
That will help you recall all these points, right.

82
00:04:51,240 --> 00:04:54,830
So you don't have to spend special time in memorizing them.

83
00:04:55,310 --> 00:04:57,860
If you know how to create, you can't remember them.

84
00:04:58,100 --> 00:05:01,460
So let us take a few keys and create a red black tree.

85
00:05:01,460 --> 00:05:07,070
So I'll show you the installation process, which is more similar to be that is by the resource tree,

86
00:05:07,340 --> 00:05:11,640
plus a reality that is to balance because rotations are also there.

87
00:05:12,200 --> 00:05:14,060
Let us look at irrigation.

