1
00:00:00,510 --> 00:00:05,100
In this video, we will see <PERSON><PERSON>'s algorithm for finding minimum cost of Spanish entry.

2
00:00:05,730 --> 00:00:09,700
So in the previous video that we have learned about Spanish and the minimum costs, apparently.

3
00:00:10,260 --> 00:00:15,480
So now let us see what is has adjusted for finding a minimum cost of Spanish.

4
00:00:15,870 --> 00:00:22,350
So for explanation, I have taken VITIT non-direct directed graph and it is connected graph, although

5
00:00:22,350 --> 00:00:23,190
it is not connected.

6
00:00:23,220 --> 00:00:26,880
So yes, we can find a Spanish or a minimum cost of Spanish.

7
00:00:27,540 --> 00:00:33,970
Now let us learn the procedure given by <PERSON><PERSON><PERSON> so <PERSON><PERSON>'s <PERSON><PERSON><PERSON>, let us learn it step by step.

8
00:00:34,290 --> 00:00:42,060
So the very first step prudence's that select a minimum crosshatch from the graph the minimum cost out

9
00:00:42,060 --> 00:00:43,610
of all these addresses.

10
00:00:43,770 --> 00:00:45,600
One common six one two six.

11
00:00:45,960 --> 00:00:47,520
So first select that one.

12
00:00:47,850 --> 00:00:49,050
So this is selected.

13
00:00:50,680 --> 00:00:56,440
Not follow the steps very carefully, next steps, repeating steps, so always we will be performing

14
00:00:56,440 --> 00:00:57,140
the same thing.

15
00:00:57,550 --> 00:00:59,610
So let us see, what is that repeating step?

16
00:01:00,630 --> 00:01:09,300
Now from the graph, select a minimum cost edge, next minimum cost edge, but it must be connected

17
00:01:09,300 --> 00:01:15,960
to already selected words selected what is one in six, so it should be connected to those selected

18
00:01:15,960 --> 00:01:16,130
for.

19
00:01:17,280 --> 00:01:21,710
So if I look into the graph, the minimum one is this one an X?

20
00:01:21,720 --> 00:01:23,200
Just this one next to this one.

21
00:01:23,200 --> 00:01:24,540
No, don't say big.

22
00:01:24,540 --> 00:01:27,620
Those are just select the one which is connected.

23
00:01:27,930 --> 00:01:33,110
So for one connected one is this one which is of twenty five and four six connected.

24
00:01:33,120 --> 00:01:34,620
One is this one that is eighteen.

25
00:01:35,010 --> 00:01:36,900
So select this one eighteen.

26
00:01:37,150 --> 00:01:38,240
That is six to five.

27
00:01:38,400 --> 00:01:41,280
So next edge is six to five.

28
00:01:43,970 --> 00:01:44,570
18.

29
00:01:46,300 --> 00:01:54,390
Not with the same thing like the minimum, but connected so from one it is twenty five from five, 16

30
00:01:54,430 --> 00:01:56,490
to 11, 12 at minimum.

31
00:01:56,770 --> 00:01:58,630
So select 12.

32
00:02:01,900 --> 00:02:09,740
Now, next, from one six, five and four, find out the minimum correct that this is twenty five does

33
00:02:09,759 --> 00:02:13,430
a 16 for this one, this is 11 and this is 10.

34
00:02:13,810 --> 00:02:14,890
So that is minimum.

35
00:02:15,310 --> 00:02:23,470
That minimum, this is three to four or four to three, which is off weight 10.

36
00:02:25,940 --> 00:02:29,010
Now, the selected voices are one six, five, four, three.

37
00:02:29,150 --> 00:02:33,150
We have to select a minimum one, which is connected to any one of these.

38
00:02:33,710 --> 00:02:38,180
So from one it is twenty five to 16 and this is 11.

39
00:02:38,390 --> 00:02:39,820
And from here, nine is there.

40
00:02:39,840 --> 00:02:41,490
So that is minimum.

41
00:02:41,500 --> 00:02:45,200
So two and nine, which is nine.

42
00:02:47,580 --> 00:02:51,610
The next minimum, two to seven, that is connected to two.

43
00:02:53,180 --> 00:02:54,050
Connected to.

44
00:02:56,310 --> 00:02:56,970
Six.

45
00:02:58,310 --> 00:03:09,260
Yes, so what is the total cost, five plus 18, plus 12, plus ten, nine and six, 16.

46
00:03:09,800 --> 00:03:11,870
So the total cost is 16.

47
00:03:13,000 --> 00:03:18,790
So this is a minimum because it's apparently suggested by Prince, we have followed the algorithm and

48
00:03:18,790 --> 00:03:20,430
we got the minimum cost Manningtree.

49
00:03:21,410 --> 00:03:24,780
But the question is why he said that it must be connected.

50
00:03:25,380 --> 00:03:28,040
See, we have to achieve two things.

51
00:03:28,430 --> 00:03:31,090
First is it must be spanning.

52
00:03:31,460 --> 00:03:35,110
Three or three will be connected and it will not have cycles.

53
00:03:35,360 --> 00:03:35,740
Yes.

54
00:03:36,140 --> 00:03:38,070
Second thing is we want minimum.

55
00:03:38,570 --> 00:03:41,920
So the first preferences, it should be connected so that it's a three.

56
00:03:42,710 --> 00:03:43,700
So if I.

57
00:03:44,590 --> 00:03:50,050
Look at the steps that have performed so fast, I selected this then this one up to here, if you see

58
00:03:50,050 --> 00:03:53,400
it's a three only connector, only the next connector.

59
00:03:53,770 --> 00:04:00,610
So if you stop in between, also if you perform a few steps and stop if you don't complete still, you

60
00:04:00,610 --> 00:04:01,400
get a train on me.

61
00:04:01,690 --> 00:04:06,490
So that is the reason everyone has to just focus on to try to maintain it as a train.

62
00:04:07,650 --> 00:04:13,830
Right, then check for minimum wage, so this is the whole premise algorithm works.

63
00:04:14,830 --> 00:04:22,690
Now, let us do some analysis on Prince Alcorcon, how much time it is taking, see what is the work

64
00:04:22,690 --> 00:04:23,480
we are doing here.

65
00:04:23,770 --> 00:04:27,880
We are generating and minus one, adjust and forming a spanning tree.

66
00:04:28,300 --> 00:04:31,420
We are scanning through and selecting and the minus one adjusts.

67
00:04:31,930 --> 00:04:35,020
So this is definitely V minus one.

68
00:04:35,710 --> 00:04:36,880
This is V minus one.

69
00:04:38,140 --> 00:04:43,690
And then minus one and one, a just facility, and every time we are checking.

70
00:04:44,780 --> 00:04:48,920
The connected one and finding minimum out of the connector register.

71
00:04:49,280 --> 00:04:52,810
So how much time it would take for finding the minimum match?

72
00:04:52,940 --> 00:04:54,980
So let's say maximum.

73
00:04:54,980 --> 00:05:00,100
We are checking all ages and from there we are selecting minimum connected matches.

74
00:05:00,470 --> 00:05:01,160
So the number of.

75
00:05:03,140 --> 00:05:12,940
So the time taken by a Gordimer's V into E vs number of bodices and E's no ventus, this is also Leanyer,

76
00:05:12,980 --> 00:05:14,080
this is also Línea.

77
00:05:14,270 --> 00:05:17,590
So we can say this is an interim that is in the square.

78
00:05:18,410 --> 00:05:21,160
So we can say this one and Indurain and Square.

79
00:05:21,440 --> 00:05:24,690
So the time taken by some algorithms and square.

80
00:05:24,770 --> 00:05:33,800
Then if you are using some heap structure for finding minimum akech from the set of edges, then the

81
00:05:33,800 --> 00:05:39,500
time can be reduced to number four to six, minus one and two long e abcess.

82
00:05:40,730 --> 00:05:45,940
So if you see this is linear and and this is logarithmic, so we can say log-in.

83
00:05:46,190 --> 00:05:49,770
So the time complexity of primps algorithm will be analog.

84
00:05:49,790 --> 00:05:57,650
And if a faster method is used for finding a minimum cost edge like we can use Ahepe or a BlackBerry

85
00:05:58,070 --> 00:06:02,150
for finding minimum, then one more thing about premature Legatum.

86
00:06:02,450 --> 00:06:06,920
See if I have a graph which is not a connected graph.

87
00:06:07,040 --> 00:06:10,640
If it is not a connected graph, does a non connected graph.

88
00:06:10,850 --> 00:06:16,670
If I to them on this one, then it will start from this minimum cost edge and it will try to select

89
00:06:16,670 --> 00:06:18,520
the next connector and next connected.

90
00:06:18,530 --> 00:06:23,250
So it will never reach the second component because it is not connected.

91
00:06:24,380 --> 00:06:29,720
Yes, we already know that for a non connected graph we cannot find out spanning at all.

92
00:06:29,900 --> 00:06:33,200
But what happens if I run Prem's algorithm on this one?

93
00:06:33,440 --> 00:06:40,040
So this algorithm may find out a minimum core spanning tree for one of the component.

94
00:06:41,510 --> 00:06:46,850
One of the components it can find out, because they are not connected, it will never reach on the

95
00:06:46,850 --> 00:06:48,050
second component.

96
00:06:49,300 --> 00:06:51,880
So that's does the analysis of Al Gore.

97
00:06:52,560 --> 00:06:58,230
Next, we will look at our program on Al Gore for finding minimum courses spanning three.

