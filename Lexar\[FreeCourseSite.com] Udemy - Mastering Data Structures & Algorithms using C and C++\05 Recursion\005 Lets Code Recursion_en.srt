1
00:00:00,570 --> 00:00:07,710
Let us start a new project for looking at an example for recursions, so I'll start a new project.

2
00:00:08,940 --> 00:00:10,470
Select command line tool.

3
00:00:12,740 --> 00:00:21,320
He mentioned the project, so I'll give the name as they head had, so the project name is still ahead.

4
00:00:21,710 --> 00:00:24,140
The language I have selected, the sea language.

5
00:00:25,590 --> 00:00:36,570
And create a project that's created this is the main function now here I can add my code, so I relied

6
00:00:36,570 --> 00:00:38,820
on the functions that we have already discussed.

7
00:00:39,170 --> 00:00:44,850
That is fun function for just displaying numbers, word function, fun mistake's parameter and.

8
00:00:47,320 --> 00:00:50,140
If and as a greater than zero.

9
00:00:53,980 --> 00:01:03,820
25 percentile, I'll give some space then and then function, call it for the value and minus one.

10
00:01:06,070 --> 00:01:09,730
Now, this slow motion, I will call it from inside main function.

11
00:01:09,760 --> 00:01:15,550
I'll take a variable integer X with the value of three, then it is called the function fun for value

12
00:01:15,550 --> 00:01:16,300
of X.

13
00:01:21,680 --> 00:01:26,570
This is an example of tail function, the function as a recursive, it is calling itself the reduced

14
00:01:26,570 --> 00:01:30,300
value of end, and it will be calling if the value of an is greater than zero.

15
00:01:30,320 --> 00:01:34,250
So initially, we're calling it for value of an as a three.

16
00:01:34,250 --> 00:01:35,260
So X is a three.

17
00:01:36,890 --> 00:01:40,340
I will run the program and show you what let us see what the output is.

18
00:01:42,090 --> 00:01:44,800
So here you can see the output is three to one.

19
00:01:45,150 --> 00:01:48,300
So far it is spending three then and two, then one.

20
00:01:50,700 --> 00:01:56,400
As this litigation we know well that stackers utilize a function will be calling itself again and again,

21
00:01:56,400 --> 00:01:58,860
so internally it uses a stack.

22
00:01:59,280 --> 00:02:02,400
Let us see how this recursive functional uses tag for that.

23
00:02:02,400 --> 00:02:03,620
I will debug the code.

24
00:02:04,050 --> 00:02:08,759
So inside the main function, the very first line, I'll put a break point by clicking here.

25
00:02:10,729 --> 00:02:18,200
So this is a break point for leaving the program and let us understand how dysfunction recursively call

26
00:02:18,200 --> 00:02:20,660
itself and what is the behavior of a function.

27
00:02:21,780 --> 00:02:28,800
Run the program, so now it is stopping at this line here, EXIS three, so now you can see that right

28
00:02:28,800 --> 00:02:33,030
now the value inside is garbage because this line is not yet executed.

29
00:02:33,690 --> 00:02:35,460
Three is not yet stored in X.

30
00:02:36,510 --> 00:02:42,480
And here inside this navigator window, you can see that the zeroth function, that is main function

31
00:02:42,540 --> 00:02:44,130
as the starting point of a program.

32
00:02:44,490 --> 00:02:47,930
So it is number zero and its main function is running.

33
00:02:48,810 --> 00:02:57,320
Now, if they continue line by line by pressing F7 or function F seven, then it moves to the next line.

34
00:02:57,320 --> 00:02:59,280
Know the value of axis initialized to three.

35
00:03:00,030 --> 00:03:04,830
Now this line is about to execute the line we just highlighted with the green color.

36
00:03:05,160 --> 00:03:11,420
This line is about to execute and you can see the value of this tree and this function is not yet call.

37
00:03:11,580 --> 00:03:15,390
So if I put the cursor here, there is no value shown here.

38
00:03:16,200 --> 00:03:19,060
Let us continue now.

39
00:03:19,080 --> 00:03:24,560
A function is it has entered inside the function and the value of an is compared with zero right now,

40
00:03:24,570 --> 00:03:25,740
the value of the tree.

41
00:03:26,130 --> 00:03:32,730
And here in the debug navigator you can see that man has became one and this one is zero.

42
00:03:33,180 --> 00:03:33,990
There are no.

43
00:03:36,220 --> 00:03:43,840
Like the presently executing zero and the previous one, who has called it as one normally of a. three,

44
00:03:44,680 --> 00:03:46,080
I will continue execution.

45
00:03:46,720 --> 00:03:50,050
Now, the next line is printers and printing is done.

46
00:03:50,050 --> 00:03:53,460
So the value 3D printer here values 3D printed.

47
00:03:53,950 --> 00:03:55,620
Now, again, the function is called.

48
00:03:57,340 --> 00:04:01,210
So it's about to call itself again, bypassing three minus one.

49
00:04:02,140 --> 00:04:07,450
So if they continue next again, the function is called it has directly entered inside and it is on

50
00:04:07,450 --> 00:04:11,380
the conditional statement right over the value of Années two.

51
00:04:11,620 --> 00:04:12,100
Yes.

52
00:04:12,470 --> 00:04:18,990
And here you can see in the debug navigator the function on the top is zero and the next is fun again.

53
00:04:19,000 --> 00:04:23,670
So function is called two times and then below that is main function maintenance.

54
00:04:23,680 --> 00:04:25,960
Call this fund and fun has called again.

55
00:04:25,960 --> 00:04:27,970
It's also two times the second call.

56
00:04:29,020 --> 00:04:33,580
Now let us continue for the printing is done so value to spend it again.

57
00:04:33,590 --> 00:04:36,880
It has called itself this time the value of an as one.

58
00:04:38,320 --> 00:04:41,720
And you can see that there are three calls here inside the debug window.

59
00:04:42,670 --> 00:04:46,780
Our main three function calls for fun are there now.

60
00:04:47,320 --> 00:04:50,830
One is greater than zero, so it will enter inside and it will print this one.

61
00:04:52,570 --> 00:04:53,340
Let us proceed.

62
00:04:54,250 --> 00:04:55,710
So printing of one is done.

63
00:04:55,730 --> 00:04:58,210
Then again, it will call itself with the value of one.

64
00:04:59,460 --> 00:05:02,730
With the value of an associate or so, this is zero.

65
00:05:03,120 --> 00:05:09,840
So these are total four calls so you can count here above men, there are four function called the topmost

66
00:05:09,840 --> 00:05:11,990
one is no at zero and so on.

67
00:05:12,990 --> 00:05:18,660
Now, for this, if we continue and is not greater than zero, so it will not enter inside the safe

68
00:05:18,720 --> 00:05:24,540
block and it will come out of the if block, it will not enter inside the safe block and also it will

69
00:05:24,540 --> 00:05:25,590
come out of this function.

70
00:05:25,890 --> 00:05:27,590
So the value of an end is zero.

71
00:05:27,620 --> 00:05:31,470
Nothing happens, printing is not done and it will not call itself.

72
00:05:31,860 --> 00:05:33,680
These two lines will not be executed.

73
00:05:35,310 --> 00:05:36,390
Let us continue for the.

74
00:05:38,570 --> 00:05:43,610
So you can see that it has reached the end of the function call and right now the value of an S zero

75
00:05:43,610 --> 00:05:43,950
only.

76
00:05:44,330 --> 00:05:48,310
So if I press the next step again, then see what happens.

77
00:05:49,450 --> 00:05:55,520
It is still there only, but this is for the previous call, and you can see that in the Navigator the

78
00:05:55,520 --> 00:05:57,160
functions are reduced by one.

79
00:05:57,440 --> 00:06:02,870
So the function that Vallecito has finished, then what is the topmost functional but the value of another

80
00:06:02,870 --> 00:06:03,200
one?

81
00:06:03,830 --> 00:06:06,140
Yes, it has gone back to the previous call.

82
00:06:06,860 --> 00:06:08,690
Now, this is having value one.

83
00:06:08,690 --> 00:06:12,560
Then when it goes back, this will have value too, and this will have value three and so on.

84
00:06:13,890 --> 00:06:15,380
And at last, it will reach men.

85
00:06:15,750 --> 00:06:19,710
So right now it is inside that function called where the value of and was run.

86
00:06:20,400 --> 00:06:24,630
If I proceed further again, you can see that it is standing there, just there.

87
00:06:24,840 --> 00:06:29,440
There was a blink that has gone to the next statement.

88
00:06:29,460 --> 00:06:35,070
So it is back on the same statement because after it has each year saw the value of an F two and you

89
00:06:35,070 --> 00:06:37,170
can see that the number of calls are reduced right now.

90
00:06:37,170 --> 00:06:42,490
It is with value to then below this one, there is one more function called with value three.

91
00:06:42,840 --> 00:06:47,130
So if we continue further, it will go back to this function and this function will terminate.

92
00:06:48,900 --> 00:06:52,260
Let me go to next and I'll go to the next step again.

93
00:06:52,260 --> 00:06:56,820
It is standing there only just it has blinked and came back to this function call at the end of the

94
00:06:56,820 --> 00:07:00,060
function call and the value of and there's a tree here right now.

95
00:07:00,870 --> 00:07:06,240
You can see in the debugger navigation that above mean there is only one function call.

96
00:07:06,510 --> 00:07:09,900
Currently we are in that function called with a value of and is a tree.

97
00:07:10,140 --> 00:07:11,880
Actually this was the first call.

98
00:07:13,570 --> 00:07:18,520
So this is how the function is returning one by one, it has to call itself again and again, now it

99
00:07:18,520 --> 00:07:19,700
is running one by one.

100
00:07:19,990 --> 00:07:24,760
Now, if it got any further, it will go back to the main function call and the program and its.

101
00:07:26,710 --> 00:07:30,710
So it's back to the line where from where the function was called here it has started.

102
00:07:31,000 --> 00:07:33,120
So it is back to this function call.

103
00:07:34,480 --> 00:07:36,250
So I'll simply finish the program now.

104
00:07:37,960 --> 00:07:42,280
So this was the example of failed regulation, which we have already discussed on board, so I have

105
00:07:42,280 --> 00:07:46,140
shown you how the function calls are piling up on our stack.

106
00:07:46,870 --> 00:07:51,820
I will modify the same function into a trigger action by just copying this line.

107
00:07:52,600 --> 00:07:57,270
I'll cut it from here and pasted just before Prenter function.

108
00:07:58,510 --> 00:08:01,570
So you can see that first of the function will be called.

109
00:08:01,690 --> 00:08:04,270
It will be called, it will be calling it recursively.

110
00:08:04,270 --> 00:08:05,650
Then it will print the value.

111
00:08:06,400 --> 00:08:11,090
So again, I'll put a big point here up on this line, the first line and said the main function.

112
00:08:11,590 --> 00:08:15,870
Now let us run the program and see what the output is.

113
00:08:15,910 --> 00:08:19,350
We know the output already that the output is going to be one, two, three.

114
00:08:19,720 --> 00:08:22,090
But let us learn how it is generated.

115
00:08:24,350 --> 00:08:29,980
Running the program now in the Reebok Navigator, you can see that the starting point is main function

116
00:08:30,460 --> 00:08:37,000
and the main function of stopping that line where we have given breakpoint the value of X, not yet

117
00:08:37,000 --> 00:08:43,419
initialize it is garbage if you continue for the first line is executing the value of X has became three,

118
00:08:43,659 --> 00:08:45,040
just about to call a function.

119
00:08:46,180 --> 00:08:50,390
And in the navigation you can check that there is only just one main function, that's all.

120
00:08:51,820 --> 00:08:56,800
If we continue further than this is the first function call, the first function call, you can see

121
00:08:57,220 --> 00:09:00,920
the value of Ednas three, then three is greater than zero.

122
00:09:00,930 --> 00:09:04,450
The letter inside and it will make a call again without printing.

123
00:09:07,530 --> 00:09:14,910
Yeah, it has came to the line where there is a function call now back again, a function called.

124
00:09:15,800 --> 00:09:24,110
This time, there are two function calls, and right now the value of an S to I'll continue again in

125
00:09:24,110 --> 00:09:25,670
a function called recursive call.

126
00:09:26,150 --> 00:09:31,070
So it has to call again itself the new value that is one, then one is greater than zero.

127
00:09:31,070 --> 00:09:32,030
It will enter inside.

128
00:09:32,030 --> 00:09:34,010
And again, it will call itself four zero.

129
00:09:37,020 --> 00:09:40,080
Yes, again, a new call, so value is zero.

130
00:09:40,110 --> 00:09:43,830
You can see that the splendiferous never executed so total.

131
00:09:43,840 --> 00:09:46,280
How many calls we have passed so far?

132
00:09:46,680 --> 00:09:50,830
Calls are for three to one and zero phone calls are there.

133
00:09:51,300 --> 00:09:54,430
So inside the debug navigator, you can check that a bombing function.

134
00:09:54,450 --> 00:09:58,130
There are four function calls right now.

135
00:09:58,140 --> 00:10:02,520
It is in this function called and right now the value of an Isidore's.

136
00:10:02,520 --> 00:10:05,470
It will not enter inside if block and it will terminate.

137
00:10:06,270 --> 00:10:09,560
So this function will terminate and it will go back to the previous call.

138
00:10:09,640 --> 00:10:10,780
So this is the last line.

139
00:10:11,220 --> 00:10:16,570
Now, if I press again, not if I press F7 again, it will go back to the previous call.

140
00:10:17,040 --> 00:10:18,750
Yeah, it is back on the previous call.

141
00:10:18,750 --> 00:10:23,370
It is on the same line where from where it has call itself and the value of and was Vodoun.

142
00:10:23,550 --> 00:10:24,000
Yes.

143
00:10:24,540 --> 00:10:27,250
Previous to zero the call was one.

144
00:10:27,870 --> 00:10:30,450
Now next line it will be printing one.

145
00:10:34,730 --> 00:10:41,390
But the execute, yes, it has been that one, and it came out of a blog, notes about to terminate

146
00:10:41,930 --> 00:10:45,700
function call with value and equal to one is about to terminate.

147
00:10:46,310 --> 00:10:49,390
If it continues, then it is back on the previous call.

148
00:10:49,400 --> 00:10:55,400
You can see that the caller has reduced here it is back on the call where it was and value was to say,

149
00:10:55,400 --> 00:10:57,000
yes, here, this and this too.

150
00:10:58,160 --> 00:11:00,320
So then it will bring this too and go back.

151
00:11:00,350 --> 00:11:05,740
So you can see that print is being executed at reading time, calling time.

152
00:11:05,750 --> 00:11:07,160
There was nothing in this function.

153
00:11:07,520 --> 00:11:10,070
So I'll continue execution print.

154
00:11:10,100 --> 00:11:15,320
Have to then go back to previous call with the value of and was three then print three, go back to

155
00:11:15,320 --> 00:11:20,480
the previous call and it has gone back to the main function and that function ends.

156
00:11:23,610 --> 00:11:29,310
So that's all we have seen, how a recursive function works, we have seen the calling phase as well

157
00:11:29,310 --> 00:11:30,360
as returning phase.

