1
00:00:00,150 --> 00:00:06,180
And this video will develop a C language program for circular linguist, so I will start a new project

2
00:00:06,180 --> 00:00:06,810
for this one.

3
00:00:07,320 --> 00:00:14,790
Let us call it as circular linguist and the language I will selected as C language project, create

4
00:00:14,790 --> 00:00:15,270
a project.

5
00:00:15,270 --> 00:00:16,820
And this is a main function.

6
00:00:16,830 --> 00:00:18,470
I'll remove all these comments.

7
00:00:18,480 --> 00:00:19,780
I will hide panels.

8
00:00:19,800 --> 00:00:22,470
Yeah, the project is ready for implementing.

9
00:00:22,500 --> 00:00:27,330
So first of all, I should define our structure for a..

10
00:00:27,330 --> 00:00:34,920
A. will contain data of integer type I'm taking and next to self-referential pointer and next is self-referential

11
00:00:34,920 --> 00:00:36,030
pointer normed.

12
00:00:36,120 --> 00:00:37,130
That is next.

13
00:00:37,140 --> 00:00:43,530
I will declare a pointer call head as a global pointer and I will be using it inside in the entire program

14
00:00:43,830 --> 00:00:46,810
and very, very clear I may be passing it as parameter also.

15
00:00:46,830 --> 00:00:52,050
Now the first thing is I will write a function for creating a circular linked list by taking array of

16
00:00:52,050 --> 00:00:52,750
elements.

17
00:00:52,770 --> 00:00:57,930
So here I will write on the function three, which will take an integer type three, as well as the

18
00:00:57,930 --> 00:00:59,120
number of elements.

19
00:00:59,130 --> 00:01:03,310
So for creating a node and for accessing that area, I may require some variables.

20
00:01:03,310 --> 00:01:07,260
So I will declare those variables struct node a temporary pointer for creating.

21
00:01:07,260 --> 00:01:08,460
In the very first thing.

22
00:01:08,460 --> 00:01:13,370
What I have to do is create the first node and make her the point on that node.

23
00:01:13,680 --> 00:01:19,640
So using the pointer, I will create a new node by calling my log function and first I have bypassed

24
00:01:19,650 --> 00:01:24,310
it, then mellark and then say size of struct node.

25
00:01:24,450 --> 00:01:28,620
So this will create a node and had North should have the data.

26
00:01:28,700 --> 00:01:33,510
That is the first element from an array that is zero element then had nodes.

27
00:01:33,510 --> 00:01:37,440
Next pointer should point on had itself that's set.

28
00:01:37,740 --> 00:01:42,660
So it is circular now and there is only one single note now for the rest of the nodes.

29
00:01:42,660 --> 00:01:47,940
I will scan for this study and I will create nodes and go on linking them with this link.

30
00:01:48,180 --> 00:01:54,600
So for this I will take one more point call last lost that will help me for inserting a new node always

31
00:01:54,600 --> 00:01:55,370
at the last.

32
00:01:55,410 --> 00:01:59,490
So let us make last point on header now using for loop.

33
00:01:59,490 --> 00:02:06,840
I'll scan through this link list for I assigned one IIs less than and and I plus plus inside the loop

34
00:02:06,840 --> 00:02:08,570
every time I will create a new norm.

35
00:02:08,580 --> 00:02:12,240
So for creating a new node, this is the code I will copied and pasted.

36
00:02:12,240 --> 00:02:19,710
Here is the node creation and these data I should assign it from aof i then I have to make links and

37
00:02:19,710 --> 00:02:26,610
I should insert it after the last node sorties and next should be pointing on last index because this

38
00:02:26,610 --> 00:02:29,750
is circular so I cannot use null here.

39
00:02:29,760 --> 00:02:37,500
Then last the next will be binding on new node E then last should move on to new note that said so with

40
00:02:37,500 --> 00:02:43,530
this code lintels will be created from a given UTI and it will be a circular linked list.

41
00:02:44,400 --> 00:02:48,210
And this is how I made it simpler by making had binding on itself.

42
00:02:48,210 --> 00:02:55,350
And every time I'm trying to maintain that same link with the help of last night inside the main function,

43
00:02:55,350 --> 00:03:01,380
I will create an array of elements two, three, four, five, six.

44
00:03:02,130 --> 00:03:07,650
Then I will create function by passing Uhry as well as the number of elements.

45
00:03:07,660 --> 00:03:12,500
So I'm pushing five elements or five elements are past Lingley should be created.

46
00:03:12,540 --> 00:03:15,900
Then next I should display a link list for displaying a link list.

47
00:03:15,900 --> 00:03:18,090
I will write on a display function here.

48
00:03:18,690 --> 00:03:20,070
Void display.

49
00:03:20,190 --> 00:03:23,490
It should take a pointer to an order that is Hudnall.

50
00:03:23,490 --> 00:03:29,610
So I will call it as a pointer edge for I had not done, as we have discussed, that it has to be implemented

51
00:03:29,610 --> 00:03:30,780
using do I loop.

52
00:03:31,140 --> 00:03:40,470
So do while every time I should print an element from Gab's data and also move X clinics nor assign

53
00:03:40,860 --> 00:03:41,850
X caps.

54
00:03:41,880 --> 00:03:49,740
Next, it will be moving on to the next node and here in a vital condition, while it is not equal to

55
00:03:50,070 --> 00:03:53,610
had had is a global variable so it can be accessed directly.

56
00:03:53,640 --> 00:04:00,000
NetSol then after displaying all the elements, let us give a line Gabb so that we can clearly see the

57
00:04:00,000 --> 00:04:00,510
output.

58
00:04:00,510 --> 00:04:05,950
NetSol now from the main function I should call display, had letters from this.

59
00:04:05,970 --> 00:04:09,410
Yes, I got the element that is two, three, four, five, six.

60
00:04:09,420 --> 00:04:14,370
It is displaying all elements of circular Linklaters created and elements are displayed.

61
00:04:14,850 --> 00:04:20,620
Now here I'm getting of warning that this Marlock function is used because I did not include a file.

62
00:04:20,670 --> 00:04:21,200
Yes.

63
00:04:21,209 --> 00:04:22,980
No, I will not get any warning.

64
00:04:23,310 --> 00:04:28,530
If I run the program, it runs smoothly and I got all the list of elements.

65
00:04:28,560 --> 00:04:33,890
If I debug it by putting a breakpoint upon the display function, then I will run it.

66
00:04:34,380 --> 00:04:36,240
Let us look at debug window.

67
00:04:36,270 --> 00:04:37,440
Here is a debug window.

68
00:04:37,650 --> 00:04:43,020
How does a point, depending on the element that is two and two is pointing on three.

69
00:04:43,140 --> 00:04:47,370
Three is pointing on four, four is pointing on the node and that is five.

70
00:04:47,370 --> 00:04:52,950
Five is pointing on the node six then six is pointing on again two then two is pointing on three.

71
00:04:52,950 --> 00:04:53,880
See the circular.

72
00:04:53,910 --> 00:04:56,160
So if I go on clicking it never stops.

73
00:04:56,850 --> 00:04:59,730
So you can see that from two, three, four, five, six.

74
00:04:59,920 --> 00:05:05,770
Again, there is a link, Google, so, yes, it has some circular link list, so it will never stop.

75
00:05:05,800 --> 00:05:09,400
Now, if I go on expanding this one, I'll be getting the elements below this one.

76
00:05:09,400 --> 00:05:11,760
Same elements will be repeating again and again.

77
00:05:11,770 --> 00:05:17,010
So this is enough to understand or confirm that a link was created, this circular.

78
00:05:17,170 --> 00:05:18,760
So I'll stop this the next.

79
00:05:18,760 --> 00:05:21,650
I have shown you a function for a recursive display.

80
00:05:21,670 --> 00:05:28,780
So let us write on recursive display order display, which takes a node pointer to head and that is

81
00:05:28,780 --> 00:05:34,420
actually then inside this function, I have shown you that we should have a static variable that is

82
00:05:34,420 --> 00:05:37,490
flag and the flag should be assigned each elizardo.

83
00:05:37,600 --> 00:05:43,690
And the condition we should check that is not equal to head or flag equals to zero.

84
00:05:43,810 --> 00:05:46,760
If both of these are false, that it will stop.

85
00:05:46,780 --> 00:05:49,660
So right now, flags equals to zero, so it will continue.

86
00:05:49,690 --> 00:05:56,890
So what we'll do is, first of all, we'll make the flag as one, then we will print the element that

87
00:05:56,890 --> 00:05:58,890
is personalized.

88
00:05:59,020 --> 00:06:03,600
And that is the point that I'm using here and display the data.

89
00:06:03,610 --> 00:06:08,730
Then after that, all the recursive display by passing each next node.

90
00:06:09,070 --> 00:06:14,650
So instead of using it here, don't get confused with variable name in linear is always I was using

91
00:06:14,660 --> 00:06:18,160
P as a variable here I'm using it as and on whiteboard.

92
00:06:18,160 --> 00:06:21,300
Also I have use B, but while writing the code I'm using it.

93
00:06:21,490 --> 00:06:27,280
Then when this, when this is becoming false, I should also make a flag as zettl once again.

94
00:06:27,310 --> 00:06:30,850
Now let us see how it works and remove the breakpoint.

95
00:06:30,850 --> 00:06:36,080
And instead of display our display for the same Linkous, let us run the program.

96
00:06:36,100 --> 00:06:38,470
Yes, it is displaying two, three, four, five, six.

97
00:06:38,480 --> 00:06:40,650
It is perfectly displaying all the elements.

98
00:06:40,660 --> 00:06:43,960
So that's all you can write on this program by yourself.

99
00:06:43,960 --> 00:06:46,200
See, practicing these programs is very important.

100
00:06:46,210 --> 00:06:47,920
You must practice them at least once.

101
00:06:48,260 --> 00:06:53,560
Then it becomes very handy and very easy for you to write the code and the code is becoming Linda and

102
00:06:53,560 --> 00:06:57,450
Linda every time as we go on moving to the next topic.

103
00:06:57,460 --> 00:06:58,900
So that's all with this video.

104
00:06:59,230 --> 00:07:02,350
We have seen creation and recursive display.

105
00:07:02,350 --> 00:07:06,100
And for the remaining operations on Circler Linkous, I'll be using the same program.

