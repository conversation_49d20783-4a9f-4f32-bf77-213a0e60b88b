1
00:00:00,180 --> 00:00:08,430
In if we do look at a procedure for finding duplicates in unsorted I, I have an example of it here,

2
00:00:08,440 --> 00:00:13,080
the elements are not sorted and there are some duplicate elements present here in this array.

3
00:00:13,470 --> 00:00:19,050
Like eight is appearing two times and six is appearing three times.

4
00:00:19,440 --> 00:00:21,870
And the rest of the elements are unique.

5
00:00:22,800 --> 00:00:29,100
Now I want to write a procedure, find out duplicate elements and also I want to count them.

6
00:00:29,100 --> 00:00:33,540
How many duplicates are there so there can be more than one solution.

7
00:00:33,600 --> 00:00:39,900
So first solution that is look at we will scan through an array, will pick up an element and look for

8
00:00:39,900 --> 00:00:41,860
its duplicate like eight.

9
00:00:41,880 --> 00:00:46,620
We will check in the rest of the array and find out is there any other eight present.

10
00:00:46,620 --> 00:00:53,570
And then I next will pick up three and checking the rest of the array to find if there is any other

11
00:00:53,580 --> 00:00:54,340
three present.

12
00:00:56,070 --> 00:00:58,640
So I will demonstrate in this list.

13
00:00:59,040 --> 00:01:05,700
So for demonstration, let us take one variable count and let us take on this one.

14
00:01:06,120 --> 00:01:07,460
Now start from here.

15
00:01:08,580 --> 00:01:12,780
This element, I'm counting it for one time now from here.

16
00:01:12,780 --> 00:01:14,360
I should check in the rest of the list.

17
00:01:14,370 --> 00:01:19,020
Is there any other eight so far that I can take one more variable that is a G.

18
00:01:19,320 --> 00:01:25,460
With the help of this variable, I will scan through this list and find out if there is any other eight.

19
00:01:25,620 --> 00:01:30,380
So let us start is a date no move to next location as a date.

20
00:01:30,390 --> 00:01:32,350
No, as a date is a date.

21
00:01:32,450 --> 00:01:33,540
No, no.

22
00:01:33,870 --> 00:01:34,970
Yes, it is fun.

23
00:01:35,280 --> 00:01:37,480
So once it is found one to this.

24
00:01:37,740 --> 00:01:40,470
So now we have to aides continue.

25
00:01:40,770 --> 00:01:42,500
So as a date is a date.

26
00:01:42,510 --> 00:01:42,910
No.

27
00:01:43,440 --> 00:01:49,800
So we have finished looking for a duplicate of first element eight and we found two elements.

28
00:01:50,070 --> 00:01:51,140
So count the students.

29
00:01:51,160 --> 00:01:53,940
Yes, there are duplicates already.

30
00:01:53,940 --> 00:01:58,200
We have to conduct eight and to count, so we should not count it again.

31
00:01:58,590 --> 00:02:01,680
It may happen that the same element is counted multiple times.

32
00:02:01,710 --> 00:02:03,090
So we will see that situation.

33
00:02:03,420 --> 00:02:10,410
So for that, we should remove that date or we should have some marking to show that that is already

34
00:02:10,410 --> 00:02:11,020
done that.

35
00:02:11,400 --> 00:02:15,180
So for that I will take minus one in this place.

36
00:02:15,330 --> 00:02:21,090
So whenever a duplicate is found, just microdots, minus one, minus one, this element is not there

37
00:02:21,590 --> 00:02:22,500
right now.

38
00:02:22,500 --> 00:02:23,280
Let's continue.

39
00:02:23,460 --> 00:02:26,140
We will find a duplicate of other element.

40
00:02:26,190 --> 00:02:29,970
Then I can explain you what is the reason of taking minus one once again.

41
00:02:30,930 --> 00:02:33,900
Now start I will find the duplicates of next elementary.

42
00:02:34,230 --> 00:02:35,730
So make this a second one.

43
00:02:35,970 --> 00:02:43,480
Now using G go on looking for three is not there not take G from here and go looking for the element

44
00:02:43,510 --> 00:02:45,660
three by comparing the elements.

45
00:02:45,690 --> 00:02:47,230
Next, next, next, next.

46
00:02:47,230 --> 00:02:50,290
So three is not the second remains one mess.

47
00:02:50,340 --> 00:02:51,720
There is only one copy of three.

48
00:02:52,110 --> 00:02:53,820
Now let us take the next element.

49
00:02:54,000 --> 00:02:59,340
Six now here I am at six now start finding six using G.

50
00:02:59,580 --> 00:03:00,440
Is it six.

51
00:03:00,450 --> 00:03:01,650
No it's not six.

52
00:03:01,860 --> 00:03:03,030
So move to next.

53
00:03:03,030 --> 00:03:03,660
Is it six.

54
00:03:03,660 --> 00:03:04,050
Yes.

55
00:03:04,320 --> 00:03:07,350
So count then again, move to next.

56
00:03:07,350 --> 00:03:08,070
It's not six.

57
00:03:08,400 --> 00:03:12,900
This is six second then this is minus one.

58
00:03:12,900 --> 00:03:13,320
Leave it.

59
00:03:13,650 --> 00:03:14,520
And it's not the same.

60
00:03:14,790 --> 00:03:15,570
So this is two.

61
00:03:15,570 --> 00:03:16,290
This is seven.

62
00:03:17,240 --> 00:03:19,020
I have came across two more.

63
00:03:19,020 --> 00:03:19,590
Six.

64
00:03:20,460 --> 00:03:20,780
Right.

65
00:03:21,030 --> 00:03:22,920
I'm finding the duplicate for this one.

66
00:03:23,220 --> 00:03:29,820
So I should mark this has minus one if minus one, minus one not count this more than one.

67
00:03:30,000 --> 00:03:32,100
So it means there are duplicates of six.

68
00:03:33,440 --> 00:03:38,010
Next, let us go to the next element, 444 find in the rest of the hour.

69
00:03:38,390 --> 00:03:42,510
There is no order for so count remains one for food.

70
00:03:43,080 --> 00:03:44,670
Now take the next element.

71
00:03:44,870 --> 00:03:46,010
Now the pointis.

72
00:03:46,310 --> 00:03:48,340
Shall I take this element or not?

73
00:03:48,680 --> 00:03:53,360
If I take this element and again, I will get the result that six is appearing two times.

74
00:03:54,740 --> 00:03:59,150
I should not take that element because already discounted at.

75
00:04:00,370 --> 00:04:05,710
So it's a duplicate element, so I have marked the duplicate elements, otherwise I may be getting the

76
00:04:05,710 --> 00:04:12,040
duplicate results, otherwise I wouldn't be getting extra results that four, six, eight, three time

77
00:04:12,070 --> 00:04:14,100
again from here, six appear two times.

78
00:04:14,530 --> 00:04:16,180
So that is the reason I'm marking them.

79
00:04:16,870 --> 00:04:18,149
So this is the procedure.

80
00:04:19,000 --> 00:04:24,920
Now, before showing you the program code, let us do some analysis how much work we are doing.

81
00:04:25,630 --> 00:04:33,370
See, we have started finding a duplicate element of a trust here, so for finding duplicates and competing

82
00:04:33,370 --> 00:04:34,830
with the rest of the elements.

83
00:04:35,080 --> 00:04:40,540
So there are total elements then how many elements I'm comparing and the minus one elements I'm comparing.

84
00:04:41,200 --> 00:04:44,610
Then again, being on three, I'm comparing the rest of the elements.

85
00:04:44,890 --> 00:04:46,270
I don't have to check on the site.

86
00:04:46,570 --> 00:04:51,160
I have to check the rest of the elements for total harmony and the minus two elements I'm comparing

87
00:04:51,520 --> 00:04:58,540
then being on six and comparing and minus the three elements, so and minus three combinations and performing.

88
00:04:58,960 --> 00:05:04,750
Then Simbi is go on reducing up to last three competition to competition and one competition.

89
00:05:05,380 --> 00:05:08,140
So these many competitions I'm doing so total.

90
00:05:08,140 --> 00:05:09,850
How many competitions are these.

91
00:05:09,880 --> 00:05:15,270
These are and then going minus one by two and then minus one by two.

92
00:05:15,490 --> 00:05:18,880
So this is an squared minus and by two.

93
00:05:19,090 --> 00:05:20,870
So the degree is and squared.

94
00:05:20,890 --> 00:05:27,930
So it is outdraw and square or big off and square, but we don't know what is big omega.

95
00:05:28,180 --> 00:05:31,410
So let me call Agha's order of that file.

96
00:05:31,480 --> 00:05:37,860
We simply calling it as order of that degree of two that is and square degrees two.

97
00:05:38,170 --> 00:05:41,710
So it is outdraw and square right now.

98
00:05:41,710 --> 00:05:43,600
Let us look at the procedure.

99
00:05:43,840 --> 00:05:47,190
So I will vote on the procedure for this, for finding duplicate.

100
00:05:47,200 --> 00:05:50,020
I have to start finding the duplicate for the first element.

101
00:05:50,020 --> 00:05:51,150
So I should start from here.

102
00:05:51,460 --> 00:06:00,220
So one is I start from zero and I should I should stop here because I don't have to check for the duplicates

103
00:06:00,220 --> 00:06:00,880
of this element.

104
00:06:01,150 --> 00:06:02,980
I should stop at this point at last.

105
00:06:03,280 --> 00:06:05,620
So that is less than an A minus one.

106
00:06:06,070 --> 00:06:06,850
Plus, plus.

107
00:06:09,450 --> 00:06:13,910
All right, so this is the main loop that is going through all the elements to find their duplicates,

108
00:06:14,360 --> 00:06:17,910
that every time I should start count that one.

109
00:06:18,300 --> 00:06:22,910
OK, read one for every element, count as one, then find other.

110
00:06:22,910 --> 00:06:24,910
Any more elements to increase the count?

111
00:06:25,820 --> 00:06:26,390
Not one.

112
00:06:26,390 --> 00:06:27,260
I is here.

113
00:06:27,500 --> 00:06:29,420
I have to scan through the rest of the list.

114
00:06:29,430 --> 00:06:29,760
Right.

115
00:06:30,020 --> 00:06:34,190
So I'm starting from here and continuing until the last element.

116
00:06:34,190 --> 00:06:36,470
So I will take one more for a look for this.

117
00:06:36,480 --> 00:06:43,400
Gee that is a sign I love, one that is next element of hate and it should go till the last element.

118
00:06:43,400 --> 00:06:45,760
So it should be less than in and plus.

119
00:06:45,760 --> 00:06:46,160
Plus.

120
00:06:48,440 --> 00:06:53,720
Then what I should do, like four eight, if I have reached here, means the element is matching, so

121
00:06:53,720 --> 00:06:58,310
I should compare this element with this one and if they are matching, I should increment count.

122
00:06:58,370 --> 00:07:07,580
So here I will say if aof i.e. is equal to eight of G, if they are equal, then I should do count plus.

123
00:07:07,580 --> 00:07:08,080
Plus.

124
00:07:08,090 --> 00:07:08,660
Yes.

125
00:07:11,720 --> 00:07:14,940
Then, as I said, also, I should mark this as minus one.

126
00:07:14,960 --> 00:07:19,220
So at eight of G, I should make it as minus one.

127
00:07:23,210 --> 00:07:30,200
So plus, plus, minus one, that end of this loop, not once they come out of this loop, I should

128
00:07:30,200 --> 00:07:31,730
check what is the value of count?

129
00:07:32,030 --> 00:07:36,020
If the count is greater than one, that means there are duplicates.

130
00:07:36,030 --> 00:07:41,600
So here I will write on death if the count is greater than one.

131
00:07:44,760 --> 00:07:52,860
Then Brent, so here I am reading percent daily percent, the first U.S. of that is eight and count

132
00:07:53,220 --> 00:07:55,340
how many times it is duplicated.

133
00:07:56,460 --> 00:07:59,640
So this will bring this one if count is greater than one.

134
00:08:00,400 --> 00:08:03,570
There is a duplicate element and how many times it is duplicated.

135
00:08:03,570 --> 00:08:06,500
But I, I'm getting so end of this loop.

136
00:08:07,200 --> 00:08:08,000
Now, one more thing.

137
00:08:08,460 --> 00:08:10,320
This is checking for the duplicate.

138
00:08:10,320 --> 00:08:13,090
I should do it for this, not minus one.

139
00:08:13,410 --> 00:08:15,150
So here I was right on the condition.

140
00:08:15,480 --> 00:08:23,460
If, if I is not equal to minus one means for this element I should not find the duplicates.

141
00:08:23,490 --> 00:08:23,830
Right.

142
00:08:23,850 --> 00:08:25,830
Because all the to begin minus one.

143
00:08:26,130 --> 00:08:29,970
So for this one I should not do and I should not enter into this one.

144
00:08:30,270 --> 00:08:31,530
So this is conditional.

145
00:08:31,830 --> 00:08:38,580
So that's solves the procedure for finding duplicates and counting how many times each element is duplicated

146
00:08:39,390 --> 00:08:39,780
already.

147
00:08:39,830 --> 00:08:40,980
You have done that analysis.

148
00:08:41,220 --> 00:08:43,919
Now let us look at the code and do the analysis.

149
00:08:44,280 --> 00:08:48,450
See from the code this loop is repeating four and minus one times.

150
00:08:48,450 --> 00:08:49,980
So almost end times.

151
00:08:50,370 --> 00:08:52,340
This will repeat for almost any time.

152
00:08:52,710 --> 00:08:54,780
And this is starting from E-Plus plus one.

153
00:08:55,110 --> 00:09:02,130
And so this is also repeating almost four end times like we all we all know the working based on that.

154
00:09:02,130 --> 00:09:03,230
We are seeing that right.

155
00:09:03,420 --> 00:09:04,950
It's not just by reading the code.

156
00:09:05,160 --> 00:09:08,670
We know the working is starting from EI plus one bill.

157
00:09:08,730 --> 00:09:12,380
And so this is also almost end times, right?

158
00:09:12,780 --> 00:09:18,930
Otherwise, if you want to consider in detail, this will be reducing one by one and minus one and minus

159
00:09:18,930 --> 00:09:19,770
two and minus three.

160
00:09:19,950 --> 00:09:22,820
So if I sum up again, it was becoming and square.

161
00:09:23,040 --> 00:09:26,370
So now roughly I'm writing this and so this is an inside end.

162
00:09:26,370 --> 00:09:27,900
So there's a follow up and said follow.

163
00:09:28,140 --> 00:09:31,690
So the timers and square that is outdraw and square.

164
00:09:32,400 --> 00:09:33,150
So that's it.

165
00:09:33,380 --> 00:09:35,190
This is the procedure for finding duplicates.

166
00:09:35,580 --> 00:09:39,020
So you can try this by yourself as a student exercise.

167
00:09:39,030 --> 00:09:40,470
I have written the code on board.

168
00:09:40,710 --> 00:09:44,890
Just you have to write a main function and inside the main function, you have to declare this early

169
00:09:45,150 --> 00:09:48,000
and write on the similar code and see the results.

170
00:09:48,060 --> 00:09:54,860
Now let's look at the second method for finding duplicates and counting duplicates in unsorted array.

171
00:09:55,800 --> 00:10:02,080
So this is using hash table symbol, hash table, or also you can call it as a bit set.

172
00:10:02,100 --> 00:10:03,270
So let us see the procedure.

173
00:10:03,600 --> 00:10:06,070
I have the same idea here, having duplicates.

174
00:10:06,090 --> 00:10:08,440
That is eight is duplicated and six is duplicated.

175
00:10:08,940 --> 00:10:15,570
Now, I have to take a hash table early and the fate of the array should be equal to the largest element

176
00:10:15,570 --> 00:10:16,620
in the array.

177
00:10:16,920 --> 00:10:20,840
So if you check in this area, the lattice element is eight.

178
00:10:21,030 --> 00:10:27,840
So I have taken to index eight wins, total sizes nine because starting from zero onwards now the procedure

179
00:10:27,840 --> 00:10:34,200
is I should scan for this list and for each element I should increment the count here and this hash

180
00:10:34,380 --> 00:10:34,870
table.

181
00:10:35,400 --> 00:10:36,240
So let us do it.

182
00:10:36,690 --> 00:10:39,150
Start from first element eight.

183
00:10:39,570 --> 00:10:42,810
Go to index eight here and increment it.

184
00:10:43,730 --> 00:10:52,280
Then move the next element three, so go to index the three here, so incremented and a six Goodhew

185
00:10:52,280 --> 00:10:52,990
index six.

186
00:10:52,990 --> 00:10:58,730
Yes, incremented for incremented six again.

187
00:10:58,950 --> 00:11:06,200
So six should be made either to override it, then five as one, then six.

188
00:11:06,740 --> 00:11:08,110
I will override this one.

189
00:11:08,900 --> 00:11:11,030
This is three, then eight.

190
00:11:11,220 --> 00:11:13,520
This is two, then two.

191
00:11:13,850 --> 00:11:16,430
This is one and seven.

192
00:11:16,730 --> 00:11:17,570
So this is one.

193
00:11:19,010 --> 00:11:22,550
Therefore, I have scanned for this area only once.

194
00:11:22,790 --> 00:11:23,430
Yes.

195
00:11:24,320 --> 00:11:26,960
So if I analyze how much time I have spent.

196
00:11:28,200 --> 00:11:30,710
Order and time, so I'll just write em.

197
00:11:31,810 --> 00:11:37,780
Now, next, what I have to do, I have to scan through this hash table wherever I got a value that

198
00:11:37,780 --> 00:11:38,800
is greater than zero.

199
00:11:38,910 --> 00:11:40,030
Yes, this is greater than zero.

200
00:11:40,030 --> 00:11:40,820
Greater than zero.

201
00:11:40,840 --> 00:11:41,770
Yes, very, very good.

202
00:11:41,800 --> 00:11:44,470
The value is greater than zero elementalist percent.

203
00:11:45,070 --> 00:11:50,650
And if the value is greater than one, then duplicate is present like this is one.

204
00:11:50,830 --> 00:11:55,510
So this is single value three single for a single five.

205
00:11:55,510 --> 00:11:59,100
A single six disappearing three times six is happening three times.

206
00:11:59,380 --> 00:12:01,720
And this is seven, a single and two.

207
00:12:01,750 --> 00:12:03,630
So eight is appearing two times.

208
00:12:04,510 --> 00:12:08,450
So I got the elements which are duplicate and also how many times they are duplicated.

209
00:12:08,470 --> 00:12:09,420
I got both of them.

210
00:12:10,270 --> 00:12:15,630
And for scanning through this, let us say time then, because actually in the minutes here, Lena,

211
00:12:16,120 --> 00:12:18,830
actually in here at Selenia, right?

212
00:12:19,000 --> 00:12:26,080
Lenie so this is Lenie list of elements, whatever the size, maybe don't check that this number of

213
00:12:26,080 --> 00:12:26,980
elements are different.

214
00:12:27,010 --> 00:12:27,700
This is different.

215
00:12:27,880 --> 00:12:32,980
If that is and this is N minus one and the means lenie in simple terms.

216
00:12:33,130 --> 00:12:33,420
Right.

217
00:12:33,730 --> 00:12:35,110
So this is also linear.

218
00:12:35,350 --> 00:12:37,900
So linear plus linear is too linear.

219
00:12:38,080 --> 00:12:40,060
So to linear is also linear.

220
00:12:40,060 --> 00:12:45,580
Only six outdraw and so two times of linear is also linear.

221
00:12:46,690 --> 00:12:49,170
So I will not trade on the gold, right?

222
00:12:49,210 --> 00:12:54,420
So already we have seen this in missing elements, so logic will be similar.

223
00:12:54,820 --> 00:12:56,810
You can write on the gold for this one.

224
00:12:57,040 --> 00:12:59,320
So this is also a student exercise.

225
00:12:59,560 --> 00:13:05,230
So you can write on the main function and that you can take this early and this early and then ride

226
00:13:05,230 --> 00:13:09,370
on the gold for finding duplicate and also displaying the count.

227
00:13:10,210 --> 00:13:10,890
So that's it.

228
00:13:10,900 --> 00:13:11,500
And this really.

