1
00:00:00,150 --> 00:00:06,030
And this video will look at a function for deleting a note from the linguist, so I'm continuing with

2
00:00:06,030 --> 00:00:07,140
the same program.

3
00:00:07,200 --> 00:00:11,910
This is the same main function where a Linklaters created and it is displayed now.

4
00:00:12,060 --> 00:00:15,580
Other functions are already there, which we have written in the previous videos.

5
00:00:15,930 --> 00:00:20,030
Now, just I have to include delete function inside this program.

6
00:00:20,040 --> 00:00:24,240
So let us write, delete function, return type and function.

7
00:00:24,240 --> 00:00:25,080
Name is delete.

8
00:00:25,680 --> 00:00:29,490
It should take structure node to first point.

9
00:00:29,550 --> 00:00:34,100
Let us call it USPI and we want to index where we want to delete.

10
00:00:34,110 --> 00:00:35,100
Then for deletion.

11
00:00:35,100 --> 00:00:41,550
I may need temporary pointer so like to, I will take a temporary pointer then I may need a variable

12
00:00:41,550 --> 00:00:43,350
X which I will initialize it.

13
00:00:43,350 --> 00:00:47,490
That's minus one as well as I know the procedure starts with.

14
00:00:47,490 --> 00:00:53,250
The first thing is I should check with the index is less than one because the minimum index Nexus One

15
00:00:53,790 --> 00:00:57,930
then if index is greater than the length of a link list.

16
00:00:58,080 --> 00:01:01,650
So I will be or even again pass first.

17
00:01:01,650 --> 00:01:08,550
If index nexus less than one or greater than land than written minus one means index is invalid.

18
00:01:08,580 --> 00:01:11,070
The next we should check if the index is one.

19
00:01:11,070 --> 00:01:13,740
If the index is one means we want to delete Fastenal.

20
00:01:14,370 --> 00:01:16,530
So it's a special case for deleting.

21
00:01:16,530 --> 00:01:21,450
First note then the method for deleting first note like the PS already pointing up on first in order

22
00:01:21,450 --> 00:01:23,960
will be passingly as a parameter to this function.

23
00:01:23,970 --> 00:01:30,360
So I should move first pointer to next node and it first is not null like it first months.

24
00:01:30,420 --> 00:01:35,730
It has not became null, then first previous should become null.

25
00:01:35,950 --> 00:01:42,810
Then I should take the value from each node inside X then three be deleted or not.

26
00:01:43,200 --> 00:01:43,730
That's all.

27
00:01:43,980 --> 00:01:49,650
So move the first point to the next node and make its previous link as null, then delete the norm.

28
00:01:49,800 --> 00:01:51,120
So first n delete it.

29
00:01:51,270 --> 00:01:55,260
If it is not first node then I should reach the index there.

30
00:01:55,440 --> 00:01:56,700
I want to delete the note.

31
00:01:56,820 --> 00:02:07,200
So using a for loop I can move this pointer B to index minus one position I placeless then every time

32
00:02:07,200 --> 00:02:12,120
to be assigned D next we will be reaching at an index there.

33
00:02:12,150 --> 00:02:17,880
We want to delete the node so P will be pointing up on that same note then as we have discussed on the

34
00:02:17,880 --> 00:02:18,540
whiteboard.

35
00:02:19,020 --> 00:02:23,430
So I have to modify the links so I will modify the links first.

36
00:02:23,870 --> 00:02:28,440
That is these previous nodes next.

37
00:02:28,980 --> 00:02:32,160
Should we assign us these next.

38
00:02:32,460 --> 00:02:35,310
Difford I have shown you and then one more link.

39
00:02:35,310 --> 00:02:38,130
I have to modify that Espy's next node previous.

40
00:02:38,400 --> 00:02:41,970
So this I should do only if a PS next notice there.

41
00:02:42,360 --> 00:02:44,430
If it is there then only I should do this one.

42
00:02:44,760 --> 00:02:51,750
So then these next nodes previous should be assigned that PS previous that's all.

43
00:02:52,110 --> 00:02:53,500
But the links are modified.

44
00:02:53,500 --> 00:03:02,580
Then take the data and x pick up the data then free node P not PS deleted the nexus.

45
00:03:02,580 --> 00:03:09,020
Take the data from this note that is reCAPTCHA then three P P will be deleted.

46
00:03:09,030 --> 00:03:10,890
So this is what we have already seen.

47
00:03:11,430 --> 00:03:16,680
So I have completed the code then return the deleted value ex.

48
00:03:16,680 --> 00:03:18,510
So delete function is computed.

49
00:03:18,990 --> 00:03:24,450
So the code is little handy because there are more than one cases now coming to the main function.

50
00:03:24,450 --> 00:03:27,930
I will delete first node and let us see what happens.

51
00:03:28,230 --> 00:03:28,860
Delete.

52
00:03:29,640 --> 00:03:33,900
First of all, I should point appointed to the first node and the index is one.

53
00:03:34,230 --> 00:03:35,760
So first node should be deleted.

54
00:03:35,760 --> 00:03:41,190
That is then should be deleted and I should get the elements twenty two fifty when I'm displaying it.

55
00:03:41,520 --> 00:03:42,280
So let us run.

56
00:03:42,510 --> 00:03:43,170
Yes.

57
00:03:44,010 --> 00:03:45,810
30, 30, 40 and 50.

58
00:03:45,810 --> 00:03:46,700
These are displayed.

59
00:03:46,710 --> 00:03:48,870
So it means the element is deleted.

60
00:03:50,090 --> 00:03:54,100
Then let us delete the last element that is in next five and again, run it.

61
00:03:54,130 --> 00:03:55,570
Yes, fifteens deleted.

62
00:03:56,480 --> 00:03:57,750
Let us do to totally.

63
00:03:58,070 --> 00:04:04,430
So they should be deleted yesterday's deleted the elements that I'm getting are and 20, 40 and 50 XOL.

64
00:04:04,430 --> 00:04:05,810
It's working perfectly here.

65
00:04:05,810 --> 00:04:11,300
I'm getting a warning that this point that you actually did not use Q and delete function.

66
00:04:11,300 --> 00:04:12,580
So I will make it as coming.

67
00:04:12,770 --> 00:04:14,510
So that's all that the delete function.

