1
00:00:00,400 --> 00:00:06,760
In this video, I'll be talking about merging tools, that is to race and the marching coolest in a

2
00:00:06,760 --> 00:00:11,860
single if they are in the singularity, hokum them and merging multiple lists.

3
00:00:13,960 --> 00:00:23,290
So let us start with merging two, that is to see merging as a process of combining to sorted list into

4
00:00:23,290 --> 00:00:29,650
a single sorted list, if you observe, I have two worries here and B and the list of elements are sorted

5
00:00:29,650 --> 00:00:31,200
here and they are sorted here.

6
00:00:31,210 --> 00:00:37,990
Also, we want to combine these two and keep them in third array in the sorted order here, I suppose

7
00:00:37,990 --> 00:00:39,820
M elements that are five elements.

8
00:00:39,820 --> 00:00:42,240
And here I have an element of that Ghaffour element.

9
00:00:42,250 --> 00:00:47,590
So these two list, we have the same number of elements, also a different number of elements.

10
00:00:47,890 --> 00:00:50,080
So their sizes need not be the same.

11
00:00:50,080 --> 00:00:53,860
They can be different also and initialised to minimum.

12
00:00:53,860 --> 00:01:00,130
One element must be then we can merge these two lists by comparing the elements one by one and copying

13
00:01:00,130 --> 00:01:02,090
smaller elements in this third.

14
00:01:02,770 --> 00:01:09,550
So for doing so, I will take I here and G here that is starting in inducers and the key here.

15
00:01:11,050 --> 00:01:17,690
Almost few elements I'm you compare off, compare it to be over, which is smaller and smaller.

16
00:01:18,040 --> 00:01:23,620
So a copy of it here and then move it to the next element.

17
00:01:24,580 --> 00:01:29,200
And also key to the next element, not one element is copied from here.

18
00:01:29,270 --> 00:01:36,340
Now again, compare I with the be of Gene all this time, B of G the smaller copy that element for.

19
00:01:38,130 --> 00:01:43,200
Will move to the next element, then the key will also move to the next element.

20
00:01:43,860 --> 00:01:45,160
In this way we will call.

21
00:01:45,180 --> 00:01:48,810
We will copy all the elements here, sell the rest of the elements.

22
00:01:49,560 --> 00:01:54,000
So similarly, I have compared them and copied all the elements, like all the elements and the sorted

23
00:01:54,000 --> 00:01:56,730
order, I was copying just the smaller element out of two.

24
00:01:57,090 --> 00:02:02,970
Now I use one last element of and here to achieve also one last element of B.

25
00:02:04,690 --> 00:02:09,680
If they compare these elements, also, if I hit it off a G.

26
00:02:09,750 --> 00:02:11,550
So this is twenty three and twenty five.

27
00:02:11,850 --> 00:02:14,720
So twenty three is a smaller twenty three is written here.

28
00:02:15,780 --> 00:02:22,600
Then I moved to next index, so I become five and the killer also moves to the next index.

29
00:02:23,490 --> 00:02:30,450
Now if you observe one of the list has finish all the elements and first list that is a hash finish

30
00:02:30,660 --> 00:02:36,930
and there is some remaining element in psychoanalyst's to be, there is some remaining element and definitely

31
00:02:36,930 --> 00:02:38,060
this is going to happen.

32
00:02:38,400 --> 00:02:43,320
One of the lists will finish and the other list will have at least one element remaining.

33
00:02:43,620 --> 00:02:44,820
So here is an example.

34
00:02:44,820 --> 00:02:45,960
There is one element remaining.

35
00:02:46,260 --> 00:02:51,810
Or if I extend this list and add a few more elements to this, then there may be a few more elements.

36
00:02:53,840 --> 00:02:55,580
Nosik, Goldoni, five.

37
00:02:55,610 --> 00:03:00,220
The competition was remaining so many elements are there, so many more men and women will be there

38
00:03:00,230 --> 00:03:03,400
and one of them is either may be left over there or will be.

39
00:03:03,650 --> 00:03:07,490
So what I have to do is copy all those remaining elements and see directly.

40
00:03:08,990 --> 00:03:16,280
So I removed this, not the remaining element, and B, I have to copy it here, so twenty five is copied

41
00:03:16,280 --> 00:03:20,260
here and moves to the next location, G also moves to next location.

42
00:03:20,630 --> 00:03:24,950
So both the lists are copied and merged together in code.

43
00:03:25,400 --> 00:03:26,090
That is C..

44
00:03:26,120 --> 00:03:27,260
So this is the procedure.

45
00:03:27,650 --> 00:03:33,090
So let me write on the algorithm or the function for merging two lists.

46
00:03:34,040 --> 00:03:40,850
So let me write on the march function is taking to address and the size of Australia and the size of

47
00:03:40,870 --> 00:03:47,960
Cicconetti and others assume that it's global or else then we write the program will decide where to

48
00:03:47,960 --> 00:03:48,380
keep it.

49
00:03:49,010 --> 00:03:50,760
Now let us start the process.

50
00:03:50,780 --> 00:03:57,050
So what we need we need I on this first list and G on this list.

51
00:03:57,530 --> 00:04:01,550
That is from starting index, also on the starting index of C.

52
00:04:01,580 --> 00:04:04,940
So all three because I got initialized with zero.

53
00:04:05,150 --> 00:04:06,020
They are at zero.

54
00:04:07,130 --> 00:04:12,350
Every time I should compare the element of the offer, i.e., that the B of G, whichever is smaller,

55
00:04:12,350 --> 00:04:21,230
I should competency of key either Elfy or Bhaiyyaji so that I right on if they offer you the smaller

56
00:04:21,230 --> 00:04:31,580
than B copy of in C of an increment bodansky otherwise Gobbi B element in C and increment as well as

57
00:04:31,580 --> 00:04:31,880
key.

58
00:04:32,840 --> 00:04:34,690
So this process has to continue.

59
00:04:34,880 --> 00:04:43,700
How long either is the finishing or B the finishing means are using less than M or G.

60
00:04:43,730 --> 00:04:44,710
So that's the end.

61
00:04:44,900 --> 00:04:54,590
So this is repeating process while so continue this as long as I is less than M and just lasdun and

62
00:04:54,920 --> 00:04:57,740
if any one of them has finished, stop.

63
00:04:57,740 --> 00:05:04,070
Come out of the loop now after coming out of the loop, like if you see the situation back again, this

64
00:05:04,080 --> 00:05:09,500
I had finished and the G was still on the last element.

65
00:05:10,220 --> 00:05:12,820
So this may be last element or there may be more elements.

66
00:05:12,830 --> 00:05:19,010
So what we should do, Coppy, all the remaining elements from B from the point of Erivedge is pointing.

67
00:05:19,220 --> 00:05:20,960
I don't again set it on zero.

68
00:05:21,200 --> 00:05:23,330
Very, very disappointing from their only copy.

69
00:05:23,330 --> 00:05:25,100
All the elements from B to C.

70
00:05:26,960 --> 00:05:32,540
Or else same situation may there and also some copy all the elements from here, from wherever I is

71
00:05:32,540 --> 00:05:33,040
pointing.

72
00:05:33,200 --> 00:05:40,930
So I will write on that process for copying from a so this local copy, all the remaining elements from

73
00:05:40,980 --> 00:05:41,420
Ari.

74
00:05:41,500 --> 00:05:47,510
If there are any elements remaining, if I is less than am, then this loop will continue.

75
00:05:47,510 --> 00:05:51,710
Copy all the elements from your file to see and implement incremented.

76
00:05:52,610 --> 00:05:57,440
And I'm not initializing, so I start from the point wherever it is right now.

77
00:05:57,440 --> 00:06:03,470
Pointing then similarly, I should copy all the elements from Biery also if there are any remaining

78
00:06:03,470 --> 00:06:05,270
elements and it that will be copied.

79
00:06:06,050 --> 00:06:10,340
So this foldable copy all the elements from Oraibi to the remaining elements.

80
00:06:10,910 --> 00:06:16,370
Now don't confuse with this C any one of the following will execute but will not execute.

81
00:06:16,730 --> 00:06:20,000
If I has become equal to them, then this fall will not execute.

82
00:06:21,250 --> 00:06:23,980
There definitely will be than 10, so this will execute.

83
00:06:24,950 --> 00:06:31,430
If I was still less than 10 minutes being this test finish, remaining elements, are there in any one

84
00:06:31,430 --> 00:06:32,350
of these Lupul?

85
00:06:32,510 --> 00:06:36,470
Well, I don't have to check where the elements are demeaning at all.

86
00:06:36,740 --> 00:06:39,520
Without that, I have just started to follow from the same point.

87
00:06:39,800 --> 00:06:41,660
So any one of the follow will look.

88
00:06:42,810 --> 00:06:50,070
So that's all this is the procedure for merging to raise in a single knob all the time taken by this

89
00:06:50,070 --> 00:06:50,620
procedure.

90
00:06:50,940 --> 00:06:57,000
What we are doing, just copying the elements, how many elements and elements from the side and elements

91
00:06:57,000 --> 00:06:57,680
from this side.

92
00:06:57,900 --> 00:07:00,350
So the time is taken as embolus and.

93
00:07:01,790 --> 00:07:08,560
First of all, it is like a notation that the time taken for merging is M plus, and next we will see

94
00:07:08,590 --> 00:07:15,830
is it possible to have more than one list in a single episode and how to merge them?

95
00:07:16,690 --> 00:07:17,360
We are here.

96
00:07:17,380 --> 00:07:24,500
I have a single array of eight elements I have take an example now if you observe two five eight two

97
00:07:24,500 --> 00:07:26,800
well in here is one list.

98
00:07:27,460 --> 00:07:28,390
This is one list.

99
00:07:28,660 --> 00:07:30,490
Then three, six, seven, 10.

100
00:07:30,880 --> 00:07:31,830
The second list.

101
00:07:31,900 --> 00:07:33,890
So to list are there in a single.

102
00:07:35,370 --> 00:07:41,870
One lesson starting from zero to three and another one is from four to seven, so to lost in a single

103
00:07:41,880 --> 00:07:43,860
area and I want to merge them.

104
00:07:43,980 --> 00:07:47,100
And after merging the result, I wanted only.

105
00:07:49,400 --> 00:07:55,380
So, yes, we can do this, so let us call this location as a low and this location as high.

106
00:07:55,610 --> 00:08:00,320
So starting index as low as a zero and ending index high is a seven.

107
00:08:00,560 --> 00:08:02,890
And let us call this as a murder.

108
00:08:03,740 --> 00:08:05,120
This is middle of these two.

109
00:08:05,450 --> 00:08:06,270
So that is three.

110
00:08:07,070 --> 00:08:11,260
So first list is from low to mid and second list is from Maiga.

111
00:08:11,270 --> 00:08:14,800
Plus one too high to list out that.

112
00:08:16,330 --> 00:08:24,490
Now, for merging them, I need another Oraibi of the same size, then they said perform the same procedure,

113
00:08:24,490 --> 00:08:30,670
like comparing the remains of two lists so far that I can take in here and here and here.

114
00:08:30,970 --> 00:08:37,659
Compare the element of a Iveta A4, Jay, and copy that, whichever is a smaller.

115
00:08:38,549 --> 00:08:44,670
So the same process I can run what I was comparing, but this process was for two days now, this is

116
00:08:44,670 --> 00:08:45,530
for a single day.

117
00:08:45,900 --> 00:08:52,640
So if I compare and copy, then forced to compete, then five in three are compared to three.

118
00:08:52,650 --> 00:08:58,350
The smaller trees compete than five and six are compared to five of the smaller then eight and six.

119
00:08:58,350 --> 00:09:00,450
So six is eight and seven.

120
00:09:00,450 --> 00:09:01,590
So seven is complete.

121
00:09:01,830 --> 00:09:02,760
Eight and ten.

122
00:09:02,760 --> 00:09:05,670
So eight is copied then 12 or 13.

123
00:09:05,670 --> 00:09:06,810
So tennis compete.

124
00:09:07,110 --> 00:09:08,500
Norman, this list has finished.

125
00:09:08,520 --> 00:09:11,570
There is one remaining element to here, this element.

126
00:09:12,150 --> 00:09:13,320
So the same process.

127
00:09:13,320 --> 00:09:17,430
Assume this is a Arabie, but actually they are in the same array.

128
00:09:17,640 --> 00:09:26,220
So list one list or two, they are merged and we got the result in Inari B so for merging we definitely

129
00:09:26,220 --> 00:09:28,380
need extra uhry.

130
00:09:28,710 --> 00:09:32,580
We cannot merge and store the result of there in itself.

131
00:09:32,590 --> 00:09:33,600
It is not possible.

132
00:09:33,960 --> 00:09:36,240
So merging is not in place.

133
00:09:36,420 --> 00:09:38,520
We need extra space for merging.

134
00:09:38,910 --> 00:09:45,210
Now finally, what I want this, I want to copy all these elements back again in a so that the sorted

135
00:09:45,210 --> 00:09:47,910
list is there in a C.

136
00:09:47,910 --> 00:09:54,210
Yes, I can copy all these elements one by one and store this complete list in this area so it becomes

137
00:09:54,210 --> 00:09:56,430
a single list of sorted elements.

138
00:09:56,760 --> 00:10:02,670
So that's all we can have two sorted list in a single array and we can merge them with the help of an

139
00:10:02,670 --> 00:10:09,840
auxiliary and we can get back the result in itself that a single sorted list how to do this process.

140
00:10:10,110 --> 00:10:11,460
So I will modify this one.

141
00:10:12,000 --> 00:10:20,460
So in this merge process, I need just E and the parameters I need are high.

142
00:10:20,490 --> 00:10:22,800
And so first, Lou.

143
00:10:25,610 --> 00:10:34,040
Second one is murder, and the third one is high, three parameters are needed and here are perfect,

144
00:10:34,040 --> 00:10:39,740
but they should not be initialized to zero, then I should be initialized with the law and they should

145
00:10:39,740 --> 00:10:41,600
be initialized with the meticulous one.

146
00:10:42,170 --> 00:10:48,440
So I is initialized with low energy use, mutualised plus one should start from here and the case should

147
00:10:48,440 --> 00:10:49,660
not start from zero.

148
00:10:49,670 --> 00:10:50,860
It should start from low.

149
00:10:51,200 --> 00:10:52,640
We don't know where that low is.

150
00:10:52,640 --> 00:10:56,590
In our example, lawyers from Zero Law can be anywhere, any part of Inari.

151
00:10:57,080 --> 00:10:58,910
So let us take care.

152
00:10:58,910 --> 00:11:01,150
Also on law is also on law.

153
00:11:02,440 --> 00:11:08,650
Now, one more thing we need we need an idea to merge all those elements we need in that equal to the

154
00:11:08,650 --> 00:11:11,150
size of this list of whatever the sizes.

155
00:11:11,650 --> 00:11:18,940
So let us create a auxillary A B. So here I am creating an array B of sizes plus one because high is

156
00:11:18,940 --> 00:11:19,680
still seven.

157
00:11:19,690 --> 00:11:21,580
So I need an area of size eight.

158
00:11:21,910 --> 00:11:25,590
So as a plus one seven plus one eight I have given.

159
00:11:25,990 --> 00:11:31,660
Otherwise you can take one bigger size really to say 100 instead of that X plus one, you can take one

160
00:11:31,660 --> 00:11:32,600
large size three.

161
00:11:34,320 --> 00:11:40,260
If you don't bother about space now, next in this code also I have to make changes, I should be less

162
00:11:40,260 --> 00:11:43,420
than or equal to murder, so I should stop at minute.

163
00:11:43,460 --> 00:11:50,280
If it is going more than the list fossilised has finished, then sameway J should be less than or equal

164
00:11:50,280 --> 00:11:52,000
to high height.

165
00:11:52,470 --> 00:12:01,250
These are the changes then comparing the element of ivied EOS only and this is a B. This is ARray's

166
00:12:01,260 --> 00:12:06,510
B and copying it and this is A, B and B of JS not there.

167
00:12:06,540 --> 00:12:07,890
This is ALJ only.

168
00:12:08,070 --> 00:12:11,510
See this is this portion of the OWFI and this portion of G.

169
00:12:11,880 --> 00:12:13,740
This is only done.

170
00:12:13,740 --> 00:12:14,280
This one.

171
00:12:14,460 --> 00:12:17,310
I'm less than equal to mid I should write.

172
00:12:17,490 --> 00:12:26,770
So this is less than or equal to copy these elements in Array B then the G less than or equal to higher.

173
00:12:26,780 --> 00:12:32,400
I should write less than or equal to high and the copy all the elements and B from A1.

174
00:12:34,930 --> 00:12:40,160
So the code is modified with some changes and it works for single all.

175
00:12:41,250 --> 00:12:45,360
Then the last thing is remaining, all these elements should be copied back to a.

176
00:12:45,660 --> 00:12:54,210
So here I will write on a loop for copying all elements for IDEX values from low to high copy elements

177
00:12:54,210 --> 00:12:55,980
from BofI to ÁLFHEIÐUR.

178
00:12:56,490 --> 00:13:01,050
So these elements will be copied back in a severe the final list.

179
00:13:02,190 --> 00:13:08,980
So this procedure is very useful, merging to list from a singular instance that is MARSOC.

180
00:13:09,000 --> 00:13:10,340
We will utilize this.

181
00:13:10,830 --> 00:13:12,250
So let me explain my thought.

182
00:13:12,270 --> 00:13:18,900
I will not be explaining this management of this procedure, which takes parameter array and low, mid

183
00:13:18,900 --> 00:13:21,100
and high onyx.

184
00:13:21,150 --> 00:13:28,110
One more thing about merging that is merging multiple lists if you have more than two lists than how

185
00:13:28,110 --> 00:13:31,740
we can merge them now merging multiple lists.

186
00:13:32,340 --> 00:13:38,790
I have taken an example of four sorted list and I want to merge them into a single sorted list.

187
00:13:39,820 --> 00:13:42,540
So what could be the procedure for first procedure?

188
00:13:42,570 --> 00:13:48,930
I'm asking you for permitting more e let us call it as E is there way to list?

189
00:13:48,940 --> 00:13:52,480
We were comparing the elements of Toolis, the smaller one we were writing.

190
00:13:52,840 --> 00:14:00,730
But now we have to compare the first element of all these four lists and copy the smallest element here

191
00:14:01,900 --> 00:14:04,180
to the smallest element that is complete copied.

192
00:14:04,540 --> 00:14:06,970
Now then this is copied to is copied.

193
00:14:07,240 --> 00:14:13,410
Let us exclude this and compare these four elements from these folders.

194
00:14:13,420 --> 00:14:15,330
That is five three five eight.

195
00:14:15,670 --> 00:14:19,000
So three is a smaller three is the culprit here.

196
00:14:19,150 --> 00:14:20,480
Then exclude this one.

197
00:14:21,220 --> 00:14:23,390
Now we have to compare these elements.

198
00:14:23,470 --> 00:14:25,110
So these are four elements.

199
00:14:25,630 --> 00:14:29,490
So it means we are comparing one element from each list.

200
00:14:29,800 --> 00:14:32,460
So at the time we are comparing four elements.

201
00:14:32,680 --> 00:14:33,180
Yes.

202
00:14:33,460 --> 00:14:37,090
And we are trying to merge all four list together.

203
00:14:37,810 --> 00:14:38,310
Yes.

204
00:14:38,650 --> 00:14:42,840
So if we are doing this, then it is called for.

205
00:14:42,850 --> 00:14:44,140
We merging.

206
00:14:44,410 --> 00:14:47,370
This method is called for to me merging.

207
00:14:47,500 --> 00:14:51,010
So it means if there are five lists then five emerging.

208
00:14:51,460 --> 00:14:56,620
If there are M lists, then it is emerging.

209
00:14:56,860 --> 00:15:05,410
So emerging is a known term and the management's merging and sorted list into a single product so that

210
00:15:05,410 --> 00:15:06,430
M can be anything.

211
00:15:06,640 --> 00:15:07,420
It can be three.

212
00:15:07,420 --> 00:15:08,110
It can be four.

213
00:15:08,110 --> 00:15:10,280
It can be then it can be anything.

214
00:15:10,300 --> 00:15:16,480
So we are merging all list together so we can build one one element from each list and copy the smallest

215
00:15:16,480 --> 00:15:16,720
one.

216
00:15:18,320 --> 00:15:20,810
Then there is another solution for this one.

217
00:15:20,990 --> 00:15:22,230
We'll look at that solution.

218
00:15:22,670 --> 00:15:25,590
Let us look at another method for civil.

219
00:15:25,590 --> 00:15:28,400
We merge these two lists and we get another list.

220
00:15:28,410 --> 00:15:30,080
So these names are ABCDE.

221
00:15:30,080 --> 00:15:33,050
So let us call this as E we get another list.

222
00:15:33,060 --> 00:15:35,590
E suppose these are merge.

223
00:15:36,470 --> 00:15:41,650
Suppose this is my list of these two, then we can post these two again.

224
00:15:41,900 --> 00:15:45,620
So this is F and the result of this merging.

225
00:15:46,620 --> 00:15:52,170
Now, instead of for I have to know, I selected two at a time that disappeared off list, I have much

226
00:15:52,470 --> 00:15:52,740
now.

227
00:15:52,740 --> 00:15:57,000
I can merge these two together and prepare another final list.

228
00:15:57,780 --> 00:16:02,250
So this is the most list of these two so that the Internet is a list of these full.

229
00:16:02,910 --> 00:16:09,300
So instead of merging all four together, I can take a pair of any two list and we can watch as we are

230
00:16:09,300 --> 00:16:10,970
selecting only two lists at a time.

231
00:16:10,980 --> 00:16:13,250
So this circle has to be merging.

232
00:16:14,360 --> 00:16:22,180
Yes, Toovey marching so there's are two marching scene still selecting these to be even I can select

233
00:16:22,190 --> 00:16:28,270
AC also Oregon's like 80 or Oregon can B.C. any I can select.

234
00:16:28,580 --> 00:16:29,390
And one more thing.

235
00:16:29,570 --> 00:16:35,850
After selecting a B instead of selecting CD, even I can select Cevdet and March.

236
00:16:35,930 --> 00:16:37,280
Then I get in that result again.

237
00:16:37,280 --> 00:16:39,470
More should be for one by one.

238
00:16:39,480 --> 00:16:44,900
You can merge them and the result for any pattern you can follow whatever the pattern suitable.

239
00:16:44,900 --> 00:16:46,130
You can follow that back up.

240
00:16:48,510 --> 00:16:55,480
So that's all about for emerging and reemerging or two emerging 9x videos.

241
00:16:55,530 --> 00:16:59,990
We'll be looking at more just sort and more sort users to emerge.

242
00:17:01,230 --> 00:17:07,200
We will look at it today, version of as well as recursive version of MOSSHART.

