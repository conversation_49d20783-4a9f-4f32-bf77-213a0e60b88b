1
00:00:00,510 --> 00:00:04,290
This video will write C++ class for start using linguist.

2
00:00:06,200 --> 00:00:11,550
I the project names Stock LLC P p that is Falangist.

3
00:00:13,330 --> 00:00:16,149
And project language is C++.

4
00:00:19,030 --> 00:00:26,750
You the project project is ready and clear of all the governments now here inside the program.

5
00:00:26,770 --> 00:00:29,500
First of all, I should write a class for Stack.

6
00:00:30,130 --> 00:00:32,409
I'm calling it as a stack, same name.

7
00:00:32,720 --> 00:00:35,990
Then what are the private members inside the private members?

8
00:00:36,010 --> 00:00:39,780
First of all, I should have appointed as a NORDER type.

9
00:00:39,790 --> 00:00:40,710
So what is Naude?

10
00:00:41,020 --> 00:00:47,460
So I will go back and write a class for Naude and order will contain two members.

11
00:00:47,580 --> 00:00:52,600
One is the data that I want to store and the other one is a node type pointer.

12
00:00:52,930 --> 00:01:00,940
So next to the pointer then these members, I want them to be accessed publicly so that I can use them

13
00:01:00,940 --> 00:01:03,640
inside this stack plus.

14
00:01:04,840 --> 00:01:08,110
Inside the stark glass, I have a pointer then.

15
00:01:09,310 --> 00:01:14,290
That's all I need, nothing more so than in the member functions, the very first function I should

16
00:01:14,290 --> 00:01:21,610
have as a constructor, and this should be a non parametrized constructor only because there is no size

17
00:01:21,610 --> 00:01:23,740
for this stack using Linklaters.

18
00:01:24,220 --> 00:01:27,370
So by default, the point that it's not their fault.

19
00:01:27,400 --> 00:01:29,470
So there's a default argument that is no argument.

20
00:01:29,470 --> 00:01:30,160
Constructor.

21
00:01:31,660 --> 00:01:37,930
I don't have to take any arguments then the other functions that should have as Bush, which will insert

22
00:01:37,930 --> 00:01:43,080
an element and the pop function that will delete an element and return the element.

23
00:01:43,450 --> 00:01:48,970
So it should be of type integer because the status integer type, then one more function is the display

24
00:01:48,970 --> 00:01:49,540
function.

25
00:01:50,710 --> 00:01:55,660
That's all these are the functions I'm going to implement them and implement them outside.

26
00:01:56,940 --> 00:02:02,670
So the first function is push, function and fosters, I should write a class name, so I should call

27
00:02:02,670 --> 00:02:07,860
it as push and parameters integer type for pushing.

28
00:02:07,860 --> 00:02:14,640
First of all, create a new order using temporary pointer, new node is created and then check whether

29
00:02:14,640 --> 00:02:20,490
if the node is created, if these equal elements node is not created, then I can give a message that

30
00:02:20,490 --> 00:02:22,290
stack is full.

31
00:02:23,240 --> 00:02:24,850
And I will not insert any note.

32
00:02:25,780 --> 00:02:32,590
Else I can insert a not so else blog I will write on so a note is created, so I should set the data

33
00:02:32,590 --> 00:02:33,400
for a..

34
00:02:35,820 --> 00:02:40,860
As X and these next should be pointing on appointer.

35
00:02:42,070 --> 00:02:48,850
And it should come up on the NetSol, a notice inserted that is a value is inserted in the stack pushed

36
00:02:48,850 --> 00:02:49,450
into stack.

37
00:02:51,440 --> 00:02:56,930
Not an next function is pop function, so first of all, class name and scope, resolution function

38
00:02:56,930 --> 00:02:59,630
name is pop and it should not ask anything.

39
00:02:59,900 --> 00:03:06,380
First thing is should check that if Topo's equals to null, then Stack is empty so we can give a message

40
00:03:06,380 --> 00:03:09,850
that stack is empty.

41
00:03:10,190 --> 00:03:12,020
We cannot delete.

42
00:03:12,020 --> 00:03:12,770
And the element.

43
00:03:14,040 --> 00:03:20,160
So I've given solution here or else we can delete an element, so for deleting an element, I should

44
00:03:20,160 --> 00:03:24,060
have one temporary variable outside that is X, which will take a deleted value.

45
00:03:25,080 --> 00:03:30,540
And initialized to minus one, then exercising of data.

46
00:03:34,760 --> 00:03:35,310
Data.

47
00:03:36,890 --> 00:03:43,520
Then I need a temporary pointer to help this new order to be deleted, so I'll take that temporary pointer

48
00:03:43,820 --> 00:03:47,790
up on top, then move up to next north.

49
00:03:49,400 --> 00:03:55,310
Porcine DOBs Nixonland, then after that delete, the note is deleted.

50
00:03:56,790 --> 00:04:00,290
Then return the element that is deleted.

51
00:04:01,590 --> 00:04:03,720
All the support function.

52
00:04:05,700 --> 00:04:12,810
The next I will ride on display function, so it's a class name is STAC scope resolution display function,

53
00:04:12,810 --> 00:04:17,339
which will display all elements of displaying all the elements I needed and the pointer to traverse

54
00:04:17,339 --> 00:04:18,120
to a inkless.

55
00:04:18,540 --> 00:04:23,490
So using a loop I can traverse it while PS not null.

56
00:04:24,030 --> 00:04:28,050
Then every time I should display a value frumpies data.

57
00:04:30,980 --> 00:04:37,640
And also give some space in between the elements, then the piece should be moving to our next normed.

58
00:04:39,690 --> 00:04:48,960
I don't I will also give N.L. for a new line, that's all the classes really I have written three functions

59
00:04:48,960 --> 00:04:51,900
that is SpongeBob and displayed are the functions you can implement.

60
00:04:53,050 --> 00:04:57,010
Then here, inside the main function, I'll create an object of class stock.

61
00:04:58,120 --> 00:05:05,290
Here at the main function will create an object of stock, a sticky so object is created that I should

62
00:05:05,290 --> 00:05:09,100
call STK or push evidence or an element that is 10.

63
00:05:11,150 --> 00:05:12,520
Then stickied or.

64
00:05:13,650 --> 00:05:20,240
Bush will insert one more element, stick it out, Bush, and insert one more today.

65
00:05:21,130 --> 00:05:21,840
That's it.

66
00:05:21,870 --> 00:05:24,990
Then I will say STK don't display.

67
00:05:26,390 --> 00:05:27,890
It should just be all elements.

68
00:05:28,130 --> 00:05:29,120
Let us run this.

69
00:05:30,170 --> 00:05:36,740
Remember, for using CNN code, you have tried using namespace us, yes, all three elements are pushed

70
00:05:36,740 --> 00:05:37,420
into the stack.

71
00:05:37,430 --> 00:05:40,080
I can see all these elements displays displaying them.

72
00:05:41,170 --> 00:05:47,210
Now, one of the element and see what happens, see out of a directory called the Function Pop, and

73
00:05:47,210 --> 00:05:49,340
I'm displaying the result given by that one.

74
00:05:50,630 --> 00:05:59,720
So should be popped out, yes, here today is Bob Paddys deleted from the stack so you can check these

75
00:05:59,720 --> 00:06:02,570
functions, Bush and Bob and display all the functions are working.

76
00:06:04,860 --> 00:06:08,520
That's all followed the class for start using legalist.

