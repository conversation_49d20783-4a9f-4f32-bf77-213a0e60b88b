1
00:00:01,170 --> 00:00:05,850
And this video, we will try to see program for diagonal matrix, so I will start a new project.

2
00:00:06,930 --> 00:00:13,290
And the project name, I will call it, as I also like the language, I see language.

3
00:00:14,460 --> 00:00:16,770
Next, create a project.

4
00:00:19,160 --> 00:00:22,580
Project is ready and here is the main function.

5
00:00:28,660 --> 00:00:34,300
I will hide these panels, not the complete programs here, as we have discussed already on white board

6
00:00:34,300 --> 00:00:41,350
for implementing a matrix, we need a single dimension array for storing the elements, donly, non-zero

7
00:00:41,350 --> 00:00:44,320
elements and also extra dimensions that is in there.

8
00:00:44,980 --> 00:00:48,690
So I will define a structure for these two things together.

9
00:00:50,600 --> 00:00:52,280
Call the structure name as.

10
00:00:53,900 --> 00:00:57,860
Not so that it is useful for all types of mattresses.

11
00:00:58,430 --> 00:01:02,630
First of all, we need an audit of some size, so I will take an array of size.

12
00:01:03,230 --> 00:01:08,130
And right now, for Degnan might see I'm taking a fixed size early.

13
00:01:08,540 --> 00:01:10,700
You can create a red anemically.

14
00:01:11,640 --> 00:01:17,400
In a heap, we already know how to create a single dimension in the heap so you can take a pointer and

15
00:01:17,850 --> 00:01:22,450
create an array, but for exploration purposes, I am digging a fixed size earning.

16
00:01:24,060 --> 00:01:25,830
Now, this is a diagonal mattocks.

17
00:01:27,110 --> 00:01:31,530
Here inside the main function, I will create a variable of I am.

18
00:01:32,850 --> 00:01:37,230
And let us set the dimension of this one as of four, this is four by four mattocks.

19
00:01:39,960 --> 00:01:45,410
Now, I will ride on the functions that we have discussed that the set and get function, so first function,

20
00:01:45,420 --> 00:01:46,410
I will ride on set.

21
00:01:48,460 --> 00:01:52,210
Said it should take a mattocks as a parameter.

22
00:01:53,190 --> 00:01:59,800
The parameter name I will give it as I'm only then it means I engie value, what is the index at which

23
00:02:00,130 --> 00:02:05,410
you want to set the element and lasting is what is the element you want to insert?

24
00:02:09,639 --> 00:02:15,340
And it's going to modify the Matrix, so it should be called by address, so I will take a pointer.

25
00:02:17,030 --> 00:02:22,670
Then as we are going to start only non-zero elements, then we should check whether the indices are

26
00:02:22,700 --> 00:02:23,690
equal or not.

27
00:02:23,690 --> 00:02:29,240
If the indicators are equal, then we will set an element at that particular index.

28
00:02:30,420 --> 00:02:36,480
Not I'm not if I can either use a minus one or minus on anything and then I should assign the variable

29
00:02:36,990 --> 00:02:37,560
X.

30
00:02:41,050 --> 00:02:45,790
So if I is equal to that, only the value will be said, otherwise nothing will be stored.

31
00:02:46,150 --> 00:02:47,740
Zero elements are not stored.

32
00:02:49,210 --> 00:02:52,550
Now, next, I will write a function for get it returns in digital value.

33
00:02:52,900 --> 00:03:00,430
It needs an early matics that is embarrassment and and then it needs in I.

34
00:03:00,430 --> 00:03:03,250
N so it returns the element.

35
00:03:03,250 --> 00:03:06,940
And that particular index, they should also return the value of as equals today.

36
00:03:06,970 --> 00:03:14,410
So first check if I is equal to G, then we can return an element or return an element from array of

37
00:03:14,590 --> 00:03:15,610
ie minus one.

38
00:03:15,610 --> 00:03:20,410
Either I can write a minus one or minus one otherwise element is not.

39
00:03:20,410 --> 00:03:21,780
Then once it is the zero.

40
00:03:23,680 --> 00:03:28,880
If it is non-zero, then the of minus one million, otherwise zero will be sent.

41
00:03:29,740 --> 00:03:36,280
Element is zero type, then I will write one more function for displaying this matrix, so function

42
00:03:36,280 --> 00:03:37,780
NamUs display.

43
00:03:39,620 --> 00:03:42,800
Then it should take Mattocks as a barometer.

44
00:03:45,650 --> 00:03:51,420
I need two variables for running to follow this, because it should display as two dimensionality,

45
00:03:51,740 --> 00:04:00,140
so using followup I will display for I assign zero and I use less than Hamdard and I plus plus.

46
00:04:01,990 --> 00:04:03,010
Then for.

47
00:04:04,690 --> 00:04:13,570
Jason, zero, just less than an end and joblessness and in this followup, I should print the value

48
00:04:13,570 --> 00:04:18,200
only if I is equal to G, then I should print F.

49
00:04:19,769 --> 00:04:22,140
Personally, I'll get some space.

50
00:04:23,520 --> 00:04:27,240
That is not of a minus one element.

51
00:04:29,840 --> 00:04:31,910
Otherwise, I should spend Zettl.

52
00:04:36,020 --> 00:04:37,970
Just little with the space.

53
00:04:39,550 --> 00:04:46,580
Then after the end of a follow, that is after the end of a rule, I should print new line that set

54
00:04:46,640 --> 00:04:47,890
their display function.

55
00:04:48,730 --> 00:04:54,080
Now from the main function, I will call set function and set some values.

56
00:04:54,080 --> 00:04:59,890
So that other diagonal matrix, then I will call a display to show you the matrix.

57
00:05:00,220 --> 00:05:04,870
So here I will be all set function and set the values.

58
00:05:04,870 --> 00:05:07,900
First of all, one comma one I will set the value five.

59
00:05:10,930 --> 00:05:12,580
And the same line I will ride on.

60
00:05:14,000 --> 00:05:19,310
Busari and Tsukamoto also said the value that is eight.

61
00:05:20,390 --> 00:05:22,340
Likewise, I will set one more value.

62
00:05:26,250 --> 00:05:28,800
Three commentary, and the value is nine.

63
00:05:30,380 --> 00:05:32,020
Then I forgot my phone.

64
00:05:32,180 --> 00:05:37,510
Also, I will check the value values tools, so I have set some values.

65
00:05:37,880 --> 00:05:40,970
No, let us display this mattocks.

66
00:05:42,840 --> 00:05:43,980
Later in the program.

67
00:05:47,220 --> 00:05:52,630
Yeah, here we go to diagonal markets, that is, the elements are non-zero only inside a diagonal.

68
00:05:52,740 --> 00:05:58,650
Rest of the elements are zeroes because I'm printing here zero values, right.

69
00:05:58,770 --> 00:06:06,330
If the I value and value are not equal, if I think values are equal and displaying em off of I write

70
00:06:07,050 --> 00:06:08,700
while storing, I'm giving them.

71
00:06:08,700 --> 00:06:10,650
This is starting from one onwards.

72
00:06:11,980 --> 00:06:18,950
So while studying, I'm taking a minus one or a minus one and retrieving what for this plane, I am

73
00:06:18,970 --> 00:06:20,170
starting from zero on work.

74
00:06:20,180 --> 00:06:27,130
So I have to only if I if I have to use the same formula minus one, then I should start this from one

75
00:06:27,130 --> 00:06:27,880
onwards.

76
00:06:27,880 --> 00:06:29,830
I assign one and assign one.

77
00:06:30,700 --> 00:06:35,380
Now let us call a function, get and get the value at some location.

78
00:06:36,650 --> 00:06:39,980
I will directly bring the value get person, Tildy.

79
00:06:41,320 --> 00:06:46,570
And new line, I will get the value of collocation to Clamato.

80
00:06:48,640 --> 00:06:50,200
So it should give me evaluate.

81
00:06:50,410 --> 00:06:51,640
So it should be an eight.

82
00:06:53,720 --> 00:06:59,690
Yes, it is, but the eight and then after that display function is displaying all elements of matics.

83
00:07:01,100 --> 00:07:06,620
So that's all anywhere inside your application, if you need a diagonal matrix, then you should have

84
00:07:06,620 --> 00:07:12,110
a structure and the supporting functions for a matrix and in fact, all you need to display the markets,

85
00:07:12,110 --> 00:07:13,850
then you can have this function also.

86
00:07:13,880 --> 00:07:18,060
Otherwise, for looking at the output, we are using this display function.

87
00:07:18,380 --> 00:07:24,090
So this may or may not be required, but certain functions are more important for this magnox.

88
00:07:24,110 --> 00:07:29,900
So this is the implementation of the Matrix now for the following markets is also the same thing we

89
00:07:29,900 --> 00:07:30,410
will do.

90
00:07:30,710 --> 00:07:31,960
Only the differences.

91
00:07:31,980 --> 00:07:36,590
You may have to change the size of this array and the formulas will change.

92
00:07:39,560 --> 00:07:42,590
So for the rest of the markets, it will be a student exercise.

93
00:07:43,980 --> 00:07:45,360
That followed this mattocks.

