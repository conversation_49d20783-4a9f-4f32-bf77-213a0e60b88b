1
00:00:00,150 --> 00:00:05,530
And this video will look at the demonstration for the lead operation, so I'm continuing the same project.

2
00:00:05,820 --> 00:00:09,280
So just the main function, I will write down or delete function.

3
00:00:09,300 --> 00:00:15,840
It will take a pointer to Had<PERSON>, let us call it USPI and the index from where we want to delete an

4
00:00:15,840 --> 00:00:16,200
element.

5
00:00:16,350 --> 00:00:18,610
Then I may require more pointers like.

6
00:00:18,610 --> 00:00:25,770
Q I will take a point a few also, and I will take an integer variable that is for traversing link list

7
00:00:25,770 --> 00:00:29,100
then the first condition if index is valid or not.

8
00:00:29,280 --> 00:00:37,400
So if index is less than zero or if index is greater than length of a linguist.

9
00:00:38,070 --> 00:00:39,990
So I should pass for this one.

10
00:00:40,020 --> 00:00:45,750
If so, then I will return minus one thing that the index is invalid.

11
00:00:45,770 --> 00:00:47,120
Otherwise I can delete.

12
00:00:47,250 --> 00:00:52,130
But for deletion I should make sure that if this had a..

13
00:00:52,140 --> 00:00:55,660
That is index this one then I should delete head north.

14
00:00:55,830 --> 00:01:00,870
So if index is equal to one then I have to delete hadnot.

15
00:01:01,350 --> 00:01:07,740
So for deleting hadnot he's already pointing up on hadnot like I have to parse this as a barometer piece

16
00:01:07,770 --> 00:01:13,700
up on Hudnall, then I should traverse through a Linkous and I should make the point upon the last note.

17
00:01:13,890 --> 00:01:21,970
So using my loop I will move B while the GOP's next is not equal to had I'll be moving beat.

18
00:01:21,990 --> 00:01:29,250
I'm writing in the same line these next and once it has reached on the last node then I can take the

19
00:01:29,250 --> 00:01:33,480
data from hadnot in a variable X data is copied.

20
00:01:34,230 --> 00:01:38,730
The normal thing I need a variable X also so I will declare a variable X on the top and come back to

21
00:01:38,730 --> 00:01:39,270
this line.

22
00:01:39,910 --> 00:01:45,030
Then after deleting head node, I should also make sure that is it the only node that is the last node.

23
00:01:45,330 --> 00:01:49,020
If it is last known then header will be equal to E.

24
00:01:49,350 --> 00:01:53,640
If had equal to payments then I should freeheld and stop delete.

25
00:01:53,640 --> 00:01:58,170
We have three function in C language then I should make that equal to not.

26
00:01:58,170 --> 00:02:00,390
Lippmann's had a sign but nothing else.

27
00:02:00,390 --> 00:02:07,380
If it is not the last node, if it is not the only node, then I should make the PS next point upon

28
00:02:07,830 --> 00:02:08,900
heads next.

29
00:02:09,570 --> 00:02:16,650
So that had an order released logically then free it from the mean memory that is from heap memory.

30
00:02:17,190 --> 00:02:22,630
Then make hard point on next node of p e next.

31
00:02:22,650 --> 00:02:29,100
So this all what I have to do if the index given is one, if index is not one, then the procedure is

32
00:02:29,400 --> 00:02:32,530
similar to deleting from a linear link list.

33
00:02:32,640 --> 00:02:38,750
So for that I will use a follow up and make it repeat for index minus two times.

34
00:02:39,180 --> 00:02:47,520
So while I zero is less than index minus two and I plus plus and every time move B to the next node

35
00:02:48,030 --> 00:02:52,840
now even be a node before the node that we want to delete, then we need to.

36
00:02:52,840 --> 00:02:59,160
Q The point on the PS next node, then I can delete that node with the Q and that is the node actually

37
00:02:59,160 --> 00:02:59,910
we want to delete.

38
00:03:00,210 --> 00:03:07,830
So but before that piece next should point on Qs next, then take the data from CU then three.

39
00:03:07,830 --> 00:03:10,730
Q That's all it will be deleted.

40
00:03:10,740 --> 00:03:14,850
And finally I should return the deleted element that is x c.

41
00:03:14,850 --> 00:03:17,340
There are more than one guesses.

42
00:03:17,340 --> 00:03:22,170
Like if a Nexus one that is we are deleting first node then is it the last node.

43
00:03:22,170 --> 00:03:28,500
So we have to make it as null otherwise deleted node and make some new node, next node as a node.

44
00:03:29,010 --> 00:03:34,020
And if it is not first indexed then we should delete the element just like we have deleted it.

45
00:03:34,020 --> 00:03:34,790
A linear link.

46
00:03:36,350 --> 00:03:40,970
Now, inside the main function, already, we have created a link out of these five elements.

47
00:03:41,360 --> 00:03:49,040
Now let us all delete function by passing ahead and let us delete node from index one that is first

48
00:03:49,040 --> 00:03:52,130
nor to then after that, our display will display a linguist.

49
00:03:52,340 --> 00:03:52,970
Let us run.

50
00:03:53,210 --> 00:03:55,070
It should get three, four, five, six.

51
00:03:55,910 --> 00:03:59,600
Yes, we got three, four, five, six fussin orders deleted.

52
00:04:00,110 --> 00:04:01,570
Let us delete second node.

53
00:04:01,580 --> 00:04:02,750
So three should be deleted.

54
00:04:02,750 --> 00:04:05,210
We should have two, four, five, six.

55
00:04:05,210 --> 00:04:07,450
Yes we got two, four, five, six.

56
00:04:07,460 --> 00:04:09,080
Then let us delete the last element.

57
00:04:09,080 --> 00:04:10,880
That is the next five six.

58
00:04:10,880 --> 00:04:12,100
Node six is deleted.

59
00:04:12,110 --> 00:04:13,820
Now we have two, three, four, five.

60
00:04:14,420 --> 00:04:20,240
So it's perfectly deleting all the in which over the next we are giving, it's deleting an element.

61
00:04:20,570 --> 00:04:24,530
Let us give an index eight and try this one on the Linklaters as it is.

62
00:04:24,530 --> 00:04:27,860
Two, three, four, five, six, because the index is invalid already.

63
00:04:27,860 --> 00:04:33,050
We have checked the condition here that if the index is invalid then return minus one, otherwise it

64
00:04:33,050 --> 00:04:34,650
will delete and return the element.

65
00:04:34,670 --> 00:04:37,730
So in my main function, I'm not taking that deleted element.

66
00:04:38,150 --> 00:04:43,640
If you want, you can take the deleted element and display so that all with this delete function and

67
00:04:43,640 --> 00:04:49,700
upon supply list I have shown you display, insert, delete and land function, rest of the function.

68
00:04:49,700 --> 00:04:56,360
What all we have done upon Linell links, you can write them on circular link also, and that's a straw

69
00:04:56,480 --> 00:04:57,140
exercise.

