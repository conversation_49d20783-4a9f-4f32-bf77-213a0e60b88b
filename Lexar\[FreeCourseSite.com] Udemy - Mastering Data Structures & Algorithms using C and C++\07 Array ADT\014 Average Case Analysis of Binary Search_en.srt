1
00:00:00,630 --> 00:00:08,460
Now, there is the average case analysis of binary search, so average <PERSON><PERSON>'s total time taken in

2
00:00:08,460 --> 00:00:11,760
all possible cases divided by number of cases.

3
00:00:12,540 --> 00:00:13,620
So what are the cases?

4
00:00:13,950 --> 00:00:20,910
I may be searching for an element which is present here in the middle of a list or route of a tracing

5
00:00:20,910 --> 00:00:21,190
tree.

6
00:00:22,020 --> 00:00:23,090
So how many combination?

7
00:00:23,100 --> 00:00:27,220
It requires just one comparison like that.

8
00:00:27,270 --> 00:00:32,549
Next next case is this one I searching for 15, which is present here, which is in the middle of the

9
00:00:32,549 --> 00:00:32,750
list.

10
00:00:32,750 --> 00:00:36,000
Sublist are thirty seven, which is the middle of the night sublist.

11
00:00:36,240 --> 00:00:38,040
So I may be searching this one or this one.

12
00:00:38,070 --> 00:00:44,310
So two cases, these two elements, how many compendiums required for these two elements.

13
00:00:44,850 --> 00:00:46,980
One competition required for these elements.

14
00:00:47,100 --> 00:00:48,480
One company should require.

15
00:00:50,190 --> 00:00:56,490
So have included bought one and two, then these four elements, how many competitions required for

16
00:00:56,490 --> 00:01:00,150
each one to four here, also one two for this, also one.

17
00:01:00,330 --> 00:01:05,099
So there are four elements which require two competitions then.

18
00:01:06,580 --> 00:01:10,780
One, two, three, four, five, six, seven, eight elements which required three.

19
00:01:11,410 --> 00:01:14,710
So there are eight elements which require three competitions.

20
00:01:16,420 --> 00:01:22,930
So this is going in the form so for 15 elements, we have taken an example, but we have to give it

21
00:01:22,930 --> 00:01:25,480
in terms of any elements, whatever the elements may be.

22
00:01:25,930 --> 00:01:35,720
So if we observe this, this can be done as one plus one into two, one plus two into dupa.

23
00:01:35,740 --> 00:01:39,250
Two plus three into two.

24
00:01:39,260 --> 00:01:40,330
Power three.

25
00:01:42,370 --> 00:01:45,170
So it is written in terms of powers of two.

26
00:01:45,790 --> 00:01:48,580
Now, this can be written as some of.

27
00:01:49,830 --> 00:01:58,440
I.e., into Dupa, I mean, so how I is taking the value, so here I use the power of one power to promote,

28
00:01:58,680 --> 00:02:04,280
to ignore this one so we can have a summation formula from these terms.

29
00:02:04,290 --> 00:02:08,310
So I start from one to three, so I start from one.

30
00:02:08,610 --> 00:02:09,580
What is that three.

31
00:02:09,870 --> 00:02:13,620
See, this is one, two, three, four.

32
00:02:13,890 --> 00:02:18,090
So this is height of tree, height of a tree.

33
00:02:18,270 --> 00:02:22,590
So height of a tree is measured by taking it just one, two, three.

34
00:02:22,890 --> 00:02:26,220
So that is a log of N log of N.

35
00:02:26,550 --> 00:02:29,400
So this is approximately log of.

36
00:02:29,400 --> 00:02:32,690
And so this log in is the height of a tree.

37
00:02:33,300 --> 00:02:39,000
So as many levels or whatever the height of the tree is, depending on that, that many times we have

38
00:02:39,000 --> 00:02:39,390
to pick.

39
00:02:39,780 --> 00:02:42,670
So here it is one, then two, then three.

40
00:02:43,380 --> 00:02:46,770
So this is the total time taken in all possible cases.

41
00:02:46,770 --> 00:02:48,690
That is some of the Times article.

42
00:02:48,690 --> 00:02:55,470
In all possible cases, I have taken the total number of competitions required for searching each and

43
00:02:55,470 --> 00:02:56,310
every element.

44
00:02:56,610 --> 00:03:00,920
Then this should be divided by N because total N elements are there.

45
00:03:01,320 --> 00:03:05,350
So that is the sum of all the possible guesses divided by number of cases.

46
00:03:05,370 --> 00:03:06,450
So what is the case here?

47
00:03:06,450 --> 00:03:08,810
Each case, each element is a case.

48
00:03:09,570 --> 00:03:13,140
Some of the times of all the elements are divided by number of elements.

49
00:03:13,630 --> 00:03:19,440
Then this numerator, this is approximately equal to log in.

50
00:03:19,800 --> 00:03:26,490
I use replace that log in and here also is replaced with logging into topower log in.

51
00:03:27,090 --> 00:03:29,130
And this is divided by N.

52
00:03:30,250 --> 00:03:37,070
Then this camera demands log and and this power can be brought here.

53
00:03:37,180 --> 00:03:40,750
So this will be an log, too.

54
00:03:41,320 --> 00:03:45,790
So here we are taking the values and powers of two.

55
00:03:45,820 --> 00:03:48,220
So that is log to base two.

56
00:03:48,460 --> 00:03:51,380
So this becomes one and divided by N.

57
00:03:51,790 --> 00:03:54,330
So this is an end gets canceled.

58
00:03:54,340 --> 00:03:57,400
So the answer is and login.

59
00:03:57,670 --> 00:04:00,690
So the average case answers also login.

60
00:04:01,240 --> 00:04:06,220
See, when we are finding average case, then we have to solve this one submission form.

61
00:04:06,220 --> 00:04:11,710
La Liga already told at finding average case analysis, finding average case results.

62
00:04:11,950 --> 00:04:13,450
It's a time consuming little.

63
00:04:13,450 --> 00:04:16,089
Some time you cannot get the result, exact result.

64
00:04:16,360 --> 00:04:18,850
We have to take approximate one if you want.

65
00:04:18,850 --> 00:04:24,390
You can convert this one into an integration formula and you can apply the limits and get the result

66
00:04:24,400 --> 00:04:28,490
that approximately this will be login and go to our login only.

67
00:04:28,510 --> 00:04:31,410
So average case time is also login.

68
00:04:32,110 --> 00:04:35,650
So we have seen best case time as one.

69
00:04:37,930 --> 00:04:48,960
Was his time as Log-in an average case time is also and then we'll do some more analysis on this one.

70
00:04:50,360 --> 00:04:58,300
Let us do average case analysis for successful surge as well as unsuccessful search, successful searches

71
00:04:58,300 --> 00:04:59,650
is if the element is found.

72
00:04:59,660 --> 00:05:06,410
So that is represented by this circular Nords and unsuccessful searches element is not found that is

73
00:05:06,410 --> 00:05:13,650
represented by this square Nords for finding the average case time for successful search.

74
00:05:13,670 --> 00:05:19,100
We have to consider these internal laws, the internal laws required.

75
00:05:19,100 --> 00:05:20,200
How many competition?

76
00:05:20,210 --> 00:05:21,470
That depends on the level.

77
00:05:21,590 --> 00:05:27,740
One competition for this to competition for this three competition for these elements depends on level

78
00:05:28,490 --> 00:05:29,090
MacAir.

79
00:05:29,090 --> 00:05:32,210
We can count, adjusts the pot.

80
00:05:32,450 --> 00:05:36,280
See, this is to adjust away from root support.

81
00:05:36,290 --> 00:05:38,420
Each year we require three competition.

82
00:05:38,780 --> 00:05:42,290
So the part plus one we can see Bonta plus one.

83
00:05:42,290 --> 00:05:45,430
So for every node we can take the part and plus one.

84
00:05:46,100 --> 00:05:50,740
So for all the nodes we can take the parts of all circular nodes.

85
00:05:50,900 --> 00:05:54,290
So these sort of nodes, we can call them as internal ones.

86
00:05:54,290 --> 00:05:59,780
So let us say I is the sum of the parts of all internal laws.

87
00:05:59,810 --> 00:06:06,800
I represent some of the parts of all internal nodes which are representing successful such nodes, the

88
00:06:06,800 --> 00:06:11,240
square nodes, let us call them as external laws which are representing unsuccessful search.

89
00:06:11,510 --> 00:06:17,180
So let us say E is the total box of all external nodes.

90
00:06:17,180 --> 00:06:20,540
So some of the parts of all external nodes.

91
00:06:20,960 --> 00:06:23,210
So there is no relationship between this one.

92
00:06:23,450 --> 00:06:29,390
If e the sum of the parts of external and also then I use the sum of the parts of internal nodes, then

93
00:06:29,540 --> 00:06:31,010
this is always true.

94
00:06:32,250 --> 00:06:35,940
Is equal to eight plus two n as always, true.

95
00:06:36,560 --> 00:06:44,150
So a small example I will take and show you like suppose there are three internal nodes so definitely

96
00:06:44,150 --> 00:06:46,670
there will be for external nodes.

97
00:06:47,240 --> 00:06:48,790
Now a number of nodes are three.

98
00:06:49,370 --> 00:06:55,340
Now some of the parts of internal nodes C the part of this one is one, the part of this one is one.

99
00:06:55,340 --> 00:07:02,200
So total part is to then for the external nodes for this is the part, this one two for this also two.

100
00:07:02,420 --> 00:07:04,550
So there are four nodes whose part is too.

101
00:07:04,760 --> 00:07:07,580
So this is for India two.

102
00:07:07,610 --> 00:07:08,510
That is eight.

103
00:07:08,990 --> 00:07:12,350
So this E is equals two plus two.

104
00:07:12,350 --> 00:07:17,480
And so this is two plus this is two and the number of nodes are three.

105
00:07:17,780 --> 00:07:20,300
So this is two, three, six plus two.

106
00:07:20,300 --> 00:07:21,030
That is eight.

107
00:07:21,350 --> 00:07:28,310
So here with an example, I have proved it then one more fact we will take that is a number of external

108
00:07:28,310 --> 00:07:31,420
lawnside equal to internal lawn's plus one.

109
00:07:31,790 --> 00:07:37,070
So if there are 15 circular nodes, there are 16 squared nodes.

110
00:07:37,550 --> 00:07:42,140
So a number of examples will be one more than a number of internal logs.

111
00:07:42,140 --> 00:07:44,030
So this another factor we take?

112
00:07:44,390 --> 00:07:44,660
No.

113
00:07:44,660 --> 00:07:48,170
What is average successful time for N elements?

114
00:07:48,620 --> 00:07:59,690
So average successful search time for an element is equal to total parts of all internal nodes divided

115
00:07:59,690 --> 00:08:01,550
by number of nodes.

116
00:08:02,000 --> 00:08:09,320
So we will get average bot and we know that for this node parties two, but the number of competitions

117
00:08:09,320 --> 00:08:10,240
are one extra.

118
00:08:10,490 --> 00:08:13,090
So for every node, one extra competition is there.

119
00:08:13,430 --> 00:08:16,250
So average part plus one.

120
00:08:16,940 --> 00:08:19,920
So this gives average successful time.

121
00:08:21,490 --> 00:08:25,390
In dozens of parts of Indiana, more than.

122
00:08:28,720 --> 00:08:36,429
What is the average time taken for unsuccessful search, so average time taken for unsuccessful search

123
00:08:36,429 --> 00:08:37,549
for any elements?

124
00:08:37,570 --> 00:08:41,919
Is it by total number of Norns average time?

125
00:08:42,159 --> 00:08:47,230
So the total number of Nawzad, we know that if nauseated, an extended loans will be endless fun.

126
00:08:47,260 --> 00:08:51,940
So this is just one average successful time, average unsuccessful time.

127
00:08:52,690 --> 00:08:55,360
Now, there is one more fact that I have to highlight here.

128
00:08:55,660 --> 00:09:00,220
That is, all these external laws are the same height.

129
00:09:00,880 --> 00:09:03,450
So I can take the part as a login.

130
00:09:03,850 --> 00:09:08,940
So total how many nodes and plus one so and plus one into login.

131
00:09:08,950 --> 00:09:20,810
So APR E is equal to log and or any plus one in the log and plus one we can take orders and login.

132
00:09:21,340 --> 00:09:25,090
So how much is the time taken in unsuccessful search.

133
00:09:25,420 --> 00:09:26,680
This can be taken as.

134
00:09:27,950 --> 00:09:35,890
Analog and divided by and plus one, so we can take it as approximate and plus one and cancel this one,

135
00:09:35,900 --> 00:09:37,240
so that is Log-in.

136
00:09:37,550 --> 00:09:41,210
So average, unsuccessful search time is also login.

137
00:09:41,600 --> 00:09:45,440
Now, don't get confused that how I am dividing and by and large fun.

138
00:09:45,630 --> 00:09:52,460
So that plus one, you can ignore it like approximately one that I'm cutting off and my endless fun.

139
00:09:53,000 --> 00:10:00,180
Seaforth Knowing the time complexity we need to approximate, we need just the idea for that idea.

140
00:10:00,200 --> 00:10:01,970
We can take the approximate value.

141
00:10:02,150 --> 00:10:03,990
We're going to have to be very accurate.

142
00:10:04,310 --> 00:10:06,350
So this is a plus one.

143
00:10:06,350 --> 00:10:08,090
Assume that this is also a plus one.

144
00:10:08,100 --> 00:10:13,130
As I said, this will be unpleasant, but we are writing in here so and plus one plus one gets canceled.

145
00:10:13,130 --> 00:10:14,090
So it is a login.

146
00:10:14,450 --> 00:10:17,270
So average unsuccessful search time is log.

147
00:10:17,270 --> 00:10:20,990
And then what about successful search time?

148
00:10:21,350 --> 00:10:27,370
See, from this formula we can see that EA and I are related to one another.

149
00:10:27,590 --> 00:10:28,090
Yes.

150
00:10:28,490 --> 00:10:34,270
So in this formula I will replace EA because ez how much and login.

151
00:10:34,520 --> 00:10:37,030
So let us replace this formula.

152
00:10:37,130 --> 00:10:45,350
So here I will to work out every successful for M is equal to one plus Ibai and.

153
00:10:46,200 --> 00:10:57,030
Ezequiel's to E-Plus to win so icicles to E minus to win so this can be the nice one plus E minus two

154
00:10:57,030 --> 00:11:09,690
and by and this can be the last one plus E by M minus two not e what e is so already you know done so

155
00:11:09,690 --> 00:11:10,980
that is and login.

156
00:11:11,250 --> 00:11:17,340
So this can be taken as one plus and the log in by N minus two.

157
00:11:17,340 --> 00:11:18,640
So this gives cancel.

158
00:11:18,930 --> 00:11:21,420
So this is approximately Log-in.

159
00:11:22,530 --> 00:11:25,740
So every successful search time is also log-in.

