1
00:00:00,600 --> 00:00:06,840
In this video, I'll write a function for Shell, sort of if you have seen an algorithm for Shell thought

2
00:00:06,840 --> 00:00:10,200
and we have discussed about it, so it's a simple function.

3
00:00:10,440 --> 00:00:16,740
It will be following the method of insertions or so, let us write on a function for shell shock.

4
00:00:16,900 --> 00:00:17,310
Yeah.

5
00:00:19,710 --> 00:00:23,400
It takes array of elements and the number of elements.

6
00:00:26,640 --> 00:00:34,350
Now, inside this, I need variable, so I will declare them like a gap variable and I and <PERSON> then the

7
00:00:34,350 --> 00:00:37,020
first volume as Gap.

8
00:00:39,270 --> 00:00:47,160
Gap that is started by end by two and the gap is greater than one.

9
00:00:49,440 --> 00:00:50,040
And then.

10
00:00:51,590 --> 00:00:58,740
Gap is divided by two every time, so there's no more Sloup than inside that fault.

11
00:00:59,000 --> 00:01:02,540
Eye for eye that starts from Gap.

12
00:01:04,129 --> 00:01:08,600
And I is less than N and I placeless.

13
00:01:13,960 --> 00:01:20,440
Then inside this, we need a temporary variable in that we will have an element of AOF, i.e., that

14
00:01:20,440 --> 00:01:26,530
we want to insert, then G should start from Gap minus I.

15
00:01:28,730 --> 00:01:31,900
Then a loop that is for shifting all the elements.

16
00:01:31,970 --> 00:01:35,900
While Jay is greater than zero and.

17
00:01:37,860 --> 00:01:38,940
Aw, gee.

18
00:01:39,890 --> 00:01:41,030
It's greater than.

19
00:01:43,770 --> 00:01:48,360
And every time we plus one, we will copy of G.

20
00:01:49,800 --> 00:01:52,020
And Jay, minus minus.

21
00:01:53,240 --> 00:01:56,480
Then at AOF G plus gap.

22
00:01:57,860 --> 00:02:05,000
We will assign the element from of energy should that agreement by J minus Gap.

23
00:02:09,610 --> 00:02:14,300
Then after the end of this loop, we should copy the element at G.

24
00:02:15,560 --> 00:02:19,130
Plus, Gap should should crocodile them.

25
00:02:21,220 --> 00:02:29,710
I mean, this gap, this immense gap, then not the same already, I have used this in many programs,

26
00:02:29,710 --> 00:02:31,720
so I will call Chelse out here.

27
00:02:33,490 --> 00:02:37,650
By passing the elements and a number of elements, let us see the result.

28
00:02:40,350 --> 00:02:47,070
The result is three five seven nine, 10, 11, 12, 13, 16 and 24 years perfect, this is working

29
00:02:47,070 --> 00:02:47,730
perfectly.

30
00:02:50,590 --> 00:02:51,260
So that's it.

31
00:02:51,280 --> 00:02:59,410
So you can vote on this program and try it by yourself and also we have Radix Sword and Binstock that

32
00:02:59,410 --> 00:03:02,410
you are supposed to implement, that you write on the code for it.

33
00:03:04,160 --> 00:03:05,270
That's all in this video.

