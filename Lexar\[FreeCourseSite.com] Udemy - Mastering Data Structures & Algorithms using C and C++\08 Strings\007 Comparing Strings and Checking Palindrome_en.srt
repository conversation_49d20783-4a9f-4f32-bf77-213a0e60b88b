1
00:00:00,290 --> 00:00:06,180
We do we will learn how to compare to strengths as well as I'll explain to you what does it mean by

2
00:00:06,210 --> 00:00:11,940
palindrome and also explain you how to find whether a string is a palindrome or not.

3
00:00:13,230 --> 00:00:16,770
So first, let us look at how to compare to six.

4
00:00:17,460 --> 00:00:19,830
So, for example, I have taken two things here.

5
00:00:19,830 --> 00:00:22,900
Like one is a painter and another painting.

6
00:00:23,370 --> 00:00:28,800
So if you look at these are strings, B, a eye and the tail here they are equal.

7
00:00:29,070 --> 00:00:31,560
And here E and I, they are different.

8
00:00:32,280 --> 00:00:38,760
So therefore we need a method to know whether these are the same or different, whether two strings

9
00:00:38,760 --> 00:00:39,930
are same or different.

10
00:00:40,770 --> 00:00:42,440
So let us see how to compare them.

11
00:00:42,450 --> 00:00:44,400
Then I will show you what should be the result.

12
00:00:44,970 --> 00:00:48,420
Let us stop for comparing them.

13
00:00:48,420 --> 00:00:52,070
I should scan one letter at a time from both those strings.

14
00:00:52,080 --> 00:00:52,470
So I will.

15
00:00:52,740 --> 00:00:57,760
I hear Angie here now using this first i n g.

16
00:00:57,840 --> 00:00:59,060
I will check for Alphabet.

17
00:00:59,310 --> 00:00:59,640
Yes.

18
00:00:59,640 --> 00:01:01,620
In the same both are capital P.

19
00:01:01,830 --> 00:01:03,030
Yes, exactly the same.

20
00:01:03,390 --> 00:01:09,240
The next move I move G these are also same.

21
00:01:09,690 --> 00:01:13,280
So in this way I should move ahead if they are seen.

22
00:01:13,440 --> 00:01:13,830
Right.

23
00:01:13,870 --> 00:01:15,480
So these are same then.

24
00:01:15,480 --> 00:01:19,080
Compatible and they are seem to be, they are same.

25
00:01:19,350 --> 00:01:27,590
Then I moved here and she moved here then here if you see it this e but it is I so they are not matching

26
00:01:27,870 --> 00:01:29,670
so no need to continue further.

27
00:01:29,850 --> 00:01:36,420
If any one alphabet corresponding locations it is not matching then those things are not saying.

28
00:01:37,170 --> 00:01:41,000
Yes, the answer is they are not equal.

29
00:01:41,160 --> 00:01:47,660
OK, but we can also tell which string come forth and visionary which comes next.

30
00:01:47,970 --> 00:01:53,130
So the one comes first in the dictionary is a smaller string, one comes next is a greater string.

31
00:01:53,430 --> 00:02:00,930
So if you check this, which comes first Ekom first E comes from before I saw the ASCII code of E e

32
00:02:00,930 --> 00:02:02,780
less than a second of I.

33
00:02:03,030 --> 00:02:06,540
So we can say that this thing is smaller than this one.

34
00:02:06,870 --> 00:02:07,260
Right.

35
00:02:07,410 --> 00:02:10,070
So even this can be greater than this one.

36
00:02:10,080 --> 00:02:10,610
Also right.

37
00:02:10,620 --> 00:02:12,210
In our example, this is smaller.

38
00:02:12,570 --> 00:02:19,290
So here we can compare two strings and the result is that they are equal or the first one is the smaller

39
00:02:19,530 --> 00:02:20,970
or first one is greater.

40
00:02:21,450 --> 00:02:22,250
Not one more thing.

41
00:02:22,260 --> 00:02:25,920
C This was the example where the strings were not matching.

42
00:02:26,400 --> 00:02:30,420
If the match, then what happens if they are matching then what happens?

43
00:02:30,700 --> 00:02:37,920
Let me change the string and show you see instead of suspending this is a positive spin to this then

44
00:02:37,920 --> 00:02:39,810
definitely will have zero.

45
00:02:40,350 --> 00:02:40,730
Right.

46
00:02:41,010 --> 00:02:42,060
So let me continue.

47
00:02:42,390 --> 00:02:45,440
I hear, I hear both are equal.

48
00:02:45,450 --> 00:02:45,930
Yes.

49
00:02:45,930 --> 00:02:46,530
Continue.

50
00:02:48,060 --> 00:02:48,420
Right.

51
00:02:48,570 --> 00:02:49,460
Both are equal.

52
00:02:49,470 --> 00:02:49,920
Yes.

53
00:02:49,920 --> 00:02:50,490
Continue.

54
00:02:50,760 --> 00:02:53,280
I will increment and G will also increment.

55
00:02:53,700 --> 00:02:54,750
No, we should stop.

56
00:02:54,900 --> 00:02:55,200
Why.

57
00:02:55,200 --> 00:02:58,430
We should stop because we have reached zero.

58
00:02:58,440 --> 00:02:58,790
Right.

59
00:02:59,130 --> 00:02:59,550
We have it.

60
00:03:00,320 --> 00:03:01,860
We should stop over procedure.

61
00:03:02,100 --> 00:03:09,380
If we have reached zero and if both are at zero means they are matching because they are equal zero

62
00:03:09,630 --> 00:03:09,870
zero.

63
00:03:09,870 --> 00:03:10,640
Yes they are equal.

64
00:03:11,070 --> 00:03:15,540
Now earlier I was pointing here and she was pointing here.

65
00:03:15,660 --> 00:03:16,040
Right.

66
00:03:17,070 --> 00:03:18,780
So these are not equal.

67
00:03:18,870 --> 00:03:20,070
So we will stop here.

68
00:03:20,370 --> 00:03:21,910
And this is paintings.

69
00:03:23,340 --> 00:03:24,240
They are not equal.

70
00:03:24,240 --> 00:03:26,040
So we should stop and check.

71
00:03:26,800 --> 00:03:27,670
They are not equal.

72
00:03:27,790 --> 00:03:28,780
Yes, they are not equal.

73
00:03:28,910 --> 00:03:31,650
The first one is smaller than this one.

74
00:03:31,660 --> 00:03:37,150
So you should say first one is smaller if suppose you have a bigger alphabet here than the first one

75
00:03:37,150 --> 00:03:39,140
is greater than four.

76
00:03:39,520 --> 00:03:42,000
So the procedure will go on comparing the alphabet.

77
00:03:42,040 --> 00:03:43,330
One by one and one.

78
00:03:43,330 --> 00:03:45,780
It will stop the situation.

79
00:03:46,120 --> 00:03:51,250
If a mismatch is found, it will stop or any one of the string is ending.

80
00:03:51,250 --> 00:03:52,050
It will stop.

81
00:03:52,390 --> 00:03:53,860
So the procedure is very simple.

82
00:03:54,070 --> 00:03:56,050
I will quickly write on the procedure here.

83
00:03:56,230 --> 00:04:03,700
I need as well as a G then using a for loop, I can start I also from zero and also from zero because

84
00:04:03,700 --> 00:04:10,240
what we are starting from here and how long I should continue unless any one of the string is ending

85
00:04:10,360 --> 00:04:12,480
any one of those things I should stop.

86
00:04:12,490 --> 00:04:18,060
So here I should say that E of I is not equal to zero.

87
00:04:18,070 --> 00:04:26,460
That is null character and B of a G if not equal to null character, no space.

88
00:04:26,470 --> 00:04:27,580
So here I will continue.

89
00:04:27,940 --> 00:04:37,360
After that I should increment I as well as increment g increment both I and G that should not inside

90
00:04:37,360 --> 00:04:37,710
the loop.

91
00:04:37,720 --> 00:04:38,470
What I should do.

92
00:04:38,770 --> 00:04:41,980
I should check if they are matching or not.

93
00:04:42,070 --> 00:04:42,880
If they are matching.

94
00:04:42,880 --> 00:04:43,870
I don't have to do anything.

95
00:04:44,200 --> 00:04:47,040
If they are not matching then I should stop the procedure.

96
00:04:47,350 --> 00:04:58,570
So this loop if, if I if not equal to be of G then simply stopped from this for loop and say break.

97
00:05:00,700 --> 00:05:01,570
Come out on this.

98
00:05:03,080 --> 00:05:10,060
That's it then after coming out of the loop, I should see that whether they are equal wherever and

99
00:05:10,070 --> 00:05:15,910
you are pointing out for fun, the greater our first one is a smaller business that I should display

100
00:05:15,920 --> 00:05:16,480
message.

101
00:05:16,760 --> 00:05:19,040
So here I will write on the code for that one.

102
00:05:19,340 --> 00:05:36,860
See here, if if I is equal to B of G print equal else if E of eye is less than B of G then say that

103
00:05:36,980 --> 00:05:40,370
first string as a smaller it comes first in the dictionary.

104
00:05:40,550 --> 00:05:42,110
So Alphie is a smaller.

105
00:05:42,120 --> 00:05:47,750
So I will simply say smaller because we are comparing the first one with the second one.

106
00:05:47,750 --> 00:05:52,710
So I will simply say someone else, I will say greater.

107
00:05:53,090 --> 00:05:57,020
So it means the first string is greater for the procedure.

108
00:05:58,580 --> 00:06:03,530
Just using your for loop, see, almost all the procedures on string are just using a follow up with

109
00:06:03,530 --> 00:06:06,050
any follow up, we have scanned the entire string.

110
00:06:06,350 --> 00:06:08,170
That depends on the length of the string.

111
00:06:08,790 --> 00:06:09,070
Right.

112
00:06:09,410 --> 00:06:15,500
And while scanning through either we are counting or we are displaying all, we are changing the cases

113
00:06:15,500 --> 00:06:18,500
or we are comparing all these things we can do for that.

114
00:06:18,500 --> 00:06:20,250
We have to scan for string once.

115
00:06:20,450 --> 00:06:22,350
So this is the program now.

116
00:06:22,370 --> 00:06:23,600
I have to show you one more time.

117
00:06:24,240 --> 00:06:31,550
See, what if this is a small letter B and this is capital letter B?

118
00:06:33,160 --> 00:06:40,180
If I come back, I and G then the ASCII code for this, a small letter and capital letter are different

119
00:06:40,540 --> 00:06:45,730
as see code for small letters are higher than this is your choice.

120
00:06:46,280 --> 00:06:49,060
Do you want to say that the P is just be?

121
00:06:49,060 --> 00:06:49,680
Yes.

122
00:06:49,720 --> 00:06:55,130
So it means independent of the cases you want to check so you can check it the how to do that.

123
00:06:55,360 --> 00:06:58,260
First of all, you should know the case of alphabet.

124
00:06:58,270 --> 00:07:00,070
It's a lowercase a. uppercase.

125
00:07:00,080 --> 00:07:00,330
Right.

126
00:07:00,580 --> 00:07:07,120
So you change them into any one on the case, both in lowercase or both in uppercase, then compare

127
00:07:07,120 --> 00:07:07,360
them.

128
00:07:08,320 --> 00:07:14,200
This is what you can do if you want to check them by ignoring their cases.

129
00:07:15,010 --> 00:07:15,310
Right.

130
00:07:15,760 --> 00:07:21,460
Otherwise, the procedure, what we wrote, it will stop here only it will not check this character

131
00:07:21,640 --> 00:07:30,100
because this is greater than this one, because capital P comes first in ASCII quotes and small letter

132
00:07:30,100 --> 00:07:31,180
B comes next.

133
00:07:31,270 --> 00:07:34,990
So this is thing will be smaller and that will be greater.

134
00:07:35,230 --> 00:07:37,960
Right, depending on the cases of alphabets.

135
00:07:38,740 --> 00:07:41,190
So then all those things are copied now.

136
00:07:41,230 --> 00:07:47,110
Next, I will show you what is palindrome and how to check whether a string is a palindrome or not.

137
00:07:47,770 --> 00:07:53,080
Let us know what is a palindrome or string is said to be a palindrome.

138
00:07:53,230 --> 00:07:57,310
If you reverse the string, it remains same.

139
00:07:58,510 --> 00:08:01,270
For example, I have a string here that is madam.

140
00:08:02,290 --> 00:08:10,510
If I reverse it so first right and then A, then D, then E, then M.

141
00:08:11,080 --> 00:08:14,590
So I have taken a DC madam.

142
00:08:14,890 --> 00:08:16,600
So again, it has formed the same thing.

143
00:08:16,850 --> 00:08:23,080
If you reverse a string and if it remains the same, that it is a palindrome, I will show you a few

144
00:08:23,080 --> 00:08:33,039
more strings which are a palindrome like little if you reverse it, it is little only or anything if

145
00:08:33,039 --> 00:08:36,370
you reverse it is a palindrome.

146
00:08:36,370 --> 00:08:38,020
So it's the same thing written only.

147
00:08:38,710 --> 00:08:42,000
This is as if you reverse it.

148
00:08:42,010 --> 00:08:42,669
It's as.

149
00:08:43,270 --> 00:08:45,220
So these are all palindromes.

150
00:08:46,180 --> 00:08:51,490
So our problem is, if a sting is given, then we have to find out whether it's a palindrome or not.

151
00:08:52,620 --> 00:08:53,990
So what is the procedure?

152
00:08:54,520 --> 00:08:59,920
The procedure is simple now we already know a few things sort of procedure become simple for us.

153
00:09:00,240 --> 00:09:02,690
First of all, you reverse a copy of string.

154
00:09:03,000 --> 00:09:08,820
So if you have a string in another, that reverse copied in another area, we have already seen it.

155
00:09:08,820 --> 00:09:11,090
In previous video, Huguley words copy a string.

156
00:09:11,850 --> 00:09:19,440
Then after he was copying a string, you have to compare two strings might compare to strings till you

157
00:09:19,470 --> 00:09:20,280
reach zero.

158
00:09:20,280 --> 00:09:24,150
They will be similarly only because it's the reverse of the same string and it is a palindrome.

159
00:09:24,630 --> 00:09:27,510
So if they are equal then it is a palindrome.

160
00:09:27,750 --> 00:09:33,300
So if you have already seen how to compare two strings and how to know whether they are equal or first

161
00:09:33,300 --> 00:09:34,550
one is getting ever smaller.

162
00:09:34,800 --> 00:09:38,350
So we don't want gradable smaller, we want whether they are equal or not.

163
00:09:38,610 --> 00:09:41,630
So if they are equal, then it's a palindrome.

164
00:09:42,150 --> 00:09:49,240
So you take it at it and taken that a B in the B, you copied these elements from it in three words.

165
00:09:49,290 --> 00:09:50,220
He was copied.

166
00:09:50,610 --> 00:09:56,700
Then compare them so reverse and compare in reverse.

167
00:09:56,700 --> 00:10:00,660
The first step and second is compare No.

168
00:10:00,870 --> 00:10:01,660
One more thing.

169
00:10:01,670 --> 00:10:05,390
So this one method I have shown, you know, is there any other possible.

170
00:10:05,400 --> 00:10:05,910
Yes.

171
00:10:05,910 --> 00:10:08,570
How you can compare the strings.

172
00:10:08,580 --> 00:10:09,290
Same strings.

173
00:10:09,300 --> 00:10:11,850
You don't need another very same string.

174
00:10:11,860 --> 00:10:13,230
Start from here and here.

175
00:10:13,440 --> 00:10:18,670
Compare they are equal so increment and decrement they are equal in agreement and the agreement is the

176
00:10:18,670 --> 00:10:19,350
same only.

177
00:10:19,350 --> 00:10:26,420
So stop if any, where it doesn't match then say that it is not a palindrome.

178
00:10:27,180 --> 00:10:32,280
So without taking another array you can compare the same string from the last.

179
00:10:32,460 --> 00:10:32,840
Right.

180
00:10:33,120 --> 00:10:35,670
So it was just like reversing, reversing.

181
00:10:35,670 --> 00:10:36,330
We have done it.

182
00:10:36,330 --> 00:10:41,250
We have instead of swiping, you have to check that the alphabets are similar or not.

183
00:10:42,000 --> 00:10:48,050
So with the second method, without any extra three, you can find out whether some palindrome or not.

184
00:10:48,960 --> 00:10:54,240
So here even I'm not writing the code how to do that because you have already seen these two things.

185
00:10:54,660 --> 00:10:59,130
So you write down by taking extra three or without taking extra money.

186
00:10:59,430 --> 00:11:02,490
But the programs you write on the student challenge.

