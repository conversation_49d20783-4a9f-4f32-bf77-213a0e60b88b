1
00:00:00,180 --> 00:00:07,140
And this video, I will show you how great a single man driven program for all the operations upon.

2
00:00:07,140 --> 00:00:13,620
And so we have seen various operations up on an array, and I'm using the same project, seeing this

3
00:00:13,620 --> 00:00:14,070
project.

4
00:00:14,070 --> 00:00:15,860
Initially, we have defined an early.

5
00:00:18,260 --> 00:00:26,180
Having the members disappointed for a UTI and the size and the length of another see, I have I have

6
00:00:26,180 --> 00:00:30,870
changed it to a fixed size area and I have shown you all the functions now back again to appointers

7
00:00:31,040 --> 00:00:34,000
so that we can have an array of required size.

8
00:00:34,610 --> 00:00:38,360
Then I have all these functions to display, append, insert or delete.

9
00:00:38,780 --> 00:00:44,990
Sublinear linear binary search and recovery, binary search getset maximon.

10
00:00:44,990 --> 00:00:49,250
Some average rewards and recursive rewards.

11
00:00:52,190 --> 00:00:54,680
Second method for reversing an.

12
00:00:56,060 --> 00:01:02,030
Inserting an assorted other, then checking with a religious order or not rearranging the elements much,

13
00:01:02,870 --> 00:01:07,760
then union intersection difference, all these functions we have seen.

14
00:01:09,290 --> 00:01:14,510
So in any application, if you required an error, you can use that one, but here I will show you a

15
00:01:14,510 --> 00:01:18,260
million different program where we can use all those functions.

16
00:01:20,720 --> 00:01:22,760
So let us write a new driven program.

17
00:01:25,330 --> 00:01:28,570
First, I will declare an era out of one.

18
00:01:30,520 --> 00:01:34,900
If any other variables are required, I will declare them so first of all, I should know what is the

19
00:01:34,900 --> 00:01:41,380
size of an one, because there is a pointer and I should dynamically create an array and assign it to

20
00:01:41,380 --> 00:01:42,120
that pointer.

21
00:01:42,790 --> 00:01:46,030
So I will take it from keyboard asking what is the size?

22
00:01:47,050 --> 00:01:51,700
So we have already seen how to take the input from the keyboard and create an array of required size

23
00:01:51,700 --> 00:01:53,580
and also filled elements.

24
00:01:54,370 --> 00:01:58,600
So I will finish the code for creating an array of required size.

25
00:01:59,900 --> 00:02:07,370
Are here initially asking the side of an and taking the size of another and then creating a body inside

26
00:02:07,370 --> 00:02:10,370
he and assigning it to appointer A.

27
00:02:11,560 --> 00:02:16,420
Now, here, I will write the code for Menal, let me finish the code and I will explain.

28
00:02:18,150 --> 00:02:23,960
Here I have prepared the menu with a few options like incertitude, search and finding some and then

29
00:02:23,970 --> 00:02:29,790
displaying and adding you can add all other options like reversing an area, checking if we decided

30
00:02:29,790 --> 00:02:35,060
or not, or from a union intersection or merging all those operations we can provide.

31
00:02:35,430 --> 00:02:38,700
I'm just giving the idea how you can prepare a menu driven program.

32
00:02:38,700 --> 00:02:40,230
So few options are there.

33
00:02:42,390 --> 00:02:44,830
Then here I am asking under your choice.

34
00:02:44,850 --> 00:02:49,260
So let us see, it is a variable here and that choice is taken.

35
00:02:49,590 --> 00:02:54,880
Then using such case, I can follow the corresponding function for each options.

36
00:02:54,900 --> 00:02:57,450
The options are having numbered from one to five.

37
00:02:57,750 --> 00:02:59,190
So what are the options given?

38
00:02:59,640 --> 00:03:01,530
I will call corresponding function.

39
00:03:02,940 --> 00:03:04,170
Here tonight, gears.

40
00:03:08,050 --> 00:03:09,160
Upon C.H..

41
00:03:11,110 --> 00:03:13,390
The case won first case.

42
00:03:14,530 --> 00:03:19,420
On the case, one I should call insert functions, so for that I should know the value that I want to

43
00:03:19,420 --> 00:03:22,080
insert and index at which I want to insert.

44
00:03:22,600 --> 00:03:28,330
So I will take those things from keyboard, enter an element and index.

45
00:03:30,920 --> 00:03:35,520
Then scan, if I should read to values the value to be inserted.

46
00:03:35,960 --> 00:03:36,870
Let us take it in.

47
00:03:38,220 --> 00:03:40,830
The value to be incertain and.

48
00:03:41,780 --> 00:03:48,950
Index, so except for the value and index, as for location where we want to insert so and these two

49
00:03:48,950 --> 00:03:50,430
variables are already declared here.

50
00:03:50,480 --> 00:03:51,920
I have those two variables.

51
00:03:54,820 --> 00:04:01,270
When I have these two things, then I should call function insert bypassing address of an array one

52
00:04:01,870 --> 00:04:04,210
because it is called bedrest then.

53
00:04:05,150 --> 00:04:11,330
Index at what I want to insert an element and also x NetSol break.

54
00:04:12,370 --> 00:04:19,149
Likewise, I will fill the rest of the cases, delete, search and display, I will finish the rest

55
00:04:19,149 --> 00:04:24,260
of the cases, then we will continue with our discussion switch cases already here.

56
00:04:24,910 --> 00:04:27,760
This is the first case for aking inserting an element.

57
00:04:28,000 --> 00:04:31,230
I'll explain in the second case for deletion.

58
00:04:31,240 --> 00:04:32,430
It's asking an index.

59
00:04:32,440 --> 00:04:36,070
Then it will delete an element and it will print the deleted element.

60
00:04:36,910 --> 00:04:43,330
And the third case we want to search, such as a team, what we want to search then called linear search

61
00:04:43,330 --> 00:04:46,000
function by passing key, and it will hit on the index.

62
00:04:46,390 --> 00:04:48,790
And the index of that element is displayed here.

63
00:04:50,850 --> 00:04:55,470
Then fourth option is for displaying the sum of all elements of Fornari, so I'm pulling some function

64
00:04:55,470 --> 00:04:57,300
and directly displaying the result here.

65
00:04:58,330 --> 00:05:00,220
And fifth option is display.

66
00:05:01,830 --> 00:05:05,220
So therefore, the second option is for exit, so.

67
00:05:06,260 --> 00:05:12,970
But I will ride on this and thing inside a loop, so I will use to do while loop so that this maneuver

68
00:05:12,980 --> 00:05:14,240
repeatedly appears.

69
00:05:17,040 --> 00:05:19,110
And this should be good line.

70
00:05:20,070 --> 00:05:21,000
Choice is.

71
00:05:22,520 --> 00:05:26,180
Less than six, if choice is greater than six, it should stop.

72
00:05:28,980 --> 00:05:36,150
So I have options from one to five so far, six at is exit, so six are greater than six.

73
00:05:36,180 --> 00:05:37,320
The program should stop.

74
00:05:39,100 --> 00:05:40,420
Let us run the program.

75
00:05:42,140 --> 00:05:46,170
The Have Faucette is asking for an arrest, so I'll give the other side a stand.

76
00:05:47,390 --> 00:05:48,490
Now, there is nothing in that.

77
00:05:48,620 --> 00:05:50,370
First of all, I should insert few values.

78
00:05:50,370 --> 00:05:56,900
So first option I will take, that is insert the value that I want to insert this to an index should

79
00:05:56,900 --> 00:05:57,380
be zero.

80
00:05:59,100 --> 00:06:00,560
I went inside one more value.

81
00:06:04,020 --> 00:06:07,800
Why do I want to insert the three and the indexer should be one?

82
00:06:10,550 --> 00:06:15,350
Let us display I should get it to list, that is two and three, yes, two and three are dead.

83
00:06:15,350 --> 00:06:18,170
And then that I want to insert one more value.

84
00:06:20,900 --> 00:06:24,590
Element is 15 and indexers zero.

85
00:06:25,680 --> 00:06:26,550
Let us display.

86
00:06:27,480 --> 00:06:35,520
Yes, 15 is inserted at index zero, and instead of one more element, that is 10 at index three.

87
00:06:37,200 --> 00:06:38,470
Let disparate elements.

88
00:06:38,490 --> 00:06:45,030
Yes, I got it then I want to delete an element I've taken index zero.

89
00:06:46,280 --> 00:06:47,410
Let us just despedida.

90
00:06:48,140 --> 00:06:51,890
Yes, value 15, that was in the beginning and accident, that is deleted.

91
00:06:54,880 --> 00:06:59,170
Let us call some of all these elements I should get 15, yes, it is perfect.

92
00:07:00,160 --> 00:07:04,780
So in this way, you can try out all the options and you can go on inserting and deleting the elements

93
00:07:05,200 --> 00:07:08,800
and you can use menu options and test whether your program is perfect.

94
00:07:08,800 --> 00:07:11,410
All the functions are working in coordination or not.

95
00:07:13,200 --> 00:07:21,390
We have seen individual functions, but this one program will help you to verify whether or not to confirm

96
00:07:21,390 --> 00:07:23,870
all the functions are coordinating properly or not.

97
00:07:26,960 --> 00:07:28,640
That's all with this management program.

