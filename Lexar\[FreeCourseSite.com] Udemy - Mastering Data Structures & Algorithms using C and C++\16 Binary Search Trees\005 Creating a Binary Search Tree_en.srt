1
00:00:00,360 --> 00:00:06,300
Let us see how to create a binary search tree from one set of keys, so the procedure is very simple

2
00:00:06,300 --> 00:00:07,030
and easy.

3
00:00:07,500 --> 00:00:09,540
So let us generate a tree.

4
00:00:10,020 --> 00:00:15,580
So here we have to insert all these keys one by one in the same order in which they are given.

5
00:00:16,260 --> 00:00:17,190
So let us start.

6
00:00:19,460 --> 00:00:21,180
First, he is nine.

7
00:00:21,320 --> 00:00:26,380
I don't have any news right now, so create the first note and make room point here.

8
00:00:28,460 --> 00:00:35,630
So this was the first not the next 15 start from Route 15 is greater than nine, so it should come on

9
00:00:35,630 --> 00:00:36,700
the right hand side.

10
00:00:37,100 --> 00:00:38,900
Yes, inserted on the right side.

11
00:00:39,940 --> 00:00:41,710
Then fine, start from <PERSON>.

12
00:00:44,260 --> 00:00:51,310
Five is a smaller than nine, so it should come on the left hand side is the pointy start from Route

13
00:00:51,330 --> 00:00:56,040
<PERSON><PERSON>sky greater than nine go tonight, predesignated and 15.

14
00:00:56,050 --> 00:00:58,240
So go to write and insert 20 here.

15
00:00:59,780 --> 00:01:04,440
Then 16, 16, start from Route 16 is greater than nine.

16
00:01:04,970 --> 00:01:08,180
Yes, so go say 16 is greater than 15.

17
00:01:08,180 --> 00:01:11,560
Also years ago to write 16 is less than 20.

18
00:01:11,570 --> 00:01:13,610
So inserted on the left hand side.

19
00:01:16,210 --> 00:01:22,330
That eight start from Route nine, that eight is a smaller four come to the left side, let's say eight

20
00:01:22,330 --> 00:01:24,830
five five eight is greater than five.

21
00:01:24,850 --> 00:01:30,820
So inserted as the right shape, the next is to start from route to get to the nine.

22
00:01:32,060 --> 00:01:40,520
Go to Rightside, 15 supporters, less than 15, so inserted as a left child, then three start from

23
00:01:40,540 --> 00:01:45,770
Route three of the nine, go to left is less than five left and inserted here.

24
00:01:46,830 --> 00:01:53,250
Then six six is less than nine, left is greater than six, Goda, right, less than eight inserted

25
00:01:53,250 --> 00:01:54,310
on the left.

26
00:01:54,330 --> 00:01:55,200
So that's it.

27
00:01:55,230 --> 00:01:58,740
There's also some generating up binary search tree.

28
00:01:59,280 --> 00:02:06,080
What is the time taken for creating a binary search tree see to have inserted and elements.

29
00:02:06,240 --> 00:02:06,610
Right.

30
00:02:06,780 --> 00:02:09,240
We have inserted and elements.

31
00:02:10,410 --> 00:02:17,730
Then each element we were searching and finding explanation and inserting it each element, so searching

32
00:02:17,730 --> 00:02:21,860
takes how much more time searching takes longer in time.

33
00:02:22,080 --> 00:02:26,950
We are assuming that the height of Autry's log-in, so search takes longer time.

34
00:02:27,000 --> 00:02:34,050
So searching we have done for every element so many times we have searched then creating a node and

35
00:02:34,050 --> 00:02:36,030
linking that will take constant time.

36
00:02:36,390 --> 00:02:40,380
Searching takes a long time so that login is done for any time.

37
00:02:40,380 --> 00:02:43,390
So the time is outdraw and log in.

38
00:02:43,560 --> 00:02:46,650
That's all about creating a binary search tree.

