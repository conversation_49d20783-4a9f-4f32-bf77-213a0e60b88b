1
00:00:00,310 --> 00:00:07,260
Now, next topic is symmetric metrics, so here I have taken an example of symmetric metrics of order,

2
00:00:07,380 --> 00:00:12,480
five cross five, let us observe the property of this metrics.

3
00:00:14,130 --> 00:00:16,710
See, these are the numbers and these are column numbers.

4
00:00:17,970 --> 00:00:28,740
The element at one commentary is two and three comma, one is also to eliminate that three comma four

5
00:00:28,890 --> 00:00:29,760
as a four.

6
00:00:31,310 --> 00:00:41,000
Unfortunately is also food, so if you observe the elements at location, IJI is equal to an element

7
00:00:41,010 --> 00:00:41,670
at location.

8
00:00:42,840 --> 00:00:55,740
So if matics of IGY is equal to Matt<PERSON> of <PERSON><PERSON><PERSON><PERSON>, then it's a similar matics.

9
00:00:57,180 --> 00:01:02,190
This is the property of Semitic matics, the definition of Semitic mappings.

10
00:01:02,850 --> 00:01:06,210
Do we have to store all the elements of a symmetric matrix?

11
00:01:06,330 --> 00:01:06,810
No.

12
00:01:07,260 --> 00:01:12,160
If I store eigenvalue value, then I can retrieve Geet also.

13
00:01:12,630 --> 00:01:16,970
So if I know any element that I command, then also I know Element Digicom.

14
00:01:16,980 --> 00:01:23,150
I so it means if I know this element for one, then also I know one four.

15
00:01:23,580 --> 00:01:31,590
So it means it's sufficient to store either elements in the lower triangle or the elements in the upper

16
00:01:31,590 --> 00:01:32,190
triangle.

17
00:01:34,160 --> 00:01:38,900
Those elements would be sufficient, so if I know these elements, then automatically I can retrieve

18
00:01:38,900 --> 00:01:40,040
those elements also.

19
00:01:40,370 --> 00:01:43,130
So it means for storing semantic matics.

20
00:01:43,130 --> 00:01:45,260
I don't have to take a two dimensional urte.

21
00:01:45,290 --> 00:01:47,840
I can take a single dimensional and stored it.

22
00:01:48,330 --> 00:01:50,360
Then how we can represent the somatics.

23
00:01:50,660 --> 00:01:59,530
We can represent a matrix either just by taking a lower triangular park or I can store only a particular

24
00:01:59,540 --> 00:02:02,900
watch so symmetric markets can be represented as.

25
00:02:04,090 --> 00:02:10,990
Either lower triangle or upper triangular, any of these two can be followed.

26
00:02:11,470 --> 00:02:17,110
And when we are looking for any element at ECOMOG, we can also retrieve an element of mine.

27
00:02:17,710 --> 00:02:23,200
Already we have discussed how a lower triangle of matics can be represented and how a petroglyph markets

28
00:02:23,200 --> 00:02:24,130
can be represented.

29
00:02:24,400 --> 00:02:28,180
We can follow any of these methods so we don't have to discuss further on this one.

30
00:02:28,570 --> 00:02:32,740
When we try to program that time will explain how we can utilize these.

