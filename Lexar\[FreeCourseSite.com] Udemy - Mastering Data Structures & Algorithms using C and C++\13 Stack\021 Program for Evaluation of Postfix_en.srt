1
00:00:00,770 --> 00:00:04,340
Let us write a function for evaluation of postfix expression.

2
00:00:05,670 --> 00:00:11,520
And the bottom it is taking is character type pointer, so which can access a character type body so

3
00:00:11,520 --> 00:00:13,170
that it can access a string.

4
00:00:13,620 --> 00:00:14,930
So the point of name is specific.

5
00:00:14,950 --> 00:00:17,010
So let us assume that it is accessing this.

6
00:00:17,320 --> 00:00:22,610
So as an example, I have taken on that, then we need a strike inside a function.

7
00:00:22,620 --> 00:00:27,970
So I have taken this tack here and this stock has to be initialized in the previous programs.

8
00:00:27,990 --> 00:00:30,220
Also, we have seen we have to do that.

9
00:00:30,540 --> 00:00:37,350
That is said the size create an idea of some size and initialized top point or two minus one, those

10
00:00:37,350 --> 00:00:37,980
three steps.

11
00:00:38,010 --> 00:00:38,850
If you have to label.

12
00:00:39,880 --> 00:00:45,340
The next what I have to do, I have to scan through this entire expression by taking one fumble at a

13
00:00:45,340 --> 00:00:48,720
time, so this is scanning, I can do it using for loop.

14
00:00:48,730 --> 00:00:51,940
Also, let us scan the expression here.

15
00:00:51,940 --> 00:00:54,370
This for Loop will help us to scan the supports.

16
00:00:54,380 --> 00:00:57,310
This expression starting from zero and every time.

17
00:00:57,310 --> 00:00:58,900
Moving ahead next, next, next.

18
00:00:59,740 --> 00:01:02,300
And it will stop when it has reached zero.

19
00:01:02,440 --> 00:01:05,300
So when it is not equal to zero, it will continue.

20
00:01:05,300 --> 00:01:07,360
When it is equal to zero, it will stop.

21
00:01:07,510 --> 00:01:12,540
Then inside this I have to write down my procedure, but I need some variables.

22
00:01:12,540 --> 00:01:14,050
So I will declare those variables.

23
00:01:14,470 --> 00:01:23,180
Like here I have used variable I so I need I and also I will take x1 x2 and are for result.

24
00:01:23,500 --> 00:01:29,170
So I have to control variables that I'll be using them in this follow up before I write here, before

25
00:01:29,170 --> 00:01:32,620
I continue writing, I have to expand one important thing here.

26
00:01:33,070 --> 00:01:37,210
See postfix expression is in the form of string.

27
00:01:37,210 --> 00:01:39,010
String is nothing but a set of characters.

28
00:01:39,010 --> 00:01:41,160
So each of these symbols are character.

29
00:01:41,410 --> 00:01:43,780
So three is not an integer value.

30
00:01:43,780 --> 00:01:49,410
Three, it is having its ASCII code and ASCII called for three is a fifty one.

31
00:01:49,420 --> 00:01:51,340
So actually it is fifty one numeric.

32
00:01:51,340 --> 00:01:51,970
Fifty one.

33
00:01:51,970 --> 00:01:53,500
It's not three.

34
00:01:53,500 --> 00:01:55,930
Number three, it's a character three.

35
00:01:56,260 --> 00:01:59,080
Anyway this is just a simulation function.

36
00:01:59,440 --> 00:02:05,520
So here I cannot take two digit number, I'm taking just a single digit numbers.

37
00:02:06,370 --> 00:02:10,930
If there are two digits then I can know which two digit belongs to which number.

38
00:02:10,940 --> 00:02:12,030
So it's the confusion.

39
00:02:12,310 --> 00:02:14,230
So this is for just one single digit.

40
00:02:14,230 --> 00:02:15,550
And those are characters.

41
00:02:16,000 --> 00:02:22,600
Knoller's percent of what I have to do when scanning said this for Loop will take me from zero to nine.

42
00:02:22,600 --> 00:02:22,870
Right.

43
00:02:22,900 --> 00:02:23,710
It will stop here.

44
00:02:23,980 --> 00:02:29,610
So for every symbol, what we are supposed to do, check if it is an operator or operator.

45
00:02:29,620 --> 00:02:35,800
So if you remember in the previous example program, that is since we wrote a function called Is the

46
00:02:35,800 --> 00:02:38,230
Operation That same function, I will use it.

47
00:02:38,230 --> 00:02:41,170
Here is Operand Postfix of Ofri.

48
00:02:41,590 --> 00:02:45,520
So if it is an option, the postfix symbol, is it an option?

49
00:02:45,820 --> 00:02:50,350
If it is an option, then we have to push it to the stack so it will get pushed into this stack.

50
00:02:50,830 --> 00:02:55,420
So postfix symbol is pushed into the stack otherwise.

51
00:02:56,080 --> 00:03:01,260
Otherwise it's an operator and I have to two symbols and perform the operation.

52
00:03:01,810 --> 00:03:04,960
So in the end spot, first of all, I will pop out symbols.

53
00:03:05,320 --> 00:03:06,550
So here I am popping out.

54
00:03:06,550 --> 00:03:12,460
Symbol for the symbol that is popped out is made a second option and the second symbol that is popped

55
00:03:12,460 --> 00:03:17,200
out is taken as first option, not depending on the operator.

56
00:03:17,200 --> 00:03:18,610
I should perform the operation.

57
00:03:18,610 --> 00:03:21,010
If it is multiplication, I should perform multiplication.

58
00:03:21,010 --> 00:03:23,020
If it is a division, I should perform division.

59
00:03:23,380 --> 00:03:25,780
So depending on that, I have to perform the operation.

60
00:03:25,780 --> 00:03:28,780
So for that I can write on such a case here.

61
00:03:29,870 --> 00:03:38,550
So here is a case based on the symbol of postfix expression in case if it is a plus, then perform additions.

62
00:03:38,580 --> 00:03:39,740
So I will write on here.

63
00:03:40,700 --> 00:03:48,920
So here the statement, this case plus this plus then perform X plus X two and get the dinner and push

64
00:03:48,920 --> 00:03:54,250
that ah into the stack and then break, stop and the case.

65
00:03:54,710 --> 00:04:01,310
This is the only addition then say maybe I should write it for subtraction, multiplication and division.

66
00:04:01,610 --> 00:04:02,640
I will write down for you.

67
00:04:03,230 --> 00:04:09,800
So here I have written the cases for addition, subtraction, multiplication and division, evaluate

68
00:04:09,800 --> 00:04:17,000
the expression and perform the operation on operands and get the result and push into the stack and

69
00:04:17,300 --> 00:04:18,339
break that case.

70
00:04:18,350 --> 00:04:21,380
So all these are the same statements in all the cases.

71
00:04:22,010 --> 00:04:27,520
So this each case will perform the operation and again push the result back into the stack.

72
00:04:27,860 --> 00:04:33,040
So if this procedure continues for a loop at the end of follow, the result will be in the stack.

73
00:04:33,320 --> 00:04:35,870
So we should return the results from the stack.

74
00:04:35,960 --> 00:04:41,180
So at the last last statement, we should a symbol and return it from the stack.

75
00:04:42,140 --> 00:04:43,200
So I don't like it here.

76
00:04:43,820 --> 00:04:50,230
So the last statement of the follow up should be written both from the stack and also written.

77
00:04:50,420 --> 00:04:51,770
So this is the end of a function.

78
00:04:52,170 --> 00:04:55,730
There was no space, so I wrote it there so that the last statement.

79
00:04:56,240 --> 00:04:56,420
No.

80
00:04:56,420 --> 00:04:58,960
One last interesting thing that we have to discuss here.

81
00:04:59,690 --> 00:05:02,270
What is the type of postfix expression?

82
00:05:03,020 --> 00:05:03,550
Stream.

83
00:05:03,800 --> 00:05:05,190
So those are all characters.

84
00:05:05,870 --> 00:05:12,620
What is the type of the stock I should take integer type or character type, integer type, because

85
00:05:12,620 --> 00:05:17,600
when they pop out the values from the stack property values and performing the arithmetic operation.

86
00:05:17,630 --> 00:05:20,200
So this is on integer values and performing.

87
00:05:20,540 --> 00:05:22,010
So the stack must be integer.

88
00:05:22,790 --> 00:05:25,390
So I'll take a stack of indigenous stock.

89
00:05:26,050 --> 00:05:26,470
OK.

90
00:05:27,580 --> 00:05:33,430
If you see here, if it is an operation, we are pushing a postfix symbol into the stack, pushing a

91
00:05:33,850 --> 00:05:35,140
symbol into the stack, right.

92
00:05:35,140 --> 00:05:38,570
If it is an option, let us take a symbol and try to push it.

93
00:05:38,590 --> 00:05:43,810
See, this is open and it is three, so three will be pushed into the stack.

94
00:05:43,840 --> 00:05:45,370
So that is character three.

95
00:05:45,640 --> 00:05:48,220
But this is a stack of type integer.

96
00:05:48,430 --> 00:05:49,660
So what will be pushed in?

97
00:05:49,870 --> 00:05:53,890
As I said, the ASCII code of three fifty one for fifty one will be pushed here.

98
00:05:55,260 --> 00:05:58,870
Wrong, so we cannot expect good results.

99
00:05:59,100 --> 00:06:01,680
So you mean four, five of what will be pushed in?

100
00:06:01,890 --> 00:06:08,970
This will be 53, 51 and 53 are pushed to the Iraqi courts will be pushed in because those are character.

101
00:06:08,970 --> 00:06:12,810
There's no numeric values, 51 and 53 that is pushed here.

102
00:06:12,930 --> 00:06:19,770
It means when this multiplication is performing poorly, perform on 53 and 51, yes, 53 and 51 will

103
00:06:19,770 --> 00:06:21,290
be multiplied wrong.

104
00:06:21,300 --> 00:06:22,650
It will give us a wrong result.

105
00:06:23,740 --> 00:06:30,430
Then what I should do, I should convert that character three into integer three, how to convert.

106
00:06:30,940 --> 00:06:31,600
Check this.

107
00:06:32,810 --> 00:06:34,100
This is character three.

108
00:06:35,210 --> 00:06:44,420
That is 51 subtractive character zero zero forty eight, subtract it, we get the result three, so

109
00:06:44,600 --> 00:06:49,840
subtract the character zero from a number, then we get the integer value of that one.

110
00:06:50,150 --> 00:06:54,890
So we should get three here and five here for that.

111
00:06:54,890 --> 00:07:00,980
Subtract ASCII code of zero from each numerical symbol or operate from there.

112
00:07:01,760 --> 00:07:08,840
Typecasting will not help by casting off a tree will give us fifty one only it will not be three so

113
00:07:09,020 --> 00:07:11,240
that I'm pushing the other into the stack.

114
00:07:11,240 --> 00:07:13,340
I should not push fifty one for three.

115
00:07:13,730 --> 00:07:16,070
I should push number three integer three.

116
00:07:16,250 --> 00:07:20,930
So when push here from the postfix symbol I will subtract.

117
00:07:22,070 --> 00:07:25,580
ASCII code of zero, so this is the interesting thing here.

118
00:07:27,870 --> 00:07:30,660
So subtract that those characters will become.

119
00:07:31,950 --> 00:07:37,680
So that's all this was the simulation function of evaluation of Postfix.

120
00:07:38,160 --> 00:07:41,010
See the infix proposed reconversion is used by the compiler.

121
00:07:41,020 --> 00:07:44,730
So when you really if you're developing a compiler, then you can do it in real.

122
00:07:44,760 --> 00:07:49,430
But here we are just looking at how compiler work, how compiler evaluate.

