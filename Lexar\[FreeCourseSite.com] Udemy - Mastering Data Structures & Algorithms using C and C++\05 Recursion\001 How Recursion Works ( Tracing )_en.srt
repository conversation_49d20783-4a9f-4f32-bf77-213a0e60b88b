1
00:00:00,360 --> 00:00:06,210
Our next topic is discussion, this is a very important topic because a lot of functions or the procedures

2
00:00:06,210 --> 00:00:11,860
can be written using recursion, that is, they can be written as recursive functions.

3
00:00:12,540 --> 00:00:14,340
So let us learn about them.

4
00:00:14,370 --> 00:00:17,170
So what are the topics that I'm going to cover in this video?

5
00:00:18,360 --> 00:00:19,290
What is the recursion?

6
00:00:20,650 --> 00:00:27,820
Example I take can explain by tracing of that recursion how to trace it, how to know the output of

7
00:00:27,830 --> 00:00:31,870
recursion, then recursion uses track.

8
00:00:31,900 --> 00:00:32,880
So what does it mean?

9
00:00:32,890 --> 00:00:36,370
How do you do this track that I'm going to explain you then?

10
00:00:36,370 --> 00:00:41,300
Also, I'll show you how to find the time and complexity of any recursive function.

11
00:00:42,250 --> 00:00:46,980
So let us start with what is recursion now before showing you recursion?

12
00:00:47,170 --> 00:00:50,590
First of all, let me remind you one thing here.

13
00:00:50,590 --> 00:00:56,380
I have one example program that is the main function and one function called function funding.

14
00:00:57,640 --> 00:01:00,010
And this main function is calling this function.

15
00:01:01,050 --> 00:01:04,110
So how dysfunction call is made, how it works.

16
00:01:04,379 --> 00:01:09,630
Let me remind you this already you have explained you in my previous video, let us look at it again.

17
00:01:10,530 --> 00:01:15,960
See, once I start executing my program, it will start executing Fomin function faster to execute the

18
00:01:15,960 --> 00:01:20,310
first statement and second statement than the third statement is function call.

19
00:01:20,670 --> 00:01:26,490
It goes to this function and those inside execute first statement, second and third statement.

20
00:01:26,730 --> 00:01:34,560
Once it has finish, the function ends and the control come back to the same line.

21
00:01:35,100 --> 00:01:36,330
That is third line.

22
00:01:36,660 --> 00:01:40,970
If any more operations present in this line, they will execute.

23
00:01:41,370 --> 00:01:45,180
Otherwise it will execute fourth line and fifth tonight.

24
00:01:46,230 --> 00:01:48,600
What does it mean by any other operations?

25
00:01:48,600 --> 00:01:56,670
For example, this function is returning something and here I have written multiplied by two something.

26
00:01:57,030 --> 00:01:59,710
So the value of that function has to be multiplied by two.

27
00:01:59,730 --> 00:02:05,400
So this multiplication will be done once the function has a return here with some value.

28
00:02:05,790 --> 00:02:08,509
Assume that that function has a return of ten.

29
00:02:08,940 --> 00:02:13,500
So ten in two can be done only if this function has already done the research.

30
00:02:14,220 --> 00:02:17,610
So once again, quickly, program starts.

31
00:02:17,610 --> 00:02:19,170
First line, second line, third line.

32
00:02:19,170 --> 00:02:22,800
Control goes here first, second, third function and control returns.

33
00:02:23,070 --> 00:02:26,480
Anything pending that will be done and then the remaining statements.

34
00:02:27,690 --> 00:02:32,680
This is the important point that we should remember for understanding recursion.

35
00:02:33,570 --> 00:02:35,670
So let me explain the next level recursion.

36
00:02:36,270 --> 00:02:38,750
Let us look at a general form of recursion.

37
00:02:38,760 --> 00:02:42,350
So here I have an example, a recursive function.

38
00:02:42,480 --> 00:02:44,300
So what does it mean by recursive function?

39
00:02:44,880 --> 00:02:49,210
Our function is calling itself.

40
00:02:49,740 --> 00:02:55,290
So if a function is calling itself, then it is called recursive function.

41
00:02:55,800 --> 00:03:00,090
So function inside that if you see it is calling itself again.

42
00:03:00,110 --> 00:03:02,640
So this is a recursive function.

43
00:03:04,210 --> 00:03:10,840
Then one important thing inside recursions here, you can see that this is a base condition, conditions

44
00:03:10,840 --> 00:03:17,900
that so there must be some base condition that will terminate recursion.

45
00:03:18,700 --> 00:03:21,700
There must be some method to terminate this one.

46
00:03:21,700 --> 00:03:24,370
Otherwise it will go away and go in finite calling.

47
00:03:24,370 --> 00:03:27,640
It is just like this, what we call the function first time.

48
00:03:28,170 --> 00:03:29,950
Then it will call itself again.

49
00:03:29,950 --> 00:03:31,210
Call itself again.

50
00:03:31,220 --> 00:03:32,920
It calls itself repeatedly.

51
00:03:32,920 --> 00:03:34,510
It will be calling again and again.

52
00:03:34,810 --> 00:03:37,900
So there must be some condition at which it must stop.

53
00:03:38,620 --> 00:03:46,090
So in this example, this will call itself if the condition is true or it can stop if the conditions

54
00:03:46,180 --> 00:03:48,580
or whatever it is, at some point, it must stop.

55
00:03:48,970 --> 00:03:50,960
So here is the condition become false.

56
00:03:50,980 --> 00:03:53,060
It will not call further and it stops.

57
00:03:53,290 --> 00:03:59,380
So finally, let us look at again a function calling itself is called recursion.

58
00:03:59,380 --> 00:04:06,910
And inside recursive function, there must be some condition which will make the recursion stop terminate.

59
00:04:07,240 --> 00:04:10,450
So in my example, if the condition become false, it will stop.

60
00:04:11,330 --> 00:04:11,840
That's it.

61
00:04:12,260 --> 00:04:17,070
So let us take some example of recursive function and study how it works.

62
00:04:17,690 --> 00:04:19,040
Let us look at an example.

63
00:04:19,310 --> 00:04:20,660
Let me explain the example.

64
00:04:20,660 --> 00:04:27,260
I have a main function which is having some value and calling function function by passing that X and

65
00:04:27,260 --> 00:04:29,540
function function, taking parameter NP.

66
00:04:30,230 --> 00:04:35,140
And if the condition is true, it is printing and then calling itself.

67
00:04:35,450 --> 00:04:37,130
I have you also see language code here.

68
00:04:37,490 --> 00:04:44,080
So it's printing and again calling itself for a reduced value of and if whatever and it's supposed and

69
00:04:44,090 --> 00:04:46,340
there's a file then it will call itself for four.

70
00:04:47,330 --> 00:04:53,250
So if I am passing through from here, what will be the result, how it works.

71
00:04:53,900 --> 00:04:57,140
Let us trace this recursive function and check.

72
00:04:58,110 --> 00:05:05,400
Now, tracing how to trace recursive function, recursive functions are traced in the form of a tree,

73
00:05:05,970 --> 00:05:09,510
so Lichtman started tracing this one see inside the function.

74
00:05:09,540 --> 00:05:12,030
There are two steps that it has to perform.

75
00:05:12,030 --> 00:05:18,330
If this condition is to force the students to Prince again step, it has to call itself bypassing and

76
00:05:18,330 --> 00:05:19,040
minus one.

77
00:05:19,260 --> 00:05:22,350
And this it has to do on leave and there's a greater than zero.

78
00:05:24,110 --> 00:05:25,850
Let us start from here.

79
00:05:25,880 --> 00:05:29,620
I'm calling function fun one by passing exit that is mandatory.

80
00:05:30,380 --> 00:05:35,320
So here, fun one by passing three.

81
00:05:35,810 --> 00:05:37,530
So first time it has got three.

82
00:05:38,180 --> 00:05:39,800
Three is greater than zero.

83
00:05:39,830 --> 00:05:40,360
Yes.

84
00:05:40,820 --> 00:05:42,360
First step, Brent.

85
00:05:42,530 --> 00:05:46,100
And so first strippers print three.

86
00:05:47,750 --> 00:05:50,450
Then what is the second step goal itself.

87
00:05:50,450 --> 00:05:52,820
Again, fun one, four, three minus one.

88
00:05:52,820 --> 00:05:53,450
That is two.

89
00:05:53,780 --> 00:06:00,890
So it will call itself again by passing fun of to see inside the function.

90
00:06:00,890 --> 00:06:02,000
There are two steps.

91
00:06:02,000 --> 00:06:03,610
Both steps have completed.

92
00:06:03,630 --> 00:06:07,670
First step was sprinting and the second step is called sprinting.

93
00:06:07,670 --> 00:06:08,990
It's nothing but output.

94
00:06:08,990 --> 00:06:11,270
So what is the output they got so far?

95
00:06:11,480 --> 00:06:12,440
Output is three.

96
00:06:13,980 --> 00:06:15,630
Then what about this?

97
00:06:15,660 --> 00:06:16,980
It's a call again.

98
00:06:18,570 --> 00:06:25,410
Whether this call has finished nor it has completed Faslane second line, it has not yet came out of

99
00:06:25,410 --> 00:06:26,140
the function.

100
00:06:26,760 --> 00:06:28,530
It has not yet came out of the function.

101
00:06:28,530 --> 00:06:31,180
It has to come out, but it is calling this one.

102
00:06:31,710 --> 00:06:37,760
So let us execute this phone off to again, start to do is greater than zero.

103
00:06:37,770 --> 00:06:39,020
Yes, two steps.

104
00:06:39,390 --> 00:06:45,420
So the first step is trying to do so, the output to then go on it.

105
00:06:45,420 --> 00:06:48,060
So for and minus one so full of.

106
00:06:50,320 --> 00:06:50,800
One.

107
00:06:54,000 --> 00:06:58,990
Now, still, the school has not finished, it has printed and it has to call this one.

108
00:06:59,310 --> 00:07:05,680
So again, a new fresh call, again, a fresh call so that fresh colors fun one with one.

109
00:07:05,680 --> 00:07:08,030
So fun, what with one one is great.

110
00:07:08,070 --> 00:07:12,330
Then later, we also perform two things for sister friend one.

111
00:07:12,330 --> 00:07:14,030
So in output we get one.

112
00:07:14,520 --> 00:07:18,090
Then the second thing is called for one minus one.

113
00:07:18,090 --> 00:07:28,730
So fun, one of one minus one is zero one zero one one zero and that is zero greater than zero.

114
00:07:28,740 --> 00:07:30,420
No, it is not greater than zero.

115
00:07:30,750 --> 00:07:32,550
It will not enter inside.

116
00:07:32,550 --> 00:07:36,000
So it will not perform these two steps and it does nothing.

117
00:07:36,360 --> 00:07:41,530
So no printing and no calling and it will not enter inside and it will come out of the function.

118
00:07:42,870 --> 00:07:45,100
So this call doesn't do anything.

119
00:07:45,540 --> 00:07:49,500
So there is no further calling then what it has to do.

120
00:07:50,910 --> 00:07:55,450
It will go back to the previous function and come out of the function.

121
00:07:55,470 --> 00:08:02,170
So this function finishes it, go back to the previous function that is in this line and come out afterwards.

122
00:08:02,220 --> 00:08:03,240
There is nothing remaining.

123
00:08:03,240 --> 00:08:07,400
So it will simply come out and go back to this function and come out of this one.

124
00:08:07,680 --> 00:08:10,020
Then it will come back to mean and terminates.

125
00:08:10,560 --> 00:08:16,020
So from this call, it goes back to the previous caller and then previous call, just like the we just

126
00:08:16,020 --> 00:08:22,380
now have shown you how a function terminates and it goes back to the place from where it was called.

127
00:08:23,580 --> 00:08:31,490
So this is a tree and this is called Preysing Tree of Recursive Function, and this is the output.

128
00:08:31,890 --> 00:08:34,110
Now, I will take one more example and show you.

129
00:08:34,360 --> 00:08:39,120
So this example, I'll just move it aside and then I'll use this portion for you.

130
00:08:39,450 --> 00:08:40,390
Next example.

131
00:08:40,919 --> 00:08:44,049
No, I have one more example of a recursive function.

132
00:08:44,490 --> 00:08:46,410
This is more similar to the first one.

133
00:08:47,880 --> 00:08:53,730
Let me compare them and show you if you check the main function, having variable X and calling function

134
00:08:53,730 --> 00:09:00,750
function here also mean having variable X and calling function frontal insight fun.

135
00:09:00,750 --> 00:09:04,230
One, it is taking parameter and and greater than zero.

136
00:09:04,500 --> 00:09:06,130
Two things are done here.

137
00:09:06,130 --> 00:09:09,140
Also taking parameter and greater than zero things are done.

138
00:09:09,870 --> 00:09:15,080
But the differences here are sprinting and then calling itself.

139
00:09:15,570 --> 00:09:19,380
But here first it is calling itself then printing.

140
00:09:20,310 --> 00:09:21,880
Then what will be the output.

141
00:09:22,590 --> 00:09:24,100
So let us trace this one.

142
00:09:24,240 --> 00:09:26,750
So this is a minor difference between these two functions.

143
00:09:27,210 --> 00:09:28,060
Let us stress.

144
00:09:28,830 --> 00:09:34,110
So from here I start first main function is calling this function Fundo.

145
00:09:34,530 --> 00:09:41,370
So by passing value three, so frustrating function will be this is three three and this three.

146
00:09:41,370 --> 00:09:43,100
So three is greater than zero.

147
00:09:43,110 --> 00:09:43,630
Yes.

148
00:09:43,650 --> 00:09:50,200
So first statement as work, call it some form minus one second statement is print.

149
00:09:50,550 --> 00:09:51,750
So first the statement.

150
00:09:51,750 --> 00:09:58,740
It will call itself for Fundo of two and three or three minus one, two.

151
00:09:59,070 --> 00:10:04,000
Then what about the next statement printing it will not be done first.

152
00:10:04,290 --> 00:10:08,080
This call has to finish, then only it will print.

153
00:10:08,850 --> 00:10:10,160
So let us take this call.

154
00:10:10,170 --> 00:10:11,210
Let us finish this one.

155
00:10:11,670 --> 00:10:17,020
So it will call itself again that any call to two two is greater than zero.

156
00:10:17,400 --> 00:10:18,740
Again, two steps.

157
00:10:18,750 --> 00:10:21,030
So first step is Colvert and minus one.

158
00:10:21,070 --> 00:10:21,930
So and this is two.

159
00:10:22,200 --> 00:10:24,870
So it will call itself for fun.

160
00:10:24,870 --> 00:10:28,590
Two of one and second statement.

161
00:10:28,890 --> 00:10:32,280
It will be done after this first statement has finished.

162
00:10:33,540 --> 00:10:34,650
What is the first statement?

163
00:10:35,010 --> 00:10:37,440
Fungo one one is greater than zero.

164
00:10:37,470 --> 00:10:39,360
So call it for a minus one.

165
00:10:39,570 --> 00:10:48,620
So again, next call, fun two of one minus one is zero and pretending that they were done afterwards

166
00:10:48,720 --> 00:10:49,140
first.

167
00:10:49,160 --> 00:10:50,160
This has to finish.

168
00:10:51,150 --> 00:10:56,140
Not one of zero, that is one two zero zero zero zero zero.

169
00:10:56,310 --> 00:11:02,900
No, sir, it will not enter inside this F block and it will come out so it does nothing.

170
00:11:04,260 --> 00:11:12,510
So this call, the department zero has terminated now once this call has been terminated, the control

171
00:11:12,520 --> 00:11:15,150
should go back to the previous call.

172
00:11:15,450 --> 00:11:17,610
So previous call was this one.

173
00:11:17,610 --> 00:11:18,860
It was called from here.

174
00:11:18,870 --> 00:11:22,710
So it will go back to this function call and this line in this line.

175
00:11:22,950 --> 00:11:24,200
This line was this one.

176
00:11:24,450 --> 00:11:26,120
So this has finished.

177
00:11:26,430 --> 00:11:28,110
Now the next line is Sprent.

178
00:11:28,110 --> 00:11:31,770
And so what is and value one single point one.

179
00:11:32,040 --> 00:11:35,790
So the output as first output is one.

180
00:11:37,970 --> 00:11:43,850
Then it will go back to the previous call and second thing that is remaining here, that is printing,

181
00:11:44,090 --> 00:11:45,560
printing, it has to do so.

182
00:11:45,560 --> 00:11:49,890
The value to print that, then this will come out of this and finish.

183
00:11:49,940 --> 00:11:53,300
So this call finishes it goes back to the previous call.

184
00:11:53,570 --> 00:11:56,300
In that call, it has completed this one of two.

185
00:11:56,300 --> 00:12:00,350
You can see that this complete full of two is in deep, it is complete.

186
00:12:00,620 --> 00:12:02,320
And then the second statement print.

187
00:12:02,330 --> 00:12:04,520
And so the value of this three.

188
00:12:04,880 --> 00:12:06,900
So this is three.

189
00:12:07,850 --> 00:12:09,650
So this is the output of this function.

190
00:12:09,650 --> 00:12:13,240
So you can see that the function values are one, two, three.

191
00:12:13,460 --> 00:12:16,370
And here the output was three to one.

192
00:12:17,540 --> 00:12:23,360
Now let us compare them and this function first the printing was done and the recursive call was made

193
00:12:23,630 --> 00:12:27,420
print and then call print and then call print and then call.

194
00:12:27,770 --> 00:12:29,850
But here first call is made.

195
00:12:29,850 --> 00:12:30,870
Then printing is done.

196
00:12:30,890 --> 00:12:31,970
So first call.

197
00:12:32,270 --> 00:12:32,990
Then call.

198
00:12:33,210 --> 00:12:33,950
Then call.

199
00:12:34,180 --> 00:12:34,790
Then call.

200
00:12:35,060 --> 00:12:37,250
So again now Redenbach print.

201
00:12:37,340 --> 00:12:38,300
Written print.

202
00:12:38,300 --> 00:12:39,110
Written print.

203
00:12:39,710 --> 00:12:45,350
So here printing was done at counting time before the function is called printing was done.

204
00:12:45,620 --> 00:12:48,860
But here printing is a done at a time.

205
00:12:50,310 --> 00:12:54,990
When the function is returning to the previous caller, the previous call, so at the reading time,

206
00:12:54,990 --> 00:12:55,780
printing is done.

207
00:12:56,010 --> 00:12:59,600
And the difference here is printing is done after the function call.

208
00:12:59,880 --> 00:13:02,470
So that portion will be done that are done in time.

209
00:13:02,910 --> 00:13:05,250
So I have shown you the differences between them.

210
00:13:05,760 --> 00:13:08,930
Now, let me take one example and show you the differences among them.

211
00:13:09,510 --> 00:13:12,000
See, suppose there is a room.

212
00:13:13,760 --> 00:13:15,620
And there is a boat in the room.

213
00:13:16,700 --> 00:13:22,410
Under the law and from that room, you can enter into one more room.

214
00:13:22,430 --> 00:13:25,550
This is the door for it and there is a fixed in this one.

215
00:13:26,880 --> 00:13:30,210
And one more door, there is a bulb fixed and this one.

216
00:13:31,730 --> 00:13:38,900
This is a room one and room two and room three, three rooms, and each room is having a ball.

217
00:13:40,430 --> 00:13:54,190
Now, if I give you the instruction saying that switch on bulb, second step is go to the next room.

218
00:13:56,390 --> 00:14:02,070
So these are the two steps if I asked you to perform recursively, so there are rooms like this, right?

219
00:14:02,450 --> 00:14:04,520
So nested room, room, inside room.

220
00:14:05,480 --> 00:14:13,490
Now, if I make you stand here and ask you to perform this recursively, then what you will do better.

221
00:14:13,550 --> 00:14:15,590
Check your decision.

222
00:14:15,590 --> 00:14:16,580
One first step.

223
00:14:17,150 --> 00:14:18,100
Second step.

224
00:14:18,110 --> 00:14:19,190
You'll go to the next room.

225
00:14:19,580 --> 00:14:26,600
So the first ball that you have switched on, this one, let us call it output, then you will enter

226
00:14:26,600 --> 00:14:30,200
in the next room, then hear what you'll do again recursively.

227
00:14:30,200 --> 00:14:31,730
So switch on bulb.

228
00:14:31,730 --> 00:14:35,150
So you'll switch on second bulb and then go to the next room.

229
00:14:35,150 --> 00:14:36,530
You'll end to this next room.

230
00:14:38,000 --> 00:14:40,850
Then in this room again, first step is switch on bulb.

231
00:14:40,850 --> 00:14:43,700
So you'll switch on third Terbil and go to the next room.

232
00:14:43,940 --> 00:14:45,320
There is no next room for.

233
00:14:46,460 --> 00:14:51,810
So it was more like a recursive call, a function calling itself again and function calling itself again.

234
00:14:52,160 --> 00:14:53,450
Now this is termination.

235
00:14:53,450 --> 00:14:54,940
There is no next room.

236
00:14:55,520 --> 00:15:01,140
What you will do now, you will definitely come out of those rooms you vodcast finished.

237
00:15:01,490 --> 00:15:04,700
So from third room you will come back to the second room.

238
00:15:05,090 --> 00:15:06,400
What will you do anything now?

239
00:15:06,400 --> 00:15:11,230
No, you will simply come out, you'll come out in the first room, then you will come out of the first

240
00:15:11,230 --> 00:15:11,750
four months.

241
00:15:12,740 --> 00:15:14,390
So third, the second, second, first.

242
00:15:14,400 --> 00:15:16,820
Then you'll come out and you simply come out.

243
00:15:16,970 --> 00:15:17,800
Nothing will do.

244
00:15:18,230 --> 00:15:21,590
So it means first stage was you were entering in.

245
00:15:22,010 --> 00:15:23,450
So it was just like calling.

246
00:15:24,080 --> 00:15:28,620
And after you have finished this one, you cannot go further, so you'll return back.

247
00:15:28,910 --> 00:15:30,010
So this is returning.

248
00:15:30,500 --> 00:15:33,080
So you have gone through two phases.

249
00:15:33,530 --> 00:15:41,420
One is the calling phase, either one is returning phase, or I can say that this was ascending and

250
00:15:41,420 --> 00:15:42,590
then descending.

251
00:15:42,950 --> 00:15:45,710
So you are going to next, next, next, next ascending.

252
00:15:45,950 --> 00:15:48,050
Then you came back and back and back.

253
00:15:48,320 --> 00:15:49,250
So descending.

254
00:15:49,430 --> 00:15:52,760
So you have done ascending and descending and descending time.

255
00:15:52,760 --> 00:15:53,830
You have not done anything.

256
00:15:54,860 --> 00:15:56,660
Now, I'll give you one more example.

257
00:15:57,020 --> 00:15:58,000
One more example.

258
00:15:58,370 --> 00:16:10,720
First step is go to the next room, go to the next room and second step is switch on bulb.

259
00:16:12,350 --> 00:16:15,310
These are the two steps I have changed the order you can see.

260
00:16:15,920 --> 00:16:18,880
Go to the next room, then switch on bulb.

261
00:16:19,520 --> 00:16:21,840
Now, let's see if these are performed.

262
00:16:21,840 --> 00:16:23,930
Then what will that result?

263
00:16:24,200 --> 00:16:28,490
If you are here, then I have given you two instruction.

264
00:16:28,490 --> 00:16:31,880
Go to next fall, then second step, a switch on bulb.

265
00:16:32,390 --> 00:16:33,500
So first what you'll do.

266
00:16:33,710 --> 00:16:36,410
You'll not switch on bulb, you'll go to the next room.

267
00:16:37,520 --> 00:16:40,070
Then there again, the first step is go to the next room.

268
00:16:40,550 --> 00:16:41,690
Silvo the next room.

269
00:16:43,380 --> 00:16:49,260
There again, the first step is go to the next room so there is no next room, there are no rooms further

270
00:16:49,860 --> 00:16:51,510
than you'll perform.

271
00:16:51,510 --> 00:16:53,490
Second step, second.

272
00:16:53,820 --> 00:17:01,920
So the first bulb to be switched on as tugboat then definitely will come back to the previous room.

273
00:17:02,280 --> 00:17:07,230
And in this room, you'll switch on second one and you'll come back to the first room switch on first.

274
00:17:07,349 --> 00:17:09,780
But that's it.

275
00:17:10,020 --> 00:17:18,750
So I have two different versions of steps here and first set of steps, second bulb go the next room.

276
00:17:18,750 --> 00:17:21,020
So the ball's first featured in this order.

277
00:17:21,030 --> 00:17:21,770
One, two, three.

278
00:17:21,990 --> 00:17:23,400
So there is the recursive call.

279
00:17:23,670 --> 00:17:29,520
This is very passive on first perform the operation, then the recursive call here first the recursive

280
00:17:29,520 --> 00:17:31,240
call, then perform the operation.

281
00:17:31,740 --> 00:17:38,190
So the main important thing that I have to show you here in the caution is that recursion has two phases.

282
00:17:38,490 --> 00:17:42,240
One is calling phase and the other one is written in phase.

283
00:17:42,510 --> 00:17:44,550
So both were switched on by going.

284
00:17:45,000 --> 00:17:46,960
Both were switched on while returning.

285
00:17:47,820 --> 00:17:53,590
So if you see this recursion print and then call switch on, then go to the next room.

286
00:17:53,910 --> 00:17:55,320
So this is the same as that one.

287
00:17:56,490 --> 00:18:00,750
And if you see this one, go to the next room, then switch on.

288
00:18:01,380 --> 00:18:04,830
So here in this example, first go to the next room.

289
00:18:04,830 --> 00:18:07,290
That is make a call and then print.

290
00:18:07,890 --> 00:18:09,480
So this is similar to this one.

291
00:18:10,140 --> 00:18:16,530
So finally, I can say that recursive functions are just like a rubber band if you stretch a rubber

292
00:18:16,530 --> 00:18:17,290
band bullet.

293
00:18:17,430 --> 00:18:19,240
So next, next, next, next.

294
00:18:19,680 --> 00:18:21,450
If you release, it will come back.

295
00:18:22,620 --> 00:18:28,710
It will come back, so save me recursive function, call and call and call and then return Rigi.

296
00:18:29,520 --> 00:18:33,010
So there are two phases, so that's all these are compared.

297
00:18:33,050 --> 00:18:36,060
But one thing that may confuse you, just let us look at this one.

298
00:18:36,070 --> 00:18:40,160
See, the first value was three to three to one for the printing was three to one.

299
00:18:40,170 --> 00:18:41,940
But here the room numbers are one, two, three.

300
00:18:41,940 --> 00:18:43,250
Don't compare this with that one.

301
00:18:43,680 --> 00:18:46,230
Just understand the procedure, how it is working.

302
00:18:46,470 --> 00:18:52,230
Otherwise they can change the room number three to one also so that output will be similar to that.

303
00:18:53,160 --> 00:18:56,700
So the important thing that we have learned is that recursion and have two phases.

304
00:18:56,700 --> 00:19:00,150
One is calling phase and one is returning phase nine.

305
00:19:00,170 --> 00:19:00,420
Right.

306
00:19:00,420 --> 00:19:03,480
Additional general form of recursive function once again and.

