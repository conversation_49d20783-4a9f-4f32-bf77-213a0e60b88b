1
00:00:00,200 --> 00:00:06,330
We do we will learn how to find out a missing element in a sequence of elements if the elements are

2
00:00:06,330 --> 00:00:07,480
stored in an area.

3
00:00:08,189 --> 00:00:13,410
First of all, I will show you how to find a single missing element in a Socotra.

4
00:00:13,440 --> 00:00:18,660
If that is all that exotic, then if there are multiple elements that are missing and then sorted out,

5
00:00:18,750 --> 00:00:19,650
we will find out that.

6
00:00:20,010 --> 00:00:27,000
And next, if the other is not sorted, how to find the missing element, if there are more of one or

7
00:00:27,000 --> 00:00:28,410
more elements, will find that.

8
00:00:29,360 --> 00:00:35,160
So let us start with the first one, finding a single missing element in our solidarity.

9
00:00:37,680 --> 00:00:45,900
So here I have an example of that sequence of elements, and these are actually forced and natural numbers,

10
00:00:45,900 --> 00:00:48,700
so the sequences are starting from one onwards.

11
00:00:48,960 --> 00:00:54,450
So if you see the elements, one, two, three, four, five, six, then seven is missing in this one,

12
00:00:54,690 --> 00:00:56,200
then eight, nine, 10, 11, 12.

13
00:00:56,250 --> 00:00:58,000
Up to 12 have taken some numbers.

14
00:00:58,710 --> 00:01:05,459
Now I have to find out that one number that is eight is missing in the sequence of first and numbers.

15
00:01:06,030 --> 00:01:07,300
So what should be the procedure?

16
00:01:07,650 --> 00:01:10,160
So there can be more than one possible solutions.

17
00:01:10,470 --> 00:01:14,310
So let us look at first solution or first method.

18
00:01:15,790 --> 00:01:23,920
Some of the first and natural numbers, there is a formula for first and natural numbers that is an

19
00:01:23,920 --> 00:01:25,990
end to end, plus one by two.

20
00:01:27,680 --> 00:01:33,230
So if I have total 12 elements, so that is the last element I should know already, what is the last

21
00:01:33,230 --> 00:01:36,970
element then some of first world natural numbers?

22
00:01:37,400 --> 00:01:43,940
This can be known by using this formula so I can use the formula if I know the first number is one and

23
00:01:43,940 --> 00:01:49,410
the last number is 12, and I want to know which number is missing, then I can use this formula.

24
00:01:49,700 --> 00:01:59,180
So, for example, for this tool, if I take it that's twelve going into thirteen by two as two six

25
00:02:00,050 --> 00:02:02,810
six three eight six one seventy eight.

26
00:02:03,170 --> 00:02:05,420
So the total should be 78.

27
00:02:05,810 --> 00:02:08,030
If total is not 78.

28
00:02:08,030 --> 00:02:10,820
If it is less, how much less it is.

29
00:02:11,550 --> 00:02:12,760
That is the missing element.

30
00:02:15,060 --> 00:02:19,060
So what I should do, I should find the sum of all these numbers.

31
00:02:19,530 --> 00:02:26,840
So finding the sum of all the elements in an area that I can do it iteratively or recursively.

32
00:02:27,210 --> 00:02:32,760
So let me write on a piece of program code for doing it iteratively.

33
00:02:34,470 --> 00:02:39,210
So for finding some of all the elements, I'll take one variable that is some and initially desirable.

34
00:02:39,510 --> 00:02:46,260
Then using a follow up, I can scan through this area from the first to the last index that is 10.

35
00:02:47,250 --> 00:02:49,050
So for I'll find zero.

36
00:02:50,280 --> 00:02:58,080
So ISIS is starting from one onwards and I should stop at 10, so I should say I use less than 11 I

37
00:02:58,320 --> 00:03:02,160
lost plus so I should know the last index, that is ten.

38
00:03:02,370 --> 00:03:04,160
So I lost 11.

39
00:03:04,170 --> 00:03:14,430
So what I have to do some assign some plus if I add all the elements from adding them here, I'll be

40
00:03:14,430 --> 00:03:22,920
getting the sum of all these elements then I know the formula using the formula if I know the last number,

41
00:03:22,920 --> 00:03:23,720
but that is 12.

42
00:03:23,730 --> 00:03:25,940
So using the formula I can calculate that.

43
00:03:26,220 --> 00:03:37,470
So let us say this is and this is and so I can find as as and in two and less one by two.

44
00:03:39,430 --> 00:03:45,460
So this will give me yes, that is some of first 12 national numbers, so they should be an.

45
00:03:47,150 --> 00:03:49,750
And this is a number of elements in an early.

46
00:03:52,160 --> 00:04:01,310
Then once they got this one, then I can know the missing number from X minus some S is obtained from

47
00:04:01,310 --> 00:04:03,420
the formula that some of national numbers.

48
00:04:03,440 --> 00:04:08,600
I got it from the formula then minus a sum that I have added all these elements.

49
00:04:08,840 --> 00:04:11,350
If I subtract them, then I get the number.

50
00:04:11,360 --> 00:04:21,350
For example, if I add all these numbers in the system plus these two 20 these to 30 days to 40 and

51
00:04:21,350 --> 00:04:24,590
versus 48, then these two numbers.

52
00:04:26,250 --> 00:04:33,360
Fifty nine, seventy one, so total, I'm getting seventy one, so this is some will be giving me seventy

53
00:04:33,360 --> 00:04:39,900
one and for this just not have calculated that was seventy eight, so seventy eight minus seventy one

54
00:04:40,140 --> 00:04:40,950
is a seven.

55
00:04:41,280 --> 00:04:46,530
So by subtracting this I can get the number so that I can print it here.

56
00:04:46,680 --> 00:04:51,900
So I will say print F missing number is S minus some.

57
00:04:55,430 --> 00:04:56,250
So that's it.

58
00:04:56,600 --> 00:05:01,610
This is the method you can find it using the sum of a natural number of formula.

59
00:05:02,960 --> 00:05:04,600
This is one approach I have shown you.

60
00:05:05,820 --> 00:05:13,020
Then I'll show you one more example where the elements are not forced national, the sequence maybe

61
00:05:13,020 --> 00:05:15,010
starting from any point.

62
00:05:15,330 --> 00:05:18,890
So let me change these elements, then we will discuss Travelgate.

63
00:05:20,210 --> 00:05:25,970
Now, here I have array of elements now here the starting number is six, it is not first a national

64
00:05:25,970 --> 00:05:27,770
number and the last number is 17.

65
00:05:28,130 --> 00:05:32,470
So first of all, I should know what is the starting number and what is the last number like?

66
00:05:32,870 --> 00:05:39,680
And in this one on the element is missing and these elements are all the disorder and one of the elements

67
00:05:39,680 --> 00:05:40,150
missing.

68
00:05:40,520 --> 00:05:42,110
So I'll show you which element is missing.

69
00:05:42,110 --> 00:05:45,270
Six, seven, eight, nine, 10, 11, 12 is missing in this one.

70
00:05:45,950 --> 00:05:48,590
So there is a sequence of sorted elements.

71
00:05:48,590 --> 00:05:53,810
I should know the starting number and ending number, and I want to know which number is missing in

72
00:05:53,810 --> 00:05:54,320
this one.

73
00:05:54,800 --> 00:05:57,280
And the total number of numbers are 11.

74
00:05:57,440 --> 00:05:58,740
That is zero this.

75
00:05:59,150 --> 00:06:02,690
So let us take so let us see how to find out this one.

76
00:06:02,720 --> 00:06:07,430
So for that, I will show you first of all, this is lower number.

77
00:06:07,460 --> 00:06:08,230
That is six.

78
00:06:08,240 --> 00:06:09,570
And this is higher number.

79
00:06:09,590 --> 00:06:10,580
That is 17.

80
00:06:10,940 --> 00:06:16,070
And the total number of numbers I have let us call them as in and let us see.

81
00:06:16,100 --> 00:06:21,180
These are 11 numbers zero 10, not from the six to 17.

82
00:06:21,200 --> 00:06:23,220
I want to know which number is missing.

83
00:06:23,630 --> 00:06:26,420
So I'll show you the procedure to the procedures.

84
00:06:27,050 --> 00:06:31,380
If you take the inbusiness, you can easily find out which number is missing.

85
00:06:31,670 --> 00:06:39,140
So here in this is can help us see the difference between the first element and the index is six minus

86
00:06:39,140 --> 00:06:44,340
zero six mix number and its index seven minus one is six.

87
00:06:44,750 --> 00:06:48,090
So eight minus two is six.

88
00:06:48,590 --> 00:06:54,500
So every number, if I take the difference, it is six difference of six 11 minus five.

89
00:06:54,780 --> 00:07:03,320
That is also six, then 13, minus six, 13, minus six as not six a seven.

90
00:07:03,920 --> 00:07:04,300
Oh.

91
00:07:04,760 --> 00:07:11,130
So here I actually I should have got the difference has six but I'm getting a difference of seven means

92
00:07:11,180 --> 00:07:12,770
this element is missing.

93
00:07:12,800 --> 00:07:13,190
Yes.

94
00:07:13,460 --> 00:07:14,600
What should be the element.

95
00:07:14,820 --> 00:07:17,120
See we know the difference was six.

96
00:07:17,120 --> 00:07:17,400
Right.

97
00:07:17,630 --> 00:07:18,830
So we are at index.

98
00:07:18,830 --> 00:07:19,730
Which index now.

99
00:07:19,730 --> 00:07:21,090
We are at index six.

100
00:07:21,320 --> 00:07:25,190
So in that you are six plus six, you add that is the difference.

101
00:07:25,200 --> 00:07:26,270
So you get the number twenty.

102
00:07:27,020 --> 00:07:28,810
So that is number two is missing.

103
00:07:29,810 --> 00:07:32,860
We know the difference is six and we are index six right now.

104
00:07:32,910 --> 00:07:35,620
So these two six percent right now.

105
00:07:35,930 --> 00:07:39,410
So six is the index and the six is the difference.

106
00:07:39,980 --> 00:07:45,200
So in this way, I can continue checking and from this point on what the difference is seven.

107
00:07:45,380 --> 00:07:47,490
So I should make sure that the difference is seven.

108
00:07:47,900 --> 00:07:52,700
So if you are sure that there is only one element missing, we can stop here because the missing element

109
00:07:52,700 --> 00:07:53,240
is found.

110
00:07:53,660 --> 00:07:59,120
And if the procedure is for finding multiple missing elements, then I can continue.

111
00:07:59,480 --> 00:08:02,310
And so here I will write on the.

112
00:08:02,480 --> 00:08:06,560
So the procedure is we have to scan through this array, checking the difference.

113
00:08:06,560 --> 00:08:11,000
If the difference is always the same, if there is a change in the defence element is missing.

114
00:08:11,030 --> 00:08:12,960
So with the help of indices, we can find out.

115
00:08:13,280 --> 00:08:15,120
So for this, we have to scan for this.

116
00:08:15,800 --> 00:08:22,130
So I will write on a piece of code for scanning through an array and finding missing element before

117
00:08:22,130 --> 00:08:23,060
writing the code.

118
00:08:23,240 --> 00:08:25,230
I should already know these things.

119
00:08:25,250 --> 00:08:28,260
That is low, high and number of elements.

120
00:08:28,550 --> 00:08:29,630
So let us take this.

121
00:08:29,930 --> 00:08:32,419
We know the array index to start from zero one.

122
00:08:32,640 --> 00:08:34,870
So first of all, I will take difference.

123
00:08:34,909 --> 00:08:38,780
Difference as low minus zero.

124
00:08:39,049 --> 00:08:40,280
That is lonely.

125
00:08:40,549 --> 00:08:42,530
Luminesce zero gives me the difference.

126
00:08:42,530 --> 00:08:46,550
So Cedro I am writing so that I can show that this is a difference.

127
00:08:46,910 --> 00:08:53,050
So starting index and this defense will be six now using a false hope against Cantor another.

128
00:08:53,060 --> 00:08:58,490
And every time I should check that the difference maintained is six only so far.

129
00:08:59,960 --> 00:09:02,580
So far I start from zero and up to end.

130
00:09:02,690 --> 00:09:07,250
That is less than 11 that is lost and extend what I should do every time.

131
00:09:07,520 --> 00:09:13,730
I should check that if for every element, if the difference is not six.

132
00:09:14,090 --> 00:09:24,650
So difference of an element of a minus, I if it is not equal to difference, if difference is not matching,

133
00:09:24,980 --> 00:09:27,340
then the missing element is found.

134
00:09:27,350 --> 00:09:30,170
So I should print this one missing element.

135
00:09:31,420 --> 00:09:33,290
Missing element is index.

136
00:09:33,400 --> 00:09:37,300
Plus the difference index, plus the difference, so indexes are, I hear.

137
00:09:37,540 --> 00:09:40,160
So it should be E-Plus difference.

138
00:09:41,770 --> 00:09:42,380
That's it.

139
00:09:43,000 --> 00:09:45,120
So that's a missing element is found.

140
00:09:45,160 --> 00:09:46,840
And I can break and stop.

141
00:09:47,880 --> 00:09:53,730
End of this loop, so this break will come out of this for loop and it will stop there because the missing

142
00:09:53,730 --> 00:09:54,580
element is found.

143
00:09:55,050 --> 00:09:58,350
So that's all this is for single missing element.

144
00:09:59,870 --> 00:10:06,230
No one thing what is the timetable for finding the missing element, so we have finished finding missing

145
00:10:06,230 --> 00:10:07,590
a single missing element.

146
00:10:07,880 --> 00:10:09,500
What is the time taken for this one?

147
00:10:09,860 --> 00:10:12,770
So the time depends on the amount of work we are doing.

148
00:10:12,800 --> 00:10:14,080
So what is the work we are doing?

149
00:10:14,090 --> 00:10:16,960
We are scanning through the elements in an array.

150
00:10:17,270 --> 00:10:19,850
How many elements are there some elements out there?

151
00:10:19,970 --> 00:10:22,950
What is that some and and elements are there.

152
00:10:23,390 --> 00:10:25,400
So how many times we are scanning through.

153
00:10:25,400 --> 00:10:26,840
We are scanning through only once.

154
00:10:27,200 --> 00:10:33,140
So the time this order of an order of an order of a degree.

155
00:10:33,590 --> 00:10:36,610
What is big data or may God that is all different.

156
00:10:36,860 --> 00:10:39,650
So we don't need all those search order of.

157
00:10:39,860 --> 00:10:40,910
And yes.

158
00:10:41,210 --> 00:10:45,620
So the time taken for finding a missing element in an area is part of an.

159
00:10:46,650 --> 00:10:53,980
Now, next, I'll show you how to modify the same goal for finding the next missing element in an array.

160
00:10:54,360 --> 00:10:56,180
So I will change some elements here.

161
00:10:56,430 --> 00:10:57,270
Then we will see.

162
00:10:57,270 --> 00:10:59,520
We will find multiple missing elements.

