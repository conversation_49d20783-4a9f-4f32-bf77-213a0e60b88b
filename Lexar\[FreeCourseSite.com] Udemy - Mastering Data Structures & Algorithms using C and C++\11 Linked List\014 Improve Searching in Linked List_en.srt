1
00:00:00,980 --> 00:00:02,690
Now, next, one more thing we have to learn.

2
00:00:04,350 --> 00:00:05,400
Linear search.

3
00:00:06,320 --> 00:00:11,810
We know that we can improve Línea search for improving <PERSON>ie search.

4
00:00:11,840 --> 00:00:13,550
There are two methods.

5
00:00:14,000 --> 00:00:16,360
One is transportation.

6
00:00:16,550 --> 00:00:19,430
Second one is move to head.

7
00:00:19,970 --> 00:00:25,140
We have already discussed this and I raise the topic in searching in an area.

8
00:00:25,190 --> 00:00:30,080
I have shown you there are two methods for improving Lenie search.

9
00:00:30,090 --> 00:00:33,230
We have seen two methods that is transportation and move to head.

10
00:00:33,560 --> 00:00:36,410
We have already learned this in a race.

11
00:00:36,740 --> 00:00:38,480
So transportation.

12
00:00:39,260 --> 00:00:44,420
So this procedure is for improving so that next time when you search for the same key, it can be found

13
00:00:44,420 --> 00:00:45,130
in less time.

14
00:00:45,620 --> 00:00:49,860
So transportation was a method where we interchange the value with the previous value.

15
00:00:49,880 --> 00:00:54,170
So if you are searching for tool tools, should be brought here and on should be senden.

16
00:00:54,560 --> 00:00:57,210
And the second method we saw that move to her remains.

17
00:00:57,230 --> 00:01:01,850
The key value should be brought in the beginning so that next time if you search for the same key,

18
00:01:01,850 --> 00:01:03,530
it is found just in one competition.

19
00:01:04,940 --> 00:01:12,140
So here in Linguist's, which is suitable transportation, we don't do because we avoid movement of

20
00:01:12,140 --> 00:01:12,950
data in the link.

21
00:01:13,280 --> 00:01:15,050
We prefer movement of nodes.

22
00:01:15,260 --> 00:01:20,330
So it's better we take out this node and added into front.

23
00:01:20,480 --> 00:01:23,930
That is beginning of a limitless will make it as a.

24
00:01:24,830 --> 00:01:28,030
So next time when we search, it can be found just in one competition.

25
00:01:28,940 --> 00:01:33,320
So I will explain you how it can be done and also I will it on the code.

26
00:01:33,650 --> 00:01:44,420
So let us look at the code for linear surge along that move to let us see if how to bring such key in

27
00:01:44,420 --> 00:01:46,520
the beginning of a link lists.

28
00:01:46,970 --> 00:01:49,340
Some of the key that we are searching is to.

29
00:01:49,340 --> 00:01:51,260
And this the key.

30
00:01:53,340 --> 00:01:59,450
We want to bring this in the beginning of unknowingness so far, this what are the steps I have to perform

31
00:02:00,540 --> 00:02:01,980
C for searching?

32
00:02:01,980 --> 00:02:09,270
We have taken the pointer beyond the point that we was moving forward and it was stopping here when

33
00:02:09,270 --> 00:02:09,780
searching.

34
00:02:09,780 --> 00:02:19,260
We was stopping on the key Norder then we have to bring this node here so far that we have to modify

35
00:02:19,260 --> 00:02:22,380
which node this not we have to modify anyway.

36
00:02:22,770 --> 00:02:25,080
Then we should modify this node.

37
00:02:25,080 --> 00:02:30,690
Also, this note is pointing on 12 node that is node seven is pointing on 2L.

38
00:02:30,960 --> 00:02:35,220
I should point on nine so I have to modify previous node also.

39
00:02:35,490 --> 00:02:39,180
So for that I should have a pointer on previous node also.

40
00:02:39,630 --> 00:02:42,600
Then how to get a point on previous node in the linked lists.

41
00:02:42,600 --> 00:02:44,190
We can get a pointer on next node.

42
00:02:44,490 --> 00:02:45,970
But how, what previous node.

43
00:02:46,380 --> 00:02:48,630
So let us get a pointer on previous node.

44
00:02:48,750 --> 00:02:55,860
Let us see the procedure initially when PS here we should also have one more point to do.

45
00:02:56,130 --> 00:02:57,360
That is the following.

46
00:02:57,360 --> 00:03:02,810
B we will call it as a tail pointer, so we should also have one pointer.

47
00:03:02,820 --> 00:03:06,030
Q So cuz initially not so.

48
00:03:06,030 --> 00:03:12,510
Q Is not Leontes up on first node, so let us search using T for the key twelve and also let the cube

49
00:03:12,510 --> 00:03:15,260
pointer follow B pointer.

50
00:03:16,200 --> 00:03:17,160
So first step.

51
00:03:18,600 --> 00:03:20,430
So let us see the steps first.

52
00:03:20,430 --> 00:03:21,360
Check this data.

53
00:03:21,360 --> 00:03:22,800
Is it matching with the key keys.

54
00:03:22,800 --> 00:03:23,990
Matching with the signal.

55
00:03:24,300 --> 00:03:26,550
So if it is not matching then move.

56
00:03:26,550 --> 00:03:33,210
Q here upon this node and move B to Nixonland then again search.

57
00:03:33,630 --> 00:03:34,590
Is it matching.

58
00:03:34,800 --> 00:03:35,340
No.

59
00:03:35,580 --> 00:03:42,150
Then the move to the next node and move B to the next node again.

60
00:03:42,150 --> 00:03:43,890
Check whether it is matching no.

61
00:03:44,280 --> 00:03:50,950
Then bring Q pointer upon node B and move B to the next node.

62
00:03:52,050 --> 00:03:57,840
Now it is matching, it is found and so that's how we can have a pointer to following pointer B and

63
00:03:57,840 --> 00:04:00,550
stops before the key node.

64
00:04:00,570 --> 00:04:04,710
So this is the key form that we have to bring this in front.

65
00:04:05,370 --> 00:04:10,120
Let me write on the code up to here, then I will show you what other changes we require.

66
00:04:10,120 --> 00:04:16,829
A search function which is taking on node pointer B and key.

67
00:04:18,570 --> 00:04:26,310
We should also have a pointer called Q, which should be initially none that we know then that using

68
00:04:26,310 --> 00:04:30,810
my loop we will be searching while PS not equal to null check.

69
00:04:31,110 --> 00:04:39,160
If key is equal to B's data, which is equal, then we have stopped here.

70
00:04:39,360 --> 00:04:41,450
So what all we have to do, we will do it here.

71
00:04:41,910 --> 00:04:52,560
Otherwise move Q one P so move Q on B then the move people make snorter move P to next node be assigned.

72
00:04:52,560 --> 00:04:56,150
PS next and here return type is Nordström.

73
00:04:58,410 --> 00:05:04,500
Now I have the already see this is traversing searching if key found and what we have to do we will

74
00:05:04,500 --> 00:05:09,270
see now if not found then move Q on B and move B.

75
00:05:09,330 --> 00:05:15,560
Next note, these are the two statements by which we have brought B on this note and Q on this Nokia's

76
00:05:15,570 --> 00:05:15,890
fault.

77
00:05:16,170 --> 00:05:17,550
What did she just have to make.

78
00:05:18,510 --> 00:05:21,090
I should make deals mixed.

79
00:05:21,240 --> 00:05:21,870
This one.

80
00:05:22,920 --> 00:05:23,880
This is three hundred.

81
00:05:24,450 --> 00:05:27,060
It should point on these.

82
00:05:27,060 --> 00:05:29,160
Next there is three fifty this one.

83
00:05:29,580 --> 00:05:31,500
So it should point on this one.

84
00:05:31,890 --> 00:05:35,040
So this address should change to three fifty.

85
00:05:37,500 --> 00:05:40,140
This address should change to three fifty.

86
00:05:40,500 --> 00:05:46,200
So what I should do in Qs next I should write down the address of PS next.

87
00:05:46,530 --> 00:05:49,440
So this is the statement in Qs next.

88
00:05:51,250 --> 00:05:53,800
Right, these next.

89
00:05:56,200 --> 00:06:02,920
So this Linkous change now is of concern for Snoad after ages three, after three to seven after seven,

90
00:06:02,920 --> 00:06:03,790
it is nine.

91
00:06:05,430 --> 00:06:11,940
So this note removal from legalist, now, this should be made as a first Naude then.

92
00:06:13,270 --> 00:06:25,570
Make it a point on first Naude so right that us here has 200 soapies makes it should be 200 soapies

93
00:06:25,570 --> 00:06:26,830
next is.

94
00:06:28,820 --> 00:06:29,600
Two hundred.

95
00:06:29,780 --> 00:06:30,390
This one.

96
00:06:30,590 --> 00:06:35,020
So who is having this address first, so please, next is a first.

97
00:06:35,450 --> 00:06:42,730
So this note will be pointing on first Naude, then this becomes possible to be back on Facebook.

98
00:06:43,340 --> 00:06:48,130
So you should bring first from here and make it point here so directly.

99
00:06:48,140 --> 00:06:50,410
I'm writing here so it should be brought here.

100
00:06:50,780 --> 00:06:52,910
So first it should be brought on.

101
00:06:53,480 --> 00:06:55,990
B so that became a footnote.

102
00:06:56,480 --> 00:06:57,980
So that's what we want.

103
00:06:58,010 --> 00:07:02,360
We want to make that key node as Heidenau that is first node.

104
00:07:02,810 --> 00:07:06,440
Now this is first Faustus having this over three hundred.

105
00:07:06,830 --> 00:07:12,830
Now from this node we can go on this note that this is sending us on this one, then this is sending

106
00:07:12,830 --> 00:07:14,610
on this one and this is sending on this one.

107
00:07:15,140 --> 00:07:20,990
So if I rearrange those nodes here, it looks like this.

108
00:07:21,020 --> 00:07:24,410
So who is first in order first and orders this one?

109
00:07:24,660 --> 00:07:28,100
Well, I'll just write keys then to this.

110
00:07:28,100 --> 00:07:37,610
Pointing on eight, then eight is pointing on three, then three is pointing on seven.

111
00:07:38,810 --> 00:07:41,720
Then seven is pointing on which node on whom.

112
00:07:41,750 --> 00:07:44,010
Nine nine.

113
00:07:45,080 --> 00:07:46,930
This is the arrangement though.

114
00:07:46,970 --> 00:07:51,200
The notes are different places, but this has became the first node.

115
00:07:51,680 --> 00:07:56,260
Next time when you search for 2L, it will be found in just one single competition.

116
00:07:57,440 --> 00:08:04,130
Start from first, it is fun, let's all stop so all these things we should do if is not all, we should

117
00:08:04,130 --> 00:08:04,870
do all these things.

118
00:08:05,180 --> 00:08:10,070
So these are the steps that we have to perform and we want to bring any node as a footnote, make that

119
00:08:10,070 --> 00:08:10,730
as first node.

120
00:08:10,910 --> 00:08:14,270
So the point of us, it's a small step and that becomes the first node.

121
00:08:14,810 --> 00:08:19,950
And that is pointing on the previously first node of previously, which was the first noted point,

122
00:08:19,960 --> 00:08:21,770
Ben Analagous continues.

123
00:08:23,730 --> 00:08:28,020
So this is move to front an improvement in linnear such.

