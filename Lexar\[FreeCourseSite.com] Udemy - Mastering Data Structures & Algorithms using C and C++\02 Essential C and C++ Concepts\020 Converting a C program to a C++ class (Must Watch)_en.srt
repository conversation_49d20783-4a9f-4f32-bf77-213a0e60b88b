1
00:00:00,690 --> 00:00:06,630
In this video we'll learn about classes and constructors in C++.

2
00:00:06,630 --> 00:00:12,340
So I'm going to discuss the concepts that are useful in the course. I'm using just classes and constructors,

3
00:00:12,480 --> 00:00:14,690
Not all the features of object orientation.

4
00:00:14,700 --> 00:00:18,730
So I will not be discussing everything about OOPS. See here,

5
00:00:18,740 --> 00:00:23,250
I'm going to transform C language code to C++ code.

6
00:00:23,490 --> 00:00:27,060
So we have already seen this in the previous video, this example.

7
00:00:27,060 --> 00:00:31,290
So if you have not seen that video, I suggest you to watch that video.

8
00:00:31,290 --> 00:00:33,990
This is the best style of coding in C language,

9
00:00:34,020 --> 00:00:41,430
I have explained in the previous video. Now, I'm going to transform this into C++ code that is using classes

10
00:00:41,550 --> 00:00:44,040
and also including constructors.

11
00:00:44,130 --> 00:00:46,500
So let me show you how to do this. Before that,

12
00:00:46,500 --> 00:00:50,780
quickly I will go through this example once again and then I will transform it.

13
00:00:50,970 --> 00:00:51,590
See here,

14
00:00:51,630 --> 00:00:54,140
I have a structure called rectangle.

15
00:00:54,200 --> 00:01:00,260
This is having length and breadth, and inside the main function I'm creating or declaring a variable of type rectangle.

16
00:01:00,960 --> 00:01:04,739
And then inside the main function if you see, there is no processing involved.

17
00:01:04,769 --> 00:01:07,230
There are no operations, just function calls are there.

18
00:01:07,770 --> 00:01:12,810
So the function call is for initializing a rectangle, and finding the area of a rectangle, and changing

19
00:01:12,810 --> 00:01:13,390
its length.

20
00:01:14,010 --> 00:01:19,170
And all these functions I'm passing rectangle as parameter, initialize function is setting the

21
00:01:19,170 --> 00:01:26,330
length and breadth, so it is taking by reference, and taking length and breadth. Area is taking a rectangle.

22
00:01:26,700 --> 00:01:33,150
changeLength is taking a rectangle by address again, and it is changing length by taking new length.

23
00:01:33,750 --> 00:01:34,720
So these are the functions.

24
00:01:34,740 --> 00:01:41,010
So, if you see these functions, these functions are defined here and all these functions are related to

25
00:01:41,180 --> 00:01:42,200
that rectangle.

26
00:01:42,880 --> 00:01:46,460
Now, this is the best style of coding in C language.

27
00:01:46,560 --> 00:01:52,710
So this I have adopted in my course, then, I have also shown in places how to transform this into

28
00:01:53,100 --> 00:02:01,620
C++ so that style I'm discussing now. Let us convert it to C++ code so watch it very carefully.

29
00:02:01,630 --> 00:02:05,920
See this is a structure, I will call it as a class.

30
00:02:05,980 --> 00:02:07,290
Class rectangle,

31
00:02:09,889 --> 00:02:12,710
structure is converted to a class.

32
00:02:13,060 --> 00:02:19,370
Then this function, initialize, area, changeLength,

33
00:02:19,480 --> 00:02:24,970
All these functions are meant for that structure only, earlier it was a structure, it was meant for that

34
00:02:24,970 --> 00:02:27,560
structure only. If that structure is not there,

35
00:02:27,760 --> 00:02:32,390
There is no use of these functions, see they are taking rectangle.

36
00:02:33,520 --> 00:02:40,360
So, now the question is, Why that outside? Include them inside the class.

37
00:02:40,360 --> 00:02:41,850
So my class ends here.

38
00:02:43,030 --> 00:02:45,780
So the class starts from here and ends here.

39
00:02:45,790 --> 00:02:50,670
So now the class contains data members, as well as functions.

40
00:02:50,980 --> 00:02:56,320
So these functions have became a part of the class now. See I'm modifying it, I'm discussing and modifying

41
00:02:56,320 --> 00:02:56,410
it.

42
00:02:56,430 --> 00:02:58,310
Watch it very carefully.

43
00:02:58,330 --> 00:03:00,510
The style is different here.

44
00:03:00,510 --> 00:03:01,750
Next.

45
00:03:01,860 --> 00:03:04,270
This is a member of Class only.

46
00:03:04,560 --> 00:03:08,220
Why does it have to be this rectangle as an argument?

47
00:03:08,210 --> 00:03:12,150
Remove this, then, just take length

48
00:03:12,580 --> 00:03:15,710
and breadth, 2 parameters.

49
00:03:15,710 --> 00:03:19,410
Then, this also.

50
00:03:19,410 --> 00:03:20,420
Remove this one.

51
00:03:20,420 --> 00:03:22,280
It's a part of rectangle only.

52
00:03:22,670 --> 00:03:26,830
Just write nothing, because it doesn't have to take anything,

53
00:03:27,040 --> 00:03:30,440
It's accessible, length and breadth are directly accessible.

54
00:03:30,530 --> 00:03:33,320
Then changeLength, this also became a part of it.

55
00:03:33,340 --> 00:03:37,160
No need of taking this rectangle has a parameter, only length is required.

56
00:03:37,190 --> 00:03:40,410
So just declare length as an argument.

57
00:03:41,230 --> 00:03:43,590
So I have made one change. Now,

58
00:03:43,600 --> 00:03:44,600
Few more changes.

59
00:03:44,600 --> 00:03:46,620
Again I'll do it for all the functions.

60
00:03:46,700 --> 00:03:52,970
See this length and breadth are directly accessible, so no need of this variable name.

61
00:03:53,060 --> 00:03:55,320
Just length = l;
breadth = b;

62
00:03:55,520 --> 00:04:00,640
Now here length and breadth directly accessible, no need of r here, because I have removed the parameter

63
00:04:00,700 --> 00:04:02,010
.

64
00:04:02,020 --> 00:04:05,680
Now, here also, no need of r, I have removed the parameter.

65
00:04:05,900 --> 00:04:10,980
It is directly accessing that length, that's it.

66
00:04:11,000 --> 00:04:16,300
So this initialize function, or area function, and changeLength function,

67
00:04:16,310 --> 00:04:18,630
This has became a C++ class.

68
00:04:18,720 --> 00:04:20,560
What we do inside a class is,

69
00:04:20,570 --> 00:04:25,210
When you have the data members we declare these data members as private.

70
00:04:25,340 --> 00:04:26,670
We declare them as private.

71
00:04:27,150 --> 00:04:34,020
And we make these functions as public, because who are accessing these data members?

72
00:04:34,050 --> 00:04:35,560
So many functions are there,

73
00:04:35,580 --> 00:04:37,590
Why do you need that data to be public?

74
00:04:37,590 --> 00:04:39,190
So let us hide it.

75
00:04:39,210 --> 00:04:42,060
Everything should be accessible using these functions.

76
00:04:42,120 --> 00:04:43,930
So now of this has become a class.

77
00:04:43,980 --> 00:04:46,560
Now, how it will affect inside the main function.

78
00:04:46,590 --> 00:04:47,770
I will show that one,

79
00:04:47,820 --> 00:04:54,930
then again, I will come back here. Inside the main function, I have created a variable of type rectangle structure,

80
00:04:54,990 --> 00:04:59,440
So now, I will change it and I will simply say rectangle.

81
00:04:59,880 --> 00:05:00,610
OK?

82
00:05:01,240 --> 00:05:06,800
Now, we call this as an object, instead of a variable, we call it as an object.

83
00:05:06,900 --> 00:05:12,980
Then, how to call these functions? These functions are present inside the rectangle only,

84
00:05:13,050 --> 00:05:20,130
So for calling these functions, instead of passing the parameter, I should say, r . initialize ( ) , then

85
00:05:20,130 --> 00:05:21,880
Then pass 10, 5.

86
00:05:22,400 --> 00:05:26,490
Remove this one, then call this as r . area ( ) ,

87
00:05:26,490 --> 00:05:27,560
remove this one.

88
00:05:27,780 --> 00:05:30,580
Then call this as r . changeLength ( ) ,

89
00:05:30,660 --> 00:05:32,850
remove this one, and just pass 20.

90
00:05:32,970 --> 00:05:33,530
and just pass 20.

91
00:05:34,830 --> 00:05:42,030
Now you can see that earlier, initialize function was taking a rectangle as parameter. Now, initialize ( )

92
00:05:42,030 --> 00:05:49,420
function itself is a part of rectangle. So this is an object and these are the functions, member functions

93
00:05:49,420 --> 00:05:50,500
of that object.

94
00:05:50,530 --> 00:05:56,380
So once you create a rectangle, What are the things you get? You get length and breadth, these are the 2 parameters,

95
00:05:56,390 --> 00:06:01,690
length and breadth, these are the data members. Now, along with that,

96
00:06:01,690 --> 00:06:09,620
We also get the functions like initialize ( ) and area ( ) and changeLength ( ) . We get these functions also.

97
00:06:15,870 --> 00:06:17,490
These are the member functions.

98
00:06:17,490 --> 00:06:19,760
So, length and breadth are data members,

99
00:06:19,770 --> 00:06:23,690
And these are the functions, you can call all of them by using dot operator.

100
00:06:24,690 --> 00:06:30,100
So this is the transformation from C language to C++.

101
00:06:30,120 --> 00:06:33,280
Now one more time, if you see this initialize ( ) function,

102
00:06:33,280 --> 00:06:35,190
This is initializing a rectangle.

103
00:06:35,190 --> 00:06:38,750
We need this function because the data members has became private,

104
00:06:38,760 --> 00:06:46,380
We cannot directly set the length and breadth. Now, When do use initialization mostly? See, usually when we

105
00:06:46,380 --> 00:06:52,130
declare, or create an object, or declare a variable, that time only we prefer initializing.

106
00:06:52,170 --> 00:06:57,390
So declaration and initialization, if you remember a normal variable, if you are declaring integer type

107
00:06:57,390 --> 00:07:00,800
variable, then this the declaration as well as initialization.

108
00:07:00,870 --> 00:07:06,330
So, we prefer that rectangle is also initialized at that time only, so instead of passing it 10, 5,

109
00:07:06,400 --> 00:07:14,970
later on, we want to pass 10, 5 here only. We don't want this function, we want direct initialization

110
00:07:15,090 --> 00:07:23,040
at the time of declaration only. How is this possible? For that, I should have a function for initialization

111
00:07:23,730 --> 00:07:28,060
but we want it to be called automatically at the time of creation of an object.

112
00:07:28,170 --> 00:07:34,430
So yes same initialize function, I will change the name, I will call it as rectangle.

113
00:07:34,950 --> 00:07:40,930
What is this? This is same as class name and no need of void or anything, just write

114
00:07:40,930 --> 00:07:41,900
class name.

115
00:07:42,120 --> 00:07:44,150
This is having same name as class name.

116
00:07:44,160 --> 00:07:47,230
So this becomes a constructor,

117
00:07:47,250 --> 00:07:49,280
Yes, this is a constructor.

118
00:07:49,620 --> 00:07:55,020
This will be automatically called, whenever we are creating an object, it does the job of initializing

119
00:07:55,020 --> 00:07:55,870
an object.

120
00:07:56,160 --> 00:07:58,410
It stops calling that function as initialize.

121
00:07:58,410 --> 00:08:04,120
We are calling this constructor and we are giving the same name as class name, so it's an initializer function

122
00:08:04,120 --> 00:08:05,160
initializer function.

123
00:08:05,160 --> 00:08:11,040
So constructor, we say, it's a function which creates the object and initialize the object, we say like this,

124
00:08:11,040 --> 00:08:14,550
So that's what, it's having same name as class name.

125
00:08:15,330 --> 00:08:20,700
Now let us look at the class once again, rectangle class is having private members and the public member

126
00:08:20,700 --> 00:08:26,250
function, this is a constructor which is initializing the members, and area function which returns length and

127
00:08:26,260 --> 00:08:28,810
breadth by calculating it, and changeLength.

128
00:08:28,940 --> 00:08:31,310
Now these functions are being called here.

129
00:08:31,470 --> 00:08:39,270
So let us see the working now. Once we create an object of rectangle by passing 10 5, then object is created

130
00:08:39,450 --> 00:08:42,100
like this and automatically this function is called.

131
00:08:42,360 --> 00:08:48,310
This will take 10 and this will take 5. So it will assign 10 and 5 so 10 and 5 are assigned.

132
00:08:49,180 --> 00:08:51,150
Then, next I am calling area function.

133
00:08:51,150 --> 00:08:57,310
So this area function will be called upon this rectangle, by multiplying length and breadth,

134
00:08:57,420 --> 00:08:58,430
It will return the result.

135
00:08:58,520 --> 00:09:05,640
So I'm not using that value. Then, changeLength, so upon this rectangle only, because this is r, this function

136
00:09:05,640 --> 00:09:10,040
is called, which will modify length, and set it as new length.

137
00:09:10,110 --> 00:09:11,290
So I'm setting 20,

138
00:09:11,430 --> 00:09:13,300
So this will become 20.

139
00:09:13,860 --> 00:09:20,300
So this is the style I have adopted for converting C language code to C++ code in my course.

140
00:09:20,340 --> 00:09:26,340
In the next video you can see, I will write down the same class, once again just like a C++ program.

141
00:09:26,430 --> 00:09:26,780
Right?

142
00:09:26,790 --> 00:09:27,230
We'll write

143
00:09:27,240 --> 00:09:28,430
Fresh from the beginning.

