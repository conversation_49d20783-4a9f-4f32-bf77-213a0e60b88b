1
00:00:00,530 --> 00:00:05,900
Now, next, <PERSON><PERSON>, see, this is a president linked list with <PERSON>.

2
00:00:06,290 --> 00:00:14,330
No, I want to insert new modes in this one where I want to insert I can insert anywhere, but suppose

3
00:00:14,330 --> 00:00:21,920
always I want to insert at the end of a long list that the new value that I want to insert is nine and

4
00:00:21,920 --> 00:00:23,950
I want it to be inserted here.

5
00:00:24,500 --> 00:00:29,530
So we know very well that for inserting a new law at the last, we should take a pointer and traverse

6
00:00:29,530 --> 00:00:30,130
still here.

7
00:00:30,140 --> 00:00:31,410
Then we can insert a new one.

8
00:00:31,910 --> 00:00:38,830
But when you are repeatedly inserting at the last only then one seditionists we can maintain one pointer

9
00:00:38,840 --> 00:00:40,850
can last up upon the last note.

10
00:00:42,580 --> 00:00:44,190
That will help us inserting.

11
00:00:45,310 --> 00:00:51,790
How let us see, I want to insert a new value that is seven, so using a pointer to create a new normal,

12
00:00:52,000 --> 00:00:57,000
insert the value, make this as null then last makes a good point on this one.

13
00:00:57,310 --> 00:00:59,980
And the last is sure to move on to the new norm.

14
00:01:00,550 --> 00:01:02,430
This is all we can insert a new note.

15
00:01:03,070 --> 00:01:05,260
Let me insert one more note and show you.

16
00:01:05,530 --> 00:01:08,130
I want to insert one more note that is 15.

17
00:01:08,140 --> 00:01:14,500
So create a new node with a temporary pointer to instead of only 15 make this has no make last.

18
00:01:14,500 --> 00:01:19,090
The next point on this one and the move last year to New Norm.

19
00:01:19,870 --> 00:01:22,460
So in this way, you can go on sitting at the last.

20
00:01:23,270 --> 00:01:29,290
So with the help of pointer last, we can insert it at the end of a Linklaters and just Konstantine

21
00:01:29,290 --> 00:01:32,120
because we don't have to traverse the linked list.

22
00:01:32,830 --> 00:01:35,960
This is possible if you are always inserting at the last.

23
00:01:36,310 --> 00:01:37,900
Then let me show it from the beginning.

24
00:01:38,380 --> 00:01:43,740
Suppose I have just one node eight, then we're Faustus.

25
00:01:43,780 --> 00:01:45,700
This is the first most of us will be here.

26
00:01:46,240 --> 00:01:47,960
Then we're lost or should be lost.

27
00:01:47,980 --> 00:01:49,520
It should also be on the same node.

28
00:01:50,200 --> 00:01:53,950
So there is only one node then both first and last point on that same.

29
00:01:53,950 --> 00:01:54,190
No.

30
00:01:54,210 --> 00:01:56,370
So this is a special case that we have to handle.

31
00:01:56,950 --> 00:01:58,540
Not let us add something to this.

32
00:01:58,930 --> 00:02:03,850
Create a new node with a temporary pointer t and insert the value.

33
00:02:03,850 --> 00:02:11,240
Miguna says no loss to make such a good point on the city and bring last on the new node D.

34
00:02:11,680 --> 00:02:13,270
Now one more case I will show you.

35
00:02:13,720 --> 00:02:20,200
If suppose there are no nodes, then both the first and last two should be null.

36
00:02:20,530 --> 00:02:23,080
When there are no nodes initially they must be null.

37
00:02:23,290 --> 00:02:23,790
Yes.

38
00:02:24,130 --> 00:02:29,710
So then you are creating first node using a temporary pointer t and inserting a value.

39
00:02:29,720 --> 00:02:36,450
Then that time we should bring first upon this known as well as last upon this.

40
00:02:36,820 --> 00:02:42,130
So this is the special case that we have to take care of that to make both first and last point on that

41
00:02:42,130 --> 00:02:43,150
new node.

42
00:02:43,690 --> 00:02:48,310
Then afterwards you can go on creating the N and with the help of last point.

43
00:02:49,210 --> 00:02:53,370
So let me write on the code for doing all of this now.

44
00:02:53,410 --> 00:02:57,870
I will write on a function called Insert Last Insert Lost.

45
00:02:58,240 --> 00:03:02,500
So let us call the function as insert last.

46
00:03:02,830 --> 00:03:04,030
So insert last minutes.

47
00:03:04,030 --> 00:03:06,130
It will not ask where to insert it.

48
00:03:06,370 --> 00:03:06,720
Right.

49
00:03:07,030 --> 00:03:09,950
So just it will take the value that we want to insert.

50
00:03:09,970 --> 00:03:11,740
So let's take the value X.

51
00:03:14,200 --> 00:03:19,280
Now, dysfunction will insert a new node in the link that at the last.

52
00:03:19,720 --> 00:03:21,920
So let us see how we can do this one.

53
00:03:22,570 --> 00:03:26,340
First of all, create a new node with the help of ten point thirty.

54
00:03:26,380 --> 00:03:36,070
OK, node B, assign new node, new node is created, then assign the value X here, whatever the value

55
00:03:36,070 --> 00:03:36,870
that you are sending.

56
00:03:36,880 --> 00:03:43,550
So these data should be X and make these next as null.

57
00:03:43,620 --> 00:03:46,630
So and next number.

58
00:03:46,990 --> 00:03:49,760
So this is not so.

59
00:03:49,780 --> 00:03:50,860
A new node is already.

60
00:03:53,870 --> 00:03:59,600
New node is really now we have to look at the cases, are there any nodes in the link?

61
00:03:59,720 --> 00:04:00,830
So Linklaters empty.

62
00:04:00,830 --> 00:04:05,990
There are no known how we can know that a first or last, but any one of them is null.

63
00:04:06,230 --> 00:04:07,540
Then it means there are no known.

64
00:04:07,550 --> 00:04:08,390
This is the first node.

65
00:04:08,630 --> 00:04:12,140
So let us check that condition if forced.

66
00:04:12,290 --> 00:04:12,880
That is point.

67
00:04:12,890 --> 00:04:15,660
The first is equal to no means.

68
00:04:15,800 --> 00:04:24,020
There are no nodes in the linguist's then Mique first as well as lost as a new Naughtie.

69
00:04:24,230 --> 00:04:26,440
So it means I will create it here separately.

70
00:04:26,690 --> 00:04:32,250
Supposed is the first node then both Fosterville come here as well as last will come here.

71
00:04:32,570 --> 00:04:40,070
So this is the situation we have Handal if the link was already empty, otherwise the repeating steps.

72
00:04:40,220 --> 00:04:41,780
So always what we were doing.

73
00:04:41,780 --> 00:04:44,260
So what we were doing made last.

74
00:04:44,270 --> 00:04:50,630
The next point going to make last, the next point on D.

75
00:04:53,120 --> 00:05:00,290
Then move last to new know, move last to New North.

76
00:05:02,020 --> 00:05:02,500
Dean.

77
00:05:04,550 --> 00:05:05,600
This is a function.

78
00:05:07,810 --> 00:05:13,660
This is the insert function, which will always insert at the last and it will always insert a..

79
00:05:14,380 --> 00:05:18,210
There are no positions, so there is no question of invalid position.

80
00:05:18,640 --> 00:05:22,080
Always it will insert a node at the end of a linguist.

81
00:05:22,540 --> 00:05:26,860
If there are no laws than a linguist, then it will make that as first node of a linguist.

82
00:05:27,310 --> 00:05:32,970
Otherwise it will ensure that the last I will write on full function calls for creating this linguist.

83
00:05:33,280 --> 00:05:34,680
I'll just write the calls here.

84
00:05:35,470 --> 00:05:40,150
So first call, I will say insert last eight.

85
00:05:40,600 --> 00:05:42,800
So this is the first node eight.

86
00:05:42,820 --> 00:05:45,540
So both the first and last will be pointing here.

87
00:05:47,520 --> 00:05:52,270
This is the first one, no doubt this has been here, but I am not showing up here in the picture,

88
00:05:52,650 --> 00:05:57,300
then again, say insert lost values or three.

89
00:05:57,630 --> 00:06:03,500
So three will be created here, Anderson reporting on this one and last will be on this one.

90
00:06:05,180 --> 00:06:15,680
Then again, they insert last nine, so nine will be created, three will be pointing on this one and

91
00:06:15,680 --> 00:06:17,150
this becomes the last known.

92
00:06:18,940 --> 00:06:25,000
So in this way, you can go on calling this function as long as you want to insert the element as many

93
00:06:25,000 --> 00:06:27,610
elements you want, you can insert by calling this function.

94
00:06:27,940 --> 00:06:35,550
This function is for just inserting one node at the end of a list and it works even the link empty.

95
00:06:36,490 --> 00:06:40,280
So it always guarantees to insert some node in the list.

96
00:06:41,320 --> 00:06:45,640
There are no invalid indices because we are always inserting at the last.

97
00:06:46,240 --> 00:06:51,610
So by using this procedure, we can create a linked list and then we can perform all of operations on

98
00:06:51,610 --> 00:06:56,590
Linklaters like adding all the node, finding maximum, reversing a linked list and so on.

99
00:06:58,350 --> 00:07:03,060
So I'll be using this function in my program for creating linguist.

