1
00:00:00,210 --> 00:00:02,990
Our next topic is double standard.

2
00:00:03,000 --> 00:00:10,740
Q The Q So does the EU not dequeue double ended?

3
00:00:10,770 --> 00:00:12,960
Q It doesn't strictly follow people.

4
00:00:13,260 --> 00:00:14,370
It all depends on you.

5
00:00:14,370 --> 00:00:16,770
If you want, you can use it as a fee for.

6
00:00:17,930 --> 00:00:23,810
It can be implemented using <PERSON><PERSON> or it can be implemented using <PERSON><PERSON><PERSON><PERSON>, so I have taken an example

7
00:00:23,810 --> 00:00:25,120
of Uhry.

8
00:00:25,760 --> 00:00:27,500
Let us understand what is it?

9
00:00:28,430 --> 00:00:32,740
See if we implement a Q using <PERSON>ry, then we need two pointers.

10
00:00:32,850 --> 00:00:39,230
Frankenthaler Similarly, in the Lincolnesque we need to point different than red is used for insertion.

11
00:00:39,920 --> 00:00:42,470
Front is used for deletion.

12
00:00:44,210 --> 00:00:53,150
But in DECA, you can use both the pointers for both operations insertion as well as deletion.

13
00:00:53,540 --> 00:00:59,630
So the three pointer we can insert as well as delete and with three pointer we can insert as well as

14
00:00:59,630 --> 00:01:00,150
delete.

15
00:01:00,680 --> 00:01:02,570
So I'll prepare a table and show you here.

16
00:01:03,290 --> 00:01:10,400
See from this table you can understand in a cube find of use for deletion rate is used for insertion

17
00:01:10,400 --> 00:01:13,490
using front we cannot insert using red we can not delete.

18
00:01:14,000 --> 00:01:21,350
But when we say decode the cube fried is used for both insertion of deletion and red is also used for

19
00:01:21,350 --> 00:01:24,490
both insertion as well as how it is possible.

20
00:01:24,740 --> 00:01:32,270
I will demonstrate by inserting and deleting few elements I want to insert, so I should say from which

21
00:01:32,270 --> 00:01:33,740
side I want to insert.

22
00:01:34,370 --> 00:01:41,450
I right now Kulis MP and front is at minus one so I can insert from our side.

23
00:01:41,570 --> 00:01:43,730
So insert from red.

24
00:01:44,090 --> 00:01:48,950
If I insert from there then increment the pointer and insert a value.

25
00:01:49,910 --> 00:01:58,790
Then again, they want to insert using red, so move Red Pointer and insert the value and insert few

26
00:01:58,790 --> 00:01:59,420
values.

27
00:01:59,840 --> 00:02:02,360
So using rare, I have inserted few values.

28
00:02:03,780 --> 00:02:10,410
So using the transition as a time now using Reira, I want to delete, so OK, take out this value and

29
00:02:10,449 --> 00:02:15,150
agreement reached a point where it's possible to delete using a real pointer also.

30
00:02:15,150 --> 00:02:16,470
But it is not feasible.

31
00:02:16,800 --> 00:02:20,780
As I said, we are not strictly following people in the queue.

32
00:02:21,300 --> 00:02:23,160
Let me delete one more value using it here.

33
00:02:23,490 --> 00:02:28,480
So just take out this value and decrement rate so that's what the mission is done.

34
00:02:28,920 --> 00:02:32,240
So from rear end, I have shown you both in such an absolute condition.

35
00:02:32,610 --> 00:02:36,570
Let us insert from Frank there is no space here to insert.

36
00:02:36,570 --> 00:02:39,040
So I cannot insert from front can delete.

37
00:02:39,210 --> 00:02:40,180
Yes, I can delete.

38
00:02:40,380 --> 00:02:41,860
So how do you delete using front.

39
00:02:41,890 --> 00:02:43,670
So this is normal method my friend.

40
00:02:43,680 --> 00:02:46,410
Pointer and delete the delete one more.

41
00:02:46,620 --> 00:02:49,080
So move FrontPoint up and delete the value.

42
00:02:49,290 --> 00:02:51,660
So this is a regular method of deletion.

43
00:02:51,820 --> 00:02:53,370
I want to insert using front.

44
00:02:53,670 --> 00:02:59,360
So OK, insert the value here at the front pointer and the move front.

45
00:02:59,400 --> 00:03:00,700
That is decrement front.

46
00:03:00,810 --> 00:03:01,760
So move it back.

47
00:03:02,430 --> 00:03:10,580
I want to insert one more so insert the value and move friend by decriminalizing it so it is moved back

48
00:03:11,250 --> 00:03:11,410
now.

49
00:03:11,550 --> 00:03:15,550
Can insert anymore no friend at minus one so it cannot insert anymore.

50
00:03:15,570 --> 00:03:15,900
Now.

51
00:03:17,290 --> 00:03:25,090
So that's all we can insert using Frank as well as delete using front instead of using rear as well

52
00:03:25,090 --> 00:03:30,540
as to delete using it for both are allowed from both the sides, though not fearful.

53
00:03:30,550 --> 00:03:37,210
But if in your application, if you want, you can use it like a Java supports a BQ type of data structure

54
00:03:37,390 --> 00:03:43,510
that's there in Java, so it allows insertion, scrambled eggs, then there are some variations in the

55
00:03:43,510 --> 00:03:43,900
queue.

56
00:03:44,110 --> 00:03:47,400
So I will prepare a table and show you are here.

57
00:03:47,410 --> 00:03:51,310
I have written there are two types of restricted dequeue.

58
00:03:51,700 --> 00:03:53,740
What is important is that the.

59
00:03:55,090 --> 00:04:03,180
So in addition to inflation and deflation is allowed by using both the Bakir input operation is restricted.

60
00:04:03,520 --> 00:04:05,770
So who actually perform in Operation?

61
00:04:06,070 --> 00:04:13,670
Reyher So insurgent is allowed only using rare threat cannot insult, but deletion both Kangal.

62
00:04:14,800 --> 00:04:21,850
So input means insert insert is restricted, the next one is output restricted to humans, deletion

63
00:04:21,850 --> 00:04:22,560
is restricted.

64
00:04:22,990 --> 00:04:25,680
So whose job is to delete the job?

65
00:04:25,840 --> 00:04:30,050
So only who can delete Fernaldi, can delete material, cannot delete.

66
00:04:30,340 --> 00:04:33,380
What about the insertion board can perform insertion?

67
00:04:34,330 --> 00:04:35,350
So this is a variant.

68
00:04:35,350 --> 00:04:38,250
If you want, you can use this type of data structure also.

69
00:04:39,010 --> 00:04:47,620
So input restricted TQM output has to be, you know, writing the program using Uhry as well as lintels.

70
00:04:47,950 --> 00:04:50,290
So I don't have to show how it works on Linklaters.

71
00:04:50,290 --> 00:04:51,590
You can understand that.

72
00:04:51,880 --> 00:04:58,330
So it's a strange exercise to write a program for implementing double standard.

73
00:04:58,390 --> 00:05:04,600
Q So for operations you have the right that is insert from front and delete from front, insert from

74
00:05:04,600 --> 00:05:06,070
there and delete from that.

75
00:05:07,000 --> 00:05:08,140
So implement this one.

