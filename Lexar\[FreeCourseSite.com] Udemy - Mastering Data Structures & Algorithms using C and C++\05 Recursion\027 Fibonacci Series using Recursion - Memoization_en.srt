1
00:00:00,590 --> 00:00:02,700
Now, the topic is <PERSON><PERSON><PERSON><PERSON> series.

2
00:00:03,230 --> 00:00:10,190
So here we will learn what is happening to kids and then we will devise recursive definition and we

3
00:00:10,190 --> 00:00:14,010
will write a recursive function for that one.

4
00:00:14,330 --> 00:00:16,930
And finally, we will analyze that function.

5
00:00:17,840 --> 00:00:18,890
So let us talk.

6
00:00:20,550 --> 00:00:26,570
See, this is a fabulous subsidies, the zero to zero next term is one than one, then good and three

7
00:00:26,640 --> 00:00:27,670
and five and so on.

8
00:00:27,690 --> 00:00:29,250
So how we're paying these, Tom?

9
00:00:29,610 --> 00:00:36,120
Each Tom is obtained by adding previous to DOMS, so that is five plus eight is 13 and the storm is

10
00:00:36,120 --> 00:00:37,710
obtained by adding these two domes.

11
00:00:38,070 --> 00:00:39,820
So initially, what are the terms?

12
00:00:39,840 --> 00:00:41,380
These are the starting dumps.

13
00:00:41,840 --> 00:00:45,120
If the zero the dome then it is zero for the system.

14
00:00:45,130 --> 00:00:47,220
One then is one rust.

15
00:00:47,250 --> 00:00:53,570
Every other dome as obtained by adding previous to done so initially you must have two terms then all

16
00:00:53,580 --> 00:00:58,360
you can define like this that every time is converting previous times.

17
00:01:00,120 --> 00:01:15,000
So this can be defined mathematically of and can really finance Ferb of and minus two plus fizbo and

18
00:01:15,000 --> 00:01:27,060
minus one then and is greater than one if and as a zero or as one then four zero two zero four one.

19
00:01:27,060 --> 00:01:27,720
And this one.

20
00:01:29,600 --> 00:01:36,570
So if anybody is one down for this directly, one for zero, the values are directly zero for is equal

21
00:01:36,680 --> 00:01:40,330
to one answer, strictly one otherwise are the previous two dumps.

22
00:01:41,630 --> 00:01:43,210
So let me put it on the table.

23
00:01:46,370 --> 00:01:52,910
Now, this is no end, and this is in some ways I want to know what the Tom answer is.

24
00:01:52,910 --> 00:01:53,300
Fine.

25
00:01:53,600 --> 00:01:57,060
If I want to know seven to Tom, answer this 13.

26
00:01:57,620 --> 00:02:04,580
So first, let me write a recursive function for Fibonacci series based on this recurrence relation

27
00:02:04,580 --> 00:02:10,550
or or based on based on this recursive definition, I will write the function.

28
00:02:12,180 --> 00:02:16,760
Integer fape of integer nd.

29
00:02:17,970 --> 00:02:22,550
So here it says that if it is zero, return zero for this one dritan one.

30
00:02:22,800 --> 00:02:31,770
So it means if anything less than or equal to one are written and itself means if and as one return

31
00:02:31,770 --> 00:02:33,840
one zero return Siedel.

32
00:02:36,190 --> 00:02:38,200
Otherwise, Ripton.

33
00:02:40,140 --> 00:02:48,090
Think of any minus two plus feet of and the minus one.

34
00:02:50,110 --> 00:02:51,550
The very definition.

35
00:02:52,950 --> 00:03:00,840
That's all if you want to find out any time, like seven to or 10, the Tom or any Tom, you call off,

36
00:03:01,140 --> 00:03:04,150
then you get turned Tom off, will not you?

37
00:03:04,170 --> 00:03:07,620
Chris golfBall seven, you get seven.

38
00:03:07,670 --> 00:03:10,860
Tom will come back to that function afterwards.

39
00:03:10,860 --> 00:03:16,070
For first of all, let us write an iterative version that is a function using loop.

40
00:03:16,410 --> 00:03:21,960
So let me write our Fibonacci function using loop integer FEBA.

41
00:03:22,080 --> 00:03:24,570
I'll give the name once again C.M.A.

42
00:03:24,570 --> 00:03:31,000
I'm using here then if the number is less than or equal to 100 and the number itself.

43
00:03:31,020 --> 00:03:38,640
So here if and there's less than or equal to one then written and it's.

44
00:03:41,460 --> 00:03:52,200
But otherwise, I need to dance to add them one by one, so integer don't zero as zero and dumb one

45
00:03:52,410 --> 00:03:53,160
as one.

46
00:03:53,410 --> 00:03:55,950
And also I have to add the value.

47
00:03:55,960 --> 00:04:02,100
So for addition, I will take some as so I have three variables.

48
00:04:02,430 --> 00:04:09,780
Now, using these three variables, I will write on a loop for finding out and the Dom S..

49
00:04:09,780 --> 00:04:15,530
Suppose I have to find the seven atoms then all they it on zero is available down.

50
00:04:15,540 --> 00:04:16,709
One is also available.

51
00:04:16,920 --> 00:04:18,570
Then I have to obtain this storm.

52
00:04:18,870 --> 00:04:22,300
One, two, three, four, five, six.

53
00:04:22,740 --> 00:04:29,370
So actually I should generate only six domme so I should perform additions only six time.

54
00:04:29,640 --> 00:04:30,900
Obviously I have these two.

55
00:04:30,900 --> 00:04:32,140
Dong's right.

56
00:04:32,430 --> 00:04:35,960
So two, three, four, five, six, seven NetSol.

57
00:04:36,180 --> 00:04:41,130
So I should start from second time onwards and stop at seven Tatong.

58
00:04:41,580 --> 00:04:43,530
So whichever time I want, I should stop that.

59
00:04:43,530 --> 00:04:45,410
So I should start from two onwards.

60
00:04:45,750 --> 00:04:48,660
So let me run a loop for.

61
00:04:50,350 --> 00:04:58,700
I sign, so I need one more variable, I and I is less than or equal to N and I plus plus.

62
00:04:58,840 --> 00:05:04,050
So this is for repeating the procedure for and the minus one times for.

63
00:05:04,060 --> 00:05:07,120
I'm starting from two to n not every time.

64
00:05:07,120 --> 00:05:12,580
What I have to do is I should be assigned with 30 zero plus at one.

65
00:05:13,120 --> 00:05:16,020
So this storm should be obtained by adding these to the DTG.

66
00:05:16,030 --> 00:05:18,920
Twenty one then after getting the same.

67
00:05:18,970 --> 00:05:21,640
Now this should become disillusioned, it should become T1.

68
00:05:21,640 --> 00:05:22,990
So forgetting the next storm.

69
00:05:23,260 --> 00:05:27,430
So I should change this value to zero and this I should make it as T1.

70
00:05:27,700 --> 00:05:37,420
So the variable should be made as a T one and T one should be made as s and repeat this procedure.

71
00:05:37,660 --> 00:05:41,080
So finally a result will be in S.

72
00:05:46,620 --> 00:05:50,420
So this is an Israeli version, so it's a diversion.

73
00:05:50,460 --> 00:05:54,400
What is the time taken by this algorithm or this procedure?

74
00:05:54,810 --> 00:05:59,940
This procedure takes let us take it, this statement will execute for one time and one time.

75
00:06:00,360 --> 00:06:04,660
This loop is written as one plus and plus one, but starting from two onwards.

76
00:06:04,660 --> 00:06:11,620
So let's say it is only 10 and the things inside will be four and minus one times and minus one times.

77
00:06:11,880 --> 00:06:12,780
And this is one.

78
00:06:13,170 --> 00:06:16,080
So total is one, two, three, four, four.

79
00:06:16,080 --> 00:06:18,180
End and minus one.

80
00:06:18,180 --> 00:06:18,800
Minus one one.

81
00:06:18,850 --> 00:06:20,390
So this plus one gets canceled.

82
00:06:20,400 --> 00:06:21,030
So this four.

83
00:06:21,030 --> 00:06:23,510
And so the time is for any order.

84
00:06:23,550 --> 00:06:26,430
And basically we wanted to know the degree.

85
00:06:26,430 --> 00:06:27,380
So to sort of offend.

86
00:06:27,900 --> 00:06:30,260
So we don't want exact formula.

87
00:06:30,630 --> 00:06:31,490
We want the degree.

88
00:06:31,620 --> 00:06:35,940
You can see that there is one for loop and for loop will repeat for End-Time.

89
00:06:35,940 --> 00:06:40,090
So the time this order and it's obvious from the function.

90
00:06:40,830 --> 00:06:43,800
So this algorithm takes a lot of time.

91
00:06:45,660 --> 00:06:53,460
So already we have it, it's a diversion now let us go back to recursive version and analyze that one

92
00:06:53,730 --> 00:06:59,270
and see how much time it takes and if any improvement required, we will do some improvements.

93
00:06:59,280 --> 00:07:03,600
And so I removed this one and then we will analyze that function.

94
00:07:03,630 --> 00:07:09,420
Now, let us stress this recursive function for five of five.

95
00:07:10,860 --> 00:07:12,600
If it passed five, what happens?

96
00:07:12,870 --> 00:07:14,640
Five is not less than one.

97
00:07:14,640 --> 00:07:16,140
So it will make two calls.

98
00:07:16,320 --> 00:07:17,580
So we know what the function is.

99
00:07:17,580 --> 00:07:18,200
Very simple.

100
00:07:18,420 --> 00:07:20,810
So I will just go on tracing this one.

101
00:07:20,820 --> 00:07:25,350
So here it will call five of three and there it will qualifiable for afterwards.

102
00:07:25,350 --> 00:07:25,950
Not now.

103
00:07:26,220 --> 00:07:28,890
First, this call will be made as this is three.

104
00:07:28,890 --> 00:07:34,830
So it will make a call for football for one and two afterwards.

105
00:07:34,830 --> 00:07:35,370
Not now.

106
00:07:35,670 --> 00:07:36,450
If it was one.

107
00:07:36,450 --> 00:07:37,110
This is the end.

108
00:07:37,200 --> 00:07:38,780
One end is less than equal to one.

109
00:07:38,790 --> 00:07:39,390
So yes.

110
00:07:39,390 --> 00:07:40,460
And this one now.

111
00:07:40,770 --> 00:07:42,450
So this will not call further.

112
00:07:42,630 --> 00:07:47,030
And the result, this one then here it has two calls for both two.

113
00:07:47,250 --> 00:07:55,110
So I'm just writing here for then if it was two further calls, fizbo of zero and feeB of one three

114
00:07:55,110 --> 00:07:56,640
one zero gives the other zero one.

115
00:07:56,640 --> 00:07:59,550
This gives the other one and they are added to that are done.

116
00:07:59,550 --> 00:08:02,130
And this is added as it is written on here.

117
00:08:02,130 --> 00:08:04,920
It will call fizbo four.

118
00:08:05,220 --> 00:08:15,140
So for the people for this is of two and three three three two five zero and of one.

119
00:08:15,570 --> 00:08:22,530
So these results are obtained 011 and they are added result this written and on this side of three again

120
00:08:22,530 --> 00:08:26,970
this is figure of one and off to then type of two.

121
00:08:27,220 --> 00:08:30,930
This is above zero and the figure of one.

122
00:08:31,500 --> 00:08:34,860
So here the call is so here the result is zero one.

123
00:08:34,860 --> 00:08:41,159
This is zero and this is one and added on added return added and final edition is done.

124
00:08:41,850 --> 00:08:48,030
So this is how our three looks like our recursion tree looks like four or five.

125
00:08:48,660 --> 00:08:54,330
If you look at the functions in the order in which they are called, this is the first function call

126
00:08:54,330 --> 00:09:00,390
in the second call and the third call, then forward call, fifth column, sixth call, then seven,

127
00:09:00,750 --> 00:09:03,000
eight, nine, ten.

128
00:09:03,210 --> 00:09:08,910
Then this is 11 call 12, 13, 14, 15.

129
00:09:09,360 --> 00:09:11,430
So forceable five.

130
00:09:11,610 --> 00:09:13,320
Number of calls are 15.

131
00:09:14,160 --> 00:09:17,160
Calls are 15, then three or four.

132
00:09:17,160 --> 00:09:18,170
How many calls are there.

133
00:09:18,180 --> 00:09:21,630
One, two, three, four, five, six, seven, eight, nine, nine calls.

134
00:09:21,630 --> 00:09:23,240
Are they then three of three.

135
00:09:23,250 --> 00:09:24,200
How many calls are there.

136
00:09:24,210 --> 00:09:27,270
One, two, three, four, five, five calls are there.

137
00:09:27,690 --> 00:09:31,860
So no, if we try to observe any pattern, there is no pattern here.

138
00:09:32,280 --> 00:09:33,000
This is five.

139
00:09:33,000 --> 00:09:34,890
So this is fifteen five.

140
00:09:34,890 --> 00:09:35,820
Three fifteen.

141
00:09:36,420 --> 00:09:37,830
Then for three it should be twelve.

142
00:09:38,870 --> 00:09:39,200
Then.

143
00:09:40,720 --> 00:09:48,080
This is forward into four or two in the four plus one, so we cannot figure out what pattern it is following.

144
00:09:48,400 --> 00:09:55,510
So if you want to know the time taken by this one, then assume that this off and the minus two is also

145
00:09:55,510 --> 00:09:56,570
for your boyfriend, minus one.

146
00:09:56,890 --> 00:10:01,630
So this is calling itself four two times and minus one and minus one and minus one.

147
00:10:01,930 --> 00:10:06,790
So many functions calling two times by a reduced value of one and the minus one.

148
00:10:07,100 --> 00:10:10,060
Then the timers order of Cooper and.

149
00:10:11,580 --> 00:10:20,580
So the time taken by this function is to pour in approximately Dupa, and so if you observe the previous

150
00:10:20,580 --> 00:10:29,470
function, iterative version was order of and and recursive version is too poor and too much time consuming.

151
00:10:30,720 --> 00:10:36,170
Now, is there any method to make it faster than to pour in?

152
00:10:36,660 --> 00:10:39,860
Let us observe now this analysis is important.

153
00:10:40,710 --> 00:10:48,000
If you see the tracing that is regression tree, the value of every function after is called here.

154
00:10:48,270 --> 00:10:50,220
And again, it is called here.

155
00:10:51,510 --> 00:10:53,610
F2 is called here first time.

156
00:10:53,830 --> 00:10:56,700
Then again, if two is called, then again if two is called.

157
00:10:56,880 --> 00:11:01,110
So similarly s01 F1 they are also called multiple times.

158
00:11:01,530 --> 00:11:06,680
So a recursive function is calling itself multiple times for the same values.

159
00:11:07,620 --> 00:11:10,560
So such a recursive function is called as.

160
00:11:13,660 --> 00:11:15,580
Excessive regulation.

161
00:11:17,270 --> 00:11:25,520
So, yes, this Fibonacci function is all excessive recursion, because it will call itself multiple

162
00:11:25,520 --> 00:11:27,890
times for the same parameters.

163
00:11:28,880 --> 00:11:30,290
This is a problem in this fund.

164
00:11:30,680 --> 00:11:39,890
So is there any way to avoid the excessive calls and just make a call only once and utilize it in further

165
00:11:39,890 --> 00:11:40,530
comments?

166
00:11:41,420 --> 00:11:43,770
So, yes, what is the matter?

167
00:11:43,790 --> 00:11:51,200
Let us devise a method if you want a function to retain the these values so that they can be utilized

168
00:11:51,200 --> 00:11:56,990
here, then for retaining the values, you have to take the help of static variable, or you can also

169
00:11:56,990 --> 00:11:58,220
take global variables.

170
00:11:58,880 --> 00:12:05,660
Let us take one uhry, maybe a static or globality, whatever you want, you can take it.

171
00:12:12,480 --> 00:12:18,540
Index zero, one, two, three, four, five, six times higher than the one you can take it, so just

172
00:12:18,540 --> 00:12:20,530
have to concentrate to be one up to five.

173
00:12:21,570 --> 00:12:23,710
Now, how to write a.

174
00:12:25,220 --> 00:12:31,070
Now, how to modify this algorithm to make it faster, so I remove fuel tanks from board and show you

175
00:12:31,280 --> 00:12:33,050
this global area or study.

176
00:12:33,740 --> 00:12:39,770
Let us initialize it with the minus one, minus one, since we don't know the value minus one.

177
00:12:41,790 --> 00:12:48,400
Now, let us stress it once again by taking the help of this global or static threat.

178
00:12:48,790 --> 00:12:54,080
First, I will show you that Preysing what modifications we can do, then we will modify our function.

179
00:12:54,780 --> 00:12:55,350
Let us see.

180
00:12:56,340 --> 00:13:01,910
golfBall five, do we really need to call it a check for five minus one?

181
00:13:01,920 --> 00:13:03,380
We don't know the answer for this.

182
00:13:03,600 --> 00:13:04,760
OK, call this one.

183
00:13:05,040 --> 00:13:08,210
So it is called it will make two calls.

184
00:13:08,250 --> 00:13:11,970
So the first call, if it was three, do we really need to make three more.

185
00:13:11,990 --> 00:13:12,290
Three?

186
00:13:12,300 --> 00:13:14,310
Yes, we have to because we don't know the answer.

187
00:13:14,700 --> 00:13:16,200
Then this will make it more fun.

188
00:13:17,220 --> 00:13:19,190
But we need to make a call for both.

189
00:13:19,230 --> 00:13:21,420
One guess because it is minus one.

190
00:13:21,720 --> 00:13:22,690
What can make the call?

191
00:13:22,920 --> 00:13:25,120
So once you make the call, the result is one.

192
00:13:25,260 --> 00:13:30,300
So once you got the result, because there's a small function and it's less than equal to one on one,

193
00:13:30,300 --> 00:13:32,320
so return zero or one, whatever it is.

194
00:13:32,760 --> 00:13:33,890
So this is one.

195
00:13:34,140 --> 00:13:35,250
So we got the answer.

196
00:13:35,400 --> 00:13:36,840
So change this to one.

197
00:13:37,770 --> 00:13:40,140
Now we know the answer for five off one.

198
00:13:40,590 --> 00:13:41,160
Yes.

199
00:13:41,790 --> 00:13:42,570
Then go back.

200
00:13:43,170 --> 00:13:45,030
Do we have to make a call to flip off two?

201
00:13:45,030 --> 00:13:45,450
Yes.

202
00:13:45,450 --> 00:13:47,160
We don't know the answer then.

203
00:13:47,160 --> 00:13:49,130
Do we have to make a call to triple zero?

204
00:13:49,140 --> 00:13:49,380
Yes.

205
00:13:49,380 --> 00:13:51,650
We don't know the answer to this function.

206
00:13:51,660 --> 00:13:53,130
So this result will be zero.

207
00:13:53,550 --> 00:13:56,220
But this one then it is less than equal to one return the same value.

208
00:13:56,580 --> 00:13:58,120
So zero return zero sum.

209
00:13:58,160 --> 00:13:59,280
Modify this also.

210
00:13:59,760 --> 00:14:06,430
Now, in order for those to then make a call for this one, do we need to make a call for this check?

211
00:14:06,600 --> 00:14:07,020
No, no.

212
00:14:07,020 --> 00:14:07,890
We don't answer.

213
00:14:08,190 --> 00:14:09,360
Don't call us.

214
00:14:10,570 --> 00:14:11,420
It's not clear.

215
00:14:11,940 --> 00:14:15,860
Yes, so avoid the call, because all of you have got the answer here.

216
00:14:16,030 --> 00:14:21,370
So this call is skipped, then I have to get an answer for this one.

217
00:14:21,380 --> 00:14:23,050
This was zero and this was one.

218
00:14:23,050 --> 00:14:24,010
So this is zero.

219
00:14:24,010 --> 00:14:25,240
Plus one is one.

220
00:14:25,450 --> 00:14:26,620
So this one's for this one.

221
00:14:26,920 --> 00:14:28,270
So modify this one.

222
00:14:28,270 --> 00:14:29,500
We Noonans for this.

223
00:14:30,040 --> 00:14:33,450
Then we got this answer one and this answer one plus one.

224
00:14:33,700 --> 00:14:35,100
So this is two.

225
00:14:35,290 --> 00:14:36,130
So we got Don.

226
00:14:36,130 --> 00:14:41,410
So that is to then let us go on that site, three or four.

227
00:14:41,770 --> 00:14:44,070
Do we know if it was for chicken?

228
00:14:44,200 --> 00:14:45,130
No, we don't know.

229
00:14:45,370 --> 00:14:46,780
So we have to call this one.

230
00:14:46,780 --> 00:14:50,530
So if you call this, there are two calls inside if you want to do.

231
00:14:50,530 --> 00:14:51,450
Benoit Yeah.

232
00:14:51,460 --> 00:14:53,890
Benoit, don't call this one.

233
00:14:53,890 --> 00:14:55,150
So don't call this.

234
00:14:55,150 --> 00:14:58,510
All sorts of other calls will also be avoided.

235
00:14:58,900 --> 00:15:05,080
Then the reason we know how much of this and this one, if you want to, is and then football three,

236
00:15:05,080 --> 00:15:06,810
shall we call it check it.

237
00:15:07,120 --> 00:15:10,030
No, we know the answer, so don't call this one.

238
00:15:10,030 --> 00:15:13,690
So then falling calls will also be avoided.

239
00:15:13,900 --> 00:15:18,760
And answer for this one is how much to then perform edition one plus two.

240
00:15:18,790 --> 00:15:24,790
This is three novenas answer for the side also that said also plus so this becomes two plus three.

241
00:15:24,790 --> 00:15:25,600
That is five.

242
00:15:25,630 --> 00:15:29,390
So this I did not read this so no this is five.

243
00:15:29,950 --> 00:15:31,270
Know the answer for all.

244
00:15:32,590 --> 00:15:34,630
So these calls are avoided.

245
00:15:34,780 --> 00:15:42,610
So if you remove those calls and check one, two, three, four, five, six.

246
00:15:42,820 --> 00:15:48,580
So it is making just six of function calls for five, six calls.

247
00:15:48,580 --> 00:15:54,430
Reason five, four, three, two, one and zero is also included so far.

248
00:15:54,460 --> 00:15:58,750
And how many calls and plus one that is end to zero.

249
00:15:59,050 --> 00:16:05,210
So the time taken by this method will be outdraw and so time is reduced.

250
00:16:05,260 --> 00:16:06,550
So just no use for that.

251
00:16:06,880 --> 00:16:13,090
That function, if you analyze it, will take outdraw to power and that is exponential.

252
00:16:13,090 --> 00:16:14,650
I did not analyze it thoroughly.

253
00:16:14,650 --> 00:16:18,400
Just have told you that it takes to board and time approximately.

254
00:16:18,700 --> 00:16:20,590
There is no problem to board and it's inoperable.

255
00:16:21,340 --> 00:16:29,020
But now by storing the result of the function in an area, we have reduced the excessive calls and the

256
00:16:29,020 --> 00:16:35,020
time is less than two percent and it has reduced down to just outdraw.

257
00:16:35,020 --> 00:16:39,640
And so a big change in the time complexity of Fibonacci function.

258
00:16:40,060 --> 00:16:48,340
So this reduction in time is because of storing the value was in early holding the reserves so that

259
00:16:48,580 --> 00:16:50,220
avoid excessive call.

260
00:16:50,560 --> 00:16:58,630
So this approach is called as memorisation, storing the results of the function call so that they can

261
00:16:58,630 --> 00:17:03,220
be utilized again when we need the same call or avoiding excessive calls.

262
00:17:03,550 --> 00:17:07,480
This approach is called as a memorisation means storing the results.

263
00:17:08,260 --> 00:17:12,490
So this is applicable here in Fibonacci series.

264
00:17:12,700 --> 00:17:15,160
So I have given introduction of memorisation.

265
00:17:15,160 --> 00:17:18,190
So this example, suitable example for memorisation.

266
00:17:18,819 --> 00:17:24,760
Now, based on this memorisation, I have to modify that function so I will remove the function and

267
00:17:24,760 --> 00:17:28,540
write a better function that is using memorisation.

268
00:17:28,840 --> 00:17:29,950
Let us write a function.

269
00:17:31,180 --> 00:17:37,180
Suppose there is some global effort which is initialized by some function that is a main function on

270
00:17:37,180 --> 00:17:46,510
minus one, not fill in min function now Fibonacci function, Fibonacci function the parameter and and

271
00:17:46,510 --> 00:17:56,530
if and is less than equal to one then in f also n store the value end and also return.

272
00:17:56,530 --> 00:18:02,200
And so if you observe the previous function was having just written and but now this function is also

273
00:18:02,200 --> 00:18:12,750
storing the resulting global array of four memorisation it is storing in f then and spot ls only are

274
00:18:12,760 --> 00:18:13,240
blindly.

275
00:18:13,240 --> 00:18:18,640
We were making two functions called that is for both and minus two and minus one but Novacek and then

276
00:18:18,640 --> 00:18:19,330
make it call.

277
00:18:19,660 --> 00:18:22,080
If value is not known then we'll make a call.

278
00:18:22,420 --> 00:18:33,360
So if F of and minus two is minus one means if you don't know the value then F of and minus two you

279
00:18:33,730 --> 00:18:36,620
get the result of football and minus two.

280
00:18:36,640 --> 00:18:38,070
So here we will make a call.

281
00:18:38,500 --> 00:18:45,760
Similarly, if we don't know the value of above and minus one and it is minus one that we don't know,

282
00:18:46,150 --> 00:18:53,310
then make a call for football and minus one and two, the result in F and minus one.

283
00:18:53,830 --> 00:19:00,580
Now we have two values then simplification addition of these two values that is free of and minus two

284
00:19:01,150 --> 00:19:04,030
plus feebles and minus one.

285
00:19:07,700 --> 00:19:16,040
So this version is that memorisation assume that that globality is initialise that minus one somewhere

286
00:19:16,040 --> 00:19:19,170
inside the main function before the function is utilized.

287
00:19:19,430 --> 00:19:21,410
So that's all about Fibonacci.

288
00:19:21,790 --> 00:19:26,150
We have learned that it can be done using iteration and using recursion.

289
00:19:26,150 --> 00:19:27,710
And the time is sort of to part.

290
00:19:27,710 --> 00:19:31,250
And then we have reduced the time by using memorisation.

291
00:19:32,550 --> 00:19:33,020
Before.

