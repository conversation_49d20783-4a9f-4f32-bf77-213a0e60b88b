1
00:00:00,360 --> 00:00:07,140
In the whiteboard lecture, we have to discuss about parameter passings, so let us have a demonstration

2
00:00:07,140 --> 00:00:08,630
for those parameter passing.

3
00:00:09,570 --> 00:00:15,060
See, this is the example program which I have used in the demonstration for functions.

4
00:00:15,600 --> 00:00:18,420
And this function is for adding two numbers.

5
00:00:19,690 --> 00:00:29,260
Which Paramatta passing method this is using see the arguments are A and and B, these are normal detailer

6
00:00:29,260 --> 00:00:30,070
variables.

7
00:00:30,460 --> 00:00:34,210
So this is called by a value mechanism.

8
00:00:34,990 --> 00:00:37,250
The parameter passing is called by value.

9
00:00:37,660 --> 00:00:46,320
So what happens here is the value of <PERSON><PERSON> and the value of NO2 will be copied in and B respectively.

10
00:00:47,230 --> 00:00:56,500
So values are passed here and this type of parameter parsing, if any changes are done to this formal

11
00:00:56,500 --> 00:01:01,190
parameters, that will not change actual parameters.

12
00:01:02,260 --> 00:01:03,120
I will explain you.

13
00:01:03,140 --> 00:01:03,980
What does it mean?

14
00:01:04,690 --> 00:01:09,190
So for explanation, I will to comment on this.

15
00:01:09,220 --> 00:01:12,780
OK, return I don't have so I will just return zero.

16
00:01:13,480 --> 00:01:15,770
Leave it not here.

17
00:01:15,790 --> 00:01:16,320
I will show you.

18
00:01:17,080 --> 00:01:19,600
I will make changes in this parameter.

19
00:01:19,640 --> 00:01:31,420
E so value of No one is copied in a but inside this function and changing a village to change number

20
00:01:31,430 --> 00:01:32,410
one also.

21
00:01:33,510 --> 00:01:39,850
No inculpate value, this actual parameter will not change, it will change.

22
00:01:40,290 --> 00:01:41,370
This will change.

23
00:01:41,640 --> 00:01:49,540
If I print this a here, then you can see that the value leverne will be printed, because from here

24
00:01:49,560 --> 00:01:51,830
in Washington, that is number one.

25
00:01:52,170 --> 00:01:58,050
It goes into a here and C++ then is printed.

26
00:01:58,290 --> 00:01:59,510
Leverne will be printed.

27
00:01:59,760 --> 00:02:03,150
But here I will come on this and before this I will print.

28
00:02:03,960 --> 00:02:06,990
No, just check this.

29
00:02:06,990 --> 00:02:09,630
No one will still remain 10.

30
00:02:09,990 --> 00:02:11,340
This proves that.

31
00:02:11,550 --> 00:02:20,760
This proves that when the value of a that is formal parameters modified, it is not reflected in actual

32
00:02:20,760 --> 00:02:21,810
parameters.

33
00:02:22,170 --> 00:02:23,370
Let us run and check.

34
00:02:23,820 --> 00:02:25,320
Oh there is no space.

35
00:02:25,320 --> 00:02:27,780
So I should add a space here.

36
00:02:28,170 --> 00:02:30,720
So I will give EnerDel before printing.

37
00:02:30,720 --> 00:02:31,270
No.

38
00:02:33,360 --> 00:02:42,220
Yes, see this the value of a and does the value of No one, no one is still an only right value of

39
00:02:42,220 --> 00:02:44,210
it has changed, but not no one.

40
00:02:44,550 --> 00:02:45,570
That is the meaning.

41
00:02:45,570 --> 00:02:50,660
If the formal parameters are change than actual parameters will not be changed.

42
00:02:51,180 --> 00:02:53,310
This is in Kaliber value.

43
00:02:53,580 --> 00:02:55,830
Then the question is when to use Kaliber value.

44
00:02:55,980 --> 00:03:03,080
Golby value is used when you want a function to process something and return the result.

45
00:03:03,630 --> 00:03:06,440
It should take the values and return the result.

46
00:03:06,450 --> 00:03:06,900
That's it.

47
00:03:07,200 --> 00:03:09,450
So this is a suitable function.

48
00:03:09,450 --> 00:03:16,400
Adding two numbers are finding maximum of numbers are finding greatest of any numbers or maximum of

49
00:03:16,440 --> 00:03:20,520
numbers or checking a number is a palindrome or not.

50
00:03:20,700 --> 00:03:27,990
So these type of functions, which will take some parameter and return the result when it is not modifying

51
00:03:27,990 --> 00:03:34,830
actual parameters, it should return result even function is modifying the actual parameters and then

52
00:03:34,830 --> 00:03:39,110
it may or may not send some results.

53
00:03:39,570 --> 00:03:42,050
It may not be mandatory for it to send the results.

54
00:03:42,140 --> 00:03:43,400
Depends on the situation.

55
00:03:43,890 --> 00:03:48,540
So question arises in what situations this is used called by value.

56
00:03:48,720 --> 00:03:55,110
So probably should be used when a function has to just process that is, do computation get some results

57
00:03:55,530 --> 00:03:57,950
and return those results.

58
00:03:58,290 --> 00:04:01,300
So then we should use callback value.

59
00:04:01,740 --> 00:04:03,360
So this is a suitable function.

60
00:04:03,990 --> 00:04:08,530
Olate so I have explained about Colpeper value and when to use it.

61
00:04:09,180 --> 00:04:15,450
Now let us look at an example of Golby address called by actors.

62
00:04:15,750 --> 00:04:17,120
So have to change the code.

63
00:04:17,370 --> 00:04:20,779
I will write on the function then I will explain you here.

64
00:04:20,779 --> 00:04:26,150
I have an example of Soire function that is using Coleby address.

65
00:04:26,550 --> 00:04:34,140
So first of all, I'll explain you the syntax and then then to use a callback address.

66
00:04:34,770 --> 00:04:37,320
Cities in Texas called my address.

67
00:04:37,320 --> 00:04:41,100
The parameters should be of type pointers.

68
00:04:41,510 --> 00:04:49,410
They take pointers and when you call a function, we purpose the address of actual parameters.

69
00:04:49,740 --> 00:04:56,040
The address of actual parameters are and that are taken in formal parameters and the formal parameters

70
00:04:56,040 --> 00:04:56,790
are pointers.

71
00:04:57,210 --> 00:05:02,440
So these pointers will be indirectly accessing these values itself.

72
00:05:02,490 --> 00:05:03,300
Number one.

73
00:05:03,300 --> 00:05:04,800
And number two, that is number one.

74
00:05:04,800 --> 00:05:05,330
And number two.

75
00:05:05,610 --> 00:05:09,180
So if you see this, this is the logic of SLAPP, right?

76
00:05:09,180 --> 00:05:11,310
Logic of SLAPP and these are pointers.

77
00:05:11,320 --> 00:05:18,350
So I have to use the reference, Englebert, that this is TotEx and Starlite for accessing the value.

78
00:05:18,630 --> 00:05:22,380
So start Ximenes that will access Namwon and start alignment's.

79
00:05:22,380 --> 00:05:26,910
It will access them to Disvalue itself, not after Collingsworth function.

80
00:05:26,910 --> 00:05:33,630
If I bring the value of number one and number two, they will modify because of this function has modified

81
00:05:33,630 --> 00:05:35,760
these actual parameters.

82
00:05:36,120 --> 00:05:39,690
Number one is ten, it will become fifteen and number two is 15.

83
00:05:39,690 --> 00:05:40,710
It will become ten.

84
00:05:41,220 --> 00:05:46,220
So if you see the values here, you here, you will get fifteen and here you will get ten.

85
00:05:46,800 --> 00:05:51,960
So Incoll address the addresses of actual parameters are passed.

86
00:05:52,260 --> 00:05:59,640
They are taken in pointers and the pointers can indirectly access actual parameters using their addresses.

87
00:06:00,050 --> 00:06:03,090
Right now I will execute it and then I will explain.

88
00:06:03,090 --> 00:06:06,860
You see, first number 15, second number ten.

89
00:06:07,350 --> 00:06:10,140
So this is 15 and this is ten.

90
00:06:10,440 --> 00:06:12,810
Their values have change, right?

91
00:06:13,110 --> 00:06:15,430
It has swept the value, the chain, the value.

92
00:06:16,260 --> 00:06:18,650
So this is called my address.

93
00:06:19,290 --> 00:06:22,050
Now, then, to use in what situations we should use.

94
00:06:22,410 --> 00:06:26,130
See, this function is modifying actual parameters.

95
00:06:26,340 --> 00:06:27,960
So it is not returning anything.

96
00:06:27,960 --> 00:06:29,310
We don't have to return anything.

97
00:06:29,670 --> 00:06:35,940
Whatever the results of this function, they are directly reflected inside actual parameters.

98
00:06:36,240 --> 00:06:38,130
So it doesn't have to return anything.

99
00:06:38,580 --> 00:06:40,940
In some situation, it may return something.

100
00:06:41,340 --> 00:06:48,090
So then you want a function to directly work upon the actual parameters, then go for GOLBY actors.

101
00:06:48,710 --> 00:06:50,670
Then one more situation here.

102
00:06:50,670 --> 00:06:57,330
You can see that Suay function is modifying two variables number one and number two.

103
00:06:57,930 --> 00:07:00,180
But a function can return only one value.

104
00:07:00,690 --> 00:07:07,590
We want two results from Suay function, so better use collaborator's Northpoint.

105
00:07:07,980 --> 00:07:16,320
If you have some variable and you want the same variable to be modified, then Sanitas address Senate

106
00:07:16,320 --> 00:07:17,220
by address.

107
00:07:17,370 --> 00:07:18,590
Galba address.

108
00:07:19,140 --> 00:07:22,680
So here are we are we are having just integer type variable.

109
00:07:22,920 --> 00:07:31,000
Suppose you have a structure type variable or an object in C++, then it's better to send it by atus

110
00:07:31,020 --> 00:07:32,770
collaborators like.

111
00:07:32,920 --> 00:07:40,810
Next parameter is called by reference, so this is supported in C++ and first of all, we will see the

112
00:07:40,810 --> 00:07:43,830
syntax, then we will see when to use them.

113
00:07:44,050 --> 00:07:47,380
So the syntax is here in formal parameters.

114
00:07:47,410 --> 00:07:53,700
You have to write ampersand instead of Asharq, so they become references and references.

115
00:07:53,710 --> 00:08:00,290
You don't have to write a star for accessing the data and then remove this ampersand from here.

116
00:08:00,750 --> 00:08:04,200
This is the change in the syntax already have shown you on whiteboard.

117
00:08:04,600 --> 00:08:11,260
So the syntax is simple, but the mechanism and the working and the performance the same as Culburra

118
00:08:11,260 --> 00:08:11,640
address.

119
00:08:11,860 --> 00:08:19,260
But one more thing I have to say in C++, the concept of references, it is a nickname of a variable,

120
00:08:19,360 --> 00:08:22,330
so it should directly represent no one.

121
00:08:22,450 --> 00:08:23,470
It is not a pointer.

122
00:08:23,470 --> 00:08:24,760
It will not hold an address.

123
00:08:25,120 --> 00:08:26,600
X means no.

124
00:08:27,040 --> 00:08:29,920
So this is possible with the help of a line function.

125
00:08:30,190 --> 00:08:32,640
So C++ supports a line function.

126
00:08:32,890 --> 00:08:40,809
So this is function may become in line function, not it will compulsorily it will not become a line

127
00:08:40,809 --> 00:08:41,260
function.

128
00:08:41,620 --> 00:08:43,630
This may become in line function.

129
00:08:43,809 --> 00:08:45,760
It all depends on the compiler.

130
00:08:46,000 --> 00:08:46,340
Right.

131
00:08:46,900 --> 00:08:48,940
It all depends on the compiler on the whiteboard.

132
00:08:48,950 --> 00:08:54,190
I have said that this may become a inline function, but it is not mandatory.

133
00:08:54,190 --> 00:08:58,570
Always, it depends how a compiler implements references.

134
00:08:59,020 --> 00:09:01,900
Conceptually, references are nicknames.

135
00:09:02,110 --> 00:09:03,400
They are not pointers.

136
00:09:03,400 --> 00:09:06,880
Right, but compiler may implement them as a pointer.

137
00:09:07,180 --> 00:09:10,330
How a compiler implements we can't see.

138
00:09:10,780 --> 00:09:17,260
But there are two possibilities that a compiler can convert its function as an inline function and copy

139
00:09:17,260 --> 00:09:18,310
the entire code here.

140
00:09:18,850 --> 00:09:22,540
Or it can make these as pointers also.

141
00:09:23,200 --> 00:09:28,720
So the syntax is simple, but the working in same as pointer that is the benefit of programmers getting.

142
00:09:29,050 --> 00:09:30,910
That's all about parameter passing.

143
00:09:31,360 --> 00:09:38,290
Not few more things like if I have two variables and one, I made it as called better friends and the

144
00:09:38,290 --> 00:09:43,510
other one is not called by reference, then will the compiler make it as an inline function?

145
00:09:43,510 --> 00:09:44,340
So I function?

146
00:09:44,860 --> 00:09:50,230
No, it's not possible for a compiler to do only one parameter reference for the other.

147
00:09:50,230 --> 00:09:56,470
One is not a kalbi reference or definitely it will not make it as in line function.

148
00:09:56,770 --> 00:10:02,490
But again, it all depends on the compiler how it's going to generate the machine code.

149
00:10:02,800 --> 00:10:03,150
Right?

150
00:10:03,460 --> 00:10:08,670
We can't say that unless and until you get the output of a compiler and see it.

151
00:10:09,070 --> 00:10:13,390
So from the observation, you can see the results given by compiler.

152
00:10:13,750 --> 00:10:19,690
But conceptually, here one is Culburra friends, other one is not called by reference or definitely

153
00:10:19,930 --> 00:10:25,720
it is not going to be in line function if I have one by one is called Bertus.

154
00:10:26,200 --> 00:10:32,440
Yes, it is possible you can have one that's called a value callback reference and one by Galba address.

155
00:10:32,440 --> 00:10:34,420
You can have different type of arguments.

156
00:10:35,080 --> 00:10:41,680
Alright, so this CI function is using callback reference, also callback address also.

157
00:10:42,850 --> 00:10:49,720
So we cannot use a single name for parameter passing for each parameter we give the type of mechanism

158
00:10:49,720 --> 00:10:57,880
used access pass by reference y's possible address that we don't use the parameter passing mechanism

159
00:10:57,880 --> 00:11:01,600
name vitta function name, we give it to the parameter.

160
00:11:01,870 --> 00:11:06,100
Alright, so this is a mixture of mechanism, parameter parsing mechanism.

161
00:11:06,400 --> 00:11:08,240
Alright, so that's it.

162
00:11:08,680 --> 00:11:14,440
So this all about parameter passing and I suggest you just, you try to check all these by yourself

163
00:11:14,440 --> 00:11:20,080
once and learn how they are working so that it is easy during the course.

