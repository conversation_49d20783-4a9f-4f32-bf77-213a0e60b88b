1
00:00:00,180 --> 00:00:01,770
Let us look at operations from the.

2
00:00:02,170 --> 00:00:04,360
For the first operation is Bush.

3
00:00:04,860 --> 00:00:08,160
This operation is used for inserting an element in this attack.

4
00:00:08,670 --> 00:00:15,660
So let us see how to insert an element in a strike using a initially top order is pointing at minus

5
00:00:15,660 --> 00:00:17,990
one means there are no elements in this tank.

6
00:00:18,270 --> 00:00:20,140
So let us push a value.

7
00:00:20,430 --> 00:00:21,860
I want to push an element 10.

8
00:00:22,650 --> 00:00:25,590
So increment up one moved up.

9
00:00:25,800 --> 00:00:28,020
So I hope to increment as it is an integer type.

10
00:00:28,020 --> 00:00:32,689
So up plus plus it will move, you know, not a soft top and I don't have any.

11
00:00:33,510 --> 00:00:36,060
Then I want to insert one more value 15.

12
00:00:36,330 --> 00:00:42,810
So incremental points plus plus top will move here and insert the value of top.

13
00:00:42,840 --> 00:00:44,030
That is a soft one.

14
00:00:44,040 --> 00:00:44,640
Top is one.

15
00:00:45,180 --> 00:00:48,720
So that's offline instead of value and instead feel more value.

16
00:00:49,020 --> 00:00:51,870
So right now top is here, which is that index of three.

17
00:00:52,020 --> 00:00:55,250
I want to insert one more value that is 20.

18
00:00:55,500 --> 00:00:59,430
So, yes, increment the top pointer and insert the value.

19
00:01:00,480 --> 00:01:02,000
Now, this is important.

20
00:01:02,370 --> 00:01:06,660
I want to insert one more value thirty five can insert.

21
00:01:06,660 --> 00:01:08,130
Is there any faceplates available?

22
00:01:08,320 --> 00:01:10,230
No, there is no freespace.

23
00:01:10,230 --> 00:01:13,140
I cannot insert because the top is equal to four.

24
00:01:13,140 --> 00:01:16,890
Four is what size minus one segment stack is full.

25
00:01:17,610 --> 00:01:22,680
So before insertion actually I must check whether the stack is full or not.

26
00:01:23,040 --> 00:01:24,870
If it is full, I cannot answer that.

27
00:01:25,140 --> 00:01:29,750
If it is not full, I can increment point in certain element in the nathi.

28
00:01:29,880 --> 00:01:30,590
So that's all.

29
00:01:30,600 --> 00:01:31,470
This is the procedure.

30
00:01:32,040 --> 00:01:34,990
Check if it is full, if it is not folded in certain element.

31
00:01:35,850 --> 00:01:38,250
Let us write a function for doing this.

32
00:01:38,460 --> 00:01:40,140
I will write a function push.

33
00:01:41,160 --> 00:01:42,810
This should take barometer's to.

34
00:01:45,450 --> 00:01:49,110
Now, let us write a function for performing Bush operation.

35
00:01:50,180 --> 00:01:56,390
Bush is the function name, what are the parameters it needs it needs a and which we want to insert

36
00:01:56,390 --> 00:01:59,770
an element and what element we want to insert.

37
00:01:59,780 --> 00:02:01,690
So two parameters are required.

38
00:02:02,240 --> 00:02:03,770
First one is a stock type.

39
00:02:04,010 --> 00:02:09,729
That is this type of structure, let us take it S.T. and also a value.

40
00:02:10,199 --> 00:02:16,010
One important thing, this is a Bush function should insert an element and this is actually the one

41
00:02:16,010 --> 00:02:16,810
that is passed.

42
00:02:17,360 --> 00:02:21,650
So if you want to modify the same tag, then it should be called by address.

43
00:02:21,680 --> 00:02:23,000
That is called by reference.

44
00:02:24,020 --> 00:02:26,150
I should write a pointer here.

45
00:02:26,330 --> 00:02:28,370
Then what the function is going to return.

46
00:02:28,550 --> 00:02:30,760
Nothing is called void.

47
00:02:31,220 --> 00:02:33,260
So this is the prototype of a function.

48
00:02:34,010 --> 00:02:35,230
Now inside the function.

49
00:02:35,240 --> 00:02:40,000
First thing, what I have to do, I have to check for this condition whether the stack is full means.

50
00:02:40,130 --> 00:02:42,590
If this is full, I cannot answer to any element.

51
00:02:42,950 --> 00:02:44,330
So let us check the condition.

52
00:02:45,110 --> 00:02:49,490
So Tobolsk ostracise minus one is the condition for stack full.

53
00:02:49,790 --> 00:02:53,540
But I have used yes, these two are inside.

54
00:02:53,540 --> 00:02:57,200
This is structure variable that is Estie and here it is appointer.

55
00:02:57,200 --> 00:02:59,420
So I have to use arrows.

56
00:02:59,600 --> 00:03:02,350
So this is the variable of this is structure.

57
00:03:02,360 --> 00:03:05,930
So there are two members here, size and top those members I'm accessing.

58
00:03:06,260 --> 00:03:07,550
And this is for the early.

59
00:03:08,530 --> 00:03:13,330
The following have already started, so they are inside the structure, so have to access them by using

60
00:03:13,330 --> 00:03:19,240
structure name, so phosphorylation, astrophysical sources minus one, if a sword in the stack is full,

61
00:03:19,510 --> 00:03:22,470
if I tried to insert anything, then it is overflown.

62
00:03:22,780 --> 00:03:29,050
So I'll give a message that stack overflow so it is full themselves, stack overflow, otherwise we

63
00:03:29,050 --> 00:03:30,070
can insert the value.

64
00:03:30,100 --> 00:03:31,710
So what is the method for insertion?

65
00:03:32,050 --> 00:03:35,720
We have already seen that upwards incremented and the value was restored.

66
00:03:36,020 --> 00:03:41,460
Suppose this is not that I'm storing top then how to insert first increment top.

67
00:03:41,650 --> 00:03:46,690
So there is a tab here Steve having to see this is top rate.

68
00:03:46,960 --> 00:03:48,610
So that is remember this is the member.

69
00:03:48,700 --> 00:03:53,370
So C SD stop plus plus the top is incremented.

70
00:03:53,950 --> 00:03:56,350
This will move the top to the next location.

71
00:03:56,650 --> 00:04:01,540
Then here I have to insert an element that is S of the top.

72
00:04:01,540 --> 00:04:05,160
So there is S and top, both of them are decent structure.

73
00:04:05,170 --> 00:04:15,170
So I have to say SD s then SD top and here the value X is stored, the value that we want to push.

74
00:04:15,670 --> 00:04:19,120
C actually this is a soft top, but both are infrastructure.

75
00:04:19,120 --> 00:04:22,200
So I am writing SD s top.

76
00:04:22,360 --> 00:04:26,270
So this is the style of code we will be writing in the remaining functions.

77
00:04:26,500 --> 00:04:30,940
Also remember that these things are inside the structure which we have already discussed.

78
00:04:31,120 --> 00:04:33,370
So this is the method for pushing a value.

79
00:04:33,640 --> 00:04:37,620
Now let us look at an next operation that is up, deleting a value.

80
00:04:37,960 --> 00:04:39,610
Let us see the procedure for deletion.

81
00:04:39,760 --> 00:04:43,930
If I want to put the value right now, top is pointing here and is the topmost value.

82
00:04:44,290 --> 00:04:47,500
So take out the value and the criminal complaint.

83
00:04:48,010 --> 00:04:50,980
The elements are still the place where the OP is pointing.

84
00:04:51,160 --> 00:04:53,620
So these are the values in the stack.

85
00:04:54,390 --> 00:05:01,170
Then I want to delete one more value, so take out the value and I'll delete a few more, delete this

86
00:05:01,170 --> 00:05:04,770
value dorpers decrement and then I want to delete this.

87
00:05:04,770 --> 00:05:10,500
Also, Topo's DiClemente got the value on the top of the agreement that sort of became, what, minus

88
00:05:10,500 --> 00:05:14,900
one laptop is minus one can delete any more values.

89
00:05:15,050 --> 00:05:16,840
No, there are no values.

90
00:05:16,860 --> 00:05:18,270
How do you say there are no values.

91
00:05:18,270 --> 00:05:19,200
Stack is empty.

92
00:05:19,650 --> 00:05:22,820
How he says target 70 Topo's equals to minus one.

93
00:05:22,830 --> 00:05:26,030
Yes top is equals two minus one stack it's 70.

94
00:05:26,370 --> 00:05:28,370
So before deletion we should confirm.

95
00:05:28,440 --> 00:05:30,780
Are there any elements present in the stack or not?

96
00:05:31,050 --> 00:05:36,670
If there are no elements, we cannot delete any element if we still try to delete the stack underfloor.

97
00:05:37,410 --> 00:05:38,550
So let me write on it.

98
00:05:38,550 --> 00:05:42,900
Function for deleting a value from the stack function.

99
00:05:42,900 --> 00:05:43,800
Name is Bob.

100
00:05:44,070 --> 00:05:49,650
If I want to modify the same stack, then I should pass it as a reference or address.

101
00:05:50,040 --> 00:05:54,490
So of that type structure stack start.

102
00:05:55,290 --> 00:05:56,520
So I'm taking your pointer.

103
00:05:57,450 --> 00:06:00,390
So this pointer, this point, it will be pointing on this stack.

104
00:06:00,420 --> 00:06:01,700
Example is the stack.

105
00:06:02,010 --> 00:06:05,910
So using the pointer I'll be accessing all these members that size and the top.

106
00:06:05,910 --> 00:06:10,430
And that's not what this function should do, delete and return the value.

107
00:06:10,590 --> 00:06:14,650
So return type should be the type of values present in this track.

108
00:06:14,670 --> 00:06:19,170
So if you have integer type values in the stack, because this arrays integer tax return by percentage

109
00:06:19,410 --> 00:06:23,120
formatting, what I should do, I should check whether I can delete any value or not.

110
00:06:23,340 --> 00:06:28,790
So that means I should check with the stack is empty for the 70 I cannot delete so phosphorylation.

111
00:06:28,860 --> 00:06:30,930
I will check if stack is empty.

112
00:06:30,930 --> 00:06:31,920
So what is the condition.

113
00:06:31,920 --> 00:06:34,970
Top is equal to minus one so I cannot say directly.

114
00:06:34,980 --> 00:06:39,390
I have to say astiz top FFE obviously goes to minus one.

115
00:06:39,390 --> 00:06:46,830
Then I should give a message that stack underfloor so stack underflow if top is equals two minus one

116
00:06:46,830 --> 00:06:48,660
then stack underflow else.

117
00:06:48,810 --> 00:06:51,510
What I have to do for deleting URLs.

118
00:06:52,170 --> 00:06:55,890
Take out the value from this please as soft top take on the value.

119
00:06:55,920 --> 00:07:03,750
So for that I will declare one variable X and also initialize it with minus one Y minus one I show,

120
00:07:03,780 --> 00:07:12,600
you know I will take this value 15 and stored it in X, so say X assign a soft top.

121
00:07:12,900 --> 00:07:15,930
But both of these members can be accessed using pointer.

122
00:07:15,930 --> 00:07:19,860
So I have to say SD s then SD top.

123
00:07:20,310 --> 00:07:22,200
So this is SD ethnicity.

124
00:07:22,240 --> 00:07:23,850
Stop the values taken in X.

125
00:07:23,850 --> 00:07:30,370
So if there is X then suppose this is X the value of this company here, then the agreement of Poynton

126
00:07:30,570 --> 00:07:31,800
not minus minus.

127
00:07:31,830 --> 00:07:35,610
So we assume that this line is deleted, so I should degremont the top line down.

128
00:07:35,790 --> 00:07:39,400
So estie stop minus minus component that is DiClemente.

129
00:07:39,810 --> 00:07:43,440
Now finally the value that is taken out from the stack, I should return it.

130
00:07:43,590 --> 00:07:49,970
So return this value that is present inside X here I return X so value is return.

131
00:07:50,190 --> 00:07:51,000
Now one more thing.

132
00:07:51,180 --> 00:07:54,000
If the stack is empty then there is nothing to delete.

133
00:07:54,300 --> 00:07:56,100
Then what this function will return.

134
00:07:56,310 --> 00:08:01,680
It is returning X so if nothing is deleted in X else part is not executed.

135
00:08:01,680 --> 00:08:04,120
That means it will return minus one.

136
00:08:05,130 --> 00:08:09,590
So if this function is returning, minus one means stack is empty.

137
00:08:09,600 --> 00:08:13,540
There is nothing to delete otherwise the deleted value.

138
00:08:14,190 --> 00:08:17,100
So like here just now we had 15, so we got 15.

139
00:08:17,310 --> 00:08:19,920
If nothing is there then it will be minus phonology.

140
00:08:20,160 --> 00:08:21,920
That's all about Bush and Pop.

141
00:08:22,230 --> 00:08:22,470
No.

142
00:08:22,470 --> 00:08:25,050
One important thing we have to discuss.

143
00:08:25,470 --> 00:08:28,170
What was the time taken for pushing an element?

144
00:08:28,320 --> 00:08:32,360
Just incrementing the topcoder and writing a value so that time was constant.

145
00:08:33,059 --> 00:08:36,360
What is the time taken in deleting an element, popping an element?

146
00:08:36,510 --> 00:08:40,929
This take out the value and agreement pointer if this simple statements are there.

147
00:08:40,980 --> 00:08:43,140
So this constant time is constant.

148
00:08:43,830 --> 00:08:47,070
So earlier I have written Bush that was also similar.

149
00:08:47,340 --> 00:08:49,110
So that time is also constant.

150
00:08:49,380 --> 00:08:53,290
So the time taken by Bush and pop operations is constant criminalities.

151
00:08:53,640 --> 00:09:02,370
The next operation is big operation now Nexus Beke operation or P operation, finding an element at

152
00:09:02,370 --> 00:09:03,510
a given position.

153
00:09:03,520 --> 00:09:09,990
So insiders track what is the element at first position, as I already told you, that this element

154
00:09:09,990 --> 00:09:12,090
is set to be first element.

155
00:09:12,090 --> 00:09:13,140
So position one.

156
00:09:14,100 --> 00:09:15,580
Position two, position three.

157
00:09:15,840 --> 00:09:18,360
So if I give position to then I should get eight.

158
00:09:18,720 --> 00:09:19,070
Right?

159
00:09:19,260 --> 00:09:21,810
If I say position for that, I should get the element ten.

160
00:09:22,830 --> 00:09:27,510
So actually, if you see them, this is in a hurry, this is index zero, one, two, three.

161
00:09:27,720 --> 00:09:33,410
So the indices are moving up from here, but position means we have to start from top.

162
00:09:33,990 --> 00:09:37,040
So we need some formula to map this thing.

163
00:09:37,620 --> 00:09:42,610
So let us take some examples and study how we can build up a formula for this one.

164
00:09:43,380 --> 00:09:47,850
If I say position as one, then what should be the index three?

165
00:09:48,480 --> 00:09:49,310
It should be three.

166
00:09:49,680 --> 00:09:56,490
If I say position two, then what should be the index to if I say position three, then index should

167
00:09:56,490 --> 00:09:56,940
be one.

168
00:09:57,150 --> 00:10:01,780
If I say position four, then index should be zero.

169
00:10:01,920 --> 00:10:03,330
How I can obtain this.

170
00:10:03,690 --> 00:10:07,530
So I think the stop pointer can help me to find out the indices.

171
00:10:07,530 --> 00:10:09,950
So for position one I want index three.

172
00:10:09,960 --> 00:10:11,090
So it is possible.

173
00:10:11,460 --> 00:10:15,720
See top is a three from that position if I subtract one.

174
00:10:16,050 --> 00:10:20,160
So actually one the index of three here I of get out of one again.

175
00:10:20,160 --> 00:10:22,400
Let us see, let us try this village.

176
00:10:22,440 --> 00:10:27,860
About four others also not set up as three and two position.

177
00:10:27,870 --> 00:10:28,720
I want us to.

178
00:10:28,740 --> 00:10:30,360
So three minus two.

179
00:10:30,870 --> 00:10:31,610
Plus one.

180
00:10:31,620 --> 00:10:32,510
How much this is.

181
00:10:32,870 --> 00:10:33,590
This is two.

182
00:10:33,870 --> 00:10:35,490
So I got it then.

183
00:10:35,500 --> 00:10:40,280
Position I want the three so top is three three minus three plus one.

184
00:10:40,770 --> 00:10:47,420
This is one and fourth position is what, three minus four plus one.

185
00:10:47,880 --> 00:10:49,140
So this is zero.

186
00:10:49,500 --> 00:10:50,640
So yes, it is working.

187
00:10:51,000 --> 00:10:55,550
So with the help of top pointer I can get the index in an array.

188
00:10:55,770 --> 00:11:02,850
So this should be taken as three minus one plus one that is answer the three three minus two plus one

189
00:11:03,120 --> 00:11:08,110
and three minus three plus one three minus four plus one.

190
00:11:09,150 --> 00:11:14,360
So this index can be obtained by minus position plus one.

191
00:11:14,580 --> 00:11:20,160
So this is how you have obtained the formula from observation by observing this positions.

192
00:11:20,160 --> 00:11:23,000
And the top point that I have obtained the formula.

193
00:11:24,030 --> 00:11:33,060
So using this formula, we can convert the position into an index and let us use this formula and write

194
00:11:33,180 --> 00:11:36,270
a big function function to speak.

195
00:11:36,270 --> 00:11:41,760
Then it means a position which position element and also it needs a stack.

196
00:11:42,030 --> 00:11:45,000
So I will send this as a parameter to this one.

197
00:11:45,090 --> 00:11:49,260
Function is big, it means a parameter rather stack.

198
00:11:49,290 --> 00:11:52,530
So I'll pass the parameter as a stack of this type.

199
00:11:52,740 --> 00:12:00,120
So stack t I should write it struck strike as t but in short I am writing this stack esti.

200
00:12:01,070 --> 00:12:07,940
Then they should be called Bio-Reference or call value, see, Galba value is enough because here we

201
00:12:07,940 --> 00:12:09,990
are just reading the contents of the stack.

202
00:12:10,340 --> 00:12:11,830
We don't want to modify it.

203
00:12:12,140 --> 00:12:13,110
Second parameter.

204
00:12:13,130 --> 00:12:15,110
We want the position.

205
00:12:15,560 --> 00:12:19,700
So position then it should return a value found dead.

206
00:12:20,110 --> 00:12:23,330
OK, then let us write the code here.

207
00:12:24,020 --> 00:12:29,300
Whatever the position is given, I should get the index using this formula and return the value from

208
00:12:29,300 --> 00:12:29,570
there.

209
00:12:30,080 --> 00:12:30,680
One more thing.

210
00:12:30,680 --> 00:12:34,100
I should take care whether the position is given valid or not.

211
00:12:34,310 --> 00:12:36,290
Right now there are four elements.

212
00:12:36,300 --> 00:12:41,540
So if I say I want to look at six Telemann or Element, it's not there.

213
00:12:41,900 --> 00:12:45,590
So first of all, I should check if that position is valid or not.

214
00:12:45,830 --> 00:12:51,690
So if don't minus position plus one, this formula should not give any value less than zero.

215
00:12:51,920 --> 00:12:59,150
So if it is less than zero, then I should give a message that position is invalid, invalid position.

216
00:12:59,660 --> 00:13:03,690
Otherwise I can return the value at a position so and so position.

217
00:13:03,830 --> 00:13:06,270
So I will take the value in some variable and return it.

218
00:13:06,530 --> 00:13:10,910
So for that I will declare a variable X and also initialize it at minus one.

219
00:13:11,180 --> 00:13:15,500
Then else I will take the value so exercised from where I want the value.

220
00:13:16,010 --> 00:13:18,030
I solve this formula.

221
00:13:18,350 --> 00:13:20,740
So this is inside STF.

222
00:13:20,750 --> 00:13:24,980
I should use this structure them Estie Dawid, S.A.C. or top.

223
00:13:25,010 --> 00:13:26,530
So let me write it.

224
00:13:27,260 --> 00:13:31,840
Exercising this two daughters of zero to minus position plus one.

225
00:13:31,850 --> 00:13:33,120
So I have used the formula.

226
00:13:33,590 --> 00:13:36,620
So here also I should write on something that is Estie.

227
00:13:36,950 --> 00:13:38,470
I have directly written this one.

228
00:13:38,810 --> 00:13:44,410
So student now finally return the value that you have found.

229
00:13:44,690 --> 00:13:47,150
So that is present in X, so written X.

230
00:13:47,150 --> 00:13:51,500
If the value is not found an invalid index then it will be returning minus one.

231
00:13:51,830 --> 00:13:55,190
So if this function is returning minus one, values not found.

232
00:13:56,210 --> 00:14:01,310
That's all with the peak now analysis, what is the time taken by this fund?

233
00:14:01,700 --> 00:14:03,370
So the timetable is constant.

234
00:14:03,650 --> 00:14:07,970
The statements are very simple, just ideas and some form of life there.

235
00:14:08,070 --> 00:14:10,350
So the time for this function is constrained.

236
00:14:10,640 --> 00:14:15,120
Now, quickly, let us look at two more function that are is 70 years is is.

237
00:14:16,130 --> 00:14:19,850
Let us look at the remaining functions of a stack.

238
00:14:20,390 --> 00:14:22,520
First one is stacked up.

239
00:14:22,670 --> 00:14:25,330
Just we want to know what is the topmost value in the stack.

240
00:14:25,340 --> 00:14:27,410
For example, here, the top most violence 20.

241
00:14:27,710 --> 00:14:30,230
So whatever top is pointing, we want the value from there.

242
00:14:30,710 --> 00:14:32,810
So here is the function stack top.

243
00:14:33,230 --> 00:14:35,080
It takes Stack as a barometer.

244
00:14:35,120 --> 00:14:39,140
This whole thing at the bottom, this whole thing means the array size and top.

245
00:14:39,590 --> 00:14:42,370
That is this structure which we have already seen in the beginning.

246
00:14:42,860 --> 00:14:44,420
So that holding it takes and check.

247
00:14:44,420 --> 00:14:48,610
If it goes to minus one, if it is minus one, then stack is empty.

248
00:14:49,460 --> 00:14:50,420
There are no elements.

249
00:14:50,840 --> 00:14:52,900
So this function returns minus one.

250
00:14:54,270 --> 00:14:56,920
Otherwise there are some elements.

251
00:14:57,230 --> 00:14:58,850
So wherever top is pointing.

252
00:14:58,850 --> 00:15:01,710
So it should return this value, this value.

253
00:15:01,910 --> 00:15:04,600
So what does this value as soft top value.

254
00:15:04,730 --> 00:15:10,670
But this is inside structure, so it should be usted orders of a seed or top.

255
00:15:10,880 --> 00:15:14,690
So a seed orders of it should return this value.

256
00:15:15,530 --> 00:15:18,590
So that is stacked up and there are simple statements.

257
00:15:18,590 --> 00:15:19,730
All the time is constant.

258
00:15:20,850 --> 00:15:24,680
The next function, as is empty, this house is empty.

259
00:15:24,720 --> 00:15:29,640
It takes stock by value, not by reference, because you are not going to modify this.

260
00:15:30,210 --> 00:15:35,910
So this is taken as a variable stock then stayed on top if it is minus one.

261
00:15:36,450 --> 00:15:41,870
We know the condition stock and the condition the Instagram, the condition, if it is minus one, then

262
00:15:41,880 --> 00:15:42,600
stock is empty.

263
00:15:42,630 --> 00:15:43,440
Yes, it is empty.

264
00:15:43,470 --> 00:15:44,470
So return true.

265
00:15:44,670 --> 00:15:48,120
So we know a lot in C language is true and false.

266
00:15:48,240 --> 00:15:55,950
Trumans one and false one zettl so true it is written in one otherwise return false that is zero then

267
00:15:56,190 --> 00:15:57,150
stack full.

268
00:15:57,480 --> 00:15:59,490
It will check whether this tag is full or not.

269
00:15:59,640 --> 00:16:01,170
If it is full it will return true.

270
00:16:01,170 --> 00:16:02,500
Otherwise it will return false.

271
00:16:02,820 --> 00:16:06,150
So it is taking this stack by value.

272
00:16:07,380 --> 00:16:12,810
So it is taking this a stack by value and here it is taking with the top as equals Forsys minus one.

273
00:16:13,230 --> 00:16:17,510
And both are inside the structure for a store to object to a store size minus one.

274
00:16:17,790 --> 00:16:20,190
The stock is equals to size minus one.

275
00:16:20,640 --> 00:16:22,650
If yes, then return through.

276
00:16:22,800 --> 00:16:26,470
So Truman's non-zero value in C language.

277
00:16:26,490 --> 00:16:28,710
So it is written in one else.

278
00:16:28,740 --> 00:16:30,810
Stack does not so return false.

279
00:16:30,820 --> 00:16:31,560
That is zero.

280
00:16:31,650 --> 00:16:35,230
So zero is false and any non-zero value is true.

281
00:16:35,370 --> 00:16:40,530
So before taking one as a true, so you have taken one for two and zero for false.

282
00:16:40,710 --> 00:16:47,670
The time taken by all these functions is constant for all the operations we saw on the stack takes Konstantine.

283
00:16:48,420 --> 00:16:51,680
That's all about stock using a.

