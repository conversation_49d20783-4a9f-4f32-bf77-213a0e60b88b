1
00:00:00,570 --> 00:00:07,950
Now, let us look at Collum major mapping story of a triangular matrix that is two dimensional in a

2
00:00:07,950 --> 00:00:15,650
single dimensional column by column, so column major, so column by column while refilling the elements.

3
00:00:15,650 --> 00:00:18,960
So the first column is having just one element that is.

4
00:00:19,910 --> 00:00:29,150
A one one, so this is column one, the next column is having two elements, one, two and two to one

5
00:00:29,150 --> 00:00:33,020
to and to do this column two.

6
00:00:33,710 --> 00:00:37,060
Likewise, I will fill up all these columns in this A.

7
00:00:39,050 --> 00:00:45,880
I have filled all the elements now we need a formula for that, I will take one example and get its

8
00:00:45,890 --> 00:00:52,010
index and from observation I will get the foreign law and order that we have seen lower triangular matics.

9
00:00:52,010 --> 00:00:55,680
And the formula will be similar to one of the formula of lower triangular.

10
00:00:56,120 --> 00:00:58,610
So let us take the example and then observe it.

11
00:00:59,780 --> 00:01:02,740
I think the example of four or five.

12
00:01:03,290 --> 00:01:12,920
So this is in the Fifth Column and that index 13 four four five index of element four, come on, five,

13
00:01:14,000 --> 00:01:17,570
four, comma five for reaching this element four comma five.

14
00:01:17,810 --> 00:01:21,430
I should cross the first column, second column, third column, fourth column.

15
00:01:21,440 --> 00:01:22,850
Then I will be in fifth column.

16
00:01:23,210 --> 00:01:25,670
So I should skip over first column.

17
00:01:25,670 --> 00:01:27,050
How many elements in first column.

18
00:01:27,050 --> 00:01:32,300
Just one and then second column two elements are there then third column three elements are there,

19
00:01:32,690 --> 00:01:34,820
then fourth column for elements of that.

20
00:01:35,180 --> 00:01:40,580
When I have Skip first column I'm here, then second column I'm here, third column I'm here, then

21
00:01:40,580 --> 00:01:42,290
fourth column and I have Skip, I'm here.

22
00:01:42,800 --> 00:01:45,920
Then from here how many spaces I should move to reach that.

23
00:01:45,920 --> 00:01:46,730
Four comma five.

24
00:01:46,730 --> 00:01:49,310
One, two, three, four, three places.

25
00:01:50,120 --> 00:01:55,460
This is one plus two, three, three, three, six, six, four, ten then thirteen.

26
00:01:55,460 --> 00:01:57,080
Yes I got thirteen.

27
00:01:57,290 --> 00:02:02,960
So if you observe this looks similar to Romijn formula for lower triangle of Matrix.

28
00:02:03,290 --> 00:02:05,690
So yes, but I n g will be different.

29
00:02:05,690 --> 00:02:10,820
So let us prepare a formula for any index and G index of.

30
00:02:13,720 --> 00:02:23,590
Any igy, this is one plus two plus three plus for up to four only because golomb them is five, so

31
00:02:23,590 --> 00:02:25,580
it means Collum minus one.

32
00:02:25,600 --> 00:02:31,660
So this will be one plus two plus three plus goes on to J minus one.

33
00:02:31,780 --> 00:02:35,970
This is J minus one and this is a three.

34
00:02:35,980 --> 00:02:37,420
So this is rule minus one.

35
00:02:37,420 --> 00:02:37,990
That is four.

36
00:02:37,990 --> 00:02:38,650
Minus one.

37
00:02:39,040 --> 00:02:40,550
So I'm minus one.

38
00:02:40,870 --> 00:02:43,580
So this becomes this is some of the national numbers.

39
00:02:43,900 --> 00:02:45,520
This is J into J.

40
00:02:45,520 --> 00:02:46,900
Minus one by two.

41
00:02:48,280 --> 00:02:50,050
Plus a minus one.

42
00:02:52,660 --> 00:02:58,920
This is the formula, so we have seen the rule, major, as the last column, Mijovic presentation for

43
00:02:59,250 --> 00:03:05,010
Upper Triangle of Metrics and we got the formula formulas are similar to the formula of lower triangular

44
00:03:05,010 --> 00:03:05,510
metrics.

45
00:03:05,760 --> 00:03:12,240
So this formula, we will use them and I will write one program by using this formula for lower triangular

46
00:03:12,240 --> 00:03:13,650
as well as upper triangular.

47
00:03:14,250 --> 00:03:16,860
So in our program, we will use these formulas.

48
00:03:17,910 --> 00:03:21,500
Now we'll move on to the next topic that is similar to nitpicks.

