1
00:00:00,510 --> 00:00:06,240
Let us learn a few more important statements that are useful and we are accessing <PERSON><PERSON>, so I remove

2
00:00:06,240 --> 00:00:11,550
these things and take some examples and I will show you let us take this example.

3
00:00:11,550 --> 00:00:19,740
I have fuel Norns, All-Inclusive there and point a piece pointing on some node and this node is having

4
00:00:19,740 --> 00:00:20,960
a pointer to the next node.

5
00:00:20,970 --> 00:00:23,040
And also I have one point a Q.

6
00:00:24,620 --> 00:00:27,380
Let us see some syntaxes and understand their meaning.

7
00:00:28,040 --> 00:00:32,520
See if I say Q assign B. What does it mean?

8
00:00:33,680 --> 00:00:35,960
Q Assign payments, whatever is there.

9
00:00:35,960 --> 00:00:37,580
And B, that has to be stored in.

10
00:00:37,580 --> 00:00:40,250
Q Also, so whatever is there and B has to be stored in.

11
00:00:40,250 --> 00:00:44,740
Q also, so Q will have the address 200.

12
00:00:44,750 --> 00:00:49,370
So the standard is there, so you will also get two hundred and Q will be binding on the same note.

13
00:00:49,490 --> 00:00:56,110
This note is 200, so if I write B assign Kuman secure will also point on the same as the meaning.

14
00:00:56,840 --> 00:00:58,620
So to point US funding on the same note.

15
00:00:59,480 --> 00:01:01,160
Now let us look at <PERSON>'s syntax.

16
00:01:03,650 --> 00:01:04,489
If I see.

17
00:01:05,550 --> 00:01:12,330
Do you assign these next, what does it mean inside?

18
00:01:12,630 --> 00:01:14,430
We want to store BP's next.

19
00:01:14,460 --> 00:01:15,720
So what is BP's next?

20
00:01:15,750 --> 00:01:17,930
This is B Services 200.

21
00:01:18,120 --> 00:01:19,690
S next is here.

22
00:01:20,160 --> 00:01:21,470
That is 210.

23
00:01:22,320 --> 00:01:23,390
So the is to 210.

24
00:01:23,400 --> 00:01:24,380
We want to see it in.

25
00:01:24,410 --> 00:01:28,240
Q So you will be getting the value to 10.

26
00:01:28,260 --> 00:01:29,290
So this is 210.

27
00:01:29,550 --> 00:01:34,120
So another Q is pointing point pointing on Nixonland.

28
00:01:35,490 --> 00:01:43,950
So if you read it let the Q point on the next note of P Q point on the next Nordoff B so please next

29
00:01:43,950 --> 00:01:47,490
assign Q so on next Nordoff Bhiku will be pointing.

30
00:01:48,880 --> 00:01:56,110
Then one more statement, if I say be a sign, please, next, like we have already seen that letter

31
00:01:56,110 --> 00:02:01,230
Q Boyington next Nordoff be so here, what is the meaning of this letter?

32
00:02:01,240 --> 00:02:04,750
P Point on next note of P P Point.

33
00:02:04,750 --> 00:02:05,950
On next note of payments.

34
00:02:06,340 --> 00:02:08,050
Baeza Two hundred right now.

35
00:02:08,320 --> 00:02:09,280
BP's next.

36
00:02:09,280 --> 00:02:10,000
BP's next.

37
00:02:10,509 --> 00:02:11,130
This one.

38
00:02:11,380 --> 00:02:12,880
What is the value that Teuton.

39
00:02:13,030 --> 00:02:18,400
So that to Tanvir it should be stored, it should be stored in P so it means this value should be taken

40
00:02:18,400 --> 00:02:19,360
and kept inside.

41
00:02:19,360 --> 00:02:23,080
B so this becomes 210.

42
00:02:24,230 --> 00:02:29,360
So when that is having to convince that be is no more pointing on this note, he's pointing on this

43
00:02:29,360 --> 00:02:31,350
note because this editor's note is 210.

44
00:02:32,150 --> 00:02:36,560
So this statement was made be moved to next known.

45
00:02:37,200 --> 00:02:39,000
He has moved on to the next norm.

46
00:02:39,410 --> 00:02:44,890
This is a very useful statement, very, very much useful statement in legalese.

47
00:02:45,140 --> 00:02:49,750
Like if I have a pointer B that was pointing on some Naude moving pointed to the next node.

48
00:02:50,020 --> 00:02:52,610
This is the code be assigned peaceniks.

49
00:02:52,880 --> 00:02:59,240
So BP's is stored again in P, so the students during p p e pointing on next note, we will see a few

50
00:02:59,240 --> 00:03:00,060
more statements.

51
00:03:00,080 --> 00:03:03,680
I'll just change the example, then we will see a few more statements on this one.

52
00:03:04,580 --> 00:03:07,820
Next, I have two examples.

53
00:03:08,180 --> 00:03:10,990
Point of P not pointing anywhere.

54
00:03:11,540 --> 00:03:18,080
It doesn't address a zero and the second one is pointing on some node in a linguist.

55
00:03:19,430 --> 00:03:22,880
How to make B as we can right here zero.

56
00:03:23,750 --> 00:03:30,650
It will be null or else we can write capital and u l l for null.

57
00:03:31,580 --> 00:03:32,840
So either you say.

58
00:03:35,050 --> 00:03:37,750
Zero or none for the same thing on the.

59
00:03:40,150 --> 00:03:47,920
Now, let us see these two examples is pointer RP's not pointing on any Norn, then it will have a value

60
00:03:47,920 --> 00:03:55,660
zero at zero that is disappointing on some known, then it will have some valid address and that value

61
00:03:55,660 --> 00:03:56,820
will not be zero.

62
00:03:56,860 --> 00:03:59,440
It is non-zero and.

63
00:04:00,630 --> 00:04:01,290
If you know.

64
00:04:02,250 --> 00:04:08,760
In C C++, programming zero means the files and any other non-zero value is true.

65
00:04:10,350 --> 00:04:15,340
So if Pointer is not pointing out any new word, then it is zero zero.

66
00:04:15,390 --> 00:04:18,209
And that is false and very disappointing.

67
00:04:18,209 --> 00:04:19,980
On some note, it will have non-zero value.

68
00:04:20,010 --> 00:04:20,970
That means that it's true.

69
00:04:22,500 --> 00:04:29,570
Now, let us see the conditions to check if appointer disappointing on some Naude or not.

70
00:04:30,150 --> 00:04:35,610
So if I write the first condition, if B is equal Domhnall.

71
00:04:37,640 --> 00:04:44,150
If I say is equal, do none of these two cases, in which case this condition will be to be equal to

72
00:04:44,150 --> 00:04:45,980
none, in this case it will be.

73
00:04:46,580 --> 00:04:54,310
So this means this will be true if Winter B is not binding on any norm, it's not binding anywhere.

74
00:04:55,250 --> 00:05:03,050
And second condition, if A right, if a B is equal to zero, then all of these two cases, which condition

75
00:05:03,050 --> 00:05:03,640
will be true?

76
00:05:03,890 --> 00:05:04,880
This will be true.

77
00:05:04,880 --> 00:05:07,790
That P is having zero must be is not binding on any normal.

78
00:05:10,360 --> 00:05:21,610
If it might not be in the case of this condition will be true, beas zero zero means false.

79
00:05:22,430 --> 00:05:28,620
But not as zero as a true again, this condition is true in this case.

80
00:05:28,910 --> 00:05:36,020
So these are the three methods of checking if a pointer is not pointing any on any order or not pointing

81
00:05:36,020 --> 00:05:36,500
anywhere.

82
00:05:36,890 --> 00:05:41,480
So if pointer is null, then these three conditions are there.

83
00:05:41,490 --> 00:05:43,790
We can check if it is null or not.

84
00:05:43,880 --> 00:05:48,800
If it is null, then these conditions which will now be checked, these conditions.

85
00:05:50,370 --> 00:06:01,290
I have to just write in one of these conditions so I can see if B is not equal to none here, please,

86
00:06:01,290 --> 00:06:02,830
none, but we are not equal to none.

87
00:06:02,910 --> 00:06:04,100
Yes, this will be true.

88
00:06:04,350 --> 00:06:05,940
So this condition will be true.

89
00:06:06,120 --> 00:06:14,220
If Pointer is pointing on some note of a linguist, then another way of writing this one is B is not

90
00:06:14,220 --> 00:06:15,080
equal to zero.

91
00:06:15,180 --> 00:06:16,890
So just inverse of this condition.

92
00:06:17,220 --> 00:06:26,610
And even I can write if A B just B so what is the value of B two hundred F two hundred or two hundred

93
00:06:26,610 --> 00:06:28,060
as true or false.

94
00:06:28,440 --> 00:06:30,440
So two hundred means true.

95
00:06:31,230 --> 00:06:37,640
So if payments to Truman's point that is binding on some Naude unrelentless.

96
00:06:38,070 --> 00:06:39,990
So these are few important conditions.

97
00:06:40,900 --> 00:06:45,690
I have one more conditions to show so far that I remove this and one more condition.

98
00:06:46,020 --> 00:06:51,780
I want to know whether after nor the P let us call this SNP because B is pointing it out.

99
00:06:52,200 --> 00:06:55,470
There is some more note or not, or B the last note.

100
00:06:55,980 --> 00:06:56,940
So let us see this.

101
00:06:57,690 --> 00:07:01,890
If B comes next.

102
00:07:02,040 --> 00:07:03,200
That is next.

103
00:07:03,750 --> 00:07:04,230
D next.

104
00:07:04,620 --> 00:07:09,140
This one is equal to null or zero.

105
00:07:10,070 --> 00:07:16,300
If this is a zero, then this condition will be true, it means this is not binding on any node, so

106
00:07:16,320 --> 00:07:18,020
it is not binding on an enormous.

107
00:07:18,040 --> 00:07:22,240
This is the last more than a linguist it supports is not binding anywhere.

108
00:07:22,790 --> 00:07:31,470
Then that will be lost forever, linked with another condition if BP's next is not equal to null.

109
00:07:33,290 --> 00:07:39,710
So then when this conditional withdrawal, if there is somewhere, this present here means after this

110
00:07:39,710 --> 00:07:44,460
also there is some node or not usually be required to check this type of conditions.

111
00:07:44,690 --> 00:07:49,940
So these are a few important statements and conditions that are commonly used while working with linguist.

112
00:07:50,810 --> 00:07:52,100
So that's all in the scheme.

