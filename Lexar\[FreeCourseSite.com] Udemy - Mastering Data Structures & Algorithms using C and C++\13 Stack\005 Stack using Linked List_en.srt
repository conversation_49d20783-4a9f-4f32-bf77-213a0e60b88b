1
00:00:00,150 --> 00:00:07,200
In this video, I'll talk about implementation of strike, using that to strike using Littlest Lincolnesque,

2
00:00:07,200 --> 00:00:08,940
we have already started as a topic.

3
00:00:09,330 --> 00:00:14,670
So Linklaters the collection of notes, which will have a list of elements of collection of elements.

4
00:00:15,060 --> 00:00:17,400
So Stark is also a collection of elements.

5
00:00:17,400 --> 00:00:21,820
But insertion and deletion is a done by using Leaford discipline.

6
00:00:22,620 --> 00:00:26,610
Let us learn how that can be implemented using the list.

7
00:00:27,090 --> 00:00:29,830
So already I have a link list here on board.

8
00:00:31,110 --> 00:00:35,090
This is the node structure of a link lists and is the structure defined in C language.

9
00:00:35,100 --> 00:00:36,680
So everything is ready now.

10
00:00:36,940 --> 00:00:38,050
There are no elements here.

11
00:00:38,430 --> 00:00:42,600
So first of all, let us discuss how we should insert and delete the elements.

12
00:00:42,840 --> 00:00:47,220
So in this stack, elements are inserted and deleted from same.

13
00:00:47,310 --> 00:00:53,570
And now if you look at this link list, there are two ends that is left hand side and to return the

14
00:00:53,610 --> 00:00:53,900
sender.

15
00:00:54,780 --> 00:00:59,940
If I select this site for inserting and deleting the elements, then how much time it takes for inserting

16
00:00:59,940 --> 00:01:00,920
and deleting on the site.

17
00:01:01,380 --> 00:01:02,260
It's constant.

18
00:01:02,790 --> 00:01:08,070
If I select this site, then from the first node I have to traverse through this link list then only

19
00:01:08,070 --> 00:01:08,770
again inserted.

20
00:01:08,790 --> 00:01:11,160
So the time taken here is order.

21
00:01:11,310 --> 00:01:17,400
And so inflation and deletion from this end and this is one.

22
00:01:18,100 --> 00:01:22,440
So now you decide which society should select for insertion and deletion.

23
00:01:22,930 --> 00:01:27,300
Definitely the site once we have learned about creating a link list.

24
00:01:27,300 --> 00:01:30,200
So that time every time we were adding a node at the end.

25
00:01:30,690 --> 00:01:35,580
But here we will add a new node always on this end Left-Hand side.

26
00:01:35,820 --> 00:01:37,400
Now let me fill up a few elements.

27
00:01:38,040 --> 00:01:42,390
Let us assume that this is the first element I have inserted in the stack.

28
00:01:42,960 --> 00:01:45,890
So Stack is still here then I want to insert 15.

29
00:01:45,900 --> 00:01:48,690
So this node is inserted and the value is 15.

30
00:01:48,960 --> 00:01:49,840
Then one more value.

31
00:01:49,840 --> 00:01:51,180
I want to insert eight.

32
00:01:51,180 --> 00:01:53,880
So this node is created and inserted, it is inserted.

33
00:01:54,180 --> 00:01:55,700
Then they want to insert one more value.

34
00:01:55,710 --> 00:01:57,800
So this is value three.

35
00:01:57,990 --> 00:02:00,840
So this is the latest value that is inserted in the stack.

36
00:02:01,290 --> 00:02:02,610
So where is the pointer?

37
00:02:02,620 --> 00:02:05,490
So we have pointer first here on the first node.

38
00:02:05,730 --> 00:02:08,070
But no, I will call that pointer top.

39
00:02:08,590 --> 00:02:12,700
That is the top is the pointer pointing up on first node.

40
00:02:13,050 --> 00:02:18,630
So now always new element is inserted from top that is on the left hand side.

41
00:02:19,140 --> 00:02:25,380
Now what are the elements in the stack three, eight, 15 then which is the topmost element where the

42
00:02:25,500 --> 00:02:31,500
top is pointing the first element of the first nodes containing the first element in the stack, and

43
00:02:31,500 --> 00:02:34,290
that is the recently inserted element in the stack.

44
00:02:34,590 --> 00:02:38,040
Now let us see if I want to push any element how I can push it.

45
00:02:38,400 --> 00:02:44,700
I should create a new node here with the help of some temporary pointer and insert the value, then

46
00:02:44,700 --> 00:02:46,620
make this point here on the stop.

47
00:02:46,860 --> 00:02:49,030
And Taube should be brought on this new node.

48
00:02:50,100 --> 00:02:51,530
This is the method of inserting.

49
00:02:51,540 --> 00:02:52,860
So we are familiar with this one.

50
00:02:52,870 --> 00:02:54,660
This is inserting at index zero.

51
00:02:56,320 --> 00:02:58,270
How much time it takes constant?

52
00:02:59,910 --> 00:03:04,670
So we'll be selecting the site for Bush then, if we want the proper value that is the legal value,

53
00:03:05,670 --> 00:03:07,830
then if I want to pop or delete a value.

54
00:03:08,070 --> 00:03:09,870
So take a point up on first note.

55
00:03:10,260 --> 00:03:11,100
This is the first node.

56
00:03:11,430 --> 00:03:16,080
So move to a pointer to the next node, then delete this node.

57
00:03:16,260 --> 00:03:23,940
So this note is deleted so that for deleting an element at index one that is deleting first node we

58
00:03:23,940 --> 00:03:28,490
have already seen this is the same as that one, how much time it has taken constant.

59
00:03:28,980 --> 00:03:34,380
So I have shown you how to push the element and how to bob that element, that is, delete the element

60
00:03:34,680 --> 00:03:36,570
inside the stack using Lechler.

61
00:03:36,590 --> 00:03:43,020
So are the same as inserting an index zero and deleting from index one.

62
00:03:43,500 --> 00:03:45,920
That is the deleting first note next.

63
00:03:46,620 --> 00:03:48,180
Let us look at some condition.

64
00:03:49,050 --> 00:03:50,520
Two important conditions out there.

65
00:03:50,670 --> 00:03:54,740
When you say stack is empty, when there are no n no elements.

66
00:03:54,800 --> 00:03:56,250
Then we say stack is empty.

67
00:03:56,250 --> 00:03:59,350
So that time top will be null.

68
00:03:59,550 --> 00:04:02,390
So if it drop it, then stack is empty.

69
00:04:02,580 --> 00:04:05,250
So right on the condition here, empty condition.

70
00:04:06,000 --> 00:04:08,880
If top is equal to null, then it is empty.

71
00:04:10,260 --> 00:04:13,200
Then we also need one more condition that is a sack full.

72
00:04:14,550 --> 00:04:21,510
In an area the size of an area was fixed, but here it is unlimited, if you want to set the size of

73
00:04:21,510 --> 00:04:22,410
that is your choice.

74
00:04:22,920 --> 00:04:24,710
But this is unlimited size.

75
00:04:25,200 --> 00:04:27,710
You can insert as many elements as you want.

76
00:04:28,080 --> 00:04:35,040
Then when the U.S. is successful, if you are unable to create any new node, means he is full, then

77
00:04:35,040 --> 00:04:35,770
successful.

78
00:04:36,210 --> 00:04:42,820
So if you are creating a new lawn and it is not getting created, then stack is full.

79
00:04:43,260 --> 00:04:45,800
So for that I will write on the full condition here.

80
00:04:46,230 --> 00:04:48,320
Create a new node, new node.

81
00:04:49,620 --> 00:04:51,830
If it is not created then T will be null.

82
00:04:53,730 --> 00:04:57,670
If these null then start as full.

83
00:04:57,960 --> 00:05:02,060
So that's all about stock using linked list.

84
00:05:02,700 --> 00:05:08,460
How to represent that is using the same node structure and from which side we should insert and delete

85
00:05:08,460 --> 00:05:09,310
from the site.

86
00:05:09,750 --> 00:05:11,430
What is the stack condition.

87
00:05:11,940 --> 00:05:14,390
What is stack full condition.

88
00:05:15,930 --> 00:05:20,250
No, let me see the operations on the stack using Lincolnesque.

