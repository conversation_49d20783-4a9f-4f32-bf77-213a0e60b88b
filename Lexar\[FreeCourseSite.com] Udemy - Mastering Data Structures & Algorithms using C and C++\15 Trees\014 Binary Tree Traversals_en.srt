1
00:00:00,300 --> 00:00:08,670
The topic is free travel, since travelers visiting all the elements are visiting all the nodes, if

2
00:00:08,670 --> 00:00:13,380
a data structure is linear, then we can traverse in two minutes.

3
00:00:14,160 --> 00:00:18,870
First to last that is forward or last to first backload.

4
00:00:20,160 --> 00:00:23,680
But it is non-linear, then what are the possibilities?

5
00:00:24,300 --> 00:00:29,430
So for binary trees, these are possible, Traviss.

6
00:00:30,430 --> 00:00:37,690
Preorder inaudible strata and level order, so let us learn about these traversal, so first I will

7
00:00:37,690 --> 00:00:44,890
read all their definitions, preorder minutes, visiting a node for visiting any node, then performing

8
00:00:44,890 --> 00:00:48,600
preordering on its left subtree and performing preorder on X-Rite subtree.

9
00:00:49,630 --> 00:00:57,520
Usually we read like this route, we say route left or right, but this is the perfect definition of

10
00:00:57,520 --> 00:00:58,180
preorder.

11
00:00:58,750 --> 00:01:05,650
Then in order to have some first perform in order traversal on lefter subtree, then visit the node,

12
00:01:05,890 --> 00:01:12,430
then performing order traversable right subtree, then post other traversal performable Storaro left

13
00:01:12,440 --> 00:01:20,330
subtree right subtree done with Naude and lost one level aldermen's visit aldermen's level by level.

14
00:01:21,070 --> 00:01:22,480
So here I have example.

15
00:01:22,480 --> 00:01:25,210
Trees of height, one that small trees.

16
00:01:25,420 --> 00:01:27,670
Let us see what is the result of these travel traversal.

17
00:01:28,210 --> 00:01:31,150
So firstly I will take pre order of this one.

18
00:01:31,390 --> 00:01:40,720
I'm going to get pre preorder first route or another e then go on its left then go on its right.

19
00:01:41,050 --> 00:01:45,370
So A B C s preorder then what is in order.

20
00:01:46,510 --> 00:01:47,410
First left.

21
00:01:48,870 --> 00:01:56,790
Then that's not a done right, so be a sea in order then for starter.

22
00:01:58,610 --> 00:02:08,449
First left B, dendrite C, then E, this is both STADA and the level and the level order for this one

23
00:02:08,449 --> 00:02:17,390
will be A next level, B, C, ABC, first level A. The next level B, C, then here I have taken only

24
00:02:17,390 --> 00:02:23,510
to N, that is a root node and its left or little n let us see what will be the result of these Traviss

25
00:02:24,170 --> 00:02:34,400
first preordering first root A then left B there is no right element then in order first left.

26
00:02:36,120 --> 00:02:45,540
Then route, there is no right limit for stardom, first left, then right.

27
00:02:45,610 --> 00:02:46,410
There is no right.

28
00:02:46,770 --> 00:02:49,010
Then take root the level of.

29
00:02:51,050 --> 00:02:55,640
First level A. The next level only is there be.

30
00:02:56,990 --> 00:03:02,510
So if you are observing this, this in order will start or same for that particular tree, because it

31
00:03:02,510 --> 00:03:05,260
is not having the right support and in order to.

32
00:03:06,290 --> 00:03:07,430
Now, what more do I have?

33
00:03:07,610 --> 00:03:11,590
Let us write the preorder first root root then.

34
00:03:11,950 --> 00:03:13,130
There is no left then.

35
00:03:13,130 --> 00:03:13,540
Right.

36
00:03:13,700 --> 00:03:16,520
OK, take it right then in order.

37
00:03:17,840 --> 00:03:25,970
First left, there is no left, then take root, root, then right, be here in this street, preordering

38
00:03:25,970 --> 00:03:26,350
in order.

39
00:03:26,360 --> 00:03:36,100
Arsène then tostada first left that there is no left, then go to right, ok, right then route a then

40
00:03:36,110 --> 00:03:41,210
level order level by level first a second level.

41
00:03:41,210 --> 00:03:42,200
There is no element here.

42
00:03:42,230 --> 00:03:44,090
Only this element is that -- B.

43
00:03:45,680 --> 00:03:50,770
So far, these are small examples, I have shown you how these travel cells are, so actually you have

44
00:03:50,780 --> 00:03:52,640
understood what is the meaning of these travel.

45
00:03:53,450 --> 00:03:57,890
Now, if the tree is larger, if it is bigger than how it is performed, let us look at.

46
00:03:59,280 --> 00:04:04,680
Now, for the bigger problem of bigotry, let us find a common sense, so the traversal that I'm going

47
00:04:04,680 --> 00:04:10,860
to show you are based on the definition sweetshop, then there is a shortcut or the easy method for

48
00:04:10,860 --> 00:04:12,030
finding the traversal.

49
00:04:12,300 --> 00:04:13,680
Just by looking at a tree.

50
00:04:13,690 --> 00:04:15,870
You can tell the traversal that.

51
00:04:15,870 --> 00:04:17,240
I will show you how to do that.

52
00:04:18,269 --> 00:04:23,450
But now let us follow their definition as per the definition, I will show you how to find a connection.

53
00:04:24,030 --> 00:04:28,560
This is because we have just not seen a smaller size tree.

54
00:04:28,560 --> 00:04:31,200
And directly we were able to find a common sense.

55
00:04:32,320 --> 00:04:37,510
So just three, Northway, their height was too, but the height is more so break this tree to smaller

56
00:04:37,510 --> 00:04:43,260
size of trees means it's a large problem to break the problem into smaller size problems.

57
00:04:43,660 --> 00:04:45,270
How we can do that, let us see.

58
00:04:45,860 --> 00:04:48,460
See, this is root is the root of a tree.

59
00:04:48,970 --> 00:04:50,620
Then this is the main node.

60
00:04:50,920 --> 00:04:53,460
Then this is the left side and the right chain.

61
00:04:53,770 --> 00:04:57,210
Left behind is not just one node, it is having its children also.

62
00:04:57,430 --> 00:05:01,750
So make it as a separate subtree then.

63
00:05:01,910 --> 00:05:02,970
Right child.

64
00:05:02,980 --> 00:05:05,920
It's not just a child, it is having its own children also.

65
00:05:06,090 --> 00:05:07,510
It is having its own family.

66
00:05:07,630 --> 00:05:09,100
So kids operate this family.

67
00:05:11,010 --> 00:05:17,070
So this is a subtree and this also swaptree, and this is the root of MindTree now from Maine, if you

68
00:05:17,070 --> 00:05:21,460
see that it's root left, it's a single block, right?

69
00:05:21,570 --> 00:05:22,500
It's a single block.

70
00:05:22,740 --> 00:05:24,870
Then inside that if you see again.

71
00:05:24,870 --> 00:05:25,470
Exactly.

72
00:05:27,130 --> 00:05:32,450
So this is based on the definition and again, I'm telling you now, let us find out, preorder first

73
00:05:32,590 --> 00:05:36,380
ruled a then left China.

74
00:05:37,210 --> 00:05:40,810
This is actually OK, put the bracket later.

75
00:05:40,810 --> 00:05:42,610
We will see what to do then.

76
00:05:42,610 --> 00:05:42,880
Right.

77
00:05:42,880 --> 00:05:46,180
China does not just one child, Sirtris subtree.

78
00:05:46,450 --> 00:05:48,700
So put the bracket will see afterwards.

79
00:05:49,450 --> 00:05:54,090
So this pre order is completed based on this street.

80
00:05:54,130 --> 00:05:56,470
It is completed and those blanks are there.

81
00:05:56,470 --> 00:05:59,830
We have to fill them now for this bracket.

82
00:06:00,220 --> 00:06:01,290
This is a child, right?

83
00:06:01,660 --> 00:06:03,160
What is the preorder for this one?

84
00:06:03,160 --> 00:06:04,110
Three nodes identically.

85
00:06:04,150 --> 00:06:04,930
We know the answer.

86
00:06:05,050 --> 00:06:07,930
B, B, e, root.

87
00:06:07,930 --> 00:06:08,710
Left, right.

88
00:06:08,860 --> 00:06:14,260
So B, b, e then what about this one?

89
00:06:14,380 --> 00:06:15,400
This belongs to this.

90
00:06:15,730 --> 00:06:17,860
So does the tree with just three nodes.

91
00:06:17,860 --> 00:06:20,620
So we directly node answer root left.

92
00:06:20,620 --> 00:06:22,450
Right see f g.

93
00:06:23,880 --> 00:06:31,230
That said, this is the preorder, let me open the brackets and write them A, B, C, if this is a

94
00:06:31,230 --> 00:06:34,410
pre order for based on their definition, I have done it.

95
00:06:34,770 --> 00:06:36,780
So this is the preorder now.

96
00:06:36,780 --> 00:06:45,330
Next, I'll show you in order in order of a treat for this root or for this node first left.

97
00:06:45,570 --> 00:06:46,980
This is not just one node.

98
00:06:47,430 --> 00:06:48,230
It's a subtree.

99
00:06:48,570 --> 00:06:51,080
OK, you put the brackets, we will see it afterwards.

100
00:06:51,840 --> 00:06:54,480
No parent not bitted.

101
00:06:54,480 --> 00:06:55,200
That is root.

102
00:06:55,830 --> 00:06:56,700
Take this node.

103
00:06:58,010 --> 00:07:03,130
Then, right, child, it's not a child, it's a subtree, OK, okay, put the brackets, we will see

104
00:07:03,130 --> 00:07:03,900
it afterwards.

105
00:07:04,540 --> 00:07:08,610
So left root, right, left root, right.

106
00:07:08,860 --> 00:07:10,870
This is all next.

107
00:07:11,500 --> 00:07:14,490
Fill this bracket in order to avoid some of this one.

108
00:07:14,860 --> 00:07:21,910
So there are only three notes in order in order traversal that is a be a left rolled right d.

109
00:07:22,090 --> 00:07:22,600
B.

110
00:07:23,880 --> 00:07:35,370
E then in order to of this part that is F.C. G, f, c, g, I'll open the brackets and write it.

111
00:07:36,720 --> 00:07:39,450
It is a d be a f g.

112
00:07:39,750 --> 00:07:40,800
This is the in order.

113
00:07:41,160 --> 00:07:42,440
Let me finish post order.

114
00:07:43,500 --> 00:07:45,340
Both Tortoise's first left.

115
00:07:46,020 --> 00:07:48,780
This is not just one node dendrite.

116
00:07:48,780 --> 00:07:51,380
This is not just one node then route.

117
00:07:51,600 --> 00:07:51,860
OK.

118
00:07:51,910 --> 00:07:58,680
Take a then whatever is left there are three nodes performable strata db.

119
00:07:59,880 --> 00:08:03,690
So DB this is the right chain, performable strata.

120
00:08:03,690 --> 00:08:04,760
Only three nodes are there.

121
00:08:04,770 --> 00:08:20,670
So FJC FJC open the brackets db fjc the last one level by level, first A, then the B C, then the

122
00:08:20,670 --> 00:08:23,050
TFG that form.

123
00:08:24,090 --> 00:08:32,390
So here are the level order level order is a b c d e f g yes.

124
00:08:32,400 --> 00:08:34,669
A b c d e f g.

125
00:08:34,679 --> 00:08:39,500
So these traversal as we have seen them based on the definition of a traversal.

126
00:08:39,929 --> 00:08:42,600
So this is the perfect way to understand them.

127
00:08:43,380 --> 00:08:48,840
But if a tree is given and you have to find out what is the traversal, if you want to find the result

128
00:08:48,870 --> 00:08:52,370
using pen and paper, this is not an easy way to do.

129
00:08:53,020 --> 00:08:55,950
Then there are some simple and easy methods available.

130
00:08:56,250 --> 00:09:00,960
We look at those and so we will learn how we can directly calatrava's all.

