1
00:00:00,600 --> 00:00:07,320
And this video will look at a procedure for converting infix expression into postfix expression by using

2
00:00:07,650 --> 00:00:08,250
STAC.

3
00:00:09,170 --> 00:00:15,500
Already I have an infix expression given here, this expression is using very limited operators like

4
00:00:15,500 --> 00:00:20,030
addition, subtraction, multiplication, division, the president's table is given here, addition,

5
00:00:20,030 --> 00:00:24,890
subtraction is having precedence one and multiplication division is having precedence, too.

6
00:00:25,190 --> 00:00:31,100
If you want more operators, you can increase the table size and add their precedences and that same

7
00:00:31,100 --> 00:00:32,830
procedure will work on them also.

8
00:00:33,770 --> 00:00:36,800
But to understand the procedure, we are taking a small example.

9
00:00:37,250 --> 00:00:42,370
Let us see how to convert this so far that I will take a stack and show you the procedure.

10
00:00:42,980 --> 00:00:45,590
Let us see the procedure so far that we require stack.

11
00:00:45,590 --> 00:00:52,050
So I will take a stack of <PERSON><PERSON><PERSON>'s ready and I have to generate a postfix expression.

12
00:00:52,970 --> 00:00:54,610
Now let us start the procedure.

13
00:00:54,620 --> 00:01:00,590
So in the procedure I have to scan through infix expression by taking one symbolic of time while scanning

14
00:01:00,590 --> 00:01:01,360
what we have to do.

15
00:01:01,370 --> 00:01:07,560
Laodicean first assemble it's operand appointments.

16
00:01:07,670 --> 00:01:09,260
Send it to Pacifics.

17
00:01:10,410 --> 00:01:18,840
Right in postfix, next symbol, it's an operator, operator, plus it's the president's is one.

18
00:01:19,770 --> 00:01:21,180
Is there anything in the stack?

19
00:01:21,190 --> 00:01:22,170
No, nothing is there.

20
00:01:22,170 --> 00:01:24,690
So push it, which I will like.

21
00:01:24,690 --> 00:01:25,590
It's precedence here.

22
00:01:25,600 --> 00:01:26,350
Just beside.

23
00:01:27,430 --> 00:01:32,470
Next symbol is an operand, send it to postfix.

24
00:01:33,600 --> 00:01:40,320
Next symbol operator, what does this precedence to ex-president is to look into the.

25
00:01:40,800 --> 00:01:41,980
Is there anything else?

26
00:01:42,150 --> 00:01:43,410
What is there on the top?

27
00:01:43,920 --> 00:01:46,170
Plus, what is this precedence one?

28
00:01:46,470 --> 00:01:48,610
And what are the precedents of this one, too?

29
00:01:48,900 --> 00:01:49,890
So this is great.

30
00:01:50,170 --> 00:01:50,700
Push it.

31
00:01:51,540 --> 00:01:52,680
Move to next symbol.

32
00:01:53,280 --> 00:01:56,910
See Alperin, send it to Postfix.

33
00:01:58,330 --> 00:02:03,950
Next symbol, operator minus, what is the president's residence?

34
00:02:03,970 --> 00:02:06,420
This one is precedences one.

35
00:02:06,760 --> 00:02:08,210
Is there anything in the stack?

36
00:02:08,210 --> 00:02:12,340
Yes, on the top asking is there what is this precedence to.

37
00:02:13,030 --> 00:02:14,890
So the precedents of this mine.

38
00:02:14,920 --> 00:02:16,870
This is a smaller than this one.

39
00:02:17,230 --> 00:02:25,420
But this one, if this is going to pop out and send it to postfix, then check again.

40
00:02:26,110 --> 00:02:28,330
This is minus precedent's one.

41
00:02:28,720 --> 00:02:33,190
What is there in the stack top topmost traduce plus an X precedences equal.

42
00:02:33,700 --> 00:02:36,190
So precedences equal then also pop out.

43
00:02:36,370 --> 00:02:39,370
So popular this one and send it to postfix.

44
00:02:39,400 --> 00:02:43,510
So it means if it is an operator we should push it into the stack.

45
00:02:44,800 --> 00:02:52,300
And before pushing check waterdog predisposition in the stack is the operators in the stack have a lower

46
00:02:52,300 --> 00:02:55,290
precedence than push that one otherwise.

47
00:02:56,170 --> 00:02:57,050
Let me finish.

48
00:02:57,970 --> 00:03:01,300
So this so this mine is pushing into the stack.

49
00:03:01,300 --> 00:03:02,260
Precedences one.

50
00:03:02,770 --> 00:03:06,070
The next is Deedes and Open Sinica Postfix.

51
00:03:07,150 --> 00:03:14,590
Then slash is precedences to this is one, push it into the stack mix's e, send it to proposed fix,

52
00:03:16,030 --> 00:03:19,690
then end of expression, we have reached the end of the expression.

53
00:03:19,930 --> 00:03:25,040
So whatever is there in the stack, empty it and go on adding the contents to the end of post.

54
00:03:25,650 --> 00:03:27,730
So Slash comes out.

55
00:03:29,370 --> 00:03:31,830
Then minus comes out minus.

56
00:03:34,480 --> 00:03:35,080
That's it.

57
00:03:36,150 --> 00:03:37,030
Does the procedure.

58
00:03:37,530 --> 00:03:40,020
So, once again, I'll show you like a revision quickly.

59
00:03:40,710 --> 00:03:42,840
Alperin Senator Postfix.

60
00:03:43,410 --> 00:03:45,870
Plus, there's nothing in the stacks of Bush.

61
00:03:45,880 --> 00:03:49,960
It'll be Operand Star.

62
00:03:50,280 --> 00:03:53,940
This is two presidents as two and the presidents here is one.

63
00:03:54,150 --> 00:03:55,080
So that is great.

64
00:03:55,230 --> 00:03:55,860
So push it.

65
00:03:58,800 --> 00:04:06,990
See Sagbo six minus precedences one, the one inside the stack is having greater precedence, so you

66
00:04:06,990 --> 00:04:11,480
cannot push that one here or this one as it goes out.

67
00:04:13,400 --> 00:04:19,490
Precedences, one, precedences, one, even it cannot be equal, this one plus goes out.

68
00:04:21,230 --> 00:04:25,020
Nothing in the stack, so nothing in the statements, no problem.

69
00:04:25,400 --> 00:04:28,060
The president said zero, let us assume that position zero.

70
00:04:28,280 --> 00:04:30,650
So push that one into the stack minus.

71
00:04:31,490 --> 00:04:41,800
Then Nixon goes to postfix slash explosive devices to say it comes here, then e then end of expression.

72
00:04:41,830 --> 00:04:43,610
So these to an item.

73
00:04:44,990 --> 00:04:45,530
That's all.

74
00:04:46,740 --> 00:04:52,700
This is the procedure, I will a table and show you what all things are happening in this procedure

75
00:04:53,240 --> 00:05:00,250
so that it is easy for you to remember that I have taken a table having all these symbols, that is

76
00:05:00,290 --> 00:05:01,330
expression is here.

77
00:05:01,670 --> 00:05:06,500
So every symbolistic and separately and this is what a stack of contents are.

78
00:05:06,500 --> 00:05:08,320
And this is postfix expression.

79
00:05:08,840 --> 00:05:11,700
So how the procedure was working, let us look.

80
00:05:12,410 --> 00:05:13,580
There is nothing in the stack.

81
00:05:13,580 --> 00:05:15,330
Initially, this is all print.

82
00:05:15,350 --> 00:05:19,790
It will go into postfix operator, push it into the stack.

83
00:05:20,060 --> 00:05:22,220
Supposed to remain to be.

84
00:05:22,220 --> 00:05:23,040
It's an opening.

85
00:05:23,360 --> 00:05:30,240
So this will go into postfix and stack this containing just plus then start statis push into the stack.

86
00:05:30,260 --> 00:05:36,110
So first start will be there next plus the libertarians and the stack from top if you will see and the

87
00:05:36,110 --> 00:05:39,990
postfix the same thing Amy then see as an opening.

88
00:05:40,010 --> 00:05:47,600
So this goes into the postfix and the stack contains start and plus, then minus one it is minus the

89
00:05:47,600 --> 00:05:53,630
start will pop out so and it will be added to postfix then plus also swapped out, it will be added

90
00:05:53,630 --> 00:05:56,390
to postfix and minus is pushed into the stack.

91
00:05:56,780 --> 00:06:05,340
B it directly goes into postfix ABC plus and B then Steiger's having minus then slash.

92
00:06:05,390 --> 00:06:10,910
So it will be pushed into the stack because it is having higher precedence then minus and the postfix

93
00:06:10,910 --> 00:06:11,660
remain same.

94
00:06:11,660 --> 00:06:22,700
ABC staff must be then e this goes into postfix ABC staff plus the E and the stack contents are seen

95
00:06:23,120 --> 00:06:25,070
now and of expression.

96
00:06:25,080 --> 00:06:26,360
So here the expression then.

97
00:06:26,360 --> 00:06:29,390
So everything from the stack popped out and added to Poohsticks.

98
00:06:29,750 --> 00:06:36,050
So the result is a b c start plus the E slash minus.

99
00:06:36,200 --> 00:06:37,360
So there's the postfix.

100
00:06:37,760 --> 00:06:39,830
So each step you can see it in the table.

101
00:06:39,830 --> 00:06:46,370
C usually when you have any procedure that is iterative, that is repeating and you want to see the

102
00:06:46,370 --> 00:06:51,870
contents of all the variables or the data structure, you can prepare a table and you can see the content.

103
00:06:51,870 --> 00:06:57,650
And so we are looking at the contents of infix expression and the stack contents as well as contents

104
00:06:57,650 --> 00:06:59,390
of postfix, how they are changing.

105
00:06:59,390 --> 00:07:00,380
We are looking at them.

106
00:07:01,280 --> 00:07:03,800
So this is a tracing of the procedure we have done.

