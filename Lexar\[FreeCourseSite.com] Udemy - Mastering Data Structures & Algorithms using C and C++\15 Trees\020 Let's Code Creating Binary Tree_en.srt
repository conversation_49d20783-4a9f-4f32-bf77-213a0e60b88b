1
00:00:00,240 --> 00:00:06,960
In this video, we look at a program for Binary Tree, we will create a binary tree and also we will

2
00:00:06,960 --> 00:00:09,630
see traversal on binary tree.

3
00:00:10,780 --> 00:00:15,850
So for creating apparently we need to create a structure where we will be storing the addresses of the

4
00:00:15,850 --> 00:00:20,200
nodes, so already we have written a C language program for.

5
00:00:21,180 --> 00:00:27,390
Do you think so I will use that same board in my new project, so I will copy this, there's a structure

6
00:00:27,390 --> 00:00:31,620
for you and create function, is there?

7
00:00:32,130 --> 00:00:34,580
And you and dequeue is also there.

8
00:00:35,250 --> 00:00:36,810
So I will take all these.

9
00:00:37,910 --> 00:00:43,460
And display, I don't need a display function, OK, this is enough, I will copy this one.

10
00:00:45,820 --> 00:00:48,070
Then I'll start a new project for.

11
00:00:49,560 --> 00:00:50,250
Tres.

12
00:00:51,720 --> 00:00:52,590
New project.

13
00:00:53,760 --> 00:00:56,880
I'll give you the name as a tree, and it's a C language project.

14
00:00:59,650 --> 00:01:00,790
So, yes, created.

15
00:01:05,170 --> 00:01:07,060
Know object is created.

16
00:01:08,080 --> 00:01:15,190
Now, here, I will add a header file where I will try on the functions that are required for two.

17
00:01:16,190 --> 00:01:17,630
So I will say new.

18
00:01:19,120 --> 00:01:22,660
And fine, and let it be a fair fight.

19
00:01:23,320 --> 00:01:27,550
Yes, it's a harder fight and harder if I lean, I will call it as queue.

20
00:01:29,200 --> 00:01:33,190
So acute that is created and inside this, I will paste everything.

21
00:01:34,920 --> 00:01:40,830
So I have great function and you and dequeue functions, these functions are for Circular Quay so that

22
00:01:40,830 --> 00:01:47,460
I can use that great circularly, then here I need more functions, so I will write on them.

23
00:01:47,710 --> 00:01:50,010
That is is empty function.

24
00:01:50,020 --> 00:01:54,300
I will write on it should take a Q as a parameter that is stuck.

25
00:01:54,600 --> 00:01:55,920
Q And this is.

26
00:01:55,950 --> 00:01:57,810
Q And it's called the value.

27
00:01:57,810 --> 00:02:00,450
It should check if friend is equal to red.

28
00:02:00,750 --> 00:02:09,240
So kudos friend if it is equal to Q rather than it should return whatever the result of the security

29
00:02:09,240 --> 00:02:09,940
operator should.

30
00:02:10,560 --> 00:02:13,360
If it is true, it will have to do otherwise it will return false.

31
00:02:14,070 --> 00:02:15,290
So there is a Q available.

32
00:02:15,660 --> 00:02:23,170
Now one important thing as this Q is going to store addresses of three nodes, so not integer type data.

33
00:02:23,520 --> 00:02:27,530
So here itself I will define a structure for node.

34
00:02:27,570 --> 00:02:30,810
This is a Pinard so I can say node or train or anything.

35
00:02:31,050 --> 00:02:43,470
The members of this one struck note that is alkyl and integer data and struck node are child.

36
00:02:45,990 --> 00:02:50,520
There's a structure for a. I have to modify this so that it can store these nodes.

37
00:02:50,550 --> 00:02:54,110
So this is a node type of point.

38
00:02:54,300 --> 00:02:58,890
So this is a double point that I should make at this point.

39
00:02:58,900 --> 00:03:05,190
It is for Uhry and this is for point of type, nor is it going to store pointer of type nodes.

40
00:03:07,290 --> 00:03:12,860
Then the skill structure than here, when you are creating an array, it is not integer type pointer,

41
00:03:13,350 --> 00:03:20,340
it's not the type of pointer to a pointer that a stable pointer and hide spanel.

42
00:03:21,740 --> 00:03:31,190
Then the number of elements are size and it should have size of know the type of pointis, so it's an

43
00:03:31,190 --> 00:03:32,120
array of points.

44
00:03:32,210 --> 00:03:37,040
So the arrays created like this dynamically from heap.

45
00:03:38,040 --> 00:03:44,510
Then new function, I have to modify it, eliminate, should insert is not the type of pointer.

46
00:03:45,580 --> 00:03:54,040
The dequeue should return me know the type of plane that is and this X should also be not the type of

47
00:03:54,040 --> 00:03:57,180
pointer and it should be initially null.

48
00:03:57,550 --> 00:03:58,810
It's not minus one.

49
00:03:58,840 --> 00:03:59,560
It is not.

50
00:04:00,100 --> 00:04:06,820
So I have done sufficient changes that are useful for making this UCU useful for three.

51
00:04:08,550 --> 00:04:10,500
Now, let us go to the main function.

52
00:04:11,470 --> 00:04:18,579
Now, outside the main function, I will create a pointer of a node as a global variable so that it

53
00:04:18,579 --> 00:04:19,640
can be accessed anywhere.

54
00:04:19,660 --> 00:04:24,590
So it's easy for accessing a route and route is initially null.

55
00:04:26,290 --> 00:04:29,290
Now we will see create function, create.

56
00:04:30,650 --> 00:04:37,610
So as I have explained you the same way I was out on the court here, so I need no pointers, so ladies

57
00:04:37,610 --> 00:04:41,060
and pointers like B and E that I was using.

58
00:04:42,420 --> 00:04:42,810
Then.

59
00:04:43,830 --> 00:04:49,830
Also, I need a variable for reading the data, then I should also create a queue queue data structure

60
00:04:50,220 --> 00:04:52,530
to then I should create a queue.

61
00:04:52,530 --> 00:04:56,750
So for creating a queue, I should send a site.

62
00:04:56,770 --> 00:04:58,160
So I will take the I handed.

63
00:04:58,170 --> 00:04:59,940
So that is sufficient for queue.

64
00:05:00,300 --> 00:05:03,000
And I should also send you as a parameter.

65
00:05:03,030 --> 00:05:04,170
That is my address.

66
00:05:04,980 --> 00:05:10,920
First thing is I should know what is the value in a root and root value.

67
00:05:13,200 --> 00:05:16,200
Then Caniff person Tildy.

68
00:05:17,460 --> 00:05:19,200
Is the value in the variable X?

69
00:05:20,390 --> 00:05:22,730
Then create a route north.

70
00:05:23,610 --> 00:05:27,570
A road, a sign struck Northpoint.

71
00:05:29,440 --> 00:05:30,250
Malaak.

72
00:05:32,150 --> 00:05:42,830
Size of struck law and order will be created, then ruled the data should be set as X and ruled alkyl

73
00:05:43,550 --> 00:05:45,200
are ruled.

74
00:05:47,320 --> 00:05:55,540
Archdale, a sign none more so than auditorily, then I should insert this route in into.

75
00:05:56,500 --> 00:06:07,510
So you do this and you in, you know, as I have shown you, I should have a loop, while not is empty

76
00:06:07,840 --> 00:06:08,290
queue.

77
00:06:08,340 --> 00:06:09,380
This is called value.

78
00:06:09,460 --> 00:06:12,490
So just queue that to a pointer P.

79
00:06:12,970 --> 00:06:16,500
I should get you from the queue and take a pointer.

80
00:06:16,510 --> 00:06:21,870
So for every note taken on from the queue, I should add four left child and right child take the left

81
00:06:22,680 --> 00:06:23,200
voucher value.

82
00:06:23,450 --> 00:06:26,110
If it is not minus one then I should answer that.

83
00:06:26,350 --> 00:06:28,500
So I will write on that procedure printf.

84
00:06:30,510 --> 00:06:33,450
And the left child.

85
00:06:35,500 --> 00:06:36,940
Then ganef.

86
00:06:38,890 --> 00:06:39,900
Person Tildy.

87
00:06:41,150 --> 00:06:50,450
Big data and X and if X is not equal to minus one, so I said that if it is minus one, we assume that

88
00:06:50,810 --> 00:06:52,090
the value is not there.

89
00:06:53,880 --> 00:06:55,560
X is not equal to minus one.

90
00:06:55,980 --> 00:07:04,940
Then we will create a. ursine, I should create a one so that Gordis here I will copied and pasted here.

91
00:07:05,610 --> 00:07:06,870
So the note is created.

92
00:07:08,200 --> 00:07:11,050
Then these data should be set as X.

93
00:07:12,630 --> 00:07:23,220
And these alkyl as well as these are child, should be set as null because this will be inserted as

94
00:07:23,230 --> 00:07:29,230
a leaf, not then he's a child that is left child should be set aside.

95
00:07:29,710 --> 00:07:33,550
And also this new node I should inserted in tube.

96
00:07:34,630 --> 00:07:38,080
Anderson, cue the city is inserted in the queue.

97
00:07:39,290 --> 00:07:45,130
Now, as I have done for live child, the same thing should be done for the right yet also so happy

98
00:07:45,150 --> 00:07:45,880
the school.

99
00:07:47,890 --> 00:07:54,790
And it here, not instead of seeing left child, just say, a child and a child.

100
00:07:57,370 --> 00:07:59,020
And the right.

101
00:08:00,050 --> 00:08:01,570
That's all everything the same.

102
00:08:04,470 --> 00:08:11,550
So this is a little lendee function for creating a pre and rooters global variable, so it is accessible

103
00:08:11,550 --> 00:08:15,090
everywhere so I can directly create function.

104
00:08:15,570 --> 00:08:19,020
Now here inside main function, I will simply call create function.

105
00:08:19,020 --> 00:08:26,160
We should create a pre then after that for displaying I should have a function for pre order traversal

106
00:08:26,520 --> 00:08:30,390
and two that I will send Ruud and I will write a recursive function here.

107
00:08:31,020 --> 00:08:32,850
Void pre order.

108
00:08:34,500 --> 00:08:36,179
Struck Naude.

109
00:08:37,690 --> 00:08:48,430
Starboy and if he does the simple code that we have already seen print person Tildy, that some space

110
00:08:48,430 --> 00:08:51,040
based data, but in the data.

111
00:08:54,390 --> 00:08:55,140
Then Paul.

112
00:08:56,230 --> 00:08:59,200
Preorder upon bees left child.

113
00:09:01,350 --> 00:09:05,640
And Paul, preorder upon piece of child.

114
00:09:08,450 --> 00:09:17,180
That's all now after creation of a tree calling preorder function, so man is having just two calls

115
00:09:17,180 --> 00:09:19,150
to functions, create and preorder.

116
00:09:21,260 --> 00:09:26,300
And one thing on the top, I should include the header file that is due.

117
00:09:28,370 --> 00:09:30,320
Children have included.

118
00:09:32,180 --> 00:09:32,770
That's all.

119
00:09:33,530 --> 00:09:35,450
Now, let us come on the program.

120
00:09:37,860 --> 00:09:39,200
Oops, I got an error.

121
00:09:40,730 --> 00:09:47,180
Redefinition of creed, we have written a function called Create for you, so I will change this create

122
00:09:47,180 --> 00:09:54,500
function, does it recreate tribal leaders, recreate function and inside the main function also recreate

123
00:09:54,500 --> 00:09:55,010
function.

124
00:09:55,130 --> 00:09:56,230
Now, let us run this.

125
00:09:57,080 --> 00:09:59,210
Now the program is running into root.

126
00:09:59,210 --> 00:10:01,510
So root value is ten root value.

127
00:10:01,520 --> 00:10:03,040
Is it having value left?

128
00:10:03,440 --> 00:10:04,400
Yes, 20.

129
00:10:05,660 --> 00:10:07,550
As it happens right yesterday.

130
00:10:10,490 --> 00:10:13,230
Then and the left child, who's left child?

131
00:10:13,280 --> 00:10:15,770
Oh, I missed this one, I should give a message.

132
00:10:15,770 --> 00:10:21,660
I'll stop this program and I will show here that is whose left child we want to find out.

133
00:10:21,950 --> 00:10:26,360
So enter left child off person Tildy.

134
00:10:26,720 --> 00:10:30,730
So here I will print the data so that I can know who's left.

135
00:10:30,740 --> 00:10:31,700
Shall we are asking.

136
00:10:32,510 --> 00:10:39,950
And even here, I should make a change when I'm asking for the left child off percentile so I can know

137
00:10:39,950 --> 00:10:45,020
who's left here and Asians are being asked on the program.

138
00:10:47,450 --> 00:10:50,090
So asking for the root values, root value is 10.

139
00:10:51,380 --> 00:10:57,230
The center left child, Bindi and Rachel Purdy, and the love child of 20, that is 14 and Rachel is

140
00:10:57,230 --> 00:11:01,150
50 left out of today's 60 70.

141
00:11:01,460 --> 00:11:03,370
And after that, I don't have any notes.

142
00:11:03,370 --> 00:11:04,750
So minus one I'm entering.

143
00:11:04,760 --> 00:11:09,590
So it will not do anything to not take any notes now.

144
00:11:09,680 --> 00:11:11,080
This is the preorder traversal.

145
00:11:11,600 --> 00:11:17,270
You can see this and 20, 40 and 50, 30, 60, 70.

146
00:11:17,280 --> 00:11:18,680
These are coming on the right hand side.

147
00:11:18,690 --> 00:11:20,690
These are coming on left hand side and this route.

148
00:11:21,200 --> 00:11:22,940
So this is the preorder traversal.

149
00:11:23,360 --> 00:11:23,990
That's it.

150
00:11:24,500 --> 00:11:28,490
So I will include one more function like this for the preorder.

151
00:11:28,490 --> 00:11:34,010
So I will write two more functions and that is for in order an all starter.

152
00:11:34,250 --> 00:11:39,350
Copy this instead of pre order, I will just name it as in order.

153
00:11:40,230 --> 00:11:45,390
And the sprinter should be in between, so at this one.

154
00:11:48,290 --> 00:11:49,640
And paste it here.

155
00:11:51,560 --> 00:11:54,280
Nine stuff preorder, this is in order.

156
00:11:55,170 --> 00:11:57,360
Now, this is both started.

157
00:11:58,230 --> 00:11:58,890
Oast.

158
00:12:01,610 --> 00:12:03,260
And therefore should not be the.

159
00:12:04,330 --> 00:12:06,280
And this is order function.

160
00:12:07,480 --> 00:12:09,220
This is Bulstrode, a function.

161
00:12:10,240 --> 00:12:13,420
Now and then, I should bring the value, yes.

162
00:12:15,840 --> 00:12:21,600
Now, after preorder, I'll give you a new line and then then we'll start a.

163
00:12:22,700 --> 00:12:29,930
New line, then this is all order I will call the function that is posted at.

164
00:12:31,290 --> 00:12:34,200
First order, always a small.

165
00:12:35,880 --> 00:12:36,830
Passing route.

166
00:12:39,060 --> 00:12:40,470
Now, let us run and check.

167
00:12:42,490 --> 00:12:49,210
I'll give only a few notes, root value is a 10, OK, left child is 20, Rachel is 30.

168
00:12:49,220 --> 00:12:55,780
Then after that I don't have any N minus one, minus one and minus one, minus one.

169
00:12:56,350 --> 00:13:01,190
C preorder is 10, 20, 30, and for starter is 30, 30.

170
00:13:01,220 --> 00:13:03,490
And it's perfect.

171
00:13:04,360 --> 00:13:10,630
So that's all in this video we have seen how to create a treat was quite lendee because I have to prepare

172
00:13:10,750 --> 00:13:11,470
you also.

173
00:13:11,710 --> 00:13:13,930
That is useful for creation of a tree.

174
00:13:14,560 --> 00:13:15,700
That's all in this video.

