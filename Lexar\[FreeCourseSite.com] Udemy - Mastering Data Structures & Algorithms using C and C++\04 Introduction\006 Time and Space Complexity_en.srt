1
00:00:00,390 --> 00:00:04,110
Let us talk about Time and Space Complexity.

2
00:00:04,120 --> 00:00:08,760
This is a very important topic, and sometimes it is difficult for the students to understand though it

3
00:00:08,760 --> 00:00:11,370
is not that difficult, it's not complex.

4
00:00:11,370 --> 00:00:15,840
Though the word complexity is used, it is very simple.

5
00:00:15,840 --> 00:00:17,090
Let me explain you,

6
00:00:17,190 --> 00:00:19,150
What does it mean by Time Complexity?

7
00:00:19,170 --> 00:00:22,530
Then, afterwards I'll tell you about Space Complexity also.

8
00:00:22,530 --> 00:00:26,750
So, first let us start with Time Complexity. See, in daily life,

9
00:00:26,760 --> 00:00:32,640
when we do any work, any task, we want to know how much time it takes for that, performing that particular

10
00:00:32,640 --> 00:00:37,410
task. Let us suppose, it is a one hour task, or a one day task.

11
00:00:37,460 --> 00:00:41,540
So that amount of time required, depending on the work that we have to do.

12
00:00:42,260 --> 00:00:47,990
So, usually in daily life, we measure the time based on the work that we have to do.

13
00:00:48,560 --> 00:00:54,010
So, now we're using machines to do our work, that is computers to do our work.

14
00:00:54,080 --> 00:00:57,950
So, we want to know how much time the machine takes for doing the same task.

15
00:00:58,280 --> 00:01:05,090
Like, if a person is making a bread in 15 minutes, 20 minutes then, if you're using machine for making

16
00:01:05,090 --> 00:01:07,240
that bread, How much time the machine takes?

17
00:01:07,240 --> 00:01:11,980
We are interested in that. What if ,machine is taking 45 hours, then it's better to do it manually.

18
00:01:12,710 --> 00:01:15,870
So, how much time a machine takes is very important for us.

19
00:01:15,920 --> 00:01:18,020
So, we use computers for problem solving,

20
00:01:18,050 --> 00:01:22,710
What type of problem solving? The work that we used to do using pen and paper.

21
00:01:22,760 --> 00:01:23,520
That same work,

22
00:01:23,540 --> 00:01:25,230
We want our computers to do that.

23
00:01:25,520 --> 00:01:30,800
So, computers are used for performing computation task. So, computation also needs time.

24
00:01:31,370 --> 00:01:35,270
So, you want to measure, how much time a machine will take.

25
00:01:35,270 --> 00:01:40,640
So, actually that depends on the process, or the procedure for completing that task.

26
00:01:40,970 --> 00:01:46,160
So, the Time Complexity basically depends on the procedure that you are adopting. So, for giving you the

27
00:01:46,160 --> 00:01:49,970
idea, I have taken various examples. Using these examples,

28
00:01:49,970 --> 00:01:55,040
I'll explain you, what procedure may take what amount of time.

29
00:01:55,220 --> 00:02:01,150
So, let us start with array. See, this is an array of some size, and some elements are there.

30
00:02:01,160 --> 00:02:02,710
So how many elements are there?

31
00:02:02,720 --> 00:02:05,540
See, this is the example already I have taken,

32
00:02:05,540 --> 00:02:11,810
and you can count, there are total 10 elements but will it be always 10 elements? or depends on the

33
00:02:11,810 --> 00:02:12,300
problem?

34
00:02:12,650 --> 00:02:13,760
So, depending on the problem.

35
00:02:13,820 --> 00:02:15,110
So, how many elements may be there in

36
00:02:15,110 --> 00:02:20,720
the list of elements in an array? We say n elements. We don't know how many elements will be there, maybe

37
00:02:20,720 --> 00:02:24,200
10, or 10 000, or 10 000 000,

38
00:02:24,260 --> 00:02:25,170
We don't know.

39
00:02:25,190 --> 00:02:32,920
So we say n. n means, some number of elements, that number of elements may start from 1, up-to infinity.

40
00:02:32,990 --> 00:02:37,430
So, we don't define infinity, so, up-to maximum number, or whatever that number you can imagine, you take

41
00:02:37,430 --> 00:02:44,150
that one. So, n means not just 5 or 10 does, this is the first important thing, mostly students get confused

42
00:02:44,150 --> 00:02:44,460
here.

43
00:02:44,960 --> 00:02:47,230
So, n means some number of elements.

44
00:02:47,270 --> 00:02:51,600
So, let us say there are n elements in this list.

45
00:02:51,760 --> 00:02:54,300
Now, What do you want to do with these elements?

46
00:02:54,300 --> 00:02:56,120
I want to add all of them.

47
00:02:56,340 --> 00:03:01,560
So, you have to go through all of them, one by one you have to take the element and go on adding it.

48
00:03:02,290 --> 00:03:05,220
So, how much time it takes depends on the number of elements.

49
00:03:05,220 --> 00:03:10,940
So, what is the time taken? n, we say n. Then next thing, what do you want to do?

50
00:03:11,030 --> 00:03:15,680
I want to search for a particular number, whether element 12 is there or not, search for it.

51
00:03:15,800 --> 00:03:16,580
Yes it is there.

52
00:03:17,120 --> 00:03:18,970
I want to search for element 21,

53
00:03:18,980 --> 00:03:21,080
Is it there or not? Check for all.

54
00:03:21,080 --> 00:03:22,730
No, 21 is not there.

55
00:03:22,970 --> 00:03:25,510
So, utmost how much time is it taking?

56
00:03:25,610 --> 00:03:28,750
It depends on the number of elements that you have to compare.

57
00:03:28,820 --> 00:03:31,130
So, how many elements are there? n elements are there.

58
00:03:31,190 --> 00:03:32,430
So, what is the time taken?

59
00:03:32,840 --> 00:03:40,440
n. So the time is n. So, it means in a list, if you have some n elements and you are going through all

60
00:03:40,440 --> 00:03:43,610
of them just once then the time is n.

61
00:03:44,370 --> 00:03:53,670
So this n, we represent it as a degree. So, we can say Order(n). We usually use the term

62
00:03:53,790 --> 00:03:56,060
order; degree or order are same thing,

63
00:03:56,130 --> 00:04:04,740
So, we use the term Order(n). There are other terms like, bigo, omega, theta, that I will explain

64
00:04:04,740 --> 00:04:09,990
you at the end of the course, because till then, you will be having the good understanding of time complexities

65
00:04:09,990 --> 00:04:16,320
then I can explain you what is bigo, omega, and theta. So, through out the course I'll be using term

66
00:04:16,769 --> 00:04:18,360
Order( ).

67
00:04:18,510 --> 00:04:22,470
So, when we know what are asymptotic notations, like bigo, omega,theta, then you will understand

68
00:04:22,500 --> 00:04:28,520
how to use them. So, let's say Order( ), then next thing.

69
00:04:28,520 --> 00:04:33,350
Next very important thing, if you want to access all the elements, then what is the code that you have

70
00:04:33,350 --> 00:04:36,820
to write? for(i=0;i<n;i++)

71
00:04:36,860 --> 00:04:43,820
for(i=0;i<n;i++), then whatever you want to do here, means you want to add all of them, or

72
00:04:43,880 --> 00:04:50,780
search something, or count number of elements, or finding maximum element, whatever you want to do, that logic

73
00:04:50,780 --> 00:04:51,570
comes here.

74
00:04:51,590 --> 00:04:53,870
So, that procedure comes here.

75
00:04:53,870 --> 00:05:00,690
So, whatever the procedure may be then, this for loop is taking us through all those elements n. So, how

76
00:05:00,700 --> 00:05:01,850
many elements? n elements.

77
00:05:01,850 --> 00:05:09,940
So, what is the time? Order(n). Now listen, the very important thing. For finding the time complexity either

78
00:05:09,940 --> 00:05:16,240
you can measure the time based on the work that you are doing, means according to your procedure, if you're clear

79
00:05:16,240 --> 00:05:22,930
with your procedure, you can know the time, or else from the code, program code also you can find the

80
00:05:22,930 --> 00:05:29,950
time complexity. If there is a for loop, going through 0 to last element, then it's n, it's taking Order(n)

81
00:05:29,950 --> 00:05:38,110
time, whatever it is there inside will repeat for n times. So, we analyse based on the procedure also,

82
00:05:38,440 --> 00:05:45,100
based on the code also. And the most confusing thing is, when the code is given we get confused

83
00:05:45,220 --> 00:05:48,480
how to analyze this one. So, actually what the code is doing,

84
00:05:48,520 --> 00:05:54,730
You do that work, and based on the work you analyse it, it's very simple. It's very simple. If you don't

85
00:05:54,730 --> 00:06:01,320
want to understand what the code is doing, then it's a difficult task. So, if a for loop is used means there is

86
00:06:01,320 --> 00:06:06,300
a chance that time is Order(n). Now, let us move to the next situation.

87
00:06:06,400 --> 00:06:10,920
See, in this list, see suppose, being on the 1st element,

88
00:06:10,920 --> 00:06:18,240
I am comparing or processing all other elements once. Then, being on the second element,

89
00:06:18,240 --> 00:06:20,870
Again I am processing all other elements,

90
00:06:20,880 --> 00:06:24,360
maybe I'm comparing for sorting purposes, whatever the purpose may be,

91
00:06:24,480 --> 00:06:30,350
this 5, I am comparing or processing with the rest of the elements, like that for each element,

92
00:06:30,420 --> 00:06:33,280
I'll be checking with all the rest of the elements.

93
00:06:33,450 --> 00:06:38,300
So, for 1 value, it will check for all, means n elements.

94
00:06:38,490 --> 00:06:39,750
Then, for next value also,

95
00:06:39,750 --> 00:06:47,240
If it is checking for all, processing for all, then n. n elements. So, n elements are being processed, but

96
00:06:47,690 --> 00:06:52,910
How many times? For each element n elements are processed. So, this will be n^2.

97
00:06:52,910 --> 00:06:56,750
This is n*n, that is n^2. So, we can say Order(n^2).

98
00:06:59,490 --> 00:07:02,300
So, this is another case. Now, in this same case,

99
00:07:02,340 --> 00:07:08,550
I'll show you one more thing, for processing like this how the code should look like? The code should

100
00:07:08,550 --> 00:07:10,060
have a nested for loop.

101
00:07:10,150 --> 00:07:17,100
Again, I should have for loop that is, for(j=0;j<n;j++), and inside this

102
00:07:17,180 --> 00:07:20,950
I'll be writing that processing, whatever the work I want to do.

103
00:07:21,000 --> 00:07:22,050
These will be the statements.

104
00:07:22,410 --> 00:07:29,190
So, when you have two nested for loops, it's n^2, simple. You don't have to check line by line every

105
00:07:29,190 --> 00:07:34,830
statement and all, just from the code you got the rough idea, you can say it's n^2. But sometimes,

106
00:07:34,870 --> 00:07:37,340
for analyzing the code we check every line.

107
00:07:37,620 --> 00:07:39,920
So, the idea is, we should not miss any line,

108
00:07:39,930 --> 00:07:40,590
We should check

109
00:07:40,590 --> 00:07:41,970
check it thoroughly.

110
00:07:41,970 --> 00:07:48,810
That's why we write the time complexity, but overall, the time complexity is written in terms of the degree

111
00:07:48,810 --> 00:07:53,490
of a polynomial. So, it is Order(n^2). Nested for loop means n^2.

112
00:07:53,730 --> 00:07:55,670
So, let us follow it like a formula.

113
00:07:56,890 --> 00:08:00,100
Third situation, for the same array.

114
00:08:00,250 --> 00:08:04,720
Suppose, being on first element, I'm processing the rest of the elements.

115
00:08:06,660 --> 00:08:12,990
So, n-1 elements I'm processing, then being on the second element, I'm processing the rest of

116
00:08:12,990 --> 00:08:15,300
the elements, I'm not processing upon this one, right?

117
00:08:15,810 --> 00:08:22,500
So n-2. Then, being on third element, I'm processing rest of them.

118
00:08:22,500 --> 00:08:23,920
It depends what you are doing.

119
00:08:23,940 --> 00:08:24,230
Right?

120
00:08:24,480 --> 00:08:29,880
So, imagine something you are doing. So, you're processing rest of the elements. So, this is n-3.

121
00:08:30,180 --> 00:08:30,690
Like that,

122
00:08:30,990 --> 00:08:37,610
It will be going on reducing, then finally it will be 4 elements, 3 elements, 2 elements, then 1.

123
00:08:37,679 --> 00:08:38,700
What is this?

124
00:08:38,900 --> 00:08:43,640
(n*(n-1))/2, sum of first natural numbers,

125
00:08:43,650 --> 00:08:45,330
So, it is not n, it is n-1.

126
00:08:45,960 --> 00:08:50,030
So this will be (n^2 - n)/2.

127
00:08:50,040 --> 00:08:52,210
So, what is the degree of this polynomial?

128
00:08:52,260 --> 00:08:53,220
This is n^2.

129
00:08:53,580 --> 00:08:59,830
So, it is Order(n^2). So, that's what. The time complexity that we are writing,

130
00:08:59,940 --> 00:09:02,140
We are writing the degree of a polynomial.

131
00:09:02,160 --> 00:09:10,100
See, this time I have done detailed analysis and I got a polynomial, and for that I got a function, this is

132
00:09:10,110 --> 00:09:15,180
like a function, and the degree of the function is n^2, so time is n^2.

133
00:09:15,180 --> 00:09:21,230
If same thing, I write it in the form of a for loop, then this j will not be starting from 0, but

134
00:09:21,240 --> 00:09:26,660
j will be starting from i+1. See, now again two nested for loops.

135
00:09:26,760 --> 00:09:29,030
So utmost, this will take n^2 time.

136
00:09:29,390 --> 00:09:34,150
Yes. Then next, I'll show you one more situation.

137
00:09:34,160 --> 00:09:35,250
Fourth one.

138
00:09:35,260 --> 00:09:36,180
Let us look at this.

139
00:09:36,220 --> 00:09:38,540
I'll remove this one. Now in this array,

140
00:09:38,540 --> 00:09:42,360
Suppose, first of all we are processing the middle element, suppose 4 is the middle element, right? There

141
00:09:42,370 --> 00:09:43,780
are 10 elements.

142
00:09:43,780 --> 00:09:46,120
So 5th element is the middle element.

143
00:09:46,330 --> 00:09:49,690
Then next, either on the left side or on the right side

144
00:09:49,760 --> 00:09:54,190
again we go in the middle, suppose this is the middle element on this side. Then again, middle

145
00:09:54,190 --> 00:09:55,060
element on this side.

146
00:09:55,360 --> 00:09:58,630
So, in this way we are not processing the entire list.

147
00:09:58,630 --> 00:10:00,690
We are processing half of the list,

148
00:10:00,700 --> 00:10:01,900
Then again it's half,

149
00:10:02,050 --> 00:10:03,100
Then again it's half,

150
00:10:03,100 --> 00:10:06,420
So, that process is always dividing the list by two.

151
00:10:06,430 --> 00:10:13,870
So, when something is successively divided until it reaches one, that is represented as log,

152
00:10:15,160 --> 00:10:17,140
And we are divided by 2, 1/2.

153
00:10:17,230 --> 00:10:26,350
So, log of n elements, "log n". So, the time complexity is log n, if we are not processing all elements.

154
00:10:26,350 --> 00:10:32,140
Then, if I write for that one, then the code will look like this, for(i=n;i>1;i=i/2)

155
00:10:35,000 --> 00:10:40,440
for(i=n;i>1;i=i/2)

156
00:10:44,720 --> 00:10:46,990
Now, just by looking at for loop,

157
00:10:47,000 --> 00:10:48,140
Don't tell

158
00:10:48,140 --> 00:10:51,970
blindly Order(n), because just now, previously we saw Order(n)

159
00:10:52,150 --> 00:10:52,700
Read it,

160
00:10:52,700 --> 00:10:55,280
How the for loop is behaving.

161
00:10:55,280 --> 00:10:58,990
That's why, we check it thoroughly. When we are reading the code,

162
00:10:59,060 --> 00:10:59,950
We totally check it

163
00:10:59,950 --> 00:11:03,350
line by line. So that, we should not go wrong.

164
00:11:04,250 --> 00:11:07,120
But overall we need answer, log n.

165
00:11:07,160 --> 00:11:16,530
If the value of counter i is getting divided by 2 every time, then it is log n, divided by 2, log n

166
00:11:16,530 --> 00:11:18,890
base 2. And how long is it happening?

167
00:11:18,890 --> 00:11:20,390
Until it reaches 1.

168
00:11:20,510 --> 00:11:24,990
The same thing can even be written by using while loop, See, i=n;

169
00:11:25,430 --> 00:11:34,430
And, while(i>1) and some processing; i=i/2;

170
00:11:34,450 --> 00:11:36,670
Same thing. Instead of for loop,

171
00:11:36,680 --> 00:11:44,760
You can use while loop also. Usually, when this is not on increment, one by one every time, for loop is

172
00:11:44,760 --> 00:11:46,970
not on increment by one every time,

173
00:11:46,980 --> 00:11:53,460
We prefer using while loop. But in C, C++, Java programming, when for loops are there, people use for loop for

174
00:11:53,460 --> 00:11:57,580
this purpose also. For loop is used for counters which are on increment by 1.

175
00:11:57,900 --> 00:12:02,820
So anyway, when this is written, you have to read it thoroughly and find out how much time it is taking.

176
00:12:02,820 --> 00:12:09,960
So, we have seen 4 different behaviors of procedures that are performed on array, and also I have shown

177
00:12:09,960 --> 00:12:16,360
you the code, and also shown you, from the code how we can analyze from the work.

178
00:12:16,440 --> 00:12:20,660
So, if you are analyzing from the work, the process, that's more better.

179
00:12:21,420 --> 00:12:23,270
Now, let us proceed ahead.

180
00:12:23,310 --> 00:12:27,690
See, this is the linked list. We'll be studying about this linked list data structure later on, this will be

181
00:12:27,690 --> 00:12:32,670
same as array; like, this is list, and this also a list.

182
00:12:32,790 --> 00:12:33,990
So, what all we studied there,

183
00:12:33,990 --> 00:12:36,090
same things apply here.

184
00:12:36,090 --> 00:12:42,470
Then, next is matrix. Matrix is having how many elements? The dimensions are 4x4.

185
00:12:42,860 --> 00:12:50,510
So, total how many elements? If dimensions is nxn, then total, n^2 elements. So, when you're

186
00:12:50,510 --> 00:12:55,730
processing upon a matrix, then it will require n^2 amount of time,

187
00:12:55,730 --> 00:13:02,450
If you're processing all the elements. If you say, No I'm just processing a row, then a row is having n

188
00:13:02,450 --> 00:13:05,570
elements, Order(n), no I'm processing just a column,

189
00:13:05,570 --> 00:13:13,270
So, again it's n, Order(n). If you're processing all elements, then it is n^2. Now, I'll show the

190
00:13:13,270 --> 00:13:14,400
code for that.

191
00:13:14,590 --> 00:13:23,350
I'll remove this. So, for processing a matrix of elements, we need 2 nested for loops,for(i=0;i<n;i++), and inside

192
00:13:23,350 --> 00:13:31,150
this, for(j=0;j<n;j++), whatever you want to do with those elements you

193
00:13:31,150 --> 00:13:32,510
can do that.

194
00:13:32,530 --> 00:13:38,980
So, this is n^2. Now, for 1 element, if you're processing more upon that element, and that requires,

195
00:13:38,980 --> 00:13:45,540
suppose, a loop or, suppose you are calling a function which is having a loop, then the time will be more,

196
00:13:45,550 --> 00:13:50,670
for, for, for, 3 for loops inside, then it will be n^3, right?

197
00:13:51,010 --> 00:13:53,480
If you are processing only one unit of time;

198
00:13:53,490 --> 00:14:00,010
Just one statement or two statements are there, simple statements, no loops, then it is n^2 only. Then, coming

199
00:14:00,040 --> 00:14:01,050
to this structure.

200
00:14:02,040 --> 00:14:07,110
This looks like array of linked lists, like total how many elements are there?

201
00:14:07,140 --> 00:14:12,380
So, here let us say n elements are there, these elements. So, you can see that, these are of different sizes,

202
00:14:12,510 --> 00:14:14,200
and here, array of size n.

203
00:14:15,050 --> 00:14:17,430
So total how much processing is required?

204
00:14:17,790 --> 00:14:23,700
We have to process all these n elements, as well as using this array, so we can say m+n processing

205
00:14:23,700 --> 00:14:24,470
is required.

206
00:14:25,610 --> 00:14:29,550
If you say, no I don't want to consider this, I want to consider only the number of elements. Okay,

207
00:14:29,570 --> 00:14:33,600
consider only n number of elements, Order(n).

208
00:14:34,340 --> 00:14:36,560
What does it mean by considering, not considering?

209
00:14:36,710 --> 00:14:43,960
I'll give you an idea. Just listen as an example. Suppose, you are giving a party to your friends, some

210
00:14:43,960 --> 00:14:48,370
4,5 friends you have called them on a hotel for a party. Then, what is the cost?

211
00:14:48,410 --> 00:14:50,500
The cost of the food that you're taking there.

212
00:14:50,810 --> 00:14:58,280
Then, the bill at the hotel, + travelling amount, and if any extra things that you are spending on,

213
00:14:58,970 --> 00:15:00,190
tips you are giving.

214
00:15:00,260 --> 00:15:04,590
Do you consider that amount also and include it as the cost for the party?

215
00:15:04,610 --> 00:15:10,650
It depends on you. Now, the same way, whether you want to analyze or not, it depends on you.

216
00:15:10,650 --> 00:15:14,690
So, you should be in a situation to understand, whether you need it or not.

217
00:15:14,920 --> 00:15:15,200
Right?

218
00:15:15,200 --> 00:15:19,940
Mostly, what students believe that, they are doing it because it is given as a task, as a challenge.

219
00:15:20,810 --> 00:15:23,420
So, somebody else's job they are doing.

220
00:15:23,420 --> 00:15:26,930
So, they get worried that, I should consider or not; Why I should leave it?

221
00:15:27,260 --> 00:15:29,020
So, that is your choice.

222
00:15:29,030 --> 00:15:31,750
You be in that situation and solve it.

223
00:15:31,850 --> 00:15:32,510
So, that's all.

224
00:15:32,900 --> 00:15:37,490
If you say, I don't want to consider this, means you are processing it, but you don't want to consider

225
00:15:37,490 --> 00:15:42,030
the time taken for that one. So, only Order(n), that's it.

226
00:15:42,040 --> 00:15:43,970
So, this structure is over.

227
00:15:44,170 --> 00:15:45,270
Now, last one.

228
00:15:45,400 --> 00:15:47,830
This one. This is a binary tree.

229
00:15:47,830 --> 00:15:52,660
So, if you have a binary tree structure, total elements are 7, 7 elements

230
00:15:52,660 --> 00:15:53,330
are there.

231
00:15:53,470 --> 00:15:55,240
Then, how much processing is done?

232
00:15:55,240 --> 00:16:00,220
Suppose, you are processing, searching or something, and you are processing upon the elements, only along

233
00:16:00,220 --> 00:16:04,210
the path, like this one, then this one, or this one, or this one, then this one, or this one.

234
00:16:04,210 --> 00:16:07,560
Suppose, you're processing just along a path, then what is the height?

235
00:16:07,560 --> 00:16:08,380
It depends on the height.

236
00:16:08,380 --> 00:16:10,070
So, how many elements you are processing?

237
00:16:10,120 --> 00:16:13,990
See, there are some elements, divided by 2, then divided by 2,

238
00:16:14,020 --> 00:16:19,960
until it reaches 1 element. So total seven elements are there. As from the bottom, if you see the number

239
00:16:19,960 --> 00:16:22,330
of elements are divided by 2, and divided by 2.

240
00:16:22,330 --> 00:16:25,700
So, it looks like log n. Yes, log n base 2.

241
00:16:25,960 --> 00:16:27,790
So, we'll be studying about this one.

242
00:16:27,850 --> 00:16:33,880
So, if you're spending time upon a tree, along the height of a tree, then it is log n.

243
00:16:34,210 --> 00:16:38,590
Next situation, if you say, no I want to process all the elements, then how many elements are there?

244
00:16:38,600 --> 00:16:39,010
n element.

245
00:16:39,010 --> 00:16:46,480
So, it is Order(n), then whatever the program code that you write for processing this one, that

246
00:16:46,500 --> 00:16:48,200
code also will be Order(n)

247
00:16:48,280 --> 00:16:51,310
Order(n) only, if you analyze that one.

248
00:16:51,310 --> 00:16:55,670
So, this is the general idea I have given you about time complexity. So, the time complexity is solely

249
00:16:55,670 --> 00:17:01,570
and solely dependent on the work that you are doing. Now, a little bit on space complexity.

250
00:17:01,720 --> 00:17:06,900
We want to know how much space is consumed in main memory during the execution of a program.

251
00:17:07,599 --> 00:17:16,630
So, array, n elements are there, so it's Order(n). This is, n elements are there, Order(n). Space is n,

252
00:17:16,930 --> 00:17:24,800
means, n integers, or n floats, or n doubles, whatever it is; n elements we say. We are not concerned

253
00:17:24,800 --> 00:17:30,380
about the number of bytes, right? We are not concerned about that. We are not calculating

254
00:17:30,380 --> 00:17:38,300
that, we want to know, the space is dependent on what? n. If the value of n is more, the space will

255
00:17:38,310 --> 00:17:46,960
be more, that's what the idea is. Then, here also it's n, but along with the elements you need space for links also.

256
00:17:47,090 --> 00:17:55,330
So, let's say 2n, again the degree is what? Order(n). Then, coming to this, this space is

257
00:17:55,450 --> 00:18:04,930
n^2, and this space is m+n, and the space here is, n nodes are there, so it's Order(n).

258
00:18:06,770 --> 00:18:10,560
So, the next video I'll take few program codes and show you.

259
00:18:10,590 --> 00:18:16,020
So, without looking at the processing, just from the program code, how to find out the time complexity,

260
00:18:16,020 --> 00:18:19,330
I will show you. So, that's all in this video.

