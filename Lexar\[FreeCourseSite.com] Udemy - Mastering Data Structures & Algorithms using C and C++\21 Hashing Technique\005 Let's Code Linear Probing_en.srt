1
00:00:00,540 --> 00:00:06,810
And this video, I will show you a program for harshing that is unique using linear probing.

2
00:00:06,900 --> 00:00:10,020
So we like to see language program for linear probing.

3
00:00:12,170 --> 00:00:15,230
I called the project name as harshing.

4
00:00:16,890 --> 00:00:20,250
And the language you see language, let us create a project.

5
00:00:21,280 --> 00:00:29,170
For implementing harshing, we need four hash table, so I'll call an area city of type integer and

6
00:00:29,170 --> 00:00:31,360
I will pick an array of size just 10.

7
00:00:32,720 --> 00:00:35,470
I limit the table size 10.

8
00:00:39,580 --> 00:00:45,160
Then as the of tents on the top I will use has defined for giving the size.

9
00:00:48,040 --> 00:00:48,480
As.

10
00:00:52,740 --> 00:00:57,960
Not for inserting or searching in this hash table, I need to function, so first function I will implement,

11
00:00:57,960 --> 00:00:59,430
that is insert.

12
00:01:01,330 --> 00:01:02,860
Insert it should take a.

13
00:01:03,860 --> 00:01:09,380
UTI as hash table, so I'll just give it an edge and next thing, this key.

14
00:01:10,680 --> 00:01:16,150
And the site is already mentioned as a constant, symbolic constant that is size and.

15
00:01:17,860 --> 00:01:25,990
So first of all, I will write on insert functions for that function name is Void Insert and it will

16
00:01:25,990 --> 00:01:32,820
take a hash table for inserting an element and also a key to be inserted.

17
00:01:34,170 --> 00:01:42,720
Now, this function needs index using hash function, so I will write a hash function, hash function

18
00:01:42,720 --> 00:01:43,920
will take a key.

19
00:01:45,270 --> 00:01:46,920
And Redbones index.

20
00:01:50,270 --> 00:01:55,100
So indexers key monocytes So Kimock size.

21
00:01:57,490 --> 00:02:02,390
Then this insert function should all that hash function and get the index of an element.

22
00:02:02,710 --> 00:02:07,360
So I will declare a variable index and all hash by passing a key.

23
00:02:08,880 --> 00:02:15,060
Not if that index is already occupied, then we should find the next three index.

24
00:02:16,680 --> 00:02:23,610
So first of all, I will check whether that index is free or not, if a hash table of index.

25
00:02:24,880 --> 00:02:28,660
Is not equal to zero, it means it's already occupied.

26
00:02:29,440 --> 00:02:35,320
Then I need another new index where I should insert an element so far that I will call next function

27
00:02:35,680 --> 00:02:36,890
that is broke.

28
00:02:36,940 --> 00:02:39,930
So it will find out next three space.

29
00:02:40,390 --> 00:02:47,330
So this to this function, I'll send a hash table as well as the key to be inserted.

30
00:02:47,770 --> 00:02:53,830
So let us write one more function called probe, which will linearly check and find out the next three

31
00:02:53,830 --> 00:02:56,110
space that is using linear probing.

32
00:02:56,590 --> 00:02:58,660
So function NamUs probe.

33
00:03:00,190 --> 00:03:04,270
And it takes a hash table as well as key.

34
00:03:05,880 --> 00:03:11,580
Now, again, here, let it all hash function, hash function will give the.

35
00:03:12,770 --> 00:03:19,160
Has called for the key that is indexed for the key now using a loop, I should find out the next three

36
00:03:19,160 --> 00:03:25,910
space so far that I will declare a variable, i.e., which will be incrementing until freespace is found.

37
00:03:26,330 --> 00:03:27,140
So while.

38
00:03:29,760 --> 00:03:31,110
Inside a hash table.

39
00:03:32,430 --> 00:03:38,070
Index plus, I, I should perform more than once again, that is more sites.

40
00:03:39,860 --> 00:03:43,850
And until this is not equal to zero, I should go on incrementing I.

41
00:03:45,330 --> 00:03:52,470
And once a free space with value is found, letters, written index plus I.

42
00:03:54,450 --> 00:03:56,010
Morde cise.

43
00:03:58,470 --> 00:04:00,900
So this is the free space for inserting an element.

44
00:04:02,840 --> 00:04:03,390
That's it.

45
00:04:03,680 --> 00:04:08,740
So this probe will return a new free index with a key value can be inserted.

46
00:04:09,110 --> 00:04:12,400
So at that place we will insert an element in the hash table.

47
00:04:12,740 --> 00:04:16,579
So edge of index assign key.

48
00:04:18,660 --> 00:04:23,620
So this is the insert function, let us first try this insert function and check it.

49
00:04:24,270 --> 00:04:27,300
So here I will insert a few keys, insert.

50
00:04:28,470 --> 00:04:32,180
Well, that should be inserted at index, too.

51
00:04:32,640 --> 00:04:39,210
I will also pass hash table S.G. and do a little insert and then insert.

52
00:04:40,390 --> 00:04:42,850
Hash table twenty five.

53
00:04:44,260 --> 00:04:50,110
Then I'll insert one more value, that is hash table thirty five.

54
00:04:53,450 --> 00:04:59,680
Let us see where it will be inserted and I will also insert one more value that is high, stable.

55
00:05:00,350 --> 00:05:01,250
Twenty six.

56
00:05:05,130 --> 00:05:08,520
Now, here, I'll put a break point and I will run the program.

57
00:05:11,390 --> 00:05:18,170
You can watch in the area, but no value is inserted, a hash table is empty then if I.

58
00:05:19,260 --> 00:05:24,330
Continually working not well is inserted at an extra inside the debugging, you can see.

59
00:05:25,230 --> 00:05:26,470
Then next to.

60
00:05:27,840 --> 00:05:30,540
Twenty five is sort of index five.

61
00:05:31,920 --> 00:05:32,670
The next.

62
00:05:33,930 --> 00:05:40,920
Thirty five, there was no free space, so in that next free space, that is six, not 26 should come

63
00:05:40,920 --> 00:05:42,260
here, but there is a collision.

64
00:05:42,270 --> 00:05:45,490
So 26 should be in that next free space.

65
00:05:46,290 --> 00:05:49,710
Yes, 26 is in that next free space.

66
00:05:50,860 --> 00:05:51,520
So that's it.

67
00:05:51,880 --> 00:05:57,530
So as the hash table sizes it, so I should not try to insert more than five values, so I'll stop here.

68
00:05:57,550 --> 00:05:59,380
I have already insulted four elements.

69
00:06:01,310 --> 00:06:03,360
So inserted into the elements.

70
00:06:03,380 --> 00:06:06,080
Now let us perform search.

71
00:06:07,290 --> 00:06:08,100
Or look up.

72
00:06:09,280 --> 00:06:11,020
So I will call it a surge.

73
00:06:12,240 --> 00:06:21,000
So Surge should give me an index, so it needs a hash table three and a key to be inserted.

74
00:06:23,170 --> 00:06:29,850
But first of all, it should get the index from hash table, so hash function, it should pass a key

75
00:06:30,150 --> 00:06:31,170
to get the index.

76
00:06:34,970 --> 00:06:35,800
Then why?

77
00:06:41,340 --> 00:06:47,160
So as this is linear probing, it should check whether the value is present or not, a key value is

78
00:06:47,160 --> 00:06:48,490
present at that index or not.

79
00:06:48,780 --> 00:06:50,670
If not, it should perform linear probing.

80
00:06:50,970 --> 00:06:52,950
So using my loop, I will do that.

81
00:06:52,980 --> 00:06:54,420
So for that, I will take a variable.

82
00:06:54,420 --> 00:06:56,100
I wish it's initialized to zero.

83
00:06:56,400 --> 00:06:59,480
NEVINE Until the key is found, we will search for it.

84
00:06:59,730 --> 00:07:03,000
So inside the hash table, we will index.

85
00:07:05,950 --> 00:07:07,630
Index plus I.

86
00:07:10,120 --> 00:07:16,570
More the size, so this is the hash function for linear probing, and as long as that is not equal to

87
00:07:16,750 --> 00:07:24,820
G, we will continue searching and we will be incrementing I and once it is found, we will return index.

88
00:07:26,450 --> 00:07:29,090
Plus, I marked size.

89
00:07:32,070 --> 00:07:35,310
This, I should put it inside brackets, yes.

90
00:07:36,450 --> 00:07:41,400
So this will find out an element by performing linear, probing the slope is for linear probing.

91
00:07:43,210 --> 00:07:49,040
I already have a function for probe, but that probe function is for insertion purposes.

92
00:07:49,060 --> 00:07:51,080
I'm using it not for searching.

93
00:07:51,100 --> 00:07:57,280
Also, I should have a different probe because this probe function is looking for free space and for

94
00:07:57,280 --> 00:07:57,850
searching.

95
00:07:57,850 --> 00:08:00,310
It needs to check for key value.

96
00:08:01,870 --> 00:08:07,890
Now, let us strive for search function and check, so here I will directly print the results sent by

97
00:08:08,320 --> 00:08:12,100
search function and here I will say Kee.

98
00:08:13,260 --> 00:08:18,650
Found at so-and-so index, so I use slashings.

99
00:08:21,360 --> 00:08:27,950
Again, and all search and I want to search forty, thirty five.

100
00:08:29,310 --> 00:08:31,010
So 35 isn't that pretty?

101
00:08:31,920 --> 00:08:37,020
So 35 is so that index six, it should return and let us run the program.

102
00:08:39,179 --> 00:08:43,409
Oh, so it's just taking too few parameters, I should pass hash table also.

103
00:08:47,520 --> 00:08:49,920
Yes, he found that in six.

104
00:08:51,550 --> 00:08:58,660
So that's all this is a simple C language program for Hash Table, and this is for Línea proving.

105
00:09:02,680 --> 00:09:08,470
Now, I'll give you an idea here, just a hint, so that you can implement quadratic probing, so instead

106
00:09:08,470 --> 00:09:16,480
of just I hear you have to write I inco I saw this place I into I this will be a quadratic probing.

107
00:09:17,300 --> 00:09:24,050
And wherever you are using I along with index, right, I enjoy that is I multiplied what I so here

108
00:09:24,050 --> 00:09:25,070
also you can write.

109
00:09:25,070 --> 00:09:25,790
I enjoy.

110
00:09:26,120 --> 00:09:28,230
So this will become a quadratic probing.

111
00:09:31,170 --> 00:09:37,920
So that's all in this video, is a simple implementation of hash function of size, then by using linear

112
00:09:37,920 --> 00:09:38,370
proving.

