1
00:00:00,660 --> 00:00:05,640
And this video, we will see and function and insert function that we have already discussed.

2
00:00:07,720 --> 00:00:13,420
So already I have written this program, I have shown you this is structure and this modified one,

3
00:00:13,420 --> 00:00:15,250
I have picked an array of size 20.

4
00:00:15,540 --> 00:00:21,610
Let us make it to 10 so that we can see the contents easily inside the Bulgaria and inside main function.

5
00:00:21,610 --> 00:00:28,180
Already, arrays initialize and the size of the end and also the present elements are five.

6
00:00:29,090 --> 00:00:31,610
Now, here I will ride on a function for appen.

7
00:00:32,800 --> 00:00:39,100
The pen function will insert a new element at the end of an array, so it should create a structure

8
00:00:40,960 --> 00:00:42,730
as it's going to modify the array.

9
00:00:42,760 --> 00:00:45,630
It should be all by address type.

10
00:00:46,150 --> 00:00:48,820
And this X is the value that we want to insert.

11
00:00:54,540 --> 00:00:58,560
Now, I just we will be inserting a new element at the end of the story.

12
00:00:58,580 --> 00:01:01,260
Right now, the elements are two, three, four, five, six.

13
00:01:01,260 --> 00:01:03,150
Whole new element should come after six.

14
00:01:04,060 --> 00:01:08,950
So before in session, first of all, we should check whether there is sufficient space or not, if

15
00:01:09,280 --> 00:01:19,120
we are at all that Scappoose length is less than eight hours size, then there is a free space available.

16
00:01:19,360 --> 00:01:21,430
Otherwise we cannot insert an element.

17
00:01:22,480 --> 00:01:24,610
So for inserting an element in an array.

18
00:01:25,720 --> 00:01:33,990
Eight of eight land so we can insurgent opposition land and also increment land, the value should be

19
00:01:34,000 --> 00:01:34,420
stored.

20
00:01:36,450 --> 00:01:38,010
That's all end of the function.

21
00:01:38,490 --> 00:01:45,540
I will call this a brain function inside main function by passing address of Iare and the value that

22
00:01:45,540 --> 00:01:46,950
I want to insert is an.

23
00:01:48,730 --> 00:01:53,770
After inserting the value, this display function will display the value I should get, the values that

24
00:01:53,770 --> 00:01:55,630
are two, three, four, five, six and 10.

25
00:01:56,590 --> 00:01:57,280
Let us run.

26
00:02:01,370 --> 00:02:04,370
Yes, the elements are two, three, four, five, six, 10.

27
00:02:06,640 --> 00:02:10,419
So this display function is displaying all elements up to the length of Inari.

28
00:02:11,390 --> 00:02:16,160
Let us debunk this program and see how deep and functional put a break point and I will run.

29
00:02:19,720 --> 00:02:24,410
We are here inside the debate video, you can see the elements in an array are two, three, four,

30
00:02:24,410 --> 00:02:25,130
five, six.

31
00:02:27,740 --> 00:02:31,400
And it's about to call a brain function, let us go to a brain function.

32
00:02:32,480 --> 00:02:38,830
It will check the length and the size, then it will store an element in another Naifeh, I expand this

33
00:02:39,140 --> 00:02:39,770
object.

34
00:02:40,940 --> 00:02:44,720
You can see that after six available and is also still.

35
00:02:46,850 --> 00:02:53,570
Back to main function and display will display all elements, so that's it, we have seen how element

36
00:02:53,570 --> 00:02:54,840
is stored in an array.

37
00:02:55,040 --> 00:02:58,030
So this display will display the elements that we have already seen it.

38
00:02:58,040 --> 00:03:00,380
So let us stop the function now.

39
00:03:00,410 --> 00:03:03,980
Next thing, we will write a function for insert.

40
00:03:05,480 --> 00:03:10,190
We have already discussed a function for insert Sloboda main function, I will write a function for

41
00:03:10,190 --> 00:03:17,280
inside, so the function for insert returns void and the function name is insert and it should take

42
00:03:17,310 --> 00:03:19,190
a barometer of type.

43
00:03:19,190 --> 00:03:26,930
Uhry my address because it is going to modify and the next thing is index at which we want to insert

44
00:03:26,930 --> 00:03:27,560
an element.

45
00:03:27,830 --> 00:03:31,670
And Lastarza the value that we want to insert element that we want to insert.

46
00:03:32,690 --> 00:03:38,300
Before the recession, let us check whether the index given is valid or not, a valid index is from

47
00:03:38,510 --> 00:03:40,270
zero to land of al-Nouri.

48
00:03:40,640 --> 00:03:46,680
So here I will check if index given is greater than or equal to zero or not.

49
00:03:47,270 --> 00:03:55,190
And also, this index should be less than or equal to Áras Lent.

50
00:03:56,450 --> 00:04:03,830
This is a valid index, if the index is valid, then only will perform in operation so far for performing

51
00:04:03,830 --> 00:04:04,720
in third operation.

52
00:04:04,730 --> 00:04:09,500
We need a follow up so far that I will declare a variable, i.e. not here using follow.

53
00:04:09,860 --> 00:04:14,170
They will shift the elements and provide a free space for a new element.

54
00:04:14,180 --> 00:04:22,880
Then we will insert that I start from air land greater than give an index and a minus minus.

55
00:04:22,890 --> 00:04:23,950
I'm writing the same code.

56
00:04:23,960 --> 00:04:26,210
What I have shown you on Lightbown.

57
00:04:27,180 --> 00:04:29,010
And if I.

58
00:04:29,910 --> 00:04:33,840
As is inside an object, so I have to use.

59
00:04:35,240 --> 00:04:39,650
Appropriate for accessing it, if I.

60
00:04:40,770 --> 00:04:48,080
Minus one, so the previous element we will pop out here at current place then after this, in an array

61
00:04:48,150 --> 00:04:57,000
at each of the given index, we can write on an element and also at length should be increment because

62
00:04:57,000 --> 00:04:58,440
we have inserted a new element.

63
00:04:59,010 --> 00:05:01,230
That's all the insert function.

64
00:05:02,210 --> 00:05:07,430
Now, let us go back to main function inside the main function, I have already shown you how to use

65
00:05:07,430 --> 00:05:07,850
a pen.

66
00:05:08,450 --> 00:05:12,470
Now let us see how to use insert function for inserting.

67
00:05:12,860 --> 00:05:15,560
I have to pass a rate as a parameter.

68
00:05:15,710 --> 00:05:17,000
That is my address.

69
00:05:17,480 --> 00:05:22,850
And the index that I want to insert, I want to insert at the next five that will be indexed.

70
00:05:23,300 --> 00:05:25,260
And the value that I want to insert is.

71
00:05:25,310 --> 00:05:28,510
And then after that all the elements will be displayed.

72
00:05:28,820 --> 00:05:33,730
So this is then should be inserted after six, because if you see the then it says this is zero.

73
00:05:33,740 --> 00:05:34,490
This is one.

74
00:05:34,490 --> 00:05:35,210
This is two.

75
00:05:35,300 --> 00:05:38,780
This is three and four at fifth in next element will be inserted.

76
00:05:38,790 --> 00:05:41,630
That is after six that is run and check.

77
00:05:44,660 --> 00:05:46,540
Yes, and is inserted at the end.

78
00:05:49,760 --> 00:05:54,470
I will change the index, I will inserted that index a little, let us run it again.

79
00:05:55,610 --> 00:05:58,790
Yes, that is inserted at index zero, that is the beginning index.

80
00:05:59,830 --> 00:06:05,920
Let us change the index once again and see who it should be inserted in between three and four.

81
00:06:05,950 --> 00:06:06,880
That is after three.

82
00:06:10,190 --> 00:06:12,050
Yes, it is in between three and four.

83
00:06:13,570 --> 00:06:14,980
The Vatican has inserted.

84
00:06:19,020 --> 00:06:23,880
No, let us give the index has minus two, then let us see what happens again.

85
00:06:25,910 --> 00:06:30,230
And the values are displayed, so and it's not there, I'll give the index nine.

86
00:06:30,590 --> 00:06:35,680
Now, let us run the program, see still and it's not inserted because the index was invalid.

87
00:06:37,860 --> 00:06:43,380
So that's all in the remaining videos, I'll be using the same program, so it is having all the functions

88
00:06:43,380 --> 00:06:46,110
and the structure of another available here.

