1
00:00:00,540 --> 00:00:06,210
Let us write a procedure for finding a maximum element in our linguist, so first I'll show you it's

2
00:00:06,210 --> 00:00:08,640
working, then I will write on a function for it.

3
00:00:09,240 --> 00:00:14,400
I want to find out maximum element so I should traverse all these elements in an English.

4
00:00:14,520 --> 00:00:19,760
I should take a pointer and let the pointer traverse to all these elements so we know how to traverse.

5
00:00:20,490 --> 00:00:24,450
But for finding maximum, I should have some variabilities emacs.

6
00:00:24,900 --> 00:00:27,000
Then it should have some initial value.

7
00:00:27,010 --> 00:00:32,450
This Max should have some initial value to call something maximum that is larger.

8
00:00:32,610 --> 00:00:37,240
You should have some small numbers so that the other number becomes larger than you are.

9
00:00:37,430 --> 00:00:39,830
But so far this what should be the initial value.

10
00:00:40,140 --> 00:00:49,410
So we should have some more or less to number so we can assign minimum integer, minimum integer, because

11
00:00:49,410 --> 00:00:51,270
all these values are integer only.

12
00:00:51,270 --> 00:00:53,220
So we should have a smaller integer.

13
00:00:53,430 --> 00:01:02,190
So I will assume that smallest indigenous minus three, two, seven, six, eight in a 16 bit integer

14
00:01:02,190 --> 00:01:03,720
that is two bytes integer.

15
00:01:03,720 --> 00:01:08,970
So we are assuming integer extra wipes and two by the smallest number we can write minus three, two,

16
00:01:08,970 --> 00:01:09,870
seven, six, eight.

17
00:01:10,270 --> 00:01:16,080
So I have explained that we must have some minimum value, but there's no any value other than this

18
00:01:16,080 --> 00:01:19,860
one is definitely greater because this the smallest one we are having now.

19
00:01:19,860 --> 00:01:23,010
See the procedure first value eight.

20
00:01:23,010 --> 00:01:24,270
Is it greater than this one?

21
00:01:24,280 --> 00:01:24,690
Yes.

22
00:01:24,960 --> 00:01:26,430
So change it to eight.

23
00:01:27,000 --> 00:01:28,470
The next value three.

24
00:01:28,470 --> 00:01:29,520
Is it agreed on the need?

25
00:01:29,610 --> 00:01:31,770
No move to the next value seven.

26
00:01:31,770 --> 00:01:32,100
Seven.

27
00:01:32,100 --> 00:01:35,250
Is it greater than the no move to the next value to a duel.

28
00:01:35,250 --> 00:01:35,700
Is it good.

29
00:01:35,700 --> 00:01:36,150
Agnete need.

30
00:01:36,150 --> 00:01:36,560
Yes.

31
00:01:36,600 --> 00:01:41,040
Make it to a change it to do in the next number nine nine is greater than twelve.

32
00:01:41,250 --> 00:01:41,720
No.

33
00:01:42,060 --> 00:01:44,220
So largest number in the Linklaters twelve.

34
00:01:45,000 --> 00:01:46,290
So this solve it every time.

35
00:01:46,290 --> 00:01:52,440
We can compare the element with Max and if the element is greater than Max we will save it in Max.

36
00:01:52,830 --> 00:01:54,360
Not the same simple procedure.

37
00:01:54,360 --> 00:01:56,250
I will write on it as a function.

38
00:01:57,240 --> 00:02:05,070
Let us call the function names, Max, it should take the parameter as first pointer, so instead of

39
00:02:05,070 --> 00:02:11,039
writing struck struct, I'll just write a note be so struck as by default we normally have to write,

40
00:02:11,520 --> 00:02:13,600
then it should return a maximum integer.

41
00:02:13,950 --> 00:02:21,460
Now here we should have one variable and that should have minus three, two, seven, six, eight.

42
00:02:21,510 --> 00:02:24,680
So this is nothing but minimum integer.

43
00:02:25,380 --> 00:02:31,470
So any of this you can assign either assign the value or set minimum integer so you can assign minimum

44
00:02:31,470 --> 00:02:31,860
integer.

45
00:02:31,870 --> 00:02:35,610
But I am writing the smallest value based on to bite integer.

46
00:02:36,870 --> 00:02:42,690
Now we have to traverse through all the laws and going to comparing the values with this.

47
00:02:42,690 --> 00:02:49,930
Emma, I'm taking the variable names and now so this travel thing is done using loop.

48
00:02:49,950 --> 00:03:01,470
So while p if B's data is greater than the value of M, then change the value of M to B's data and move

49
00:03:01,470 --> 00:03:03,780
B to next Norn.

50
00:03:05,500 --> 00:03:10,720
This had been moving to the next node for traversing, and at the end we will be having the smallest

51
00:03:10,720 --> 00:03:15,210
element in variable m Rickon and.

52
00:03:16,830 --> 00:03:23,910
This is the function for finding maximum of all the elements and linguists that I relied on a recursive

53
00:03:23,910 --> 00:03:25,020
function for this one.

54
00:03:25,030 --> 00:03:27,660
So let us call the function name also Max.

55
00:03:28,050 --> 00:03:39,120
And this takes node pointer B and return integer recursive function, hogy writing us for another condition.

56
00:03:39,330 --> 00:03:45,370
If A B is null written minimum integer that is mean.

57
00:03:45,750 --> 00:03:49,020
And if it is not null then.

58
00:03:50,370 --> 00:03:59,890
Hellespont here I will use a temporary variable and I will call this function on next Naude these next

59
00:03:59,910 --> 00:04:02,340
and all already.

60
00:04:02,340 --> 00:04:07,750
I have shown you this in one of the examples that we can declare some variable and we can take the resultant

61
00:04:07,770 --> 00:04:13,440
variable, not whatever the result that we are getting if this result is greater than.

62
00:04:15,150 --> 00:04:25,380
Of data, then return this resod acts otherwise written BS data.

63
00:04:26,850 --> 00:04:34,100
So here we get the results in variable X and that result is greater than these data that is data inside.

64
00:04:34,830 --> 00:04:39,690
Then we will return that result X, otherwise we will return piece of data.

65
00:04:40,170 --> 00:04:41,690
So this is a recursive function.

66
00:04:42,000 --> 00:04:45,330
I will rewrite same function in a different way here.

67
00:04:45,330 --> 00:04:47,660
I will show you the same function.

68
00:04:47,670 --> 00:04:59,550
I will rewrite again and max node starboy integer x assign something.

69
00:05:01,410 --> 00:05:14,460
Even you can leave it on initialised, be assigned zero return and written men, and as I said, if

70
00:05:14,460 --> 00:05:19,500
you already have a written statement inside, if you don't need else so simply I can write on those

71
00:05:19,500 --> 00:05:29,630
a statement without else exercising max of bees next then this competition, if else I can write on

72
00:05:29,650 --> 00:05:31,430
same thing using ternary operator.

73
00:05:31,770 --> 00:05:33,930
So simply write a written.

74
00:05:35,910 --> 00:05:38,670
If X is greater than BP's data.

75
00:05:40,660 --> 00:05:51,680
Return X, otherwise, BP's data for this function looks simple, just in a few lines, it has finished.

76
00:05:51,700 --> 00:05:53,080
It is not lending like this.

77
00:05:53,650 --> 00:05:57,370
So by using ternary operator, we can reduce the code.

78
00:05:57,400 --> 00:06:03,070
So if you are used to with this type of statements, then it will be very concise.

79
00:06:03,070 --> 00:06:03,700
A statement.

80
00:06:05,010 --> 00:06:06,750
Then it will be very easy to read.

81
00:06:07,950 --> 00:06:10,350
Then writing this lendee final score.

82
00:06:11,150 --> 00:06:13,850
So this is a function for finding maximum.

83
00:06:14,510 --> 00:06:17,590
Now, I don't have to explain how you define minimum.

84
00:06:18,110 --> 00:06:20,950
So for Max, we are checking for greater for minute.

85
00:06:20,990 --> 00:06:22,430
We have to check it for smaller.

