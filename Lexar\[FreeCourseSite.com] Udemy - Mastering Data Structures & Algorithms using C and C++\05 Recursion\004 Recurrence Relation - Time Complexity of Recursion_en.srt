1
00:00:00,240 --> 00:00:07,200
Let me tell you the basic concept for finding the time complexity, we assume that every statement in

2
00:00:07,200 --> 00:00:12,080
our program takes one unit of time for execution.

3
00:00:13,390 --> 00:00:15,200
Let me give you an idea behind that one.

4
00:00:16,390 --> 00:00:21,910
See, suppose there are some books kept here and you have to move the book and keep it on that shelf

5
00:00:21,910 --> 00:00:24,910
or in Iraq, how much time it takes.

6
00:00:26,150 --> 00:00:31,880
Maybe half a second quarter of a second for somebody who walks very slowly, <PERSON><PERSON>, once again for

7
00:00:31,880 --> 00:00:34,520
keeping one book there one by one, if you have to keep.

8
00:00:35,180 --> 00:00:37,970
So time varies from person to person.

9
00:00:38,570 --> 00:00:41,630
So we don't mention seconds or milliseconds.

10
00:00:41,630 --> 00:00:49,750
We say one unit of time, like if you take the example of currency, one dollar, one would be one born.

11
00:00:50,540 --> 00:00:51,790
So we say one.

12
00:00:52,160 --> 00:00:54,900
But what is the market value that would be different?

13
00:00:55,610 --> 00:01:00,410
So we say one bug or one unit of currency.

14
00:01:00,950 --> 00:01:04,670
So simply we assume that every statement makes one unit of time.

15
00:01:05,300 --> 00:01:11,410
If that statement is repeated multiple times, then you count the frequency how many times it is executed.

16
00:01:11,690 --> 00:01:14,570
So that is sufficient for analyzing our function.

17
00:01:14,600 --> 00:01:20,660
Now let us see what our function is doing is doing nothing just printing.

18
00:01:21,080 --> 00:01:28,190
So it is printing a value and how much time it takes for printing one unit of time.

19
00:01:28,760 --> 00:01:29,810
One unit of time.

20
00:01:31,310 --> 00:01:33,170
How many times print was written here?

21
00:01:33,380 --> 00:01:34,170
Only one time.

22
00:01:34,940 --> 00:01:36,350
So it is just one.

23
00:01:36,860 --> 00:01:38,600
But this is a recursive function.

24
00:01:38,600 --> 00:01:43,430
So it is calling itself again and again so total how many times it's printing.

25
00:01:43,760 --> 00:01:51,710
So let us look at and do the briefing three or recursion three here to spending one time to time three

26
00:01:51,710 --> 00:01:53,780
times so total.

27
00:01:53,780 --> 00:01:54,620
How many units.

28
00:01:55,130 --> 00:01:56,570
One one one.

29
00:01:56,840 --> 00:02:06,730
So I can see it takes three units of time for the value of an A3 value of N as a three for the first

30
00:02:06,740 --> 00:02:07,760
value of industry.

31
00:02:09,270 --> 00:02:18,640
If I make it as five then it will take five units of time then for and it will take and units of time.

32
00:02:20,150 --> 00:02:22,400
So it is just like coming back to the example.

33
00:02:22,550 --> 00:02:28,580
If you have to keep one book in Iraq, you are taking one unit of time, ten books, one unit of time

34
00:02:28,970 --> 00:02:33,080
and the books and unit of time, what is and whatever may be.

35
00:02:33,470 --> 00:02:36,680
So the time depends on a number of books.

36
00:02:36,710 --> 00:02:40,630
So here the time depends on the value that we are passing from here.

37
00:02:41,090 --> 00:02:43,630
It may be three, it may be five, or it can be anything.

38
00:02:43,640 --> 00:02:51,560
So we say the time is and units of time so the time can be represented as outdraw.

39
00:02:51,590 --> 00:02:54,800
And the answer that we got is then unit.

40
00:02:54,830 --> 00:02:57,040
So the degree of the polynomial is one.

41
00:02:57,050 --> 00:02:58,730
So we rated as outdraw.

42
00:02:58,730 --> 00:03:01,280
And so the time taken this order of.

43
00:03:01,460 --> 00:03:06,160
And there is one more method of finding the time complexity using recurrence relation.

44
00:03:06,530 --> 00:03:12,770
I will show you how to write recurrence relation and how to solve it to find the time complexity of

45
00:03:13,130 --> 00:03:14,840
recursive functions.

46
00:03:15,650 --> 00:03:19,340
Now let us find out the time complexity of this recursive function.

47
00:03:19,340 --> 00:03:27,680
Using the current solution, we assume that the time taken by this function as B of and for time, if

48
00:03:27,680 --> 00:03:35,780
the time taken that function fondren one is ten, then that total time should be some of all the times

49
00:03:35,780 --> 00:03:37,600
taken by the statements in sight.

50
00:03:38,450 --> 00:03:40,010
So let us look at the statement.

51
00:03:40,400 --> 00:03:41,600
See, there is a statement.

52
00:03:42,020 --> 00:03:43,400
This is a conditional statement.

53
00:03:43,670 --> 00:03:46,220
How much time it takes for execution.

54
00:03:46,220 --> 00:03:48,770
Just one unit of time one.

55
00:03:49,160 --> 00:03:51,830
Then there is a printed statement how much time it takes.

56
00:03:52,010 --> 00:03:53,320
This also takes one.

57
00:03:53,960 --> 00:03:57,470
Then there is one more function called how much time it takes one.

58
00:03:58,370 --> 00:04:04,850
As I said, every statement takes one unit of time for execution for this also have written one.

59
00:04:05,720 --> 00:04:06,890
No, it's wrong.

60
00:04:07,310 --> 00:04:07,990
It's wrong.

61
00:04:08,510 --> 00:04:10,100
See, that is a function call.

62
00:04:10,760 --> 00:04:14,770
So I should know total time taken by that function call.

63
00:04:15,200 --> 00:04:19,700
So it is not just a normal statement, it will call again itself.

64
00:04:19,709 --> 00:04:22,070
So there is something more behind that one.

65
00:04:22,310 --> 00:04:25,340
So I need to know how much time that function call is ticking.

66
00:04:26,120 --> 00:04:33,050
So let us see closely what I said that this function call total time ten then this is similar to that

67
00:04:33,050 --> 00:04:36,050
one and this is and the minus one.

68
00:04:36,260 --> 00:04:41,090
So yes, this will be taking the end minus one time.

69
00:04:42,140 --> 00:04:42,770
Yes.

70
00:04:43,430 --> 00:04:45,320
This is not a normal statement.

71
00:04:45,620 --> 00:04:49,010
There is a function called and it takes being minus one time.

72
00:04:50,210 --> 00:04:55,450
Then what is this total t I said the sum of all the times taken by the statement.

73
00:04:55,460 --> 00:05:02,940
So let's take the sum so that p and as equals to be in the minus one the bigger commemorating first

74
00:05:03,200 --> 00:05:05,090
plus two plus two.

75
00:05:05,450 --> 00:05:10,790
So the total times the energy goes to D and minus one, plus two.

76
00:05:11,000 --> 00:05:20,930
So the reconciliation as the and as equals two big and minus one plus two then.

77
00:05:21,200 --> 00:05:23,820
And is the greater than zero then.

78
00:05:24,080 --> 00:05:25,370
And there's a greater than.

79
00:05:26,120 --> 00:05:32,360
What happens when and is equal to zero one and is equal to zero, it will just check the condition,

80
00:05:32,360 --> 00:05:34,610
it will not enter inside and it will come out.

81
00:05:34,910 --> 00:05:37,340
So just checking the condition, one unit of time.

82
00:05:37,640 --> 00:05:39,580
So it takes one unit of time.

83
00:05:41,430 --> 00:05:46,500
So this is the reconciliation formed from that function.

84
00:05:50,300 --> 00:05:57,290
So the time complexity of recursive function can be represented in the form of reconciliation, not

85
00:05:57,290 --> 00:06:04,370
if we solve this using induction method, also called the successive substitution method, we can get

86
00:06:04,370 --> 00:06:05,100
the answer.

87
00:06:06,020 --> 00:06:07,370
So let us solve this one.

88
00:06:08,030 --> 00:06:10,970
Now, before solving this, I should tell you one thing.

89
00:06:11,390 --> 00:06:16,690
If you have any constant value here, then let us write it as one.

90
00:06:16,700 --> 00:06:19,590
So that means constant is false.

91
00:06:19,940 --> 00:06:26,060
So the reconciliation is city and as equals to be and the minus one plus one.

92
00:06:26,840 --> 00:06:29,030
This is the first equation we have.

93
00:06:29,600 --> 00:06:35,420
I can solve this if I can know what is it T and minus one scene here.

94
00:06:35,420 --> 00:06:35,810
Right.

95
00:06:36,320 --> 00:06:46,340
Since the end is equal to B and the minus one plus one, then what will be B and the minus one.

96
00:06:46,730 --> 00:06:49,630
So if this is and replace it then minus one.

97
00:06:49,640 --> 00:06:51,740
So this will repeat in the minus two.

98
00:06:52,040 --> 00:06:52,840
Plus one.

99
00:06:53,090 --> 00:06:58,020
So I can substitute this one T and minus two, plus one in place of being minus one.

100
00:06:58,310 --> 00:07:00,570
So this I can replace here.

101
00:07:00,920 --> 00:07:09,470
So the next question that I get is the end is equals two to be in the minus one for you D and minus

102
00:07:09,470 --> 00:07:09,650
two.

103
00:07:09,650 --> 00:07:10,350
Plus one.

104
00:07:10,580 --> 00:07:13,040
So the safety and the minus two.

105
00:07:13,040 --> 00:07:13,790
Plus one.

106
00:07:14,090 --> 00:07:16,200
And this plus one as it is.

107
00:07:17,120 --> 00:07:22,590
So this is substituted with this one and then this plus one as it is.

108
00:07:22,880 --> 00:07:23,990
So what is this one.

109
00:07:23,990 --> 00:07:30,220
Finally the end is equal to B and the minus two plus two.

110
00:07:30,410 --> 00:07:32,390
So that is called the first second increase.

111
00:07:33,200 --> 00:07:35,160
Then let us substitute in this place.

112
00:07:35,170 --> 00:07:38,420
So what this will be this will be B and minus three.

113
00:07:38,420 --> 00:07:39,300
Plus one.

114
00:07:39,590 --> 00:07:48,100
So if I substitute that then this will be tiendas equals to B and minus three, plus one and plus two.

115
00:07:48,380 --> 00:07:53,030
So this can be the nasty and as equals to B and minus three.

116
00:07:53,030 --> 00:07:54,470
Plus three.

117
00:07:55,190 --> 00:07:56,450
There's the total accretion.

118
00:07:57,440 --> 00:08:01,120
So I have substituted two times how long I should do this.

119
00:08:01,520 --> 00:08:12,230
Let us continue it four times so K times B and the minus K plus key because this was three.

120
00:08:12,260 --> 00:08:14,780
So there's also three for this four then this will be four.

121
00:08:14,990 --> 00:08:17,030
If this is K then this will also be K.

122
00:08:17,210 --> 00:08:19,100
So the fourth equation, Inglot.

123
00:08:20,690 --> 00:08:27,560
See, we have to solve this one, so substitute and then substitute and then substitute go on substituting

124
00:08:27,860 --> 00:08:32,059
until it reduces down to a smaller value.

125
00:08:32,059 --> 00:08:35,250
That is an equal level where we know the answer.

126
00:08:35,260 --> 00:08:41,299
That is one C when you don't know the answer for a bigger expression, you know the answer for a smaller

127
00:08:41,299 --> 00:08:44,450
expression, then break the bigger one into smaller one.

128
00:08:45,400 --> 00:08:51,100
And it so the same thing we have done, we don't know how much this is, but we know more than is equal

129
00:08:51,100 --> 00:08:51,630
to zero.

130
00:08:51,650 --> 00:08:53,050
And so this is directly one.

131
00:08:53,060 --> 00:08:53,850
So we know that.

132
00:08:54,070 --> 00:08:58,950
So we have tried to reduce this and reduce dissent by substituting and we got this one.

133
00:08:59,890 --> 00:09:04,280
Now we see that this and minus K actually has became zero.

134
00:09:04,450 --> 00:09:05,880
So on that side, I will try.

135
00:09:05,920 --> 00:09:08,110
Don't assume that.

136
00:09:10,990 --> 00:09:17,600
And minus K is equal to zero, so it means we have and minus one that we made it as a minus student

137
00:09:17,600 --> 00:09:20,650
and minus three and so on up to what it became zero.

138
00:09:20,920 --> 00:09:22,990
So we assume that this has become zero.

139
00:09:23,230 --> 00:09:26,560
So it means therefore end is equals to K.

140
00:09:27,010 --> 00:09:37,000
So in this equation, if I substitute that will be B and equals to the and the minus and plus.

141
00:09:37,480 --> 00:09:45,050
And so at the place of K I have written Edner so and minus and plus and so I got this one.

142
00:09:45,070 --> 00:09:56,140
So this is billion as equals to be zero plus and so t and is equal to one plus and so that's all I got

143
00:09:56,140 --> 00:09:56,410
done.

144
00:09:56,410 --> 00:10:00,730
So Deanne's equals one plus and this can be done.

145
00:10:00,850 --> 00:10:07,040
An order of N C earlier directly from the dressing room.

146
00:10:07,090 --> 00:10:11,060
Also I have shown you and plus one was the number of calls.

147
00:10:11,070 --> 00:10:16,780
So yes, I got the number of calls here and the time taken by this function depends on the number of

148
00:10:16,780 --> 00:10:17,270
calls.

149
00:10:17,410 --> 00:10:25,810
So yes, it is outdraw and so it will be for any time from the calls will be and plus one that is one

150
00:10:25,810 --> 00:10:27,910
more than the number of times of printing.

151
00:10:28,780 --> 00:10:30,860
Let us summarize the things that we have learned.

152
00:10:31,090 --> 00:10:35,140
See, I have shown you what does it mean by recursion that I have shown you how to write the recursion

153
00:10:35,140 --> 00:10:36,730
I have traced to that one.

154
00:10:37,120 --> 00:10:39,460
I have shown you two different types of recursion.

155
00:10:40,000 --> 00:10:42,970
Then I have shown you how a stack utilized.

156
00:10:43,300 --> 00:10:47,590
And then we saw how to find the time complexity from the three.

157
00:10:47,620 --> 00:10:53,260
Also, I have shown you and overriding recurrence relation also have shown you and the time complexity

158
00:10:53,260 --> 00:10:54,180
is mostly written.

159
00:10:54,180 --> 00:10:58,540
The order of our degree of that is order of n c.

160
00:10:58,540 --> 00:11:03,420
I'm not using the notations like big omegle data because we have not discussed about them.

161
00:11:03,730 --> 00:11:07,600
So I'll give you the about these notations at some other time.

