1
00:00:00,520 --> 00:00:07,990
Let us check whether incision's orders adoptive or not, so we have already learned about insertion

2
00:00:07,990 --> 00:00:08,870
site procedure.

3
00:00:09,220 --> 00:00:12,750
It will start inserting the element from 7:00 a.m. onwards.

4
00:00:12,760 --> 00:00:14,910
It will start answering on the left hand side.

5
00:00:15,370 --> 00:00:20,920
So let us perform incisions on procedure on this list of elements or leave the first element, second

6
00:00:20,920 --> 00:00:22,900
element, take out then check.

7
00:00:23,200 --> 00:00:24,400
Is it greater than five?

8
00:00:24,400 --> 00:00:26,080
No fight comes here only.

9
00:00:27,260 --> 00:00:34,020
Wolly one competition, then eight, let us insert this and check five, is it a greater than eight?

10
00:00:34,040 --> 00:00:35,050
No, that is smaller.

11
00:00:35,210 --> 00:00:36,450
So insert eight here.

12
00:00:36,510 --> 00:00:39,560
Only we perform one competition and there is no shifting.

13
00:00:40,430 --> 00:00:43,270
Then take out 10 and compare with this one.

14
00:00:43,400 --> 00:00:44,120
Try to shift.

15
00:00:44,210 --> 00:00:45,490
This is not greater than 10.

16
00:00:45,800 --> 00:00:47,880
So no shift being required under.

17
00:00:47,900 --> 00:00:48,260
I don't.

18
00:00:48,440 --> 00:00:49,070
Here on the.

19
00:00:50,490 --> 00:00:57,960
Then to end this as I write this, is this the X 12 E of Jay Z greater than X?

20
00:00:58,120 --> 00:01:00,150
No, no shift shifting required.

21
00:01:00,150 --> 00:01:01,370
This condition feels right.

22
00:01:01,380 --> 00:01:08,400
No shifting required then right on the element of the GST plus one plus one copy element to an.

23
00:01:09,580 --> 00:01:15,020
So for every element, we perform only one competition and there was no slapping at all.

24
00:01:15,460 --> 00:01:18,630
So only one competition for how many competitions?

25
00:01:18,640 --> 00:01:22,690
Not total number of competitions for one element.

26
00:01:22,690 --> 00:01:24,460
One competition for what?

27
00:01:24,460 --> 00:01:27,090
We have done it for only four elements.

28
00:01:27,370 --> 00:01:31,590
We have not done it for frostily so and the minus one competitions.

29
00:01:31,840 --> 00:01:35,980
So this is outdraw and and how many stripes we have done.

30
00:01:35,980 --> 00:01:39,130
A number of slaps swaps here are shifting.

31
00:01:39,340 --> 00:01:40,810
Have we shifted any elements.

32
00:01:40,810 --> 00:01:44,140
No, zero elements are shifted so it is outdraw one.

33
00:01:44,530 --> 00:01:51,070
So the time taken by inflation for this order of and if the list is already sorted so it is taking minimum

34
00:01:51,070 --> 00:01:51,550
time.

35
00:01:51,760 --> 00:01:55,320
If so, insertions is adaptive.

36
00:01:55,630 --> 00:01:58,980
So yes, it is adaptive insertion.

37
00:01:59,750 --> 00:02:00,330
Adapt.

38
00:02:01,550 --> 00:02:02,550
Now one more thing.

39
00:02:03,280 --> 00:02:07,340
Did we use any flag or anything like this like we have done in bubble?

40
00:02:07,870 --> 00:02:09,400
No, we did not use anything.

41
00:02:09,880 --> 00:02:13,390
So by nature, in its nature it is adaptive.

42
00:02:13,930 --> 00:02:18,700
So we did not made it adopted by injuries, by introducing something extra.

43
00:02:19,240 --> 00:02:20,830
By nature, it is adaptive.

44
00:02:20,860 --> 00:02:25,360
Yes, incision's for the behavior itself is adaptive behavior.

45
00:02:25,810 --> 00:02:34,840
So insertions all the time if you take minimum time is order of and maximum time as outdraw and scran

46
00:02:35,530 --> 00:02:43,510
and swamp's minimum Sahibzada of one if the list is already sodded maximum so absorbed and squared if

47
00:02:43,510 --> 00:02:45,900
the listless reverse reversed Sadanand.

48
00:02:46,270 --> 00:02:52,510
So if the Lister's in descending order then it requires maximum competition and maximum slaps.

49
00:02:53,380 --> 00:02:58,780
If it is an ascending order then minimum competitions and minimum slaps.

50
00:02:58,930 --> 00:03:01,780
So I can say that best case of insertion.

51
00:03:01,790 --> 00:03:09,040
So this list is already sorted in ascending order and the worst case of insertion for this list is already

52
00:03:09,050 --> 00:03:11,020
sorted in descending order.

53
00:03:11,110 --> 00:03:17,050
We are starting in ascending rate, but it is already in defending then all elements we have to reverse

54
00:03:17,470 --> 00:03:19,910
the next thing we have to check whether it is stable or not.

55
00:03:20,650 --> 00:03:21,850
Now, let us look at this.

56
00:03:22,270 --> 00:03:27,940
So in this example, we can see that the list is for the pin here and this is the last element to be

57
00:03:27,940 --> 00:03:28,510
photic.

58
00:03:29,690 --> 00:03:32,390
And that element is duplicate, right?

59
00:03:32,420 --> 00:03:35,080
This is one black color five, and this is red color five.

60
00:03:35,390 --> 00:03:37,160
So that five I have to insert.

61
00:03:37,990 --> 00:03:44,710
So let us see how to insert and what happens after insertion, I will insert five pickled five right

62
00:03:44,950 --> 00:03:51,460
now have to find a place of five chicken so shivved elements, toilers, greater schifter done is good

63
00:03:51,550 --> 00:03:55,120
shifted eight is shifted, five is not greater.

64
00:03:55,300 --> 00:04:02,110
So this five will come here after this black five swindal regionalist phosphors black five next four.

65
00:04:02,410 --> 00:04:05,170
Right five four and sartorialist also the same.

66
00:04:05,410 --> 00:04:08,190
It will not shift the elements if it is equal.

67
00:04:08,680 --> 00:04:11,340
So that's how it keeps the element stable.

68
00:04:11,740 --> 00:04:12,400
So yes.

69
00:04:13,300 --> 00:04:15,610
Insertion Sardars Steben.

70
00:04:17,200 --> 00:04:20,940
So we have check that is adaptive as well as Steben.

