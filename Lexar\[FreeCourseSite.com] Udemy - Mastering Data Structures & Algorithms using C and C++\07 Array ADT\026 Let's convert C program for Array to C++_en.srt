1
00:00:00,570 --> 00:00:06,920
In this video, I will show you how to convert our C language program into a C++ program.

2
00:00:07,840 --> 00:00:14,110
So already we have seen all the functions performed on an early I will use that same code and I will

3
00:00:14,110 --> 00:00:17,230
transform it into a C++ class.

4
00:00:20,050 --> 00:00:28,180
Let us go next, and this is <PERSON><PERSON>, CPB, Project Nim, I'm giving it as a recipe and the language I

5
00:00:28,180 --> 00:00:30,010
will say that does C++.

6
00:00:33,230 --> 00:00:33,800
Next.

7
00:00:36,080 --> 00:00:36,680
Created.

8
00:00:42,390 --> 00:00:48,690
And this is the main function, I'll copy the complete program, what we have written for C language,

9
00:00:48,870 --> 00:00:54,440
that same program, I will pop here and I will modify it to make it as a C++ program.

10
00:00:55,360 --> 00:01:00,210
So I will remove this main function, I will copy and paste that program here.

11
00:01:01,760 --> 00:01:09,140
Yes, I have a complete C language program we have written out explaining this program first C we have

12
00:01:09,140 --> 00:01:14,960
defined this a structure for an array containing a pointer for pointing on an array dynamically and

13
00:01:14,960 --> 00:01:16,220
the size and the land.

14
00:01:16,610 --> 00:01:19,400
These are the data members of a structure.

15
00:01:20,720 --> 00:01:22,850
Then we have written various functions on this.

16
00:01:23,600 --> 00:01:26,050
All those function prototypes are available here.

17
00:01:26,060 --> 00:01:31,910
I have written their prototype and followed by that these functions definitions are available.

18
00:01:33,000 --> 00:01:34,630
See, all these functions we have written.

19
00:01:35,310 --> 00:01:38,610
And finally, I have written a main function.

20
00:01:42,340 --> 00:01:45,340
And that is meant to be one main function of hurricane.

21
00:01:48,850 --> 00:01:56,020
Here is a main function, so the same program I'm using it, I will modify it as a program for C++,

22
00:01:56,410 --> 00:01:58,510
so I will convert it into a class.

23
00:02:00,250 --> 00:02:06,010
So one important thing is sea language programs are more related to the logic of the procedures, so

24
00:02:06,010 --> 00:02:13,470
already we have all these procedures, then C++ is more related to designing or organizing the code.

25
00:02:13,780 --> 00:02:16,870
So the same code will organize it using C++.

26
00:02:19,170 --> 00:02:25,170
So here I will start making changes, first of all, instead of, I will say class, there's a class

27
00:02:25,170 --> 00:02:32,320
three and class array is having a pointer that is definitely I need a pointer and a.

28
00:02:33,410 --> 00:02:39,440
Site of an area and the land, yes, we need all these things and these are data members, so I should

29
00:02:39,440 --> 00:02:40,790
make them as private.

30
00:02:41,030 --> 00:02:42,020
All these are private.

31
00:02:44,860 --> 00:02:51,100
Then inside the glass, we will have the members and the functions and the functions we usually make

32
00:02:51,100 --> 00:02:52,030
the mass public.

33
00:02:52,460 --> 00:02:53,590
Yes, I made public.

34
00:02:54,520 --> 00:02:58,630
And the first type of functions that we write as a constructor for another.

35
00:02:59,020 --> 00:03:03,720
So I will write on a constructor function that is non parametrized constructor.

36
00:03:03,730 --> 00:03:04,240
I will write.

37
00:03:06,670 --> 00:03:12,490
And this constructor let us set the size of an Audi as a minimum, and so by default, the size of an

38
00:03:12,490 --> 00:03:13,870
eraser and.

39
00:03:15,080 --> 00:03:20,060
See, I don't have to use any membership operation now because this is inside a class only so I can

40
00:03:20,060 --> 00:03:21,230
directly say size.

41
00:03:22,450 --> 00:03:28,450
And is zero initially, because there are no elements than this, I should create a dynamic, so I should

42
00:03:28,450 --> 00:03:30,550
say new kind of size.

43
00:03:32,640 --> 00:03:39,780
Size, so size already I have made it estimate there's a non barometer's constructor and I will also

44
00:03:39,780 --> 00:03:42,580
write a parametrized constructor, which will take size.

45
00:03:42,660 --> 00:03:47,580
So I will give the variable name, as I said, and I will do all these things here also.

46
00:03:47,580 --> 00:03:48,960
Same thing I will do here.

47
00:03:52,690 --> 00:03:58,860
See, so often I have to set the size as isn't that song as a barometer constructor?

48
00:04:00,120 --> 00:04:04,200
Then also, I should have a destructor, so I would later structure for an early.

49
00:04:05,710 --> 00:04:10,990
Because here we are using dynamic memory, so I should delete that memory, delete a.

50
00:04:12,190 --> 00:04:19,029
As it is an area I should use subscript operator, national constructors and district authority now,

51
00:04:19,029 --> 00:04:27,700
I should write all these functions inside this class so simply I will include the prototype of all these

52
00:04:27,700 --> 00:04:29,360
functions inside the glass.

53
00:04:29,360 --> 00:04:31,530
So I will remove this closing bucket of a glass.

54
00:04:31,660 --> 00:04:32,410
Watch it here.

55
00:04:32,890 --> 00:04:33,970
This same removing.

56
00:04:34,910 --> 00:04:37,080
Now these functions are inside the glass.

57
00:04:37,100 --> 00:04:39,230
Now all these are glass members.

58
00:04:40,190 --> 00:04:46,910
Then at the end here, end of all these functions, I'll give a closing bracket, of course, is the

59
00:04:46,910 --> 00:04:48,200
closing of a class.

60
00:04:50,210 --> 00:04:57,020
That's said all these functions are inside the glass, not all these are members of this class not have

61
00:04:57,020 --> 00:04:59,630
to modify these functions, bring some changes in them.

62
00:05:00,380 --> 00:05:02,920
So I will change all these functions one by one.

63
00:05:03,350 --> 00:05:05,050
First, let us see display function.

64
00:05:05,060 --> 00:05:11,150
It is taking an array as an object of type structure so it doesn't have to be because it is already

65
00:05:11,150 --> 00:05:12,170
there inside the glass.

66
00:05:12,180 --> 00:05:14,800
But it can directly access these members.

67
00:05:15,260 --> 00:05:17,100
It doesn't need any parameter.

68
00:05:17,630 --> 00:05:21,430
So yes, it means all these function doesn't need any parameter.

69
00:05:21,490 --> 00:05:26,080
Yes, this doesn't need any parameter of type structure that we were passing.

70
00:05:26,090 --> 00:05:27,140
We don't have to pass it.

71
00:05:27,500 --> 00:05:28,880
They can directly access it.

72
00:05:32,430 --> 00:05:39,600
And I mean, the dysfunction function see dysfunction, we have written it for using them and said another

73
00:05:39,600 --> 00:05:43,350
function so it is not available globally, so I will.

74
00:05:44,430 --> 00:05:46,380
Make this function as a private.

75
00:05:47,470 --> 00:05:52,500
This is a local function, this is useful only within the class and it's useful for other functions,

76
00:05:52,510 --> 00:05:56,220
so I will make it as private it is inside the private block now.

77
00:05:58,500 --> 00:06:01,190
The linear search, I don't have to send a.

78
00:06:03,840 --> 00:06:10,410
I should only say the key then, the barometer for binary search, it doesn't need an.

79
00:06:12,810 --> 00:06:19,530
It meets only a key, then get a function, this doesn't need any idea as an object.

80
00:06:19,560 --> 00:06:25,190
So all this doesn't need as a barometer, they need other parameters like set.

81
00:06:25,200 --> 00:06:29,000
We need the index on the value finding maximum whose maximum?

82
00:06:29,010 --> 00:06:30,500
The same arrays maximum.

83
00:06:30,510 --> 00:06:36,610
It is inside this array class only inside the three class whose maximum this is maximum.

84
00:06:36,990 --> 00:06:40,390
So we don't have to pass any parameter as an array.

85
00:06:40,680 --> 00:06:42,150
So all these are removed.

86
00:06:43,410 --> 00:06:47,490
I mean, the models union and intersection, they are talking to parameters.

87
00:06:47,850 --> 00:06:55,230
But here it should take just one parameter, second array so I don't have to write stuck for that remove

88
00:06:55,230 --> 00:06:59,650
struck and here also return type a move because it's a class for class.

89
00:06:59,670 --> 00:07:01,800
You don't have to write a class or struct anything.

90
00:07:01,800 --> 00:07:03,960
They're just a class name is sufficient.

91
00:07:04,800 --> 00:07:06,810
They all should take only one parameter.

92
00:07:08,040 --> 00:07:11,070
That is the Cicconetti study, it is itself.

93
00:07:13,240 --> 00:07:15,010
So I remove this first parameter.

94
00:07:28,900 --> 00:07:32,500
Yes, all these functions became members of the class.

95
00:07:32,770 --> 00:07:37,290
Are all members of the class now so they can directly access this Uhry.

96
00:07:40,160 --> 00:07:45,170
And also the glass, I should implement all these functions before implementing all these functions,

97
00:07:45,170 --> 00:07:51,920
I should write on last name and scope resolution and the function name this, I should add it before

98
00:07:51,950 --> 00:07:54,700
every function and the changes.

99
00:07:54,710 --> 00:08:02,180
What I have done is this object should be removed right then when accessing, I should say lente.

100
00:08:02,330 --> 00:08:04,220
I do not have to say aeronaut.

101
00:08:04,940 --> 00:08:09,170
I don't have to say aeronaut not this function is change.

102
00:08:10,410 --> 00:08:13,620
I will change a few functions and I stopped them, you can do it by yourself.

103
00:08:13,650 --> 00:08:14,370
That is easy.

104
00:08:15,000 --> 00:08:21,070
Remove the sorry parameter because it's not taking and scope this belonging to a class and for land.

105
00:08:21,120 --> 00:08:22,590
Don't say Ursulines.

106
00:08:22,590 --> 00:08:27,360
Just say land and just say size because it is inside the glass itself.

107
00:08:28,170 --> 00:08:30,960
And here, don't say we are not operating.

108
00:08:31,000 --> 00:08:32,190
We don't need anything.

109
00:08:37,200 --> 00:08:38,750
And then insert function.

110
00:08:41,049 --> 00:08:46,870
Doesn't need any object as a barometer, and whatever the members are, it can access them directly

111
00:08:46,870 --> 00:08:48,850
because it's a member functional.

112
00:09:05,290 --> 00:09:10,630
Yes, I have modified this insert function by removing Daria's parameter and directly accessing the

113
00:09:10,630 --> 00:09:14,710
members, I will do the same thing for rest of them also.

114
00:09:16,700 --> 00:09:23,270
I have modified all the functions, now it's coming to merge function multi-function, I am sticking

115
00:09:23,270 --> 00:09:24,470
to parameters, so.

116
00:09:26,010 --> 00:09:33,340
Here, I should remove only one parameter, second parameter is mandatory because it's going to mortuary's

117
00:09:33,420 --> 00:09:35,880
first array is itself and the second array.

118
00:09:35,910 --> 00:09:40,820
We have to pass that parameter and it need not be called by others.

119
00:09:42,170 --> 00:09:47,780
Then here for creating an area, I should use a new operator for creating an object.

120
00:09:49,440 --> 00:09:57,300
See a new study and I will call the parameters constructor, the majority size should be the same as

121
00:09:58,470 --> 00:10:02,700
length of study that is disarrayed and the length of.

122
00:10:04,430 --> 00:10:05,200
Cicconetti.

123
00:10:08,300 --> 00:10:09,140
Does the change?

124
00:10:10,100 --> 00:10:16,910
And for accessing first remembers, I don't have to use any operator that is membership operator for

125
00:10:16,910 --> 00:10:22,490
the second area, I should access I should have membership operator, I made it by value.

126
00:10:22,490 --> 00:10:23,450
So it should be docked.

127
00:10:23,990 --> 00:10:25,250
It should not be an arrow.

128
00:10:25,790 --> 00:10:28,070
And for perjury, it should be as it is.

129
00:10:28,130 --> 00:10:29,240
This I will remove.

130
00:10:32,040 --> 00:10:33,810
Then second, marriage should be not.

131
00:10:36,080 --> 00:10:38,430
Thirdly, it should be added because it is a pointer.

132
00:10:38,450 --> 00:10:39,590
I have taken a pointer.

133
00:10:41,800 --> 00:10:44,230
Then for study, I modified it.

134
00:10:46,750 --> 00:10:49,120
Then this one removed this membership.

135
00:10:56,090 --> 00:10:57,480
Then this is operator.

136
00:10:58,330 --> 00:10:59,380
There's also dot.

137
00:11:01,290 --> 00:11:01,690
Yes.

138
00:11:01,710 --> 00:11:04,910
So, again, this they should be removed.

139
00:11:06,710 --> 00:11:08,030
And this should be Dorte.

140
00:11:10,020 --> 00:11:13,740
And don't have to set the size all of I have given the size, that's all.

141
00:11:14,340 --> 00:11:18,150
Now, similarly, I will modify a union intersection and difference also.

142
00:11:20,450 --> 00:11:26,810
I've been to the main function inside the main function, I should create an array object, so I will

143
00:11:26,810 --> 00:11:28,190
make it as a pointer.

144
00:11:29,750 --> 00:11:34,730
And when I know the size here, I will create an object.

145
00:11:37,480 --> 00:11:47,440
But passing size as a barometer for this line, I'll be creating an object here, assign New Urte and

146
00:11:47,440 --> 00:11:50,170
whatever the size is given here now.

147
00:11:55,570 --> 00:12:01,150
Then inside the main function, I will take one more variable that is, as is added for the size then

148
00:12:01,150 --> 00:12:05,520
here and taking the size, I should take Ezard.

149
00:12:08,770 --> 00:12:11,200
And dynamically, I should create an object now.

150
00:12:16,020 --> 00:12:21,030
Here are one assigned new array of given size.

151
00:12:22,600 --> 00:12:23,410
Yes.

152
00:12:24,730 --> 00:12:27,070
That said, I don't have to initialize anything.

153
00:12:29,590 --> 00:12:36,430
Now, the menu options, if you look at them, every function, I should call it with that or not insert.

154
00:12:36,940 --> 00:12:42,790
I don't have to parse this, but it become it has become a member, so I should say not.

155
00:12:42,910 --> 00:12:48,580
So this parameter I should remove and here delete, I should say, are not.

156
00:12:51,020 --> 00:12:53,370
Then Leanyer search area not.

157
00:12:54,440 --> 00:12:57,710
This barometer that was passed should be removed.

158
00:13:02,760 --> 00:13:06,960
Then A.R. Rahman dorsum and Ali Parameterize should remove.

159
00:13:07,900 --> 00:13:11,290
Now, some game inside, a different object, right?

160
00:13:11,680 --> 00:13:13,620
So this is a of one.

161
00:13:15,030 --> 00:13:16,170
But this plane.

162
00:13:20,120 --> 00:13:27,620
So that's all I have modified everything inside the program now you can see that this has become a C++

163
00:13:27,620 --> 00:13:28,130
program.

164
00:13:29,390 --> 00:13:37,190
So I have also changed the defense kind of functions and made them seen and colleagues so completely

165
00:13:37,190 --> 00:13:38,360
C++ code.

166
00:13:39,740 --> 00:13:45,260
Now, let us see, are there any errors, anything I missed, I have actually seen the preferences in

167
00:13:45,260 --> 00:13:49,090
this Xcode option that is show life issues.

168
00:13:49,790 --> 00:13:50,900
I have checked this one.

169
00:13:52,050 --> 00:13:53,490
Now, let us go through this.

170
00:13:54,630 --> 00:13:55,890
Let us run the program.

171
00:14:00,860 --> 00:14:07,250
Yeah, Pergament running is asking for the size of another and give the side 10 now first option, able

172
00:14:07,250 --> 00:14:08,700
to insert an element.

173
00:14:08,720 --> 00:14:11,420
I want to insert the three at index zero.

174
00:14:13,220 --> 00:14:18,920
Then under the choice, once again, I will in a certain element that is five at the next one.

175
00:14:21,690 --> 00:14:27,770
Then I will display the elements so you can see the two elements are added in a list that is in another

176
00:14:28,170 --> 00:14:29,520
that is three and five.

177
00:14:31,080 --> 00:14:34,860
Allen said a few more elements insert nine.

178
00:14:35,920 --> 00:14:37,510
An index to.

179
00:14:39,410 --> 00:14:44,390
Then once again, insert eight, index zero.

180
00:14:46,370 --> 00:14:51,740
Let us display this list, see, eight is inserted in the beginning, that is at zero, then we have

181
00:14:51,740 --> 00:14:52,880
three, five and nine.

182
00:14:53,660 --> 00:14:55,250
Here are the elements displayed.

183
00:14:56,120 --> 00:14:58,790
Nonreaders display five five is added next to.

184
00:14:59,210 --> 00:15:05,860
So I will select the option that is delete options to that index.

185
00:15:05,870 --> 00:15:12,770
I'll give it as to the letters, display the list once again, not a list of eight, three and nine

186
00:15:13,460 --> 00:15:14,420
five years gone.

187
00:15:15,950 --> 00:15:22,970
Let me call the function some and see the result of adding all these numbers, yes, Sommer's 20.

188
00:15:23,330 --> 00:15:29,210
So that's it like this, you can create a menu and try all the operations of an array.

189
00:15:29,420 --> 00:15:32,110
I am trying only a few options here, though.

190
00:15:32,120 --> 00:15:36,200
I haven't read a lot of operations inside a C++ class.

191
00:15:36,510 --> 00:15:41,270
This is how we have learned how to convert the C language programming into C++ program.

