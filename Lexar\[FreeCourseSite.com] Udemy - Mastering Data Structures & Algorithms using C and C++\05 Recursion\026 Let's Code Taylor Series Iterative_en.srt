1
00:00:00,610 --> 00:00:07,660
In this, we do look at implementation of function for inner cities using iterative method that is using

2
00:00:07,660 --> 00:00:08,050
loop.

3
00:00:10,160 --> 00:00:13,970
So already I have the body of a function, I will implement the body.

4
00:00:18,300 --> 00:00:20,970
I declare a variable that will be the only one.

5
00:00:24,780 --> 00:00:27,630
Then for iteration, I will take one variable I.

6
00:00:30,280 --> 00:00:34,600
We need numerator and denominator, so I will take double.

7
00:00:36,140 --> 00:00:41,600
Numerator that is initialized with one and double the denominator initialized with one.

8
00:00:43,710 --> 00:00:50,850
Next, follow the start I from one on and going up to and I placeless every time.

9
00:00:53,960 --> 00:01:01,280
Now for look numerator, I should multiply every time by X and denominator should be multiplied by I

10
00:01:01,310 --> 00:01:02,000
every time.

11
00:01:03,020 --> 00:01:06,520
So I one that I used to, so we'll get the factorial.

12
00:01:08,400 --> 00:01:10,320
Then there should be added that.

13
00:01:12,920 --> 00:01:18,900
Numerator by denominator, I don't know, said the loop right on.

14
00:01:20,390 --> 00:01:22,910
So this is an iterative version.

15
00:01:24,190 --> 00:01:33,400
Let us print the result obtained by that function percentile L.F. new line then called function by passing

16
00:01:33,400 --> 00:01:34,270
one command.

17
00:01:35,470 --> 00:01:36,790
So you already know the result.

18
00:01:36,790 --> 00:01:38,160
We should get the same result.

19
00:01:42,430 --> 00:01:45,520
Two point seven one a perfect.

20
00:01:47,220 --> 00:01:49,440
So this is the implementation of Taylor LCD.

21
00:01:51,110 --> 00:01:54,620
For finding the value of Airport X, for instance.

22
00:01:57,340 --> 00:02:00,610
For given the number of times, so that's all.

