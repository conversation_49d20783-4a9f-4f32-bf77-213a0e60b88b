1
00:00:00,750 --> 00:00:06,000
The topic is linear probing, it is a collision resolution technique.

2
00:00:06,660 --> 00:00:08,570
In the previous video, we saw cheating.

3
00:00:09,240 --> 00:00:11,280
Now, this is linear probing.

4
00:00:13,260 --> 00:00:20,670
So this is another technique that is probing, this comes under closed hashing, so we'll try to utilize

5
00:00:20,670 --> 00:00:25,730
the space given for a hash table only will not consume any extra space.

6
00:00:26,220 --> 00:00:31,950
So for certain, explain you what does it mean by linear probing then in that I will explain these operations

7
00:00:31,950 --> 00:00:34,570
insert search and we will do analysis on search.

8
00:00:34,890 --> 00:00:36,360
Then also will say delete.

9
00:00:37,260 --> 00:00:38,910
Now let me explain linear probing.

10
00:00:38,920 --> 00:00:39,980
So for explanation.

11
00:00:40,380 --> 00:00:46,460
Already I have taken a key space that set of keys and a hash table of size 10 and the hash function

12
00:00:46,530 --> 00:00:52,230
is atrophic equal to X more than what we will be using modified hash function for linear probing.

13
00:00:52,230 --> 00:00:53,700
That is as a dash of X.

14
00:00:54,120 --> 00:00:55,600
That is actual facts.

15
00:00:55,680 --> 00:01:00,240
Plus I feel fine then resolved more by 10.

16
00:01:01,500 --> 00:01:03,140
What is the firefighting effort?

17
00:01:03,270 --> 00:01:09,820
I is equal to I what is, I think the values starting from zero one two onwards?

18
00:01:10,650 --> 00:01:13,860
No, I will show you how to insert these keys.

19
00:01:14,280 --> 00:01:16,830
Inserting these keys procedure is very simple.

20
00:01:16,950 --> 00:01:18,830
Not without using that function directly.

21
00:01:18,840 --> 00:01:20,840
I will use this function and explain you.

22
00:01:21,120 --> 00:01:24,070
Then I'll explain you how we are actually following that function.

23
00:01:24,450 --> 00:01:30,640
So let us stop Foskey statistics statistics more than 10 as a six so it will get the story here.

24
00:01:30,960 --> 00:01:32,090
So there is nothing here.

25
00:01:32,100 --> 00:01:34,500
It is three empty statistics installed here.

26
00:01:35,960 --> 00:01:40,790
So we are not forming a languorously in chinning, this is a place for storing a key, so we have stored

27
00:01:40,790 --> 00:01:43,880
it the nexus of 30 more to 10 gifts.

28
00:01:43,880 --> 00:01:46,070
ZEITELS So toddies are stored here.

29
00:01:47,500 --> 00:01:54,340
Forty five more to ten gifts, index five, so five forty five is the store here then Tony.

30
00:01:54,350 --> 00:01:56,050
Three more to ten as the three.

31
00:01:56,050 --> 00:01:58,060
So twenty three is the story here.

32
00:01:59,690 --> 00:02:02,830
Now, let's observe this twenty five.

33
00:02:03,080 --> 00:02:06,390
This is a collusion software in selling secrets.

34
00:02:06,740 --> 00:02:08,810
I got a collusion here, see?

35
00:02:08,880 --> 00:02:13,300
Twenty five is here only, but this place is already occupied.

36
00:02:13,760 --> 00:02:16,610
Then I cannot have two kids at the same place.

37
00:02:16,610 --> 00:02:17,420
Then do what?

38
00:02:18,430 --> 00:02:25,800
It's Selenia probing, openair dressing, so store try to store it at the next space, next space again,

39
00:02:25,810 --> 00:02:27,150
there is a key available there.

40
00:02:27,160 --> 00:02:27,970
It's not free.

41
00:02:28,360 --> 00:02:30,950
Steelcase try to store the next space.

42
00:02:31,270 --> 00:02:32,370
Yes, this is free.

43
00:02:32,590 --> 00:02:32,830
So.

44
00:02:32,830 --> 00:02:34,750
Twenty five years of store here.

45
00:02:35,530 --> 00:02:41,120
C twenty five was mapped here, but there was no place here so no place here.

46
00:02:41,650 --> 00:02:43,770
This was weekend so we have used this one.

47
00:02:44,020 --> 00:02:51,940
So this is linear probing so often how many collisions we got at that free space to collusion software

48
00:02:51,940 --> 00:02:52,620
to probe.

49
00:02:52,630 --> 00:02:54,150
So we got a free space.

50
00:02:54,490 --> 00:02:55,450
So it's very simple.

51
00:02:55,450 --> 00:02:59,350
Whenever there is a collision, try to store an element at the next free space.

52
00:02:59,770 --> 00:03:06,180
Now let me show you how that function is used, what that function sees for twenty five.

53
00:03:06,340 --> 00:03:18,190
Let us do it as your dish of twenty five and it's actually twenty five plus F of zero more than 10.

54
00:03:18,670 --> 00:03:21,240
So the result is twenty five age of twenty five.

55
00:03:21,250 --> 00:03:27,910
This is the hash function Rossellini's five plus four zero eight for Phase II for Phase II.

56
00:03:27,940 --> 00:03:31,380
So this is zero zero more than 10.

57
00:03:31,720 --> 00:03:33,140
So answer is five.

58
00:03:33,430 --> 00:03:37,990
So this has given index of five but that is a collusion.

59
00:03:38,110 --> 00:03:39,490
We have used that function.

60
00:03:39,490 --> 00:03:39,730
Right.

61
00:03:40,060 --> 00:03:41,650
So collision the next.

62
00:03:41,650 --> 00:03:48,330
What we will do at Chadash of twenty five, we will calculate once again each of twenty five plus F

63
00:03:48,760 --> 00:03:49,360
one.

64
00:03:49,360 --> 00:03:53,340
This time I value is taking from zero onwards.

65
00:03:53,340 --> 00:03:56,990
So first of zero we try the next one more to ten.

66
00:03:57,160 --> 00:04:00,550
So this is five plus F of one.

67
00:04:01,090 --> 00:04:02,320
That is it for phase II.

68
00:04:02,320 --> 00:04:03,680
So flooding is one only.

69
00:04:03,700 --> 00:04:06,220
So there's this one mod then.

70
00:04:06,490 --> 00:04:10,450
So there are the six Check-Out indexed six all the keys there.

71
00:04:10,450 --> 00:04:11,350
It's not vacant.

72
00:04:11,350 --> 00:04:12,850
Can then try NextRadio.

73
00:04:12,880 --> 00:04:22,180
Fine then as you dash off thirty five as I charge twenty five plus f all four to this time and four

74
00:04:22,180 --> 00:04:24,850
zero F of London F of two more to ten.

75
00:04:25,270 --> 00:04:27,400
This is five plus.

76
00:04:27,400 --> 00:04:29,020
This will be two more.

77
00:04:29,020 --> 00:04:32,620
The ten is seven years Chicot seven.

78
00:04:32,920 --> 00:04:33,910
There is no collusion.

79
00:04:33,910 --> 00:04:34,890
So you store this.

80
00:04:35,650 --> 00:04:37,900
So this is how this function is computed.

81
00:04:37,900 --> 00:04:43,000
More than one time, first time collusion, second time collusion, third time we go to space.

82
00:04:44,700 --> 00:04:51,600
So instead of calling that function step by step, just a sad story, that next freespace, that for

83
00:04:51,600 --> 00:04:55,650
the function, see, now let me insert remaining keys.

84
00:04:57,160 --> 00:04:59,590
Forty three, it is mapped here.

85
00:04:59,920 --> 00:05:06,310
There is a collision, so use that same function F of I value zero, there's a collision then Gallimard

86
00:05:06,310 --> 00:05:08,070
with I value one next place.

87
00:05:08,080 --> 00:05:08,830
Yes, it is three.

88
00:05:09,070 --> 00:05:10,570
So store forty three here.

89
00:05:12,510 --> 00:05:15,780
The next 74 seventy for this map here.

90
00:05:17,700 --> 00:05:18,780
All reoccupied.

91
00:05:20,340 --> 00:05:28,080
The next occupied, next occupied, next, occupied next, yes, this is free, so seventy four will

92
00:05:28,080 --> 00:05:28,830
come here.

93
00:05:31,360 --> 00:05:33,200
See, very long distance, actually.

94
00:05:33,220 --> 00:05:35,320
It was mapped out for by the store at.

95
00:05:36,560 --> 00:05:41,710
Eight, so, so many coalition forces toting seventy four, one, two, three, four coalition, four

96
00:05:41,720 --> 00:05:43,700
after four coalition, it got stalled.

97
00:05:44,300 --> 00:05:44,930
So that's it.

98
00:05:44,930 --> 00:05:49,480
In this linear probing solution, there may be many collisions, right?

99
00:05:49,490 --> 00:05:55,380
More than one collisions are possible and we try to probe linearly until we get out of space.

100
00:05:56,000 --> 00:05:58,220
Now, I will add two more keys and show you.

101
00:06:00,450 --> 00:06:06,400
One is 19, so 19 will be mapped here, 19 goes here, this is free.

102
00:06:06,870 --> 00:06:07,950
There was no collusion.

103
00:06:08,370 --> 00:06:08,680
No.

104
00:06:08,700 --> 00:06:11,340
Next, I will insert twenty nine.

105
00:06:12,620 --> 00:06:21,530
Twenty nine, let me evaluate it here at your dash of twenty nine S s of twenty nine plus four four

106
00:06:21,620 --> 00:06:24,000
zero more than ten.

107
00:06:24,620 --> 00:06:26,510
This is twenty nine.

108
00:06:26,510 --> 00:06:30,170
Each of twenty nine is what, nine five zero zero.

109
00:06:30,650 --> 00:06:33,470
More than ten is a nine only.

110
00:06:33,710 --> 00:06:34,880
But there is a collision.

111
00:06:35,390 --> 00:06:36,230
There is a collision.

112
00:06:36,560 --> 00:06:38,840
Then try next value of as of.

113
00:06:38,870 --> 00:06:41,030
AII right let us try that.

114
00:06:41,930 --> 00:06:44,090
It will be nine plus force one.

115
00:06:44,090 --> 00:06:48,140
It will be so one more than that is zero.

116
00:06:48,560 --> 00:06:53,840
So try zero six goehring cyclic C because we have given more than here.

117
00:06:54,140 --> 00:06:56,960
Ten is the side of the table, so it will go cyclic.

118
00:06:57,140 --> 00:06:58,400
So try at zero.

119
00:06:58,430 --> 00:07:05,360
It is also occupied, then try next to one nine plus effort to you try to monitor ten.

120
00:07:05,570 --> 00:07:07,220
That is eleven more than this one.

121
00:07:07,610 --> 00:07:08,780
So try that one.

122
00:07:09,080 --> 00:07:10,190
Yes it is found.

123
00:07:10,370 --> 00:07:12,200
So twenty nine is a store here.

124
00:07:12,950 --> 00:07:15,350
So this is how insertions are done.

125
00:07:16,070 --> 00:07:24,680
So I have inserted nineteen and twenty nine to show you that the places are found circularly circularly

126
00:07:25,400 --> 00:07:29,750
though we are going linear but once we have reached the end of a table then again will start from the

127
00:07:29,750 --> 00:07:30,140
top.

128
00:07:30,470 --> 00:07:31,990
So that's all the word insertion.

129
00:07:32,570 --> 00:07:34,970
Now let us see how to search for the keys.

130
00:07:35,270 --> 00:07:38,600
So for searching also we use the same hash function.

131
00:07:38,870 --> 00:07:41,570
This function will be sufficient for us for searching.

132
00:07:41,900 --> 00:07:42,960
Let us look at it.

133
00:07:43,310 --> 00:07:45,620
Suppose I want to search for a key.

134
00:07:45,950 --> 00:07:47,060
Forty five.

135
00:07:47,810 --> 00:07:49,400
Forty five more to ten.

136
00:07:49,400 --> 00:07:50,390
Go to this place.

137
00:07:50,390 --> 00:07:50,660
Yes.

138
00:07:50,660 --> 00:07:51,560
Keys found.

139
00:07:51,710 --> 00:07:52,310
Yes.

140
00:07:52,700 --> 00:07:53,900
So it's a successful.

141
00:07:54,260 --> 00:07:56,210
I want to search for a key.

142
00:07:59,110 --> 00:08:09,910
Seventy four seventy four more under ten gives us index of four, is it 74, nor is it 74, nor is it

143
00:08:09,910 --> 00:08:14,140
74, nor is it 74, nor is it 74.

144
00:08:14,560 --> 00:08:16,020
Yes, stop.

145
00:08:17,930 --> 00:08:24,410
We got the key stop, see how many comparisons we have done, one, two, three, four, five, five

146
00:08:24,410 --> 00:08:25,790
comparisons we have done.

147
00:08:26,810 --> 00:08:27,360
That's it.

148
00:08:27,590 --> 00:08:32,049
So this Lilium probing method as little time ticking, right?

149
00:08:32,350 --> 00:08:33,330
It's not constant.

150
00:08:33,350 --> 00:08:34,549
It's more than constant.

151
00:08:34,559 --> 00:08:36,500
So it's little time ticking.

152
00:08:36,530 --> 00:08:37,909
Pathologists successful.

153
00:08:39,090 --> 00:08:49,500
Seventy four, this farm, let us strive for a key 40, 40, no, was this 40, 40 more than 10 zero

154
00:08:49,950 --> 00:08:52,530
as it 40, nor is it 40.

155
00:08:52,710 --> 00:08:55,890
No next place it's vacant.

156
00:08:56,490 --> 00:09:02,730
So you should stop your search once you got a free space, if you got a free space, then you should

157
00:09:02,730 --> 00:09:03,220
stop.

158
00:09:03,480 --> 00:09:04,650
Now, listen carefully.

159
00:09:04,950 --> 00:09:06,510
I know you've got one doubt.

160
00:09:07,600 --> 00:09:14,020
You get down to it, listen, once again, when our key is being search, use the hash function, go

161
00:09:14,020 --> 00:09:19,150
to that index, if it is not equal, next, not equal empty, please stop.

162
00:09:20,160 --> 00:09:26,190
Emplace formants element is not there, so the method for searching is take a hash function, get the

163
00:09:26,190 --> 00:09:35,030
index, go to that index, go searching linearly until if the queues form or you got a free space.

164
00:09:35,550 --> 00:09:38,040
So this was unsuccessful.

165
00:09:38,840 --> 00:09:40,190
40 was not found.

166
00:09:41,420 --> 00:09:44,330
So that's all I have shown you how to search.

167
00:09:46,500 --> 00:09:53,880
Right now, I'll come to analysis, let us do analysis and remove this and I'll do analysis now let

168
00:09:53,880 --> 00:10:00,870
us do analysis already in training, I have explained that analysis of hashing is done based on the

169
00:10:00,870 --> 00:10:01,880
loading factor.

170
00:10:02,160 --> 00:10:04,500
It is not done based on a number of elements.

171
00:10:05,500 --> 00:10:10,780
In all the data structures, we have done it based on the number of elements of the input, but it is

172
00:10:10,780 --> 00:10:12,920
never done based on size of input.

173
00:10:13,240 --> 00:10:19,570
If you try to do it on size of input and on the order of always hashing, it will be same as linear

174
00:10:19,570 --> 00:10:19,980
search.

175
00:10:20,140 --> 00:10:22,740
So we'll do the analysis based on loading.

176
00:10:23,150 --> 00:10:24,280
So what is loading factor?

177
00:10:24,520 --> 00:10:30,220
Loading factor is number of elements, a number of keys divided by the size of a table.

178
00:10:30,700 --> 00:10:35,650
So in my example, if you see the side of the table, the stand and the number of elements I have one,

179
00:10:35,650 --> 00:10:37,840
two, three, four, five, six, seven, eight, nine.

180
00:10:38,350 --> 00:10:47,160
So if I calculate loading factor in my example, loading factor is nine by ten point nine.

181
00:10:47,950 --> 00:10:51,250
So loading factor is point nine in our example.

182
00:10:51,790 --> 00:10:53,740
See loading factor we got in.

183
00:10:54,800 --> 00:10:58,120
Decimal, that is less than one in the chain.

184
00:10:58,310 --> 00:11:04,410
It was greater than one, but here the size of the table is a fixed, so you cannot exceed the size.

185
00:11:04,790 --> 00:11:07,510
So that's why it is less than one.

186
00:11:07,550 --> 00:11:10,940
It's always a loading factor will be less than one only it will be in decimal.

187
00:11:11,810 --> 00:11:18,920
Now, one important thing that is very, very important thing that is loading factors should always

188
00:11:18,920 --> 00:11:20,660
be less than or equal.

189
00:11:20,660 --> 00:11:22,370
Two point five.

190
00:11:23,310 --> 00:11:29,850
It should not exceed point five, this point is very important to answer, many doubts are clear, many

191
00:11:29,850 --> 00:11:30,360
doubts.

192
00:11:31,010 --> 00:11:38,280
It means that if the hash table sizes are 10, you should not feel more than psyche's.

193
00:11:39,370 --> 00:11:40,880
More than five keys, right?

194
00:11:41,080 --> 00:11:46,390
So whatever the size of the hash table, at the most, it can be half of what you see.

195
00:11:46,390 --> 00:11:50,740
In my example, I have size 10, but I have inserted nine keys.

196
00:11:50,770 --> 00:11:51,610
That is wrong.

197
00:11:51,670 --> 00:11:54,930
That is wrong for explanation purpose.

198
00:11:54,940 --> 00:12:00,670
I have taken small size and I have shown you more keys so I can show you various possibilities.

199
00:12:01,450 --> 00:12:02,400
But this is wrong.

200
00:12:02,590 --> 00:12:04,810
You should not have more than five keys.

201
00:12:05,200 --> 00:12:05,970
So that's it.

202
00:12:06,220 --> 00:12:11,070
If you have half of the table is empty, then a lot of these pieces will be there.

203
00:12:11,380 --> 00:12:17,440
Then while searching, if you remember, for unsuccessful search, we stop whenever we get free space,

204
00:12:17,770 --> 00:12:19,090
then we can easily stop.

205
00:12:19,360 --> 00:12:20,980
We can get a lot of free space.

206
00:12:22,210 --> 00:12:27,490
So here in my example, I have only one free space because I have installed nine keys, which is from.

207
00:12:28,870 --> 00:12:36,520
All right, so LAMDA should be less than one five, so LAMDA should be less than equal 2.5, right?

208
00:12:36,880 --> 00:12:38,830
I think this has cleared many doubts.

209
00:12:39,870 --> 00:12:45,780
Then what is the time taken by is dependent on the loading factor, so every successful time?

210
00:12:45,810 --> 00:12:46,880
This is a known formula.

211
00:12:46,890 --> 00:12:49,290
So if you don't have to do analysis, there's an old formula.

212
00:12:49,290 --> 00:12:56,370
Just you have to accept this formula and average unsuccessful search at times one by one minus LAMDA.

213
00:12:56,850 --> 00:13:03,000
So these are the successful search and unsuccessful search average times for linear protein.

214
00:13:04,050 --> 00:13:10,240
Now, drawback of linear probing, first of all, you have to keep half of the hash table vacant, so

215
00:13:10,320 --> 00:13:11,210
space wasted.

216
00:13:11,940 --> 00:13:12,320
Yes.

217
00:13:12,810 --> 00:13:17,810
Second thing, a lot of keys may be accumulating at one place and forming a cluster.

218
00:13:18,120 --> 00:13:22,690
They all are forming a single group and that is cluster continuous to have keys.

219
00:13:23,070 --> 00:13:28,740
So when you're searching for something, for example, suppose someone before is not there and even

220
00:13:28,740 --> 00:13:30,130
twenty five is also not there.

221
00:13:30,180 --> 00:13:30,840
Assume that.

222
00:13:31,110 --> 00:13:32,400
No, I'm searching for.

223
00:13:33,790 --> 00:13:40,480
Thirty four, if I'm searching for four so far, twenty four to go to the next four, is it for no for

224
00:13:40,480 --> 00:13:44,040
no electricity for no there is a free space, blank space.

225
00:13:44,040 --> 00:13:49,780
So we reach blank space keys not far so for searching some key that belongs to the next fall.

226
00:13:49,780 --> 00:13:54,400
We are searched comparing with those keys also, which doesn't belong to index four.

227
00:13:54,400 --> 00:13:55,780
They belong to some other index.

228
00:13:56,530 --> 00:14:01,050
So different keys together may form a cluster, a block of keys at one place.

229
00:14:01,450 --> 00:14:04,780
So this is the drawback, we call it as primary clustering.

230
00:14:04,780 --> 00:14:09,040
So linear probing have a problem of primary clustering of keys.

231
00:14:09,040 --> 00:14:11,500
That is group of keys together at one place.

232
00:14:12,220 --> 00:14:13,920
Now let us look at delete.

233
00:14:15,160 --> 00:14:18,220
Suppose I want to delete twenty five.

234
00:14:18,400 --> 00:14:26,140
This key I want to delete, use the hash function, go to that particular index that is five then start

235
00:14:26,140 --> 00:14:27,760
searching for twenty five.

236
00:14:27,760 --> 00:14:28,540
Is it twenty five.

237
00:14:28,840 --> 00:14:30,190
Is it twenty five known as it.

238
00:14:30,190 --> 00:14:32,920
Twenty five years found then delete.

239
00:14:35,300 --> 00:14:40,130
Thirty five years removed, so I was to take this place this weekend, is that right?

240
00:14:40,920 --> 00:14:47,130
As a trader, what about the seventy four seventy four of us kept here because there was no space here.

241
00:14:47,790 --> 00:14:50,340
Seventy four actually belongs to location for.

242
00:14:51,980 --> 00:14:56,050
So if this is free, then seventy four should be moved here.

243
00:14:57,080 --> 00:15:05,210
Yes, it should be moved there, so it's not simple that you just delete a key and leave the place vacant.

244
00:15:05,510 --> 00:15:08,810
Now imagine if I'm searching for 70 for what happens.

245
00:15:09,170 --> 00:15:11,590
Seventy four for is it seventy four.

246
00:15:11,600 --> 00:15:12,470
No, no.

247
00:15:12,620 --> 00:15:13,640
Freespace stop.

248
00:15:13,680 --> 00:15:14,180
Seventy four.

249
00:15:14,180 --> 00:15:14,750
It's not there.

250
00:15:15,510 --> 00:15:21,000
So if you don't fill this space by seventy four, seventy four cannot be searched at all, so you have

251
00:15:21,000 --> 00:15:26,730
to shift this key, you have to shift that key, shifting that keys it, that is.

252
00:15:27,940 --> 00:15:34,010
No, let us suppose I'm the leading forty five if forty five is gone.

253
00:15:34,880 --> 00:15:36,790
Twenty 26 aboard this one No.

254
00:15:36,790 --> 00:15:38,560
Twenty six belongs to this place only.

255
00:15:38,980 --> 00:15:40,270
There's no meaning in shifting.

256
00:15:40,510 --> 00:15:41,820
So shifting is not easy.

257
00:15:41,830 --> 00:15:45,390
You have to check whether it belongs to that index or the next index what it is.

258
00:15:45,700 --> 00:15:46,510
So there are many things.

259
00:15:46,510 --> 00:15:52,250
So solution for this one is if really you want to delete, then search for the key.

260
00:15:52,250 --> 00:15:56,950
If it is found, then take out all the keys and instead all the keys once again.

261
00:15:57,490 --> 00:16:00,250
So that is as rehashing.

262
00:16:02,490 --> 00:16:08,400
So the solution for deletion is not simple, when you want to delete a key, you have to adjust the

263
00:16:08,400 --> 00:16:13,890
rest of the keys and there are a lot of things that you have to take care so far that better take out

264
00:16:13,890 --> 00:16:16,890
all the keys and insert them once again, one by one.

265
00:16:18,020 --> 00:16:19,940
So this is a lot of effort, a lot of work.

266
00:16:20,120 --> 00:16:24,140
Yes, so that is the reason interlinear probing.

267
00:16:24,440 --> 00:16:27,310
We don't suggest deletion of element.

268
00:16:27,470 --> 00:16:29,060
We don't delete elements.

269
00:16:29,930 --> 00:16:34,040
We suggest we don't suggest deletion of device, what we do is.

270
00:16:35,440 --> 00:16:41,320
Hmm, if really you want to delete it, then one thing you can do is you can have some flag to show

271
00:16:41,320 --> 00:16:44,140
whether this key's there or not there so badly.

272
00:16:44,140 --> 00:16:47,670
Along with the table, you can have a flag and show that this is not this.

273
00:16:47,800 --> 00:16:48,730
So this is their.

274
00:16:48,730 --> 00:16:49,300
This is their.

275
00:16:49,300 --> 00:16:49,790
This is their.

276
00:16:49,810 --> 00:16:51,070
So this is not there.

277
00:16:51,370 --> 00:16:58,090
So I can put a flag beside each to show it is present or not present, if at all really you want to

278
00:16:58,090 --> 00:17:00,430
delete because rehashing is not suggested.

279
00:17:00,430 --> 00:17:01,450
It is time consuming.

280
00:17:02,990 --> 00:17:11,150
So I should say deletion is not easy and linear probing, so we don't recommend deletion and linear

281
00:17:11,150 --> 00:17:11,569
probing.

282
00:17:12,440 --> 00:17:14,450
So that's all about cleaning and probing.

283
00:17:14,450 --> 00:17:19,970
Next video, we'll look at quarterdeck proving it is more similar to linear probing only with a minor

284
00:17:19,970 --> 00:17:20,569
difference.

285
00:17:20,810 --> 00:17:22,089
So that's all in this region.

