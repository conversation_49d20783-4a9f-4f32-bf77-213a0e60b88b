1
00:00:00,540 --> 00:00:07,920
In this video, we learn how to display a linguist if our little English is there, how to display all

2
00:00:07,920 --> 00:00:09,510
the elements of linguists.

3
00:00:09,840 --> 00:00:16,320
So for this, we should know how to traverse a limitless traversing, so traversing minutes, visiting

4
00:00:16,320 --> 00:00:18,750
all the norms of a linguist once.

5
00:00:19,650 --> 00:00:26,590
So let us see how to traverse so far visiting all the nodes one by one, we must start from furstenburg.

6
00:00:27,270 --> 00:00:33,460
So this pointer first will give the ideas of a small we should not disturb or force.

7
00:00:33,480 --> 00:00:37,850
We should take one more temporary pointer and we should take the help of that pointer.

8
00:00:38,160 --> 00:00:41,290
So let us take one point to be pointing on this first node.

9
00:00:41,550 --> 00:00:44,570
So its addresses doing this as is doing it.

10
00:00:44,940 --> 00:00:46,200
So this should be 200.

11
00:00:47,530 --> 00:00:57,910
So how that can be done programmatically, say struck gold star, be so disappointed, there's.

12
00:00:58,870 --> 00:01:07,900
And this is having a restaurant admits it seems first, so a sign of it first so that this line B will

13
00:01:07,900 --> 00:01:09,400
be a pointer pointing up on him.

14
00:01:09,400 --> 00:01:11,020
Not very fast is pointing.

15
00:01:13,360 --> 00:01:20,350
So we are on the first note then, as I said, travelers visiting on, so let us move to the next node,

16
00:01:20,650 --> 00:01:23,150
how to move to the next node.

17
00:01:23,440 --> 00:01:27,850
So in the previous video, we saw how appointer can move to next node.

18
00:01:27,860 --> 00:01:30,700
So we will take this address.

19
00:01:30,730 --> 00:01:32,620
This addresses what piece next.

20
00:01:33,160 --> 00:01:35,410
So these next.

21
00:01:36,830 --> 00:01:40,140
That's what do tend to be.

22
00:01:40,280 --> 00:01:44,600
So this should be come to 10, so then this is given to bid.

23
00:01:44,600 --> 00:01:50,240
We became to ten minutes where pointing no, it's not on this 200 at this point on this nor 210.

24
00:01:50,690 --> 00:01:52,010
So a signed copy.

25
00:01:52,310 --> 00:01:56,780
So this means we have moved to next Norder.

26
00:01:57,350 --> 00:01:58,430
So I remove this.

27
00:02:00,100 --> 00:02:06,460
We have moved to next more so in this way, I can again move to the next, nor the next, nor the next,

28
00:02:06,460 --> 00:02:08,110
nor so I can go on doing this.

29
00:02:08,320 --> 00:02:11,980
So this means this should be done repeatedly in the loop.

30
00:02:11,980 --> 00:02:15,430
So I will use my loop now.

31
00:02:15,430 --> 00:02:15,970
Why, why?

32
00:02:16,330 --> 00:02:17,460
Why not follow up?

33
00:02:17,770 --> 00:02:19,930
We don't know how many nodes are there.

34
00:02:20,650 --> 00:02:26,250
If you know how many times you have to repeat, you can go for follow up when you don't know, use wide

35
00:02:26,260 --> 00:02:26,570
loop.

36
00:02:26,920 --> 00:02:29,330
So this has to be done in one loop.

37
00:02:30,430 --> 00:02:34,150
If we continue this, we will move to the next node.

38
00:02:36,060 --> 00:02:39,090
Again, repeat, so people move to next more.

39
00:02:41,290 --> 00:02:48,140
So, again, repeat, we will move to the next node, so if this is repeated again, we will move to

40
00:02:48,140 --> 00:02:49,030
the next node.

41
00:02:49,060 --> 00:02:51,360
So what is the next node next to this?

42
00:02:51,370 --> 00:02:51,840
None.

43
00:02:51,850 --> 00:02:57,830
Nothing is then so be became what became zero seven P became zero.

44
00:02:57,850 --> 00:02:58,510
We should stop.

45
00:02:58,510 --> 00:03:01,030
It means we have reached the end of our Linklaters.

46
00:03:01,360 --> 00:03:03,240
We have gone outside the limits.

47
00:03:03,700 --> 00:03:07,840
So we should continue this as long as PS not zero.

48
00:03:08,110 --> 00:03:11,620
So it means we should stop vampyres zero here.

49
00:03:11,640 --> 00:03:17,410
We should mention the condition not for termination but for continuation.

50
00:03:17,920 --> 00:03:23,210
Termination is one B is equal to zero then continuation B not equal to zero.

51
00:03:23,500 --> 00:03:27,420
So here again the condition B not equal to zero.

52
00:03:28,030 --> 00:03:29,870
So yes, B was not zero.

53
00:03:29,920 --> 00:03:33,510
So it moved, not moved to not zero move, not zero move.

54
00:03:33,820 --> 00:03:35,260
Then it became zero strupp.

55
00:03:36,640 --> 00:03:42,610
So this loop will help us traverse entire legalist instead of zero.

56
00:03:42,640 --> 00:03:49,180
We can also say none have already shown you this condition, even we can say it will be for this one.

57
00:03:49,840 --> 00:03:56,500
So this is very important, traversing the entire linguist's, this loop and this statement.

58
00:03:57,250 --> 00:04:00,310
So the point out that we'll take you through all the notes one by one.

59
00:04:00,490 --> 00:04:01,550
This is very important.

60
00:04:02,050 --> 00:04:08,650
Now, while traversing, we can do a lot of things, for example, displaying all the notes.

61
00:04:10,320 --> 00:04:16,829
Counting the north, just as you are going on moving at one count, one, two, three, so you can count

62
00:04:16,829 --> 00:04:22,860
the north or you can add all the elements out against search for an element, sort of the element,

63
00:04:23,330 --> 00:04:26,060
all the operations can be done by traversing.

64
00:04:26,850 --> 00:04:27,960
So just one operation.

65
00:04:27,960 --> 00:04:30,030
I mean, right here, this is a statement.

66
00:04:30,060 --> 00:04:30,420
All right.

67
00:04:30,420 --> 00:04:33,600
Next, be a sign piece next.

68
00:04:33,930 --> 00:04:41,580
But before that, I will say April 10th percentile, the peak ABS data.

69
00:04:42,590 --> 00:04:43,860
Now, let me read the quote.

70
00:04:44,460 --> 00:04:47,550
See, Fossett will bring the data and move the pointer.

71
00:04:47,700 --> 00:04:54,510
Pointer goes here again, bring the data, move to next node pointer goes here.

72
00:04:54,990 --> 00:04:58,230
Pointer is not in the data and move to next node.

73
00:04:58,560 --> 00:05:04,890
So seven it will then move to next node then pointer is not number in the data brittelle and move to

74
00:05:04,890 --> 00:05:05,670
next node.

75
00:05:07,940 --> 00:05:16,700
Continue in this way, this will print and dial inkless, so this is the code for displaying a linguist,

76
00:05:17,330 --> 00:05:24,410
I can even make it out as a function, let us call it a function display, which takes this as a barometer.

77
00:05:26,700 --> 00:05:35,850
Now, how to call this function call does display by passing first point, you can call this function

78
00:05:35,850 --> 00:05:37,120
by passing first pointer.

79
00:05:38,220 --> 00:05:41,430
So this is for displaying the list.

