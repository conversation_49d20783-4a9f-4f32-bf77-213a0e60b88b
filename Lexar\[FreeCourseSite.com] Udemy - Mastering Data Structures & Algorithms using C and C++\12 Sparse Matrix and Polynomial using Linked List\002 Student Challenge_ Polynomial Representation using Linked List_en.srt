1
00:00:00,510 --> 00:00:08,160
In the video, we learn about polynomial representation using this topic we have already covered in

2
00:00:08,189 --> 00:00:08,970
arrays.

3
00:00:09,930 --> 00:00:14,070
If you have not seen that video, you can watch it polynomial representation using arrays.

4
00:00:15,190 --> 00:00:19,650
For now, legacy presented using linguist, here is an example polynomial.

5
00:00:19,750 --> 00:00:26,250
This is a univariate polynomial and this is defined in terms of just a single variable X, so it is

6
00:00:26,290 --> 00:00:27,950
summation of atoms.

7
00:00:27,970 --> 00:00:30,480
There are more than one terms being added together.

8
00:00:31,210 --> 00:00:36,170
If you know the value of X, we can evaluate and get the result of that polynomial.

9
00:00:37,150 --> 00:00:40,690
We want to represent this in our applications.

10
00:00:41,300 --> 00:00:45,160
So for representation, we have to store the data about that polynomial.

11
00:00:45,610 --> 00:00:49,030
That data can be stored either in an array or linguist.

12
00:00:49,210 --> 00:00:54,980
So we have already seen the how to represent the data related to polynomial in a linguist.

13
00:00:56,410 --> 00:01:02,180
So if you observe a polynomial, each atom is having its coefficient an exponent.

14
00:01:02,590 --> 00:01:08,140
Then if we know the value of X, then we can trace the value of X to the power that is exponent and

15
00:01:08,140 --> 00:01:10,990
then multiply it coefficient so we can get down to four digits.

16
00:01:10,990 --> 00:01:12,370
One term like that.

17
00:01:12,370 --> 00:01:14,640
We can evaluate all the atoms and get the result.

18
00:01:15,250 --> 00:01:19,750
So it is sufficient to store coefficient and exponent of each item.

19
00:01:21,310 --> 00:01:27,740
So these storms, we can represent them in the form of a link so far so we can define each item as a..

20
00:01:27,760 --> 00:01:29,730
And these are set of nodes.

21
00:01:30,010 --> 00:01:33,520
So let us define a node for one single column.

22
00:01:34,740 --> 00:01:40,480
Here is a. having coefficient exponent on a pointer to next node.

23
00:01:40,890 --> 00:01:42,920
Let us define a structure for this one.

24
00:01:44,230 --> 00:01:51,820
This is a note having integer coefficient in digit exponent and pointed to the next Snork, so you have

25
00:01:51,820 --> 00:01:56,050
to get integers if you feel that coefficient of the float, you can take float also.

26
00:01:57,700 --> 00:02:03,820
Now, one more thing you can observe, this is a note of a linguist and just having three members now

27
00:02:04,380 --> 00:02:10,000
coefficient exponent and a pointer so far, whatever the linguist we have studied that time, we were

28
00:02:10,000 --> 00:02:15,700
taking only one value, but now we're using a linguist for based on the requirement, a note can have

29
00:02:15,700 --> 00:02:17,020
any number of data numbers.

30
00:02:17,620 --> 00:02:21,420
So now let me present this polynomial as a link.

31
00:02:21,420 --> 00:02:22,000
The list.

32
00:02:23,400 --> 00:02:28,650
Here I have a list of these type of notes where each note is having three sections.

33
00:02:28,680 --> 00:02:31,610
This is for Coefficient Exponent and Appointer Conex note.

34
00:02:31,980 --> 00:02:38,640
So there are four terms in a polynomial I have to call for n let us fill up the values from that polynomial.

35
00:02:39,030 --> 00:02:41,450
First coefficient is for an export industry.

36
00:02:41,460 --> 00:02:45,810
So this is for common three and this is nine to.

37
00:02:47,540 --> 00:02:55,130
Nine, two, and this is six and power is one six and power is one, exponent is one, that is seven

38
00:02:55,130 --> 00:02:56,180
and zero.

39
00:02:56,210 --> 00:02:56,900
That is seven.

40
00:02:56,900 --> 00:03:00,740
And explaining this little result can be represented.

41
00:03:01,340 --> 00:03:05,900
If you remember, in other we were also storing how many guns have there here.

42
00:03:05,900 --> 00:03:07,040
We don't need to store it.

43
00:03:07,040 --> 00:03:08,990
If you want, you can store it somewhere else.

44
00:03:09,350 --> 00:03:13,370
Otherwise the number of nodes are equal to a number of items.

45
00:03:13,380 --> 00:03:18,080
You will not have any extra nodes here for that solid representation.

46
00:03:19,250 --> 00:03:23,000
Now we will learn how to evaluate or polynomial.

47
00:03:23,720 --> 00:03:29,000
Already we have a polynomial and this is represented using a linguist using this node structure.

48
00:03:29,360 --> 00:03:36,800
Now, if we know the value of X, then how we can get the result of this polynomial.

49
00:03:36,980 --> 00:03:42,860
If suppose X is one, then this will be four plus nine plus six plus seven effects of the two.

50
00:03:43,100 --> 00:03:49,220
Then it will be forwarded to Tokyo plus nine into two squared, plus six and two two seven.

51
00:03:49,340 --> 00:03:51,100
That's all I have to evaluate this one.

52
00:03:51,770 --> 00:03:56,390
I will show you the procedure and also write a function how to evaluate this one.

53
00:03:57,920 --> 00:03:59,170
Let us look at the procedure.

54
00:03:59,690 --> 00:04:03,730
They know the value of X X is something, let's say two, three, whatever it is.

55
00:04:03,800 --> 00:04:09,480
So for getting the result of this polynomial, I should raise this two to the power of three.

56
00:04:10,250 --> 00:04:13,590
I should take two point three and then multiply that with four.

57
00:04:13,910 --> 00:04:15,170
Then go to the next node.

58
00:04:16,240 --> 00:04:22,510
Then over to multiply together with nine, go to next mode to power one, then multiply the result with

59
00:04:22,510 --> 00:04:26,600
six and then they go to the next node 2.0, multiply that seven.

60
00:04:26,980 --> 00:04:33,640
So I have to scan through the link lists at each node I show to raise the value of X to this exponent

61
00:04:33,640 --> 00:04:35,380
and the result should be multiplied with this.

62
00:04:36,420 --> 00:04:37,170
Coefficient.

63
00:04:38,160 --> 00:04:44,130
And add all the value your research, so the procedure is very simple, so I will try to function here

64
00:04:44,130 --> 00:04:45,360
for evaluation.

65
00:04:46,280 --> 00:04:51,470
Here is the function, function, image evaluation that is a rather have taken, which is taking a parameter,

66
00:04:51,650 --> 00:04:58,220
the value of X, then I have some temporary variables, that is some for finding the sum of all the

67
00:04:58,220 --> 00:04:58,670
terms.

68
00:04:59,150 --> 00:05:02,810
And I have taken a Pointer Cube that is pointing on P.

69
00:05:03,020 --> 00:05:06,920
I have taken the name of the first pointer speed because it's representing a polynomial.

70
00:05:07,220 --> 00:05:07,450
So.

71
00:05:07,460 --> 00:05:12,310
Q I'm taking it as a temporary pointer for scanning through this list for Q Value pointing here.

72
00:05:13,160 --> 00:05:15,910
So you will be accessing a dome and moving to the next term.

73
00:05:15,920 --> 00:05:17,870
In this way it will scan the whole linked list.

74
00:05:18,260 --> 00:05:24,230
So let us do this process so we know well that whatever the value of X that should be raised to the

75
00:05:24,500 --> 00:05:25,540
power of this one.

76
00:05:25,550 --> 00:05:34,010
So find out power of X comma cuz exponent that is cuz exponent.

77
00:05:34,430 --> 00:05:37,010
Q gabs exponent.

78
00:05:38,470 --> 00:05:44,110
This will fight or the power that they should be multiplied, it kills coefficient this coefficient

79
00:05:44,470 --> 00:05:48,520
that is coefficient, so kuze coefficient.

80
00:05:50,800 --> 00:05:57,960
Then this result, whatever we get, we get just one bomb and this should be added in some so some plus,

81
00:05:58,660 --> 00:06:00,520
that's all the expression.

82
00:06:00,940 --> 00:06:06,190
It will evaluate when Tom DeLay should move to next room and do the same thing.

83
00:06:06,210 --> 00:06:12,290
You assign Qs next so you will move to the next node and again, repeat the same thing.

84
00:06:12,970 --> 00:06:15,880
So I have to repeat the same thing means it should be in the loop.

85
00:06:15,880 --> 00:06:16,990
So I will write a loop.

86
00:06:17,320 --> 00:06:20,900
How long this loop should work until Q becomes null.

87
00:06:21,070 --> 00:06:26,470
So while it's not null white Q is not why?

88
00:06:26,560 --> 00:06:28,420
Q Not equal to none.

89
00:06:31,360 --> 00:06:37,260
Continue these two steps that follow this will evaluate the outcome of the next storm and then evaluate

90
00:06:37,270 --> 00:06:40,870
it and move to the next storm, then finally return the results some.

91
00:06:41,970 --> 00:06:42,760
So that's all.

92
00:06:43,020 --> 00:06:49,530
And the three, we have learned how to represent the polynomial and how to evaluate a polynomial, not

93
00:06:49,560 --> 00:06:52,980
adding to polynomials, that is a student exercice.

94
00:06:53,310 --> 00:06:55,270
We have already seen that in an area.

95
00:06:55,410 --> 00:07:01,260
So hereupon the linguists, it will be similar, but some minor changes because this is a linguist,

96
00:07:01,530 --> 00:07:07,320
so you will go on it and write on the program or adding to polynomial evaluation of polynomial.

