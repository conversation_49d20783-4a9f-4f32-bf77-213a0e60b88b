1
00:00:00,590 --> 00:00:10,790
Our next topic is <PERSON>'s Matics, a matrix in which there are more number of zero elements that I have

2
00:00:10,790 --> 00:00:19,550
an example, matrix of order, eight by nine, and you can see that it is having very few non-zero elements

3
00:00:19,550 --> 00:00:21,560
like the three, eight, 10.

4
00:00:22,510 --> 00:00:24,160
And for two, six, five, nine.

5
00:00:25,380 --> 00:00:31,890
There are very few non-zero elements, if you count one, two, three, four, five, six, seven,

6
00:00:31,890 --> 00:00:39,280
eight, only eight non-zero elements are there, mostly in the statistical data or survey data data

7
00:00:39,690 --> 00:00:41,280
presented in the form of a matrix.

8
00:00:41,280 --> 00:00:43,590
And there may be a lot of zero values.

9
00:00:44,450 --> 00:00:52,520
If so, then if you are using sparse mattresses in our program, then we may be having zero values for

10
00:00:52,520 --> 00:00:56,270
which we may be wasting space and also processing time.

11
00:00:56,900 --> 00:00:57,980
So, again, the same thing.

12
00:00:57,980 --> 00:01:03,780
We want to store metrics in less amount of space and the processing is also less.

13
00:01:04,730 --> 00:01:08,300
So what we should do, we should start only non-zero elements.

14
00:01:09,110 --> 00:01:13,440
So for storing non-zero elements, there are more than one approach available.

15
00:01:13,850 --> 00:01:19,610
One method is coordinate list and the second one is compressed Spar's roll.

16
00:01:20,060 --> 00:01:24,590
So these are two representation coordinate list and compressed spots all.

17
00:01:24,830 --> 00:01:29,190
And the SCOTNEY list is also called as three column representation.

18
00:01:29,660 --> 00:01:35,840
So first we will see coordinated list all three column representation for coordinate list for every

19
00:01:35,840 --> 00:01:36,990
non zero element.

20
00:01:37,580 --> 00:01:38,720
We need three things.

21
00:01:38,960 --> 00:01:47,480
First is Exaro number signees column number and the element itself so far, three rule number is one,

22
00:01:47,480 --> 00:01:50,640
column number is eight, and the element is three four eight.

23
00:01:50,780 --> 00:01:54,730
This one rule number is two column number history and the element is eight.

24
00:01:55,010 --> 00:01:58,130
So we will have a couple of three values.

25
00:01:58,130 --> 00:02:03,230
That is rule number, column number and the element so we can have the list of tuple so we can call

26
00:02:03,230 --> 00:02:07,000
them as coordinates or we can represent them in the form of columns.

27
00:02:07,010 --> 00:02:08,000
That is three columns.

28
00:02:08,449 --> 00:02:17,150
First column is rule and the second columns column number, and the third one is element itself.

29
00:02:17,510 --> 00:02:22,210
Now let's represent those non-zero values and these three columns.

30
00:02:22,640 --> 00:02:28,730
So first, non-zero value as one comma eight, one, eight and three.

31
00:02:29,690 --> 00:02:31,610
I have left the Fosterville blank.

32
00:02:31,620 --> 00:02:39,230
So again, I will show you what is the use of that first rule then eight two three eight two 610.

33
00:02:41,130 --> 00:02:48,260
The neat rule, raw numbers to call the number three elements, eight, two, three and eight.

34
00:02:49,510 --> 00:02:53,410
Likewise, I will fill up all those non-zero elements.

35
00:02:54,440 --> 00:03:00,920
So here I have the three columns that there's one number, column number of each element and the element

36
00:03:00,920 --> 00:03:03,320
itself, not in this one.

37
00:03:03,800 --> 00:03:10,640
First I left it empty that I can use it for storing information about the spice market.

38
00:03:10,670 --> 00:03:13,720
So about the medicaments, what are the dimensions?

39
00:03:14,330 --> 00:03:19,350
Eight rows and nine columns, eight rolls, nine columns.

40
00:03:19,850 --> 00:03:21,540
Then how many elements are there?

41
00:03:21,560 --> 00:03:26,330
One, two, three, four, five, six, seven, eight, eight non-zero elements.

42
00:03:27,690 --> 00:03:32,780
So first of all, we can use it for a number of rules and then a number of columns and number of non-zero

43
00:03:32,790 --> 00:03:33,310
elements.

44
00:03:33,660 --> 00:03:36,790
So this is a commonly used method using this method.

45
00:03:36,810 --> 00:03:40,370
I will show you how to perform addition and subtraction of mitosis.

46
00:03:40,830 --> 00:03:46,080
But before that, let us look at the second method that is compressed Spar's.

47
00:03:47,730 --> 00:03:56,200
Now, second, Mutata compressed spiral and this method of spasmodically represented using three arrays.

48
00:03:56,730 --> 00:03:58,040
So the first three.

49
00:03:59,500 --> 00:04:12,430
As list of non-zero elements, I relied on non-zero elements, three, eight, 10, four to six, nine

50
00:04:12,700 --> 00:04:13,530
and five.

51
00:04:14,200 --> 00:04:16,300
So this is a list of non-zero elements.

52
00:04:16,300 --> 00:04:20,519
I have taken all those non-zero elements in the same order in which they are appearing.

53
00:04:21,220 --> 00:04:24,040
Don't change the first 10, first eight, then 10.

54
00:04:24,050 --> 00:04:25,630
Don't change the order 10 and eight.

55
00:04:27,000 --> 00:04:34,320
Next, we need an array for roles, for roles, so this is an important one.

56
00:04:34,350 --> 00:04:40,470
Just watch it carefully so we will have an array of roles in this.

57
00:04:40,650 --> 00:04:42,470
The very first thing is zero.

58
00:04:43,470 --> 00:04:47,310
Very first value is zero for initial value, zero hour.

59
00:04:47,320 --> 00:04:48,990
And this is all starting from one on one.

60
00:04:49,030 --> 00:04:53,640
So this is for zeroth index zero index then.

61
00:04:54,570 --> 00:04:58,300
Next infrastruture, how many elements are there?

62
00:04:58,590 --> 00:05:02,150
One element is that, first of all, this is for No.

63
00:05:03,430 --> 00:05:09,280
Then in second row, how many elements are there, second or two elements are there then plus one that

64
00:05:09,280 --> 00:05:11,380
will also civil, right, three here.

65
00:05:11,800 --> 00:05:17,030
So the tree is nothing but two elements and the next role plus one.

66
00:05:17,830 --> 00:05:22,410
So this will take the cumulative of number of elements.

67
00:05:22,780 --> 00:05:26,590
Initially, that is zero then for having one element.

68
00:05:26,800 --> 00:05:32,650
Then in the second row total three elements are that then in third row there are no elements.

69
00:05:32,650 --> 00:05:33,760
So it should be zero.

70
00:05:33,910 --> 00:05:35,740
But this previous number, we will take it.

71
00:05:35,920 --> 00:05:40,750
So this shows that three minus three, there are zero elements here, three minus one.

72
00:05:40,760 --> 00:05:41,860
There are two elements here.

73
00:05:42,920 --> 00:05:49,040
So we'll take the cumulative of the number of elements for each role, even I will write down the LDS

74
00:05:49,040 --> 00:05:50,600
for them, but this is four zero.

75
00:05:50,870 --> 00:05:51,640
There is no zero.

76
00:05:51,830 --> 00:05:53,360
This is first of all, there's a scandal.

77
00:05:53,360 --> 00:05:56,740
And this has to do then in the fourth role, there is one element.

78
00:05:56,750 --> 00:06:01,520
So I just for one more in this three plus one, that is four.

79
00:06:01,760 --> 00:06:04,610
So there's a fourth quarter then fifth through.

80
00:06:04,610 --> 00:06:08,170
There are new elements already before only then six through the night.

81
00:06:08,450 --> 00:06:09,470
There is one element.

82
00:06:09,480 --> 00:06:14,600
So this will be five, then seven or there is one element, then it will be six, eight.

83
00:06:14,840 --> 00:06:15,950
There are two elements.

84
00:06:16,100 --> 00:06:17,390
Then this will be eight.

85
00:06:18,650 --> 00:06:22,340
So this is for four through fifth, sixth, seventh and eight.

86
00:06:22,940 --> 00:06:29,600
So for every rule will contain the number of its own elements, plus the value in the previous rule.

87
00:06:30,350 --> 00:06:33,760
Then the third one is column for G values.

88
00:06:33,830 --> 00:06:36,560
So this is G a.

89
00:06:38,540 --> 00:06:42,890
Now, this element is present in each column, this is corresponding to the element.

90
00:06:42,920 --> 00:06:47,990
This is an eight column, then this is in third column.

91
00:06:48,000 --> 00:06:48,720
Third column.

92
00:06:49,220 --> 00:06:54,570
This, again, is in six to column six for every element will have a column number.

93
00:06:54,860 --> 00:06:57,080
This is in first column and two.

94
00:06:57,260 --> 00:07:01,840
This is in third column, then six as in fourth column.

95
00:07:02,810 --> 00:07:06,650
The nine is in the second column, then five is and fifth column.

96
00:07:08,210 --> 00:07:14,120
So as many elements are there, that many columns are there, but here as many rules are there plus

97
00:07:14,120 --> 00:07:16,340
one because start from zero onwards.

98
00:07:16,490 --> 00:07:20,800
So this is another matter that is compressed through.

99
00:07:21,440 --> 00:07:24,120
Let us analyze this little bit upon space.

100
00:07:25,010 --> 00:07:27,560
This is a sports matrix of eight, cross nine.

101
00:07:28,370 --> 00:07:36,260
Total 72 elements, including zeros that zeroes in the together, there are 72 elements.

102
00:07:37,660 --> 00:07:40,540
And if each element is an integer and is sticking to it.

103
00:07:40,580 --> 00:07:43,720
So this is 144 four bytes.

104
00:07:45,680 --> 00:07:48,350
So if we focus on the number of elements that sufficient.

105
00:07:49,260 --> 00:07:57,460
But here, how much space is required, 892 elements than this one, eight non-zero elements.

106
00:07:58,350 --> 00:07:59,740
Then what about this one?

107
00:08:00,120 --> 00:08:02,210
Total eight are there plus one.

108
00:08:02,220 --> 00:08:03,250
So there's a nine.

109
00:08:03,690 --> 00:08:05,630
So this depends on a number of rules.

110
00:08:05,640 --> 00:08:08,400
And this, too, depends on the number of non-zero elements.

111
00:08:08,880 --> 00:08:09,540
So total.

112
00:08:09,540 --> 00:08:10,910
How many elements are there?

113
00:08:12,540 --> 00:08:20,170
Twenty five elements are there that if all these are integers, then require into two 50 bytes of memory.

114
00:08:20,940 --> 00:08:23,500
So that is 140 for under six fifty.

115
00:08:23,970 --> 00:08:27,650
So almost 30 percent of that space.

116
00:08:27,990 --> 00:08:32,460
So 30 percent of memory is reduced if you are following this method.

117
00:08:33,090 --> 00:08:37,320
So similarly, in the coordinate list of the three column method, also the memory is saved.

118
00:08:37,740 --> 00:08:45,650
Now, I will take two small examples of my picks and show you how addition of spots from can be done.

