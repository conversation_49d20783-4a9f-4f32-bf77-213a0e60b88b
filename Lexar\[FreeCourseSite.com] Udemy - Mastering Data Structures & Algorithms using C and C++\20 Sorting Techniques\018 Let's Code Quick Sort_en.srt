1
00:00:00,640 --> 00:00:06,970
In this video, we will write a function for quicksort, so already we have seen the procedure, so

2
00:00:06,970 --> 00:00:09,160
we have written actually two functions for quicksort.

3
00:00:09,490 --> 00:00:12,910
One was partition and the other was recursive quicksort function.

4
00:00:13,450 --> 00:00:14,890
I've learned on both functions.

5
00:00:15,760 --> 00:00:18,700
So let us start writing a program for quicksort.

6
00:00:19,870 --> 00:00:21,640
See, already I have an array here.

7
00:00:21,650 --> 00:00:23,390
These are the Afghan elements.

8
00:00:23,390 --> 00:00:25,230
So what are you doing here?

9
00:00:25,240 --> 00:00:27,520
At the last I will add one more element.

10
00:00:27,520 --> 00:00:27,970
That is.

11
00:00:30,360 --> 00:00:33,220
Six, five, five, three, five.

12
00:00:33,600 --> 00:00:37,560
This is the largest positive integer or else I can take.

13
00:00:38,910 --> 00:00:45,490
And underscore, Max, that is maximum integer, so this will act as infinity.

14
00:00:45,610 --> 00:00:52,020
Now I have total 11 elements, including infinity, as in the procedure, we were using infinity as

15
00:00:52,020 --> 00:00:53,620
the last element in an array.

16
00:00:53,910 --> 00:00:59,490
So that will help us to show the end of an array that is a delimiter for the list.

17
00:01:01,530 --> 00:01:06,810
Now, let us ride on the function for quicksort, so the first function I will write on is partition,

18
00:01:07,140 --> 00:01:09,000
partition function returns integer.

19
00:01:10,610 --> 00:01:14,690
And it takes a lot of elements and.

20
00:01:15,800 --> 00:01:20,600
Law as well as height, that is the starting point of a list.

21
00:01:22,020 --> 00:01:28,220
Now, here, inside this, I need some variables like the reward for picking a first element that is

22
00:01:28,230 --> 00:01:35,790
pivotal element of law and I need I that starts from low and GS starts from high.

23
00:01:39,750 --> 00:01:44,130
Then using do I look, we have written the code, so and do I look?

24
00:01:45,610 --> 00:01:50,620
Fosterville incrementally in a do while loop, so I plus plus.

25
00:01:53,510 --> 00:01:54,980
And this is fine.

26
00:01:55,680 --> 00:02:02,150
See, I should move as long as the elements are smaller or equal means it should stop and element is

27
00:02:02,150 --> 00:02:02,810
greater.

28
00:02:05,350 --> 00:02:09,520
So I should write on the condition for continuation, not for strobing, so I should say.

29
00:02:10,880 --> 00:02:15,260
If it is less than or equal to even.

30
00:02:18,170 --> 00:02:19,770
So if it is less, it will move.

31
00:02:19,790 --> 00:02:22,730
It will not stand, if it is greater, it will stop.

32
00:02:23,000 --> 00:02:24,140
Yes, this is perfect.

33
00:02:24,530 --> 00:02:27,850
Then do G minus minus.

34
00:02:28,340 --> 00:02:30,590
And here I should say while.

35
00:02:31,880 --> 00:02:35,510
E of G is greater than ever.

36
00:02:36,080 --> 00:02:38,900
So if the limit is greater, Jaisha decrement.

37
00:02:39,990 --> 00:02:42,510
If it is a smaller or equal, it should stop.

38
00:02:42,540 --> 00:02:50,430
Yes, then after that we check if I is still less than if so, then we will swab the elements from each

39
00:02:50,460 --> 00:02:56,630
of i.e. that E of G at the suffering of G, then swabbing is done.

40
00:02:56,670 --> 00:02:59,580
Now I have to write the condition for why loop.

41
00:02:59,820 --> 00:03:00,420
So do I.

42
00:03:00,420 --> 00:03:06,660
Look, while I use less than G and I becomes greater than J should stop and when it has stopped and

43
00:03:06,660 --> 00:03:10,110
came out of the loop I should swap the elements that is.

44
00:03:11,530 --> 00:03:17,050
We all follow that spirit element that, aw, gee.

45
00:03:21,300 --> 00:03:24,070
This is the last stop, then it should be done.

46
00:03:26,880 --> 00:03:29,070
That's all this is a partisan function.

47
00:03:30,920 --> 00:03:34,820
As a partisan function, already, we have learned about this one.

48
00:03:35,180 --> 00:03:41,620
No, I should write on a quicksort function that is recursive function wide, quick, short.

49
00:03:42,320 --> 00:03:48,020
It should take array of elements and low and high.

50
00:03:50,390 --> 00:03:57,260
Then it should check that at least there are two elements, that is if lawyers less than high, then

51
00:03:57,650 --> 00:03:58,910
it should all partition.

52
00:04:01,250 --> 00:04:04,820
By sending uhry and low and high.

53
00:04:06,280 --> 00:04:11,570
Then whatever the result return, it should take in a variable G that is the partitioning position.

54
00:04:11,970 --> 00:04:14,670
So I should have one more local variable that is G.

55
00:04:15,330 --> 00:04:15,900
Yes.

56
00:04:17,089 --> 00:04:26,810
Then after calling partition, it should all quicksort once again frown upon an array from Lotu G.

57
00:04:31,170 --> 00:04:33,870
And whipsawed from.

58
00:04:35,710 --> 00:04:37,300
Plus, want to hide?

59
00:04:39,270 --> 00:04:41,160
So actually, it should be minus one.

60
00:04:42,180 --> 00:04:42,540
This is.

61
00:04:43,770 --> 00:04:45,660
So actually this should be minus one.

62
00:04:45,660 --> 00:04:51,720
But I told you that it position element, that is, which is already sorted across infinity for the

63
00:04:51,720 --> 00:04:52,290
first list.

64
00:04:52,290 --> 00:04:56,140
And this edge, this is having infinity on the right hand side.

65
00:04:57,420 --> 00:04:59,160
So that's all a quick thought program.

66
00:04:59,430 --> 00:05:07,890
Let us call quicksort up on this list and see that it sorts the elements with sort balzary and the law

67
00:05:07,890 --> 00:05:08,910
is a zero here.

68
00:05:08,910 --> 00:05:09,720
How is it?

69
00:05:09,810 --> 00:05:13,040
And that is a loss and extend to the 11 elements I'm having.

70
00:05:14,160 --> 00:05:15,480
Let us compile and run.

71
00:05:17,730 --> 00:05:18,170
Oops.

72
00:05:18,240 --> 00:05:21,330
This is in the 32 max.

73
00:05:21,360 --> 00:05:25,610
Yes, that is 32 bit integer in the 32 underscore, Max.

74
00:05:25,800 --> 00:05:26,630
This was the error.

75
00:05:26,640 --> 00:05:27,500
So I have removed that.

76
00:05:27,900 --> 00:05:32,430
Now you can see that the color also has changed the causes of predefined constant.

77
00:05:35,300 --> 00:05:42,830
Yes, the elements are sorted three five seven nine, 10, 11, 12, 13, 16, 24, seven assorted elements.

78
00:05:44,090 --> 00:05:50,690
Still, it is giving a warning because, OK, variable and is not you see, I'm not using it.

79
00:05:51,090 --> 00:05:54,770
You want you can use this one that is an A minus one that is 10.

80
00:05:55,790 --> 00:05:57,200
I should not give any warning.

81
00:05:57,380 --> 00:06:03,830
Yes, warning is gone, elements are sorted, so this is the function for quicksort.

82
00:06:04,250 --> 00:06:05,070
So you can try this.

83
00:06:05,100 --> 00:06:11,000
There's a simple program that looks like little difficulty to the students, but it's very easy compared

84
00:06:11,000 --> 00:06:16,580
to the other programs that is for trees and able to use the lendee programs we have seen.

85
00:06:18,150 --> 00:06:24,180
I have divided into two functions, that is partition and quicksort here, partition and nexxus quicksort,

86
00:06:24,180 --> 00:06:27,540
even you can combine them and divided as a single function also.

87
00:06:28,580 --> 00:06:29,390
So it's up to you.

88
00:06:31,540 --> 00:06:32,800
So that's all in this video.

