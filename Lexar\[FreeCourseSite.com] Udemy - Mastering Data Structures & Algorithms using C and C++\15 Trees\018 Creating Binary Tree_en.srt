1
00:00:00,210 --> 00:00:07,290
In this video, we will learn how to generate a binary tree using legal representation.

2
00:00:07,440 --> 00:00:12,870
We have seen two representation and linked representation, so I'll be showing it for legal representation.

3
00:00:13,020 --> 00:00:18,380
So for generating a binary, we can start generating <PERSON><PERSON><PERSON> routine or <PERSON><PERSON> can create LoopNet,

4
00:00:18,870 --> 00:00:20,990
then we can create left and right.

5
00:00:22,270 --> 00:00:27,540
But when you are creating a left and right child, we should know whether the left child and <PERSON>

6
00:00:27,580 --> 00:00:32,290
are dead or not like this, a full volunteer completed by entry in this.

7
00:00:32,590 --> 00:00:35,210
Every node is having both left and right, child also.

8
00:00:35,590 --> 00:00:40,080
But here, some nodes are not having left child, someone not having Rachel.

9
00:00:40,540 --> 00:00:43,840
So first of all, we should know whether a child is there or not.

10
00:00:44,020 --> 00:00:46,360
If it is there, then we can create an attachment.

11
00:00:48,520 --> 00:00:50,410
So this is the main thing that we have to take care.

12
00:00:51,330 --> 00:00:51,850
So we should.

13
00:00:52,530 --> 00:00:54,320
So this is the main thing that we have to take care.

14
00:00:54,370 --> 00:00:56,390
We have to check if the child noticed there.

15
00:00:56,420 --> 00:00:57,300
Then we will create it.

16
00:00:58,140 --> 00:01:03,330
Then one more thing, we will generate it level by level, so first of all, clear to all, then we

17
00:01:03,330 --> 00:01:07,970
will create the north of first second level, the third level and fourth level, so on.

18
00:01:08,100 --> 00:01:13,170
So that same procedure, we can use it for a full blown entry also that we can as a child is there or

19
00:01:13,170 --> 00:01:13,520
not.

20
00:01:13,890 --> 00:01:15,420
But here we need not ask.

21
00:01:15,570 --> 00:01:21,630
So I'll show you the procedure, how to generate this tree in the linked representation.

22
00:01:21,990 --> 00:01:27,570
So for generating a tree using Lingley representation, we need a Kuga structure.

23
00:01:28,860 --> 00:01:31,820
So I'm just drawing enough to show that it's a cube.

24
00:01:32,700 --> 00:01:38,020
I will take a cube so the school will have one point to point out all the mechanism, you know, already.

25
00:01:38,070 --> 00:01:42,570
So I don't have to explain ewart's mechanism, just revealing in certain the element from the cube.

26
00:01:43,110 --> 00:01:44,490
Now let us start the procedure.

27
00:01:44,640 --> 00:01:47,410
The very first step is create Robonaut.

28
00:01:47,790 --> 00:01:49,080
So create a node.

29
00:01:51,300 --> 00:01:56,880
And make a rude point on that note and insert the value that make this another.

30
00:01:57,950 --> 00:01:59,820
So I assume that this is the only note now.

31
00:01:59,840 --> 00:02:03,620
This is the first note and is the only supposed address of this notice to her.

32
00:02:03,630 --> 00:02:06,500
And so that address inserted in the Q.

33
00:02:08,020 --> 00:02:13,540
So we need a justice, we don't want value five, we want evidence of an incident here.

34
00:02:14,790 --> 00:02:17,440
So this is the initial work that we have to do.

35
00:02:17,880 --> 00:02:23,750
So once again, I repeat, take a cue, create note inside the address of root node in the kill.

36
00:02:25,190 --> 00:02:27,660
Now we are ready for a repeating procedure.

37
00:02:27,770 --> 00:02:29,840
So now the steps are repeating steps.

38
00:02:29,840 --> 00:02:32,620
So if you look at one step, one or two steps, you can understand.

39
00:02:33,740 --> 00:02:34,900
Let us see what we have to do.

40
00:02:36,720 --> 00:02:43,260
They got this address from the Q so turn it and make some temporary point-to-point on that soapies pointing

41
00:02:43,260 --> 00:02:48,100
that standing on that note, ask whether it is having left China.

42
00:02:48,630 --> 00:02:51,980
So, yes, as for the three that we want to create, yes, it is having a lift.

43
00:02:52,710 --> 00:02:53,750
Your left, China is there.

44
00:02:53,910 --> 00:02:59,280
So then create a new node with the help of temporary pointer and insert the value, whatever the value

45
00:02:59,280 --> 00:02:59,640
is.

46
00:03:00,000 --> 00:03:05,040
Then set these points as null then as this is having left China.

47
00:03:05,050 --> 00:03:07,110
So make this be left.

48
00:03:07,110 --> 00:03:14,850
The point on the Snork and suppose that this of this is to Ben then this new node inserted into Q.

49
00:03:16,780 --> 00:03:18,820
No, it's this incident.

50
00:03:20,140 --> 00:03:26,830
So if a child is there then in second nomics for the same number to be asked whether this more than

51
00:03:26,830 --> 00:03:29,180
five, is it having a child or not?

52
00:03:29,560 --> 00:03:31,290
Yes, it is having a child.

53
00:03:31,310 --> 00:03:33,780
So as per the tree that we want to create the resurrection.

54
00:03:34,060 --> 00:03:40,690
So, OK, take the temporary pointer and create a new node and insert that value.

55
00:03:40,690 --> 00:03:42,760
Six, make this as null.

56
00:03:43,060 --> 00:03:46,180
And the PS right child pointing to this one.

57
00:03:46,420 --> 00:03:51,490
Suppose that this of this note is two 30, then insert that it letters to 30 in the cube.

58
00:03:53,450 --> 00:03:56,570
That's all this has finished one step.

59
00:03:57,410 --> 00:03:59,970
So what was the step that are just repeated quickly?

60
00:04:00,350 --> 00:04:04,780
We took the pointer from the Q and make people in there and asked for the live chat, is they not?

61
00:04:04,790 --> 00:04:08,190
And we created it and inserted Setas rideshares.

62
00:04:08,190 --> 00:04:08,690
A little note.

63
00:04:08,690 --> 00:04:10,750
Yes, created and inserted psytrance.

64
00:04:11,120 --> 00:04:13,220
Same thing will be repeating again and again.

65
00:04:14,060 --> 00:04:15,620
So I'll be creating 93.

66
00:04:15,980 --> 00:04:20,149
But before going to the next step, just one thing I want to tell you here.

67
00:04:20,149 --> 00:04:23,900
We have to ask whether left is there or not.

68
00:04:24,140 --> 00:04:27,080
If it is there, then we create again.

69
00:04:27,080 --> 00:04:28,670
Ask Rachel is there or not?

70
00:04:28,860 --> 00:04:30,340
If yes, then create.

71
00:04:31,220 --> 00:04:37,640
So instead of asking, we can simply take the value of left child and we can put a marker that if the

72
00:04:37,640 --> 00:04:40,900
value entered this minus one, the child is not there.

73
00:04:41,980 --> 00:04:48,550
If it is other than minus one less positive than the others, then let us continue and create all the

74
00:04:48,560 --> 00:04:49,700
nonsense every day.

75
00:04:49,720 --> 00:04:56,500
And I will do this quickly because all of you have explaining next step, take out the next address

76
00:04:56,500 --> 00:04:59,840
from the queue that is to then and make the point that the point on that.

77
00:04:59,860 --> 00:05:04,960
So this is the note then being here asks what is the value of this site?

78
00:05:04,970 --> 00:05:07,180
Let's say it is minus one, so it is minus one.

79
00:05:07,180 --> 00:05:11,970
Don't create anything then big the value of rightside, the value is nine.

80
00:05:11,980 --> 00:05:14,980
So create a new node with the help of temporary pointer t.

81
00:05:16,990 --> 00:05:23,200
Then into the value make business BS right on this one, suppose that this of this modest 250 insert

82
00:05:23,200 --> 00:05:30,070
215 to the Q So we have finished with left and right now they called the exodus from the Q that is two

83
00:05:30,610 --> 00:05:33,250
thirty and make B point on that note.

84
00:05:34,680 --> 00:05:39,660
And on the left side of this side, value is three, so to create a new order with the help of ten point

85
00:05:39,660 --> 00:05:40,140
thirty.

86
00:05:41,860 --> 00:05:45,360
Insert the value, make this as Nundle piece left should point on this one.

87
00:05:46,060 --> 00:05:46,630
Start us off.

88
00:05:46,640 --> 00:05:50,380
This one is 300, then inserted into the Q.

89
00:05:53,160 --> 00:05:58,420
Then ask the right child, Rachel is minus one, so if the value is minus one, don't create anything.

90
00:05:58,740 --> 00:05:59,880
So we have finished with left.

91
00:06:00,330 --> 00:06:00,780
That's right.

92
00:06:01,740 --> 00:06:06,450
Then next step, take orders from the Cucu 15 and make point on that one.

93
00:06:06,480 --> 00:06:09,820
So this is 250 as the left value is there.

94
00:06:10,080 --> 00:06:10,980
So that is four.

95
00:06:11,220 --> 00:06:16,090
So create a new order with the help of a temporary pointer to insert the value.

96
00:06:16,110 --> 00:06:19,570
Make this as null and make these left point on that one.

97
00:06:19,890 --> 00:06:22,090
So all the rest of the small is 350.

98
00:06:22,440 --> 00:06:28,080
So insert three fifteen to the cube then as the right Rachel values.

99
00:06:28,080 --> 00:06:28,990
Derrius, it is two.

100
00:06:29,010 --> 00:06:30,160
So it is not minus one.

101
00:06:30,180 --> 00:06:35,310
So take the value and create a new node with the help of temporary pointer instead the value and make

102
00:06:35,310 --> 00:06:37,290
this a small piece right on this one.

103
00:06:37,860 --> 00:06:45,820
And suppose that addresses to seven 370, then insert data 370 into the cube and continue to take orders

104
00:06:45,840 --> 00:06:46,520
from the cube.

105
00:06:46,530 --> 00:06:47,700
That is three hundred.

106
00:06:48,120 --> 00:06:49,730
So make the point on that one.

107
00:06:49,980 --> 00:06:52,040
Also the left child it is minus one.

108
00:06:52,050 --> 00:06:56,460
Don't create the not for the right child, minus one don't create because there are no left children.

109
00:06:56,460 --> 00:07:02,100
Rachel for this one then take out the exodus from the cube that is three fifty and make point on that

110
00:07:02,100 --> 00:07:02,400
one.

111
00:07:02,730 --> 00:07:04,820
Also the left child is not there.

112
00:07:04,860 --> 00:07:08,790
As for the right child minus one on there, then move to the next.

113
00:07:08,910 --> 00:07:12,900
So take all that as 370 and make point on that note, 370.

114
00:07:15,150 --> 00:07:19,590
As for the left child, minus one is not there for the right child, minus one, it's not that.

115
00:07:21,080 --> 00:07:28,210
Then take orders from Cuba, there's nothing in the queue, Cuba came empty, so stop end of the procedure

116
00:07:28,370 --> 00:07:34,490
and we have generated a tree and heap and this is the road which is holding the entire tree.

117
00:07:35,610 --> 00:07:41,520
So we have learned the procedure, how to generate a tree with the help of to not actually have to write

118
00:07:41,520 --> 00:07:43,570
a function for generating a tree.

119
00:07:43,590 --> 00:07:47,960
So I will show you the program code for generating this tree with the help of Q.

