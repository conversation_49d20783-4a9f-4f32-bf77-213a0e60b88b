1
00:00:00,550 --> 00:00:07,120
And this video, I will show you how to implement law triangular matrix using Roman representation as

2
00:00:07,120 --> 00:00:09,100
well as imagery presentation.

3
00:00:10,020 --> 00:00:17,040
So I'm using the same project for my teachers already, I have defined a structure for a matrix, having

4
00:00:17,370 --> 00:00:20,610
a fixed size and in four dimensions.

5
00:00:22,470 --> 00:00:27,660
This is a structure we have already used in diagrammatic, so I am using the same one, so you should

6
00:00:27,660 --> 00:00:33,960
get the idea how we can modify the existing program to make it as a new program instead of writing the

7
00:00:33,960 --> 00:00:35,260
whole program from the beginning.

8
00:00:35,730 --> 00:00:36,990
I will modify this one.

9
00:00:38,120 --> 00:00:42,020
Now, this <PERSON> is going to be a Lord Wrangler, <PERSON><PERSON>, so we don't know the size.

10
00:00:42,050 --> 00:00:43,460
We cannot take Fixie.

11
00:00:43,470 --> 00:00:49,790
So I have to take a pointer and I will create a urry dynamically for a required size.

12
00:00:53,700 --> 00:00:59,430
So the first thing, let us create an attack inside the main function, I have created an object of

13
00:00:59,430 --> 00:01:05,000
my and then here I will take the dimension of a matics from Keibel.

14
00:01:05,040 --> 00:01:07,320
So let us give a message and ask.

15
00:01:08,490 --> 00:01:09,840
What is the dimensions?

16
00:01:14,260 --> 00:01:15,280
Another dimension.

17
00:01:17,360 --> 00:01:18,650
Then Kenneth.

18
00:01:20,970 --> 00:01:25,220
But isn't it the dimension in M dot and.

19
00:01:26,880 --> 00:01:32,820
CRM is a variable of type metrics and Emmas having variable and that is determined, but and that is

20
00:01:32,820 --> 00:01:33,720
for the dimension.

21
00:01:36,320 --> 00:01:38,660
And once we know the dimension, we should create an.

22
00:01:39,140 --> 00:01:43,370
So I'm not a is a pointer for an ugly head.

23
00:01:43,370 --> 00:01:45,050
I should look at the memory from him.

24
00:01:45,680 --> 00:01:48,090
For that I will call mellark function.

25
00:01:48,330 --> 00:01:51,860
Now, the question is, what is the size of the array that we need?

26
00:01:52,360 --> 00:01:57,880
We already know the number of non-zero elements present and Lord Wrangler Mattocks.

27
00:01:57,950 --> 00:02:03,880
We have already seen the formula that is an interval and plus one by two.

28
00:02:06,150 --> 00:02:10,919
These many number of dangerous elements and this should be multiplied that size of.

29
00:02:12,680 --> 00:02:13,340
Integer.

30
00:02:15,070 --> 00:02:19,690
This will create an array of required sites for storing data, elements that are in danger, and that's

31
00:02:19,720 --> 00:02:22,450
one way to know the area is ready.

32
00:02:23,060 --> 00:02:27,250
Now, we should have the function for storing an element and retrieving an element.

33
00:02:27,260 --> 00:02:28,920
So we should have the function for set.

34
00:02:28,930 --> 00:02:31,680
And so already I have the body of these functions.

35
00:02:31,690 --> 00:02:38,830
I will implement set function so that function will take a matrix and it will take and just entities

36
00:02:38,830 --> 00:02:40,900
and element to be inserted.

37
00:02:41,890 --> 00:02:44,890
So what is the element to be inserted and where it should be inserted?

38
00:02:49,040 --> 00:02:54,400
So Lord Wrangler will have elements only if I assume greater than I to chain.

39
00:02:54,440 --> 00:02:55,160
So if.

40
00:02:58,050 --> 00:03:02,640
I is greater than or equal to the only element as non-zero.

41
00:03:04,580 --> 00:03:10,910
So we will store the element in an array of at a location I into a minus one by two.

42
00:03:16,070 --> 00:03:22,670
Plus minus one is the formula we have seen there, we will store the element, so it's so simple.

43
00:03:23,210 --> 00:03:28,250
We have already seen in a diagonal matrix it was a minus one, but now it's four, five, minus one.

44
00:03:28,250 --> 00:03:29,450
The formula has changed.

45
00:03:29,750 --> 00:03:31,100
The rest of the things are seen.

46
00:03:32,240 --> 00:03:33,650
So that's Hollinshead function.

47
00:03:33,860 --> 00:03:35,560
Now let us implement that function.

48
00:03:36,080 --> 00:03:40,580
So in good function, it is taking parameters, matics and other indices.

49
00:03:40,580 --> 00:03:42,350
We want an element from an index icon.

50
00:03:43,400 --> 00:03:45,020
So here also the same condition.

51
00:03:45,020 --> 00:03:49,310
If I is greater than 2G, then only we have non-zero elements, so we will return.

52
00:03:53,470 --> 00:03:58,580
An element from location eye into a minus one by two plus minus one.

53
00:03:58,600 --> 00:03:59,530
Same location.

54
00:04:11,950 --> 00:04:19,260
Otherwise, we will return zero minutes, the element is zero if I get it, and then it is not relevant,

55
00:04:19,269 --> 00:04:21,160
otherwise it is a zero element.

56
00:04:22,670 --> 00:04:28,280
Now, this is a display functionality we have used for agonal mattocks, so here that I might hold for

57
00:04:28,280 --> 00:04:29,790
a loop starting from zero one works.

58
00:04:30,150 --> 00:04:36,050
Now let us make them from one on one so that we can properly use the formula without changing the formula.

59
00:04:36,620 --> 00:04:42,830
So I and Giblett are starting from one and ending at N now here non-zero element will be.

60
00:04:44,160 --> 00:04:49,620
If I get that apology and the element is at the location, I tend to.

61
00:04:50,670 --> 00:04:54,390
I minus one by two plus minus one.

62
00:04:55,560 --> 00:04:59,730
So this is the place for me to you can get the elements, otherwise the element is a little.

63
00:05:01,540 --> 00:05:07,090
So that's all know inside the main function, I will try to read all the elements of a matrix from the

64
00:05:07,090 --> 00:05:08,610
keyboard, then display them.

65
00:05:08,860 --> 00:05:13,170
So for reading all the elements of a matrix, I will say print F.

66
00:05:14,910 --> 00:05:17,130
Enter all elements.

67
00:05:23,690 --> 00:05:28,670
Then using follow by should read all the elements of a matrix of law that are required variables and

68
00:05:29,030 --> 00:05:30,490
for an input for loops.

69
00:05:32,360 --> 00:05:36,920
So Maddox has to be using goodness to follow up, so for I assign one.

70
00:05:37,940 --> 00:05:42,300
I is less than or equal to Martin and I plus.

71
00:05:44,510 --> 00:05:45,710
Then for.

72
00:05:47,920 --> 00:05:51,340
GSI, in one case, less than equal to.

73
00:05:52,280 --> 00:06:02,290
And end and joblessness is to follow the inside follow, I will read a value in a variable percentile.

74
00:06:03,440 --> 00:06:04,520
Person Tildy.

75
00:06:05,690 --> 00:06:09,950
I'm person X for reading, evalu also I need a variable, so I will declare the variable.

76
00:06:10,370 --> 00:06:21,140
Then I should set a value in a matrix at index icon Ikoma and the value to be considered is X, so I

77
00:06:21,140 --> 00:06:26,720
should set the value at that particular location in the Matrix using the set function.

78
00:06:28,130 --> 00:06:30,320
So here I will declare one more variable X.

79
00:06:32,040 --> 00:06:35,880
Then after reading all the values, I'll give a line gap.

80
00:06:40,660 --> 00:06:43,690
Then I will call display function by passing.

81
00:06:46,510 --> 00:06:47,460
Mattocks and.

82
00:06:48,420 --> 00:06:51,010
No, let's run the program here.

83
00:06:51,030 --> 00:06:55,030
Isn't that a member of an stip stuck mattocks is a pointer.

84
00:06:55,130 --> 00:07:00,440
Yeah, here we have taken the pointer but I'm using dot operator I should use appropriate to.

85
00:07:01,740 --> 00:07:06,620
I remember one more thing, as we are using my function, Astudillo should also be included.

86
00:07:09,260 --> 00:07:17,060
So I have changed to am not, and I wrote a letter from it now, yes, it's successful.

87
00:07:18,160 --> 00:07:21,490
Now here is asking for dimensions, so dimension.

88
00:07:23,970 --> 00:07:29,520
So the dimensions are four by four, so I'll just give for now under all the elements, I will enter

89
00:07:29,520 --> 00:07:30,210
the elements.

90
00:07:32,730 --> 00:07:37,710
Two zero zero zero is the first to admit that can see I'm giving zeros also.

91
00:07:39,190 --> 00:07:39,580
Then.

92
00:07:41,890 --> 00:07:44,050
One three zero zero.

93
00:07:46,170 --> 00:07:48,090
Then one, two, three.

94
00:07:49,560 --> 00:07:53,760
And little analysis, one, two, three, four.

95
00:07:53,820 --> 00:07:59,220
So I'm giving all the elements of a mattocks see, I got the cymatics.

96
00:08:00,530 --> 00:08:07,220
So how this is working is when I haven't heard two zero zero zero, so this follow up has executed for

97
00:08:07,220 --> 00:08:11,000
their entire role and it is and it has called four times.

98
00:08:12,530 --> 00:08:15,940
First it will read to then it will set it, then zero, then it will start.

99
00:08:15,950 --> 00:08:18,430
And again for the remaining two zeros it will set.

100
00:08:18,890 --> 00:08:20,530
So it will set all the values here.

101
00:08:20,540 --> 00:08:21,140
See this.

102
00:08:22,120 --> 00:08:30,130
All four values are set by this scarf and said function, and this Lupul repeated four four times the

103
00:08:30,130 --> 00:08:32,549
next row again, this loop will repeat four, four times.

104
00:08:33,280 --> 00:08:38,799
So if you check this set, the function said function will store the value only for this non zero if

105
00:08:38,799 --> 00:08:40,530
isolated incident and analytical store.

106
00:08:40,539 --> 00:08:41,919
Otherwise it will not store.

107
00:08:43,460 --> 00:08:48,830
So that's follows the implementation of lower triangular markets using the old media representation.

108
00:08:51,220 --> 00:08:55,040
I will quickly modify this and make it as call a media representation.

109
00:08:56,650 --> 00:09:03,670
See what other changes have to do, first of all, this is a mathematic structure, let it be seen,

110
00:09:04,820 --> 00:09:08,500
then the set and get and display function will come to them afterwards.

111
00:09:09,550 --> 00:09:11,020
Here, the matrix is created.

112
00:09:11,920 --> 00:09:14,710
Then enter the dimension and the mellark.

113
00:09:14,710 --> 00:09:16,070
What should be the side of rheumatics?

114
00:09:16,090 --> 00:09:16,930
It should be the same.

115
00:09:18,460 --> 00:09:18,850
Then.

116
00:09:20,090 --> 00:09:25,450
Then here's a poll I want to create a matrix and displayed it should be Samone and I will be calling

117
00:09:25,450 --> 00:09:29,530
said function so everything remains the same inside the main function.

118
00:09:29,560 --> 00:09:32,500
What all I want to do in the main function, something I'll be doing.

119
00:09:33,480 --> 00:09:41,700
Then going to the set function inside said function, this formula will change if you remember the formula

120
00:09:42,390 --> 00:09:44,640
for Holomisa representation.

121
00:09:46,120 --> 00:09:50,210
The formula was an input, so the system arrow and.

122
00:09:51,650 --> 00:09:52,310
INTU.

123
00:09:54,250 --> 00:09:57,880
Jay, minus one, yes, then plus.

124
00:10:00,770 --> 00:10:02,510
Jimenez, two and two.

125
00:10:07,260 --> 00:10:08,370
Jay, minus one.

126
00:10:09,800 --> 00:10:11,810
Bitel plus.

127
00:10:13,710 --> 00:10:20,030
I mean, this the was the formula, so already you have explained this formula, the same formula,

128
00:10:20,030 --> 00:10:28,050
I'm using it here for setting the values that I will copy this formula and forget.

129
00:10:28,050 --> 00:10:29,820
Also, I should use the same formula.

130
00:10:32,180 --> 00:10:34,970
Then in display also, I should follow the same formula.

131
00:10:36,680 --> 00:10:37,340
Yes.

132
00:10:38,360 --> 00:10:41,570
Now the elements will be stored Dolomites.

133
00:10:43,700 --> 00:10:48,800
Whatever the way they may be stored, the system will store the values on display, will display the

134
00:10:48,800 --> 00:10:49,100
value.

135
00:10:49,140 --> 00:10:55,670
See, if you look from the main function site, main function doesn't bother how the elements are stored

136
00:10:55,670 --> 00:11:01,310
and retrieve main function is giving all the elements and it want all the elements are displayed should

137
00:11:01,310 --> 00:11:02,360
display all elements.

138
00:11:02,390 --> 00:11:03,530
So who is storing them?

139
00:11:03,570 --> 00:11:05,860
Said who will display them display.

140
00:11:07,130 --> 00:11:10,190
So whatever the elements we have given, you should get back the same elements.

141
00:11:10,790 --> 00:11:11,960
Let us run the program.

142
00:11:13,390 --> 00:11:14,510
Oh, there's an error.

143
00:11:14,950 --> 00:11:22,030
See, here, it is called value and it's called value, so it is not so it's not at all a desktop inside

144
00:11:22,030 --> 00:11:24,780
the display function then here in that function.

145
00:11:24,790 --> 00:11:26,070
Also, it's not an arrow.

146
00:11:26,380 --> 00:11:28,660
It is actually with this one.

147
00:11:28,900 --> 00:11:32,890
See, this type of problem arises when you try to modify the existing program.

148
00:11:33,190 --> 00:11:36,970
Then you made me do some mistakes and you may get those errors.

149
00:11:37,210 --> 00:11:41,620
And sometimes these mistakes and these errors are very much time consuming.

150
00:11:41,620 --> 00:11:43,420
It take a lot of time for removing them.

151
00:11:45,640 --> 00:11:48,300
So it's better you retype the program from the beginning.

152
00:11:49,790 --> 00:11:54,710
But if you are able to convert it Error-free, then it will save a lot of time.

153
00:11:56,040 --> 00:11:57,690
Let us run the program once again.

154
00:11:59,710 --> 00:12:01,040
Yes, it's running.

155
00:12:01,120 --> 00:12:05,610
There were two errors, I have removed them, not the dimensions I want to give them since I was five.

156
00:12:05,920 --> 00:12:07,840
I'll give the same elements one.

157
00:12:10,190 --> 00:12:13,100
Four zeros, then one to.

158
00:12:16,110 --> 00:12:23,760
Three zeros, then one, two, three, two zeros, and one, two, three, four, then one more zero

159
00:12:23,760 --> 00:12:26,520
than one, two, three, four, five.

160
00:12:28,480 --> 00:12:34,990
Yes, I got all the elements, yes, they are stored properly by using this column formula.

161
00:12:37,770 --> 00:12:45,050
So that's it for a Wrangler matics, we have seen the implementations, so Romijn as well as Majendie

162
00:12:45,060 --> 00:12:46,140
presentations before.

