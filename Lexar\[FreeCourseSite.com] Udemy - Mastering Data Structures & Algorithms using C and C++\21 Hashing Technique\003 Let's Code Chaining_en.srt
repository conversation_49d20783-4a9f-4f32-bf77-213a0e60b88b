1
00:00:00,410 --> 00:00:08,750
And this video we will implement harshing, using, chinning, so I will start a new project and I will

2
00:00:08,750 --> 00:00:10,070
call it as chinning.

3
00:00:11,000 --> 00:00:12,860
And it is using C language program.

4
00:00:15,080 --> 00:00:16,560
Create this project.

5
00:00:17,570 --> 00:00:24,380
For implementing chinning, we require Lincolnesque, so we have already written programs for list,

6
00:00:24,710 --> 00:00:30,830
so I will copy the required code for implementing link lists like insert and delete and search.

7
00:00:32,610 --> 00:00:35,940
So I tried a new header file and this header file.

8
00:00:37,040 --> 00:00:45,670
I will call it as chains, chains, not inside the or I will copy the code for linguist.

9
00:00:45,680 --> 00:00:48,730
So here I will pass the code for linguist.

10
00:00:50,130 --> 00:00:56,990
There's an old structure of the data and the next pointer and insert this is sorted insert because as

11
00:00:56,990 --> 00:01:01,810
I told you, that we can insert the elements in the sorted order so that it's easy for insertion searching.

12
00:01:01,820 --> 00:01:02,360
But was.

13
00:01:04,220 --> 00:01:09,980
So this is sorted and searched and then this is search searching for a key element.

14
00:01:11,490 --> 00:01:16,370
Now, the sodded inside will take a pointed to a point that so we will see this one now.

15
00:01:17,470 --> 00:01:23,020
It let's come back to the main functions and the main function, I should have a hash table, so hash

16
00:01:23,020 --> 00:01:32,920
table structure, node array of point notes, so let us the exercise as so I have an unstable assistant.

17
00:01:34,540 --> 00:01:39,850
This hash table should be initialized, so for that, I will use a follow up and initialize.

18
00:01:39,910 --> 00:01:43,510
This one is less than 10 I plus plus.

19
00:01:45,200 --> 00:01:46,670
Hash table of aying.

20
00:01:47,760 --> 00:01:50,280
Is equal to none in the same.

21
00:01:52,070 --> 00:01:53,250
Now, how stable is really.

22
00:01:54,910 --> 00:02:00,910
No, we should have a function for inserting in a hash table, so let us write a function that is insert

23
00:02:00,910 --> 00:02:01,780
in the hash table.

24
00:02:02,620 --> 00:02:08,740
So for inserting a hash table, it should take a parameter that is hash table.

25
00:02:09,130 --> 00:02:18,790
So the same thing struck Gold Star and let us call it as an array of pointers and the key to be inserted.

26
00:02:21,280 --> 00:02:28,450
No inside function need has poured so far that I will write a function called hash, which takes a key,

27
00:02:28,870 --> 00:02:36,220
and here it returns key mod size sizes 10 that glamorising size.

28
00:02:36,550 --> 00:02:40,210
And this time I have not taken a symbolic constant.

29
00:02:42,220 --> 00:02:48,520
Now, this insert function should, in a certain element are given index, so I should give the index

30
00:02:48,940 --> 00:02:52,540
from hash function by sending a key.

31
00:02:54,990 --> 00:02:56,060
Now, inside of.

32
00:02:57,730 --> 00:03:05,740
Now, inside an of a change that is that a given index I should perform inside, so I will call a function

33
00:03:05,740 --> 00:03:08,770
that is sorted insert.

34
00:03:11,440 --> 00:03:18,270
And I should send the address of this location that is address and the key that I want to insert key

35
00:03:18,340 --> 00:03:19,450
that I want to insert.

36
00:03:20,900 --> 00:03:26,510
Next on this should in certain element that I give an index sodded index function is already dead in

37
00:03:26,510 --> 00:03:31,820
a Linklaters now here instead the main function I will perform insert.

38
00:03:33,410 --> 00:03:34,970
I will send a hash table.

39
00:03:37,140 --> 00:03:37,950
He is.

40
00:03:40,590 --> 00:03:41,010
Ms.

41
00:03:42,400 --> 00:03:48,700
Then I will insert one more element with the same index, so let us see whether it will insert in that

42
00:03:48,700 --> 00:03:50,760
same chain or not.

43
00:03:51,740 --> 00:03:56,600
Insert hash table and the value is to.

44
00:03:59,190 --> 00:04:05,940
And I'll put a break point here and then run it, if it has executed, let us check hash table.

45
00:04:07,770 --> 00:04:10,860
This is second index, yes, it is having value.

46
00:04:10,990 --> 00:04:18,089
Well, the next the value study to next the value is OK, but values are inserted.

47
00:04:20,040 --> 00:04:27,570
OK, 42 is not the answer, because I have stopped at 42, if I continue execution, then 42 will also

48
00:04:27,570 --> 00:04:30,030
be inserted 12, 22 and 42.

49
00:04:30,060 --> 00:04:32,970
If you look into this debug area, you can find it.

50
00:04:34,470 --> 00:04:40,530
So this is about insert now let us perform search also and confirm it.

51
00:04:42,470 --> 00:04:49,350
I'll call a function search, so for that, I need a temporary pointer for a. because it returns up

52
00:04:49,370 --> 00:04:54,230
node structure struck node star.

53
00:04:54,710 --> 00:04:57,380
I'll take a variable like pointer amp.

54
00:04:58,980 --> 00:05:01,710
Sort of St. Paul function Serj.

55
00:05:03,830 --> 00:05:04,670
By passing.

56
00:05:05,670 --> 00:05:13,890
Hash table of hash, function of key, whatever the value written by that function, we will send it.

57
00:05:17,660 --> 00:05:20,120
That index and also the key.

58
00:05:21,900 --> 00:05:23,880
So now here I will print.

59
00:05:25,550 --> 00:05:32,150
Percentile the and the value is them gabs data.

60
00:05:34,360 --> 00:05:38,140
So here, key value that I will give it as 22.

61
00:05:40,870 --> 00:05:41,970
Letter from Nancy.

62
00:05:43,440 --> 00:05:46,380
Yeah, this also 22, this is Anita.

63
00:05:47,410 --> 00:05:55,020
So I'll send the hash table index of 22 and the key that I want to search in that particular place.

64
00:05:58,130 --> 00:05:59,370
Yes, it is fun.

65
00:06:01,350 --> 00:06:06,540
If I give any other key like, say, twenty one, which is not present, then I should get an error

66
00:06:06,540 --> 00:06:09,720
here because I'm trying to access the data there.

67
00:06:10,230 --> 00:06:11,900
There is no such Lord.

68
00:06:12,850 --> 00:06:13,960
So I should get another.

69
00:06:15,440 --> 00:06:21,140
Yes, I got a random error because 21 is not present, so it will go to the next one and there it is

70
00:06:21,150 --> 00:06:21,500
null.

71
00:06:21,800 --> 00:06:24,310
So none of the data is giving an error.

72
00:06:27,780 --> 00:06:35,060
So that's it, I have implemented only insert and search, so if you want, you can implement delete.

73
00:06:35,070 --> 00:06:38,640
Also, this was just a demonstration program for hashing.

74
00:06:38,940 --> 00:06:41,610
Now the applications of harshing are very vast.

75
00:06:41,960 --> 00:06:46,050
They are used at many places to reduce the time taken for searching.

76
00:06:50,140 --> 00:06:51,370
So that's all in this video.

