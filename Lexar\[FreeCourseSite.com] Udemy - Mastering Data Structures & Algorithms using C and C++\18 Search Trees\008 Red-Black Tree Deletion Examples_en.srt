1
00:00:00,360 --> 00:00:02,580
Now, let us look at the from Red Blakley.

2
00:00:02,610 --> 00:00:08,550
I have an example, the BlackBerry, let us delete the normal, so I'll take some examples and I will

3
00:00:08,550 --> 00:00:14,240
delete and if required, I will perform locations or recovering whatever the case as we will see that

4
00:00:14,250 --> 00:00:14,380
not.

5
00:00:14,430 --> 00:00:20,120
First of all, let us check the black height from the black one, too.

6
00:00:20,760 --> 00:00:22,960
And this man will also black.

7
00:00:22,980 --> 00:00:23,440
So three.

8
00:00:23,730 --> 00:00:25,470
OK, so one, two, three.

9
00:00:25,680 --> 00:00:27,180
So in either direction it is three.

10
00:00:27,570 --> 00:00:28,440
One, two.

11
00:00:28,470 --> 00:00:29,090
I'm here.

12
00:00:29,220 --> 00:00:30,260
Nulls three.

13
00:00:30,720 --> 00:00:32,290
One, two, three.

14
00:00:32,310 --> 00:00:34,320
One, two, three.

15
00:00:34,740 --> 00:00:38,330
So number of black sites seems perfectly balanced.

16
00:00:38,730 --> 00:00:39,120
All right.

17
00:00:39,160 --> 00:00:41,480
BlackBerry eight, the north side and black.

18
00:00:42,420 --> 00:00:45,110
Now, let us really denote first value.

19
00:00:45,130 --> 00:00:50,580
I want to delete as 90 search for 90, start from here 70.

20
00:00:51,510 --> 00:00:52,890
And that is greater than this.

21
00:00:53,070 --> 00:00:55,230
One hundred ninety is less than this.

22
00:00:55,530 --> 00:00:58,950
Nine days later than this fund itself.

23
00:00:58,950 --> 00:01:03,630
Not extraverted, not deleted if this is deleted.

24
00:01:04,200 --> 00:01:06,060
Let us check Blacklight in this direction.

25
00:01:06,080 --> 00:01:12,420
If this is gone, then count the number of blacks one to Arnett's another children three six three.

26
00:01:13,170 --> 00:01:15,150
So there's no effect to the black site.

27
00:01:15,150 --> 00:01:17,280
So just delete reticular.

28
00:01:17,580 --> 00:01:19,350
Guess what, I'm not deleted.

29
00:01:19,350 --> 00:01:21,340
I kept it as it is not a.

30
00:01:21,690 --> 00:01:22,470
Another example.

31
00:01:22,770 --> 00:01:24,540
I want to delete Hendrickx.

32
00:01:25,290 --> 00:01:26,420
So search for hundred.

33
00:01:27,150 --> 00:01:30,600
So it is better than this one is found then.

34
00:01:30,960 --> 00:01:32,280
It is having children.

35
00:01:32,550 --> 00:01:33,810
So many are deleting an order.

36
00:01:33,810 --> 00:01:34,500
It's not a leaf.

37
00:01:34,500 --> 00:01:34,800
No.

38
00:01:34,800 --> 00:01:40,590
Then find out in order predecessor or in order successor and replace that one.

39
00:01:40,920 --> 00:01:46,260
So in order for this as a whole to find go to the left site and extreme right.

40
00:01:46,440 --> 00:01:51,230
So 90 we have or else go to right extreme left.

41
00:01:51,630 --> 00:01:52,920
So there is no extreme left.

42
00:01:52,930 --> 00:01:54,030
So it is one done.

43
00:01:54,390 --> 00:01:57,870
So either ninety or 110 can take its place.

44
00:01:57,870 --> 00:02:00,120
Now it's your choice, whichever one you want.

45
00:02:00,540 --> 00:02:05,370
So in programming, if you want to do, you can do it always on the left side are based on the height

46
00:02:05,370 --> 00:02:09,330
you can do that are different criteria you can adopt so you can select anyone.

47
00:02:09,750 --> 00:02:13,440
So let us send ninety at that place.

48
00:02:13,440 --> 00:02:15,800
So this becomes ninety.

49
00:02:16,560 --> 00:02:17,290
Not Richemont.

50
00:02:17,310 --> 00:02:18,150
I have to delete.

51
00:02:18,300 --> 00:02:22,660
Not that one doesn't want to have to delete because this is replaced there.

52
00:02:22,860 --> 00:02:24,510
So this no one has to be deleted.

53
00:02:24,510 --> 00:02:25,290
What is the color.

54
00:02:25,350 --> 00:02:27,380
Red color deleted.

55
00:02:29,430 --> 00:02:30,000
That's all.

56
00:02:30,180 --> 00:02:34,880
It will not affect the black high ticket one two and black No.

57
00:02:35,310 --> 00:02:37,080
Three is perfect.

58
00:02:38,160 --> 00:02:45,890
Now, let us take one more example, I want to delete one thing, which is one, it is a great educator.

59
00:02:46,050 --> 00:02:53,460
Yes, one can found then if I delete this one, then who will take its place in order for this is what

60
00:02:53,460 --> 00:02:55,260
is there in order successor?

61
00:02:55,290 --> 00:02:55,650
Yes.

62
00:02:55,650 --> 00:02:58,070
One point is that bring it in this place.

63
00:02:58,080 --> 00:03:01,830
So one comes here now which one I have to delete 120.

64
00:03:02,250 --> 00:03:03,090
What is the color.

65
00:03:03,180 --> 00:03:03,960
Red color.

66
00:03:04,110 --> 00:03:04,860
The deleted.

67
00:03:06,600 --> 00:03:07,990
No effective blackout.

68
00:03:08,070 --> 00:03:11,030
One to nulls that is three.

69
00:03:11,970 --> 00:03:15,960
So this all about red colors have shown you so always.

70
00:03:15,960 --> 00:03:19,930
I was reading the note that was red color not getting deleted so simply deleted.

71
00:03:21,060 --> 00:03:23,880
Now let us take some blackmond.

72
00:03:23,940 --> 00:03:25,880
See what happens if we delete black.

73
00:03:25,910 --> 00:03:26,830
No, no.

74
00:03:26,940 --> 00:03:33,140
You want to delete eighty search for eighty eight is greater than seventy eight is less than 90.

75
00:03:33,180 --> 00:03:34,650
Yes this is it.

76
00:03:35,610 --> 00:03:36,660
Delete this.

77
00:03:37,020 --> 00:03:38,180
Who will take its place.

78
00:03:38,190 --> 00:03:39,900
Nobody is there to take its place.

79
00:03:40,230 --> 00:03:42,420
Which color in order to black color more.

80
00:03:42,690 --> 00:03:45,870
So if I delete this then there is nothing.

81
00:03:46,050 --> 00:03:51,000
So this becomes null and this novel becomes double black.

82
00:03:51,280 --> 00:03:55,840
That's for when you delete a black color known it becomes its null value.

83
00:03:55,860 --> 00:03:58,140
Definitely do not have any child and all will be there.

84
00:03:58,470 --> 00:04:00,470
And bacchanals double black.

85
00:04:00,900 --> 00:04:03,300
No, we have to adjust this double blackness.

86
00:04:03,810 --> 00:04:08,850
So I said that you have to check for the sibling who is a sibling for this one, wondering what is the

87
00:04:08,850 --> 00:04:09,250
color.

88
00:04:09,450 --> 00:04:10,520
It is also black.

89
00:04:10,710 --> 00:04:14,160
So if he is also black means check its children.

90
00:04:14,640 --> 00:04:18,839
If they are red, perform rotation, if they are black, change the color.

91
00:04:19,500 --> 00:04:20,709
It doesn't have any children.

92
00:04:20,709 --> 00:04:21,050
No, no.

93
00:04:21,060 --> 00:04:23,130
It is having null values.

94
00:04:23,460 --> 00:04:24,030
NULL values.

95
00:04:24,030 --> 00:04:25,200
What is the color of nulls?

96
00:04:25,740 --> 00:04:30,360
Black sibling is black and then the children are also black.

97
00:04:30,370 --> 00:04:33,690
So in this case we recall that only in this case really color.

98
00:04:33,990 --> 00:04:42,090
So how do recolour convert this one bendy to red color and the Spirent which was already that make it

99
00:04:42,090 --> 00:04:44,180
as black nitzan.

100
00:04:45,420 --> 00:04:48,110
So this blackness is absorb here.

101
00:04:48,420 --> 00:04:50,720
Now count the number of blacks in this direction.

102
00:04:50,730 --> 00:04:55,140
One, two, three, one, two, three or one, two, three.

103
00:04:55,170 --> 00:04:56,580
Yes, three n are there.

104
00:04:57,000 --> 00:05:02,550
So this extra blackness is taken away by this disenrolled and that became red.

105
00:05:03,390 --> 00:05:06,150
This is what we already have shown you in the cases.

106
00:05:06,840 --> 00:05:13,020
If siblings of black children are black recolour, the restriction of the restriction.

107
00:05:13,020 --> 00:05:18,060
Traditionally that case we will do not let us delete one more value.

108
00:05:18,990 --> 00:05:20,670
I want to delete 120.

109
00:05:20,850 --> 00:05:22,380
OK, delete this one Banty.

110
00:05:22,440 --> 00:05:23,190
No problem.

111
00:05:24,690 --> 00:05:26,250
It's already deleted.

112
00:05:26,670 --> 00:05:28,500
Check the black one too.

113
00:05:28,830 --> 00:05:30,030
And this is not right.

114
00:05:30,030 --> 00:05:30,480
No.

115
00:05:30,900 --> 00:05:33,780
Three, one, two, three.

116
00:05:33,810 --> 00:05:35,550
One, two, three.

117
00:05:35,970 --> 00:05:37,680
So it's perfect Blakley.

118
00:05:38,220 --> 00:05:39,240
Now let us delete.

119
00:05:39,240 --> 00:05:42,090
Ninety search for ninety nine is here.

120
00:05:42,330 --> 00:05:42,870
Yes.

121
00:05:43,320 --> 00:05:47,190
And if we delete ninety there is nobody to take its place because it's a leaf node.

122
00:05:47,460 --> 00:05:52,980
So directly delete this one so no node will take its place or null will come here as it was a leaf.

123
00:05:52,980 --> 00:05:53,250
No.

124
00:05:53,610 --> 00:05:57,450
And it becomes double black because we have deleted Blackmond.

125
00:05:58,500 --> 00:06:02,890
Not if a black note is deleted, then you have to check the sibling.

126
00:06:02,950 --> 00:06:06,270
Yes, this is the sibling and this is between and siblings.

127
00:06:06,630 --> 00:06:13,020
And if children are black, so if anyone thinks sibling, its children, if anyone is rare, we have

128
00:06:13,020 --> 00:06:14,050
to perform rotation.

129
00:06:14,670 --> 00:06:17,490
Yes, we have to perform rotation as this is red.

130
00:06:17,910 --> 00:06:20,580
Now, see, here is a special case.

131
00:06:20,580 --> 00:06:25,950
We will now perform rotation around this be right because this is red.

132
00:06:26,430 --> 00:06:27,510
So perform rotation.

133
00:06:27,520 --> 00:06:29,460
So if you perform rotation, what happens?

134
00:06:29,700 --> 00:06:32,760
70 comes down here, 70 comes down.

135
00:06:33,090 --> 00:06:33,480
Right.

136
00:06:33,870 --> 00:06:35,260
And 40 will move up.

137
00:06:35,580 --> 00:06:37,450
So 40 will move up.

138
00:06:37,860 --> 00:06:38,650
This was red.

139
00:06:38,970 --> 00:06:41,340
So this is 40 will become red.

140
00:06:41,670 --> 00:06:43,640
Then 50 should go that side.

141
00:06:43,890 --> 00:06:45,720
So I will drop 50 here.

142
00:06:46,110 --> 00:06:47,250
And this is 50.

143
00:06:47,250 --> 00:06:50,430
This child, which was black, should become confirmed.

144
00:06:51,270 --> 00:06:54,780
And this is 60 is already red.

145
00:06:55,140 --> 00:06:57,690
OK, so this is 50 and 60.

146
00:06:58,200 --> 00:07:00,240
OK, let me ask this first.

147
00:07:00,240 --> 00:07:01,170
Let us move up.

148
00:07:01,170 --> 00:07:02,460
So now 20 will move up.

149
00:07:02,460 --> 00:07:03,870
These have gone directly down.

150
00:07:04,200 --> 00:07:06,710
This have gone did not even move up.

151
00:07:06,720 --> 00:07:07,820
So this is 20.

152
00:07:08,160 --> 00:07:09,440
And what about his children?

153
00:07:09,450 --> 00:07:11,840
They are ready so they will move up along with 20.

154
00:07:12,150 --> 00:07:18,540
So in comes here and 30 comes here and they are still red in color.

155
00:07:19,110 --> 00:07:22,260
Now this one change is see, this note was 40.

156
00:07:22,260 --> 00:07:23,130
Was there a trend?

157
00:07:23,140 --> 00:07:23,600
That's right.

158
00:07:23,820 --> 00:07:26,550
And these are black, black, white, gazidis.

159
00:07:27,510 --> 00:07:32,700
But the child of the side, when it goes there and becomes red, this is what the special thing that

160
00:07:32,700 --> 00:07:36,540
we have to take care of right now before dealing with this.

161
00:07:36,540 --> 00:07:39,870
First of all, check Route 47.

162
00:07:40,110 --> 00:07:43,200
If there are more laws above this 40, then it's OK.

163
00:07:43,200 --> 00:07:48,240
But as a result of a we so we know that we have to change its color code black.

164
00:07:48,580 --> 00:07:49,620
It must be black.

165
00:07:49,740 --> 00:07:52,540
So this is black in color.

166
00:07:52,890 --> 00:07:58,980
OK, first thing we do know in this case, when you have performed rotation, so there may be further

167
00:07:58,980 --> 00:07:59,700
violations.

168
00:07:59,700 --> 00:08:01,390
So you have to check that one also.

169
00:08:01,410 --> 00:08:02,730
That's what we are learning now.

170
00:08:03,090 --> 00:08:07,620
So once this is added here, so this red the red conflict is there.

171
00:08:07,920 --> 00:08:09,600
So 100 conflict is there.

172
00:08:09,600 --> 00:08:11,250
Again, we have to perform rotation.

173
00:08:11,250 --> 00:08:12,790
So which option it will take.

174
00:08:13,170 --> 00:08:16,010
This is the exact that is an art rotation.

175
00:08:16,020 --> 00:08:17,940
So what happens in an observation?

176
00:08:18,210 --> 00:08:20,770
We can directly switch this 60 here.

177
00:08:21,180 --> 00:08:24,410
So this is 60 and 70 will come on this side.

178
00:08:24,780 --> 00:08:25,950
This is right in color.

179
00:08:26,160 --> 00:08:28,540
So this is 70 will come on this side.

180
00:08:28,890 --> 00:08:30,640
OK, so this is gone.

181
00:08:31,290 --> 00:08:33,520
So 60 to 70 come here.

182
00:08:33,870 --> 00:08:41,100
So when you are performing the rotation with the color sibling, then you may have to perform rotations

183
00:08:41,100 --> 00:08:42,130
more than one time.

184
00:08:42,570 --> 00:08:49,050
So even if you remember in Aviel, Trees for Adjustment, we have to perform rotations multiple times.

185
00:08:49,060 --> 00:08:52,870
So that's what here we have to perform rotation multiple times.

186
00:08:53,250 --> 00:08:56,030
So you have to take care of this if there is any further rotation.

187
00:08:56,430 --> 00:08:59,970
So the child which was black, which went on that side, it became black.

188
00:09:00,120 --> 00:09:01,300
That's what you have to pick.

189
00:09:02,010 --> 00:09:03,700
So that's all this is sufficient.

190
00:09:03,720 --> 00:09:06,710
I have shown you all possible cases by taking an example.

191
00:09:07,080 --> 00:09:14,130
So I suggest you you practice this one by yourself once, then it will be easy for you to remember and

192
00:09:14,130 --> 00:09:17,760
also to program it unless you do it once by yourself.

193
00:09:17,770 --> 00:09:19,350
You cannot recall these things.

194
00:09:19,350 --> 00:09:21,160
You cannot remember these things easily.

195
00:09:21,450 --> 00:09:22,770
So practice them once.

196
00:09:22,770 --> 00:09:24,190
You will remember of this.

197
00:09:24,600 --> 00:09:28,020
So that's all about deletion from the black.

