1
00:00:00,180 --> 00:00:07,290
The topic is a gender binary search tree from distance, similar topic we have studied in the binary

2
00:00:07,290 --> 00:00:12,750
trees, just binary trees, but these are binary searches and in binary trees.

3
00:00:12,750 --> 00:00:15,780
We studied that for generating a binary tree.

4
00:00:16,110 --> 00:00:18,780
We need any to travel souls.

5
00:00:19,020 --> 00:00:26,610
And among that, one of the traversal must be in order to preorder plus in order or both Sharda plus

6
00:00:26,610 --> 00:00:27,140
in order.

7
00:00:27,480 --> 00:00:33,200
Any one of these combinations must be given, then only we can generate a binary tree.

8
00:00:34,200 --> 00:00:38,600
Now coming back to binary searching for generating a binary source tree.

9
00:00:38,640 --> 00:00:45,420
If we are having preordering in in order or post order and in order, then we can generate by the results

10
00:00:45,420 --> 00:00:48,470
to also by following that same old procedure.

11
00:00:49,020 --> 00:00:52,680
But in the example I have only preorder.

12
00:00:53,220 --> 00:00:58,650
What about in order so I don't have to worry in order traversal of binary.

13
00:00:58,650 --> 00:01:00,410
So she gets a sorted order.

14
00:01:00,420 --> 00:01:06,770
This is the property of binary search team so I can take the sorted order of these elements like ten,

15
00:01:06,780 --> 00:01:10,950
fifteen, twenty, twenty five, thirty, forty, forty five and fifty.

16
00:01:11,220 --> 00:01:17,340
If I take the sorted order then I'll be having pre order and in order then I can follow that old procedure.

17
00:01:19,200 --> 00:01:22,980
Yes, so how many traversal should be given to us?

18
00:01:23,390 --> 00:01:29,240
Only one driver self-sufficient, that is either pre order or post order in order.

19
00:01:29,250 --> 00:01:30,150
I can find out.

20
00:01:30,940 --> 00:01:39,010
But without in order also binary, so she can be generated just using preorder or just using post.

21
00:01:39,640 --> 00:01:44,990
So we don't need in order without that also binary so she can be generated.

22
00:01:45,400 --> 00:01:52,330
So just using preorder, we can generate binary search to let us see how we can generate a binary search

23
00:01:52,330 --> 00:01:57,410
tree just using preorder preorders let us created by necessity for this procedure.

24
00:01:57,700 --> 00:02:00,030
I need a stack, so I take a stack.

25
00:02:00,130 --> 00:02:00,910
Let us stop.

26
00:02:02,150 --> 00:02:06,090
Scan through this preorder by taking one element at a time.

27
00:02:06,110 --> 00:02:09,660
So right now we are at the first element that is pre of zero.

28
00:02:10,550 --> 00:02:13,550
So for the very first key, create a..

29
00:02:15,510 --> 00:02:22,620
Created and make a good point on it and also take some temporary pointers pointing on it.

30
00:02:22,650 --> 00:02:25,230
This is the first node, this is the very first norm.

31
00:02:25,240 --> 00:02:28,290
So for initial part, we have to do it separately.

32
00:02:28,290 --> 00:02:28,510
Right?

33
00:02:28,530 --> 00:02:30,460
Because a road should point on that.

34
00:02:31,110 --> 00:02:32,490
So this is the first step.

35
00:02:32,760 --> 00:02:34,810
Initial step, not a repeating step.

36
00:02:35,280 --> 00:02:36,120
Initial step.

37
00:02:36,870 --> 00:02:43,920
Not repeating step starts, first notice created a binary search, trees ready just with one key, no

38
00:02:43,920 --> 00:02:44,740
move to next.

39
00:02:45,810 --> 00:02:48,870
This element is smaller than 30.

40
00:02:49,110 --> 00:02:50,750
Yes, smaller than 30.

41
00:02:51,000 --> 00:02:56,220
So create a new node with some temporary pointer and.

42
00:02:57,720 --> 00:03:03,690
Set to that evalu make this has no piece left the point on this one because there's a smaller.

43
00:03:04,560 --> 00:03:12,480
And Bush, the address of 13 to the address of 13 to the stack and move be underscored.

44
00:03:12,720 --> 00:03:15,760
So now he's pointing on not the next.

45
00:03:15,810 --> 00:03:17,350
This is repeating steps, right?

46
00:03:17,430 --> 00:03:20,470
Remember, this is repeating step four, one step forward.

47
00:03:20,670 --> 00:03:22,860
It was also inserted on this site.

48
00:03:23,130 --> 00:03:27,990
Otherwise, what we should do, we will see next month to next element.

49
00:03:28,260 --> 00:03:30,440
Is it smaller than 20 years?

50
00:03:30,690 --> 00:03:34,280
So create a new node with the help of temporary pointer and insert the value.

51
00:03:34,300 --> 00:03:36,420
Make this and make this point on this one.

52
00:03:36,870 --> 00:03:42,240
This is the address of that pointing to the stack and move on new node.

53
00:03:42,450 --> 00:03:45,390
So this I have done it using the second one more step.

54
00:03:45,390 --> 00:03:46,610
We have repeated the same thing.

55
00:03:47,070 --> 00:03:48,410
Now move to next one.

56
00:03:49,560 --> 00:03:50,400
This is 15.

57
00:03:50,910 --> 00:03:52,880
Is it smaller than this one?

58
00:03:52,890 --> 00:03:54,140
Is it smaller than this one?

59
00:03:54,150 --> 00:03:56,010
Ten, not smaller than 10.

60
00:03:56,340 --> 00:03:57,830
Definitely the president.

61
00:03:57,840 --> 00:03:59,310
And yes, it is greater than 10.

62
00:03:59,610 --> 00:04:05,540
So it is greater than 10 minutes insert here or confirm, but it comes there only or not.

63
00:04:06,060 --> 00:04:13,020
I have to confirm how that element, that next element should be in between these two.

64
00:04:13,170 --> 00:04:16,260
It should be smaller than this one and greater than 10.

65
00:04:16,440 --> 00:04:18,630
So it should be in between 10 and 20.

66
00:04:19,260 --> 00:04:19,610
Right.

67
00:04:20,130 --> 00:04:20,550
So.

68
00:04:21,649 --> 00:04:23,220
It is not less than this one.

69
00:04:23,260 --> 00:04:29,660
Yes, else it should be greater than this one and less than 20.

70
00:04:29,660 --> 00:04:30,590
So it is 20.

71
00:04:30,830 --> 00:04:33,410
So the topmost element to the stack, don't delete it.

72
00:04:33,560 --> 00:04:34,280
Don't delete it.

73
00:04:34,550 --> 00:04:36,770
Just check the data from the printer.

74
00:04:37,280 --> 00:04:42,830
So it's greater than 10 and smaller than 15 is greater than 10 and smaller than 20.

75
00:04:43,130 --> 00:04:46,820
Then create a new node with the help of a temporary pointer.

76
00:04:47,390 --> 00:04:47,810
Right.

77
00:04:48,080 --> 00:04:49,790
And insert that value 15.

78
00:04:49,790 --> 00:04:52,530
Make this as null, make peace right.

79
00:04:52,880 --> 00:04:57,230
Belong to this one and bring peace upon this new node.

80
00:04:57,560 --> 00:04:59,120
Don't push the address.

81
00:05:00,350 --> 00:05:06,850
So this is the difference, the left child with pushing diapers and going on neonate, but it is right

82
00:05:06,860 --> 00:05:10,610
child, just go on, you know, don't push that as often.

83
00:05:10,910 --> 00:05:12,620
We don't have to go back to ten now.

84
00:05:13,220 --> 00:05:13,680
Years left.

85
00:05:13,970 --> 00:05:16,400
It was not the right child reinserted.

86
00:05:16,970 --> 00:05:19,600
So Ten's role is over.

87
00:05:19,850 --> 00:05:23,530
Dr. Nords role is completed, so we don't need it in this tap.

88
00:05:24,050 --> 00:05:28,610
Next move to the next step is rebidding.

89
00:05:30,970 --> 00:05:33,550
Twenty five, is it less than 15?

90
00:05:33,880 --> 00:05:37,540
No, it's not less than 15, is it greater than 15?

91
00:05:37,570 --> 00:05:39,490
Yes, obviously it is greater than 15.

92
00:05:39,730 --> 00:05:40,930
Shalane So that right?

93
00:05:41,230 --> 00:05:41,740
No, no.

94
00:05:42,070 --> 00:05:48,430
You have to check whether it is lying in between the range of this 15 and the.

95
00:05:49,270 --> 00:05:51,360
So what does this note digresses here?

96
00:05:51,370 --> 00:05:53,920
Just take the 15 and 20.

97
00:05:54,040 --> 00:05:56,220
Is it lying in between 15 and 20?

98
00:05:56,260 --> 00:05:57,550
No, that is stratified.

99
00:05:57,820 --> 00:05:59,130
So it's not in between.

100
00:05:59,540 --> 00:06:02,440
Also, so it will not come as the rate of 15.

101
00:06:03,310 --> 00:06:06,730
So if it is within range inserted, otherwise what to do?

102
00:06:07,120 --> 00:06:12,710
Pop out the Sirtris from the stack and move B here.

103
00:06:13,300 --> 00:06:15,940
So at one point we got that list of 20.

104
00:06:16,660 --> 00:06:22,570
So in this system we did not move I to the next element because it is not yet in second step is complicated.

105
00:06:22,580 --> 00:06:23,270
Don't move on.

106
00:06:24,330 --> 00:06:25,440
Let us start again.

107
00:06:26,410 --> 00:06:28,780
Twenty five years ago, less than 20.

108
00:06:28,930 --> 00:06:31,030
No, definitely it's greater.

109
00:06:31,480 --> 00:06:37,990
So if it is greater, it should lie in between the 20 and the topmost element in this track, but most

110
00:06:37,990 --> 00:06:39,650
northern Australia is having today.

111
00:06:40,030 --> 00:06:41,980
So it should be between 20 and 30.

112
00:06:42,280 --> 00:06:43,550
Yes, it is in between.

113
00:06:43,810 --> 00:06:51,800
So if it is in between, insert twenty five on the right hand side, new node link it be to new node.

114
00:06:52,450 --> 00:06:58,450
Don't push that in the time when we are creating right child we are not inserting divests then also

115
00:06:58,450 --> 00:07:01,120
move to the next element because twenty five is inserted.

116
00:07:02,130 --> 00:07:04,350
Step completed, repeat.

117
00:07:05,550 --> 00:07:08,310
Forty, is it less than five current?

118
00:07:08,340 --> 00:07:10,980
No, this was fun, no greater than twenty five.

119
00:07:11,030 --> 00:07:11,580
Yes.

120
00:07:11,910 --> 00:07:15,360
What is it like in between 25 and this value 30?

121
00:07:15,780 --> 00:07:18,360
No, 40 is not line between these two.

122
00:07:18,600 --> 00:07:22,970
Then both the address and the move to that address.

123
00:07:24,180 --> 00:07:24,870
Don't move.

124
00:07:24,870 --> 00:07:25,860
I let it be.

125
00:07:25,860 --> 00:07:28,200
Their only step is completed.

126
00:07:28,650 --> 00:07:30,840
Continue 40.

127
00:07:30,870 --> 00:07:35,000
Is it less than 30 nor is it greater than 30 40 years.

128
00:07:35,490 --> 00:07:43,410
Compared the value from here to stack when Stack is not having anything means let it be infinity maximum

129
00:07:43,410 --> 00:07:44,870
value, let it be infinity.

130
00:07:45,180 --> 00:07:47,720
So this line between 30 and infinity.

131
00:07:47,850 --> 00:07:48,330
Yes.

132
00:07:48,480 --> 00:07:56,760
So I did as a child create a new node with a temporary point thirty forty is inserted make this afternoon

133
00:07:57,180 --> 00:07:59,190
and the PS right pointing here.

134
00:07:59,340 --> 00:08:00,720
Bring me on this one.

135
00:08:01,440 --> 00:08:03,510
Now I've shown you all cases not remaining true.

136
00:08:03,510 --> 00:08:07,220
I will finish quickly now I want fifty fifty smaller.

137
00:08:07,230 --> 00:08:07,710
No fifty.

138
00:08:07,710 --> 00:08:08,340
Is it greater.

139
00:08:08,340 --> 00:08:09,120
Yes greater.

140
00:08:09,120 --> 00:08:11,550
What is it like in between 40 and nothing.

141
00:08:11,550 --> 00:08:12,020
Is that so.

142
00:08:12,030 --> 00:08:12,810
It is infinity.

143
00:08:13,110 --> 00:08:14,910
So it's laying in between 40 and infinity.

144
00:08:15,120 --> 00:08:18,900
So insert 50 on the site with a temporary pointer and link it.

145
00:08:19,140 --> 00:08:22,410
Bring peace upon that new node and also move I.

146
00:08:24,040 --> 00:08:29,020
So that furphies insider next element is forty five, forty five, is it less than 50?

147
00:08:29,050 --> 00:08:30,370
Yes, it is less than 50.

148
00:08:30,580 --> 00:08:36,580
So insert here forty five and link it and push of 15.

149
00:08:36,580 --> 00:08:39,360
The stack of 50 pushed into the stack.

150
00:08:39,669 --> 00:08:40,710
Bring me here.

151
00:08:41,610 --> 00:08:42,460
Move I.

152
00:08:43,419 --> 00:08:45,730
I has reached the end of the preorder.

153
00:08:46,120 --> 00:08:48,860
So the procedure ends and we got resod.

154
00:08:50,100 --> 00:08:52,590
So we have reached the end of an era and we got the.

155
00:08:54,040 --> 00:08:57,850
So that follows the procedure, I will write on a function and show you.

