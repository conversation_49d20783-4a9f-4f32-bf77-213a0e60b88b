1
00:00:00,330 --> 00:00:08,430
Let us look at a recent operation of two spots, Matisse<PERSON>, so already I have discussed how it is done,

2
00:00:08,430 --> 00:00:14,280
but now let us ride on the code, a function for adding to spots mitosis.

3
00:00:14,610 --> 00:00:18,540
So, for example, I have taken two spots, mitosis as one and as two.

4
00:00:18,870 --> 00:00:24,120
And this is the representation that is a structure called spa's.

5
00:00:24,120 --> 00:00:31,050
We have already seen it so that these are the objects of spots structure and already I have Phyllis's

6
00:00:31,050 --> 00:00:37,080
for cross five matics, four, five, six non-zero elements are there that are one, two, three, four,

7
00:00:37,080 --> 00:00:38,390
five, six six nine zero.

8
00:00:38,400 --> 00:00:38,790
Limerence.

9
00:00:39,090 --> 00:00:44,300
And all the elements I have already felt here like this element one commentary's or three.

10
00:00:44,310 --> 00:00:49,070
So one commentary's three or four common two is a six or four to six.

11
00:00:49,500 --> 00:00:51,150
Similarly, I have felt this once.

12
00:00:51,540 --> 00:00:57,890
Now let's write a function for adding these two spots, Matisses and generating a third matrix that

13
00:00:57,900 --> 00:00:59,430
is sum of two mitosis.

14
00:00:59,910 --> 00:01:02,150
So already have kept one pointer here.

15
00:01:02,160 --> 00:01:05,280
Now let us look at a function function namers.

16
00:01:06,120 --> 00:01:08,460
It needs these two Matisses.

17
00:01:08,790 --> 00:01:12,960
Now where these mattresses are available, they are maybe present in some other function.

18
00:01:12,960 --> 00:01:16,770
So this function needs pointers to those two madrassas.

19
00:01:17,040 --> 00:01:19,320
So I will write spots.

20
00:01:20,700 --> 00:01:26,370
Actually I should write structure response, but I'm just writing spottier pointer as one.

21
00:01:27,090 --> 00:01:35,130
So this is a pointer for first matics, then spot's pointer as to this is the pointer for the second

22
00:01:35,130 --> 00:01:35,610
matics.

23
00:01:36,060 --> 00:01:37,860
Now the function is taking two pointers.

24
00:01:37,870 --> 00:01:43,950
S willingness to let us imagine that this pointer is pointing on this one and this pointer is pointing

25
00:01:43,950 --> 00:01:46,060
on this matics.

26
00:01:46,800 --> 00:01:50,040
So these are present somewhere inside the same information.

27
00:01:50,760 --> 00:01:52,680
No, it has to add those two.

28
00:01:52,680 --> 00:01:54,000
So inside the function.

29
00:01:54,000 --> 00:01:59,190
First of all, let us check whether these two Matisses can be added or not, whether they can be added

30
00:01:59,190 --> 00:01:59,580
or not.

31
00:01:59,580 --> 00:02:02,030
We can do it by comparing two dimensions.

32
00:02:02,040 --> 00:02:03,780
The number of rules that is four.

33
00:02:03,780 --> 00:02:04,860
Four should be same.

34
00:02:05,130 --> 00:02:08,430
A number of columns, five, five dimensions must be seen.

35
00:02:08,729 --> 00:02:10,740
So let us write on that first condition.

36
00:02:11,130 --> 00:02:16,470
So what I'm thinking here, if roles are not matching, columns are not matching, don't do anything

37
00:02:17,070 --> 00:02:17,400
so far.

38
00:02:17,420 --> 00:02:19,350
So I wrote the code for checking this one.

39
00:02:19,650 --> 00:02:26,010
See, even I could have check whether they are equal or this is also equal then to all the operations.

40
00:02:26,160 --> 00:02:30,240
But the other way I have written, if they are not matching struct, don't do anything.

41
00:02:30,570 --> 00:02:35,460
So first I have check the false condition that is failure condition.

42
00:02:35,460 --> 00:02:36,960
If they are not same then stop.

43
00:02:36,960 --> 00:02:42,270
Don't do anything otherwise they can be added how to do that.

44
00:02:42,540 --> 00:02:46,590
So for adding first of all, I should create a third object.

45
00:02:46,800 --> 00:02:49,260
So here already I have taken a pointer, call some.

46
00:02:49,260 --> 00:02:50,940
So for that I will create an object.

47
00:02:51,210 --> 00:02:56,070
So for that I should have a pointer inside this function that responds.

48
00:02:57,670 --> 00:03:05,050
Point of some, so that is a pointer, so to that point, I should create an object.

49
00:03:07,320 --> 00:03:17,670
Of sports matics, having any number of elements and also appointer e I should have an.

50
00:03:19,740 --> 00:03:22,030
Study of some sites.

51
00:03:23,550 --> 00:03:29,370
So what should be the size of the city, I should create the object and also I should create an array

52
00:03:29,370 --> 00:03:30,700
of elements of some type.

53
00:03:30,720 --> 00:03:33,270
So, first of all, let us create this object.

54
00:03:34,710 --> 00:03:41,010
So I should say some assign new spots.

55
00:03:43,460 --> 00:03:44,600
Object is created.

56
00:03:46,530 --> 00:03:52,620
Then here, I should fill the dimensions, I can take the dimensions from either as one or I can take

57
00:03:52,620 --> 00:03:54,660
them from S2, so.

58
00:03:56,890 --> 00:04:05,620
Some number of roads should be same as Aslan's number of rules and the same line, I'm continuing some

59
00:04:06,100 --> 00:04:10,150
number of columns that should be same as S1 number of columns.

60
00:04:11,370 --> 00:04:17,200
So this four and five, I can copy that here for then how many number of elements?

61
00:04:17,220 --> 00:04:17,910
I don't know.

62
00:04:18,089 --> 00:04:18,700
I don't know.

63
00:04:18,720 --> 00:04:21,950
Once I have finished adding, then I'll be doing how many elements are there?

64
00:04:22,650 --> 00:04:26,490
So this I cannot fill up then this object I have to create.

65
00:04:27,180 --> 00:04:28,890
So what should be the size of this one?

66
00:04:28,920 --> 00:04:35,920
So unless they are, they cannot know what should be the size but total maximum size they can have.

67
00:04:36,120 --> 00:04:40,830
So what is the maximum size number of elements here are six and here also six.

68
00:04:41,400 --> 00:04:42,550
So total 12.

69
00:04:42,810 --> 00:04:48,870
So I will create an area of size 12, 12, maybe more than the required size, but no problem if you

70
00:04:48,870 --> 00:04:52,310
have some extra spaces that are unused spaces, no problem.

71
00:04:52,320 --> 00:04:57,110
So we will create and assume that an area of file to all is created.

72
00:04:57,120 --> 00:05:00,180
So I make a few checking is completed.

73
00:05:01,170 --> 00:05:07,320
This is the way to check the creation of a new matrix this past.

74
00:05:07,350 --> 00:05:10,380
Mattocks was created and we have created in heap.

75
00:05:10,400 --> 00:05:15,630
Remember this we have created in here so that it can be used anywhere in the program.

76
00:05:16,350 --> 00:05:19,750
If anywhere in the program you have appointed to this one, you can access this one.

77
00:05:19,770 --> 00:05:23,570
So I have created this and he then I have filled this value.

78
00:05:24,240 --> 00:05:26,340
So let us create this for that.

79
00:05:26,340 --> 00:05:31,750
I have to say Sum's E assign new.

80
00:05:32,430 --> 00:05:34,950
So what is that array of elements.

81
00:05:34,950 --> 00:05:36,810
So new elements.

82
00:05:37,860 --> 00:05:39,540
So element is one structure.

83
00:05:39,540 --> 00:05:40,530
We have already seen it.

84
00:05:40,800 --> 00:05:42,180
And what should be the size.

85
00:05:42,190 --> 00:05:49,310
So the size should be the combined size of S1 and S2 non-zero elements.

86
00:05:49,560 --> 00:05:52,620
So let us say as once num.

87
00:05:54,090 --> 00:05:56,300
Plus as tools.

88
00:05:57,030 --> 00:06:04,350
So this is created so we have finished with the initial work, so I cannot write everything here, I

89
00:06:04,350 --> 00:06:05,460
have to remove this.

90
00:06:06,570 --> 00:06:08,370
So just have a look of dysfunction.

91
00:06:08,670 --> 00:06:10,800
I have a pointer here.

92
00:06:11,040 --> 00:06:14,820
Then I am checking whether their dimensions are not matching.

93
00:06:14,820 --> 00:06:17,360
Then stop there itself, return zero.

94
00:06:18,180 --> 00:06:25,800
Otherwise create an object of sparse metrics, then create an array of elements and set the dimensions

95
00:06:25,800 --> 00:06:26,720
four and five.

96
00:06:26,760 --> 00:06:28,280
So this much work is over.

97
00:06:28,640 --> 00:06:33,360
Further, I have to add them so far that I will remove this and write on the code for lation.

98
00:06:34,020 --> 00:06:36,910
Now let us write on the code for adding them so far.

99
00:06:37,020 --> 00:06:44,130
Adding these markers says we need index pointers, i.e. at the starting of this G at the starting of

100
00:06:44,130 --> 00:06:45,140
this and key.

101
00:06:46,050 --> 00:06:48,310
So I j k all of them should be zero.

102
00:06:48,900 --> 00:06:50,880
Then let us see the procedure.

103
00:06:51,920 --> 00:06:58,430
See, first, check the raw numbers, if raw numbers among these raw numbers, if it's the ones raw

104
00:06:58,430 --> 00:07:05,660
numbers are smaller than this one, that if as the tools, raw numbers are smaller than copy that one.

105
00:07:05,690 --> 00:07:08,630
So whoever the raw numbers are smaller, copy that one.

106
00:07:08,810 --> 00:07:17,380
So first, I'll check the raw numbers, see if each of I got a raw number, that is.

107
00:07:18,200 --> 00:07:21,880
If you remember, these are eyes and cheese and these are X.

108
00:07:22,370 --> 00:07:30,440
So if this is less than S2 cap of iron, if rule number of this one is smaller than that raw number,

109
00:07:30,710 --> 00:07:32,360
then copy this element.

110
00:07:32,480 --> 00:07:34,320
That is this element in the key.

111
00:07:34,670 --> 00:07:42,860
So some of E of K plus plus a sign that this element.

112
00:07:42,890 --> 00:07:45,320
So all these three things can be copied directly.

113
00:07:45,590 --> 00:07:49,870
Aslan's E of I blessedness.

114
00:07:50,510 --> 00:07:56,670
So if any one off, if this is small, copied this one, but in our example they are equal.

115
00:07:56,900 --> 00:08:00,890
So if it is small, copy that one else.

116
00:08:02,520 --> 00:08:18,210
If s once e I don't I it's greater than as tools, e.g., I mean, if that one small number is a smaller

117
00:08:18,210 --> 00:08:19,780
than copy that number here.

118
00:08:20,460 --> 00:08:33,210
So some of it of K plus plus is assigned that as tools E of C++.

119
00:08:35,570 --> 00:08:42,620
So if one is a small copy, that one second one is a small copy, that one, if both are equal, then

120
00:08:42,620 --> 00:08:44,600
what chick column?

121
00:08:44,630 --> 00:08:51,470
So this comes as Hellespont ends no checker column numbers.

122
00:08:51,710 --> 00:08:54,200
If this one's column is the small copy, that one.

123
00:08:54,200 --> 00:08:57,080
If that one's column is small, copy that one.

124
00:08:57,470 --> 00:08:58,980
Otherwise add them.

125
00:08:59,270 --> 00:09:02,570
So I have to continue the logic here if.

126
00:09:04,710 --> 00:09:05,730
S once.

127
00:09:07,640 --> 00:09:18,880
He of IDOT chair is a smaller than as Stool's E of J's column, then copied the Saliman first.

128
00:09:18,890 --> 00:09:20,530
So I will continue on the same line.

129
00:09:20,900 --> 00:09:27,080
So copy in some of it of Cabelas plus copy.

130
00:09:28,800 --> 00:09:29,790
S once.

131
00:09:31,380 --> 00:09:32,220
Eight of.

132
00:09:33,390 --> 00:09:34,500
I placeless.

133
00:09:36,550 --> 00:09:50,830
Otherwise, if as one city of GAO is greater than as stool's E, G of G means this is greater than that

134
00:09:50,830 --> 00:09:51,610
one column.

135
00:09:51,670 --> 00:09:57,620
We are checking see, these are columns, G.R. columns, then copy this element there.

136
00:09:57,640 --> 00:09:58,930
So some of.

137
00:10:00,970 --> 00:10:12,210
E of C++, a sign that s tools E of the surplus, so we have finished with the role as a small or always

138
00:10:12,220 --> 00:10:17,380
greater column is a small L Scholem's greater, otherwise both are equal.

139
00:10:17,590 --> 00:10:19,500
So this is in finals.

140
00:10:20,290 --> 00:10:22,000
We have to add these to one.

141
00:10:22,340 --> 00:10:26,780
The results are so final and I'll simply write on the code here itself.

142
00:10:27,100 --> 00:10:31,930
So some of E of K plus plus.

143
00:10:32,970 --> 00:10:44,370
His assignment as one of four coping, I have to copy three things wrong number, column number and

144
00:10:44,370 --> 00:10:45,450
the sum of the elements.

145
00:10:45,450 --> 00:10:46,560
So far, no column.

146
00:10:46,560 --> 00:10:53,580
No, simply, I will copy the values from S1 C S1 of I plus plus.

147
00:10:54,300 --> 00:11:02,420
So actually I have simply copied S1 this element to some, but I should add them.

148
00:11:02,760 --> 00:11:07,000
So I should add the value from this is to simply add the value.

149
00:11:07,260 --> 00:11:08,860
So I need one more statement.

150
00:11:09,090 --> 00:11:12,630
So after this and I should say some of.

151
00:11:14,480 --> 00:11:26,770
E now C++ in this X value, I should add, the value from S2, so as tools E of G plus plus dot.

152
00:11:27,170 --> 00:11:27,770
Excellent.

153
00:11:28,820 --> 00:11:32,760
That's it then this procedure should be repeated.

154
00:11:32,990 --> 00:11:35,360
So this has to be continued.

155
00:11:35,630 --> 00:11:47,450
While AI is less than number of elements of this one, and that is num num, Aslan's num and G should

156
00:11:47,450 --> 00:11:50,520
be less than a stool's num.

157
00:11:51,170 --> 00:11:57,110
So this loop will be comparing and copying that I will just appraise this code and show you a little

158
00:11:57,110 --> 00:11:57,410
bit.

159
00:11:58,130 --> 00:11:59,630
So let us trace the code.

160
00:11:59,750 --> 00:12:02,270
See, first round numbers are compared.

161
00:12:02,930 --> 00:12:03,990
Numbers are the same.

162
00:12:04,370 --> 00:12:05,210
So it will.

163
00:12:05,630 --> 00:12:06,960
So this is not smaller.

164
00:12:06,980 --> 00:12:07,970
This is not greater.

165
00:12:08,000 --> 00:12:11,140
So it will come to this Ellsburg then column numbers.

166
00:12:11,140 --> 00:12:13,820
The check first ones column number is a smaller.

167
00:12:14,060 --> 00:12:17,990
So this will be true at this point that this will be true.

168
00:12:18,290 --> 00:12:22,120
That is the raw numbers of these two are same, but the column number is smaller.

169
00:12:22,130 --> 00:12:23,880
So one three three will be copied here.

170
00:12:24,170 --> 00:12:28,610
So one, three and three Scoppetta here gaze moved ahead.

171
00:12:29,000 --> 00:12:30,410
I is moved ahead.

172
00:12:31,850 --> 00:12:34,220
Now in this case are all numbers are checked.

173
00:12:35,210 --> 00:12:36,020
This is two.

174
00:12:36,200 --> 00:12:37,090
This is one.

175
00:12:37,460 --> 00:12:39,580
So this is false.

176
00:12:40,070 --> 00:12:41,060
This will be true.

177
00:12:42,040 --> 00:12:50,260
So this role number is a smaller one, five two is copied, so one five two is copied, then G is incremented

178
00:12:50,530 --> 00:12:52,000
and KS incremented.

179
00:12:53,780 --> 00:13:01,400
So this was true in this case, so the element from S2 is copied, let us continue now.

180
00:13:01,760 --> 00:13:07,810
Raw numbers are equal two and two, but the column number is one and column number is two.

181
00:13:08,090 --> 00:13:11,420
So, again, this condition will be true column numbers.

182
00:13:11,450 --> 00:13:12,640
This condition will be true.

183
00:13:12,890 --> 00:13:17,270
So to one forward is copied to one forward is copied.

184
00:13:17,690 --> 00:13:26,510
Gays incremented as well as I is incremented now this time two and two run numbers are the same.

185
00:13:27,110 --> 00:13:28,950
But column number, this is greater.

186
00:13:29,150 --> 00:13:30,440
This is smaller.

187
00:13:30,830 --> 00:13:33,670
So the numbers are the same and said will go into a spot.

188
00:13:34,130 --> 00:13:36,380
But the column number, this is greater than that one.

189
00:13:36,390 --> 00:13:38,620
So this will enter into this spot.

190
00:13:38,840 --> 00:13:45,830
See, this column number is greater than the column number of S1 as one column number is greater than

191
00:13:45,830 --> 00:13:47,090
ESTOS column number.

192
00:13:47,270 --> 00:13:50,320
So S2 will be copied, this S2 will be copied.

193
00:13:50,600 --> 00:13:55,380
So two to five is copy, two to five is copy and KS incremented.

194
00:13:55,790 --> 00:13:57,180
JS incremented.

195
00:13:57,650 --> 00:14:06,410
Now this time round number two to our seem so it will enter into Hellespont then here column number

196
00:14:06,410 --> 00:14:11,270
five five Aasim then it will enter into this part this Hellespont.

197
00:14:11,600 --> 00:14:20,450
So it will copy the element from this one that is two five seven four two five seven as I have copied

198
00:14:20,720 --> 00:14:27,350
as one element then is increment it will move to the next, but that value should also be added.

199
00:14:27,380 --> 00:14:29,660
So here I am adding an X.

200
00:14:29,660 --> 00:14:35,630
The value of this one X is added so it should be added plus assigned.

201
00:14:35,930 --> 00:14:38,740
So this value added six is added to this one.

202
00:14:38,750 --> 00:14:45,560
So this becomes 13 and K is incremental as well as Giese incremental.

203
00:14:45,740 --> 00:14:46,310
That's all.

204
00:14:46,520 --> 00:14:48,560
I've shown you all the cases one time.

205
00:14:48,560 --> 00:14:51,800
It was true here and it was true here also.

206
00:14:51,980 --> 00:14:55,370
And I've also shown you all three cases now.

207
00:14:55,400 --> 00:14:59,150
Similarly, it will continue and it will add all the elements.

208
00:14:59,480 --> 00:15:06,680
So this type of code I will be writing in my program for adding two spots, Matisses, that at the end,

209
00:15:06,800 --> 00:15:08,690
whatever the value of K will be.

210
00:15:08,870 --> 00:15:13,960
So that will be set as the key value number of non-zero elements.

211
00:15:14,840 --> 00:15:17,810
So that's all about additional spots.

212
00:15:17,830 --> 00:15:20,090
Makes us look at the program.

