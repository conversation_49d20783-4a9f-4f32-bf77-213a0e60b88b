1
00:00:00,330 --> 00:00:06,660
Now, in this video, we'll talk about these operations that are reversing a list that is already left,

2
00:00:06,660 --> 00:00:13,260
shifting of elements, left rotation of elements and the right to shift and tradition are similar to

3
00:00:13,260 --> 00:00:13,770
these.

4
00:00:13,780 --> 00:00:15,930
So I'll be discussing these operations.

5
00:00:16,360 --> 00:00:20,100
Let us start with three for reversing an earlier.

6
00:00:20,520 --> 00:00:22,170
We have two methods.

7
00:00:22,800 --> 00:00:25,030
Let us look at the first networks, law enforcement.

8
00:00:25,040 --> 00:00:28,320
Third, we will take an auxiliary of the extraordinary.

9
00:00:28,680 --> 00:00:31,290
So we'll take one, let us call it as B.

10
00:00:33,550 --> 00:00:39,550
In this area, we will copy all those elements from the actual original Uhry and we will copy them in

11
00:00:39,550 --> 00:00:41,170
reverse from last element.

12
00:00:41,170 --> 00:00:41,710
We'll copy.

13
00:00:41,720 --> 00:00:49,570
So the first element that will copy here will be for Ford is copied here to all this copy that was copied

14
00:00:49,570 --> 00:00:52,480
here, then seven is copied, then 10.

15
00:00:53,290 --> 00:00:55,950
So in this way, we will reverse copy all the elements.

16
00:00:59,480 --> 00:01:02,770
So the elements are reverse copied then?

17
00:01:03,690 --> 00:01:10,940
No, we will copy these elements back into the original array, so corresponding elements, same location

18
00:01:10,940 --> 00:01:11,870
elements will be copied.

19
00:01:11,900 --> 00:01:15,380
So this becomes for and this becomes 2L and this becomes too.

20
00:01:15,530 --> 00:01:18,350
So we'll replace the elements in the original.

21
00:01:20,360 --> 00:01:26,990
So this is my third and fourth, my third, we require an extra day, how much time it is taking for

22
00:01:26,990 --> 00:01:33,580
reversing the elements so so reverse copying from A to B. So from A to B, when you copy the elements,

23
00:01:33,610 --> 00:01:35,780
the number of elements are in there.

24
00:01:35,840 --> 00:01:39,200
Again, you have to copy the elements from B to A to B to eight.

25
00:01:39,440 --> 00:01:42,620
Again, there are an element so total to end.

26
00:01:42,990 --> 00:01:44,330
So this order of N.

27
00:01:46,030 --> 00:01:53,980
So the time taken in order of any knowledge is right on the court for reversing that remix using auxillary

28
00:01:54,070 --> 00:02:01,600
A B, we have to copy the elements from A, that's from Lasek next in B, starting from Facenda.

29
00:02:01,700 --> 00:02:08,490
So we will take two integer indexed pointers, i.e. start from there and just transfer from here.

30
00:02:09,070 --> 00:02:12,800
I will be decreasing and G will be increasing.

31
00:02:13,060 --> 00:02:15,610
So this can be done using a loop.

32
00:02:15,910 --> 00:02:19,570
I start from last indexes lenda minus one.

33
00:02:20,680 --> 00:02:22,180
This is Lento minus one.

34
00:02:26,230 --> 00:02:32,320
And also, we need gee, that is Jyothi and Zettl, so you'll be doing two initialization then semicolon

35
00:02:32,320 --> 00:02:38,220
so we can have two initialization and follow separated by comma, then comes a semicolon.

36
00:02:38,650 --> 00:02:41,550
How long we should do this until I reach zero.

37
00:02:41,560 --> 00:02:46,530
So beyond this one we should stop so i.e. greater than or equal to zero.

38
00:02:48,230 --> 00:02:51,440
So when it becomes less than zero, it should stop.

39
00:02:52,550 --> 00:03:00,590
Then every time I should be diclemente, OK, comma, then the G should be incrementing G plus plus.

40
00:03:02,030 --> 00:03:09,650
Then while scanning through this study from back side and this from Frontside, we have to copy the

41
00:03:09,650 --> 00:03:20,360
elements and to be at the location G from a film location, i.e. this spot as reverse copying the elements.

42
00:03:21,470 --> 00:03:26,780
Then after doing this, you have to copy the elements from Betawi directly, so for this.

43
00:03:28,950 --> 00:03:36,010
We can start from here, I that seem I can be used here also zero zero one one one two two zero zero

44
00:03:36,390 --> 00:03:38,890
zero one is covered in one and so on.

45
00:03:39,030 --> 00:03:41,490
So, again, one more follow forward.

46
00:03:41,850 --> 00:03:43,770
I assign zero.

47
00:03:44,130 --> 00:03:46,610
I is less than lente.

48
00:03:47,130 --> 00:03:55,590
We have to go up to the last element then I placeless in this follow we can copy the elements from B

49
00:03:55,800 --> 00:04:04,290
to A so elements are copied in A from B, this portion is reverse copying then copying the elements

50
00:04:04,290 --> 00:04:07,020
from B to A now from the code.

51
00:04:07,030 --> 00:04:13,200
If I see then this statement is executing four times foreign elements and this we know well that the

52
00:04:13,200 --> 00:04:16,300
condition but this one will be one extra.

53
00:04:16,320 --> 00:04:17,640
So this and plus one.

54
00:04:18,060 --> 00:04:19,740
We are not concerned about this one.

55
00:04:19,740 --> 00:04:21,630
We are concerned about the actual work done.

56
00:04:21,870 --> 00:04:26,520
Then this statement is also and see, these loops are not inside oneanother.

57
00:04:26,880 --> 00:04:28,800
They are after one another.

58
00:04:28,830 --> 00:04:32,010
So it's time this time of separate for these two.

59
00:04:32,010 --> 00:04:33,960
And so the times out of.

60
00:04:33,990 --> 00:04:35,810
Ten times out of and.

61
00:04:37,150 --> 00:04:44,290
So this is one method of reversing, not second method, I will shoot in second method, we can scan

62
00:04:44,290 --> 00:04:51,700
from two Sons of Anarchy and interchange the elements of the elements like first element is swapped

63
00:04:51,700 --> 00:04:52,840
with the last element.

64
00:04:53,530 --> 00:04:56,800
We will swap these two elements, interchange them.

65
00:04:57,400 --> 00:05:04,870
Then these two elements are swapped three with the twelve and nine with the two, then 15 with seven,

66
00:05:05,440 --> 00:05:06,790
then six with the ten.

67
00:05:07,810 --> 00:05:12,490
We need twenty six I from here and from here then to change the elements.

68
00:05:12,490 --> 00:05:21,460
So copy for here and copy it here then decrement I and decrement G then in the change element.

69
00:05:21,470 --> 00:05:24,940
So 12 here and three here.

70
00:05:25,540 --> 00:05:30,490
So in this way if we continue, the rest of the elements will also be interchanged like.

71
00:05:33,420 --> 00:05:38,940
Similarly, if we continue, we can interchange rest of the elements, so then we should stop when I

72
00:05:38,970 --> 00:05:44,340
and Johanes came on the same place or I became greater than, gee, we should stop.

73
00:05:44,520 --> 00:05:47,720
So in this way, we can reverse the entire area.

74
00:05:48,600 --> 00:05:50,300
So how much time it is taking?

75
00:05:50,400 --> 00:05:52,160
We have to control the elements.

76
00:05:52,170 --> 00:05:52,880
How many times?

77
00:05:52,890 --> 00:05:53,530
Just one time.

78
00:05:53,850 --> 00:05:55,770
So time is how much of an.

79
00:05:57,330 --> 00:06:03,610
We are swapping total elements, we are accessing total and elements for the same thing.

80
00:06:03,630 --> 00:06:06,120
I will write on the code and show you for reversing.

81
00:06:07,670 --> 00:06:15,080
We want I'm starting from zero and just starting from lanta minus one.

82
00:06:18,640 --> 00:06:25,780
Then this can be done using for loop and how long we should continue, as long as I is less than G when

83
00:06:25,780 --> 00:06:28,290
I becomes greater than to stop anyone for.

84
00:06:29,780 --> 00:06:38,990
Then I plus plus G minus, minus, what do we have to do each time, interchange the elements of the

85
00:06:38,990 --> 00:06:47,090
rim and so forth, slapping some temporary variable, and in that story, a fine then in oenophile story

86
00:06:47,090 --> 00:06:51,950
of t an energy store temp.

87
00:06:54,390 --> 00:07:02,150
That's all this record for reversing early, so we don't need an auxiliary area for this one.

88
00:07:02,490 --> 00:07:09,120
So the time is of and now next to let us look at less shift and then left the rotation.

89
00:07:10,110 --> 00:07:17,310
Let us look at left shift operation, I have taken the small size of five five and I have five elements

90
00:07:17,310 --> 00:07:19,860
in that one now what does it mean by left shift?

91
00:07:19,890 --> 00:07:24,070
We want to shift all the elements on the left hand side us see.

92
00:07:24,090 --> 00:07:27,240
So from each element, we should start from the first element.

93
00:07:27,930 --> 00:07:29,330
If I shift this is six.

94
00:07:29,490 --> 00:07:30,570
There's a little space here.

95
00:07:30,570 --> 00:07:32,730
Six Vilborg ok let it go.

96
00:07:32,850 --> 00:07:39,660
No problem then three will come here and it will come here then five.

97
00:07:39,780 --> 00:07:42,710
Comfier nine crunchier here.

98
00:07:43,470 --> 00:07:45,690
This place is three so we can put zero there.

99
00:07:48,280 --> 00:07:52,640
So this is left shifting of elements, we will lose one element in this one.

100
00:07:52,660 --> 00:07:53,140
Yes.

101
00:07:53,950 --> 00:07:58,170
So it means if I have to shift right, then I should start from here.

102
00:07:58,510 --> 00:08:03,490
We will lose the last element and all the elements will move one step forward and we get free space

103
00:08:03,490 --> 00:08:04,500
here and it is zero.

104
00:08:04,900 --> 00:08:07,720
So just the direction is different left and right shift.

105
00:08:10,250 --> 00:08:18,040
So just the direction is different and left shift and shift, this is shift operation, then what is

106
00:08:18,060 --> 00:08:18,770
rotation?

107
00:08:19,710 --> 00:08:24,460
The deleted element, the first element that you got, you copied in the last location.

108
00:08:24,780 --> 00:08:29,450
It means that six has not removed, but it came here.

109
00:08:29,790 --> 00:08:30,900
So it is a rotation.

110
00:08:32,520 --> 00:08:33,150
NetSol.

111
00:08:34,250 --> 00:08:38,130
So shifting them, just moving them, how many spaces you want, you can move.

112
00:08:38,150 --> 00:08:44,990
We usually move by one place, then a rotation means the deleted element is copied at the last.

113
00:08:45,320 --> 00:08:46,670
This is called rotation.

114
00:08:47,950 --> 00:08:49,370
How much time it has taken?

115
00:08:49,840 --> 00:08:56,710
We have moved and elements of the time sort of shifting takes a lot of time and if you have to rotate,

116
00:08:56,710 --> 00:09:00,250
you can keep hold of the first element and you get a copy at the last.

117
00:09:01,240 --> 00:09:03,820
So this is called left shift and left rotation.

118
00:09:04,090 --> 00:09:11,350
Now, what is the use actually for explaining the use and show you not usually on display boards, LCD

119
00:09:11,350 --> 00:09:12,210
display boards?

120
00:09:12,220 --> 00:09:21,040
We find that the advertisements are scrawled on the display board that is eligible, for example, this

121
00:09:21,250 --> 00:09:24,390
would welcome as they're displayed on the board.

122
00:09:25,150 --> 00:09:29,980
So this can be constantly displayed like this, assuming a reward only.

123
00:09:30,580 --> 00:09:36,170
But actually, Sonari, now we want to show it as moving, scrolling.

124
00:09:36,700 --> 00:09:39,550
So what we have to do this should move the site.

125
00:09:39,550 --> 00:09:42,190
So remove W move in here.

126
00:09:42,490 --> 00:09:46,850
Move in here and see o m.

127
00:09:48,060 --> 00:09:54,970
E and blank space here, that blank space here, that blank space here, and then add a blank so the

128
00:09:54,980 --> 00:09:55,710
value is gone.

129
00:09:57,020 --> 00:10:02,220
No, if I repeat again this all let us move shift on the site and he will be gone then and will be gone.

130
00:10:02,240 --> 00:10:05,200
So it looks like as if this word welcomeness going away.

131
00:10:07,280 --> 00:10:13,790
So it's crawling now, followed by that you can start adding some other letters to them so the advertisements

132
00:10:13,790 --> 00:10:17,360
will be moving right to left on the display board.

133
00:10:17,570 --> 00:10:22,160
So with the left shifting, we can show them as scrolling advertisements.

134
00:10:23,000 --> 00:10:28,310
If you want this to work, just like this will come what is going around and again, coming back.

135
00:10:28,340 --> 00:10:29,360
It's like a location.

136
00:10:29,720 --> 00:10:30,740
It's like vacation.

137
00:10:30,980 --> 00:10:34,320
Then for that, this deleted letter, you can shoot.

138
00:10:36,930 --> 00:10:42,720
So now this evil leader out there, so that will come will be coming again here and it is fading away

139
00:10:42,720 --> 00:10:44,760
from the site and again, it will start from there.

140
00:10:45,210 --> 00:10:50,280
So this is the logic used for these reports.

141
00:10:50,680 --> 00:10:53,970
So this logic is common in electronics.

142
00:10:54,000 --> 00:11:01,740
So in computer science also, this technique is not as left or right or left, shift or shift.

143
00:11:02,970 --> 00:11:06,360
So that's about the basic operations we have seen up on tonight.

144
00:11:06,480 --> 00:11:07,980
So let's talk about this operation.

