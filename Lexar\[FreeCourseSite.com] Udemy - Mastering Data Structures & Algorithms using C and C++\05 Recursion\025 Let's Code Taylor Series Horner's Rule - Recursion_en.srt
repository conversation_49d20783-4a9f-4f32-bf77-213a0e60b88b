1
00:00:00,150 --> 00:00:05,100
So here in this video, we look at a recursive function for finding <PERSON><PERSON><PERSON>, that is for Taylor cities.

2
00:00:05,470 --> 00:00:08,780
We have already seen the procedure using ho<PERSON><PERSON><PERSON> in the previous video.

3
00:00:09,060 --> 00:00:11,010
That same function I will implement here.

4
00:00:11,490 --> 00:00:13,800
Already I have a body of a function.

5
00:00:13,800 --> 00:00:20,340
I will implement a function, I'll take a static double values while explanation.

6
00:00:20,340 --> 00:00:24,960
I have taken it as integer, but the result is a double type or a similar type.

7
00:00:26,550 --> 00:00:28,620
If and is equal to zero.

8
00:00:29,580 --> 00:00:30,930
Written as.

9
00:00:32,960 --> 00:00:35,600
Otherwise assessed, evaluated as one plus.

10
00:00:36,740 --> 00:00:44,720
Extra divided by N into S, C, X and and the both are of type integer, so I'll get integer division

11
00:00:44,720 --> 00:00:52,910
here, so I should typecast as double or else I can multiply X numerator with X beforehand so that this

12
00:00:52,910 --> 00:00:56,400
result becomes double and then divided by integer also gets double.

13
00:00:56,630 --> 00:00:59,000
So writing statement will give a perfect result.

14
00:01:00,640 --> 00:01:02,880
Otherwise, I have to tape cost X, Y and.

15
00:01:03,920 --> 00:01:05,860
And the next statement is written.

16
00:01:07,170 --> 00:01:10,890
The function call XCOM and the minus one.

17
00:01:14,270 --> 00:01:14,800
Deadfall.

18
00:01:15,790 --> 00:01:22,840
Inside the main function, I will directly print the result obtained by that function, so printf percentile

19
00:01:22,960 --> 00:01:27,880
L.F. and slashing all the function, even 10.

20
00:01:30,520 --> 00:01:32,710
Let us run the program, we already know the result.

21
00:01:32,740 --> 00:01:35,080
Let's see whether we get the same result or not.

22
00:01:35,260 --> 00:01:37,840
Yes, two point seven one eight two eight two.

23
00:01:40,060 --> 00:01:41,560
If I give the value to.

24
00:01:44,400 --> 00:01:50,970
Yes, it is seven point three eight eight seven one, let us shake it on a calculator to Ipoh Rexes,

25
00:01:50,970 --> 00:01:55,250
seven point three, eight to nine and we go to three eight eight.

26
00:01:55,260 --> 00:02:01,200
So that's less a little less if we want more precise value than we can increase the number of times.

27
00:02:03,200 --> 00:02:07,740
So that's all the implementation of inner cities using honor roll.

