<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>AAA-Budget Browser Demo — "NEON ASCENT"</title>
<style>
/* ======= CSS: sleek HUD + menus ======= */
:root{
  --accent:#00e6ff;
  --accent2:#ff61a6;
  --bg: #071021;
  --glass: rgba(255,255,255,0.04);
  --glass-2: rgba(255,255,255,0.02);
  --font-sans: system-ui,-apple-system,Segoe UI,Roboto,"Helvetica Neue",Arial;
}
html,body{height:100%;margin:0;background:linear-gradient(180deg,#02040a 0%,#071021 60%);font-family:var(--font-sans);color:#e6f7ff;overflow:hidden}
#gameCanvas{display:block;width:100vw;height:100vh;touch-action:none;background:#000}
.hud{
  position:fixed;left:20px;top:20px;z-index:50;
  background:linear-gradient(180deg,var(--glass),var(--glass-2));
  padding:10px;border-radius:12px;backdrop-filter: blur(6px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.6);
  min-width:220px;
}
.hud .stat{display:flex;justify-content:space-between;gap:12px;font-size:14px}
.hud .bar{height:12px;background:rgba(255,255,255,0.07);border-radius:8px;overflow:hidden;width:120px}
.hud .bar > i{display:block;height:100%;background:linear-gradient(90deg,var(--accent),var(--accent2));width:100%}
.center-ui{position:fixed;left:50%;top:50%;transform:translate(-50%,-50%);z-index:60;text-align:center}
.btn{padding:10px 16px;border-radius:10px;background:linear-gradient(90deg,var(--accent),var(--accent2));color:#021;cursor:pointer;border:none;font-weight:700}
.menu{
  position:fixed;z-index:60;left:16px;right:16px;top:60px;margin:auto;
  background:linear-gradient(180deg,rgba(0,0,0,0.5),rgba(0,0,0,0.4));
  border-radius:14px;padding:14px;backdrop-filter: blur(8px);
  display:flex;gap:12px;align-items:center;justify-content:space-between;
}
.top-right{position:fixed;right:20px;top:20px;z-index:50;color:#9fb7ff}
.small{font-size:12px;opacity:0.85}
#fps{color:#cde}
.controls-tip{position:fixed;left:50%;bottom:20px;transform:translateX(-50%);background:rgba(0,0,0,0.2);padding:8px 12px;border-radius:8px;font-size:13px}
.overlay{position:fixed;inset:0;display:flex;align-items:center;justify-content:center;z-index:80}
.modal{background:linear-gradient(180deg,#07121b,#071827);padding:22px;border-radius:14px;min-width:320px;max-width:720px;color:#d7eefc}
.small-note{font-size:12px;opacity:0.8;margin-top:8px}
.settings{display:flex;gap:12px;flex-wrap:wrap}
.row{display:flex;gap:8px;align-items:center}
kbd{background:#051726;border-radius:6px;padding:6px 8px;font-weight:700}
.badge{background:rgba(255,255,255,0.04);padding:6px 8px;border-radius:8px;font-weight:700}
.footer-small{position:fixed;right:12px;bottom:10px;color:#7fa;opacity:0.6;font-size:12px}
.loadingOverlay{position:fixed;inset:0;display:flex;align-items:center;justify-content:center;background:linear-gradient(180deg,rgba(0,0,0,0.6),rgba(0,0,0,0.75));z-index:200}
.logo{font-weight:900;font-size:20px;letter-spacing:1px}
</style>
</head>
<body>
<!-- Canvas -->
<canvas id="gameCanvas"></canvas>

<!-- HUD -->
<div class="hud" id="hud">
  <div class="stat"><div>Health</div><div class="bar"><i id="hpFill" style="width:100%"></i></div></div>
  <div class="stat"><div>Ammo</div><div id="ammo" class="small">12 / 12</div></div>
  <div style="margin-top:8px" class="small">Level: <span id="level">1</span> • Score: <span id="score">0</span></div>
</div>

<div class="top-right small" id="weatherText">Day</div>

<!-- Menus -->
<div class="menu" id="mainMenu">
  <div class="logo">NEON ASCENT — DEMO</div>
  <div style="display:flex;gap:8px;align-items:center">
    <button class="btn" id="startBtn">Start</button>
    <button class="btn" id="settingsBtn" style="background:linear-gradient(90deg,#333,#555)">Settings</button>
  </div>
</div>

<div class="controls-tip" id="controlsTip">WASD + mouse to move • Click to shoot • Esc to pause</div>

<div class="footer-small">Built with WebGL • Prototype</div>

<!-- Loading overlay -->
<div id="loading" class="loadingOverlay">
  <div style="text-align:center;color:#dff">
    <div style="font-size:28px;font-weight:800">NEON ASCENT</div>
    <div class="small-note">Rendering demo — loading assets & shaders…</div>
    <div style="height:10px"></div>
    <div id="loadProgress" class="small-note">0%</div>
  </div>
</div>

<!-- Victory / Game Over overlays -->
<div id="overlay" class="overlay" style="display:none;pointer-events:none"></div>

<!-- ======= External libs via CDN (recommended) =======
If you want fully offline, download three.min.js and the examples files and swap the <script> src to local files.
-->

<!-- Load Three.js core -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r150/three.min.js"></script>

<script>
  /* Simple loader that waits for Three.js to be available */
  (function(){
    function waitForThree() {
      if (typeof THREE !== 'undefined') {
        console.log('Three.js loaded successfully');
        startGame();
      } else {
        console.log('Waiting for Three.js...');
        setTimeout(waitForThree, 100);
      }
    }

    function showErrorFallback(){
      const loading = document.getElementById('loading');
      loading.innerHTML = `<div style="text-align:center;color:#fdd">
        <div style="font-size:20px">Could not load Three.js from CDN</div>
        <div class="small-note">To run offline: download <code>three.min.js</code> and place it next to this file, or allow CDN access.</div>
        <div style="height:12px"></div>
        <button class="btn" id="localRetry">Retry</button>
      </div>`;
      document.getElementById('localRetry').onclick = ()=> location.reload();
    }

    // Start checking for Three.js
    waitForThree();

    function showErrorFallback(){
      const loading = document.getElementById('loading');
      loading.innerHTML = `<div style="text-align:center;color:#fdd">
        <div style="font-size:20px">Could not load Three.js from CDN</div>
        <div class="small-note">To run offline: download <code>three.min.js</code> and the examples modules and place them next to this file, or allow CDN access.</div>
        <div style="height:12px"></div>
        <button class="btn" id="localRetry">Retry</button>
      </div>`;
      document.getElementById('localRetry').onclick = ()=> location.reload();
    }
  })();
</script>

<script>
/* ========== MAIN GAME CODE ========== 
   This block runs after Three.js and helpers are available.
*/
async function startGame(){
  // Grab modules - now they're all available as globals from the regular script loading
  const THREE = window.THREE; // three global loaded by three.min.js

  // These are now available as globals from the regular script includes
  const PointerLockControls = THREE.PointerLockControls;
  const EffectComposer = THREE.EffectComposer;
  const RenderPass = THREE.RenderPass;
  const UnrealBloomPass = THREE.UnrealBloomPass;
  const ShaderPass = THREE.ShaderPass;
  const FXAAShader = THREE.FXAAShader;

  // Basic globals
  const canvas = document.getElementById('gameCanvas');
  const loading = document.getElementById('loading');
  const loadProgress = document.getElementById('loadProgress');
  const hud = {
    hpFill: document.getElementById('hpFill'),
    ammo: document.getElementById('ammo'),
    level: document.getElementById('level'),
    score: document.getElementById('score'),
    weather: document.getElementById('weatherText'),
  };

  // Scene & Renderer
  const renderer = new THREE.WebGLRenderer({canvas, antialias:true, powerPreference:'high-performance'});
  renderer.setPixelRatio(Math.min(window.devicePixelRatio,1.5));
  renderer.shadowMap.enabled = true;
  renderer.shadowMap.type = THREE.PCFSoftShadowMap;
  renderer.outputEncoding = THREE.sRGBEncoding;
  renderer.setClearColor(0x071021,1);
  // Camera
  const camera = new THREE.PerspectiveCamera(70, innerWidth/innerHeight, 0.1, 2000);
  camera.position.set(0,2,6);

  // Scene
  const scene = new THREE.Scene();

  // Loaders
  const textureLoader = new THREE.TextureLoader();
  const audioCtx = new (window.AudioContext || window.webkitAudioContext)();

  // Simple asset manifest (we will use procedural geometry and tiny generated textures to stay self-contained)
  let assetsLoaded = 0, assetsTotal = 10;
  function progress() { assetsLoaded++; loadProgress.textContent = Math.round(assetsLoaded/assetsTotal*100) + '%'; if(assetsLoaded>=assetsTotal){ setTimeout(()=>loading.style.display='none',400); } }

  // Tiny procedural textures
  const baseCol = new THREE.CanvasTexture(makeNoiseCanvas('#112', '#0af'));
  baseCol.encoding = THREE.sRGBEncoding; baseCol.wrapS = baseCol.wrapT = THREE.RepeatWrapping;
  progress();

  const normalTex = new THREE.CanvasTexture(makeNormalCanvas());
  normalTex.wrapS = normalTex.wrapT = THREE.RepeatWrapping; progress();

  const roughTex = new THREE.CanvasTexture(makeNoiseCanvas('#333','#111')); roughTex.wrapS=roughTex.wrapT=THREE.RepeatWrapping; progress();

  // Environment: hemispheric ambient + moving directional sun
  const hemi = new THREE.HemisphereLight(0x88aaff, 0x0b0f1a, 0.6);
  scene.add(hemi);

  const sun = new THREE.DirectionalLight(0xfff4e1, 1.0);
  sun.position.set(5,10,5);
  sun.castShadow = true;
  sun.shadow.camera.left = -30; sun.shadow.camera.right = 30; sun.shadow.camera.top = 30; sun.shadow.camera.bottom = -30;
  sun.shadow.mapSize.set(2048,2048);
  scene.add(sun);

  // Fog & skybox-ish gradient
  scene.fog = new THREE.FogExp2(0x071827, 0.012);

  // Ground / terrain (procedural heightmap via sine + noise)
  const groundGeo = new THREE.PlaneGeometry(800,800,160,160);
  deformTerrain(groundGeo);
  const groundMat = new THREE.MeshStandardMaterial({
    map: baseCol, normalMap: normalTex, roughnessMap: roughTex, roughness:0.9, metalness:0.05
  });
  const ground = new THREE.Mesh(groundGeo, groundMat);
  ground.rotation.x = -Math.PI/2; ground.receiveShadow = true; ground.position.y = -1;
  scene.add(ground);
  progress();

  // Player (simple capsule + gun)
  const player = { pos: new THREE.Vector3(0,1.4,0), velocity: new THREE.Vector3(), hp:100, ammo:12, score:0, level:1 };

  const playerGroup = new THREE.Group();
  const body = new THREE.Mesh(new THREE.CapsuleGeometry(0.4,0.8,6,12), new THREE.MeshStandardMaterial({color:0x223344, metalness:0.2, roughness:0.5}));
  body.castShadow=true; body.receiveShadow=false; playerGroup.add(body);
  body.position.y = 0.6;
  scene.add(playerGroup);

  // Weapon (attached to camera)
  const weapon = new THREE.Mesh(new THREE.BoxGeometry(0.2,0.12,0.8), new THREE.MeshStandardMaterial({color:0x00e6ff, emissive:0x002233, metalness:0.9, roughness:0.1}));
  weapon.position.set(0.4,-0.3,-0.8);
  camera.add(weapon);
  scene.add(camera);

  // Enemies pool
  const enemies = [];
  const enemyGeom = new THREE.IcosahedronGeometry(0.45,1);
  const enemyMat = new THREE.MeshStandardMaterial({color:0xff4444, metalness:0.2, roughness:0.6, emissive:0x220000});
  for(let i=0;i<20;i++){
    const m = new THREE.Mesh(enemyGeom, enemyMat.clone());
    m.castShadow=true; m.receiveShadow=true;
    m.position.set((Math.random()-0.5)*120, Math.random()*6+0.5, (Math.random()-0.5)*120);
    m.userData.hp = 20;
    m.userData.speed = 0.6 + Math.random()*0.6;
    scene.add(m);
    enemies.push(m);
  }
  progress();

  // Simple collectible pickups
  const pickups = [];
  const pickGeo = new THREE.TorusGeometry(0.25,0.08,10,20);
  const pickMat = new THREE.MeshStandardMaterial({color:0xffde00, emissive:0x442200, metalness:0.6, roughness:0.3});
  for(let i=0;i<14;i++){
    const p = new THREE.Mesh(pickGeo, pickMat);
    p.position.set((Math.random()-0.5)*80,0.6,(Math.random()-0.5)*80);
    p.rotation.x = Math.PI/2;
    scene.add(p); pickups.push(p);
  }
  progress();

  // Particle systems (explosion, smoke, sparks, rain, magic)
  const particles = new THREE.Group(); scene.add(particles);
  const particleMat = new THREE.PointsMaterial({size:0.06, map: makeSprite(), transparent:true, depthWrite:false});
  // create a reusable pool of points
  const particlePool = [];
  for(let i=0;i<200;i++){
    const geom = new THREE.BufferGeometry();
    geom.setAttribute('position', new THREE.Float32BufferAttribute(new Float32Array(1*3),3));
    const p = new THREE.Points(geom, particleMat.clone());
    p.visible=false; particles.add(p); particlePool.push(p);
  }
  progress();

  // Post-processing (composer, bloom, fxaa, cheap DOF)
  const composer = new EffectComposer(renderer);
  const renderPass = new RenderPass(scene, camera);
  composer.addPass(renderPass);

  const bloomPass = new UnrealBloomPass(new THREE.Vector2(innerWidth,innerHeight), 0.9, 0.6, 0.1);
  bloomPass.threshold = 0.1; bloomPass.strength = 0.9; bloomPass.radius = 0.2;
  composer.addPass(bloomPass);

  const fxaaPass = new ShaderPass(FXAAShader);
  fxaaPass.material.uniforms['resolution'].value.set(1 / innerWidth, 1 / innerHeight);
  composer.addPass(fxaaPass);
  progress();

  // Simple "chromatic aberration" shader pass (cheat)
  const chromatic = new ShaderPass(new THREE.ShaderMaterial({
    uniforms: { tDiffuse:{value:null}, amount:{value:0.002 + Math.random()*0.002} },
    vertexShader: `varying vec2 vUv; void main(){ vUv = uv; gl_Position = projectionMatrix * modelViewMatrix * vec4(position,1.0); }`,
    fragmentShader: `
      uniform sampler2D tDiffuse; uniform float amount; varying vec2 vUv;
      void main(){
        vec2 uv = vUv;
        vec4 c = texture2D(tDiffuse, uv);
        float rOff = amount;
        float bOff = -amount;
        float r = texture2D(tDiffuse, uv + vec2(rOff,0.0)).r;
        float g = texture2D(tDiffuse, uv).g;
        float b = texture2D(tDiffuse, uv + vec2(bOff,0.0)).b;
        gl_FragColor = vec4(r,g,b,1.0);
      }`
  }), 'tDiffuse');
  chromatic.renderToScreen = true; composer.addPass(chromatic);
  progress();

  // Simple HUD update
  function updateHUD(){
    hud.hpFill.style.width = Math.max(0,(player.hp/100)*100) + '%';
    hud.ammo.textContent = player.ammo + ' / 12';
    hud.level.textContent = player.level;
    hud.score.textContent = player.score;
  }

  // Input & Controls (pointer lock)
  const controls = new PointerLockControls(camera, document.body);
  document.getElementById('startBtn').onclick = ()=>{
    document.getElementById('mainMenu').style.display='none';
    controls.lock();
  };
  controls.addEventListener('lock', ()=> { document.getElementById('controlsTip').style.display='none'; });
  controls.addEventListener('unlock', ()=> { document.getElementById('controlsTip').style.display='block'; document.getElementById('mainMenu').style.display='flex'; });

  // Movement state
  const keys = {w:0,s:0,a:0,d:0,space:0};
  addEventListener('keydown', e=>{
    if(e.code==='KeyW') keys.w=1;
    if(e.code==='KeyS') keys.s=1;
    if(e.code==='KeyA') keys.a=1;
    if(e.code==='KeyD') keys.d=1;
    if(e.code==='Space') keys.space=1;
    if(e.code==='Escape') controls.unlock();
  });
  addEventListener('keyup', e=>{
    if(e.code==='KeyW') keys.w=0;
    if(e.code==='KeyS') keys.s=0;
    if(e.code==='KeyA') keys.a=0;
    if(e.code==='KeyD') keys.d=0;
    if(e.code==='Space') keys.space=0;
  });

  // Shooting
  let lastShot = 0;
  document.addEventListener('mousedown', e=>{
    shoot();
  });

  function shoot(){
    const now = performance.now();
    if(now - lastShot < 140) return;
    lastShot = now;
    if(player.ammo <= 0){ playSfx('dry'); return; }
    player.ammo--;
    updateHUD();
    // Raycast from camera
    const raycaster = new THREE.Raycaster(camera.getWorldPosition(new THREE.Vector3()), camera.getWorldDirection(new THREE.Vector3()), 0, 80);
    const hit = raycaster.intersectObjects(enemies, false)[0];
    if(hit){
      hit.object.userData.hp -= 12;
      spawnParticles(hit.point, 'explosion');
      screenShake(0.25);
      playSfx('hit', hit.point);
      if(hit.object.userData.hp <= 0){
        killEnemy(hit.object);
      }
    } else {
      spawnParticles(camera.position.clone().add(camera.getWorldDirection(new THREE.Vector3()).multiplyScalar(8)), 'sparks');
      playSfx('shoot');
    }
  }

  // Basic audio sfx generator
  const sfx = {};
  function genSfx(){
    // very small procedural oscillator bursts
    sfx.shoot = ()=>{ beep(800,0.08,0.2); }
    sfx.hit = ()=>{ beep(200,0.06,0.5,'saw'); }
    sfx.explosion = ()=>{ beep(80,0.36,1.0,'noise'); }
    sfx.dry = ()=>{ beep(1200,0.04,0.02); }
    sfx.pick = ()=>{ beep(1400,0.06,0.08); }
  }
  genSfx();

  // Play sfx by name (optionally positional)
  function playSfx(name, pos){
    try{
      if(!sfx[name]) return;
      sfx[name]();
    }catch(e){console.warn(e)}
  }

  // spawnParticles: simple point sparks
  function spawnParticles(origin, type){
    for(let i=0;i<6;i++){
      const p = particlePool.find(x=>!x.visible);
      if(!p) break;
      p.visible = true;
      const pos = p.geometry.attributes.position.array;
      pos[0] = origin.x; pos[1]=origin.y; pos[2]=origin.z;
      p.geometry.attributes.position.needsUpdate = true;
      p.userData = {life: 0.45 + Math.random()*0.45, vel: new THREE.Vector3((Math.random()-0.5)*3,(Math.random()-0.5)*3,(Math.random()-0.5)*3).multiplyScalar(type==='explosion'?1.6:0.6)};
    }
  }

  // Particle update loop
  function updateParticles(dt){
    for(let p of particlePool){
      if(!p.visible) continue;
      p.userData.life -= dt;
      if(p.userData.life <= 0){ p.visible=false; continue; }
      const pos = p.geometry.attributes.position.array;
      pos[0] += p.userData.vel.x*dt*6; pos[1] += p.userData.vel.y*dt*6; pos[2] += p.userData.vel.z*dt*6;
      p.geometry.attributes.position.needsUpdate = true;
      // fade
      p.material.opacity = Math.max(0, p.userData.life);
    }
  }

  // AI: chase player
  function updateEnemies(dt){
    for(const e of enemies){
      if(e.userData.dead) continue;
      const dir = new THREE.Vector3().subVectors(playerGroup.position, e.position);
      const dist = dir.length();
      dir.normalize();
      // movement
      if(dist > 2.0) e.position.addScaledVector(dir, e.userData.speed * dt);
      // simple attack
      if(dist < 1.8) {
        player.hp -= dt * 6; // damage over time while close
        if(player.hp <= 0) gameOver();
      }
      // bob and rotate
      e.rotation.x += dt * 0.6;
      e.position.y = 0.6 + Math.sin(performance.now()/500 + e.id)*0.25;
    }
  }

  function killEnemy(obj){
    obj.userData.dead = true;
    obj.visible = false;
    spawnParticles(obj.position.clone(), 'explosion');
    playSfx('explosion');
    player.score += 150;
    updateHUD();
  }

  // pickups collect
  function checkPickups(){
    for(let p of pickups){
      if(!p.visible) p.visible=true;
      const d = p.position.distanceTo(playerGroup.position);
      if(d<1.2){
        // collect
        p.visible=false;
        player.ammo = Math.min(12, player.ammo + 4);
        player.score += 50;
        playSfx('pick');
        updateHUD();
      }
    }
  }

  // Screen shake function (temporary camera offset)
  let shake = {time:0, power:0};
  function screenShake(power){
    shake.time = 0.35; shake.power = Math.max(shake.power, power);
  }

  // Simple helper: spawn "volumetric god ray" using light cone (cheap)
  function spawnGodRays(){
    const coneGeo = new THREE.ConeGeometry(6,20,32,1,true);
    const mat = new THREE.MeshBasicMaterial({color:0xfff4d8, transparent:true, opacity:0.045, depthWrite:false});
    const cone = new THREE.Mesh(coneGeo, mat);
    cone.position.copy(sun.position).multiplyScalar(0.5);
    cone.lookAt(new THREE.Vector3(0,0,0));
    scene.add(cone);
    setTimeout(()=> scene.remove(cone), 3500);
  }

  // Screen Resize
  function onResize(){
    camera.aspect = innerWidth/innerHeight; camera.updateProjectionMatrix();
    renderer.setSize(innerWidth, innerHeight);
    composer.setSize(innerWidth, innerHeight);
    fxaaPass.material.uniforms['resolution'].value.set(1/innerWidth,1/innerHeight);
  }
  addEventListener('resize', onResize);
  onResize();

  // Basic game states
  let last = performance.now();
  let running = true;
  let timeAccum = 0;
  updateHUD();

  // Game loop
  function loop(){
    if(!running) return;
    const now = performance.now();
    const dt = Math.min(0.05, (now - last)/1000); last = now;

    // day/night cycle
    const t = performance.now()*0.00008;
    const sunAngle = t * Math.PI * 2;
    sun.position.set(Math.sin(sunAngle)*30, Math.cos(sunAngle)*20, Math.cos(sunAngle)*16);
    sun.intensity = Math.max(0.2, Math.cos(sunAngle)*1.4);
    hud.weather.textContent = (sun.position.y>0? 'Day' : 'Night');

    // Movement input
    const speed = 6;
    const forward = new THREE.Vector3();
    controls.getDirection(forward);
    forward.y = 0; forward.normalize();
    const right = new THREE.Vector3().crossVectors(camera.up, forward).normalize();

    let move = new THREE.Vector3();
    if(keys.w) move.add(forward);
    if(keys.s) move.sub(forward);
    if(keys.a) move.add(right);
    if(keys.d) move.sub(right);
    move.normalize().multiplyScalar(speed * dt);
    playerGroup.position.add(move);
    camera.position.copy(playerGroup.position).add(new THREE.Vector3(0,1.4,0));

    // update enemies and particles
    updateEnemies(dt);
    updateParticles(dt);
    checkPickups();

    // simple fog-based LOD: hide distant enemies based on distance from camera
    for(const e of enemies){
      const d = camera.position.distanceTo(e.position);
      e.visible = (!e.userData.dead) && d < 220;
      // reduce render cost: simple scale down for distant
      e.scale.setScalar(Math.max(0.5, 1.0 - d/400));
    }

    // Screen shake effect
    if(shake.time > 0){
      shake.time -= dt;
      const s = shake.power * (shake.time / 0.35);
      camera.position.x += (Math.random()-0.5)*s;
      camera.position.y += (Math.random()-0.5)*s*0.4;
    } else {
      shake.power = 0;
    }

    // composer render (postprocessing)
    composer.render();

    // simple performance stat
    document.getElementById('fps')?.remove();
    const fpsEl = document.createElement('div'); fpsEl.id='fps'; fpsEl.style.position='fixed'; fpsEl.style.left='20px'; fpsEl.style.bottom='20px'; fpsEl.style.zIndex=60; fpsEl.className='small'; fpsEl.textContent = Math.round(1/dt) + ' FPS'; document.body.appendChild(fpsEl);

    requestAnimationFrame(loop);
  }

  // Start the render loop once ready
  loading.style.display='none';
  last = performance.now();
  loop();

  // Utility & helpers below -------------------------------------------------

  function makeNoiseCanvas(a,b){
    const s = 512;
    const c = document.createElement('canvas'); c.width=c.height=s;
    const ctx = c.getContext('2d');
    ctx.fillStyle = a; ctx.fillRect(0,0,s,s);
    for(let i=0;i<1500;i++){
      ctx.fillStyle = (Math.random()>0.5?b:a);
      ctx.fillRect(Math.random()*s,Math.random()*s,Math.random()*3,Math.random()*3);
    }
    return c;
  }
  function makeNormalCanvas(){
    const s = 256; const c = document.createElement('canvas'); c.width=c.height=s; const ctx = c.getContext('2d');
    ctx.fillStyle='#8080ff'; ctx.fillRect(0,0,s,s);
    // draw some bumps
    for(let i=0;i<60;i++){
      ctx.fillStyle = 'rgba(128,128,'+(Math.floor(120+Math.random()*80))+',0.35)';
      ctx.beginPath(); ctx.arc(Math.random()*s,Math.random()*s,10+Math.random()*20,0,Math.PI*2); ctx.fill();
    }
    return c;
  }
  function makeSprite(){
    const s = 64; const c = document.createElement('canvas'); c.width=c.height=s; const ctx = c.getContext('2d');
    const g = ctx.createRadialGradient(32,32,0,32,32,32); g.addColorStop(0,'rgba(255,255,255,1)'); g.addColorStop(0.2,'rgba(255,220,180,0.9)'); g.addColorStop(1,'rgba(255,80,0,0)');
    ctx.fillStyle = g; ctx.fillRect(0,0,s,s); return new THREE.CanvasTexture(c);
  }

  function deformTerrain(geo){
    const pos = geo.attributes.position;
    for(let i=0;i<pos.count;i++){
      const x = pos.getX(i), y = pos.getY(i);
      const nx = 0.02 * x, nz = 0.02 * y;
      const h = Math.sin(nx*1.2)*Math.cos(nz*1.1) * 3.0 + (Math.random()-0.5)*0.6;
      pos.setZ(i, h);
    }
    pos.needsUpdate = true;
    geo.computeVertexNormals();
  }

  // Simple procedural beep SFX generator
  function beep(freq, dur=0.1, vol=0.1, type='sine'){
    const o = audioCtx.createOscillator();
    const g = audioCtx.createGain();
    const now = audioCtx.currentTime;
    o.type = type; o.frequency.setValueAtTime(freq,now);
    g.gain.setValueAtTime(vol, now);
    g.gain.exponentialRampToValueAtTime(0.001, now + dur);
    o.connect(g); g.connect(audioCtx.destination);
    o.start(now); o.stop(now + dur);
  }

  // Game over and victory
  function gameOver(){
    running=false;
    showOverlay('GAME OVER', `Score: ${player.score}\nRefresh to play again`, ['Restart']);
    playSfx('explosion');
  }

  function showOverlay(title, text, buttons=['OK']){
    const overlay = document.getElementById('overlay');
    overlay.style.display='flex'; overlay.style.pointerEvents='auto';
    overlay.innerHTML = `<div class="modal"><div style="font-size:22px;font-weight:900">${title}</div><div class="small-note" style="white-space:pre-line;margin-top:6px">${text}</div><div style="margin-top:12px;display:flex;gap:10px;justify-content:center">${buttons.map((b,i)=>`<button class="btn" id="ovbtn${i}">${b}</button>`).join('')}</div></div>`;
    buttons.forEach((b,i)=>document.getElementById('ovbtn'+i).onclick = ()=>{ overlay.style.display='none'; running=true; last = performance.now(); loop(); });
  }

  // Minimal saving to localStorage
  function saveState(){
    const state = {hp:player.hp,ammo:player.ammo,score:player.score,level:player.level};
    localStorage.setItem('neon_save', JSON.stringify(state));
  }

  function loadState(){
    const s = localStorage.getItem('neon_save');
    if(!s) return;
    const st = JSON.parse(s);
    player.hp = st.hp; player.ammo = st.ammo; player.score = st.score; player.level = st.level;
    updateHUD();
  }

  // Periodically autosave
  setInterval(saveState, 6000);

  // small progress completion mark
  progress(); // final progress increment to remove loading overlay
}
</script>

</body>
</html>