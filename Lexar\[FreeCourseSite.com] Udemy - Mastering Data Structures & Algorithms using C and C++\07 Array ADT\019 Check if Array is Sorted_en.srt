1
00:00:00,390 --> 00:00:09,460
Let us look at a few more operations on an area like inserting, and we want to insert a new value in

2
00:00:09,780 --> 00:00:13,880
UTI, which is already Sloggett, so we should add a new value order for that position.

3
00:00:14,220 --> 00:00:16,930
The next one is checking if an array is already sorted.

4
00:00:17,610 --> 00:00:23,790
Then one more are changing all negative elements on the left side and positive elements on the right

5
00:00:23,790 --> 00:00:24,370
hand side.

6
00:00:25,080 --> 00:00:26,440
So let us check these out.

7
00:00:27,810 --> 00:00:31,080
So these are a few simple operations, but that are important operations.

8
00:00:31,080 --> 00:00:33,120
So let us check how to perform them.

9
00:00:33,780 --> 00:00:40,530
Now, the first operation, I want to insert a new element, 18 in this area and this area, if you

10
00:00:40,530 --> 00:00:41,990
observe it, is already sorted.

11
00:00:43,120 --> 00:00:49,450
So in a certain area, I want to insert a new element to such that it is inserted and it's assorted

12
00:00:49,450 --> 00:00:49,790
Polish.

13
00:00:50,500 --> 00:00:52,240
So where this it should come?

14
00:00:53,560 --> 00:00:57,040
So if you check here, it is greater than for greater than eight.

15
00:00:57,340 --> 00:00:59,980
Greater than 13, greater than 16.

16
00:01:00,220 --> 00:01:01,630
But less than twenty.

17
00:01:01,630 --> 00:01:02,820
Eighteen is less than 20.

18
00:01:02,830 --> 00:01:04,780
So it should come here.

19
00:01:06,520 --> 00:01:07,980
This is the place for eating.

20
00:01:09,490 --> 00:01:11,020
So I have to insert it in there.

21
00:01:11,200 --> 00:01:13,180
But already some number is present there.

22
00:01:13,360 --> 00:01:16,540
So I have to shift the element and make freespace for it.

23
00:01:16,720 --> 00:01:18,010
Then only I can insert it.

24
00:01:18,550 --> 00:01:22,900
So for shifting, I should start from last element.

25
00:01:22,900 --> 00:01:26,980
I should shift 33, then 38 and twenty five, then 20.

26
00:01:27,250 --> 00:01:29,840
Then I'll get a free space, then I can insert this one.

27
00:01:30,040 --> 00:01:35,280
Now let us look at the exact procedure for inserting a new element 18 in this list.

28
00:01:35,290 --> 00:01:38,710
I don't have to find out its position by checking these numbers.

29
00:01:39,850 --> 00:01:43,000
I can simply start checking this number found by the last.

30
00:01:44,050 --> 00:01:48,220
I can simply start checking this number from the last number onwards.

31
00:01:48,610 --> 00:01:51,640
If those numbers are greater than this one, I can shift them.

32
00:01:52,780 --> 00:01:55,480
If suppose this is not 18, this is 38.

33
00:01:55,600 --> 00:01:57,010
So this is greater than 33.

34
00:01:57,010 --> 00:01:58,870
So I can simply store thirty eight here.

35
00:01:59,770 --> 00:02:03,970
But this number is a smaller so I can start shifting them so.

36
00:02:05,820 --> 00:02:12,290
So Calitri has to be shifted and indeed has to be shifted and 25 has to be shifted to and is also shifted,

37
00:02:12,690 --> 00:02:15,090
then this number is 16.

38
00:02:15,270 --> 00:02:17,280
So 16 is a smaller than 18.

39
00:02:17,410 --> 00:02:19,350
So the next place is 18 only.

40
00:02:19,770 --> 00:02:22,980
So I can insert it in here that follows the procedure.

41
00:02:23,370 --> 00:02:28,770
The important thing that we have to observe here is that, you know, need to find the position of this

42
00:02:28,770 --> 00:02:32,650
element by comparing these elements because of the competition.

43
00:02:32,690 --> 00:02:36,180
Also, you have to shift them so that start shifting them.

44
00:02:37,160 --> 00:02:42,920
Now, let us see the procedure, how I can shift the elements and I can insert this new element, let

45
00:02:42,920 --> 00:02:49,820
us call this element X, so let us say it is 18, for example, then I should start shifting from last.

46
00:02:50,030 --> 00:02:53,110
So from here I should start.

47
00:02:53,420 --> 00:02:56,870
So let us call that I as Lenthall minus one.

48
00:02:57,290 --> 00:02:59,540
That is Lento's the number of elements in the list.

49
00:02:59,870 --> 00:03:00,940
That is eight.

50
00:03:01,130 --> 00:03:02,200
So eight minus one.

51
00:03:02,210 --> 00:03:02,890
That is seven.

52
00:03:02,900 --> 00:03:06,470
So I start from seven now repeatedly.

53
00:03:06,470 --> 00:03:12,200
What I have to do, I have to go on shifting the elements, so I have to go on shifting the element.

54
00:03:12,220 --> 00:03:15,720
So that is why loop I will do how to shift an element.

55
00:03:15,940 --> 00:03:18,530
This if I should be cappelletti of a plus one.

56
00:03:18,890 --> 00:03:24,260
So aof I plus one should contain a lot of money.

57
00:03:25,850 --> 00:03:29,150
So I have to shift this shifting has to be done repeatedly.

58
00:03:29,150 --> 00:03:29,810
How long.

59
00:03:30,260 --> 00:03:34,280
As long as this X is smaller than that element.

60
00:03:34,940 --> 00:03:40,720
If this becomes greater than I should stop, if this become greater or equal, I should stop.

61
00:03:41,600 --> 00:03:51,320
So as long as AOF II is greater than X and after shifting in element I should also degremont I.

62
00:03:51,440 --> 00:03:55,610
That is the agreement I so I minus Wanis.

63
00:03:55,880 --> 00:03:58,040
So using my loop I have written the code.

64
00:03:58,040 --> 00:03:59,870
Even I can use a follow up also.

65
00:04:00,050 --> 00:04:05,330
But for loop is useful when you know how many times you are going to repeat and while loop we don't

66
00:04:05,330 --> 00:04:07,460
know how many times you are going to repeat this one.

67
00:04:07,730 --> 00:04:13,550
We have to repeat based on the condition that this element is smaller than the element shifted.

68
00:04:14,360 --> 00:04:21,350
So I have used Vilem now once this has finished, so if this loop is executed, then it will move to

69
00:04:21,470 --> 00:04:27,230
three, then also twenty then twenty five then brinda then it when it comes here it will stop.

70
00:04:27,350 --> 00:04:35,030
I will stop here because this element that is aof i.e. it's not greater than eighty, so this is not

71
00:04:35,030 --> 00:04:35,630
greater than that.

72
00:04:35,750 --> 00:04:36,450
So it is false.

73
00:04:36,490 --> 00:04:37,100
It will stop.

74
00:04:37,400 --> 00:04:40,520
So when I should insert this new element E-Plus Funplex.

75
00:04:40,520 --> 00:04:46,160
So at aof I plus one I should insert the element X.

76
00:04:47,830 --> 00:04:55,540
That's it, this is the pseudocode for inserting an element in that list, and it is one of the important

77
00:04:55,540 --> 00:04:56,300
procedures.

78
00:04:57,070 --> 00:05:01,950
Now let us move to the next one, checking whether the list is already sorted or not.

79
00:05:02,260 --> 00:05:07,360
Let us see how to check that list is already sorted or not here.

80
00:05:07,360 --> 00:05:10,470
As an example, already I have a sorted list with me.

81
00:05:11,140 --> 00:05:15,200
So the procedure for checking that it is sorted on notice start from this one.

82
00:05:16,090 --> 00:05:20,350
This is a smaller than this and this is smaller than the next one, but this is smaller than the next

83
00:05:20,350 --> 00:05:20,740
number.

84
00:05:21,040 --> 00:05:25,180
So every number is smaller than the next number.

85
00:05:25,660 --> 00:05:27,740
Then it means the list is sorted.

86
00:05:28,420 --> 00:05:37,690
If suppose here I have twenty six then four is smaller than eight, eight is smaller than 13.

87
00:05:37,690 --> 00:05:40,290
Today is smaller than twenty six years up to here.

88
00:05:41,080 --> 00:05:44,040
Twenty six is smaller than pretty new wrong.

89
00:05:44,470 --> 00:05:46,600
So it means the list is not sorted.

90
00:05:47,800 --> 00:05:53,890
So the procedure is check the current number with the next number if it is smaller going continuing

91
00:05:54,130 --> 00:05:56,610
and if you have reached the end of a list.

92
00:05:57,010 --> 00:06:01,680
So finally in the procedure we will check the current number is a smaller than the next number.

93
00:06:02,050 --> 00:06:05,140
If not, then it is not sorted.

94
00:06:06,100 --> 00:06:12,730
If we have reached at the end of a list by comparing this means it is sorted, I would write like an

95
00:06:12,730 --> 00:06:16,390
algorithm that USSI algorithm isn't sorted.

96
00:06:16,390 --> 00:06:22,060
I'm calling the algorithm as is sorted and it takes an array and the number of elements.

97
00:06:23,290 --> 00:06:30,670
No, we have to start checking from first element next, the next so on, so until where we should continue,

98
00:06:31,000 --> 00:06:36,160
we should stop here because we cannot compare it with any other number.

99
00:06:36,610 --> 00:06:36,850
Right.

100
00:06:37,150 --> 00:06:40,390
So love to say there are eight elements and there's eight.

101
00:06:40,540 --> 00:06:41,200
This is eight.

102
00:06:41,200 --> 00:06:42,240
So we should stop here.

103
00:06:42,460 --> 00:06:44,400
No, we have to stop before this one.

104
00:06:44,680 --> 00:06:46,780
So we should stop at minus two police.

105
00:06:47,050 --> 00:06:48,940
So using force, look, we can do this one.

106
00:06:49,210 --> 00:06:55,390
So far it takes the value zero I is less than number of elements.

107
00:06:55,720 --> 00:07:00,730
No, usually we reach a deal here when we say and but we have to stop before that.

108
00:07:00,730 --> 00:07:01,930
So and the minus one.

109
00:07:03,100 --> 00:07:03,970
Plus plus.

110
00:07:06,100 --> 00:07:15,130
That in this what we have to check for, is there any fear of elements where the first one is greater

111
00:07:15,130 --> 00:07:19,890
than the second one, that means the list is not so in check for false condition.

112
00:07:20,410 --> 00:07:30,540
If AOF, i.e., is greater than Elfy plus one, any number is greater than the next number, then return

113
00:07:31,510 --> 00:07:34,860
false means that it's just not sorted.

114
00:07:35,680 --> 00:07:39,000
We got this condition satisfied, then we return false.

115
00:07:39,400 --> 00:07:42,760
This does not photic and that's all end of loop.

116
00:07:44,170 --> 00:07:51,500
If we have completed the whole uhry completely and we never came across as such a pair of elements.

117
00:07:51,880 --> 00:07:55,030
We have finished the remains with the socket.

118
00:07:55,270 --> 00:08:00,890
So from here we can see right on through that means list is sorted.

119
00:08:01,750 --> 00:08:06,750
So if at all, any time this condition is true, it will stop and say false.

120
00:08:07,450 --> 00:08:10,330
Otherwise it has reached the end and then it will return.

121
00:08:10,720 --> 00:08:11,110
True.

122
00:08:11,890 --> 00:08:15,490
So in this case, it will check Ivied eight plus one.

123
00:08:16,420 --> 00:08:18,430
This is not great to continue.

124
00:08:19,180 --> 00:08:22,140
This is not greater than this one ayari this one.

125
00:08:22,270 --> 00:08:23,580
This is not better than that one.

126
00:08:23,830 --> 00:08:24,460
Continue.

127
00:08:24,790 --> 00:08:26,770
This is not greater than next one.

128
00:08:27,310 --> 00:08:28,350
OK, continue.

129
00:08:28,750 --> 00:08:30,410
This is greater than this one.

130
00:08:30,430 --> 00:08:31,980
Yes, this is greater than this one.

131
00:08:32,200 --> 00:08:36,100
So return false is suppose this is not a twenty six.

132
00:08:36,100 --> 00:08:36,700
This is six.

133
00:08:36,940 --> 00:08:38,720
Only nine is completely sorted.

134
00:08:38,740 --> 00:08:40,350
So it will check this one with this one.

135
00:08:40,720 --> 00:08:41,080
Right.

136
00:08:41,409 --> 00:08:44,410
So this is not greater than this is not better than this one.

137
00:08:44,710 --> 00:08:45,930
This is not greater than this one.

138
00:08:45,940 --> 00:08:47,800
This is not greater than this one is not greater.

139
00:08:48,400 --> 00:08:51,560
Not greater than so in here it will check not greater.

140
00:08:51,790 --> 00:08:57,320
So here it will come out of the loop and see through the list is already sorted.

141
00:08:57,760 --> 00:09:03,380
So this is also useful, like before sorting the elements to conform, if they are sorted or not, we

142
00:09:03,400 --> 00:09:04,670
run this procedure.

143
00:09:05,020 --> 00:09:07,570
So how much time this procedure is taking it?

144
00:09:07,570 --> 00:09:13,300
Is it reading through all the list of elements then it is taking outdraw and time.

145
00:09:14,640 --> 00:09:20,460
If the list is sorted, it will take and if it is not sorted, then it may stop at any place that is

146
00:09:20,790 --> 00:09:23,010
minimum one competition it performs.

147
00:09:23,160 --> 00:09:26,100
So minimum time is constant, maximum.

148
00:09:26,130 --> 00:09:26,950
Times are tough.

149
00:09:26,970 --> 00:09:31,960
And now next, we will see negative numbers on one side, positive for another five.

150
00:09:34,360 --> 00:09:40,680
Now, the next problem we have on our list, which is having positive as well as negative numbers,

151
00:09:41,720 --> 00:09:46,000
there's an example list, it is having mixed positive as well as negative number.

152
00:09:46,870 --> 00:09:51,660
What we want to do is we want to bring all negative numbers on left hand side.

153
00:09:51,670 --> 00:09:54,690
And followed by that, we should have positive numbers.

154
00:09:55,060 --> 00:09:59,320
So in the beginning of January, we should have all negative than one and negatives have finished then

155
00:09:59,320 --> 00:10:00,420
positive should stop.

156
00:10:00,610 --> 00:10:01,820
So but they are mixed.

157
00:10:01,960 --> 00:10:03,880
So how to bring them back to one?

158
00:10:03,880 --> 00:10:06,040
Please let us see how we can do it.

159
00:10:06,910 --> 00:10:12,580
So for this, we can take two pointers that this index points.

160
00:10:12,970 --> 00:10:18,300
One is I from the beginning of a list and one is gee, from the end of a list.

161
00:10:18,820 --> 00:10:20,890
So I from here and from here.

162
00:10:21,980 --> 00:10:28,400
Then using I will look for positive numbers if there are any positive numbers on this side.

163
00:10:28,760 --> 00:10:29,860
Let us send them there.

164
00:10:30,230 --> 00:10:34,760
And with the GAO, we will find out if there are any negative numbers on this site.

165
00:10:34,760 --> 00:10:41,960
So let us send them here so I will try to find a positive number and try to find a negative number.

166
00:10:41,990 --> 00:10:44,050
If found, they will exchange.

167
00:10:44,330 --> 00:10:46,960
So I will also simultaneously write on the code.

168
00:10:47,570 --> 00:10:55,540
I is the starting from zero and the GS is starting from lente of a list minus one that is from there.

169
00:10:56,990 --> 00:10:58,700
Then what we have to do check.

170
00:10:58,700 --> 00:10:59,870
Is it positive?

171
00:10:59,870 --> 00:11:02,130
If it is a positive then we have to send it there.

172
00:11:02,360 --> 00:11:03,620
No this is negative.

173
00:11:03,830 --> 00:11:05,090
So move next.

174
00:11:05,810 --> 00:11:07,040
Is it positive.

175
00:11:07,070 --> 00:11:15,440
Yes, we got the number so it means I should continue incrementing until it reaches a positive number.

176
00:11:15,450 --> 00:11:19,790
So if it is negative it should just move ahead so I can write.

177
00:11:21,430 --> 00:11:34,810
While eight of IHI is less than zero, continue placeless, so when the number is negative, let I move

178
00:11:34,810 --> 00:11:35,200
ahead.

179
00:11:35,320 --> 00:11:36,820
If it is positive, it's top.

180
00:11:36,860 --> 00:11:43,210
So, yes, we got positive that the same we should be discriminated until it gets a negative number.

181
00:11:43,240 --> 00:11:43,960
Is it negative?

182
00:11:43,990 --> 00:11:44,380
No.

183
00:11:44,470 --> 00:11:44,920
No.

184
00:11:45,220 --> 00:11:45,970
Is it negative?

185
00:11:46,070 --> 00:11:46,420
No.

186
00:11:46,810 --> 00:11:49,670
Stop means Jaisha DiClemente.

187
00:11:49,700 --> 00:11:50,770
The number is positive.

188
00:11:50,770 --> 00:11:52,800
Should stop if the number is negative.

189
00:11:53,260 --> 00:12:05,060
So while eight of G is greater than or equal to zero G minus minus.

190
00:12:05,440 --> 00:12:10,430
So if it is less than zero, it should stop since we got the numbers.

191
00:12:10,510 --> 00:12:13,130
Then what we should do is to change them.

192
00:12:13,150 --> 00:12:14,910
These numbers should begin to change.

193
00:12:15,190 --> 00:12:20,190
So that's minus four is brought here and three incentive there.

194
00:12:20,440 --> 00:12:21,580
So we should stop them.

195
00:12:21,770 --> 00:12:28,930
So I was right on slap eight of i.e. a eight of G.

196
00:12:30,900 --> 00:12:32,140
I should do the same thing.

197
00:12:32,710 --> 00:12:33,370
I plus.

198
00:12:33,370 --> 00:12:35,260
Plus is it a positive.

199
00:12:35,260 --> 00:12:37,200
No, no I placeless.

200
00:12:37,210 --> 00:12:37,920
Is it possible.

201
00:12:37,930 --> 00:12:38,340
No.

202
00:12:38,400 --> 00:12:38,910
Yes.

203
00:12:39,280 --> 00:12:41,410
So this loop has finished then.

204
00:12:41,650 --> 00:12:43,980
Decrement G G minus minus.

205
00:12:43,990 --> 00:12:45,070
Is it a negative number.

206
00:12:45,070 --> 00:12:46,780
No J minus minus.

207
00:12:46,790 --> 00:12:47,870
Is it a negative number.

208
00:12:48,340 --> 00:12:48,870
Yes.

209
00:12:49,690 --> 00:12:55,750
So just agreement that until we got the negative number then interchange them into change.

210
00:12:56,020 --> 00:12:59,220
So minus nine comes here and then goes down.

211
00:13:00,250 --> 00:13:01,150
Now continue.

212
00:13:01,360 --> 00:13:03,640
I plus plus is a positive number.

213
00:13:03,700 --> 00:13:04,300
Yes.

214
00:13:04,660 --> 00:13:11,140
The number is not negative so I stop there then J minus minus.

215
00:13:11,390 --> 00:13:12,540
Is it a negative number.

216
00:13:12,640 --> 00:13:13,230
Yes.

217
00:13:13,600 --> 00:13:15,580
So when the number is negative it stops.

218
00:13:15,820 --> 00:13:17,350
So then again swap them.

219
00:13:17,470 --> 00:13:21,040
So minus seven comes here and five goes down.

220
00:13:23,620 --> 00:13:27,250
Then continue I placeless as redeposited.

221
00:13:27,280 --> 00:13:27,620
No.

222
00:13:27,880 --> 00:13:29,620
Yes, it's a positive no stop.

223
00:13:30,160 --> 00:13:30,990
Minus minus.

224
00:13:31,270 --> 00:13:32,420
Is it a negative number?

225
00:13:32,710 --> 00:13:39,130
Yes, negative numbers stop, but not we will not interchange them because I have to become greater

226
00:13:39,130 --> 00:13:39,990
than Jim means.

227
00:13:40,000 --> 00:13:42,330
We have checked the entire list.

228
00:13:43,000 --> 00:13:46,470
So this swopping we will not do and we will stop the procedure.

229
00:13:46,780 --> 00:13:49,150
So the swapping is done if I.

230
00:13:49,150 --> 00:13:50,900
Is less than Jane.

231
00:13:51,610 --> 00:13:52,750
So this is conditional.

232
00:13:54,190 --> 00:14:02,110
So moving and moving, moving, moving, Jan, into changing the element was repeating procedure, how

233
00:14:02,110 --> 00:14:10,180
long we have done this, as long as I is less than G while I is less than June.

234
00:14:12,130 --> 00:14:15,460
So this is the procedure for swapping the elements.

235
00:14:16,960 --> 00:14:24,460
So this is the procedure for arranging all the negative on one side and positive one other side announces

236
00:14:24,460 --> 00:14:25,900
how much time it has taken.

237
00:14:26,470 --> 00:14:34,130
The time was spent on comparing the elements so total, how many elements were compared and the plus

238
00:14:34,220 --> 00:14:35,560
two elements compared.

239
00:14:35,590 --> 00:14:42,490
We started from this with a and from there also we started with the T and I has increased and checked

240
00:14:42,490 --> 00:14:43,180
one more number.

241
00:14:43,210 --> 00:14:46,200
They also decrease and check one more number.

242
00:14:46,510 --> 00:14:50,500
So total and plus two competitions and then swapping.

243
00:14:50,830 --> 00:14:56,770
It depends if all of the all the negatives on this side and positive that side, no slapping will be

244
00:14:56,770 --> 00:14:57,030
done.

245
00:14:57,700 --> 00:15:00,570
But the meantime is spent on comparing the elements.

246
00:15:00,610 --> 00:15:06,490
So we can say that times are tough and this is scanning the whole list just once.

247
00:15:06,820 --> 00:15:12,430
An exact number of competitions are and plus two competitions now analysis.

248
00:15:13,150 --> 00:15:14,440
What is the work done here?

249
00:15:14,560 --> 00:15:20,320
Comparing the element and into changing them into changing is happening if there is a conflict in the

250
00:15:20,320 --> 00:15:26,460
condition and the changing is happening only if positive is on the side of negative on that side.

251
00:15:27,010 --> 00:15:29,040
Otherwise competition is always done.

252
00:15:29,050 --> 00:15:31,100
So at most, how many elements are compared?

253
00:15:31,390 --> 00:15:32,930
All and elements are coming.

254
00:15:33,310 --> 00:15:37,390
So the time taken by this procedure is out of order.

255
00:15:37,570 --> 00:15:43,190
And so therefore all these very few important operations that are performed on the list.

256
00:15:43,210 --> 00:15:44,480
So we have seen them on.

