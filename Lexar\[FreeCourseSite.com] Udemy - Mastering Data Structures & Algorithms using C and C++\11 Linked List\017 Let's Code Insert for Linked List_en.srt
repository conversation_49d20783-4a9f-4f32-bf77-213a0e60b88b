1
00:00:00,150 --> 00:00:06,689
In this video, we will write a function for inserting a new element in a link list, as we have already

2
00:00:06,840 --> 00:00:12,060
discussed, there are two cases of insertion that is inserting before Fastenal or inserting at any other

3
00:00:12,060 --> 00:00:12,850
given position.

4
00:00:12,870 --> 00:00:17,420
So I will write on a function for insert, and this is the same project that we are continuing.

5
00:00:17,460 --> 00:00:21,300
We have already done all the previous functions inside the same program.

6
00:00:21,320 --> 00:00:24,360
This is having all those functions now here about the main function.

7
00:00:24,360 --> 00:00:31,520
I will write on insert function void, insert this insert function should take on the two of US node,

8
00:00:31,530 --> 00:00:37,710
so let us call it as be as a pointer to a footnote then index at which we want to insert and the value

9
00:00:37,710 --> 00:00:39,110
that we want to know.

10
00:00:39,150 --> 00:00:44,940
The first thing I have to check whether the index given is valid or not indexed will be valid if it

11
00:00:44,940 --> 00:00:49,000
is greater than or equal to zero and less than or equal to lend.

12
00:00:49,740 --> 00:00:51,960
So for that I will write on the condition here.

13
00:00:51,960 --> 00:00:55,080
If index is less than zero, then it means it is invalid.

14
00:00:55,080 --> 00:01:00,270
I cannot continue or if index is greater than the end of a long list, then it is invalid.

15
00:01:00,270 --> 00:01:05,700
And how know the length of the links so far, finding the length of willingness we should on the number

16
00:01:05,700 --> 00:01:06,270
of nodes.

17
00:01:06,270 --> 00:01:09,300
So for count, we already have a function present here.

18
00:01:09,310 --> 00:01:14,850
We have already done a function count which takes pointed to a footnote and it returns the length of

19
00:01:14,850 --> 00:01:17,040
a list that is number of nodes in the links.

20
00:01:17,040 --> 00:01:18,520
We can use that function here.

21
00:01:18,540 --> 00:01:20,160
So back to the sensate function.

22
00:01:20,160 --> 00:01:25,080
Say if index is greater than down and I should send a pointed to the first node does not want to go

23
00:01:25,080 --> 00:01:28,800
Fastenal B if it is greater than the index is invalid.

24
00:01:28,810 --> 00:01:31,610
So I should just stop the function by saying the return.

25
00:01:31,630 --> 00:01:35,010
Now we have checked for the validity of index the mixtures.

26
00:01:35,010 --> 00:01:39,270
If the index is valid, then we can insert a new normal for inserting a new node.

27
00:01:39,270 --> 00:01:41,130
I should first of all create a new node.

28
00:01:41,220 --> 00:01:47,880
So for creation I will take a temporary pointer e then here after this condition I will create a new

29
00:01:47,880 --> 00:01:53,970
node, ursine struck node pointer and Málaga, the function for creating a new node in here.

30
00:01:54,030 --> 00:01:58,380
So I should give the size of struct note so it will create a new normal.

31
00:01:58,410 --> 00:02:02,810
And after creating a new note, I should fill the data and then also later assign X.

32
00:02:02,820 --> 00:02:04,070
Now the node is ready now.

33
00:02:04,120 --> 00:02:08,300
Next I have to link this new node that is T in the Linkous.

34
00:02:08,550 --> 00:02:13,850
So there are two cases that if the index is less than zero then it should be on the left hand side of

35
00:02:13,890 --> 00:02:14,180
fust.

36
00:02:14,280 --> 00:02:16,240
Otherwise it should be at a given position.

37
00:02:16,260 --> 00:02:18,330
So first I will check the condition.

38
00:02:18,330 --> 00:02:23,700
If index is equal to zero, if it is equal to zero means it should be inserted as a node.

39
00:02:23,700 --> 00:02:30,720
So the procedure already we have seen that is next to should point on first and first should be pointing

40
00:02:30,720 --> 00:02:35,420
on the that these are the two steps for inserting a new node before Fastenal.

41
00:02:35,430 --> 00:02:40,200
If index is not equal to zero, then we have to insert at some given position.

42
00:02:40,200 --> 00:02:41,940
So for that I should move this pointer.

43
00:02:41,940 --> 00:02:47,040
People give an index then in certain elements before moving this, I need a follow up.

44
00:02:47,040 --> 00:02:49,560
So losing for loop, I need a variable.

45
00:02:49,560 --> 00:02:50,610
I now here.

46
00:02:50,640 --> 00:02:54,630
First of all, I should move the pointer for index minus one time.

47
00:02:54,870 --> 00:03:02,040
So far I assign zero Iceland in index minus one and I plus plus for this many times point that P should

48
00:03:02,040 --> 00:03:02,480
be moved.

49
00:03:02,520 --> 00:03:07,290
Then after that I have to make necessary links for inserting a new node.

50
00:03:07,290 --> 00:03:13,280
So these next should be these next then these next should point on it.

51
00:03:13,310 --> 00:03:13,860
That's it.

52
00:03:13,980 --> 00:03:15,890
So these are the steps for insertion.

53
00:03:15,900 --> 00:03:21,390
So as I have explained you on whiteboard, I have written the barcode now excerpting here I, I'm checking

54
00:03:21,390 --> 00:03:23,580
whether the given index is valid or not.

55
00:03:23,580 --> 00:03:25,050
Not coming to the main function.

56
00:03:25,050 --> 00:03:30,990
We have already written a create function for creating a link list for this given array of elements.

57
00:03:30,990 --> 00:03:32,340
I will remove the elements.

58
00:03:32,340 --> 00:03:37,230
I will just take three elements and this one I have it is the size so we can see it clearly how insert

59
00:03:37,230 --> 00:03:38,280
function is working.

60
00:03:38,280 --> 00:03:39,420
So there are three elements.

61
00:03:39,450 --> 00:03:40,690
This will create the links.

62
00:03:40,740 --> 00:03:46,430
So let us display this link lists and afterwards I'll show you how we can insert elements here.

63
00:03:46,500 --> 00:03:50,980
Here it is displaying three five seven display function is displaying the linker.

64
00:03:51,000 --> 00:03:54,840
So we have a list of elements three five and seven non.

65
00:03:54,840 --> 00:04:01,820
Let us try insert function after create function I will insert and I will send the pointer to a first

66
00:04:01,830 --> 00:04:03,330
node at the first pointer.

67
00:04:03,330 --> 00:04:09,000
And I want to insert a new note that integral and the value is n now after that I will display a link.

68
00:04:09,390 --> 00:04:12,750
So let us see whether the stand gets inserted before three or not.

69
00:04:12,780 --> 00:04:15,810
Yes, it is getting inserted and three, five and seven.

70
00:04:15,840 --> 00:04:17,430
I will try with two more elements.

71
00:04:17,640 --> 00:04:19,680
The elements are three, five and seven.

72
00:04:19,680 --> 00:04:21,089
So there are total three elements.

73
00:04:21,089 --> 00:04:23,970
So I will insert a new element and at the next three.

74
00:04:23,970 --> 00:04:25,440
So I have modified this index.

75
00:04:25,440 --> 00:04:29,840
It was zero now letters in so that three three minutes it should be inserted after seven.

76
00:04:29,850 --> 00:04:30,630
Let us check it.

77
00:04:30,780 --> 00:04:34,050
Yes, it is inserted after seven three five seven zero.

78
00:04:34,050 --> 00:04:34,890
Yes, it's working.

79
00:04:35,050 --> 00:04:38,490
I will try to give of invalid index in third position.

80
00:04:38,490 --> 00:04:40,910
I will say insert at eight position.

81
00:04:40,930 --> 00:04:42,750
No, I have only three elements.

82
00:04:43,080 --> 00:04:47,400
Eight position is not there so it should not insert anything and the lists should remain same.

83
00:04:47,400 --> 00:04:48,570
That is three five seven.

84
00:04:48,570 --> 00:04:54,090
Yes Linklaters same, that is three five seven and is not inserted because the index was invalid.

85
00:04:54,090 --> 00:04:57,600
So we have tried various position four insert function.

86
00:04:57,600 --> 00:04:59,910
We have inserted at different locations now.

87
00:05:00,000 --> 00:05:05,410
Using the insert function, I will try to create entire link list, so I will remove these two lines.

88
00:05:05,460 --> 00:05:09,710
Let us directly use insert function for inserting various notes.

89
00:05:09,750 --> 00:05:14,700
See the very first note that I can insert, if I can insert that index is evil because there is nothing

90
00:05:14,700 --> 00:05:15,270
in the link lists.

91
00:05:15,330 --> 00:05:20,250
If it is not index of zero, if I give some other index, then let us see what happens.

92
00:05:20,280 --> 00:05:23,850
So there is nothing to display because there are no norms at all.

93
00:05:23,880 --> 00:05:27,300
Now let me insert the sad index OSIEL and run the program.

94
00:05:27,330 --> 00:05:29,150
Yes, one element is inserted.

95
00:05:29,610 --> 00:05:33,000
I have inserted one element and Naseeruddin I have displayed it.

96
00:05:33,240 --> 00:05:36,350
So yes, it is inserting because zero is the valid index.

97
00:05:36,360 --> 00:05:40,230
I cannot insert at any other index because the Lincolnesque is empty.

98
00:05:40,500 --> 00:05:42,390
I can insert only at index zero.

99
00:05:42,420 --> 00:05:47,640
Now the next element, I can insert it at index zero as well as one.

100
00:05:47,790 --> 00:05:51,150
So I will insert the next one and I will insert value 20.

101
00:05:51,180 --> 00:05:54,840
Then next I can insert an element already I have to know.

102
00:05:54,870 --> 00:06:00,240
So I have dreamed this was available that the zero one or two so I will inserted at two a.m. insert

103
00:06:00,240 --> 00:06:01,620
element pudi Lidstrom.

104
00:06:01,620 --> 00:06:03,750
I should get the elements 10, 20 and 30.

105
00:06:03,780 --> 00:06:09,420
Yes I got that and 20 and 30 next I will in certain element add in zero.

106
00:06:09,480 --> 00:06:10,420
That is five.

107
00:06:10,620 --> 00:06:11,910
This is before Fastenal.

108
00:06:11,910 --> 00:06:12,590
That is ten.

109
00:06:12,720 --> 00:06:13,290
Let us run.

110
00:06:13,290 --> 00:06:18,280
This is five is inserted before and so the elements are five and twenty thirty.

111
00:06:18,340 --> 00:06:18,820
That's it.

112
00:06:18,990 --> 00:06:26,250
So it means even using insert function I can create a list, I can call insert function one by one and

113
00:06:26,250 --> 00:06:28,260
I can insert various elements in.

114
00:06:28,650 --> 00:06:31,920
So this is a method I can use for creating an Darlington's.

115
00:06:32,250 --> 00:06:33,750
That's all with this function.

