1
00:00:00,530 --> 00:00:06,530
And the previous whiteboard lecture, I have given introduction to structures, so in this video, I

2
00:00:06,530 --> 00:00:09,260
will give their demonstration for that structure.

3
00:00:09,260 --> 00:00:12,200
Whatever we have learned, we will see it practically.

4
00:00:13,610 --> 00:00:18,620
So for practicing, you can use an online compiler and you can practice there.

5
00:00:19,100 --> 00:00:23,740
If you want to practice it in any I.D., then you can download dieties.

6
00:00:23,750 --> 00:00:31,790
That is explain in next section so you can log in code blocks or C++ or even you can continue working

7
00:00:31,790 --> 00:00:32,280
online.

8
00:00:32,570 --> 00:00:34,820
So where to work online.

9
00:00:34,820 --> 00:00:36,350
So I will give you the website.

10
00:00:36,590 --> 00:00:43,790
You can type in online GDI be like online gdb.

11
00:00:44,690 --> 00:00:48,200
This is an online compiler on the right hand side.

12
00:00:48,200 --> 00:00:49,700
You can select a compiler.

13
00:00:49,850 --> 00:00:54,560
I will select C++ or C++ 14 and 17.

14
00:00:54,560 --> 00:01:02,480
Any one of these you can select five and select C++ 17 C if you want to write the code in C language

15
00:01:02,480 --> 00:01:04,840
style, you can do that in C++ also.

16
00:01:05,180 --> 00:01:08,420
So that is the reason I am starting a C++ program.

17
00:01:09,080 --> 00:01:18,290
So this is the sample code already given has included studio, which I will include stream on, so I

18
00:01:18,360 --> 00:01:23,620
will stream then using Naem Space S3.

19
00:01:24,170 --> 00:01:25,060
This is also there.

20
00:01:25,790 --> 00:01:30,190
Now I can write on the C style code as well as a C++ style code.

21
00:01:31,700 --> 00:01:36,650
Now very first thing we will see is structure defining a structure struct.

22
00:01:38,070 --> 00:01:45,960
I'll give the structure Mima's rectangle, the rectangle structure, and it is having two members,

23
00:01:45,960 --> 00:01:49,500
Leonard and Brett.

24
00:01:51,580 --> 00:01:53,540
This is the definition of a structure.

25
00:01:54,310 --> 00:01:55,220
This is a definition.

26
00:01:55,240 --> 00:02:01,130
Definition doesn't consume memory unless and until you declare any variable of this type structure.

27
00:02:01,720 --> 00:02:02,890
This is what we are seeing.

28
00:02:02,890 --> 00:02:05,080
This is how a structure should look like.

29
00:02:05,470 --> 00:02:07,090
It should have length and breadth.

30
00:02:07,600 --> 00:02:09,080
We are just defining it.

31
00:02:10,180 --> 00:02:15,310
We are giving its description how it should look like there are two members of this structure.

32
00:02:16,000 --> 00:02:17,870
So this is the definition of a structure.

33
00:02:18,340 --> 00:02:22,940
Now, next comes declaration of a structure declaring a variable of type structure.

34
00:02:23,590 --> 00:02:25,420
So I will declare a variable here.

35
00:02:26,050 --> 00:02:28,750
Struct rectangle.

36
00:02:31,070 --> 00:02:36,950
Are one directional structural variables created?

37
00:02:37,730 --> 00:02:39,900
Oh, there's a spelling mistake here.

38
00:02:39,920 --> 00:02:40,660
I will change it.

39
00:02:40,920 --> 00:02:41,870
A rectangle.

40
00:02:41,870 --> 00:02:44,060
So rectangle structure, there's a structure.

41
00:02:44,390 --> 00:02:47,740
See, unless and until you compile, you will not see the errors here.

42
00:02:47,780 --> 00:02:48,110
Right.

43
00:02:48,470 --> 00:02:53,070
So anyway, this is the definition and this is the declaration of a variable.

44
00:02:53,510 --> 00:02:56,280
Now, this variable is local to the main function.

45
00:02:57,050 --> 00:03:02,390
And I will show you the other method of declaring a variable can include this variable outside main

46
00:03:02,390 --> 00:03:02,900
function.

47
00:03:03,230 --> 00:03:05,890
Yes, I can declare a variable outside main function.

48
00:03:05,900 --> 00:03:07,930
I have copied and pasted here.

49
00:03:08,450 --> 00:03:13,460
This will be available or accessible for all the function in your program.

50
00:03:14,540 --> 00:03:20,090
You can declare it here also, and even when you are declaring a variable, you don't have to write

51
00:03:20,090 --> 00:03:27,620
this, you can directly declare it along with the definition you can write out of unhide.

52
00:03:27,620 --> 00:03:29,520
Also liked.

53
00:03:29,640 --> 00:03:32,540
This is also a variable declaration definition.

54
00:03:32,540 --> 00:03:35,010
Along with that, some variable is also declared.

55
00:03:35,240 --> 00:03:40,550
And if you have more variables, you can declare all of them, you can see are one comma or two comma.

56
00:03:41,450 --> 00:03:43,500
You can declare as many variable as you have.

57
00:03:43,850 --> 00:03:47,750
So this will become a global variable which will be accessible to all the function.

58
00:03:48,140 --> 00:03:53,000
This is as good as writing separately.

59
00:03:55,330 --> 00:03:57,100
Struct rectangle.

60
00:03:58,530 --> 00:04:05,850
And are now two or three, so they're same even you declare them here all, you write them separately

61
00:04:05,850 --> 00:04:06,640
in the next line.

62
00:04:07,350 --> 00:04:10,560
This is the declaration of Variable A.

63
00:04:11,550 --> 00:04:15,290
Now, let us remove it from here and put it inside main function.

64
00:04:15,600 --> 00:04:18,120
I'll just take one more variable that is out of it.

65
00:04:18,730 --> 00:04:24,390
Now I want to initialize length and breadth of rectangle so you can initialize directly by using the

66
00:04:24,390 --> 00:04:27,660
square bracket Lent Austin and Berettas five.

67
00:04:28,680 --> 00:04:34,180
So Lindenberg will have value five, ten and five to ten initialized like this.

68
00:04:34,770 --> 00:04:39,020
The next thing we will see size of structure.

69
00:04:39,330 --> 00:04:42,870
So already printf is that I will use this one.

70
00:04:43,050 --> 00:04:53,780
Remove HelloWallet, I will use printf percentile D then he and I will print size of right.

71
00:04:54,120 --> 00:04:58,830
I can write understructure name and even I can use a variable name of a structure out of it.

72
00:04:59,400 --> 00:05:02,100
Can you guess what could be the size of this structure.

73
00:05:02,700 --> 00:05:03,900
Leontes integer type.

74
00:05:03,900 --> 00:05:05,340
And that is also integer type.

75
00:05:05,670 --> 00:05:10,350
So Integer takes four bytes right in the latest compilers integer text for bytes.

76
00:05:10,680 --> 00:05:12,420
So four four eight bytes.

77
00:05:13,370 --> 00:05:18,960
Remember once again I am telling you in the whiteboard sessions I'm assuming indigeneity, sticking

78
00:05:18,960 --> 00:05:19,620
to whites.

79
00:05:19,630 --> 00:05:23,250
The reason is I have to draw a little white on white board.

80
00:05:23,850 --> 00:05:24,240
Right.

81
00:05:24,600 --> 00:05:27,660
If I draw for whites it will become lendee on white board.

82
00:05:28,320 --> 00:05:32,550
So come back to this length and breadth for four whites to delete whites.

83
00:05:32,940 --> 00:05:34,320
Let us compile and run.

84
00:05:35,330 --> 00:05:41,510
So directly, Ariel Sharon, if there are any errors, it will show here, yeah, I'm getting an error

85
00:05:41,510 --> 00:05:43,630
here that is using namespace, isn't it?

86
00:05:43,690 --> 00:05:44,420
Yeah, I have.

87
00:05:44,450 --> 00:05:45,770
There's a spelling mistake here.

88
00:05:48,010 --> 00:05:49,200
Using namespace.

89
00:05:49,510 --> 00:05:54,910
Now, listen, once again here, I got some warning, but also I got the message.

90
00:05:55,480 --> 00:06:00,580
So the output is eight bytes, like I said, that it will take a bite, sort of taking eight bites.

91
00:06:01,060 --> 00:06:02,800
But also I am getting some warning.

92
00:06:02,800 --> 00:06:10,060
It is saying that the size of operator will give you a long, unsigned, integer, long, unsigned integer

93
00:06:10,060 --> 00:06:10,450
here.

94
00:06:11,020 --> 00:06:12,850
But I have used percentile.

95
00:06:13,240 --> 00:06:17,920
So it's a warning anyway, to remove that I can give a proper formatting.

96
00:06:18,990 --> 00:06:25,680
Character, so that is all, you know, I will not get any warning and just get out, but that is long

97
00:06:25,680 --> 00:06:28,850
and sound like it is not.

98
00:06:30,030 --> 00:06:31,730
So this is the size of a structure.

99
00:06:32,670 --> 00:06:32,900
No.

100
00:06:32,910 --> 00:06:39,990
One interesting thing I will show you, I will take a character also as its member.

101
00:06:40,260 --> 00:06:43,490
So I will just give X Cat X, not total.

102
00:06:43,500 --> 00:06:49,710
How many bites of this two bites for Lent to bite for but one bite for character.

103
00:06:49,710 --> 00:06:50,090
Right.

104
00:06:50,640 --> 00:06:57,920
So totally should take nine bites for whites plus forehead whites plus one nine whites.

105
00:06:58,350 --> 00:07:00,450
Let us run and see what happens.

106
00:07:01,080 --> 00:07:03,000
Oh it is not taking nine.

107
00:07:03,390 --> 00:07:06,240
It is sticking to whites Weissel.

108
00:07:06,690 --> 00:07:13,260
See the reason is it is easy for our machine to read for four bites at a time.

109
00:07:13,560 --> 00:07:18,660
So for land it is taking for whites and then reading for whites, for bread, for character.

110
00:07:18,660 --> 00:07:22,770
Also it is looking for white but it will be using only one bite out of it.

111
00:07:23,610 --> 00:07:29,700
So at the time it will pick up four bites but use only one bite.

112
00:07:30,240 --> 00:07:37,950
Olate see, for example, if you are buying medicine from a pharmacy, so if you want to buy some tablet,

113
00:07:38,070 --> 00:07:42,160
just you want one tablet but you have to buy a strip.

114
00:07:42,420 --> 00:07:44,040
So it is easy for the seller.

115
00:07:44,040 --> 00:07:46,830
So the pharmacy people to sell strips.

116
00:07:46,950 --> 00:07:47,370
Right.

117
00:07:47,640 --> 00:07:49,470
They will not sell a single tablet.

118
00:07:49,770 --> 00:07:53,940
So you buy one strip that the 10 tablets or eight tablets.

119
00:07:53,940 --> 00:07:54,940
It depends on the type of.

120
00:07:55,680 --> 00:07:59,350
But you will be consuming only one tablet.

121
00:07:59,670 --> 00:08:05,370
Similarly, other machines can access for whites at the time, but it will be using one white artifact

122
00:08:05,580 --> 00:08:07,980
so far, making its accessibility easy.

123
00:08:08,160 --> 00:08:13,500
It is educating for four bytes for each member, so total is twelve bytes.

124
00:08:14,070 --> 00:08:17,010
If you take floor, then it will take greater number of bytes.

125
00:08:17,010 --> 00:08:18,990
Like definitely don't say that again.

126
00:08:18,990 --> 00:08:21,390
So it is not looking for whites, it is taking extra.

127
00:08:21,690 --> 00:08:24,210
So it definitely depends on the size of the data type.

128
00:08:24,570 --> 00:08:31,080
But for character, instead of taking one bytes, it will take the nearest bigger size that is integer.

129
00:08:31,290 --> 00:08:32,490
So it is sticking to him.

130
00:08:32,700 --> 00:08:36,260
And this adjustment in the memory is scarless padding.

131
00:08:36,730 --> 00:08:43,380
It is less padding, so inside structures, padding of memory is done means it will take extra memory,

132
00:08:43,590 --> 00:08:50,760
like actual size of a structure is nine bytes, but it will take three bytes extra so that it is easy

133
00:08:50,760 --> 00:08:55,890
for our machine that is processor to read their internal structure at once.

134
00:08:56,100 --> 00:09:02,190
The later on, it will discard the last three bytes, first for this one, next four for this one and

135
00:09:02,190 --> 00:09:03,210
one might for this one.

136
00:09:03,420 --> 00:09:04,980
Birdsell, this was the important thing.

137
00:09:04,980 --> 00:09:06,570
I have explained you nudniks.

138
00:09:06,960 --> 00:09:10,200
Let us see how to access this members of a structure.

139
00:09:10,530 --> 00:09:15,660
So I will first of all display them then also I will show you how to access.

140
00:09:15,960 --> 00:09:18,370
So instead of using print, I will see out.

141
00:09:18,600 --> 00:09:25,560
You can use anything now you can say to C++ program so I will display are not linked then.

142
00:09:27,180 --> 00:09:27,690
And then.

143
00:09:29,750 --> 00:09:31,930
All right, the Nixey out.

144
00:09:34,410 --> 00:09:36,660
Are not Brett.

145
00:09:38,110 --> 00:09:46,320
And and then let us run so I should give the Palestinian five years ago, the Palestinian five next,

146
00:09:46,840 --> 00:09:53,170
I will change the values, how to accept the members are one does not see this as a variable, normal

147
00:09:53,170 --> 00:09:53,590
variable.

148
00:09:53,590 --> 00:10:01,630
So you should use dot operator for accessing the member's land, assign fifteen an hour to one dot,

149
00:10:02,110 --> 00:10:04,500
but at a site seven.

150
00:10:05,200 --> 00:10:08,310
Now I am changing lintang, but initially it was ten and five.

151
00:10:08,320 --> 00:10:13,830
Now I have changed them to fifteen and seven so I should get the output as 15 and seven.

152
00:10:14,050 --> 00:10:16,450
Yes, I got the result as 15 and seven.

153
00:10:16,830 --> 00:10:17,530
That's it.

154
00:10:18,070 --> 00:10:21,390
So this is the demo of structures.

155
00:10:21,400 --> 00:10:27,010
So once again I repeat the things I have explained you how to define a structure, how to declare it

156
00:10:27,010 --> 00:10:32,200
and how to initialize and explain what is spatting then how to access the members.

157
00:10:32,340 --> 00:10:37,870
DOT is used for accessing the members so you can write on the if you are opening a project of C++,

158
00:10:37,870 --> 00:10:43,680
you can write on the code and C style as well as C++ style structures are available in both languages.

159
00:10:43,690 --> 00:10:45,070
So that's all in this video.

160
00:10:45,070 --> 00:10:47,500
I suggest you go online and practice this one.

161
00:10:47,770 --> 00:10:48,520
So that's on.

