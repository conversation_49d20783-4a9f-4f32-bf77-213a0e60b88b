1
00:00:00,210 --> 00:00:07,020
Now, let us look at in direct and indirect that there may be more than one function and they are calling

2
00:00:07,020 --> 00:00:13,500
one another in a circular fashion, so that if the first function called second one and the second call,

3
00:00:13,500 --> 00:00:18,860
code one and the third one again called back first function, then it becomes a cycle, third becomes

4
00:00:18,860 --> 00:00:19,490
the recursion.

5
00:00:19,830 --> 00:00:26,610
Let's suppose that is a function A and the function is calling function B and the function B calling

6
00:00:26,610 --> 00:00:29,650
function C and <PERSON>'s calling again.

7
00:00:30,030 --> 00:00:34,710
So so instead of a function calling itself here function, calling another function and it's calling

8
00:00:34,710 --> 00:00:38,400
another function and Da<PERSON>'s calling it so it's becoming a cycle.

9
00:00:38,550 --> 00:00:38,920
It becomes.

10
00:00:40,140 --> 00:00:44,070
So here I have a skeleton for showing you are indirectly caution.

11
00:00:44,370 --> 00:00:49,740
Here is function A function E isn't having some condition and the condition is satisfied.

12
00:00:49,740 --> 00:00:53,460
It is calling function B and the function B, there is some condition.

13
00:00:53,470 --> 00:00:55,710
If it is satisfied, it is calling function A..

14
00:00:56,220 --> 00:01:03,240
So from here it is calling B and then B calling A, so it becomes indirectly records here.

15
00:01:03,240 --> 00:01:08,160
B's call would reduce the value of and again is called with some more reduced values often.

16
00:01:08,400 --> 00:01:09,740
So they are calling each other.

17
00:01:09,750 --> 00:01:11,400
At one point they stop.

18
00:01:11,580 --> 00:01:17,280
If the condition fails, if any one function stops, then it will return back to the first starting

19
00:01:17,310 --> 00:01:17,670
call.

20
00:01:18,760 --> 00:01:25,620
To explain how this work, I have set of functions as an example here, these are an indirect recursion.

21
00:01:25,900 --> 00:01:30,500
So let me try this with an example and let us trace this example.

22
00:01:30,530 --> 00:01:34,570
So, first of all, I really don't see this as a function A.

23
00:01:35,900 --> 00:01:40,850
I'm taking parameter, and if it is greater than zero, then it is printing and then calling function

24
00:01:40,850 --> 00:01:48,980
B by and minus one, this function B if anything greater than one, then it is printing and calling

25
00:01:48,980 --> 00:01:51,710
function A. by passing and the bitou.

26
00:01:52,790 --> 00:02:00,100
So I'll start this by calling function F first letter C function E is called for value.

27
00:02:00,130 --> 00:02:02,150
Pretty little strings.

28
00:02:02,150 --> 00:02:06,450
This one function is taking trends parameter pretty zero.

29
00:02:06,740 --> 00:02:13,580
Then it has to perform two things that are predictive value and all function B by passing and minus

30
00:02:13,580 --> 00:02:13,860
one.

31
00:02:13,880 --> 00:02:17,210
So function they will be calling B by passing in minus one.

32
00:02:17,600 --> 00:02:21,500
So the first thing it will bring to point B and it will call function B.

33
00:02:22,810 --> 00:02:24,070
By passing 19.

34
00:02:25,440 --> 00:02:31,510
Now, what function beat us function B, if N is greater than one, that it will print and call function

35
00:02:31,770 --> 00:02:40,870
by passing and by two so two step father first print and then all function by half off.

36
00:02:40,890 --> 00:02:47,370
And so this is function B, so it will print the 19 and it will call function A..

37
00:02:47,790 --> 00:02:50,940
By passing 19 by two nine.

38
00:02:52,950 --> 00:02:58,770
So, again, control goes back to function, a function age, the argument, and, you know, well,

39
00:02:58,770 --> 00:03:04,590
that is sprinting and calling be Sprints nine and Colbie bypassing.

40
00:03:05,890 --> 00:03:08,570
And minus one, that is nine, minus one, that is eight.

41
00:03:09,420 --> 00:03:15,740
Now it is back on function B, function B, print and call A by half of this one.

42
00:03:16,090 --> 00:03:23,600
So print age and call fun of it by passing half of that is in my trophy.

43
00:03:23,950 --> 00:03:33,550
So sort of for cognitive function, a function in print and call the principal and call B by passing

44
00:03:34,420 --> 00:03:39,730
four minus one, that is A three function B and is greater than one.

45
00:03:39,740 --> 00:03:47,350
Yes, three is better than one print and call function E so 3d printed and call function E but passing

46
00:03:47,800 --> 00:03:51,060
one that is three by two or just integer division.

47
00:03:51,070 --> 00:04:01,000
We are doing integer division here, not a it will print one and B function B bypassing zero.

48
00:04:02,680 --> 00:04:04,770
Function B is greater than zero.

49
00:04:04,780 --> 00:04:05,380
It works.

50
00:04:05,380 --> 00:04:12,070
Otherwise it stops because and as the zero now so it will not enter inside, it will determine what

51
00:04:12,090 --> 00:04:19,600
the function terminates that goes back to function A and B, then A and B and then B a third goes back.

52
00:04:20,769 --> 00:04:28,630
And the starting point and the function calls and the output will be 20, 19, nine, eight, four,

53
00:04:28,630 --> 00:04:29,500
three and one.

54
00:04:30,560 --> 00:04:35,930
So that's all about agriculture and I have just taken the sample program to demonstrate how indirect

55
00:04:35,930 --> 00:04:37,640
requestion works and what does it mean?

