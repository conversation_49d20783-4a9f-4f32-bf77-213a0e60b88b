1
00:00:00,600 --> 00:00:07,050
Here in this video, we will look at the demonstration for reversing a linguist, we have seen different

2
00:00:07,050 --> 00:00:12,380
versions of functions for reversing a linguist, so I will show you all those methods.

3
00:00:12,750 --> 00:00:15,180
So the first method was using URRY.

4
00:00:15,750 --> 00:00:19,840
So let us write on a function for reversing and inkless using Uhry.

5
00:00:19,910 --> 00:00:24,450
So I'm using the same mean function, having done some sort of elements and the linguist has already

6
00:00:24,450 --> 00:00:25,040
created.

7
00:00:25,500 --> 00:00:30,210
So let us write the function for reversing a linguist so hearable the main function.

8
00:00:30,220 --> 00:00:37,430
I will write a function reverse one which will take a pointer to our first node and let us be then for

9
00:00:37,430 --> 00:00:37,910
reversing.

10
00:00:37,920 --> 00:00:39,790
We will store the elements in an array.

11
00:00:39,840 --> 00:00:45,270
So for storing in elements in an array, we should create an array and we don't know how many elements

12
00:00:45,270 --> 00:00:45,580
we need.

13
00:00:45,600 --> 00:00:49,890
So we will find a lot of links and we will create an array of tax size.

14
00:00:50,730 --> 00:00:54,170
So for that I will take a pointer so that we can create dynamically.

15
00:00:54,270 --> 00:00:56,670
So I will take a pointer at the next.

16
00:00:56,690 --> 00:01:01,590
Here I will create an array of size equal to the length of Hollinghurst.

17
00:01:01,710 --> 00:01:10,170
So it assign integer type pointer and dynamic memory location mellark then size of integer type because

18
00:01:10,170 --> 00:01:13,920
the data is integer type multiplied by land of Lincoln.

19
00:01:13,990 --> 00:01:14,480
So forth.

20
00:01:14,680 --> 00:01:16,680
The following year we already have a function.

21
00:01:17,250 --> 00:01:21,560
We will use that function and possible be that is already available here.

22
00:01:21,900 --> 00:01:28,310
I'm using the same project so I will count the number of nodes in a long list that is nothing but land

23
00:01:28,330 --> 00:01:29,900
of this function.

24
00:01:29,910 --> 00:01:31,650
So coming back to the word function.

25
00:01:31,650 --> 00:01:33,320
So arrays are created.

26
00:01:33,540 --> 00:01:34,560
So not for reversing.

27
00:01:34,560 --> 00:01:39,960
I have to scan through the last two times one for copying elements from Linklaters to the second time

28
00:01:39,960 --> 00:01:42,780
from array rulings so far traversing.

29
00:01:42,780 --> 00:01:48,000
I will take one more pointer that will help me to traverse talentless that is queue.

30
00:01:48,180 --> 00:01:53,140
I will use a pointer queue for traversing in P because B's already pointing on first node.

31
00:01:53,160 --> 00:01:58,140
Then also I may need an index that is I and II is initialized to zero.

32
00:01:58,890 --> 00:02:01,080
Now here I will control Englis.

33
00:02:01,180 --> 00:02:02,850
That is while queue.

34
00:02:02,850 --> 00:02:04,680
As I said I'm using Q instant p.

35
00:02:05,010 --> 00:02:12,870
Q is not equal to null and every time in an array of I copy an element from Qs data.

36
00:02:12,930 --> 00:02:13,350
Yes.

37
00:02:13,800 --> 00:02:16,580
Then you should move on to next node.

38
00:02:16,650 --> 00:02:17,820
So this will copy.

39
00:02:17,820 --> 00:02:21,590
And also I placeless I should be thens.

40
00:02:21,600 --> 00:02:27,510
Once it has reached the end of a list, all the elements are cooperating and then Q should again start

41
00:02:27,510 --> 00:02:28,990
from first point.

42
00:02:29,090 --> 00:02:32,730
That is P soapies first here and I minus minus.

43
00:02:32,730 --> 00:02:36,930
I should go back to the last element, then again run a loop.

44
00:02:36,930 --> 00:02:41,190
While Q is not equal to none against controlling list.

45
00:02:41,460 --> 00:02:51,210
And while scanning will popular elements from array to relentless sulcus data as assigned with aof I

46
00:02:51,330 --> 00:02:57,810
and then you should move to the next node and also ie minus minus NetSol.

47
00:02:57,810 --> 00:03:00,450
So all of you have explaining the whole procedure.

48
00:03:00,810 --> 00:03:08,790
So the code I have written and for demonstration not inside the main function, I will call a function

49
00:03:08,790 --> 00:03:12,290
that is reverse one, reverse one upon first.

50
00:03:12,300 --> 00:03:16,400
Now after reversing it should display a link so the link lists must be reversed.

51
00:03:16,450 --> 00:03:17,570
Let us run the program.

52
00:03:17,580 --> 00:03:19,470
Yes Linklaters reversed.

53
00:03:19,650 --> 00:03:22,110
So the elements are fifty, 40, 30, 20.

54
00:03:22,110 --> 00:03:26,240
And then originally Lindis was 10, 20, 30, 40, 50.

55
00:03:26,250 --> 00:03:27,600
So it's working perfectly.

56
00:03:27,730 --> 00:03:29,130
So t on the whiteboard.

57
00:03:29,130 --> 00:03:34,170
I have use a pointer before scanning through the list here I have used it is not.

58
00:03:34,170 --> 00:03:37,470
Q but here is coming as a pointed to a footnote.

59
00:03:37,470 --> 00:03:38,130
So I'm using.

60
00:03:38,130 --> 00:03:44,340
Q So Q starts from B and Q will be scanning for first time and again.

61
00:03:44,340 --> 00:03:48,390
Q Start from B and you will be scanning troublingly for second time.

62
00:03:48,960 --> 00:03:50,910
So don't get confused with the variables.

63
00:03:50,910 --> 00:03:52,680
We can use any variable name.

64
00:03:52,770 --> 00:03:59,280
Now I will write on second method which I have shown you for reversing a link so far that I will call

65
00:03:59,280 --> 00:04:01,650
a function as a reverse to reverse tool.

66
00:04:01,650 --> 00:04:06,920
It takes a pointer to offer node and let us call that point on the must be appointed to a Fastenal.

67
00:04:06,960 --> 00:04:11,370
And in this procedure we require three pointers is already there.

68
00:04:11,550 --> 00:04:19,589
Q We declare that should be initially null and also we require ah that is also null then p I will be

69
00:04:19,589 --> 00:04:20,190
using it.

70
00:04:20,430 --> 00:04:22,800
So is binding upon first node.

71
00:04:23,040 --> 00:04:27,870
So I had three pointer three values and I will directly write on the code how it works already.

72
00:04:27,870 --> 00:04:33,600
I have explained you will be is not equal to null until PD just null.

73
00:04:33,930 --> 00:04:41,930
Then first of all I should come up on queue and you should come upon me and repeat should move to next

74
00:04:41,930 --> 00:04:45,540
and then after that you should point on Qs next.

75
00:04:45,540 --> 00:04:46,560
Should point on.

76
00:04:46,590 --> 00:04:52,350
Are this how we proceed then once we come out of the Vilo first point there should point on node two.

77
00:04:52,350 --> 00:04:58,980
It's all function is finished now inside mean function instead of calling reverse one I will call reverse

78
00:04:58,980 --> 00:04:59,850
to and run it.

79
00:05:00,080 --> 00:05:01,490
And see what it looks.

80
00:05:02,680 --> 00:05:06,750
Yes, it is working 50 40 today to the end, it's perfect.

81
00:05:06,760 --> 00:05:11,950
So we have seen two functions now lost function is recursive function for reversing a linguist.

82
00:05:12,110 --> 00:05:15,100
So I will write down reverse three four.

83
00:05:15,100 --> 00:05:20,080
Recursive function should take two pointers struct normed pointer.

84
00:05:20,080 --> 00:05:24,460
First of all, cue and the next pointer is current pointer.

85
00:05:24,460 --> 00:05:32,650
That is P then if p that speech is not equal to null, it will be calling itself reverse three by passing

86
00:05:33,160 --> 00:05:36,040
here B and next Espy's.

87
00:05:36,040 --> 00:05:39,340
Next is two point as it will passing on while returning.

88
00:05:39,340 --> 00:05:45,700
It will make these next as Q and if B is null then it should make first.

89
00:05:45,700 --> 00:05:53,380
As Q It's all just three four lines of code and inside the mean function instead of reverts to I will

90
00:05:53,380 --> 00:05:54,840
call it was three.

91
00:05:55,150 --> 00:05:55,810
Let us see.

92
00:05:55,960 --> 00:05:58,200
Oops I should pass two parameter.

93
00:05:58,210 --> 00:06:02,950
So the first parameter is null and the second parameter is first.

94
00:06:02,950 --> 00:06:04,540
So it's taken two parameters.

95
00:06:04,660 --> 00:06:05,170
Yes.

96
00:06:05,170 --> 00:06:08,320
Linklaters reverse fifty 40 30, 2010.

97
00:06:08,920 --> 00:06:10,600
I'll try with different values.

98
00:06:10,600 --> 00:06:18,650
I will change the values and make them at fifty, forty and thirty twenty so I should get 20, 30 and

99
00:06:18,880 --> 00:06:22,450
40 and 50, 20, 30 and 40 and 50.

100
00:06:22,630 --> 00:06:23,860
Yes, it's perfect.

101
00:06:23,870 --> 00:06:24,520
So that's all.

102
00:06:24,520 --> 00:06:27,630
We have seen three different versions of reversing a link.

103
00:06:27,640 --> 00:06:34,210
This one is using another and the second one is using three pointers and the third one is recursive

104
00:06:34,210 --> 00:06:36,580
function so you can practice these functions.

