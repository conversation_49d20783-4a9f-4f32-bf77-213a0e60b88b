1
00:00:00,740 --> 00:00:11,120
No, the topic is Uhry entity, that is, but as abstract data type, abstract data type means representation

2
00:00:11,120 --> 00:00:15,260
of data and the set of operations on the data.

3
00:00:16,760 --> 00:00:24,710
Now, Uhry is a basic data structure provided by almost every programming language like C, C++ provides

4
00:00:25,430 --> 00:00:27,440
Uhry as a basic structure.

5
00:00:28,220 --> 00:00:32,009
So representational data is defined by the compiler itself.

6
00:00:32,780 --> 00:00:36,100
Now, the operations on the data is not given by the compiler.

7
00:00:36,380 --> 00:00:42,380
We are supposed to implement operations or provide operations on a data structure.

8
00:00:43,040 --> 00:00:48,880
So data structure and a set of operations together we can call it as ality.

9
00:00:49,970 --> 00:00:56,610
So basically we will be learning how we can perform various operations on and on.

10
00:00:57,830 --> 00:01:03,860
So here is the list of operations that are possible on another day and many more operations are possible.

11
00:01:04,099 --> 00:01:08,160
So some of the operations here, we will discuss in this video and more operations.

12
00:01:08,180 --> 00:01:10,410
I'll show you in the coming lectures.

13
00:01:11,420 --> 00:01:17,300
Now, I will quickly read out just the set of operations here, displaying in adding order, pending

14
00:01:17,300 --> 00:01:23,180
an element in an array or inserting an element at a given index in an array, deleting an element from

15
00:01:23,180 --> 00:01:29,450
a given index, searching for an element in an array, then getting an element from a given index,

16
00:01:29,450 --> 00:01:35,540
knowing what is the element present at a given index, and that is replacing an element of the particular

17
00:01:35,540 --> 00:01:43,260
index, then finding maximum or minimum, reversing an array and also shifting and rotation of an array.

18
00:01:43,970 --> 00:01:49,040
So we'll discuss all these operations and see how these operations are performed on an array and also

19
00:01:49,040 --> 00:01:50,170
we'll analyze them.

20
00:01:50,740 --> 00:01:54,710
Now, first of all, let us look at the representation of raw data of an item.

21
00:01:54,800 --> 00:01:58,380
So the first thing is we need an array space of some size.

22
00:01:58,670 --> 00:02:04,220
So let us take an array of some size that is array of size and.

23
00:02:06,560 --> 00:02:08,449
Here is an of system.

24
00:02:08,870 --> 00:02:11,060
Now, the question is, how will you create this?

25
00:02:11,810 --> 00:02:17,390
There are two methods of creating this study that is statically inside the stack, all we can created

26
00:02:17,390 --> 00:02:18,030
in a heap.

27
00:02:18,410 --> 00:02:23,780
So one method is I can declare a normal early legacy of size 10.

28
00:02:26,050 --> 00:02:28,460
Now, this will be creating an area of size a 10.

29
00:02:28,460 --> 00:02:31,760
Whenever I run my program, the size of 30 will be 10 only.

30
00:02:32,120 --> 00:02:42,680
No, I want to create an array of desired size so that you can take a pointer and later you can create

31
00:02:42,680 --> 00:02:44,920
an array of required five.

32
00:02:45,230 --> 00:02:48,380
I'm writing a C++ syntax here, so it's easy.

33
00:02:48,380 --> 00:02:56,510
Instead of writing mellark function and writing just so new, so new offices, whatever the size you

34
00:02:56,510 --> 00:03:05,060
want to mention so we can have the size of another in the size of an array is ten, so a 10 will be

35
00:03:05,060 --> 00:03:05,570
created.

36
00:03:06,320 --> 00:03:08,960
So we have two options for creating an array.

37
00:03:09,200 --> 00:03:12,260
If you are following this one then you must mention the size.

38
00:03:13,010 --> 00:03:17,690
If you're following this one, then at one time you can take the size and create an array from Hape.

39
00:03:18,050 --> 00:03:23,010
So this option is better for creating an array of required size.

40
00:03:24,350 --> 00:03:27,860
So let us assume that we have taken Appointer Pointer and created an array.

41
00:03:28,520 --> 00:03:31,190
So here is a pointer pointing to this.

42
00:03:33,140 --> 00:03:40,910
So two things I have completed array and size, so if you know the size then you can create an array

43
00:03:40,910 --> 00:03:44,270
of required size at one time from Heap.

44
00:03:44,690 --> 00:03:46,100
So this is a creation.

45
00:03:46,230 --> 00:03:48,900
So I have written the piece of code for creating an update.

46
00:03:49,730 --> 00:03:51,830
Now the next is Lente.

47
00:03:51,830 --> 00:03:55,240
Often I see I have an array of size ten.

48
00:03:55,520 --> 00:03:58,510
It's not necessary that I am using all 10 spaces.

49
00:03:58,520 --> 00:04:03,200
I may be having fewer elements in that one or I may be using all spaces.

50
00:04:03,620 --> 00:04:07,110
So possibilities there that I may or may not be using all spaces.

51
00:04:07,130 --> 00:04:11,280
So I want to know presently how many elements are dead in an array.

52
00:04:11,540 --> 00:04:16,370
So for that, we should also have one more property of a note.

53
00:04:16,640 --> 00:04:18,320
That is LEMP right now.

54
00:04:18,320 --> 00:04:19,529
Nothing is needed in that.

55
00:04:19,760 --> 00:04:22,089
So let us see the veto.

56
00:04:22,280 --> 00:04:24,470
So I have completed the presentation.

57
00:04:24,800 --> 00:04:29,810
So I have an array and two more properties that is lent and the size of an array.

58
00:04:30,500 --> 00:04:33,620
Now let us perform these operations on this.

