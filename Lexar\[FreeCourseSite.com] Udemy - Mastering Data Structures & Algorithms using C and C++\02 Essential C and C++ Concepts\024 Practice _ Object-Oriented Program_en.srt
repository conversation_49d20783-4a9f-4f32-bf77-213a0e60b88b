1
00:00:00,390 --> 00:00:04,710
This is the program we have written, the previous demonstration lecture.

2
00:00:05,070 --> 00:00:11,940
Now we will convert this program into object oriented program and first I'll explain this program,

3
00:00:11,940 --> 00:00:14,080
then I will convert it into object rendition.

4
00:00:14,760 --> 00:00:18,750
So here we have a structure for rectangle having land and that.

5
00:00:19,720 --> 00:00:27,400
This is a initialise function which is initializing a rectangle light area function, using a rectangle

6
00:00:27,400 --> 00:00:33,940
and finding data perimeter function, using a rectangle as parameter, taking it as parameter and finding

7
00:00:33,940 --> 00:00:38,740
its perimeter main function is creating a variable of type rectangle.

8
00:00:40,080 --> 00:00:46,080
Calling initialize function for initializing it to area function and parameter to calculate irion pentimento

9
00:00:46,590 --> 00:00:47,710
does how it is written.

10
00:00:48,000 --> 00:00:52,680
No question arises here for whom this initialize function is working.

11
00:00:53,070 --> 00:00:59,760
That's working for a rectangle structure, for whom this area is working for a structure parameter,

12
00:00:59,760 --> 00:01:03,060
for a structure or a network infrastructure.

13
00:01:03,400 --> 00:01:05,610
Then they are written outside.

14
00:01:05,610 --> 00:01:09,560
The structure structure is closing here, right there in an old site.

15
00:01:09,870 --> 00:01:10,230
Why.

16
00:01:10,230 --> 00:01:11,370
To write them outside.

17
00:01:11,550 --> 00:01:12,960
I write them inside only.

18
00:01:13,230 --> 00:01:17,850
So remove this bracket and close it here.

19
00:01:18,360 --> 00:01:19,060
That's it.

20
00:01:19,080 --> 00:01:20,290
This is the idea.

21
00:01:20,520 --> 00:01:24,960
These functions, they are working upon this rectangle structure.

22
00:01:24,960 --> 00:01:25,380
Then why?

23
00:01:25,380 --> 00:01:26,790
To write them outside.

24
00:01:27,090 --> 00:01:29,520
Write them inside the structure itself.

25
00:01:29,520 --> 00:01:32,190
Then does it need to take the parameter.

26
00:01:32,220 --> 00:01:34,530
No, remove it directly.

27
00:01:34,530 --> 00:01:37,800
It can access the internet because this is inside the structure.

28
00:01:38,070 --> 00:01:41,520
Then does this area has to take a rectangle as a parameter.

29
00:01:41,520 --> 00:01:41,910
No.

30
00:01:41,910 --> 00:01:42,570
Or remove it.

31
00:01:42,960 --> 00:01:48,030
It can directly access length and breadth because these are available here, right.

32
00:01:48,180 --> 00:01:49,230
They are declaring site.

33
00:01:49,680 --> 00:01:57,510
All are inside rectangle structure only then what about parameter remove it then they can directly access

34
00:01:57,510 --> 00:02:00,450
land and bread and that's it.

35
00:02:00,750 --> 00:02:08,789
So we have included all three functions which are working for a rectangle inside a rectangle structure

36
00:02:08,789 --> 00:02:09,210
itself.

37
00:02:09,240 --> 00:02:17,640
This is still stacked right structurally and inside the structure in C++ we can write on functions.

38
00:02:18,060 --> 00:02:21,420
So I have written all the functions inside the structure.

39
00:02:21,780 --> 00:02:27,480
They are directly accessing content, but they are accessing directly and then set alight.

40
00:02:27,900 --> 00:02:32,730
Then whatever the main function in main function, we will create a variable of type structure like

41
00:02:32,730 --> 00:02:32,940
this.

42
00:02:32,940 --> 00:02:41,640
Only instead of calling, initialize and passing are we will say are dot because art itself is having

43
00:02:41,640 --> 00:02:42,980
initialize function now.

44
00:02:43,260 --> 00:02:50,250
So you can access the member of the structure by using dot operator now instead of giving out as a parameter

45
00:02:50,250 --> 00:02:57,420
to Adrià and instead of giving it as parameter, we will write our DOT area.

46
00:02:57,420 --> 00:03:03,670
We are calling our area and remove from parameter and this is are spreading meter.

47
00:03:04,210 --> 00:03:06,740
Now the syntax has changed, right.

48
00:03:07,170 --> 00:03:09,230
This is object orientation.

49
00:03:09,720 --> 00:03:16,860
So what is an object writing the data members and all the member functions together at a single place.

50
00:03:17,010 --> 00:03:20,330
We can define a class and we can create its object.

51
00:03:20,550 --> 00:03:21,750
So these are object.

52
00:03:21,990 --> 00:03:28,210
Object will have X properties and methods that its member functions.

53
00:03:28,510 --> 00:03:30,300
So what I have written a structure here.

54
00:03:30,310 --> 00:03:34,160
Yes, you can write structure also in place of class in C++.

55
00:03:34,650 --> 00:03:36,830
Let us run this land.

56
00:03:36,900 --> 00:03:38,550
Austin Standard five.

57
00:03:38,550 --> 00:03:42,170
It's working o all the functions in such structure.

58
00:03:42,180 --> 00:03:45,250
Yes, this is object orientation.

59
00:03:45,480 --> 00:03:48,240
Now lastly, this should be a class now.

60
00:03:48,240 --> 00:03:50,040
I would like it as a class then.

61
00:03:50,940 --> 00:03:51,810
I will run it.

62
00:03:52,940 --> 00:03:55,850
No, Bellfield, there's an error.

63
00:03:56,360 --> 00:04:03,020
OK, I cannot initialize like this, just like a structure we can directly initialize, but a rectangle

64
00:04:03,020 --> 00:04:04,050
we cannot initialize.

65
00:04:04,580 --> 00:04:05,690
OK, I will remove it.

66
00:04:06,140 --> 00:04:07,040
I will remove it.

67
00:04:07,460 --> 00:04:07,790
OK.

68
00:04:08,060 --> 00:04:09,460
Oh, again, the error.

69
00:04:10,040 --> 00:04:10,910
What is the error?

70
00:04:11,300 --> 00:04:16,250
Initializes private member is private member perimeter's private.

71
00:04:16,760 --> 00:04:22,150
All so because I wrote a class here, all these members are private.

72
00:04:22,760 --> 00:04:26,360
If you write struck here, all the members are public.

73
00:04:26,790 --> 00:04:30,830
So in C++ structure and class are the same.

74
00:04:31,430 --> 00:04:33,500
Only the differences in the structure.

75
00:04:33,500 --> 00:04:38,710
Everything is public, but in class everything is by default private.

76
00:04:38,930 --> 00:04:44,990
So if you make it public, then everything will be same as a structure, similar structure.

77
00:04:45,380 --> 00:04:46,880
But darroch initialization.

78
00:04:46,880 --> 00:04:52,480
We cannot do like we have done it earlier inside structure, not on the program.

79
00:04:52,910 --> 00:04:55,730
So I have just changed a structure to a class.

80
00:04:56,360 --> 00:04:56,970
That's it.

81
00:04:57,230 --> 00:04:57,920
Same thing.

82
00:04:57,920 --> 00:04:58,670
Same result.

83
00:04:59,240 --> 00:04:59,670
Right.

84
00:05:00,140 --> 00:05:06,200
So this program as an object oriented program, I have written a class class condensed and then read

85
00:05:06,500 --> 00:05:09,690
and it is containing all the functions I liked.

86
00:05:10,010 --> 00:05:15,340
So we have converted a C language program to a C++ program.

87
00:05:15,770 --> 00:05:22,340
So in the entire course when you are writing a simpler C language code by writing functions and parsing

88
00:05:22,340 --> 00:05:29,180
parameters as a structure, later on you can convert the entire thing into a C++ code by converting

89
00:05:29,180 --> 00:05:30,820
into a class like this.

90
00:05:31,250 --> 00:05:36,530
Alright, so this will help you because in most of the topics in the lecture we will be writing a function

91
00:05:36,530 --> 00:05:36,980
for that.

92
00:05:36,980 --> 00:05:38,780
We don't write a class, we write a function.

93
00:05:38,780 --> 00:05:43,970
Only if they didn't C or C++ like what writing class means.

94
00:05:43,970 --> 00:05:45,530
C++ style programming.

95
00:05:45,740 --> 00:05:48,470
Writing just a function means C style programming.

96
00:05:48,740 --> 00:05:51,560
That is the reason most of the time I have written functions.

97
00:05:51,560 --> 00:05:54,350
So I am saying that I am following C style coding.

98
00:05:54,800 --> 00:05:57,410
So that is also a C++ style coding.

99
00:05:57,710 --> 00:05:58,060
Right?

100
00:05:58,310 --> 00:06:01,850
Unless you write a class, you can't say this is object oriented program.

101
00:06:02,420 --> 00:06:07,540
So classes cannot be written always unless and until you have a big enough program.

102
00:06:08,250 --> 00:06:08,620
Right.

103
00:06:08,840 --> 00:06:10,390
So that's all in this lecture.

104
00:06:10,400 --> 00:06:12,470
So I suggest you to practice this one.

105
00:06:12,890 --> 00:06:16,340
And then in the next lecture I will write on a complete class.

