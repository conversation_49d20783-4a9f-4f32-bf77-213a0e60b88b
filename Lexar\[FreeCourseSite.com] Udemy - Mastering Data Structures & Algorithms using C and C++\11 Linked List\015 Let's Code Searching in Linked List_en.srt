1
00:00:00,150 --> 00:00:06,310
In this video, we will write a function for linnear, such as the one which we have already discussed.

2
00:00:06,720 --> 00:00:09,840
I will show you a great version as well as recursive version.

3
00:00:09,840 --> 00:00:15,240
And also I will show you move to front operation after linnear such.

4
00:00:15,250 --> 00:00:20,130
I'm using the same program for a Linklaters, which is having all the functions that we have already

5
00:00:20,140 --> 00:00:25,350
written and making it as a single, large program for all the operations of a linguist.

6
00:00:26,400 --> 00:00:32,880
So here, above the main function, I will write a function for linear search, linear search function

7
00:00:32,880 --> 00:00:35,140
should return on structure type node.

8
00:00:35,340 --> 00:00:42,240
So this should be struck node and let us call this function as a search that is linear search.

9
00:00:43,120 --> 00:00:49,240
And it should take a pointer to a structure note and let us call the pointer Aspey, and the next thing

10
00:00:49,240 --> 00:00:52,330
is the key that we want to search now inside the function.

11
00:00:52,360 --> 00:00:59,290
We should scan through a link list searching for a key value so that we can use availability that is

12
00:00:59,290 --> 00:01:07,900
not equal to null, or we can just write wildy and check if key is equal to these data.

13
00:01:07,930 --> 00:01:14,830
If it is found, then on E we will return the A. Otherwise we will move to the next note.

14
00:01:15,550 --> 00:01:21,040
So it will be immediately searching and whenever key is found, it will return that key node at the

15
00:01:21,040 --> 00:01:25,210
end of the loop once it has reached the end of elopements, the key is not found.

16
00:01:25,210 --> 00:01:28,030
So the return null.

17
00:01:28,180 --> 00:01:29,090
That is the zettl.

18
00:01:29,110 --> 00:01:34,810
Next on the linear search function, let us call this linear search function that is also a function

19
00:01:34,810 --> 00:01:38,470
inside mean function as the linear search function is returning a node.

20
00:01:38,470 --> 00:01:42,440
So I will declare a node pointer that is temporary point that I will take.

21
00:01:42,670 --> 00:01:48,940
Let us call it as a temp and this should be of type struck node and here inside temp.

22
00:01:49,570 --> 00:01:51,780
I will take that as written by that function.

23
00:01:51,790 --> 00:01:55,960
So I will call function and search by passing pointer first.

24
00:01:55,960 --> 00:02:01,030
And the key value that I want to search for, the key value that I want to search is 25 us send twenty

25
00:02:01,030 --> 00:02:01,420
five.

26
00:02:01,450 --> 00:02:09,160
Now the system, if it is not nine months, if M and said is not null then only again print the results.

27
00:02:09,160 --> 00:02:12,230
I will print the result that the data is found.

28
00:02:12,250 --> 00:02:16,210
Key is found out and the keys so and so.

29
00:02:16,360 --> 00:02:17,620
So already we know the key.

30
00:02:18,190 --> 00:02:19,930
But still I will display the key.

31
00:02:19,930 --> 00:02:22,900
A message is found is sufficient for us.

32
00:02:22,900 --> 00:02:26,040
If we have to process anything up on that key then we can do it.

33
00:02:26,050 --> 00:02:29,170
But in our example, just we want to know whether to there or not.

34
00:02:29,440 --> 00:02:31,810
If camp is null, then we will print keys.

35
00:02:31,810 --> 00:02:36,580
Not found that Sol let us run the program c keys found the keys.

36
00:02:36,580 --> 00:02:37,360
Twenty five.

37
00:02:37,360 --> 00:02:40,550
I'll give slash here so that it comes in the next line.

38
00:02:40,640 --> 00:02:46,050
Now this time I will look for a key that is 17 seven.

39
00:02:46,060 --> 00:02:48,340
This is not in the list so I should get a message.

40
00:02:48,350 --> 00:02:49,170
Key not found.

41
00:02:49,270 --> 00:02:50,750
Yes, key not found.

42
00:02:50,860 --> 00:02:53,330
If I give the value seven then I should get a message.

43
00:02:53,410 --> 00:02:58,870
Key found and seven is key found and seven that it this is working.

44
00:02:58,870 --> 00:03:01,450
So this is an iterative version of linear search.

45
00:03:02,200 --> 00:03:09,760
Then let us write a recursive version of linear search function should return a note, a pointer and

46
00:03:09,760 --> 00:03:16,690
let us call this function as R search and it should take a parameter that struct node at this point

47
00:03:16,690 --> 00:03:20,800
it will first node and also a key that we want to search.

48
00:03:20,800 --> 00:03:22,210
Effie's null.

49
00:03:23,050 --> 00:03:24,730
It's not pointing on any node.

50
00:03:24,940 --> 00:03:33,380
Then return null means the key is not found, else it can return a recursive call to an external that

51
00:03:33,400 --> 00:03:36,520
Espy's data and also a key.

52
00:03:36,660 --> 00:03:37,260
That's it.

53
00:03:37,270 --> 00:03:45,040
If PS not null and it should check if key is equal to these data, if it gives equal to beat it, then

54
00:03:45,040 --> 00:03:54,310
it should return B, otherwise it should call itself recursively our search by sending these next and

55
00:03:54,310 --> 00:03:55,330
also a key.

56
00:03:55,350 --> 00:04:01,330
So here also I will write Rickon NetSol does the recursive version of Linear Search.

57
00:04:01,930 --> 00:04:05,710
Now instead of L search, I will pull our search here.

58
00:04:05,710 --> 00:04:09,670
That is recursive search and rest of the things are same inside main function.

59
00:04:10,120 --> 00:04:12,090
So I have just modified the function name.

60
00:04:12,100 --> 00:04:13,360
That is recursive search.

61
00:04:13,360 --> 00:04:16,269
I will be calling this function now I'm searching for a key value.

62
00:04:16,269 --> 00:04:16,630
Seven.

63
00:04:16,630 --> 00:04:17,170
Let us see.

64
00:04:17,170 --> 00:04:19,390
I should get the message that is found.

65
00:04:19,720 --> 00:04:23,140
Keys phone if I give twenty seven keys not phone.

66
00:04:23,350 --> 00:04:23,830
Yes.

67
00:04:23,830 --> 00:04:25,060
Here is the message.

68
00:04:25,060 --> 00:04:26,230
Keys not phone.

69
00:04:26,230 --> 00:04:28,750
If I give twenty five then keys phone.

70
00:04:29,350 --> 00:04:31,870
Yes I got a message that is keys phone.

71
00:04:32,020 --> 00:04:37,120
So that's all the demo for linear search using iteration as well as recursion.

72
00:04:37,120 --> 00:04:44,350
Now inside Linear Search, I have already discussed how we can perform multiple front operation so that

73
00:04:44,350 --> 00:04:47,200
next time when we search for the same key, it can be done faster.

74
00:04:47,680 --> 00:04:53,860
So for that I have to pick one more temporary pointer that is still pointer and let it be to an inside

75
00:04:53,860 --> 00:04:56,560
the loop before moving it to the next node.

76
00:04:56,560 --> 00:05:02,320
I should move up on the already we have discussed the call for just some modifying based on that call.

77
00:05:02,320 --> 00:05:07,630
And inside, if if the key is found, then I should make that key node as a first node.

78
00:05:07,630 --> 00:05:16,900
So for that I should say Gils next should point on these next and these next should point on first node

79
00:05:16,930 --> 00:05:23,530
and first it should be move upon the first point that is actually global here we have declared so this

80
00:05:23,530 --> 00:05:25,750
function can directly modify this first.

81
00:05:25,750 --> 00:05:33,130
So these are the three steps by which we can move a node, Kinnard, and make it as a first node that

82
00:05:33,130 --> 00:05:33,430
is had.

83
00:05:33,500 --> 00:05:34,000
No, no.

84
00:05:34,000 --> 00:05:40,800
Let's try this and see here inside the main function, I will call Elshaug function that is effectively

85
00:05:40,830 --> 00:05:42,700
nil search function, which we have modified.

86
00:05:42,770 --> 00:05:48,950
And made it as move to front, and I suppose I am searching for a of twenty five, then after searching

87
00:05:48,950 --> 00:05:51,770
when it is found it should be brought as the first node.

88
00:05:52,280 --> 00:05:55,390
So for testing this one I should call display function.

89
00:05:55,400 --> 00:06:00,160
So for testing this one I should call display function by sending first pointer.

90
00:06:00,650 --> 00:06:04,760
I should get that twenty five as the first element letters from this.

91
00:06:04,760 --> 00:06:05,300
Yes.

92
00:06:05,690 --> 00:06:06,940
Give on twenty five.

93
00:06:07,130 --> 00:06:12,440
And if you look at the list now, twenty five is the first node that is before three here I will try

94
00:06:12,440 --> 00:06:12,950
something.

95
00:06:12,950 --> 00:06:19,670
After performing linear search for twenty five I will again perform linear search for one more key value

96
00:06:19,670 --> 00:06:24,140
by passing first and I will also search for eight.

97
00:06:25,240 --> 00:06:30,790
Let's see what happens, see first hand if I should be brought as a first and again, it should be brought

98
00:06:30,790 --> 00:06:37,210
as a first note, then after these two search operation, I should get the keys as first aid, then

99
00:06:37,210 --> 00:06:37,840
25.

100
00:06:37,840 --> 00:06:40,000
Then after that, I should have three US run.

101
00:06:40,110 --> 00:06:40,410
Yeah.

102
00:06:40,420 --> 00:06:44,680
Here you can see that first aid and twenty five, then three, then five seven.

103
00:06:44,680 --> 00:06:46,170
Then three, two and two.

104
00:06:46,210 --> 00:06:50,380
So this eight and twenty five are removed and they are brought out of the Fastenal first time.

105
00:06:50,380 --> 00:06:50,950
Twenty five.

106
00:06:50,950 --> 00:06:52,210
The next is eight.

107
00:06:52,210 --> 00:06:54,680
So eight is the latest Fastenal.

108
00:06:54,700 --> 00:06:59,940
So next time if I search for eight it will be found faster just in one competition.

109
00:06:59,950 --> 00:07:05,570
So this all we can improve linear search in the Lincolnesque by performing move to front.

110
00:07:05,830 --> 00:07:09,700
So these will fuel lines for performing move to front.

111
00:07:09,820 --> 00:07:15,700
And here is giving a warning that is not initialized because here I have declared but not initialized.

112
00:07:15,700 --> 00:07:20,110
So it's guessing that it is not initialization because any error.

113
00:07:20,650 --> 00:07:22,720
So we should initialize the variables.

114
00:07:22,730 --> 00:07:25,350
If you initialize them, then this error will be gone anyway.

115
00:07:25,360 --> 00:07:26,170
It's not an issue.

116
00:07:26,170 --> 00:07:32,170
Our code is perfect because if is initialized here, that compiler is unable to see that.

117
00:07:32,170 --> 00:07:38,830
So it's giving you a warning or else we should remove this dual s.b and make it as the first statement.

118
00:07:39,160 --> 00:07:40,450
So it makes no difference.

119
00:07:40,450 --> 00:07:43,680
Whether it is done at displays or displays makes no difference.

120
00:07:44,170 --> 00:07:46,480
So if I can pilot, this error is gone.

121
00:07:46,790 --> 00:07:47,700
Warning is con.

122
00:07:47,740 --> 00:07:54,490
So that's all in this video we have seen in search and to version of Lenio Search that is iterative

123
00:07:54,490 --> 00:07:55,660
as well as recursive.

124
00:07:56,020 --> 00:07:58,690
Then also we have seen move to front operation.

