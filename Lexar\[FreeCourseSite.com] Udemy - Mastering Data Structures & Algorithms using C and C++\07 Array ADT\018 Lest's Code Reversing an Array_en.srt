1
00:00:00,630 --> 00:00:04,290
In this video, we look at operations upon <PERSON><PERSON>, that is.

2
00:00:05,380 --> 00:00:08,410
First, I would read on a function for reversing the contents of an.

3
00:00:12,250 --> 00:00:17,870
Rewards, it should take an array and it should take by others.

4
00:00:17,910 --> 00:00:20,170
So at this point, there are.

5
00:00:22,780 --> 00:00:25,240
Then for reversing, I have discussed two methods.

6
00:00:25,270 --> 00:00:29,230
So first method I will follow, I'll take one auxillary.

7
00:00:29,740 --> 00:00:31,720
So for that, I will take a pointer B.

8
00:00:33,840 --> 00:00:40,650
Then I need other variables like I and I will declare them also, the first thing is the Array B should

9
00:00:40,650 --> 00:00:42,930
be of size equal to eight.

10
00:00:43,410 --> 00:00:48,100
So I should pick the size of an array and create a B from heap.

11
00:00:48,150 --> 00:00:52,440
So I will treat a B from him by calling my function.

12
00:00:53,520 --> 00:00:54,960
So <PERSON>lark.

13
00:00:56,360 --> 00:00:57,740
There should be same as.

14
00:01:00,010 --> 00:01:05,379
Land, often we don't need size, we need land, lend number of elements that we are having.

15
00:01:06,310 --> 00:01:10,540
Then the next is size of its integer type, so it is integer.

16
00:01:14,600 --> 00:01:15,480
No, I haven't.

17
00:01:15,500 --> 00:01:21,860
Are they ready now, let us copy all the elements from Oraibi, from right hand side who are ready.

18
00:01:21,860 --> 00:01:25,520
So for that I will take a follow up like I start at.

19
00:01:28,040 --> 00:01:36,380
Aris Lanta minus one and G starts at zero and will continue as long as I is greater than equal to zero,

20
00:01:37,220 --> 00:01:38,810
then eight minus.

21
00:01:38,810 --> 00:01:41,480
Minus Anji plus plus.

22
00:01:43,580 --> 00:01:49,130
Every time in biology copy an element from AOF I.

23
00:01:52,650 --> 00:01:55,920
This will reveal all the elements of Arabi.

24
00:01:56,640 --> 00:02:02,850
Then again, I have to run a follow up starting from zero and I is less than land.

25
00:02:04,970 --> 00:02:10,530
I plus plus, I should copy all the elements from Arab, who are they?

26
00:02:10,729 --> 00:02:14,510
So if I should be assigned that to be of I.

27
00:02:20,590 --> 00:02:26,740
Yes, this will reverse an area that is called reverse function here, and then they will display an

28
00:02:26,740 --> 00:02:27,120
early.

29
00:02:29,630 --> 00:02:34,100
Rivers address of a next statement is displayed.

30
00:02:35,140 --> 00:02:40,960
So this function, Escalante's function will reverse all the contents of an array by using axillary

31
00:02:41,090 --> 00:02:47,230
a let us run the program, we should get this reverse, that is, instead of two, three, four, five,

32
00:02:47,230 --> 00:02:49,150
six, we should have six, five, four, three, two.

33
00:02:51,700 --> 00:02:55,660
Yes, the elements are reverse, six, five, four, three, two.

34
00:02:56,800 --> 00:03:03,310
So this function is working, so once again, see, I have created an auxiliary in heap that I have

35
00:03:03,310 --> 00:03:10,330
copied all the elements from other aid to a B in Arabia, started from right hand and Arabi, we started

36
00:03:10,330 --> 00:03:15,280
from left hand side, then popping all the elements from very back to Earth.

37
00:03:16,150 --> 00:03:19,380
This is one method and one moment that I have shown you.

38
00:03:19,390 --> 00:03:23,650
So let us write a function that is reverse to a second version.

39
00:03:28,710 --> 00:03:29,580
Reverts to.

40
00:03:31,890 --> 00:03:36,000
It should be taken up by address.

41
00:03:39,660 --> 00:03:40,540
Not in this matter.

42
00:03:40,560 --> 00:03:47,940
Also, I need two variables, I n g so let us take those variables and as I said, using a volume we

43
00:03:47,940 --> 00:03:50,000
can exchange the elements for.

44
00:03:53,940 --> 00:03:55,500
I assign Zettl.

45
00:03:58,000 --> 00:03:59,230
And Jason.

46
00:04:00,510 --> 00:04:07,980
Aris lenth minus one, that is from the last index, and we should continue as long as I less than G

47
00:04:08,790 --> 00:04:12,450
and I should increment and decrement.

48
00:04:17,329 --> 00:04:23,000
Then every time we swab the elements already we have written a function for swab in the previous videos

49
00:04:23,000 --> 00:04:26,280
I have shown you, the soft function will exceed the values.

50
00:04:26,300 --> 00:04:28,310
So this function will call from here.

51
00:04:31,480 --> 00:04:32,130
SLAPP.

52
00:04:36,220 --> 00:04:36,650
Of.

53
00:04:37,640 --> 00:04:38,150
I.

54
00:04:39,300 --> 00:04:39,780
That.

55
00:04:42,220 --> 00:04:48,670
Aw, gee, and I should pass the roses, so I should write unperson here for addresses.

56
00:04:55,700 --> 00:04:56,570
So Bexell.

57
00:04:57,640 --> 00:05:03,820
This reversionary, so let us call version two from main function here inside main function, that is

58
00:05:03,820 --> 00:05:05,890
called version two, that the three words to.

59
00:05:07,810 --> 00:05:08,780
Run the program.

60
00:05:09,710 --> 00:05:12,920
Yes, it is also reversing that a six, five, four, three, two.

61
00:05:13,520 --> 00:05:14,270
It's working.

62
00:05:16,590 --> 00:05:18,150
So this was about reversing an.

63
00:05:19,820 --> 00:05:21,230
So that's all in this video.

64
00:05:21,260 --> 00:05:26,960
We have two more functions remaining that is left shift and right left rotation.

65
00:05:26,990 --> 00:05:29,210
Similarly, we have right shift and right rotation.

66
00:05:29,570 --> 00:05:31,760
So you can try those functions by yourself.

67
00:05:32,160 --> 00:05:34,340
That is like that is a student exercise.

