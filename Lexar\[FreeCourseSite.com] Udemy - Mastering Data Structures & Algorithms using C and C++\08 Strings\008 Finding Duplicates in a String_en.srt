1
00:00:00,060 --> 00:00:03,870
Now, the topic is finding duplicates in a string.

2
00:00:05,390 --> 00:00:09,620
Here is the street we have to find out, are there any duplicate alphabets in this thing?

3
00:00:09,620 --> 00:00:15,380
For example, in a given listing, I is repeating more than one time and is repeating more than one

4
00:00:15,380 --> 00:00:15,660
time.

5
00:00:15,920 --> 00:00:19,610
So we have to find out, are there any alphabets which are repeating more than one time?

6
00:00:20,030 --> 00:00:26,210
So this is the same as finding duplicate elements in an array of integers, which we have already seen

7
00:00:26,210 --> 00:00:27,410
in the topic studies.

8
00:00:27,950 --> 00:00:30,950
So the same thing is for string.

9
00:00:30,950 --> 00:00:32,570
That is alphabets we have to check.

10
00:00:32,720 --> 00:00:35,050
So there are more than one method for finding duplicates.

11
00:00:35,060 --> 00:00:39,080
We have already seen multiple methods for finding duplicate numbers.

12
00:00:39,350 --> 00:00:44,640
Now we will see what are the methods for finding duplicate characters in this string.

13
00:00:45,440 --> 00:00:52,040
So the first method is comparing with other letters than the second one is using hash table or counting

14
00:00:52,040 --> 00:00:55,370
the letters, and then the third one is using bits.

15
00:00:56,420 --> 00:01:02,780
Now, this method, the first method is same as what we have learned in arrays like taking a number

16
00:01:02,780 --> 00:01:05,160
and comparing the first of the numbers in an array.

17
00:01:05,180 --> 00:01:07,830
So we will take an alphabet and compare it with the rest of the alphabet.

18
00:01:07,830 --> 00:01:08,410
Cincinnati.

19
00:01:08,960 --> 00:01:10,010
So the method is seen.

20
00:01:10,400 --> 00:01:11,950
So I will not explain here in detail.

21
00:01:12,290 --> 00:01:17,570
You can refer back to the video finding duplicate elements in an array of indigence.

22
00:01:18,600 --> 00:01:24,170
Then second method is also similar to the one we have already seen, but there is a little change here,

23
00:01:24,450 --> 00:01:25,690
so I would explain this one.

24
00:01:26,460 --> 00:01:30,770
Then the third one is the new one that is using bits that I will cover in the next video.

25
00:01:31,590 --> 00:01:38,310
So let us start with the first method, comparing with other elements, literally comparing our alphabet

26
00:01:38,310 --> 00:01:39,360
with the rest of the alphabet.

27
00:01:39,600 --> 00:01:44,010
So the procedure is we will start scanning this thing from left hand side.

28
00:01:44,280 --> 00:01:45,330
Let us start from here.

29
00:01:45,360 --> 00:01:49,860
First off, now we have to check, is there any other F instead of string?

30
00:01:50,550 --> 00:01:59,250
If so, we have to count it so we can take help of G counter and start checking as it f no next character.

31
00:01:59,250 --> 00:02:00,270
Is it F No.

32
00:02:00,270 --> 00:02:02,190
Is it is it equal to this?

33
00:02:02,640 --> 00:02:05,400
Is this equal to the arsenal as an equal to this.

34
00:02:05,430 --> 00:02:11,520
No, we have reached the end, so we have scanned through the rest of the characters in a string then

35
00:02:12,090 --> 00:02:13,300
F is not found.

36
00:02:13,500 --> 00:02:15,400
Next we will move to the next character.

37
00:02:16,380 --> 00:02:19,110
Is it the same as I know as it seems.

38
00:02:19,500 --> 00:02:19,950
This one.

39
00:02:20,170 --> 00:02:20,780
Is it the same?

40
00:02:20,820 --> 00:02:21,900
Yes, it is found.

41
00:02:22,140 --> 00:02:23,900
So we found one duplicate.

42
00:02:24,120 --> 00:02:28,890
So I already told you that we should remove this and make it to zero so that it should not be counted

43
00:02:28,890 --> 00:02:29,580
once again.

44
00:02:30,120 --> 00:02:30,430
Right.

45
00:02:30,690 --> 00:02:31,820
Then check with this one.

46
00:02:31,830 --> 00:02:33,410
No chicken with this one.

47
00:02:33,420 --> 00:02:37,610
No, we have reached the end, so we have found one more.

48
00:02:38,220 --> 00:02:42,620
So there are two occurrences of this one and another one that we found.

49
00:02:43,290 --> 00:02:48,540
So the similar procedure you can follow, you can check the video once again for finding duplicate elements

50
00:02:48,540 --> 00:02:51,330
in an array like this method I have shown you.

51
00:02:51,570 --> 00:02:52,710
So it's the same method.

52
00:02:52,740 --> 00:02:58,530
Only thing is here, the alphabet for father and you have to stop when you are reaching null character.

53
00:02:59,250 --> 00:02:59,580
Right.

54
00:02:59,760 --> 00:03:04,080
And we have also done the analysis for that one, the time taken in order of N squared.

55
00:03:04,530 --> 00:03:06,270
That is big off and square.

56
00:03:07,550 --> 00:03:12,270
So the same thing, same analysis, the same procedure, only these are alphabet's.

57
00:03:12,290 --> 00:03:13,990
All right, so I don't want to elaborate this one.

58
00:03:14,360 --> 00:03:22,450
Now, let us go to the second method that is using hash table or counting the occurrences of alphabets.

59
00:03:22,700 --> 00:03:26,670
So let us look at hash table for using hash table.

60
00:03:26,690 --> 00:03:29,600
We should have an order to work out and hash table.

61
00:03:29,900 --> 00:03:31,190
Now, what should be the size of a.

62
00:03:31,880 --> 00:03:34,170
That depends on the numbers that we are storing.

63
00:03:34,550 --> 00:03:38,940
So whatever the largest number that you are storing, we need an area of that size.

64
00:03:39,510 --> 00:03:39,870
Yes.

65
00:03:39,890 --> 00:03:42,070
So let us see what we are actually going to store.

66
00:03:42,660 --> 00:03:47,940
We are going to store Alphabet's and these alphabets are having their ASCII calls.

67
00:03:48,080 --> 00:03:48,590
Yes.

68
00:03:48,890 --> 00:03:49,840
ASCII code.

69
00:03:50,210 --> 00:03:53,180
I have taken all these alphabets, I's and lowercase.

70
00:03:53,540 --> 00:03:56,260
So they are in the range of lowercase alphabets.

71
00:03:56,630 --> 00:03:59,460
So we should know that ASCII code of all these alphabet.

72
00:03:59,480 --> 00:04:02,990
So let me write on as equals three as he called off.

73
00:04:03,170 --> 00:04:09,590
These is hundred and this will not to G is not three.

74
00:04:10,490 --> 00:04:12,230
I will not five.

75
00:04:13,910 --> 00:04:15,680
And unless one Dan.

76
00:04:17,899 --> 00:04:19,310
These are the ASCII quotes.

77
00:04:20,810 --> 00:04:23,770
So I think those are more than 100, right?

78
00:04:24,080 --> 00:04:32,180
So if you know the range of lower cases, the lower case range starts from 97 to 122.

79
00:04:32,480 --> 00:04:41,600
So it means I need to hurry up to the maximum length of 122 and I will start using that idea from 97

80
00:04:41,600 --> 00:04:45,980
onwards means all the elements from zero to nine to six are useless.

81
00:04:46,610 --> 00:04:53,300
Why do you create that much, Lendio, when we know that the first letter lowercase A. is ninety seven

82
00:04:53,600 --> 00:04:55,990
and lowercase that is 122.

83
00:04:56,660 --> 00:04:59,240
So why can't we call this as a zero.

84
00:04:59,900 --> 00:05:00,180
Right.

85
00:05:00,500 --> 00:05:02,050
And the B as one and so on.

86
00:05:02,420 --> 00:05:06,260
So this will be 25 so zero five total thirty six.

87
00:05:06,680 --> 00:05:08,740
So it means an array of size.

88
00:05:08,800 --> 00:05:10,660
Twenty five will be sufficient for us.

89
00:05:11,000 --> 00:05:11,520
Yes.

90
00:05:11,690 --> 00:05:13,930
Array of twenty five is sufficient.

91
00:05:14,450 --> 00:05:17,900
So this is how we can reduce the size of the hash table.

92
00:05:18,080 --> 00:05:25,100
We can take just off size twenty six, that is twenty five and we can find out the counting of these

93
00:05:25,100 --> 00:05:27,200
alphabets and duplicates.

94
00:05:27,200 --> 00:05:27,870
Alphabets.

95
00:05:28,040 --> 00:05:28,300
Right.

96
00:05:28,730 --> 00:05:29,390
And Mindich.

97
00:05:29,450 --> 00:05:32,120
This is only for lowercase alphabets.

98
00:05:32,330 --> 00:05:33,590
There are no uppercase.

99
00:05:34,040 --> 00:05:38,870
So let us draw in a hash table and run the procedure and see how we can count them.

100
00:05:40,530 --> 00:05:45,140
So here I have an idea for hash table that is starting index zero and ending in Texas.

101
00:05:45,510 --> 00:05:47,700
Twenty five, so I have an idea of five.

102
00:05:47,700 --> 00:05:48,350
Twenty six.

103
00:05:49,050 --> 00:05:54,650
Now let us run the procedure and scan for the strength, not one moving this array.

104
00:05:54,750 --> 00:05:57,160
Assume that it is filled with the Zeitels, right.

105
00:05:57,450 --> 00:06:00,980
I did not fill it with zero to make it just simple to look simple.

106
00:06:01,230 --> 00:06:03,210
I have kept them and left them empty.

107
00:06:03,230 --> 00:06:06,480
Assume that that are all Zeitels right now.

108
00:06:06,480 --> 00:06:16,050
Let's scan through this string first alphabet f ASCII Code of ethics one zero two right minus ninety

109
00:06:16,050 --> 00:06:16,620
seven.

110
00:06:16,620 --> 00:06:19,200
If we do this is five.

111
00:06:19,560 --> 00:06:22,830
So go to the next five and already to zero.

112
00:06:22,830 --> 00:06:23,970
Just increment it.

113
00:06:24,060 --> 00:06:24,840
So it is one.

114
00:06:26,070 --> 00:06:34,740
Then move to the next alphabet, I mean, this is one, not five, so I guess one, not five minus ninety

115
00:06:34,740 --> 00:06:35,100
seven.

116
00:06:35,100 --> 00:06:37,860
If you do, then this will be eight.

117
00:06:38,130 --> 00:06:41,340
So go to index eight and increment it.

118
00:06:41,350 --> 00:06:42,370
So it was zero.

119
00:06:42,400 --> 00:06:46,080
Just make it as one plus one the next and the.

120
00:06:46,970 --> 00:06:57,350
And aski gordis 110, right, subtract 97, so it 13, go to the next 13 and incremented, then move

121
00:06:57,350 --> 00:06:58,360
to the next alphabet.

122
00:06:58,400 --> 00:07:04,130
That is a D d I think all is 100 minus 97 gives three.

123
00:07:04,520 --> 00:07:08,260
Go to index three and increment it then next.

124
00:07:08,750 --> 00:07:14,510
I, I asked for this one, not five minus ninety seven eight.

125
00:07:14,780 --> 00:07:16,340
Go to index eight and increment.

126
00:07:16,370 --> 00:07:17,810
So this was already one.

127
00:07:17,810 --> 00:07:20,630
So it will be come to the next and.

128
00:07:21,810 --> 00:07:32,070
Putting go a he won't make it to the next F g g ASCII code as one, not two, three minus ninety seven.

129
00:07:32,070 --> 00:07:35,460
So there's a six to index, six and incremented.

130
00:07:36,510 --> 00:07:38,190
Then next, it's another.

131
00:07:39,230 --> 00:07:46,460
So that's all we have finished the string, now we have the counting of all the alphabets, see first

132
00:07:46,520 --> 00:07:52,480
is representing zero zero zero plus ninety seven, right.

133
00:07:52,850 --> 00:07:57,150
Plus ninety seven because we have subtract that ninety seven and ninety seven to this one.

134
00:07:57,500 --> 00:08:05,130
Ninety seven is zero times then plus one ninety eight Baeza zero time sees also zero time D one time.

135
00:08:05,330 --> 00:08:09,420
Likewise we know the pounding of each and every alphabet in a string.

136
00:08:09,650 --> 00:08:11,510
So if you want to know what is this one.

137
00:08:11,630 --> 00:08:15,100
So I'm ninety seven to ninety seven plus eight.

138
00:08:15,410 --> 00:08:19,100
So this is one of three and one of three is G.

139
00:08:19,520 --> 00:08:24,800
So that's what we can get back the alphabet by adding ninety seven into the values in the hash table.

140
00:08:25,980 --> 00:08:26,440
Right.

141
00:08:26,460 --> 00:08:32,280
So this all you can count on, if any number is more than one time, then there are duplicates.

142
00:08:33,090 --> 00:08:33,470
All right.

143
00:08:33,870 --> 00:08:39,539
So let us write on a piece of code here to perform the same procedure on the display.

144
00:08:39,539 --> 00:08:46,350
Only those alphabets, which are appearing more than one time like I and and it's appearing more than

145
00:08:46,350 --> 00:08:48,290
one time only those.

146
00:08:48,690 --> 00:08:51,000
So what all we have done, I've already done the same thing here.

147
00:08:51,330 --> 00:08:54,420
So first of all, I need a hash table edge of size.

148
00:08:55,730 --> 00:08:56,180
Six.

149
00:08:57,920 --> 00:09:07,150
And I mean, I also know using a for loop, I will scan drawstring for asylum if I is not equal to null

150
00:09:07,160 --> 00:09:09,340
character and I plus.

151
00:09:11,960 --> 00:09:14,500
No, for every alphabet or whatever it is, alphabet.

152
00:09:14,560 --> 00:09:16,810
Subtract 97 and stored in the hash table.

153
00:09:17,150 --> 00:09:26,570
So in a hash table at what index, whatever it is from that, subtract 97 and either one.

154
00:09:26,580 --> 00:09:31,300
So for that I will assign plus assign one or even I can C++.

155
00:09:31,460 --> 00:09:33,430
So I'm incrementing it.

156
00:09:33,590 --> 00:09:42,620
So in a hash table at a given index, like for example if I f f f f one or two this is one or two,

157
00:09:43,040 --> 00:09:44,690
one or two minus ninety seven.

158
00:09:44,870 --> 00:09:46,390
Humorlessness five.

159
00:09:46,640 --> 00:09:47,740
So this is five.

160
00:09:47,750 --> 00:09:52,680
So this becomes what each of five Ashrafi is incremented by one.

161
00:09:52,700 --> 00:09:55,960
So I show five plus one and it becomes like this.

162
00:09:56,360 --> 00:10:00,650
So for every alphabet it will be calculated in the same way.

163
00:10:00,830 --> 00:10:02,290
So the indices are looking.

164
00:10:02,660 --> 00:10:06,770
So this is how the entire hash table will be filled.

165
00:10:07,550 --> 00:10:14,380
The next I should scan for this hash array and find out if any value is more than one.

166
00:10:14,480 --> 00:10:17,030
If it is more than one, Alphabet is repeating.

167
00:10:17,300 --> 00:10:18,440
So I'll continue writing.

168
00:10:18,650 --> 00:10:19,960
So this is the end of time.

169
00:10:19,980 --> 00:10:21,160
We have scanned for a string.

170
00:10:21,170 --> 00:10:23,210
Now we have to scan through a hash table.

171
00:10:23,510 --> 00:10:27,650
How stable starts from zero and hash table and at twenty five.

172
00:10:27,710 --> 00:10:28,970
So less than twenty six.

173
00:10:28,970 --> 00:10:31,450
I can write here then I plus plus.

174
00:10:31,910 --> 00:10:38,900
Then what I have to do is if at any place of each of I if it is greater than one means there are more

175
00:10:38,900 --> 00:10:40,490
than one occurrence of an alphabet.

176
00:10:40,730 --> 00:10:41,650
So which alphabet.

177
00:10:41,660 --> 00:10:42,800
So I will print it.

178
00:10:43,130 --> 00:10:48,060
Print F button's percentile C I have to print the alphabet.

179
00:10:48,260 --> 00:10:51,820
So what I should do suppose here it is more than one eight.

180
00:10:52,100 --> 00:10:53,450
So at ninety seven.

181
00:10:53,460 --> 00:10:58,230
So it is eight plus ninety seven and this will not five and not five is I.

182
00:10:58,430 --> 00:11:00,560
So this I will be adding more than one time.

183
00:11:00,570 --> 00:11:04,280
So I have to add ninety seven for this index.

184
00:11:04,370 --> 00:11:08,210
So whatever the index is right now I is pointing here.

185
00:11:08,210 --> 00:11:08,610
Right.

186
00:11:08,630 --> 00:11:16,850
So it just pointing here so I plus ninety seven and also how many times it is printing appearing.

187
00:11:16,850 --> 00:11:18,380
I have to print that one also.

188
00:11:18,410 --> 00:11:22,550
So print f percentile D I will print the number of times.

189
00:11:22,560 --> 00:11:29,930
So that is if I give you the number of times this will be done, only if the value of that hash table

190
00:11:29,930 --> 00:11:31,090
is a greater than one.

191
00:11:31,610 --> 00:11:34,210
So that's all this is the procedure.

192
00:11:34,670 --> 00:11:39,580
So you can know the alphabets which are appearing more than one time, duplicates.

193
00:11:39,590 --> 00:11:43,550
You can find out not if we do the analysis, how much time it is taking.

194
00:11:44,030 --> 00:11:45,650
We are scanning through this string.

195
00:11:45,650 --> 00:11:47,320
So string this linear always.

196
00:11:47,330 --> 00:11:48,000
So let us see.

197
00:11:48,020 --> 00:11:51,560
And and again, we are scanning for this hash table.

198
00:11:51,830 --> 00:11:53,380
So this is also linear.

199
00:11:53,540 --> 00:11:57,410
So this also and I just said that we don't check the sizes.

200
00:11:57,410 --> 00:12:03,410
We see that whether the behavior is linear or not, like the number of alphabets here are total seven

201
00:12:04,310 --> 00:12:06,320
and hash table sizes twenty six.

202
00:12:06,560 --> 00:12:08,540
We don't say that these are different.

203
00:12:08,660 --> 00:12:09,050
Right.

204
00:12:09,330 --> 00:12:10,700
We say that this is linear.

205
00:12:10,730 --> 00:12:12,640
So n this also linear.

206
00:12:12,650 --> 00:12:15,380
And so this is two and two times linear.

207
00:12:15,380 --> 00:12:17,600
So two times linear is also linear.

208
00:12:18,110 --> 00:12:20,570
So the time as we go off and.

209
00:12:21,590 --> 00:12:25,980
Now, this is truly student exercise, so you have to ride on this program and check it by yourself.

210
00:12:26,000 --> 00:12:29,590
There is no demonstration on this one, so you do it by yourself.

211
00:12:30,020 --> 00:12:34,100
And one more thing I have shown only for lower case alphabet's.

212
00:12:34,430 --> 00:12:39,410
Now, if there are poor kids also right or upper and lower case, those are mixed.

213
00:12:39,620 --> 00:12:42,350
Then you have to increase the size of hash table.

214
00:12:42,570 --> 00:12:45,020
You may have to take up to size 122.

215
00:12:45,230 --> 00:12:46,770
So you try for that one also.

216
00:12:47,480 --> 00:12:48,750
So that's all in this video.

217
00:12:49,070 --> 00:12:54,650
Now, there is one more method that is using bits that I will explain you in the next video.

