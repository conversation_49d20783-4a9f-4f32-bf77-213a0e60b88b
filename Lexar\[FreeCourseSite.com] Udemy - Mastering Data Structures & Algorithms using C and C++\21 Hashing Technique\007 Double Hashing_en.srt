1
00:00:00,520 --> 00:00:06,250
Now the topic is double harshing, we have already seen <PERSON><PERSON><PERSON> probing and probing, those were the methods

2
00:00:06,250 --> 00:00:07,700
for resolving collusion.

3
00:00:07,720 --> 00:00:12,440
So this is also a method for resolving collusion.

4
00:00:13,000 --> 00:00:15,720
The idea here is we will have to hash function.

5
00:00:16,149 --> 00:00:18,490
What is the basic function then?

6
00:00:18,490 --> 00:00:23,240
If there is a collusion, then the other hash function will help us to remove collusion.

7
00:00:23,620 --> 00:00:26,370
So for explanation, I have take an example here.

8
00:00:26,830 --> 00:00:29,800
See, this is the first hash function, Exmoor 10.

9
00:00:29,860 --> 00:00:33,400
So that is a regular hash function that we have used in various examples.

10
00:00:33,640 --> 00:00:34,540
So each one is.

11
00:00:35,360 --> 00:00:38,890
Exmoor, 10, because the size of the hash table 10.

12
00:00:40,960 --> 00:00:42,880
Then this is the second hash function.

13
00:00:43,770 --> 00:00:49,080
That is the primary number that is smaller than the size of the hash table, so the size of the hash

14
00:00:49,080 --> 00:00:51,300
table is 10 and less than 10.

15
00:00:51,300 --> 00:00:53,450
What is the nearest prime number?

16
00:00:53,730 --> 00:00:54,540
That is seven.

17
00:00:54,750 --> 00:00:56,160
So ours should be seven.

18
00:00:56,340 --> 00:00:57,420
Avenal more defiant.

19
00:01:03,700 --> 00:01:09,780
So I have modified it to seven, actually, second hash function is having two desired properties.

20
00:01:10,300 --> 00:01:13,520
One is it should never give a result zero.

21
00:01:13,720 --> 00:01:15,760
It should not give index is the first one.

22
00:01:16,120 --> 00:01:22,450
And the second thing is it should try to probe all the locations means whenever there is a collision,

23
00:01:22,930 --> 00:01:25,390
it should not give the indices in the same pattern.

24
00:01:25,630 --> 00:01:32,470
It should give different illnesses such that all the locations are utilised so far that the seditionists,

25
00:01:32,770 --> 00:01:35,060
this form of hash function that the prime number.

26
00:01:35,380 --> 00:01:41,650
So is the suggestion has functions of use this one or else you can design any other hash function you

27
00:01:41,650 --> 00:01:42,010
like.

28
00:01:42,040 --> 00:01:42,640
But two things.

29
00:01:42,640 --> 00:01:43,450
You have to take care.

30
00:01:43,460 --> 00:01:45,670
The result of that hash function should not be zero.

31
00:01:46,000 --> 00:01:50,270
And the second thing is it should try to cover all the indices.

32
00:01:50,470 --> 00:01:54,010
The next thing we will use the regular hash function.

33
00:01:54,370 --> 00:01:58,760
If there is a collision, then we will try to use this modified hash function.

34
00:01:59,200 --> 00:02:01,590
So let me explain this modified hash function.

35
00:02:01,870 --> 00:02:11,310
This is each one, this one scene plus I endou resilient of second hash function and and all this more

36
00:02:11,320 --> 00:02:12,940
by the size of the hash dribble.

37
00:02:13,920 --> 00:02:21,060
Then this value should be multiplied by the writings, the value zero one two onwards, so it is similar

38
00:02:21,060 --> 00:02:23,550
to linear probing and probing.

39
00:02:24,540 --> 00:02:27,420
So the first time when you are using I rally will be zero.

40
00:02:27,450 --> 00:02:29,180
So this is of no use.

41
00:02:29,190 --> 00:02:30,990
So we can say that directly.

42
00:02:30,990 --> 00:02:34,700
We'll use this hash function for first time, second time onwards.

43
00:02:34,710 --> 00:02:37,080
I will be one then this is considerable.

44
00:02:37,350 --> 00:02:39,920
Then we can use this modified hash function.

45
00:02:40,440 --> 00:02:46,530
So for demonstrating double harshing, I already have a key space and the hash table really let us map

46
00:02:46,530 --> 00:02:49,590
these guys on the table using this double hash.

47
00:02:50,400 --> 00:02:51,300
So let us stop.

48
00:02:52,860 --> 00:02:59,160
First case of five five, more than five or so, this is my on a location five.

49
00:02:59,610 --> 00:03:00,900
So five is stored here.

50
00:03:02,220 --> 00:03:07,580
There are twenty five, twenty five more, ten as a five, so this is again mapped on the same location.

51
00:03:07,880 --> 00:03:09,050
So this is a collision.

52
00:03:10,510 --> 00:03:15,940
Then I should use this modified hash function, modified hash function for.

53
00:03:17,440 --> 00:03:24,820
Thirty five is this result we got that's five for five plus I should be one now.

54
00:03:25,000 --> 00:03:26,350
Earlier I was zero.

55
00:03:26,380 --> 00:03:32,230
That's why we took directly five and found that there is a collision, that I should be one in two as

56
00:03:32,320 --> 00:03:32,980
two affects.

57
00:03:33,100 --> 00:03:35,090
So I should try it on twenty five here.

58
00:03:35,110 --> 00:03:36,620
So what is the result of twenty five.

59
00:03:36,910 --> 00:03:37,990
I will calculated.

60
00:03:40,000 --> 00:03:43,270
Seven minus twenty five more.

61
00:03:43,310 --> 00:03:43,970
Seven.

62
00:03:44,530 --> 00:03:48,420
So this is seven minus, this is four, so this is three.

63
00:03:48,760 --> 00:03:53,200
So it should be three more and this will be five plus three.

64
00:03:53,230 --> 00:03:54,100
That is eight.

65
00:03:54,430 --> 00:03:57,520
So twenty five should be stored at index eight.

66
00:03:57,640 --> 00:04:01,740
So twenty five is a story here, not 15.

67
00:04:01,750 --> 00:04:02,500
See the keys.

68
00:04:02,500 --> 00:04:04,480
I have taken all running with five.

69
00:04:04,490 --> 00:04:06,680
So they will collide with this index.

70
00:04:06,790 --> 00:04:07,080
Right.

71
00:04:07,390 --> 00:04:11,650
So all are colliding only so we can see it's extreme cases also.

72
00:04:12,040 --> 00:04:16,870
So let us map 15, 15 more 10 as the same index.

73
00:04:17,870 --> 00:04:18,360
Collusion.

74
00:04:18,980 --> 00:04:22,160
Now, let us use this modified hash function for 15.

75
00:04:25,310 --> 00:04:30,440
The result of this function was five, so there is a collision five plus no one is.

76
00:04:30,440 --> 00:04:36,020
Do we have to get the result of this function that is H2 of X axis 15.

77
00:04:36,030 --> 00:04:45,140
So I will evaluate this one seven minus 15 mod seven seven minus C seven will divide 15 four two times

78
00:04:45,140 --> 00:04:45,540
14.

79
00:04:45,540 --> 00:04:46,490
The remainder is one.

80
00:04:46,520 --> 00:04:48,170
So this is seven minus one.

81
00:04:48,200 --> 00:04:49,310
This is six.

82
00:04:49,880 --> 00:04:52,880
So this is six more then.

83
00:04:53,600 --> 00:04:56,360
So five plus five is for this one plus.

84
00:04:56,360 --> 00:05:01,560
Plus as it is I is one then S2 affects the six that I have evaluated here.

85
00:05:02,300 --> 00:05:04,960
So this is 11 more ten.

86
00:05:04,970 --> 00:05:06,330
So the answer is one.

87
00:05:06,890 --> 00:05:10,010
So this 15 should be stored at index one.

88
00:05:10,160 --> 00:05:12,020
The next is thirty five.

89
00:05:13,380 --> 00:05:14,170
Thirty five more.

90
00:05:14,190 --> 00:05:15,370
Ten as a five.

91
00:05:15,390 --> 00:05:22,070
So there is a little map there, only then we should use this modified hash function as a dash of thirty

92
00:05:22,080 --> 00:05:26,760
five is each one of thirty five.

93
00:05:26,760 --> 00:05:29,430
Just now we got down to the five, four, five plus.

94
00:05:29,910 --> 00:05:33,740
I should be one now in two is two of thirty five.

95
00:05:33,750 --> 00:05:37,230
Let us calculate seven minus thirty five.

96
00:05:37,230 --> 00:05:38,490
More than seven.

97
00:05:39,150 --> 00:05:45,020
So seven minus this is zero thirty five will get exactly divided by seven so that when there is zero

98
00:05:45,030 --> 00:05:47,630
so this is seven minus zero is seven.

99
00:05:48,000 --> 00:05:49,470
So this is seven more.

100
00:05:49,690 --> 00:05:50,070
Ten.

101
00:05:51,750 --> 00:05:59,250
So this is five plus seven, that is 12 more 10 to so thirty five should be stored at two.

102
00:06:02,130 --> 00:06:08,290
So one thing to observe here is that whenever there is a collision for any key one, I am finding out

103
00:06:08,290 --> 00:06:10,200
the modified hash function value.

104
00:06:10,420 --> 00:06:12,240
I'm getting different locations.

105
00:06:13,180 --> 00:06:18,130
A linear probing or quadratic probing, we were getting the same location next, plus one or two plus

106
00:06:18,190 --> 00:06:20,550
four like plus one and plus one.

107
00:06:20,560 --> 00:06:27,240
So every time next location in linear probing and probing was giving us plus one or plus a four plus

108
00:06:27,250 --> 00:06:27,800
a nine.

109
00:06:27,820 --> 00:06:29,050
So it was quadratic.

110
00:06:29,560 --> 00:06:35,080
But here every time we are getting different location though, all these elements are being mapped at

111
00:06:35,080 --> 00:06:36,180
the same index five.

112
00:06:36,940 --> 00:06:43,320
So that's what this double hash function is giving us for different locations, for different keys.

113
00:06:45,380 --> 00:06:51,080
Now, the last one remaining is ninety five, ninety five more ten, so that is five again.

114
00:06:51,350 --> 00:06:53,350
So it will be marked here only so far.

115
00:06:53,360 --> 00:06:53,960
Ninety five.

116
00:06:53,960 --> 00:07:01,610
I will calculate here as a dash of ninety five as five plus this is five plus.

117
00:07:01,610 --> 00:07:03,550
I should be one in.

118
00:07:05,410 --> 00:07:13,940
As you do affects that is seven minus a key mod, seven, seven minus the keys, ninety five more seven.

119
00:07:14,800 --> 00:07:16,090
So what is the result here?

120
00:07:16,810 --> 00:07:17,910
So this is seven.

121
00:07:18,250 --> 00:07:19,690
And remember, if you check.

122
00:07:20,890 --> 00:07:23,650
This is before, so the answer is three.

123
00:07:24,920 --> 00:07:28,470
See, when we evaluate this answer is three, four, twenty five.

124
00:07:28,520 --> 00:07:33,230
Also, we got the same thing then for ninety five, we are getting three once again.

125
00:07:34,280 --> 00:07:34,650
Right.

126
00:07:35,120 --> 00:07:42,680
So this is five plus one and two, three more, and then this is eight five plus three.

127
00:07:42,680 --> 00:07:43,810
Eight more, ten is eight.

128
00:07:44,090 --> 00:07:46,790
So eight now this is second collision.

129
00:07:47,120 --> 00:07:50,030
See, for this key, we got two collisions still.

130
00:07:50,030 --> 00:07:52,200
No, we did not get the two collisions at all.

131
00:07:53,210 --> 00:07:56,750
Every time the key was placed at some unique place, that was empty place.

132
00:07:57,540 --> 00:08:01,700
So there's only one collision was happening a second time we were getting a free space.

133
00:08:02,000 --> 00:08:04,190
But now this is collision once again.

134
00:08:04,610 --> 00:08:11,510
Then what I should do, I should calculate the same thing that the do here so that for now I is taking

135
00:08:11,510 --> 00:08:12,440
value to now.

136
00:08:12,530 --> 00:08:13,760
First time zero.

137
00:08:14,210 --> 00:08:15,250
That was just five.

138
00:08:15,380 --> 00:08:16,010
No one.

139
00:08:16,190 --> 00:08:18,670
It has given us eight, then two.

140
00:08:18,680 --> 00:08:21,990
So this will be five plus six 11.

141
00:08:22,110 --> 00:08:26,270
Levermore ten is one, one is already occupied.

142
00:08:26,300 --> 00:08:27,470
So again, collision.

143
00:08:27,650 --> 00:08:32,770
So total three collision one at five and five then at eight danetta one.

144
00:08:33,140 --> 00:08:37,690
So I have to modify this I and take the next value of that is three.

145
00:08:38,510 --> 00:08:40,400
So this is five plus nine.

146
00:08:40,400 --> 00:08:42,950
That is fourteen more than is four.

147
00:08:44,010 --> 00:08:47,400
Yes, a Ford is located right on ninety five here.

148
00:08:49,310 --> 00:08:55,850
So this is the key for which there were more than one collusions, otherwise just one collusions, second

149
00:08:56,120 --> 00:08:59,600
index was valid index or empty location.

150
00:08:59,810 --> 00:09:03,200
So this double hashing reduces collusions.

151
00:09:04,950 --> 00:09:11,440
So that's all about pashing, so we have seen various hashing methods chaining, linear probing, already

152
00:09:11,490 --> 00:09:13,200
probing and double hasheem.

153
00:09:14,550 --> 00:09:16,590
So that's all about hashing methods.

154
00:09:17,480 --> 00:09:23,030
So then in the next video, we'll look at hash functions, various hash functions, and also will have

155
00:09:23,030 --> 00:09:25,550
some general discussion about hash.

