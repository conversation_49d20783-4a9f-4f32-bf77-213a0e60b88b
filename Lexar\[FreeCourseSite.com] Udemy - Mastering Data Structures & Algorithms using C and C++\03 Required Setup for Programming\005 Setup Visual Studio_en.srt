1
00:00:02,360 --> 00:00:09,530
In this video, we will see how to download and set up Visual Studio to open a browser and search for

2
00:00:09,530 --> 00:00:11,000
Download Visual Studio.

3
00:00:17,580 --> 00:00:20,340
He, our first linguist from Microsoft, so click on it.

4
00:00:21,860 --> 00:00:25,340
It will take you to the website, Microsoft Web site here.

5
00:00:25,370 --> 00:00:32,060
There are various version of Visual Studio available, so you can select the first one that is for community

6
00:00:32,060 --> 00:00:37,330
and it's a free version for students and for researchers.

7
00:00:37,520 --> 00:00:41,150
So select the first one click on free download.

8
00:00:48,390 --> 00:00:49,620
It will start downloading.

9
00:00:52,280 --> 00:00:54,470
And it will pop up and ask you to select the.

10
00:00:55,670 --> 00:01:01,010
Type of applications that they are going to develop while it is downloading, it allows you to select

11
00:01:01,010 --> 00:01:06,440
the type of applications that will be developing so you can select a desktop application and sign up

12
00:01:06,440 --> 00:01:09,450
by entering your email and selecting your country.

13
00:01:09,710 --> 00:01:13,200
Click on the file for installation after sign up.

14
00:01:13,220 --> 00:01:14,570
You can start installation.

15
00:01:15,760 --> 00:01:17,480
You can hear the installation starts.

16
00:01:22,670 --> 00:01:29,690
Yes, here it gets options for what purpose you want to download C++ so you can select desktop development

17
00:01:29,690 --> 00:01:36,320
with C++, so select this option and click on install.

18
00:01:40,640 --> 00:01:41,360
And strong.

19
00:01:43,210 --> 00:01:48,460
Northville will install Visual Studio for C++.

20
00:01:58,750 --> 00:02:02,680
Once the installation is over, you have to restart your computer, so you started.

21
00:02:05,300 --> 00:02:12,160
So the installation of Visual Studio has finished now let us start Visual Studio and Development First

22
00:02:12,170 --> 00:02:18,090
Project So Good will start and he and I will type Visual Studio.

23
00:02:18,110 --> 00:02:22,100
So there is a Visual Studio 2019 for click on this one.

24
00:02:24,060 --> 00:02:34,050
It has started now here, say, create a new project, so select this one right for every program that

25
00:02:34,050 --> 00:02:38,550
you write, I suggest you create a new project that is better and you can have all the projects in one

26
00:02:38,550 --> 00:02:39,000
folder.

27
00:02:40,260 --> 00:02:43,190
Next here you have to select the type of the project.

28
00:02:43,200 --> 00:02:48,900
So on the top left, you'll find here the language so selected as C++.

29
00:02:49,710 --> 00:02:50,130
Right.

30
00:02:50,490 --> 00:02:52,160
And the platform is Windows.

31
00:02:52,170 --> 00:02:53,940
OK, you need not select that one.

32
00:02:54,130 --> 00:02:56,340
You can leave it as it is for all platform.

33
00:02:56,700 --> 00:03:00,630
If you scroll down, you'll find one option that is gunsel app.

34
00:03:01,140 --> 00:03:03,430
So you have to select gunsel, right?

35
00:03:03,950 --> 00:03:07,680
So all the applications that we are going to develop are council applications.

36
00:03:07,680 --> 00:03:11,610
So you always have to select this one, say next.

37
00:03:12,960 --> 00:03:16,080
Now here you have to mention the project name.

38
00:03:16,080 --> 00:03:24,210
So I'll give the project name as my first and you have to select the part so you can select the part

39
00:03:24,210 --> 00:03:25,950
and then say create.

40
00:03:32,630 --> 00:03:37,620
So project created and here is the sample code, the code is ready.

41
00:03:38,030 --> 00:03:43,140
So all the information is available so you can start typing your program here, right.

42
00:03:43,200 --> 00:03:47,420
Already, the basic hydrofoil of the standard hydrofoil is already included.

43
00:03:47,490 --> 00:03:53,900
That is, I will stream and HelloWallet programmers automatically download it now here on the right

44
00:03:53,900 --> 00:04:00,130
hand side and the solution explorer or in this window, if you expand the source file, you will find

45
00:04:00,130 --> 00:04:02,410
this my first CBB.

46
00:04:02,720 --> 00:04:05,870
So this is what the file it is showing here, right?

47
00:04:08,150 --> 00:04:15,020
Now, if you want to write a program, simply avoid one program with the three variables A, B, C,

48
00:04:15,980 --> 00:04:24,520
and then assign some value and be assigned that some value, then C, assign A plus B..

49
00:04:25,970 --> 00:04:38,390
Now, I will display this value, also standard study called C and also a study.

50
00:04:38,750 --> 00:04:43,460
And this will add two numbers and display them.

51
00:04:43,760 --> 00:04:47,400
Now you can see that something is line here with red color.

52
00:04:47,420 --> 00:04:54,310
So this shows, etc. So this idea will help you to find the errors wherever you are going wrong.

53
00:04:54,770 --> 00:04:56,480
So it's very programmer friendly.

54
00:04:56,490 --> 00:04:58,550
So here actually you have given single color.

55
00:04:58,580 --> 00:05:00,170
So I should write scope resolution.

56
00:05:00,170 --> 00:05:02,080
So double time.

57
00:05:02,090 --> 00:05:03,230
I have great color.

58
00:05:03,360 --> 00:05:04,700
So now this is perfect.

59
00:05:05,660 --> 00:05:06,860
Now let us see how to run.

60
00:05:06,870 --> 00:05:13,820
So here we can go to billion option on the top and Sabil solution or build my first.

61
00:05:14,120 --> 00:05:15,450
So this is my first album.

62
00:05:15,770 --> 00:05:23,440
So it will be compiled and executable will be created now for running go to the work option for running,

63
00:05:23,540 --> 00:05:26,380
go to the back option and start without debugging.

64
00:05:27,200 --> 00:05:30,460
So debugger I'll be showing it in the next video.

65
00:05:30,830 --> 00:05:32,870
So without debugging, just we want to run.

66
00:05:33,110 --> 00:05:36,020
She can also Parascandola five for running the program.

67
00:05:36,620 --> 00:05:38,240
So run this one now here.

68
00:05:38,280 --> 00:05:46,700
This place out here, this is the result that is 30, 10 plus 20 is 30 and also Jelawat.

69
00:05:48,680 --> 00:05:49,160
So that.

70
00:05:49,400 --> 00:05:52,160
So you can complete the program and you can remove all these comments.

71
00:05:52,160 --> 00:05:55,480
If you don't like them, you can type all your programs.

72
00:05:55,490 --> 00:06:00,320
So for every time, for every program, I suggest you to create a new project and do it and let the

73
00:06:00,320 --> 00:06:02,720
project name be same as the program that we are doing.

74
00:06:03,590 --> 00:06:08,410
It's all if you would like to use Visual Studio, which is how you can set up and start using it.

