1
00:00:00,180 --> 00:00:06,840
So here is a function for In Session Ford, which is taking an Audi and a number of elements in our

2
00:00:06,840 --> 00:00:09,150
example, the size of an Audi, the five.

3
00:00:09,160 --> 00:00:10,770
So and there's a five year.

4
00:00:11,130 --> 00:00:15,330
So let us assume that this is five, but this is all from zero to four.

5
00:00:15,330 --> 00:00:18,030
Only now fosters follow four.

6
00:00:19,530 --> 00:00:22,640
How many passive foreign elements and minus one percent.

7
00:00:22,920 --> 00:00:28,390
But if you see the process and fastpass which element they are trying to insert, that is second element

8
00:00:28,410 --> 00:00:29,320
or the next one.

9
00:00:29,400 --> 00:00:31,260
So we are trying to insert this one.

10
00:00:31,260 --> 00:00:31,620
Right.

11
00:00:31,890 --> 00:00:33,490
So this is the next one.

12
00:00:34,290 --> 00:00:36,080
So we don't have to start from zero.

13
00:00:36,090 --> 00:00:37,350
We have to start from one.

14
00:00:37,620 --> 00:00:41,200
So insert the element from the next one, then two and three and four.

15
00:00:41,910 --> 00:00:44,190
So this loop should start from one on word.

16
00:00:44,190 --> 00:00:48,500
So it takes values from one and I it's less than four.

17
00:00:48,540 --> 00:00:51,270
It should go so less than five less in.

18
00:00:51,270 --> 00:00:54,600
So going forward it will go to nine plus plus.

19
00:00:55,860 --> 00:01:01,440
So from one to four, because not zero the next, we have the first element and that we are assuming

20
00:01:01,440 --> 00:01:02,010
it's already the.

21
00:01:02,970 --> 00:01:04,510
So we are starting from this one.

22
00:01:05,069 --> 00:01:07,530
So this is four repeating passes.

23
00:01:08,340 --> 00:01:11,120
Then in each pass what we are supposed to do.

24
00:01:11,160 --> 00:01:13,050
Then I will take one example here.

25
00:01:13,410 --> 00:01:16,280
From that, we will learn what we should do in every pass.

26
00:01:16,290 --> 00:01:17,520
Then we will write on the code.

27
00:01:18,090 --> 00:01:22,740
Now I have an example, an array of size seven and thus all the detail here.

28
00:01:22,740 --> 00:01:24,000
The elements are sorted.

29
00:01:24,000 --> 00:01:26,550
That is zero to five, nine and 10.

30
00:01:26,730 --> 00:01:29,390
So take out that in some variable X.

31
00:01:29,880 --> 00:01:32,730
So right now I'm inserting this element.

32
00:01:32,740 --> 00:01:35,100
So this I is present here.

33
00:01:35,280 --> 00:01:36,470
I is pointing here.

34
00:01:36,690 --> 00:01:39,210
So this is the element that I'm inserting.

35
00:01:39,210 --> 00:01:45,840
So that element I have taken in X, now I have to shift the elements and make this happen in certain

36
00:01:45,840 --> 00:01:46,800
the forward position.

37
00:01:47,490 --> 00:01:49,670
So right now this is on six.

38
00:01:50,050 --> 00:01:52,250
Now what is the procedure?

39
00:01:52,650 --> 00:01:55,570
So I should start shifting from where this element onward.

40
00:01:55,590 --> 00:01:57,080
So I will take Junior.

41
00:01:58,190 --> 00:02:00,530
Jay is I minus one?

42
00:02:00,810 --> 00:02:06,530
Yes, the first thing I need is G, g is I minus one.

43
00:02:06,890 --> 00:02:13,500
Yes, I need this G and also I should take this last element wherever I is pointing that in X.

44
00:02:13,520 --> 00:02:21,020
So yes, I will do this also X assign eight of um that is the element that was present here.

45
00:02:21,020 --> 00:02:21,360
Right.

46
00:02:21,380 --> 00:02:24,790
So I'm taking an X so I am assuming that it's not there.

47
00:02:25,340 --> 00:02:26,650
Then what is the procedure.

48
00:02:26,690 --> 00:02:32,420
I have to shift all the greater elements so I should compare Alpha with the element X.

49
00:02:32,870 --> 00:02:36,350
This is greater so shifted and J minus minus.

50
00:02:37,890 --> 00:02:44,880
This is greater than this shifted gear, minus minus, this is greater than this one.

51
00:02:45,390 --> 00:02:47,880
So shifted gear, minus minus.

52
00:02:50,030 --> 00:02:54,210
This is greater than this one, so shifted gear, minus minus.

53
00:02:55,220 --> 00:02:57,130
Now there's a small stop.

54
00:02:58,070 --> 00:02:59,630
So the procedure is very simple.

55
00:02:59,930 --> 00:03:00,440
Go on.

56
00:03:00,440 --> 00:03:05,020
Shifting the elements as long as AOG is greater than X.

57
00:03:05,360 --> 00:03:07,520
So, yes, while.

58
00:03:10,440 --> 00:03:18,530
E of G is a greater X go on shifting the elements of refugees pointing moved to the next location.

59
00:03:19,690 --> 00:03:27,490
He offered cheap plus one should be copied with E of G as well as a J should be discriminated.

60
00:03:29,320 --> 00:03:35,380
This is what I have to do for shifting the elements, and right now the Serenity's move, the is also

61
00:03:35,380 --> 00:03:37,820
moved and fourteeners also moved to areas also moved.

62
00:03:37,840 --> 00:03:38,800
This place is free.

63
00:03:39,060 --> 00:03:39,880
This place is free.

64
00:03:40,390 --> 00:03:42,150
So this element will go to smaller.

65
00:03:42,400 --> 00:03:44,950
So this element should be inserted at which place?

66
00:03:45,490 --> 00:03:46,130
Plus one.

67
00:03:46,450 --> 00:03:52,780
So once you come out of this loop, the elements should be inserted at plus one, plus one the same

68
00:03:52,780 --> 00:03:53,410
X.

69
00:03:55,130 --> 00:03:55,790
McFarlan.

70
00:03:57,360 --> 00:04:04,260
I have stopped the loop also not one more thing, this place, I left it empty, so let us suppose this

71
00:04:04,260 --> 00:04:05,410
is not a tender.

72
00:04:05,940 --> 00:04:10,260
If this is true, then what happens from this point?

73
00:04:10,260 --> 00:04:12,990
If I continue, it is greater than to shift it.

74
00:04:13,860 --> 00:04:17,640
Minus minus six is greater than to shift it.

75
00:04:18,390 --> 00:04:22,100
Minus minus Souji became one minus one now.

76
00:04:22,380 --> 00:04:23,120
So stop.

77
00:04:23,310 --> 00:04:30,240
So when G becomes minus one stop means continue as long as the G is greater than minus one.

78
00:04:30,270 --> 00:04:31,530
So one more condition.

79
00:04:31,530 --> 00:04:37,100
I should try Don G greater than minus one and this condition.

80
00:04:38,250 --> 00:04:41,580
So this is the algorithm for insertions.

81
00:04:41,610 --> 00:04:48,000
Thought we found that the time complexity of in for this and square and hear from the call if you see

82
00:04:48,000 --> 00:04:49,920
for loop then inside that y loop.

83
00:04:50,220 --> 00:04:52,090
So to nested loops are there.

84
00:04:52,380 --> 00:04:54,540
So this is and square.

85
00:04:55,690 --> 00:04:58,990
So from the court also, we can say this and square.

86
00:04:59,820 --> 00:05:05,190
So the next thing that is remaining is we have to check whether this adoptive or stable or not.

