1
00:00:00,510 --> 00:00:01,920
The topic is chinning.

2
00:00:03,250 --> 00:00:09,640
In the previous video, we have seen the modulars hash function and the drawback of modulars hash function,

3
00:00:09,640 --> 00:00:14,720
that is more than one will map on the same location and it causes collision.

4
00:00:15,520 --> 00:00:22,900
So let us see how to avoid collision or how to resolve collusion for resolving collusion.

5
00:00:23,230 --> 00:00:27,770
Chaining is one of the method, and it also comes under open hashing.

6
00:00:28,810 --> 00:00:30,440
So let us learn about this topic.

7
00:00:30,460 --> 00:00:35,920
So on this topic, I'll be showing you how to perform following operations like how to insert and how

8
00:00:35,920 --> 00:00:38,680
to search and the analysis of the.

9
00:00:38,680 --> 00:00:40,320
Then we will see how to delete.

10
00:00:40,990 --> 00:00:43,620
So these are the operations we will learn on cheating.

11
00:00:44,380 --> 00:00:45,260
So let us talk.

12
00:00:45,880 --> 00:00:52,900
I already have a set of keys kept here and those sort of keys I have taken in the key space and the

13
00:00:52,900 --> 00:00:54,490
hash table sizes 10.

14
00:00:55,210 --> 00:01:02,620
So hash function is each of X is X mod sizes 10.

15
00:01:02,620 --> 00:01:05,790
So I will directly write down here that is the size.

16
00:01:06,340 --> 00:01:11,730
Now using this hash function, I will insert these keys in the hash table.

17
00:01:12,520 --> 00:01:17,340
So let us do it one by one, 16 more to ten as a six.

18
00:01:17,350 --> 00:01:19,630
So it is inserted at display's.

19
00:01:21,090 --> 00:01:27,450
Now, as this is changing, so it is not directly inserted at this place, but here on notice created,

20
00:01:27,630 --> 00:01:31,200
that is a link list and did the values incident.

21
00:01:32,640 --> 00:01:37,750
Yes, so it means disappointed to see the Sahad pointed to a necklace.

22
00:01:38,040 --> 00:01:45,000
So this is a story offer pointers to Lincoln as it is implemented using an array of pointers.

23
00:01:45,180 --> 00:01:49,170
Then let us insert rest of them to more than as a tool.

24
00:01:49,170 --> 00:01:51,900
So it is inserted at displays in an old.

25
00:01:54,320 --> 00:01:56,660
Twenty five and five.

26
00:01:58,610 --> 00:01:59,390
Thirty nine.

27
00:02:01,750 --> 00:02:08,919
Six, there's a collision, but here we are sitting in the Lincolnesque, so I will create a new node

28
00:02:09,940 --> 00:02:16,900
and insert here, but the six is a smaller than 16, so 16 should go there and six should be inserted.

29
00:02:16,900 --> 00:02:23,470
Here means these keys must be inserted in the socket or that they must be kept in sorted order so that

30
00:02:23,470 --> 00:02:25,450
while searching it is easy for such.

31
00:02:28,730 --> 00:02:29,660
Wonderingly to.

32
00:02:32,210 --> 00:02:33,660
122 is greater than 12.

33
00:02:33,740 --> 00:02:38,050
It's in there, the next is five, five is mapped here.

34
00:02:38,330 --> 00:02:40,600
So five is a smaller than twenty five.

35
00:02:40,850 --> 00:02:45,440
So twenty five, five comes as a node before thirty five.

36
00:02:47,700 --> 00:02:48,840
Before 25.

37
00:02:51,620 --> 00:02:54,860
Sixty eight is inserted here.

38
00:02:58,080 --> 00:03:03,690
Seventy five, again, mapped at five, seventy five is greater than five hundred twenty five, so it

39
00:03:03,690 --> 00:03:05,150
should come here at last.

40
00:03:06,240 --> 00:03:12,620
So that's all I have inserted all these keys and the keys are inserted in chains.

41
00:03:12,630 --> 00:03:14,540
So, yes, these are array of chains.

42
00:03:14,820 --> 00:03:20,460
So how stable is made out of array of linguist's or array of chains?

43
00:03:21,450 --> 00:03:28,740
See, the hash table sizes is 10, so beyond this, we are taking more extra space so this link can

44
00:03:28,740 --> 00:03:32,690
go to up to any size so you can insert as many keys as you want.

45
00:03:33,930 --> 00:03:35,950
There is no limit for the size here.

46
00:03:36,470 --> 00:03:39,490
Now, we have seen insertion, insertion was very easy.

47
00:03:40,230 --> 00:03:42,920
The next let us see how to search.

48
00:03:43,590 --> 00:03:46,950
I want to search for a key 12.

49
00:03:48,420 --> 00:03:56,850
Use hash function 12 10 as the two go to index tool and search and this link in the chain, so your

50
00:03:57,030 --> 00:04:02,250
tool is found in just one competition, successful search is successful.

51
00:04:02,700 --> 00:04:07,080
I want to search for a key seventy five.

52
00:04:08,260 --> 00:04:14,710
Take this hash function, hash function will give the index a five, Google Index five and search for

53
00:04:14,710 --> 00:04:20,140
seventy five in this chain in this link as it is, only five seventy five, No.

54
00:04:20,660 --> 00:04:22,300
Seventy five is found.

55
00:04:23,920 --> 00:04:29,320
So search is successful now, search for a key sixty five.

56
00:04:31,070 --> 00:04:38,120
Take this hash function sixty five, moderniser five, go to index five, search for it, five no.

57
00:04:38,120 --> 00:04:39,220
Sixty five is greater.

58
00:04:39,890 --> 00:04:40,730
Twenty five, No.

59
00:04:40,730 --> 00:04:41,750
Sixty five greater.

60
00:04:42,200 --> 00:04:43,220
Seventy five No.

61
00:04:43,220 --> 00:04:44,390
Sixty five is smaller.

62
00:04:44,390 --> 00:04:48,080
Sooky is not found, is not found.

63
00:04:48,260 --> 00:04:51,530
So searches unsuccessful for these cookies.

64
00:04:51,530 --> 00:04:52,420
It was successful.

65
00:04:52,670 --> 00:04:54,200
Now let me take one more key.

66
00:04:54,920 --> 00:04:55,580
Fifteen.

67
00:04:57,300 --> 00:05:04,800
15 more, 10 five search here, is it 15 No, 15, 15, No.

68
00:05:04,800 --> 00:05:06,410
15 is a smaller stop.

69
00:05:06,420 --> 00:05:07,500
Don't check further.

70
00:05:08,810 --> 00:05:13,700
Is there thought that that is the benefit you can stop if the key is not found and if you've got an

71
00:05:13,700 --> 00:05:15,650
element greater than the key that you're searching?

72
00:05:17,600 --> 00:05:20,150
So far, 15 also searches unsuccessful.

73
00:05:21,320 --> 00:05:26,220
That's all insertion is also easy and searching is also easy.

74
00:05:26,630 --> 00:05:32,360
Now let us do analysis and find out how much time it is taking for searching in case of successful search

75
00:05:32,660 --> 00:05:34,530
as well as unsuccessful search.

76
00:05:35,240 --> 00:05:36,800
Now, let us do analysis.

77
00:05:37,580 --> 00:05:41,000
I have the number of keys that are in supples.

78
00:05:41,030 --> 00:05:43,410
I have 100 keys here.

79
00:05:43,430 --> 00:05:44,390
I have few keys.

80
00:05:44,390 --> 00:05:45,620
Only nine keys are there.

81
00:05:45,920 --> 00:05:48,260
But assume there are 100 keys.

82
00:05:49,450 --> 00:05:56,710
Right on the size of the hash table, is it because the hash functions Exmoor datin, so the hash table

83
00:05:56,710 --> 00:05:58,080
size is then.

84
00:06:00,710 --> 00:06:05,440
Seen, this is chilling, though, the size of the table is certainly, but there is no upper limit,

85
00:06:05,450 --> 00:06:09,220
you can add as many keys as you want because they are getting inserted in chain.

86
00:06:09,890 --> 00:06:12,630
So I'm thinking size, number of keys, one hundred.

87
00:06:13,220 --> 00:06:21,400
Now, the very important thing I take and the bite size, that is a number of keys by science.

88
00:06:21,860 --> 00:06:28,010
This is represented with the lambda or alpha and it is called the loading factor.

89
00:06:30,930 --> 00:06:37,860
The flooding factor is this loading factor is very important in harshing, so we find out loading factor,

90
00:06:37,860 --> 00:06:42,050
loading factor is a number of keys divided by side of the table.

91
00:06:43,370 --> 00:06:48,380
Now, remember one thing, all of these data structures that we have analyzed so far, we were analyzing

92
00:06:48,380 --> 00:06:55,430
them based on a number of elements and and but here it is not based on number of elements that is not

93
00:06:55,430 --> 00:07:04,940
based on keys and but it is based on loading factor analysis of hashing is always done based on loading

94
00:07:04,940 --> 00:07:05,380
factor.

95
00:07:05,390 --> 00:07:06,020
Remember this.

96
00:07:06,530 --> 00:07:12,530
So as part of an example, if I say I have a hundred keys and the sizes are ten hash table sizes tenements,

97
00:07:12,840 --> 00:07:14,840
hundred by ten is ten.

98
00:07:14,870 --> 00:07:17,100
So the loading factor is ten.

99
00:07:18,680 --> 00:07:19,670
So what does it mean?

100
00:07:20,150 --> 00:07:23,870
It means that at each location there are ten keys.

101
00:07:24,320 --> 00:07:26,490
There are ten locations at each location.

102
00:07:26,490 --> 00:07:32,390
Directness because this is assuming we got the loading factor greater than one, we go to value ten.

103
00:07:33,140 --> 00:07:41,240
So the distance we are assuming that the hundred keys that I have are uniformly distributed at each

104
00:07:41,240 --> 00:07:42,050
location.

105
00:07:42,050 --> 00:07:45,910
So each location is having around ten, ten keys.

106
00:07:46,430 --> 00:07:47,690
We are assuming this.

107
00:07:48,640 --> 00:07:53,530
Our assumption may be wrong, but we are assuming this or actually we are expecting this.

108
00:07:55,390 --> 00:08:00,610
We are expecting that every location is having Tenten elements if I have it.

109
00:08:00,920 --> 00:08:06,310
So if you observe in this example what we have seen, one element, one element and two elements to

110
00:08:06,340 --> 00:08:08,940
having three elements, and they are not having any elements.

111
00:08:08,950 --> 00:08:09,960
These are still empty.

112
00:08:10,090 --> 00:08:10,870
These are empty.

113
00:08:11,140 --> 00:08:12,340
This annuls still.

114
00:08:13,760 --> 00:08:19,880
Anyway, the reality is something else, but we are assuming that other kids are uniformly distributed

115
00:08:20,270 --> 00:08:28,880
now based on this, we will do analysis now to see what is the time taken for searching in case of successful

116
00:08:28,880 --> 00:08:32,240
search time taken for successful search.

117
00:08:34,220 --> 00:08:40,190
So for successful search, we say titmus, first of all, we use hash function for getting the index

118
00:08:40,490 --> 00:08:42,380
and that takes constant time.

119
00:08:42,530 --> 00:08:49,340
One, yes, one for computing hash function, which takes Konstantine.

120
00:08:49,970 --> 00:08:55,440
Second one is we have to search in that particular chain in that particular Lincolnesque.

121
00:08:55,940 --> 00:08:58,670
So what would be the average time?

122
00:08:58,670 --> 00:09:03,060
Average time will be half of that loading factor.

123
00:09:03,530 --> 00:09:06,600
So are assuming that at each location and keys are there.

124
00:09:06,810 --> 00:09:11,030
So for searching maximum, how many competitions can competition minimum?

125
00:09:11,030 --> 00:09:11,750
How many companies?

126
00:09:11,900 --> 00:09:13,820
Just one competition average.

127
00:09:13,820 --> 00:09:14,300
How many?

128
00:09:14,330 --> 00:09:15,030
Half of it.

129
00:09:15,440 --> 00:09:19,120
So whatever the lambda is lambda by two.

130
00:09:19,460 --> 00:09:25,130
So this is the time taken in case of successful search and the average time.

131
00:09:25,400 --> 00:09:26,660
This is average time.

132
00:09:27,110 --> 00:09:27,410
Right.

133
00:09:27,770 --> 00:09:32,930
Average successful search time one plus lambda by one is for hash function.

134
00:09:32,930 --> 00:09:35,570
Lambda is the loading factor.

135
00:09:35,870 --> 00:09:37,610
Bitou gives average.

136
00:09:39,980 --> 00:09:44,510
Then what is the time in case of unsuccessful search if the key is not found?

137
00:09:46,050 --> 00:09:52,710
For the average unsuccessful search time for searching Anikeeva use hash function so far, hash function

138
00:09:52,720 --> 00:10:00,990
time is one one plus keys not found means we will be checking entire Linklaters entire chain.

139
00:10:01,020 --> 00:10:05,240
We don't know where it may stop, so we say that maximum time it takes.

140
00:10:05,240 --> 00:10:06,630
So that's how many keys are there?

141
00:10:06,630 --> 00:10:07,380
10 keys are there.

142
00:10:07,410 --> 00:10:08,450
That's what lamda.

143
00:10:08,790 --> 00:10:10,780
So this one plus lamda.

144
00:10:10,890 --> 00:10:14,460
So we are taking maximum time here, the maximum possible time.

145
00:10:14,490 --> 00:10:16,650
We don't know when it will stop.

146
00:10:16,860 --> 00:10:17,240
Right.

147
00:10:17,430 --> 00:10:19,950
So it means we assume that it will stop at the end.

148
00:10:20,160 --> 00:10:21,560
So the key is not found.

149
00:10:22,080 --> 00:10:30,420
So this LAMDA one plus Lambton so for searching successful search, this is the time taken and unsuccessful

150
00:10:30,420 --> 00:10:30,850
search.

151
00:10:31,230 --> 00:10:32,350
This is the time to go.

152
00:10:32,790 --> 00:10:38,630
So I have discussed searching analysis also not a lasting delete.

153
00:10:38,910 --> 00:10:41,030
Let us see how to delete any key.

154
00:10:41,700 --> 00:10:44,610
Suppose I want to delete a key to end this key.

155
00:10:44,610 --> 00:10:45,470
I want to delete.

156
00:10:46,350 --> 00:10:53,100
So for deleting dual use hash function it gives indexed to go to index to search in this chain if it

157
00:10:53,100 --> 00:11:00,360
is found delete and or simply it is just like deleting a node from Lingley, so deleting a log if it

158
00:11:00,360 --> 00:11:01,610
is found deleted.

159
00:11:02,580 --> 00:11:03,860
So the procedure is simple.

160
00:11:04,350 --> 00:11:07,530
So for insert search and delete for all of them procedure.

161
00:11:07,530 --> 00:11:15,360
A simple insertion time you insert in relentless search time, you search for it sociolinguist and deletion

162
00:11:15,360 --> 00:11:15,660
time.

163
00:11:15,660 --> 00:11:16,960
You delete a node from a link.

164
00:11:17,070 --> 00:11:19,620
So the operations are the same as Lindqvist.

165
00:11:20,100 --> 00:11:23,700
So that's all about the operations in case of chinning.

166
00:11:24,800 --> 00:11:26,810
Now, a few important things from this one.

167
00:11:28,140 --> 00:11:36,480
See, the hash function need not be Exmoor than we are doing it based on last digit.

168
00:11:39,710 --> 00:11:46,060
Last digit, that is, we are looking at last digit, if you say no, I want to look at second digit,

169
00:11:46,070 --> 00:11:48,170
OK, you can look at second digit also from.

170
00:11:48,170 --> 00:11:49,060
Right, right.

171
00:11:49,340 --> 00:11:50,350
Second last digit.

172
00:11:50,360 --> 00:11:53,060
So you can trade a hash function for that one.

173
00:11:53,060 --> 00:11:58,280
Also has functional effects as equals two X divided by.

174
00:11:59,680 --> 00:12:00,040
And.

175
00:12:01,320 --> 00:12:07,620
Then, Mark, and if you want, you can modify the hash function, it's not necessary that you take

176
00:12:07,620 --> 00:12:15,340
motorman's last digit, only the benefit of more is the size of the table will be limited.

177
00:12:16,200 --> 00:12:19,090
So if you say more than you need the size from zero to nine.

178
00:12:19,110 --> 00:12:20,270
Only that is ten.

179
00:12:20,520 --> 00:12:24,800
If you say more to 20 than the size of the table can be from zero to 19.

180
00:12:25,260 --> 00:12:28,290
So you can limit the size of a hash table.

181
00:12:28,320 --> 00:12:29,910
That's why we use mortar.

182
00:12:30,600 --> 00:12:34,740
But for generating the indices, you don't have to take all this loss of digital.

183
00:12:34,750 --> 00:12:36,060
You can pick any digits.

184
00:12:36,540 --> 00:12:38,260
Now, I will show you one problem.

185
00:12:39,000 --> 00:12:42,810
And from that problem, after that problem, we will also see a solution.

186
00:12:43,030 --> 00:12:43,640
Just watch.

187
00:12:43,640 --> 00:12:47,880
You see, suppose I have the keys like five.

188
00:12:49,070 --> 00:12:57,350
Thirty five, ninety five, one forty five, one seventy five to sixty five, eight forty five.

189
00:12:58,010 --> 00:12:59,030
Fifty five.

190
00:13:00,300 --> 00:13:08,940
Twenty five, all the keys are ending with the five only if I use this hash function, the smarter 10,

191
00:13:09,300 --> 00:13:11,120
then all the keys will be mapped.

192
00:13:11,140 --> 00:13:18,100
That same index five and the chain of all the keys will be formed here only and all will be empty.

193
00:13:18,810 --> 00:13:19,440
Yes.

194
00:13:19,920 --> 00:13:25,260
So the idea that loading factor means the keys are uniformly distributed.

195
00:13:25,830 --> 00:13:29,680
That feels that feels that assumption fails.

196
00:13:30,210 --> 00:13:33,300
So then who responsible hash function.

197
00:13:34,440 --> 00:13:41,010
Hash function, hash function is it is standard one that you must take this only no, you can take any

198
00:13:41,010 --> 00:13:41,790
hash function.

199
00:13:42,180 --> 00:13:48,230
Then who is responsible, the person or a programmer who has selected the hash function.

200
00:13:48,660 --> 00:13:54,120
So you select the hash function such that the keys are uniformly distributed.

201
00:13:54,680 --> 00:13:55,220
Yes.

202
00:13:55,620 --> 00:14:01,860
So we assume that you have selected a proper hash function which is uniformly distributing the keys.

203
00:14:01,890 --> 00:14:04,590
That's why we do that analysis based on Landsdowne.

204
00:14:06,070 --> 00:14:10,990
If you're not selecting a proper hash function, you don't know, harshing simply.

205
00:14:13,660 --> 00:14:19,720
Don't blame hashing technique, you don't know how to use hash, that is the meaning, then how to decide

206
00:14:19,720 --> 00:14:20,390
hash function.

207
00:14:20,680 --> 00:14:23,830
You must have idea about the keys that you are going to insert.

208
00:14:23,890 --> 00:14:27,280
Yes, you must have the idea what keys you are inserting.

209
00:14:27,280 --> 00:14:27,960
What is your data?

210
00:14:27,970 --> 00:14:30,430
You should know then only you will find a solution, right?

211
00:14:30,860 --> 00:14:32,970
Without knowing that you cannot find a solution.

212
00:14:33,280 --> 00:14:40,930
So you can select a hash function so that at least one major extent, the keys are uniformly distributed.

213
00:14:41,620 --> 00:14:45,100
So we are assuming that approximate the distribution.

214
00:14:45,100 --> 00:14:45,310
Right.

215
00:14:45,760 --> 00:14:52,240
So it's not a great uniform distribution, approximately 10 keys out there at each location, more or

216
00:14:52,240 --> 00:14:53,570
less not a problem.

217
00:14:53,680 --> 00:14:59,690
So that's all about changing as a resolution, method of resolution, technique in hashing.

218
00:15:00,340 --> 00:15:01,630
Now we have other methods.

219
00:15:01,630 --> 00:15:03,990
Also, we'll look at the next videos.

