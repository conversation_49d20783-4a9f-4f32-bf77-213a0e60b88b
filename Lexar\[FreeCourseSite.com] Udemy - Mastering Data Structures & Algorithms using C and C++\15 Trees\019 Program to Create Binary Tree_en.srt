1
00:00:00,150 --> 00:00:02,920
So procedurally, we have seen for creating a tree.

2
00:00:03,150 --> 00:00:07,140
So now I will write on a function, I have written a function here clear.

3
00:00:07,350 --> 00:00:11,990
I have taken the required pointers like a plenty of the pointers that we require.

4
00:00:12,420 --> 00:00:14,580
<PERSON>, assume that it is global.

5
00:00:15,180 --> 00:00:18,200
Then I have declared a Q also a structure.

6
00:00:18,210 --> 00:00:20,680
So we have already learned about Kurata structure so that.

7
00:00:20,680 --> 00:00:22,740
Q And it must be initialized.

8
00:00:22,740 --> 00:00:25,110
So I'm not writing the code for initializing that.

9
00:00:25,110 --> 00:00:25,410
Q.

10
00:00:26,480 --> 00:00:28,040
So I assume that it is initialised.

11
00:00:28,520 --> 00:00:33,740
One more thing, we don't know how many laws will be there, so we can't determine the size of the school.

12
00:00:33,740 --> 00:00:38,210
So instead of picking and choosing, we can go for accusing linguist.

13
00:00:38,690 --> 00:00:40,280
Now, let me start writing the code.

14
00:00:40,460 --> 00:00:44,220
As I have already explained you, the very first thing we should do is create a rule.

15
00:00:44,660 --> 00:00:46,970
So I relied on the code here for creating rules.

16
00:00:47,480 --> 00:00:53,680
So printf and the rule value then askaris the value of ruled in variable X.

17
00:00:53,900 --> 00:01:00,860
So I'm reading the value, not create a node using pointer root and fill this value and make this as

18
00:01:00,860 --> 00:01:01,160
null.

19
00:01:01,910 --> 00:01:07,370
So I'm creating a root and all using my log function and filling the data in that node and making left

20
00:01:07,370 --> 00:01:09,460
and right side of that root as none.

21
00:01:09,470 --> 00:01:11,000
So I wrote zero here for none.

22
00:01:11,900 --> 00:01:15,050
Then I should insert this address of this root node in.

23
00:01:15,050 --> 00:01:19,640
Q So here NQ Root, this is inserted in.

24
00:01:19,640 --> 00:01:23,480
Q Are they explained that initially this said two hundred wins so inserted here.

25
00:01:24,350 --> 00:01:29,390
Not the repeating procedure I have to write on that has to be written inside the loop so I can use y

26
00:01:29,390 --> 00:01:29,690
loop.

27
00:01:30,020 --> 00:01:33,740
How long that loop should continue until Q is not empty.

28
00:01:33,950 --> 00:01:35,270
If it is empty should stop.

29
00:01:35,540 --> 00:01:40,160
So I will write on loop here while y not is empty.

30
00:01:40,160 --> 00:01:46,840
Q So as long as Q is not empty, continue when it is empty then stop then all the steps I have to write

31
00:01:46,850 --> 00:01:47,870
on inside this one.

32
00:01:48,790 --> 00:01:53,550
So what is the first step, take out that some cue and take it to the point.

33
00:01:53,590 --> 00:01:55,940
Repeat that initially we did it like this, right?

34
00:01:56,080 --> 00:01:59,170
So be a sign dequeue.

35
00:01:59,440 --> 00:02:02,370
So be a sign dequeue from cue.

36
00:02:02,620 --> 00:02:08,800
So take all the value from Q1 one ticket in point B, so be pointing then not being there, we should

37
00:02:08,800 --> 00:02:16,090
ask the value of live child if it is not minus one created the last the right chain, if it is not minus

38
00:02:16,090 --> 00:02:16,870
one created.

39
00:02:17,140 --> 00:02:23,190
Those are steps I have right now so I would right on the steps for left China but indef and the left.

40
00:02:23,190 --> 00:02:26,440
China can take the value in variable X here.

41
00:02:26,440 --> 00:02:28,660
I'm taking effects is not equal to minus one.

42
00:02:28,960 --> 00:02:34,210
If it is not minus one, then we will create an order with the help of temporary pointer and enter the

43
00:02:34,210 --> 00:02:41,920
data and make this as null and make these left the point on that one and also insert into the Q All

44
00:02:41,920 --> 00:02:44,650
these are steps I have to do, so I will write on these steps.

45
00:02:45,070 --> 00:02:51,820
So here, create a new board with the help of temporary point to set the data in that note said the

46
00:02:51,820 --> 00:03:00,370
data in the north and set the left and right zero set left as null then also makes space left as a t

47
00:03:00,760 --> 00:03:08,710
ps left D'Asti then enter the address of this new law in Q So insert the words of that new node in Q

48
00:03:08,710 --> 00:03:19,560
So nq that t this all will do for left child C from here all these steps are for just left child then

49
00:03:19,570 --> 00:03:22,650
same steps I should repeat for right chinlone.

50
00:03:22,660 --> 00:03:23,020
So.

51
00:03:24,690 --> 00:03:31,160
But in the end, the right change take the value, if it is not equal to minus one, then create a..

52
00:03:31,380 --> 00:03:35,560
And this line will change instead of peace and child, I should write.

53
00:03:35,580 --> 00:03:38,370
These are all the steps are seen.

54
00:03:39,120 --> 00:03:40,160
All the steps seem.

55
00:03:40,350 --> 00:03:41,340
So there's no space.

56
00:03:41,340 --> 00:03:42,360
I cannot continue.

57
00:03:42,570 --> 00:03:47,100
So I should write all these things once again for the writer and also then close.

58
00:03:47,100 --> 00:03:50,280
Why loop and closed that function.

59
00:03:51,740 --> 00:03:53,400
So those steps will repeat here.

60
00:03:54,140 --> 00:04:01,070
I should keep it to the steps, the steps that are inside this block, all these steps should be repeated

61
00:04:01,070 --> 00:04:06,440
again for the right channels, the ones I think the program will complete the code and I will show its

62
00:04:06,440 --> 00:04:07,180
execution.

63
00:04:07,310 --> 00:04:11,270
So that's always the procedure for generating a tree with the help of Q.

