1
00:00:02,210 --> 00:00:08,119
And this video, I will show you how to write a C++ class for an array from the scratch.

2
00:00:09,540 --> 00:00:14,590
So all of these language program we have seen, we have developed various functions, I will develop

3
00:00:14,590 --> 00:00:17,780
a few functions here in C++ class.

4
00:00:17,790 --> 00:00:21,810
So you get the idea how to write a C++ program for the same thing.

5
00:00:22,530 --> 00:00:27,570
Once you know how to write on the procedures, then either you can develop a C program or you can make

6
00:00:27,570 --> 00:00:28,890
it as a C++ program.

7
00:00:28,900 --> 00:00:29,840
Charges years.

8
00:00:30,150 --> 00:00:33,240
It's not difficult to develop a C++ program.

9
00:00:33,630 --> 00:00:36,470
It's as easy as writing a C language program.

10
00:00:37,110 --> 00:00:40,290
So let us see how to write this C++ program.

11
00:00:40,590 --> 00:00:42,090
How to write a class for.

12
00:00:43,140 --> 00:00:43,550
Eddie.

13
00:00:45,250 --> 00:00:48,490
I'll give the Project Nim as a class.

14
00:00:51,020 --> 00:00:53,600
And the language selected as C++.

15
00:00:55,370 --> 00:00:56,330
Create a project.

16
00:00:57,490 --> 00:01:04,480
Now the project is ready, let us go to main function, he and his main dog, CBB here is a main function

17
00:01:05,170 --> 00:01:08,760
as they move these panels on the left and right side.

18
00:01:09,430 --> 00:01:11,150
Now, only a main function is here.

19
00:01:11,650 --> 00:01:13,790
Now, let us write the class for Uhry.

20
00:01:14,720 --> 00:01:16,460
I like a class of.

21
00:01:20,650 --> 00:01:25,240
A class in C++ should have eight members and no function.

22
00:01:25,270 --> 00:01:27,060
So what are the data members required for?

23
00:01:27,910 --> 00:01:32,440
We need an UTI for storing the elements and size and length.

24
00:01:32,920 --> 00:01:36,370
So data members, I will make them as private.

25
00:01:37,850 --> 00:01:43,940
So first of all, for us, I will not make a fixes Cesari, but I will take it as a pointer so that

26
00:01:44,300 --> 00:01:46,280
I can dynamically create an array.

27
00:01:46,850 --> 00:01:51,080
The next is the size of an area that they want and next is lent.

28
00:01:51,300 --> 00:01:53,750
That is the number of elements that are present in the Nathi.

29
00:01:54,260 --> 00:01:59,340
So if you look at these numbers, these are the same members of a structure in C program.

30
00:02:00,830 --> 00:02:05,810
Now we have written various functions in C program, so here also we will write them as member functions

31
00:02:05,810 --> 00:02:09,639
of array class and the member functions must be made as public.

32
00:02:09,650 --> 00:02:14,890
So I will make them as public as this is a class of C++ class.

33
00:02:14,900 --> 00:02:16,640
It must have constructors and the.

34
00:02:17,750 --> 00:02:21,350
So first of all, I will write constructor and I will expand it here.

35
00:02:21,350 --> 00:02:23,600
Only the Zanon parameter is constructor.

36
00:02:24,170 --> 00:02:30,380
And in this non parameter constructor we should create an array of some different size and set the default

37
00:02:30,380 --> 00:02:30,810
size.

38
00:02:31,430 --> 00:02:38,810
So I will first set the size of an early as NP and the default area will create it of size.

39
00:02:39,650 --> 00:02:42,220
And so let us make an array of size.

40
00:02:42,230 --> 00:02:42,680
A 10.

41
00:02:45,580 --> 00:02:49,150
Then there are no elements initially, then it is a little.

42
00:02:51,480 --> 00:02:57,220
The next day will right on parametrized constructor's so that I should be able to create an area of

43
00:02:57,220 --> 00:03:06,640
desired size, so I will take the parameterize, Ezard and Fosterville said the size as said then also

44
00:03:06,640 --> 00:03:11,650
I will set the land zero because there are no elements so far that I will create an added anemically

45
00:03:11,950 --> 00:03:12,970
of size.

46
00:03:14,600 --> 00:03:21,510
A size any size, all the size, as that is said in the size or whatever, the size of that size, it

47
00:03:21,560 --> 00:03:22,340
will be created.

48
00:03:23,750 --> 00:03:25,040
This is about a constructor.

49
00:03:25,880 --> 00:03:30,080
Then you should also have a destructor district that should release resources.

50
00:03:30,100 --> 00:03:32,300
So any resource you are acquiring here.

51
00:03:32,330 --> 00:03:35,840
Yes, we are dynamically creating an array in a heap.

52
00:03:36,140 --> 00:03:37,840
So this array should be released.

53
00:03:37,850 --> 00:03:40,270
So let us try to destructor the structure.

54
00:03:40,280 --> 00:03:45,620
Should have the same name as class name, just prefix with tile symbol.

55
00:03:46,190 --> 00:03:49,340
So here I will delete dynamically created.

56
00:03:50,010 --> 00:03:53,390
That is a as it is an area I should use subscript symbol.

57
00:03:56,970 --> 00:04:02,910
Now, I will write a few functions here, the functions already we have seen like insert or delete displays

58
00:04:02,930 --> 00:04:07,390
or so on, so I will write down only three functions insert, delete and display.

59
00:04:07,560 --> 00:04:10,060
So first of all, let's write a function for display.

60
00:04:10,540 --> 00:04:11,560
Is a display function.

61
00:04:12,360 --> 00:04:17,070
Now, if you remember in C language programming, we're passing Arreaza a parameter to the function,

62
00:04:17,070 --> 00:04:21,320
but we don't have to do it here because this function is a part of the class.

63
00:04:21,660 --> 00:04:23,830
It can access these members directly.

64
00:04:24,180 --> 00:04:24,520
Yes.

65
00:04:24,540 --> 00:04:24,660
Yes.

66
00:04:25,200 --> 00:04:28,500
So this doesn't require any parameter at all.

67
00:04:29,710 --> 00:04:35,470
The Nix's inside function as part of the language we have seen, that insert function was taking three

68
00:04:35,470 --> 00:04:39,100
parameters Uhry and the index and element.

69
00:04:39,130 --> 00:04:42,970
So here I will just take index an element.

70
00:04:45,300 --> 00:04:46,500
Then delete function.

71
00:04:47,580 --> 00:04:54,720
We'll take just index and it will delete the element from that index and return it, so that's all three

72
00:04:54,720 --> 00:05:00,990
functions I have written and you can continue writing all of the function like soerjadi words merge,

73
00:05:00,990 --> 00:05:04,200
finding maximum funding, minimum, setting an element, getting an element.

74
00:05:04,200 --> 00:05:06,600
All the functions that we have seen you can implement.

75
00:05:06,630 --> 00:05:07,320
Similarly.

76
00:05:09,530 --> 00:05:14,630
Now, the functions prototype looks similar to C language functions, except that they are not taking

77
00:05:14,630 --> 00:05:16,910
parameter as struct uhry.

78
00:05:18,570 --> 00:05:23,640
Except everything will be seen, so the procedures written inside these functions will also be seen.

79
00:05:24,090 --> 00:05:28,660
So now I will implement these functions outside the class using scope resolution operator.

80
00:05:29,070 --> 00:05:33,500
So the first function that I'm going to implement this display function.

81
00:05:33,510 --> 00:05:35,050
So this is a plus name.

82
00:05:35,880 --> 00:05:37,080
Then this is the display.

83
00:05:39,300 --> 00:05:47,820
And this function is for display purposes, so I can directly declare a variable here in C++ for and

84
00:05:47,820 --> 00:05:55,440
I assign zero, I is less than lente so virulent this length, which is directly declared inside the

85
00:05:55,440 --> 00:05:55,840
class.

86
00:05:55,860 --> 00:05:57,750
So this letter can directly access.

87
00:06:00,600 --> 00:06:03,210
I love the land than I placeless.

88
00:06:05,090 --> 00:06:10,460
Then I should you code for displaying the elements that is aof, i.e. all the elements I should display,

89
00:06:10,700 --> 00:06:13,670
and I want the spaces in between the elements that all.

90
00:06:14,740 --> 00:06:22,240
Then at the end, I should also have panel for the new line now here I'm getting an error that is called

91
00:06:22,240 --> 00:06:24,030
is an undefined identifier.

92
00:06:24,040 --> 00:06:27,460
So here actually I should use names, places.

93
00:06:29,060 --> 00:06:29,840
Using.

94
00:06:31,900 --> 00:06:38,950
Mindspace chastity, then I'm using a standard namespace as to the namespace so that I can use all the

95
00:06:38,950 --> 00:06:40,480
objects inside that namespace.

96
00:06:40,480 --> 00:06:42,060
So error is gone.

97
00:06:43,030 --> 00:06:46,180
Scott was giving error here, but now that it's gone.

98
00:06:47,240 --> 00:06:53,120
Does their display function no, let us right insert functions for insert function, I should be right

99
00:06:53,130 --> 00:06:55,970
plus name and scope resolution then.

100
00:06:58,710 --> 00:07:04,590
Insert sticking two parameters, that is index and element that we want to insert.

101
00:07:06,030 --> 00:07:10,770
So the procedure we already know that we should check whether the index is valid or not, index should

102
00:07:10,770 --> 00:07:15,390
be greater than equal to zero and index should be less than.

103
00:07:18,960 --> 00:07:26,350
Equal to lend, it should not be beyond Lent if it is from zero to lend and it's a valid index, and

104
00:07:26,350 --> 00:07:32,280
then you can insert an element at a given position for inserting an element, I should start shifting

105
00:07:32,280 --> 00:07:33,750
the elements from the last element.

106
00:07:34,740 --> 00:07:40,110
I should start shifting the element from last element on word so far that I will take I, I should start

107
00:07:40,110 --> 00:07:45,170
from lenth minus one because the last element does index lenda minus one.

108
00:07:45,600 --> 00:07:55,290
Then I should continue until I just index and always say a minus minus then at if of eight plus one

109
00:07:55,290 --> 00:07:56,700
I should copy the element.

110
00:07:58,260 --> 00:08:05,110
From AOF, I mean, I should copy I should shift element to the next place.

111
00:08:05,740 --> 00:08:10,560
This goes on in for loop and of course, shifting all the elements at the next place.

112
00:08:10,560 --> 00:08:17,550
I should copy an element X then after that I should also increase the level because a new element is

113
00:08:17,550 --> 00:08:18,100
inserted.

114
00:08:18,120 --> 00:08:18,450
Now.

115
00:08:20,520 --> 00:08:26,590
NetSol went in insert function, no, let us write or delete function for that, I will write last name

116
00:08:26,590 --> 00:08:28,230
and resolution, then delete.

117
00:08:28,620 --> 00:08:30,780
It takes index as parameter.

118
00:08:37,159 --> 00:08:41,840
Now, inside the delete function, I should check whether the index is given as valid or not.

119
00:08:42,260 --> 00:08:45,470
So index should be greater than or equal to zero or.

120
00:08:47,860 --> 00:08:55,820
And index should be less than lente, if it is valid, then we can delete it.

121
00:08:55,840 --> 00:09:00,790
So for deletion, I should have one variable X that is initialized to zero in this variable, I'll take

122
00:09:00,800 --> 00:09:01,310
the value.

123
00:09:02,200 --> 00:09:06,250
So first step is take the value from any at a given index.

124
00:09:08,660 --> 00:09:15,380
Then after taking the value, I should shift all the elements, then follow up, I start from index

125
00:09:15,380 --> 00:09:18,970
onwards and I should stop at Lente.

126
00:09:20,110 --> 00:09:22,900
Minus one, I plus plus.

127
00:09:24,580 --> 00:09:29,920
And here at if I should copy, an element from AOF I plus one.

128
00:09:32,270 --> 00:09:39,620
And after doing this, I should also reduce land to reduce land, land minus minus and finally return

129
00:09:39,620 --> 00:09:41,210
the element that is deleted.

130
00:09:43,430 --> 00:09:45,920
That's all these are the functions I have written.

131
00:09:46,920 --> 00:09:50,100
Now I will develop a main function, I will remove this.

132
00:09:52,320 --> 00:09:56,980
Now, instead, the main function, you can make it as a menu driven function also, but I will read

133
00:09:56,980 --> 00:09:58,050
it as a simple function.

134
00:09:58,060 --> 00:10:05,260
First of all, I will create an array object IRR of size and I'll take the same size then.

135
00:10:06,230 --> 00:10:09,290
Then here I will insert a few elements and then delete the elements.

136
00:10:09,320 --> 00:10:12,800
First of all, Iran does not insert.

137
00:10:15,560 --> 00:10:18,480
Index zero, aliment five.

138
00:10:18,500 --> 00:10:24,680
So first time giving next, then I'm giving element five, then aeronaut insert.

139
00:10:25,640 --> 00:10:29,300
And I will give the index next index that is value six.

140
00:10:30,670 --> 00:10:37,210
Then there are the insert alguien element at index two, that is nine.

141
00:10:38,740 --> 00:10:45,100
Now, let us display the elements so I should use object name that is Iara and call function display

142
00:10:45,430 --> 00:10:46,690
a display.

143
00:10:49,370 --> 00:10:54,050
Let us on the program still here and see I should get the elements five, six and nine.

144
00:10:58,110 --> 00:11:04,020
Yes, I got the elements that are five, six and nine, the elements are displayed, they're properly

145
00:11:04,020 --> 00:11:04,620
inserted.

146
00:11:05,730 --> 00:11:08,340
Now, after this, let us try to delete an element.

147
00:11:11,030 --> 00:11:16,520
So whatever the element is deleted, I will try to display that element here and not delete.

148
00:11:17,560 --> 00:11:21,490
And I will deliver an element from index zero, so five should be deleted.

149
00:11:24,010 --> 00:11:25,840
And then and then for the new line.

150
00:11:27,720 --> 00:11:29,230
I evenly split elements.

151
00:11:31,130 --> 00:11:34,620
I should get the remaining elements that are six and nine.

152
00:11:34,790 --> 00:11:35,720
Let us try this.

153
00:11:39,780 --> 00:11:44,730
The first five, six, nine were the element and five was deleted and the remaining elements are six

154
00:11:44,730 --> 00:11:45,360
and nine.

155
00:11:47,090 --> 00:11:52,480
That's all this is working, I have tried a few functions, I have written a few functions and I have

156
00:11:52,480 --> 00:11:53,110
tried them.

157
00:11:55,270 --> 00:12:02,080
Not a very interesting thing that this uhry, what we have created is only of type integer.

158
00:12:04,050 --> 00:12:06,150
If we want a float type, then.

159
00:12:07,610 --> 00:12:13,490
I should write a separate class, no, instead of writing a separate class, it's possible to make a

160
00:12:13,490 --> 00:12:15,420
generic array in C++.

161
00:12:15,420 --> 00:12:20,960
So for creating generic arrays, we can create a template.

162
00:12:22,390 --> 00:12:27,460
So template class T. Instead of this integer type array, we can mcginest.

163
00:12:29,020 --> 00:12:35,350
Now, I will show you how we can write our template class, so the class we have written, we will convert

164
00:12:35,350 --> 00:12:37,300
it into a template class.

165
00:12:37,450 --> 00:12:38,350
It's very easy.

166
00:12:38,650 --> 00:12:42,690
Once you have a complete class ready with you, you can easily convert into a template.

167
00:12:43,390 --> 00:12:44,330
So let us do it.

168
00:12:44,500 --> 00:12:49,360
So on the top, I have written a complete class team before the class and inside the class.

169
00:12:49,360 --> 00:12:55,300
Wherever I want to make a generic data type, I should try to be there, not inside the constructor

170
00:12:55,300 --> 00:12:57,090
or is creator of type integer.

171
00:12:57,100 --> 00:13:00,340
Let us see and hear inside the parameters.

172
00:13:00,340 --> 00:13:01,050
Constructor.

173
00:13:01,090 --> 00:13:04,180
This also is team then.

174
00:13:05,320 --> 00:13:09,570
These are fully functional elements that I'm going to insert that should also be.

175
00:13:12,050 --> 00:13:15,480
And the delete function will delete an element and return the value.

176
00:13:15,500 --> 00:13:18,600
So this should also be the know the class ends here.

177
00:13:18,620 --> 00:13:20,580
So also a template ends here.

178
00:13:22,040 --> 00:13:27,290
The effect of this template has finished at the end of the class of when we are implementing functions

179
00:13:27,290 --> 00:13:30,400
outside the class using scope resolution than for each function.

180
00:13:30,410 --> 00:13:31,280
We should try, Don.

181
00:13:33,070 --> 00:13:40,630
Then for each function, we should write on Template Klasky and we should use this template, see,

182
00:13:40,630 --> 00:13:43,480
first of all, the important thing is the closest template type.

183
00:13:43,480 --> 00:13:44,830
So I should write here.

184
00:13:47,090 --> 00:13:49,280
And inside dysfunction, nothing is template.

185
00:13:50,860 --> 00:13:57,550
Then for this class, then for this function also, I should write a complete class de.

186
00:13:58,530 --> 00:14:04,710
And first of all, I should make this last name as city, I should pass the parameterized for this class

187
00:14:04,710 --> 00:14:10,440
name and the X value, the element that we are taking is of Taiping.

188
00:14:12,090 --> 00:14:19,170
That sits inside the clock, inside the function, there is nothing like a template now here for delete

189
00:14:19,170 --> 00:14:19,570
function.

190
00:14:19,590 --> 00:14:25,500
Also, I should write on Template Plasty for each function we must write on and the class name.

191
00:14:25,980 --> 00:14:33,060
We should bastia as a parameter and also change the data members if they are supposed to be template

192
00:14:33,060 --> 00:14:36,390
type like delete function is going to return template type.

193
00:14:36,390 --> 00:14:39,240
And this X is also a template type.

194
00:14:41,340 --> 00:14:46,320
NetSol, everything is really inside the main function when I'm creating an object of type Uhry here

195
00:14:46,320 --> 00:14:49,150
in this line, I should mention that I type now.

196
00:14:49,380 --> 00:14:52,700
No, I'm going to use it and it's going to be integer type utting.

197
00:14:54,420 --> 00:15:00,900
They all see it was so simple, I have converted a complete glass into a complete glass, so if you

198
00:15:00,900 --> 00:15:06,010
don't know this and you want to practice this, then you can watch it again and do it by yourself simultaneously.

199
00:15:06,720 --> 00:15:08,310
I will run the program and show you.

200
00:15:10,580 --> 00:15:13,340
Yes, Burgan look successfully the same results we got.

201
00:15:14,960 --> 00:15:23,390
No, let us try to take different data types here instead of integer, let us take float and the values.

202
00:15:23,390 --> 00:15:29,980
What I will write here is five point two and six point four and nine point one.

203
00:15:31,100 --> 00:15:32,840
Let us see whether it works or not.

204
00:15:36,500 --> 00:15:42,170
Yes, it is working five point to six point four, nine point one and eliminate the next little was

205
00:15:42,170 --> 00:15:46,400
5.2 that was deleted and the remaining elements are six point forty nine point one.

206
00:15:47,120 --> 00:15:49,820
Now, you can take any data type and you can.

207
00:15:51,110 --> 00:15:59,870
Implement this study, so I will change at a time to a character that I will take few characters, see

208
00:16:00,140 --> 00:16:06,530
character is a and second character is see and the third character is the.

209
00:16:08,130 --> 00:16:11,400
Now, Akhdar inserted an evil will be deleted.

210
00:16:13,810 --> 00:16:19,330
Yes, it's working, actors are inserted and it is deleted, Nancy, and the remaining.

211
00:16:22,290 --> 00:16:30,080
So that's all but C++ program, I have shown you few functions remaining function as a student, exercise,

212
00:16:30,090 --> 00:16:31,940
you complete all the remaining function.

213
00:16:32,400 --> 00:16:37,410
The procedures are the same as what we have learned on the whiteboard, only the way we write the code.

214
00:16:37,410 --> 00:16:39,330
The style of writing the code is different.

215
00:16:39,420 --> 00:16:42,480
And it's not major different from C program.

216
00:16:42,930 --> 00:16:44,670
It's almost similar to C program.

217
00:16:46,650 --> 00:16:48,620
Back to this program that follows.

