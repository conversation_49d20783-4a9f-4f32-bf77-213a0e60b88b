1
00:00:00,120 --> 00:00:03,969
So the topic is formulas for rotation at the time of insertion.

2
00:00:04,830 --> 00:00:07,260
So we have already seen four rotations.

3
00:00:07,860 --> 00:00:09,900
We have seen up on only three nodes.

4
00:00:10,170 --> 00:00:14,790
If there are many nodes in the tree, then how rotation should we perform?

5
00:00:16,320 --> 00:00:20,940
So for that, we will look at a formula, so I'll show you a little Formula One for a little rotation

6
00:00:20,940 --> 00:00:21,990
Danniella rotation.

7
00:00:22,380 --> 00:00:24,120
Same thing applies for other people.

8
00:00:25,110 --> 00:00:26,480
Let us look at Fuseproject.

9
00:00:27,030 --> 00:00:29,400
This is for L'il rotation.

10
00:00:29,640 --> 00:00:31,230
I have taken an example three.

11
00:00:32,490 --> 00:00:37,230
Only trainloads know it is having left child, right child.

12
00:00:37,350 --> 00:00:41,330
This is having a child, I imagine a lot of Noles out there.

13
00:00:42,570 --> 00:00:46,910
So Azeroth know imaginable that also a lot of elements are there.

14
00:00:47,250 --> 00:00:53,190
So where you are performing a rotation, we are performing rotation upon this node who became imbalanced

15
00:00:53,190 --> 00:00:58,490
because balance factors to rotation is perform over this more.

16
00:00:58,920 --> 00:01:03,510
So we are talking about only three nodes, maybe tolerance of node farther than that.

17
00:01:03,510 --> 00:01:05,170
We imagine a lot of nodes out there in there.

18
00:01:05,640 --> 00:01:06,150
All right.

19
00:01:06,570 --> 00:01:07,620
So now take it out.

20
00:01:07,680 --> 00:01:11,040
The formula only with the trainloads if these are the balance factor.

21
00:01:11,070 --> 00:01:11,640
This is true.

22
00:01:11,640 --> 00:01:14,700
This is one of those settlements where the insertion is done.

23
00:01:14,700 --> 00:01:16,210
Left, left, right.

24
00:01:16,500 --> 00:01:19,850
If this is minus one means it is on.

25
00:01:20,950 --> 00:01:27,950
OK, this is this is in balance with the two men sitting on the side, then it's left child is one.

26
00:01:28,240 --> 00:01:29,460
It is again on the side.

27
00:01:29,650 --> 00:01:31,840
If it is minus one means it's on the right side.

28
00:01:32,200 --> 00:01:33,910
OK, but this also you can observe.

29
00:01:34,790 --> 00:01:41,090
Now, finally, let us see tradition, which we are going to perform a little rotation, a little rotation

30
00:01:41,090 --> 00:01:44,740
around the snow up on this north, then what is the result?

31
00:01:45,050 --> 00:01:46,060
Who will come here?

32
00:01:46,700 --> 00:01:49,030
B will come here where?

33
00:01:49,040 --> 00:01:51,160
A will go on the right side.

34
00:01:51,530 --> 00:01:54,140
Then who will come in the place of B, C?

35
00:01:56,640 --> 00:02:04,530
What about the rest of the North Sea's left children, China, as it is, is right, China as it is

36
00:02:04,560 --> 00:02:08,100
right now, who is missing bees, right?

37
00:02:08,100 --> 00:02:09,300
China Sea.

38
00:02:09,570 --> 00:02:15,000
This is on the right hand side of B and B has became root all the right side.

39
00:02:15,000 --> 00:02:17,300
Is there then will this be should come.

40
00:02:17,550 --> 00:02:22,830
It will be on the right hand side of B only, but it will become the left child of A.

41
00:02:23,870 --> 00:02:30,230
And after rotation, we know that the balance factors, all of them becomes level is for those three

42
00:02:30,230 --> 00:02:30,710
notes.

43
00:02:32,300 --> 00:02:34,190
That's what this is like, a formula.

44
00:02:35,440 --> 00:02:42,040
So how this is working, I'll show you, I said that this is a trade, OK, so I'm keeping my finger

45
00:02:42,040 --> 00:02:43,800
here and pulling this thread.

46
00:02:43,930 --> 00:02:50,770
So if something is attached to this thread like this or something is attached to this thread like this,

47
00:02:50,770 --> 00:02:51,290
like this.

48
00:02:51,670 --> 00:02:58,060
So if I put a finger and pull it on the side, then this will stretch like this and this will reduce

49
00:02:58,060 --> 00:02:58,420
like.

50
00:03:01,280 --> 00:03:02,890
Then what about that ping connected?

51
00:03:03,080 --> 00:03:04,100
It will come the site.

52
00:03:05,680 --> 00:03:07,350
It will come the site, right?

53
00:03:07,600 --> 00:03:13,120
So whatever it was here on the site, it will come on that side, same thing this be was on this site.

54
00:03:13,120 --> 00:03:14,950
Just it's like a third bullet.

55
00:03:15,130 --> 00:03:16,950
So this will come this site.

56
00:03:18,180 --> 00:03:23,550
So the effect is just like a rotation, so that's like people who have developed this one, they have

57
00:03:23,550 --> 00:03:27,710
given the name as rotation because the effect is just like rotation.

58
00:03:28,470 --> 00:03:33,660
Let us look at example of a location and apply that formula as an example.

59
00:03:33,660 --> 00:03:37,470
I have taken a bigger tree with around nine nodes out there.

60
00:03:38,430 --> 00:03:42,160
Now let us give balance factor and check whether everything is balanced or not.

61
00:03:42,840 --> 00:03:44,820
Let us give the balance factor for lease.

62
00:03:44,850 --> 00:03:47,780
This is zero zero zero zero zero.

63
00:03:47,790 --> 00:03:49,370
Then let us move up.

64
00:03:49,680 --> 00:03:52,050
So this is zero, right?

65
00:03:52,140 --> 00:03:55,710
This one zero one minus one.

66
00:03:56,220 --> 00:03:57,920
This is one, two, one, two.

67
00:03:58,320 --> 00:03:59,180
So this is zero.

68
00:04:00,310 --> 00:04:03,730
This one, one, two, three, one, two.

69
00:04:05,230 --> 00:04:10,250
One, one, two, three, minus two for one.

70
00:04:10,540 --> 00:04:13,850
And what about this zero and one minus one?

71
00:04:14,590 --> 00:04:15,720
So this is a balance?

72
00:04:15,910 --> 00:04:19,930
No, I will insert a new value that is for insert four.

73
00:04:20,320 --> 00:04:22,180
Key four, right.

74
00:04:23,200 --> 00:04:28,870
For four is a smaller four is a smaller than Tuqay and also smaller than 20.

75
00:04:28,870 --> 00:04:29,760
Smaller than 10.

76
00:04:29,770 --> 00:04:30,800
Smaller than five.

77
00:04:31,090 --> 00:04:32,400
So it comes here.

78
00:04:33,490 --> 00:04:37,430
So once it comes here, the balance factors along this path will update.

79
00:04:37,780 --> 00:04:38,780
So this is zero.

80
00:04:39,100 --> 00:04:39,960
What about this one?

81
00:04:39,980 --> 00:04:41,720
Now, this is one zero one.

82
00:04:42,070 --> 00:04:42,820
What about this one?

83
00:04:42,850 --> 00:04:43,150
No.

84
00:04:43,300 --> 00:04:44,550
One, two and one.

85
00:04:44,560 --> 00:04:46,690
So this is one, two, minus one one.

86
00:04:47,050 --> 00:04:47,710
What about this?

87
00:04:47,710 --> 00:04:48,760
One, two, three.

88
00:04:49,090 --> 00:04:52,210
And one, two, three, minus two is one.

89
00:04:52,510 --> 00:04:53,200
What about this?

90
00:04:53,200 --> 00:04:55,150
One, two, three, four.

91
00:04:55,630 --> 00:04:56,540
One, two.

92
00:04:56,560 --> 00:04:57,790
So this became two.

93
00:04:59,410 --> 00:05:02,770
Yes, this node became imbalance.

94
00:05:03,070 --> 00:05:06,210
So here root node became imbalance.

95
00:05:06,350 --> 00:05:06,720
Right.

96
00:05:06,940 --> 00:05:08,230
I selected the three sides.

97
00:05:08,230 --> 00:05:09,700
That rule becomes imbalance.

98
00:05:09,860 --> 00:05:13,600
OK, now we can see perfectly who became imbalance.

99
00:05:13,750 --> 00:05:17,740
This one where the insertion was done from here.

100
00:05:17,760 --> 00:05:18,160
Chicken.

101
00:05:18,730 --> 00:05:22,570
Left, left, left, left.

102
00:05:24,160 --> 00:05:26,480
Shall I say LLN four times?

103
00:05:26,620 --> 00:05:33,240
No, you have to consider only three N three N or two steps.

104
00:05:33,610 --> 00:05:34,720
So what are those steps?

105
00:05:35,080 --> 00:05:35,650
These.

106
00:05:36,970 --> 00:05:41,980
Only three notes El Al after this point.

107
00:05:43,100 --> 00:05:49,970
Wherever it is, Incirlik, Etisalat, see this game on the site, if suppose this was not for I have

108
00:05:49,970 --> 00:05:56,690
inserted Suppos six so it will come the site then also it then we have to see from this node just two

109
00:05:56,690 --> 00:06:01,360
steps, NetSol, which will perform electrocution only.

110
00:06:01,670 --> 00:06:04,060
So I will draw or get a tree here.

111
00:06:04,280 --> 00:06:08,660
So the rotation will be performed around this node around Besner.

112
00:06:08,960 --> 00:06:09,380
So.

113
00:06:10,800 --> 00:06:21,930
He has sold 20 will move up and the place of 2010 will come at the place of 10 five and for all will

114
00:06:21,930 --> 00:06:26,610
move five four, order will move and 30 will move the site.

115
00:06:26,790 --> 00:06:32,040
So 30 to site 40, this site 50, this site.

116
00:06:33,370 --> 00:06:39,160
Now, what about right, child of ten, ten is Dorna remains on its own place.

117
00:06:39,790 --> 00:06:44,040
What about the right child of 20 20 became newgroup now?

118
00:06:44,500 --> 00:06:49,750
So this subtree of opportunity will go on this side here.

119
00:06:51,140 --> 00:06:51,960
So what is there?

120
00:06:52,070 --> 00:06:58,610
Twenty five and along with that, it is having the right to keep it as it is, don't disturb anything

121
00:06:59,780 --> 00:07:05,360
just instead of linking here it is linking the site because Trinity has become a new route.

122
00:07:06,140 --> 00:07:06,950
This is the three.

123
00:07:08,670 --> 00:07:18,210
Let us check the balance factors, zero one zero zero, this is zero, this is zero one minus one,

124
00:07:18,720 --> 00:07:20,610
this one zero one minus one.

125
00:07:21,430 --> 00:07:29,680
What about this one, two and one, one, two and one, so this is one, this is one two one two zero.

126
00:07:30,070 --> 00:07:34,860
This one, one, two, three, one, two, three zero.

127
00:07:35,560 --> 00:07:39,760
See the normal over which we perform rotation that should be considered.

128
00:07:39,770 --> 00:07:40,820
All right.

129
00:07:41,590 --> 00:07:43,870
So this is a balanced Aviel three.

130
00:07:44,830 --> 00:07:51,250
So I have given you an example without food, then I have inserted food, then it became imbalanced

131
00:07:51,260 --> 00:07:54,980
and I have shown you how to use that a location for balancing.

132
00:07:55,390 --> 00:07:57,570
So you must practice this one by yourself.

133
00:07:57,610 --> 00:07:58,390
Watch it again.

134
00:07:58,390 --> 00:08:01,950
Watch this video again and pause it and do it by yourself.

135
00:08:02,960 --> 00:08:06,520
Next, I will show you a larger rotation formula.

136
00:08:07,070 --> 00:08:11,750
This is an example tree which will work out a formula for a large rotation.

137
00:08:11,960 --> 00:08:19,520
So already we have three nodes and one of the nodes imbalance, it is a positive means it is left side

138
00:08:19,520 --> 00:08:21,500
imbalance right now.

139
00:08:21,620 --> 00:08:22,190
This is negative.

140
00:08:22,470 --> 00:08:23,450
This is right sided.

141
00:08:23,730 --> 00:08:28,310
So this is I assume that something is inserted below sea, right?

142
00:08:28,520 --> 00:08:30,680
Not just see under that node.

143
00:08:30,680 --> 00:08:34,890
See, as I said, tradition is done only among three nodes.

144
00:08:34,909 --> 00:08:36,240
So this became in balance.

145
00:08:36,289 --> 00:08:37,970
We'll just see three nodes.

146
00:08:40,030 --> 00:08:43,429
Now, a lot of this is a double rotation.

147
00:08:44,020 --> 00:08:48,900
We know the dark matter, that sea will move up and it will move on the right hand side.

148
00:08:49,150 --> 00:08:50,350
So I will show it like that.

149
00:08:50,350 --> 00:08:56,590
Only so sees coming here, being in its own place, it will move on the right hand side.

150
00:08:57,950 --> 00:08:58,480
Perfect.

151
00:08:59,160 --> 00:09:01,540
What about bees left, China bees left.

152
00:09:01,540 --> 00:09:06,190
China will be added this whatever it is right it will be as it leaves.

153
00:09:07,390 --> 00:09:14,080
And the problem is with the children on see this is on the left side of Cedar's on the right hand side

154
00:09:14,080 --> 00:09:21,700
of C, so s.L will remain on the left hand side of C, C, R will remain on the right hand side of C.

155
00:09:22,940 --> 00:09:25,000
That's how the children will get distributed.

156
00:09:25,570 --> 00:09:34,870
Once again, he sees s.L S.R. So C became root, so Cele on the left subtree right to the site and C

157
00:09:34,870 --> 00:09:35,740
are on that side.

158
00:09:35,890 --> 00:09:37,690
So I'll take one example and show you.

159
00:09:37,690 --> 00:09:45,480
Suppose this is a given tree check the balance factors zero zero zero zero.

160
00:09:46,420 --> 00:09:48,250
Now this is one and zero one.

161
00:09:48,520 --> 00:09:51,270
This is one minus one zero.

162
00:09:51,310 --> 00:09:54,130
This is one two one two two zero.

163
00:09:54,360 --> 00:09:57,370
This is zero and one minus one.

164
00:09:57,730 --> 00:09:58,840
One, two, three.

165
00:09:59,950 --> 00:10:01,570
One, two, one.

166
00:10:02,820 --> 00:10:12,500
So everything is perfect and balanced, evidently, no, let us insert a key thirty seven, key twenty

167
00:10:12,510 --> 00:10:15,930
seven start from here.

168
00:10:15,960 --> 00:10:18,270
Search for a location for twenty seven.

169
00:10:18,270 --> 00:10:20,850
Right, search for 27, if not for inserted.

170
00:10:22,220 --> 00:10:28,310
Twenty seven is smaller, more, the site 27 has agreed to move the site 27, a smaller move, the site

171
00:10:28,310 --> 00:10:29,360
27 is greater.

172
00:10:29,390 --> 00:10:34,050
It's not insert 127 is inserted here now.

173
00:10:34,070 --> 00:10:35,780
But in fact, there's an update.

174
00:10:36,560 --> 00:10:37,490
This is zero.

175
00:10:38,860 --> 00:10:45,190
This is minus one because zero minus one, minus one, one, two, and this is one, so this becomes

176
00:10:45,190 --> 00:10:45,550
one.

177
00:10:46,390 --> 00:10:49,160
This one, two, one, two, three.

178
00:10:49,330 --> 00:10:51,910
So two minus three becomes minus one.

179
00:10:52,510 --> 00:10:57,400
And for this one, one, two, three, four, one, two.

180
00:10:57,640 --> 00:10:59,590
So this became imbalance.

181
00:10:59,740 --> 00:11:01,270
This in order to get imbalance.

182
00:11:03,170 --> 00:11:07,690
Two is the violence factor, so the north became imbalance, we have to perform rotation on that one.

183
00:11:07,700 --> 00:11:11,870
So we traditionally should perform check the direction of insertion.

184
00:11:11,900 --> 00:11:13,370
This is newly inserted node.

185
00:11:13,910 --> 00:11:16,880
Left, right, left, right.

186
00:11:17,210 --> 00:11:17,970
A lot and.

187
00:11:18,230 --> 00:11:21,200
No, no, just we have to check with the three nodes.

188
00:11:21,740 --> 00:11:25,310
It went on the left hand side and then the right hand side.

189
00:11:26,030 --> 00:11:26,410
Right.

190
00:11:26,520 --> 00:11:31,460
So this 37 went on the left side, then on the right side and below that where it has gone, we don't

191
00:11:31,460 --> 00:11:31,690
know.

192
00:11:32,570 --> 00:11:36,100
We are concerned about only these three nodes because this node became imbalance.

193
00:11:36,110 --> 00:11:40,220
So from that point, just Raynaud's in the direction of insertion.

194
00:11:40,940 --> 00:11:43,640
So this is a lot a lot of rotation.

195
00:11:43,940 --> 00:11:45,320
So we know the direct answer.

196
00:11:45,330 --> 00:11:51,560
So will directly to take this one so that we will go to 40 at the place of 40, 40 will move on the

197
00:11:51,560 --> 00:11:52,310
right hand side.

198
00:11:52,610 --> 00:11:55,230
And on the left hand side, Pándy remains as it is.

199
00:11:55,340 --> 00:12:01,670
So let us finish these extreme left then then five as it is on that side.

200
00:12:01,670 --> 00:12:05,540
We have 50 and 60 as it is 50 and 60.

201
00:12:08,060 --> 00:12:10,430
Then Turkey has become a new route.

202
00:12:10,460 --> 00:12:17,450
So what about twenty five and twenty seven, the remaining left side only so twenty five will be as

203
00:12:17,750 --> 00:12:24,740
shell of this one and it's right here and it's 27, then thirty five will remain on the right hand side

204
00:12:24,740 --> 00:12:25,960
only right subtree only.

205
00:12:26,210 --> 00:12:30,830
So this will become a left child of forty thirty five.

206
00:12:32,200 --> 00:12:34,220
It will become a left shell of previous food.

207
00:12:34,960 --> 00:12:37,670
So this is a balanced aviary.

208
00:12:38,290 --> 00:12:40,610
Let us check the balance factors and find out.

209
00:12:41,350 --> 00:12:43,710
Zero zero zero zero.

210
00:12:43,990 --> 00:12:45,540
This is one minus zero one.

211
00:12:45,910 --> 00:12:47,830
This is zero minus one minus one.

212
00:12:48,040 --> 00:12:53,540
This one two one two two minus two, zero versus zero.

213
00:12:54,220 --> 00:12:56,810
This is zero minus one, minus one.

214
00:12:56,830 --> 00:12:58,560
So this is one minus two.

215
00:12:58,750 --> 00:12:59,790
So this is minus one.

216
00:13:00,050 --> 00:13:01,840
This one, one, two, three.

217
00:13:02,080 --> 00:13:03,320
Are you come this way also.

218
00:13:03,340 --> 00:13:04,240
One, two, three.

219
00:13:04,570 --> 00:13:07,180
And one, two, three, three minus three zero.

220
00:13:07,750 --> 00:13:08,640
That is balanced.

221
00:13:08,860 --> 00:13:13,090
So I've shown you formula for a and a lot.

222
00:13:14,150 --> 00:13:18,420
Order and order, I hope you have understood, so I don't have to show them.

223
00:13:18,800 --> 00:13:21,560
All right, so that is a student exercice.

224
00:13:22,500 --> 00:13:28,080
So you prepared the format for them, you work out and prepare the foreigners, so next we will take

225
00:13:28,080 --> 00:13:32,710
a few keys and we'll see how aviad trees are generated.

226
00:13:32,760 --> 00:13:34,620
So that'll explain you in the next video.

