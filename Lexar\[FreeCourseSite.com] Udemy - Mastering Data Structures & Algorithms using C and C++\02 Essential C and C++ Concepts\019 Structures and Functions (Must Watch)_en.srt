1
00:00:00,300 --> 00:00:05,030
In this video I will talk about structures and functions.

2
00:00:05,140 --> 00:00:07,170
See, this is a very important topic.

3
00:00:07,170 --> 00:00:09,790
The style of code that I have written here,

4
00:00:10,050 --> 00:00:13,250
the same style is followed in the entire course.

5
00:00:13,440 --> 00:00:17,810
So you should be familiar with this structure, or with this style of programming.

6
00:00:18,950 --> 00:00:22,550
So for example, for explanation already I have taken an example.

7
00:00:22,940 --> 00:00:24,690
So let us study the example.

8
00:00:24,800 --> 00:00:30,160
See I have a structure for rectangle which we have already seen having length and breadth, and this is

9
00:00:30,170 --> 00:00:36,020
the main function, main function is having a variable of type rectangle. This belongs to main function

10
00:00:36,050 --> 00:00:44,150
that is r, then the most important thing to observe here is main function is not having any instructions

11
00:00:44,240 --> 00:00:47,480
of its own but only calling functions.

12
00:00:47,510 --> 00:00:51,560
Yes everything is done by calling functions.

13
00:00:51,580 --> 00:00:57,680
There is a function for initialize, there is a function for area and change length. Let us see the function, initialize

14
00:00:57,680 --> 00:00:58,250
function.

15
00:00:58,250 --> 00:01:01,520
This, we want to initialize this rectangle by filling 10 and 5.

16
00:01:02,120 --> 00:01:05,180
And this is for calculating area of a rectangle.

17
00:01:05,180 --> 00:01:12,250
This is for changing the length of a rectangle to 20. Now, if you observe these functions,

18
00:01:12,290 --> 00:01:18,110
This initialize function should modify this one, so function must be called by address. So I am sending

19
00:01:18,260 --> 00:01:20,540
& address of the rectangle.

20
00:01:20,550 --> 00:01:25,280
Then change length function should change this rectangle that is actual parameter, this one.

21
00:01:25,610 --> 00:01:29,140
So here I am sending by address. Then area,

22
00:01:29,240 --> 00:01:34,270
for that we don't have to make any changes just we want the result, that is length * breadth.

23
00:01:34,280 --> 00:01:36,740
So this is Call by Value.

24
00:01:36,740 --> 00:01:42,830
So let us look at these functions. See it is the first function, initialize ( ) , which is taking the address

25
00:01:43,160 --> 00:01:44,210
address variable r.

26
00:01:44,600 --> 00:01:50,720
So this r will be a pointer to this rectangle as we are sending address here. Now using the pointer

27
00:01:50,840 --> 00:01:56,450
it will fill up length and breadth. Then, this is area function.

28
00:01:56,450 --> 00:02:03,310
This is taking Call by Value, so it will have its own copy of rectangle containing length and breadth.

29
00:02:04,640 --> 00:02:07,180
So this will not directly access that one.

30
00:02:07,280 --> 00:02:08,419
This will have its own copy.

31
00:02:08,419 --> 00:02:10,410
This is call by value.

32
00:02:10,440 --> 00:02:13,120
And next let us look at this change in function.

33
00:02:13,130 --> 00:02:16,520
This is having a pointer on another length, that is new length,

34
00:02:16,520 --> 00:02:19,000
So this is also pointing to the same thing.

35
00:02:19,380 --> 00:02:22,270
And it is just changing length.

36
00:02:22,520 --> 00:02:28,060
These two functions are pointers, so they are having call by address, so they are using arrows,

37
00:02:28,160 --> 00:02:29,310
And this is call by value,

38
00:02:29,300 --> 00:02:33,350
So this is using dot operator and it is accessing its own rectangle.

39
00:02:33,350 --> 00:02:35,450
Now let us see the working of this entire code.

40
00:02:36,290 --> 00:02:38,690
Let us start executing main function.

41
00:02:38,690 --> 00:02:44,080
Having a rectangle, this one, then call initialize ( ) function by sending an address a new value length and

42
00:02:44,270 --> 00:02:46,350
breadth, so length and breadth.

43
00:02:46,370 --> 00:02:50,440
This is call by value. 10 is copied in this one, 5 is copied in this one, and this is a pointer pointing

44
00:02:50,450 --> 00:02:56,870
to that rectangle that belongs to main ( ) function, and 
r -> length = l; and, r -> breadth = b; so this is

45
00:02:56,870 --> 00:03:03,400
will be 10 and 5. And the function returns, is not returning anything because,

46
00:03:03,500 --> 00:03:09,680
It doesn't have to return anything it is just initializing those values. Then, back to the main ( ) function,

47
00:03:09,710 --> 00:03:11,320
call area ( ) function.

48
00:03:11,750 --> 00:03:19,150
This is call by value, a new object is created for r then 10 and 5 are copied and it is taking called

49
00:03:19,180 --> 00:03:25,580
a value then it is calculating length * breadth, area and returning, so it returns integer type; but I'm

50
00:03:25,580 --> 00:03:32,600
not using that value, just I called area ( ) function. Then, third changeLength ( ) function. I want to change the length of

51
00:03:32,600 --> 00:03:42,290
this rectangle to 20, so send the address, then also a new length that is 20 then, r -> length = 20; So this

52
00:03:42,290 --> 00:03:49,830
will change to 20, this will become 20, an actual value is changed, actual parameter is changeed.

53
00:03:51,060 --> 00:03:51,880
So that's all.

54
00:03:51,960 --> 00:03:59,360
So I have just demonstrated, how we can write down functions upon structure. Now, conclusion.

55
00:03:59,510 --> 00:04:07,470
See, this is style of programming leads to object orientation. And this is the highest level of programming

56
00:04:07,470 --> 00:04:15,960
that we can do in C language by writing structures and functions. How it leads to object orientation and

57
00:04:15,960 --> 00:04:20,100
How it is highest, let us see. So, this is a structure.

58
00:04:20,100 --> 00:04:25,840
This function is related to this structure, and this function area ( ) ,

59
00:04:26,010 --> 00:04:28,400
This is also dependent on the same structure.

60
00:04:28,650 --> 00:04:29,710
And this one also.

61
00:04:29,970 --> 00:04:37,590
So all these functions are related to that structure only. So in C programming, this is the highest level

62
00:04:37,590 --> 00:04:43,020
of programming, where we define a structure and we write all the functions related to that structure

63
00:04:43,410 --> 00:04:48,960
because, grouping of data at one place is a structure, grouping the instructions for performing a task

64
00:04:49,050 --> 00:04:50,550
is a function.

65
00:04:50,550 --> 00:04:55,560
So this is the style we follow in C language. Then in coming video,

66
00:04:55,580 --> 00:04:59,690
I will discuss the concept of Object Orientation, so I will show how write a class,

67
00:05:00,030 --> 00:05:07,440
So the same example I will take and the similar code I will modify it as a class and show you.

68
00:05:07,500 --> 00:05:08,490
So that's it.

69
00:05:08,490 --> 00:05:14,550
This code is very important because, I am following the same style in the entire course. So that's all in

70
00:05:14,550 --> 00:05:15,030
So that's all in this video.

