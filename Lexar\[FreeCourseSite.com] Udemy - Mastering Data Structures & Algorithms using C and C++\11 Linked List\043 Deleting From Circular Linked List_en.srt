1
00:00:00,510 --> 00:00:03,210
And this video will look at the lead operation.

2
00:00:03,990 --> 00:00:07,910
There are two cases for deletion just similar to linear list.

3
00:00:08,550 --> 00:00:10,980
Two cases are deleting ignored.

4
00:00:11,280 --> 00:00:14,100
That is this sort of special in this one.

5
00:00:14,400 --> 00:00:18,570
If this note is deleted, then some other note.

6
00:00:18,570 --> 00:00:20,160
You have to make it as hadnot.

7
00:00:21,030 --> 00:00:22,320
You have to move ahead.

8
00:00:22,530 --> 00:00:25,060
So whichever one you can select because it is circular.

9
00:00:25,260 --> 00:00:28,350
So we will be selecting this next node as a handyman.

10
00:00:28,980 --> 00:00:32,640
Now, second one is deleting a node from a given position.

11
00:00:32,640 --> 00:00:38,080
So any other node like third or fourth or fifth or deleting a node from any given position.

12
00:00:38,880 --> 00:00:42,660
So this was the same as the deleting from linear links.

13
00:00:43,470 --> 00:00:45,340
So first, let us look at this procedure.

14
00:00:45,510 --> 00:00:49,040
Let us look at these steps quickly, because we already know about this one.

15
00:00:49,380 --> 00:00:51,780
Then next we will look at deleting Hadnot.

16
00:00:52,170 --> 00:00:55,610
So let us see deleting node from a given position.

17
00:00:56,750 --> 00:01:01,530
Let us delete a note from Ford, and that is this note I want to read.

18
00:01:02,000 --> 00:01:03,320
So what is the procedure?

19
00:01:04,040 --> 00:01:10,130
If you want to delete this note, then you should manipulate the snowman and make this third point on

20
00:01:10,140 --> 00:01:12,920
fifth and then you can delete this.

21
00:01:12,920 --> 00:01:16,600
Not so for that, I need a pointer on this.

22
00:01:16,610 --> 00:01:19,420
Ford, Naude also and Turner also.

23
00:01:19,790 --> 00:01:21,670
So remember, we will have one point.

24
00:01:21,730 --> 00:01:26,750
That is the tail pointer thirty two point us, one point that will be moving and the second point will

25
00:01:26,750 --> 00:01:27,380
be following.

26
00:01:28,280 --> 00:01:34,130
Or else we can bring a pointer, tilt her north and take one more pointer on next Nixonland or we can

27
00:01:34,130 --> 00:01:35,780
bring 2.0 from the beginning.

28
00:01:36,020 --> 00:01:37,310
So two options are there.

29
00:01:37,610 --> 00:01:43,640
So now I will be bringing just one pointer from here till card, nor then I will take one more pointer

30
00:01:43,640 --> 00:01:45,790
here so I will not be moving to point.

31
00:01:45,980 --> 00:01:47,290
I'll be just moving one point.

32
00:01:47,480 --> 00:01:48,140
So let us see.

33
00:01:48,500 --> 00:01:55,770
Let us take a pointer B on hadnot I have to delete or so I should bring it on three.

34
00:01:55,790 --> 00:01:57,530
So how many steps if you move.

35
00:01:57,740 --> 00:01:58,490
One, two.

36
00:01:58,760 --> 00:02:02,210
So only two steps forward, not just two steps.

37
00:02:02,600 --> 00:02:07,280
It means fifth nordman to three steps so two steps less.

38
00:02:07,760 --> 00:02:11,900
So I need a pointer B so I will directly write on the goal be assigned here.

39
00:02:11,910 --> 00:02:13,290
The P starts from head.

40
00:02:13,880 --> 00:02:20,330
So with this, so this look will move a be for position minus two times or B will be pointing on a..

41
00:02:20,690 --> 00:02:22,880
Before the new one which we want to delete.

42
00:02:23,150 --> 00:02:23,620
So forth.

43
00:02:23,620 --> 00:02:23,750
No.

44
00:02:24,140 --> 00:02:24,740
Hard not.

45
00:02:25,700 --> 00:02:26,570
No I need a pointer.

46
00:02:26,570 --> 00:02:27,700
Unfortunate also.

47
00:02:27,710 --> 00:02:28,580
So take a pointer.

48
00:02:28,580 --> 00:02:29,780
Q Here.

49
00:02:30,080 --> 00:02:31,940
So Cure's these next.

50
00:02:32,330 --> 00:02:32,720
So.

51
00:02:32,720 --> 00:02:34,790
Q Is B's next.

52
00:02:35,510 --> 00:02:37,220
So cool pointing on the next block.

53
00:02:38,360 --> 00:02:44,950
Now, what changes have to make these next to should the point on kills soapies so peaceniks should

54
00:02:44,960 --> 00:02:50,000
point on cuz NICS soapies next is binding on kibbutzniks.

55
00:02:50,030 --> 00:02:50,790
So this is over.

56
00:02:51,170 --> 00:02:53,570
Now I can easily delete this note.

57
00:02:53,630 --> 00:02:57,200
It's not a part of Lync list so I can take out the value and delete it.

58
00:02:57,440 --> 00:03:00,380
So take out the data and variable X and delete them.

59
00:03:01,460 --> 00:03:05,150
So this procedure the same as deleting from Línea linguist.

60
00:03:06,770 --> 00:03:12,260
Now, let us see the bleeding head, and if I'm bleeding head, no this nor I can make it as hadnot,

61
00:03:12,440 --> 00:03:15,900
but the last checkpoint on this new head north.

62
00:03:16,100 --> 00:03:17,780
So this is the modification I have to do.

63
00:03:18,740 --> 00:03:25,910
So if I'm the first more so for doing this, I should take a pointer B up on the nose and move it until

64
00:03:25,910 --> 00:03:34,700
it reaches last known as Portishead note, then I should make that last known point on next Nordoff

65
00:03:34,700 --> 00:03:34,970
head.

66
00:03:35,240 --> 00:03:36,970
That is, this link will change.

67
00:03:37,700 --> 00:03:43,670
It should point here, then delete held and the move headed to the Snork.

68
00:03:45,350 --> 00:03:46,490
So it will be removed.

69
00:03:46,590 --> 00:03:47,750
And this note is deleted.

70
00:03:48,620 --> 00:03:50,510
These are the steps so far.

71
00:03:50,510 --> 00:03:53,030
This let me quickly right on the procedure for this one.

72
00:03:53,030 --> 00:03:56,770
So I will read on the pieces of code that perform this operation.

73
00:03:57,380 --> 00:03:59,000
I need to be up on head.

74
00:04:01,580 --> 00:04:04,520
We will move until it reaches last node.

75
00:04:05,390 --> 00:04:09,500
So, B, if moved until last note, you already know what the condition is.

76
00:04:09,770 --> 00:04:11,330
D, next is not equal.

77
00:04:11,350 --> 00:04:12,560
Go ahead then.

78
00:04:12,560 --> 00:04:14,810
Besnik should point on its next.

79
00:04:15,200 --> 00:04:16,420
So this is pointing here.

80
00:04:16,579 --> 00:04:17,570
This is pointing here.

81
00:04:18,620 --> 00:04:21,260
Then I should take out the data and delete this.

82
00:04:21,260 --> 00:04:26,060
Hard data is taken in X delete this node is deleted.

83
00:04:26,420 --> 00:04:28,620
Then I should make that point on this node.

84
00:04:28,640 --> 00:04:30,410
What is the small piece next.

85
00:04:30,560 --> 00:04:32,710
So make that point on piece next.

86
00:04:33,710 --> 00:04:37,670
So I have the code for deleting had do I have the code for deleting any other normal.

87
00:04:38,810 --> 00:04:46,400
Announces the time taken for deleting had notice, and because I have to be till the last note and the

88
00:04:46,400 --> 00:04:53,480
time taken here is minimum, constant, maximum and so forth, which normally this minimum second-order,

89
00:04:53,690 --> 00:04:58,010
if you're are sick, second, no time this constant, otherwise it is.

90
00:04:58,020 --> 00:05:02,660
And so I will on a complete function for deletion and I will show you.

91
00:05:03,170 --> 00:05:05,770
So here I have a complete delete function.

92
00:05:06,320 --> 00:05:07,520
Let me read it out.

93
00:05:07,940 --> 00:05:14,350
If position is one that means I want to delete first node then we should have a pointer B moving up

94
00:05:14,360 --> 00:05:15,350
till the last note.

95
00:05:15,410 --> 00:05:18,650
So this is the code for moving up to last than then.

96
00:05:18,650 --> 00:05:22,850
Here I have taken the data from head north in these statements.

97
00:05:22,850 --> 00:05:29,360
I'm taking it here that if peezy calls to head this not only node, there are no more nodes, just a

98
00:05:29,360 --> 00:05:35,390
single node in a link lists, then if you make the point up to last, then P will remain there only.

99
00:05:36,610 --> 00:05:41,740
If there's a single note, then a single note is deleted, then I should make Hasanuddin so that I'm

100
00:05:41,740 --> 00:05:48,100
taking care of her safety as a single note, then I should handle it especially otherwise.

101
00:05:48,340 --> 00:05:49,510
Make these next.

102
00:05:49,540 --> 00:05:52,280
That is point on the next last note.

103
00:05:52,280 --> 00:05:58,150
Should point on this note, then delete this note and move ahead to listen to these statements out there.

104
00:05:58,150 --> 00:06:00,730
Already we have seen these statements that are in single line.

105
00:06:00,730 --> 00:06:01,840
I have written them.

106
00:06:01,870 --> 00:06:07,840
This is the code for deleting a note from any other given position apart from index one or position

107
00:06:07,840 --> 00:06:08,090
one.

108
00:06:08,680 --> 00:06:14,560
Now, here again, I did not check the condition whether the position given is valid or not.

109
00:06:15,460 --> 00:06:21,520
And if I'm not checking what happens, you have to analyze this one figured out, take some position

110
00:06:21,520 --> 00:06:27,730
that is beyond the five five and take a five and take the size of six or ten or something.

111
00:06:28,090 --> 00:06:32,080
Then check what happens while writing the program.

112
00:06:32,080 --> 00:06:33,430
I will write down that condition.

