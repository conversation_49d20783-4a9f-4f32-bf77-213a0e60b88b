1
00:00:00,570 --> 00:00:07,840
And this we do have a look at a recursive function for pre-trial cells that are pre order in order and

2
00:00:07,840 --> 00:00:08,540
Polestar.

3
00:00:09,570 --> 00:00:15,780
So either you have an example ready, let us start with the preorder for traversal, I have taken an

4
00:00:15,780 --> 00:00:18,870
example trade with the LINGA representation.

5
00:00:19,950 --> 00:00:22,560
These are the notes with the data inside the note.

6
00:00:22,560 --> 00:00:26,560
And this is the address of unordered below each node addresses written.

7
00:00:26,940 --> 00:00:30,050
This is a recursive function for preorder traversal.

8
00:00:30,780 --> 00:00:32,520
So let us read the function.

9
00:00:32,549 --> 00:00:34,980
Then I will show how this function will find out.

10
00:00:35,310 --> 00:00:42,420
Preorder of this function is taking a parameter that is root of our tree, so we will pass a rule to

11
00:00:42,420 --> 00:00:42,810
that one.

12
00:00:42,810 --> 00:00:47,370
Supposedly, if you are calling, then we call it as preorder root.

13
00:00:47,400 --> 00:00:50,320
So we'll pass the rest of the root note that is 200 to this one.

14
00:00:50,700 --> 00:00:53,550
So here the parameter name is deep inside the function.

15
00:00:53,550 --> 00:00:57,210
It will check if these not null, then there is some node.

16
00:00:57,420 --> 00:01:02,640
If there is some node, then it will perform three steps, printed the data from that node.

17
00:01:04,090 --> 00:01:08,290
Second step, call itself recursively upon left China.

18
00:01:09,630 --> 00:01:13,860
Code step code itself recursively upon right change.

19
00:01:14,460 --> 00:01:21,150
So these are the three steps it will perform as it is recursive, it will call itself again and again

20
00:01:21,150 --> 00:01:24,950
until and Tartary is a tribalist and preorder Tyvaso.

21
00:01:25,710 --> 00:01:28,080
We already know how recursive function works.

22
00:01:28,080 --> 00:01:29,520
We have seen the tracing.

23
00:01:29,520 --> 00:01:31,200
We have generated the tracing tree.

24
00:01:31,440 --> 00:01:37,420
And we also know that stack is used by recursive function for each call activation.

25
00:01:37,440 --> 00:01:39,570
The cards are created in the system stack.

26
00:01:40,050 --> 00:01:45,050
So similarly, letters are traced to this preorder function upon the citrine.

27
00:01:45,240 --> 00:01:47,100
So that is generated recently.

28
00:01:47,520 --> 00:01:50,510
So for Precinct three, I will take stock also.

29
00:01:50,730 --> 00:01:56,400
So here I will create a stack that is system stack to show how the activation of the cards are created

30
00:01:56,400 --> 00:01:57,150
for each call.

31
00:01:58,120 --> 00:02:03,220
Let us start think that function using, tracing, training, first time when the function is called

32
00:02:03,220 --> 00:02:06,290
by passing through the status of the first node, that is 200.

33
00:02:06,790 --> 00:02:11,990
So the function is called the address 200, pre 200.

34
00:02:12,160 --> 00:02:16,980
In short, I'm writing Jaspreet first call Biery 200.

35
00:02:17,290 --> 00:02:20,130
So as rude as 200, 200 is passed for once.

36
00:02:20,200 --> 00:02:22,240
The function is called an activation.

37
00:02:22,240 --> 00:02:23,200
The code is created.

38
00:02:23,380 --> 00:02:28,000
Activation of the card will contain all the variables that are used by that function.

39
00:02:28,120 --> 00:02:31,670
So what are the variables using the function only to use?

40
00:02:31,840 --> 00:02:32,920
So that is Dicus.

41
00:02:32,920 --> 00:02:35,020
So I don't just add a student at.

42
00:02:36,240 --> 00:02:42,970
Activation that God will be were divided, devalues 200, then let us execute FUSA statement D not no.

43
00:02:43,310 --> 00:02:45,930
Yes, 200 is not another 200 is not null.

44
00:02:46,320 --> 00:02:47,800
Then perform three steps.

45
00:02:48,180 --> 00:02:49,080
So three steps.

46
00:02:49,470 --> 00:02:50,330
First step.

47
00:02:50,370 --> 00:02:52,200
Let's bring the data.

48
00:02:52,320 --> 00:02:54,600
Bring the date of this node eight.

49
00:02:56,540 --> 00:03:03,160
Then second step, call itself again on left child left shoulder, that is to call itself again on to

50
00:03:04,510 --> 00:03:07,890
the next step, call itself again on the right chain.

51
00:03:08,230 --> 00:03:09,470
We will do it afterwards.

52
00:03:09,470 --> 00:03:10,820
First, let us finish this one.

53
00:03:11,740 --> 00:03:14,830
So this call, what happens again?

54
00:03:14,830 --> 00:03:15,430
Inactivation.

55
00:03:15,440 --> 00:03:20,010
The card is created, but the divide us to ten before continuing.

56
00:03:20,230 --> 00:03:22,950
Let me write on the output that we got so far.

57
00:03:23,170 --> 00:03:26,280
Value A is printed, value eight is printed.

58
00:03:26,560 --> 00:03:30,370
Now for this gone to let us start the procedure once again.

59
00:03:30,640 --> 00:03:31,780
Butan is not null.

60
00:03:31,810 --> 00:03:33,910
Yes, not three steps.

61
00:03:34,030 --> 00:03:35,020
Print column.

62
00:03:35,030 --> 00:03:35,800
Left column.

63
00:03:35,800 --> 00:03:36,190
Right.

64
00:03:37,400 --> 00:03:40,190
Printed data for 210, values three.

65
00:03:41,510 --> 00:03:46,580
Then call it again upon left child, that is 270.

66
00:03:48,590 --> 00:03:50,850
Call itself to step up on drive change.

67
00:03:50,900 --> 00:03:51,910
We will do it afterwards.

68
00:03:51,920 --> 00:03:55,280
First of all, this has to finish, not for this goal.

69
00:03:55,280 --> 00:03:56,960
Activision record is created.

70
00:03:57,260 --> 00:04:00,680
This is 217 for this 270.

71
00:04:00,680 --> 00:04:02,110
And the function is called again.

72
00:04:02,390 --> 00:04:03,790
Again, this is not not.

73
00:04:03,810 --> 00:04:04,370
Yes, it is.

74
00:04:04,370 --> 00:04:07,130
270 does not perform three steps.

75
00:04:07,370 --> 00:04:08,180
So three steps.

76
00:04:08,190 --> 00:04:12,110
We know the step will now be checking every time here we know the steps.

77
00:04:12,620 --> 00:04:14,690
Call upon left side kollapen right side.

78
00:04:14,870 --> 00:04:17,410
I will be following these three steps every time.

79
00:04:17,990 --> 00:04:20,480
So now this is not so.

80
00:04:20,829 --> 00:04:25,550
The value for gold itself upon left child.

81
00:04:25,820 --> 00:04:27,360
What is left child null.

82
00:04:27,590 --> 00:04:29,540
This is not this I'm writing.

83
00:04:29,550 --> 00:04:30,770
Be here for preorder.

84
00:04:31,620 --> 00:04:35,680
Then again, Activision record is created with.

85
00:04:35,800 --> 00:04:40,020
No, not this time is not no, no, no, it is none.

86
00:04:40,350 --> 00:04:43,200
So it will not enter into this F and ends.

87
00:04:43,500 --> 00:04:46,040
So this function terminates without doing anything.

88
00:04:46,290 --> 00:04:49,740
So it's Activision records deleted ones.

89
00:04:49,740 --> 00:04:50,580
This is deleted.

90
00:04:50,730 --> 00:04:57,060
It will go back to the previous function called previous function call was 270 in that function called

91
00:04:57,450 --> 00:04:58,860
out of three steps.

92
00:04:59,130 --> 00:05:03,240
Two steps are completed printing and going on left right now.

93
00:05:03,240 --> 00:05:05,310
What is remaining go on the right side.

94
00:05:05,550 --> 00:05:06,410
So B off.

95
00:05:06,420 --> 00:05:08,910
What is the right side here for this node 270.

96
00:05:09,180 --> 00:05:10,580
It is here at 270.

97
00:05:11,040 --> 00:05:13,590
So right side is right, Rightside.

98
00:05:13,590 --> 00:05:13,950
It's null.

99
00:05:14,250 --> 00:05:16,030
So again, it will call itself that null.

100
00:05:16,230 --> 00:05:19,650
So again, the activation of the card is created at the same place.

101
00:05:20,130 --> 00:05:23,310
A new activation of the guard is created, but the equal to null.

102
00:05:23,610 --> 00:05:24,820
But this call is different.

103
00:05:24,840 --> 00:05:27,050
This was over already a new gun.

104
00:05:27,390 --> 00:05:29,850
So again, for this call is not null.

105
00:05:30,140 --> 00:05:31,420
No, no, these null.

106
00:05:31,650 --> 00:05:34,490
So do not enter inside if nothing is done.

107
00:05:34,740 --> 00:05:35,970
So this will terminate.

108
00:05:36,480 --> 00:05:41,550
Once this terminates its activation, the code is deleted and it goes back to the previous function

109
00:05:41,550 --> 00:05:42,840
call that is 270.

110
00:05:43,050 --> 00:05:45,570
Back on this one, asport 270.

111
00:05:45,810 --> 00:05:47,820
All three steps are completed.

112
00:05:48,390 --> 00:05:49,910
Three steps are completed.

113
00:05:50,310 --> 00:05:52,410
So this is also completed.

114
00:05:53,520 --> 00:05:55,080
Go back to the previous call.

115
00:05:56,370 --> 00:06:04,920
To turn this one, this one for this call to step sort of printing and calling on left side, calling

116
00:06:04,920 --> 00:06:09,980
on the left side, this one now it has to call on the right side, that is 300.

117
00:06:10,350 --> 00:06:13,170
So it will call itself up on the right side.

118
00:06:13,320 --> 00:06:15,640
That is 300 for 300.

119
00:06:15,660 --> 00:06:19,910
A new activation record is created at this place for third function gone.

120
00:06:20,280 --> 00:06:24,440
Now, this is not three steps, so I'll perform those three steps quickly.

121
00:06:24,750 --> 00:06:26,540
First one as print the value.

122
00:06:26,570 --> 00:06:27,360
So what is the value?

123
00:06:27,390 --> 00:06:29,010
Nine nine is printing.

124
00:06:29,520 --> 00:06:32,640
Then call itself again for left China.

125
00:06:32,970 --> 00:06:33,750
That is eedle.

126
00:06:33,960 --> 00:06:36,150
So activation of the card is created once again.

127
00:06:36,660 --> 00:06:37,980
But this terminates.

128
00:06:38,010 --> 00:06:38,880
So this is over.

129
00:06:38,890 --> 00:06:43,550
Back to this one second, third step call upon the right chain.

130
00:06:43,590 --> 00:06:44,410
This is also none.

131
00:06:44,640 --> 00:06:46,500
So new activation of the card is created.

132
00:06:46,560 --> 00:06:47,260
This is null.

133
00:06:47,550 --> 00:06:48,670
So this terminates.

134
00:06:48,690 --> 00:06:49,590
So this is over.

135
00:06:51,150 --> 00:06:54,820
Go back to this call this call three steps or all three steps out of work.

136
00:06:54,840 --> 00:06:55,920
So this call finishes.

137
00:06:56,070 --> 00:06:57,000
So this ends.

138
00:06:58,410 --> 00:06:59,810
Node goes back to 210.

139
00:07:00,450 --> 00:07:03,750
This one for this one, all three steps are completed.

140
00:07:04,050 --> 00:07:06,870
Printing Kollapen left child Kollapen right.

141
00:07:07,290 --> 00:07:10,130
So below this, there are more steps that are finished.

142
00:07:10,500 --> 00:07:11,440
These are also finished.

143
00:07:11,760 --> 00:07:14,280
So this function call has finished.

144
00:07:15,430 --> 00:07:18,520
So it will go back to previous call, this one mean call.

145
00:07:19,360 --> 00:07:20,110
Sort of this.

146
00:07:21,010 --> 00:07:26,560
Those steps are completed, third step is remaining, so I stress this little more.

147
00:07:27,990 --> 00:07:34,570
Here, so third step, it has to perform, that is go on right chain Biyani upon right China right.

148
00:07:34,580 --> 00:07:43,130
Cellist's 230 new activation record is created about this one with a 230 for this call.

149
00:07:43,380 --> 00:07:44,430
This will all be complete.

150
00:07:44,430 --> 00:07:46,650
Akutan so it is calling this one.

151
00:07:47,550 --> 00:07:48,510
This is not null.

152
00:07:48,660 --> 00:07:50,580
So not nonmainstream steps.

153
00:07:50,580 --> 00:07:52,620
It has to perform the value.

154
00:07:52,620 --> 00:07:53,280
Kollapen left.

155
00:07:53,570 --> 00:07:54,300
Kollapen right.

156
00:07:54,870 --> 00:07:56,010
So pretty disvalue.

157
00:07:56,040 --> 00:07:56,940
So what is the value.

158
00:07:56,970 --> 00:07:57,630
Five.

159
00:07:59,070 --> 00:08:01,530
Right, then call upon left child.

160
00:08:02,640 --> 00:08:10,530
Pre order of left, that is 310 right afterwards, foster 310, a new activation, the guard is created

161
00:08:10,650 --> 00:08:11,520
with the 310.

162
00:08:12,640 --> 00:08:17,290
For this, again, not all three steps, so pretty, three times value, what is the value inside that

163
00:08:17,530 --> 00:08:20,160
seven then call itself for none.

164
00:08:20,440 --> 00:08:25,800
So TFI and zero new activation of the card is created, but this ends.

165
00:08:25,930 --> 00:08:27,190
So this is terminated.

166
00:08:27,430 --> 00:08:28,740
So this is deleted.

167
00:08:29,020 --> 00:08:33,730
Goes back to this call, this call again, p0 is called for a child.

168
00:08:34,330 --> 00:08:35,820
Settle this Terminix.

169
00:08:35,980 --> 00:08:39,539
It ends goes back to this call, this is completed.

170
00:08:39,760 --> 00:08:40,809
So this is deleted.

171
00:08:41,169 --> 00:08:44,140
Then go back to this call 320.

172
00:08:44,140 --> 00:08:52,780
So before Rightside three twenty three during the for this one it will print the two and it will call

173
00:08:52,780 --> 00:08:54,990
itself for zero and zero.

174
00:08:55,480 --> 00:09:00,330
OK, so two more zeros are created then they are deleted.

175
00:09:00,550 --> 00:09:01,290
This is over.

176
00:09:01,300 --> 00:09:02,770
So three grand is finished.

177
00:09:03,370 --> 00:09:06,700
So 320 is finished then but it is also finished then.

178
00:09:06,700 --> 00:09:07,630
This is also finished.

179
00:09:07,720 --> 00:09:09,490
It's not like the output like a second.

180
00:09:09,490 --> 00:09:17,710
Right output first it was printed, then three was printed and then for the nine was printed.

181
00:09:19,380 --> 00:09:23,340
Then five, then seven, then to.

182
00:09:24,290 --> 00:09:28,880
So this is the output of preorder, so tracing Reid has expanded like anything.

183
00:09:30,510 --> 00:09:36,990
So once you know how the trees are generated, if you are familiar with this tree, you can even walk

184
00:09:36,990 --> 00:09:39,480
out upon this tree structure itself.

185
00:09:39,510 --> 00:09:43,890
You don't have to generate a huge tree, but you have to do it at once.

186
00:09:44,220 --> 00:09:47,640
Then you get the idea how to do it directly upon the tree.

187
00:09:49,420 --> 00:09:54,220
Now, let us analyze the trees, first of all, how many calls are there?

188
00:09:54,370 --> 00:09:55,960
Let us check the number of calls.

189
00:09:56,860 --> 00:10:01,180
One, two, three, four, five, six, seven.

190
00:10:02,200 --> 00:10:02,560
Then.

191
00:10:03,670 --> 00:10:06,520
Eight, nine, 10, 11, 12, 13, 14, 15.

192
00:10:08,380 --> 00:10:14,170
15 calls, actually, if you see these retired officers that are equal to N.

193
00:10:15,380 --> 00:10:23,060
And these nulls are for nulls, so if you remember, we already saw that if there are no words, there

194
00:10:23,060 --> 00:10:25,310
will be endless funnel pointers.

195
00:10:25,730 --> 00:10:33,350
So total number of calls are to end plus one and for a number of nodes then and plus one for a number

196
00:10:33,350 --> 00:10:34,240
of null pointers.

197
00:10:34,520 --> 00:10:37,080
So total number of calls are going plus one.

198
00:10:37,430 --> 00:10:40,520
Then once again, I will show you in which order the calls are made.

199
00:10:40,850 --> 00:10:45,740
This was the first call, second call, third and fourth call.

200
00:10:46,070 --> 00:10:46,820
Fifth call.

201
00:10:47,120 --> 00:10:48,690
Then this was the sixth call.

202
00:10:49,070 --> 00:10:52,070
Then this was seven call and eight call.

203
00:10:53,910 --> 00:11:00,870
Then go back and come the site is the ninth call tent gone 11 12.

204
00:11:01,590 --> 00:11:07,800
Go back and come the site 13, Colle 14th and 15th call.

205
00:11:08,820 --> 00:11:14,700
In this order, these calls are made, if you know in which order the calls are made, then you have

206
00:11:14,700 --> 00:11:18,570
understood the procedure, how it is working, then let us look at the stock.

207
00:11:19,050 --> 00:11:22,040
How many activation of the cards were created and destroyed?

208
00:11:22,380 --> 00:11:25,230
Depends on the number of calls put on 15 calls.

209
00:11:25,710 --> 00:11:28,400
So 15 activation records were created in the strike.

210
00:11:29,310 --> 00:11:32,040
But you see at the time how many activities in the course of day.

211
00:11:32,250 --> 00:11:34,440
One, two, three, four.

212
00:11:35,430 --> 00:11:43,200
One, two, three, three levels are there, plus one, because four notes, also calls made, right.

213
00:11:43,740 --> 00:11:47,940
So it depends on what you call dimension of a tree.

214
00:11:48,280 --> 00:11:49,810
That is height of a tree.

215
00:11:50,730 --> 00:11:52,110
No doubt it is four.

216
00:11:52,110 --> 00:11:55,850
But the heights zero one to high the only but they are four.

217
00:11:56,250 --> 00:11:58,650
Don't go on count, don't count.

218
00:11:58,980 --> 00:12:00,710
It is dependent on height.

219
00:12:00,930 --> 00:12:07,210
So I can see that the size of the stack will be height plus two, whatever the height may be.

220
00:12:07,800 --> 00:12:13,200
So if I write height plus two, then I can say that it is dependent on height value that was constant

221
00:12:13,200 --> 00:12:14,270
always to you'll be adding.

222
00:12:14,550 --> 00:12:17,920
So the actual size depends on height.

223
00:12:18,390 --> 00:12:21,840
So you're saying that depends on height is not wrong.

224
00:12:22,680 --> 00:12:24,500
So don't want to count all of this.

225
00:12:24,720 --> 00:12:27,250
So how many activations records are created.

226
00:12:27,270 --> 00:12:28,560
What is the size of the stack.

227
00:12:28,780 --> 00:12:30,430
It depends on the height of a tree.

228
00:12:30,480 --> 00:12:32,770
That's all this is the procedure to have analyzed.

229
00:12:33,120 --> 00:12:37,140
So in the next procedure and in the coming procedure, I may not be showing a stack.

230
00:12:37,140 --> 00:12:42,150
Every time you know how it works, I try to keep the same example so that you all didn't know about

231
00:12:42,150 --> 00:12:44,300
this and you already know the tree.

232
00:12:44,730 --> 00:12:50,070
So with the minimum work, I will try to show you the tracing of remaining functions upon tree.

