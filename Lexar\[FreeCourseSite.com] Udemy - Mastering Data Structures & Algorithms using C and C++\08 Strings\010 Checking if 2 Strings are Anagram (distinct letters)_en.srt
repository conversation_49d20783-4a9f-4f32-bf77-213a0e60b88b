1
00:00:00,330 --> 00:00:04,680
Another topic is checking whether two strings are anagram or not.

2
00:00:05,660 --> 00:00:08,220
So first of all, we'll see what does it mean by anagram?

3
00:00:08,660 --> 00:00:12,620
Then we will see what are the possible procedures one by one.

4
00:00:13,520 --> 00:00:19,390
See, anagram are two sort of strings which are form using same set of alphabets.

5
00:00:20,150 --> 00:00:29,600
So, for example, here I have a word that is a decimal and the same alphabets that are MRL same are

6
00:00:29,600 --> 00:00:32,600
used here to form another string that is Medicon.

7
00:00:32,750 --> 00:00:33,680
So another word.

8
00:00:33,980 --> 00:00:38,720
And it is having the same sort of alphabet a D there is the C. I.

9
00:00:38,780 --> 00:00:39,660
Everything is there.

10
00:00:40,610 --> 00:00:42,270
So these are anagrams.

11
00:00:42,350 --> 00:00:46,530
So the problem is to check whether two strings are anagram or not.

12
00:00:47,270 --> 00:00:52,760
So the first and foremost team check whether two strings are of equal size.

13
00:00:53,000 --> 00:00:55,580
If they are different sizes, they cannot be anagram.

14
00:00:57,160 --> 00:01:01,120
The first condition, so we assume that they are of equal status.

15
00:01:01,270 --> 00:01:04,269
Yes, we have taken equal status, otherwise we have to check.

16
00:01:04,690 --> 00:01:06,120
So this is based on assumption.

17
00:01:06,130 --> 00:01:06,820
They are equal.

18
00:01:06,820 --> 00:01:13,200
Insiders know how to check whether the strings are having same set of letters.

19
00:01:13,630 --> 00:01:18,530
One simple approaches take alphabet from a string and such.

20
00:01:19,330 --> 00:01:20,380
B is found.

21
00:01:20,630 --> 00:01:22,090
E Yes, it is found.

22
00:01:22,360 --> 00:01:23,890
C Yes, it is found.

23
00:01:23,980 --> 00:01:25,750
I guess it is found.

24
00:01:26,440 --> 00:01:27,580
Yes, it is found here.

25
00:01:27,940 --> 00:01:29,890
A Yes, it is found here.

26
00:01:29,920 --> 00:01:35,140
And if for every letter you can check it through this entire string.

27
00:01:35,800 --> 00:01:36,160
Right.

28
00:01:36,580 --> 00:01:38,850
If suppose any one of the letter.

29
00:01:39,100 --> 00:01:40,330
This is not an.

30
00:01:41,490 --> 00:01:46,830
If it is key, then when you search for key, it is not found, so you can see that they are not in.

31
00:01:48,030 --> 00:01:51,040
There's a simple I'll put it back, right.

32
00:01:51,420 --> 00:01:57,000
So how much time this procedure take every little search here searching in this one.

33
00:01:57,000 --> 00:01:58,250
Linear, so linear.

34
00:01:58,500 --> 00:01:59,970
And that is linear search.

35
00:02:00,000 --> 00:02:04,350
You have to perform search time for searching and then for this letter.

36
00:02:04,350 --> 00:02:05,430
Also search this letter.

37
00:02:05,440 --> 00:02:07,600
How many times and times.

38
00:02:07,620 --> 00:02:14,330
So this is an intrusion and this will be big off and square, so big and square time.

39
00:02:14,760 --> 00:02:19,860
So the procedure I have shown you, this is the simplest procedure and the stakes are off and square

40
00:02:19,860 --> 00:02:20,160
time.

41
00:02:20,610 --> 00:02:23,760
So this procedure is a time consuming if you do it like this.

42
00:02:24,720 --> 00:02:28,780
Then one more thing, we have to take care in this one, what is that?

43
00:02:29,140 --> 00:02:31,800
See, there are no duplicate alphabets in this one, right?

44
00:02:31,830 --> 00:02:35,590
I have not taken any duplicate alphabet, if there are any duplicates.

45
00:02:35,610 --> 00:02:37,680
Then you have to deal with that complexity.

46
00:02:37,950 --> 00:02:44,760
So we have already learned about the counting number of duplicates in an array and we have used the

47
00:02:44,760 --> 00:02:45,150
logic.

48
00:02:45,150 --> 00:02:46,860
The same logic can apply here.

49
00:02:48,560 --> 00:02:48,900
Right.

50
00:02:49,160 --> 00:02:53,960
So we have already seen that logic, you have to implement it now, let us look at the second, third

51
00:02:53,960 --> 00:02:54,830
and the second method.

52
00:02:54,830 --> 00:02:57,840
Also we are familiar with that is using hash table.

53
00:02:58,070 --> 00:03:00,050
Yes, this is hash table.

54
00:03:00,650 --> 00:03:04,070
I have taken an array of size 26.

55
00:03:04,070 --> 00:03:06,650
So already we know why we are taking size 26.

56
00:03:06,920 --> 00:03:11,390
And we already know in the previous video we have learned that we can how we can make use of this hash

57
00:03:11,390 --> 00:03:11,740
table.

58
00:03:12,380 --> 00:03:14,940
And one thing, all these are lower cases.

59
00:03:15,170 --> 00:03:20,930
So if you say no, I have a Perkins's also and the digits also special characters also, then you should

60
00:03:20,930 --> 00:03:23,950
have an array of size 128.

61
00:03:24,230 --> 00:03:26,860
So I have taken only 26.

62
00:03:27,590 --> 00:03:29,930
Know how we can make use of this one.

63
00:03:30,200 --> 00:03:30,830
Let us see.

64
00:03:31,070 --> 00:03:33,260
Using hash table how we can find.

65
00:03:33,590 --> 00:03:36,230
Are these two strings anagram or not.

66
00:03:36,420 --> 00:03:37,570
Let us see the procedure.

67
00:03:38,780 --> 00:03:43,810
First of all, I was right on the ASCII codes of these lowercase alphabets.

68
00:03:43,820 --> 00:03:44,200
Right.

69
00:03:44,630 --> 00:03:47,020
So this is 97.

70
00:03:47,030 --> 00:03:48,690
Does that code right.

71
00:03:49,430 --> 00:03:51,440
And the C is ninety nine.

72
00:03:51,800 --> 00:03:53,360
D is 100.

73
00:03:53,930 --> 00:03:55,650
E is one, not one.

74
00:03:56,060 --> 00:03:59,450
Then I as well not five.

75
00:04:01,960 --> 00:04:11,110
And as one, not eight, were not nine, so these are the ASCII codes of lowercase alphabets that are

76
00:04:11,110 --> 00:04:12,920
used in this string, right?

77
00:04:13,180 --> 00:04:18,089
I already know that because you can refer you can check in Google and you can find out what the last

78
00:04:18,100 --> 00:04:19,230
course of the alphabets.

79
00:04:19,630 --> 00:04:21,600
Now, let us see what we have to do next.

80
00:04:21,910 --> 00:04:24,790
Scan through this string so we know how to scan for this thing.

81
00:04:24,790 --> 00:04:30,640
Using a phone, we can go to each and every alphabet that for every alphabet like this is 100.

82
00:04:30,920 --> 00:04:35,430
So 100 minus this is 100 minus ninety seven.

83
00:04:35,440 --> 00:04:36,450
So this gives three.

84
00:04:36,760 --> 00:04:38,840
So added three increment.

85
00:04:38,860 --> 00:04:43,190
So assume that all these are filled with the Zeitels increments, then becomes one.

86
00:04:43,630 --> 00:04:49,180
I have not written Zeitels that looks congested, so I have just assumed that these are all zeroes.

87
00:04:49,180 --> 00:04:51,630
So this became one no next year.

88
00:04:52,030 --> 00:04:52,900
Well not one.

89
00:04:53,230 --> 00:04:56,000
So this gives so much for.

90
00:04:56,170 --> 00:04:57,600
So actually this was three.

91
00:04:57,850 --> 00:05:02,820
So for Go2Net for an increment it incremented nomics this, this is ninety nine.

92
00:05:03,040 --> 00:05:05,750
So this will be to go to index to one market.

93
00:05:06,190 --> 00:05:08,530
Then this is ninety seven.

94
00:05:08,530 --> 00:05:14,590
If you subtract from this one then this will be eight, go to index eight and increment and then go

95
00:05:14,590 --> 00:05:15,290
to index.

96
00:05:15,490 --> 00:05:16,750
This is will not nine.

97
00:05:16,750 --> 00:05:19,380
Will not nine minus ninety seven.

98
00:05:19,720 --> 00:05:20,720
This will be twelve.

99
00:05:20,770 --> 00:05:22,630
Go to the tool and market.

100
00:05:23,410 --> 00:05:28,570
So this is 2L and this is a ninety seven minus ninety seven gives a zero.

101
00:05:28,570 --> 00:05:31,850
So increment this one then will not eight.

102
00:05:32,020 --> 00:05:37,080
So 108 minus ninety seven is lemon goodwill index lemon and incremented.

103
00:05:37,300 --> 00:05:40,130
So see all these alphabets are unique.

104
00:05:40,150 --> 00:05:42,910
There are no duplicates so we got just one one one.

105
00:05:43,510 --> 00:05:48,010
Suppose if any character is repeating then it will increment and it will become too.

106
00:05:49,090 --> 00:05:54,610
So this procedure, we have already seen it earlier for finding duplicates from our string.

107
00:05:54,610 --> 00:05:55,700
So does the same thing.

108
00:05:56,440 --> 00:05:58,090
Now what is the next step?

109
00:05:58,270 --> 00:06:00,760
Next step scan through this string.

110
00:06:01,030 --> 00:06:01,450
Right.

111
00:06:01,450 --> 00:06:07,750
Scantron is a string and for each character and so it will be indexed well.

112
00:06:07,790 --> 00:06:14,620
So this actually ask ASCII calls for not nine minus ninety seven gifts to go to the next tool and document

113
00:06:14,620 --> 00:06:14,860
it.

114
00:06:16,260 --> 00:06:20,110
So we came to see it should not become minus one.

115
00:06:20,140 --> 00:06:24,890
So zero is there, it is becoming minus one minute of this alphabet is actually not bad.

116
00:06:25,660 --> 00:06:30,820
All right, so you can stop then and there after subtracting if it becomes minus one.

117
00:06:31,720 --> 00:06:38,830
All right, the next alphabet E is asking for this one, not one unindexed before I go there and decrement

118
00:06:38,920 --> 00:06:40,570
for one minus one zero.

119
00:06:40,960 --> 00:06:45,880
So yes, it is not coming minus one D it's indexed will be what it will be.

120
00:06:45,880 --> 00:06:47,700
Three we have already calculated here.

121
00:06:47,710 --> 00:06:48,580
So go to three.

122
00:06:49,030 --> 00:06:51,130
So index three decriminalised.

123
00:06:51,310 --> 00:06:52,440
So there's not minus one.

124
00:06:52,450 --> 00:06:53,020
It is zero.

125
00:06:53,260 --> 00:06:54,460
Right then I.

126
00:06:54,760 --> 00:06:55,450
So we are here.

127
00:06:55,460 --> 00:06:57,500
No I, I go.

128
00:06:57,530 --> 00:06:59,470
This will not five minus minus seven is eight.

129
00:06:59,470 --> 00:07:06,040
Go to negotiate an agreement and then C C that figure is ninety nine and its index is to go through

130
00:07:06,040 --> 00:07:12,940
next to the agreement and then a degree that all the agreement that Finnish.

131
00:07:14,530 --> 00:07:18,980
Now, we never got a minus one value.

132
00:07:19,870 --> 00:07:24,460
It means all the characters are as it is available here.

133
00:07:24,730 --> 00:07:26,830
So these two things are anagrams.

134
00:07:27,670 --> 00:07:30,820
If we got a minus one, we can stop them in there.

135
00:07:31,120 --> 00:07:32,290
So this is the procedure.

136
00:07:32,290 --> 00:07:38,490
By using one string, you can make an account in a hash table and second string you can go on decriminalizing

137
00:07:38,530 --> 00:07:38,660
it.

138
00:07:38,860 --> 00:07:45,100
If any number is reducing below zero, that is becoming minus one means.

139
00:07:45,210 --> 00:07:45,960
It is not fun.

140
00:07:45,970 --> 00:07:47,980
You can stop an otherwise.

141
00:07:48,100 --> 00:07:53,370
You can scan for this entirety once more and see that confirm that all the zeros right.

142
00:07:53,800 --> 00:07:57,990
If anything is not zero, you can stop and say these are not anagram.

143
00:07:58,180 --> 00:07:59,730
So let us do some analysis.

144
00:07:59,740 --> 00:08:00,970
How much time it is taking.

145
00:08:00,980 --> 00:08:02,520
We are scanning tool for string.

146
00:08:02,570 --> 00:08:03,700
So how much time it is.

147
00:08:03,940 --> 00:08:04,890
It is linear.

148
00:08:04,900 --> 00:08:07,170
So it is and and this also linear.

149
00:08:07,180 --> 00:08:11,210
So it is and and then this table we are accessing every time.

150
00:08:11,230 --> 00:08:12,010
So how much time.

151
00:08:12,010 --> 00:08:13,690
We are not accessing an entire table.

152
00:08:14,080 --> 00:08:16,050
We are accessing a particular location.

153
00:08:16,060 --> 00:08:16,360
Right.

154
00:08:16,660 --> 00:08:22,600
So accessing this table takes no time because we are not scanning through this entire table.

155
00:08:23,170 --> 00:08:23,530
Right.

156
00:08:23,920 --> 00:08:26,800
So so we don't have to include this time, if at all.

157
00:08:26,800 --> 00:08:28,940
If you include also then this will be endless.

158
00:08:28,960 --> 00:08:31,480
And listen, this is and and it is order.

159
00:08:31,660 --> 00:08:33,690
And basically we are not accessing this.

160
00:08:33,700 --> 00:08:40,600
So it is only two and so too and is also big of an order of n for the famous order of and.

161
00:08:41,970 --> 00:08:47,010
Like, so let us read on the program code for this one, so we need a hash table.

162
00:08:47,310 --> 00:08:56,790
So for that I will take an array of each size 26 and initialized with the zero I already have.

163
00:08:56,840 --> 00:08:58,020
And we have two things.

164
00:08:58,020 --> 00:09:03,740
That is the similar medical knowledge to scan for this fossil string or using for loops, control fossil

165
00:09:03,750 --> 00:09:11,630
string for our U.S. two, as long as the fire is not equal to null character and I blusterous.

166
00:09:14,120 --> 00:09:20,750
And every time what I have to do, whatever the letter is, it's ASCII code minus ninety seven at that

167
00:09:20,750 --> 00:09:22,940
index, you incremented so.

168
00:09:24,220 --> 00:09:32,360
Whatever the elements characteris minus 97, use this as an index of hash table and their incremented

169
00:09:32,360 --> 00:09:32,930
by one.

170
00:09:34,400 --> 00:09:35,420
The first string.

171
00:09:37,020 --> 00:09:44,790
Then this is over, no second string again, I have to scan through a second string for I assign zero

172
00:09:44,970 --> 00:09:55,620
I that is B of is not equal to zero I plus plus then what I have to do every time, whatever the alphabet

173
00:09:55,620 --> 00:09:57,660
is, I should decrement at the same place.

174
00:09:57,660 --> 00:10:08,880
So the same thing each of if I minus ninety seven at that place minus a fine one so one diclemente but

175
00:10:08,880 --> 00:10:11,520
after decremental check if it has been given minus one.

176
00:10:13,330 --> 00:10:24,490
If each of a of a minus 97 is less than zero, if it is less than zero, just print.

177
00:10:27,650 --> 00:10:31,820
Not anagram, not anagram.

178
00:10:33,690 --> 00:10:39,180
All right, then also break come out of this loop loop.

179
00:10:41,740 --> 00:10:44,320
But it continues, there is no space here.

180
00:10:45,550 --> 00:10:46,480
So if.

181
00:10:47,340 --> 00:10:54,030
This is less than zero than, say, not anagramming come out of the loop and if we come out of the loop,

182
00:10:54,180 --> 00:10:57,530
we may be coming out somewhere around any of this character.

183
00:10:57,560 --> 00:11:00,830
We will not be reaching zero outside this loop.

184
00:11:00,840 --> 00:11:05,910
Check if we have reached zero, B of A is equal to null.

185
00:11:05,910 --> 00:11:06,420
Correct.

186
00:11:06,900 --> 00:11:09,320
Then it means everything was perfectly checked.

187
00:11:09,450 --> 00:11:13,890
So print f its anagram print of this one.

188
00:11:16,280 --> 00:11:17,540
So this is the procedure.

189
00:11:18,010 --> 00:11:21,890
This is a simple code, very simple code to for loops and some conditions.

190
00:11:22,280 --> 00:11:23,810
So you can try it by yourself.

191
00:11:24,140 --> 00:11:25,970
You can write on the program by yourself.

192
00:11:26,330 --> 00:11:27,800
So there's a student exercise.

193
00:11:28,010 --> 00:11:29,180
Do it by yourself.

194
00:11:30,270 --> 00:11:30,660
Right.

195
00:11:31,200 --> 00:11:35,830
So that's all with this topic, but one more thing I will show you on this one.

196
00:11:36,150 --> 00:11:40,020
What if there are any duplicate alphabets?

197
00:11:40,080 --> 00:11:41,670
See, the alphabets are not repeating.

198
00:11:41,670 --> 00:11:44,870
These not repeating is not a pretty if there are duplicates.

199
00:11:44,920 --> 00:11:45,530
Then what?

200
00:11:45,750 --> 00:11:49,020
So I learn a few things and show you this one quickly.

201
00:11:49,980 --> 00:11:50,170
Yeah.

202
00:11:50,280 --> 00:11:59,250
Here I have duplicate letters in a string like this is lowballs E and observe E is repeating.

203
00:11:59,640 --> 00:12:04,260
So how to handle this one so we don't have to take our special care for this one.

204
00:12:04,770 --> 00:12:06,540
The same method will manage this.

205
00:12:06,960 --> 00:12:07,410
How.

206
00:12:07,410 --> 00:12:07,980
Let us see.

207
00:12:08,760 --> 00:12:13,160
This is when we come on this alphabet e is our score.

208
00:12:13,170 --> 00:12:20,100
This will not one so well not one minus ninety seven is for starting next fall.

209
00:12:20,100 --> 00:12:24,880
It will become one increment right the next time when we get another E.

210
00:12:25,140 --> 00:12:29,070
So again at the same index it will become to as we are implementing it.

211
00:12:30,380 --> 00:12:35,190
Right, so I'm just taking duplicates now while we are checking this string.

212
00:12:35,210 --> 00:12:36,860
It's an anagram of that one or not.

213
00:12:36,860 --> 00:12:43,040
So for all B.S., everything we check, then when we come on E, we go to the same index and decrement

214
00:12:43,040 --> 00:12:44,410
it and it will become one.

215
00:12:44,980 --> 00:12:49,860
Then you come to this E again, we'll go to the same index and it will become zero.

216
00:12:49,880 --> 00:12:51,110
So it will not be minus one.

217
00:12:51,560 --> 00:12:57,630
So even using harshing using hash table, we are able to get the duplicate false.

218
00:12:59,410 --> 00:13:05,770
Now, the last point, you can check whether two things are anagram or not, if they are not having

219
00:13:05,770 --> 00:13:12,190
duplicates, you can use bits set also the set of which already we have seen in the previous videos.

220
00:13:12,640 --> 00:13:16,720
So you can for the video, we can use those set of bits also.

221
00:13:16,720 --> 00:13:17,600
That is using bits.

222
00:13:17,620 --> 00:13:21,290
Also, we can find out if there are no duplicate letters in this.

223
00:13:21,760 --> 00:13:26,680
Otherwise we cannot store two or more than one we cannot store.

224
00:13:27,160 --> 00:13:32,080
So that's the reason hash table is useful for duplicate letters also.

225
00:13:32,410 --> 00:13:35,050
But bits are sufficient if there are no duplicates.

226
00:13:35,200 --> 00:13:36,500
So that's all in the studio.

227
00:13:36,520 --> 00:13:38,800
You have to write on these programs and practice them.

