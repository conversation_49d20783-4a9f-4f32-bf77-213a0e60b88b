1
00:00:00,550 --> 00:00:07,270
In this video, we will develop a C language program for space markets already what we have discussed

2
00:00:07,270 --> 00:00:08,610
on whiteboard, the same thing.

3
00:00:08,620 --> 00:00:11,660
We will write the program and look at the demonstration.

4
00:00:12,280 --> 00:00:16,810
So for that, I'm starting a new project and the project name I will palletize.

5
00:00:18,030 --> 00:00:19,230
Spots, matics.

6
00:00:21,000 --> 00:00:22,980
The language literacy language.

7
00:00:25,430 --> 00:00:27,200
Next creative project.

8
00:00:29,020 --> 00:00:31,600
Project is ready and does the main function.

9
00:00:32,920 --> 00:00:34,420
I'll remove these comments.

10
00:00:35,840 --> 00:00:41,120
Yeah, we are ready with the main function and main function, I'll remove the panels.

11
00:00:41,990 --> 00:00:47,120
Now, let us start, as we have discussed on white board for implementing this past matrix.

12
00:00:47,130 --> 00:00:50,570
We will define a structure for non-zero elements.

13
00:00:50,570 --> 00:00:54,050
So I will first define a structure for element.

14
00:00:54,080 --> 00:00:55,860
This is representing non-zero element.

15
00:00:56,180 --> 00:00:56,930
This is the.

16
00:00:58,280 --> 00:01:06,370
Rule number and column number, an element so ice for no GS four, column number and excess for non-zero

17
00:01:06,380 --> 00:01:06,800
element.

18
00:01:08,310 --> 00:01:13,110
Then the next structure we saw was spots and polidor spots.

19
00:01:16,640 --> 00:01:19,010
And the next structure we saw was spots.

20
00:01:20,140 --> 00:01:25,470
And this is power structure, we need the dimensions of a matrix that is m that is a number of rules,

21
00:01:26,000 --> 00:01:32,110
number of columns, and we also should know the number of non-zero elements of all that we have taken

22
00:01:32,110 --> 00:01:37,300
a variable that is num, num, then we need array of elements.

23
00:01:37,330 --> 00:01:45,670
So for that structure element and I will take a pointer that is early so that we can create an array

24
00:01:45,670 --> 00:01:46,690
of elements.

25
00:01:47,290 --> 00:01:51,550
Whatever the number of non-zero elements we have, we will create an array of that size and we will

26
00:01:51,550 --> 00:01:52,600
create a dynamically.

27
00:01:54,480 --> 00:01:59,740
Now, structure authority, then first function, I have shown you work for creating a sports market,

28
00:01:59,770 --> 00:02:05,730
so I will write a function for creating a space matrix which takes a structure of.

29
00:02:07,850 --> 00:02:08,630
Spot.

30
00:02:11,420 --> 00:02:17,050
It will take Paul by reference, so let's call it SS because it has to modify the structure.

31
00:02:18,530 --> 00:02:21,900
Now, here I will take all the inputs from the keyboard.

32
00:02:21,920 --> 00:02:24,450
That is what are the dimensions and how many non-zero elements?

33
00:02:24,470 --> 00:02:28,480
And also I will read all the non-zero elements so far that I may require some variables.

34
00:02:28,490 --> 00:02:29,790
I will declare them afterwards.

35
00:02:30,320 --> 00:02:36,050
Let us first take the input that is dimension so far that I will say printf and dimensions.

36
00:02:43,320 --> 00:02:48,960
Then again, if I should take two inputs, that is number of the number of columns.

37
00:02:50,010 --> 00:02:53,120
Unperson as adle and.

38
00:02:54,130 --> 00:02:58,930
Anderson, as I do, and so these other dimensions of rheumatics.

39
00:02:59,970 --> 00:03:02,820
Then I should also take the number of non-zero elements.

40
00:03:07,090 --> 00:03:14,430
I'm making a pretty simple number of non zero, so number of non-zero elements, that is so scanner's.

41
00:03:16,090 --> 00:03:16,590
Caps.

42
00:03:17,080 --> 00:03:22,480
That is S.A.M., so I know the number of non-zero elements also.

43
00:03:23,300 --> 00:03:29,150
Now, as we know, the number of non-zero elements, we must create an array of elements here dynamically

44
00:03:29,150 --> 00:03:31,560
from heap of required size.

45
00:03:31,850 --> 00:03:35,250
So how many number of non-zero elements I have, it is given here.

46
00:03:35,510 --> 00:03:37,430
So that should be the size of an element.

47
00:03:38,120 --> 00:03:42,280
So here, next line, I will create an array of size and No.

48
00:03:44,250 --> 00:03:46,770
So scarps Eyerly, assign.

49
00:03:48,250 --> 00:03:51,790
I should call my log function for creating an area of some size.

50
00:03:51,820 --> 00:03:56,070
So what should be the size as should be the size?

51
00:03:57,660 --> 00:04:04,260
And this size should be multiplied by the element, so it should be three of element, so I should say

52
00:04:04,770 --> 00:04:06,090
struct element.

53
00:04:07,600 --> 00:04:12,190
Then as Mellark function returns white pointer, I must drive past it.

54
00:04:13,160 --> 00:04:22,190
Sort of type element, so this will create an array of size numbers and I will be off type elements.

55
00:04:22,210 --> 00:04:26,080
It will be creating in the heap and this eyerly pointer will be pointing on that one.

56
00:04:27,860 --> 00:04:33,650
Then, as I have used my function here on the top, I should include the header file for it that is

57
00:04:33,650 --> 00:04:35,760
still in the dark edge.

58
00:04:37,850 --> 00:04:39,330
Now back to the private function.

59
00:04:39,680 --> 00:04:46,160
I have an array, then I should take all non-zero elements, so I will slip and enter all elements.

60
00:04:51,600 --> 00:04:56,580
Now, there are more than 100 elements I should treat them using for looks, so for that I will take

61
00:04:56,580 --> 00:05:03,330
a variable I starting from zero as long as I use less than number of non-zero elements.

62
00:05:03,330 --> 00:05:04,600
And I placeless.

63
00:05:06,850 --> 00:05:13,540
I will directly scan the value, so not for scanning, I should read the three values that is rule number,

64
00:05:13,540 --> 00:05:15,220
column number and eliminate.

65
00:05:18,140 --> 00:05:19,760
So I will take all those values.

66
00:05:22,700 --> 00:05:26,630
S of element of I thought I.

67
00:05:27,670 --> 00:05:28,570
The next is.

68
00:05:30,900 --> 00:05:35,070
S soft element of I thought, gee, the second.

69
00:05:36,020 --> 00:05:36,770
Dimension.

70
00:05:38,680 --> 00:05:46,120
Then it's dark element of I thought this, the element that since.

71
00:05:47,500 --> 00:05:52,720
So that's all it will be reading all non-zero elements, as I have used one variable, that is I will

72
00:05:52,720 --> 00:05:54,400
declare the variable I also have.

73
00:05:55,810 --> 00:05:56,930
Great function is ready.

74
00:05:57,120 --> 00:06:01,710
No, I will write one function for Desplaines pass mattocks, I will display it as a matrix.

75
00:06:01,720 --> 00:06:04,750
I will show zero elements as well as non-zero elements.

76
00:06:04,750 --> 00:06:09,440
But both elements I will show not just non-zero, I will display zero elements also.

77
00:06:09,880 --> 00:06:11,860
So let us have a function for display.

78
00:06:14,100 --> 00:06:19,170
Display function should take a barometer of type struct spots, matics.

79
00:06:20,430 --> 00:06:24,480
And Paul Begala is sufficient because it's not going to modify the mattocks.

80
00:06:25,910 --> 00:06:27,920
And for displaying a matrix, we require.

81
00:06:28,840 --> 00:06:34,870
Two dimensions of that I need to follow, so I and as well as for non-zero elements also overtaking

82
00:06:35,320 --> 00:06:37,350
and the scale that is initialized with zero.

83
00:06:38,250 --> 00:06:44,940
Now, using to follow ups, we know how to display a matrix or same code, I will write for loops.

84
00:06:46,110 --> 00:06:50,520
I is less than as a dot m and I plus plus.

85
00:06:52,450 --> 00:06:54,760
And for sign zero.

86
00:06:56,050 --> 00:07:01,000
And just less than as an angioplasty plus.

87
00:07:04,380 --> 00:07:11,910
This is for two follow ups for displaying a matrix now here I should display a non-zero element if I

88
00:07:11,910 --> 00:07:12,930
is equal to.

89
00:07:16,260 --> 00:07:18,360
As the element of.

90
00:07:20,080 --> 00:07:27,280
He will be tracking the elements and ETSI as well as it should also be equal, so they should be equal

91
00:07:27,280 --> 00:07:33,790
to as dot element, a kid element, A.J. dimension.

92
00:07:34,700 --> 00:07:40,340
If these two are matching that is and are matching, then I should print an element.

93
00:07:41,660 --> 00:07:42,620
With the space.

94
00:07:44,560 --> 00:07:52,510
S element of K dot x, the element I will display, and also I should move to the next element in those

95
00:07:52,510 --> 00:07:53,500
spots Mattocks.

96
00:07:55,600 --> 00:07:58,900
And if this is not matching, then I should print Zettl.

97
00:08:00,870 --> 00:08:02,100
And I'll give you a space.

98
00:08:03,560 --> 00:08:09,230
As we have already on the programs for displaying the Matarasso after the first follow up, if a line

99
00:08:09,230 --> 00:08:12,820
gap is given, then it probably looks like a Magnox on the screen.

100
00:08:13,880 --> 00:08:15,620
Let us give a new line.

101
00:08:16,700 --> 00:08:23,630
Yes, I have two functions already now inside the main function, I will create an object of type spot

102
00:08:23,660 --> 00:08:24,260
matrix.

103
00:08:25,460 --> 00:08:29,360
S and I will, first of all, set it for three eight function.

104
00:08:30,880 --> 00:08:34,840
Then I will send it to display function for displaying the markets.

105
00:08:36,799 --> 00:08:43,159
Now, let us first compile and check whether there are any errors, let us compile.

106
00:08:44,179 --> 00:08:45,580
Yeah, there are some errors.

107
00:08:45,830 --> 00:08:51,070
So you see, I'm showing you the errors also because I have just typed and maybe some mistakes.

108
00:08:51,070 --> 00:08:54,670
So you should know if there are any errors, how to remove them.

109
00:08:57,440 --> 00:09:01,340
The first stimulus expected explosion, so something we have missed here.

110
00:09:01,370 --> 00:09:06,340
Yeah, this should be size of struck element.

111
00:09:06,650 --> 00:09:07,490
Yes, perfect.

112
00:09:08,000 --> 00:09:10,520
Then here are mistype Strutt.

113
00:09:15,100 --> 00:09:16,000
The next is.

114
00:09:17,000 --> 00:09:19,290
M is not a member of Sparks.

115
00:09:19,310 --> 00:09:20,920
See, this is called my value.

116
00:09:20,930 --> 00:09:22,580
I have taken it as a value.

117
00:09:22,850 --> 00:09:25,310
So this should be not it should not be eral.

118
00:09:26,710 --> 00:09:29,830
And here also, it should be not it should not be an arrow.

119
00:09:31,060 --> 00:09:32,540
Let us come by once again.

120
00:09:33,580 --> 00:09:35,290
Yes, all errors are gone.

121
00:09:36,560 --> 00:09:38,200
Not program is ready to run.

122
00:09:40,640 --> 00:09:48,440
Enter dimensions, I will take the dimensions as five by five and I will give five non-zero elements

123
00:09:48,860 --> 00:09:51,190
and I'll give elements in first column.

124
00:09:51,350 --> 00:09:57,110
So let us give the room number, column number nine zero zero no column number zero zero.

125
00:09:57,110 --> 00:09:59,450
And the element is one then.

126
00:10:00,970 --> 00:10:06,310
Rule number is one, problem number is zero, an element is one than rule two, column zero element

127
00:10:06,310 --> 00:10:08,290
is one, row three, column zero.

128
00:10:08,380 --> 00:10:10,810
Element is one rule for column zero.

129
00:10:10,810 --> 00:10:11,680
Element this one.

130
00:10:12,940 --> 00:10:18,420
Yes, here it is displaying the entire matrix along with the zeros, it's working perfect.

131
00:10:20,370 --> 00:10:23,820
So we have finished with the creation and displaying of a mattocks.

