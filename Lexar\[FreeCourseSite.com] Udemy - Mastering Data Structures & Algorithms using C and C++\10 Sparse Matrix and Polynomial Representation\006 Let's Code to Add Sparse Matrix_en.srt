1
00:00:00,690 --> 00:00:04,140
So we have finished with the creation and displaying of a mattocks.

2
00:00:06,160 --> 00:00:11,980
Now you have to write a program for adding to Matisses that we have already discussed, so let us write

3
00:00:11,980 --> 00:00:16,180
a function for adding to Matisses, the function name must be odd.

4
00:00:16,420 --> 00:00:18,850
It must take two spots, Matisses.

5
00:00:22,770 --> 00:00:29,280
Sparse and it can take them all valuable by address, so I take them by <PERSON> as though the function

6
00:00:29,280 --> 00:00:31,620
is not going to modify those madrases.

7
00:00:34,840 --> 00:00:43,140
Spots Pointer as to who matches the city's taking, then, after adding it should return address to.

8
00:00:44,200 --> 00:00:45,430
Arithmetics.

9
00:00:49,300 --> 00:00:51,940
So it will return a pointer to spots mattocks.

10
00:00:55,750 --> 00:01:01,720
Now, inside the function, I should also have one more variable for storing the addition of mattresses

11
00:01:01,770 --> 00:01:07,950
so far that I will take a pointer type object so that it is created inside heap.

12
00:01:08,800 --> 00:01:12,190
So fustiness a smart point that I have taken.

13
00:01:13,930 --> 00:01:20,940
And first of all, let me create the object of this past mattocks, so some assign using the log function,

14
00:01:20,950 --> 00:01:22,960
I have to create an object.

15
00:01:22,990 --> 00:01:27,790
So first of all, struct Spartz pointer.

16
00:01:29,160 --> 00:01:30,540
Then mellark function.

17
00:01:32,920 --> 00:01:36,460
Size of we are creating an object of spots, mattocks.

18
00:01:40,890 --> 00:01:46,260
Yes, then after creating an object of space matrix, I should also create an array for storing the

19
00:01:46,260 --> 00:01:47,680
elements of this pointer.

20
00:01:48,240 --> 00:01:51,000
I should assign an array of elements to this one.

21
00:01:51,020 --> 00:01:56,520
So after adding puzzle, how many elements maybe did at the a number of elements will be equal to the

22
00:01:56,520 --> 00:01:59,960
number of elements in X1 and a number of elements in S2.

23
00:02:00,090 --> 00:02:03,080
So far that I should create an array of elements.

24
00:02:03,080 --> 00:02:12,330
So for some of element should be assigned with an array of type elements or struct element pointer.

25
00:02:13,450 --> 00:02:20,320
And mallock function and a number of elements, as I said, it may be accomplished at once, a number

26
00:02:20,320 --> 00:02:29,410
of elements plus as to number of elements, and they should be multiplied that the size of a structure

27
00:02:29,420 --> 00:02:29,860
element.

28
00:02:30,580 --> 00:02:31,810
So size of.

29
00:02:34,670 --> 00:02:35,290
Struck.

30
00:02:37,470 --> 00:02:38,130
Element.

31
00:02:41,040 --> 00:02:49,770
Yes, so this line will create an array of elements for storing all non-zero elements in the submission

32
00:02:49,770 --> 00:02:50,280
matrix.

33
00:02:51,590 --> 00:02:56,840
No, I should write on the loop for comparing and popping the elements of war that requires some variables

34
00:02:56,840 --> 00:03:02,440
like IJI and Key and then initialize all of them to zero.

35
00:03:02,480 --> 00:03:05,720
I assign G assign assigned Zettl.

36
00:03:07,910 --> 00:03:12,410
As we have already discussed, the procedure for copying, comparing and copying the elements or simply

37
00:03:12,410 --> 00:03:20,840
having tried on the procedure while I is less than a certain number of non-zero elements and G is less

38
00:03:20,840 --> 00:03:24,080
than a stool's number of non-zero elements.

39
00:03:27,690 --> 00:03:34,260
And while scanning through the madrassahs, we should compare their raw numbers first, that is as once.

40
00:03:37,470 --> 00:03:43,890
Element of I thought it's rule number if it is less than Astudillo, no.

41
00:03:46,730 --> 00:03:53,420
Jadot, i.e., that is rule number if this is less than in some of element.

42
00:03:54,320 --> 00:03:58,100
Off key, I should copy the element from Esslin.

43
00:04:00,980 --> 00:04:09,120
I and I should increment both I as well as Kay, so this case incremented as well as Ising demanded.

44
00:04:11,420 --> 00:04:20,180
Or else else, if S2 is a smaller so I will ride on the same board and modified these two lines.

45
00:04:24,910 --> 00:04:31,100
Not here in the first condition, the road, if anyone's road numbers are smaller here.

46
00:04:31,120 --> 00:04:37,780
I will just modify this and see if it's once a problem which is greater than in some of C++.

47
00:04:37,780 --> 00:04:41,460
I should copy the element from S2 and it should be G.

48
00:04:41,860 --> 00:04:44,020
Yes, these two modifications I have done.

49
00:04:47,970 --> 00:04:53,530
And otherwise, if mean raw numbers are the same, then I should compare numbers.

50
00:04:54,300 --> 00:04:56,820
So whoever column is the smaller, I should copy that one.

51
00:04:56,850 --> 00:05:01,320
So again, I may require the same site type of code, but with the column number.

52
00:05:01,320 --> 00:05:06,900
So I will copy that code and pasted here and I will modify it for columns.

53
00:05:08,060 --> 00:05:10,020
So here it was comparing row No.

54
00:05:10,190 --> 00:05:15,740
So this is G column no now and this is also G column number.

55
00:05:17,180 --> 00:05:20,200
So if Aslan's column number is smaller, that will be Colquitt.

56
00:05:21,080 --> 00:05:29,210
Otherwise, if a column number is greater than a stool's column number, then S2 element will be copied.

57
00:05:30,320 --> 00:05:36,010
At last, the final means both the raw numbers and the numbers are matching.

58
00:05:36,870 --> 00:05:39,180
If both of them are matching, then in some.

59
00:05:41,560 --> 00:05:49,120
Of element of, gee, I will assign the element of first matics.

60
00:05:51,070 --> 00:05:57,570
Element of I then, so when I signed this rule number and column number will be copied.

61
00:05:57,700 --> 00:06:01,300
So instead of copying them individually, I'm writing the entire element.

62
00:06:01,310 --> 00:06:04,690
So I wrote no column number as well as element will be copied.

63
00:06:04,690 --> 00:06:06,400
But Element, I will add it.

64
00:06:10,430 --> 00:06:22,850
Element of K plus plus should be assigned, that is one element of A plus plus X X value added with

65
00:06:23,240 --> 00:06:28,410
a stool's element, array of G plus plus X value.

66
00:06:30,930 --> 00:06:31,550
That's it.

67
00:06:32,520 --> 00:06:36,920
This will add more democracies, not this violence will continue.

68
00:06:38,240 --> 00:06:39,260
As long as.

69
00:06:43,760 --> 00:06:50,570
Islands' Dennis Fanatic's and go next to Matics, and it will culminate when any one of the mattocks

70
00:06:50,570 --> 00:06:51,310
has finished.

71
00:06:51,830 --> 00:06:56,970
So Rastus simply I should copy the rest of the elements from the remaining Magnox.

72
00:06:56,970 --> 00:07:00,470
So remaining matics may be either the first matrix or the second matics.

73
00:07:00,830 --> 00:07:05,500
So whoever index has not reached their end, I should copy them.

74
00:07:06,230 --> 00:07:12,830
So if I use less than as one number of elements, I should copy all the elements from First Matrix,

75
00:07:12,830 --> 00:07:14,420
so I will continue in the same line.

76
00:07:15,700 --> 00:07:26,380
Some of element of Kate Plus Plus will be assigned that as one element of I, I don't have to die because

77
00:07:26,380 --> 00:07:28,270
it's all agreement inside follow.

78
00:07:29,510 --> 00:07:31,280
And the same thing I should do it for.

79
00:07:32,660 --> 00:07:41,180
Second, Maddox also copied and pasted here, and I will modify it for second mattocks, so this is

80
00:07:41,180 --> 00:07:51,530
G less than second matrix number of elements and plus, plus, plus, plus SICAD and I should be copying

81
00:07:51,530 --> 00:07:54,800
from the second matrix and here it should be Jean.

82
00:07:56,190 --> 00:07:56,610
That's.

83
00:07:57,210 --> 00:08:00,720
So this is the code for adding to Matisses.

84
00:08:01,730 --> 00:08:07,820
And at last, for the somatics, I should mention, I mentioned so it's m should be equal to any one

85
00:08:07,820 --> 00:08:08,900
of the metrics to demonstrate.

86
00:08:08,900 --> 00:08:17,680
I can take that if s ones and some of N should be equal to anyone metrics, columns, I can take that

87
00:08:17,680 --> 00:08:22,550
the same thing that this guy is having, the number of non-zero elements.

88
00:08:22,570 --> 00:08:27,170
Hegazy tracking the number of elements that we are storing in non-zero elements.

89
00:08:27,180 --> 00:08:30,260
Uhry So KIG is the number of non-zero elements.

90
00:08:30,800 --> 00:08:37,130
So some of the number of non-zero elements that is num is equal to key is the key.

91
00:08:39,370 --> 00:08:42,470
So that's all now here inside the main function.

92
00:08:42,490 --> 00:08:51,400
I will have to madrassahs and of s I will take S1 then as to as well as I will take S3 for S3, I should

93
00:08:51,400 --> 00:08:59,860
take a pointer because this area function will add to my desires and returns the address of some somatics

94
00:08:59,860 --> 00:09:01,220
that will be created in heap.

95
00:09:01,240 --> 00:09:02,380
It is creating a heap.

96
00:09:03,400 --> 00:09:05,260
So this is written by address.

97
00:09:07,050 --> 00:09:14,610
So he didn't say the main function, I will create Humanises, first create S1, then create.

98
00:09:16,750 --> 00:09:17,350
As to.

99
00:09:19,660 --> 00:09:29,050
Then we will call ad function by passing a dress off as one and a dress off as to then the result sent

100
00:09:29,050 --> 00:09:30,330
by this are the function.

101
00:09:30,340 --> 00:09:31,690
We will take it in St..

102
00:09:33,650 --> 00:09:39,140
Then to this display function, I will send all three murders as one by one so that we can see the results

103
00:09:39,140 --> 00:09:40,520
are the ones on the screen.

104
00:09:41,180 --> 00:09:47,420
Now, before calling display here, I will give a message that we are calling showing first Maddox or

105
00:09:47,450 --> 00:09:50,540
the first Maddox.

106
00:09:53,200 --> 00:09:54,010
Slashing.

107
00:09:55,970 --> 00:09:57,620
Then this should be S1.

108
00:09:59,180 --> 00:10:05,360
Likewise, I will build the second matrix, so I will copy the score and pasted here, this is for the

109
00:10:05,360 --> 00:10:06,410
second Matrix.

110
00:10:10,680 --> 00:10:11,970
Then this should be HESTA.

111
00:10:14,270 --> 00:10:17,780
And after that, I will display some somatics.

112
00:10:23,250 --> 00:10:24,090
New line.

113
00:10:25,050 --> 00:10:30,180
Then all display function by sending Todd Maddox, now Todd Maddox, actually, it's a pointer, so

114
00:10:30,180 --> 00:10:33,410
I should send it dereference to value of that one.

115
00:10:33,510 --> 00:10:34,860
So it should be called value.

116
00:10:35,280 --> 00:10:38,880
So I should be different and send it, that's all.

117
00:10:39,760 --> 00:10:46,450
Programs really we are creating two major sources for the next two and then calling that function for

118
00:10:46,450 --> 00:10:50,890
adding them, then here and displaying all three matters first and second and third.

119
00:10:52,140 --> 00:10:53,340
Now, let us on the program.

120
00:10:54,900 --> 00:11:01,470
Program is running, Fosset is asking the dimensions of Osmakac, so I will give it a five by five the

121
00:11:01,470 --> 00:11:03,480
number of non-zero elements, I'll give us five.

122
00:11:03,870 --> 00:11:08,040
I'm asking for all the elements, so I will give the elements only in a diagonal.

123
00:11:09,270 --> 00:11:17,270
So zero zero, the element is one one command element is one to commit to is also one three, three

124
00:11:17,310 --> 00:11:19,730
is one for coming forward as one.

125
00:11:19,740 --> 00:11:21,570
So I have given all values as one.

126
00:11:23,170 --> 00:11:25,150
It's asking the dimensions of second.

127
00:11:25,570 --> 00:11:32,070
So here also I'll get the dimensions 5.5 and the number of non-zero elements, I'll give five non-zero

128
00:11:32,080 --> 00:11:32,570
elements.

129
00:11:33,160 --> 00:11:38,200
Now, here I will give the elements only in the column, the non-zero elements only in first column.

130
00:11:38,990 --> 00:11:41,800
That is zero zero as to.

131
00:11:42,930 --> 00:11:51,150
One zero is also two to zero is also to three, zero is also to four zero.

132
00:11:51,150 --> 00:11:57,000
All elements I'm giving seem now here you can see that the first smart exercise of having all the elements

133
00:11:57,000 --> 00:12:01,380
in diagonal second matrix is having all the elements in the first column here.

134
00:12:01,400 --> 00:12:06,350
The elements are in first column and the submission matrix submission of these two mattresses.

135
00:12:06,810 --> 00:12:08,220
Here I got the element three.

136
00:12:08,220 --> 00:12:09,930
That is the sum of one and two.

137
00:12:10,170 --> 00:12:15,300
And the rest of the elements are as it is taken from first hand, the second matrix because there are

138
00:12:15,300 --> 00:12:16,630
no common elements.

139
00:12:18,570 --> 00:12:23,970
Now let me run the program once again and I will give them access as a diagonal Magnus's both of them

140
00:12:23,970 --> 00:12:25,080
as diagonal Matisses.

141
00:12:28,110 --> 00:12:29,160
I'll do this quickly.

142
00:12:31,240 --> 00:12:36,740
Dimension of cosmetics is five by five, and the number of non-zero elements are five, other elements

143
00:12:36,740 --> 00:12:38,240
are zero zero one.

144
00:12:39,570 --> 00:12:47,430
One one one two two is also one three, three is also one four, four is also one.

145
00:12:48,320 --> 00:12:53,570
Not mention of second matics, five in the five and the number of elements are non-zero, elements are

146
00:12:53,570 --> 00:12:57,710
five, and I will enter all the elements in diagonal that is five.

147
00:12:58,310 --> 00:13:04,130
One common one is also five to commentary's, also five, three commentary's also five for common for

148
00:13:04,460 --> 00:13:04,910
of five.

149
00:13:06,080 --> 00:13:11,610
First, my taxes are diagonal matrix with all one and second murder suicide attacks and all fives and

150
00:13:11,670 --> 00:13:15,470
the submission matrix as there are common elements or they all are added.

151
00:13:17,830 --> 00:13:18,450
That's it.

152
00:13:20,710 --> 00:13:26,890
So that's all we have seen, the implementation of somatics, that is creation displaying and also adding

153
00:13:26,890 --> 00:13:27,810
to mitosis.

154
00:13:28,360 --> 00:13:29,650
So practice this program.

