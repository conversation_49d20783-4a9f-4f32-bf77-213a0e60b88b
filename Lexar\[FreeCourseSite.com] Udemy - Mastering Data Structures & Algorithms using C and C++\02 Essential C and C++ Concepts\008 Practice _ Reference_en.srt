1
00:00:00,420 --> 00:00:05,970
In the previous lecture, I have explained about offences, so let us look at the demonstration for

2
00:00:06,000 --> 00:00:08,580
a reference so I will go to online.

3
00:00:10,890 --> 00:00:19,020
OK, I will select C++ compiler now here I can write on C as well as C++ code like I have shown in whiteboard.

4
00:00:19,170 --> 00:00:20,730
I will declare a variable here.

5
00:00:21,710 --> 00:00:32,659
Integer a bit to value 10, so this is going to be created inside the stack now integer person are see

6
00:00:32,659 --> 00:00:36,680
the difference here is that you have to observe the syntax.

7
00:00:37,310 --> 00:00:44,210
See, when you see ampersand are at the time of declaration, when you say ampersand, it means reference.

8
00:00:44,570 --> 00:00:51,280
If you don't write anything, it's a variable which can store integer value if you register as a pointer

9
00:00:51,290 --> 00:00:53,130
variable, which can store address.

10
00:00:53,720 --> 00:00:58,070
So once again, nothing is given as a variable to store.

11
00:00:58,070 --> 00:01:03,970
Integer started given a pointer to store the address and it is given.

12
00:01:03,980 --> 00:01:05,209
So it's a reference.

13
00:01:05,870 --> 00:01:14,630
And when you are declaring a reference, you must initialize it that time only see if I run then what

14
00:01:14,630 --> 00:01:15,040
happens.

15
00:01:15,050 --> 00:01:23,330
I have not initialize it, but I see what happens, said Sinitta declaration declared as reference,

16
00:01:23,330 --> 00:01:27,140
but not initialize so artistically as reference but not initialize.

17
00:01:27,470 --> 00:01:31,040
So it must be initialized and references under another name.

18
00:01:31,290 --> 00:01:32,680
Yeah, it's another name.

19
00:01:33,020 --> 00:01:38,240
Now suppose you have a guy, your friend, best friend in your class and your classmate decided to give

20
00:01:38,240 --> 00:01:39,880
some nickname to him.

21
00:01:40,070 --> 00:01:44,060
So first of all, you will select the person, then you assign some nickname.

22
00:01:44,300 --> 00:01:46,790
So the name is ah whose name it is.

23
00:01:47,030 --> 00:01:52,040
That is the name of a so you must write a you must initialize it.

24
00:01:52,310 --> 00:01:57,160
So R Armin's a Armin's a date right now.

25
00:01:57,170 --> 00:02:02,270
Suppose the name of a person is Rajinder and you are going to call him as Saraj.

26
00:02:02,540 --> 00:02:06,560
So A's rajinder and Odd is a nickname Raje.

27
00:02:07,190 --> 00:02:07,570
Right.

28
00:02:07,820 --> 00:02:17,570
So the short name or nickname to this same E so a an R same right now later on you cannot change this

29
00:02:17,570 --> 00:02:19,340
reference to some other variable.

30
00:02:19,610 --> 00:02:26,910
Some students think that if I declare one more variable B and later on if I write are assigned B now

31
00:02:26,960 --> 00:02:34,460
be added a reference to be no is not a reference to B are will take the value of B 25 and it will store

32
00:02:34,460 --> 00:02:35,410
the value 25.

33
00:02:35,660 --> 00:02:39,310
So what is R is nothing but A so it will become 25.

34
00:02:39,530 --> 00:02:44,030
So this assignment does not initialization this not an initialization.

35
00:02:44,030 --> 00:02:45,770
So you cannot change the reference.

36
00:02:46,160 --> 00:02:48,670
The Ottomans always remember it.

37
00:02:48,830 --> 00:02:49,340
All right.

38
00:02:49,760 --> 00:02:51,950
Now I will do one thing and remove these lines.

39
00:02:52,490 --> 00:02:56,490
I will print so I can use printf as well as I can you see out.

40
00:02:56,930 --> 00:03:02,960
So I will print a Annell also R and and then let us see what happens.

41
00:03:03,200 --> 00:03:05,540
I'm printing it as well as R, right.

42
00:03:06,620 --> 00:03:14,480
So I should get values tend to times, yes, two times have gotten so aren't able to seem now here before

43
00:03:14,480 --> 00:03:16,950
printing, I will change the value of eight to 25.

44
00:03:17,360 --> 00:03:19,790
So what is, ah, I don't know, 25.

45
00:03:20,660 --> 00:03:24,560
I don't know, 25 odd is a reference symbol to 25.

46
00:03:25,010 --> 00:03:29,590
So instead of changing it to 25, I will change after 225.

47
00:03:29,930 --> 00:03:31,810
Support is not our theme.

48
00:03:32,150 --> 00:03:36,590
So what are Seamans again 25 and 25 is what is printed.

49
00:03:36,890 --> 00:03:37,980
What are 25.

50
00:03:38,240 --> 00:03:40,250
So are an eight are seen.

51
00:03:40,700 --> 00:03:44,060
So conceptually a reference doesn't consume memory.

52
00:03:44,220 --> 00:03:44,670
It is.

53
00:03:44,690 --> 00:03:50,740
It uses the same memory of a this is conceptually but how compiler implements.

54
00:03:50,750 --> 00:03:55,070
We don't know that we can see it depends on the compiler.

55
00:03:55,220 --> 00:03:58,760
But by concept reference is just another name.

56
00:03:58,970 --> 00:04:00,500
It doesn't consume any memory.

57
00:04:00,500 --> 00:04:01,840
It is not like a pointer.

58
00:04:02,270 --> 00:04:09,590
Some people believe that reference is like a pointer, but C++ is also having a constant pointer as

59
00:04:09,590 --> 00:04:10,910
having a constant pointer.

60
00:04:11,270 --> 00:04:14,180
So reference is not a pointer, reference is different.

61
00:04:14,900 --> 00:04:18,420
So if it is a pointer, then you can directly declare a constant pointer.

62
00:04:18,620 --> 00:04:21,250
So anyway, conceptually it is different.

63
00:04:21,440 --> 00:04:23,910
It means just under the name of a variable.

64
00:04:24,410 --> 00:04:25,520
Now I will show you one more time.

65
00:04:26,270 --> 00:04:32,600
I will declare variable B and I will assign this as thirty and I will assign are assigned B..

66
00:04:33,080 --> 00:04:36,050
Now this doesn't mean that it will be a reference to be.

67
00:04:36,360 --> 00:04:43,370
This means that 30 value parties are stored in our study, the stored in a Armin's itself.

68
00:04:43,400 --> 00:04:45,170
So what should bring the 33.

69
00:04:45,590 --> 00:04:48,790
Let us check it is I got 30.

70
00:04:49,250 --> 00:04:54,670
So when I change R is change because A's and the but R right.

71
00:04:55,220 --> 00:04:58,060
So that's all this is about references.

72
00:04:58,460 --> 00:05:00,860
So we will be using them in our program.

73
00:05:01,130 --> 00:05:01,420
Right.

74
00:05:01,430 --> 00:05:02,240
Wherever required.

75
00:05:02,270 --> 00:05:04,340
So you should be aware of references.

76
00:05:04,700 --> 00:05:08,890
So I suggest you to practice this once so that you get your hands on on this one.

77
00:05:09,110 --> 00:05:09,580
All right.

78
00:05:09,850 --> 00:05:13,570
So it will be easy for you to use them in the programs.

79
00:05:13,790 --> 00:05:15,160
So that's all in this video.

