1
00:00:00,360 --> 00:00:06,280
And if we do have introduction to Circular Linkous, what are the methods of representing a cyclone?

2
00:00:06,720 --> 00:00:08,160
There are two macaques.

3
00:00:08,160 --> 00:00:12,920
I'll show you both the methods for representing a circular linked list.

4
00:00:13,440 --> 00:00:14,640
So first one already.

5
00:00:14,640 --> 00:00:15,950
I have an example here.

6
00:00:16,379 --> 00:00:17,460
This is a circular likeness.

7
00:00:17,880 --> 00:00:23,120
So what is the circular link list with respect to a linear link with Se<PERSON>ia?

8
00:00:23,130 --> 00:00:28,350
Once a last node was having none so far and what we have studied was a linear.

9
00:00:28,860 --> 00:00:35,870
So this is how it became circular and which the last known little point on first node.

10
00:00:36,120 --> 00:00:42,840
Then it is circular because the solid is defined are the other definition is the nodes are circularly

11
00:00:42,840 --> 00:00:46,140
connected collection of nodes circularly connector.

12
00:00:46,170 --> 00:00:51,430
So the collection of <PERSON><PERSON><PERSON>'s, when they are circularly connected then which one is the first node.

13
00:00:51,720 --> 00:00:57,020
So there is no first so far that we usually use our term head.

14
00:00:57,180 --> 00:01:00,370
That is a pointer name had for one of the node.

15
00:01:00,690 --> 00:01:03,010
So there is no first node or last node.

16
00:01:03,210 --> 00:01:03,970
They are circular.

17
00:01:04,200 --> 00:01:07,260
So one of the more we can call it that hatamoto.

18
00:01:07,530 --> 00:01:11,300
So here head is more suitable than calling it as first.

19
00:01:11,430 --> 00:01:13,520
So I will be using pointer name as head.

20
00:01:13,890 --> 00:01:16,670
Any way you can use the first word that is your choice.

21
00:01:16,840 --> 00:01:17,960
It's not a big issue.

22
00:01:18,390 --> 00:01:20,190
What is the benefit of interest?

23
00:01:20,640 --> 00:01:23,840
We can try words these n circularly.

24
00:01:23,850 --> 00:01:25,440
Suppose you have started from head.

25
00:01:25,440 --> 00:01:28,620
You can go to the next node the next nor the next, nor from this node.

26
00:01:28,620 --> 00:01:36,750
If you go next again, you look back on Heidenau so you can traverse this legalists circularly in our

27
00:01:36,750 --> 00:01:39,000
computer system or in our mobile apps.

28
00:01:39,000 --> 00:01:44,190
At many places we find that things are list or collection of hundred circularly.

29
00:01:44,370 --> 00:01:50,100
If you take the example of contactless in your mobile phone, suppose you are scrolling up the contacts

30
00:01:50,310 --> 00:01:55,860
and you have reached the last contact that is starting with the Z, then again in some mobile phones

31
00:01:55,860 --> 00:01:56,850
or in some ads.

32
00:01:57,000 --> 00:02:00,460
If you scroll up again, the contacts with a will start again.

33
00:02:00,960 --> 00:02:07,890
So it means we are able to access the list of contacts, list of contacts, circularly we can move circularly

34
00:02:09,060 --> 00:02:10,169
doing that app.

35
00:02:10,169 --> 00:02:13,150
You can move up and down bidirectional movement.

36
00:02:13,440 --> 00:02:16,970
But here there is only forward direction movement and it is circular.

37
00:02:17,250 --> 00:02:22,320
So just we have added one extra feature to the link lists that we have been studying so far.

38
00:02:22,500 --> 00:02:30,020
We will learning about a singly link the list now this singly and circular links.

39
00:02:30,150 --> 00:02:36,270
So just we have added one feature extra to make the axis a circle that no way to use it.

40
00:02:36,420 --> 00:02:42,120
If you have only one direction axis and you also one circular axis, then you can go for it.

41
00:02:42,900 --> 00:02:48,080
Now, when we talk about circular links, we don't have to study much because what all you can do on

42
00:02:48,090 --> 00:02:53,870
a linear link, the same thing you can do here, also will not be studying much about this one.

43
00:02:53,890 --> 00:02:56,460
Few important things we will discuss now.

44
00:02:56,470 --> 00:02:58,010
Let us see more about this one.

45
00:02:58,440 --> 00:03:00,750
See, there are five nodes in this link.

46
00:03:00,790 --> 00:03:03,480
Let's suppose there is only one node in the link.

47
00:03:03,810 --> 00:03:07,860
If suppose there is only one known, then it should point on itself.

48
00:03:07,860 --> 00:03:08,820
This is a node.

49
00:03:09,750 --> 00:03:12,350
If there is only one node, then it can be circular.

50
00:03:12,360 --> 00:03:15,330
If checkpoint on itself, then it is circular.

51
00:03:15,990 --> 00:03:21,300
If there are zero nodes, no nodes at all, then this will be null.

52
00:03:22,080 --> 00:03:27,570
No one problem here if suppose I have a linear linguist's.

53
00:03:27,570 --> 00:03:29,850
So let us call the point of the mass first.

54
00:03:30,270 --> 00:03:32,450
There are two nodes in the linear link list.

55
00:03:32,880 --> 00:03:37,080
If there are no laws in the Linell Inkless, then first appointment will be null.

56
00:03:37,080 --> 00:03:42,180
So here also, if there are no nodes in a cellular linguist's, then the third point that is null.

57
00:03:42,450 --> 00:03:45,860
Now when they don't know nodes here, there are no nodes, they both are null.

58
00:03:45,870 --> 00:03:48,690
How do you prove that this is circular?

59
00:03:50,070 --> 00:03:55,500
There is no way to show that this is circular just by name, we cannot say I will not use a readable

60
00:03:55,500 --> 00:04:02,670
name, I will use a name B the recipe, the pointer for the first note be the pointer for one of the

61
00:04:02,670 --> 00:04:05,220
known unrelentless, not be the pointer from name.

62
00:04:05,220 --> 00:04:09,100
You cannot say that this is circular, that Selenia butanol.

63
00:04:09,570 --> 00:04:16,529
So there's an argument that that a circular link cannot be it can be null if it is empty also it should

64
00:04:16,529 --> 00:04:17,149
be circular.

65
00:04:17,459 --> 00:04:18,269
Yes it is empty.

66
00:04:18,269 --> 00:04:21,240
There are no knowns, but it should be circular.

67
00:04:21,630 --> 00:04:25,800
So to support this argument, there is one more representation.

68
00:04:25,800 --> 00:04:27,420
I will draw that one and show you.

69
00:04:28,050 --> 00:04:31,080
Now this is the second representation of circular letters.

70
00:04:31,500 --> 00:04:33,270
So listen carefully.

71
00:04:33,270 --> 00:04:36,310
We have to representation this one and this one.

72
00:04:36,570 --> 00:04:42,120
If your choice, whichever you want, just based on the argument that if it is empty also it should

73
00:04:42,120 --> 00:04:45,470
be circular for that we have introduced a new representation.

74
00:04:45,750 --> 00:04:51,420
There are only three nodes in a circle are linguists and the last node is pointing on first node, I

75
00:04:51,420 --> 00:04:51,930
should say.

76
00:04:52,440 --> 00:04:52,830
Right.

77
00:04:53,160 --> 00:04:56,730
Are these nodes are circularly connected now?

78
00:04:56,730 --> 00:04:57,990
This node is empty.

79
00:04:58,140 --> 00:04:59,610
What is the purpose of this node?

80
00:04:59,850 --> 00:05:01,710
This is acting as addnode.

81
00:05:02,010 --> 00:05:07,470
It is pointing on one of the node in this circular link so that we can access the circular links like

82
00:05:07,470 --> 00:05:07,740
that.

83
00:05:07,890 --> 00:05:10,640
We have a pointer here so that you can access all the notes.

84
00:05:10,920 --> 00:05:16,200
So this notice, this node is pointing on some note so that we can access all the notes.

85
00:05:16,350 --> 00:05:17,310
So this is the head node.

86
00:05:17,620 --> 00:05:21,160
There is no data in this node does not use for storing data.

87
00:05:21,570 --> 00:05:24,570
These laws are enforced, storing data for how many nodes are there?

88
00:05:24,570 --> 00:05:26,720
Not for only three nodes out there.

89
00:05:27,600 --> 00:05:28,830
So there is only one node.

90
00:05:28,830 --> 00:05:35,190
How the link lists looks like if there is only one node, then this will point on itself.

91
00:05:35,400 --> 00:05:38,850
And this is the circular.

92
00:05:39,330 --> 00:05:40,110
This is circular.

93
00:05:41,040 --> 00:05:44,700
So from Headache's, you go to this node and from there that nobody will be reaching that.

94
00:05:44,700 --> 00:05:50,280
Not only so this is how it looks like if there is only one node hard look like if there are no nodes

95
00:05:50,280 --> 00:05:56,160
at all, if there are no nodes at all, then this will be pointing on itself.

96
00:05:56,160 --> 00:05:58,650
That is happening now.

97
00:05:58,650 --> 00:06:01,190
It is empty and it is circular.

98
00:06:01,200 --> 00:06:02,790
If you start from head, you will.

99
00:06:02,790 --> 00:06:05,750
But each one had only then you can say it is circular.

100
00:06:06,450 --> 00:06:07,920
So there are two representation.

101
00:06:07,920 --> 00:06:10,710
This is the first representation and second representation.

102
00:06:11,460 --> 00:06:13,490
Now, which representation should be followed?

103
00:06:13,860 --> 00:06:16,400
That is your choice in your applications.

104
00:06:16,410 --> 00:06:22,320
You can choose anyone you want, either this one or this fund if you feel that you know when it is empty.

105
00:06:22,320 --> 00:06:27,160
Also, it should be for this function and it looks perfect whether you use it like this.

106
00:06:27,720 --> 00:06:32,880
So it's not a big change that you are introducing empty node and making circular, even if it is empty.

107
00:06:33,150 --> 00:06:34,470
That is not much important.

108
00:06:34,710 --> 00:06:41,280
So commonly used even in the books you find is the first one simple study, the first one.

109
00:06:42,090 --> 00:06:43,770
What about second representation?

110
00:06:43,770 --> 00:06:48,910
So if we follow second and also the procedures will be same as that link.

111
00:06:48,910 --> 00:06:50,700
Also look at the minor changes.

112
00:06:51,390 --> 00:06:55,100
So that's all about introduction to circular linked list.

