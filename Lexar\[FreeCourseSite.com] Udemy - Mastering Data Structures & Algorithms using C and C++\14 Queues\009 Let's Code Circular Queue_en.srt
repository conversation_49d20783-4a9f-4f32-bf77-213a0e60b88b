1
00:00:00,120 --> 00:00:06,420
And this video will look at Circular Quay using <PERSON><PERSON>, so already we have implemented actually using

2
00:00:06,430 --> 00:00:08,670
a so let us look at the structure.

3
00:00:08,670 --> 00:00:11,610
Structure remains the same, the size and the front and rear pointer.

4
00:00:11,940 --> 00:00:14,700
And the cube is an area where the elements are stored.

5
00:00:15,630 --> 00:00:17,490
Then let us look at create function.

6
00:00:17,490 --> 00:00:18,900
The sizes seem only.

7
00:00:20,900 --> 00:00:27,410
But front and rear should not be at minus one, they should be initially at zero, then creation of

8
00:00:27,410 --> 00:00:33,720
an array of size given size, then incorporation and into operation using more operation.

9
00:00:33,740 --> 00:00:37,520
I will write on the condition for two full.

10
00:00:39,040 --> 00:00:49,150
Condition is, if real, plus one more size that is more size kuze size, and they should be inside

11
00:00:49,150 --> 00:00:49,750
bracket.

12
00:00:50,770 --> 00:00:59,410
Yes, if real plus one more size is equals, two kills a front than two full.

13
00:01:00,780 --> 00:01:05,910
And if CU is not full, then they should be implemented by Tules Rare.

14
00:01:07,840 --> 00:01:12,400
Plus one, then there should be more to it cuz size.

15
00:01:14,590 --> 00:01:18,370
More choose sites that said.

16
00:01:21,900 --> 00:01:29,520
And Tuzer assign value X, then Valdek you the same one from the second cultural to is empty.

17
00:01:29,800 --> 00:01:36,360
Otherwise front should be incremented and again incrementing front is using more operation that is cusa

18
00:01:36,360 --> 00:01:40,530
front plus one mod CU's size.

19
00:01:41,070 --> 00:01:42,720
It should be more of its size.

20
00:01:44,170 --> 00:01:47,510
Does display function, I should try using my loop.

21
00:01:47,530 --> 00:01:56,620
I will actually write using do I look so this I should start from front plus one that is used front

22
00:01:56,980 --> 00:01:57,730
plus one.

23
00:01:57,730 --> 00:01:58,960
I should start from there.

24
00:01:59,410 --> 00:02:07,240
And using Bouvines every time after printing an element, I should increment using I plus one.

25
00:02:07,240 --> 00:02:08,229
More sites.

26
00:02:08,740 --> 00:02:09,669
More sites.

27
00:02:10,940 --> 00:02:12,140
And while.

28
00:02:13,430 --> 00:02:20,540
And while I is not equal to I should display the last element also, so I should say kills rare.

29
00:02:21,910 --> 00:02:22,570
Plus one.

30
00:02:23,990 --> 00:02:27,830
More sites, more Skewes sites.

31
00:02:29,120 --> 00:02:30,530
So it should go up to.

32
00:02:31,620 --> 00:02:36,180
Last element, and it should be the last element also, and then it should stop.

33
00:02:37,770 --> 00:02:38,790
Let us run this.

34
00:02:39,030 --> 00:02:43,220
Yes, it's working perfectly now let me insert a few more elements here.

35
00:02:43,230 --> 00:02:44,710
I will insert a few more elements.

36
00:02:44,820 --> 00:02:46,810
That is 40, 50 and 60.

37
00:02:46,830 --> 00:02:47,700
Let us run this.

38
00:02:48,780 --> 00:02:54,630
She got the message asking for two times, because in an area of size of SCI five, five can insert

39
00:02:54,630 --> 00:02:55,520
only four elements.

40
00:02:55,520 --> 00:03:00,360
So it's able to insert and 30, 40, only 50, 60 are not inserted.

41
00:03:00,660 --> 00:03:03,570
So the elements in the Q are 10, 20, 30 and 40.

42
00:03:04,530 --> 00:03:11,340
And when I delete the an element is deleted, so that's all over the circular queue implementation using

43
00:03:11,340 --> 00:03:11,670
a.

