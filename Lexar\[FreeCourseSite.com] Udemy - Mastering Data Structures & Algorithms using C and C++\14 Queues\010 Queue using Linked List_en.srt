1
00:00:00,490 --> 00:00:06,830
And this video will learn about implementation of <PERSON><PERSON> using <PERSON><PERSON><PERSON><PERSON>, we are already aware for linguist,

2
00:00:07,210 --> 00:00:12,360
so just we have to learn how to implement cues, the logical data structure.

3
00:00:12,380 --> 00:00:15,800
So we have to insert and delete an element in fearful fashion.

4
00:00:16,390 --> 00:00:22,360
So far, implementing it using <PERSON><PERSON><PERSON><PERSON> already have taken an example of linguist from disappoint,

5
00:00:22,360 --> 00:00:23,980
depending upon first Naude.

6
00:00:24,460 --> 00:00:27,740
And there is a point according up one last note.

7
00:00:29,020 --> 00:00:35,210
So for insertion point, it will be also elements will be inserted on the site and for deletion FrontPoint,

8
00:00:35,210 --> 00:00:37,730
that will be also the elements that were deleted from the site.

9
00:00:38,380 --> 00:00:45,160
So this an insertion and deletion usually on the link will have only one point that that is upon first

10
00:00:45,160 --> 00:00:45,450
note.

11
00:00:45,490 --> 00:00:47,920
Then why do I have one more point that I just read here?

12
00:00:48,450 --> 00:00:55,000
Reason this real pointer will help me insert a new node and time and all of Israel will be pointing

13
00:00:55,000 --> 00:00:55,870
up one last note.

14
00:00:56,170 --> 00:00:59,620
So right now in the cube, the first element is eight and the last element is four.

15
00:01:00,790 --> 00:01:04,930
We have to learn how to insert and how to delete and how to NQ and how to be.

16
00:01:05,770 --> 00:01:09,200
But before that, let us look at a few important conditions.

17
00:01:09,760 --> 00:01:13,180
First thing is, initially, when the cube is empty.

18
00:01:13,520 --> 00:01:18,330
Front and rear will be when initially queues empty, both front and rear will be none.

19
00:01:18,820 --> 00:01:22,320
So if any one of them is on the legacy front, is not miscues empty?

20
00:01:22,330 --> 00:01:23,310
There are no nodes.

21
00:01:23,500 --> 00:01:29,650
So what is the condition for empty if front is close to none, so that on the condition here for empty

22
00:01:29,650 --> 00:01:30,220
condition?

23
00:01:30,920 --> 00:01:33,480
A front as it goes from here is empty.

24
00:01:34,420 --> 00:01:40,570
Now, second thing, when I'm creating the very first more, then what all I have to do, say let us

25
00:01:40,570 --> 00:01:45,940
take a new node, the pointer T and insert some value.

26
00:01:46,270 --> 00:01:52,330
This is not the first node created, but when they know that is the first node, how we can know this

27
00:01:52,330 --> 00:01:56,890
is the first node because traditional front is not an skewes right now empty.

28
00:01:56,920 --> 00:01:58,090
So this is the very first node.

29
00:01:58,420 --> 00:02:03,610
So I should make both front as well as arrear point on this node.

30
00:02:04,900 --> 00:02:09,820
So this is the special case that I have to take it when I first noticed it, so that both front and

31
00:02:09,820 --> 00:02:11,130
center should point on this one.

32
00:02:11,590 --> 00:02:14,790
Then the third, what is the full condition?

33
00:02:15,340 --> 00:02:21,280
So as it is implemented using Lindqvist, unless you impose a some size, there is no limit here.

34
00:02:21,550 --> 00:02:23,270
You can go on adding nodes.

35
00:02:23,290 --> 00:02:28,600
So there is no fixed size, but then the heap is full and you are unable to create any node.

36
00:02:28,600 --> 00:02:31,160
Then we can say cure's full.

37
00:02:31,390 --> 00:02:33,200
So I relied on the full condition here.

38
00:02:34,000 --> 00:02:35,530
So here, create a new node.

39
00:02:35,530 --> 00:02:40,570
If a node is not created, then cure's full actually heap is full.

40
00:02:40,570 --> 00:02:40,810
So.

41
00:02:42,340 --> 00:02:50,590
Now let me write down NQ function that is insert function and here I have NQ function already.

42
00:02:50,590 --> 00:02:51,580
You have written the function.

43
00:02:51,580 --> 00:02:57,280
Let us study this function and you function which takes parameter X the value to be inserted.

44
00:02:57,730 --> 00:03:01,660
Now there is front and rear assume these are globally accessible.

45
00:03:02,770 --> 00:03:07,670
Then for insertion, what is the first condition I have to check that the queue is full.

46
00:03:07,780 --> 00:03:10,180
So how do know Cuba's full so directly?

47
00:03:10,180 --> 00:03:11,900
I'm creating a new normal here.

48
00:03:12,070 --> 00:03:14,980
Then after that, if these annulments is full.

49
00:03:15,250 --> 00:03:21,220
So the condition that we have already seen here, same thing I have written here and it's full ls if

50
00:03:21,220 --> 00:03:27,810
it is not full, if it is not full, then suppose the more are created equal.

51
00:03:27,970 --> 00:03:31,090
If an order is created with the T that.

52
00:03:32,950 --> 00:03:38,740
Here, I should set up the data and also set to the next point the rationale for this new node.

53
00:03:39,070 --> 00:03:41,390
So I will set the data and also set that point.

54
00:03:41,650 --> 00:03:42,010
No.

55
00:03:43,660 --> 00:03:48,310
Then after that, I said that there is a special case, if this is the first to check, if it is the

56
00:03:48,310 --> 00:03:50,070
first, nor how do you say it is?

57
00:03:50,110 --> 00:03:53,080
First of all, a front is not so here.

58
00:03:53,080 --> 00:03:54,580
A front is equal to null.

59
00:03:55,270 --> 00:04:02,190
If it is the very first note, then make front and rear to both point on t so both the front and rear

60
00:04:02,230 --> 00:04:05,200
should point on the small front and rear.

61
00:04:06,370 --> 00:04:11,020
So if it is the first node or this is the only node now in the queue, then both front and rear will

62
00:04:11,020 --> 00:04:12,040
point on that same.

63
00:04:12,820 --> 00:04:18,990
If all the deals are there at all in also are there, then these are the steps.

64
00:04:19,120 --> 00:04:22,480
So let us see how to insert a new node after that.

65
00:04:23,140 --> 00:04:30,700
We already know about it, but just showing it again, if a node is created like this, the right and

66
00:04:30,700 --> 00:04:32,820
the values inserted and this is said as done.

67
00:04:33,040 --> 00:04:41,340
So these two lines are over and spot rates next assigned to the slate are assigned to Soraya's next

68
00:04:41,380 --> 00:04:49,840
they should point on T then move there to be so this waypoint should be moved on to the new node that

69
00:04:49,840 --> 00:04:51,170
is inserted at the end.

70
00:04:51,340 --> 00:04:53,860
So again, the areas at the last node.

71
00:04:54,310 --> 00:05:00,010
So whenever a new nodes insert will try to move upon that, you know, so we make sure that there is

72
00:05:00,010 --> 00:05:06,310
always pointing a last node so that we can finish insertion in just two steps, these two steps.

73
00:05:07,580 --> 00:05:13,600
So that's all about income, how much time it is taking because of the help of hair pointer.

74
00:05:14,090 --> 00:05:15,830
This procedure is very simple.

75
00:05:16,040 --> 00:05:21,880
Otherwise, if you remember inserting a new note at the end of a Lincolnesque means we have to traverse

76
00:05:21,890 --> 00:05:24,830
to this link list and then we can insert a new loan.

77
00:05:25,040 --> 00:05:27,760
Unless we reach the last note, we cannot enter the new node.

78
00:05:28,070 --> 00:05:30,380
So because of that time is saved.

79
00:05:30,530 --> 00:05:35,540
All the statements in this procedure are simple, just conditional statement printf or setting that

80
00:05:35,550 --> 00:05:37,040
on a conditional statement.

81
00:05:37,370 --> 00:05:38,300
There is no loop.

82
00:05:38,300 --> 00:05:44,900
If Lupus Theremins, the time may be and may be in, but there is no loop's or statements are simple.

83
00:05:44,910 --> 00:05:45,890
So this constant.

84
00:05:47,430 --> 00:05:48,630
So this is a potential.

85
00:05:49,550 --> 00:05:56,030
Next, we will look at dequeue now let us look at dequeue operation did your operation will delete an

86
00:05:56,030 --> 00:05:59,270
element and return that element so far deleting an element.

87
00:05:59,270 --> 00:06:01,780
I have taken a variable X that is initialized to minus one.

88
00:06:01,790 --> 00:06:07,100
I have a pointer also here for temporary usage than before deletion.

89
00:06:07,100 --> 00:06:10,010
I should check whether there are any nodes in the queue or not.

90
00:06:10,010 --> 00:06:13,450
If there are no nodes, we cannot delete no nodes once it is empty.

91
00:06:13,820 --> 00:06:15,950
So what is the condition already?

92
00:06:15,950 --> 00:06:17,140
We have seen that condition.

93
00:06:17,150 --> 00:06:19,130
So here I'm checking that condition.

94
00:06:19,430 --> 00:06:23,240
If friend is equal to none then cuz empty I cannot delete it.

95
00:06:24,220 --> 00:06:30,640
Otherwise, else, I can delete it, so which notes should be deleted front for how to delete this front

96
00:06:30,640 --> 00:06:32,320
door for deleting?

97
00:06:32,320 --> 00:06:33,670
First note, we know the procedure.

98
00:06:33,670 --> 00:06:34,920
We should have a temporary point.

99
00:06:34,920 --> 00:06:36,430
The people ending on the first note.

100
00:06:36,700 --> 00:06:43,450
So please pointing one front note, then we should be in front and make it point on next Norn so friends

101
00:06:43,450 --> 00:06:45,350
should move on to the next node.

102
00:06:45,940 --> 00:06:48,180
This is step front assignments next.

103
00:06:48,730 --> 00:06:55,990
Then take out the data in variable X, this data is taken Malcolm X then three B so this node is deleted.

104
00:06:56,140 --> 00:06:58,720
Then after end of L's return X.

105
00:06:58,930 --> 00:07:01,960
So whatever the value was deleted that will be returned.

106
00:07:03,120 --> 00:07:09,510
If Sapolsky was empty, then it will return minus one to minus one is return, since there is no value

107
00:07:09,510 --> 00:07:10,890
or the queue was empty.

108
00:07:12,600 --> 00:07:14,910
So this is simple deleting for Snork.

109
00:07:16,030 --> 00:07:19,090
And delivering for snow removal that it takes Constantine.

110
00:07:19,120 --> 00:07:23,380
So, yes, this procedure is taking Constantine all the other functions.

111
00:07:23,380 --> 00:07:28,090
If you want to write, like displaying a Winklers or losing the first value, knowing the last value

112
00:07:28,450 --> 00:07:29,350
you can write down.

113
00:07:29,650 --> 00:07:33,070
So while writing the program, I may be adding more functions.

114
00:07:34,810 --> 00:07:40,000
So that's all about Cuba, so we have seen two implementations of queue that is queue using Uhry and

115
00:07:40,000 --> 00:07:42,340
the next is queue using linguist.

