1
00:00:00,240 --> 00:00:05,970
In this, we do have a look at these operations like this get Operation SEC, operation finding maximum

2
00:00:05,970 --> 00:00:09,770
of all the elements and the minimum the smallest element in the list.

3
00:00:10,290 --> 00:00:15,680
Then we will find out how to find the total sum of all the elements and also average.

4
00:00:17,160 --> 00:00:19,990
So let us look at this one by one of the first one.

5
00:00:19,990 --> 00:00:20,900
This get operation.

6
00:00:20,910 --> 00:00:22,860
This is a very simple Operation Ketamine's.

7
00:00:22,860 --> 00:00:25,850
We want to know element at a particular index.

8
00:00:26,340 --> 00:00:28,460
So I want to know what is there.

9
00:00:28,470 --> 00:00:29,440
I can explain.

10
00:00:29,700 --> 00:00:33,720
So the element is currently forget the element from that index.

11
00:00:34,350 --> 00:00:38,490
Not only one thing we have to pick at is whether the index given is valid or not.

12
00:00:39,090 --> 00:00:43,760
Like Lente <PERSON> maximum index is 14, so total 15 elements are there.

13
00:00:44,130 --> 00:00:46,860
So we should not give any index beyond that 14.

14
00:00:47,310 --> 00:00:53,160
So it should not be greater than or equal to length and even it should not be less than zero.

15
00:00:53,340 --> 00:01:00,000
So the first thing we can check is if index is greater than or equal to zero.

16
00:01:01,370 --> 00:01:08,570
And indexes less than land than the valid index.

17
00:01:08,760 --> 00:01:13,490
This index, checking them, required many places to know whether the index is valid or not so we can

18
00:01:13,490 --> 00:01:13,890
check it.

19
00:01:14,270 --> 00:01:16,600
So this is the condition for valid index.

20
00:01:16,610 --> 00:01:19,490
If this condition is true, then the index is valid.

21
00:01:19,880 --> 00:01:25,550
If it is valid, just we have to get the element that that particular index so we can just return the

22
00:01:25,550 --> 00:01:31,040
element from any given index fund.

23
00:01:31,490 --> 00:01:33,050
So I have just written a pseudocode.

24
00:01:33,050 --> 00:01:38,780
It's not a complete function or something, but I like the program that I will show a complete function.

25
00:01:39,020 --> 00:01:40,710
So what is the time taken by this fund?

26
00:01:41,090 --> 00:01:43,260
Just checking a condition and getting an element.

27
00:01:43,400 --> 00:01:46,370
So there are only two steps, so the time is constant.

28
00:01:46,760 --> 00:01:47,900
So that's all I would get.

29
00:01:48,200 --> 00:01:53,210
Now let us look at Sekhmet hard so the quarter will be similar to get slightly.

30
00:01:53,210 --> 00:01:58,030
Just change the name of this methode second one that is set.

31
00:02:00,800 --> 00:02:05,150
This method is to replace a value at a particular index.

32
00:02:05,580 --> 00:02:08,470
Suppose I want to replace a value at index six.

33
00:02:08,690 --> 00:02:15,590
So whatever the values, I want to just replace with this new value set, replacing a value or overriding

34
00:02:15,590 --> 00:02:15,960
value.

35
00:02:16,730 --> 00:02:22,430
So for setting a value like a given index, again, I should check whether the index is valid or not.

36
00:02:22,880 --> 00:02:29,180
If it is valid, then I should change that index location with the Element X.

37
00:02:31,570 --> 00:02:38,200
So that's on now get them a third world for reading the value sentimentalities, for writing the value

38
00:02:38,830 --> 00:02:41,290
depending on a given index.

39
00:02:42,370 --> 00:02:49,040
So the time taken by this is also constant now let us move to the next method that is finding maximum.

40
00:02:49,060 --> 00:02:52,300
So for that, I will change the list because this is a solid list.

41
00:02:52,300 --> 00:02:53,520
So I'll take some of the list.

42
00:02:53,860 --> 00:02:58,950
Now, let us look at the max operation that is finding the maximum of these elements.

43
00:02:58,960 --> 00:03:00,940
So I have a list of size or ten.

44
00:03:01,510 --> 00:03:02,590
So the size is 10.

45
00:03:02,590 --> 00:03:05,260
And also I have all the elements both in there.

46
00:03:05,260 --> 00:03:12,220
So the lenders also can see from this list, if you check the largest number is yeah, here you have

47
00:03:12,220 --> 00:03:13,940
15 is the largest number.

48
00:03:14,320 --> 00:03:20,320
So this means unless I check the entire list, I cannot say which is the maximum element for finding

49
00:03:20,320 --> 00:03:22,830
a maximum element in the unsorted list.

50
00:03:22,840 --> 00:03:24,390
I have to check the entire list.

51
00:03:24,790 --> 00:03:30,140
If the list is already sorted to the last element is a maximum element.

52
00:03:31,060 --> 00:03:35,050
So for unsorted list, I have to check on, so let us see how I can check it.

53
00:03:35,470 --> 00:03:42,140
So for that I will take one variable called Max and in that I will store the first element that is the

54
00:03:42,160 --> 00:03:42,540
Siedel.

55
00:03:42,940 --> 00:03:44,650
So this element, I will keep it there.

56
00:03:47,220 --> 00:03:55,950
So Max is now first element that is eight now gone, comparing this with all the rest of the elements

57
00:03:55,950 --> 00:03:59,810
and if any larger element is found and replace this one.

58
00:03:59,820 --> 00:04:00,740
So let us do it.

59
00:04:01,230 --> 00:04:02,860
Three, is it greater than this one?

60
00:04:02,970 --> 00:04:04,170
No name.

61
00:04:04,170 --> 00:04:05,230
Is it greater than this one?

62
00:04:05,250 --> 00:04:05,760
Yes.

63
00:04:05,760 --> 00:04:08,490
Modified this to nine, then 15.

64
00:04:08,490 --> 00:04:09,610
Is it greater than nine?

65
00:04:09,630 --> 00:04:09,990
Yes.

66
00:04:09,990 --> 00:04:15,080
We got still greater than this then six is a greater number and it's not greater.

67
00:04:15,090 --> 00:04:18,180
Seven, it's not greater to it's not greater.

68
00:04:18,670 --> 00:04:22,560
Twelve, it's not greater than fifty four it's not greater than fifteen.

69
00:04:22,560 --> 00:04:25,970
So the remaining one is the largest element.

70
00:04:27,060 --> 00:04:31,800
So this is a symbol and how much time it has taken for doing this.

71
00:04:31,800 --> 00:04:36,180
If you observe the work I have done, I have gone through all the elements once.

72
00:04:36,180 --> 00:04:40,110
So the timers are and not the same thing.

73
00:04:40,110 --> 00:04:43,740
I will write it as a code which will find out the maximum.

74
00:04:43,740 --> 00:04:50,010
So I have to traverse from first element onwards till the last element so I can scan through all these

75
00:04:50,010 --> 00:04:52,920
elements or traverse all these elements by using a loop.

76
00:04:53,400 --> 00:05:01,440
So far I assign from one onwards because already zero element we have taken in Max then I it's less

77
00:05:01,440 --> 00:05:11,070
than lente and I plus know what I have to do every time as I starting from here, I should check that

78
00:05:11,070 --> 00:05:15,510
whether that AOF II is greater than the maximum, that is the maximum.

79
00:05:16,590 --> 00:05:26,010
If each of eye is greater than Max, if any element is greater than that Max then copy that element

80
00:05:26,010 --> 00:05:26,580
in Max.

81
00:05:26,760 --> 00:05:33,690
So inside a max copy that element of light, that's all.

82
00:05:34,230 --> 00:05:38,430
So at the end of this loop we will be having the largest element in the max.

83
00:05:38,430 --> 00:05:42,660
If you want, you get a return, this one return Max.

84
00:05:44,370 --> 00:05:46,250
So function we will see afterwards.

85
00:05:46,360 --> 00:05:49,710
Now let us see the analysis, see inside the code.

86
00:05:49,710 --> 00:05:51,360
Also, I will write on here.

87
00:05:51,390 --> 00:05:53,100
This statement takes one unit of time.

88
00:05:53,100 --> 00:05:58,110
This statement takes one unit of time, and this conditional statement is inside the loop.

89
00:05:58,110 --> 00:05:59,640
So how many times it will execute?

90
00:05:59,910 --> 00:06:05,580
It depends on a number of times the loop will execute, loop will execute from one to last element.

91
00:06:05,670 --> 00:06:09,510
So just one element less so almost and minus one element.

92
00:06:09,510 --> 00:06:12,210
So this will be executed for and minus one times.

93
00:06:13,810 --> 00:06:19,020
Then the loop itself will execute four end times, one extra, why one extra?

94
00:06:19,330 --> 00:06:21,930
Because this condition will be checked for any time.

95
00:06:21,940 --> 00:06:24,480
So for administering the condition will be true.

96
00:06:24,490 --> 00:06:27,360
And one more time the coalition fail.

97
00:06:27,400 --> 00:06:30,160
That is, it becomes equal to let the circle stop.

98
00:06:30,490 --> 00:06:32,410
So that termination condition also.

99
00:06:32,440 --> 00:06:37,890
So this is end and minus one and this may execute all this may not go away.

100
00:06:37,930 --> 00:06:42,400
So finally, this is how much to end this.

101
00:06:42,540 --> 00:06:50,430
Plus, finally, this is how much to end to end and one one gets canceled plus one.

102
00:06:50,830 --> 00:06:58,100
So the time function for this one is Efavirenz equals to two and plus one, which is of degree one.

103
00:06:58,120 --> 00:06:59,620
So this order of N.

104
00:07:01,040 --> 00:07:07,550
That so from the cold, if you want to do analysis, you can assume every statement takes one unit of

105
00:07:07,550 --> 00:07:10,850
time and find out how many times a statement is repeating.

106
00:07:11,300 --> 00:07:14,500
Then you can come up with some formula from the formula.

107
00:07:14,510 --> 00:07:16,770
You can read on the degree and that is out of hand.

108
00:07:17,210 --> 00:07:23,360
Otherwise, if you know how you are working on this one to get down to business and working on it all

109
00:07:23,360 --> 00:07:24,220
the time.

110
00:07:24,920 --> 00:07:28,140
So basically you should tell the time complexity built on the working.

111
00:07:28,690 --> 00:07:29,550
That's more work.

112
00:07:29,580 --> 00:07:35,580
I know next operation is finding minimum, so it is just similar to maximum.

113
00:07:35,600 --> 00:07:44,350
So what I have to do is this method is minimum and first element, I should call it as minimum.

114
00:07:44,570 --> 00:07:51,650
So this aid should be as a minimum and I should go on checking it for any number that is smaller than

115
00:07:51,650 --> 00:07:54,000
that is found and replace it.

116
00:07:54,050 --> 00:07:56,120
Three is a smaller replace.

117
00:07:56,750 --> 00:07:58,220
Nine is not smaller than three.

118
00:07:58,220 --> 00:07:59,240
15, not smaller.

119
00:07:59,240 --> 00:08:02,060
Six, not smaller, 10, not smaller, seven, not smaller.

120
00:08:02,060 --> 00:08:04,970
Two is a smaller tool is not smaller.

121
00:08:04,970 --> 00:08:05,970
Food is not smaller.

122
00:08:05,990 --> 00:08:07,320
So finally got the answer.

123
00:08:08,030 --> 00:08:18,140
So instead of checking for greater than Max, then check it for less than Min and if so, modify this

124
00:08:20,270 --> 00:08:21,890
and the result is also Min.

125
00:08:23,980 --> 00:08:30,400
For the is similar, the procedure similar just in sort of Macksville finding men, so the time complexities,

126
00:08:30,400 --> 00:08:35,590
also same analysis, also saying we have to scan through all the elements that is known as the time.

127
00:08:36,820 --> 00:08:43,030
Now, let us look at finding some of all the elements for finding the total of all the elements.

128
00:08:43,030 --> 00:08:50,020
I should traverse through all elements and the go on adding them to some variable, that student.

129
00:08:50,470 --> 00:08:56,950
So, for example, I will have one variable, let us say total, and whose value is zero initially,

130
00:08:57,500 --> 00:09:06,520
then go to AOF zero and add it to this one eight and then go to one and add to this one, then go to

131
00:09:06,520 --> 00:09:09,220
air for two, then add to this.

132
00:09:09,230 --> 00:09:12,340
So every time I should add a value to that total.

133
00:09:13,120 --> 00:09:15,550
So traversing through all the elements.

134
00:09:17,360 --> 00:09:19,550
So I need a variable that is total.

135
00:09:21,570 --> 00:09:29,520
That should be initially zero, I should control the elements or four, I assign zero, I is less than

136
00:09:29,760 --> 00:09:39,870
lente then I plus plus then inside total I should every time I have I.

137
00:09:43,070 --> 00:09:48,500
That's it, then return to the Nexus analysis, how much time it takes.

138
00:09:48,890 --> 00:09:53,120
We are scanning through all the elements one by one, and we are going on adding them to total.

139
00:09:53,180 --> 00:10:00,800
So for all elements and total elements are and for the times, order and otherwise from the code.

140
00:10:00,800 --> 00:10:04,880
If you see this, a statement is simple statement.

141
00:10:05,030 --> 00:10:07,250
Constantine, this is Constantine.

142
00:10:07,640 --> 00:10:12,460
And this will repeat for how many times it is going from zero to land.

143
00:10:12,590 --> 00:10:14,830
So end times and elements are there.

144
00:10:15,260 --> 00:10:16,580
So we are using land elements.

145
00:10:16,580 --> 00:10:24,020
We have land elements equal to that us and we usually at the time complexity in terms of M then.

146
00:10:25,640 --> 00:10:32,180
I start from zero, I have to tell, and then it will stop, so up to minus one it works.

147
00:10:32,990 --> 00:10:35,800
And for Lent also it will check the condition and stop.

148
00:10:35,990 --> 00:10:37,210
So this is plus fun.

149
00:10:37,550 --> 00:10:41,200
So was actually inside the faludi's will execute only for one time.

150
00:10:41,450 --> 00:10:47,090
This will be good for any time and this will execute for and one time so that and plus one right here.

151
00:10:47,690 --> 00:10:49,870
So it means we are ignoring these two guests.

152
00:10:49,880 --> 00:10:51,710
We are concerned about this condition.

153
00:10:51,710 --> 00:10:55,550
So we have it in that condition then total how much to end Blackstreet.

154
00:10:55,570 --> 00:10:57,230
So two and three.

155
00:10:57,560 --> 00:10:59,240
So the time function is fourth.

156
00:10:59,250 --> 00:11:05,840
Fun is equal to two and plus three degree of this is one third order of an hour, one that is.

157
00:11:05,840 --> 00:11:09,410
And so there's a simple task for adding not the same tasks.

158
00:11:09,410 --> 00:11:12,260
We can also write it recursively.

159
00:11:13,160 --> 00:11:18,910
Let us look at a recursive definition for finding the sum of elements in an array.

160
00:11:19,730 --> 00:11:29,270
Suppose the function name is a sum and it takes Uhry and number of elements and this edness Lenthall

161
00:11:29,270 --> 00:11:29,900
minus one.

162
00:11:30,020 --> 00:11:32,100
So here the array starts from zero onwards.

163
00:11:32,100 --> 00:11:34,430
So the last index is nine.

164
00:11:34,580 --> 00:11:39,380
If the array starts from London, losses potentially the number of elements, not just the loss index

165
00:11:39,710 --> 00:11:41,520
does not and does the index.

166
00:11:41,520 --> 00:11:43,640
So let us say this is a number of elements.

167
00:11:44,180 --> 00:11:47,450
Then what is the sum of all elements in an array?

168
00:11:48,080 --> 00:11:57,230
Sum of all the elements in an array can be recursively defined as someone else, and the minus one elements

169
00:11:58,520 --> 00:12:00,980
for foreign elements are often minus one elements.

170
00:12:02,210 --> 00:12:04,370
Plus, entertainment.

171
00:12:06,450 --> 00:12:08,280
That's how we can define it recursively.

172
00:12:09,730 --> 00:12:19,090
For any greater than or equal to zero then, and is less than zero, then it is zero, much less than

173
00:12:19,090 --> 00:12:22,540
the Romans minus one minus if anything you give them, that is zero.

174
00:12:22,960 --> 00:12:28,630
So does the recursive definition for this recursive definition I will write on are function.

175
00:12:31,650 --> 00:12:35,100
And some a.

176
00:12:36,480 --> 00:12:41,040
And and this is a pseudocode I'm not writing or C or C++ function.

177
00:12:44,040 --> 00:12:54,480
Now, if and as less than zero rate on zero, this one than this one else.

178
00:12:55,890 --> 00:13:07,280
Written somewhere else, a comma and minus one, plus eight of and that's all.

179
00:13:08,940 --> 00:13:18,560
This is some function must be called by passing some Uhry and Leonard minus one, that is the last line

180
00:13:19,350 --> 00:13:20,760
they should be called like this.

181
00:13:22,430 --> 00:13:27,590
Gone should be like this, so if you want to know the time taken by this fund for this will be calling

182
00:13:27,590 --> 00:13:33,590
itself how many times, it depends on a number of elements so famous outdraw, and it is calling itself

183
00:13:33,590 --> 00:13:38,480
only one time for all elements sort of end and recursion use of the stack.

184
00:13:38,490 --> 00:13:41,720
So the size of the stack will be a number of elements.

185
00:13:41,720 --> 00:13:46,330
So recursive calls will be piling up one up or another, depending on a number of elements.

186
00:13:46,670 --> 00:13:50,260
They start from last index towards the first index zero.

187
00:13:51,080 --> 00:13:52,490
So that's all about some.

188
00:13:53,400 --> 00:13:58,500
Now, let us look at average, average is nothing, but some of all the elements are divided by a number

189
00:13:58,500 --> 00:13:59,090
of elements.

190
00:13:59,130 --> 00:14:02,650
Already we have seen method for some, for finding some.

191
00:14:03,030 --> 00:14:05,440
Now let us just change it to average.

192
00:14:06,300 --> 00:14:08,640
So the next operation is average.

193
00:14:08,910 --> 00:14:11,230
So for average, we have to find the total.

194
00:14:11,580 --> 00:14:15,900
So the sum total will be getting the total number of elements then simply written.

195
00:14:17,840 --> 00:14:24,710
Total divided by number of elements that all this will give the average, once you have a sum, then

196
00:14:24,710 --> 00:14:28,790
the final oppressiveness total divided by number of elements.

197
00:14:29,060 --> 00:14:34,190
So these are the simple operations on an early night in the coming media as we look at more operations

198
00:14:34,190 --> 00:14:34,400
on.

