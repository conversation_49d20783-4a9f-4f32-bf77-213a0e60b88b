1
00:00:00,700 --> 00:00:06,860
In this video, we will see the procedure for removing duplicate elements from our psychedelic list.

2
00:00:07,670 --> 00:00:12,110
Here's an example of Suddenlink list elements are three five five eight eight eight.

3
00:00:12,280 --> 00:00:17,620
So there are duplicate elements if you take only distinct elements that are three, five and eight.

4
00:00:18,140 --> 00:00:19,930
So we want to remove duplicates.

5
00:00:20,350 --> 00:00:22,600
What should be the procedure for removing duplicates?

6
00:00:23,110 --> 00:00:26,250
So let me show you the procedure for removing duplicates.

7
00:00:26,260 --> 00:00:27,420
We need to pointers.

8
00:00:27,460 --> 00:00:31,000
Let us take one point up on first note and also on the next note.

9
00:00:31,180 --> 00:00:32,560
We should have one point or two.

10
00:00:33,340 --> 00:00:37,900
The idea is we have to check whether the current node and the next day did same.

11
00:00:37,900 --> 00:00:40,150
If they die, same, we can delete any one of the notes.

12
00:00:40,150 --> 00:00:41,750
So let us delete the second order.

13
00:00:42,730 --> 00:00:48,070
So for that, we need to point out so I have first point up one for Snoad and the pointer queue up on

14
00:00:48,070 --> 00:00:48,770
next node.

15
00:00:49,720 --> 00:00:51,030
Now let us see the procedure.

16
00:00:51,490 --> 00:00:57,130
The procedure is checked whether the data in the peace node and the key node, is it matching?

17
00:00:57,130 --> 00:00:58,120
It's not matching.

18
00:00:58,480 --> 00:01:01,030
If the data is not matching, that is cute.

19
00:01:01,090 --> 00:01:03,820
Three and five they are not matching, then move.

20
00:01:03,820 --> 00:01:08,890
Be up on cue and the move to the next node.

21
00:01:10,250 --> 00:01:17,180
If they're not matching them, slide them now, let us see next piece the time cues that it's matching.

22
00:01:17,570 --> 00:01:21,310
If the data is matching, then what we should do, we have to delete the norm.

23
00:01:21,320 --> 00:01:24,620
So which one we the second note actually we can delete anymore.

24
00:01:24,680 --> 00:01:26,470
But here we are deleting psychonaut.

25
00:01:26,780 --> 00:01:29,780
So how to delete the small make B's.

26
00:01:29,780 --> 00:01:32,180
Next point on Qs next.

27
00:01:32,940 --> 00:01:36,560
The first step then delete this.

28
00:01:36,560 --> 00:01:43,550
No that Q delete the note then make a Q point on this Snork.

29
00:01:45,340 --> 00:01:46,720
That is BP's next.

30
00:01:46,900 --> 00:01:51,450
So these are the three steps make this point on next, more of Q delete.

31
00:01:51,490 --> 00:01:54,260
Q Then move to peace next.

32
00:01:54,280 --> 00:02:00,880
So, Bill, here, let me write down some procedure, the code, then I will continue the rest of the

33
00:02:00,880 --> 00:02:02,010
North Sea.

34
00:02:02,470 --> 00:02:03,640
We need appointer.

35
00:02:08,530 --> 00:02:17,900
Be it one for Snork, then we need a point that on next north of First North Cue is pointing on second

36
00:02:17,900 --> 00:02:18,270
node.

37
00:02:18,370 --> 00:02:19,960
That is next off first.

38
00:02:20,410 --> 00:02:22,390
We have already seen this initial.

39
00:02:22,840 --> 00:02:27,810
Then what we have to do, compare the data for PACU if they are equal or not equal.

40
00:02:28,300 --> 00:02:31,830
So if they are not equal, then we will slide the pointer.

41
00:02:31,990 --> 00:02:34,780
So let us right on that call if.

42
00:02:36,370 --> 00:02:46,200
Bikash data is per capita is not equal to Kuka better than the movie on Q And the move due to next Norvig.

43
00:02:46,960 --> 00:02:48,690
So they will be sliding like this.

44
00:02:50,160 --> 00:02:50,820
There's over.

45
00:02:52,500 --> 00:02:59,370
So if the data is equal, data is equal, then what are the steps we have done in Elsipogtog right on

46
00:02:59,880 --> 00:03:03,360
Besnik should be Kyuss next year, remember who was here, right?

47
00:03:03,750 --> 00:03:05,760
So BP's next here.

48
00:03:05,760 --> 00:03:06,960
It should be Kyuss next.

49
00:03:07,560 --> 00:03:07,880
So.

50
00:03:08,530 --> 00:03:10,230
So peaceniks excuse next.

51
00:03:12,100 --> 00:03:14,410
Then believe this, an automatic use pointing.

52
00:03:16,640 --> 00:03:26,420
Delete this note will be deleted, then move you to make snort of B, so clearly pointing on next Nordoff

53
00:03:26,420 --> 00:03:28,250
of P, Q will be pointing here.

54
00:03:29,150 --> 00:03:32,950
So I have written the program called Up to what all we have learned.

55
00:03:33,440 --> 00:03:39,200
Now let us continue and this time we will be tracing the code will be tracing the code, not be is and

56
00:03:39,200 --> 00:03:39,910
Q is here.

57
00:03:39,930 --> 00:03:41,690
Check the data five and eight.

58
00:03:41,840 --> 00:03:45,350
They are not equal so P should point on.

59
00:03:45,350 --> 00:03:45,680
Q

60
00:03:48,380 --> 00:03:50,240
and Q should point on next note.

61
00:03:53,850 --> 00:03:56,490
They have slighted if pot was executed.

62
00:03:57,460 --> 00:04:05,260
Once again, once again, repeat, is the data matching eight eight, they are not equal, none of them

63
00:04:05,260 --> 00:04:05,710
are equal.

64
00:04:05,710 --> 00:04:07,880
It's matching enter into a spot.

65
00:04:08,080 --> 00:04:11,960
So what we should do in part, these next should be Qs next.

66
00:04:12,220 --> 00:04:14,920
These next should be Qs next.

67
00:04:15,220 --> 00:04:15,880
So this one.

68
00:04:16,940 --> 00:04:20,000
Then delete load Q not Q is deleting.

69
00:04:21,170 --> 00:04:25,610
Then make a quick point on peaceniks, you should point on these next.

70
00:04:27,040 --> 00:04:29,680
So when they were marching, we did this one continue.

71
00:04:30,770 --> 00:04:34,190
They are not equal eight not equal.

72
00:04:34,220 --> 00:04:36,670
No, no, it is equal enter into a sport.

73
00:04:37,010 --> 00:04:43,070
OK, these next two should be kibbutzniks peaceniks should be Kucinich next.

74
00:04:43,100 --> 00:04:43,850
What is Kucinich?

75
00:04:43,910 --> 00:04:44,200
None.

76
00:04:44,450 --> 00:04:45,380
So make it null.

77
00:04:46,790 --> 00:04:48,890
Make it now then delete.

78
00:04:49,460 --> 00:04:50,740
This note is deleted.

79
00:04:50,990 --> 00:04:59,420
Next accused goes to PS next so should be Campese next what is next null so cu became so.

80
00:04:59,420 --> 00:05:04,150
Q has reached the end of the Lechler so there are no known for further.

81
00:05:04,160 --> 00:05:05,090
So we should stop.

82
00:05:05,790 --> 00:05:06,520
We should stop.

83
00:05:06,770 --> 00:05:11,870
So this repeatedly we have done and we have stopped when Bennetto became.

84
00:05:12,500 --> 00:05:18,290
So this should be repeated in a loop while cure's not equal to none.

85
00:05:18,290 --> 00:05:21,420
So what condition is kill is not equal to none.

86
00:05:21,530 --> 00:05:29,310
So this is the procedure for removing duplicates from our littlest little bit of analysis.

87
00:05:29,360 --> 00:05:34,940
How many point does required extra point US B and Q can we do it with less number of points?

88
00:05:34,940 --> 00:05:36,110
That is just one pointer.

89
00:05:36,590 --> 00:05:37,090
Check it.

90
00:05:37,280 --> 00:05:42,650
Whether it is possible or not, you find out then what is the time taken by this one?

91
00:05:42,920 --> 00:05:50,930
It is scanning through intelligence that is traversing a limitless order and it must to traverse the

92
00:05:50,930 --> 00:05:51,470
Internet.

93
00:05:51,630 --> 00:05:53,360
So it's always out of hand.

94
00:05:53,660 --> 00:05:55,090
So there is no minimum maximum.

95
00:05:56,090 --> 00:05:58,370
So times are tough and.

