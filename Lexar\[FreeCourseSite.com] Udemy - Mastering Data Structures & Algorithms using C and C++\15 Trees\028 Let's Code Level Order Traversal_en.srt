1
00:00:00,430 --> 00:00:04,790
And this video will look at the level of the traversal of a binary tree.

2
00:00:04,990 --> 00:00:10,040
So already we have seen a program for creating a banyan tree and performing various traversal.

3
00:00:10,570 --> 00:00:15,010
So in the same project, I'm just adding a function for performing level order traversal.

4
00:00:15,490 --> 00:00:17,890
So let us write on a function for level order.

5
00:00:21,360 --> 00:00:31,460
Which will take a load of type route, and that is not freenode then, so inside this, we need a tube.

6
00:00:31,470 --> 00:00:35,250
So I will take a Tulita structure to that.

7
00:00:35,250 --> 00:00:37,240
I should create a queue also.

8
00:00:37,620 --> 00:00:42,330
So let us call it as create ampersand queue and the size.

9
00:00:42,330 --> 00:00:45,390
I will take it as a bigger size so that it's sufficient for me.

10
00:00:46,300 --> 00:00:49,900
Then as for the procedure we have seen, first of all, we will print.

11
00:00:50,860 --> 00:00:56,560
The data from the route, so this is data is data we will spend.

12
00:00:58,280 --> 00:01:00,890
And after printing data, we will NQ.

13
00:01:01,970 --> 00:01:07,370
The route root, so in queue, I will send you the route, so please, I pointed to the route.

14
00:01:08,670 --> 00:01:15,380
The one thing will change its name from Pitou route as it's a local variable is the exact actual route.

15
00:01:16,070 --> 00:01:20,180
So you can read it properly and stop calling it as route.

16
00:01:20,330 --> 00:01:22,030
So route is inserted into the queue.

17
00:01:23,450 --> 00:01:27,140
Now, while not is empty queue.

18
00:01:29,410 --> 00:01:31,270
As long as Cuba is not empty.

19
00:01:32,570 --> 00:01:34,030
Print left and right.

20
00:01:34,360 --> 00:01:37,040
And if there is a note, we will also enter into the queue.

21
00:01:37,420 --> 00:01:38,260
So first of all.

22
00:01:39,890 --> 00:01:40,520
Barut.

23
00:01:42,400 --> 00:01:44,290
Dequeue Evalu from the Q.

24
00:01:45,810 --> 00:01:50,250
Could and if Rulz left Charla's there.

25
00:01:52,210 --> 00:02:01,390
Then bring that live child born, deaf person, child, and some space roots left child data.

26
00:02:02,310 --> 00:02:03,180
A sprinter.

27
00:02:04,620 --> 00:02:06,750
And left Charla's NQ.

28
00:02:10,090 --> 00:02:13,270
Inserted in the queue, that is roots left child.

29
00:02:15,200 --> 00:02:16,910
NetSol, then.

30
00:02:18,410 --> 00:02:23,420
Again, we should check with Rachel is there if Rachel is there, and that it should also be treated

31
00:02:23,420 --> 00:02:24,230
in the same way.

32
00:02:24,560 --> 00:02:28,030
So I copied and pasted and this is right child.

33
00:02:29,110 --> 00:02:32,410
So Rachel instead, and Rachel, it is printed on.

34
00:02:33,650 --> 00:02:34,610
It is NQ.

35
00:02:35,820 --> 00:02:40,150
That's all so after this loop, so there is nothing we have to do.

36
00:02:40,650 --> 00:02:47,100
So this is the level order, so that follows the level order function and no inside the main function

37
00:02:47,100 --> 00:02:49,420
already created is already there.

38
00:02:49,740 --> 00:02:51,930
So now I will level order.

39
00:02:51,930 --> 00:02:54,210
Function, level order.

40
00:02:57,560 --> 00:03:01,550
Bus route as a pointer, let us run this.

41
00:03:05,100 --> 00:03:12,090
So I will in the north, that is route north and it's left child is 20, 30 years left childless 40

42
00:03:12,090 --> 00:03:15,020
and Rachel 50, then 60 and 70.

43
00:03:15,030 --> 00:03:20,720
So I should get all the elements and the level 10, 20, 30, 40, 50, 60 and 70.

44
00:03:21,180 --> 00:03:22,610
Now there are no more nodes.

45
00:03:22,620 --> 00:03:23,850
So all minus one.

46
00:03:26,520 --> 00:03:28,730
Yes, I got the elements in the same order.

47
00:03:29,240 --> 00:03:30,290
There's the level of the.

48
00:03:32,900 --> 00:03:38,960
That's all so I have just added one extra function to a free program or project.

