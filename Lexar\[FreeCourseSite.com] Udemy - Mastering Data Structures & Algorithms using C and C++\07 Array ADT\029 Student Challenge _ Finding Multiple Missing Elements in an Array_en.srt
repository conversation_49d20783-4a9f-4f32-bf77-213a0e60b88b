1
00:00:00,510 --> 00:00:05,520
No, I have more than one missing elements in an update, let us look at the elements first.

2
00:00:06,900 --> 00:00:14,650
Six, seven, eight, nine, 10 is missing, 11, 12, 13, 14 is missing.

3
00:00:14,670 --> 00:00:17,520
Then I have 15, 16, 17, 18 and 19.

4
00:00:18,240 --> 00:00:24,570
So more than one elements are missing and also from 12 to 15, if you see continuously two elements

5
00:00:24,570 --> 00:00:25,080
are missing.

6
00:00:25,680 --> 00:00:27,690
So I have taken both the cases.

7
00:00:27,690 --> 00:00:33,370
That is one or more elements are missing and also continuously more than one elements are missing together.

8
00:00:33,750 --> 00:00:36,080
So let us see how we can find out this one.

9
00:00:36,630 --> 00:00:42,740
So we will follow the same logic that I have shown here by taking the defense of index and the element.

10
00:00:43,140 --> 00:00:46,730
So let us start how we have to modify the logic here.

11
00:00:47,280 --> 00:00:49,320
See, let us start from here.

12
00:00:49,350 --> 00:00:51,390
The difference is six, right.

13
00:00:51,570 --> 00:00:53,250
Six minus little differences, six.

14
00:00:53,520 --> 00:00:54,510
So different six.

15
00:00:54,720 --> 00:00:56,070
And here difference is six.

16
00:00:56,070 --> 00:00:57,960
So element is not missing difference.

17
00:00:57,960 --> 00:00:59,670
The six element is not missing.

18
00:00:59,970 --> 00:01:03,560
Difference of six elements is not missing differences.

19
00:01:03,570 --> 00:01:04,260
Seven.

20
00:01:04,560 --> 00:01:05,940
So Element is missing.

21
00:01:05,940 --> 00:01:08,310
What is that element difference.

22
00:01:09,300 --> 00:01:11,860
Difference of six and the index is four.

23
00:01:11,880 --> 00:01:17,100
So the missing element is 10, but the difference and new difference is seven.

24
00:01:17,880 --> 00:01:18,440
Yes.

25
00:01:18,840 --> 00:01:22,840
So I can know the missing element by taking the index as well as the difference.

26
00:01:22,840 --> 00:01:24,090
So I'll get the number 10.

27
00:01:24,090 --> 00:01:25,050
So 10 is missing.

28
00:01:25,320 --> 00:01:29,360
And now from here, from a single event onwards, the difference has changed.

29
00:01:29,700 --> 00:01:31,880
That is 11 minus four to seven.

30
00:01:32,280 --> 00:01:33,400
So this is seven.

31
00:01:33,450 --> 00:01:34,380
OK, continue.

32
00:01:34,390 --> 00:01:35,810
Next difference is seven.

33
00:01:35,850 --> 00:01:36,780
OK, continue.

34
00:01:37,080 --> 00:01:37,740
No problem.

35
00:01:38,040 --> 00:01:39,210
Not differences.

36
00:01:39,330 --> 00:01:43,010
Nine oh four two elements are missing.

37
00:01:43,200 --> 00:01:46,880
So previous difference for seven and a new difference and nine.

38
00:01:47,460 --> 00:01:49,120
So how many elements are missing?

39
00:01:49,590 --> 00:01:52,530
So right now we are at index six.

40
00:01:52,530 --> 00:02:01,530
So different four, seven, seven plus six and 13, 14 is missing and also eight plus the six I should

41
00:02:01,530 --> 00:02:01,920
take.

42
00:02:02,100 --> 00:02:06,150
That is fourteen then nine.

43
00:02:06,450 --> 00:02:10,020
Oh, I should stop because the difference is nine.

44
00:02:10,020 --> 00:02:10,710
I should stop.

45
00:02:11,160 --> 00:02:16,260
So I got the two elements missing, so I should start from seven different seven and also different

46
00:02:16,370 --> 00:02:20,400
eight different nine stop because the difference is nine and we have that element.

47
00:02:21,390 --> 00:02:25,230
So for the Slint multiple elements I have to do multiple elements.

48
00:02:25,230 --> 00:02:27,510
So again it means I should use the loop here.

49
00:02:27,660 --> 00:02:28,110
Yes.

50
00:02:28,620 --> 00:02:30,750
Then next let us continue further.

51
00:02:30,750 --> 00:02:35,490
The difference is nine and the difference is nine and the difference is nine and the difference is nine

52
00:02:35,490 --> 00:02:37,470
so that therefore everything is perfect.

53
00:02:38,610 --> 00:02:41,220
So we have found three missing elements.

54
00:02:41,250 --> 00:02:45,780
That is 10 and 13 and 14, so logic will be similar.

55
00:02:45,780 --> 00:02:46,830
I should make a difference.

56
00:02:46,830 --> 00:02:50,730
And scandal, wherever the difference is not matching, the element is missing.

57
00:02:51,060 --> 00:02:53,110
So let me write on the code here.

58
00:02:53,460 --> 00:02:56,720
So this is the work I have done, so I'll put it in a box.

59
00:02:57,030 --> 00:02:58,640
I will work on the code for actually.

60
00:02:58,740 --> 00:03:01,650
See, first of all, we have taken different differences.

61
00:03:01,950 --> 00:03:07,530
Stubing element like this lower number six six minus zero is the difference.

62
00:03:08,490 --> 00:03:11,010
And what I have to do, I have to scan for this.

63
00:03:11,370 --> 00:03:19,290
So OK, follow before I takes the values from zero on words and I should be less than equal to 10.

64
00:03:19,290 --> 00:03:20,580
So that is less than 11.

65
00:03:20,880 --> 00:03:23,310
Less than an eight plus minus.

66
00:03:25,310 --> 00:03:30,440
Then every time I should check that whether the difference between the element is maintained, same

67
00:03:30,440 --> 00:03:31,370
difference is maintained.

68
00:03:31,610 --> 00:03:33,210
So index versus element.

69
00:03:33,440 --> 00:03:34,340
So if.

70
00:03:36,430 --> 00:03:43,720
Element at I and I itself, if we take a difference, if it is not equal to the startling difference,

71
00:03:43,790 --> 00:03:51,640
this one, if it is not equal, means there is some element missing right now, this difference may

72
00:03:51,640 --> 00:03:55,740
be off one or maybe off two or maybe more.

73
00:03:56,020 --> 00:04:00,060
See, in this example, the difference was one new difference was one.

74
00:04:00,370 --> 00:04:03,220
Now, in this example, no difference is more than one.

75
00:04:03,700 --> 00:04:07,820
So I assume that it may be one or more than one.

76
00:04:08,170 --> 00:04:12,510
So let us see more than one only so far that I will display all the elements.

77
00:04:12,760 --> 00:04:16,630
So how to display all the elements, the current difference plus index.

78
00:04:16,880 --> 00:04:17,300
Right.

79
00:04:17,470 --> 00:04:19,019
So that is five.

80
00:04:19,390 --> 00:04:24,030
The current difference plus index right now here I got a mismatch, for example.

81
00:04:24,280 --> 00:04:25,720
So this is 13.

82
00:04:25,720 --> 00:04:31,540
If you are put in, then make it as eight, then this will give you 14, then make it as nine.

83
00:04:31,540 --> 00:04:32,730
So it has become equal.

84
00:04:32,740 --> 00:04:33,370
So stop.

85
00:04:33,590 --> 00:04:38,200
So I should start from the current index to the new index.

86
00:04:38,200 --> 00:04:46,420
So if it does not equal current defense right now, whatever the differences is less than the new defense.

87
00:04:46,470 --> 00:04:51,370
If I minus one see, this is the new defense and this is the existing defense right now.

88
00:04:51,670 --> 00:04:54,650
And if this new defense is not equal, that element is missing.

89
00:04:54,880 --> 00:05:00,040
So while this is not equal, what I should do, I should bring the elements directly and bringing the

90
00:05:00,050 --> 00:05:06,850
element printf the element is current index plus difference.

91
00:05:08,210 --> 00:05:15,470
And I should move to next different so different C++, that's it.

92
00:05:15,830 --> 00:05:22,780
So using a loop, I will print all the missing elements, present different plus index that is present

93
00:05:22,820 --> 00:05:23,950
a difference right now.

94
00:05:23,960 --> 00:05:26,030
But again, the difference is seven plus indexes.

95
00:05:26,030 --> 00:05:26,720
Six, right.

96
00:05:26,980 --> 00:05:29,060
So I'll get put in then defense plus.

97
00:05:29,060 --> 00:05:32,930
Plus now the new difference is eight plus index 614.

98
00:05:33,230 --> 00:05:37,310
Then different C++ differences become nine and this difference is nine.

99
00:05:37,460 --> 00:05:38,210
It is nine.

100
00:05:38,210 --> 00:05:40,490
So it will stop now.

101
00:05:40,490 --> 00:05:43,800
The difference as modified again, the same loop will continue.

102
00:05:44,540 --> 00:05:45,080
That's all.

103
00:05:46,990 --> 00:05:53,440
So if there are multiple elements missing continuously for them, I have to write a loop here, so using

104
00:05:53,440 --> 00:05:55,420
a loop and the rest of the elements.

105
00:05:56,170 --> 00:06:01,950
So this is the method for finding missing elements and sequence of elements and elements must be sorted.

106
00:06:02,320 --> 00:06:06,130
Now, how much time or just taking see what is the amount of work we are doing?

107
00:06:06,370 --> 00:06:08,290
We are scanning for the list of elements.

108
00:06:08,320 --> 00:06:09,120
How many times?

109
00:06:09,130 --> 00:06:09,910
Only one time.

110
00:06:11,060 --> 00:06:18,590
So the tameness outdraw and so there are elements of the time, and so if you look from the code for

111
00:06:18,590 --> 00:06:24,440
Leupp is there for loop, how many times it will repeat and nine times out of ten more.

112
00:06:24,640 --> 00:06:25,370
What about this?

113
00:06:25,550 --> 00:06:28,110
What about this Siembra missing element?

114
00:06:28,490 --> 00:06:30,080
How many will it be printing?

115
00:06:30,080 --> 00:06:31,430
All elements known one.

116
00:06:31,430 --> 00:06:32,390
Just a few elements.

117
00:06:32,690 --> 00:06:33,660
So it's negligible.

118
00:06:33,980 --> 00:06:41,740
So overall, if you see the number of times the process is done, it's from six to last, number 19.

119
00:06:42,260 --> 00:06:48,950
So this loop will repeat from six to 19 and this loop will bring to the missing element.

120
00:06:48,950 --> 00:06:49,930
So ten is missing.

121
00:06:49,940 --> 00:06:52,660
So that is printed 13, 14 is missing.

122
00:06:52,670 --> 00:06:53,440
So that is printer.

123
00:06:53,750 --> 00:06:55,950
So those elements are also included.

124
00:06:56,000 --> 00:06:58,290
So without missing all the elements, we are.

125
00:06:58,910 --> 00:07:02,450
So this loopers accessing all elements, missing any element.

126
00:07:04,310 --> 00:07:10,610
So it means we don't have to consider the time taken by this loop because this time is negligible.

127
00:07:11,130 --> 00:07:14,660
It is filling up the gaps that is missing elements.

128
00:07:15,410 --> 00:07:18,410
So the total times are tough and so.

129
00:07:20,410 --> 00:07:23,140
Time taken by this algorithm or any.

130
00:07:24,230 --> 00:07:31,250
So that's all this is a matter of how to find a missing element, one element or multiple elements in

131
00:07:31,250 --> 00:07:37,730
the sorted out and the sequence of elements maybe starting from one onwards, maybe starting from any

132
00:07:37,730 --> 00:07:38,450
other number.

133
00:07:39,110 --> 00:07:45,980
So this procedure I have shown you this, you have to write on the program and execute it so you can

134
00:07:45,980 --> 00:07:48,440
write a mean function and that can be clear on that.

135
00:07:48,860 --> 00:07:52,700
And inside the main function, only you can write on this logic and check it.

136
00:07:54,250 --> 00:07:55,960
So this is a student exercise.

137
00:07:58,110 --> 00:08:04,350
And the next video, I will show you how to find a missing element in unsorted.

