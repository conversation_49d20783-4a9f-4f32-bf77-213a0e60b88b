1
00:00:00,860 --> 00:00:06,830
The topic is harshing technique and this video, while be discussing about why we need harshing, what

2
00:00:06,830 --> 00:00:10,410
is the use of harshing and what is the ideal hash function?

3
00:00:11,060 --> 00:00:16,550
The basic idea about Hasheem, then we'll see what are the drawbacks of fighting hash function.

4
00:00:16,560 --> 00:00:18,650
Then we will see more or less hash function.

5
00:00:19,160 --> 00:00:20,440
Then it's a drawback.

6
00:00:20,450 --> 00:00:22,360
And what are the possible solutions?

7
00:00:23,330 --> 00:00:30,800
So let us start why we need harshing now, let us understand why we need hasheem see, harshing is useful

8
00:00:30,800 --> 00:00:32,110
for searching.

9
00:00:32,930 --> 00:00:38,540
So if you have the list of keys and you want to search some key from them, then you can use harshing.

10
00:00:39,110 --> 00:00:43,260
So for searching, we already have some known search procedures.

11
00:00:43,290 --> 00:00:48,680
Yes, those are Leanyer search and binary search, which we have already done.

12
00:00:49,550 --> 00:00:51,350
And we already analyzed them.

13
00:00:51,350 --> 00:00:57,860
And we know that the time taken for linear searches, order of N and the time taken for binary searches,

14
00:00:58,250 --> 00:00:58,850
login.

15
00:00:59,870 --> 00:01:02,810
So login is faster than any.

16
00:01:03,050 --> 00:01:04,890
So we know faster searching methode.

17
00:01:04,910 --> 00:01:06,650
That is binary search.

18
00:01:07,010 --> 00:01:09,390
So we want still faster than this one.

19
00:01:09,680 --> 00:01:12,560
So what is the time that is more smaller than this one.

20
00:01:12,860 --> 00:01:16,660
That is one constant or anybody that is less than Log-in.

21
00:01:17,360 --> 00:01:20,200
So we want a search method that is less than the login.

22
00:01:20,690 --> 00:01:24,620
So you have to for that idea of hashing techniques, interviews.

23
00:01:25,430 --> 00:01:30,460
So Harshing will try to search the keys less, then log in time.

24
00:01:31,760 --> 00:01:33,650
So almost constant time.

25
00:01:35,600 --> 00:01:43,910
So let us compare these first we so let us compare this linear search and binary search and then hachim

26
00:01:44,580 --> 00:01:45,540
for explanation.

27
00:01:45,560 --> 00:01:47,180
I have taken a set of keys here.

28
00:01:48,260 --> 00:01:52,490
And these guys are stored in an attic as it is in the same order, they are kept.

29
00:01:52,970 --> 00:01:59,510
Now, if the keys are as it is kept in a nuti, which are not sorted, they are not in any order, then

30
00:01:59,810 --> 00:02:05,100
upon this, we can perform linear search so far, performing linear search.

31
00:02:05,120 --> 00:02:07,900
We don't want keys in the particular order.

32
00:02:08,600 --> 00:02:11,080
The next one is same keys.

33
00:02:11,090 --> 00:02:12,830
I have kept them in the sorted order.

34
00:02:13,220 --> 00:02:16,450
If they are in sorted order, then we can perform binary search.

35
00:02:18,080 --> 00:02:21,470
So the requirement for binary search is the keys must be sorted.

36
00:02:21,770 --> 00:02:24,350
So this is the extra work that you have to do.

37
00:02:24,530 --> 00:02:31,500
If you want to apply binary search that make sure keys are in sorted for keeping them in sorted order.

38
00:02:31,790 --> 00:02:38,570
You have to put some extra effort for definitely so binary search time that is log-in can be achieved

39
00:02:38,780 --> 00:02:42,140
if you are putting efforts in keeping the list in order.

40
00:02:42,650 --> 00:02:45,260
Now, the third idea that is Hasheem.

41
00:02:45,680 --> 00:02:51,940
So if you have the keys, then how they should be arranged so that if you can find them in constant

42
00:02:52,010 --> 00:02:52,430
time.

43
00:02:52,880 --> 00:02:57,170
So here already you have taken an eddy to show you how these keys should be stored.

44
00:02:57,890 --> 00:02:59,440
Let us do these keys here.

45
00:02:59,870 --> 00:03:02,570
Eight should be stored at index eight.

46
00:03:03,780 --> 00:03:10,380
Then three should be stored at the next three, six at the next six, likewise 10.

47
00:03:11,480 --> 00:03:12,290
15.

48
00:03:14,340 --> 00:03:15,090
18.

49
00:03:16,270 --> 00:03:23,950
And for so, kids are stored in the same index and stock indices are empty, they are blank, they don't

50
00:03:23,950 --> 00:03:25,060
have any keys.

51
00:03:25,900 --> 00:03:30,740
So you can put their zero or minus one to show that kids are not there.

52
00:03:31,180 --> 00:03:34,400
So kid is made of the index and that is stored there.

53
00:03:34,510 --> 00:03:40,240
So now if I want to search for any key, let us say I want to search for 10, go to index of 10 U.S.

54
00:03:40,250 --> 00:03:46,000
attorneys, then how much time it takes to find out the 10 this constant time.

55
00:03:46,360 --> 00:03:47,530
I want to find out.

56
00:03:47,530 --> 00:03:52,930
Eight, this they will not go to index 18, so I don't have to check the rest of the case directly to

57
00:03:52,930 --> 00:03:54,090
index eighteen, is there.

58
00:03:54,430 --> 00:03:55,970
Yes, there for keys found.

59
00:03:56,320 --> 00:04:00,910
Now suppose I want to find out a key seven, six, seven is not bad.

60
00:04:01,090 --> 00:04:02,520
So OK, go to index seven.

61
00:04:02,530 --> 00:04:02,700
Yes.

62
00:04:02,740 --> 00:04:04,800
It's not that so directly.

63
00:04:04,810 --> 00:04:08,350
Go to that particular index and find out keys there or not.

64
00:04:08,350 --> 00:04:10,440
They're sticking just Konstantine.

65
00:04:10,960 --> 00:04:11,340
Yes.

66
00:04:11,350 --> 00:04:13,540
This is the fastest method of searching.

67
00:04:14,050 --> 00:04:16,779
Yes, housing is fastest searching method.

68
00:04:17,470 --> 00:04:18,430
No drawback.

69
00:04:18,940 --> 00:04:23,740
The drawback of hatching is see I have only seven keys available here.

70
00:04:24,010 --> 00:04:28,150
But what is the size of the size of the tree is a 19.

71
00:04:28,150 --> 00:04:29,470
I must take 19.

72
00:04:29,470 --> 00:04:31,440
I have to go to 19.

73
00:04:31,460 --> 00:04:38,410
I have taken so I must have space nineteen for this one because the largest number is eighteen.

74
00:04:38,590 --> 00:04:41,980
Then I must have an array up to index eighteen.

75
00:04:42,760 --> 00:04:43,090
Right.

76
00:04:43,360 --> 00:04:49,420
So if, suppose the next number, if I want to insert in this list of keys 100 then I should have an

77
00:04:49,420 --> 00:04:50,840
index hundred also in then.

78
00:04:51,520 --> 00:04:55,780
So do I have only eight elements of the nine, maybe having hundred spaces.

79
00:04:56,710 --> 00:05:00,250
So a lot of space is required, a lot of space is wasted.

80
00:05:00,460 --> 00:05:01,750
So this is a drawback.

81
00:05:02,350 --> 00:05:10,510
So we have to do something to reduce this huge consumption of space so far that let us start studying

82
00:05:10,510 --> 00:05:17,650
about hasheem so far, starting about harshing will represent this harshing in some mathematical model.

83
00:05:18,160 --> 00:05:20,800
So that mathematical model we will look at now.

84
00:05:22,220 --> 00:05:25,610
Let us look at McManigal model for Hasheem.

85
00:05:26,890 --> 00:05:32,200
These are the set of keys, those set of keys, let us assume them as keys space.

86
00:05:33,630 --> 00:05:40,410
And this is hash table, the only way we will be storing the elements or the keys, this is hash table.

87
00:05:42,540 --> 00:05:51,690
Kids should be mobbed on hash table, so it is just like domain and range for the elements from domain

88
00:05:51,690 --> 00:05:54,630
are mapped on the elements in range.

89
00:05:55,580 --> 00:06:04,640
So this is like a relationship, so mapping domain upon a range is a relation or function.

90
00:06:05,390 --> 00:06:10,340
So let me show you what other types of mappings available in relations.

91
00:06:11,120 --> 00:06:12,780
So there are four relational mappings.

92
00:06:12,810 --> 00:06:13,720
Let us look at them.

93
00:06:14,510 --> 00:06:17,000
Here are for relational mapping.

94
00:06:17,390 --> 00:06:22,160
All of these four mappings, one to one, and the many two one.

95
00:06:22,520 --> 00:06:24,460
These are called functions.

96
00:06:24,920 --> 00:06:26,480
These are called functions.

97
00:06:27,260 --> 00:06:30,230
So, yes, function is a type of relationship.

98
00:06:30,290 --> 00:06:32,990
So here we will use a function for mapping.

99
00:06:33,290 --> 00:06:37,990
First of all, let us map them upon the based on the idea that I have given you.

100
00:06:38,270 --> 00:06:42,170
So let us map this aid should go at index eight.

101
00:06:42,380 --> 00:06:53,240
OK, this here then three should go on index three, then six should go on index six and should go on

102
00:06:53,240 --> 00:06:54,230
index 10.

103
00:06:55,220 --> 00:06:55,820
15.

104
00:06:55,820 --> 00:06:56,900
On Index 15.

105
00:06:58,410 --> 00:07:00,510
The name, unindexed name.

106
00:07:02,320 --> 00:07:04,250
Ford, unindexed, Ford.

107
00:07:05,140 --> 00:07:12,190
See, this is one to one mapping, yes, every Kei's mad on one index in a hash table.

108
00:07:12,580 --> 00:07:15,990
It is one to one mapping this mapping function.

109
00:07:16,150 --> 00:07:17,990
So what is the function used here?

110
00:07:18,280 --> 00:07:21,370
See, the function of X?

111
00:07:21,370 --> 00:07:24,940
If I give X is eight, then what is the answer here?

112
00:07:25,060 --> 00:07:25,600
Eight.

113
00:07:26,170 --> 00:07:29,980
If I give you ten of them answers, what 10.

114
00:07:30,580 --> 00:07:33,310
So H of X is equal to X.

115
00:07:33,340 --> 00:07:38,780
This is the function used here for mapping keys on a hash table.

116
00:07:38,800 --> 00:07:42,480
So we say that we are not storing the elements directly.

117
00:07:42,910 --> 00:07:44,980
We are using hash function.

118
00:07:45,370 --> 00:07:50,530
That Hoj function is giving us the index and we are storing a key at that index.

119
00:07:50,920 --> 00:07:54,100
Given the hash function, is this idea.

120
00:07:55,000 --> 00:07:59,950
So next time when we want to search for any key home, you should ask for index again.

121
00:07:59,950 --> 00:08:01,790
Hash function split.

122
00:08:01,810 --> 00:08:06,480
I want to search for nine of nine answers.

123
00:08:06,490 --> 00:08:14,100
What nine Gugger Index nine C hash function has given us the index or location where that is present.

124
00:08:14,980 --> 00:08:20,980
I want to search for 12 each of twelve answers to each of toilets to go to twelve.

125
00:08:21,280 --> 00:08:23,000
It's not the keys not there.

126
00:08:23,770 --> 00:08:29,920
So using hash function, we will map the keys on a hash table so that are for searching.

127
00:08:29,920 --> 00:08:34,010
Also use same hash function for finding an element.

128
00:08:34,659 --> 00:08:38,350
So when we say function, function supports two types of mapping.

129
00:08:38,650 --> 00:08:41,520
That is one to one are many to one.

130
00:08:41,770 --> 00:08:45,230
So our function that we are using here is one to one function.

131
00:08:45,940 --> 00:08:48,730
So this function of X is equal to X..

132
00:08:48,730 --> 00:08:51,400
We call it as ideal hasheem.

133
00:08:52,340 --> 00:08:58,410
Because the timetable for searching or storing or deleting an element is constant.

134
00:08:58,760 --> 00:09:04,280
Just use the hash function and go and store the value there and use the hash functionality of the value.

135
00:09:04,280 --> 00:09:05,450
So the time is constant.

136
00:09:05,690 --> 00:09:08,240
The time is equal to the time spent by hash function.

137
00:09:08,630 --> 00:09:10,490
So this is ideal caching.

138
00:09:11,850 --> 00:09:18,930
Now, let us talk about the drawback of ideal harshing, the drawback of ideal harshing is.

139
00:09:19,890 --> 00:09:22,770
The space required is very huge.

140
00:09:23,690 --> 00:09:30,630
A lot of space is required, even though I have only seven elements here than the air space, I have

141
00:09:30,630 --> 00:09:31,890
a zero to 16.

142
00:09:32,340 --> 00:09:39,330
And if I have one larger key here, like what I have done, then I should have an index 10 that also

143
00:09:39,750 --> 00:09:42,980
then only it's possible to achieve ideal harshing.

144
00:09:44,170 --> 00:09:45,100
This is the drawback.

145
00:09:46,870 --> 00:09:49,210
So who is responsible for this Trubek?

146
00:09:50,810 --> 00:09:58,230
Hash function, who is giving the index hash function is giving the index so hash function is responsible.

147
00:09:58,250 --> 00:09:59,420
Ideal hash function.

148
00:10:00,200 --> 00:10:04,130
If I want to reduce the space, I want to achieve the same thing.

149
00:10:04,130 --> 00:10:06,260
Hashing in limited space.

150
00:10:06,560 --> 00:10:07,580
Then do what?

151
00:10:07,910 --> 00:10:11,270
Modify hash function, modify this one.

152
00:10:11,870 --> 00:10:16,770
Now let us see how we can modify hash function, how much space I want to provide.

153
00:10:17,030 --> 00:10:18,890
I want to provide only ten spaces.

154
00:10:18,920 --> 00:10:20,000
That is zero to nine.

155
00:10:20,810 --> 00:10:25,550
So if I'm providing only 029 spaces then hash function should be change.

156
00:10:25,820 --> 00:10:29,080
Otherwise for 15 it will give the index 15 15.

157
00:10:29,090 --> 00:10:29,770
It's not there.

158
00:10:31,740 --> 00:10:34,120
Now, let us modify hash function.

159
00:10:34,230 --> 00:10:43,590
Am I modifying hash function XIAFLEX equal to Exmoor 10 so I will remove the space, I will take only

160
00:10:43,590 --> 00:10:47,130
029 And this is not the hash table sizes.

161
00:10:47,130 --> 00:10:52,620
029 I am ready to provide only the size of ten only because I have seven keys.

162
00:10:52,830 --> 00:10:56,220
So I think that is sufficient if I have taken space 10.

163
00:10:56,460 --> 00:10:58,410
So what should the hash function.

164
00:10:58,590 --> 00:11:01,820
Hash function is modified that X model 10.

165
00:11:02,010 --> 00:11:06,060
So this hash function will never give you value greater than nine.

166
00:11:06,540 --> 00:11:09,900
So get the values from zero to nine only because it is more ten.

167
00:11:11,810 --> 00:11:18,660
Now, let us map these values once again, using that hash function so that a stock eight eight more

168
00:11:18,690 --> 00:11:25,100
under 10 see eight more the Ten Commandments, the remainder, the remainder one eight is divided by

169
00:11:25,100 --> 00:11:25,610
10.

170
00:11:25,610 --> 00:11:28,520
The remainder is what it only because.

171
00:11:30,060 --> 00:11:35,600
Eight divided by 10, so it doesn't get divided zero times, so there's only so Remender is, what,

172
00:11:35,610 --> 00:11:35,930
10?

173
00:11:36,540 --> 00:11:38,090
So it is eight only.

174
00:11:38,340 --> 00:11:43,260
So this eight is mapped on index eight, then three, three more to 10.

175
00:11:44,850 --> 00:11:51,000
Three, only six, more than six only then the ten, the more ten and the more ten.

176
00:11:51,000 --> 00:11:54,890
If this is ten, then one to ten and Remender is zero.

177
00:11:55,200 --> 00:11:56,340
So we get nine zero.

178
00:11:56,340 --> 00:11:58,080
So 10 should be stored here.

179
00:11:59,230 --> 00:12:05,740
So this modulars function has given the index zero, so I have stood there not 15, 15, more 10.

180
00:12:05,750 --> 00:12:10,250
So if it is 15 more, 10, ten, one, ten, and then subtract this remainder five.

181
00:12:10,540 --> 00:12:11,690
So remember, we got five.

182
00:12:11,710 --> 00:12:14,790
So 15 should be stored at index five.

183
00:12:15,580 --> 00:12:16,290
So that's all.

184
00:12:16,290 --> 00:12:19,550
All the keys are stored or mapped using this hash function.

185
00:12:20,000 --> 00:12:23,260
Now let's see the problem with this of hash function.

186
00:12:23,290 --> 00:12:27,610
The drawback of modulars hash function if I have a key value.

187
00:12:27,620 --> 00:12:28,450
Twenty five.

188
00:12:29,650 --> 00:12:36,970
Whether it should be manned twenty five more to ten, so then will divide to define so 10 to 20.

189
00:12:38,390 --> 00:12:46,160
Remember this fight, so this twenty five is also moved on in the next five dislocation, only see 15

190
00:12:46,160 --> 00:12:48,820
is already present there, then twenty five.

191
00:12:49,010 --> 00:12:50,600
There is no space for twenty five.

192
00:12:51,420 --> 00:12:58,590
So two kids are back, same index, because we have modified hash function and this model's hash function,

193
00:12:58,750 --> 00:13:02,810
when cookies are mapped out, same location we call it, as collusion.

194
00:13:03,090 --> 00:13:05,160
So there is a collision of keys.

195
00:13:06,120 --> 00:13:11,130
And if you are wanting to kiss my dad, same place, many to one.

196
00:13:11,400 --> 00:13:13,650
So dysfunction is not one to one.

197
00:13:13,770 --> 00:13:15,040
So dysfunction is many.

198
00:13:15,070 --> 00:13:16,260
The one for more than one.

199
00:13:16,260 --> 00:13:18,220
Kids may be mapping at the same place.

200
00:13:18,240 --> 00:13:21,590
Yes, 15 will be map there.

201
00:13:21,720 --> 00:13:23,270
Five will also map there.

202
00:13:23,280 --> 00:13:23,910
Twenty five.

203
00:13:23,910 --> 00:13:24,870
Also thirty five.

204
00:13:24,870 --> 00:13:25,560
Forty five.

205
00:13:25,950 --> 00:13:29,760
Any number and ending with the five digit five will be map there only.

206
00:13:30,210 --> 00:13:32,900
So if you have multiple keys all will be mapped differently.

207
00:13:33,210 --> 00:13:34,320
So there's this confusion.

208
00:13:34,770 --> 00:13:37,950
So this is the drawback of model of function.

209
00:13:38,160 --> 00:13:44,910
When you modify hash function from ideal to any other function than there is a problem of collusion

210
00:13:45,180 --> 00:13:49,020
because ideally hash function is one to one and any other hash function.

211
00:13:49,020 --> 00:13:52,260
If you write, if it is not one to one, then definitely this, many to one.

212
00:13:52,530 --> 00:13:53,100
So many do.

213
00:13:53,100 --> 00:13:57,230
One means more than one keys will come on the same index and that is collision.

214
00:13:57,990 --> 00:13:59,670
Now, how to resolve this collision.

215
00:13:59,940 --> 00:14:05,640
So I'll show you what are the solutions for resolving this collision.

216
00:14:06,540 --> 00:14:10,890
So next, we will see what should be done for avoiding collusion.

217
00:14:12,120 --> 00:14:15,890
Now let us see what other methods for resolving collusion.

218
00:14:16,230 --> 00:14:21,510
So if a function is a many to one and if there is a collision two or more, these are mapped assemblies,

219
00:14:21,870 --> 00:14:25,660
then how to solve that problem for solving the problem?

220
00:14:25,980 --> 00:14:31,570
There are two major methods open hashing, closed hashing fossil delu this difference.

221
00:14:31,590 --> 00:14:37,800
So first, I will say what is close to see the size of the hash table is a ten and this is the space

222
00:14:37,800 --> 00:14:38,190
given.

223
00:14:38,460 --> 00:14:41,020
So we will utilize only that much space.

224
00:14:41,490 --> 00:14:45,950
We will not increase the space that is close to hashing.

225
00:14:46,290 --> 00:14:47,660
Then what is open hashing.

226
00:14:48,000 --> 00:14:50,340
We will consume extra space.

227
00:14:51,760 --> 00:14:58,480
Beyond this hash table, we will take more space, so that's why that is open hashing and in that open

228
00:14:58,480 --> 00:15:01,810
hashing method, chaining matters.

229
00:15:02,170 --> 00:15:09,920
We will look at these matters for one of them at chaining then enclosed hashing treatments out there.

230
00:15:10,150 --> 00:15:12,160
So these are under open other.

231
00:15:12,540 --> 00:15:18,400
So I'll explain what does it mean by open addressing if two more keys are mapped that same place, if

232
00:15:18,400 --> 00:15:24,160
there is collision, like suppose twenty five, I got the same index, but this place is already occupied.

233
00:15:24,670 --> 00:15:31,420
Hash function has given this place, but it is occupied so we'll stop it at any other free space.

234
00:15:31,720 --> 00:15:38,770
So we're not strictly stored at a place where hash function has given that address, we may store that

235
00:15:38,770 --> 00:15:39,660
any other address.

236
00:15:39,670 --> 00:15:41,620
So that's all this is open addressing.

237
00:15:42,340 --> 00:15:44,530
But the question is, where will you store?

238
00:15:44,950 --> 00:15:49,900
There are three options linear probing, quadratic probing and double hashing.

239
00:15:50,410 --> 00:15:53,740
So we look at all these methods and next videos.

240
00:15:54,680 --> 00:15:59,570
So that's all about introduction to hashing an ideal hash function.

