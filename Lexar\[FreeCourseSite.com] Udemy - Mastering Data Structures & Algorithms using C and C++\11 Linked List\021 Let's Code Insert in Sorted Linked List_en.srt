1
00:00:00,210 --> 00:00:07,290
In this video, we will look at a function for inserting a new node in the socket link list here, I'm

2
00:00:07,290 --> 00:00:09,240
using the same project already.

3
00:00:09,240 --> 00:00:12,420
We have done so many functions upon Linklaters.

4
00:00:12,720 --> 00:00:16,290
Now we will write a function for inserting and a solid link.

5
00:00:16,320 --> 00:00:20,590
So already I have a link list created here inside the main function.

6
00:00:20,610 --> 00:00:25,600
I have an array of elements and create function is creating a linking sort of those five elements.

7
00:00:25,890 --> 00:00:32,580
Now the main function I will write on a function for inserting in a sartorialist, so I will call the

8
00:00:32,580 --> 00:00:37,770
function names sorted insert and it will take barometer's that is pointed to.

9
00:00:37,770 --> 00:00:42,130
First note is appointed the first node, then the element that we want to insert.

10
00:00:42,360 --> 00:00:43,990
Now what are the variables required?

11
00:00:44,010 --> 00:00:48,620
I will be declaring them what first of all, I need a variable for creating a new node.

12
00:00:48,630 --> 00:00:52,140
So I take a pointer for creating a new order.

13
00:00:52,620 --> 00:00:58,230
And also we need a trailing pointer that will be following pointer B, so I will get you.

14
00:00:58,230 --> 00:01:00,570
And that is initialized to null initially.

15
00:01:00,720 --> 00:01:05,430
Now this element will be definitely get inserted in the Linkous.

16
00:01:05,640 --> 00:01:11,190
So as it's going to be inserted for sure, I will create a new node, the assign.

17
00:01:12,130 --> 00:01:20,800
Struck North Pointer and using mallock function, I will create a new node size of struct, not a node

18
00:01:20,800 --> 00:01:24,580
is created and I will also send the data in this node.

19
00:01:24,730 --> 00:01:29,350
The data is also set and I will also set the next pointer also null.

20
00:01:29,380 --> 00:01:32,560
So this will complete the work of creating and initializing a node.

21
00:01:33,650 --> 00:01:38,960
Now we can deal with the rest of the things, now we have to check for some special cases here that

22
00:01:38,960 --> 00:01:44,600
I did not discussed on the whiteboard, the very first thing is, is it a first note created?

23
00:01:44,600 --> 00:01:47,900
If this is the first node, then already pointer first will be null.

24
00:01:48,320 --> 00:01:49,630
That is B will be null.

25
00:01:49,640 --> 00:01:54,410
The conceptional or I can use global variable first and check whether it is null.

26
00:01:54,680 --> 00:01:59,060
Then if it is the very first node then I should meet first point on.

27
00:02:00,110 --> 00:02:00,480
E!

28
00:02:01,720 --> 00:02:08,530
Does the first special case then, if it is not a first note, then we have to insert in a solid position

29
00:02:08,530 --> 00:02:13,350
so far that we use a loop for iterating through a link list for finding a position.

30
00:02:13,360 --> 00:02:21,760
So I will read on that loop will be an easy data as less X and every time you will move upon E and P

31
00:02:21,760 --> 00:02:23,430
will move to next.

32
00:02:23,450 --> 00:02:28,450
Known this we have already seen and once we have reached a node whose data is greater than or equal

33
00:02:28,450 --> 00:02:31,540
to X, then they will stop and insert a node now.

34
00:02:31,560 --> 00:02:33,010
Again, this is a special case.

35
00:02:33,460 --> 00:02:35,310
The place where we have Staab.

36
00:02:35,320 --> 00:02:36,430
Is it the first node?

37
00:02:36,440 --> 00:02:40,140
If it is the first node and the new node will be inserted on the left hand side.

38
00:02:40,570 --> 00:02:45,880
So I should check the condition that if B is equal before then, I should insert a new node before the

39
00:02:45,880 --> 00:02:46,540
first node.

40
00:02:46,540 --> 00:02:54,810
So far that I should say is next, should be equal to first and first should point on P.

41
00:02:54,820 --> 00:03:04,030
If PS not first, then I can insert a new node between the NQ pointers seeking the help of a Windex.

42
00:03:04,030 --> 00:03:11,070
So these next should be same as Qs next and Qs next should point on the next and so on.

43
00:03:11,080 --> 00:03:17,230
Whiteboard I have shown you only these things that is taking a falling pointer and moving B and Q and

44
00:03:17,230 --> 00:03:18,610
then linking them.

45
00:03:19,180 --> 00:03:21,430
But here we have to deal with the special case.

46
00:03:21,430 --> 00:03:26,260
Also, if the element that we are inserting is the smallest, then it will come before the first node.

47
00:03:26,290 --> 00:03:31,600
So if bees are staying up on the first, not only then we have to insert a new node before Fastenal

48
00:03:31,600 --> 00:03:36,730
and above this initially, if there is no not at all, then it should be a very fast node.

49
00:03:36,730 --> 00:03:38,230
So two special cases.

50
00:03:38,230 --> 00:03:39,360
I have handled them here.

51
00:03:39,370 --> 00:03:44,700
So this makes a complete program are complete function for inserting in that list.

52
00:03:44,800 --> 00:03:51,340
Let us run the program and display the list here inside the main function I will call display function

53
00:03:51,350 --> 00:03:52,450
for displaying a link list.

54
00:03:52,840 --> 00:03:56,380
So right now I have only five elements that extend to 50.

55
00:03:56,770 --> 00:03:59,230
Then it should display all those elements.

56
00:03:59,240 --> 00:04:03,520
Let us run and extend of key elements are displayed after the split function.

57
00:04:03,520 --> 00:04:09,220
I will also give a new line so that I can clearly see the output not before display function.

58
00:04:09,220 --> 00:04:12,490
I will call sorted insert now sorted insert.

59
00:04:12,490 --> 00:04:18,010
I will give an element that is thirty five, so it should come in between 30 and 40.

60
00:04:18,010 --> 00:04:25,030
Then after that anyway I'm displaying a list so I should get the elements and 20, 30, 35, 40 and

61
00:04:25,030 --> 00:04:25,810
50 here.

62
00:04:25,810 --> 00:04:32,800
Also I should send a pointer first as a barometer then thirty five is the element so that I see output.

63
00:04:32,980 --> 00:04:37,510
Yes I got the elements and 20, 30, 35, 40, 50.

64
00:04:37,930 --> 00:04:39,670
Yes it is inserting it properly.

65
00:04:39,790 --> 00:04:41,650
Then I will insert an element just five.

66
00:04:41,650 --> 00:04:43,500
So it should be inserted before ten.

67
00:04:43,510 --> 00:04:48,460
Yes, it's working five is inserted before then I will insert a new element that is fifty five that

68
00:04:48,460 --> 00:04:50,130
should be inserted after fifty.

69
00:04:50,170 --> 00:04:51,820
Yes, this is also working.

70
00:04:51,820 --> 00:04:54,160
Fifty five is inserted after fifty dollars.

71
00:04:54,160 --> 00:04:54,850
Do one thing.

72
00:04:54,850 --> 00:05:00,190
I will make this comment so there is no link at all that I am inserting the very first element that

73
00:05:00,190 --> 00:05:00,880
is 55.

74
00:05:00,880 --> 00:05:02,890
So 55 will become the very first element.

75
00:05:02,890 --> 00:05:04,140
There is no lintels at all.

76
00:05:04,150 --> 00:05:05,040
The first is null.

77
00:05:05,560 --> 00:05:07,480
So when I display I should get the element.

78
00:05:07,480 --> 00:05:08,110
Fifty five.

79
00:05:08,140 --> 00:05:09,100
Yes I got it.

80
00:05:09,400 --> 00:05:14,110
So even if the link is empty, there is nothing and Foster's null then also it is inserting.

81
00:05:14,110 --> 00:05:18,880
Then after that I will it for one more time first and I will give the value that is twenty five.

82
00:05:18,880 --> 00:05:23,370
So twenty five should come before fifty five is twenty five and fifty five.

83
00:05:23,410 --> 00:05:25,120
Yes it is working perfectly.

84
00:05:25,120 --> 00:05:31,870
So that's all in this video we have seen how to insert an element and sorted lengthiest and apart from

85
00:05:31,870 --> 00:05:36,640
the discussion up on whiteboard, we have written the code for two extra cases.

86
00:05:36,640 --> 00:05:37,350
That is of the link.

87
00:05:37,420 --> 00:05:42,460
This is already empty or the new element is going to be inserted before the first home.

88
00:05:42,460 --> 00:05:46,810
So practice this programme and you can you can find a PDF below this video.

