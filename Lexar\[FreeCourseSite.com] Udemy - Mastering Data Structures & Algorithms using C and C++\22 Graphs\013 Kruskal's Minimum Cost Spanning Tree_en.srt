1
00:00:00,150 --> 00:00:04,270
And the video we look at <PERSON>'s method for finding minimum costs.

2
00:00:04,290 --> 00:00:07,470
Apparently this is a simplistic and easy method.

3
00:00:08,220 --> 00:00:10,170
Already you have seen <PERSON>'s method.

4
00:00:10,570 --> 00:00:12,830
This is more simpler than this method.

5
00:00:13,810 --> 00:00:18,900
Let us use crosscuts method and find the minimum cost of spending for this graph.

6
00:00:19,760 --> 00:00:20,660
So let us start.

7
00:00:21,610 --> 00:00:23,650
So there is no initial step here.

8
00:00:24,570 --> 00:00:31,320
All the steps are beating only, so what is that revealing step always select minimum cost.

9
00:00:32,340 --> 00:00:32,940
That's it.

10
00:00:33,570 --> 00:00:36,110
So what is the minimum cost edge in this graph?

11
00:00:36,120 --> 00:00:38,770
The smallest edge in this graph, this one, two, six.

12
00:00:38,940 --> 00:00:40,440
So let's take that at.

13
00:00:42,180 --> 00:00:49,230
One to six, and it's cost to fight, then select the next minimum, next minimum wage to seven.

14
00:00:50,970 --> 00:00:52,230
This is two to seven.

15
00:00:53,960 --> 00:00:58,790
And the cost eight ain't the next minimum, the next minimum is nine.

16
00:00:58,970 --> 00:00:59,940
That is two to three.

17
00:01:00,350 --> 00:01:01,350
So two to three.

18
00:01:02,210 --> 00:01:03,140
The first nine.

19
00:01:04,110 --> 00:01:05,000
Next minimum is a.

20
00:01:05,550 --> 00:01:11,010
That is three to four selected for and the system.

21
00:01:12,270 --> 00:01:20,170
The next minimum is four to seven, select that one, no, this is forming a cycle.

22
00:01:21,300 --> 00:01:28,460
Yes, Cresco says that always select a minimum cost edge, but make sure they are not forming a cycle

23
00:01:28,920 --> 00:01:31,150
if it is forming a cycle, just to skip that one.

24
00:01:31,830 --> 00:01:33,060
OK, don't take this one.

25
00:01:33,090 --> 00:01:34,110
This is forming a cycle.

26
00:01:35,000 --> 00:01:37,410
The next minimum, next minimum is four to five.

27
00:01:37,430 --> 00:01:38,060
That is 12.

28
00:01:40,510 --> 00:01:41,350
The Fastweb.

29
00:01:42,780 --> 00:01:48,270
Next minimum of 16, this one that is five to seven, if I take it, will fall.

30
00:01:48,270 --> 00:01:51,480
Most likely it will be a closed circuit, right?

31
00:01:51,780 --> 00:01:52,940
So don't take that one.

32
00:01:53,310 --> 00:01:55,540
The next minimum is, what, 18?

33
00:01:55,880 --> 00:01:58,330
OK, five to six selected.

34
00:01:58,860 --> 00:01:59,910
So this is 18.

35
00:02:00,970 --> 00:02:07,990
That's all there are total seven bodices and I have selected six edges and the cost of the three is

36
00:02:08,350 --> 00:02:13,930
sixteen, so already we have got a similar green algorithm also for the same goals at a.

37
00:02:15,040 --> 00:02:16,810
So I once again, I repeat the procedure.

38
00:02:17,110 --> 00:02:23,500
Kruskal says that always select a minimum cost from the graph and make sure that selection of that vertex

39
00:02:23,500 --> 00:02:24,690
is not forming a cycle.

40
00:02:24,700 --> 00:02:27,370
If it is forming a cycle, then go to the next minimum wage.

41
00:02:27,950 --> 00:02:29,740
That's all about Kruskal smattered.

42
00:02:30,720 --> 00:02:32,820
Now, let us do some analysis.

43
00:02:33,970 --> 00:02:40,480
What is the time taken by this algorithm, so the time taken depends on the number of edges it has selected,

44
00:02:40,840 --> 00:02:47,290
so it is selecting a number of what is as minus one, adjust these many and we minus one.

45
00:02:47,500 --> 00:02:48,810
That is seven minus one six.

46
00:02:48,820 --> 00:02:50,710
And just one, two, three, four, five, six.

47
00:02:50,710 --> 00:02:51,100
Yes.

48
00:02:52,090 --> 00:03:00,070
Then, while selecting each edge, it is finding out the minimum crosshatch from all the edges, so

49
00:03:00,070 --> 00:03:03,880
the time taken for finding a minimum from all of it, just as E..

50
00:03:04,660 --> 00:03:11,360
So the total dimness V into E, this is M and this is E, this is linear.

51
00:03:11,380 --> 00:03:12,550
This is also linear.

52
00:03:12,790 --> 00:03:14,650
So we can write it as an interim.

53
00:03:14,660 --> 00:03:21,010
So the times are rough and square Kuskokwim until this order of and squared time.

54
00:03:22,760 --> 00:03:27,400
Independent of the data structure it is using, whether it is using are just on semantics or just a

55
00:03:27,490 --> 00:03:27,800
list.

56
00:03:28,250 --> 00:03:29,590
We are not bothered about that.

57
00:03:29,960 --> 00:03:36,550
We are just talking about the work done here using pen and paper, search and square.

58
00:03:37,280 --> 00:03:39,980
Then can we improve this critical matter?

59
00:03:40,140 --> 00:03:41,200
Yes, we can improve.

60
00:03:41,600 --> 00:03:44,180
See, always finding a minimum cost.

61
00:03:44,630 --> 00:03:51,000
So there is a data structure called AHEPE, which always gives you minimum value or maximum value.

62
00:03:51,020 --> 00:03:58,760
So many always gives minimum value so we can take the benefit of many for all this become a minimum

63
00:03:58,760 --> 00:03:59,680
cost edge.

64
00:04:00,020 --> 00:04:04,460
So finding a minimum value from here takes longer time.

65
00:04:04,880 --> 00:04:06,650
So this is Logan.

66
00:04:06,860 --> 00:04:09,190
This is Logan Log, actually.

67
00:04:09,320 --> 00:04:14,200
So instead of log, I can write and so and into login.

68
00:04:14,210 --> 00:04:19,370
So the time complexity of Kruskal, Curtis and Logan, if he is used.

69
00:04:21,250 --> 00:04:29,570
Then next, competition between Kruskal and primps since the provincial algorithm, the focus this morning

70
00:04:29,620 --> 00:04:33,910
on finding a tree rather than minimum tree.

71
00:04:34,270 --> 00:04:40,620
But here, more focus is on finding a minimum tree than forming a tree itself.

72
00:04:41,320 --> 00:04:44,530
If it is violating tree, Kruskal says don't take that.

73
00:04:45,190 --> 00:04:46,780
So main focus is on minimum.

74
00:04:47,080 --> 00:04:50,950
So this is the difference between the approach of these two algorithm's.

75
00:04:51,970 --> 00:04:56,670
Let us see if a grothus noncontact a graph how Kruskal works.

76
00:04:57,100 --> 00:04:58,690
I'll take a non connected graph.

77
00:05:00,650 --> 00:05:06,040
Here I have taken a non connected graph, there are two components in the graph, if I run go slower

78
00:05:06,250 --> 00:05:10,870
than what it will do, it will always select minimum Kasich segments like this one.

79
00:05:10,900 --> 00:05:11,740
Then this one.

80
00:05:11,750 --> 00:05:12,520
Then this one.

81
00:05:12,880 --> 00:05:14,980
This is forming a cycle.

82
00:05:15,280 --> 00:05:16,720
So it will not take this one.

83
00:05:17,980 --> 00:05:23,290
For Delphi, it will select this one six, it will select this one seven is forming a cycle.

84
00:05:23,290 --> 00:05:24,410
It will not like this one.

85
00:05:24,850 --> 00:05:29,050
So there are total workers as it should actually select six edges.

86
00:05:29,260 --> 00:05:30,400
But how many deselected?

87
00:05:30,400 --> 00:05:36,010
One, two, three, four, five, only to select selected one is missing, but because they were forming

88
00:05:36,280 --> 00:05:38,050
cycles, it has not included them.

89
00:05:38,080 --> 00:05:39,850
So if you see the result, what do you find?

90
00:05:40,450 --> 00:05:46,840
If there are more than one components in a graph, it will find us three for each component individually,

91
00:05:47,140 --> 00:05:48,670
but not for the entire graph.

92
00:05:48,790 --> 00:05:50,470
Because the graph is not connected.

93
00:05:50,740 --> 00:05:52,690
You cannot find spanning three for the graph.

94
00:05:54,220 --> 00:06:01,180
But it is possible to find the spinal injuries for the components of a graph so Kruskal can find spinal

95
00:06:01,180 --> 00:06:03,400
injuries for individual components.

96
00:06:05,220 --> 00:06:11,580
This was not possible in RIM's algorithm, so that's all so we'll look at a demonstration program on

97
00:06:11,940 --> 00:06:13,460
Google's algorithm.

98
00:06:14,960 --> 00:06:16,100
So that's all in the studio.

