1
00:00:00,560 --> 00:00:04,670
No, the topic is deletion from Aviad.

2
00:00:06,600 --> 00:00:11,010
Deletions from April three is the same as the deletion from binary search tree.

3
00:00:13,170 --> 00:00:20,150
So I'll just tell you the procedure first search for a key that you want to delete is found deleted

4
00:00:20,460 --> 00:00:27,430
that will take its place in order predecessor in order, predecessor or in order successor.

5
00:00:28,380 --> 00:00:33,960
Will take its place, right, if you want to walk, if you want to learn it, you can go back to Delicia

6
00:00:33,970 --> 00:00:34,590
from Binelli.

7
00:00:34,590 --> 00:00:37,430
So the streets and you can watch that video once again.

8
00:00:37,830 --> 00:00:40,680
So same procedure is followed by the Namir.

9
00:00:41,040 --> 00:00:47,550
But if your trees are hired to balance the binary search trees and the high balancing is done with balance

10
00:00:47,550 --> 00:00:52,920
factors, so if any notice becomes an imbalance, we perform rotation for balancing it.

11
00:00:53,700 --> 00:00:57,750
So we have already seen how the rotations are performed at the time of insertion.

12
00:00:58,290 --> 00:01:02,370
If any notice imbalance, then there are four different types of rotation.

13
00:01:02,370 --> 00:01:06,410
You can perform the suitable one, not the time of deletion.

14
00:01:06,420 --> 00:01:12,950
Also, any node may become imbalance or if any notice is becoming imbalanced, then we need to perform

15
00:01:12,960 --> 00:01:13,560
rotation.

16
00:01:13,950 --> 00:01:16,280
So what are the types of rotations available?

17
00:01:16,650 --> 00:01:18,900
So there are six rotations of.

18
00:01:19,500 --> 00:01:21,390
Don't panic with these rotations.

19
00:01:21,610 --> 00:01:28,350
These are not New Orleanian insertion we have seen for rotations to their singular rotations and Cuvier

20
00:01:28,620 --> 00:01:33,690
double rotations that same rotations we perform, but we have given different names.

21
00:01:34,260 --> 00:01:40,670
DC these are six names are to identify which rotation should be performed out of those four.

22
00:01:40,920 --> 00:01:42,060
So let us look at them.

23
00:01:44,430 --> 00:01:49,850
So it will not take much time for me to explain these locations, so I already have taken some examples

24
00:01:49,860 --> 00:01:51,280
now through those examples.

25
00:01:51,360 --> 00:01:58,980
We learn about them so far as we will learn about these three rotations, that is L1 and minus one and

26
00:01:58,980 --> 00:02:02,760
Elzie locations like so far that already I have examples.

27
00:02:02,760 --> 00:02:06,120
Then after that we will see out of an hour minus one and hours rotations.

28
00:02:07,210 --> 00:02:10,880
So let us look at this alliterations first rotation.

29
00:02:11,290 --> 00:02:13,180
This is all one rotation.

30
00:02:14,490 --> 00:02:20,790
That's the example for a long tradition now look at this, watch it carefully, suppose I'm deleting

31
00:02:20,790 --> 00:02:22,940
40 this more than deleting.

32
00:02:23,460 --> 00:02:30,630
If this note is deleted, then how the tree looks like this is how the tree looks like and the balance

33
00:02:30,630 --> 00:02:32,400
factors will be zero one.

34
00:02:32,400 --> 00:02:34,290
And this becomes too.

35
00:02:35,100 --> 00:02:37,940
So this note became imbalance, right?

36
00:02:38,070 --> 00:02:39,370
This in order to give him balance.

37
00:02:39,750 --> 00:02:42,540
So what is the reason of becoming imbalanced?

38
00:02:42,720 --> 00:02:47,280
Because we have deleted a note from right side from right hand side.

39
00:02:47,280 --> 00:02:49,630
Right deletion is done from right hand side.

40
00:02:50,070 --> 00:02:52,880
So all these examples will be deleted from right hand side only.

41
00:02:53,400 --> 00:02:55,890
So it is becoming imbalanced from which side?

42
00:02:55,890 --> 00:02:56,880
From left hand side.

43
00:02:57,060 --> 00:03:00,860
So if you remove something from right side, it will become heavy on left side.

44
00:03:00,960 --> 00:03:01,450
Definitely.

45
00:03:01,790 --> 00:03:04,530
So it is imbalance up on the left hand side.

46
00:03:04,950 --> 00:03:10,430
Now, if you look at the very first child of this small right on the left hand.

47
00:03:11,580 --> 00:03:16,790
So if you look at the left child of this node, its balance factor is one.

48
00:03:17,280 --> 00:03:20,750
So that's why we call it as L1 rotation.

49
00:03:21,180 --> 00:03:23,060
So this year we should perform here.

50
00:03:23,490 --> 00:03:29,250
Now, you know very well that rotation should be performed over this node, right over this node, this

51
00:03:29,250 --> 00:03:30,100
type of rotation.

52
00:03:30,390 --> 00:03:33,480
So earlier we were calling it as a little rotation.

53
00:03:33,760 --> 00:03:39,200
This is a little traditionally, but in deletion we are giving a different name that is L1.

54
00:03:39,780 --> 00:03:45,390
So only the purpose of name is to identify which rotation I should perform, this type of rotation I

55
00:03:45,390 --> 00:03:45,970
should perform.

56
00:03:46,230 --> 00:03:50,510
So depending on the balance factor of the smaller, I should perform a little rotation.

57
00:03:50,670 --> 00:03:53,410
So the new tree will be this one.

58
00:03:53,610 --> 00:03:58,560
So there's a balance of three and the balance factors are zero zero zero.

59
00:03:59,990 --> 00:04:01,460
So first is complicated.

60
00:04:02,300 --> 00:04:05,570
Now let us look at the second one, L minus one.

61
00:04:07,010 --> 00:04:10,520
Now, again, we will delete 40 from right inside.

62
00:04:10,820 --> 00:04:16,430
If I delete 40 from right hand side, then after deletion, the tree looks like this, huh?

63
00:04:16,550 --> 00:04:18,209
This is after reading 40.

64
00:04:18,579 --> 00:04:19,980
Now, what are the balance factors.

65
00:04:20,029 --> 00:04:21,029
Zero minus one.

66
00:04:21,050 --> 00:04:22,200
And this became too.

67
00:04:22,490 --> 00:04:24,260
So this node became imbalance.

68
00:04:24,530 --> 00:04:27,530
So which imbalance, what is the balance factor of its left.

69
00:04:27,530 --> 00:04:27,950
China.

70
00:04:28,220 --> 00:04:31,290
It's minus one severely leading from right side.

71
00:04:31,310 --> 00:04:33,260
So check the balance factor of left China.

72
00:04:33,500 --> 00:04:34,380
It is minus one.

73
00:04:34,580 --> 00:04:37,190
So this is L minus one imbalance.

74
00:04:37,550 --> 00:04:39,990
So which direction I should perform first.

75
00:04:40,050 --> 00:04:41,730
This one, then this one.

76
00:04:41,990 --> 00:04:44,050
So this is a large rotation.

77
00:04:44,210 --> 00:04:48,250
It is heavy because of left right y right.

78
00:04:48,260 --> 00:04:49,500
Because this is minus one.

79
00:04:49,520 --> 00:04:50,680
So it is right side heavy.

80
00:04:50,690 --> 00:04:51,720
This is left side heavy.

81
00:04:52,250 --> 00:04:53,430
This is positive too.

82
00:04:53,450 --> 00:04:55,400
So this is I said this is negative one.

83
00:04:55,410 --> 00:04:59,150
So this is right side heavy to perform a larger rotation.

84
00:04:59,330 --> 00:05:00,610
So this is double rotation.

85
00:05:00,620 --> 00:05:05,510
So if you perform this double the rotation then the resulting three will be twenty will go up in the

86
00:05:05,510 --> 00:05:07,400
root and ten remains on the side.

87
00:05:07,400 --> 00:05:09,070
Only today remains on this side.

88
00:05:09,320 --> 00:05:12,320
So this is the same as a lot of rotation.

89
00:05:12,710 --> 00:05:13,010
Right.

90
00:05:13,280 --> 00:05:15,500
But if you are calling it as L minus one.

91
00:05:15,500 --> 00:05:22,570
Right, because this note became imbalance X left chinless minus one, the third one, this is L zero

92
00:05:22,580 --> 00:05:23,240
rotation.

93
00:05:24,440 --> 00:05:30,140
From this, again, will delete this note 40 after deletion three looks like this, this is the tree

94
00:05:30,140 --> 00:05:30,920
after deletion.

95
00:05:32,180 --> 00:05:35,570
Balance factors are zero zero zero and this is true.

96
00:05:36,050 --> 00:05:41,490
Now, this note became imbalanced because of deletion from right side, so it just left side heavy.

97
00:05:41,900 --> 00:05:46,790
What is the balance factor of its left child, the societal which traditionally can perform?

98
00:05:46,910 --> 00:05:53,420
I can perform either 11 or 12 minus one, any one rotation you can perform.

99
00:05:53,420 --> 00:05:54,440
You have a choice here.

100
00:05:54,640 --> 00:06:01,970
So it means if I perform 11 rotation, then the 10 will move up and 30 will come to the right side,

101
00:06:02,340 --> 00:06:05,550
20 will be on the left side of the sun and five will be here.

102
00:06:06,020 --> 00:06:06,910
This is balance.

103
00:06:07,370 --> 00:06:15,770
If I perform as a minus one rotation, then 20 will go up in the room, then 10 on the side, five on

104
00:06:15,770 --> 00:06:18,900
the side and 30 will be moved on to the right side.

105
00:06:19,400 --> 00:06:23,020
Any one of these three, this is based on one rotation.

106
00:06:23,030 --> 00:06:24,670
This is L minus one rotation.

107
00:06:25,160 --> 00:06:29,000
This is a single rotation and this is double rotation.

108
00:06:29,330 --> 00:06:36,080
If I check the balance factors of zero zero and this is one and this one and the balance factors here

109
00:06:36,080 --> 00:06:39,500
are zero one zero and this is also one.

110
00:06:40,070 --> 00:06:45,890
So but in this one, if you see the rule upon which we have perform rotation, that is not becoming

111
00:06:45,890 --> 00:06:46,360
WSDL.

112
00:06:46,670 --> 00:06:46,960
Right.

113
00:06:47,000 --> 00:06:51,050
It's not becoming Zettl because this side was perfectly balanced.

114
00:06:52,080 --> 00:06:53,930
Right beside was perfectly balanced.

115
00:06:53,950 --> 00:06:57,240
So when you are trying to balance this one, this is not becoming Zeitels.

116
00:06:57,240 --> 00:06:59,940
Earlier this was becoming Siedel, this was becoming Zettl.

117
00:07:00,510 --> 00:07:01,260
So that's all.

118
00:07:01,590 --> 00:07:03,050
These are the.

119
00:07:03,780 --> 00:07:09,310
So that's all these are the rotation's when we are deleting any note from right side.

120
00:07:09,870 --> 00:07:10,140
Right.

121
00:07:10,380 --> 00:07:12,770
So you have to perform information from that site.

122
00:07:12,960 --> 00:07:15,320
So that's why drl zero and minus one.

123
00:07:15,330 --> 00:07:19,660
And so that's why these are 11 and minus one and zero rotation.

124
00:07:20,370 --> 00:07:21,810
I hope I don't have to explain.

125
00:07:21,810 --> 00:07:28,860
You are one R minus one and Oddisee to rotation those rotations will be similar to these just it's a

126
00:07:28,860 --> 00:07:30,870
mirror image of these three cases.

127
00:07:31,140 --> 00:07:35,870
Deletion will be done from that site and it will be imbalanced from right side.

128
00:07:36,150 --> 00:07:37,290
So we balance it.

129
00:07:39,160 --> 00:07:39,550
Like.

130
00:07:40,510 --> 00:07:45,300
So, like here we have a little tradition there, we will have our tradition here, it is a long tradition.

131
00:07:45,310 --> 00:07:50,820
They will have oral tradition and here we will have any of the options when it is our Zevo.

132
00:07:51,700 --> 00:07:53,830
So you can try out this one by yourself.

133
00:07:54,070 --> 00:07:55,450
So three more rotations.

134
00:07:55,450 --> 00:07:56,520
You can go by yourself.

135
00:07:56,830 --> 00:07:59,800
That's all about rotations at the time of division.

