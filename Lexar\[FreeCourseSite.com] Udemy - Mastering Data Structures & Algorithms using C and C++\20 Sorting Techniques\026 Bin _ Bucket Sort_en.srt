1
00:00:00,330 --> 00:00:05,750
Our next topic is buckett thought or been thought, this is similar to counsel.

2
00:00:06,630 --> 00:00:09,840
So let us understand this quickly, because the concept are same.

3
00:00:10,500 --> 00:00:13,500
<PERSON><PERSON><PERSON> let us look at the example.

4
00:00:13,500 --> 00:00:15,290
I have an array of elements.

5
00:00:15,690 --> 00:00:16,750
This I have to sort.

6
00:00:18,250 --> 00:00:22,530
Not for certain, I need extra memory space, so I have taken a.

7
00:00:24,190 --> 00:00:31,660
And this size should be equal to the largest settlement present here, 15, so the maximum index available

8
00:00:31,660 --> 00:00:33,590
here is 15 this year.

9
00:00:33,790 --> 00:00:35,290
I have to initialize it.

10
00:00:35,590 --> 00:00:37,210
So I will initialize them with the.

11
00:00:37,750 --> 00:00:39,100
So all these are not.

12
00:00:41,200 --> 00:00:46,900
So to me, these are pointers, pointers, pointers to what you will come to know.

13
00:00:48,020 --> 00:00:56,260
Now, obviously, I have an array of pointers named as and the size of this are the same as the largest

14
00:00:56,260 --> 00:00:58,110
number presented in this study.

15
00:00:59,290 --> 00:01:01,170
And they are all initialise to none.

16
00:01:02,170 --> 00:01:03,950
Now, let us start the procedure.

17
00:01:03,970 --> 00:01:05,000
So what is the procedure?

18
00:01:05,290 --> 00:01:11,970
I have to scan through these elements and copy them and this means drop them in these bins or buckets.

19
00:01:12,790 --> 00:01:19,550
So let us iStock, I will scan through this until the very first Telemann six, go to index six and

20
00:01:19,570 --> 00:01:22,380
drop that number six at this index.

21
00:01:23,590 --> 00:01:30,690
So actually I will insert that element six here at this index, six six is inserted here.

22
00:01:31,000 --> 00:01:35,590
And this is not this is no more than so this is.

23
00:01:36,760 --> 00:01:37,300
Yes.

24
00:01:37,600 --> 00:01:42,740
This is an array of a linguist array of linguists each index.

25
00:01:42,760 --> 00:01:44,890
This is a bendin of elements.

26
00:01:45,130 --> 00:01:46,510
All those bins are named.

27
00:01:46,690 --> 00:01:48,510
Now, this has been this having one element.

28
00:01:48,520 --> 00:01:50,480
So does the link lists of elements.

29
00:01:50,500 --> 00:01:51,010
Yes.

30
00:01:52,900 --> 00:01:59,560
Next element, next element is eight Google index aid and insert element aid at this Lincolnesque.

31
00:02:01,510 --> 00:02:09,610
Medicine industry, go to industry, insert an element three at this index, three go to index at ten

32
00:02:10,870 --> 00:02:17,890
in certain elements, same element we are inserting 15, go to index 15 and insert 15.

33
00:02:20,150 --> 00:02:26,870
Six go to index six in certain elements, all the elements there, so at the end, one more element

34
00:02:26,870 --> 00:02:27,740
will be inserted.

35
00:02:29,610 --> 00:02:32,940
The nine go to index nine and in certain element.

36
00:02:36,420 --> 00:02:43,620
To go to index to a certain element six, go to index six and insert six.

37
00:02:44,030 --> 00:02:47,420
So at the end of this, Linklaters six will be inserted.

38
00:02:48,990 --> 00:02:54,990
Then three, go to index three and insert a new element three here.

39
00:02:56,560 --> 00:03:04,590
Therefore, depending on the numbers, they are dropped in their corresponding indexed bins, trees

40
00:03:04,600 --> 00:03:09,380
dropped in bin 315 is dropped in bin 15.

41
00:03:09,820 --> 00:03:15,330
So if you compare with the council here for two times the trees, they're celebrating, too.

42
00:03:15,340 --> 00:03:18,490
But now we have to nolte's contending that the trees.

43
00:03:20,140 --> 00:03:23,650
Now, one step is completed, we have copied all the elements now.

44
00:03:23,680 --> 00:03:30,600
Next, we have to empty all these bins from left to right and copy the elements back in early.

45
00:03:31,940 --> 00:03:34,340
So remove these elements and show you.

46
00:03:37,590 --> 00:03:44,610
Yeah, I have removed elements now we have to scan through these bins and empty bins, so let us scan

47
00:03:44,610 --> 00:03:48,540
this array of bits first bin Ziegel, it's already.

48
00:03:48,960 --> 00:03:49,820
It's already done.

49
00:03:50,010 --> 00:03:51,290
It's already done.

50
00:03:51,600 --> 00:03:53,430
What about these elements are there.

51
00:03:53,670 --> 00:03:54,440
So delete.

52
00:03:54,540 --> 00:03:56,160
So the first element will be deleted.

53
00:03:56,580 --> 00:04:02,670
So you'll have to follow pfieffer like fashion linguist's insert at the end, delete from front.

54
00:04:02,940 --> 00:04:09,100
So delete three and insert value three there then delete next the three insert value three there.

55
00:04:09,390 --> 00:04:11,610
Now this becomes emdin, this will be empty.

56
00:04:12,120 --> 00:04:14,250
So when it became null move to NICS.

57
00:04:14,520 --> 00:04:18,000
So it remains until that link is empty.

58
00:04:18,300 --> 00:04:21,350
Copy all the elements from this link lists.

59
00:04:21,579 --> 00:04:24,570
And so likewise I should continue all of them.

60
00:04:24,870 --> 00:04:30,390
I should empty all the bins if it is not empty, delete elements if I let him be just moved to the next

61
00:04:30,390 --> 00:04:30,690
bin.

62
00:04:31,680 --> 00:04:33,930
So I will not delete this, let them be as it is.

63
00:04:33,930 --> 00:04:40,070
That is easy for us to write down the function or algorithm, but I will write on these elements, sorted

64
00:04:40,080 --> 00:04:40,530
elements.

65
00:04:40,530 --> 00:04:42,420
I'll get six, six, six, three times.

66
00:04:44,140 --> 00:04:51,500
Then eight one time, then nine one time, the next is a 10 and 12 and 15, 10, 12 and 15.

67
00:04:51,520 --> 00:04:55,180
So there's a sort of assume that these are all deleted.

68
00:04:56,430 --> 00:05:04,860
So that is the working now announces how much time it takes same and elements are copied here that these

69
00:05:04,860 --> 00:05:07,100
are elements, they are getting copied back there.

70
00:05:07,440 --> 00:05:09,940
So and plus total times out of.

71
00:05:10,030 --> 00:05:13,320
And so the time, complexity of this algorithm is also.

72
00:05:13,470 --> 00:05:14,790
And the next.

73
00:05:14,820 --> 00:05:15,900
What about the space?

74
00:05:16,500 --> 00:05:19,500
It takes a day off bins.

75
00:05:19,530 --> 00:05:21,250
So first of all, array is required.

76
00:05:21,600 --> 00:05:24,520
Then again, depending on the elements and nodes will be created.

77
00:05:25,230 --> 00:05:28,570
So it's taking huge space, lot of space.

78
00:05:28,800 --> 00:05:35,370
So if I say anything, the size that is the maximum element is M and the number of elements are N,

79
00:05:35,670 --> 00:05:38,220
then this is for maximum number.

80
00:05:40,810 --> 00:05:43,690
And this is for a number of elements.

81
00:05:45,040 --> 00:05:50,210
So this is M m so this and listen, this is nothing but linear only so that it is linear.

82
00:05:50,230 --> 00:05:56,680
We can also see out of and it's not that we are adding Immonen is giving n we are saying it is linear

83
00:05:57,010 --> 00:05:58,180
endlessness linear.

84
00:05:58,480 --> 00:05:59,530
So this also linear.

85
00:05:59,530 --> 00:06:05,090
You can also say that now next I will write on the function complete function.

86
00:06:05,110 --> 00:06:07,930
Then I'll show you because the code is same as Gonzales.

87
00:06:08,470 --> 00:06:11,780
So here I have a function ready for Benfold.

88
00:06:11,950 --> 00:06:13,720
Let me explain you that function.

89
00:06:14,320 --> 00:06:19,920
See, the function name has been sorted is taking an array and number of elements like here.

90
00:06:19,930 --> 00:06:24,020
The number of elements are 10 sort of sticking to the next.

91
00:06:24,430 --> 00:06:29,200
I have to find out the maximum number from this one so that I can create an array.

92
00:06:29,530 --> 00:06:30,580
So find Max.

93
00:06:30,610 --> 00:06:41,140
This is finding the maximum number from this array of size and then I should create this array of appointers

94
00:06:41,140 --> 00:06:42,940
of type linguist's nodes.

95
00:06:43,000 --> 00:06:49,620
So here already I have a double pointer type Tibetan's because see, these are all pointers.

96
00:06:49,630 --> 00:06:51,660
So this means a pointer to a pointer.

97
00:06:51,670 --> 00:06:54,550
So I have a double pointer of type node for Lincolnesque.

98
00:06:55,090 --> 00:07:01,360
Then here I am creating an array of Suezmax plus one of type node pointers.

99
00:07:01,630 --> 00:07:03,700
So all these are Northparkes.

100
00:07:04,940 --> 00:07:08,480
Next, I should initialise this entire array of it, none.

101
00:07:08,960 --> 00:07:13,640
So using this for loop, this arrays initialized with none.

102
00:07:14,940 --> 00:07:21,750
Then the next thing is I should scan through this area and whatever the element is, I should drop in

103
00:07:21,750 --> 00:07:23,610
that corresponding bin index.

104
00:07:23,610 --> 00:07:25,710
If it is three, drop it in index three.

105
00:07:26,190 --> 00:07:34,340
So scan through zero to N, that is zero in whatever the elements of a drop in that corresponding bin.

106
00:07:34,350 --> 00:07:36,060
That element we have to insert.

107
00:07:36,420 --> 00:07:43,470
So this is insert function, insert functions for inserting an element in the linked list and this function,

108
00:07:43,470 --> 00:07:49,130
assuming that it inserts at the end of a Linkous because every element we are adding at the end.

109
00:07:51,420 --> 00:07:55,750
So you write down the required function here, so it's inserting the element at the end.

110
00:07:56,220 --> 00:08:02,270
So this followed this for copying all the elements from there and dropping them in the bins now.

111
00:08:02,460 --> 00:08:06,840
Next step is we have to empty all these bins and copy the elements back there.

112
00:08:07,320 --> 00:08:09,720
So for copying bins at each bin, I should go.

113
00:08:09,720 --> 00:08:13,250
If it is empty, then empty, it's not empty.

114
00:08:13,440 --> 00:08:16,050
So delete all the elements until it becomes empty.

115
00:08:18,150 --> 00:08:19,690
So this is the loop here.

116
00:08:20,010 --> 00:08:27,000
See, this is the loop by loop before scanning through this happens, and that is until it becomes empty.

117
00:08:27,030 --> 00:08:31,800
We have to delete and copy the elements here so far that, again, this is a loop.

118
00:08:34,350 --> 00:08:36,820
So whatever the element is deleted is inserted in.

119
00:08:38,250 --> 00:08:41,450
So this is the loop, empty bin.

120
00:08:42,320 --> 00:08:45,680
So it is similar to Gonzales only, only the difference this.

121
00:08:45,950 --> 00:08:47,900
We are emptying bins.

122
00:08:49,460 --> 00:08:55,970
That's all our dysfunction, not from the cold, if I show you the time complexity, then this is a

123
00:08:55,970 --> 00:08:57,590
follow up taking in time.

124
00:08:57,590 --> 00:09:04,640
Does a follow up follow polytheistic in time, but this is instead of a loop loop inside loop, so.

125
00:09:04,640 --> 00:09:05,340
And square.

126
00:09:05,600 --> 00:09:09,440
No, it's not end square because what is the main operation done?

127
00:09:09,740 --> 00:09:10,310
Delete.

128
00:09:10,520 --> 00:09:14,180
So how many elements will be deleted as many elements are there.

129
00:09:14,330 --> 00:09:19,340
So that and elements only so combined together they are taking time and only.

130
00:09:20,500 --> 00:09:29,860
Initialization copying elements from eco bins and from bins to a so total is three and again because

131
00:09:29,860 --> 00:09:31,350
of including initialization.

132
00:09:31,360 --> 00:09:32,830
So it's outdraw and.

133
00:09:34,790 --> 00:09:41,300
So from the court also times order, and so that's all about the function of Vincent.

134
00:09:42,080 --> 00:09:47,660
Now, this has been sort of taking a lot of extra space and it is having the number of villains that

135
00:09:47,660 --> 00:09:53,600
are equal to the maximum number, is it not possible that of less number of bills?

136
00:09:53,600 --> 00:09:54,740
We do the same thing.

137
00:09:55,830 --> 00:10:02,520
That's what let us see the same thing with less number of beans in the next video that is Radix thought

138
00:10:02,790 --> 00:10:07,110
it will be almost the same as a Binstock with some differences.

139
00:10:07,800 --> 00:10:10,350
So next video will be on Radix SORP.

