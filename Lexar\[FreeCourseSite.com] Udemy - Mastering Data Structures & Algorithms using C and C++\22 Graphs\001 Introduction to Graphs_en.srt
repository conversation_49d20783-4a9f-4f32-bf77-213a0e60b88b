1
00:00:00,300 --> 00:00:06,420
This election is about <PERSON><PERSON>'s, so we learn various things about grass, so but in this video, we

2
00:00:06,420 --> 00:00:11,640
will have an introduction to the terminology used in carafes for introducing terminology.

3
00:00:11,650 --> 00:00:14,590
I have taken various example graphs through these graphs.

4
00:00:14,610 --> 00:00:16,020
Let us know the terminology.

5
00:00:16,440 --> 00:00:24,030
SIGGRAPH is defined as a collection of what it says and just see every graph, if you see it is having

6
00:00:24,030 --> 00:00:24,580
what it says.

7
00:00:24,600 --> 00:00:25,580
These are vertices.

8
00:00:26,250 --> 00:00:28,530
The Circle one nodes are what is this?

9
00:00:28,860 --> 00:00:31,930
And the links are the links are pictures.

10
00:00:32,130 --> 00:00:33,570
SIGGRAPH is defined as.

11
00:00:34,570 --> 00:00:45,270
GS equals to v e will we set off first an easy set off it just let us start with the first example.

12
00:00:45,280 --> 00:00:46,620
Here is the first example.

13
00:00:46,900 --> 00:00:49,090
See, this is a directed graph.

14
00:00:49,150 --> 00:00:54,280
So if the judges are having direction, then it is called as directed graph.

15
00:00:54,460 --> 00:00:57,920
This image coming out from one and connecting to four.

16
00:00:58,180 --> 00:01:03,790
So this is all going from one and incoming one for outgoing from for an incoming one.

17
00:01:04,180 --> 00:01:10,110
So it is inside and from for an incident on three in a directed graph.

18
00:01:10,120 --> 00:01:14,410
If there is an edge connecting the same vertex, then it is called self loop.

19
00:01:14,560 --> 00:01:15,430
Does this have a loop?

20
00:01:15,580 --> 00:01:20,570
And if there are a bit of edges between seem to what is this like?

21
00:01:20,570 --> 00:01:23,560
It is connecting from four to three and then there is an edge from three to four.

22
00:01:23,560 --> 00:01:26,680
Then such ideas are called parallel edges.

23
00:01:26,710 --> 00:01:34,180
The next thing, the number of objects connecting up on a vortex that are coming in is called integrate.

24
00:01:34,210 --> 00:01:38,870
The number of are disconnected like this is coming from here, the next coming from here.

25
00:01:38,890 --> 00:01:40,150
So integrate the two.

26
00:01:40,580 --> 00:01:46,120
And if there is an edge going out, the number of changes that are going out are counted as our degree

27
00:01:46,120 --> 00:01:52,380
of a what if there are two Odyssey's connected by an extra, then they are called adjacent towards us.

28
00:01:52,570 --> 00:01:54,900
So these are few times we have learned from there.

29
00:01:55,270 --> 00:01:56,680
Not let us come to this graph.

30
00:01:56,950 --> 00:02:02,280
This graph is not having South Loop and there are no parallel edges.

31
00:02:02,590 --> 00:02:09,039
So without self loop and with just a graph is called this simple graph the next.

32
00:02:09,039 --> 00:02:10,270
Let us look at this graph.

33
00:02:10,449 --> 00:02:11,950
There are no direct energies.

34
00:02:12,370 --> 00:02:18,610
This are non-direct that are just or undirected or just so it is called as non-direct or undirected

35
00:02:18,610 --> 00:02:19,000
graph.

36
00:02:19,300 --> 00:02:21,480
And in short, we can also call it as graph.

37
00:02:21,650 --> 00:02:26,520
Graph means undirected dictograph means directed graph.

38
00:02:26,530 --> 00:02:31,730
Then in a undirected graph on director graph, the number of a disconnected or vertex is called.

39
00:02:31,730 --> 00:02:33,160
That's a degree just to degree.

40
00:02:33,160 --> 00:02:34,630
There is no end degree or degree.

41
00:02:34,990 --> 00:02:35,940
It is just a degree.

42
00:02:36,070 --> 00:02:41,290
And if there's an X from Vertex one to two, for example, there is an edge, then it is taken as in

43
00:02:41,290 --> 00:02:42,190
both directions.

44
00:02:42,200 --> 00:02:45,850
So it is assumed as it is from one to two also and to the world.

45
00:02:46,000 --> 00:02:48,720
What it is not a privilege, it's a non-direct index.

46
00:02:48,730 --> 00:02:52,000
So it is counted either way one to two as well as two to one.

47
00:02:52,170 --> 00:02:56,290
Then coming to this graph, see here I have graph of seven.

48
00:02:56,290 --> 00:02:57,220
What is this?

49
00:02:57,700 --> 00:03:00,280
And there are two pieces of this graph.

50
00:03:00,280 --> 00:03:01,380
There are two components.

51
00:03:01,540 --> 00:03:04,090
So this is not connected graph.

52
00:03:04,370 --> 00:03:05,440
It's not connected.

53
00:03:06,070 --> 00:03:08,620
There are more than one pieces, more than one component.

54
00:03:08,630 --> 00:03:09,660
So they are not connected.

55
00:03:09,730 --> 00:03:11,530
This is non connected graph.

56
00:03:12,110 --> 00:03:17,860
And if suppose I draw an X and connected them now, the graph has to be connected.

57
00:03:18,070 --> 00:03:18,880
It is connected.

58
00:03:19,270 --> 00:03:22,030
But you can see that there are two components connected here.

59
00:03:22,160 --> 00:03:24,940
So we also call it s connected components.

60
00:03:25,060 --> 00:03:30,570
A graph is having connected components, more than one components which are connected then the non-moving

61
00:03:31,000 --> 00:03:35,800
in this graph after having this edge if I remove six.

62
00:03:36,810 --> 00:03:43,470
One deck, six along with the edges, then again, it will get divided into two components see me if

63
00:03:43,470 --> 00:03:46,050
I remove one six four along with these are.

64
00:03:46,560 --> 00:03:49,960
Then again, graphs will get divided into multiple components.

65
00:03:50,160 --> 00:03:56,910
So if there are any worklessness in the graph whose removal will split the graph into multiple components,

66
00:03:57,210 --> 00:04:02,520
such words are called articulation points of articulation point.

67
00:04:02,820 --> 00:04:09,630
So Ford is also an articulation point for as well as six butat articulation points that in that same

68
00:04:09,630 --> 00:04:12,450
graph, if I add one more inch.

69
00:04:13,500 --> 00:04:19,560
No, there are no articulation points, if I remove any vertex, then Graf will not split into multiple

70
00:04:19,560 --> 00:04:20,160
components.

71
00:04:20,170 --> 00:04:21,730
It remain as a single component.

72
00:04:22,140 --> 00:04:24,980
So that is by connected components.

73
00:04:24,990 --> 00:04:31,080
You see, it means there may be components, but they are strongly connected to and you can see that

74
00:04:31,290 --> 00:04:32,840
it was earlier not connected.

75
00:04:32,850 --> 00:04:35,100
I have added one inch, so it's connected.

76
00:04:35,340 --> 00:04:40,710
One more night is a stronger it cannot be broken into components by removing one vertex.

77
00:04:40,710 --> 00:04:43,460
So it is byte connected components.

78
00:04:43,470 --> 00:04:49,500
So you can simply call disconnected graph or byte connected component components, not just this one

79
00:04:49,800 --> 00:04:56,190
does a director graph and in this directed graph from any vertex you can start and you can reach on

80
00:04:56,580 --> 00:05:01,440
all other words, since you can go to, to or from there you can go to five or you can go to four,

81
00:05:01,470 --> 00:05:01,920
then three.

82
00:05:02,130 --> 00:05:04,140
If you start from four, you can go to two.

83
00:05:04,140 --> 00:05:08,240
Also from that you can reach five, you can go to three and from there you can go to one.

84
00:05:08,610 --> 00:05:11,640
So from every vertex we can reach all of the vertices.

85
00:05:11,640 --> 00:05:20,490
It means there is a part between every pair of bodices or from any starting vertex you can reach all

86
00:05:20,490 --> 00:05:20,970
other.

87
00:05:20,970 --> 00:05:21,830
What is this?

88
00:05:21,840 --> 00:05:28,560
If directed graph is satisfying this property, then it is called as strongly connected graph so strongly

89
00:05:28,560 --> 00:05:35,220
is the term used for directed graphs so same way here from one vertex we can reach all other words so

90
00:05:35,220 --> 00:05:36,330
that side is connected.

91
00:05:36,480 --> 00:05:39,900
So in non-direct or graph we just use the term connector.

92
00:05:40,110 --> 00:05:42,140
But here we say strongly connected.

93
00:05:42,370 --> 00:05:49,050
Suppose this edge was in this direction then we cannot reach you, but it is connected a graph only

94
00:05:49,080 --> 00:05:55,650
that is connected, but it is not strongly connected because we cannot reach on all Waters's from one.

95
00:05:55,650 --> 00:05:57,340
If I start, I cannot go on five.

96
00:05:57,360 --> 00:06:02,160
So when we have an edge like this then it is strongly connected graph.

97
00:06:02,280 --> 00:06:09,690
The next thing we will learn about Barpak is set of all the what is says in between a pair of attacks

98
00:06:09,990 --> 00:06:14,340
like from one to five, the pontiff's one to two, then go to five.

99
00:06:14,520 --> 00:06:20,880
Now similarly here, apart from one two seven this you can go to two, then five, then seven does it.

100
00:06:20,880 --> 00:06:22,320
But this is about.

101
00:06:23,690 --> 00:06:29,450
Or else I can go from one to three, then three to four, four, two, two, two, two, five, five

102
00:06:29,450 --> 00:06:36,260
to six, then seven, but can be off with less number of edges, also more number of which is also shorter

103
00:06:36,260 --> 00:06:39,910
length, part or longest, but also possible support.

104
00:06:39,920 --> 00:06:45,170
The set of autism's, which are connecting a bit of what the next cycle.

105
00:06:45,620 --> 00:06:51,320
If there is a part that is starting from somewhere next, I'm going around a few set of modifiers and

106
00:06:51,320 --> 00:06:52,340
again reaching back.

107
00:06:53,030 --> 00:06:57,770
Then it is called the cycle, like from one to two, two two five, five to four, then four to three,

108
00:06:57,770 --> 00:07:00,590
then three to one, two, five, four, then two.

109
00:07:00,740 --> 00:07:01,520
It's a cycle.

110
00:07:01,670 --> 00:07:03,320
And here also we have cycles.

111
00:07:03,350 --> 00:07:05,270
This is not directly, but still we have cycle.

112
00:07:05,270 --> 00:07:06,680
One, two, four, three, one.

113
00:07:07,370 --> 00:07:11,510
So cycle is a circular part or a circuit.

114
00:07:11,520 --> 00:07:16,220
We can also say that starting from one vertex and ending at the same LUKACS.

115
00:07:16,340 --> 00:07:21,380
The next is this graph directed a cyclic graph.

116
00:07:21,590 --> 00:07:26,240
DG Dagg this are directed graph and there are no cycles from this one.

117
00:07:26,480 --> 00:07:27,470
There are no cycles.

118
00:07:27,800 --> 00:07:31,790
If you start from any vertex, you cannot reach that same vertex in any way.

119
00:07:31,820 --> 00:07:33,290
You select any starting vertex.

120
00:07:33,500 --> 00:07:37,190
So that is directed cyclic graph.

121
00:07:37,220 --> 00:07:41,960
There is no cycle and the last thing is this is directed acyclic graph.

122
00:07:41,960 --> 00:07:46,490
We can arrange all the words Sessa linearly, linearly in a single line.

123
00:07:46,490 --> 00:07:52,700
Those Watterson's are arranging a single line such that the edges are going only in forward direction.

124
00:07:53,360 --> 00:07:54,890
They are just are not coming back.

125
00:07:55,550 --> 00:07:59,810
If you can arrange the words that that just are going on, lean forward direction.

126
00:07:59,810 --> 00:08:03,710
Then it is called topology ordering of what is this?

127
00:08:04,580 --> 00:08:08,710
This is possible only in directed acyclic graph.

128
00:08:08,720 --> 00:08:09,380
So that's all.

129
00:08:09,650 --> 00:08:14,810
These are various things we were supposed to know about graphs, incoming videos we learn more about

130
00:08:14,810 --> 00:08:17,990
graph SIGGRAPH are very useful data structure.

131
00:08:18,320 --> 00:08:22,100
Many problems, various problems can be represented in the form of a graph.

132
00:08:22,430 --> 00:08:25,580
And if we can solve a graph, then we can solve a problem.

133
00:08:25,700 --> 00:08:27,500
So problems are converted into graph.

134
00:08:27,500 --> 00:08:32,450
And so if you want to solve any challenge, then you can convert that challenge in the form of a graph

135
00:08:32,750 --> 00:08:34,400
and find a solution in the graph.

136
00:08:34,640 --> 00:08:36,580
Then you find a solution for a problem.

137
00:08:36,590 --> 00:08:39,440
So graph is very useful data structure and problem solving.

138
00:08:40,760 --> 00:08:43,570
So let us learn more about Graff's incoming videos.

