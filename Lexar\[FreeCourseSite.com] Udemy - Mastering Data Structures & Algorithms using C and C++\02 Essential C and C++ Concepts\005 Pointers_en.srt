1
00:00:00,090 --> 00:00:02,570
In this video we will learn about pointers.

2
00:00:02,580 --> 00:00:07,440
So, first of all, I'll give you the definition of a pointer. What does it mean by pointer? and Why we require

3
00:00:07,440 --> 00:00:08,280
Why we require pointers?

4
00:00:08,280 --> 00:00:11,300
Then I'll show you how to declare and how to initialize,

5
00:00:11,520 --> 00:00:13,650
What does it mean by dereferencing?

6
00:00:13,650 --> 00:00:19,170
And we'll also learn about dynamic memory allocation. So, Let us start with the definition.

7
00:00:19,240 --> 00:00:27,840
See, the definition of a pointer. Pointer is an address variable that is meant for storing address of data,

8
00:00:28,830 --> 00:00:37,100
not the data itself; normal variables are data variables, but the pointers are address variables, and Pointers

9
00:00:37,100 --> 00:00:39,590
are used for indirectly accessing the data.

10
00:00:40,460 --> 00:00:43,700
So the question is, Why do you need to access data indirectly?

11
00:00:44,480 --> 00:00:47,500
So let us understand why actually we need pointers.

12
00:00:47,510 --> 00:00:49,880
Then we will see how to declare and use them.

13
00:00:49,880 --> 00:00:53,510
So first of all see this picture again I'm drawing it here.

14
00:00:53,630 --> 00:00:55,170
See this is the main memory.

15
00:00:55,310 --> 00:00:57,020
Main memory.

16
00:00:57,830 --> 00:00:58,510
Right.

17
00:00:58,520 --> 00:01:03,590
And this is the CPU which will execute our programs. So, how this main memory is utilized?

18
00:01:03,590 --> 00:01:06,010
Already this picture we're following it.

19
00:01:06,080 --> 00:01:08,320
This is divided into three parts here.

20
00:01:08,330 --> 00:01:10,250
Actually our program resides.

21
00:01:10,280 --> 00:01:11,790
So I'm writing main function.

22
00:01:11,930 --> 00:01:14,130
It will be having more of the functions.

23
00:01:14,180 --> 00:01:20,970
Now, this area, See this is a stack, so actually stack will be on the top, right, but I'm showing it here.

24
00:01:21,110 --> 00:01:23,930
Then the other part of the memories heap memory.

25
00:01:23,930 --> 00:01:30,420
So the main memory is divided into three sections and utilized this is code section.

26
00:01:30,510 --> 00:01:37,250
This is code section, and this is stack, and this is heap. So Code section, and, this will be stack and

27
00:01:37,250 --> 00:01:42,440
this will be heap, but I usually show here as the stack, so it's easy for understanding. Anyway,

28
00:01:42,470 --> 00:01:45,880
There are three portions of memory, code section, stack and heap.

29
00:01:45,950 --> 00:01:50,500
So the program will utilize the main memory by dividing into three sections.

30
00:01:50,540 --> 00:01:55,490
So this program can directly access these two areas.

31
00:01:55,550 --> 00:02:03,580
What are those? code section and stack; program will not automatically access heap, right. The policy for program is

32
00:02:03,590 --> 00:02:05,000
It will not directly access it.

33
00:02:05,390 --> 00:02:09,419
So heap memory is external to the program, outside the program.

34
00:02:10,190 --> 00:02:15,200
So program doesn't directly access, then how to access that? From that it needs a pointer.

35
00:02:15,440 --> 00:02:20,360
Yes, one of the reason of using pointer is accessing heap memory.

36
00:02:20,750 --> 00:02:26,270
So program should have a pointer within itself and with that point that it can access anything

37
00:02:26,270 --> 00:02:27,110
in the heap.

38
00:02:27,110 --> 00:02:32,590
So I may have an array or anything inside heap and the program can access it.

39
00:02:32,690 --> 00:02:39,260
So, for accessing this memory from the program we need a pointer. So, pointer is useful for accessing the

40
00:02:39,260 --> 00:02:42,270
resources that are outside the program.

41
00:02:42,290 --> 00:02:49,370
So this is one example, heap is one thing. Then, if the files are on the hard disk, now for accessing

42
00:02:49,430 --> 00:02:55,340
a file, program cannot access this hard disk files directly, because hard disk is external or files are

43
00:02:55,430 --> 00:03:00,650
external to a program, so far that, it needs a pointer for accessing and that pointer should be a file

44
00:03:00,680 --> 00:03:08,600
type and we can access that file. Then, a program may be accessing a keyboard, a program may be accessing

45
00:03:08,960 --> 00:03:15,950
a monitor, a program may be accessing internet or network connection, all these things are external to

46
00:03:15,950 --> 00:03:23,040
a program, so all these things can be accessed with the help of pointers. So, one major usage of pointer

47
00:03:23,090 --> 00:03:27,500
is accessing the resources which are outside the program.

48
00:03:27,660 --> 00:03:37,590
So, I will list it, see pointers are used for accessing heap memory, right, and for accessing resources

49
00:03:40,590 --> 00:03:48,420
and pointers are used for parameter passing also, this I will be explaining

50
00:03:48,520 --> 00:03:53,620
afterwards, how pointers are useful for passing parameters.

51
00:03:53,640 --> 00:03:59,730
So these are the major usages of pointer. Accessing heap, which is like a resource accessing, other resources

52
00:04:00,240 --> 00:04:01,610
or parameter passing.

53
00:04:02,610 --> 00:04:07,610
So, now next, Let us learn how to declare a pointer, how to initialize and how to use it.

54
00:04:07,620 --> 00:04:12,430
So by taking some small example, I will show you. This is our data variable.

55
00:04:13,070 --> 00:04:16,730
I'm declaring a and also initialize it, right.

56
00:04:17,100 --> 00:04:22,460
So this is a data variable, and normally we declare variables like this. 

57
00:04:22,460 --> 00:04:24,830
Then, second thing, pointer.

58
00:04:24,960 --> 00:04:28,580
This is the address variable, right.

59
00:04:28,580 --> 00:04:36,340
This is an address variable, so address variables are called as pointers in C C++ .

60
00:04:36,350 --> 00:04:38,330
Normally we declare the variables like this.

61
00:04:38,330 --> 00:04:40,520
Suppose this is inside a main function,

62
00:04:40,520 --> 00:04:41,950
I'm writing it as a function, okay,

63
00:04:42,060 --> 00:04:48,650
So this variable, it will occupy the memory inside the stack frame of our main function, inside the

64
00:04:48,650 --> 00:04:50,630
stack, like it is having value 10.

65
00:04:51,080 --> 00:04:53,840
So if I draw its bigger picture here,

66
00:04:53,840 --> 00:04:55,760
So, like, this is there. Suppose,

67
00:04:56,060 --> 00:04:59,810
This is taking 2 bytes, integer is taking 2 bytes, so it's as

68
00:04:59,810 --> 00:05:01,850
200 and 201.

69
00:05:01,880 --> 00:05:04,520
There are two bytes in which this value is stored.

70
00:05:04,580 --> 00:05:09,440
One is 200 and other is 201, right, so 2 bytes.

71
00:05:09,440 --> 00:05:11,330
See that is the reason to show only 2 bytes,

72
00:05:11,330 --> 00:05:19,070
I am saying integer is taking 2 bytes, so 2 addresses. Now, this is a pointer, this pointer I will initialize it,

73
00:05:19,220 --> 00:05:21,220
So, I will initialize it in the next line.

74
00:05:21,250 --> 00:05:23,750
p = &a ;

75
00:05:23,900 --> 00:05:26,520
So this will store the address of a.

76
00:05:26,630 --> 00:05:27,270
So what is this p?

77
00:05:27,270 --> 00:05:27,940
So what is this p?

78
00:05:28,050 --> 00:05:31,100
p will also occupy memory, pointer is also variable.

79
00:05:31,100 --> 00:05:32,670
This will also occupy memory.

80
00:05:32,780 --> 00:05:37,580
So this is created inside the stack frame of the same main function.

81
00:05:38,390 --> 00:05:43,450
If I show it diagrammatically, it looks like this. This pointer will also take 2 bytes.

82
00:05:43,490 --> 00:05:51,060
So let us say the address of this pointer is 210 and 211, just I am assuming a random number,

83
00:05:51,530 --> 00:05:55,480
Don't confuse that, the next should be 202. Whatever it may be,

84
00:05:55,490 --> 00:06:00,530
I am saying it is as 210 and 211. Now, when I say &a,

85
00:06:00,530 --> 00:06:04,880
This will have the address of this variable, So it is pointing to this one.

86
00:06:06,080 --> 00:06:12,880
So here, inside the memory if you see this is pointing to this one. So that pointer is pointing there, right.

87
00:06:13,450 --> 00:06:15,210
Now focus on the syntax.

88
00:06:15,220 --> 00:06:18,010
This is a variable, this is a pointer variable.

89
00:06:18,070 --> 00:06:22,030
This is initialization, address of a variable is given to p.

90
00:06:22,040 --> 00:06:30,640
Now, I want to access this 10, So I want to print, so I can say printf ("%d", a) ;

91
00:06:30,970 --> 00:06:32,830
So, this will print the value. Then,

92
00:06:33,100 --> 00:06:35,970
I want to access same thing by using pointer.

93
00:06:36,040 --> 00:06:37,840
So, from pointer I want to access it.

94
00:06:37,900 --> 00:06:47,520
So again, I will say  printf ("%d",  and if I say p, it will be taking 200, but no I want this

95
00:06:47,530 --> 00:06:48,030
data.

96
00:06:48,250 --> 00:06:52,140
So say *p.

97
00:06:52,880 --> 00:06:58,900
So, this is declaration, and this is assignment or initialization,

98
00:06:58,930 --> 00:07:05,470
That is, it is initialized with some value, and this is dereferencing, this portion is dereferencing.

99
00:07:05,470 --> 00:07:11,330
So when you are dereferencing, you have to use *, and declaring you have to use a * and initialization

100
00:07:11,350 --> 00:07:14,020
directly we can write p.

101
00:07:14,050 --> 00:07:18,350
So you have to get familiar with the syntax, declaration, initialization, dereferencing.

102
00:07:18,790 --> 00:07:22,840
So this how I have already taken a data variable and making a pointer point on it.

103
00:07:22,840 --> 00:07:29,650
In this example I'm not using pointer to access the heap memory, but next I will show you how to access

104
00:07:29,830 --> 00:07:31,720
heap memory using a pointer.

105
00:07:31,740 --> 00:07:35,770
I will declare a pointer variable. First of all, this is a pointer variable.

106
00:07:36,970 --> 00:07:44,170
So this, whenever you declare something, that definitely takes place in stack; stack frame of that particular

107
00:07:44,170 --> 00:07:46,680
function. So, p is created here,

108
00:07:47,990 --> 00:07:48,460
Right.

109
00:07:48,520 --> 00:07:54,160
Whenever you declare it will be inside stack. Remember, every variable declared, when you declare the variable

110
00:07:54,220 --> 00:07:55,900
it will be inside stack.

111
00:07:55,900 --> 00:07:58,860
Now I want to allocate memory in heap.

112
00:07:58,870 --> 00:08:02,390
So I want to create an array of size of 5 in the heap.

113
00:08:02,380 --> 00:08:04,580
So, how to get memory in heap?

114
00:08:05,230 --> 00:08:08,200
In C language the function name is malloc ( ).

115
00:08:08,320 --> 00:08:11,430
When you write malloc ( ), then only you get the memory in heap.

116
00:08:11,710 --> 00:08:16,180
So here I will write down, malloc ( ) .

117
00:08:16,300 --> 00:08:24,820
So for this I have to include some library, that is, #include  , right.

118
00:08:24,820 --> 00:08:29,010
Now I can use this function malloc ( )

119
00:08:29,020 --> 00:08:32,990
Now here, I should mention the size, so malloc ( ) 

120
00:08:33,010 --> 00:08:35,620
take the size, how much memory you want.

121
00:08:35,620 --> 00:08:38,080
So you have to mention in terms of bytes.

122
00:08:38,140 --> 00:08:41,590
So I want 5 integer so, 5.

123
00:08:41,590 --> 00:08:45,470
Then what is the size of the integer we are assuming it as 2 bytes.

124
00:08:45,490 --> 00:08:48,260
So let the compiler decide it.

125
00:08:48,420 --> 00:08:54,160
see, we are assuming it, but it may be taking 4 bytes, or 2 bytes, so let the compiler decide the size,

126
00:08:54,490 --> 00:08:59,840
so we can use the operator, called the sizeof operator and to that

127
00:08:59,840 --> 00:09:07,090
operator, I will pass int, so it will give the size of an integer, size of integer, if it is 2 bytes then

128
00:09:07,080 --> 00:09:14,590
2*5, total 10 bytes of memory this malloc ( ) function will allocate. So, it will allocate total 5 integers,

129
00:09:15,700 --> 00:09:24,030
right. Now, that memory is allocated. Suppose the starting address of this is 5000, for example,

130
00:09:25,170 --> 00:09:33,240
First byte address is 5000, and so on. Then, that should be pointed by a pointer because we cannot access

131
00:09:33,240 --> 00:09:35,530
it unless we have a pointer.

132
00:09:35,760 --> 00:09:38,990
So I will assign this to a pointer.

133
00:09:39,120 --> 00:09:42,410
Now this malloc ( ) function returns void pointer.

134
00:09:42,420 --> 00:09:46,270
So we have to type-caste it and say it is integer pointer.

135
00:09:46,380 --> 00:09:49,410
Now this line will allocate heap memory.

136
00:09:49,440 --> 00:09:53,760
Remember, whenever you say malloc then only it will allocate in heap.

137
00:09:53,760 --> 00:09:56,060
So this p, will be pointing on this one.

138
00:09:56,310 --> 00:09:58,360
So p will get the address 5000.

139
00:09:59,240 --> 00:10:01,490
So, it is just like something is there in heap,

140
00:10:01,680 --> 00:10:09,160
now, pointer is holding it, right. It is just holding one edge of that array that is 5000.

141
00:10:09,420 --> 00:10:12,530
Now using that pointer we can access the entire array.

142
00:10:13,110 --> 00:10:19,570
So this is how heap memory is created. And how to do the same thing in C++?

143
00:10:19,570 --> 00:10:26,980
So just we have to say  new. new is an operator in C++ of type Integer and say size, 5.

144
00:10:27,070 --> 00:10:29,120
So in C++ it is very simple.

145
00:10:29,140 --> 00:10:36,490
This is C++, and this C language. C language syntax is a little bit lengthy and C++ is very simple.

146
00:10:36,520 --> 00:10:38,510
So while explanation, I maybe writing new

147
00:10:38,520 --> 00:10:45,460
most of the time, right, on whiteboard; But actually in C language, I have to use malloc ( ) function for allocating

148
00:10:45,550 --> 00:10:46,600
memory in heap.

149
00:10:46,750 --> 00:10:51,100
So, this is how pointers are used for creating heap memory.

150
00:10:51,100 --> 00:10:56,560
So this was the introduction of a pointer. In the coming videos, we will see how to have a pointer upon

151
00:10:56,560 --> 00:10:59,920
an array and how to have a pointer upon a structure.

152
00:10:59,920 --> 00:11:01,450
We'll learn about all these things.

