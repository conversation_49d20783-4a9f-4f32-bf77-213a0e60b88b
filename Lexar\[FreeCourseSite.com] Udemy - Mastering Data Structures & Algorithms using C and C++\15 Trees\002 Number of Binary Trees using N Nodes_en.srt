1
00:00:00,120 --> 00:00:03,880
The topic is number of binary trees for a given set of nodes.

2
00:00:04,080 --> 00:00:11,100
It means it's also a number of nodes are given like three nodes are given, then using three knows how

3
00:00:11,100 --> 00:00:14,250
many different binary trees can be generated.

4
00:00:14,400 --> 00:00:15,540
We are going to learn that.

5
00:00:15,720 --> 00:00:22,290
We will learn about two types of nodes that is on the label nodes and labeled nodes.

6
00:00:22,770 --> 00:00:27,520
Let us start with unlabelled the nor does the example of unlabelled n.

7
00:00:27,570 --> 00:00:28,800
These are empty nodes.

8
00:00:28,980 --> 00:00:34,540
Labeling is not the alphabet's ABC or numbers one, but nothing is written in that.

9
00:00:34,920 --> 00:00:36,960
So these are the labels.

10
00:00:37,260 --> 00:00:42,570
So if a three nodes are given, how many different boundaries can be generated, I will draw them and

11
00:00:42,570 --> 00:00:50,610
show you this is one sheep fust, one second sheep that is left and right, third shape with the three

12
00:00:50,610 --> 00:00:51,000
nodes.

13
00:00:51,150 --> 00:00:53,310
Now next will be a mirror image of this.

14
00:00:53,310 --> 00:00:54,630
One is the fourth one.

15
00:00:55,020 --> 00:00:57,840
The next will be mirror image of these are five different trees.

16
00:00:58,170 --> 00:01:03,870
So if there are three nodes, given the number of trees, if it is a function, then the value of this

17
00:01:03,870 --> 00:01:04,890
function five.

18
00:01:05,099 --> 00:01:11,550
So if there are three nodes, we can generate five different shared binary trees five, one, two,

19
00:01:11,550 --> 00:01:13,470
three, four or five nights.

20
00:01:13,650 --> 00:01:17,850
If there are four nodes, then how many differentiate binary trees can be generated.

21
00:01:18,450 --> 00:01:19,590
I'll take four nodes.

22
00:01:19,590 --> 00:01:20,940
Number of nodes are four.

23
00:01:21,210 --> 00:01:22,770
These are four unlabeled nodes.

24
00:01:23,130 --> 00:01:25,550
I will draw different shaped by entries here.

25
00:01:25,680 --> 00:01:27,240
So here I have generated few.

26
00:01:27,600 --> 00:01:29,430
Look at them first.

27
00:01:29,430 --> 00:01:33,060
One second, third, fourth, fifth, sixth, seventh.

28
00:01:33,780 --> 00:01:34,920
I have drawn seven.

29
00:01:35,610 --> 00:01:40,410
Then I can take the mirror images and I can draw seven more.

30
00:01:41,260 --> 00:01:42,150
This is one shape.

31
00:01:42,390 --> 00:01:45,810
So this is left skewed that also I can have right skewed.

32
00:01:46,260 --> 00:01:46,620
Right.

33
00:01:46,800 --> 00:01:50,760
So for each I can have a mirror image so seven more I can draw.

34
00:01:51,000 --> 00:01:52,200
So I will complete that.

35
00:01:52,920 --> 00:01:55,470
Now I have all fourteen trees.

36
00:01:55,680 --> 00:02:02,220
One, two, three, four, five, six, seven, eight, nine, 10, 11, 12, 13, 14, 14 different

37
00:02:02,220 --> 00:02:03,840
shapes of binary trees.

38
00:02:04,110 --> 00:02:10,289
So it means if number of nodes are four, then we can have 14 different shaped binary trees.

39
00:02:10,620 --> 00:02:14,240
So for three nodes, record number of trees are five and four four.

40
00:02:14,250 --> 00:02:20,370
And also the number of trees has a fourteen, then four five knows how many it will be for six nodes.

41
00:02:20,370 --> 00:02:23,460
How many will be shall I draw every time and check them?

42
00:02:23,940 --> 00:02:25,860
No, just for observation.

43
00:02:25,860 --> 00:02:27,260
I have drawn that to explain you.

44
00:02:27,270 --> 00:02:29,070
What does it mean by number of trees.

45
00:02:29,070 --> 00:02:30,930
I have drawn them right now.

46
00:02:30,930 --> 00:02:32,640
There must be some formula for this one.

47
00:02:32,640 --> 00:02:33,930
Yes, there is some formula.

48
00:02:34,230 --> 00:02:44,070
That formula is number of trees for a given number of nodes are to M C and by and plus one.

49
00:02:45,750 --> 00:02:50,710
This is the famous formula, the name of this formula is catalog number.

50
00:02:51,000 --> 00:02:54,270
There's a known formula to NCM by and one.

51
00:02:54,390 --> 00:03:00,460
So if any orders are given, then these many number of binary trees can be generated.

52
00:03:00,870 --> 00:03:02,250
So four, five, how many?

53
00:03:02,400 --> 00:03:09,930
Let me calculate these five will be two do five C five by five plus one.

54
00:03:11,010 --> 00:03:16,460
This is N C five by six n c five by six.

55
00:03:17,100 --> 00:03:23,170
And this is an eight or nine into eighteen to seven in the six divided by five to four in the three

56
00:03:23,470 --> 00:03:25,380
to one holding divided by six.

57
00:03:25,470 --> 00:03:30,010
Now let us divide this and get down so six six gets cancelled.

58
00:03:30,060 --> 00:03:36,480
Five to ten is gone for two's eight and three threes nine.

59
00:03:36,790 --> 00:03:38,670
So this is three to two in two.

60
00:03:38,670 --> 00:03:39,940
Seven is remaining here.

61
00:03:40,110 --> 00:03:41,450
So this is 42.

62
00:03:41,820 --> 00:03:44,510
So these five is 42.

63
00:03:44,610 --> 00:03:50,010
So for any given number of N, you can find out how many different shape entries can be generated,

64
00:03:50,280 --> 00:03:50,820
if at all.

65
00:03:50,820 --> 00:03:55,470
You want to try out and see how those trees looks like, then you have to draw all of them.

66
00:03:55,680 --> 00:03:56,820
That may not be practical.

67
00:03:56,820 --> 00:03:59,220
So have just shown for three nodes and four nodes.

68
00:03:59,940 --> 00:04:01,980
No more observation I have to do here.

69
00:04:02,520 --> 00:04:06,360
See, then three nodes are given what could be the maximum height.

70
00:04:06,360 --> 00:04:07,760
Height starts from zero on what.

71
00:04:07,770 --> 00:04:10,660
So zero one two zero one two.

72
00:04:10,710 --> 00:04:15,620
So the maximum height possible to the strings of height two and two.

73
00:04:15,630 --> 00:04:16,829
And this is not at all.

74
00:04:16,839 --> 00:04:18,380
This is one to two.

75
00:04:18,839 --> 00:04:22,780
So how many trees are there with the maximum height.

76
00:04:23,130 --> 00:04:24,480
One, two, three, four.

77
00:04:25,080 --> 00:04:28,760
So the number of trees with the maximum height are four.

78
00:04:28,770 --> 00:04:30,010
So that is two square.

79
00:04:31,530 --> 00:04:34,800
This is for how many nodes and equal to three nodes.

80
00:04:34,800 --> 00:04:38,970
Then what about an equal to four nodes, number of trees at maximum height.

81
00:04:38,970 --> 00:04:40,350
Let us count them zero.

82
00:04:40,350 --> 00:04:41,190
One, two, three.

83
00:04:41,200 --> 00:04:43,920
Yes, this is maximum height because there are four nodes for the height.

84
00:04:43,920 --> 00:04:45,060
Should be maximum tree.

85
00:04:45,330 --> 00:04:47,640
So one, two, three, four.

86
00:04:47,880 --> 00:04:48,810
This not maximum.

87
00:04:48,840 --> 00:04:51,430
No, no, no, no, no.

88
00:04:51,450 --> 00:04:51,900
Yes.

89
00:04:52,300 --> 00:04:53,890
Five, six, seven, eight.

90
00:04:54,270 --> 00:04:54,850
So eight.

91
00:04:54,850 --> 00:04:57,140
The trees are there with the maximum height.

92
00:04:58,230 --> 00:04:59,330
So this is too cute.

93
00:05:00,000 --> 00:05:01,730
So four and equal to five.

94
00:05:01,740 --> 00:05:02,640
How much it will be.

95
00:05:03,720 --> 00:05:05,790
You can get some sixteen.

96
00:05:05,800 --> 00:05:07,160
Yes, this is too four.

97
00:05:07,500 --> 00:05:12,350
So for any m how many trees will be added to power and minus one.

98
00:05:12,630 --> 00:05:18,510
So we have learned how many different shaped by trees can be generated for given number of nodes.

99
00:05:18,510 --> 00:05:20,130
So we don't have to generate the tree.

100
00:05:20,370 --> 00:05:21,680
We can use the formula.

101
00:05:21,990 --> 00:05:23,130
And one more observation.

102
00:05:23,130 --> 00:05:26,910
We then how many binary trees are possible with the maximum height.

103
00:05:27,210 --> 00:05:29,370
So that is too poor and the minus one.

104
00:05:29,490 --> 00:05:33,600
Now let's let us generate one more formula for Coca-Cola number.

105
00:05:33,870 --> 00:05:36,990
So I will write down two values of a catalog number.

106
00:05:37,260 --> 00:05:41,880
Then from that, let's see how we can come up with one more formula for catalog number.

107
00:05:42,030 --> 00:05:48,930
So first of all, I will take the table of values for catalog no see if is given then catalog number

108
00:05:49,260 --> 00:05:57,540
in that we know the formula to win C and by and plus one see here value of N and this is the catalog

109
00:05:57,540 --> 00:05:57,960
number.

110
00:05:58,140 --> 00:06:01,670
So I will take the values of N and write on the catalog number of items.

111
00:06:01,680 --> 00:06:03,900
Also see if it is zero.

112
00:06:04,200 --> 00:06:12,390
Then if we put zero in this formula you get one, then if it is one then it is still one four, two,

113
00:06:12,690 --> 00:06:14,070
two, four, three.

114
00:06:14,220 --> 00:06:16,560
We know the answer directly, just not result.

115
00:06:16,800 --> 00:06:19,020
Then for four it is fourteen.

116
00:06:19,350 --> 00:06:21,750
For five it is forty two.

117
00:06:22,050 --> 00:06:25,530
So for six I will find out using a different method.

118
00:06:25,530 --> 00:06:26,880
I will not use this formula.

119
00:06:27,360 --> 00:06:28,590
So just watch this one.

120
00:06:29,550 --> 00:06:31,440
I want done so for six.

121
00:06:32,370 --> 00:06:41,910
So for that I will multiply these values one into forty two plus one and two fourteen plus twenty five

122
00:06:41,910 --> 00:06:52,250
plus five in two to see it was two will to fight now five in the two plus fourteen and one plus 42 and

123
00:06:52,440 --> 00:06:52,800
one.

124
00:06:53,400 --> 00:06:55,920
This gives the answer for six.

125
00:06:56,130 --> 00:06:59,490
So the result of this one is one thirty two.

126
00:07:00,860 --> 00:07:07,850
Let us use the formula for the six and check whether our answer is correct or not, let us verify these

127
00:07:07,850 --> 00:07:15,800
six is two to six to NCM right during the six season six by six plus one.

128
00:07:16,610 --> 00:07:20,570
So this is 12 C six by seven.

129
00:07:22,190 --> 00:07:30,380
This is this is the numerator than the denominator will be seven, let us cancel these seven seven,

130
00:07:30,380 --> 00:07:40,340
just cancel Sixtus tollgates, cancel five to ten for twos, eight three threes, nine remaining is

131
00:07:40,370 --> 00:07:46,410
11 and two, two and two, three into two.

132
00:07:46,970 --> 00:07:50,790
So this is level two, two, three, six, six to 12.

133
00:07:51,020 --> 00:07:54,800
So 11 into 12, 132.

134
00:07:55,900 --> 00:08:01,840
So, yes, we got the correct answer by substituting in this formula also we the correct answer.

135
00:08:02,140 --> 00:08:07,600
So how I got this one, I multiply this one with this one and this one and this one, then this, then

136
00:08:07,600 --> 00:08:09,540
this, then this, then this tool.

137
00:08:10,090 --> 00:08:12,100
So let us prepare a formula for that one.

138
00:08:12,220 --> 00:08:13,680
So this is three six.

139
00:08:13,690 --> 00:08:24,250
So this is P0 in two, 42 is the five B five plus the one in two to four.

140
00:08:24,910 --> 00:08:28,030
Then to sort of devalues.

141
00:08:28,030 --> 00:08:28,660
We are multiplying.

142
00:08:28,660 --> 00:08:28,950
Right.

143
00:08:29,260 --> 00:08:41,210
So to do it to be three then the tree in duty two then T four into P1 then if I loop zero.

144
00:08:41,500 --> 00:08:45,670
So these are the Tom and E is the product of two values.

145
00:08:45,680 --> 00:08:47,920
So this can be done in general form.

146
00:08:47,920 --> 00:08:52,090
TNM is a summation of two values.

147
00:08:52,990 --> 00:08:55,130
T of something, the P of something.

148
00:08:55,150 --> 00:08:57,150
So what is this starting from zero.

149
00:08:57,160 --> 00:09:00,180
One, two, three, four, up to five.

150
00:09:00,520 --> 00:09:03,470
And this is five, four, three, two, one.

151
00:09:03,730 --> 00:09:06,630
So one sided is increasing, the other parties decreasing.

152
00:09:06,640 --> 00:09:07,510
So this is increasing.

153
00:09:07,520 --> 00:09:08,300
This is decreasing.

154
00:09:08,590 --> 00:09:14,980
So if I take I do starting from one to N then I should say I'm minus one because I have one first time

155
00:09:14,980 --> 00:09:15,640
which would be zero.

156
00:09:15,940 --> 00:09:20,650
And this is in the minus I see as we got the formula.

157
00:09:21,010 --> 00:09:31,690
So Catala number to N as to N, C and by and plus one, two and C and by and plus one.

158
00:09:31,690 --> 00:09:41,220
This is one formula and the other one is summation of idex values from one grand I minus one in to be

159
00:09:41,230 --> 00:09:42,740
off and minus eight.

160
00:09:43,240 --> 00:09:45,670
So the catalog number can also be written like this.

161
00:09:45,880 --> 00:09:49,980
This is a combination formula and this is a recursive formula.

162
00:09:49,990 --> 00:09:53,390
So that's all about the formula Keadilan formula.

163
00:09:53,650 --> 00:09:55,150
We have got to formula now.

164
00:09:55,150 --> 00:10:02,930
We have to learn about labeler N, so I will remove this then I'll explain your label n no to C label

165
00:10:02,950 --> 00:10:03,980
n label.

166
00:10:03,980 --> 00:10:05,940
No means these three nodes are there.

167
00:10:05,950 --> 00:10:07,370
Now you can see they are labeled.

168
00:10:07,570 --> 00:10:10,090
Right and previously there were no labels here.

169
00:10:10,360 --> 00:10:11,320
They were blank.

170
00:10:11,320 --> 00:10:13,000
Now something is written in that.

171
00:10:13,270 --> 00:10:14,380
So labelling is done.

172
00:10:14,710 --> 00:10:21,560
So ABCL labels then if a label nodes are there then how many different benefits can be generated.

173
00:10:21,580 --> 00:10:23,710
That's what we have to find out for the total label.

174
00:10:23,710 --> 00:10:24,880
Already we know the answer.

175
00:10:25,240 --> 00:10:30,640
Without labels there are five and formalized twenty and brand plus one.

176
00:10:31,000 --> 00:10:32,420
We all know the formula.

177
00:10:32,770 --> 00:10:35,920
This is the formula and for three nonsense is.

178
00:10:36,940 --> 00:10:39,790
But no, these are label for label means.

179
00:10:40,060 --> 00:10:45,520
If I pick up just one tree, this tree, if I become, then this is unlabelled.

180
00:10:45,820 --> 00:10:49,480
So if I try to label it how many ways I can level it.

181
00:10:50,080 --> 00:10:52,990
Three labels can be filled in how many ways.

182
00:10:53,800 --> 00:10:55,150
Three factorial with.

183
00:10:55,960 --> 00:10:57,400
Let us see this.

184
00:10:57,400 --> 00:11:04,090
I have a label this A, B, C and one more labelling a c.b.

185
00:11:05,260 --> 00:11:08,530
Same tree shape is same, but the labels are change.

186
00:11:08,950 --> 00:11:10,920
Permutation of label has changed.

187
00:11:11,050 --> 00:11:19,060
So I have different ways of filling ABC HCB be a C B.C. A C a b CBA.

188
00:11:19,600 --> 00:11:21,650
So one, two, three, four, five, six.

189
00:11:21,670 --> 00:11:23,650
So yes, these are six means.

190
00:11:23,650 --> 00:11:25,030
This is three factorial.

191
00:11:25,240 --> 00:11:30,870
So one shape can be feltri factorial this second shape again, three factorial, three factor of three

192
00:11:30,880 --> 00:11:38,350
factor for every shape and refactor always so far three n three factorial for notes and factory.

193
00:11:38,740 --> 00:11:48,430
This is the formula for label N formula is twenty plus one and two and factorial because the filling

194
00:11:48,430 --> 00:11:49,200
of the labels.

195
00:11:49,510 --> 00:11:57,130
So if you look at this formula and separated into two parts, this part is four shapes and this part

196
00:11:57,130 --> 00:12:02,340
is for filling permutation in how many of its notes can be filled.

197
00:12:02,860 --> 00:12:05,670
So this gives you how many different shapes we can get.

198
00:12:05,690 --> 00:12:12,390
Five different shapes then and factorial gives one shape can be filled in how many ways that is N factorial

199
00:12:12,400 --> 00:12:14,740
with seven and also our label this.

200
00:12:15,120 --> 00:12:16,720
So that's all end of the topic.

201
00:12:18,940 --> 00:12:25,360
So we have learned a number of N versus number of differentiated binary trees.

202
00:12:25,960 --> 00:12:30,940
So this discussion or this analysis is useful for the other topics.

