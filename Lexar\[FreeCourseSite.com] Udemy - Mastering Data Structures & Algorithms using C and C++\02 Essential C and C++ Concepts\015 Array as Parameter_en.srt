1
00:00:00,450 --> 00:00:03,700
In this video, we will see Array as Parameter.

2
00:00:04,860 --> 00:00:10,380
So already I have an example, and here I will show you how arrays are passed as parameter,

3
00:00:10,550 --> 00:00:12,780
more about arrays we will see.

4
00:00:12,790 --> 00:00:17,600
See here, the main function is having an array of size 5.

5
00:00:17,640 --> 00:00:19,440
And also it is initialized.

6
00:00:19,440 --> 00:00:24,540
So this is the part of main function, array and its name is A.

7
00:00:24,630 --> 00:00:31,840
Now here I'm calling a function fun ( ) by passing array and a number of elements that is of size 5. And, this function,

8
00:00:32,320 --> 00:00:33,340
fun ( ) ,

9
00:00:33,370 --> 00:00:42,430
It is taking parameter array, i.e, A, and for passing array as parameter, we have to mention empty brackets, right.

10
00:00:42,520 --> 00:00:48,100
We should not give any size. And this function doesn't know the size of an array because they actually

11
00:00:48,100 --> 00:00:53,950
belong to main function, so we should also pass what is the size of the array, means how many elements do

12
00:00:54,010 --> 00:00:55,270
we access.

13
00:00:55,330 --> 00:01:03,830
So, this A is actually like a pointer to an array. It's not an array itself, it's a pointer to an array,

14
00:01:04,300 --> 00:01:07,210
and this is number of elements.

15
00:01:07,270 --> 00:01:13,000
Now in this code I'm just printing all the elements of an array. This for loop is printing all the elements

16
00:01:13,000 --> 00:01:14,930
of an array.

17
00:01:15,010 --> 00:01:20,210
Now the important thing, let us see what is the parameter passing method used here.

18
00:01:20,320 --> 00:01:25,040
See array is passed by address.

19
00:01:25,150 --> 00:01:28,250
Arrays cannot be passed by value at all,

20
00:01:29,130 --> 00:01:31,740
whether it is C language or C++.

21
00:01:31,740 --> 00:01:37,780
Arrays can be passed only by address. So, it means the address of this array, the base address of this array

22
00:01:37,780 --> 00:01:39,790
is given to this pointer.

23
00:01:39,880 --> 00:01:47,520
This is just a pointer. I'm giving the bracket means, it is a pointer to an array. And what about the next thing?

24
00:01:47,530 --> 00:01:49,120
that is 5 is passed here.

25
00:01:49,150 --> 00:01:55,900
So this is which type of parameter? If you see this, there is no *. So it's not Call by Address.

26
00:01:55,990 --> 00:01:56,830
There is no &,

27
00:01:56,860 --> 00:01:58,300
This is not Call by Reference.

28
00:01:58,310 --> 00:01:59,910
Yes it is Call by Value.

29
00:02:00,250 --> 00:02:01,520
Just a normal variable.

30
00:02:01,970 --> 00:02:03,440
So this is Call by Value.

31
00:02:03,440 --> 00:02:04,630
So there are two parameters.

32
00:02:04,640 --> 00:02:06,030
One is passed by address,

33
00:02:06,290 --> 00:02:07,800
Other one is passed by value.

34
00:02:07,850 --> 00:02:10,039
So this is its own local variable n. 

35
00:02:10,080 --> 00:02:15,290
So the value 5 is copied in this n and the address of A is copied in this one.

36
00:02:15,710 --> 00:02:19,940
So that function is directly accessing this array, right.

37
00:02:20,420 --> 00:02:23,590
So arrays are only passed by addresses.

38
00:02:23,750 --> 00:02:26,170
Next thing, instead of writing brackets,

39
00:02:26,240 --> 00:02:27,640
Even I can write,

40
00:02:27,680 --> 00:02:28,650
* here.

41
00:02:29,750 --> 00:02:33,560
So this is will be an integer pointer.

42
00:02:33,930 --> 00:02:38,390
And that pointer will be pointing to an array. See, the difference is,

43
00:02:38,420 --> 00:02:43,520
This pointer can point on any integer and even it can point on an array.

44
00:02:44,360 --> 00:02:51,350
But if we were using brackets like this then, it means it's a       pointer to array only.

45
00:02:51,350 --> 00:02:58,680
So when you want to be specific with arrays then better use this one, and writing * is general method.

46
00:02:58,750 --> 00:02:59,940
This is array as parameter.

47
00:02:59,940 --> 00:03:00,810
This is array as parameter.

48
00:03:01,050 --> 00:03:09,830
Now one more thing, inside this function, if I want I can make changes to the array and it will make changes

49
00:03:09,830 --> 00:03:14,300
to the actual parameter, that is the array of main function.

50
00:03:14,300 --> 00:03:19,940
So I'll remove this and show you, if I make changes how it will reflect on this array.

51
00:03:20,090 --> 00:03:21,650
Here, array as a parameter,

52
00:03:22,010 --> 00:03:29,340
If I say, A[0] = 25, then at this place, it will be changed to 25.

53
00:03:29,360 --> 00:03:31,780
See the array belongs to the main function only.

54
00:03:31,860 --> 00:03:37,630
And this function is able to modify the contents of the array because it is called by address.

55
00:03:37,640 --> 00:03:42,530
So you can modify the array also, and you can modify all the elements.

56
00:03:42,530 --> 00:03:46,070
Even I can write a for loop and change all the elements.

57
00:03:46,070 --> 00:03:53,380
So this is sufficient to show you that even a function can modify the actual parameter.

58
00:03:53,390 --> 00:03:54,680
Now one more thing I will show you.

59
00:03:54,690 --> 00:03:56,470
Returning array as a parameter.

60
00:03:56,510 --> 00:03:59,090
So I will remove this and change the function.

61
00:03:59,090 --> 00:04:09,180
Then I will discuss about it. So here I have an example, showing how a function can return an array.

62
00:04:09,260 --> 00:04:12,860
So let us look at this code. Here, I have main function,

63
00:04:12,980 --> 00:04:18,899
I have a pointer inside main function and I am calling a function by passing value 5.

64
00:04:19,100 --> 00:04:22,820
Then the rest of the things whatever maybe there inside the main function.

65
00:04:22,820 --> 00:04:27,910
Now this is a function which is taking parameter n and this is passed by value.

66
00:04:28,010 --> 00:04:28,980
Right.

67
00:04:29,030 --> 00:04:30,810
There is no &, there is no *,

68
00:04:30,830 --> 00:04:37,070
So this is pass by value, call by value, and it is having a pointer and it is allocating an array of

69
00:04:37,070 --> 00:04:37,840
size

70
00:04:37,850 --> 00:04:41,710
this much of type Integer and it is returning p.

71
00:04:41,720 --> 00:04:42,620
It is returning p,

72
00:04:42,620 --> 00:04:45,110
that is pointer, returning a pointer.

73
00:04:45,110 --> 00:04:48,620
So what is the return type? int [ ] .

74
00:04:48,620 --> 00:04:50,920
So, [ ] resembles an array.

75
00:04:50,960 --> 00:04:53,820
So it means it is returning an array.

76
00:04:53,840 --> 00:04:56,990
Yes it is a returning, function is returning an array.

77
00:04:57,650 --> 00:04:59,080
And if you see the working,

78
00:04:59,420 --> 00:05:01,030
Let us look at the working now.

79
00:05:01,160 --> 00:05:02,990
Let us start from main function.

80
00:05:03,200 --> 00:05:09,240
Main function is having a pointer, then it is calling function fun by passing 5.

81
00:05:09,810 --> 00:05:11,380
Function fun ( ) is called.

82
00:05:11,420 --> 00:05:13,540
It is taking a parameter n, so it is

83
00:05:13,580 --> 00:05:19,970
also having n, it will have the value 5, then malloc ( ), it will allocate the memory in heap.

84
00:05:20,060 --> 00:05:23,950
So, inside the heap an array of size 5 will be created.

85
00:05:23,960 --> 00:05:29,280
Then the address of that will be present in p, because that is stored in p and that p belongs to the

86
00:05:29,280 --> 00:05:36,920
function fun ( ), that array in heap is indirectly accessible by this pointer p. Now, after allocating, it

87
00:05:36,920 --> 00:05:38,040
returns p.

88
00:05:38,060 --> 00:05:44,450
So it is returning that pointer, so where it is being taken inside the main function, in a, so what it is having?

89
00:05:44,450 --> 00:05:45,800
It is having the address on that one.

90
00:05:45,800 --> 00:05:48,090
So it means a will get the address of this one.

91
00:05:49,610 --> 00:05:51,990
So a will be pointing that array.

92
00:05:52,060 --> 00:05:55,520
So it returning by array.

93
00:05:56,240 --> 00:06:03,440
So this type of logic is useful sometimes like a function is allocating a memory in heap and manipulating,

94
00:06:03,440 --> 00:06:07,310
storing some data and then it is returning the array to the calling function.

95
00:06:07,310 --> 00:06:08,330
So it is returning,

96
00:06:08,450 --> 00:06:16,100
and this function is getting that array, created by that function, right? Now, once the function ends,

97
00:06:16,240 --> 00:06:18,350
its activation record is deleted.

98
00:06:18,350 --> 00:06:22,910
So this is p gone, but a is still there and a is accessing that array.

99
00:06:22,940 --> 00:06:26,730
So this is removed. Now, a is pointing on that one.

100
00:06:26,750 --> 00:06:28,620
So this is returned by array.

101
00:06:28,820 --> 00:06:29,660
Now one more thing,

102
00:06:29,780 --> 00:06:37,880
instead of writing [ ] , even I can write * here, because it is returning a pointer. So, pointer

103
00:06:37,880 --> 00:06:40,740
can point on one element, or array of elements.

104
00:06:40,910 --> 00:06:48,080
But when you have given a [ ] , it means it should point on array of elements only. That is especially

105
00:06:48,080 --> 00:06:49,010
for array.

106
00:06:49,190 --> 00:06:54,680
But you can use any of these, whichever you are comfortable with. And if [ ] is used, the code

107
00:06:54,680 --> 00:06:56,020
itself is readable.

108
00:06:56,360 --> 00:07:00,600
And from the code we can read that, it is related to an array, right?

109
00:07:00,920 --> 00:07:06,630
So any one thing you can use. So that's all, this is how a function can return an array.

110
00:07:07,520 --> 00:07:14,210
So we have learned, How functions can handle array taking as parameter and returning as an address

111
00:07:14,210 --> 00:07:15,550
an array.

112
00:07:15,740 --> 00:07:19,130
So we will be using this type of code sometime in our course.

113
00:07:19,880 --> 00:07:26,480
So that's all in this video. In the coming videos, I will show you how to handle structures and functions.

