1
00:00:00,210 --> 00:00:07,050
Let us look at the representation of across all that, if you have seen it for non-direct, we follow

2
00:00:07,050 --> 00:00:09,300
the same pattern that is adjacency matrix.

3
00:00:09,300 --> 00:00:11,210
And I just want to list.

4
00:00:11,550 --> 00:00:14,180
So here I have a dedicated graph.

5
00:00:14,520 --> 00:00:16,260
Let us see how to represent that one.

6
00:00:16,260 --> 00:00:21,990
Using a different cymatics, I've taken to market size four by four, because here the number of boxes

7
00:00:21,990 --> 00:00:23,320
are for number four.

8
00:00:23,340 --> 00:00:27,120
This is our four and the number of just are fine.

9
00:00:29,050 --> 00:00:34,270
Focuses on five or just depending on the number for the says, we should take a square matrix of size

10
00:00:34,390 --> 00:00:43,300
four by four that is in and now each at all, our column represents what looks like phospholipids,

11
00:00:43,300 --> 00:00:45,840
first vertex or first column, 140 percent for what?

12
00:00:46,960 --> 00:00:48,740
Just like for an undirected graph here.

13
00:00:48,770 --> 00:00:52,570
Also, we have to mark value one with a little bit of an edge.

14
00:00:52,900 --> 00:00:54,190
So let us quickly do that.

15
00:00:54,790 --> 00:00:57,010
One, two, and one, two, four.

16
00:00:57,040 --> 00:01:02,740
There are just one, two, two and one two four remaining are Zeitels, then two to three.

17
00:01:02,750 --> 00:01:07,080
There is only one edge and two to one goo goo goo goo goo for the other.

18
00:01:07,150 --> 00:01:10,280
Just three to one there is an edge.

19
00:01:10,480 --> 00:01:14,470
Three to one there is an edge and remaining on equals four to three.

20
00:01:14,480 --> 00:01:15,160
That is a niche.

21
00:01:16,380 --> 00:01:24,630
Remaining on the before before an presentation for our director graph in order to figure out when there's

22
00:01:24,630 --> 00:01:26,700
an answer from one to two, we were counting it.

23
00:01:27,090 --> 00:01:33,390
Egberto, we want to go as well as two to one, but here it is having direction just to take this one

24
00:01:33,390 --> 00:01:33,780
to two.

25
00:01:34,230 --> 00:01:41,420
Next, let us look at just a little list representation for I just a list here of an array of linguists

26
00:01:41,430 --> 00:01:50,700
that already we have sitting across each location in another is representing what they're then who are

27
00:01:50,700 --> 00:01:54,230
just one word that says we should have the adjacency list.

28
00:01:54,720 --> 00:01:57,560
So for one who just two and four.

29
00:01:57,870 --> 00:02:00,990
So these are the list of what is what I just said.

30
00:02:01,020 --> 00:02:03,690
So this is just to list them for two.

31
00:02:03,690 --> 00:02:06,450
There's only one word text that is three and four three.

32
00:02:06,630 --> 00:02:13,950
There is only one word text that is one and four for there is only one word that is three.

33
00:02:16,200 --> 00:02:20,710
That's all this is just a list, then one more thing to observe here.

34
00:02:21,030 --> 00:02:28,380
See, I want to know, what are the just going out from what one vertex, one at just going out are

35
00:02:28,830 --> 00:02:30,890
one to two and one before.

36
00:02:30,930 --> 00:02:32,460
Yes, one to go and one before.

37
00:02:33,230 --> 00:02:37,500
They want to know what other ideas that are coming in on Vertex four.

38
00:02:38,040 --> 00:02:39,250
So check the column.

39
00:02:39,750 --> 00:02:40,390
Yes.

40
00:02:40,800 --> 00:02:43,950
This is actually coming in from three to one three one.

41
00:02:43,950 --> 00:02:44,690
Check the column.

42
00:02:45,600 --> 00:02:46,730
So three to one there is one.

43
00:02:46,740 --> 00:02:48,390
It's coming from three to one.

44
00:02:48,390 --> 00:02:55,680
So it's coming in quite a bit more in degree of four decks, one by picking column out degree for this,

45
00:02:55,680 --> 00:03:01,970
one by taking a number of runs in total, like there are ones obvious to what in addition to the list,

46
00:03:01,980 --> 00:03:07,490
we only have a degree o degree, much more in degree of Vertex one.

47
00:03:07,500 --> 00:03:11,580
I should search all links and find out where one is occurring.

48
00:03:11,580 --> 00:03:12,970
So I found one one time.

49
00:03:13,080 --> 00:03:16,560
So in this one, I want to know any of three.

50
00:03:17,100 --> 00:03:17,630
Three.

51
00:03:17,640 --> 00:03:19,680
Yes, one time to time.

52
00:03:20,010 --> 00:03:21,930
So individual four three is a two.

53
00:03:22,290 --> 00:03:25,980
So I have to check all linked list for all degree of vertex one.

54
00:03:26,070 --> 00:03:30,750
Just count the number of nodes and it's on the list for this nodes are there.

55
00:03:30,900 --> 00:03:32,490
So the left of the link is a stool.

56
00:03:32,490 --> 00:03:35,850
So all degrees to any degree we cannot know.

57
00:03:35,880 --> 00:03:43,310
Suppose Neung in degrees is also important for us, then we can have one more type of just the list

58
00:03:43,350 --> 00:03:46,320
that is called inverse of just centralised.

59
00:03:46,320 --> 00:03:50,670
So I Dorottya somewhat inverse addition to list the method the same.

60
00:03:51,950 --> 00:03:58,070
We will take area of interest that whatever the ideas that are coming on board, next one, we will

61
00:03:58,070 --> 00:03:58,840
draw them here.

62
00:03:59,240 --> 00:04:05,030
So they're just coming from key and on to the edges are coming from one on the country.

63
00:04:05,030 --> 00:04:10,490
The edges are coming from two and four or three.

64
00:04:10,490 --> 00:04:11,540
There are two and four.

65
00:04:11,540 --> 00:04:17,570
So you can see that there are two are just coming in, one for for the next coming in from next one.

66
00:04:17,870 --> 00:04:22,470
So this is involves a to list.

67
00:04:23,000 --> 00:04:27,820
So there's a shooting going out or just and these are that are just that are coming in.

68
00:04:28,100 --> 00:04:35,120
So if that is the requirement of an application then you can have in the words of a to list also and

69
00:04:35,120 --> 00:04:37,850
also you can have actual admissions to list.

70
00:04:38,420 --> 00:04:41,700
So that's all about representation of director.

71
00:04:42,540 --> 00:04:49,240
You can also prepare a contact list, but you can show outgoing a different you can have in words compact's

72
00:04:49,280 --> 00:04:51,600
also that you can do it by yourself.

73
00:04:51,920 --> 00:04:55,040
So all of you have seen compatriots on the ground.

74
00:04:55,150 --> 00:04:59,930
It will be in a similar fashion then a little bit of analysis if directed.

75
00:04:59,940 --> 00:05:06,930
Gadhafi's representative, the form of a different somatics, then total number of elements are in England

76
00:05:07,220 --> 00:05:07,910
and Square.

77
00:05:08,210 --> 00:05:13,280
And the algorithm, which is processing upon this data structure, may have to go to all the elements

78
00:05:13,730 --> 00:05:17,670
of this matrix, at least to check whether there is a zettl or what.

79
00:05:17,990 --> 00:05:24,140
So it has to process all the elements of the time taken maybe and square if it is processing all the

80
00:05:24,140 --> 00:05:25,010
elements once.

81
00:05:25,160 --> 00:05:28,210
So the time, minimum time we can see that is and square.

82
00:05:28,460 --> 00:05:34,700
If I just said cymatics is used and if any algorithm is using a different list than the time taken for

83
00:05:34,700 --> 00:05:42,640
processing all this, a number of bodices and that is me and these all edges.

84
00:05:42,800 --> 00:05:53,630
So e that a small E so this we can say both and for as I'm sure you can and and we can say and this

85
00:05:53,630 --> 00:05:54,970
is part of and square.

86
00:05:55,280 --> 00:06:00,730
So if I just said this, that the structure is used and the timing may be less and if not X is used

87
00:06:00,730 --> 00:06:01,820
than the time, maybe more.

88
00:06:02,000 --> 00:06:08,320
So whenever we are analyzing the algorithms, we will analyse their time assuming that they are using

89
00:06:08,330 --> 00:06:09,470
adjustments to the list.

90
00:06:09,890 --> 00:06:16,490
So we assume that the time taken for accessing the data structure maybe and actually analyzed be used

91
00:06:16,490 --> 00:06:23,080
on set off or just a sense of just how much processing is done is repeated in terms of what the system

92
00:06:23,100 --> 00:06:27,440
edges, because there are two different lists, lists of witnesses and list of ages.

93
00:06:27,650 --> 00:06:31,650
So that's all with the analysis and coming to us, we will see algorithms on.

