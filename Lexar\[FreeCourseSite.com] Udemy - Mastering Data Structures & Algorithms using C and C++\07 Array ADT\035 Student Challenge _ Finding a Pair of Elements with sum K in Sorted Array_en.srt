1
00:00:00,210 --> 00:00:06,930
In this video, we will solve the problem to find a pair of elements in an sorted out such that the

2
00:00:06,930 --> 00:00:09,970
total is equal to a given number, for example.

3
00:00:11,160 --> 00:00:12,530
We want a pair of elements.

4
00:00:12,550 --> 00:00:14,860
And besides that, the total is equal to 10.

5
00:00:14,880 --> 00:00:17,390
So in my example, I have to contain it can be anything.

6
00:00:17,400 --> 00:00:22,950
So for any given number, we have to find out pair of elements if it is existing in an area or not.

7
00:00:23,730 --> 00:00:29,420
So already I have an idea which is already sorted for in a solitary how to find this.

8
00:00:29,430 --> 00:00:32,150
We will see all show you the procedure.

9
00:00:32,729 --> 00:00:33,840
See we want appear pair.

10
00:00:34,080 --> 00:00:38,240
So first let us check whether there are any elements or not like yeah.

11
00:00:38,250 --> 00:00:43,720
One nine is there and for six is also there.

12
00:00:43,740 --> 00:00:44,180
Yes.

13
00:00:44,760 --> 00:00:47,910
So we have two pair of elements who sum is equal to ten.

14
00:00:48,720 --> 00:00:49,490
So hold the final.

15
00:00:49,530 --> 00:00:51,570
This as the result will be sorted.

16
00:00:51,750 --> 00:00:56,090
All the smaller elements are on this side and the greater element are on that side.

17
00:00:56,100 --> 00:00:56,380
Right.

18
00:00:56,820 --> 00:00:58,440
So we want a pair of elements.

19
00:00:58,440 --> 00:01:05,129
So when they disappear then one number may be smaller and one number will be larger like three seven

20
00:01:05,129 --> 00:01:06,300
or two eight.

21
00:01:06,300 --> 00:01:07,700
So that is total ten.

22
00:01:08,160 --> 00:01:11,250
So smaller numbers are on the site, larger number on that side.

23
00:01:11,250 --> 00:01:17,640
So we can start scanning the array from either direction, from the direction to find one small element,

24
00:01:17,640 --> 00:01:20,570
one large element so that the total is equal to ten.

25
00:01:21,480 --> 00:01:25,430
Yes, we will follow this idea so far as scanning through this one.

26
00:01:25,440 --> 00:01:30,540
So I will take I that is starting on zero and G on last index.

27
00:01:30,540 --> 00:01:31,670
That is N minus one.

28
00:01:32,970 --> 00:01:35,790
Then using this I will proceed in this direction.

29
00:01:35,790 --> 00:01:37,470
Gibbsville moving in this direction.

30
00:01:37,830 --> 00:01:39,360
Then what I have to do every time.

31
00:01:39,720 --> 00:01:42,930
Check whether these two are equal to ten.

32
00:01:43,540 --> 00:01:49,190
No this is greater, this is greater one plus 14 as 15 that is greater.

33
00:01:49,500 --> 00:01:52,620
So when it is greater because that number is greater.

34
00:01:53,130 --> 00:01:54,160
So this is greater.

35
00:01:54,300 --> 00:01:57,030
The reason is that number is bigger.

36
00:01:57,180 --> 00:01:57,540
Right.

37
00:01:57,870 --> 00:01:59,610
So let us check a smaller number.

38
00:01:59,610 --> 00:02:01,650
So I will be criminology.

39
00:02:02,070 --> 00:02:03,300
I will not increase.

40
00:02:03,300 --> 00:02:06,060
I let I be there only right now.

41
00:02:06,060 --> 00:02:08,190
I again check one plus twelve thirteen.

42
00:02:08,460 --> 00:02:09,500
This is still greater.

43
00:02:09,960 --> 00:02:14,420
So because of that bigger number, we are getting the result greater than ten.

44
00:02:14,700 --> 00:02:16,380
So let us degremont only.

45
00:02:16,560 --> 00:02:17,120
Right.

46
00:02:17,130 --> 00:02:21,140
Let us only know this one ten plus one leverne.

47
00:02:21,190 --> 00:02:26,250
Still it is greater than ten so diclemente now one plus nine.

48
00:02:26,250 --> 00:02:30,810
Yes we got up here one and nine are together forming ten for.

49
00:02:30,840 --> 00:02:33,270
Yes we got a pair next.

50
00:02:33,300 --> 00:02:43,050
Once we got a pair then we should increment I also and Degremont Jones Mortada both I have done.

51
00:02:43,410 --> 00:02:44,040
Now watch.

52
00:02:45,550 --> 00:02:52,060
Three plus eight, no, this is 11, this is greater for what should be diclemente when you are getting

53
00:02:52,060 --> 00:02:54,430
any greater number means it is greater than 10 minutes.

54
00:02:54,430 --> 00:02:57,040
And DICLEMENTE Not uncommon.

55
00:02:57,340 --> 00:02:58,170
This is important.

56
00:02:59,020 --> 00:03:00,040
Three and six.

57
00:03:00,080 --> 00:03:01,030
It's nine.

58
00:03:01,340 --> 00:03:05,810
It is less than 10 reason the number may be more smaller.

59
00:03:06,040 --> 00:03:07,840
So let us go to a little bigger number.

60
00:03:08,050 --> 00:03:09,210
So move I.

61
00:03:09,640 --> 00:03:14,260
So if the total is greater than 10 K then.

62
00:03:14,530 --> 00:03:22,860
DiClemente If the total is less than the increment I know, check them four plus six.

63
00:03:22,870 --> 00:03:31,060
Yes, there is one more pair, one more pair of element whose total is equal to ten then move next.

64
00:03:32,480 --> 00:03:39,230
Then, as we got to bear in agreement, I also and Degremont also know they have met at the same place

65
00:03:39,230 --> 00:03:40,820
that are on single element.

66
00:03:40,820 --> 00:03:42,350
So building is not possible.

67
00:03:42,650 --> 00:03:44,270
So stop the procedure.

68
00:03:45,630 --> 00:03:46,680
So this is the procedure.

69
00:03:46,950 --> 00:03:52,710
Let me out on the court and show you what performing this procedure we need in the beginning and at

70
00:03:52,710 --> 00:03:53,130
the end.

71
00:03:53,250 --> 00:03:56,220
So and we are not incrementing agreement every time.

72
00:03:56,220 --> 00:03:58,050
We are not discriminating every time.

73
00:03:58,060 --> 00:04:00,960
So we cannot use formal follow up.

74
00:04:00,960 --> 00:04:06,420
Will always do eight plus plus or minus minus six will perform all this we don't want always.

75
00:04:06,420 --> 00:04:06,690
Right.

76
00:04:06,990 --> 00:04:14,070
So we cannot use the follow we can use, but the court will not be easily able to make it simple.

77
00:04:14,700 --> 00:04:20,550
I will use while loop I define zero energy at and minus one.

78
00:04:20,769 --> 00:04:23,820
Yes this is here and this is here right now.

79
00:04:23,820 --> 00:04:28,220
How long I should perform this process while I is less than G.

80
00:04:28,530 --> 00:04:32,310
Once they become equal, stop then what I should do every time.

81
00:04:32,970 --> 00:04:33,300
Check.

82
00:04:33,300 --> 00:04:35,870
If these two are added then the result should be equal.

83
00:04:35,920 --> 00:04:43,380
OK, check if if I plus eight of G is equal to K.

84
00:04:43,770 --> 00:04:45,420
If it is equal then print.

85
00:04:45,600 --> 00:04:47,090
So I'll print three values.

86
00:04:47,100 --> 00:04:49,390
That is first number and the second number of the total.

87
00:04:49,620 --> 00:04:55,550
So the first number and second number I suppose it was nine, so one and nine will be printed.

88
00:04:55,770 --> 00:05:00,810
So if it is equal, this is first case and one they are equal.

89
00:05:00,810 --> 00:05:06,530
I should increment I also as well as I should decrement a G right then.

90
00:05:07,170 --> 00:05:12,780
Second case, if they are not equal then this total may be greater or smaller.

91
00:05:12,870 --> 00:05:23,130
So I will write on else if if I plus eight of G that is less than the key.

92
00:05:23,460 --> 00:05:26,960
If it is less than if it is less means the number is smaller.

93
00:05:26,970 --> 00:05:28,800
So I should go to the bigger numbers.

94
00:05:28,800 --> 00:05:34,500
So I plus plus I plus plus else means the number is greater.

95
00:05:34,500 --> 00:05:35,500
So I should be criminal.

96
00:05:35,500 --> 00:05:37,740
G ls J minus minus.

97
00:05:38,850 --> 00:05:39,220
Right.

98
00:05:40,330 --> 00:05:48,090
See if they're equal to this, if it is less than incremented, I if it is greater than DiClemente.

99
00:05:48,790 --> 00:05:55,600
So this will continue as long as I use less than Gaim and it will stop once I is.

100
00:05:56,600 --> 00:06:02,030
EqualLogic, so this is the procedure, not analysis, what is the amount of time taken?

101
00:06:02,150 --> 00:06:06,920
It's very simple, just we are going through all the elements, scanning them once right from the left

102
00:06:06,920 --> 00:06:09,320
side, partial and from right side.

103
00:06:09,320 --> 00:06:12,960
We are taking partial total number of elements that we are scanning us.

104
00:06:13,040 --> 00:06:17,210
And so the time is linear, I guess, linear lineaments.

105
00:06:17,210 --> 00:06:23,400
And so I should say it's out of M or B go off and whatever you call it, as I'm calling it, that's

106
00:06:23,420 --> 00:06:27,370
out of an order of an Selenia.

107
00:06:27,770 --> 00:06:28,480
No, at last.

108
00:06:28,490 --> 00:06:29,180
One more thing.

109
00:06:29,450 --> 00:06:31,250
C I have you I look here.

110
00:06:31,530 --> 00:06:35,120
Is it possible that we can do it using for loop.

111
00:06:35,600 --> 00:06:36,380
Let us check.

112
00:06:37,610 --> 00:06:40,970
If I write for a loop then this is a condition.

113
00:06:41,090 --> 00:06:45,910
OK, I'll just write it iis less than G right then.

114
00:06:45,950 --> 00:06:54,020
This is the initial part so I should be equal to zero and comma G should be equal to and the minus one.

115
00:06:54,050 --> 00:06:54,370
Right.

116
00:06:54,500 --> 00:06:59,690
And the minus one then is the condition I then g then what about eight plus.

117
00:06:59,690 --> 00:07:00,770
Plus minus minus.

118
00:07:00,770 --> 00:07:02,250
I don't have to do it always.

119
00:07:02,330 --> 00:07:06,230
It is based on condition so leave it blank, you can leave it blank.

120
00:07:06,920 --> 00:07:09,080
So now I don't need these escapement.

121
00:07:09,080 --> 00:07:09,500
Right.

122
00:07:09,500 --> 00:07:13,000
Because they have the game initialization part under the condition.

123
00:07:13,370 --> 00:07:18,440
So this is the follow up look like now for Lucas not having one of the statement inside.

124
00:07:18,440 --> 00:07:21,230
That is whether incremented or decrement, what is done here.

125
00:07:21,540 --> 00:07:22,400
This is not there.

126
00:07:22,700 --> 00:07:23,660
So that is missing.

127
00:07:23,660 --> 00:07:26,330
So it will confuse the reader what is happening here.

128
00:07:27,380 --> 00:07:32,840
So it's better using my loop, though, you can use of the loop or even you can use a do while loop

129
00:07:32,840 --> 00:07:33,260
also.

130
00:07:33,440 --> 00:07:34,820
So that's all in this video.

