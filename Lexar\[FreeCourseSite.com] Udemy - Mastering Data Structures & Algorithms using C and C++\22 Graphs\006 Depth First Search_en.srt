1
00:00:00,480 --> 00:00:07,140
Now, in this, we we look at depth first search, this is one of the graph traversal McCard.

2
00:00:08,430 --> 00:00:13,170
This is similar to preorder traversal of a binary trees or trees.

3
00:00:14,300 --> 00:00:20,600
Let us understand how it works, so for understanding, we should know to dance Foster's.

4
00:00:22,380 --> 00:00:29,430
Visited when it was vortexes, visited when we have reached record, it has visited thing is.

5
00:00:33,980 --> 00:00:40,520
Exploding about exploding advertisements, visiting all are just on water source like suppose one are

6
00:00:40,520 --> 00:00:44,370
just on what is this are two, three and four visiting all of them.

7
00:00:45,320 --> 00:00:49,550
So based on these two things, I will define and explain depth.

8
00:00:49,550 --> 00:00:50,770
First search.

9
00:00:51,680 --> 00:00:55,640
So let us perform the first search up on this graph here.

10
00:00:55,690 --> 00:01:01,170
I love writing the result of the first search and it uses abstracts.

11
00:01:01,190 --> 00:01:04,660
I will use a stack on so you know where to start.

12
00:01:05,390 --> 00:01:07,490
What should be the initial starting vertex?

13
00:01:07,520 --> 00:01:13,140
You can select animatics as a starting riddick's, so I will select starting what access one one is

14
00:01:13,160 --> 00:01:14,300
visiting one.

15
00:01:15,250 --> 00:01:18,410
Once it is visited, start exploring what is fun.

16
00:01:18,730 --> 00:01:19,840
So who ordered the Santa?

17
00:01:19,860 --> 00:01:22,830
One, two, three and four.

18
00:01:22,900 --> 00:01:23,320
Which one?

19
00:01:23,320 --> 00:01:30,880
I should go I can go on any more vertex so I can go on for also three also thought I will go on to OK

20
00:01:31,030 --> 00:01:38,050
to two is visiting to is and then what is the next vertex of one.

21
00:01:38,380 --> 00:01:38,740
I just.

22
00:01:38,800 --> 00:01:39,650
And what is a three.

23
00:01:39,970 --> 00:01:49,360
No no don't go on next vertex of one suspend one exploration of one and start exploring two then what

24
00:01:49,360 --> 00:01:50,080
to do with one.

25
00:01:50,500 --> 00:01:51,730
Push it into the stack.

26
00:01:53,060 --> 00:01:55,920
So that we can continue its exploration afterwards?

27
00:01:56,360 --> 00:01:59,120
No, we on what to start exploring to.

28
00:02:01,090 --> 00:02:10,120
From to we can go on three or go on three three, suspend exploration off a two and start exploring

29
00:02:10,120 --> 00:02:12,070
three, so two in the stack.

30
00:02:13,590 --> 00:02:19,910
So it means whenever we get a new word, we will suspend all our current products and will start exploring

31
00:02:19,910 --> 00:02:20,430
the new one.

32
00:02:20,750 --> 00:02:27,970
Yes, that is the approach of that for such explode three from three and four out of the last five.

33
00:02:28,320 --> 00:02:29,760
I prefer going on five.

34
00:02:29,910 --> 00:02:31,700
OK, go on five then.

35
00:02:31,710 --> 00:02:32,730
What about three?

36
00:02:33,770 --> 00:02:39,500
Push it into the stack, suspended the exploration, we have a good fight then from five where I can

37
00:02:39,500 --> 00:02:42,770
go, I can go on six and seven, so first I will go on seven.

38
00:02:43,100 --> 00:02:47,870
Seven is pushing five into the stack so that we can explore it afterwards.

39
00:02:48,200 --> 00:02:50,410
Start exploring seven from seven.

40
00:02:50,410 --> 00:02:51,500
I cannot go anywhere.

41
00:02:51,980 --> 00:02:54,760
Then seven is completely explored.

42
00:02:55,760 --> 00:03:01,670
If you have a psychologist and all there is Nordhausen, we say it is completely explored, then go

43
00:03:01,670 --> 00:03:05,910
back and continue the exploration of previous vertex veritas in the stack.

44
00:03:06,380 --> 00:03:08,090
OK, we are on five now.

45
00:03:08,480 --> 00:03:09,770
Start exploring five.

46
00:03:10,130 --> 00:03:12,050
Next vertex is six.

47
00:03:12,050 --> 00:03:13,370
OK, was it six.

48
00:03:14,990 --> 00:03:17,060
Push five again into the stock.

49
00:03:18,430 --> 00:03:21,740
And start exploring six from six.

50
00:03:21,760 --> 00:03:24,250
We cannot go anywhere, so this is completely explored.

51
00:03:24,640 --> 00:03:27,240
Go back to the previous vortex for about five.

52
00:03:27,370 --> 00:03:30,330
So we are again here from five.

53
00:03:30,370 --> 00:03:32,490
I can go on four also.

54
00:03:32,920 --> 00:03:34,450
Yes, go to four.

55
00:03:35,900 --> 00:03:40,430
Bush fire again into the stock and start exploring for.

56
00:03:42,370 --> 00:03:46,930
From forward, I can go from four, I can go on three and one.

57
00:03:48,110 --> 00:03:52,710
Three states already visited, one which is already visited.

58
00:03:53,060 --> 00:03:58,220
So this is the first time I came across all that visited versus I have drawn our daughter right there

59
00:03:59,150 --> 00:04:00,890
before is completely explored.

60
00:04:02,870 --> 00:04:06,440
Go back to previous, what, five continuous exploration.

61
00:04:07,440 --> 00:04:08,220
From five.

62
00:04:09,270 --> 00:04:14,650
We can go on seven, six, four or four, six, seven.

63
00:04:14,670 --> 00:04:17,870
All of them have visited so far, is completely explored.

64
00:04:18,180 --> 00:04:20,970
Go back on a previous vortex and continue this exploration.

65
00:04:20,970 --> 00:04:21,519
Who is back?

66
00:04:21,550 --> 00:04:24,960
Three from three who are just one, two, three.

67
00:04:26,100 --> 00:04:30,170
Two, four, five, as well as one, so two from their own lives are coming.

68
00:04:30,520 --> 00:04:31,860
Five have already visited.

69
00:04:31,860 --> 00:04:34,070
Ford is already with the daughter that is also there.

70
00:04:34,440 --> 00:04:35,540
What a three to one?

71
00:04:35,580 --> 00:04:36,400
There is no doubt it.

72
00:04:36,630 --> 00:04:37,980
So I will draw, Chief.

73
00:04:40,150 --> 00:04:42,130
Trade is completely explored.

74
00:04:43,920 --> 00:04:50,290
Take our next words to do is completely explode, take the next one is explode.

75
00:04:50,640 --> 00:04:55,280
So what a time when you return and go back to the previous what he says, we'll find them.

76
00:04:55,290 --> 00:04:56,460
They are already explored.

77
00:04:57,330 --> 00:04:59,670
So once again, I'll explain that first search.

78
00:04:59,940 --> 00:05:03,990
You can start from anywhere you like, then you have to explore it.

79
00:05:04,290 --> 00:05:08,900
And once you got a new vortex, suspended exploration, start exploring new waters.

80
00:05:10,250 --> 00:05:11,270
This is how we define.

81
00:05:12,620 --> 00:05:21,140
So we have seen this, rather, the three that we got is that the first search spanning three, this

82
00:05:21,140 --> 00:05:22,760
is the first search spanning three.

83
00:05:27,740 --> 00:05:32,000
And the daughter that just that we are getting, we call them as just.

84
00:05:34,300 --> 00:05:39,670
And if you remember, when I was on four, I have drawn the sketch when I was on four, I have drawn

85
00:05:39,670 --> 00:05:41,530
this country, I have drawn the sketch.

86
00:05:41,800 --> 00:05:46,780
So these are back and just connecting back to the already visited Watterson's.

87
00:05:48,710 --> 00:05:55,160
So they are collecting packages, so this is one of the top four search, we can have various different

88
00:05:55,250 --> 00:05:57,600
search drivers for the same groups.

89
00:05:58,250 --> 00:06:03,930
The next thing I said that before, search is just like preorder offertory.

90
00:06:04,220 --> 00:06:06,490
So this is literally like this lottery.

91
00:06:07,190 --> 00:06:13,740
It is a device did not level with deputised generated from preorder on this one.

92
00:06:13,760 --> 00:06:16,250
So like, you know, but just by moving your finger, we can get it.

93
00:06:16,700 --> 00:06:21,350
One, two, three, five, seven, then six and four.

94
00:06:21,620 --> 00:06:23,930
One, two, three, five, seven, six, four.

95
00:06:24,050 --> 00:06:30,770
So for distributers preorder traversal like the next thing, we will see the analysis.

96
00:06:31,430 --> 00:06:35,000
What is the time taken by this data for search method?

97
00:06:36,090 --> 00:06:39,880
So the time depends on the work that we are doing, what we are doing here.

98
00:06:40,170 --> 00:06:42,600
We are visiting all the water just once.

99
00:06:43,230 --> 00:06:45,680
So how many words is out there and what is the sonnet?

100
00:06:45,990 --> 00:06:53,920
So the times are tough and so the time taken for that first such traversal and analytical time.

101
00:06:53,940 --> 00:06:59,580
Remember this when we write the program, the program may be taking more time because it depends on

102
00:06:59,580 --> 00:07:01,120
the data structure that we are using.

103
00:07:01,530 --> 00:07:04,970
If you're using a different cymatics, then it be in square.

104
00:07:05,370 --> 00:07:09,900
If you are just on the list, then it will be so analytically.

105
00:07:09,900 --> 00:07:15,670
We say the time taken is outdraw and because we are doing nothing, just we are visiting, although

106
00:07:15,670 --> 00:07:16,640
what is this once.

107
00:07:17,670 --> 00:07:18,250
So that's it.

108
00:07:18,270 --> 00:07:19,860
I have explained you the search.

109
00:07:20,190 --> 00:07:29,670
Now I will remove this and show you various valid Deptford searches so you get the idea how we can perform

110
00:07:29,670 --> 00:07:35,610
the first search in various ways in let us perform a on that a little just near by this graph.

111
00:07:36,510 --> 00:07:41,230
First, when I'm writing starting work, we can select any word excel starting work experience, what

112
00:07:41,340 --> 00:07:41,780
is one?

113
00:07:43,170 --> 00:07:48,660
Then from there, I wonder to I can visit anywhere, so I'll go on three, then from three I will go

114
00:07:48,660 --> 00:07:49,320
on five.

115
00:07:49,860 --> 00:07:53,550
OK then from five a.m. go on forward, then from four.

116
00:07:53,550 --> 00:07:58,770
I cannot go further because all anyone is visited come back before from there is no other route.

117
00:07:58,860 --> 00:08:00,690
Come back to five from five.

118
00:08:00,690 --> 00:08:04,530
I can go on seven or seven from five I can go on six.

119
00:08:04,530 --> 00:08:09,620
Yes six then come back to fight like this cannot be explored further.

120
00:08:09,630 --> 00:08:10,820
So this is completely explored.

121
00:08:10,830 --> 00:08:12,550
Go back to three from three a.m..

122
00:08:12,550 --> 00:08:13,110
Go to.

123
00:08:14,140 --> 00:08:15,040
There's also relict.

124
00:08:17,000 --> 00:08:17,810
Second one.

125
00:08:18,800 --> 00:08:24,020
Starting with Texas one, one, two, two, OK, two to three.

126
00:08:24,200 --> 00:08:31,310
OK, from three, I can go on for some for I can go on five from 5:00 and one six from six.

127
00:08:31,310 --> 00:08:32,289
I cannot go anywhere.

128
00:08:32,630 --> 00:08:33,520
Go back to five.

129
00:08:33,740 --> 00:08:34,080
Come on.

130
00:08:34,100 --> 00:08:34,520
Seven.

131
00:08:36,210 --> 00:08:37,159
There's also valid.

132
00:08:38,140 --> 00:08:42,340
Then one more third one, I'll start from word one, OK?

133
00:08:42,400 --> 00:08:44,140
One that I'll go on for.

134
00:08:44,290 --> 00:08:50,460
Yes, from four, I will go and fight from five I will go on seven from seven.

135
00:08:50,470 --> 00:08:51,380
I cannot go further.

136
00:08:51,380 --> 00:08:52,270
Come back to five.

137
00:08:52,540 --> 00:08:53,270
Then from five.

138
00:08:53,300 --> 00:08:53,620
Go, go.

139
00:08:53,620 --> 00:08:58,300
Go to three or three from three, two, two, two from two.

140
00:08:58,300 --> 00:09:00,790
Dissolve the music and go back from three.

141
00:09:00,790 --> 00:09:01,650
I cannot go anywhere.

142
00:09:01,990 --> 00:09:04,180
Go back to two.

143
00:09:04,180 --> 00:09:05,920
Back three then seven.

144
00:09:05,920 --> 00:09:07,330
Already gone five.

145
00:09:07,330 --> 00:09:09,550
We are one five from five.

146
00:09:09,550 --> 00:09:10,790
I can go on six years.

147
00:09:10,810 --> 00:09:11,890
Six from six.

148
00:09:11,890 --> 00:09:12,760
I cannot go anywhere.

149
00:09:13,060 --> 00:09:13,900
Go back to five.

150
00:09:13,910 --> 00:09:14,800
Go back to four.

151
00:09:14,800 --> 00:09:15,320
Go back to.

152
00:09:16,580 --> 00:09:19,040
So how to go back, that stock will help you.

153
00:09:19,400 --> 00:09:21,920
Or else you practice this, you can get it easily.

154
00:09:22,340 --> 00:09:29,000
It's not difficult, just you do paperwork by yourself, do it once and one more.

155
00:09:29,000 --> 00:09:29,560
I'll show you.

156
00:09:30,050 --> 00:09:32,330
I want to select some of the are starting blocks.

157
00:09:32,420 --> 00:09:38,810
OK, selected this one for some for I can go to one from one.

158
00:09:38,810 --> 00:09:41,230
I will go to three two.

159
00:09:41,240 --> 00:09:42,820
I skipped I came on three.

160
00:09:42,860 --> 00:09:44,750
Yes I can go anywhere from three.

161
00:09:44,750 --> 00:09:47,690
I will go to five or from five to six.

162
00:09:49,040 --> 00:09:50,060
Go back to five.

163
00:09:50,420 --> 00:09:57,260
Go to seven then this is finished then go back to three from three to is remaining.

164
00:09:57,560 --> 00:10:00,380
Take two is also valid.

165
00:10:01,430 --> 00:10:08,570
So these are various possible, that first surge, but the different starting what is so different order

166
00:10:08,570 --> 00:10:15,620
of visiting and all these are usually students believe that there is only one method, one answer we

167
00:10:15,620 --> 00:10:15,830
get.

168
00:10:15,830 --> 00:10:17,550
No, you're getting a different answer.

169
00:10:17,850 --> 00:10:21,570
See, again, the answer that you are getting is not important.

170
00:10:21,950 --> 00:10:24,370
Visiting all the what is this is very important.

171
00:10:25,310 --> 00:10:26,730
You should visit all the what ifs.

172
00:10:27,890 --> 00:10:30,050
So that's all about the first such method.

173
00:10:30,470 --> 00:10:33,230
We will see a program or algorithm on this one.

