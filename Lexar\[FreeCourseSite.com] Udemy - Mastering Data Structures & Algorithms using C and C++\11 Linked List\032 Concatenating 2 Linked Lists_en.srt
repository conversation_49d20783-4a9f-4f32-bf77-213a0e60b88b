1
00:00:00,330 --> 00:00:06,530
Now, in this video, we'll look at concatenation of dueling, plus the topic is very simple.

2
00:00:07,580 --> 00:00:14,420
Here I have to say, this is the first link and this is the second list concatenation since joining

3
00:00:14,420 --> 00:00:22,860
them or offending one lintels with another, littleness like I want to append second linguists to facilities,

4
00:00:23,540 --> 00:00:28,390
it can be done in order way also like it can happen fossilised with signals to anyone.

5
00:00:29,000 --> 00:00:32,770
So it is just like doing first plus second.

6
00:00:32,930 --> 00:00:36,050
And we want to generate combined interest.

7
00:00:37,070 --> 00:00:38,120
Procedure is simple.

8
00:00:38,960 --> 00:00:40,390
Let us look at the procedure.

9
00:00:40,730 --> 00:00:48,080
We have to reach the end of the first lindqvist and instead of null, let it point on first node of

10
00:00:48,080 --> 00:00:53,030
second link list so it becomes a complete single linked list.

11
00:00:53,180 --> 00:00:54,470
One single linguist.

12
00:00:54,980 --> 00:01:03,560
So for this I should take a pointer B and traverse it and make it stop at last node move next, next,

13
00:01:03,560 --> 00:01:04,010
next.

14
00:01:04,250 --> 00:01:07,290
And let us stop at the last node.

15
00:01:07,670 --> 00:01:15,110
So how do we know that Nordiques last node because its next is null, so stop at an order whose next

16
00:01:15,110 --> 00:01:16,100
point that is null.

17
00:01:16,550 --> 00:01:19,600
And once you reach that node, join these two.

18
00:01:19,640 --> 00:01:23,390
So here I will grade on the could be assigned first.

19
00:01:23,390 --> 00:01:28,250
So please on the first node then go on moving to be assigned.

20
00:01:28,250 --> 00:01:29,030
PS next.

21
00:01:29,480 --> 00:01:30,770
How long I should do this.

22
00:01:31,160 --> 00:01:42,080
While B's next is not equal to none if it is not equal to will continue when it is equal to non stop.

23
00:01:42,350 --> 00:01:43,930
Don't move further.

24
00:01:44,480 --> 00:01:49,460
So with the simple loop I can reach here then I have to join them.

25
00:01:49,470 --> 00:01:59,030
So far that make peace an extra point on second PS next point on the first node of sickeningly.

26
00:01:59,120 --> 00:02:02,270
So we have a pointer that is second pointing there.

27
00:02:02,570 --> 00:02:04,400
So secretase.

28
00:02:04,760 --> 00:02:14,210
Second lesson from first I can go on order to two Wilson on nine nine one seven six, six on eight and

29
00:02:14,210 --> 00:02:16,300
eight will give that as of ten and so on.

30
00:02:16,670 --> 00:02:18,890
So this became just one linguist.

31
00:02:19,310 --> 00:02:23,660
Now whether you want that pointer second, still pointing on that or not, it's your choice.

32
00:02:23,660 --> 00:02:30,010
If you say no, no, remove it from there so we can make a pointer second as one.

33
00:02:30,980 --> 00:02:34,880
So pointer second is remove from there, then analysis.

34
00:02:35,450 --> 00:02:36,960
We need one extra pointer.

35
00:02:37,310 --> 00:02:39,170
This is one thing, second thing.

36
00:02:39,500 --> 00:02:42,170
It requires order of any time.

37
00:02:42,370 --> 00:02:47,840
Right, because we have to traverse the pointer piece so that it reaches the last node and the time

38
00:02:47,840 --> 00:02:53,260
taken for reaching the last node depends on the number of northern.

39
00:02:53,990 --> 00:02:59,720
So that is an order of four times out of ten.

40
00:03:00,140 --> 00:03:04,010
That's all about concatenation of two lindqvist.

