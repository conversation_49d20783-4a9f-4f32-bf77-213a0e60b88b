1
00:00:00,890 --> 00:00:08,280
Now, let us look at binary search for performing binary search, the prerequisite or the conditions

2
00:00:08,280 --> 00:00:13,290
stack the list of keys or the elements must be sorted out.

3
00:00:13,910 --> 00:00:17,300
So I have to construct a list of elements, TotalFina elements.

4
00:00:17,300 --> 00:00:22,760
I have begun array sizes, also 15, and the number of elements are also 15 and they are sorted.

5
00:00:22,880 --> 00:00:24,590
So there's the first and foremost condition.

6
00:00:25,880 --> 00:00:31,460
So why called as binary search, because it will always check for a key element in the middle of a sorted

7
00:00:31,460 --> 00:00:35,420
list and it will split the list into two.

8
00:00:35,630 --> 00:00:37,190
So that's how it is binary.

9
00:00:37,460 --> 00:00:38,660
So let us see the working.

10
00:00:39,500 --> 00:00:43,790
For example, I have taken the key element 18, which is presented.

11
00:00:45,080 --> 00:00:51,000
We will search for this element if I'm performing linear search in how many comparisons I can find.

12
00:00:51,410 --> 00:00:54,740
Eighteen, one, two, three, four, five.

13
00:00:54,950 --> 00:00:57,320
So in five comparisons I'll be reaching there.

14
00:00:57,770 --> 00:01:01,060
Now let us see how binary search works so far.

15
00:01:01,070 --> 00:01:02,270
Performing binary search.

16
00:01:02,270 --> 00:01:04,370
We need three index variables.

17
00:01:04,530 --> 00:01:15,280
So those are lower, higher and mid and this is low plus high divided by two.

18
00:01:15,470 --> 00:01:23,170
And we will take the floor value now using these three will perform binary search.

19
00:01:23,210 --> 00:01:27,520
So what is the low low should be initially pointing on in next zettl?

20
00:01:27,530 --> 00:01:32,450
That is the beginning of a list and he should be pointing on the end of a list.

21
00:01:32,870 --> 00:01:34,490
So I spot our example.

22
00:01:34,670 --> 00:01:35,800
Low is zero.

23
00:01:35,900 --> 00:01:37,340
Height is 14.

24
00:01:38,210 --> 00:01:39,270
This is initial.

25
00:01:39,630 --> 00:01:41,740
Now let us perform repeating procedure.

26
00:01:42,050 --> 00:01:51,290
Find out MIDA zero plus 14 by two zero plus 14 by two as seven as seven seven.

27
00:01:53,030 --> 00:01:55,520
So he flew on high to give uncalculated murder.

28
00:01:55,820 --> 00:01:59,660
Now check if 18 key element is present here.

29
00:02:00,050 --> 00:02:02,490
No, this is not the key element we are searching for.

30
00:02:03,090 --> 00:02:07,100
Then our key element is its smaller, greater, a smaller.

31
00:02:07,250 --> 00:02:13,940
So it is on the left hand side how you can see that, because the list of sorted is the key element

32
00:02:13,940 --> 00:02:17,890
that we are searching is greater than this one, then definitely it is on the right hand side.

33
00:02:18,290 --> 00:02:20,980
But our key element is getting sort of on the left hand side.

34
00:02:21,470 --> 00:02:27,980
If it is on left hand side, then reduce the list in behalf of a list of all those from low to high

35
00:02:28,430 --> 00:02:29,020
school there.

36
00:02:29,240 --> 00:02:35,360
Now, it should be from low to this point, although this element we have checked like so up to this

37
00:02:35,360 --> 00:02:41,490
point, so more high to make the minus one so high should be moved here.

38
00:02:41,660 --> 00:02:45,800
So this should be quite high, should be changed to seven minus one.

39
00:02:45,800 --> 00:02:49,660
That is six and low remains Z only.

40
00:02:50,150 --> 00:02:55,820
So now we have split into two halves and we are checking for an element in one half because the other

41
00:02:55,820 --> 00:02:57,260
half element cannot be found.

42
00:02:58,040 --> 00:02:59,180
No continued.

43
00:02:59,630 --> 00:03:00,350
Find out.

44
00:03:00,390 --> 00:03:02,810
Miller zero plus six by two.

45
00:03:03,100 --> 00:03:03,960
That is three.

46
00:03:04,280 --> 00:03:06,560
So Numata value is a three.

47
00:03:08,310 --> 00:03:11,800
Is that the key element that you're searching, our element is 18.

48
00:03:12,210 --> 00:03:13,440
No, this is 15.

49
00:03:13,650 --> 00:03:15,620
So key element is greater.

50
00:03:15,660 --> 00:03:18,580
So it is on the right hand side of the list.

51
00:03:18,840 --> 00:03:20,520
So this is the present list.

52
00:03:20,550 --> 00:03:20,940
Now.

53
00:03:21,090 --> 00:03:21,680
And this is.

54
00:03:22,140 --> 00:03:25,310
So the element is on the right hand side in this portion.

55
00:03:25,590 --> 00:03:28,560
So change this law, the middle plus one other.

56
00:03:28,560 --> 00:03:29,610
If you have checked this element.

57
00:03:29,610 --> 00:03:30,860
So don't include this one.

58
00:03:31,140 --> 00:03:36,750
So law should be brought on for so law should be changed to three plus one.

59
00:03:36,750 --> 00:03:39,600
That is Ford and high remains of six only.

60
00:03:41,220 --> 00:03:42,120
Now find out.

61
00:03:42,120 --> 00:03:46,040
Numata Meadowvale is four plus six bitou.

62
00:03:46,980 --> 00:03:48,150
This is five.

63
00:03:48,990 --> 00:03:49,800
This is M..

64
00:03:52,940 --> 00:03:56,720
Check, is it the key element to anyone, this lady No.

65
00:03:56,900 --> 00:04:04,090
18 is less than 21, then check on which side of the list, check on the left hand side of the list.

66
00:04:04,370 --> 00:04:10,590
You can observe that the list of sizes getting ridiculous and every time it is getting divided by two.

67
00:04:11,000 --> 00:04:14,000
So now, so far, checking on the left of a list.

68
00:04:14,150 --> 00:04:15,730
Modified is high.

69
00:04:16,220 --> 00:04:18,260
How should we worded minus one?

70
00:04:18,269 --> 00:04:19,329
Don't include this element.

71
00:04:19,550 --> 00:04:23,570
So it means low volatility for high will also point on the same location.

72
00:04:23,930 --> 00:04:25,940
So many minus one.

73
00:04:25,940 --> 00:04:27,320
So five minus one.

74
00:04:27,320 --> 00:04:32,420
So four higher for so low volatility for now.

75
00:04:32,430 --> 00:04:37,180
Find out Numata four plus four bitou as a four.

76
00:04:37,400 --> 00:04:38,990
So it is also for.

77
00:04:40,640 --> 00:04:43,220
Now, check is in the element that you're searching for.

78
00:04:43,560 --> 00:04:51,020
Yes, elements are found at index four elements found such a successful.

79
00:04:52,380 --> 00:04:56,490
So this is how badly search work total, how many comparisons it has found.

80
00:04:57,120 --> 00:05:01,100
One, two, three, four, and only for comparison we got there.

81
00:05:01,830 --> 00:05:04,020
Let us take one more key and search for it.

82
00:05:04,290 --> 00:05:06,200
I'm taking 34.

83
00:05:06,720 --> 00:05:08,460
So case three for.

84
00:05:11,470 --> 00:05:15,160
The values that we need are low, high ironmen.

85
00:05:16,700 --> 00:05:25,700
Over, you know, the formula format now, they're stock initially lotas here and Hypers is here, so

86
00:05:25,970 --> 00:05:29,670
Loy's Zettl High is 14 Fineman's.

87
00:05:30,350 --> 00:05:31,720
Do we always get the same things?

88
00:05:31,730 --> 00:05:33,350
Hoopla's 14 is always seven.

89
00:05:33,710 --> 00:05:35,020
So we'll be here.

90
00:05:36,110 --> 00:05:38,510
So for this list is all missing.

91
00:05:39,560 --> 00:05:40,490
Anyway, let us check.

92
00:05:40,490 --> 00:05:41,550
What is the number there?

93
00:05:41,570 --> 00:05:45,500
Is it a key thirty for no keys greater than this one.

94
00:05:45,770 --> 00:05:51,670
So key's on the right hand side so change low to mid plus one that is eight.

95
00:05:52,070 --> 00:05:54,490
Now this is the list in which we have to search.

96
00:05:55,190 --> 00:06:00,110
So law will change to eight and he will remain 14.

97
00:06:00,470 --> 00:06:04,010
Now again, calculate murder murders 11.

98
00:06:04,670 --> 00:06:06,910
So murder will move to 11.

99
00:06:07,910 --> 00:06:09,320
So you are on the right hand side.

100
00:06:09,350 --> 00:06:15,230
So the list is divided into half and we are checking on the other half of it because the number cannot

101
00:06:15,230 --> 00:06:16,270
be on the first half.

102
00:06:16,940 --> 00:06:19,730
Now, check, is it the number that we are searching for?

103
00:06:19,730 --> 00:06:21,620
Not three, four is smaller than that.

104
00:06:21,620 --> 00:06:23,570
One third should be checked on the site.

105
00:06:23,750 --> 00:06:31,570
So mortified, high to 10, so high becomes 11 minus one that is 10 and remains means eight.

106
00:06:32,360 --> 00:06:34,700
Now find out murder murders nine.

107
00:06:35,870 --> 00:06:36,650
This is murder.

108
00:06:38,650 --> 00:06:44,020
Is it the number that we are looking for today for no, that is 33, so the number is definitely on

109
00:06:44,020 --> 00:06:44,890
the right hand side.

110
00:06:45,160 --> 00:06:49,720
So mortified, low to mid plus one that is Boothville.

111
00:06:49,720 --> 00:06:52,380
Come on the same place, nine plus one 10.

112
00:06:52,690 --> 00:06:57,490
And this remains generally the final minutes again then only.

113
00:06:57,640 --> 00:06:59,210
So midpoints on same time.

114
00:06:59,800 --> 00:07:01,110
Yes, there's the element.

115
00:07:01,780 --> 00:07:03,950
So you got that element in for competitions.

116
00:07:03,970 --> 00:07:06,150
One, two, three, four, four competitions.

117
00:07:06,160 --> 00:07:08,100
Element is found at index 10.

118
00:07:09,190 --> 00:07:13,370
So this is all we have seen two examples in which search is successful.

119
00:07:13,870 --> 00:07:17,100
Now, let's take one example for unsuccessful search.

120
00:07:17,590 --> 00:07:21,910
I take one element which is not present here, so I'll take value.

121
00:07:21,910 --> 00:07:23,230
That is twenty five.

122
00:07:23,530 --> 00:07:24,190
Twenty five.

123
00:07:24,190 --> 00:07:26,340
It should be after to twenty four, but it's not there.

124
00:07:26,590 --> 00:07:28,770
So let us take a key twenty five and check it.

125
00:07:29,920 --> 00:07:34,600
Key is twenty five, low high.

126
00:07:34,840 --> 00:07:37,000
And these are the three wonders we need.

127
00:07:37,480 --> 00:07:38,920
Lodestars starts from here.

128
00:07:39,010 --> 00:07:40,480
High starts from here.

129
00:07:40,990 --> 00:07:46,210
Now first the value is seven so zero 14 and seven murders here.

130
00:07:47,890 --> 00:07:49,510
Now, is it a key case?

131
00:07:49,540 --> 00:07:54,100
Twenty five, so this is not thirty five, twenty five smaller, so check on the left hand side, so

132
00:07:54,100 --> 00:07:58,120
move high to six, seven minus one.

133
00:07:58,130 --> 00:07:58,900
That is six.

134
00:07:59,050 --> 00:08:01,330
And this is zero murders three.

135
00:08:03,050 --> 00:08:08,240
Gotten rid of this one, so this is the middle of this list from low to high, this the middle one,

136
00:08:08,420 --> 00:08:10,170
is it Ballymun that we are looking for?

137
00:08:10,190 --> 00:08:10,760
Twenty five.

138
00:08:10,760 --> 00:08:11,870
Twenty five is a great time.

139
00:08:12,140 --> 00:08:13,630
So check on the right hand side.

140
00:08:13,640 --> 00:08:18,930
So this will be new low that is plus one low becomes four and hiding in six.

141
00:08:18,950 --> 00:08:20,740
Now find out that is five.

142
00:08:21,230 --> 00:08:27,510
So this is not is it the number that we are searching for 21 nor 35 is greater than that.

143
00:08:27,540 --> 00:08:29,140
So twenty five on the right hand side.

144
00:08:29,420 --> 00:08:30,680
So mortified.

145
00:08:30,680 --> 00:08:32,700
This low and low also becomes six.

146
00:08:32,720 --> 00:08:33,760
That is plus one.

147
00:08:34,100 --> 00:08:35,000
So five plus one.

148
00:08:35,010 --> 00:08:35,870
This is also six.

149
00:08:35,870 --> 00:08:36,799
And this also six.

150
00:08:37,100 --> 00:08:38,860
Finerman this is six.

151
00:08:39,710 --> 00:08:45,200
So right now low and high and all of them are on six now.

152
00:08:45,200 --> 00:08:47,120
Is it the element that we are checking for.

153
00:08:47,270 --> 00:08:47,900
Twenty five.

154
00:08:47,930 --> 00:08:48,260
No.

155
00:08:48,560 --> 00:08:49,940
Twenty five is a greater.

156
00:08:51,300 --> 00:08:58,170
So modify law, if it is agreed, that will modify law, so law will become more MIDA plus one.

157
00:08:58,770 --> 00:09:02,540
So what is right now it is six so long will become seven.

158
00:09:02,910 --> 00:09:07,210
So Megapolis one law will become seven and height is a six.

159
00:09:07,230 --> 00:09:09,000
Only now we will stop.

160
00:09:09,220 --> 00:09:10,400
We will not find me.

161
00:09:10,430 --> 00:09:11,730
No, we will stop.

162
00:09:11,910 --> 00:09:15,780
Reason law has became greater than high.

163
00:09:15,990 --> 00:09:21,510
Lohez became greater than high law should be on the left hand side and high should be on the right hand

164
00:09:21,510 --> 00:09:21,830
side.

165
00:09:22,170 --> 00:09:24,570
But the law became greater than high.

166
00:09:24,570 --> 00:09:28,590
So it means the element is not there in the least.

167
00:09:29,130 --> 00:09:31,340
So searches unsuccessful.

168
00:09:31,590 --> 00:09:33,640
So this search is unsuccessful.

169
00:09:33,930 --> 00:09:37,100
So these two guys were found and divided in the list.

170
00:09:37,470 --> 00:09:38,960
Twenty five is not on the list.

171
00:09:39,480 --> 00:09:41,750
Now get to see how many comparisons it has taken.

172
00:09:42,510 --> 00:09:46,350
One, two, three, four, four competitions.

173
00:09:46,350 --> 00:09:47,640
And here we don't do anything.

174
00:09:47,680 --> 00:09:49,590
Stop saying for the competition.

175
00:09:49,590 --> 00:09:50,430
We got the result.

176
00:09:50,850 --> 00:09:53,050
That element is not there.

177
00:09:54,270 --> 00:09:56,050
So this is how my research works.

178
00:09:56,070 --> 00:09:58,310
Now, let us write the algorithm for this.

