1
00:00:00,120 --> 00:00:05,939
In the previous video, already we have got the introduction of a string in this section, we will be

2
00:00:05,939 --> 00:00:12,150
writing functions upon string, so commonly used functions we will write and most of these functions

3
00:00:12,150 --> 00:00:18,600
are available as library functions or the class members in C and C++.

4
00:00:19,050 --> 00:00:25,830
So we learn how D function works so that if required, we can develop our own logic for performing any

5
00:00:25,830 --> 00:00:27,230
operation which is not available.

6
00:00:28,080 --> 00:00:33,390
So let us start with the first operation that is finding the length of our string, so already I have

7
00:00:33,390 --> 00:00:35,580
an example string here we want to find out.

8
00:00:35,790 --> 00:00:42,720
That means the number of characters present in the string so far, finding the number of characters

9
00:00:42,720 --> 00:00:45,030
in this early or a string.

10
00:00:45,340 --> 00:00:53,970
We have to count all the characters until we reach zero like zero one, two, three, four, five,

11
00:00:53,970 --> 00:00:55,590
six, seven.

12
00:00:55,620 --> 00:01:02,040
We got zero means there are total seven characters, the length of Australia's seven one, two, three,

13
00:01:02,040 --> 00:01:04,060
four, five, six, seven.

14
00:01:04,410 --> 00:01:08,370
There are seven alphabets as they and this is all starting from zero onwards.

15
00:01:08,380 --> 00:01:10,710
So at seven we have zero.

16
00:01:11,280 --> 00:01:12,330
So this is how we count.

17
00:01:12,820 --> 00:01:20,490
If I raise a question, why don't we know the land offer array array sizes eight zero seven eight minus

18
00:01:20,490 --> 00:01:22,650
one is the size of the string.

19
00:01:23,040 --> 00:01:27,720
But already in the previous video I said that the length of array can be anything.

20
00:01:27,720 --> 00:01:30,640
The size of an array can be bigger than the size of the four string.

21
00:01:31,020 --> 00:01:33,770
So how do we know where we have a string?

22
00:01:33,780 --> 00:01:35,620
So we identified by zero.

23
00:01:35,880 --> 00:01:41,790
So yes, the method for finding a LENTO first thing is we have to find Siedel.

24
00:01:42,150 --> 00:01:43,040
So let us take.

25
00:01:43,440 --> 00:01:44,850
So the procedure is very simple.

26
00:01:44,850 --> 00:01:49,970
Just we have to scan through this array of characters until we reach zero.

27
00:01:50,250 --> 00:01:56,940
So we have to travel to this one so that we can take an integer pointer eye and we can go on looking

28
00:01:56,940 --> 00:01:59,340
at the character for zero.

29
00:01:59,460 --> 00:02:00,680
Is it zero?

30
00:02:00,690 --> 00:02:01,140
No.

31
00:02:01,380 --> 00:02:02,460
Then move to the next.

32
00:02:02,610 --> 00:02:03,240
Next.

33
00:02:03,240 --> 00:02:05,110
Is it is it zero?

34
00:02:05,400 --> 00:02:05,790
No.

35
00:02:05,790 --> 00:02:06,170
No.

36
00:02:06,330 --> 00:02:07,770
Yes, we got zero.

37
00:02:07,770 --> 00:02:09,080
That is null character.

38
00:02:09,150 --> 00:02:09,970
So stop there.

39
00:02:10,440 --> 00:02:11,650
So it will act.

40
00:02:11,760 --> 00:02:13,200
Whichever index we have stopped.

41
00:02:13,370 --> 00:02:15,870
That is the land of Austin let's say.

42
00:02:16,110 --> 00:02:21,450
So I will write on the piece of program called and this finding the length and logic is very important.

43
00:02:21,930 --> 00:02:24,720
We perform all other operations in a similar way.

44
00:02:24,840 --> 00:02:27,000
So let us write on a main function directly.

45
00:02:27,000 --> 00:02:36,450
I will write on here, here Fosterville create a string car string name is S, so I will take a pointer

46
00:02:36,690 --> 00:02:44,040
and I will write a string that is welcome of zero because it is enclosed in double calls.

47
00:02:44,040 --> 00:02:51,050
So C language or C++ compiler will create this as this type of array along with the slideshow.

48
00:02:51,180 --> 00:02:52,870
It will fill that character.

49
00:02:53,430 --> 00:02:55,470
Now I have to scan through this.

50
00:02:55,470 --> 00:03:00,630
So for that I may need a variable that is only then using a for loop.

51
00:03:00,630 --> 00:03:01,790
I can scan for this.

52
00:03:01,830 --> 00:03:07,380
So far I find zero and every time I plus plus.

53
00:03:07,750 --> 00:03:11,370
But how long I should continue then I should stop.

54
00:03:11,560 --> 00:03:14,070
Then this s of I r.

55
00:03:14,070 --> 00:03:16,640
S office slash zero.

56
00:03:16,650 --> 00:03:18,600
That is an old character I should stop.

57
00:03:19,140 --> 00:03:22,090
So if it is not equal to null character, I should continue.

58
00:03:22,410 --> 00:03:28,810
So here the condition should be s offer i.e. not equal to null character.

59
00:03:30,280 --> 00:03:30,640
That's.

60
00:03:32,090 --> 00:03:38,150
So this for loop will start from zero and go on incrementing and it will stop when it has reached this

61
00:03:38,600 --> 00:03:39,290
character.

62
00:03:40,700 --> 00:03:41,940
So this is the follow up.

63
00:03:42,290 --> 00:03:45,740
Now, what I have to do in this one, I don't have to do anything.

64
00:03:45,740 --> 00:03:48,580
It is empty because I just am finding land.

65
00:03:48,920 --> 00:03:49,420
Yes.

66
00:03:49,700 --> 00:03:56,240
So it has to remain empty only now, once you come out of this for loop, the value of I will show the

67
00:03:56,240 --> 00:03:57,140
length of a string.

68
00:03:57,290 --> 00:03:58,850
So, well, I will be stopping.

69
00:03:59,000 --> 00:04:00,170
It will be stopping in.

70
00:04:00,190 --> 00:04:02,290
So what is the site of the value of your index to you.

71
00:04:02,540 --> 00:04:03,740
I value seven.

72
00:04:03,740 --> 00:04:05,730
So Lento of string seven.

73
00:04:05,810 --> 00:04:06,560
So here I will.

74
00:04:08,420 --> 00:04:14,250
So print lenders, percentiles I so I it's the length of a string that's all.

75
00:04:14,270 --> 00:04:16,010
This is the end of the main function.

76
00:04:16,010 --> 00:04:17,930
So I can write return zero and stop.

77
00:04:19,510 --> 00:04:23,500
This is the code or a function for finding the length of a string.

78
00:04:24,580 --> 00:04:30,100
That said, I will not give a demo for this one, so does the student exercice, you have to do it by

79
00:04:30,100 --> 00:04:30,560
yourself.

80
00:04:30,580 --> 00:04:36,820
You write on the score and tested in the C or C++ anyway, way you can write on this piece of code and

81
00:04:36,820 --> 00:04:38,180
check it, that's all.

82
00:04:38,200 --> 00:04:42,600
So similarly, we will look at other simple operations on string incoming videos.

