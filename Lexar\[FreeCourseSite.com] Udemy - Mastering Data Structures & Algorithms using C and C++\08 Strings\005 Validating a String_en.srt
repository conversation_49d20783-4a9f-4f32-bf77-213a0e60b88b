1
00:00:00,470 --> 00:00:06,880
Now, in this video, we will learn how to validate or string whether a given string is valid or not.

2
00:00:07,840 --> 00:00:13,710
Most of the time, while creating a login or an account, we have to mention a user name or password,

3
00:00:14,080 --> 00:00:18,200
and for most passwords we find that a valid password is required.

4
00:00:18,610 --> 00:00:19,390
So what is valid?

5
00:00:19,390 --> 00:00:22,780
Password only alphabet or numbers are allowed.

6
00:00:22,780 --> 00:00:24,390
Special characters are not allowed.

7
00:00:24,640 --> 00:00:26,040
So similar thing.

8
00:00:26,050 --> 00:00:27,130
We are going to check it.

9
00:00:27,310 --> 00:00:32,040
So we have a string here and this is string only alphabet and numbers out there.

10
00:00:32,259 --> 00:00:34,360
So it is a valid string.

11
00:00:34,510 --> 00:00:37,690
If any special character is there, then it is invalid.

12
00:00:37,900 --> 00:00:42,340
For example, I will change one of the alphabet and make it as a question mark like this, a special

13
00:00:42,340 --> 00:00:42,830
character.

14
00:00:43,150 --> 00:00:46,630
So if it is a special character, then it is invalid string.

15
00:00:47,540 --> 00:00:50,540
Right, so this is what we have to check, whether it is valid or not.

16
00:00:51,400 --> 00:00:52,570
So there are two methods.

17
00:00:52,600 --> 00:00:54,950
One is a simple method that I'm going to show you.

18
00:00:55,330 --> 00:00:58,090
Second method is using regular expressions.

19
00:00:58,210 --> 00:01:01,810
So if you want to learn about regular expression, you can learn and use it.

20
00:01:02,140 --> 00:01:06,580
Even in C C++ programs, you can use that knowledge to follow the basic method.

21
00:01:06,850 --> 00:01:12,940
So in this method, what I should do to check whether it is valid or not, I should scan for this entire

22
00:01:12,940 --> 00:01:16,410
string and find each and every alphabet is valid or not.

23
00:01:16,630 --> 00:01:21,680
If any one of the alphabet is not valid, then I should say it is invalid.

24
00:01:21,700 --> 00:01:24,380
String like a yes, it's capitally.

25
00:01:24,460 --> 00:01:26,110
It's OK then then ok.

26
00:01:26,170 --> 00:01:28,390
I questionmark invalid.

27
00:01:28,660 --> 00:01:29,320
So stop.

28
00:01:29,320 --> 00:01:33,610
I don't have to check the rest if suppose this was valid it was L earlier.

29
00:01:33,790 --> 00:01:34,600
So it's valid.

30
00:01:34,600 --> 00:01:36,940
Valid, valid, valid reason then.

31
00:01:37,090 --> 00:01:38,070
So true.

32
00:01:38,080 --> 00:01:40,810
It's string as valid string.

33
00:01:41,350 --> 00:01:45,930
So for performing this procedure I should scan through the string and check for each and every alphabet.

34
00:01:46,270 --> 00:01:51,360
So let us write on a function for that one instead of writing and said the main function I will write

35
00:01:51,370 --> 00:01:52,680
is that I did as a function.

36
00:01:52,690 --> 00:01:53,620
So let us check it.

37
00:01:54,390 --> 00:01:55,920
I'll change it to question mark.

38
00:01:55,950 --> 00:01:57,100
It is invalid, right?

39
00:01:57,300 --> 00:02:02,010
So I like the function function name is valid, right.

40
00:02:02,310 --> 00:02:07,200
And it will take a string of the parameters for character and star name.

41
00:02:08,669 --> 00:02:11,340
So that will appear here and it should return.

42
00:02:11,340 --> 00:02:17,030
Whether it is a true or false means, valid or not, if it is valid, it will return one.

43
00:02:17,370 --> 00:02:18,910
Otherwise it will return zero.

44
00:02:19,390 --> 00:02:23,640
Then what I have to do in this one, I should scan through this a string so far that I would take a

45
00:02:23,640 --> 00:02:24,090
variable.

46
00:02:24,090 --> 00:02:25,770
I am using it for a loop.

47
00:02:25,770 --> 00:02:35,860
I will scan for this for I assign zero and as long as the name of I is not equal to null character continue

48
00:02:36,150 --> 00:02:36,810
I plus.

49
00:02:36,810 --> 00:02:37,200
Plus.

50
00:02:39,340 --> 00:02:46,630
That each time I should check whether Alphabeat is valid or not, so for being valid, it should be

51
00:02:46,630 --> 00:02:50,470
within the range of capital letters, small letters, phone numbers.

52
00:02:50,980 --> 00:02:53,300
If it is not, then it is invalid.

53
00:02:53,650 --> 00:02:57,790
So here I will write on the conditions, just like the commission if.

54
00:02:59,000 --> 00:03:00,320
Name of I.

55
00:03:01,870 --> 00:03:07,180
Is less greater than or equal to sixty five and.

56
00:03:08,280 --> 00:03:14,440
Name of I is less than equal to 90, so what is this?

57
00:03:14,730 --> 00:03:16,220
This is for valid range.

58
00:03:16,890 --> 00:03:22,460
Yes, it is valid range, valid abacuses, but I have to check for invalid.

59
00:03:22,680 --> 00:03:30,200
So instead of taking this, I should say not of this means it is not in this one.

60
00:03:30,390 --> 00:03:35,240
If it is not in this one, if it is not in this one, then it is invalid.

61
00:03:35,880 --> 00:03:36,260
Right.

62
00:03:36,720 --> 00:03:39,270
But other ranges are also there.

63
00:03:39,450 --> 00:03:40,860
Lower cases are also there.

64
00:03:41,070 --> 00:03:46,210
So I should write and I should check for the next set of characters.

65
00:03:46,230 --> 00:03:51,110
That is small liquor's then again not of small letter.

66
00:03:51,300 --> 00:04:00,510
That is the name of AI is a good it doesn't equal to ninety seven and name of IHI is less than equal

67
00:04:00,510 --> 00:04:10,220
to 122 and means it is not in this range and not in this range and not out of the range.

68
00:04:10,890 --> 00:04:15,700
So it means it's not in this range and not in this range as well as phone numbers.

69
00:04:15,720 --> 00:04:18,329
Also I should try it on so I would not complete this one.

70
00:04:18,329 --> 00:04:23,670
So again, I have to say the name of that is forty eight to fifty seven.

71
00:04:24,090 --> 00:04:24,460
Right.

72
00:04:24,550 --> 00:04:25,710
You can understand this one.

73
00:04:25,980 --> 00:04:31,400
So these three conditions, if it is not among these then is a special character.

74
00:04:31,500 --> 00:04:35,220
So here I should return funds.

75
00:04:35,220 --> 00:04:36,000
That is zero.

76
00:04:36,240 --> 00:04:41,910
And this is all happening inside for a loop like inside the loop for every alphabet.

77
00:04:41,910 --> 00:04:47,160
We are checking whether it is not in this range and not in this range and not in this range, then it

78
00:04:47,160 --> 00:04:54,120
is invalid symbol like invalid letter written zero return false.

79
00:04:54,300 --> 00:05:00,110
Otherwise, if I have came out of the loop by reaching the end of a string, then everything was perfect.

80
00:05:00,120 --> 00:05:02,280
So return one.

81
00:05:05,490 --> 00:05:06,010
That's it.

82
00:05:06,640 --> 00:05:09,170
So this is the validation function.

83
00:05:09,700 --> 00:05:11,480
Now they're inside the main function.

84
00:05:11,500 --> 00:05:17,830
I will write on if validate send the name.

85
00:05:18,940 --> 00:05:24,700
If it returns are true, if the sojourns true, then this will be true, then we will print just as

86
00:05:24,710 --> 00:05:26,190
right here for printing.

87
00:05:26,530 --> 00:05:36,870
Then I will say valid string or valid string right then otherwise in the spot I will write print.

88
00:05:37,090 --> 00:05:38,490
That is just I'm saying pdf.

89
00:05:38,830 --> 00:05:40,690
So it is invalid.

90
00:05:40,930 --> 00:05:44,860
String means if it is false then it is invalid string.

91
00:05:48,830 --> 00:05:53,990
The main function for in this function, we haven't done a lot of conditions, so, yes, a lot of rules

92
00:05:53,990 --> 00:05:55,780
are dead and lot of conditions will be there.

93
00:05:56,060 --> 00:05:58,740
So we have seen a validation function.

94
00:05:59,000 --> 00:06:00,320
You can try this function.

95
00:06:00,320 --> 00:06:02,090
You can write on this function by your.

