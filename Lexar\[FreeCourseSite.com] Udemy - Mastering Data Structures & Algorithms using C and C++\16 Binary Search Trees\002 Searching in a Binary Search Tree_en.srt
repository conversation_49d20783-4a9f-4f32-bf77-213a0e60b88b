1
00:00:00,180 --> 00:00:03,780
Now, let us look at searching in binary search tree.

2
00:00:04,860 --> 00:00:11,100
I have an example of binary research here, I will show you the procedure by taking some keys somewhere,

3
00:00:11,100 --> 00:00:16,860
they want to search for a key value that is twenty five then for searching for if I you can see that

4
00:00:16,860 --> 00:00:17,970
25 percent here.

5
00:00:18,390 --> 00:00:21,690
Start from Ruediger, pretty point guard on the route of North.

6
00:00:23,170 --> 00:00:24,470
No, let us see the procedure.

7
00:00:24,580 --> 00:00:31,900
What are other steps, whenever disappointing check for the data and compare it with the key keys smaller

8
00:00:31,900 --> 00:00:32,580
than the data.

9
00:00:32,600 --> 00:00:36,360
Safety is a small move on the left side.

10
00:00:37,420 --> 00:00:43,450
The next same thing repeat repeating step wherever d pointing, collect the data and compare it with

11
00:00:43,450 --> 00:00:47,660
the key keys greater than data in the snow.

12
00:00:47,980 --> 00:00:56,350
So move to on the right side then I repeat, check wherever disappointing data compare it.

13
00:00:56,350 --> 00:01:00,090
The same for element is found in that particular node.

14
00:01:00,660 --> 00:01:02,420
So that's also just very simple.

15
00:01:03,010 --> 00:01:08,640
So in how many comparisons I got the Saliman one, two, three, three comparisons.

16
00:01:08,650 --> 00:01:09,550
I got the element.

17
00:01:10,540 --> 00:01:17,320
Now, I'll take one more key element, keys 40, start from route and start the procedure, compare

18
00:01:17,320 --> 00:01:22,660
data with the key keys greater than this data move would be on the right side.

19
00:01:23,140 --> 00:01:31,600
Then again, repeat key with their data keys, equal matching element found in how many Cumbrian we

20
00:01:31,600 --> 00:01:34,550
found just in to comprehensive on this.

21
00:01:35,440 --> 00:01:39,690
So it means if I am searching for 30, I'll get it in this one competition.

22
00:01:40,180 --> 00:01:43,060
So maximum, how many comparisons are required for searching?

23
00:01:43,240 --> 00:01:48,640
If I'm searching for these elements which are in the leaf, then the maximum comparisons are depending

24
00:01:48,640 --> 00:01:49,690
on the height of the tree.

25
00:01:50,970 --> 00:01:59,250
Yes, the time taken for searching depends on how big of a tree, so I can say that the time is where

26
00:01:59,250 --> 00:02:04,190
we know when that height of a binary tree can be minimum Log-in and Zemo.

27
00:02:04,260 --> 00:02:07,460
And so height can be from logging to end.

28
00:02:08,250 --> 00:02:11,610
So we can go for a minimum one and see this logging.

29
00:02:11,850 --> 00:02:13,200
So minimum payments log in.

30
00:02:13,440 --> 00:02:17,070
So the time taken for searching in a binary search, trees logging.

31
00:02:17,610 --> 00:02:24,890
Next, we look at unsuccessful search these two searches, but key elements were present in the tree.

32
00:02:25,050 --> 00:02:26,490
So there's a successful search.

33
00:02:26,670 --> 00:02:29,270
If the key element is not present, then what happens?

34
00:02:29,580 --> 00:02:31,560
Let us take a key element, Greven.

35
00:02:33,340 --> 00:02:39,500
Start searching, start from route, dumping data with the key key is a smaller than data.

36
00:02:39,760 --> 00:02:40,360
So go on,

37
00:02:43,120 --> 00:02:44,770
compare data with the key.

38
00:02:44,770 --> 00:02:46,400
Key is greater than this data.

39
00:02:46,690 --> 00:02:53,220
So move on right site compared with the data keys is smaller than this one.

40
00:02:53,230 --> 00:02:53,650
So.

41
00:02:55,570 --> 00:02:59,800
Move on the left side became another.

42
00:03:00,720 --> 00:03:07,730
On the left side of qualify, it was not so TB game, so we have reached the end of binary search tree,

43
00:03:08,010 --> 00:03:10,170
so it means the element is not found.

44
00:03:10,560 --> 00:03:11,850
Unsuccessful search.

45
00:03:12,210 --> 00:03:14,360
So an unsuccessful search, also maximum.

46
00:03:14,370 --> 00:03:15,700
How many competitions are done?

47
00:03:16,110 --> 00:03:17,200
One, two, three.

48
00:03:17,340 --> 00:03:18,000
This is No.

49
00:03:18,020 --> 00:03:19,230
So we don't do anything.

50
00:03:19,560 --> 00:03:23,160
So maximum competitions are equal to the height of a tree.

51
00:03:23,520 --> 00:03:28,410
So height we assume that at this minimum, we don't assume that it is maximum.

52
00:03:28,740 --> 00:03:30,810
We assume at this minimum for the Sloggett.

53
00:03:31,350 --> 00:03:38,090
Next, I will write on a recursive procedure for performing such a binary search tree function.

54
00:03:38,100 --> 00:03:47,520
They are such recursive search, which takes pointer to a node cornetist.

55
00:03:47,880 --> 00:03:50,130
And the key element that we want to search.

56
00:03:51,340 --> 00:03:57,730
What it will done right at the end, every time, what we have to do, whether that is some or not,

57
00:03:58,090 --> 00:03:59,910
if it is not known, then we come back.

58
00:04:00,220 --> 00:04:08,320
If it is annulments elements not found and we will stop for the first time and right on if B is null.

59
00:04:09,370 --> 00:04:11,470
I will write on failure condition first.

60
00:04:11,770 --> 00:04:16,420
If it is null, then return null element is not found.

61
00:04:18,160 --> 00:04:26,460
Otherwise what compare if key is equal to a piece of data.

62
00:04:27,070 --> 00:04:35,080
If it is matching then the keys phone then return to third should return appointed to a node in which

63
00:04:35,080 --> 00:04:36,550
the key is phone.

64
00:04:38,590 --> 00:04:39,340
As.

65
00:04:41,060 --> 00:04:51,130
It's Ghys Lasdun these data is on the left hand side, then perform search on the left subtree recursively

66
00:04:51,280 --> 00:04:54,970
so it will call itself recursively or search.

67
00:04:56,820 --> 00:05:09,630
By passing these left child anarchy, this result will be written, otherwise it's not matching, it's

68
00:05:09,630 --> 00:05:12,300
not less than definitely it is a greater.

69
00:05:12,480 --> 00:05:18,930
So search in the right subtree are search and right subtree.

70
00:05:19,410 --> 00:05:23,870
So this is a recursive function for searching in binary search tree.

71
00:05:24,570 --> 00:05:28,380
So there's a recursive function for searching in a binary search tree.

72
00:05:29,010 --> 00:05:36,090
And if you observe fossickers, comparing for the data and the last statement is calling itself either

73
00:05:36,110 --> 00:05:42,510
the statement or the statement, the last statement and a function is a recursive call.

74
00:05:42,930 --> 00:05:45,000
Then it is called as a tail recursion.

75
00:05:45,300 --> 00:05:46,560
So it's a tail recursion.

76
00:05:46,890 --> 00:05:49,290
Anyway, we have solid enough recursions.

77
00:05:49,710 --> 00:05:51,570
You can understand how this is working.

78
00:05:51,570 --> 00:05:53,400
I don't have to trace it and show you.

79
00:05:53,520 --> 00:05:54,200
So that's it.

80
00:05:54,570 --> 00:05:56,190
This takes longer time.

81
00:05:56,430 --> 00:05:59,250
That is equal to the height of a tree for searching.

82
00:05:59,760 --> 00:06:04,170
Now let us look at iterative, functional, iterative function that is using loop.

83
00:06:04,320 --> 00:06:10,670
So the search function which takes a pointer to a root node and the key, the key that we want to search,

84
00:06:11,040 --> 00:06:16,690
suppose we want to search for a key twenty five then to start from root.

85
00:06:16,840 --> 00:06:17,910
So these are true.

86
00:06:17,940 --> 00:06:21,000
So when you call this function will be passing, routed to this function.

87
00:06:21,450 --> 00:06:25,110
Now what it does every time, check the data with the key.

88
00:06:26,310 --> 00:06:26,850
If.

89
00:06:28,610 --> 00:06:29,540
He is.

90
00:06:30,770 --> 00:06:37,280
Equal to these data, if gives equal to these data.

91
00:06:38,180 --> 00:06:40,400
The data is found so that on.

92
00:06:42,580 --> 00:06:49,270
Ft data is matching, if it is not matching check, if it is less, if it is not matching check, if

93
00:06:49,270 --> 00:06:51,580
it is less, if it is less, then go to the site.

94
00:06:52,120 --> 00:06:55,700
So else if it kiselev, then these data.

95
00:06:55,840 --> 00:06:59,530
If so, then move to the left hand side.

96
00:07:00,070 --> 00:07:03,720
They will move the left hand side, see our business model.

97
00:07:03,730 --> 00:07:05,440
So it has to move on the left hand side.

98
00:07:05,710 --> 00:07:10,330
Otherwise, if the key is greater than it has to move on the right hand side in every step, it has

99
00:07:10,330 --> 00:07:11,200
to do these things.

100
00:07:11,530 --> 00:07:14,850
So else move these up on the right side.

101
00:07:15,520 --> 00:07:19,350
So I have already checked the condition for equal, unless otherwise it is great.

102
00:07:20,290 --> 00:07:23,110
So this process I have to repeat, I have to repeat.

103
00:07:23,380 --> 00:07:24,910
So this should come in the loop.

104
00:07:25,060 --> 00:07:28,940
Why let me try this once once again for the key.

105
00:07:28,960 --> 00:07:31,170
Twenty five days waiting here.

106
00:07:31,180 --> 00:07:32,020
That is the truth.

107
00:07:32,230 --> 00:07:35,110
That first step is equal to data.

108
00:07:35,110 --> 00:07:36,430
Is it equal to that or no.

109
00:07:36,790 --> 00:07:38,050
Is it less keys.

110
00:07:38,060 --> 00:07:38,410
Less.

111
00:07:38,410 --> 00:07:38,800
Yes.

112
00:07:39,070 --> 00:07:43,150
Move on left side than continue.

113
00:07:44,780 --> 00:07:48,340
Again, check is it matching with the data, the 20 and.

114
00:07:48,920 --> 00:07:54,320
They are not saying, is it less sickies, less to Nephites, less than 20, no greater.

115
00:07:54,350 --> 00:07:55,850
So more on the right hand side.

116
00:07:57,930 --> 00:08:02,870
Then continue once again, not check, is it matching, yes, matching return.

117
00:08:03,030 --> 00:08:03,840
It will stop.

118
00:08:05,060 --> 00:08:08,940
So found, suppose this was not a twenty five.

119
00:08:08,960 --> 00:08:10,220
This was twenty six.

120
00:08:11,440 --> 00:08:13,820
Then is it matching with the data?

121
00:08:13,840 --> 00:08:14,200
No.

122
00:08:14,830 --> 00:08:17,380
Is it less than this one case, not less.

123
00:08:17,410 --> 00:08:18,150
It's a great answer.

124
00:08:18,190 --> 00:08:20,800
More on the right side, more on the right side.

125
00:08:20,980 --> 00:08:24,540
The right side is not he's not continue.

126
00:08:24,580 --> 00:08:26,680
No, no need to continue to be criminal.

127
00:08:27,010 --> 00:08:32,460
So it means this repetition we should do as long as it is not known.

128
00:08:33,640 --> 00:08:35,530
If this becomes not, then stop.

129
00:08:36,460 --> 00:08:39,309
And when you stop it, come out of the loop, then say.

130
00:08:40,510 --> 00:08:41,320
All right, Don.

131
00:08:44,130 --> 00:08:44,590
None.

132
00:08:45,570 --> 00:08:46,450
So this return.

133
00:08:49,010 --> 00:08:54,270
We don't know when the key is not found, if the key is found, it will return here.

134
00:08:54,980 --> 00:08:57,490
So that's all with the search process.

135
00:08:57,500 --> 00:08:58,200
So this is a.

136
00:08:59,180 --> 00:09:03,370
So that's all this is a great version of search.

137
00:09:04,160 --> 00:09:09,800
So that's all it really was, you know, search on a binary search tree to search them.

138
00:09:09,800 --> 00:09:10,870
Already we have analyzed.

139
00:09:10,870 --> 00:09:13,030
So just log height of a tree.

140
00:09:13,280 --> 00:09:14,050
So that's all.

141
00:09:14,450 --> 00:09:19,800
This is a search procedure for searching in a binary search and this iterative.

142
00:09:20,240 --> 00:09:25,370
Not one thing I would like to show you here that we saw recursive function in a recursive function.

143
00:09:25,670 --> 00:09:27,090
It was the tail recursion.

144
00:09:27,620 --> 00:09:34,610
Now, once we learn that when you convert or recursion into iteration, then you may need stack.

145
00:09:35,060 --> 00:09:37,400
But here we don't need any STAC.

146
00:09:38,330 --> 00:09:40,340
This is implemented just using a loop.

147
00:09:40,340 --> 00:09:41,780
We don't need any static.

148
00:09:42,170 --> 00:09:44,180
So all this it's not required.

149
00:09:44,300 --> 00:09:50,240
And whether the Sattell recursion definitely you don't require effective recursion is converted into

150
00:09:50,240 --> 00:09:53,300
a loop, then you don't need static.

151
00:09:54,890 --> 00:09:55,540
So that's it.

