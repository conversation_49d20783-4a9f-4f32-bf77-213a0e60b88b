1
00:00:00,740 --> 00:00:07,330
In this section I will be revising the concepts of C and C++ programming that are used in this course

2
00:00:07,710 --> 00:00:11,160
as the courses data structures just using C and C++.

3
00:00:11,160 --> 00:00:13,350
Basically the course is in C language.

4
00:00:13,950 --> 00:00:18,140
Plus I have also shown how to write on the code in C++.

5
00:00:18,140 --> 00:00:21,710
Also it is added advantage if you know C++.

6
00:00:21,750 --> 00:00:27,000
Even if you don't know C++ it is sufficient to know C language so you should be already knowing basics

7
00:00:27,000 --> 00:00:27,770
of C programming.

8
00:00:27,780 --> 00:00:33,390
We should already have some experience of C programming under the concepts of C programming that are

9
00:00:33,390 --> 00:00:35,100
frequently used in this course.

10
00:00:35,100 --> 00:00:40,230
I'm going to revise them so you are well acquainted or well familiar with those concepts and you

11
00:00:40,230 --> 00:00:43,050
can easily understand the rest of the topics.

12
00:00:43,170 --> 00:00:49,040
So in this section, I'll be going to discuss about basic subject areas though we have a big topic on arrays

13
00:00:49,080 --> 00:00:51,030
there are a lot of things we are going to learn about.

14
00:00:51,360 --> 00:00:57,810
But here basics about arrays, I will discuss then structures in C language, then pointers, What are pointers

15
00:00:57,810 --> 00:01:06,360
you will study, references parameter passing in C as well as C++, then classes, constructors and templates.

16
00:01:06,360 --> 00:01:12,720
These topics are related to C++, these topics are in C language, so these topics are more frequently used

17
00:01:12,990 --> 00:01:16,320
and how do we use them and what is the approach I'm following.

18
00:01:16,320 --> 00:01:20,180
You can understand that approach from these topics. And one more thing.

19
00:01:20,460 --> 00:01:25,560
If you are already very familiar with C++ programming and you are a good programmer I suggest you to

20
00:01:25,590 --> 00:01:32,700
go through this section so that you understand style of programming that I have adopted in this course and

21
00:01:32,700 --> 00:01:38,250
you get very familiar and it will be very easy for you to understand the rest of the topics.

22
00:01:38,400 --> 00:01:40,380
So let us start with Arrays.

23
00:01:40,380 --> 00:01:44,600
So, right now I will discuss a little bit about arrays, then in coming videos,

24
00:01:44,610 --> 00:01:46,660
We will learn about all these things one by one.

25
00:01:47,420 --> 00:01:53,810
So let us start with arrays. Arrays are defined as a collection of similar data elements.

26
00:01:53,880 --> 00:02:00,000
If you have some set of integers or set of floats, you can group them under one name as an array.

27
00:02:00,270 --> 00:02:04,640
See the method of declaring an array is, if you want an integer type,

28
00:02:04,660 --> 00:02:06,840
let's say, int A[5]

29
00:02:06,840 --> 00:02:13,320
Then you get 5 integers, array name is A, and all those integers you can access them by the name A.

30
00:02:13,890 --> 00:02:22,650
So this is an array and you get 5 integer spaces and the indices will be 0 1 2 3 4 . So, 5 locations

31
00:02:22,650 --> 00:02:27,940
means the indices are from 0 to 4, total 5 spaces.

32
00:02:28,070 --> 00:02:32,040
Now I can store five digits, every location is an integer.

33
00:02:32,090 --> 00:02:34,730
Every location Integer, and if suppose, integer,

34
00:02:34,730 --> 00:02:40,720
If we assume integer is taking 2 bytes, then total will be 10 bytes; 2 bytes each.

35
00:02:40,720 --> 00:02:46,440
Now, each location can be accessed with the help of index, like A[0] = 27, so I can store 27

36
00:02:46,450 --> 00:02:50,310
at this place. Now, A[1]

37
00:02:50,330 --> 00:02:55,160
If I want to store something, then I can store a value then like this here.

38
00:02:56,120 --> 00:02:59,090
So you can now have the group of elements together at one place.

39
00:02:59,100 --> 00:03:03,600
Now I will show you, how to declare and initialize an array. Here, main ( )

40
00:03:03,610 --> 00:03:10,520
Now in this, suppose, I want to declare an array of size 5, an array of size of 5 will be created.

41
00:03:10,590 --> 00:03:11,900
Now, when the program is running,

42
00:03:11,900 --> 00:03:13,670
It runs inside the main memory.

43
00:03:13,670 --> 00:03:17,980
This is main memory, and the main memory is divided into three sections.

44
00:03:18,020 --> 00:03:25,640
That is code section, and stack and heap. It will be inside the code section and with any variable declared,

45
00:03:25,670 --> 00:03:31,800
like an array is declared, so it will be created here inside the stack.

46
00:03:31,820 --> 00:03:38,190
This is where the array will be created, and that will be directly accessible to the main function; and directly I

47
00:03:38,230 --> 00:03:42,120
can store the values, like already I have shown you I can store some values.

48
00:03:42,230 --> 00:03:44,980
10 5 8 3 9 , we can store them.

49
00:03:46,690 --> 00:03:52,390
This is how I can declare an array and where it will be created in the memory I have shown you.

50
00:03:52,390 --> 00:03:55,950
Next thing I will show you, how to declare and initialize a array,

51
00:03:55,960 --> 00:03:57,710
This is a declaration of an array, and

52
00:03:57,820 --> 00:04:06,770
along with this I can also initialize it like 2 4 6 8 10 . So, array will be created and it will be already

53
00:04:06,770 --> 00:04:07,890
filled with the values.

54
00:04:07,930 --> 00:04:16,860
Like suppose an array B is created inside the stack then it will be filled with the values 2 4 6 8 10.

55
00:04:16,899 --> 00:04:21,230
So this is declaration of an array,

56
00:04:21,290 --> 00:04:27,290
this portion, and, this is initialization of the array. Together I can do. This is just declaration and

57
00:04:27,290 --> 00:04:30,260
this is the declaration as well as initialization.

58
00:04:30,260 --> 00:04:36,410
Now, next thing is how to access an Array. For accessing an Array, we can access all the elements one by one.

59
00:04:36,410 --> 00:04:36,620
.

60
00:04:36,630 --> 00:04:40,490
Suppose I want to print all of them, then I can use a for loop.

61
00:04:40,580 --> 00:04:42,400
So for that I would take one variable.

62
00:04:42,440 --> 00:04:44,950
I am using for loop for

63
00:04:44,990 --> 00:04:52,360
integer i = 0, i < 5, i++ . Then, using printf, I can print it.

64
00:04:52,610 --> 00:04:55,630
So,

65
00:04:56,540 --> 00:04:58,840
printf ( " %d ", B[i] ) ;

66
00:04:59,930 --> 00:05:03,760
You must be familiar with the for loop.

67
00:05:03,850 --> 00:05:07,450
So, i = 0, i < 5, i++.

68
00:05:07,450 --> 00:05:09,710
So, initially, i will be zero.

69
00:05:09,790 --> 00:05:11,770
Then it checks whether I is less than 5.

70
00:05:11,810 --> 00:05:14,620
Then it will bring this, B[0].

71
00:05:14,680 --> 00:05:20,560
So it will be B[0]; B[0] the element will be printed. Then, next time i++, i becomes 1.

72
00:05:20,560 --> 00:05:28,630
1 is less than  5, so it will print B[1] and so on up to B[4] it will print all elements.

73
00:05:28,630 --> 00:05:33,990
So for loop is used for scanning through the list of elements in an array.

74
00:05:34,120 --> 00:05:38,550
More frequently we use for loop for accessing all the elements of an array.

75
00:05:38,590 --> 00:05:39,130
So that's it.

76
00:05:39,160 --> 00:05:44,290
This is sufficient because we have a very big topic called arrays and we will be learning lots and

77
00:05:44,290 --> 00:05:48,490
lots of things about arrays; Just as an introduction, as a revision,

78
00:05:48,490 --> 00:05:49,450
I have done it.

79
00:05:49,450 --> 00:05:50,150
So that's it.

80
00:05:50,170 --> 00:05:51,510
About an Array.

81
00:05:51,700 --> 00:05:55,020
So, the points that I have discussed, let me put them together.

82
00:05:55,150 --> 00:06:01,000
I have explained you what does it mean by an array and how to declare and initialize it and when it is 

83
00:06:01,000 --> 00:06:05,850
declared inside a function then where it will appear inside the main memory.

84
00:06:05,860 --> 00:06:10,630
This is sufficient for us now because we have lot of things to learn about array.

85
00:06:10,630 --> 00:06:13,130
There is a big section called arrays.

86
00:06:13,150 --> 00:06:15,490
So there you will learn everything about arrays.

87
00:06:15,940 --> 00:06:20,230
So, as a revision, I have discussed this much, that is sufficient.

88
00:06:20,230 --> 00:06:22,160
We have to learn about other topics.

89
00:06:22,240 --> 00:06:23,440
Let us continue in other video.

