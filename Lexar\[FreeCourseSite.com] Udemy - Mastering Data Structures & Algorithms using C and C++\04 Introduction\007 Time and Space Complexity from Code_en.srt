1
00:00:00,900 --> 00:00:05,190
Now, let us learn how to find out the complexity from the program code.

2
00:00:05,880 --> 00:00:10,120
So when we analyze the program gold, we get a proper function, time function.

3
00:00:10,500 --> 00:00:12,120
So how do we get the time functions?

4
00:00:12,180 --> 00:00:13,350
We will learn about this.

5
00:00:13,890 --> 00:00:16,890
So for explaining, I have taken three example program codes.

6
00:00:17,140 --> 00:00:20,930
That is three functions I have taken now to analyze.

7
00:00:21,300 --> 00:00:26,080
We assume that every single statement in function or the program takes one unit of time.

8
00:00:26,670 --> 00:00:28,370
What does it mean by a simple statement?

9
00:00:28,830 --> 00:00:33,540
The statement may be having arithmetic operations assignment or conditional statement.

10
00:00:35,190 --> 00:00:41,760
However, it is more complex than we have to study that also in detail, so let us see how to get the

11
00:00:42,150 --> 00:00:44,310
information and how to find that complexity.

12
00:00:44,610 --> 00:00:45,940
So first, I will take this one.

13
00:00:47,190 --> 00:00:51,390
This is a function taking two parameters and interchanging the values of X and Y.

14
00:00:53,160 --> 00:00:59,030
What are the statements inside these are the three statements, how much time each segment takes one

15
00:00:59,040 --> 00:01:02,990
unit of time because it is just an assignment, so total time is three.

16
00:01:03,180 --> 00:01:08,370
So the function of an as a three, so you got the time function as three.

17
00:01:08,700 --> 00:01:11,190
So that three is constant.

18
00:01:11,370 --> 00:01:18,500
Order of one word is constant because the degree of end is zero here, three minutes three and the of

19
00:01:19,110 --> 00:01:19,820
that is the meaning.

20
00:01:20,220 --> 00:01:21,930
So anything of zero is written.

21
00:01:21,930 --> 00:01:24,330
That's what one side is constant.

22
00:01:24,930 --> 00:01:28,120
So this was a simple function and it is taking constant.

23
00:01:28,140 --> 00:01:29,810
So it's not a proper function.

24
00:01:29,820 --> 00:01:33,350
I have not written data and all this pseudocode I have written.

25
00:01:34,380 --> 00:01:36,590
Now, this is exactly legacy functions.

26
00:01:36,600 --> 00:01:37,740
Let us look at this one.

27
00:01:38,980 --> 00:01:39,350
Here.

28
00:01:41,050 --> 00:01:45,340
Let us look at the statement first, the statement assignment one year of time.

29
00:01:47,090 --> 00:01:53,810
Then assignment, condition, assignment, this is plus plus increment, just an operator, but.

30
00:01:54,650 --> 00:01:56,490
Is it executed just one time?

31
00:01:56,720 --> 00:02:01,570
No, the salute, this statement will execute 14 times.

32
00:02:02,300 --> 00:02:08,690
See if you follow this will execute four one time initially and this increment will happen four times,

33
00:02:09,590 --> 00:02:11,330
condition also ten times.

34
00:02:11,330 --> 00:02:13,520
But one time the conviction will fail.

35
00:02:13,520 --> 00:02:14,280
So it will stop.

36
00:02:14,690 --> 00:02:16,700
So this is total and plus one.

37
00:02:18,570 --> 00:02:24,190
So we to ignore all of that and take just one plus one, so just for look, we are looking at this one.

38
00:02:24,210 --> 00:02:25,960
Just follow this one.

39
00:02:26,370 --> 00:02:27,060
Let us say time.

40
00:02:27,090 --> 00:02:28,190
Take a listen to this one.

41
00:02:30,190 --> 00:02:32,530
Then you can ask me why you are leaving this one and this one.

42
00:02:32,560 --> 00:02:36,460
OK, if you want to include, then it should be two and last one.

43
00:02:36,700 --> 00:02:37,510
That is your choice.

44
00:02:38,020 --> 00:02:40,570
But mostly we find like this in textbooks.

45
00:02:40,570 --> 00:02:41,780
Every author says this one.

46
00:02:42,070 --> 00:02:44,490
Mostly we focus on this conditional statement.

47
00:02:44,500 --> 00:02:46,290
So I'm reading the conditional statement.

48
00:02:46,300 --> 00:02:50,290
Otherwise, if you sum up all this, this is two times of endless fun.

49
00:02:52,180 --> 00:02:57,790
The next what is there in this statement, there's a simple assignment and arithmetic operation, Ed.

50
00:02:58,180 --> 00:03:02,800
How many times as long as spending how many times Loop will run and times?

51
00:03:02,890 --> 00:03:03,670
This is an.

52
00:03:05,020 --> 00:03:11,920
The last return is how many times this will execute just one time so total, how much I'll find the

53
00:03:11,920 --> 00:03:13,810
sum of all these two times.

54
00:03:13,810 --> 00:03:20,940
And so the time function is to and plus, one, two, three, two, three.

55
00:03:21,310 --> 00:03:22,840
This is the time function we got.

56
00:03:23,020 --> 00:03:24,850
And what is the degree of this polynomial?

57
00:03:25,270 --> 00:03:27,780
This is order of N degree one.

58
00:03:27,800 --> 00:03:32,050
So we and so the time sort of n no order.

59
00:03:32,050 --> 00:03:36,550
In the previous video I have shown you that when you have a follow up, which is repeating four times

60
00:03:36,550 --> 00:03:42,700
going from zero to N and eight plus plus every time it's obviously N, so even when I have checked each

61
00:03:42,700 --> 00:03:45,280
and every line, also final answer is rough.

62
00:03:45,440 --> 00:03:52,960
And that's what if you focus on the processing, then you can directly answer this one.

63
00:03:53,290 --> 00:03:57,130
If you are interested in this function, then you can get it line by line.

64
00:03:57,160 --> 00:03:58,010
You get it.

65
00:03:59,110 --> 00:04:00,280
So let us see the procedure.

66
00:04:00,280 --> 00:04:03,310
Actually, this is sum of all and the elements in an array.

67
00:04:04,830 --> 00:04:09,150
Some of all elements and elements in the network, how much time it will take, it depends on a number

68
00:04:09,150 --> 00:04:09,810
of elements.

69
00:04:09,810 --> 00:04:17,100
How many elements are there and elements timing so much out of simple doing line by line.

70
00:04:17,459 --> 00:04:18,959
It needs a little practice.

71
00:04:18,990 --> 00:04:21,290
OK, so I have shown you two examples.

72
00:04:21,570 --> 00:04:25,510
So based on this, we can practice and find out the time from the code also.

73
00:04:25,950 --> 00:04:27,610
Now, the last one remaining is this one.

74
00:04:28,770 --> 00:04:30,600
Let us find out the time, so I'll remove that.

75
00:04:31,170 --> 00:04:33,570
Now this one this is a for loop.

76
00:04:33,570 --> 00:04:39,030
As I said, this will take and plus one time so and plus one times the execution will happen.

77
00:04:39,450 --> 00:04:44,260
Not anything inside the loop will execute four and times every statement.

78
00:04:44,490 --> 00:04:45,870
So what is there inside this loop.

79
00:04:46,140 --> 00:04:47,090
This one is there.

80
00:04:47,400 --> 00:04:47,850
This one.

81
00:04:48,210 --> 00:04:48,960
And this one.

82
00:04:50,790 --> 00:04:57,690
So I have finished with outer loop of the loop is over, not inside, that is not a normal, simple

83
00:04:57,690 --> 00:04:58,380
statement.

84
00:04:59,760 --> 00:05:05,040
Just like this normal, simple statement I have taken in for this also have taken and then but if you

85
00:05:05,040 --> 00:05:08,700
look at this, this is also a loop, then what about it's time?

86
00:05:09,240 --> 00:05:14,450
The time for this one is how many times the condition is checked and plus one.

87
00:05:14,670 --> 00:05:17,520
So there's already and because it is inside the loop.

88
00:05:17,520 --> 00:05:21,240
So Schlereth plus and plus one not into this one.

89
00:05:22,280 --> 00:05:28,900
Nestor Lopez there, Nestor Lopez there, so it should be into it should be Engo multiplication.

90
00:05:29,330 --> 00:05:32,960
So this is into and plus one.

91
00:05:34,060 --> 00:05:41,140
Then this is this is statemented inside this fall loop also, so this loop repeated for and more time.

92
00:05:41,160 --> 00:05:42,710
So this is in the end.

93
00:05:42,880 --> 00:05:43,540
So that's it.

94
00:05:43,930 --> 00:05:49,040
So if you want, you can watch it again and find out how to do this one right now.

95
00:05:49,060 --> 00:05:52,210
Finally, we can prepare a function time function.

96
00:05:52,210 --> 00:06:00,400
What does the time function and square and square, so to when squared plus an end to end plus one.

97
00:06:01,180 --> 00:06:03,610
This is the time function we got.

98
00:06:04,930 --> 00:06:08,530
What is the degree to say, Zaroff and Square?

99
00:06:09,690 --> 00:06:10,920
Times Square.

100
00:06:12,140 --> 00:06:17,860
This is all from the time function, we get the highest degree of a function and that presented as time

101
00:06:18,110 --> 00:06:24,770
now I am calling it an order of if you are interested, you can see big often square or take off and

102
00:06:24,770 --> 00:06:27,490
square and omegle and square.

103
00:06:27,920 --> 00:06:33,830
But then to use these, we will be learning at the end of the course, as I said, but.

104
00:06:36,180 --> 00:06:43,050
By the time you can even get an order off or even you can use Begal and stuff order, you can be all

105
00:06:43,290 --> 00:06:43,710
right.

106
00:06:43,740 --> 00:06:46,470
But in my retools, I'll be calling it off every time.

107
00:06:47,550 --> 00:06:52,230
And at last, I'll show you one more example, I'll remove these things and I'll write one, a small

108
00:06:52,230 --> 00:06:53,940
example program and I'll explain you.

109
00:06:54,920 --> 00:07:00,350
So here is an example I have just taken a skeleton of to function, function well and function.

110
00:07:01,760 --> 00:07:06,020
Now, if you want to find out the time complexity, as I said, every statement we should consider does

111
00:07:06,380 --> 00:07:07,340
one end of time.

112
00:07:07,550 --> 00:07:09,430
So how much time this function is taking?

113
00:07:09,440 --> 00:07:11,840
I want the time taken by this function.

114
00:07:11,840 --> 00:07:15,470
Only fun one only then what is there inside?

115
00:07:15,650 --> 00:07:16,460
Just one line.

116
00:07:16,880 --> 00:07:17,890
One enough time.

117
00:07:18,100 --> 00:07:19,130
So this constant.

118
00:07:20,550 --> 00:07:21,040
Wrong.

119
00:07:21,820 --> 00:07:22,790
That is a function.

120
00:07:23,810 --> 00:07:29,050
How much time that function is taking, you find out that one don't say that statement is one.

121
00:07:29,450 --> 00:07:31,100
OK, look at this one.

122
00:07:31,490 --> 00:07:35,840
This is having a loop, so loop will take off in time.

123
00:07:36,410 --> 00:07:37,220
So it's an.

124
00:07:38,370 --> 00:07:44,280
So the time taken by this function is not one that is actually any slowing done, the time taken by

125
00:07:44,280 --> 00:07:52,800
this function is also and it's not one, it's not one because it is calling a function which is taking

126
00:07:52,800 --> 00:07:53,600
a different time.

127
00:07:53,850 --> 00:07:59,700
So definitely the time also belongs to someone because no one is using fund two and fund two is taking

128
00:07:59,700 --> 00:08:00,480
a of time.

129
00:08:01,560 --> 00:08:02,250
So that's all.

130
00:08:02,460 --> 00:08:06,300
Look into the details of these statements and try to find all the time.

131
00:08:06,630 --> 00:08:10,710
If there are simple statement, you can directly write on the time as one.

132
00:08:11,160 --> 00:08:14,580
If it is complex, then see what it is doing, then build on that.

133
00:08:14,580 --> 00:08:15,470
You can write all the time.

134
00:08:16,230 --> 00:08:21,810
So mostly loops are making the time as an or in square or maybe any cube.

135
00:08:22,200 --> 00:08:29,070
So mostly depends on loops that you're using file loop for loop or why loop.

136
00:08:29,370 --> 00:08:33,320
They both are similar sometimes to this thing that followed.

137
00:08:33,360 --> 00:08:34,200
OK, understood.

138
00:08:34,380 --> 00:08:35,610
Why loop how to do that.

139
00:08:36,710 --> 00:08:43,659
So far, looking for this repeating from one to an incremental loop counter condola, then it is then

140
00:08:44,059 --> 00:08:48,110
if it is behaving in a different way, then definitely you have to read the code and understand maybe

141
00:08:48,110 --> 00:08:50,360
it is log-in or root and what it is.

142
00:08:50,840 --> 00:08:52,900
You have to find out that that's all in this video.

143
00:08:52,910 --> 00:08:58,220
We have seen the time functions or space complexity already have explained in the previous video.

144
00:08:58,220 --> 00:09:03,280
So space complexity also we can prepare a function so that following this video.

