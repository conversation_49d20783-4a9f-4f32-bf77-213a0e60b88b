1
00:00:00,480 --> 00:00:08,220
This video, let us look at how to create a tree, I already have a set of keys here using these keys,

2
00:00:08,220 --> 00:00:11,260
I have to insert them one by one and generator.

3
00:00:11,350 --> 00:00:11,950
Absolutely.

4
00:00:12,780 --> 00:00:15,000
The process is the same as a binary search.

5
00:00:15,390 --> 00:00:18,270
But also we have to calculate balance factors.

6
00:00:18,540 --> 00:00:22,860
If any notice is becoming imbalance, then we have to balance that node.

7
00:00:23,760 --> 00:00:29,220
So let us start by inserting these keys one by one and generate Aviel three.

8
00:00:29,670 --> 00:00:32,830
So first let us insert a 10 insert.

9
00:00:32,850 --> 00:00:38,190
And so 10 is the only key, not so much the balance factor of 10 zero nine.

10
00:00:38,210 --> 00:00:41,780
So the next key that is 20, I will insert 20 also.

11
00:00:41,790 --> 00:00:46,780
So 20 comes on the right hand side because it's greater than 10 or 20 is the new normal.

12
00:00:47,430 --> 00:00:51,120
If I calculate balance factors, then this is zero.

13
00:00:51,120 --> 00:00:51,780
And this is.

14
00:00:52,820 --> 00:00:53,780
Minus one.

15
00:00:54,930 --> 00:01:02,180
I think balance is zero and minus one hour balance now let us insert one more key that is 30 today,

16
00:01:02,270 --> 00:01:03,210
also inside out.

17
00:01:03,210 --> 00:01:05,660
So 20 is greater than 10 and greater than 20.

18
00:01:05,670 --> 00:01:07,520
So it gets inserted here.

19
00:01:08,250 --> 00:01:10,300
And I have to recalculate the balance factor.

20
00:01:10,330 --> 00:01:14,810
So this is zero and this is minus one and this is the game minus two.

21
00:01:15,030 --> 00:01:17,030
So this nor to begin imbalance.

22
00:01:17,580 --> 00:01:18,780
So which imbalance?

23
00:01:18,780 --> 00:01:20,280
The direction of insertion is.

24
00:01:20,280 --> 00:01:20,630
Right.

25
00:01:20,630 --> 00:01:21,080
Right.

26
00:01:22,110 --> 00:01:25,110
So this is minus two minutes that is in balance due to right side.

27
00:01:25,410 --> 00:01:27,000
And what about its child.

28
00:01:27,000 --> 00:01:27,240
Right.

29
00:01:27,330 --> 00:01:28,620
And that is also minus one.

30
00:01:28,920 --> 00:01:31,050
It's also imbalance on the right side.

31
00:01:31,060 --> 00:01:34,320
So we have to perform are rotation.

32
00:01:34,320 --> 00:01:36,720
So on our revision, we will perform.

33
00:01:39,790 --> 00:01:42,700
Then after the audition, what will be the key, I will it right here.

34
00:01:43,510 --> 00:01:46,230
So this is 20 and this is 10.

35
00:01:46,450 --> 00:01:52,050
And the study, the balance factors are zero zero.

36
00:01:52,060 --> 00:01:55,240
And this is OK, this is the tree we got.

37
00:01:55,540 --> 00:02:00,280
I was inserting the key when the became imbalanced, one of the more became imbalance.

38
00:02:00,280 --> 00:02:01,600
I have performed rotation.

39
00:02:01,600 --> 00:02:02,770
I have adjusted it.

40
00:02:03,250 --> 00:02:05,360
So that's all the moment to ignore.

41
00:02:05,370 --> 00:02:06,610
It is becoming imbalance.

42
00:02:06,610 --> 00:02:10,500
We perform rotation and we keep the ability always balanced.

43
00:02:11,350 --> 00:02:17,830
Now, one important thing I will tell you, souls, I have not perform a rotation or this one I just

44
00:02:17,830 --> 00:02:18,580
ignored it.

45
00:02:18,880 --> 00:02:25,180
Like if I ignored it, then even the balance factor may become minus three, minus four.

46
00:02:25,180 --> 00:02:27,550
Also, if I go on blindly inserting it.

47
00:02:27,940 --> 00:02:29,380
So that should not happen.

48
00:02:29,650 --> 00:02:31,930
So you should never get the balance factor.

49
00:02:32,230 --> 00:02:35,430
More than two are less than minus two.

50
00:02:35,740 --> 00:02:36,080
Right.

51
00:02:36,340 --> 00:02:39,060
So you should never get minus three or minus four lichter's.

52
00:02:39,760 --> 00:02:42,640
That's what always balance the three.

53
00:02:42,640 --> 00:02:44,770
Whenever any note is becoming imbalance.

54
00:02:45,010 --> 00:02:45,940
Let us continue.

55
00:02:46,810 --> 00:02:48,880
I have to insert more KeIso here.

56
00:02:48,880 --> 00:02:55,900
I will insert Grindley five now this is the result in three I have so I will insert twenty five right

57
00:02:55,930 --> 00:03:00,490
here I have instead of three keys not twenty five is greater than 20 but less than thirty.

58
00:03:00,490 --> 00:03:02,020
So it comes on the side.

59
00:03:02,170 --> 00:03:02,830
Twenty five.

60
00:03:03,220 --> 00:03:04,780
What are the new balance factors.

61
00:03:05,020 --> 00:03:09,310
This is zero and this is one and this is one and two.

62
00:03:09,310 --> 00:03:10,300
So this is one.

63
00:03:10,300 --> 00:03:11,620
Minus two is minus one.

64
00:03:14,040 --> 00:03:17,640
Not an excuse 28, 28, insert 28.

65
00:03:19,130 --> 00:03:25,660
Twenty eight is greater than 20, less than 30, greater than 25, so it comes here, let us see balance

66
00:03:25,670 --> 00:03:26,330
factors.

67
00:03:26,510 --> 00:03:30,130
This is zero and versus zero minus one to minus one.

68
00:03:30,380 --> 00:03:32,370
And this is one to minus zero.

69
00:03:32,390 --> 00:03:33,200
So this is two.

70
00:03:33,500 --> 00:03:37,040
This one is one one, two, three, one minus three.

71
00:03:37,040 --> 00:03:38,280
So this is minus two.

72
00:03:38,750 --> 00:03:40,600
So how many nodes became imbalance?

73
00:03:40,640 --> 00:03:42,040
Two nodes became imbalanced.

74
00:03:42,050 --> 00:03:44,600
This one is also imbalance and this one is also imbalance.

75
00:03:45,020 --> 00:03:46,370
Newly installed nodes.

76
00:03:46,370 --> 00:03:50,060
This one then, which is the first ancestor which became imbalanced.

77
00:03:50,060 --> 00:03:52,550
This is the first ancestor which became imbalance.

78
00:03:53,570 --> 00:03:59,990
We should perform on that one based on this in which direction fashion is done left left-to-right right.

79
00:04:02,170 --> 00:04:04,960
So we have to perform a lot of rotation the other way.

80
00:04:04,990 --> 00:04:07,100
What is the balance factor to Boorman's?

81
00:04:07,150 --> 00:04:12,490
It is heavy on the left hand side then this is minus one, so it's heavy on the right hand side.

82
00:04:12,670 --> 00:04:18,350
So LRB, shouldn't you have to perform so another rotation upon this one like this and then this one.

83
00:04:18,610 --> 00:04:23,950
So this is an odd rotation up on this note then what will be the result.

84
00:04:23,950 --> 00:04:25,710
Entry the string the.

85
00:04:27,220 --> 00:04:31,070
And then will remain as it is, they are not touched.

86
00:04:31,090 --> 00:04:32,060
They are not mortified.

87
00:04:32,350 --> 00:04:39,670
Now, the word about this as this is a lot of to will take the place of Route 30 will come on the right

88
00:04:39,670 --> 00:04:43,310
hand side and twenty five will remain as a left child.

89
00:04:43,960 --> 00:04:46,520
So this is the tree after performing that operation.

90
00:04:46,540 --> 00:04:47,920
So what are the balance factors?

91
00:04:48,280 --> 00:04:49,930
Zero zero.

92
00:04:49,930 --> 00:04:53,870
And this is zero because these are leaf nodes and this is one minus one zero.

93
00:04:53,890 --> 00:04:54,970
This is one minus two.

94
00:04:54,970 --> 00:04:56,080
This is minus one.

95
00:04:56,110 --> 00:04:59,490
The log in that is balanced binary substrate.

96
00:04:59,500 --> 00:05:02,490
That is absolutely Sawtell here, 28.

97
00:05:02,500 --> 00:05:04,120
I have finished then.

98
00:05:04,480 --> 00:05:05,570
Twenty seven is there.

99
00:05:05,590 --> 00:05:13,020
So in that tree I will insert a 27 insert 27, 27 where it will be inserted.

100
00:05:13,570 --> 00:05:17,290
Twenty seven is greater than 20 but less than 28 but didn't ratify.

101
00:05:17,390 --> 00:05:20,960
So it comes here on the right hand side of twenty five.

102
00:05:21,400 --> 00:05:23,180
Let us update the balance factors.

103
00:05:23,560 --> 00:05:26,350
This is zero and this is minus one.

104
00:05:26,590 --> 00:05:28,280
This is one two minus one.

105
00:05:28,300 --> 00:05:30,700
So this is one two minus one is one.

106
00:05:31,000 --> 00:05:31,600
This is zero.

107
00:05:32,140 --> 00:05:33,000
What about this?

108
00:05:33,220 --> 00:05:33,910
This is one.

109
00:05:34,150 --> 00:05:35,300
One, two, three.

110
00:05:35,680 --> 00:05:37,360
So this is one minus three.

111
00:05:37,610 --> 00:05:38,980
This is minus two.

112
00:05:40,670 --> 00:05:43,160
So that unknown group, though, became imbalanced.

113
00:05:43,190 --> 00:05:44,550
This note became imbalanced.

114
00:05:44,560 --> 00:05:46,400
So what was the direction of insertion?

115
00:05:47,060 --> 00:05:48,320
Right, left.

116
00:05:48,800 --> 00:05:50,540
Ah well ah.

117
00:05:50,710 --> 00:05:52,640
And so actually the new mode.

118
00:05:52,640 --> 00:05:54,800
And so they are on.

119
00:05:55,190 --> 00:06:01,100
As I already said, we always perform on rotation within three nodes are by taking only two steps.

120
00:06:02,240 --> 00:06:04,850
So our rotation we have to perform.

121
00:06:05,370 --> 00:06:05,630
Right.

122
00:06:05,810 --> 00:06:07,550
In other words, this is minus two.

123
00:06:07,560 --> 00:06:09,560
So this is heavy on the right hand side.

124
00:06:09,920 --> 00:06:15,850
And is the child, if you see this one positive, it is heavy on the left hand side of it.

125
00:06:16,070 --> 00:06:20,650
So our elder addition, we have to perform oral tradition when we perform it directly.

126
00:06:20,660 --> 00:06:24,620
So this goes into the root and this comes on the left hand side.

127
00:06:24,640 --> 00:06:26,000
Who comes on the left hand side.

128
00:06:26,240 --> 00:06:32,630
So I will throw it here if I will move up so twenty five miles up, then into the present root will

129
00:06:32,630 --> 00:06:33,920
come on the left hand side.

130
00:06:34,430 --> 00:06:38,730
OK, so 10 will remain its left shoulder only then the right side.

131
00:06:38,730 --> 00:06:42,110
It was 28, so 28 will remain its own place.

132
00:06:42,470 --> 00:06:48,190
Then 30 was its right side, so it remained its own place when twenty five has became a new rule to

133
00:06:48,200 --> 00:06:50,300
what happens to twenty seven.

134
00:06:50,300 --> 00:06:50,830
X-Rite right.

135
00:06:51,200 --> 00:06:51,830
Thirty seven.

136
00:06:51,830 --> 00:06:55,280
Right chain right child remains in right subtree on this one.

137
00:06:55,280 --> 00:06:55,860
Dexatrim.

138
00:06:55,870 --> 00:06:59,270
This becomes the left child of twenty eight.

139
00:07:00,230 --> 00:07:00,830
This funny.

140
00:07:02,180 --> 00:07:03,980
We have already seen this form LA.

141
00:07:04,900 --> 00:07:13,780
I already have explained it, this one, so we have performed our elder rotation, so this is our rotation,

142
00:07:14,170 --> 00:07:16,350
then this is the result of oral tradition.

143
00:07:16,780 --> 00:07:20,170
Let us check the balance factors, zero zero zero.

144
00:07:20,530 --> 00:07:21,490
And this is one.

145
00:07:21,760 --> 00:07:22,610
This is zero.

146
00:07:22,660 --> 00:07:24,120
This is one two, one, two.

147
00:07:24,130 --> 00:07:27,280
So this is zero as we have performed rotation about this one.

148
00:07:27,280 --> 00:07:28,390
So that Bekim zero.

149
00:07:30,880 --> 00:07:33,940
Now, one last case remaining, that is five.

150
00:07:35,140 --> 00:07:37,900
Insert insert five.

151
00:07:39,190 --> 00:07:45,040
So if I insert five, five is smaller than twenty five, smaller than grindy, smaller than 10, so

152
00:07:45,040 --> 00:07:46,990
it comes as the left side here.

153
00:07:47,830 --> 00:07:49,570
Now the balance factor slabbed.

154
00:07:49,720 --> 00:07:50,570
This is zero.

155
00:07:50,860 --> 00:07:51,760
This is one.

156
00:07:52,180 --> 00:07:53,410
And this is two.

157
00:07:54,070 --> 00:07:58,040
And this one is one, two, three, one, two.

158
00:07:58,060 --> 00:07:59,070
So this becomes one.

159
00:08:00,160 --> 00:08:01,540
So who became imbalance?

160
00:08:02,050 --> 00:08:03,940
This node became imbalance.

161
00:08:04,830 --> 00:08:10,280
Right, incision is done on this side, left of left to perform a little rotation.

162
00:08:10,560 --> 00:08:15,030
So if I perform a little rotation around this node, then what will be the result?

163
00:08:15,050 --> 00:08:17,610
And this is 25 here.

164
00:08:19,590 --> 00:08:24,840
This will move up and this will come on the right hand side, so there's a stand and right side is 20

165
00:08:25,140 --> 00:08:26,490
and left side is five.

166
00:08:26,490 --> 00:08:29,820
Only the left side of ten remains as it is.

167
00:08:30,270 --> 00:08:32,909
And the other side, right hand side of the tree remains the same.

168
00:08:33,510 --> 00:08:37,650
So this is 28 and this is 27.

169
00:08:38,980 --> 00:08:42,690
Today, the balance factors, these are zero zero zero.

170
00:08:42,919 --> 00:08:47,180
Only this will update zero zero zero and there's also zero.

171
00:08:48,160 --> 00:08:55,540
So this is the final three that we got after inserting all the keys and also balancing it simultaneously.

172
00:08:56,920 --> 00:08:59,510
So there's the final Aviel tree, right?

173
00:08:59,890 --> 00:09:03,480
So this was the first one, then the second one and the third one fourth.

174
00:09:03,820 --> 00:09:05,430
And this is after Petion.

175
00:09:08,280 --> 00:09:14,640
And this is off probation and we shall be shown we have perform a rotation, we have performed.

176
00:09:18,730 --> 00:09:22,720
Now, let us count how many of your patients have performed for generating this.

177
00:09:23,740 --> 00:09:31,510
One, two, three, four, four patients have performed, and in this rotation, the cells are and this

178
00:09:31,510 --> 00:09:32,260
is Alal.

179
00:09:32,470 --> 00:09:36,820
So two single patients and this is a law and order.

180
00:09:37,000 --> 00:09:38,790
So to double rotations.

181
00:09:38,920 --> 00:09:46,960
So for these keys total for patients are required to watch single rotations and two are double rotations

182
00:09:47,170 --> 00:09:51,400
and seven keys and inserting what is the height of Aviel three.

183
00:09:51,580 --> 00:09:54,760
Finally, I got zero one to.

184
00:09:55,650 --> 00:09:59,210
So this is the minimum height, so that's all.

185
00:09:59,730 --> 00:10:01,940
Not one more thing I would like to show you here.

186
00:10:02,370 --> 00:10:09,440
If I am not performing rotation's simply I'm creating binary search tree, then what happens?

187
00:10:09,480 --> 00:10:10,510
Let us look at it.

188
00:10:10,770 --> 00:10:13,620
Let us create a binary search tree for these keys.

189
00:10:14,280 --> 00:10:15,300
First is 10.

190
00:10:17,410 --> 00:10:25,690
20 is greater than this one today is greater than Tallangatta, and 20 30 is greater than 10 greater

191
00:10:25,690 --> 00:10:31,960
than 20, less than 30, 28 is greater than 10, greater than 20.

192
00:10:32,320 --> 00:10:35,590
Less than 30, but greater than 25.

193
00:10:35,980 --> 00:10:36,930
Here it comes.

194
00:10:37,510 --> 00:10:38,230
Thirty seven.

195
00:10:38,500 --> 00:10:39,970
Greater, greater.

196
00:10:41,050 --> 00:10:42,130
Smaller than this.

197
00:10:42,370 --> 00:10:43,360
Greater than this.

198
00:10:43,360 --> 00:10:44,320
Smaller than this.

199
00:10:46,620 --> 00:10:50,790
Five smaller than see what is the height.

200
00:10:51,540 --> 00:11:00,990
Zero, one, two, three, four, five here, the hiders, zero one two and are the height is five.

201
00:11:01,910 --> 00:11:07,970
See, the difference by research is useful for searching in this country if I search.

202
00:11:08,210 --> 00:11:11,020
What is the maximum number of competitions required?

203
00:11:11,300 --> 00:11:15,260
Six, one, two, three, four, five, six, six competitions required.

204
00:11:15,470 --> 00:11:21,680
If I'm searching for 27, it will be found after comparing six elements here.

205
00:11:21,860 --> 00:11:24,830
If I search any maximum.

206
00:11:24,830 --> 00:11:25,750
How many competitions?

207
00:11:25,760 --> 00:11:26,550
One, two, three.

208
00:11:26,720 --> 00:11:31,170
So in three competitions I can find a key here.

209
00:11:31,190 --> 00:11:32,810
Six competitions maximum.

210
00:11:34,770 --> 00:11:40,170
If I'm searching for 10 or five or like that, then less competition, but at most of any maximum,

211
00:11:40,710 --> 00:11:44,240
so this is having almost a maximum height.

212
00:11:44,700 --> 00:11:50,760
So that's why if you're making a binary search tree, make it easier to try to make it balanced so that

213
00:11:50,760 --> 00:11:52,130
the searching time is less.

214
00:11:52,150 --> 00:11:55,650
So it really is a perfect binary search tree.

215
00:11:57,540 --> 00:11:59,670
No one more lasting S..

216
00:12:00,970 --> 00:12:04,410
Height is, too, but what are the maximum comparisons?

217
00:12:04,810 --> 00:12:05,630
One, two, three.

218
00:12:06,040 --> 00:12:09,300
So why don't we can't hide from one onwards?

219
00:12:10,510 --> 00:12:14,150
So the number of comparisons are exactly equal to the height.

220
00:12:14,980 --> 00:12:17,490
I don't have to say hi to plus one.

221
00:12:17,500 --> 00:12:19,320
I can say hypothetically height.

222
00:12:19,780 --> 00:12:21,840
So actually higher than levels.

223
00:12:21,850 --> 00:12:26,350
All these are meant for analysis purposes like they are for analysis purposes.

224
00:12:26,890 --> 00:12:29,200
So you can start from zero also one also.

225
00:12:29,200 --> 00:12:30,070
That is your choice.

226
00:12:30,310 --> 00:12:33,620
So if you are counting height from one onwards, that's not wrong.

227
00:12:34,210 --> 00:12:39,640
So that's all about the creation of Aviel three and also have compared with Vinicky socially.

228
00:12:39,910 --> 00:12:44,710
Then also I have told you that height can also be conquered from starting from one onwards.

229
00:12:45,790 --> 00:12:47,400
So that's all in this topic.

