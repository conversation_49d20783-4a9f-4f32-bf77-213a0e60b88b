1
00:00:00,870 --> 00:00:07,410
The topic is two, three, four traits already in the previous video, we have learned about two, three.

2
00:00:07,980 --> 00:00:14,520
If you did not watch that video, I suggest you must watch it for understanding this one, because a

3
00:00:14,520 --> 00:00:15,520
lot of concept already.

4
00:00:15,520 --> 00:00:19,260
We have discussed in the previous video, if you have skipped it, go and watch it.

5
00:00:19,470 --> 00:00:20,540
Then you can understand.

6
00:00:20,550 --> 00:00:24,120
Here, I will show you a few things that are three, four things.

7
00:00:25,260 --> 00:00:29,240
Let us see what is two, three, four, three, bisymmetry of degree four.

8
00:00:29,370 --> 00:00:32,100
So what is a bit of everything I have explained in the previous video?

9
00:00:33,240 --> 00:00:35,580
Bedri, the multiverse history.

10
00:00:35,580 --> 00:00:38,880
And it is a highly balanced multiverse story.

11
00:00:39,360 --> 00:00:45,420
So how it will balance the height by imposing two conditions or leave at the same level?

12
00:00:45,760 --> 00:00:51,780
This one of the condition that every node must have four by two degree by bill number of children that

13
00:00:51,780 --> 00:00:52,330
Silveri.

14
00:00:52,680 --> 00:00:54,360
So this is for four to two children.

15
00:00:54,360 --> 00:00:54,970
Must be various.

16
00:00:54,990 --> 00:01:01,410
Obviously every node will have definitely Kushima now, but the degree is the four then Hoddinott looks

17
00:01:01,410 --> 00:01:05,019
like let legacy degree is performance for children not possible.

18
00:01:05,019 --> 00:01:06,570
So three keys are possible.

19
00:01:06,580 --> 00:01:11,190
So here are three spaces are therefore the keys and two more children can be there.

20
00:01:11,490 --> 00:01:15,090
But here there are two children, only one key or one key.

21
00:01:15,100 --> 00:01:15,940
There are two children.

22
00:01:16,170 --> 00:01:17,370
This is first possibility.

23
00:01:17,700 --> 00:01:23,820
Second to Keyssar, then three children are there and then three keys and four children out there.

24
00:01:23,820 --> 00:01:25,980
So that's why degree is a four.

25
00:01:26,430 --> 00:01:30,840
But the minimum, there can be two children or three or four children.

26
00:01:31,200 --> 00:01:34,290
So that's why it is called as two, three, four, three.

27
00:01:34,500 --> 00:01:41,880
So every node can have either one or two key or three kids know how to create or Bedri.

28
00:01:41,880 --> 00:01:44,680
We have already seen it in two, three trees and deletion.

29
00:01:44,700 --> 00:01:49,140
Also, I told you here, there is some differences there so that we have to see.

30
00:01:49,350 --> 00:01:53,760
So let us take some keys and create two, three, four, three.

31
00:01:54,930 --> 00:01:56,160
So here I have keys.

32
00:01:56,160 --> 00:01:58,380
Let us create a two, three, four, three.

33
00:01:58,800 --> 00:02:00,900
So let us insert all these keys one by one.

34
00:02:01,200 --> 00:02:04,980
So first step is I will insert ten four system.

35
00:02:05,340 --> 00:02:10,410
So four that create a node, the degree four for three keys are possible.

36
00:02:10,680 --> 00:02:12,210
Foskey done is inserted.

37
00:02:12,960 --> 00:02:18,690
The next insert going to extend again means that inserted here thirty today is inserted here.

38
00:02:20,100 --> 00:02:21,420
So this is full now.

39
00:02:21,840 --> 00:02:23,730
Now next step I will show you here.

40
00:02:23,910 --> 00:02:27,000
Insert forty if I insert forty.

41
00:02:27,000 --> 00:02:29,100
Now this is the thing that we have to learn.

42
00:02:29,520 --> 00:02:32,790
New thing, 10, 20, 30, 40.

43
00:02:32,790 --> 00:02:35,490
There is no space when there is no space.

44
00:02:35,730 --> 00:02:38,810
What we are doing in two, three, three we have learned already there.

45
00:02:38,820 --> 00:02:41,610
So here just I am showing it for degree four.

46
00:02:42,060 --> 00:02:45,420
So when you have one more key, there is more space, then you have to split.

47
00:02:45,810 --> 00:02:47,310
So let us split this one.

48
00:02:47,520 --> 00:02:52,110
So here we get one node and of the norm.

49
00:02:52,260 --> 00:02:53,820
So let us make that node.

50
00:02:53,820 --> 00:02:54,630
Absolutely not.

51
00:02:54,720 --> 00:02:55,310
OK, this.

52
00:02:55,320 --> 00:02:55,890
No, that's OK.

53
00:02:55,950 --> 00:03:02,520
OK, so now along with these three keys, we have to consider forty also that is forty also.

54
00:03:02,910 --> 00:03:07,950
Then out of this we have to split and we know very, very well that one of the key will go in the route.

55
00:03:08,250 --> 00:03:13,290
But how many will go on left and right side here you have even number of keys.

56
00:03:13,770 --> 00:03:15,570
That's that is the point.

57
00:03:15,690 --> 00:03:18,450
That is the difference here compared to TT's.

58
00:03:18,720 --> 00:03:27,120
So when you have even number of keys for splitting, then either you can be left by Arzt or right by

59
00:03:27,120 --> 00:03:32,310
AST so you can have more keys on the side or you can have more keys on the right side because you cannot

60
00:03:32,310 --> 00:03:33,000
divide them.

61
00:03:33,000 --> 00:03:37,890
Uniformally that set so you can have left by also splitting.

62
00:03:37,890 --> 00:03:39,150
Alright, by splitting.

63
00:03:39,510 --> 00:03:42,090
This is the new thing we are learning in two, three, four trees.

64
00:03:42,900 --> 00:03:48,180
So here I will do one thing and then here and going in the road.

65
00:03:48,720 --> 00:03:49,110
Right.

66
00:03:49,380 --> 00:03:53,040
And I will take two keys on this side, 30 and 40.

67
00:03:53,310 --> 00:03:54,990
So I made it left.

68
00:03:54,990 --> 00:03:55,320
Right.

69
00:03:55,440 --> 00:03:55,970
I asked.

70
00:03:55,980 --> 00:03:57,570
I have made it right Barsa.

71
00:03:57,780 --> 00:03:59,620
So this is the left shoulder structure.

72
00:04:00,390 --> 00:04:01,500
So splitting is done.

73
00:04:01,500 --> 00:04:06,720
One key on that side to keep on the side, because I have taken some keys based on that, I want to

74
00:04:06,720 --> 00:04:07,800
show you more splitting.

75
00:04:07,800 --> 00:04:12,920
So that's the reason I'm taking to on this side or else you can take to the side and one that side.

76
00:04:13,200 --> 00:04:17,279
So in that case, twenty will be here today, will go here and forty will be at this place.

77
00:04:18,029 --> 00:04:19,560
So this is for speaking.

78
00:04:20,010 --> 00:04:22,260
Now, let us continue in the other keys.

79
00:04:22,260 --> 00:04:23,940
So I have finished till forty.

80
00:04:24,240 --> 00:04:27,000
Not next is insert fifty.

81
00:04:27,540 --> 00:04:28,860
So let us check.

82
00:04:29,430 --> 00:04:30,750
I will copy the same thing.

83
00:04:31,560 --> 00:04:31,910
Right.

84
00:04:32,160 --> 00:04:39,600
So this is twenty and on this side we have ten and this is 30 and 40.

85
00:04:40,650 --> 00:04:43,920
Nine including 50 50 should come at this place.

86
00:04:43,920 --> 00:04:46,320
50 is better than pretty and greater 30 and 40.

87
00:04:46,320 --> 00:04:48,330
So it comes here the next.

88
00:04:48,330 --> 00:04:49,950
I will continue in saving sixty.

89
00:04:49,950 --> 00:04:52,680
Also here there is no place for sixty.

90
00:04:52,800 --> 00:04:54,750
There's no splits that split.

91
00:04:54,960 --> 00:04:58,560
So I will take a small pictures now because I need more space.

92
00:04:58,740 --> 00:04:59,910
So split.

93
00:05:00,380 --> 00:05:04,980
Once again, so this is splitted now here, we already had ten.

94
00:05:05,390 --> 00:05:06,780
Now this is being split.

95
00:05:06,800 --> 00:05:10,490
And so we've got one more note and here we have 20 already.

96
00:05:10,500 --> 00:05:17,420
This was pointing here, but when there's a split, 30 on this side, 40 goes up and 50 and 60 in the

97
00:05:17,430 --> 00:05:17,750
snow.

98
00:05:18,230 --> 00:05:21,400
Now, this is the second child and this is the third child.

99
00:05:22,910 --> 00:05:26,330
So this is the result of splitting when we were inserting 60.

100
00:05:26,600 --> 00:05:28,070
So you can count the number of splits.

101
00:05:28,070 --> 00:05:33,690
Also, foster split and the second split fossilised was at 40 and second split is at 60.

102
00:05:34,100 --> 00:05:36,140
And here I got two extra notes.

103
00:05:36,140 --> 00:05:40,010
And here we got just one extra astronaut right now.

104
00:05:40,040 --> 00:05:42,720
Next, 70 and 80.

105
00:05:42,740 --> 00:05:46,040
So here I will say insert 70.

106
00:05:46,790 --> 00:05:49,520
So in this, I will continue.

107
00:05:49,880 --> 00:05:52,190
So this is 20 and 40.

108
00:05:52,710 --> 00:05:54,380
And on this side we have 10.

109
00:05:55,460 --> 00:05:59,210
And here we have 30.

110
00:05:59,970 --> 00:06:00,310
Right.

111
00:06:00,680 --> 00:06:04,630
And here we have 50 and 60.

112
00:06:04,940 --> 00:06:09,010
So insert seventy seven becomes here, then insert 80 also.

113
00:06:09,030 --> 00:06:10,610
So there is more space for it.

114
00:06:10,940 --> 00:06:12,060
So again, split it.

115
00:06:12,260 --> 00:06:19,310
So here we got this just one node, second node undisturbed node.

116
00:06:19,490 --> 00:06:20,780
It will split further.

117
00:06:21,050 --> 00:06:25,420
So this was a 10 as it is 30 as it is 20.

118
00:06:25,460 --> 00:06:27,800
And this was pointing here one here to 40.

119
00:06:28,070 --> 00:06:29,040
This was pointing here.

120
00:06:29,450 --> 00:06:30,940
Now, this is how split.

121
00:06:31,280 --> 00:06:35,990
So 50 comes here, 60 goes up, and these are 70 and 80.

122
00:06:36,110 --> 00:06:37,410
So this will be pointing here.

123
00:06:38,690 --> 00:06:39,890
Now, this is the result.

124
00:06:40,100 --> 00:06:41,960
So one split, one extra node.

125
00:06:42,410 --> 00:06:45,590
Then we are inserting 80 the remaining nodes.

126
00:06:45,590 --> 00:06:47,610
I have an idea, 100 and 110.

127
00:06:47,630 --> 00:06:51,580
So next let us insert nineteen hundred and seed here.

128
00:06:52,400 --> 00:06:55,420
If I insert 90, I will directly insert accurately.

129
00:06:55,430 --> 00:06:56,930
So 90 will be inserted here.

130
00:06:56,960 --> 00:07:01,010
So actually this is the result of this one, but there is no space, so I will show it here only.

131
00:07:01,400 --> 00:07:05,200
So insert 90 as well as Hendron.

132
00:07:05,450 --> 00:07:06,480
So 90 already.

133
00:07:06,480 --> 00:07:07,800
I haven't sorted one.

134
00:07:07,820 --> 00:07:14,070
I tried to insert that comes here and there is no space here and there is no space again specked.

135
00:07:14,270 --> 00:07:15,800
So here I will show you.

136
00:07:16,210 --> 00:07:25,010
OK, so this then as as it is is as it is and this is 50 as it is.

137
00:07:25,790 --> 00:07:34,380
Then here we have to split, we have to split this one 70, 80, 90 hundred so there is more space split.

138
00:07:34,670 --> 00:07:39,350
So 70 remains on the site and here will be 90.

139
00:07:39,390 --> 00:07:42,140
And back then, who goes up 80?

140
00:07:42,440 --> 00:07:50,410
So this is the node which is already having 20 and 40 and 60, 40 and 60.

141
00:07:50,660 --> 00:07:52,860
So when it comes up, there is more space.

142
00:07:53,480 --> 00:07:57,600
So this 80 goes up, there is more space, then again split.

143
00:07:57,890 --> 00:08:03,590
So again, if I split, then on the top, I should have one more, more and 40 as we are making it right

144
00:08:03,590 --> 00:08:03,870
back.

145
00:08:03,900 --> 00:08:07,370
So 40 goes up and this is 60 and 80.

146
00:08:07,370 --> 00:08:08,910
I should have one more here.

147
00:08:09,380 --> 00:08:12,000
So this is 60 and 80, right.

148
00:08:12,380 --> 00:08:13,830
So 40 has made up.

149
00:08:13,850 --> 00:08:14,600
So this is the left.

150
00:08:14,600 --> 00:08:16,460
Channel 42 is the right, Channel 40.

151
00:08:16,730 --> 00:08:20,000
And this was the left of 20 already in the Rachel of 20 already.

152
00:08:20,290 --> 00:08:23,270
Now, this is known as the split that saw 60 years left.

153
00:08:23,270 --> 00:08:24,260
And the second.

154
00:08:24,560 --> 00:08:27,270
And it is right, Ptacek.

155
00:08:27,950 --> 00:08:29,690
So this is how the tree looks like.

156
00:08:29,930 --> 00:08:33,350
So two levels of splitting is done for this.

157
00:08:33,360 --> 00:08:34,480
We are all very familiar.

158
00:08:34,909 --> 00:08:39,799
And so just you can take the snapshot and these keys and do it by yourself.

159
00:08:39,799 --> 00:08:40,750
Do it by yourself.

160
00:08:40,760 --> 00:08:44,750
If you do it by yourself once, then you can remember it any time you can do it.

161
00:08:44,750 --> 00:08:46,560
And also you can easily program it also.

162
00:08:46,970 --> 00:08:49,300
So just take a snapshot of this one.

163
00:08:49,520 --> 00:08:50,330
So that's it.

164
00:08:50,630 --> 00:08:51,620
Does the information.

165
00:08:51,860 --> 00:08:55,100
Now, I'll show you a little bit about deletion, the same as two to three.

166
00:08:55,430 --> 00:09:00,740
But once more I will delete few keys and I will show you I will copy that tree here.

167
00:09:01,010 --> 00:09:03,050
Then I will show you how to delete.

168
00:09:04,190 --> 00:09:09,080
Let us see how to delete keys from two, three, four.

169
00:09:09,140 --> 00:09:11,930
Trace the procedure the same as good three three.

170
00:09:12,170 --> 00:09:14,660
But as an example, let us do it once here.

171
00:09:15,590 --> 00:09:18,370
Already I have shown you cases in two, three, three cinches.

172
00:09:18,380 --> 00:09:23,600
This applies here, so I'll just repeat those cases by selecting some keys here I want to delete.

173
00:09:23,990 --> 00:09:26,470
So first of all, search for underhandedness greater than 40.

174
00:09:26,480 --> 00:09:32,030
So go to the right side is a great idea to create a phone, then delete software, deletion that list.

175
00:09:32,030 --> 00:09:33,650
There must be one or two children.

176
00:09:33,650 --> 00:09:34,610
Goochland must be there.

177
00:09:34,610 --> 00:09:36,260
But actually, these are not lymph nodes.

178
00:09:36,260 --> 00:09:37,100
They don't have children.

179
00:09:37,460 --> 00:09:39,500
But there is a possibility of two children.

180
00:09:39,500 --> 00:09:40,040
One case.

181
00:09:40,230 --> 00:09:41,510
Most of them are possible.

182
00:09:41,510 --> 00:09:41,930
Yes.

183
00:09:42,770 --> 00:09:44,780
Then this is a deleted file.

184
00:09:44,780 --> 00:09:46,340
Put back that once again.

185
00:09:46,880 --> 00:09:48,590
No second example.

186
00:09:48,770 --> 00:09:50,360
I want to delete seventy.

187
00:09:50,510 --> 00:09:55,430
So search for seventy seven days deleted 70s deleted.

188
00:09:55,430 --> 00:09:56,690
This note became empty.

189
00:09:56,840 --> 00:09:57,710
So what to do.

190
00:09:58,010 --> 00:09:59,810
Replace it by boring the key.

191
00:09:59,880 --> 00:10:06,330
So from this side, we cannot borrow from that side, we can borrow so we can bring it here and 90 at

192
00:10:06,330 --> 00:10:10,910
this place and this becomes 100, OK, and that is shifted here.

193
00:10:12,330 --> 00:10:14,270
This is how you can delete that 70.

194
00:10:15,450 --> 00:10:18,360
Now, in this case, I want to delete 30.

195
00:10:18,660 --> 00:10:19,770
So search for 30.

196
00:10:20,100 --> 00:10:21,210
Today is less than this.

197
00:10:21,210 --> 00:10:22,340
Today is greater than 30.

198
00:10:22,350 --> 00:10:23,910
Go to the site, yesterday's phone.

199
00:10:24,270 --> 00:10:25,710
So delete this one.

200
00:10:26,280 --> 00:10:30,770
Now, this became empty so Boracay can be Boracay.

201
00:10:30,780 --> 00:10:32,120
There is no key available here.

202
00:10:32,310 --> 00:10:37,160
Then the what, March 20 is brought here and it is Marshwood, this one.

203
00:10:37,230 --> 00:10:38,380
So this is in this note.

204
00:10:38,940 --> 00:10:42,360
Now this became empty and this became a demonstrator bottle.

205
00:10:42,360 --> 00:10:43,050
Candy bottle.

206
00:10:43,260 --> 00:10:43,530
Yes.

207
00:10:43,530 --> 00:10:44,890
We can borrow from that chain.

208
00:10:45,210 --> 00:10:47,190
So how about this one?

209
00:10:47,520 --> 00:10:48,970
This 60 goes here.

210
00:10:49,230 --> 00:10:52,300
So this will become 60 and 40 comes here.

211
00:10:52,440 --> 00:10:53,240
This is 40.

212
00:10:53,670 --> 00:10:56,130
Then this left child of 16.

213
00:10:56,280 --> 00:10:59,600
The left child of 60 will come on the site.

214
00:10:59,610 --> 00:11:00,840
So this will be 50.

215
00:11:01,350 --> 00:11:01,760
Right.

216
00:11:01,920 --> 00:11:02,930
So 60 60s there.

217
00:11:03,150 --> 00:11:04,470
So 50 is removed.

218
00:11:04,740 --> 00:11:05,750
So what do we do now?

219
00:11:05,880 --> 00:11:07,350
We have to shift this slide.

220
00:11:07,350 --> 00:11:08,580
This 90 comes here.

221
00:11:08,610 --> 00:11:11,600
So this is 80 and this is handmade.

222
00:11:12,120 --> 00:11:14,480
So basically these are shifted.

223
00:11:14,700 --> 00:11:17,770
So I'm removing this thought, OK, I'm removing this one.

224
00:11:18,990 --> 00:11:22,320
So when this became empty, there was a possibility of borings.

225
00:11:22,320 --> 00:11:25,790
If you have borrowed from the sibling y our parent.

226
00:11:26,490 --> 00:11:26,800
Right.

227
00:11:26,850 --> 00:11:27,540
And this is over.

228
00:11:28,440 --> 00:11:30,270
The last one last example.

229
00:11:30,600 --> 00:11:33,900
I want to delete Eddie, so let us delete it.

230
00:11:34,380 --> 00:11:36,270
It is gone now.

231
00:11:36,280 --> 00:11:36,990
This is empty.

232
00:11:37,470 --> 00:11:38,850
Can you borrow from sibling.

233
00:11:38,910 --> 00:11:39,420
No.

234
00:11:39,720 --> 00:11:42,270
Then the work much veny emerging.

235
00:11:42,660 --> 00:11:43,650
Very emerging.

236
00:11:43,650 --> 00:11:46,620
So 90s and it comes here in the singlehood.

237
00:11:46,920 --> 00:11:51,750
So this note is gone and this is empty now this node is empty.

238
00:11:51,750 --> 00:11:54,170
So the word bottle kinniburgh from here.

239
00:11:54,510 --> 00:11:58,200
No, it cannot be borrowed because there is only one key on the site.

240
00:11:58,500 --> 00:12:00,050
If two keys are there then you can borrow.

241
00:12:00,750 --> 00:12:05,510
There is only one key then to what must that one also some ones, these two together.

242
00:12:05,610 --> 00:12:07,580
So bring that one here, 60.

243
00:12:07,830 --> 00:12:12,000
So this will be the RAYCHELLE of 60 and this is might see these two.

244
00:12:12,630 --> 00:12:13,910
So this node is not there.

245
00:12:14,160 --> 00:12:16,230
So then that key will come in one node.

246
00:12:16,320 --> 00:12:17,970
So this is also gone.

247
00:12:18,390 --> 00:12:20,540
This is all the result after deletion.

248
00:12:20,850 --> 00:12:23,040
So I have deleted all the keys, various keys.

249
00:12:23,040 --> 00:12:24,320
I have deleted one by one.

250
00:12:24,570 --> 00:12:28,440
So this is the you need to do practice like I have done the work.

251
00:12:28,440 --> 00:12:33,020
So watch it slowly at a slow speed or give pauses and do it by yourself.

252
00:12:33,030 --> 00:12:35,730
What I am doing, you have to observe it and do it by yourself.

253
00:12:35,730 --> 00:12:42,720
If you do it by yourself using that on paper, then you remember just watching the not you can't remember

254
00:12:42,720 --> 00:12:43,200
this one.

255
00:12:43,530 --> 00:12:45,480
You can't again put it back on paper.

256
00:12:45,840 --> 00:12:51,600
So you do it right now, all this one and check every step and do it by yourself.

257
00:12:52,320 --> 00:12:55,290
So that's all I have to tell about two, three, four trees.

258
00:12:55,290 --> 00:13:00,660
I have shown you what our two, three, four trees then in social and creation then also have deleted

259
00:13:00,990 --> 00:13:02,670
by taking all possible cases.

260
00:13:03,360 --> 00:13:08,460
And if you do the analysis, it is similar to two, three, three, so you can do the analysis by yourself.

261
00:13:09,150 --> 00:13:15,870
So this height of this one will also be always log-in because this is a bit of degree for so always

262
00:13:15,870 --> 00:13:16,290
log in.

263
00:13:17,190 --> 00:13:20,580
So that's all about the stability for peace.

264
00:13:20,970 --> 00:13:22,170
So that's what in this video.

