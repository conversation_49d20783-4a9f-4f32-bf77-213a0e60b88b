1
00:00:00,670 --> 00:00:07,780
Let us see how to insert a new node in our existing link list, here is an example already I have a

2
00:00:07,780 --> 00:00:12,280
link lists of a 5N then and this link lists.

3
00:00:12,520 --> 00:00:19,950
I want to insert a new node for insolation, I should mention at the what position I want to insert

4
00:00:19,960 --> 00:00:21,860
at what index I want to insert.

5
00:00:22,180 --> 00:00:23,640
So there are no indices here.

6
00:00:23,650 --> 00:00:26,050
So let us give some indices to these nodes.

7
00:00:26,530 --> 00:00:29,340
Let us call this morning's first node and this is second.

8
00:00:29,410 --> 00:00:30,610
So I have given indices.

9
00:00:31,060 --> 00:00:37,110
So usually in arrays we start index from zero on word so that this was given by the compiler itself.

10
00:00:37,480 --> 00:00:38,820
But here it's our wish.

11
00:00:38,830 --> 00:00:44,590
We can give the indices from Anonymous also, or if you want, you can change them to zero volts, not

12
00:00:44,600 --> 00:00:45,760
inserting a new node.

13
00:00:46,120 --> 00:00:50,080
Let us see what other places that are available that I can insert a new node.

14
00:00:50,320 --> 00:00:53,100
I can insert a new node before fust node.

15
00:00:53,920 --> 00:00:58,660
Let us call this as one of the position and next I can insert it in between.

16
00:00:58,660 --> 00:01:02,350
These two are in between this and so on.

17
00:01:03,430 --> 00:01:09,760
And also, I can insert a new note after the last note, let us give it, and this is for these positions,

18
00:01:09,970 --> 00:01:14,920
let us call this as zero, because this is the first note before that position.

19
00:01:14,920 --> 00:01:15,950
Let us say this is zero.

20
00:01:16,240 --> 00:01:17,680
This is one and two.

21
00:01:17,920 --> 00:01:20,610
So I have given indices from zero to five.

22
00:01:20,860 --> 00:01:26,280
So if I'm inserting here, let us call this index zero, I want to insert at index zero.

23
00:01:26,530 --> 00:01:33,740
Then if I say I want to insert that index one minutes after first in order, if I say next three months

24
00:01:33,760 --> 00:01:42,130
after third, let us call it like this, I may want to insert a new note before the first node, or

25
00:01:42,250 --> 00:01:46,380
I may want to insert a new node after a given position.

26
00:01:46,390 --> 00:01:48,270
Let us say fourth position.

27
00:01:48,280 --> 00:01:50,090
So I want to insert after footnote.

28
00:01:50,320 --> 00:01:59,320
So let us take these two cases of insertion, first cases inserting before first node, second cases

29
00:01:59,620 --> 00:02:02,450
inserting after a given position.

30
00:02:02,560 --> 00:02:04,420
So these are the two cases of insertion.

31
00:02:05,170 --> 00:02:09,759
So we look at first case, first one inserting before the first node.

32
00:02:09,970 --> 00:02:15,160
If you are inserting a new law before the first node, then you have to move first point also because

33
00:02:15,160 --> 00:02:22,300
that new node becomes the node, otherwise inserting after any nor there is no change in first simple,

34
00:02:22,310 --> 00:02:25,300
you can insert a new node at any given index.

35
00:02:25,780 --> 00:02:28,260
So we will handle these as two different cases.

36
00:02:28,570 --> 00:02:32,550
So let us look at first case inserting before the first node.

37
00:02:32,950 --> 00:02:36,910
Let us see the step for inserting a new node before first node.

38
00:02:37,390 --> 00:02:40,470
So first step, create a new node.

39
00:02:41,050 --> 00:02:44,800
Let us take a new node with the help of a pointer.

40
00:02:45,040 --> 00:02:49,540
Let us be so first step is creating a new node.

41
00:02:50,170 --> 00:02:52,930
Then after creating a new node, initialize the node.

42
00:02:52,930 --> 00:02:55,830
Whatever the data you want to insert, insert that data.

43
00:02:55,840 --> 00:02:59,200
So let's say I want to store it then is the second step.

44
00:03:00,420 --> 00:03:05,820
Now, this law should become a force known because it is getting inserted before Fastenal, so this

45
00:03:05,820 --> 00:03:07,620
should point on this one.

46
00:03:08,040 --> 00:03:09,300
So this is the third step.

47
00:03:09,750 --> 00:03:10,620
Create a..

48
00:03:10,800 --> 00:03:14,160
Insert data and make it point on first.

49
00:03:14,710 --> 00:03:18,870
Then the move first from here to this new node.

50
00:03:20,440 --> 00:03:28,750
First, so these are the steps while just right those steps, so first step, creating a new law for

51
00:03:28,750 --> 00:03:29,800
creating a new node.

52
00:03:29,800 --> 00:03:38,500
We need a pointer named as a T node T and directly here and single step, I will say a new node, a

53
00:03:38,500 --> 00:03:39,750
new node will be created.

54
00:03:40,930 --> 00:03:46,950
Then in this place I should write down data that is that these data should be assigned with the value

55
00:03:46,950 --> 00:03:47,370
of ten.

56
00:03:47,710 --> 00:03:52,720
So these data assign it with some value, whatever the value is.

57
00:03:52,720 --> 00:03:59,950
So let's say there is some value in X five months, meaning that the next step make a D next point on

58
00:03:59,950 --> 00:04:00,420
first.

59
00:04:00,580 --> 00:04:04,630
So D next point on first.

60
00:04:04,960 --> 00:04:11,110
The next, the next step is bring first on this new order t so change first to T.

61
00:04:11,680 --> 00:04:15,630
So these are the four steps required for inserting a new node before first node.

62
00:04:16,060 --> 00:04:17,610
So steps are just four.

63
00:04:17,620 --> 00:04:19,269
So there are constant steps.

64
00:04:19,990 --> 00:04:26,090
If we want to know the time complexity for inserting before first node, then the time is order of one.

65
00:04:26,560 --> 00:04:33,430
So it means inserting a new node on the site of a link that is left inside of a Linklaters takes Constanta

66
00:04:33,430 --> 00:04:33,960
time.

67
00:04:34,000 --> 00:04:35,650
Remember this now.

68
00:04:35,680 --> 00:04:39,470
Next, let us learn how to insert a new node at a given position.

69
00:04:40,300 --> 00:04:47,520
Let us insert a new node after position for that is indexed for so let us call position.

70
00:04:47,530 --> 00:04:52,460
As far as example from taking Bewes position as four.

71
00:04:52,840 --> 00:04:56,470
So I want to insert a new node of the position for one of the things I have to do.

72
00:04:56,860 --> 00:04:58,590
First of all, I should create a new node.

73
00:04:58,630 --> 00:05:04,720
So for creating a new node again, take a temporary pointer and create a new node the and initialized

74
00:05:04,720 --> 00:05:06,980
with some data, whatever the data you want to insert.

75
00:05:07,330 --> 00:05:09,520
This is really not one of the things I have to do.

76
00:05:09,700 --> 00:05:15,370
I have to make this point on six that it's node five and node for the seven.

77
00:05:15,580 --> 00:05:19,420
This node should point on T, so these are the two changes I have to make.

78
00:05:20,470 --> 00:05:26,860
So for modifying this one, I should note that this of this node five and to modify this one, I should

79
00:05:26,860 --> 00:05:28,510
have some pointer accessing this.

80
00:05:28,510 --> 00:05:30,160
More like this.

81
00:05:30,160 --> 00:05:34,300
Not you can access because you have a pointer on this note, the same way there should be some pointer

82
00:05:34,300 --> 00:05:39,090
on this note and only we can access that one or modify that one for having a pointer there.

83
00:05:39,100 --> 00:05:42,030
I should bring a pointer from the first node onwards.

84
00:05:42,040 --> 00:05:43,900
I cannot directly get a pointer on this node.

85
00:05:44,170 --> 00:05:47,760
So let us take some pointer B and bring it in there.

86
00:05:48,160 --> 00:05:52,720
So let us take a pointer, take a pointer, be pointing one first node.

87
00:05:53,910 --> 00:06:02,720
This is pointing Monsignore not move, be the next, nor the next, nor here, so be a fine piece next,

88
00:06:03,030 --> 00:06:04,770
then one more time be assigned.

89
00:06:04,780 --> 00:06:07,950
These next two times it is done, then again be assigned.

90
00:06:07,950 --> 00:06:09,410
These next two becomes here.

91
00:06:09,810 --> 00:06:11,370
So in three steps.

92
00:06:11,410 --> 00:06:12,510
People come here.

93
00:06:12,510 --> 00:06:15,720
First step, second step and three steps.

94
00:06:15,720 --> 00:06:16,700
B will move there.

95
00:06:16,920 --> 00:06:21,900
So three times I should move B so that it comes on the node four so that I can answer them.

96
00:06:21,910 --> 00:06:27,750
You know, after that node fake means if the position is four I should move P four three times.

97
00:06:28,410 --> 00:06:33,600
If suppose the position was three then B should move just two times.

98
00:06:33,840 --> 00:06:36,310
So B should move for position minus one times.

99
00:06:37,260 --> 00:06:38,310
So this is one step.

100
00:06:38,970 --> 00:06:40,800
No, I have to modify the links.

101
00:06:40,800 --> 00:06:42,510
So what other links should be modified.

102
00:06:42,840 --> 00:06:46,210
D next should point on the smaller.

103
00:06:46,230 --> 00:06:47,940
So how do you get the gist of this note.

104
00:06:48,180 --> 00:06:49,220
That is BS next.

105
00:06:49,950 --> 00:06:56,100
Yes D next should point on the node whose addresses president these next.

106
00:06:56,410 --> 00:06:58,500
So this is one step now.

107
00:06:58,530 --> 00:06:59,280
Last step.

108
00:07:00,030 --> 00:07:05,220
PS Next this one should point on T that's on.

109
00:07:05,760 --> 00:07:12,390
Now if you start from first nought you can, you can go to the next node that is three, the next note

110
00:07:12,390 --> 00:07:16,170
is nine, the next node seven, the next node Austin and next to the six.

111
00:07:16,350 --> 00:07:24,150
So the stent is inserted in between that node four and five are often in order for these are the steps.

112
00:07:25,350 --> 00:07:27,390
Let me write on the code for doing this one.

113
00:07:27,780 --> 00:07:28,880
What was the first step?

114
00:07:29,130 --> 00:07:30,240
Create a new node.

115
00:07:30,390 --> 00:07:37,380
So for creating a new node, node B and C new node, an order will be created in he.

116
00:07:38,880 --> 00:07:46,230
The next step I have written down here, so the data is X, whatever the value we want.

117
00:07:48,540 --> 00:07:59,730
Then I brought a pointer p tail here, so start P from first, so P starts from first and move P for

118
00:07:59,730 --> 00:08:01,890
how many times it was for.

119
00:08:01,920 --> 00:08:03,450
So we have moved for three times.

120
00:08:04,140 --> 00:08:08,130
So if it is position be worse than it should be.

121
00:08:08,430 --> 00:08:10,050
Position minus one times.

122
00:08:10,050 --> 00:08:11,300
Buell's minus one times.

123
00:08:11,580 --> 00:08:16,050
So go on moving P so how to move P P assigned B's next.

124
00:08:16,210 --> 00:08:19,490
There's no code for moving P and this is step.

125
00:08:19,500 --> 00:08:22,040
I should perform it for position minus one times.

126
00:08:22,410 --> 00:08:24,480
So let us write a follow for doing this.

127
00:08:25,170 --> 00:08:30,690
For I assign zero is less than position minus one leg position is given here.

128
00:08:31,320 --> 00:08:32,580
I plus plus.

129
00:08:34,789 --> 00:08:42,470
This will bring me on that fourth note that after this, what are the steps these next should point

130
00:08:42,470 --> 00:08:43,030
on this one?

131
00:08:43,039 --> 00:08:46,420
So then at that time he was pointing here, if you remember.

132
00:08:47,150 --> 00:08:49,280
So this is an extra point on BP's next.

133
00:08:49,850 --> 00:08:54,870
So these next should be pointing on BP's next.

134
00:08:54,890 --> 00:09:00,710
So wherever BP's next is assemblies, let me also point on that one, then change.

135
00:09:00,710 --> 00:09:04,610
BP's next to be so modified.

136
00:09:04,910 --> 00:09:08,150
BP's next as the.

137
00:09:09,540 --> 00:09:10,080
That's wrong.

138
00:09:10,620 --> 00:09:17,040
So these are the steps creating a new law, filling the data, bringing beyond that node for node,

139
00:09:17,310 --> 00:09:18,870
then modifying links.

140
00:09:19,770 --> 00:09:22,200
Let us analyze this code first thing.

141
00:09:22,480 --> 00:09:23,940
How many extra points required?

142
00:09:23,940 --> 00:09:30,270
Two extra points, one for creating a new node and one for changing links, modifying links without

143
00:09:30,270 --> 00:09:31,470
B, we cannot insert it.

144
00:09:31,590 --> 00:09:34,220
So two extra points then.

145
00:09:34,230 --> 00:09:36,720
Second thing, how many links are modified?

146
00:09:36,720 --> 00:09:37,540
S.V. Modified.

147
00:09:37,560 --> 00:09:39,900
These are colossal things like these are pointers.

148
00:09:40,250 --> 00:09:44,850
Actually, this is also a pointer, but these pointers are meant for linking and these pointers are

149
00:09:44,850 --> 00:09:45,960
meant for accessing.

150
00:09:46,320 --> 00:09:50,610
So the points which are meant for accessing, we call them as pointers and the pointers which are meant

151
00:09:50,610 --> 00:09:52,950
for holding the next node we call the link.

152
00:09:53,700 --> 00:09:54,980
So how many links are modified?

153
00:09:55,260 --> 00:09:56,790
One link and two links.

154
00:09:56,940 --> 00:09:58,690
So two links are modified in this one.

155
00:09:59,280 --> 00:10:00,780
Next, how much time it is taking.

156
00:10:02,260 --> 00:10:07,090
So all the steps are taking Constantine, creating a new node, filling the data, modifying links.

157
00:10:07,120 --> 00:10:08,650
But who is taking more time?

158
00:10:08,980 --> 00:10:12,310
This moving P to that particular position takes more time.

159
00:10:12,580 --> 00:10:15,520
And the time for moving B depends on the position.

160
00:10:15,520 --> 00:10:17,490
So position in the position.

161
00:10:17,860 --> 00:10:19,060
So it is outdraw.

162
00:10:19,120 --> 00:10:26,180
And so the time taken by this procedure is outdraw and not one more thing in this one.

163
00:10:26,860 --> 00:10:33,100
So the time taken this order of M that depends on the position C Fineman's setting off the last position,

164
00:10:33,340 --> 00:10:35,030
then it will take maximum time.

165
00:10:35,560 --> 00:10:38,440
What if I'm inserting off the first node?

166
00:10:39,660 --> 00:10:42,860
How much time it takes so we will not move at all.

167
00:10:42,910 --> 00:10:50,160
We will remain on for not only we will not move, so it will not take on time, it will take one time.

168
00:10:50,490 --> 00:10:56,610
So the time taken for insertion minimum time is one maximum time outdraw.

169
00:10:56,640 --> 00:10:58,530
And so here are right on.

170
00:10:58,980 --> 00:11:05,390
Minimum time taken is one order of one and maximum time taken in order of.

171
00:11:06,240 --> 00:11:12,540
So when it is one, if you are inserting after first not here because we will not move, we do not have

172
00:11:12,540 --> 00:11:16,640
to move B and is then if you are insisting after last.

173
00:11:17,490 --> 00:11:20,100
So this is the analysis, not one more thing.

174
00:11:20,640 --> 00:11:25,370
This quote I have taken an example for insertion of footnote.

175
00:11:25,950 --> 00:11:29,070
Do you think it will work in setting off the third node also.

176
00:11:29,190 --> 00:11:29,820
Yes.

177
00:11:30,330 --> 00:11:32,010
After psychonaut also yes.

178
00:11:32,370 --> 00:11:33,540
After first node.

179
00:11:34,580 --> 00:11:40,010
If you have a little secret, then after last inaudible the school took.

180
00:11:41,150 --> 00:11:46,770
Maybe let us verify it, so let us verify whether this court will work for insulting after Fastenal

181
00:11:47,090 --> 00:11:48,850
and insulting after last fall.

182
00:11:49,130 --> 00:11:54,800
So I will remove your things and I will trace the score for inserting after first note and after last

183
00:11:54,800 --> 00:11:56,200
call and let us check it.

184
00:11:56,480 --> 00:11:58,220
Let us see instead of the first note.

185
00:11:58,430 --> 00:12:04,100
First of all, create a new order with the help of pointer to create a new norm, then make this data

186
00:12:04,100 --> 00:12:04,750
as example.

187
00:12:04,760 --> 00:12:06,590
They want to insert in the next list.

188
00:12:06,590 --> 00:12:12,760
Be on first, be on first, then next year we will move for position minus one time.

189
00:12:12,770 --> 00:12:13,970
So what is the position here?

190
00:12:14,210 --> 00:12:15,290
Position this one.

191
00:12:15,440 --> 00:12:18,620
So position is one one minus one Dimond's zero times.

192
00:12:18,980 --> 00:12:21,080
So it means this loop will not execute at all.

193
00:12:21,090 --> 00:12:22,190
B will not move.

194
00:12:22,370 --> 00:12:27,070
So pediments, on the first note, only then the next step is a D next Espy's next.

195
00:12:27,080 --> 00:12:28,760
So D next is Besnik.

196
00:12:28,880 --> 00:12:34,210
So where PS next is second order so it will point on psychonaut then PS next city.

197
00:12:34,610 --> 00:12:38,600
So PS Next which is binding on second node nine will point on this new node.

198
00:12:39,630 --> 00:12:45,870
Yes, it is working, the same steps are working, so if I start from four Snoad after eight, I'll

199
00:12:45,870 --> 00:12:49,040
be going on 10, then 10 will send me on this note three.

200
00:12:49,950 --> 00:12:50,940
So it's inserted.

201
00:12:52,260 --> 00:12:59,310
Let us verify whether it will insert after 50 position or not, after last not position is fine.

202
00:12:59,350 --> 00:13:04,680
Suppose I want to instead of the last note, let us follow these steps and trace them and see whether

203
00:13:04,680 --> 00:13:05,490
it's working or not.

204
00:13:05,790 --> 00:13:08,330
First of all, create a new node with the help of pointer.

205
00:13:08,910 --> 00:13:12,330
So with the help of Pointer to create a new norm then through data there.

206
00:13:12,360 --> 00:13:20,900
So then this will then be assigned first PS on first and then move P for position minus one time.

207
00:13:20,910 --> 00:13:23,880
This line is for moving P for position minus one time.

208
00:13:23,910 --> 00:13:25,270
So what is position five.

209
00:13:25,590 --> 00:13:28,140
So it has to move P four four times.

210
00:13:28,410 --> 00:13:30,450
One, two, three, four.

211
00:13:30,690 --> 00:13:34,080
So P comes here for PS on last node.

212
00:13:34,290 --> 00:13:41,210
Next step is these next Espy's next 36 should be same as business for this piece and none.

213
00:13:41,220 --> 00:13:43,930
So this is none then these next Steve.

214
00:13:44,220 --> 00:13:47,190
So next is be working.

215
00:13:47,610 --> 00:13:50,190
It has inserted a new node after last nodes.

216
00:13:50,280 --> 00:13:55,620
You start from here, it sends you on three three sends you on nine nine six seven seven seven seven

217
00:13:55,740 --> 00:13:57,030
and six and six.

218
00:13:57,030 --> 00:14:00,070
And you go on ten and after ten there is nothing.

219
00:14:00,540 --> 00:14:03,960
So this is inserting comfortably after the last note also.

220
00:14:04,170 --> 00:14:10,290
So this code is perfect for inserting anywhere in the linked list, but after a given position.

221
00:14:11,250 --> 00:14:16,040
But if you want to insert before the first node, then the code is a special.

222
00:14:16,050 --> 00:14:17,310
We have already seen this.

223
00:14:18,060 --> 00:14:22,640
I will combine these two things and write it as a single function for insertion.

224
00:14:22,980 --> 00:14:26,130
I will write on the code and just quickly explain the code.

225
00:14:26,670 --> 00:14:28,830
I have redundant insert function here.

226
00:14:28,830 --> 00:14:30,810
Let us quickly read this function.

227
00:14:31,500 --> 00:14:35,730
See, insert function is taking two parameters at which position you want to insert and what is the

228
00:14:35,730 --> 00:14:37,170
value that you want to insert.

229
00:14:37,950 --> 00:14:44,420
Then we need a pointer first for the linguist, also assume that it is a global variable part, the

230
00:14:44,430 --> 00:14:47,210
first is accessible to all the functions in the program.

231
00:14:47,220 --> 00:14:53,910
So this function can also explicit another to see what is happening inside a function and begin to understand

232
00:14:54,120 --> 00:14:55,860
that we require extra pointers.

233
00:14:56,040 --> 00:15:03,050
That if position is zero means we have to insert before for Fassnacht, then create a new audience or

234
00:15:03,060 --> 00:15:07,520
data and make that new Northpoint point on first note and bring first on that.

235
00:15:07,530 --> 00:15:09,770
You know, these are the steps already we have seen.

236
00:15:10,290 --> 00:15:14,130
So these are the steps that are required for inserting before the first Naude.

237
00:15:14,670 --> 00:15:15,650
This work is over.

238
00:15:16,380 --> 00:15:21,750
If the position is greater than zero means any other position apart from Zettl, then we already know

239
00:15:21,750 --> 00:15:27,530
the steps we have to take point of being and bring it to a given position, for example, forward.

240
00:15:27,540 --> 00:15:29,010
So we have to bring it up to forward.

241
00:15:29,610 --> 00:15:31,770
So that moment of PME doing it first.

242
00:15:33,340 --> 00:15:40,090
And while moving, I'm also checking whether a B is not null or not, if he becomes null, it's useless.

243
00:15:40,120 --> 00:15:49,420
Stop means if there are only five North and giving index as A 10, so B has to stop after five, it

244
00:15:49,420 --> 00:15:50,470
should not continue.

245
00:15:51,070 --> 00:15:53,980
Otherwise, it's under Dimitar and the program will crash.

246
00:15:54,130 --> 00:15:58,790
So I am also checking whether P is pointing on some node or not.

247
00:15:59,200 --> 00:16:05,650
It should not be null, so it will stop if it has reached the exact position or B became null.

248
00:16:06,580 --> 00:16:11,380
So afterwards here I am checking their PS valid or not, it's pointing on some node or not.

249
00:16:11,650 --> 00:16:17,470
If it is pointing, then these other steps, creating a new node, assigning data, making new Northpoint

250
00:16:17,470 --> 00:16:25,390
on next node of B and then B's next pointing T these steps already we have seen so like this we can

251
00:16:25,390 --> 00:16:31,690
write a single function for inserting any it in the link lists at a given position starting from zero

252
00:16:31,810 --> 00:16:33,310
up to the limit of a linguist.

253
00:16:33,970 --> 00:16:35,440
So that's all about insertion.

