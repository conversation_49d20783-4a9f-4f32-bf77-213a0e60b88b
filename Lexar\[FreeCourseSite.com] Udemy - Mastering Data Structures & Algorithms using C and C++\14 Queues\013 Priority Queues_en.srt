1
00:00:00,440 --> 00:00:07,210
Topic is a priority, cuz there are two methods of implementing articles depending on the situations,

2
00:00:07,820 --> 00:00:08,800
so it'll study them.

3
00:00:09,290 --> 00:00:11,280
One is a limited set of priorities.

4
00:00:11,300 --> 00:00:13,210
I'm calling it a limited set of priorities.

5
00:00:13,250 --> 00:00:16,250
Are there other priorities are fixed?

6
00:00:16,250 --> 00:00:21,190
The nexus element priority for the element itself will have its own priority.

7
00:00:21,680 --> 00:00:24,160
So let us see these two methods one by one.

8
00:00:24,200 --> 00:00:25,730
So let us look at first Matak.

9
00:00:26,060 --> 00:00:27,590
Let us look at first network.

10
00:00:27,980 --> 00:00:31,640
See, this method is useful mostly in operating system.

11
00:00:32,750 --> 00:00:39,170
Some operating system allows priority base scheduling like in Java.

12
00:00:39,350 --> 00:00:47,630
JVM supports multitracking, so allow priorities upon Trex so you can set the priorities for attack.

13
00:00:48,020 --> 00:00:56,270
And Java supports priorities from one to ten to ten different priorities Uhtred can have so higher priority

14
00:00:56,270 --> 00:00:57,800
Trex will execute fast.

15
00:00:58,280 --> 00:01:02,180
That same thing is implemented using limited set of priorities like in Java.

16
00:01:02,180 --> 00:01:03,290
Ten priorities are there.

17
00:01:03,290 --> 00:01:06,290
So here in my example, I have taken three priorities.

18
00:01:07,190 --> 00:01:08,480
Now these are the elements.

19
00:01:08,750 --> 00:01:15,260
So instead of numbers, I have taken alphabet's and each element is having its own priorities.

20
00:01:15,260 --> 00:01:17,180
So priorities are numeric numerical.

21
00:01:17,180 --> 00:01:20,060
And that's why I have taken elements of alphabets.

22
00:01:20,660 --> 00:01:28,780
So this is the priority now, one man's highest priority and two, a little less than that and three

23
00:01:28,790 --> 00:01:30,050
is least priority.

24
00:01:30,260 --> 00:01:31,550
You can set it either way.

25
00:01:31,550 --> 00:01:39,230
Also, Freeman's highest priority and one man's lowest priority, like in Java 10, is highest priority.

26
00:01:39,230 --> 00:01:40,370
One is low priority.

27
00:01:40,850 --> 00:01:47,210
But here in my example, something smaller, the number higher, the priority or larger, the number

28
00:01:47,210 --> 00:01:48,530
smaller, the priority.

29
00:01:48,560 --> 00:01:54,380
This what I'm seeing for implementing priority queue as there are three priorities only in the system.

30
00:01:54,380 --> 00:01:55,940
So I have taken three calls.

31
00:01:56,270 --> 00:01:58,700
So you have to take three cues for implementing this one.

32
00:01:58,880 --> 00:02:03,910
So now let us look at the working seven, insert these elements in this particular case.

33
00:02:04,400 --> 00:02:09,020
So every element is inserted in the priority queue depending on its priority.

34
00:02:09,259 --> 00:02:11,690
So A's having priority one.

35
00:02:11,690 --> 00:02:15,980
So it will be inserted in Q1, then having priority one.

36
00:02:15,980 --> 00:02:20,330
So it will be inserted in Q1 CS to these three.

37
00:02:21,830 --> 00:02:27,320
Easy to us as one, then G is to.

38
00:02:28,530 --> 00:02:36,900
Each is three, I am are two, so the elements I have inserted them based on the priority is more elements

39
00:02:36,900 --> 00:02:37,410
are coming.

40
00:02:37,410 --> 00:02:38,460
I'll be inserting them.

41
00:02:38,460 --> 00:02:44,220
Whenever I have to insert any element, I will take its priority and I will inserted that.

42
00:02:44,520 --> 00:02:46,650
I just look at delete whenever you want.

43
00:02:46,650 --> 00:02:47,280
You can delete.

44
00:02:47,610 --> 00:02:48,930
But anyway, you are deleting.

45
00:02:48,930 --> 00:02:53,810
You must delete an element from highest priority queue that is first one.

46
00:02:54,090 --> 00:02:55,770
So this element must be deleted.

47
00:02:55,950 --> 00:02:57,990
You cannot delete any other element.

48
00:02:58,200 --> 00:03:01,890
You must delete an element only from two one.

49
00:03:02,460 --> 00:03:03,600
Only from Q one.

50
00:03:04,550 --> 00:03:09,980
And that 24 for fashion, so strictly for you should follow next element they want to delete.

51
00:03:10,010 --> 00:03:11,240
So who's the next element?

52
00:03:11,240 --> 00:03:11,780
Q1?

53
00:03:11,780 --> 00:03:12,770
Q1 is not empty.

54
00:03:12,800 --> 00:03:13,580
There is an element.

55
00:03:13,580 --> 00:03:14,420
Delete that one.

56
00:03:15,320 --> 00:03:17,810
Supples after I got one more element.

57
00:03:17,810 --> 00:03:19,220
Whose priority is the one?

58
00:03:19,220 --> 00:03:22,200
Only then it will come here only in Q1.

59
00:03:22,960 --> 00:03:26,330
Now again they want to delete ok from Q1 deleted.

60
00:03:26,750 --> 00:03:28,640
I want to delete Q1.

61
00:03:28,640 --> 00:03:29,240
Delete it.

62
00:03:30,330 --> 00:03:36,440
Now, again, delete Q one is empty, then go for second priority, that is this one.

63
00:03:36,810 --> 00:03:39,320
Now start deleting the elements from this.

64
00:03:39,330 --> 00:03:45,360
Q Suppose after this again I have one element and whose parody one it comes here.

65
00:03:46,570 --> 00:03:50,260
Then again, if I want to delete, I was delete this one, I must leave this one.

66
00:03:50,290 --> 00:03:55,210
So from this working, I can say that deletion should be done always from highest priority queue.

67
00:03:55,390 --> 00:03:57,280
And if it is empty, go to the next priority.

68
00:03:57,280 --> 00:03:59,550
If that is all simply, then go to the next priority.

69
00:03:59,770 --> 00:04:01,240
That's all about particle's.

70
00:04:01,240 --> 00:04:02,290
No more on this one.

71
00:04:02,300 --> 00:04:05,290
That is starvation and all that other concepts of operating system.

72
00:04:05,300 --> 00:04:11,990
So if you want to study further, you can study it and you have to implement this one as a student exercise.

73
00:04:12,160 --> 00:04:19,089
So right on the program, which is having more than one kills on each, you must have priority and take

74
00:04:19,089 --> 00:04:21,450
the elements along with the priorities and insert them.

75
00:04:21,579 --> 00:04:23,040
So there's this student exercise.

76
00:04:23,260 --> 00:04:28,360
Now, let's let us look at second method where every element is having its own priority.

77
00:04:29,530 --> 00:04:34,010
Now, second, third there, the element itself is a priority.

78
00:04:34,420 --> 00:04:38,380
For example, I have set of elements six, eight, three, eight and so on.

79
00:04:38,950 --> 00:04:40,540
The element itself is a priority.

80
00:04:40,540 --> 00:04:43,160
So there is no magic number itself is the priority.

81
00:04:43,420 --> 00:04:46,390
So six is the priority of that element.

82
00:04:47,080 --> 00:04:49,680
And this element, second element value is eight.

83
00:04:49,810 --> 00:04:51,270
So the priority is also eight.

84
00:04:51,910 --> 00:04:57,700
So it means there are unlimited priorities, pretty community and even the elements can have the same

85
00:04:57,700 --> 00:04:58,100
priority.

86
00:04:58,120 --> 00:05:00,460
Also, like I can have duplicate elements.

87
00:05:00,460 --> 00:05:01,480
Eight one more eight.

88
00:05:01,490 --> 00:05:02,410
I have eight.

89
00:05:02,560 --> 00:05:03,840
One more eight I have.

90
00:05:03,850 --> 00:05:06,310
So this is duplicate same 30.

91
00:05:06,670 --> 00:05:11,740
So two elements that seem ready and which one is minimum priority wishfulness, maximum priority so

92
00:05:11,740 --> 00:05:15,910
far that I have said the rule here, that smaller number, higher priority.

93
00:05:15,940 --> 00:05:18,310
If the number is small then priority is higher.

94
00:05:18,910 --> 00:05:20,330
You can change this to other.

95
00:05:20,330 --> 00:05:23,620
We also like larger number, higher priority.

96
00:05:23,620 --> 00:05:26,490
So if the number is bigger or larger, then it is higher priority.

97
00:05:26,860 --> 00:05:33,970
So based on this one, which is of highest priority element to the second one is three, the next one

98
00:05:33,970 --> 00:05:34,750
is five.

99
00:05:34,870 --> 00:05:37,660
So like this, these are the highest priority elements.

100
00:05:37,660 --> 00:05:38,200
So go on.

101
00:05:38,530 --> 00:05:44,320
It means if you want the elements in the decreasing order of the first highest priority, the next highest

102
00:05:44,320 --> 00:05:47,320
priority, then you have to address the elements in increasing order.

103
00:05:47,890 --> 00:05:50,890
So that next the question is how to implement this.

104
00:05:51,340 --> 00:05:52,810
Now, this is not paid for.

105
00:05:52,840 --> 00:05:53,920
This is not feasible.

106
00:05:53,950 --> 00:05:54,330
Right.

107
00:05:54,970 --> 00:05:56,340
So I have just taken an Audi.

108
00:05:56,770 --> 00:05:58,840
I have to store those elements in Unmarry.

109
00:05:59,170 --> 00:06:04,750
So how should the store when you want to implement a particular using array for these set of numbers,

110
00:06:04,750 --> 00:06:06,190
that is unlimited priorities.

111
00:06:06,670 --> 00:06:08,160
There is one the method.

112
00:06:08,320 --> 00:06:10,330
We will learn that in some other section.

113
00:06:10,780 --> 00:06:15,700
But here I'm having two options or two suggestions, so let us see what those solutions are.

114
00:06:15,970 --> 00:06:21,310
First one is that I am inserting the elements, insert them in the same order as they are coming in,

115
00:06:21,310 --> 00:06:25,540
like suppose insert six, then eight and three and so on while deleting.

116
00:06:25,870 --> 00:06:31,420
Find out the maximum priority element, search for that element and then delete that element.

117
00:06:32,690 --> 00:06:33,830
Then second, Mattock.

118
00:06:35,520 --> 00:06:41,010
Inserted increasing order of the priority, so when the elements are coming in, don't as it is, try

119
00:06:41,020 --> 00:06:46,530
to reach them in increasing order of the priority so that the last element is of highest priority.

120
00:06:47,540 --> 00:06:50,850
And when you are reading, you can simply delete the last element.

121
00:06:51,620 --> 00:06:57,410
So the difference in these two methods is, one, inserting we are simply inserting it, but while deleting,

122
00:06:57,410 --> 00:07:00,290
we are finding out or searching for an element and deleting it.

123
00:07:01,160 --> 00:07:06,230
And here while inserting we are trying to properly arrange it while deleting, we are simply deleting

124
00:07:06,230 --> 00:07:06,760
an element.

125
00:07:06,980 --> 00:07:09,830
So I will demonstrate how these networks looks one by one.

126
00:07:10,130 --> 00:07:16,230
So first of all, I will take let us insert and delete a few elements such as I want to insert element

127
00:07:16,320 --> 00:07:21,290
six inserted next element at the last next element at the last.

128
00:07:21,680 --> 00:07:22,970
Next element at the last.

129
00:07:23,210 --> 00:07:25,460
So every time I am inserting an element at the last.

130
00:07:25,490 --> 00:07:30,000
So how much time it is taking for one element, Konstantine like I want to insert 15.

131
00:07:30,020 --> 00:07:32,100
OK, inserted here this blast element right.

132
00:07:32,660 --> 00:07:35,060
The next I want to insert to insert it here.

133
00:07:35,150 --> 00:07:37,880
So Konstantine then insert here name.

134
00:07:38,780 --> 00:07:39,080
Right.

135
00:07:39,410 --> 00:07:41,800
So this the recently last inserted element.

136
00:07:42,410 --> 00:07:44,360
So the time for insert is constant.

137
00:07:45,370 --> 00:07:46,510
And I want to delete.

138
00:07:47,890 --> 00:07:54,490
Delete so I can delete, I cannot delete any element that I like, I have to delete highest priority

139
00:07:54,510 --> 00:07:56,670
element only then only we say it's critical.

140
00:07:57,370 --> 00:08:03,210
So which is of highest priority, the stories of highest priority, as I said, some all them number

141
00:08:03,250 --> 00:08:04,150
higher priority.

142
00:08:04,360 --> 00:08:08,830
So to is of highest priority then how do I know that is of minimum priority.

143
00:08:09,070 --> 00:08:10,090
Search for it.

144
00:08:10,510 --> 00:08:11,250
Search for it.

145
00:08:11,650 --> 00:08:14,120
So search and find out then delete.

146
00:08:14,770 --> 00:08:17,470
So first search and then find out, then delete.

147
00:08:17,740 --> 00:08:23,020
OK, after deleting this one move nine here because I have to shift elements onto.

148
00:08:25,110 --> 00:08:31,020
The next highest priority search, which is minimum, this one, smaller number, higher priority,

149
00:08:31,290 --> 00:08:35,090
so delete this one after deleting, also have to shoot the elements.

150
00:08:35,850 --> 00:08:37,350
So I have to do two things.

151
00:08:39,260 --> 00:08:49,100
Search, which takes all the time and shift I have to do for deletion, so that also takes a lot of

152
00:08:49,250 --> 00:08:49,600
time.

153
00:08:49,910 --> 00:08:51,770
So total it is to in.

154
00:08:53,470 --> 00:08:59,650
Doing so, would you guys outdraw and the insertion takes constant time and deletion takes a lot of

155
00:08:59,840 --> 00:09:00,120
time.

156
00:09:01,360 --> 00:09:04,990
So I was simply copying the elements and while deleting, I was searching.

157
00:09:06,080 --> 00:09:12,320
No, I'm you second matter that the second solution, the second solution, I will insert those elements,

158
00:09:12,320 --> 00:09:16,610
just watch Halam inserting six or six perfect.

159
00:09:17,330 --> 00:09:26,160
Then eight C six is smaller than each move six here and insert eight moving insert then three.

160
00:09:26,360 --> 00:09:28,560
OK, three is a smaller than six.

161
00:09:28,560 --> 00:09:30,260
So insert ten.

162
00:09:31,100 --> 00:09:31,580
Ten.

163
00:09:31,580 --> 00:09:33,050
So 10 is greater than this one.

164
00:09:33,050 --> 00:09:33,740
Move three.

165
00:09:35,150 --> 00:09:35,900
Six.

166
00:09:37,030 --> 00:09:43,180
And this is good, doesn't it also Movahed then incertain here, so I am inserting an element in the

167
00:09:43,180 --> 00:09:44,080
sorted order.

168
00:09:44,980 --> 00:09:45,490
Yes.

169
00:09:45,580 --> 00:09:47,050
And that is decreasing order.

170
00:09:48,090 --> 00:09:49,600
So in certain elements.

171
00:09:51,370 --> 00:09:59,830
So, again, I want to insert so move three, move six, move eight, move ten and then insert 15.

172
00:10:00,420 --> 00:10:01,960
So this will shift all the elements.

173
00:10:01,960 --> 00:10:05,190
So how much time it is taking for insertion itself?

174
00:10:05,910 --> 00:10:08,610
And because I have to shift the elements.

175
00:10:08,790 --> 00:10:11,480
So Sudsing, I don't need and shifting.

176
00:10:11,640 --> 00:10:13,650
So shifting is taking outdraw of time.

177
00:10:14,870 --> 00:10:20,800
I have to shift because I have to insert in the Sakurada then how I delete delete.

178
00:10:20,840 --> 00:10:23,910
So which is the smallest element right now right presently.

179
00:10:23,930 --> 00:10:25,080
What is the smallest element.

180
00:10:25,410 --> 00:10:26,920
Three, certainly three.

181
00:10:26,930 --> 00:10:27,820
So take all three.

182
00:10:27,830 --> 00:10:29,000
That's on the list.

183
00:10:29,030 --> 00:10:29,870
Still here only.

184
00:10:30,290 --> 00:10:33,450
So how much time we is taking deletion Konstantine.

185
00:10:33,740 --> 00:10:35,010
So this has changed.

186
00:10:35,150 --> 00:10:38,730
So this is ticking in time and this is taking one time.

187
00:10:38,990 --> 00:10:41,140
So that's all the solutions I have given you.

188
00:10:41,150 --> 00:10:47,090
One suggestion was insert them as it is while deleting you such a second method.

189
00:10:47,300 --> 00:10:54,740
Insert them in the sorted order, decreasing order of value, increasing order of priority so that the

190
00:10:54,740 --> 00:11:00,060
highest priority element at the end, so that while deleting, we can simply delete it in question time.

191
00:11:00,650 --> 00:11:04,670
So whichever method you follow, one of the operation is taking a lot of time.

192
00:11:04,850 --> 00:11:07,880
The other operation is ticking, just constant time.

193
00:11:08,240 --> 00:11:08,900
So that's all.

194
00:11:08,900 --> 00:11:11,150
There is not the end of this particular.

195
00:11:11,510 --> 00:11:15,230
We will learn about the priority queue once again in some of the section.

