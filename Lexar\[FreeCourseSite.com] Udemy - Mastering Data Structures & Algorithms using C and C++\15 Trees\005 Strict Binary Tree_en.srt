1
00:00:00,150 --> 00:00:06,180
We will learn about strict boundaries, strict boundaries are also called as a by the entry or sometime

2
00:00:06,180 --> 00:00:08,970
they are also called complete binary tree.

3
00:00:09,330 --> 00:00:11,000
This table trees for analysis.

4
00:00:11,010 --> 00:00:16,710
If you have a binary tree and you found that it is a stricter than whatever the analysis we are learning

5
00:00:16,710 --> 00:00:19,060
that are suitable for that stick by the tree.

6
00:00:19,920 --> 00:00:23,980
So we'll be learning hydrolysis notes that we have already seen in binary tree.

7
00:00:24,450 --> 00:00:27,810
So here, what are the changes we will look at then?

8
00:00:27,960 --> 00:00:30,260
Internal sources, external laws.

9
00:00:30,780 --> 00:00:32,820
This also we have done in binary tree.

10
00:00:33,450 --> 00:00:37,830
Let us see what is the formula for internal and external in state money trees.

11
00:00:38,490 --> 00:00:41,430
So let us find out what are set by the trees.

12
00:00:42,550 --> 00:00:48,730
So first of all, let us know what artistic by nature is general boundary treatments, every note can

13
00:00:48,730 --> 00:00:56,530
have zero one or two children, but in their strict money, tree A. can have either a zero children

14
00:00:56,890 --> 00:00:58,960
or exactly two children.

15
00:00:59,650 --> 00:01:01,850
A. cannot have one child.

16
00:01:02,380 --> 00:01:05,420
So stick means it must be strictly binary.

17
00:01:05,920 --> 00:01:09,430
This should not be any node with the degree one that is unity.

18
00:01:10,390 --> 00:01:12,130
So here I have some examples.

19
00:01:12,280 --> 00:01:16,880
Let us take those examples to understand more clearly about binary trees.

20
00:01:17,410 --> 00:01:18,970
Let us look at this binary tree.

21
00:01:19,990 --> 00:01:21,540
This is known as having two children.

22
00:01:21,650 --> 00:01:24,470
OK, allow this notice having two children.

23
00:01:24,490 --> 00:01:27,220
Yes, this is known as having two children allowed.

24
00:01:27,730 --> 00:01:28,380
What about this?

25
00:01:28,730 --> 00:01:30,140
No children for this one.

26
00:01:30,400 --> 00:01:31,330
What about this note?

27
00:01:31,540 --> 00:01:32,740
Degrees zero degrees.

28
00:01:32,740 --> 00:01:34,050
Zero degrees zero.

29
00:01:34,270 --> 00:01:34,810
Yes.

30
00:01:34,810 --> 00:01:35,410
Perfect.

31
00:01:36,160 --> 00:01:37,630
What about this degree?

32
00:01:37,630 --> 00:01:38,980
Two degrees, zero degrees.

33
00:01:38,980 --> 00:01:40,000
Two degrees.

34
00:01:40,000 --> 00:01:40,620
Zero degrees.

35
00:01:41,290 --> 00:01:42,160
Yes, perfect.

36
00:01:42,850 --> 00:01:43,450
What about this?

37
00:01:43,450 --> 00:01:45,130
One degree, two degrees.

38
00:01:45,140 --> 00:01:45,880
Zero degrees.

39
00:01:45,880 --> 00:01:48,370
One degree one does not allow.

40
00:01:48,760 --> 00:01:51,280
So this is not a stick by military.

41
00:01:52,500 --> 00:01:58,380
Whatever this one degree, two degrees one, so because of this or there are other also this one is

42
00:01:58,380 --> 00:01:58,910
also there.

43
00:01:59,280 --> 00:02:06,390
So because of these laws and it's not a strict monitoring, this one for this, no degrees three.

44
00:02:06,930 --> 00:02:08,550
So this is not developed by the military.

45
00:02:08,820 --> 00:02:11,600
Let us look at this one degree, one degree one.

46
00:02:11,610 --> 00:02:14,050
So this is not a state by military.

47
00:02:14,460 --> 00:02:20,100
So in this example, these two are separate and a lot of them are not binary trees.

48
00:02:20,100 --> 00:02:21,990
And this is not even a mandatory.

