1
00:00:00,430 --> 00:00:05,860
And this video, we will write a recursive function for counting the number of nodes in a binary tree

2
00:00:06,400 --> 00:00:09,790
and also the function for finding the height of a binary tree.

3
00:00:10,540 --> 00:00:17,410
This project, the same project that we have used for creating a tree, performing traversal and also

4
00:00:17,410 --> 00:00:18,760
for level order traversal.

5
00:00:19,060 --> 00:00:20,740
Same program I'm using here.

6
00:00:21,040 --> 00:00:27,220
Just here before the main function, I will vote on a function for counting number of nodes in a binary

7
00:00:27,220 --> 00:00:30,010
tree, so it should take a pointer to rule.

8
00:00:30,220 --> 00:00:34,510
So I will name it as root only then if a root is not null.

9
00:00:36,490 --> 00:00:43,600
Then it should return call itself count on rudes alkyl.

10
00:00:46,120 --> 00:00:46,600
Plus.

11
00:00:47,760 --> 00:00:51,780
Down good cell phone rules, right, child?

12
00:00:53,940 --> 00:00:54,570
Plus one.

13
00:00:56,160 --> 00:00:57,930
Otherwise written Zettl.

14
00:00:59,550 --> 00:01:03,090
That's it, and also I will write a function for finding the right.

15
00:01:04,459 --> 00:01:07,250
Height struck Naude.

16
00:01:09,690 --> 00:01:16,440
Route if route is equal to zero, that is null, then written Zettl.

17
00:01:17,990 --> 00:01:24,860
Otherwise, I need two variables, X and Y, D, I will assign them to zero initially, then call the

18
00:01:24,860 --> 00:01:26,030
function recursively.

19
00:01:27,170 --> 00:01:30,830
Upon left child, that is rudes a child.

20
00:01:32,140 --> 00:01:33,580
And why?

21
00:01:35,830 --> 00:01:38,860
Height rules, right, child?

22
00:01:40,120 --> 00:01:44,430
Then whichever is greater, if X is greater than Y written.

23
00:01:45,720 --> 00:01:50,370
Plus one otherwise written, right, plus one.

24
00:01:51,690 --> 00:01:53,470
That's all this is for, fighting height.

25
00:01:53,490 --> 00:02:01,440
And also we have written a function for counting n print f I will display first count, so called the

26
00:02:01,440 --> 00:02:03,550
function bound and root.

27
00:02:05,170 --> 00:02:06,310
Then printf.

28
00:02:09,020 --> 00:02:11,250
Height percentile D.

29
00:02:15,100 --> 00:02:16,600
Height of Ruth.

30
00:02:19,060 --> 00:02:20,890
Now, let us run this program and see.

31
00:02:22,770 --> 00:02:31,300
Be it a tree, so roots turn left, child is 20, Rachel is 30, Earley's left child is a 40 when this

32
00:02:31,300 --> 00:02:32,440
Rachel is 50.

33
00:02:34,040 --> 00:02:36,230
And is not having any children.

34
00:02:38,550 --> 00:02:46,740
40S, not having any children, 50s having a child, 60 and right is not the 60s left child and Rachel

35
00:02:46,740 --> 00:02:47,300
are not there.

36
00:02:47,910 --> 00:02:52,110
So the number of known to have created are six and the height is four.

37
00:02:53,430 --> 00:02:59,850
So you can draw this tree on paper and check it and you can enter the values and verify the results

38
00:02:59,850 --> 00:03:02,870
written by this functions that discount on height.

39
00:03:04,320 --> 00:03:06,020
So try this program by yourself.

40
00:03:06,030 --> 00:03:08,580
Already we have seen this working and everything on Lightbown.

41
00:03:09,710 --> 00:03:14,480
So this is just a demonstration so you can practice this program, that's all in this video.

