1
00:00:01,070 --> 00:00:04,720
Yeah, in the video, we'll talk about asymptotic notations.

2
00:00:05,939 --> 00:00:11,600
We have seen various algorithms for various functions and various operations on data structures.

3
00:00:12,000 --> 00:00:14,760
We the time complexities in various forms.

4
00:00:16,320 --> 00:00:23,040
Like, we got the same complexity as one that is constant and sometimes regarded as log-in and sometimes

5
00:00:23,070 --> 00:00:25,530
an hour and log-in, so on.

6
00:00:28,280 --> 00:00:35,210
For example, inserting an element in a cube that is MQ or push, it was taking Konstantin<PERSON>.

7
00:00:36,970 --> 00:00:40,480
Something in a binary, if you're able to read, was taking log in time.

8
00:00:42,320 --> 00:00:43,940
Sensing an element in an array.

9
00:00:45,090 --> 00:00:45,510
Or.

10
00:00:47,370 --> 00:00:55,350
Finding the maximum element in an array or legalist, it was taking maximum amount of time, then analog

11
00:00:55,350 --> 00:00:59,610
in the fourth, more sort or quicksort, best case of quicksort.

12
00:01:00,730 --> 00:01:06,640
Then and square, that is matics also in the grass.

13
00:01:07,150 --> 00:01:11,120
The time was dependent on the metrics, the superbrain and prediction.

14
00:01:11,130 --> 00:01:11,900
We get this one.

15
00:01:12,190 --> 00:01:15,200
So too boring or it may be up to and boring.

16
00:01:15,490 --> 00:01:20,290
So this portion this time complexities are called exponential complexities.

17
00:01:24,780 --> 00:01:30,720
And these time complexities are called as polynomial time complexities, these are called polynomial.

18
00:01:32,470 --> 00:01:37,960
So all those strange complexities I have arranged them in the increasing order of their value, see

19
00:01:37,960 --> 00:01:45,190
and square value will be greater than in Logan and Logan will be greater than ours, say Logan is less

20
00:01:45,190 --> 00:01:46,720
than 10 and it's less than in Logan.

21
00:01:46,720 --> 00:01:50,620
For some, values often don't start in from zero or one.

22
00:01:50,830 --> 00:01:52,060
Don't say if I pulled zero.

23
00:01:52,070 --> 00:01:53,010
All are equal only.

24
00:01:53,020 --> 00:01:54,100
No, don't take that.

25
00:01:54,700 --> 00:01:56,250
If I take 10, they are equal.

26
00:01:56,260 --> 00:01:57,190
No, don't take 10.

27
00:01:57,190 --> 00:02:00,870
Also, you take a larger value, right?

28
00:02:02,830 --> 00:02:10,919
So the starting value of and not need not be zero or one, it can be any starting value, but beyond

29
00:02:10,930 --> 00:02:14,290
that, say, let us say zero to infinity.

30
00:02:14,470 --> 00:02:21,110
So from which point it is starting from there to infinity, if someone is greater than we say, it is

31
00:02:21,110 --> 00:02:21,540
a greater.

32
00:02:23,160 --> 00:02:31,380
Like, for example, I have a cube and to Warren is greater proportions much greater than Cube, how

33
00:02:31,620 --> 00:02:37,120
if I take two, so then this is two Kubis eight and has two squares form.

34
00:02:37,140 --> 00:02:40,720
See if one is a two, then how come it is greater.

35
00:02:40,920 --> 00:02:41,970
So don't take two.

36
00:02:43,860 --> 00:02:48,930
Take 10, so tendu this polzin to over 10.

37
00:02:48,960 --> 00:02:52,130
That's one zero two for all, this is bigger.

38
00:02:52,560 --> 00:02:54,570
OK, 11 Cuba Otake.

39
00:02:54,770 --> 00:03:01,300
So this may be one, two to one and this will be two over levels.

40
00:03:01,890 --> 00:03:05,130
Two zero four eight seat is double of this one.

41
00:03:05,380 --> 00:03:06,320
So much bigger.

42
00:03:07,140 --> 00:03:08,780
So don't take small values often.

43
00:03:09,210 --> 00:03:09,620
All right.

44
00:03:09,960 --> 00:03:12,420
So from some value of often, that is correct.

45
00:03:12,840 --> 00:03:16,770
So we have arranged them in increasing order of their values or village.

46
00:03:17,640 --> 00:03:21,790
Now, next thing, see what all the algorithms we have analyzed.

47
00:03:21,810 --> 00:03:25,680
We are getting the time, complexities like time function, we getting days.

48
00:03:26,040 --> 00:03:33,110
And so we calling the order of end or some time we were getting down and square, so and square.

49
00:03:33,450 --> 00:03:35,640
We did not analyze the code line by line.

50
00:03:35,790 --> 00:03:37,080
We were writing the time complex.

51
00:03:37,080 --> 00:03:38,400
It is based on the work done.

52
00:03:38,760 --> 00:03:40,500
So we're directly getting these values.

53
00:03:40,860 --> 00:03:46,800
Now, sometimes when you analyze a function, suppose you got the time complexity as F often as equals

54
00:03:46,800 --> 00:03:50,280
two sigma, i.e. it takes values from one to 10.

55
00:03:50,460 --> 00:03:51,390
How much versus.

56
00:03:52,870 --> 00:03:56,080
How much does this is part of an.

57
00:03:58,110 --> 00:04:01,200
So you should know mathematical form of dysfunction.

58
00:04:01,530 --> 00:04:04,930
You should know how to simplify and get a single value for this one.

59
00:04:05,250 --> 00:04:17,550
So if you expand this, it will be up to outdraw and Nassau's it is FFM into Sigma I intu too.

60
00:04:17,589 --> 00:04:20,910
But I'm IDEX values from one to 10.

61
00:04:21,750 --> 00:04:22,720
How to solve this.

62
00:04:22,980 --> 00:04:28,410
So if you know the mathematical expansion of the sigma then this is nothing but one plus two plus three

63
00:04:28,410 --> 00:04:39,630
plus goes on to and this is what any one by two then how much this is and square or and square does

64
00:04:39,720 --> 00:04:40,780
of time function.

65
00:04:41,460 --> 00:04:46,890
Now suppose the time function is like this sigmoid x values from one to end.

66
00:04:47,280 --> 00:04:48,450
I interviewed two.

67
00:04:50,580 --> 00:04:52,840
Expand this and get a single formula.

68
00:04:53,790 --> 00:04:54,900
This is difficult to get.

69
00:04:55,110 --> 00:04:56,720
We can't get the exact formula.

70
00:04:57,030 --> 00:04:59,660
We have to get the approximate formula.

71
00:04:59,670 --> 00:05:02,520
So by solving this one, we get approximate.

72
00:05:02,850 --> 00:05:10,260
So this is enough for me to explain to you that if you are getting a time function in some form, which

73
00:05:10,260 --> 00:05:16,230
cannot be simplified in terms of and directly, then you cannot give the time, complexity.

74
00:05:17,210 --> 00:05:25,280
You cannot mention the time if you cannot get in the form polynomial and exactly if you are getting

75
00:05:25,280 --> 00:05:33,580
approximate, then if that approximation is lower value or a higher value.

76
00:05:34,430 --> 00:05:41,300
So the result of a function in the form of a polynomial is that the polynomial, lower value or a higher

77
00:05:41,300 --> 00:05:42,460
value is not exact.

78
00:05:42,800 --> 00:05:44,070
So which one you are taking?

79
00:05:44,480 --> 00:05:52,640
So if you are taking lower value, then you can say, oh my God, if you are taking a value, then you

80
00:05:52,640 --> 00:05:54,130
can say Begal.

81
00:05:55,370 --> 00:05:59,360
If you are taking exact value then we see to.

82
00:06:01,600 --> 00:06:02,750
These are the notations.

83
00:06:04,300 --> 00:06:14,140
These notations are lower bound, upper bound, these notations are lower bound, upper bound and tight

84
00:06:14,140 --> 00:06:14,530
bond.

85
00:06:14,920 --> 00:06:16,960
So lower bound is.

86
00:06:21,800 --> 00:06:24,020
God, he got a bounders.

87
00:06:27,070 --> 00:06:31,210
Wiggle tight bond this.

88
00:06:34,600 --> 00:06:40,420
So we have these three notations, Hughes, these are useful in the situations when you cannot get the

89
00:06:40,420 --> 00:06:46,790
exact polynomial for our formula for a function, then you can go for Biegel.

90
00:06:47,050 --> 00:06:53,470
Now, if you are not interested in analyzing this one at the malls, this will be an to power.

91
00:06:53,470 --> 00:06:55,060
And yes.

92
00:06:56,330 --> 00:07:05,170
You can see that is dwarfed and the bottom upper bound, big innovation and omega, so ignoring this,

93
00:07:05,180 --> 00:07:09,460
it may be too poor or too boring or may not.

94
00:07:09,500 --> 00:07:15,360
So you are talking about so when we prefer when we go for far lower one, then you cannot.

95
00:07:16,220 --> 00:07:22,790
But if you say no, I can solve this by using integration and limits on integration, by using integration

96
00:07:22,790 --> 00:07:25,520
by parts, I can get the exact value.

97
00:07:25,520 --> 00:07:32,060
If you see that, if you say that, then you can go for some of the -- and in order to board.

98
00:07:33,320 --> 00:07:35,540
So this is your exact function.

99
00:07:36,560 --> 00:07:39,350
So if you have exact function, you use theta.

100
00:07:40,040 --> 00:07:44,560
If you don't know, if you are not sure and you are taking upper bound sebago.

101
00:07:45,200 --> 00:07:52,400
So mostly we use Begal to say that at Maust C, whenever you are talking about any expenses of the time,

102
00:07:52,730 --> 00:07:53,660
we say at most.

103
00:07:53,670 --> 00:07:54,230
This much.

104
00:07:54,290 --> 00:07:55,190
At most this much.

105
00:07:56,200 --> 00:08:01,000
So we want to know the idea that this is a polemic, so what is the upper bound if you know the upper

106
00:08:01,000 --> 00:08:02,470
bound and we can do that work?

107
00:08:03,160 --> 00:08:04,870
First of all, I want to buy a mobile phone.

108
00:08:05,020 --> 00:08:12,460
If you say 2000 Indian rupees, just minimum amount, one laghmani, one lakh Indian rupees is the maximum

109
00:08:12,460 --> 00:08:12,850
amount.

110
00:08:13,930 --> 00:08:18,380
So when you want to know how much is the cost of any mobile phone, which one you are interested in

111
00:08:18,380 --> 00:08:22,120
knowing at most, at most not beyond one leg.

112
00:08:22,150 --> 00:08:23,010
OK, fine.

113
00:08:24,720 --> 00:08:30,070
So unless you can get it most of the time, we prefer upper bound, right?

114
00:08:30,300 --> 00:08:32,559
So that's why we go for big innovation.

115
00:08:33,490 --> 00:08:33,650
Right.

116
00:08:33,929 --> 00:08:36,169
So I was not using any of these notations.

117
00:08:36,179 --> 00:08:43,380
I will think of some more industrial notation, why I was not interested in innovation in all the examples

118
00:08:43,380 --> 00:08:45,630
we have seen in all the topics.

119
00:08:45,900 --> 00:08:53,190
The reason I was always getting tight bond, I was getting the equation, the formula exact formulated

120
00:08:53,190 --> 00:08:54,320
was not approximate.

121
00:08:54,970 --> 00:08:56,030
It is not approximate.

122
00:08:56,250 --> 00:09:01,230
So really, you come across a situation where you need approximate in algorithms.

123
00:09:01,230 --> 00:09:02,520
It's not a common situation.

124
00:09:02,520 --> 00:09:05,500
That analysis will give you approximate results.

125
00:09:05,610 --> 00:09:11,400
Know most of the time you get exact results that you can go for take long.

126
00:09:11,430 --> 00:09:13,650
So when I was saying outdraw, that was to.

127
00:09:15,850 --> 00:09:21,850
But on the safe side, mostly we biegel so in the textbooks, everywhere you find it, is Biegel the

128
00:09:21,850 --> 00:09:30,940
reason of using Begal Ultra you see if suppose a function f often as big of an as well as function f

129
00:09:30,940 --> 00:09:33,070
often is omcg off.

130
00:09:33,070 --> 00:09:36,930
And then we say that for fairness teed off.

131
00:09:36,940 --> 00:09:47,980
And so it means this big of an s not just apparently equal or droppable or maybe less equal or lower,

132
00:09:47,980 --> 00:09:54,550
but when you got to you can also use Begal because Bigo represents equal or upper.

133
00:09:55,460 --> 00:09:57,890
So that's why Bigo is commonly used.

134
00:09:58,250 --> 00:10:04,250
So now you can convert all of them all the time complexes that we got in the form of Titta or Biegel,

135
00:10:04,250 --> 00:10:06,710
that if upon your requirement, all you needed.

136
00:10:07,880 --> 00:10:08,830
And one last thing.

137
00:10:09,170 --> 00:10:11,720
See, these are the long time complexities.

138
00:10:11,720 --> 00:10:13,940
We understand how the algorithm may be working.

139
00:10:14,510 --> 00:10:20,120
If you got a formula, analytical result in some other form, which you cannot produce in this form,

140
00:10:20,300 --> 00:10:25,070
then you will select any one of these that is upper bound or lower.

141
00:10:25,080 --> 00:10:30,270
But if it can be shown like this, then you can go for TIBONI.

142
00:10:32,750 --> 00:10:35,780
So that's all about asymptotic mortician's.

