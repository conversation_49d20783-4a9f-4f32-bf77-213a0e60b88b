1
00:00:00,770 --> 00:00:07,730
Our next topic is quicksort, those are mostly discussed one of the most famous sorting technique,

2
00:00:08,090 --> 00:00:13,480
and sometimes it is believed to be a difficult one, but actually it's very easy and very interesting.

3
00:00:13,850 --> 00:00:20,180
So as in the previous sorting techniques, I was explaining the basic idea behind the sorting method

4
00:00:20,660 --> 00:00:21,010
here.

5
00:00:21,020 --> 00:00:25,400
Also, we will understand what is the basic concept behind this technique.

6
00:00:26,720 --> 00:00:33,230
So let us understand the basic idea upon which it works so far, explaining the basic idea.

7
00:00:33,240 --> 00:00:35,330
I have a few examples here.

8
00:00:35,530 --> 00:00:38,650
I have a few questions for you in the first list.

9
00:00:39,380 --> 00:00:44,030
Find out which element is already in a solid position, which is already sorted.

10
00:00:44,600 --> 00:00:46,790
This I want one element in a single glance.

11
00:00:46,790 --> 00:00:47,840
You tell me, dancer.

12
00:00:49,790 --> 00:00:52,850
Yes, you are right, <PERSON>, on the desertec.

13
00:00:54,010 --> 00:00:57,630
The rest of the elements are not may or may not be sorted, but then you got it.

14
00:00:58,530 --> 00:01:04,410
I don't bother about other elements and it does not have go to the second list and a single glance.

15
00:01:04,410 --> 00:01:07,130
Just check it and tell me which element is all.

16
00:01:07,140 --> 00:01:08,730
It is hard to just say one one element.

17
00:01:10,480 --> 00:01:11,650
Yes, you are right.

18
00:01:12,280 --> 00:01:14,990
So it is 1990s, all the resort.

19
00:01:16,170 --> 00:01:18,330
Not in this list in a single glance.

20
00:01:18,360 --> 00:01:20,910
Find out which element is all that exotic.

21
00:01:22,760 --> 00:01:28,070
Is 50, is already Sagat, maybe other elements are also there.

22
00:01:28,100 --> 00:01:28,960
I don't want that.

23
00:01:29,390 --> 00:01:33,020
I'm looking at 50 right now.

24
00:01:33,020 --> 00:01:33,890
On what basis?

25
00:01:33,900 --> 00:01:37,030
I'm saying that 50 something like this was correct.

26
00:01:37,040 --> 00:01:41,030
This is smaller sentence in the beginning, and that is larger cities have done so.

27
00:01:41,030 --> 00:01:42,750
They are smarter on what basis?

28
00:01:42,750 --> 00:01:51,110
They can see 50 sorted because 50 years at the position where all the elements of before 50 are smaller

29
00:01:51,110 --> 00:01:56,320
than 50 and all the elements after 50 are greater than 50.

30
00:01:56,780 --> 00:02:00,290
So that's all 50 is in a solid position.

31
00:02:00,680 --> 00:02:05,240
If I saw this list, 50 will be a fourth element in the list.

32
00:02:05,420 --> 00:02:06,870
Definitely it will be for.

33
00:02:09,759 --> 00:02:14,230
Because there is no other element of the smaller than 50 that will come on the side, there is not another

34
00:02:14,230 --> 00:02:18,110
element that 50 which will come on that side 50 minutes in its own place.

35
00:02:18,460 --> 00:02:21,900
So 50 is already in its position.

36
00:02:21,970 --> 00:02:23,920
So let me repeat the idea.

37
00:02:24,520 --> 00:02:29,500
Quicksort works on the idea that an element is in a solid position.

38
00:02:29,920 --> 00:02:37,780
If all the elements before that element are smaller, all the elements after that element are greater,

39
00:02:37,960 --> 00:02:41,140
then we say that element is in position.

40
00:02:41,680 --> 00:02:42,910
So that is the idea.

41
00:02:43,600 --> 00:02:48,310
Now, I have one example to give you a clear picture of quicksort.

42
00:02:48,880 --> 00:02:52,150
See, these are students and the heights are different.

43
00:02:52,480 --> 00:02:57,760
So the teacher wants them to stand in increasing order of their height.

44
00:02:58,800 --> 00:03:04,620
So teacher has two options, teacher can show the position of each and every student.

45
00:03:06,970 --> 00:03:12,850
You can decide who should come first, who should come next behind that next behind and teacher can

46
00:03:13,180 --> 00:03:17,610
all the students to form a line in the increasing order of their height.

47
00:03:17,980 --> 00:03:19,260
So teacher is swatting them.

48
00:03:19,690 --> 00:03:25,260
Second option is a teacher can ask the student to arrange by themselves.

49
00:03:26,500 --> 00:03:30,400
So you tell me which one will be faster or which one will be quick.

50
00:03:31,640 --> 00:03:33,680
Teachers showing the positions and supporting them.

51
00:03:34,760 --> 00:03:37,580
Are the students finding their own places?

52
00:03:38,600 --> 00:03:42,670
Yes, definitely student finding their own places is quick.

53
00:03:43,690 --> 00:03:49,660
Maybe you have heard a statement from one of your teacher that students quickly form a line in increasing

54
00:03:49,660 --> 00:03:50,500
order of your height.

55
00:03:52,340 --> 00:03:58,610
So that is quick, one more thing I want to go out here quick here quick so it doesn't mean this is

56
00:03:58,610 --> 00:03:59,360
the fastest.

57
00:04:00,320 --> 00:04:02,150
No, this is not the fastest.

58
00:04:02,150 --> 00:04:06,500
Sought after analysis will understand whether it is fastest or not.

59
00:04:06,860 --> 00:04:09,680
Now, let us see how students will arrange themselves.

60
00:04:09,890 --> 00:04:11,240
Some of these other students.

61
00:04:11,240 --> 00:04:13,100
And this is yourself, right?

62
00:04:13,130 --> 00:04:20,990
You have to find a place where you should be you should go and stand in line such that all those students

63
00:04:20,990 --> 00:04:26,300
who are shorter than you should be on the left hand side and those who are taller should be on the right

64
00:04:26,300 --> 00:04:29,690
hand side, you will try to find a place that's home.

65
00:04:30,790 --> 00:04:39,140
So you are supposed to start in your sodded position so far that what you do, you check that, you

66
00:04:39,160 --> 00:04:42,640
check with other students and you tell him that you are taller than me.

67
00:04:42,820 --> 00:04:46,240
If you go out the back and you watch this, then you are shorter than me.

68
00:04:46,250 --> 00:04:50,260
Why don't you come and stand in front and you watch this one until you are shorter than me.

69
00:04:50,260 --> 00:04:53,950
Come and stand for you are the stronger person to go and stand at the back.

70
00:04:54,130 --> 00:04:57,780
So in this way, you try to arrange other student to find your own place.

71
00:04:59,620 --> 00:05:00,280
Yes.

72
00:05:02,000 --> 00:05:07,100
That's what the approach of quicksort not extend this example a little bit to give you the complete

73
00:05:07,100 --> 00:05:10,700
idea of quicksort, suppose you are supposed to stand in this line.

74
00:05:10,700 --> 00:05:12,320
So you are pivot element.

75
00:05:13,860 --> 00:05:19,440
Then some decide you have to check if anybody is taller than you should go on that site and from that

76
00:05:19,440 --> 00:05:21,640
side you will check anybody is shorter than you.

77
00:05:21,660 --> 00:05:22,770
He should come on the site.

78
00:05:23,010 --> 00:05:28,800
So you will take I and G here using my you will find out anybody taller is he is taller using you.

79
00:05:28,800 --> 00:05:30,250
You will find out anybody shorter.

80
00:05:30,270 --> 00:05:30,480
Yes.

81
00:05:30,480 --> 00:05:36,960
He shorter asked them to interchange their places so that again can move to the next place, then move

82
00:05:37,170 --> 00:05:38,480
to the next place and check.

83
00:05:38,760 --> 00:05:41,490
Is he taller than you know.

84
00:05:41,490 --> 00:05:42,840
Is he taller than you use.

85
00:05:43,080 --> 00:05:50,640
Is he shorter than you ask them to change their places in this way from both the sides from and you

86
00:05:50,640 --> 00:05:57,020
will check if anybody taller, anybody shorter, all of them to change their places so that at one point

87
00:05:57,030 --> 00:05:59,400
you can say that this is my position.

88
00:06:01,500 --> 00:06:06,450
How do you confirm you are in this position, suppose you are standing in the queue in a line and saying

89
00:06:06,450 --> 00:06:12,120
that I'm standing in my proper position because all the people in front of you are shorter, all the

90
00:06:12,120 --> 00:06:15,540
people at the back are taller, whether they are shorter or not.

91
00:06:15,660 --> 00:06:16,860
That is not my business.

92
00:06:16,870 --> 00:06:19,440
These are solid or not is not my business.

93
00:06:19,770 --> 00:06:21,710
I am in my position.

94
00:06:22,260 --> 00:06:22,740
That's all.

95
00:06:22,740 --> 00:06:26,400
You will find your position as a student standing in the line.

96
00:06:26,700 --> 00:06:27,980
That's all this idea.

97
00:06:28,440 --> 00:06:32,580
Next, I will take some example and I will sort the elements.

98
00:06:32,580 --> 00:06:35,550
And also I will show you the algorithm for quicksort.

