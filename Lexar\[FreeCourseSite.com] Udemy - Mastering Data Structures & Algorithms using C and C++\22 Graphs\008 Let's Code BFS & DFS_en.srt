1
00:00:00,580 --> 00:00:06,520
And this video, we will develop functions for Grauwe private cells, that is for search and.

2
00:00:07,600 --> 00:00:14,320
Depth first search, I have an example graph, I will show that graph, see, this is a graph, a simple

3
00:00:14,320 --> 00:00:14,620
graph.

4
00:00:14,620 --> 00:00:21,760
I have taken that six vertices and fewer just only performing for search.

5
00:00:21,760 --> 00:00:23,350
And the first search on this graph.

6
00:00:24,670 --> 00:00:25,930
And I have already prepared.

7
00:00:26,500 --> 00:00:28,600
I just want, cymatics, for this graph.

8
00:00:30,750 --> 00:00:39,540
This is not just cymatics do I have only six words since I have taken off size four by four because

9
00:00:40,020 --> 00:00:42,450
there is no word index zettl.

10
00:00:43,230 --> 00:00:51,060
See, I'm using this portion of C, I'm using only this portion of Two-dimensional Uhry.

11
00:00:51,450 --> 00:00:54,170
I'm not using zero two and zero column.

12
00:00:54,180 --> 00:00:57,420
So you can observe that zero through and zero column.

13
00:00:58,200 --> 00:00:59,850
They all have value zeros.

14
00:01:00,480 --> 00:01:04,390
So as when I did waterwork, I have started using what is just from one on word.

15
00:01:04,410 --> 00:01:06,870
So that is the reason I'm skipping this.

16
00:01:07,940 --> 00:01:16,970
What Zettl Girl six words says, I have taken matters so far to 77 then for performing before such I

17
00:01:16,970 --> 00:01:18,520
required a cue that I structure.

18
00:01:18,530 --> 00:01:21,170
So I have already taken a header file for you.

19
00:01:21,650 --> 00:01:24,770
And this is using Linda list.

20
00:01:26,800 --> 00:01:31,570
Already I have the functions like NQ Dequeue as well as SMB functions.

21
00:01:31,750 --> 00:01:34,660
These are useful in performing, but for such.

22
00:01:36,790 --> 00:01:40,000
Now, let us write a function for bed for such.

23
00:01:42,950 --> 00:01:48,920
Void that for search, it should take a matrix that is G.

24
00:01:50,460 --> 00:01:56,730
First, dimensions are not required, second dimensions I have to pass, that is seven, and what is

25
00:01:56,730 --> 00:02:02,070
the starting point and what are the dimensions of a matrix that is seven by seven?

26
00:02:05,460 --> 00:02:10,410
Now, inside this, what all the variables I will be declaring them whenever I use.

27
00:02:10,440 --> 00:02:14,920
So, first of all, I need a variable that is for a start of a.

28
00:02:16,140 --> 00:02:16,830
Traversal.

29
00:02:17,870 --> 00:02:23,090
Then I need a cue, so I will say you I will declare it to.

30
00:02:27,170 --> 00:02:31,010
And here on the top, I should include Gilbert EJ.

31
00:02:32,870 --> 00:02:33,920
That is the harder fight.

32
00:02:35,740 --> 00:02:39,490
Then here, I need an update that is visited urte.

33
00:02:42,460 --> 00:02:47,620
That is visited a study of size six seven.

34
00:02:48,930 --> 00:02:53,640
And this should be initialized with the zero minutes none of the vertex has so far visited.

35
00:02:55,020 --> 00:03:01,140
Then the first thing I should do is I should visit the starting word exile into that starting vortex,

36
00:03:01,190 --> 00:03:01,770
I have begun.

37
00:03:01,770 --> 00:03:10,160
And then I then also I should not visit that of I as one show that decided this already visited.

38
00:03:12,450 --> 00:03:14,010
And I should thank you.

39
00:03:15,040 --> 00:03:15,520
I.

40
00:03:17,040 --> 00:03:25,740
So I think, you know, I should then I should perform a repeating tasks by picking one more excited

41
00:03:25,740 --> 00:03:26,900
time and then exploring it.

42
00:03:26,910 --> 00:03:30,930
So for that loop, while not, is empty.

43
00:03:33,680 --> 00:03:34,070
Q.

44
00:03:35,560 --> 00:03:36,970
So I don't have the barometer.

45
00:03:37,030 --> 00:03:38,170
Q Does A.

46
00:03:39,390 --> 00:03:43,410
I don't have the parameter Q Because there's a dual using Lindqvist.

47
00:03:45,100 --> 00:03:51,010
Now, each time I will take out the verdicts from a so say dequeue.

48
00:03:52,360 --> 00:03:53,260
Look at the verdicts.

49
00:03:55,310 --> 00:04:01,850
And I should start exploring it, so for exploration, I will take one more variable that is G that

50
00:04:01,850 --> 00:04:06,230
should start from one and as long as GS less than.

51
00:04:07,810 --> 00:04:14,470
Seven, that's less than an Anji Plus plus, and it's the dimensions.

52
00:04:17,110 --> 00:04:22,610
Then every time I should check if there is an edge and what if this was a threat or not.

53
00:04:22,990 --> 00:04:36,730
So if I got my G is equal to one that has an edge and visited of G as a still zero means it is not yet

54
00:04:36,730 --> 00:04:41,500
visited, then I will pretend that I'm sure that it is visited.

55
00:04:42,500 --> 00:04:43,820
So I should bring Jay.

56
00:04:45,050 --> 00:04:55,550
And I should mark the start of G as one, because it's already visited now and also I should NQ G.

57
00:04:56,800 --> 00:04:59,590
And here I should declare one more variable, that is Jane.

58
00:05:01,310 --> 00:05:09,290
So this is a bit first search function now up on this graph, let us call that first search for a main

59
00:05:09,290 --> 00:05:10,240
function here.

60
00:05:10,250 --> 00:05:18,310
I will call that first search and I should pass G as a parameter that is a graph to dimensionality and

61
00:05:18,320 --> 00:05:23,690
the starting X, I will give it as one then the number of what the SA seven that is the dimensions are

62
00:05:23,690 --> 00:05:26,630
seven actually number of forces are six.

63
00:05:26,870 --> 00:05:27,950
Dimensions have seven.

64
00:05:29,010 --> 00:05:30,060
Let us run this.

65
00:05:31,770 --> 00:05:34,860
Oh, there is an error, so there I have gone wrong.

66
00:05:35,220 --> 00:05:35,670
OK.

67
00:05:37,160 --> 00:05:39,480
Structure is incompatible type.

68
00:05:40,340 --> 00:05:41,030
Let us check.

69
00:05:43,660 --> 00:05:49,060
Oh, yes, they are using a cue using language so we don't have to declare or create anything.

70
00:05:49,810 --> 00:05:52,120
This is not a using uhry.

71
00:05:54,310 --> 00:06:00,370
Directly, the school classes having been sent under control, the redeclared because for implementing

72
00:06:00,370 --> 00:06:07,360
a coup using linguist, we need just front and rear pointers so I can directly start using the cue in

73
00:06:07,360 --> 00:06:11,020
the program so I don't have to declare anything, said Solidere.

74
00:06:11,900 --> 00:06:13,190
So let us run it now.

75
00:06:15,710 --> 00:06:22,130
Yes, it is successful, let us see the results, see bid for search, I got the result as one, two,

76
00:06:22,130 --> 00:06:23,420
three, four, five, six.

77
00:06:24,800 --> 00:06:26,570
I will change the starting vortex.

78
00:06:28,570 --> 00:06:33,060
Starting Vertex was one, so I will say starting what is it for, let us see what happens now.

79
00:06:34,320 --> 00:06:40,680
So far, four, two, three, five, six, then afterwards, one, how we got this, let us look at

80
00:06:40,680 --> 00:06:43,860
the graph, see the graph, and we are starting from four.

81
00:06:48,160 --> 00:06:55,810
And Ford is exploring the just what is this are two and three and four, five and six, four, four,

82
00:06:55,810 --> 00:06:58,650
two, three, five, six, then afterwards we can reach one.

83
00:06:59,260 --> 00:07:00,520
So, yes, it is correct.

84
00:07:01,060 --> 00:07:06,700
Not if I give those starting what is a five, then I should get the answer like four, then two, three,

85
00:07:06,700 --> 00:07:07,660
six and one.

86
00:07:08,790 --> 00:07:15,270
But it should go on for more for four additional words are two, three six, then one zero five, four,

87
00:07:15,510 --> 00:07:16,470
three, six and one.

88
00:07:17,070 --> 00:07:18,570
Let us strive towards five.

89
00:07:21,880 --> 00:07:25,510
Yes, five, four, two, three, six and one, just perfect.

90
00:07:26,760 --> 00:07:31,830
If I give the starting with at three, then four, three, it should be one for.

91
00:07:33,270 --> 00:07:39,630
Then two, five, six, if I change it to three, one, four, two.

92
00:07:41,730 --> 00:07:42,310
It's like.

93
00:07:43,770 --> 00:07:47,710
Three, one, four, two, five, six, yes, perfect.

94
00:07:47,940 --> 00:07:50,110
So our breakfast is working perfectly.

95
00:07:50,580 --> 00:07:53,880
Now let us write a function for up to four such.

96
00:07:57,010 --> 00:08:02,670
Deptford, such as a recursive function, it should take a graph as a barometer.

97
00:08:03,230 --> 00:08:11,120
So the first parameter is empty and the second one is seven, then I should give the starting vertex

98
00:08:11,750 --> 00:08:12,590
and also.

99
00:08:14,650 --> 00:08:15,700
The dimensions.

100
00:08:19,140 --> 00:08:26,610
Inside, this will require a visitor study, so I will declare it as a static type, so that is accessible

101
00:08:26,610 --> 00:08:27,670
in every call.

102
00:08:28,200 --> 00:08:32,840
So this is static size at seven and I will make it as Zettl.

103
00:08:33,570 --> 00:08:34,500
So there's a static.

104
00:08:36,559 --> 00:08:38,150
Then every time we check that.

105
00:08:40,490 --> 00:08:47,110
If that starting vortex is visited or not, if visited of starting blocks.

106
00:08:49,840 --> 00:08:53,260
Is equal to zero, it is not yet visited then.

107
00:08:54,300 --> 00:09:02,630
We will check if there is a then we will bring that one in that vortex, that saying that it is visited.

108
00:09:02,910 --> 00:09:05,820
So I'm giving them a startlingly.

109
00:09:07,180 --> 00:09:07,570
Then.

110
00:09:09,950 --> 00:09:16,190
We have to start exploring the Sodexho for that, we will take a variable Gyges charge from one onwards

111
00:09:16,190 --> 00:09:20,150
and she is less than N and C++.

112
00:09:21,340 --> 00:09:25,030
And every time we check that if there is an edge from.

113
00:09:26,210 --> 00:09:29,810
I that is starting with to G.

114
00:09:31,090 --> 00:09:39,100
If this is equal to one and also check that if it is not visited, visited as is still equal to zero,

115
00:09:39,400 --> 00:09:47,130
and then we call the first surge repulsively by passing URRY and she will be the starting vertex and

116
00:09:47,140 --> 00:09:47,950
a number of.

117
00:09:48,820 --> 00:09:52,540
What is this hour, and that is the dimensions of a mattocks and.

118
00:09:54,840 --> 00:10:00,420
That's all so it's a very simple program for the for the first search, because it is recursive.

119
00:10:02,150 --> 00:10:08,150
Let us call up for such instant before, so she will call the first search and we will call it upon

120
00:10:08,150 --> 00:10:10,250
first word, starting with this one.

121
00:10:14,950 --> 00:10:18,680
Oops, I did not declare a variable change, so I will declare a variable J.

122
00:10:19,570 --> 00:10:20,700
So I got another.

123
00:10:20,710 --> 00:10:26,020
You can see that C I'm showing you the errors so that if you type and you create any errors, you learn

124
00:10:26,020 --> 00:10:27,220
how to remove the errors.

125
00:10:29,120 --> 00:10:32,990
If it is successful or it is printing so many values.

126
00:10:34,190 --> 00:10:40,920
So it has gone into infinite calling, so let us see what is the problem where we have gone wrong.

127
00:10:41,630 --> 00:10:43,140
So it's not stopping at all.

128
00:10:44,000 --> 00:10:49,910
One thing we did is after printing this one, after visiting a vortex, we should marketers visit that.

129
00:10:50,480 --> 00:10:52,290
We have not Magnum's visited.

130
00:10:52,310 --> 00:10:57,600
That is the reason they are being explored again and again, should be made as one.

131
00:10:57,980 --> 00:10:59,360
So this part, I missed it.

132
00:10:59,360 --> 00:11:01,160
So now it is marked as one.

133
00:11:01,160 --> 00:11:03,510
So it will not be explored once again.

134
00:11:03,620 --> 00:11:04,760
So let us run it now.

135
00:11:06,910 --> 00:11:11,320
Yes, the result is one, two, four, three, five, six.

136
00:11:12,020 --> 00:11:12,640
Have you got the.

137
00:11:13,600 --> 00:11:21,130
Let us check it starting with X was one one two four, then form four, three and five and six are a

138
00:11:21,150 --> 00:11:22,240
distance of three.

139
00:11:22,240 --> 00:11:23,380
Then five, then six.

140
00:11:25,060 --> 00:11:30,190
We are visiting S. Remember, we are exploring them in the same order that is numerical order.

141
00:11:30,520 --> 00:11:35,390
So first trees with then five, then six, because three comes first for three, the smaller.

142
00:11:35,740 --> 00:11:39,910
So in the numerical order they are being listed, though, we can visit them in any order.

143
00:11:40,210 --> 00:11:42,580
So even we can visit them in random order.

144
00:11:42,580 --> 00:11:44,440
First five, then three, then six.

145
00:11:44,440 --> 00:11:45,520
So we can change the order.

146
00:11:46,330 --> 00:11:47,710
But we have to change the order.

147
00:11:48,610 --> 00:11:52,240
No, let us change the starting index starting next.

148
00:11:52,240 --> 00:11:53,320
Let us make it as four.

149
00:11:53,320 --> 00:12:01,020
So for four it will be food and one, then three, then five six four two one three, five, six.

150
00:12:01,330 --> 00:12:04,480
If I give the starting an excess for let us see what happens.

151
00:12:06,840 --> 00:12:14,670
Yes, four two one three five six percent, so that's all you can try this program and you can take

152
00:12:14,670 --> 00:12:19,260
your own graph and take some values you can trade for directed graphs also.

153
00:12:19,890 --> 00:12:22,440
So draw the graph first and prepare a matrix.

154
00:12:22,440 --> 00:12:25,110
Then I dysfunctions and try them.

