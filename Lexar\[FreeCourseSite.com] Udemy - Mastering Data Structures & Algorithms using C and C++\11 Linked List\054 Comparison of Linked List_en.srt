1
00:00:00,330 --> 00:00:06,000
And this video, let us compare various type of link lists that we have studied, so here are various

2
00:00:06,000 --> 00:00:06,740
types of links.

3
00:00:06,750 --> 00:00:09,330
We have seen this linear single increase.

4
00:00:09,930 --> 00:00:15,070
So every node is having just a single link, then circler single link list.

5
00:00:15,480 --> 00:00:20,680
So this is single link, but also the circular than the linear double-dealing list.

6
00:00:20,700 --> 00:00:23,170
So this is linear, but the two links are there.

7
00:00:23,460 --> 00:00:28,690
That is on the next less previous then this is circular of the interest.

8
00:00:29,160 --> 00:00:32,250
So this is a doubly as well as circular.

9
00:00:32,430 --> 00:00:35,300
Two links are there as well as we can access it circularly.

10
00:00:36,060 --> 00:00:42,390
So we'll compare them based on this criteria that Espace and the delete operation and Trivers.

11
00:00:42,390 --> 00:00:43,470
So we'll compare these.

12
00:00:43,480 --> 00:00:48,510
So first thing about space, which type of link mistakes, more space.

13
00:00:48,990 --> 00:00:52,680
See, this is for data and this is a link.

14
00:00:52,830 --> 00:00:58,540
So along with the data, it's also taking up space for storing the address of the next node.

15
00:00:58,920 --> 00:01:01,270
So here for nodes are there.

16
00:01:01,290 --> 00:01:07,980
So it means I can store four values along with values for pointers are also required, which will store

17
00:01:07,980 --> 00:01:09,170
that the source next node.

18
00:01:09,750 --> 00:01:16,070
So for four elements for extra point of four elements and extra pointers.

19
00:01:16,890 --> 00:01:17,790
What about this?

20
00:01:18,090 --> 00:01:20,070
Again, same anextra pointers.

21
00:01:20,400 --> 00:01:28,530
But these two links, if you see they require two pointers for each node, so to an extra space is required

22
00:01:28,620 --> 00:01:29,430
to enter.

23
00:01:29,460 --> 00:01:32,670
So now we should count this to double the space.

24
00:01:32,940 --> 00:01:39,960
So I can say that Double-dealing takes double the space of pointers required in Singler.

25
00:01:39,960 --> 00:01:43,770
Linguist Double four pointers only not for data.

26
00:01:44,730 --> 00:01:51,090
So this is about the space then let us talk about traversing versus traversing so we can move on, lean

27
00:01:51,090 --> 00:01:51,960
forward direction.

28
00:01:51,960 --> 00:01:55,110
We can move forward as well as a circular direction.

29
00:01:55,560 --> 00:01:57,140
We can move in either direction.

30
00:01:57,180 --> 00:02:02,790
This we can move in the other direction as well as a circular direction and thinks about insert and

31
00:02:02,790 --> 00:02:03,950
delete operations.

32
00:02:03,960 --> 00:02:04,770
We will discuss.

33
00:02:04,770 --> 00:02:06,070
We have already seen all of them.

34
00:02:06,660 --> 00:02:14,730
So if you insert oppression in all these linguists inserting before foster, before hired as a special

35
00:02:14,730 --> 00:02:18,630
case and inserting at any other place is a common case.

36
00:02:19,200 --> 00:02:26,670
Now, inserting before first in this link mistakes order of one time and here it takes order of N because

37
00:02:26,670 --> 00:02:32,810
we have to modify this link also and we have to traverse and inserting before this side takes order

38
00:02:32,820 --> 00:02:37,860
of one time Konstantine and inserting before this one takes constant time.

39
00:02:38,130 --> 00:02:39,690
Do we have to modify this one?

40
00:02:39,900 --> 00:02:41,460
But we don't have to traverse this.

41
00:02:41,460 --> 00:02:44,040
We can directly go from here and modify it.

42
00:02:44,250 --> 00:02:45,770
So it takes constant time here.

43
00:02:46,050 --> 00:02:51,150
So only in this linguists, that is singly linguists, if you're sitting on the left hand side, time

44
00:02:51,200 --> 00:02:52,180
is outdraw.

45
00:02:52,230 --> 00:02:58,080
And then how many links are modified when you are sitting on the left hand side here?

46
00:02:58,110 --> 00:03:00,300
Only one link that is a new Northville.

47
00:03:00,300 --> 00:03:05,730
By linking here to links, new node will be linking as well as of this node will be pointing on new

48
00:03:05,730 --> 00:03:06,120
node.

49
00:03:07,050 --> 00:03:09,540
Suppose there's a new note that should also point on this one.

50
00:03:09,560 --> 00:03:11,670
So two links are modified here.

51
00:03:11,850 --> 00:03:16,230
Three links are modified new nodes, the two links as well as this link.

52
00:03:17,040 --> 00:03:18,930
Then here, four links are modified.

53
00:03:18,960 --> 00:03:21,930
If you're sitting on the left hand side, that is before Heckmann.

54
00:03:22,260 --> 00:03:25,010
Next, let us talk about inserting at any other position.

55
00:03:25,470 --> 00:03:31,620
So inserting at any other position takes minimum constraints of time, maximum and time.

56
00:03:32,100 --> 00:03:39,030
And the procedure there and here is saying inserting here is constant in sitting at the last end.

57
00:03:39,300 --> 00:03:40,740
And here also seem.

58
00:03:42,220 --> 00:03:49,240
And here also seem in all these linguist's the time taken for insulting at any other politician as seen.

59
00:03:50,560 --> 00:03:51,700
But what about links?

60
00:03:52,150 --> 00:03:56,050
When you are inserting here, we have to modify Gollings if you are insulting.

61
00:03:56,080 --> 00:04:02,610
We have to modify two links and here we have to modify for and if we are inserting after last node,

62
00:04:02,620 --> 00:04:03,980
only three links are required.

63
00:04:04,720 --> 00:04:06,470
I just said that there is no node beyond.

64
00:04:06,490 --> 00:04:08,410
So you don't have to make a point on this one.

65
00:04:08,420 --> 00:04:13,520
Like if I'm inserting here, then they should also point if I'm inserting here, there is no note to

66
00:04:13,570 --> 00:04:14,420
point on this one.

67
00:04:14,770 --> 00:04:17,140
So only three links when you're sitting at the last.

68
00:04:17,709 --> 00:04:19,790
But here are always four links.

69
00:04:20,110 --> 00:04:27,250
So this is a in such let us talk briefly about delete because most of the things came under insert only

70
00:04:27,790 --> 00:04:35,220
delete again for deleting deleting node cases different and deleting any other node cases different.

71
00:04:35,640 --> 00:04:37,060
Let us deleting head node.

72
00:04:37,420 --> 00:04:41,230
If you're deleting this node then simply you have to move first and delete the node.

73
00:04:41,440 --> 00:04:45,130
No links are modified payments constant deleting this one.

74
00:04:46,150 --> 00:04:54,730
We have to make this last point on this one, so this change needs for changing this, you have to move

75
00:04:54,970 --> 00:04:56,510
here this afternoon.

76
00:04:57,370 --> 00:05:01,580
So here, deletion of this one is constant and here, deliciousness out of end.

77
00:05:01,900 --> 00:05:03,880
And here the mission again, constant.

78
00:05:04,750 --> 00:05:10,240
And here deleting this note, we have to move ahead and modify that note also so we can go from here

79
00:05:10,240 --> 00:05:14,810
and modify it first constant only in this link head north.

80
00:05:14,830 --> 00:05:16,600
The deletion is sticking out of time.

81
00:05:16,840 --> 00:05:22,630
Otherwise, in any of the linguists, if you're deleting head node time constant next, how many links

82
00:05:22,630 --> 00:05:24,550
are modified when you delete this?

83
00:05:24,550 --> 00:05:28,180
No links are modified just so you have to move fast pointer here.

84
00:05:28,330 --> 00:05:29,650
We have to modify the link.

85
00:05:30,070 --> 00:05:35,020
When you delete more than here, if you're deleting one link, you have to modify this.

86
00:05:35,020 --> 00:05:35,870
We have to make none.

87
00:05:37,030 --> 00:05:39,550
Then here we have to modify two links.

88
00:05:39,550 --> 00:05:45,370
That is, we have to make a decision point on this last and last point on this one, because this will

89
00:05:45,370 --> 00:05:46,090
be a new head.

90
00:05:46,540 --> 00:05:52,000
So things are modified when you're deleting head nexxus, deleting from any other position.

91
00:05:52,420 --> 00:05:57,310
So the time taken for the leading a particular node, the minimum time, as if you're deleting this

92
00:05:57,310 --> 00:06:02,160
node constant, if you're deleting any of the node, it depends on this location that is order.

93
00:06:03,400 --> 00:06:04,930
So here also same thing here.

94
00:06:04,930 --> 00:06:05,840
Also same thing here.

95
00:06:05,840 --> 00:06:12,490
Also in all, deleting a node from given position based outdraw ending minimum one maximum.

96
00:06:12,490 --> 00:06:18,130
And then what about pointis, if you delete a node, we have to modify this link.

97
00:06:19,240 --> 00:06:23,740
If you delete the node, we have to modify just one link for one link is modified when you're deleting

98
00:06:23,740 --> 00:06:25,750
it more than here.

99
00:06:26,050 --> 00:06:31,420
If you are deleting an this one, then we should make this point on this one at some point on this links

100
00:06:31,420 --> 00:06:33,210
some modified than here.

101
00:06:33,220 --> 00:06:34,400
Also, if you're deleting, No.

102
00:06:34,420 --> 00:06:35,560
Two links are modified.

103
00:06:35,940 --> 00:06:41,440
But in this link, if you're deleting last node, then you don't have any node on that site.

104
00:06:41,440 --> 00:06:44,770
So you don't have to modify one link for only one link is required.

105
00:06:45,070 --> 00:06:49,610
Minimum one link will be modified when you are deleting any node from any given position.

106
00:06:49,750 --> 00:06:52,840
Now, one more point I will discuss about deletion.

107
00:06:53,350 --> 00:06:56,800
If I am giving you a pointer already up on this note.

108
00:06:57,100 --> 00:07:02,140
On this, there is a point of pointing out how much time it will take for deleting this code.

109
00:07:02,740 --> 00:07:06,070
So for deleting that node, just I have to make it does not delete that node.

110
00:07:06,080 --> 00:07:07,830
So, yes, it is constraint.

111
00:07:08,170 --> 00:07:13,030
So if you already have a pointer, you don't have to bring it from here, then the time is constant.

112
00:07:14,330 --> 00:07:19,640
But what about if a point responding here and you have to believe the same note, how much time it takes

113
00:07:20,000 --> 00:07:27,080
seem normal that it should modify the sort of this I should come from the beginning again, so randomly

114
00:07:27,080 --> 00:07:31,950
on some Naude if a point that is given how much time it will take to delete that same note.

115
00:07:32,300 --> 00:07:34,370
So for that I should modify previous more.

116
00:07:34,370 --> 00:07:37,910
So the time is order and it depends on the position that you are giving.

117
00:07:39,090 --> 00:07:44,150
This is one thing now understandably linguist's if a point that is given here, how much time it will

118
00:07:44,150 --> 00:07:51,050
take to delete that same note so I can go to previous and make it point on next and go to next and make

119
00:07:51,050 --> 00:07:51,970
it point on PBS.

120
00:07:52,310 --> 00:07:56,350
So being on that same note, we can change the point and delete them.

121
00:07:56,360 --> 00:08:02,220
OK, so time is constant so that we can understand that here being on the same note, we can modify

122
00:08:02,240 --> 00:08:02,930
what the point is.

123
00:08:02,960 --> 00:08:07,670
But here being on the same note, you need to modify previous Northpoint not lasting.

124
00:08:09,840 --> 00:08:17,640
Richard Linklater says we should use it depends on your climate if you have to access data one by one

125
00:08:17,640 --> 00:08:23,850
and you don't have to come back to any data, then you can use this single integrase, for example,

126
00:08:23,850 --> 00:08:24,750
if you're implementing.

127
00:08:24,750 --> 00:08:27,600
Q So you'll be going to the next and next and next.

128
00:08:27,920 --> 00:08:32,490
So you're going to be accessing next node every time so you don't have to go back.

129
00:08:32,970 --> 00:08:38,580
So you can use singly linguist and and if you want to access circularly then you can use this.

130
00:08:38,789 --> 00:08:42,460
Then again, this is the benefit of this furniss bidirectional.

131
00:08:42,750 --> 00:08:44,350
This is bidirectional and circular.

132
00:08:44,940 --> 00:08:48,600
So this is a final link which is having all possible features.

133
00:08:48,840 --> 00:08:53,550
You can move in either direction by direction as well as you can move Sutcliff.

134
00:08:53,820 --> 00:08:55,990
So this is perfect linguist's.

135
00:08:56,040 --> 00:08:57,820
That is circular w linguist.

136
00:08:58,620 --> 00:09:07,230
So what I say that the best to Linklaters this one, but it takes extra space memory space for the pointis.

137
00:09:07,800 --> 00:09:13,350
If you're worried about the pointers and you think that that is taking a lot of space then go for.

138
00:09:13,830 --> 00:09:15,920
No, no I have to move in the direction.

139
00:09:15,960 --> 00:09:16,770
Come back again.

140
00:09:16,770 --> 00:09:20,260
Here I go in one circular then go for this one.

141
00:09:20,490 --> 00:09:26,430
Actually this is taking in and actually these two are taking the same space so there is no meaning in

142
00:09:26,430 --> 00:09:27,360
having a gesture.

143
00:09:27,360 --> 00:09:29,470
DoubleLine is not a circular.

144
00:09:29,720 --> 00:09:31,860
So if you have you can make it circular.

145
00:09:32,340 --> 00:09:37,280
So doublings circular Linklaters best so you can always use that.

146
00:09:37,740 --> 00:09:46,020
If I talk about languages like Java, it provides building classes for linguists and that linguist has

147
00:09:46,020 --> 00:09:46,550
this one.

148
00:09:46,860 --> 00:09:53,390
So Java does not have classes for all types of linguist, though C++ is still there on the forward list.

149
00:09:53,400 --> 00:09:56,400
That is English linguist and there is a list that is this one.

150
00:09:56,700 --> 00:09:59,840
But in Java there is only one language that is circular.

151
00:09:59,850 --> 00:10:00,690
W Linkous.

152
00:10:01,830 --> 00:10:08,790
Because nowadays the space is not an issue, so we can go for this one, if in any system, if you are

153
00:10:08,790 --> 00:10:17,460
making it for any device with a limited memory, then you can think of space and you can go for it and

154
00:10:17,910 --> 00:10:20,010
you can go for the signal list.

155
00:10:20,550 --> 00:10:26,880
Now, what I believe is that in academics, we learn about this to get the idea about what is linguist.

156
00:10:27,570 --> 00:10:28,530
Then you learn this too.

157
00:10:28,590 --> 00:10:30,350
OK, we can make circler also.

158
00:10:30,360 --> 00:10:36,990
We learn one extra feature, then we learn one more teacher and say, OK, it can be accessed by directionally

159
00:10:36,990 --> 00:10:37,410
also.

160
00:10:37,450 --> 00:10:44,580
OK, then finally we see this one that is circular also and bidirectional so that if you see this is

161
00:10:44,580 --> 00:10:45,470
a little confusing.

162
00:10:45,960 --> 00:10:46,650
So that's all.

163
00:10:46,650 --> 00:10:49,300
If after crossing all this, this becomes demis.

164
00:10:49,350 --> 00:10:51,410
So that's why academics are discovered like this.

165
00:10:51,420 --> 00:10:52,950
So we are used to this one.

166
00:10:53,430 --> 00:10:57,170
But actually based on the requirement, you can go for any of them.

167
00:10:57,570 --> 00:11:00,180
Now, if you're using language like Java, you don't have any option.

168
00:11:00,480 --> 00:11:01,470
You have to use the.

169
00:11:01,830 --> 00:11:06,520
So I say that if memory space is not a constraint, you can blindly go for this one.

170
00:11:06,720 --> 00:11:09,610
So that's all about comparisons of linguist.

