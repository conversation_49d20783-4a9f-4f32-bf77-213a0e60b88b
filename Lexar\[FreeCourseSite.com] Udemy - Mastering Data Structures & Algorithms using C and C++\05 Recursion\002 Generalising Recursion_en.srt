1
00:00:00,630 --> 00:00:04,780
Here I have a function recursive function that is calling itself.

2
00:00:05,100 --> 00:00:10,170
There are three statements, FUSA statement, there is something and then its calling itself and there

3
00:00:10,170 --> 00:00:11,080
is one more statement.

4
00:00:11,400 --> 00:00:18,300
So the question is when this statement will execute, this will execute at calling time and then this

5
00:00:18,310 --> 00:00:19,560
escapement will execute.

6
00:00:19,620 --> 00:00:22,790
It will execute at returning time.

7
00:00:25,100 --> 00:00:31,270
And if anything is there followed by this function, then that will also execute at returning time,

8
00:00:31,280 --> 00:00:34,860
only if any computations are there any more things are done.

9
00:00:35,930 --> 00:00:36,740
So that's it.

10
00:00:36,800 --> 00:00:41,900
So from this, like you can take it as a formula in the recursive function called before the function

11
00:00:41,900 --> 00:00:42,140
call.

12
00:00:42,140 --> 00:00:44,840
If anything is there, then that will be executed at calling time.

13
00:00:45,350 --> 00:00:48,120
After the function call it is executed at a turning point.

14
00:00:48,140 --> 00:00:57,260
So we can call this portion as ascending and this we can call it descending.

15
00:00:58,490 --> 00:01:03,390
So recursive functions have two phases that are ascending and descending.

16
00:01:04,430 --> 00:01:09,890
So the main important thing that we have learned now is that the recursion will have two phases.

17
00:01:11,090 --> 00:01:18,020
Let us compare recursion with loop loops are repeating statement, then what is recursion?

18
00:01:18,320 --> 00:01:23,350
Recursion is also repeating see a function gone, then I guess it is calling again.

19
00:01:23,360 --> 00:01:24,020
It is calling.

20
00:01:24,050 --> 00:01:25,240
So is it not repeating?

21
00:01:25,250 --> 00:01:25,610
Yes.

22
00:01:25,860 --> 00:01:28,040
Recursion is also just like repeating.

23
00:01:28,370 --> 00:01:36,230
But the difference is a loop will have only ascending phase, but the recursion will have ascending

24
00:01:36,230 --> 00:01:38,010
as well as descending phase.

25
00:01:38,360 --> 00:01:47,880
So that is the strength or power of recursion that you can utilize or exploit it for writing code easier.

26
00:01:48,590 --> 00:01:50,890
So these two phases are there.

27
00:01:50,900 --> 00:01:53,680
So the descending is there only in recursion.

28
00:01:54,260 --> 00:01:56,420
So that's all I end here in this video.

29
00:01:56,570 --> 00:02:02,930
Then the next video, I'll show you how stock is used in these two recursive functions and also I'll

30
00:02:02,930 --> 00:02:04,280
show you the time complexity.

31
00:02:04,500 --> 00:02:07,490
So we'll continue with the same functions in the next few days.

