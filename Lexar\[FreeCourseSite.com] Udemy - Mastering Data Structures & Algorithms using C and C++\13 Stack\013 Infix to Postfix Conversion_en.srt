1
00:00:00,330 --> 00:00:04,110
The topic of this video is infix, supposedly conversion.

2
00:00:04,620 --> 00:00:10,800
We will learn what is postfix and why we need more sex than what our precedences.

3
00:00:11,220 --> 00:00:18,780
Then we will learn how to convert infix expression to four six as well as prefect's MANUELITA that is

4
00:00:18,780 --> 00:00:19,920
using pen and paper.

5
00:00:20,610 --> 00:00:24,180
Then later we will see how to convert it using a procedure.

6
00:00:25,830 --> 00:00:30,930
There are three notations of writing an expression, the first one is infix.

7
00:00:31,170 --> 00:00:33,160
This is commonly used in mathematics.

8
00:00:33,180 --> 00:00:34,440
We followed this notation.

9
00:00:34,440 --> 00:00:37,390
So basically we know only one notation that is infix notation.

10
00:00:37,920 --> 00:00:38,610
What is it?

11
00:00:39,120 --> 00:00:42,720
First operand, then operator, then operand.

12
00:00:43,260 --> 00:00:49,110
If it is a binary operator like plus the binary operator which will add two opening.

13
00:00:49,130 --> 00:00:51,590
So operator will be in between two options.

14
00:00:53,040 --> 00:00:57,710
This is the common method that we use that is used in mathematics, but in computer sciences.

15
00:00:57,720 --> 00:01:06,510
Apart from this, we have discovered or introduced two more methods that is prefix and postfix.

16
00:01:06,810 --> 00:01:09,790
So let us see prefix prefix means operator.

17
00:01:09,810 --> 00:01:14,010
In short, I have written here then operand and an operator for example.

18
00:01:14,490 --> 00:01:17,750
That plus operation binary operation comes before to open.

19
00:01:17,760 --> 00:01:22,110
So first you will have operator, then that operation has to perform on Inbee.

20
00:01:23,950 --> 00:01:27,360
Then postfix open option operator.

21
00:01:28,550 --> 00:01:32,060
So first two options, then the binary opposition plus.

22
00:01:33,080 --> 00:01:41,660
This means A added, but B, this means add what and B, this means what, and B, do what would then

23
00:01:41,660 --> 00:01:42,400
be add.

24
00:01:42,800 --> 00:01:48,100
So this is a single operation I have taken with the two options or directly I have written them in the

25
00:01:48,110 --> 00:01:49,850
in six perfect six form.

26
00:01:50,270 --> 00:01:56,480
If I expression is having more number of operations than how it looks like and prefix and postfix that

27
00:01:56,480 --> 00:01:57,260
we have to learn.

28
00:01:58,430 --> 00:02:02,130
But before that we will see why do we need prefix or postfix.

29
00:02:02,240 --> 00:02:06,500
Basically we have been using infix then why this prefix.

30
00:02:06,500 --> 00:02:08,660
And let us see this now.

31
00:02:08,660 --> 00:02:10,910
Why we need postfix or prefix.

32
00:02:11,090 --> 00:02:11,950
Let us see this.

33
00:02:12,380 --> 00:02:17,260
See if I have given this expression to you and ask you to find the answer for this one.

34
00:02:17,810 --> 00:02:19,620
So you have to evaluate this.

35
00:02:19,690 --> 00:02:21,530
So for evaluation and what you will do.

36
00:02:22,310 --> 00:02:24,740
Will you add eight entry?

37
00:02:25,340 --> 00:02:25,700
No.

38
00:02:25,700 --> 00:02:28,100
You know what marks at all in mathematics?

39
00:02:28,820 --> 00:02:32,470
The higher precedences for brackets that the powers.

40
00:02:33,350 --> 00:02:35,320
I'm assuming that you know Boardman's rule.

41
00:02:35,510 --> 00:02:37,190
So let me evaluate this one.

42
00:02:37,520 --> 00:02:41,210
See, first I have to solve this one, so I'll go to bracket.

43
00:02:41,810 --> 00:02:47,540
So I have skipped a plus and multiplication then directly junkier, then I'll see you both.

44
00:02:47,600 --> 00:02:48,830
So I will with this one.

45
00:02:48,830 --> 00:02:51,920
So this is the first one and is the second one then.

46
00:02:51,920 --> 00:02:52,750
Anything more.

47
00:02:52,760 --> 00:02:53,150
Ngunnawal.

48
00:02:53,210 --> 00:02:55,430
So now multiplication divisions are leftovers.

49
00:02:55,730 --> 00:02:58,730
Aspelin in mathematics division is higher precedence.

50
00:02:58,760 --> 00:03:02,000
So I will perform this one third one then.

51
00:03:02,000 --> 00:03:04,250
This is division fourth one, then I'll come here.

52
00:03:04,370 --> 00:03:07,930
Fifty one then addition additional remaining.

53
00:03:07,940 --> 00:03:10,760
So this is the sixth one then.

54
00:03:10,760 --> 00:03:11,780
This is the seventh one.

55
00:03:13,770 --> 00:03:21,360
If you observe the order in which I have performed those operations, I just quickly, I will show you

56
00:03:21,360 --> 00:03:26,100
them for this once again here third and fourth and fifth and sixth and seventh.

57
00:03:26,400 --> 00:03:32,220
So I'm jumping here and there in the expression, I have to look into the expression and find out first

58
00:03:32,220 --> 00:03:33,510
which one I should perform.

59
00:03:33,810 --> 00:03:34,770
The next which one.

60
00:03:35,010 --> 00:03:35,670
Next, which one.

61
00:03:35,700 --> 00:03:41,210
So for finding out which one should be performed next, I have to scan this whole expression.

62
00:03:42,120 --> 00:03:42,770
Yes.

63
00:03:43,140 --> 00:03:47,160
So it means I have scanned this expression for multiple times.

64
00:03:49,110 --> 00:03:52,960
Otherwise, it's randomly moving here and there in the exhibition.

65
00:03:52,980 --> 00:03:59,340
So can we write a program which behaves like this, which will go jump directly here, then it will

66
00:03:59,340 --> 00:04:05,400
go farther than division than division, then multiplication so we can try to program this is a random

67
00:04:05,400 --> 00:04:06,830
behavior so we cannot do this.

68
00:04:06,840 --> 00:04:11,850
So if you write a program, then you have to make it search for highest precedence, finish brackets,

69
00:04:12,180 --> 00:04:15,300
then finish powers, then finish your divisions.

70
00:04:15,330 --> 00:04:19,660
So one by one, it has to scan the expression for each type of objects.

71
00:04:19,920 --> 00:04:21,209
So this is more time consuming.

72
00:04:21,750 --> 00:04:28,070
What we want is we will not scan this expression multiple times, just in one scan.

73
00:04:28,140 --> 00:04:29,280
We want the result.

74
00:04:30,360 --> 00:04:36,630
It's not possible in six form, it is not possible then is there any other form by which it is possible

75
00:04:36,810 --> 00:04:37,920
is postfix form?

76
00:04:37,920 --> 00:04:38,670
It is possible.

77
00:04:39,090 --> 00:04:43,860
So to give you the idea and to finish my discussion, I will write on the postfix form of this one,

78
00:04:44,190 --> 00:04:45,280
how I am getting it.

79
00:04:45,450 --> 00:04:46,570
Don't bother about that.

80
00:04:46,920 --> 00:04:49,920
So here I have a postfix form expression.

81
00:04:50,340 --> 00:04:51,680
Don't worry how I got it.

82
00:04:51,810 --> 00:04:52,500
We will learn.

83
00:04:53,130 --> 00:04:54,440
Now let me evaluate this.

84
00:04:54,450 --> 00:04:58,620
Based on the mathematics Boardman's rule, I have concluded laodicean.

85
00:04:59,780 --> 00:05:02,960
I'm evaluating this one eight operand ignored.

86
00:05:02,990 --> 00:05:06,500
Go ahead, operand, go ahead, name operand, go ahead.

87
00:05:06,500 --> 00:05:07,700
Six open and go ahead.

88
00:05:08,090 --> 00:05:10,400
Minus Parlophone minus first.

89
00:05:11,530 --> 00:05:17,050
So which operation is getting performed first minuses you see here also we have done the same thing,

90
00:05:17,300 --> 00:05:19,890
so this is far more toward the previous two operate.

91
00:05:20,200 --> 00:05:25,640
Assume that this is performed well, finished, then move ahead to square.

92
00:05:25,810 --> 00:05:27,540
So this is square one to only.

93
00:05:27,820 --> 00:05:28,840
So this is the second one.

94
00:05:28,840 --> 00:05:30,370
So you can see this was the second one.

95
00:05:30,370 --> 00:05:30,720
We did.

96
00:05:31,060 --> 00:05:33,940
So this is completed now and move ahead.

97
00:05:34,210 --> 00:05:34,780
Divide.

98
00:05:34,930 --> 00:05:36,640
Divide what the previous to values.

99
00:05:36,640 --> 00:05:42,010
What are those values, this underlying this one and this underlines look, these two should be divided.

100
00:05:42,160 --> 00:05:44,290
So you can see that third one when perform.

101
00:05:44,320 --> 00:05:45,940
This is divided by the slided.

102
00:05:46,180 --> 00:05:50,770
So the operators are arranging the order in which they are supposed to be evaluated there.

103
00:05:50,830 --> 00:05:52,060
They are arranged in this order.

104
00:05:52,240 --> 00:05:57,220
So that's why whenever we are coming across an operator, we are performing operation on previous two

105
00:05:57,220 --> 00:05:57,810
options.

106
00:05:58,630 --> 00:06:03,490
So if it is arranging for sixth form, it can be evaluated in just one single scan.

107
00:06:04,630 --> 00:06:10,720
So finally, I can say that the purpose of writing an expression in both sixth form is that we can scan

108
00:06:10,720 --> 00:06:13,630
the expression only once and perform all the operations.

109
00:06:14,110 --> 00:06:20,530
If it is an infix, it's not easy to evaluate it in one single scan, but how to write it in postfix?

110
00:06:20,560 --> 00:06:21,870
That's what we have to learn.

111
00:06:22,000 --> 00:06:24,830
So let us learn how to write it in here.

112
00:06:24,850 --> 00:06:26,320
I have a precedence table.

113
00:06:26,320 --> 00:06:28,360
I have not taken all type of operators.

114
00:06:28,360 --> 00:06:33,940
I have taken just a few operators, addition and subtraction, multiplication, division and also Brackett's.

115
00:06:34,820 --> 00:06:40,330
Addition, subtraction is having less precedence, and both addition and subtraction have seen presidents

116
00:06:40,330 --> 00:06:48,280
equal precedence in programming, in mathematics, minus having higher precedence than plus, but in

117
00:06:48,280 --> 00:06:50,770
programming, they both have equal precedence.

118
00:06:50,800 --> 00:06:52,870
Remember this same vein?

119
00:06:53,020 --> 00:06:58,480
Multiplication and division also have same precedents, but they have higher precedence than addition

120
00:06:58,480 --> 00:07:02,700
and subtraction then bracket as highest of all.

121
00:07:02,740 --> 00:07:04,300
So I have given the number three.

122
00:07:04,690 --> 00:07:13,000
Now here I have two expressions as an example here we will learn how to convert them from infix to prefix

123
00:07:13,000 --> 00:07:15,790
as well as specifics for conversion.

124
00:07:16,000 --> 00:07:23,980
The important thing remember this whenever you write an expression, actually you should make it fully

125
00:07:23,980 --> 00:07:25,240
parenthesized.

126
00:07:26,530 --> 00:07:34,360
Fully parenthesized, you cannot you should not read an expression without parentheses, make it as

127
00:07:34,360 --> 00:07:37,840
a first rule, is it fully parenthesized?

128
00:07:37,850 --> 00:07:40,470
There are no parentheses at all, no.

129
00:07:40,630 --> 00:07:43,750
This is a call for parentheses are not given.

130
00:07:44,830 --> 00:07:49,060
Actually, compiler needs fully parenthesized expression.

131
00:07:49,060 --> 00:07:50,740
Everything should be parenthesized.

132
00:07:51,070 --> 00:07:52,750
No operator should be left open.

133
00:07:54,070 --> 00:08:00,940
Then when they are not parenthesized, then the compiler will parenthesized it not physically, logically

134
00:08:01,240 --> 00:08:01,660
equal.

135
00:08:01,660 --> 00:08:02,920
Parenthesized it.

136
00:08:03,640 --> 00:08:04,150
How?

137
00:08:04,540 --> 00:08:06,190
By using precedences.

138
00:08:07,580 --> 00:08:14,390
This is a very important concept, precedences doesn't mean order of execution, I suppose, in this

139
00:08:14,390 --> 00:08:17,440
one multiplications their highest precedence.

140
00:08:17,750 --> 00:08:23,490
It doesn't mean first multiply, nor what operation will be performed.

141
00:08:23,670 --> 00:08:30,350
It's a different thing, but that is having higher precedence means first, put that one inside the

142
00:08:30,350 --> 00:08:36,020
bucket for parenthesized that one, then you Barondess as additions and subtractions.

143
00:08:36,500 --> 00:08:38,179
That is the meaning of precedence.

144
00:08:38,510 --> 00:08:44,650
So precedences as well as associativity are meant for parenticide nation.

145
00:08:44,990 --> 00:08:51,510
So let us do parenticide vision and then convert infix to perfect's and postfix.

146
00:08:51,510 --> 00:08:54,500
So Fosterville parenthesized this one properly.

147
00:08:54,500 --> 00:09:00,570
Parenthetic this C A plus B into C.

148
00:09:01,070 --> 00:09:06,400
So let me parenthesized this in this one who is having higher precedence multiplication.

149
00:09:06,740 --> 00:09:12,980
So this is multiplying what B with the C are able to see B plus see obviously.

150
00:09:13,190 --> 00:09:19,010
So put that one first in the bracket, put this one first in the bracket because this was having higher

151
00:09:19,010 --> 00:09:20,420
precedence multiplication.

152
00:09:21,290 --> 00:09:23,480
Now, next, what is the meaning of it?

153
00:09:23,510 --> 00:09:26,470
There is only one object, so put that one in the bracket.

154
00:09:26,480 --> 00:09:33,520
So this is A plus B, just B, no, A plus being C, so put that whole thing in the bracket.

155
00:09:34,310 --> 00:09:34,870
That's it.

156
00:09:35,150 --> 00:09:38,670
So this means that this should be added with the result of this one.

157
00:09:39,650 --> 00:09:41,770
This is the meaning, Soberon.

158
00:09:41,810 --> 00:09:48,170
This addition gives you clear cut meaning what operation should be performed on which a set of operands

159
00:09:48,740 --> 00:09:51,850
that for this precedences gives you the same idea.

160
00:09:52,040 --> 00:09:54,170
Let us convert first into perfect's.

161
00:09:54,590 --> 00:10:03,380
So for prefix facilitate operator then we like operands for this one prefix inside this one which brackets

162
00:10:03,380 --> 00:10:06,950
should be evaluated for this bracket should be valued at first.

163
00:10:06,950 --> 00:10:07,910
Innermost one.

164
00:10:07,910 --> 00:10:08,230
Right.

165
00:10:08,810 --> 00:10:11,210
So for that convert it into prefix.

166
00:10:11,220 --> 00:10:21,830
So I will write everything as it is A plus B to C should be written as Wattstax first, then B C and

167
00:10:21,830 --> 00:10:25,670
I'm putting in the square bracket to show that this is completed.

168
00:10:25,880 --> 00:10:26,690
Next step.

169
00:10:26,690 --> 00:10:27,530
Which bracket.

170
00:10:27,680 --> 00:10:29,180
So round brackets on the right.

171
00:10:29,480 --> 00:10:31,520
This round bracket will perform next.

172
00:10:32,150 --> 00:10:38,360
This we have finished now the next one, this whole thing will perform then that is having what a plus

173
00:10:38,360 --> 00:10:45,060
this holding so prefix means first right then left hand side and right hand side.

174
00:10:45,320 --> 00:10:54,470
So first rate plus then a the right hand side we have star B C so keep right on completing star B C

175
00:10:54,770 --> 00:10:57,180
so this is the perfect form of that expression.

176
00:10:57,890 --> 00:11:03,170
Now let us write postfix for the fun in this bracket is evaluated for this one.

177
00:11:03,560 --> 00:11:12,910
Convert that into postfix form E plus this is round bracket B C start convert it into postfix Bechstein.

178
00:11:13,340 --> 00:11:16,700
I will put it in the square bracket to show that this is converted.

179
00:11:17,060 --> 00:11:19,100
Now which bracket is evaluated next.

180
00:11:19,340 --> 00:11:27,740
This bracket is evaluated for this operation, so write it in the post from left hand side A right hand

181
00:11:27,740 --> 00:11:30,720
side B C start then.

182
00:11:30,740 --> 00:11:34,250
Right plus plus this the sixth form.

183
00:11:34,370 --> 00:11:36,440
So quickly let me show you the procedure.

184
00:11:36,440 --> 00:11:43,930
First of all, you fully parenthesized based on the precedences, then whichever one has to be evaluated

185
00:11:43,940 --> 00:11:44,240
first.

186
00:11:44,240 --> 00:11:47,530
You go on evaluating them in the order of their evaluation.

187
00:11:47,540 --> 00:11:52,400
So I don't evaluate convert it to prefix or convert it into postfix.

188
00:11:52,670 --> 00:11:54,170
Now, one more thing I would like to add.

189
00:11:55,010 --> 00:11:57,980
C prefix is not much used.

190
00:11:58,280 --> 00:11:59,360
Postfix is use.

191
00:11:59,930 --> 00:12:05,310
So infix operators in between postfix operators are done.

192
00:12:05,450 --> 00:12:11,060
Not only one thing is remaining that operators, in fact lot of the glue that holds that is prefix.

193
00:12:11,300 --> 00:12:12,290
But we don't use it.

194
00:12:12,290 --> 00:12:13,250
We use postfix.

195
00:12:13,760 --> 00:12:19,010
I have one more example, so I will quickly solve that one and I will show you a few more examples and

196
00:12:19,010 --> 00:12:24,560
solve them quickly so that you get the idea how to convert them and you should be able to convert it

197
00:12:24,560 --> 00:12:25,250
by yourself.

198
00:12:25,370 --> 00:12:27,380
Let us convert this into prefix.

199
00:12:28,370 --> 00:12:36,080
And I don't the expression once again, A plus B plus C, start the argument right parentheses, it

200
00:12:36,080 --> 00:12:41,930
will be too much contested so that I will show you who is having higher precedence.

201
00:12:42,080 --> 00:12:44,170
Multiplication is having highest precedence.

202
00:12:44,510 --> 00:12:46,350
So this has to be considered first.

203
00:12:46,370 --> 00:12:53,090
So this portion, that multiplication, is it between C and D, so I was right on E plus B plus this

204
00:12:53,090 --> 00:12:59,690
imperfect stuff as a star then CD, I'll put it in a square bracket to show that that is completed.

205
00:13:02,030 --> 00:13:07,730
Now, next, who is having a higher percentage, only quarryman plus, plus, and the plus are family,

206
00:13:08,090 --> 00:13:11,740
then which one I should take, I cannot take as per my wish.

207
00:13:11,750 --> 00:13:16,730
I cannot select anyone randomly because I love think I should take them from left to right.

208
00:13:16,890 --> 00:13:22,380
Remember this, if the president says ah same, then take them from left to right.

209
00:13:22,700 --> 00:13:23,990
So foster this plus.

210
00:13:23,990 --> 00:13:26,390
So let us convert this into postfix form.

211
00:13:26,450 --> 00:13:29,540
So plus then maybe there is the perfect form of that one.

212
00:13:29,540 --> 00:13:38,030
And put this on, frame the scroll back to show that this is completed then stod c d so this is completed,

213
00:13:38,030 --> 00:13:39,010
this one is completed.

214
00:13:39,140 --> 00:13:40,550
Now what is remaining this plus.

215
00:13:40,820 --> 00:13:45,890
So this plus has to be done up on what this left hand side and this right hand side on both.

216
00:13:46,220 --> 00:13:52,960
So prefix means first right plus the left hand side is plus a B right hand side is starts CD.

217
00:13:53,240 --> 00:13:56,370
So this is the prefix form of expression.

218
00:13:57,230 --> 00:13:58,970
Now let me show you the postfix form.

219
00:13:59,780 --> 00:14:04,340
This one A plus B plus C started.

220
00:14:04,670 --> 00:14:07,130
So which one is having highest precedence.

221
00:14:07,130 --> 00:14:07,600
This one.

222
00:14:07,880 --> 00:14:09,860
So this is one more left hand side to see.

223
00:14:09,860 --> 00:14:15,620
The return side is V, so A plus B plus C, D star postfix.

224
00:14:15,980 --> 00:14:20,740
As I have finished this, let me put it in the square bracket to show that that is completed now.

225
00:14:20,750 --> 00:14:24,890
Next, what are the remaining plus plus equal precedence.

226
00:14:24,900 --> 00:14:26,710
Which one should be the left hand side.

227
00:14:27,110 --> 00:14:34,370
This one, this plus is up on what A and B, so converting to postfix A, B plus, put it in the bracket

228
00:14:34,370 --> 00:14:35,300
to show it is complete.

229
00:14:35,300 --> 00:14:38,690
And then plus, as it is, this is remaining as it is.

230
00:14:40,610 --> 00:14:44,910
Then what is the meaning of this plus this plus up on what this is left, right and this is right,

231
00:14:45,050 --> 00:14:50,480
say so right on the left hand side A, B plus the right hand side Christan then.

232
00:14:50,570 --> 00:14:51,620
Right plus.

233
00:14:53,100 --> 00:14:57,780
There's the postfix, so this is the specific form of the expression.

234
00:14:59,960 --> 00:15:07,100
So I have taken one more example and shown you how to convert into prefix and postfix a few more examples

235
00:15:07,100 --> 00:15:07,670
and show you.

236
00:15:08,800 --> 00:15:15,360
Nick's expression, this is already having brackets converted into prefix 1st prefix file, right on

237
00:15:15,370 --> 00:15:22,780
the expression once again, A plus B star C minus D, so which one has to be performed?

238
00:15:22,780 --> 00:15:24,670
First bracket, but I have to bracket.

239
00:15:24,670 --> 00:15:25,660
So which bracket to take.

240
00:15:25,660 --> 00:15:27,090
First left hand side.

241
00:15:27,310 --> 00:15:28,720
So convert this one first.

242
00:15:29,050 --> 00:15:30,430
So I'm writing perfect.

243
00:15:30,430 --> 00:15:36,930
So plus a B put it in a square bracket to show that this is completed and start as it is then inside

244
00:15:36,950 --> 00:15:41,130
brackets C minus D then what is the next one.

245
00:15:41,770 --> 00:15:43,060
Multiplication of bracket.

246
00:15:43,150 --> 00:15:44,640
Bracket is having a president.

247
00:15:44,920 --> 00:15:47,100
So convert this into postfix form.

248
00:15:47,320 --> 00:15:53,620
So this left hand side is as it is, then start on the same then this has to be perfect.

249
00:15:53,630 --> 00:15:58,570
So this is minus C, D and this is prefix.

250
00:15:59,230 --> 00:16:00,250
Know what is remaining.

251
00:16:00,700 --> 00:16:02,440
Everything that is completed is in square.

252
00:16:02,500 --> 00:16:04,510
I get for whatever is outside is remaining.

253
00:16:04,870 --> 00:16:08,030
This state is remaining multiplication this one.

254
00:16:08,080 --> 00:16:12,790
So this is up on the left hand side, on the right hand side, so that the profits of this one start,

255
00:16:13,090 --> 00:16:18,160
first of all the left hand side plus A, B, the right hand side minus KDDI.

256
00:16:18,340 --> 00:16:21,730
So this is the perfect form of expression.

257
00:16:24,050 --> 00:16:27,950
Then let us write the specific form of this expression, E-Plus be.

258
00:16:29,140 --> 00:16:36,440
Star C minus Dean Novich won first two brackets are there, so left and right back at first.

259
00:16:36,580 --> 00:16:46,560
So this one in the postfix form, A, B plus completed star, then C minus D as it is now the next bracket.

260
00:16:46,570 --> 00:16:48,530
This is C minus D, this has to be done.

261
00:16:48,550 --> 00:16:53,860
So take this left hand side as it is then.

262
00:16:54,240 --> 00:16:55,930
Then convert that one into postfix.

263
00:16:55,930 --> 00:16:58,630
So C minus, put it into the square brackets.

264
00:16:58,930 --> 00:17:02,030
Now this multiplication is remaining up on the left hand side and right hand side.

265
00:17:02,290 --> 00:17:05,589
So a B plus seed minus start.

266
00:17:06,490 --> 00:17:09,940
This is the postfix force fighting.

267
00:17:09,940 --> 00:17:16,150
These examples are sufficient for you to learn how to convert expression from infix to postfix.

268
00:17:17,359 --> 00:17:22,380
So practice this one so you can watch it again and again if you are facing any problems.

269
00:17:22,520 --> 00:17:27,800
Watch it again and again and you should be able to convert it just by looking at an expression you should

270
00:17:27,800 --> 00:17:28,520
be able to tell.

271
00:17:28,520 --> 00:17:30,110
What is the postfix form of this one?

272
00:17:30,560 --> 00:17:32,110
That much practice you must have.

273
00:17:32,330 --> 00:17:34,830
If you understand the logic, then you can understand other things.

274
00:17:34,850 --> 00:17:35,270
Also.

