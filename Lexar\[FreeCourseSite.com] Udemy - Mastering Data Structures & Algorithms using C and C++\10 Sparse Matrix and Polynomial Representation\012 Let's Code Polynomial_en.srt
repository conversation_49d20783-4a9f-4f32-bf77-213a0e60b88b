1
00:00:00,520 --> 00:00:06,590
In this video, we look at the implementation of polynomial, so the representation of polynomial and

2
00:00:06,590 --> 00:00:11,050
the evaluation of Polynomial and also adding to polynomials.

3
00:00:11,230 --> 00:00:13,180
So let us create a new project for this one.

4
00:00:13,540 --> 00:00:15,910
I will call the project name as Polynomial.

5
00:00:19,290 --> 00:00:21,540
And the language you see language.

6
00:00:22,970 --> 00:00:24,170
Let us create a project.

7
00:00:26,810 --> 00:00:28,160
Here is the main function.

8
00:00:29,560 --> 00:00:31,480
And clear of all these comments.

9
00:00:33,530 --> 00:00:39,540
Hide the panels is the complete code is dead in front of us now.

10
00:00:39,560 --> 00:00:43,440
The first thing that we have discussed is the representation of a polynomial.

11
00:00:43,460 --> 00:00:46,370
For that, we have to first define for that.

12
00:00:46,370 --> 00:00:49,940
We have defined two structures, the first structure as Tom.

13
00:00:51,140 --> 00:00:58,010
And the storm contains coefficient and exponent coefficient can be either flawed or integer, but I'm

14
00:00:58,010 --> 00:01:02,340
taking it as integer so far, structure is strong.

15
00:01:02,690 --> 00:01:04,819
Then the second structure is polynomial.

16
00:01:04,819 --> 00:01:06,800
So I will just call it as poly.

17
00:01:07,550 --> 00:01:12,530
And they should have the number of toms in a polynomial then it should have.

18
00:01:14,570 --> 00:01:17,030
Structure type of thoms.

19
00:01:18,390 --> 00:01:23,340
And we will take a point there, because we don't know how many times I did, so I will call this variable

20
00:01:23,340 --> 00:01:24,540
name as it comes.

21
00:01:27,620 --> 00:01:33,790
That's all this is really a representation is over now here I have a very different function for creating

22
00:01:33,790 --> 00:01:34,540
a polynomial.

23
00:01:34,960 --> 00:01:36,640
So function name is create.

24
00:01:38,220 --> 00:01:46,080
It should take a polynomial by reference or by address, so stop e pointer B.

25
00:01:48,980 --> 00:01:55,370
Now, here inside villans, the number of non-zero elements and also will take their coefficients and

26
00:01:55,370 --> 00:01:55,890
exponent.

27
00:01:56,510 --> 00:02:00,530
So first of all, I will display a message asking for a number of tons.

28
00:02:06,370 --> 00:02:09,009
Then scan and read the number of Thom's.

29
00:02:12,480 --> 00:02:14,600
In BP's variable, that is enth.

30
00:02:15,790 --> 00:02:20,590
Now, once I know the number of times I should create an array of times because I have a pointer here

31
00:02:20,610 --> 00:02:22,930
so dynamically, I should create an array of tones.

32
00:02:23,470 --> 00:02:29,230
So these doms should be assigned that Mallott function.

33
00:02:29,230 --> 00:02:31,520
I will use for creating a.

34
00:02:32,080 --> 00:02:37,240
So for that number a pointer and the function is mellark.

35
00:02:39,210 --> 00:02:45,510
And this should be equal to the number of elements, the species having multiplied by size of.

36
00:02:49,460 --> 00:02:54,830
Multiplied by size of structural funds.

37
00:02:56,260 --> 00:03:03,970
Yes, this will create an aura in heap, as I have used multi-function, I should include a steadily

38
00:03:03,980 --> 00:03:06,820
predefine, so I'll include here.

39
00:03:12,030 --> 00:03:16,830
Liabilities included, not ones that is really I should read all the terms.

40
00:03:18,810 --> 00:03:21,650
Apprenticed under Tom's.

41
00:03:23,030 --> 00:03:29,670
Not for reading bombs, I should use a follow for reading all the bombs, and so for that I need a variable

42
00:03:29,670 --> 00:03:30,060
light.

43
00:03:30,080 --> 00:03:31,910
So here on the top, I have declared it.

44
00:03:32,690 --> 00:03:35,330
Now, using a follow up, I will read all the terms.

45
00:03:37,630 --> 00:03:42,700
I start from zero, I goes up to number of terms, I plus plus.

46
00:03:44,880 --> 00:03:49,940
Then scan, if I should read Tuvalu's, that is coefficient an exponent.

47
00:03:52,140 --> 00:03:57,840
These terms of I, I should read coefficient.

48
00:04:00,330 --> 00:04:02,580
And also, I should read Exponent.

49
00:04:06,800 --> 00:04:10,040
These terms of I.

50
00:04:12,160 --> 00:04:20,170
Explained that it this will read all the times, so we have taken the number of items here asking how

51
00:04:20,170 --> 00:04:20,980
many items are there?

52
00:04:20,980 --> 00:04:27,160
And we created an array of items of that size given size, then we are taking all the items that is

53
00:04:27,160 --> 00:04:32,200
for each time we need coefficient and exponent, that's all create function is finish.

54
00:04:32,230 --> 00:04:36,490
Now let us have a function for displaying a polynomial display.

55
00:04:37,270 --> 00:04:43,060
It should make a polynomial structure and all of a value is sufficient because it's not going to modify

56
00:04:43,060 --> 00:04:43,330
them.

57
00:04:45,050 --> 00:04:50,180
Then I have to display all the list of elements I need available, I already have declared it.

58
00:04:51,600 --> 00:04:59,500
Now, using follow up, I will display all the elements for I assign zero and I use less than the dot

59
00:04:59,640 --> 00:05:01,740
number of elements because this is called value.

60
00:05:01,790 --> 00:05:02,490
Remember this?

61
00:05:05,540 --> 00:05:05,900
Then.

62
00:05:08,110 --> 00:05:08,920
Barondess.

63
00:05:10,460 --> 00:05:15,790
I'll bring that down, just like overrepresent polynomial, that is coefficient exponent, exponent,

64
00:05:16,310 --> 00:05:19,430
so for that first Fosterville display coefficient.

65
00:05:20,890 --> 00:05:28,560
So for that, first I will proficient and X, then exponent, then plus for the next term.

66
00:05:30,310 --> 00:05:35,800
First values be the term of I thought coefficient.

67
00:05:37,070 --> 00:05:40,280
And second, Thomas, bees come of I.

68
00:05:41,770 --> 00:05:43,000
But Exponent.

69
00:05:45,400 --> 00:05:51,810
That's it after all of this, I should give you a new line so that I get some line gaps, that output

70
00:05:51,810 --> 00:05:52,330
looks good.

71
00:05:53,940 --> 00:05:55,810
So just I'm displaying a polynomial.

72
00:05:57,030 --> 00:05:59,660
That's all we have a function for create.

73
00:05:59,670 --> 00:06:01,310
We have a function for polynomial.

74
00:06:01,650 --> 00:06:05,460
So here inside the main function, let us create a polynomial.

75
00:06:08,290 --> 00:06:15,060
Even I would think, first of all, I will call create function upon even so address of people because

76
00:06:15,060 --> 00:06:18,570
it's called address, then I will be displayed.

77
00:06:19,230 --> 00:06:20,470
So I will send people.

78
00:06:22,390 --> 00:06:24,400
Let us run the program and test.

79
00:06:25,640 --> 00:06:28,650
How it takes polynomial and how it will displayed.

80
00:06:30,820 --> 00:06:33,700
Yes, first thing it's asking the number of times.

81
00:06:35,300 --> 00:06:38,510
So I say there are four times.

82
00:06:41,080 --> 00:06:50,080
Enter Tom, so first, um, coefficients to an export industry, next as efficient as five, and explain

83
00:06:50,080 --> 00:06:58,270
this to then be efficient as the three explained this one and efficient as for exponent, a zero.

84
00:07:01,940 --> 00:07:08,810
So the output that we are getting is two extra report of three, five extra Zipporah, four plus three

85
00:07:08,810 --> 00:07:12,260
extra reports of one plus four extra report of zero.

86
00:07:13,420 --> 00:07:15,970
Yes, so this is not multiplication.

87
00:07:16,000 --> 00:07:16,990
This is X.

88
00:07:17,960 --> 00:07:22,740
That's what we are displaying it here now, creation and display, we have finished.

89
00:07:22,830 --> 00:07:27,080
Now let us try to function for adding to polynomials.

90
00:07:27,980 --> 00:07:30,390
So already we have discussed that on whiteboard.

91
00:07:30,950 --> 00:07:37,130
I will simply write on the code function will return pointed to a polynomial and the function name is

92
00:07:37,130 --> 00:07:40,990
and it will take the two variables of type polynomial.

93
00:07:41,420 --> 00:07:45,290
So all by address are called by reference and you can send.

94
00:07:45,300 --> 00:07:47,870
So I will send it to my call by reference.

95
00:07:50,530 --> 00:07:52,570
Polly Esta.

96
00:07:53,510 --> 00:08:02,010
Our function at function is to two parameters so that it is returning the address of summation of Polynomial,

97
00:08:02,010 --> 00:08:10,130
and so for that I need one more polynomial that is poly some and it should be inside he.

98
00:08:10,370 --> 00:08:12,200
So I will take it as a pointer.

99
00:08:14,510 --> 00:08:21,770
And first of all, I should create this submission polynomial in Hebrew, so some assign using Marlock

100
00:08:21,770 --> 00:08:23,780
function, I will create struck.

101
00:08:26,810 --> 00:08:27,340
Polly.

102
00:08:28,390 --> 00:08:33,250
Poynter and Mellark function should be the size of.

103
00:08:34,340 --> 00:08:35,799
Struck Wally.

104
00:08:38,440 --> 00:08:44,860
Now, this really then inside this, I should create an area for Tom, so how many times at most how

105
00:08:44,860 --> 00:08:46,890
many times might be there at the moment?

106
00:08:46,900 --> 00:08:51,700
The number of times we call the number of times in polynomial one and polynomial to the.

107
00:08:53,100 --> 00:08:57,930
It will be equal to the number of storms in these two polynomials, so I will create an array equal

108
00:08:57,930 --> 00:08:58,710
to that size.

109
00:08:59,280 --> 00:09:00,360
So some of.

110
00:09:01,780 --> 00:09:04,390
Bonds should be equal to.

111
00:09:06,610 --> 00:09:17,710
Struck dumb pointer here and creating an array equal to the size evens number of storms plus paedos,

112
00:09:17,710 --> 00:09:19,210
number of storms.

113
00:09:21,380 --> 00:09:22,520
Multiplied by.

114
00:09:23,880 --> 00:09:26,730
Size of structure of Tom.

115
00:09:28,740 --> 00:09:30,240
Yes, this will create an another.

116
00:09:32,670 --> 00:09:38,330
Now I have to scan through to polynomials and also generate submissions so far that I need three for

117
00:09:38,340 --> 00:09:38,860
loops.

118
00:09:38,880 --> 00:09:41,910
So so for that I need three variables.

119
00:09:41,910 --> 00:09:43,920
I and G and K.

120
00:09:45,110 --> 00:09:52,250
And these three variables must be initialized with all Zeitels, IJI and Caillebotte of all three of

121
00:09:52,250 --> 00:09:53,310
them are zeros.

122
00:09:54,170 --> 00:09:59,340
Now, as we have already seen that using why, look, we can scan them and we can copy the elements.

123
00:09:59,340 --> 00:10:06,770
So I will quickly write on the code while I use less than evens number of elements and just less than

124
00:10:06,770 --> 00:10:08,840
people's number of elements.

125
00:10:11,600 --> 00:10:12,830
Now compare and copy.

126
00:10:14,010 --> 00:10:15,870
For comparison, if.

127
00:10:17,850 --> 00:10:20,640
BE1 storms of I.

128
00:10:22,870 --> 00:10:28,170
But Exponent, if that is greater than paedos from.

129
00:10:29,500 --> 00:10:30,670
Of G.

130
00:10:31,730 --> 00:10:35,810
Exponent means first, one exponent is greater than that one should be written in some.

131
00:10:36,910 --> 00:10:38,680
Some of Tom's.

132
00:10:40,900 --> 00:10:44,950
Of Kibler surplus should be copied with be once.

133
00:10:46,010 --> 00:10:47,540
Terms of I.

134
00:10:50,360 --> 00:10:52,680
The first one is greater, will be the first one.

135
00:10:53,270 --> 00:10:56,840
Otherwise, if second one is greater than the copy, the second one.

136
00:10:56,850 --> 00:11:00,440
So I will copy this code and I will modify it for second.

137
00:11:02,360 --> 00:11:09,500
So if first polynomial is greater, this is great, I'll just InDesign and I will see if it is a smaller

138
00:11:09,500 --> 00:11:14,630
than second polynomial exponent then copied element from the second polynomial.

139
00:11:15,570 --> 00:11:18,540
So they should be sick and vulnerable and they should be.

140
00:11:18,900 --> 00:11:22,560
And also it should be a plus plus and here also should be a plus plus.

141
00:11:24,320 --> 00:11:30,110
Yes, if just one integrated copy that one otherwise copied the second one, if second one is greater

142
00:11:30,770 --> 00:11:36,890
and finally else it means if both of them are equal, then I should add coefficients and Exponent should

143
00:11:36,890 --> 00:11:37,580
be same.

144
00:11:38,720 --> 00:11:41,690
So submission of forms of.

145
00:11:42,690 --> 00:11:43,200
Jay.

146
00:11:46,160 --> 00:11:57,170
Not exponent should be same as BE1 terms of I thought explained, so first I finish Exponent, then

147
00:11:57,170 --> 00:11:58,370
let us take coefficient.

148
00:11:58,670 --> 00:11:59,840
So terms of.

149
00:12:01,670 --> 00:12:02,870
Plus plus I will do.

150
00:12:05,180 --> 00:12:07,460
But coefficients should be equal to.

151
00:12:09,570 --> 00:12:13,710
Pevensie terms of AI plus plus I will do not.

152
00:12:14,760 --> 00:12:16,680
Coefficient plus.

153
00:12:18,650 --> 00:12:20,630
Beatles terms of.

154
00:12:23,450 --> 00:12:25,900
G plus plus dot.

155
00:12:27,800 --> 00:12:28,430
Coefficient.

156
00:12:30,770 --> 00:12:34,640
That's all so this loop will be comparing and copying them.

157
00:12:36,490 --> 00:12:40,510
Once it has came out of the loop, there may be some remaining elements either in.

158
00:12:42,180 --> 00:12:46,920
First polynomial or maybe in second polynomial, so I should take care of copying all those elements

159
00:12:46,920 --> 00:12:55,410
while I use less than Evens and an AI plus, plus I should copy that elements of first polynomial in

160
00:12:55,410 --> 00:12:57,690
terms of K plus plus.

161
00:12:59,850 --> 00:13:01,710
We once, Thomas.

162
00:13:03,720 --> 00:13:10,260
The same way I should do it for second polynomial also, so I will copied and pasted here and modify

163
00:13:10,260 --> 00:13:10,770
this one.

164
00:13:11,070 --> 00:13:14,070
This is from G and this was P1.

165
00:13:14,070 --> 00:13:15,090
So this is P2.

166
00:13:16,060 --> 00:13:26,560
And this is G and some of Tom's C++ assigned, this is Paedos Thoms, yeah, I missed this I and this

167
00:13:26,560 --> 00:13:27,460
is G.

168
00:13:30,260 --> 00:13:38,670
Yeah, additional storms just finish now some number of elements that should be equal to K, then also

169
00:13:38,670 --> 00:13:39,680
return on some.

170
00:13:43,210 --> 00:13:48,500
That's all official function has finished its little and the you have to practice this one.

171
00:13:49,480 --> 00:13:56,080
Now let us create two polynomials so P1 and P2 and also for addition, I will have a polynomial that

172
00:13:56,080 --> 00:13:56,650
is three.

173
00:13:58,540 --> 00:14:04,180
Now here first, we are creating first polynomial that will do one thing, will create the second polynomial

174
00:14:04,180 --> 00:14:06,520
also that is easy to.

175
00:14:08,220 --> 00:14:14,310
After this, we will call our function and take the resulting poetry poetry reassign ad.

176
00:14:15,140 --> 00:14:18,620
Address of BE1 and address of people.

177
00:14:20,020 --> 00:14:24,550
Then I will display all the polynomial, so I will not write any message, just I will make sure that

178
00:14:24,550 --> 00:14:25,720
it comes in new line.

179
00:14:26,140 --> 00:14:33,400
So, again, the first one and after displaying first polynomial again, I will say printf that is slash

180
00:14:33,400 --> 00:14:34,480
and that is new line.

181
00:14:35,730 --> 00:14:38,910
And call this function upon second polynomial.

182
00:14:42,660 --> 00:14:50,520
Then again, a new line, then I will call this play upon purpose, and it should be differenced.

183
00:14:51,740 --> 00:14:53,030
And that is Peter.

184
00:14:55,170 --> 00:14:55,770
Xol.

185
00:14:58,070 --> 00:15:04,370
Number of times, I'll give three for the first polynomial and the terms are first as power, five,

186
00:15:04,370 --> 00:15:05,510
poverty and.

187
00:15:06,900 --> 00:15:07,480
I'll give.

188
00:15:09,650 --> 00:15:12,590
So coefficient is one and part is five.

189
00:15:13,950 --> 00:15:18,060
Coefficient is one and our history coefficient is one over this one.

190
00:15:19,410 --> 00:15:27,330
Then second polynomial, this also three tons of power for 2012 will give coefficient in one of these

191
00:15:27,330 --> 00:15:32,700
four coefficients, one of what is a two and coefficient is one of zero.

192
00:15:34,210 --> 00:15:40,550
Yeah, you can see that the first polynomial is one in two, actually four a five and one in to export

193
00:15:40,550 --> 00:15:41,920
three, export one.

194
00:15:42,560 --> 00:15:45,850
This is four to zero one addition is done.

195
00:15:45,850 --> 00:15:49,480
I got all the Toms that is four, three, two, one and zero.

196
00:15:51,800 --> 00:15:53,030
Let me try another.

197
00:15:55,800 --> 00:16:03,930
Again, I'm running the number of times are three only, so first one is efficient is one and exponent

198
00:16:04,200 --> 00:16:04,680
three.

199
00:16:10,500 --> 00:16:17,130
Then second one coefficient is one and exponent to the third one is coefficient is one and exponent

200
00:16:17,160 --> 00:16:17,820
also one.

201
00:16:18,790 --> 00:16:25,120
And for this also, I'll give terms for the second polynomial, the efficiency, I'll give them as five

202
00:16:25,720 --> 00:16:27,940
and the first, but it's too efficient.

203
00:16:27,940 --> 00:16:31,840
It's five second poverties one and Nexus Power zero.

204
00:16:33,030 --> 00:16:38,330
Now you can see that two terms are overlapping, that they are having common poverty and poverty won.

205
00:16:38,900 --> 00:16:39,950
Let us see what happens.

206
00:16:40,380 --> 00:16:42,010
This is the first polynomial that is power.

207
00:16:42,040 --> 00:16:45,320
Three to one second polynomial is two one zero.

208
00:16:45,690 --> 00:16:49,620
And here three is that coefficient one and two.

209
00:16:49,620 --> 00:16:53,700
And one are having question six because it is five and one is added.

210
00:16:54,690 --> 00:16:57,200
This five is added with this.

211
00:16:58,380 --> 00:17:00,420
There's a five as added with this one.

212
00:17:01,140 --> 00:17:04,440
And the last one is just five x zero.

213
00:17:04,440 --> 00:17:05,880
That is five zero.

214
00:17:07,990 --> 00:17:12,940
That's all so this is looking perfect so you can try this program.

215
00:17:15,020 --> 00:17:21,349
You can find the downloadable file below the video, so if you want, you can verify your code in the

216
00:17:21,349 --> 00:17:22,010
previous.

