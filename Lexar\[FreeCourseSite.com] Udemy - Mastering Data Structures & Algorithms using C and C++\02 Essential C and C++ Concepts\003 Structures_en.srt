1
00:00:00,330 --> 00:00:06,060
In this video we will learn about Structures. Structure can be defined as a collection of data members

2
00:00:06,120 --> 00:00:12,000
that are related data members under one name; and those data members may be of similar type, maybe of 

3
00:00:12,000 --> 00:00:12,790
dissimilar type.

4
00:00:12,890 --> 00:00:19,950
So usually it is defined as a collection of dissimilar data items under one name; that is grouping the

5
00:00:19,960 --> 00:00:21,220
data items.

6
00:00:21,450 --> 00:00:26,940
Structure is used for defining user defined data types. Apart from the primitive data types,

7
00:00:26,940 --> 00:00:31,850
What we have in any programming language, like C language, we have primitive data types, like integer, float, 

8
00:00:31,850 --> 00:00:39,410
double so on, using these primitives we can define our own data type depending on our requirement.

9
00:00:39,450 --> 00:00:43,620
So this is structure I am using more frequently in my course.

10
00:00:43,620 --> 00:00:49,080
So in this video we will learn how to define the structure and what does it mean by the size of a structure,

11
00:00:49,140 --> 00:00:55,890
like how much memory it consumes then building a structure then accessing the members of a structure.

12
00:00:56,460 --> 00:00:58,540
Let us learn about these things one by one.

13
00:00:59,310 --> 00:01:08,310
Let us take an example of a rectangle. A rectangular figure will have properties like length and breadth.

14
00:01:08,400 --> 00:01:16,550
So it will have length and breadth, these two things together defines a rectangle.

15
00:01:17,330 --> 00:01:23,030
So if in your programs you need something like a rectangle then you can define the structure for it

16
00:01:23,060 --> 00:01:26,390
because a rectangle is not defined by just one value,

17
00:01:26,390 --> 00:01:28,970
It is defined by a set of values.

18
00:01:29,000 --> 00:01:34,610
So that is length and breadth so you can group them together under one name and define it as a structure.

19
00:01:34,610 --> 00:01:36,440
For example a rectangle.

20
00:01:36,440 --> 00:01:39,680
So, here I define, struct rectangle

21
00:01:43,950 --> 00:01:49,200
now, inside this, I will take integer type length, and integer type breadth

22
00:01:49,210 --> 00:01:53,670
Now this is having two members that are length and breadth.

23
00:01:53,970 --> 00:01:55,770
I'm picking them as integer type.

24
00:01:55,920 --> 00:02:00,720
If you want you can take them as float or double any type depending on your requirement.

25
00:02:00,720 --> 00:02:05,660
I'm assuming that these are simple values which are integral type, no decimals are there.

26
00:02:05,890 --> 00:02:11,400
So structure rectangle is defined by its length and breadth; These two members together are defining

27
00:02:11,400 --> 00:02:12,100
a rectangle.

28
00:02:12,480 --> 00:02:15,360
So this is the definition of a structure.

29
00:02:16,050 --> 00:02:17,810
I have taken an example of rectangle.

30
00:02:17,820 --> 00:02:20,880
I'll be showing you more examples upon structures later on.

31
00:02:21,630 --> 00:02:24,430
So let us learn further about this one.

32
00:02:24,570 --> 00:02:27,800
Now how much memory this rectangle will be consuming?

33
00:02:27,850 --> 00:02:31,270
See it is having two members this land integer type.

34
00:02:31,380 --> 00:02:37,590
If I assume integer takes 2 bytes, or it may take 4 bytes but, I'm assuming it has 2 bytes. Now, breadth

35
00:02:37,590 --> 00:02:44,670
also takes 2 bytes, total it is 4 bytes of memory. So, this structure is taking 4 bytes

36
00:02:44,670 --> 00:02:45,160
of memory.

37
00:02:45,180 --> 00:02:48,940
Right now it's not consuming any memory because it is just our definition.

38
00:02:48,960 --> 00:02:54,740
So if I create a variable of this type then it will be occupying that much memory.

39
00:02:54,880 --> 00:02:56,580
Now we have seen how do we define.

40
00:02:56,580 --> 00:03:01,710
We have learned how to know its size. The size of a structure is the total amount of memory consumed

41
00:03:01,710 --> 00:03:04,220
by all its members.

42
00:03:04,260 --> 00:03:08,190
Now next let us learn how to declare a variable of that type structure.

43
00:03:08,610 --> 00:03:13,920
So inside the main ( ) , I will write down main ( ). Now, inside the mean ( ) ,

44
00:03:13,950 --> 00:03:17,640
If I declare a variable, so the method of declaration is write,

45
00:03:17,640 --> 00:03:22,110
Struct as a keyword then give the name rectangle.

46
00:03:22,410 --> 00:03:24,930
Then I'll give the name as r.

47
00:03:25,320 --> 00:03:26,220
Rectangle r.

48
00:03:26,730 --> 00:03:28,330
This is a declaration.

49
00:03:28,350 --> 00:03:32,200
Now, this r will occupy the memory space, right.

50
00:03:32,280 --> 00:03:33,930
This will be created in the memory.

51
00:03:34,030 --> 00:03:39,840
So, how it looks like? like r is of type triangle, so it is having 2 members that is length and breadth, so

52
00:03:39,960 --> 00:03:46,700
r will occupy a total of 4 bytes, and first 2 bytes are for length and next 2 bytes are for breadth.

53
00:03:47,340 --> 00:03:50,290
So yes that is occupying memory.

54
00:03:50,400 --> 00:03:52,410
This is a method of declaration.

55
00:03:52,600 --> 00:04:00,070
Now, if suppose, I want to initialize it, then I can declare and simultaneously I can initialize it.

56
00:04:00,120 --> 00:04:06,640
So let us say, struct, I will rewrite the same thing, I will say rectangle.

57
00:04:07,010 --> 00:04:12,390
I'll use the same name r with values, I can say 10 , 5 .

58
00:04:12,420 --> 00:04:16,740
So, length is 10 and breadth is 5, so, 10 and 5 will be filled in this one.

59
00:04:17,820 --> 00:04:20,660
So this is how we can declare as well as initialize

60
00:04:20,700 --> 00:04:21,470
.

61
00:04:21,600 --> 00:04:27,990
This is just a declaration, right; and this is declaration plus initialization, together.

62
00:04:27,990 --> 00:04:30,720
This is a declaration plus initialization

63
00:04:33,130 --> 00:04:37,520
then, inside the main memory, if you want to see where this r is created,

64
00:04:37,550 --> 00:04:43,450
So it is created inside the stack for the main ( ) , is it inside the stack frame of main ( ) .

65
00:04:43,580 --> 00:04:48,020
So r will be having length and breadth, this is length and breadth,

66
00:04:48,190 --> 00:04:49,730
And this is 10 and 5.

67
00:04:49,800 --> 00:04:51,460
So I'm showing them side by side.

68
00:04:53,410 --> 00:04:55,350
So we have learned definition,

69
00:04:55,360 --> 00:05:01,750
declaration and initialization. Now, how to access the members of the structure? Suppose, I want to access

70
00:05:01,750 --> 00:05:02,500
this length.,

71
00:05:02,500 --> 00:05:09,430
I want to modify it to 15. So, for accessing the members of a structure we should use structure variable name

72
00:05:09,790 --> 00:05:11,290
and dot length.

73
00:05:11,530 --> 00:05:20,170
So this is length = 15, so this value will be changed to 15, right; so, it is change to 15 in the memory.

74
00:05:20,170 --> 00:05:21,950
So this is how we can access,

75
00:05:21,950 --> 00:05:27,940
So what is the operator we use for accessing a member? dot operator is used for accessing a member. Then, r . breadth ,

76
00:05:27,980 --> 00:05:32,900
I can modify it, r . breadth = 10 .

77
00:05:33,020 --> 00:05:39,430
So this is modified to 10. So, if you want to read or write the members of a structure dot operator is

78
00:05:39,430 --> 00:05:45,360
used. Here I will calculate the area of a rectangle and show you, so directly I will print that as 

79
00:05:45,440 --> 00:05:50,740
printf (" Area of Rectangle is %d",

80
00:05:54,350 --> 00:05:56,540
printf ("Area of Rectangle is %d",

81
00:05:56,540 --> 00:05:58,740
So here, I will write the formula.

82
00:05:58,760 --> 00:06:01,750
So, what is the formula for area? length*breadth .

83
00:06:01,880 --> 00:06:08,570
So how to access that length and breadth? I have to use the dot operator. So, say,

84
00:06:08,570 --> 00:06:11,950
r . length * r . breadth

85
00:06:12,150 --> 00:06:18,410
So this is how I can access the members. So, length and breadth will be multiplied, that is 15 * 10

86
00:06:18,420 --> 00:06:21,390
and I get the result is 150. So, that's it

87
00:06:21,390 --> 00:06:25,620
about the structure, this is sufficient and more about the structures, I will discuss when I talk about

88
00:06:25,980 --> 00:06:27,890
pointers and functions.

89
00:06:27,900 --> 00:06:30,360
So this was just the introduction to structure.

90
00:06:31,040 --> 00:06:34,070
So let us continue with other topics in the next videos.

91
00:06:34,130 --> 00:06:39,270
So that's it. We have learnt about how to define, how to declare and how to access the members and where it is created

92
00:06:39,290 --> 00:06:43,500
in the memory when you are directly creating or declaring it inside the main ( ) . 

93
00:06:43,600 --> 00:06:49,190
declaration and initialization is like this when it is done. So, it's inside the stack frame of that particular

94
00:06:49,190 --> 00:06:50,360
function.

95
00:06:50,370 --> 00:06:56,000
Now I will give you a few more examples of structure. So, I will write down few more structures to give you the idea

96
00:06:56,270 --> 00:07:01,230
how or where we can use structures. Let us look at the example,

97
00:07:01,250 --> 00:07:08,940
So the first example I will write a complex number.
So complex number in mathematics is defined

98
00:07:08,940 --> 00:07:14,060
in terms of a + ib , where i is an imaginary number.

99
00:07:14,090 --> 00:07:17,040
So, two real numbers are a and b.

100
00:07:17,060 --> 00:07:19,850
where, i is defined as root of -1.

101
00:07:20,270 --> 00:07:24,530
So if you are familiar with the complex number then you can understand this, otherwise, just you can Google

102
00:07:24,530 --> 00:07:30,770
it and find out what a complex number is. So, complex number is defined in terms of a + ib

103
00:07:30,800 --> 00:07:36,310
So we need two members A and B. So, complex number is defined in terms of two elements.

104
00:07:36,410 --> 00:07:39,050
So that you can group them and define this structure.

105
00:07:39,080 --> 00:07:47,700
So here we can define it as structure, struct complex , and inside, that we can have two members.

106
00:07:47,700 --> 00:07:50,840
One is a, or I can call it as real part.

107
00:07:51,000 --> 00:07:59,380
And second one is imaginary, I can name it as b or also I can call it as imaginary that is img .

108
00:07:59,400 --> 00:08:01,240
This is our definition.

109
00:08:01,320 --> 00:08:05,790
Now you can define a complex number like this and you can create the variables of complex numbers and

110
00:08:05,790 --> 00:08:13,740
you can use them in your program. if you need this complex number. Now, second example I will take an example

111
00:08:13,740 --> 00:08:14,570
of a student,

112
00:08:14,580 --> 00:08:17,980
Suppose your program is dealing with students' information.

113
00:08:18,160 --> 00:08:25,810
So for a student we can define a structure structure for us to then make and then the details of a student.

114
00:08:25,830 --> 00:08:29,230
So let us say I want to store the roll number of a student.

115
00:08:29,340 --> 00:08:31,670
And suppose a  roll number is integer type.

116
00:08:31,740 --> 00:08:34,710
Next I want to store the name of a student.

117
00:08:34,770 --> 00:08:36,659
So name of a student is a string.

118
00:08:36,659 --> 00:08:40,890
So I will have an array of type character for name.

119
00:08:40,890 --> 00:08:47,520
Let us reserve 25 spaces, so it can store a name up to 25 characters.

120
00:08:47,520 --> 00:08:52,360
Next thing, I want to store department of students, so department is also a name.

121
00:08:52,440 --> 00:08:54,000
So it's a character type.

122
00:08:54,000 --> 00:09:01,710
Let us give the name of size 10, like computer science or mechanical engineering or anything you can

123
00:09:01,710 --> 00:09:04,150
give a short form of the department name.

124
00:09:04,290 --> 00:09:07,680
Next, I want to store the address of a student, so address,

125
00:09:07,680 --> 00:09:14,500
I can take it as, of some size, let us say, 50 bytes, right.

126
00:09:15,050 --> 00:09:20,870
So I think it is sufficient. If you want to add, you can add many things, like the phone number of a student,

127
00:09:20,870 --> 00:09:25,790
and email address of a student, Date of Birth of a student, a lot of things you can add, so that all together

128
00:09:25,790 --> 00:09:31,070
forms a single student structure. And when you declare a variable you get all these things together

129
00:09:31,520 --> 00:09:35,300
and you can access each of them individually using dot operator.

130
00:09:35,870 --> 00:09:38,310
So this is another idea of a structure.

131
00:09:38,370 --> 00:09:43,570
Now if I calculate the amount of memory it is taking, like if I said this is 2 bytes and 2 bytes,

132
00:09:43,580 --> 00:09:49,670
So this is taking total 4 bytes, and if I take this one, this is integer, taking 2 bytes.

133
00:09:49,700 --> 00:09:55,550
So this is character type, 25 characters take 25 bytes because character takes just 1 byte

134
00:09:55,970 --> 00:09:57,300
and this 10 bytes,

135
00:09:57,380 --> 00:09:58,850
and this is 50 bytes,

136
00:09:58,910 --> 00:10:01,640
So this is total 77.

137
00:10:01,640 --> 00:10:05,430
So this is taking 77 bytes.

138
00:10:05,560 --> 00:10:07,630
So this is another example.

139
00:10:07,630 --> 00:10:13,530
This is just for a student, you can define it for an employee so you can have employee id, employee name,

140
00:10:13,680 --> 00:10:19,260
employee department, employee address, employee phone number, so on. Then, if you want to define a structure 

141
00:10:19,260 --> 00:10:26,720
for a book, you can give book title, book author name, book version or edition, then year of publication and so on.

142
00:10:26,840 --> 00:10:33,150
So, you can have all the members. So, a lot of data you can combine together under one name and when you

143
00:10:33,150 --> 00:10:36,220
declare a variable of type student, suppose, for example for this,

144
00:10:36,240 --> 00:10:46,320
If I do further, then this is a variable I am declaring, struct student s . So, I can say, s . roll

145
00:10:46,680 --> 00:10:54,600
I can set the roll number. Let us say roll number is 10, then I can say s . name and I can assign

146
00:10:54,740 --> 00:11:02,410
some name. Like this, I can access all members. This is just for one student. So, similarly we can have

147
00:11:02,410 --> 00:11:08,860
for various students and we can deal with their information in our program. So,

148
00:11:08,860 --> 00:11:11,560
One more example for a structure I will take and show you.

149
00:11:12,800 --> 00:11:20,030
So next example I take an example for playing cards. A deck of playing cards will have some properties.

150
00:11:20,040 --> 00:11:21,580
It will have a face value.

151
00:11:21,660 --> 00:11:24,960
Let us say kings of hearts.

152
00:11:24,960 --> 00:11:26,220
So there is a face value.

153
00:11:26,220 --> 00:11:32,670
And this will have color, that is a red color and also shape is hearts. Any playing card can be defined

154
00:11:32,670 --> 00:11:40,200
in terms of three properties. First is, face value, then face value can start from 1 onwards. 1 means,

155
00:11:40,230 --> 00:11:50,030
let us call it as ACE, so, ACE, then 2, 3, 4, 5, . . . .  10 . then a J is 11, and Queen is 12 

156
00:11:50,130 --> 00:11:51,610
and King as 13.

157
00:11:51,750 --> 00:11:57,080
So these are the face value from 1 to 13. Then, next is shapes are there.

158
00:11:57,480 --> 00:11:58,810
So what are the shapes?

159
00:11:58,860 --> 00:12:06,640
There are 4 shapes, like Hearts, Diamonds, Spades, and Club. So, 4 shapes are there.

160
00:12:06,660 --> 00:12:12,000
So let us define the shapes in terms of codes. We will give code
for them.

161
00:12:12,000 --> 00:12:13,470
Let us say 0,

162
00:12:13,530 --> 00:12:14,450
is for club.

163
00:12:14,700 --> 00:12:18,130
So this club will be like this, right.

164
00:12:18,480 --> 00:12:20,300
And 1 is for spade.

165
00:12:20,550 --> 00:12:22,720
So this is Spade.

166
00:12:22,790 --> 00:12:26,930
Alright, then, 3 is for diamond.

167
00:12:26,970 --> 00:12:30,630
Let us take it as Diamond, and 4 is for hearts.

168
00:12:30,690 --> 00:12:34,320
So let us define these as shapes. So for each shape,

169
00:12:34,350 --> 00:12:37,970
I have given a number. Now, next thing is color.

170
00:12:38,190 --> 00:12:42,840
So as there are only two colors, I will define colors also in terms of codes.

171
00:12:42,840 --> 00:12:48,390
Let us say 0 for black color and 1 for red color.

172
00:12:48,720 --> 00:12:50,570
So 1 for the red color.

173
00:12:50,770 --> 00:12:52,570
So black and red color.

174
00:12:52,650 --> 00:13:00,420
So this is how we can define a playing card. Now ,this I can represent it as a structure. Let us define a

175
00:13:00,420 --> 00:13:07,660
structure; struct card ;  What are the members?

176
00:13:07,660 --> 00:13:13,290
All these are integers only now. First one is

177
00:13:13,300 --> 00:13:21,250
is int face ; and the second one is int shape ; and the third one is int color

178
00:13:21,250 --> 00:13:21,950
int color

179
00:13:25,380 --> 00:13:27,200
this is how we can define a playing card.

180
00:13:27,450 --> 00:13:30,300
So this is the definition. Then, how much space is it taking?

181
00:13:30,300 --> 00:13:37,140
Suppose, if I say, int type is taking 2 bytes, so total 6 bytes of memory it is taking. This is just our definition

182
00:13:37,140 --> 00:13:38,420
of a card.

183
00:13:38,600 --> 00:13:44,850
Now let us declare a variable. So, inside main ( ) , if I declare a variable of this type of

184
00:13:44,850 --> 00:13:53,700
structure, so I can have a playing card, struct card c . This c, how it will occupy the memory?

185
00:13:53,970 --> 00:14:00,120
it will have three members, first one is a face, and the second one is shape, right?

186
00:14:00,480 --> 00:14:06,260
And the third one is color. Now you can access all these members by using a dot operator, I can say, 

187
00:14:06,270 --> 00:14:14,190
c . face = 1 , next, c . shape = 0.

188
00:14:14,250 --> 00:14:20,480
So this is for club. Now, c . color = 0 . This is black color definitely.

189
00:14:20,490 --> 00:14:21,350
So this is 0 .

190
00:14:22,020 --> 00:14:27,630
So this how I can create a variable of datatype structure, and I can initialize its member. So, this will be

191
00:14:27,630 --> 00:14:32,980
1 0 0 . So, you can see that I am using dot operator.

192
00:14:33,100 --> 00:14:35,370
Otherwise; Let us forget this.

193
00:14:35,590 --> 00:14:43,870
Otherwise I can directly initialize also. struct card c , I can directly initialize the value, that is

194
00:14:43,870 --> 00:14:47,800
first, face value is 1 , and then, shape is 0 , and the color is also 0.

195
00:14:48,310 --> 00:14:56,950
So, this is declaration plus initialization. And, inside the memory it will be here, like c will have 3 members,

196
00:14:56,950 --> 00:15:08,640
1 0 0 0 . It will be inside the stack, that is belonging to main ( ) , or the stack frame belonging

197
00:15:08,640 --> 00:15:09,480
to the main ( ) .

198
00:15:10,640 --> 00:15:13,560
Now, one more thing, I'll just remove this and show you one more thing.

199
00:15:15,280 --> 00:15:24,230
See, inside the main ( ) , I want to have a deck of cards, so, total there will be 52 cards.

200
00:15:24,250 --> 00:15:26,080
This is the definition of a card.

201
00:15:26,110 --> 00:15:27,470
So just for one card.

202
00:15:27,800 --> 00:15:32,950
Likewise I want 52 cards so I can declare a variable,

203
00:15:33,010 --> 00:15:34,110
struct card ,

204
00:15:34,150 --> 00:15:37,380
Now, this is the structure name, and now variable name,

205
00:15:37,390 --> 00:15:46,100
I will call it as deck of size 52. This will form an array of structures.

206
00:15:46,100 --> 00:15:47,020
This is important.

207
00:15:47,490 --> 00:15:55,190
Array of structures. so I'll be having 52 such structures. So, one structure is taking 6 bytes,

208
00:15:55,530 --> 00:16:05,850
Total how many bytes will it take? It will be taking, 52 * 6 bytes. So, 312 bytes it

209
00:16:05,850 --> 00:16:06,480
will take.

210
00:16:06,630 --> 00:16:10,200
So I'll get 52 different cards. Now,

211
00:16:10,260 --> 00:16:13,980
Each card I can access it with the help of its index.

212
00:16:14,430 --> 00:16:19,240
Even I can initialize this array of structures.

213
00:16:19,380 --> 00:16:29,400
So if I want to initialize it, for a few cards I will do. First one, first card is ACE of club

214
00:16:29,880 --> 00:16:31,210
and black color.

215
00:16:31,230 --> 00:16:36,600
This is first card then second, that is card 2, face value is 2,

216
00:16:36,690 --> 00:16:43,800
and this is club and color is 0 , so on. I can fill this one. Now, afterwards, if suppose I want to start

217
00:16:43,800 --> 00:16:47,940
filling the other colors, or, other shapes like 

218
00:16:48,290 --> 00:16:56,250
ACE of spade and the color is 0, right. Then card value is 2 and it is a spade.

219
00:16:56,250 --> 00:16:58,970
So next is color is 0.

220
00:16:59,370 --> 00:17:02,960
So this is face value and this is shape and this is color.

221
00:17:03,240 --> 00:17:07,290
So like this I can fill up all 52 cards if I want to initialize them.

222
00:17:08,619 --> 00:17:11,160
So we can initialize an array of structures like this.

223
00:17:11,160 --> 00:17:14,119
This is the first structure, and second structure and so on.

224
00:17:14,500 --> 00:17:21,010
So in this way we can initialize array of structures if I want to access them individually, like the

225
00:17:21,010 --> 00:17:24,750
first card I want to display, then I can use dot operator.

226
00:17:24,910 --> 00:17:34,970
So let us print just a face value of first card, printf ("%d", deck[0] . face )

227
00:17:35,200 --> 00:17:45,880
That is the second, so it will show face of the first card then,

228
00:17:47,200 --> 00:17:55,800
printf ("%d", deck[0] . shape ) So, I can display shape.

229
00:17:56,010 --> 00:17:58,800
That's it. I can display other things also, like color also

230
00:17:58,800 --> 00:18:03,760
I can display. So, this how by using array name, then index,

231
00:18:03,760 --> 00:18:04,840
Then I can access it.

232
00:18:04,840 --> 00:18:06,300
So here we have learned,

233
00:18:06,760 --> 00:18:08,270
How do you find a structure.,

234
00:18:08,320 --> 00:18:14,470
How to declare an array of structure, and how to initialize, if you want to initialize then, how to access

235
00:18:14,470 --> 00:18:15,130
of the members.

236
00:18:15,670 --> 00:18:18,410
So this I'll be giving a demonstration also.

237
00:18:18,430 --> 00:18:23,200
This is sufficient right now for the structure. More about structures, that is pointer to a structure

238
00:18:23,200 --> 00:18:26,840
and the parameter of the structure, we will be learning them in coming videos.

