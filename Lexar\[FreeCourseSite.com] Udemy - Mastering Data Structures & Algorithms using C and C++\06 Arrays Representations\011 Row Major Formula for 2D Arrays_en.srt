1
00:00:00,360 --> 00:00:07,020
Let us see how our two dimensional arrays manage are handled by a compiler innovative program.

2
00:00:07,050 --> 00:00:09,490
We declare a two dimensional array like this.

3
00:00:09,900 --> 00:00:15,600
Suppose I'm declaring an integer type array of dimensions, three, three, four, three rows and four

4
00:00:15,600 --> 00:00:16,170
columns.

5
00:00:16,650 --> 00:00:19,620
Then this is how to the array we draw on paper.

6
00:00:20,700 --> 00:00:26,300
We visualize that this is how we got a two dimensional any element we can access with the help of its

7
00:00:26,310 --> 00:00:28,080
role number and column number.

8
00:00:28,080 --> 00:00:30,510
So this is a of to one.

9
00:00:31,110 --> 00:00:36,200
But the reality is during execution, the actual memory allocated will be linear.

10
00:00:36,750 --> 00:00:42,620
It will not be in terms of rows and columns, but it will be like a single dimension array.

11
00:00:43,200 --> 00:00:46,420
So total three force to one locations are there.

12
00:00:46,440 --> 00:00:51,410
So here we will have two locations of type, whatever you have mentioned here.

13
00:00:51,420 --> 00:00:52,380
So it's an integer.

14
00:00:52,380 --> 00:00:58,580
So to the weights I'm taking here now, these are total 11 location and assuming that the starting at

15
00:00:58,590 --> 00:01:05,489
us is 200, so, so on to the weights I have increased in the last bytes addresses two hundred and twenty

16
00:01:05,489 --> 00:01:06,430
two and twenty three.

17
00:01:06,960 --> 00:01:13,410
Now the problem is how the elements of two dimensional array are stored in single dimensional, how

18
00:01:13,410 --> 00:01:14,520
the mappings are done.

19
00:01:15,300 --> 00:01:17,150
So there are two methods of mapping.

20
00:01:17,520 --> 00:01:25,080
First, what this rule means of mapping, and second one is column major mapping.

21
00:01:25,590 --> 00:01:32,010
So these are the two representations of our two Deery mapped on a single dimension.

22
00:01:33,780 --> 00:01:40,040
So we call it as ROADM mapping or Romeijn representation columns of mapping or column representation.

23
00:01:40,320 --> 00:01:41,670
So we learn about both.

24
00:01:41,700 --> 00:01:45,220
So first, let us look at how ROADM mapping is done.

25
00:01:45,750 --> 00:01:49,490
Let us look at major mapping symposium that we have.

26
00:01:49,490 --> 00:01:54,660
A two dimensional environments are organized like this, but the reality is they are stored in a single

27
00:01:54,660 --> 00:01:56,540
dimension and how they are stored.

28
00:01:56,760 --> 00:02:00,500
So they are stored a role, vital role, vital.

29
00:02:00,510 --> 00:02:03,450
So environmental mapping, the elements are stored.

30
00:02:03,450 --> 00:02:05,790
Robledo So I will copy these elements.

31
00:02:05,790 --> 00:02:10,710
Robledo So forceful elements are zero zero zero one zero two zero three, so I will write them.

32
00:02:10,710 --> 00:02:12,860
Here is zero zero.

33
00:02:12,880 --> 00:02:18,870
This the first value in the first two zero one zero two and zero three.

34
00:02:19,320 --> 00:02:23,020
So this is rule number zero.

35
00:02:23,460 --> 00:02:25,310
This is index zero.

36
00:02:25,620 --> 00:02:29,080
So I have completed the first rule that whose index is zero.

37
00:02:29,520 --> 00:02:32,780
Now I will write down the elements of next to me.

38
00:02:33,690 --> 00:02:39,950
This is a rule index one, then a last eight to zero.

39
00:02:41,280 --> 00:02:43,590
So this is through index two.

40
00:02:44,640 --> 00:02:52,000
So that's how the elements of that two dimensional uhry are modeled on a single dimension array, a

41
00:02:52,080 --> 00:02:52,940
roll by it all.

42
00:02:53,220 --> 00:02:55,670
So this is a rule major mapping.

43
00:02:56,100 --> 00:03:01,350
Now we need a formula that should be used by a compiler for accessing that two dimensional array.

44
00:03:02,280 --> 00:03:05,100
Let us take one example and frame our formula.

45
00:03:05,530 --> 00:03:11,490
Suppose I want to access an element at index of one to.

46
00:03:13,000 --> 00:03:17,170
This is the element, if one, two is present here.

47
00:03:19,040 --> 00:03:23,810
Now, in our program, we will be writing like this, like, say, we often come, go, I want one goes

48
00:03:23,810 --> 00:03:31,800
to the and then that is often comma to has to be converted in the form of an address by the compiler.

49
00:03:31,820 --> 00:03:36,100
So already we know that the addresses cannot be known that compile time.

50
00:03:36,110 --> 00:03:39,400
So Compiler wrote the formula for obtaining an address.

51
00:03:39,800 --> 00:03:47,270
So let us observe what should be the formula for getting the address of a location eight of.

52
00:03:48,390 --> 00:03:51,570
One to this is for Idris.

53
00:03:53,560 --> 00:04:00,290
Now, to reach here, that is one to one that is present here to reach each year the starting gate,

54
00:04:00,290 --> 00:04:08,500
this is 200, 200, then I should leave all the elements of one rule that is rule zero.

55
00:04:08,500 --> 00:04:11,890
I should leave all these elements for how many elements are there in the room?

56
00:04:11,900 --> 00:04:13,020
One, two, three, four.

57
00:04:13,270 --> 00:04:15,640
That is equal to number of columns.

58
00:04:15,760 --> 00:04:19,450
So I should leave four elements or four elements.

59
00:04:19,450 --> 00:04:24,730
I have left them 200 as this index.

60
00:04:24,910 --> 00:04:28,160
If I leave four elements that one, two, three, four.

61
00:04:28,540 --> 00:04:30,700
So here I was in the beginning of the first row.

62
00:04:30,940 --> 00:04:33,850
Now here and the beginning of the next room.

63
00:04:34,210 --> 00:04:35,360
That is the next one.

64
00:04:35,800 --> 00:04:36,410
So here.

65
00:04:36,740 --> 00:04:40,510
So from here, how many elements I should move further one, two elements.

66
00:04:40,810 --> 00:04:43,090
So I should move for the first two elements.

67
00:04:44,790 --> 00:04:52,180
Then this should be multiplied by there are too wide for each element because it's integer.

68
00:04:52,440 --> 00:04:53,330
So this is two.

69
00:04:53,820 --> 00:04:58,580
So this is 200 plus six in two.

70
00:05:00,000 --> 00:05:01,590
That is 212.

71
00:05:02,130 --> 00:05:03,840
Yes, I got the address.

72
00:05:06,050 --> 00:05:11,900
So I have just worked out by taking the addresses and the index and I got the address, that is two

73
00:05:11,900 --> 00:05:12,950
hundred twelve.

74
00:05:13,520 --> 00:05:15,740
Now, let us try for one more element.

75
00:05:15,950 --> 00:05:17,300
Let us take this element.

76
00:05:17,540 --> 00:05:20,630
Tucumán E of Tucumcari.

77
00:05:21,320 --> 00:05:22,640
So address of.

78
00:05:24,410 --> 00:05:27,200
Eight of two, three.

79
00:05:28,280 --> 00:05:33,690
So if you see that eight of two commentaries that address 222, so I should get this one.

80
00:05:34,400 --> 00:05:40,610
Now, let us follow the same method, first base address 200, then some here.

81
00:05:40,610 --> 00:05:42,530
I should skip two rows.

82
00:05:43,310 --> 00:05:51,250
So two rows means two in into four elements in each all C four elements and fourth elements.

83
00:05:51,280 --> 00:05:52,690
I should skip to all those.

84
00:05:52,700 --> 00:05:56,740
I will be in the beginning of this third row that is in next to.

85
00:05:57,350 --> 00:05:59,970
So it means earlier this was one in the four.

86
00:06:00,310 --> 00:06:09,260
Now this is two four plus one says if I I'm here then I skip one more, I'm here that I'm skipping four

87
00:06:09,260 --> 00:06:10,580
for element for each rule.

88
00:06:10,580 --> 00:06:11,390
Not from here.

89
00:06:11,390 --> 00:06:13,520
I should move by one, two, three elements.

90
00:06:13,520 --> 00:06:15,280
So that is three elements.

91
00:06:15,690 --> 00:06:17,800
This whole thing should be multiplied by two.

92
00:06:18,800 --> 00:06:24,680
So how much it gives this is four to eight plus three leverne into two 22.

93
00:06:24,800 --> 00:06:26,990
So this will be two hundred and twenty two.

94
00:06:27,290 --> 00:06:28,790
So yes, I got back twice.

95
00:06:29,900 --> 00:06:34,730
Now from these two examples, we are in a position to write a formula for it.

96
00:06:35,060 --> 00:06:45,680
So address of any index, eight of iji we take I n g for replacing the intercepts.

97
00:06:46,040 --> 00:06:50,540
So this is l not plus l not the starting address.

98
00:06:50,540 --> 00:06:54,230
That is the president's L zero location zero plus.

99
00:06:54,740 --> 00:06:56,300
This is I.

100
00:06:57,630 --> 00:07:02,650
Into a number of columns, so let us call this has em crossed.

101
00:07:02,690 --> 00:07:09,920
And so this is N plus this one s three third element.

102
00:07:09,930 --> 00:07:10,890
So that is Jane.

103
00:07:12,190 --> 00:07:14,370
And this whole thing multiplied by double.

104
00:07:15,280 --> 00:07:22,190
So this is the ROMELIO formula used by a compilers for obtaining the address of elements of two dimensional

105
00:07:22,190 --> 00:07:22,530
Larry.

106
00:07:24,170 --> 00:07:30,320
Two dimensional arrays are accessed using two indexes, but actually they are kept in a single dimensionally,

107
00:07:30,320 --> 00:07:32,930
which needs just one index or the address.

108
00:07:33,230 --> 00:07:35,000
So I didn't like this.

109
00:07:35,860 --> 00:07:42,700
So there's a whole room here, no one wanting see if all languages are lying in, this is starting from

110
00:07:42,700 --> 00:07:45,870
one on worse than how the formula looks like.

111
00:07:46,390 --> 00:07:50,770
So I'll just remove this and show you here if a language is allowing it.

112
00:07:50,920 --> 00:07:58,720
This is from AOF one, two, three, and one, two, four.

113
00:07:59,590 --> 00:08:02,490
I will not work out everything directly, every word on the formula.

114
00:08:02,620 --> 00:08:10,390
So the address of any location of IGY can be obtained by Elad.

115
00:08:10,400 --> 00:08:17,260
Plus see already we have seen it for single dimensionless ie minus one was used because the are starting

116
00:08:17,260 --> 00:08:17,950
for one on word.

117
00:08:17,960 --> 00:08:29,620
So here it will be ie minus one in two and plus G minus one also then multiplied by world size.

118
00:08:29,680 --> 00:08:32,049
That is the data type sites.

119
00:08:33,220 --> 00:08:39,130
So this is the formula C in C C++ Arianism starts from zero only.

120
00:08:39,340 --> 00:08:47,590
So C C++ uses this formula C language and C++ language follows Romeijn representation and they use this

121
00:08:47,590 --> 00:08:48,250
formula.

122
00:08:48,790 --> 00:08:53,610
But if any language is allowing integers from one onwards, then it has to use this formula.

123
00:08:54,400 --> 00:08:57,670
Now when it comes to two letters, count the number of operations done.

124
00:08:58,030 --> 00:08:59,700
One, two, three, four.

125
00:08:59,890 --> 00:09:03,790
And here, one, two, three, four, five, six.

126
00:09:04,180 --> 00:09:07,450
So two extra arithmetic operations are performed.

127
00:09:07,870 --> 00:09:12,370
So this formula will be a little slower or costly in terms of time.

128
00:09:12,670 --> 00:09:17,770
If you have many elements and the dimensions are very large, then accessing all the elements will be

129
00:09:17,770 --> 00:09:20,100
very time consuming using this formula.

130
00:09:20,500 --> 00:09:25,360
So that is the reason the languages like C C++ they started differs from zero only.

131
00:09:26,140 --> 00:09:32,120
They are only source only from zero onwards, so that the formula is little simpler than the other formula.

132
00:09:32,290 --> 00:09:36,970
Now next, let us frame a formula for column major mapping.

