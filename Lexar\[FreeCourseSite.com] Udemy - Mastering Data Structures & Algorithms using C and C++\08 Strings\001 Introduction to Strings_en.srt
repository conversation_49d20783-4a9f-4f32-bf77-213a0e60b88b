1
00:00:00,540 --> 00:00:06,570
In this section, we will learn about strengths, so first of all, in this video, I'll give you introduction

2
00:00:06,570 --> 00:00:09,290
to String's for giving introduction.

3
00:00:09,300 --> 00:00:13,690
I'll be talking about the character suits or aski goals.

4
00:00:13,950 --> 00:00:17,960
Next, I'll explain what does it mean by character array.

5
00:00:18,000 --> 00:00:23,740
Then I will explain string and we will learn how to create strings in C language.

6
00:00:25,170 --> 00:00:28,930
Let us start with characters that are characters.

7
00:00:28,950 --> 00:00:35,220
That is the set of characters that are supported by a programming language like C, C++ or any other

8
00:00:35,220 --> 00:00:35,720
language.

9
00:00:36,150 --> 00:00:40,680
So the set of characters supported by a language will be the same as the set of characters that are

10
00:00:40,680 --> 00:00:42,980
supported by any computer system.

11
00:00:43,840 --> 00:00:52,600
We know that computer system works on binary number system, so everything in computer is numbers then

12
00:00:52,600 --> 00:00:59,500
how they can support characters, so basically they don't support characters, then how we make them

13
00:00:59,500 --> 00:01:00,750
work on characters.

14
00:01:01,240 --> 00:01:05,580
We will define some set of numbers as characters.

15
00:01:05,590 --> 00:01:10,420
So it means for every character we define some numeric value.

16
00:01:10,720 --> 00:01:12,780
So we give some numeric codes.

17
00:01:13,210 --> 00:01:21,310
So yes, for the English alphabet, for every character, there are some codes defined and those codes

18
00:01:21,310 --> 00:01:23,050
are standard codes.

19
00:01:23,050 --> 00:01:30,130
Every electronic machine follows that same set of calls and those codes are called as American Standard

20
00:01:30,130 --> 00:01:32,290
Code for Information Interchange.

21
00:01:32,290 --> 00:01:35,140
That is ASCII code or ASCII codes.

22
00:01:35,950 --> 00:01:40,120
These codes are given by American National Standards Institute.

23
00:01:40,120 --> 00:01:43,480
That is ANZI and also it is ISO standard.

24
00:01:43,660 --> 00:01:51,670
So that's the reason every electronic device supposedly is code for English language and moreover for

25
00:01:51,670 --> 00:01:59,230
other national languages like Chinese and Japanese or Hindi for various other national languages.

26
00:01:59,230 --> 00:02:05,590
The codes are defined and those are a useful standard code and those codes are called US unique codes.

27
00:02:05,950 --> 00:02:08,889
So we will be learning about ASCII code first.

28
00:02:08,889 --> 00:02:12,340
Then I will explain you a little bit about Unicode also.

29
00:02:12,850 --> 00:02:17,740
So ASCII codes are for English language now how the codes are defined.

30
00:02:18,130 --> 00:02:23,910
See, for every symbol, for every letter or character, there is a code available.

31
00:02:24,220 --> 00:02:26,460
So let us look at English alphabets.

32
00:02:26,590 --> 00:02:38,770
So for capital E uppercase e ascii gordis sixty five then for capital B it is 66, so 67 and so on.

33
00:02:39,040 --> 00:02:43,110
Like this up to Capital Z and the capital that it is 90.

34
00:02:43,720 --> 00:02:48,430
So these are uppercase letters and the codes are starting from sixty five up to 90.

35
00:02:48,580 --> 00:02:51,850
There are total 36 alphabets in English language.

36
00:02:51,850 --> 00:02:55,850
So these are twenty six codes for lowercase alphabets.

37
00:02:55,870 --> 00:03:04,120
Also as he goes out there that is ninety seven for lowercase E then ninety eight for lowercase B.

38
00:03:05,410 --> 00:03:10,960
Ninety nine and so on, and Lorqess, that is to.

39
00:03:12,500 --> 00:03:20,660
So these are four alphabets, English language letters, then, for numerical symbols like zero, one,

40
00:03:20,660 --> 00:03:21,080
two, three.

41
00:03:21,080 --> 00:03:23,030
Also ASCII codes are there.

42
00:03:23,240 --> 00:03:25,180
So what does that code for zero.

43
00:03:25,400 --> 00:03:29,030
Is 48, then for one it is forty nine.

44
00:03:29,210 --> 00:03:32,480
For two it is 50 and so on.

45
00:03:32,780 --> 00:03:34,850
Last digits nine.

46
00:03:35,000 --> 00:03:36,980
So it is fifty seven.

47
00:03:38,590 --> 00:03:42,410
So these are the codes for numerical symbols.

48
00:03:42,790 --> 00:03:49,380
Now, basically, if you see what all the symbols that you find on keyboard that forms a character set

49
00:03:49,660 --> 00:03:53,940
and for every symbol on the keyboard, there is some ASCII code available.

50
00:03:54,430 --> 00:03:57,970
Now, the other symbols are remaining like special characters.

51
00:03:59,480 --> 00:04:02,630
Like opening bracket, closing bracket Gomaa.

52
00:04:04,460 --> 00:04:06,620
Questionmark, plus minus.

53
00:04:07,520 --> 00:04:14,890
And star, so there are many special characters of our special characters are also symbols are that

54
00:04:14,960 --> 00:04:17,470
generally we work with these symbols.

55
00:04:17,510 --> 00:04:23,820
I have written the ASCII calls and you should remember these ASCII calls for uppercase English alphabets,

56
00:04:23,840 --> 00:04:33,530
lowercase, as well as these numeric symbols and also for enter ASCII could stand for spacebar.

57
00:04:33,710 --> 00:04:40,300
ASCII code is 13 and for escape ASCII called is 27.

58
00:04:41,090 --> 00:04:44,150
So these will also be helpful if you can remember them.

59
00:04:44,990 --> 00:04:47,450
So this is a set of ASCII calls.

60
00:04:47,900 --> 00:04:53,990
So if you see that as equals, I have given that this is forty eight to fifty seven, then next to sixty

61
00:04:53,990 --> 00:04:56,810
five to thousand ninety seven wanted to go.

62
00:04:57,260 --> 00:05:00,540
So actually from where they are starting and where they are ending.

63
00:05:00,560 --> 00:05:10,400
So that is important as it goes are starting from zero to one 127 total 128 ASCII calls are their total

64
00:05:10,400 --> 00:05:18,830
128 that are starting from zero to 137 to represent these as he calls to represent any one symbol.

65
00:05:19,160 --> 00:05:22,900
Seven bits are sufficient, seven bits are sufficient.

66
00:05:23,180 --> 00:05:24,320
That is binary bits.

67
00:05:24,770 --> 00:05:28,000
I guess you know how binary numbers are stored.

68
00:05:28,010 --> 00:05:31,130
So you need binary digits or binary bits.

69
00:05:31,130 --> 00:05:32,570
So seven digits are enough.

70
00:05:33,950 --> 00:05:36,840
Like Dupa seven is one twenty eight.

71
00:05:36,980 --> 00:05:39,850
So it ranges from zero to 127.

72
00:05:40,100 --> 00:05:43,420
So only seven bits are sufficient for ASCII codes.

73
00:05:44,390 --> 00:05:51,230
But while I look at the memory in main memory that is in the RAM, seven bits are not and look at that

74
00:05:51,470 --> 00:05:53,230
minimum, one by dislocates.

75
00:05:53,240 --> 00:06:00,020
So that's why a character that is ASCII coded, distorted one byte in computer memory.

76
00:06:00,470 --> 00:06:06,240
Nardella a little bit about Unicode like Unicode are for all the language.

77
00:06:06,270 --> 00:06:09,890
So I see codes become the subset of Unicode.

78
00:06:09,890 --> 00:06:12,140
So like English is also one of the language.

79
00:06:12,380 --> 00:06:14,690
So it becomes a subset of Unicode.

80
00:06:15,020 --> 00:06:19,340
Unicode takes two bytes of memory, two bytes.

81
00:06:19,340 --> 00:06:21,260
That is 16 words.

82
00:06:23,040 --> 00:06:30,770
Right, so because it is supporting all national languages and this can be represented in the form of

83
00:06:31,160 --> 00:06:40,070
hexadecimal codes, so hexadecimal codes are represented in Forbess, so these are represented in four

84
00:06:40,440 --> 00:06:42,450
forwards hexadecimal.

85
00:06:43,490 --> 00:06:44,910
That is four for 16.

86
00:06:45,140 --> 00:06:52,520
So these ASCII codes are represented in hexadecimal form in four digits of hexadecimal, like, for

87
00:06:52,520 --> 00:06:56,240
example, C zero three five.

88
00:06:56,270 --> 00:07:02,120
So these are four digits of hexadecimal or C zero three.

89
00:07:02,210 --> 00:07:05,150
So this is four digits of hexadecimal.

90
00:07:05,450 --> 00:07:09,130
So unique codes are represented in the form of hexadecimal.

91
00:07:09,410 --> 00:07:15,740
So you can go to a website called Unicode Dot Walji, Unicode dot org, you can visit.

92
00:07:16,070 --> 00:07:22,940
And if you look at the charts for the codes of languages, you can find codes for various languages

93
00:07:22,940 --> 00:07:23,480
available.

94
00:07:24,110 --> 00:07:25,610
And same in Google.

95
00:07:25,610 --> 00:07:30,110
If you search for ASCII call, you can find because there are a lot of websites giving the information

96
00:07:30,110 --> 00:07:30,740
about this one.

97
00:07:31,760 --> 00:07:34,940
So this is all about asking courts now.

98
00:07:34,990 --> 00:07:40,100
Next, let us understand how our characters are represented and what is character, and then we will

99
00:07:40,100 --> 00:07:41,090
learn about strengths.

100
00:07:41,420 --> 00:07:46,340
Let us see how to declare a character type variable in C and C++.

101
00:07:48,330 --> 00:07:57,100
Dr. Titus Carr thought child or carries the day to day and suppose I declared a variable name as a temp,

102
00:07:57,700 --> 00:08:02,050
not as a temp, as the variable of type character, how much memory it consumes.

103
00:08:02,350 --> 00:08:04,170
It takes one bite of memory.

104
00:08:04,570 --> 00:08:06,950
Then what is the data present here?

105
00:08:07,210 --> 00:08:08,560
There is nothing present here.

106
00:08:08,560 --> 00:08:10,180
So temp is just one bite.

107
00:08:10,390 --> 00:08:15,060
And if we want to store something in that, then I can initialize it with the character.

108
00:08:15,250 --> 00:08:17,580
Let us see a capital E..

109
00:08:17,890 --> 00:08:20,370
So it is stored here.

110
00:08:20,740 --> 00:08:28,860
See, for giving a character constant, it must be in single code and we can give only a single alphabet.

111
00:08:29,140 --> 00:08:39,220
So for example, if I write temp assign A as well as B and goals, this is wrong and if I write A simply

112
00:08:39,220 --> 00:08:40,570
a result wrong.

113
00:08:40,809 --> 00:08:46,200
If I write temp A, if I use a double Gord's then also it's wrong.

114
00:08:46,510 --> 00:08:51,550
So it should be in single calls and we should have only one single alphabet then that will be stored

115
00:08:51,550 --> 00:08:51,850
here.

116
00:08:53,340 --> 00:08:57,870
Now, what actually is a story in the memory, actually inside the memory value?

117
00:08:57,900 --> 00:09:00,640
Sixty five is stored at 65.

118
00:09:01,020 --> 00:09:06,670
It's not a right is not represented in computer memory, 65 years old.

119
00:09:07,320 --> 00:09:10,460
Then next, how to print this alphabet.

120
00:09:10,710 --> 00:09:19,190
I can use a printer for printing that alphabet, print F percentile C and stamp that variable, name

121
00:09:19,230 --> 00:09:19,560
them.

122
00:09:21,090 --> 00:09:22,370
Not the value of temblors.

123
00:09:22,380 --> 00:09:23,090
Sixty five.

124
00:09:23,100 --> 00:09:30,150
So actually print should print sixty five, but we have given our control character as a C so it will

125
00:09:30,150 --> 00:09:34,530
print a letter A on the screen instead of C.

126
00:09:34,530 --> 00:09:38,910
If I make it as a D then decimal number that is integer type.

127
00:09:39,090 --> 00:09:41,600
So it will display sixty five on the screen.

128
00:09:42,090 --> 00:09:48,270
So with this you can understand basically the sixty five as we said, that we want this in character

129
00:09:48,270 --> 00:09:48,660
form.

130
00:09:48,660 --> 00:09:51,240
So print the full print button a.

131
00:09:52,620 --> 00:10:01,890
Then same way in C++, if we see out temp, then CEO knows that this is a character type variable,

132
00:10:02,080 --> 00:10:03,570
it is declared as character.

133
00:10:03,720 --> 00:10:05,720
So that is the reason it will display.

134
00:10:06,000 --> 00:10:07,680
So Bodies' will display.

135
00:10:07,920 --> 00:10:11,160
So this is a method of displaying our character.

136
00:10:11,640 --> 00:10:17,310
So so this is about the character declaration of a character and initialization of a character.

137
00:10:17,520 --> 00:10:20,310
And even I can initialize it here directly.

138
00:10:20,700 --> 00:10:21,000
Right.

139
00:10:21,060 --> 00:10:25,140
Instead of initializing it later, I can declare as well as initialize it.

140
00:10:26,120 --> 00:10:33,620
Now, next thing we will see, what is character Holligan, creator array of characters for creating

141
00:10:33,620 --> 00:10:36,500
an array of characters just like any other.

142
00:10:37,370 --> 00:10:43,850
I will take a Ima's X and this is and I mentioned the sites.

143
00:10:44,570 --> 00:10:48,020
So array of sci fi characters will be created.

144
00:10:48,380 --> 00:10:50,510
Now, can I initialize this one?

145
00:10:50,540 --> 00:10:51,080
Yes.

146
00:10:51,500 --> 00:10:53,220
So I will take the same name here.

147
00:10:53,240 --> 00:11:01,220
I will not change the name so I can set the name as X and five and if I want to initialize I can give

148
00:11:01,400 --> 00:11:02,070
alphabet's.

149
00:11:02,150 --> 00:11:07,910
This is a declaration plus initialization for an array will be created with the name X.

150
00:11:12,080 --> 00:11:19,490
And it will have alphabet's, a, b, c, d, e, Ascoli that as he calls the president that are in

151
00:11:20,010 --> 00:11:25,120
that third method, I can create an array without giving any size.

152
00:11:25,400 --> 00:11:32,810
So a B so same type of work will be created of size five and initialized with all these alphabets.

153
00:11:32,820 --> 00:11:37,460
I have not mentioned the size size will be taken depending on the number of alphabets I'm assigning.

154
00:11:38,540 --> 00:11:39,710
And one more method.

155
00:11:39,710 --> 00:11:48,110
I can create an array by either mentioning or not mentioning the size I will write sixty five, 66 six.

156
00:11:49,670 --> 00:11:54,620
So these are the ASCII code for these alphabets for the same thing will appear same thing same or they

157
00:11:54,620 --> 00:12:00,170
will be created because sixty five is for capital letter A and sixty six for B and so on.

158
00:12:00,380 --> 00:12:01,850
So Samori will be created.

159
00:12:01,860 --> 00:12:08,430
So all these three methods, same array will be created and I will create one more ready algorithm,

160
00:12:08,430 --> 00:12:11,540
not a God of size of five.

161
00:12:12,080 --> 00:12:15,350
And I will mention only two alphabets.

162
00:12:17,600 --> 00:12:23,960
Right, so now the area that is created only alphabet A B school.

163
00:12:23,990 --> 00:12:26,170
So these are all Zeitels initialized with zero.

164
00:12:26,480 --> 00:12:32,300
So the set of characters are still here only, but the array sizes, the total five, I have only two

165
00:12:32,300 --> 00:12:32,880
alphabets.

166
00:12:32,880 --> 00:12:34,170
So, yes, it is possible.

167
00:12:34,550 --> 00:12:38,340
So then I should tell where I have kept the characters.

168
00:12:38,780 --> 00:12:40,390
So it is just like a normal integer.

169
00:12:40,970 --> 00:12:47,090
RSI is maybe 100, but you are having only 15 elements or 10 elements and that is the same as that one

170
00:12:47,450 --> 00:12:49,320
array of characters of size five.

171
00:12:49,640 --> 00:12:52,210
But I have only two valid alphabets.

172
00:12:52,250 --> 00:12:55,880
Rest of the places are empty, vacant, not in use.

173
00:12:56,030 --> 00:12:57,700
This is possible now.

174
00:12:57,750 --> 00:13:04,160
Next, I will carry on with the same example here and I will explain you what are strings.

175
00:13:04,400 --> 00:13:11,510
I want to store a name in an array so I will create an array of characters.

176
00:13:11,720 --> 00:13:20,240
Then let us caldari the mass name and let us give it does say that Steane and here I will store mean

177
00:13:21,080 --> 00:13:24,590
name is John Aryal, size tennis creator.

178
00:13:24,590 --> 00:13:29,720
And in that John is a store for Geotech and only for Alphabet's.

179
00:13:30,060 --> 00:13:32,440
Rest of them are all zeroes.

180
00:13:32,900 --> 00:13:39,730
Yes, this is character or any of the characters array of characters.

181
00:13:39,740 --> 00:13:40,150
Right.

182
00:13:42,550 --> 00:13:49,930
So this is a trio of characters having named John, so array of characters is nothing but a string,

183
00:13:50,360 --> 00:13:50,820
right?

184
00:13:50,890 --> 00:13:57,070
It's a string for string names for storing the words or sentences or paragraphs.

185
00:13:57,070 --> 00:13:58,200
We need string.

186
00:13:58,810 --> 00:14:00,520
It is nothing but a set of characters.

187
00:14:00,910 --> 00:14:03,700
So string is nothing but a set of characters, basically.

188
00:14:04,060 --> 00:14:08,010
So a name as a string, but not the problem.

189
00:14:08,020 --> 00:14:09,700
Let us understand more about string.

190
00:14:10,240 --> 00:14:16,450
See here, the size of another is a 10, but the string size is only four alphabets.

191
00:14:17,110 --> 00:14:20,480
Then how do I know where this string is ending?

192
00:14:21,040 --> 00:14:26,410
So that is the important thing when the size of an array may be larger, but you are having only part

193
00:14:26,410 --> 00:14:31,150
of it as a string, then we need to know where we have a string.

194
00:14:33,930 --> 00:14:40,470
So you should know that the length of a string or we should have the end point of a string, so yes,

195
00:14:40,470 --> 00:14:48,070
in C and C++, it is a mark of a null character that is zero zero.

196
00:14:48,330 --> 00:14:54,020
This is an old symbol or a string delimiter or end of the string character.

197
00:14:54,180 --> 00:15:04,350
It is called string delimiter or end of the string character or another character, or also we call

198
00:15:04,350 --> 00:15:06,330
it string Karmelita.

199
00:15:06,630 --> 00:15:07,970
String Terminator.

200
00:15:11,130 --> 00:15:11,450
Right.

201
00:15:11,910 --> 00:15:14,610
So that's what this is used to show.

202
00:15:14,610 --> 00:15:15,990
The end of a string.

203
00:15:15,990 --> 00:15:17,170
End of string marker.

204
00:15:17,460 --> 00:15:23,910
So in C C++ strings are delimited the limit at the null character that is zero.

205
00:15:24,990 --> 00:15:31,530
But whereas in other language like Java strings will not have a zero, then how do you know how many

206
00:15:31,530 --> 00:15:32,590
alphabets are valid?

207
00:15:32,820 --> 00:15:36,060
So that is known with the help of a lend.

208
00:15:36,390 --> 00:15:37,770
So this is Lent.

209
00:15:38,280 --> 00:15:45,270
Lent is how much for in Java string Lent is known or the size of a string is known by its length.

210
00:15:45,540 --> 00:15:52,570
But in C C++ the size of a string is known by finding a termination character that is an old character.

211
00:15:52,980 --> 00:15:55,560
So strings are limited by Celesio.

212
00:15:55,890 --> 00:16:01,320
Now this is just an array of characters how to make it as a string in c C++.

213
00:16:01,470 --> 00:16:04,070
I must write slash Z also.

214
00:16:04,350 --> 00:16:08,130
So take a good and slash zero and close the.

215
00:16:09,030 --> 00:16:10,610
So this is within ghoul's.

216
00:16:10,980 --> 00:16:13,130
So John dominated by zero.

217
00:16:13,200 --> 00:16:14,560
So this becomes a string.

218
00:16:14,580 --> 00:16:20,520
Now without this it is just an array of characters, but adding slightly to it becomes a string.

219
00:16:20,530 --> 00:16:24,210
So that's the difference between the array of characters and a string.

220
00:16:24,400 --> 00:16:28,440
Then you have words or names or names of things or places.

221
00:16:28,470 --> 00:16:30,840
Then you can take it as a string.

222
00:16:31,050 --> 00:16:37,000
Now let's see what other methods of creating or declaring a string and also initializing it.

223
00:16:37,440 --> 00:16:38,690
So let us see the methods.

224
00:16:39,000 --> 00:16:44,080
See, this is the first method for declaring as well as initializing a string.

225
00:16:44,400 --> 00:16:51,840
Second thing, I can declare a string without signs and I will use same name.

226
00:16:59,890 --> 00:17:05,170
This is of the just the differences have not given the size, then what will be the size of this?

227
00:17:05,560 --> 00:17:09,180
One, two, three, four, five, the size of an is five.

228
00:17:09,400 --> 00:17:15,609
So for storing four alphabets of a word or a name, I must take five spaces so that I should provide

229
00:17:15,609 --> 00:17:20,200
a space for zero also so slashes it also consumes space.

230
00:17:20,650 --> 00:17:30,280
The next method of declaring or initializing a string sinem not the same as it is a string I can write

231
00:17:30,280 --> 00:17:32,830
down in double codes.

232
00:17:32,840 --> 00:17:33,700
So yes.

233
00:17:34,210 --> 00:17:41,310
So only John is written as it is in double codes, so slash zero will be automatically included.

234
00:17:41,320 --> 00:17:43,060
C compiler will take care of it.

235
00:17:44,030 --> 00:17:45,380
So we don't have to mention.

236
00:17:46,310 --> 00:17:49,280
So this looks better than these two matters.

237
00:17:49,310 --> 00:17:52,990
Yes, strings are enclosed in double quotes, right?

238
00:17:53,360 --> 00:17:57,710
That next one more method of creating a string.

239
00:18:03,800 --> 00:18:05,330
This is a character pointer.

240
00:18:05,360 --> 00:18:08,760
Yes, this is a character pointer, then where does the string will be created?

241
00:18:08,780 --> 00:18:15,230
Now, the speciality of this one is this A string will be automatically created in him, though we did

242
00:18:15,230 --> 00:18:18,280
not use a Mallott function or we did not like a new.

243
00:18:18,590 --> 00:18:21,140
But this is implicitly a look at it in him.

244
00:18:21,410 --> 00:18:25,830
Then where these arrays are created, all these areas are created inside the stack.

245
00:18:25,850 --> 00:18:26,850
So what does it mean?

246
00:18:27,080 --> 00:18:29,550
We have already seen this memory structure, right?

247
00:18:29,780 --> 00:18:32,500
So here the program will be there, the code will be there.

248
00:18:32,720 --> 00:18:34,880
And this is the stack and this is heap.

249
00:18:34,880 --> 00:18:35,180
Right.

250
00:18:35,480 --> 00:18:38,540
So this is stack and heap more.

251
00:18:38,630 --> 00:18:42,530
If I take all these arrays, all those areas are created here.

252
00:18:48,140 --> 00:18:52,270
They are treated like this, if I take the example of this one, then what about this?

253
00:18:52,430 --> 00:18:53,480
I'll just change the name.

254
00:18:53,480 --> 00:18:58,240
I'll make it as just an and this is inside heap, right.

255
00:18:58,460 --> 00:18:59,950
So I have seen the name.

256
00:19:00,440 --> 00:19:09,110
And now if you look at this one, then this is an and this is string is created inside heap along with

257
00:19:09,110 --> 00:19:14,420
the slab and is pointing there so and as a character pointer pointing on this string.

258
00:19:15,410 --> 00:19:20,570
So this is the difference, so if you create these these are all created inside the stack, right.

259
00:19:20,870 --> 00:19:24,550
Which is directly accessible to a program, this is created in here.

260
00:19:24,710 --> 00:19:27,230
So this indirectly accessible using a pointer.

261
00:19:27,680 --> 00:19:33,610
So this is created for automatic compiler will create this string inside.

262
00:19:33,620 --> 00:19:35,470
Hape and Pointer will point there.

263
00:19:36,080 --> 00:19:40,570
Now, let me talk about the fancy old or scanner a little bit about this one.

264
00:19:40,580 --> 00:19:44,730
So to learn how to display this string and how that works.

265
00:19:44,750 --> 00:19:45,770
This is a string.

266
00:19:45,770 --> 00:19:46,960
I'm taking this a string.

267
00:19:46,970 --> 00:19:54,200
The RFID is and it is having a string of size four and including zero is also there that if I use a

268
00:19:54,200 --> 00:20:03,370
printer to print, if I want to printed this entire string that is holding a complete name, so say

269
00:20:03,380 --> 00:20:03,820
yes.

270
00:20:04,190 --> 00:20:12,250
So this control character is just for string percentile percentile as then here I should just mention

271
00:20:12,380 --> 00:20:13,120
name.

272
00:20:13,700 --> 00:20:15,370
So that is the name of an array.

273
00:20:15,700 --> 00:20:16,070
Yes.

274
00:20:16,070 --> 00:20:18,110
You can just give the name of an array.

275
00:20:18,620 --> 00:20:22,910
A string will be displayed so it is not possible for any other type of her.

276
00:20:22,940 --> 00:20:27,920
Remember this, it's not for integer or float like just you are giving the name and it is displaying

277
00:20:27,920 --> 00:20:34,670
the entire original here print of n still where it has to print alphabets because it will be gone printing

278
00:20:34,670 --> 00:20:38,860
the alphabet from the screen until it finds slash zettl.

279
00:20:39,020 --> 00:20:39,320
Right.

280
00:20:39,680 --> 00:20:45,270
So this will print this complete name and it will stop when it reaches zero.

281
00:20:45,290 --> 00:20:52,630
So how it will do it will be first G then or the edge then and then it will stop.

282
00:20:53,450 --> 00:20:53,740
Right.

283
00:20:54,290 --> 00:21:02,060
So that's how print works then if suppose I want to read some new name, another name and this one so

284
00:21:02,060 --> 00:21:06,500
I can use caniff n ganef person tieless again.

285
00:21:06,510 --> 00:21:09,760
So that is for reading a string and give the name of Inari.

286
00:21:10,490 --> 00:21:11,000
Yes.

287
00:21:11,000 --> 00:21:18,290
Skytrans can also read a string from the keyboard and store those alphabets there followed by zero.

288
00:21:18,590 --> 00:21:21,890
So print defense can have both are dependent on that.

289
00:21:22,640 --> 00:21:29,200
So all the library functions of C language that are meant for strings are dependent on slagel.

290
00:21:29,210 --> 00:21:30,340
That is not a character.

291
00:21:30,680 --> 00:21:33,050
The work of on string still defined slightly.

292
00:21:33,590 --> 00:21:36,590
So this is ganef if you type something from the keyboard.

293
00:21:37,840 --> 00:21:42,280
Like, if you're typing David, then David will be stored here like this.

294
00:21:48,480 --> 00:21:55,380
Then followed by that, it will add to this skinful store by itself.

295
00:21:55,860 --> 00:22:04,140
So we don't have time to also from the keyboard just to type it like this and hit enter it will store

296
00:22:04,140 --> 00:22:04,410
that.

297
00:22:04,920 --> 00:22:10,500
Then similarly, CNN code also works in the same way up on the string.

298
00:22:10,560 --> 00:22:12,380
That is character evidence.

299
00:22:12,720 --> 00:22:14,430
You know, one last thing.

300
00:22:15,180 --> 00:22:17,660
This is caniff, not one more lasting.

301
00:22:17,910 --> 00:22:21,350
This is kind of useful for reading just one word string.

302
00:22:21,510 --> 00:22:24,570
If you have multiple words, it cannot read them.

303
00:22:25,230 --> 00:22:30,600
If you are writing a string, like suppose you are writing Taj Mahal, right?

304
00:22:30,870 --> 00:22:32,610
Then there is a space in between.

305
00:22:32,790 --> 00:22:37,230
So if you type this, then Skynet will just take foreswore.

306
00:22:37,230 --> 00:22:38,630
It will skip the second word.

307
00:22:38,640 --> 00:22:40,370
It cannot take the remaining words.

308
00:22:40,710 --> 00:22:47,760
So if you want to read all the words till you hit, enter right here and then you can use a function

309
00:22:47,760 --> 00:22:51,220
called Get US and give the regime.

310
00:22:51,630 --> 00:22:56,280
So get string or get S is a function for reading it.

311
00:22:56,290 --> 00:22:58,410
Interesting until you hit enter.

312
00:22:59,190 --> 00:23:01,560
So the problem of Scalf can be overcome.

313
00:23:01,560 --> 00:23:02,640
But get this.

314
00:23:03,780 --> 00:23:08,430
So that's one of the things we have learned about as he calls underrepresentation of strength, we have

315
00:23:08,430 --> 00:23:14,400
learned about the character of them in the coming videos will look on operations, upon strengths,

316
00:23:14,410 --> 00:23:16,020
various operations we will discuss.

