1
00:00:00,500 --> 00:00:02,930
Next Matrixes Toeplitz Matics.

2
00:00:04,100 --> 00:00:08,119
So this is a totally semantics of order, five five five zero five.

3
00:00:09,220 --> 00:00:16,870
Let us observe the metrics this is having all non-zero elements, then what is the property of the Cmax?

4
00:00:17,380 --> 00:00:22,210
So if you observe the elements in our diagonal are same.

5
00:00:23,560 --> 00:00:32,400
To almost our scene and the elements in Lord are Applegate, this are also seem these are seen upper

6
00:00:32,560 --> 00:00:35,020
diagonal again, they are seen, they're all the same.

7
00:00:35,470 --> 00:00:38,800
So elements in our diagonal are same.

8
00:00:40,260 --> 00:00:41,100
So it means.

9
00:00:43,150 --> 00:00:49,030
Five Gomo five Element is famous for common for that same as three commentary.

10
00:00:50,430 --> 00:00:58,320
So from boredom, I have started then if I take this element five Akama for that is simantov for commentary

11
00:00:59,130 --> 00:01:02,010
C five minus one and four minus one three.

12
00:01:02,550 --> 00:01:11,500
So it means that if a mattocks is having elements such that I'm of IG is Seamans and more.

13
00:01:12,500 --> 00:01:15,110
I am minus one and Jim minus one.

14
00:01:16,210 --> 00:01:18,820
So this is the same as this one, this is same, Anderson.

15
00:01:18,850 --> 00:01:20,180
So which is the original element?

16
00:01:20,200 --> 00:01:21,220
This is the original element.

17
00:01:21,230 --> 00:01:23,510
We can say this is the original meaning for this one.

18
00:01:23,530 --> 00:01:24,510
This is the original element.

19
00:01:25,210 --> 00:01:29,940
So when the elements are repeating, there are duplicates and there is some pattern followed, like

20
00:01:29,980 --> 00:01:31,630
this is a pattern followed then.

21
00:01:31,690 --> 00:01:34,360
You don't have to store all non-zero elements.

22
00:01:35,090 --> 00:01:36,770
Then how many elements are sufficient?

23
00:01:37,120 --> 00:01:43,180
It is sufficient to store a row of elements and a column of elements.

24
00:01:43,690 --> 00:01:46,530
This is sufficient and this is a common element.

25
00:01:46,540 --> 00:01:51,330
So we have to store only first and first column then.

26
00:01:51,400 --> 00:01:52,750
How many elements are there?

27
00:01:52,990 --> 00:01:53,960
Three elements.

28
00:01:53,980 --> 00:02:00,430
If I dig first always is having an element that is N that is five then column.

29
00:02:00,430 --> 00:02:02,890
I don't have to take this again because this is common.

30
00:02:02,900 --> 00:02:06,060
So minus one plus and minus four elements.

31
00:02:06,820 --> 00:02:09,430
So only these many elements are sufficient.

32
00:02:11,460 --> 00:02:16,270
So let us see how to represent this one, so I'll take on Uhry and store all these elements.

33
00:02:16,650 --> 00:02:18,960
So what should be the site of another site of an array?

34
00:02:18,960 --> 00:02:21,470
Should be five plus five, 10, ten minus four nine.

35
00:02:21,840 --> 00:02:23,820
So I will take an array of size nine.

36
00:02:24,660 --> 00:02:29,880
So I have taken an array of eight and nine now how to represent these elements so I don't have to store

37
00:02:29,880 --> 00:02:34,320
all the elements hostile, restoring first element, then let us talk elements.

38
00:02:34,420 --> 00:02:35,180
That is sufficient.

39
00:02:35,490 --> 00:02:41,250
So all elements, two, three, four, five, six, four, two, three, four, five, six.

40
00:02:41,280 --> 00:02:42,060
This is rule.

41
00:02:43,360 --> 00:02:45,850
And First Amendment seven, eight, nine, 10.

42
00:02:46,150 --> 00:02:52,770
So these are seven, eight, nine, 10, and this is column so mapping of these elements.

43
00:02:52,780 --> 00:02:59,170
I have done know what should be the formula for retrieving the elements, see if the elements are in

44
00:02:59,170 --> 00:03:08,110
the upper triangular part means these elements, these elements that are in the room here and these

45
00:03:08,110 --> 00:03:11,330
lower triangular elements, they are in a column.

46
00:03:12,520 --> 00:03:15,250
So how do you identify the elements here?

47
00:03:15,280 --> 00:03:21,390
So here the conditions are such that I use less than a G, but including diagonal.

48
00:03:21,400 --> 00:03:23,220
So I use less than equal to G.

49
00:03:23,230 --> 00:03:26,210
It means the elements are of this upper triangle.

50
00:03:26,620 --> 00:03:35,470
So for any index of an element of I g then the formula we can come up.

51
00:03:35,670 --> 00:03:36,820
Case one is.

52
00:03:38,230 --> 00:03:45,880
If I is less than or equal to G means the elimination of triangle, so for example, I want to three,

53
00:03:46,170 --> 00:03:52,540
four or three comma, five to come on, three to come forward or to come five, any of these elements

54
00:03:52,540 --> 00:03:55,020
or two is smaller than three, four, five.

55
00:03:55,060 --> 00:03:58,110
Not so too is a smaller than three, four, five.

56
00:03:58,360 --> 00:04:04,870
So that's what I is less than equal, which means the elements are in upper triangular part of a matrix,

57
00:04:04,870 --> 00:04:06,400
so they belong to a room.

58
00:04:06,580 --> 00:04:08,340
So I should get an element from here.

59
00:04:08,800 --> 00:04:17,200
So I take some examples and show you suppose I want an element at eight of Tucumán four to come forward.

60
00:04:18,700 --> 00:04:21,130
To come forward is four, that is three.

61
00:04:21,130 --> 00:04:23,470
And that food is present here at two.

62
00:04:24,990 --> 00:04:30,120
So how are you going to get by index, so let us subtract four minus two.

63
00:04:30,150 --> 00:04:30,830
That is true.

64
00:04:31,920 --> 00:04:34,800
Then what about the element that is three comma?

65
00:04:36,400 --> 00:04:46,870
For that history and this one three comma for so three comma, four elementary, that one, so let us

66
00:04:46,870 --> 00:04:48,780
do for minus three is one.

67
00:04:49,150 --> 00:04:53,170
So it means if I subtract a G minus, I am getting there.

68
00:04:53,650 --> 00:04:58,030
So four case, one upper triangle, how we should treat one element.

69
00:04:58,030 --> 00:04:59,160
What should be the formula.

70
00:05:00,100 --> 00:05:07,390
See, these are all diagonal elements or diagonal elements means, i.e., minus G is equal to zero.

71
00:05:07,600 --> 00:05:13,000
So, yes, this element too is present at zero, then these elements three three, three.

72
00:05:13,030 --> 00:05:14,580
This is a minus G.

73
00:05:14,590 --> 00:05:17,230
If I do, this is minus one.

74
00:05:20,620 --> 00:05:26,660
And the element is to and it is that index element is three and it is an index one.

75
00:05:27,040 --> 00:05:27,840
So yes, one.

76
00:05:28,030 --> 00:05:29,740
So this I am getting minus one.

77
00:05:29,740 --> 00:05:32,260
So let us make it does G minus three.

78
00:05:32,290 --> 00:05:35,550
So this is a plus one that these elements that is four.

79
00:05:35,710 --> 00:05:39,500
So if you take T minus five, this is two for two.

80
00:05:39,610 --> 00:05:40,800
So the element is four.

81
00:05:41,020 --> 00:05:42,920
So it is proven that index of Thornlie.

82
00:05:43,120 --> 00:05:48,490
So this means I can obtain the index of any element in this array by doing J minus eight.

83
00:05:48,850 --> 00:05:50,590
So yes, index is.

84
00:05:53,000 --> 00:05:54,190
Jay minocycline.

85
00:05:55,800 --> 00:06:05,100
That's it then, second is what about the elements that are in lower triangle for kids, too, if I

86
00:06:05,100 --> 00:06:06,420
is greater than.

87
00:06:07,380 --> 00:06:13,250
So these elements then if I want this element seven seven.

88
00:06:13,620 --> 00:06:16,000
So if I do I minus G.

89
00:06:16,000 --> 00:06:16,980
Gee, this is fun.

90
00:06:18,510 --> 00:06:25,590
And here I am, minus G is a two I minus G is a three, so this is one and two and three.

91
00:06:25,740 --> 00:06:31,620
The elements of the regular one that the diagonal is to distinguish the three letters of the derivative

92
00:06:31,620 --> 00:06:32,640
starting from five.

93
00:06:32,910 --> 00:06:37,470
So first of all, I have to leave all the elements of this rule, then I can go there.

94
00:06:37,710 --> 00:06:39,030
So index will be.

95
00:06:41,110 --> 00:06:46,990
And plus, so leave five elements once we leave, five elements will be on five, and this should be

96
00:06:46,990 --> 00:06:51,350
zero wherever you are now, that location picked at zero.

97
00:06:51,370 --> 00:06:53,380
So if we are here, let us call it a zero.

98
00:06:53,860 --> 00:06:58,630
So that zero minutes here we get the next one from minus one we can do so.

99
00:06:58,630 --> 00:07:02,590
This is I minus G, minus one.

100
00:07:03,830 --> 00:07:07,020
So this is the index for lower triangular elements.

101
00:07:07,040 --> 00:07:09,500
So that's all about the complex matrix.

102
00:07:09,920 --> 00:07:14,100
So write the program and we will write a program for complex semantics.

103
00:07:14,120 --> 00:07:15,410
I'll be discussing the program.

