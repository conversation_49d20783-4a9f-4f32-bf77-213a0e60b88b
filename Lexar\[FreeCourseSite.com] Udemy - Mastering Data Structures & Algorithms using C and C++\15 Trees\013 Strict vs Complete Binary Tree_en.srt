1
00:00:00,420 --> 00:00:08,280
We have learned about strict binary tree and complete binary tree, let us compare them, see Stick

2
00:00:08,280 --> 00:00:11,610
by the tree is also called as a proper binary tree.

3
00:00:12,750 --> 00:00:22,590
The definition is every note can have either zero degree or degree to mince no children or two children.

4
00:00:24,780 --> 00:00:30,810
Then we complete one entry complete by increments one, it is represented in an array.

5
00:00:30,960 --> 00:00:34,020
There should not be any blank spaces in between the elements.

6
00:00:35,200 --> 00:00:41,990
Our other definition, if the height is etch, then up to hydrogen minus one, it is full loss level.

7
00:00:41,990 --> 00:00:43,930
The elements are filled from left to right.

8
00:00:45,590 --> 00:00:49,850
So strict is different, completely different, but.

9
00:00:50,830 --> 00:00:58,570
Some auteur's at some places you may find complete by military definition as a stigma.

10
00:00:59,140 --> 00:01:06,170
So it means stigma is also less complete than what you call to that one that is sometimes called as

11
00:01:06,220 --> 00:01:07,310
almost complete.

12
00:01:07,800 --> 00:01:11,680
So if this is called as complete, then this is called as almost complete.

13
00:01:12,250 --> 00:01:16,010
If you call this a strict, then you can call that one that's complete.

14
00:01:16,390 --> 00:01:18,220
So there is no fixed name for this one.

15
00:01:18,310 --> 00:01:19,660
So ask what are your requirements?

16
00:01:19,670 --> 00:01:21,270
You can call it with any name.

17
00:01:21,430 --> 00:01:23,760
No, let us compare their examples.

18
00:01:24,130 --> 00:01:26,620
See, this is an example of a strict binary tree.

19
00:01:26,860 --> 00:01:34,090
So Stigman increments every node can have either degree two or zero degree to this are zero is perfect.

20
00:01:34,810 --> 00:01:38,280
But at this Stroeve and a store in an array, there are blank spaces.

21
00:01:38,620 --> 00:01:43,620
So this binary tree is strict, but it is not complete.

22
00:01:44,260 --> 00:01:46,080
It is strict, but not complete.

23
00:01:46,360 --> 00:01:50,150
I prefer calling these names quite strict and complete.

24
00:01:50,440 --> 00:01:52,880
So this is strict but not complete.

25
00:01:53,100 --> 00:01:53,830
Complete means.

26
00:01:53,830 --> 00:01:55,950
There are blank spaces here that is not complete.

27
00:01:55,960 --> 00:01:56,910
Then what about this?

28
00:01:57,610 --> 00:02:01,050
This is having to agree to this having degree two degrees, zero degrees, zero zero.

29
00:02:01,360 --> 00:02:02,390
So this is strict.

30
00:02:03,100 --> 00:02:03,790
Yes.

31
00:02:03,790 --> 00:02:05,920
This is what I will complete.

32
00:02:06,220 --> 00:02:07,860
First element to last element.

33
00:02:07,870 --> 00:02:10,320
There are no blank spaces in between the elements here.

34
00:02:10,330 --> 00:02:11,260
We have spaces.

35
00:02:11,260 --> 00:02:12,700
You see two spaces.

36
00:02:12,940 --> 00:02:14,100
There are no blank spaces.

37
00:02:14,230 --> 00:02:15,920
So this is also complete.

38
00:02:16,000 --> 00:02:18,370
So this is strict, also complete also.

39
00:02:19,090 --> 00:02:22,660
So from the examples you can understand, I will draw a few more trees.

40
00:02:22,670 --> 00:02:25,420
Then we will see whether they are strict or complete or not.

41
00:02:25,420 --> 00:02:26,280
We will compare them.

42
00:02:26,830 --> 00:02:31,410
Tell me that strict or complete or nothing, what it is.

43
00:02:32,760 --> 00:02:38,970
This is not strict because this is having degree one, so there's not strict, but whether it is complete,

44
00:02:39,180 --> 00:02:47,560
let us store it in an area and see A, then B, C, B.C., then from left to right.

45
00:02:47,580 --> 00:02:47,850
Right.

46
00:02:47,880 --> 00:02:50,170
So these are not there's only these various.

47
00:02:50,460 --> 00:02:54,180
So from first element, the last element, there is no gap in between the elements.

48
00:02:54,210 --> 00:02:57,630
So this is complete but not strict.

49
00:02:58,110 --> 00:02:59,460
So strict.

50
00:03:00,330 --> 00:03:03,000
This is not strict, but it is complete.

51
00:03:03,390 --> 00:03:06,140
C This was strict but not complete.

52
00:03:06,330 --> 00:03:08,490
This was both strict as well as complete.

53
00:03:08,730 --> 00:03:11,510
That is complete but not strict.

54
00:03:12,060 --> 00:03:12,720
Then one more.

55
00:03:12,720 --> 00:03:15,810
I will take this one as it strict.

56
00:03:16,470 --> 00:03:19,610
This note is having just one child so it is not strict.

57
00:03:19,920 --> 00:03:21,900
Let us see whether it is complete or not.

58
00:03:21,910 --> 00:03:23,940
So I will store them in an area.

59
00:03:24,210 --> 00:03:32,350
So first is A, then left, right B C then from here these elements are missing blank blank then B and

60
00:03:32,350 --> 00:03:33,120
this is also missing.

61
00:03:33,480 --> 00:03:37,200
That is I would say though that is after that we don't need it.

62
00:03:37,830 --> 00:03:40,610
So you can see that there are blank spaces here.

63
00:03:40,860 --> 00:03:42,960
So it's neither strict nor complete.

64
00:03:43,230 --> 00:03:43,450
Right.

65
00:03:43,740 --> 00:03:45,540
So I have all types of examples.

66
00:03:45,660 --> 00:03:49,500
Only strict bought only complete nothing.

67
00:03:50,610 --> 00:03:52,570
So I have come to both of them now.

68
00:03:52,650 --> 00:03:57,120
Choice is yours, whatever the name you want, you can call them with that name and complete by the

69
00:03:57,450 --> 00:03:58,490
is more useful.

70
00:03:58,710 --> 00:04:03,790
We will be using it afterwards in one of the topics will be using complete bindery.

71
00:04:03,960 --> 00:04:07,220
This one, this one, this one will be using.

72
00:04:07,380 --> 00:04:09,840
This is very important because there are no blank spaces.

73
00:04:09,840 --> 00:04:11,820
Means we can use this in an.

