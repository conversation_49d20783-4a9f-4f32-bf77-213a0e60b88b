1
00:00:00,840 --> 00:00:04,410
Now, let us look at static versus dynamic array.

2
00:00:05,510 --> 00:00:13,300
So study means the size of an eraser static and the dynamically the size of an array is the dynamic.

3
00:00:14,150 --> 00:00:17,950
See, one scenario is created, exercice cannot be modified.

4
00:00:18,740 --> 00:00:23,330
So in our programs, when we declare an for example, I have a function.

5
00:00:23,460 --> 00:00:32,759
I mean, and inside this function, if I have declared an integer of size eight five, then out of five

6
00:00:32,780 --> 00:00:33,960
of five will be created.

7
00:00:34,940 --> 00:00:41,750
So the question is where it is created as a dimension inside the main function as a variable, just

8
00:00:41,750 --> 00:00:46,690
a vector variable than the memory for this array will be created inside stack.

9
00:00:47,390 --> 00:00:56,630
So an array of size five will be created inside the main memory as a part of activation record of the

10
00:00:56,630 --> 00:00:57,700
main function.

11
00:00:58,010 --> 00:01:00,290
So this array will be created in such a stack.

12
00:01:01,460 --> 00:01:08,330
So why I'm calling a static because the size of this UNEV was decided at the combined time, though

13
00:01:08,330 --> 00:01:10,910
the memory will be a look at it during the runtime.

14
00:01:10,910 --> 00:01:18,230
Only memory cannot be allocated at compile time, so memory will be a look at it during runtime and

15
00:01:18,230 --> 00:01:22,690
the size of that memory was decided already at the compilation time.

16
00:01:23,150 --> 00:01:29,450
Definitely the size of an array has to be decided at compile time in C language, in C language.

17
00:01:29,450 --> 00:01:34,370
When you are mentioning the size of an array, it must be a constant value.

18
00:01:34,550 --> 00:01:37,580
It cannot be a variable, it must be a constant.

19
00:01:37,590 --> 00:01:39,490
So you must write some constant value there.

20
00:01:40,280 --> 00:01:45,620
But in C++ we can create an array of any size at one time.

21
00:01:45,620 --> 00:01:53,810
So at runtime, aside from that, it can be decided and it will be created inside the stack only, whereas

22
00:01:53,810 --> 00:02:00,650
in C language the size has to be decided at dumbuya time only for the arrays inside the stack.

23
00:02:01,070 --> 00:02:02,340
Let us see in C++.

24
00:02:02,350 --> 00:02:07,720
Suppose they have one variable N and see in the end.

25
00:02:07,880 --> 00:02:16,430
So it means I'm reading some input from the keyboard the size of UTI as n then I can declare an array

26
00:02:16,460 --> 00:02:23,840
of some size and so whatever the input comes from the keyboard, I can create an array of that particular

27
00:02:23,840 --> 00:02:24,470
size.

28
00:02:25,280 --> 00:02:29,110
So size of another is decided at runtime.

29
00:02:29,480 --> 00:02:35,660
You can enter any value and at the time, so an array of that size will be created inside the stack.

30
00:02:36,050 --> 00:02:42,110
So size of an array can be divided over time in C++, but in C language it has to be mentioned at compile

31
00:02:42,110 --> 00:02:42,680
time only.

32
00:02:43,070 --> 00:02:48,620
This is the difference and both these are are created inside the stack only, so there's no space to

33
00:02:48,620 --> 00:02:49,630
show you B also.

34
00:02:49,640 --> 00:02:52,100
So I assume that B is also created inside the stack.

35
00:02:53,060 --> 00:02:56,240
So this part is in C language and in C++.

36
00:02:56,270 --> 00:02:57,450
This is also allowed.

37
00:02:58,240 --> 00:03:02,960
Next, we will learn how to create an uhry inside heap.

38
00:03:03,350 --> 00:03:05,900
So whose size and the type?

39
00:03:05,900 --> 00:03:08,060
Everything is decided at runtime.

40
00:03:08,780 --> 00:03:15,580
Now one thing you remember for accessing anything from Heap, we must have a pointer.

41
00:03:15,920 --> 00:03:21,970
So first of all, I should declare a pointer so I will declare the name of a pointer as a B.

42
00:03:21,980 --> 00:03:30,190
This is a pointer now where the speech will get the memory as a as a variable of type array B is also

43
00:03:30,200 --> 00:03:30,800
variable.

44
00:03:30,950 --> 00:03:34,070
So this will also get the memory inside stack.

45
00:03:35,770 --> 00:03:41,140
Now, remember, in any language, when you declare a variable, the memory for that variable will go

46
00:03:41,140 --> 00:03:42,580
inside the stack only.

47
00:03:43,690 --> 00:03:49,450
Then when it will be a look at it and he let us see now that point that I will use it for creating memory

48
00:03:49,450 --> 00:03:58,780
and help, so then I should say, be a fine new end of size five, then the memory for five individuals

49
00:03:58,790 --> 00:04:00,540
will be created inside he.

50
00:04:01,840 --> 00:04:07,770
And this point that we will be pointing on to that address, suppose the beginning, and resolve this

51
00:04:07,840 --> 00:04:14,200
continuous memory as the five hundred assume that then 500 will be stored in this point in time.

52
00:04:14,650 --> 00:04:19,839
So now the program can access that key by coming onto the pointer and taking the address and it can

53
00:04:19,839 --> 00:04:20,800
go there and access it.

54
00:04:21,160 --> 00:04:23,730
So memory cannot be accessed directly.

55
00:04:23,740 --> 00:04:25,630
It has to be accessed indirectly.

56
00:04:26,200 --> 00:04:31,240
Now, one important thing I have to show you here, when you get the memory from him, whenever you

57
00:04:31,240 --> 00:04:33,740
see in you, then only you get the memory from him.

58
00:04:33,760 --> 00:04:37,810
Otherwise, all the variables, whatever the variables you declare, they will get the memory inside

59
00:04:37,810 --> 00:04:38,230
the stack.

60
00:04:38,230 --> 00:04:43,840
Only now Neil is the operator that is in C++.

61
00:04:44,080 --> 00:04:46,670
Then how to create the same thing inside C.

62
00:04:46,960 --> 00:04:53,820
So for that I have to say function and block or Mallott function and I should mention the size.

63
00:04:53,830 --> 00:04:57,780
So I want five integer so five into no side of an integer.

64
00:04:57,940 --> 00:05:01,350
Maybe two by four, four by depends on the system or the compiler.

65
00:05:01,690 --> 00:05:06,580
So I should mention here size of end.

66
00:05:06,760 --> 00:05:12,160
So this is a common practice that is done in C language instead of mentioning two bytes dimension size

67
00:05:12,160 --> 00:05:19,600
of so that depending on the system or compiler it takes that size, then Mallott function will look

68
00:05:19,600 --> 00:05:24,130
at just raw memory, just the block of memory, not that memory.

69
00:05:24,130 --> 00:05:29,270
We want to use it as integer so we have to type construct as integer window.

70
00:05:29,950 --> 00:05:33,940
So this is in C language and this is in C++.

71
00:05:35,740 --> 00:05:42,010
New ones that are used in C++ and Mallott function is used in C language.

72
00:05:42,520 --> 00:05:46,570
So once again, when you declare a normal array then it will be graded instead.

73
00:05:46,570 --> 00:05:52,870
Stack when you want an array in heap then you must have a pointer and then you should look at the memory

74
00:05:52,870 --> 00:05:58,480
that using new operator or using Mallott function, it will be created in a heap and the heap memory

75
00:05:58,750 --> 00:06:01,690
can be accessed indirectly with the help of pointer.

76
00:06:02,920 --> 00:06:03,160
No.

77
00:06:03,160 --> 00:06:10,390
One more important thing, when we have allocated the memory in the heap and after some time during

78
00:06:10,390 --> 00:06:17,050
the execution of program, at any stage, if that memory is not required, then you must delete the

79
00:06:17,050 --> 00:06:17,400
memory.

80
00:06:17,410 --> 00:06:23,910
Also, if you don't delete the memory and use memory, then it causes memory leak problem.

81
00:06:24,940 --> 00:06:30,130
If the unused memory is not released, then it causes a memory leak problem.

82
00:06:31,240 --> 00:06:34,730
So how to release the memory in C++?

83
00:06:34,750 --> 00:06:45,970
We have to say delete B, but B is used for an array, so it must be mentioned here as a subscript and

84
00:06:45,970 --> 00:06:55,420
in C language C three B so this is the method of dislocation in C++ and this is the method of dislocation

85
00:06:55,420 --> 00:06:57,300
in C language.

86
00:06:58,480 --> 00:07:01,120
So I have shown you the syntax for what the language is.

87
00:07:01,330 --> 00:07:06,670
So the main concern here is how to get the array inside Hape now.

88
00:07:06,790 --> 00:07:10,480
Next, we will see how to access that array from Hape.

89
00:07:11,050 --> 00:07:16,750
This is a normal array inside the stack and the array inside heap know how we access this nonmonetary

90
00:07:16,750 --> 00:07:17,690
inside the stack.

91
00:07:17,950 --> 00:07:21,870
So for accessing any location, I should say, of zero or same five.

92
00:07:22,150 --> 00:07:24,040
So the value five will be stored here.

93
00:07:24,350 --> 00:07:27,670
The same thing I can say B of A zero.

94
00:07:27,670 --> 00:07:30,410
Assign the five just like a normal.

95
00:07:30,410 --> 00:07:34,440
Normally I can access this also so the value of five will be stored here.

96
00:07:35,860 --> 00:07:39,240
So it is as simple as accessing an array inside the stack.

97
00:07:39,550 --> 00:07:43,060
You can access an array from him by using a pointer.

98
00:07:43,660 --> 00:07:47,000
So Pointer acts as the name of a tree.

99
00:07:47,800 --> 00:07:52,810
So already we have learned that the center array can be accessed with the help of a loop for loop.

100
00:07:53,080 --> 00:07:58,960
Then that can also be accessed with the help of a loop, as we saw that this can be accessed using pointer

101
00:07:59,410 --> 00:08:01,540
so even that can be accessed using parenteral.

102
00:08:02,350 --> 00:08:04,220
So we don't have to rely on ducting.

103
00:08:04,240 --> 00:08:06,250
We already know it not.

104
00:08:06,280 --> 00:08:13,180
The next important thing here is that whenever you create an array, well-educated inside stack all

105
00:08:13,180 --> 00:08:20,720
you created inside the heap, once the array of some side is created, it cannot be resized exercise.

106
00:08:20,740 --> 00:08:21,990
It cannot be changed.

107
00:08:23,010 --> 00:08:31,290
If at all, you want to increase the size of an early, it is possible in another way, but it is possible

108
00:08:31,290 --> 00:08:35,690
only in heap stack, they cannot be precise at all.

109
00:08:36,030 --> 00:08:38,909
But a separate same array cannot be precise.

110
00:08:39,070 --> 00:08:41,240
But we have some alternative for that.

111
00:08:41,490 --> 00:08:46,740
So let us learn how we can increase or decrease the size of an.

