1
00:00:00,150 --> 00:00:05,230
In this video, we will write our insert function for inserting a node in our circular list.

2
00:00:05,250 --> 00:00:10,410
So already I have started a project in the previous video and the same project I'm using, which is

3
00:00:10,410 --> 00:00:13,320
already having functions for creating and displaying <PERSON><PERSON><PERSON><PERSON>.

4
00:00:14,300 --> 00:00:18,180
I did the same program, and here I will write on a function for insert.

5
00:00:19,410 --> 00:00:27,140
So let us try to function above main function, void insert, I will pass her as a pointer.

6
00:00:27,600 --> 00:00:33,240
So let us take it Aspey because we have been using P next as the index card, which I want to insert.

7
00:00:33,460 --> 00:00:35,970
Then next is the element that I want to insert.

8
00:00:36,000 --> 00:00:43,590
Then for insertion I required a few things like a temporary pointer, so I will declare a pointer that

9
00:00:43,590 --> 00:00:47,310
is t then I may require some variable so I'll take some variable I.

10
00:00:48,250 --> 00:00:54,920
No, as in Linell English, we were first checking whether the index given is valid or not.

11
00:00:55,270 --> 00:00:57,760
So even without checking also we can do it.

12
00:00:58,270 --> 00:01:02,430
Like if the index is zero, then definitely we are going to win, certainly not otherwise.

13
00:01:02,440 --> 00:01:06,180
If the index is going beyond the length of a link list, then we should stop.

14
00:01:06,880 --> 00:01:11,830
So here, first of all, I will write on the code for checking if our index is zero.

15
00:01:11,890 --> 00:01:14,200
So first, if the index is zero.

16
00:01:15,450 --> 00:01:16,680
The position of Zettl.

17
00:01:17,610 --> 00:01:21,420
If the pollution is zero, then I have to install a new node before heading north.

18
00:01:21,780 --> 00:01:24,380
So for that, first of all, I should create a new node.

19
00:01:24,810 --> 00:01:25,970
I will write on the code.

20
00:01:26,010 --> 00:01:33,510
I have created a new node, then send the data to X, then node is created and the data is set.

21
00:01:33,510 --> 00:01:37,990
And this new node now, as we are inserting before, had node.

22
00:01:38,280 --> 00:01:44,940
So first of all, check if that is already null, if it is already null, does the first node we are

23
00:01:44,940 --> 00:01:52,650
going to insert then if it is the first node then should point Aponte and then that should point itself

24
00:01:52,650 --> 00:01:53,310
had gaps.

25
00:01:53,310 --> 00:02:00,050
Next should point on itself that is had otherwise there are some nodes already in the link list.

26
00:02:00,060 --> 00:02:06,270
Then I should take the help of Pointer P and scan through a link and reach the last node and make the

27
00:02:06,270 --> 00:02:07,400
necessary changes.

28
00:02:07,950 --> 00:02:16,890
So using my loop I can see why this next is not equal to had gone moving p e assigned these next.

29
00:02:16,920 --> 00:02:26,250
Then once P has reached on the last node then the PS next should point on, you know the P and D next

30
00:02:26,250 --> 00:02:30,990
should point on head and then move on to new node.

31
00:02:31,740 --> 00:02:35,150
So that's also all that we have discussed this type of code on whiteboard.

32
00:02:35,580 --> 00:02:40,320
So this is inserting before had if indexes zettl else.

33
00:02:40,320 --> 00:02:45,960
If the index is not zero then the same as the code for inserting in the linear link.

34
00:02:46,470 --> 00:02:50,030
So again, going to have to create a new node and set the data.

35
00:02:50,040 --> 00:02:57,690
So first of all, using for loop I will move P and make it point on the index where we want to insert.

36
00:02:57,690 --> 00:03:02,700
So index is lasdun this one and then I placeless in the same line.

37
00:03:02,700 --> 00:03:05,000
I will write on the assigned PS next.

38
00:03:05,340 --> 00:03:10,890
So we will be moving to the north and it will be stopping at the node where we want to insert, then

39
00:03:11,280 --> 00:03:12,300
create a new node.

40
00:03:12,300 --> 00:03:15,990
So for creating a new node and initializing it all that we have the courts.

41
00:03:15,990 --> 00:03:17,530
I will publish that code here.

42
00:03:17,550 --> 00:03:17,940
Yes.

43
00:03:17,940 --> 00:03:23,090
Node excluded then the necessary links I have to modify that is a D next.

44
00:03:23,100 --> 00:03:30,600
Should be pointing on these next and then PS next should point on P.

45
00:03:30,720 --> 00:03:31,330
That's it.

46
00:03:31,350 --> 00:03:32,790
So an order is inserted.

47
00:03:32,790 --> 00:03:36,240
So the code is little and the so already I have discussed everything.

48
00:03:36,270 --> 00:03:38,700
I just am typing it for the demonstration.

49
00:03:38,730 --> 00:03:44,220
Now one thing I did not do here that I'm not checking whether the index given is valid or not.

50
00:03:44,520 --> 00:03:50,250
Like in the English, you remember we have written the code for checking if the index is valid or not.

51
00:03:50,280 --> 00:04:00,510
So here I should have some code like if index is less than zero or indexes greater than.

52
00:04:01,620 --> 00:04:09,630
Count or say, lente, then stop, don't insert anything just written, so I should write a function

53
00:04:09,630 --> 00:04:10,770
for Lent also.

54
00:04:10,860 --> 00:04:13,050
OK, here I will write on a function for Lent.

55
00:04:14,370 --> 00:04:15,810
Integer lente.

56
00:04:18,610 --> 00:04:20,950
Struck Gold Star P..

57
00:04:21,800 --> 00:04:22,280
And.

58
00:04:23,500 --> 00:04:30,100
Here I should have some variable land and this is initialized with zero and what to do while we can

59
00:04:30,250 --> 00:04:37,070
go on incrementing land as well as moving the next north until it reaches the last note.

60
00:04:37,090 --> 00:04:41,140
That is fine, but is not equal to header.

61
00:04:41,140 --> 00:04:44,710
And after this return lente.

62
00:04:46,020 --> 00:04:48,640
That's all it is, a simple function for finding land.

63
00:04:48,930 --> 00:04:50,490
So all three of you are familiar with this code.

64
00:04:50,490 --> 00:04:54,510
We have redundant and linear links, but now the loop is different.

65
00:04:55,380 --> 00:04:56,280
We have to use it.

66
00:04:56,300 --> 00:04:58,050
Do I look for this one, NetSol?

67
00:04:58,050 --> 00:05:02,950
So insert functions already now here inside the main function.

68
00:05:03,270 --> 00:05:07,320
I will try to insert a new element that is after node three.

69
00:05:07,350 --> 00:05:09,600
That is a second position.

70
00:05:09,780 --> 00:05:17,610
So insert I should pass had the pointer then index that is at two and I want to insert value and then

71
00:05:17,610 --> 00:05:19,650
after that it will display a linguist.

72
00:05:19,660 --> 00:05:20,130
Yes.

73
00:05:20,130 --> 00:05:21,480
And is inserted after three.

74
00:05:21,480 --> 00:05:25,320
You can see that two, three and four, five, six is working.

75
00:05:25,480 --> 00:05:30,110
Let us check if we can insert after six that is at index five.

76
00:05:30,120 --> 00:05:32,310
So and should be inserted after six.

77
00:05:32,340 --> 00:05:32,850
Yes.

78
00:05:32,850 --> 00:05:34,110
And is inserted after six.

79
00:05:34,140 --> 00:05:36,120
That is two, three, four, five, six and 10.

80
00:05:36,270 --> 00:05:39,280
Now let us check the index zero can be inserted before.

81
00:05:39,660 --> 00:05:43,170
Yes it is inserting and two, three, four, five, six.

82
00:05:43,170 --> 00:05:44,360
Yes it is working.

83
00:05:44,370 --> 00:05:46,300
So that's all the insert function.

84
00:05:46,770 --> 00:05:47,100
So.

85
00:05:47,100 --> 00:05:47,370
Right.

86
00:05:47,370 --> 00:05:48,630
This program by yourself.

87
00:05:48,630 --> 00:05:48,840
Right.

88
00:05:48,840 --> 00:05:50,730
This function is given.

89
00:05:50,730 --> 00:05:54,930
You can look at the programming that we give and you can type it by yourself for any help.

90
00:05:54,930 --> 00:05:56,620
You can use that pedia.

