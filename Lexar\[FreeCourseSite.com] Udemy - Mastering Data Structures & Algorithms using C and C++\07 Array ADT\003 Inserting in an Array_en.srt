1
00:00:00,270 --> 00:00:03,030
First of all, I will fill a few elements in this early.

2
00:00:04,800 --> 00:00:13,440
So total, I have six elements or letters, the length of this, maybe six not assume that already I

3
00:00:13,440 --> 00:00:15,800
have some elements in al-Nouri.

4
00:00:16,440 --> 00:00:19,340
Now based on this, let us look at these operations.

5
00:00:19,800 --> 00:00:21,780
So the first operation is a display.

6
00:00:21,990 --> 00:00:31,490
Let us see how we can display all the elements of an early so first operation display, displaying or

7
00:00:31,530 --> 00:00:35,530
traversing an array as visiting all the elements of an array.

8
00:00:35,760 --> 00:00:36,630
One by one.

9
00:00:37,710 --> 00:00:42,780
Now here I will show you the procedure by writing some pseudocode and it's not a complete function of

10
00:00:42,780 --> 00:00:43,380
the program.

11
00:00:43,680 --> 00:00:47,860
Afterwards you will see a complete program combining all these functions together.

12
00:00:48,180 --> 00:00:51,820
So let us see the logical part, how to performed operations.

13
00:00:51,840 --> 00:00:56,880
For that, I'll be writing the code now for displaying the elements of what I want to display will be

14
00:00:56,880 --> 00:01:03,170
the first element and I should say, but indef percentile D.

15
00:01:06,340 --> 00:01:13,170
Eight of the zero, this will be ending just a first element that is at index zero.

16
00:01:13,630 --> 00:01:21,430
But if I want to bring all these elements, then here I should use index so far that I can travel through

17
00:01:21,430 --> 00:01:27,500
this area from index zero to five by taking a follow up to four, I assign value zero.

18
00:01:27,700 --> 00:01:29,690
So initially I start from zero.

19
00:01:29,830 --> 00:01:34,120
So here I can use I then I should go till they're still five.

20
00:01:34,130 --> 00:01:36,000
So that five is the number of elements.

21
00:01:36,000 --> 00:01:37,960
So zero to five, I have to dump six elements.

22
00:01:37,960 --> 00:01:43,460
So I should say I is less than lente and then I plus.

23
00:01:43,470 --> 00:01:43,830
Plus.

24
00:01:44,110 --> 00:01:45,780
So he had an escape attempt.

25
00:01:46,750 --> 00:01:50,500
Now this piece of gold will bring all the elements.

26
00:01:51,160 --> 00:01:52,630
First time I value will be zero.

27
00:01:52,630 --> 00:01:54,430
So it will be a zero.

28
00:01:54,430 --> 00:01:57,100
The next I becomes become C++, I becomes one.

29
00:01:57,100 --> 00:01:59,950
So it will print A of London off to.

30
00:02:00,520 --> 00:02:02,200
So on up to five.

31
00:02:03,010 --> 00:02:10,150
Now here instead of using a specific language syntax, I prefer seeing print eight of eight.

32
00:02:12,160 --> 00:02:13,720
So this becomes a pseudocode.

33
00:02:13,750 --> 00:02:16,210
Now this doesn't belong to any particular language.

34
00:02:16,220 --> 00:02:19,120
It may be C language or C++ or any language.

35
00:02:19,660 --> 00:02:23,660
So in C language it becomes print and in C++ it may be sealed.

36
00:02:24,130 --> 00:02:25,900
So this is the method for printing.

37
00:02:26,530 --> 00:02:30,430
Now let us go to the next operation and so I'll explain that oppression.

38
00:02:30,430 --> 00:02:32,290
Then I will show how it works.

39
00:02:32,650 --> 00:02:39,400
The second oppression of ad or append adding or offending, it's nothing but adding a new element at

40
00:02:39,400 --> 00:02:42,670
the end of an era that is at the next free space in an.

41
00:02:43,360 --> 00:02:46,970
So we have an array of five 10, but we have only six elements.

42
00:02:46,970 --> 00:02:48,460
So we have mixed pre space.

43
00:02:48,460 --> 00:02:50,320
Add in six.

44
00:02:50,980 --> 00:02:57,460
So who says that six is a free space because the lenders six for six minutes the number of elements.

45
00:02:57,470 --> 00:03:00,490
So I have one, two, three, four, five, six elements.

46
00:03:01,330 --> 00:03:04,690
Last index is five, but it is keeping the count of number of elements.

47
00:03:04,930 --> 00:03:06,700
So index six as a three.

48
00:03:07,000 --> 00:03:08,050
So it's very simple.

49
00:03:08,260 --> 00:03:16,180
Just in an array of lent, I can store an element, whichever element is passed here so I can store

50
00:03:16,180 --> 00:03:16,710
X.

51
00:03:17,170 --> 00:03:20,270
So for all I wanted to insert a new element that is a ten.

52
00:03:20,620 --> 00:03:22,830
So ten is inserted here then.

53
00:03:22,930 --> 00:03:26,950
Now I have total seven elements, so this length should also become seven.

54
00:03:27,310 --> 00:03:28,510
So let us change that.

55
00:03:28,510 --> 00:03:30,130
Lent to seven.

56
00:03:30,610 --> 00:03:34,630
So I must also update Lente after performing this operation.

57
00:03:34,930 --> 00:03:37,400
So Lente plus plus.

58
00:03:40,630 --> 00:03:42,340
So this will increase the length.

59
00:03:43,240 --> 00:03:47,120
So adding an element or opinion element means adding an element at the end of another.

60
00:03:47,410 --> 00:03:49,780
So it's a simple syntax, just two segments.

61
00:03:50,020 --> 00:03:52,570
So what is the time taken by this operation?

62
00:03:52,840 --> 00:03:54,460
The time taken by this operation?

63
00:03:54,670 --> 00:04:00,520
If I say each statement takes one point of time for execution, so just two statements are there.

64
00:04:00,670 --> 00:04:02,560
So the time is constant.

65
00:04:03,980 --> 00:04:05,360
We see time is constant.

66
00:04:06,280 --> 00:04:12,040
Now, here, I will tell you what is the meaning of this constant, so let me go a little bit detail

67
00:04:12,040 --> 00:04:12,340
here.

68
00:04:12,700 --> 00:04:20,769
See the time that they got to so function of time as to know when the function is to, then what is

69
00:04:20,769 --> 00:04:21,940
the degree of a function.

70
00:04:22,120 --> 00:04:26,590
So degree of a function is nothing but to in to end poverty.

71
00:04:26,590 --> 00:04:27,010
Zero.

72
00:04:27,250 --> 00:04:30,590
So what is the degree zero for mPower zero.

73
00:04:30,790 --> 00:04:33,240
So it is outdraw and poverty zero.

74
00:04:33,520 --> 00:04:36,330
So anything over zero is written as one.

75
00:04:36,610 --> 00:04:38,040
So that's why we write one.

76
00:04:38,050 --> 00:04:40,980
So actually we mean and policy and stuff.

77
00:04:40,990 --> 00:04:41,890
One we mean and.

78
00:04:41,890 --> 00:04:42,160
But what.

79
00:04:42,170 --> 00:04:42,510
Zero.

80
00:04:43,090 --> 00:04:44,090
So it's a constant.

81
00:04:44,560 --> 00:04:49,660
So the time taken whenever you get any constant value you got two thirds constant.

82
00:04:49,870 --> 00:04:52,310
If you get two thousand also it's constant.

83
00:04:52,570 --> 00:04:57,930
So whatever the figure might be when it is a constant source and zero and that value is one.

84
00:04:58,090 --> 00:05:03,940
So we are not seeing that the other two is equal to one, but we are seeing that degree of a function

85
00:05:03,940 --> 00:05:05,700
is impossible.

86
00:05:05,710 --> 00:05:06,560
That is one.

87
00:05:07,660 --> 00:05:09,450
So this was the simplest operation.

88
00:05:09,820 --> 00:05:15,480
So we have finished how hundred disparate elements and we have finished up and now let us see insert

89
00:05:15,940 --> 00:05:20,840
next operation insert insert operation takes index and element.

90
00:05:21,310 --> 00:05:25,030
So this means inserting an element at a given index.

91
00:05:25,030 --> 00:05:30,420
So as an example, I have taken the index has a four and the element that I want to insert is 15.

92
00:05:30,700 --> 00:05:37,530
So it means I want to insert a new element that is 15 at this place, but already some element is there.

93
00:05:38,140 --> 00:05:41,640
So I should provide a free space for that 15.

94
00:05:42,010 --> 00:05:45,940
So for providing free space, these elements should be sifted forward.

95
00:05:46,420 --> 00:05:49,470
Then a free space will open up here, then I can store 15.

96
00:05:49,480 --> 00:05:53,350
So for that I should move and then I should move nine.

97
00:05:53,750 --> 00:05:55,350
Then also I should move six.

98
00:05:55,840 --> 00:05:59,270
Then a free space will appear here, then I can insert 15 there.

99
00:05:59,590 --> 00:06:01,940
So 10 should be moved here, nine should be moved here.

100
00:06:02,260 --> 00:06:04,660
So how to do that same thing programmatically.

101
00:06:05,020 --> 00:06:11,920
C I should start from last index of seven then standing there I should copy the element from six.

102
00:06:12,430 --> 00:06:13,810
So 10 will be copied here.

103
00:06:15,160 --> 00:06:20,560
Then move down to this location standing here, copy the element from this location, so name will be

104
00:06:20,560 --> 00:06:26,010
copied here, then move in here, standing here, copy the element from this index.

105
00:06:26,380 --> 00:06:30,310
So six is moved here, then move it here then.

106
00:06:30,310 --> 00:06:32,320
This is the place where we want to insert.

107
00:06:32,440 --> 00:06:37,780
So stop when it became that equal before then stop and insert the element there.

108
00:06:38,710 --> 00:06:40,450
So for that I was right on the code.

109
00:06:40,810 --> 00:06:42,790
I have this started from 7:00.

110
00:06:42,790 --> 00:06:43,510
That is lente.

111
00:06:43,810 --> 00:06:52,060
So like I is starting from Lente so I have to do this repeatedly.

112
00:06:52,070 --> 00:06:55,570
So let us have four then.

113
00:06:55,750 --> 00:07:01,680
How long it should continue while I is greater than the index that I want to insert.

114
00:07:01,990 --> 00:07:05,920
So this is index and this is X so index.

115
00:07:07,720 --> 00:07:14,540
Then I should be documenting every time that, as I said when I was standing here, so it has copied

116
00:07:14,540 --> 00:07:15,500
the element from here.

117
00:07:15,760 --> 00:07:21,850
So at AOF i.e. we should copy the element from eight of i.e. minus one.

118
00:07:23,230 --> 00:07:23,800
That's it.

119
00:07:24,160 --> 00:07:29,020
So being at index, I copied elements from index minus one.

120
00:07:29,380 --> 00:07:35,680
Then the more I the agreement, I then copied elements from this index, then diclemente and copied

121
00:07:35,680 --> 00:07:39,790
elements from this index so big I kept on some index.

122
00:07:41,970 --> 00:07:47,850
Eyes on some index and copied elements from the previous index, so this process has to continue until

123
00:07:48,030 --> 00:07:49,970
I reach this particular index.

124
00:07:50,220 --> 00:07:54,270
So when I has reached here, the simply we can insert this element 15.

125
00:07:54,270 --> 00:08:02,370
They're so out of index means the index active what we wanted to copied November 15.

126
00:08:02,670 --> 00:08:04,330
So 15 will be copied here.

127
00:08:05,250 --> 00:08:09,340
Now, once the suspense copied here, the number of elements has increased by one.

128
00:08:09,600 --> 00:08:12,210
So the seven should become eight.

129
00:08:12,360 --> 00:08:18,240
So it means I should also increment lend, lend, plus, plus.

130
00:08:19,200 --> 00:08:25,770
So that's how we can insert a new element at a given index by shifting the other elements and providing

131
00:08:25,770 --> 00:08:27,300
a free space for the new element.

132
00:08:28,080 --> 00:08:28,800
Now one more thing.

133
00:08:28,800 --> 00:08:31,250
We should take it here before performing the operation.

134
00:08:31,680 --> 00:08:34,730
This index should not be beyond the land of Inari.

135
00:08:35,039 --> 00:08:36,090
See total elements.

136
00:08:36,090 --> 00:08:39,210
I have from zero to seven total eight elements I have.

137
00:08:39,450 --> 00:08:46,470
And if I say that I want to insert that index 10 or 11, it should not go beyond Landauer even it should

138
00:08:46,470 --> 00:08:48,780
not go beyond the size of an array.

139
00:08:48,990 --> 00:08:50,790
That condition we should check first.

140
00:08:50,970 --> 00:08:56,580
If it is valid, then we should perform this operation so that we're taking care of all those things

141
00:08:56,580 --> 00:09:03,720
when we write on the complete program, not let us analyze this one, how much time it has taken for

142
00:09:03,720 --> 00:09:04,820
inserting an element.

143
00:09:05,460 --> 00:09:06,810
So what is the work done?

144
00:09:07,320 --> 00:09:14,500
The work done is shifting off elements, then copying off element, copying of element as a constant

145
00:09:14,500 --> 00:09:17,400
time, but shifting of elements.

146
00:09:17,400 --> 00:09:19,020
How many elements we are shifting.

147
00:09:19,560 --> 00:09:26,910
See, it is possible that now, right now there are eight elements and I want to insert that index eight.

148
00:09:27,150 --> 00:09:28,500
So no shifting required.

149
00:09:28,500 --> 00:09:33,960
Simply the element will be inserted here, so there may not be any shifting at all.

150
00:09:34,050 --> 00:09:35,140
That is 050.

151
00:09:36,750 --> 00:09:44,430
Ah, I want to insert an element at index zero, so index zero, I should shift all elements so and

152
00:09:44,430 --> 00:09:50,170
maximum shifting can be and so we don't know how much shifting may be required.

153
00:09:50,430 --> 00:09:56,400
So either zero shifting or and shifting depends where you want to insert the element.

154
00:09:56,730 --> 00:10:02,390
If you're inserting at the end of an array, then no shifting is required at the beginning of another.

155
00:10:02,550 --> 00:10:08,240
Then all the elements are present in an area that is laodicean elements and elements we have to move.

156
00:10:08,520 --> 00:10:19,020
So the time taken by this procedure of this function is order of one that is minimum and orders and

157
00:10:19,230 --> 00:10:20,250
that is maximum.

158
00:10:21,450 --> 00:10:24,090
So minimum time is one maximum time.

159
00:10:24,270 --> 00:10:32,090
And so whenever we mention the time we try to talk about maximum time taken by any function or procedure.

160
00:10:32,430 --> 00:10:38,510
So this procedure takes outdraw any time, but it is having minimum time as well as maximum time.

161
00:10:38,820 --> 00:10:42,240
So minimum time they can we can call it as best case time.

162
00:10:42,660 --> 00:10:48,990
And the cases we are sitting at the end of an alley and the maximum time can be called as worst case

163
00:10:48,990 --> 00:10:54,380
time and the worst cases we are inserting at the beginning index of an error that is zero.

164
00:10:55,800 --> 00:10:58,950
So that's all about inserting an element at a given index.

165
00:10:59,130 --> 00:11:00,430
So we have finished this one.

166
00:11:01,020 --> 00:11:04,230
Now let us talk about deleting an element from a given index.

