1
00:00:00,540 --> 00:00:07,470
For practicing C++ program or developing applications in C++, you need some idea where you can type

2
00:00:07,470 --> 00:00:11,830
the program and compile the programs and also you need a compiler.

3
00:00:12,210 --> 00:00:15,130
So for <PERSON>, choose it.

4
00:00:15,150 --> 00:00:16,370
That is code blocks.

5
00:00:16,379 --> 00:00:25,110
So let us see how to download code blocks, open chrome and say download code blocks.

6
00:00:26,220 --> 00:00:29,070
So good blogs dot org does that.

7
00:00:29,100 --> 00:00:32,159
You are also, if you want, you know that you are right.

8
00:00:32,549 --> 00:00:34,230
So if you want, you can directly go to this.

9
00:00:34,230 --> 00:00:36,230
You are and that is called block start borgese.

10
00:00:36,540 --> 00:00:39,600
Download it and download three options out there.

11
00:00:39,680 --> 00:00:46,470
That is download binary release or source code and retrieve source code from subversion that is as free

12
00:00:46,470 --> 00:00:49,140
and will go to binary release.

13
00:00:50,460 --> 00:00:52,290
And here we have multiple options.

14
00:00:52,660 --> 00:01:03,210
That is only set a file or no set up just a zip or mingy w set up or mingy w no set up.

15
00:01:03,540 --> 00:01:10,530
So you have to select mingy w set up this or download compiler as well as Idy.

16
00:01:11,070 --> 00:01:15,270
So select this one, go to the website at SourceForge for downloading.

17
00:01:15,540 --> 00:01:18,330
It will start to downloading the file.

18
00:01:21,340 --> 00:01:28,360
Yeah, it's downloaded, so let us start citified, you know, here it starts installation of code blocks.

19
00:01:30,740 --> 00:01:32,670
See you next and agree on.

20
00:01:35,280 --> 00:01:43,980
And clicked, I agree, next, then he is a place where the code blocks will be installed if you want

21
00:01:43,980 --> 00:01:47,790
to change there, Derek Duke and Jim and I will start installation.

22
00:01:47,800 --> 00:01:52,950
So it will extract all the files of I.T. and the compiler.

23
00:01:57,110 --> 00:02:02,270
So do you want to run good blocks now, so I'll say no and let us finish this one.

24
00:02:02,270 --> 00:02:04,690
So next and it is finish it is installed.

25
00:02:05,390 --> 00:02:07,280
No, let us open the code blocks.

26
00:02:12,190 --> 00:02:19,270
It's not a code blocks, it's open now before we start using it, before you start writing the programs,

27
00:02:19,280 --> 00:02:21,310
we have to make some settings here.

28
00:02:22,430 --> 00:02:29,240
And these are one time settings, so let us see what are the settings we have to do, go to menu and

29
00:02:29,720 --> 00:02:32,420
at the end you will find settings option.

30
00:02:32,420 --> 00:02:35,300
Select that one and go to compiler.

31
00:02:37,020 --> 00:02:44,370
Now, this time is open, see here right now, if you see the language selected here as C++ 98.

32
00:02:45,600 --> 00:02:52,110
So there's an older version, so he should select a later version so that we can use all the features

33
00:02:52,110 --> 00:02:54,880
that we'll be talking about in this course.

34
00:02:55,860 --> 00:02:58,800
So that is C++ live in select C++.

35
00:03:00,290 --> 00:03:01,920
This is one setting you have to change.

36
00:03:02,520 --> 00:03:05,640
Otherwise, some of the features that I'll be showing you will not work.

37
00:03:05,850 --> 00:03:09,510
So you have to select C++ and then.

38
00:03:10,520 --> 00:03:16,790
In toolchain executables, there's a tab above here and there for the compiler settings, right after

39
00:03:16,790 --> 00:03:26,480
selecting this, go to the next tab here, that is toolchain executables here insert the results compiler.

40
00:03:26,780 --> 00:03:29,470
We have to select a compiler.

41
00:03:30,110 --> 00:03:32,240
So click on this one then.

42
00:03:32,240 --> 00:03:38,480
Here, if you scroll down, you will find GDB or GDB 32.

43
00:03:39,400 --> 00:03:42,620
Here I got GDP 32, so I will select this one.

44
00:03:42,640 --> 00:03:47,170
So if you are getting GDP, you select that GDP for debugger.

45
00:03:49,200 --> 00:03:49,860
OpenNet.

46
00:03:50,790 --> 00:03:52,050
And say, OK.

47
00:03:53,290 --> 00:03:56,910
Nor does debulking will be looking at it in the next videos.

48
00:03:57,780 --> 00:04:01,240
Now I have to make one more settings here, so go to settings again.

49
00:04:01,240 --> 00:04:02,030
Not this time.

50
00:04:02,040 --> 00:04:03,150
Select Beeber that.

51
00:04:03,630 --> 00:04:05,850
So we have to and they will be bigger than only.

52
00:04:05,850 --> 00:04:07,320
We will be able to table the program.

53
00:04:07,330 --> 00:04:13,320
So they will allow us to trace the program line by line, more of a little bit tracing the programs.

54
00:04:13,530 --> 00:04:17,410
So same if you would like to do that in your ID, you can do it here.

55
00:04:18,120 --> 00:04:19,500
So select default.

56
00:04:20,790 --> 00:04:22,270
OK, once again, I'll come back.

57
00:04:22,710 --> 00:04:27,580
So go to settings and select debugger and here select default.

58
00:04:28,290 --> 00:04:31,060
And here you have to select the executable part.

59
00:04:31,560 --> 00:04:35,880
So here click and again, select the same file GDP to.

60
00:04:40,260 --> 00:04:42,780
Judy, we try to select this site.

61
00:04:44,090 --> 00:04:52,130
Open that, let's say, OK, now you are ready with the indictment, now we can use code blocks for

62
00:04:52,130 --> 00:04:55,530
writing C++ programs that are shown in discourse.

63
00:04:56,510 --> 00:04:58,730
Now I will show you how to create a new project.

64
00:04:58,730 --> 00:05:04,580
And whatever the steps I'm showing you, always follow those same steps for creating a new project for

65
00:05:04,580 --> 00:05:05,450
every program.

66
00:05:05,450 --> 00:05:07,800
It's better you create a new project.

67
00:05:08,240 --> 00:05:15,290
So let us see how to create a new project so I can go to files and see a new project.

68
00:05:15,290 --> 00:05:23,510
I can start a new project and the project that you have to select this console application and the language

69
00:05:23,510 --> 00:05:26,810
that you have to select the C++ and the project.

70
00:05:26,810 --> 00:05:28,910
Name my first.

71
00:05:31,870 --> 00:05:32,530
Finnish.

72
00:05:34,290 --> 00:05:38,730
Now, here the project is created and in the process, this is a C++ file.

73
00:05:39,000 --> 00:05:42,870
Double click on it and this is the source code file.

74
00:05:42,900 --> 00:05:49,080
So here is already some code is written so you can type your own code in this displays and you can write

75
00:05:49,080 --> 00:05:56,130
all your programs here, see already your stream header files included and the namespace is also included

76
00:05:56,130 --> 00:05:56,440
here.

77
00:05:56,850 --> 00:06:03,210
So using namespace this HelloWallet is displayed how you can write all your programs here that is using

78
00:06:03,210 --> 00:06:08,450
this main function and you can write your own functions or classes that will be learning slowly.

79
00:06:08,910 --> 00:06:10,310
So let us see how to run.

80
00:06:10,320 --> 00:06:11,970
So first you have to build this one.

81
00:06:11,970 --> 00:06:17,100
So combine, say, build this project, run the program so it displays.

82
00:06:17,400 --> 00:06:18,080
Hello.

83
00:06:18,630 --> 00:06:19,350
So that's it.

84
00:06:19,380 --> 00:06:22,970
So this is how you can download and use this ID.

