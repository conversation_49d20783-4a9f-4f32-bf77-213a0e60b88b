1
00:00:00,650 --> 00:00:07,939
Let us see Bush operation that is inserting an element in the stack of the Bush function, which takes

2
00:00:07,939 --> 00:00:13,610
parameter, what is the value to be pushed so appointer assume that it is a global top point.

3
00:00:13,610 --> 00:00:18,710
It is a pointer of pipe node, just like first or head pointer of linguist.

4
00:00:20,340 --> 00:00:27,120
Now, what we should do in Bush, check for full condition for that, create a new order, create a

5
00:00:27,120 --> 00:00:30,690
new order, if no not is not created, then it is full.

6
00:00:32,400 --> 00:00:38,490
If no, it is not created equal to another one we are creating and all these elements in order is not

7
00:00:38,490 --> 00:00:48,060
created, then print stockists full, but stack overflow or else if subordinate is created.

8
00:00:48,510 --> 00:00:49,410
So I will also draw.

9
00:00:50,800 --> 00:00:55,570
Hidenori Scrooged, then fill up this note, make this point on this one and move top.

10
00:00:57,870 --> 00:01:06,790
So inside, I will write on computer data here, x value here, these data is X, these data is X.

11
00:01:07,260 --> 00:01:12,960
So suppose the value is during the hour instead of 20, then make this point on this node.

12
00:01:13,170 --> 00:01:14,880
These next stop.

13
00:01:16,380 --> 00:01:17,670
These next stop.

14
00:01:17,880 --> 00:01:22,850
So this one point on this one, then as does the new node stop should be brought here.

15
00:01:23,880 --> 00:01:31,120
So Pop should be brought on D is assignment to remove T we don't need it.

16
00:01:31,560 --> 00:01:32,250
So that's all.

17
00:01:32,250 --> 00:01:33,180
These are the steps.

18
00:01:34,450 --> 00:01:38,920
Simple steps are there, inserting a new normal before heading order before Fastenal.

19
00:01:38,940 --> 00:01:43,870
That same procedure we have followed and the timetable is constant because the steps are simple steps

20
00:01:44,290 --> 00:01:45,670
and this function works.

21
00:01:45,670 --> 00:01:49,300
Even if you are inserting fussin order, let us check it.

22
00:01:49,540 --> 00:01:56,500
If you are inserting very first note that time top line, there will be no and if you create a node

23
00:01:56,500 --> 00:01:58,090
for all the node is created.

24
00:01:59,170 --> 00:02:01,410
This is not coming to the spot.

25
00:02:01,840 --> 00:02:03,550
These data assign some value.

26
00:02:03,580 --> 00:02:06,390
So again, I'll find some value these next to stop.

27
00:02:06,400 --> 00:02:07,120
So what to stop?

28
00:02:07,120 --> 00:02:07,460
None.

29
00:02:07,480 --> 00:02:09,970
So this becomes null then top unsigned.

30
00:02:11,080 --> 00:02:14,050
So top and come up won't be the name of the street.

31
00:02:15,550 --> 00:02:20,020
No, you can see that this is the only mode and the top is pointing on that node.

32
00:02:20,770 --> 00:02:23,320
So even if it is first node, it works.

33
00:02:23,320 --> 00:02:24,580
The same function looks.

34
00:02:25,510 --> 00:02:31,810
Now, let us look at pop function deleting function so we know well that deletion is done from this

35
00:02:31,810 --> 00:02:32,170
site.

36
00:02:32,270 --> 00:02:33,620
So we will delete this note.

37
00:02:34,540 --> 00:02:37,100
Let us look at pop function, how to delete.

38
00:02:37,240 --> 00:02:38,790
Actually, we have to delete Fastenal.

39
00:02:39,040 --> 00:02:44,260
But before deletion, what we should check, whether there are some nodes or not, means if it is empty,

40
00:02:44,260 --> 00:02:45,510
we cannot delete any normal.

41
00:02:46,120 --> 00:02:47,540
So check for this condition.

42
00:02:48,850 --> 00:02:58,720
So right here, if the opposite goes to another, then stack is empty or underfloor print stack is empty.

43
00:02:59,380 --> 00:03:01,390
Otherwise we can delete a..

44
00:03:01,630 --> 00:03:02,920
So let us see.

45
00:03:02,920 --> 00:03:04,870
What is the procedure for deleting first node?

46
00:03:06,540 --> 00:03:11,500
As for deletion, I should have a point of some point that he let us call that point of.

47
00:03:12,210 --> 00:03:19,350
So I need a pointer so I will declare a pointer also here, Loadstar Starboy then be assigned top.

48
00:03:19,350 --> 00:03:21,530
So P should be pointing on first Norder.

49
00:03:21,570 --> 00:03:22,920
So P or T anything.

50
00:03:23,490 --> 00:03:33,390
I'm usually taking P now moved up to next, nor should move on to the next node to assign Thorpes next.

51
00:03:33,870 --> 00:03:36,810
Then take out this data base data.

52
00:03:36,840 --> 00:03:41,000
Take out in variable x Mediaplex exercise NP's data.

53
00:03:41,010 --> 00:03:44,470
So this data is taken in the variable X then delete the node.

54
00:03:45,150 --> 00:03:46,590
So this mode will be freed.

55
00:03:47,900 --> 00:03:54,860
Freebie, even I can try to delete or three B then the last statement also happened right after that

56
00:03:54,860 --> 00:03:58,090
is written X. I should return the deleted value.

57
00:03:58,820 --> 00:04:04,520
There's no space just writing it right on X, so whatever the value is deleted, that value will be

58
00:04:04,520 --> 00:04:04,910
returned.

59
00:04:06,790 --> 00:04:11,690
If it is unable to delete, because if the stack is empty, then it will return minus one.

60
00:04:12,190 --> 00:04:16,140
So if this function is returning, minus one means stack is empty.

61
00:04:18,490 --> 00:04:22,180
Next, let us look at beep or big operation.

62
00:04:23,180 --> 00:04:29,840
Now, let us look at the big operation, which takes position and gives you the value at that given

63
00:04:29,840 --> 00:04:30,380
position.

64
00:04:30,830 --> 00:04:37,330
For example, if position is one, then what is the value at one position in the stack printing?

65
00:04:37,820 --> 00:04:39,980
What is the value proposition to the stack?

66
00:04:40,190 --> 00:04:42,530
Three are position three.

67
00:04:42,530 --> 00:04:44,970
The value is eight position for value 15.

68
00:04:44,990 --> 00:04:49,810
So this function takes the position and gives the value at that particular position.

69
00:04:50,270 --> 00:04:52,640
So they want then the position value.

70
00:04:52,780 --> 00:04:54,990
So first, second, third, fourth, fifth.

71
00:04:55,550 --> 00:04:58,210
Beyond that, there is nothing so invalid position.

72
00:04:58,430 --> 00:05:00,930
We cannot give a value, so we return minus one.

73
00:05:01,910 --> 00:05:05,690
So let us see how this workbook big function takes a position.

74
00:05:05,960 --> 00:05:08,330
Suppose, for example, I have given position.

75
00:05:08,330 --> 00:05:13,600
As for that is for the position for position one, two, three, four.

76
00:05:13,880 --> 00:05:14,900
This is the fourth node.

77
00:05:15,170 --> 00:05:17,780
I want to know the element present here that is 15.

78
00:05:18,260 --> 00:05:24,380
So for that I should traverse through this linguist's starting from top node or the very first node.

79
00:05:24,740 --> 00:05:31,510
So for that I take a point of P and make it point on top here then to reach forward node.

80
00:05:31,520 --> 00:05:33,250
How many times P should move.

81
00:05:34,190 --> 00:05:36,010
One, two, three.

82
00:05:36,020 --> 00:05:40,770
So it should move four, three times, one, two, then three.

83
00:05:41,270 --> 00:05:44,300
So after moving three times it can reach on fourth node.

84
00:05:45,080 --> 00:05:52,030
So by using for loop I will move a B until it reaches that given position or it has become not.

85
00:05:52,670 --> 00:05:53,960
So I will vote on the court here.

86
00:05:54,590 --> 00:06:02,990
C I is the starting from zero and I is listening position minus one C++ and before that also I am checking

87
00:06:03,000 --> 00:06:07,060
BS, not knowing if it is null then we don't have to go beyond that one.

88
00:06:07,430 --> 00:06:12,790
So also check whether it is becoming the law or not, then move it for position minus one time.

89
00:06:12,810 --> 00:06:15,560
So for moving P assign PS next.

90
00:06:15,740 --> 00:06:18,980
So this will be moving up to next node every time.

91
00:06:20,060 --> 00:06:23,420
So this fall will take B upon that particular position.

92
00:06:24,140 --> 00:06:27,830
If the position is valid, if it is invalid, P will become null.

93
00:06:28,310 --> 00:06:30,740
So I should check whether it is valid or invalid.

94
00:06:31,130 --> 00:06:39,020
So after the loop check if it is not equal, do not mean it is valid, then return the element at that

95
00:06:39,020 --> 00:06:39,680
position.

96
00:06:39,680 --> 00:06:42,810
Fifteen written piece of data.

97
00:06:43,340 --> 00:06:46,730
This is not deleting just we are sending the value copy off of that.

98
00:06:47,030 --> 00:06:49,430
If it is not then return minus one.

99
00:06:51,530 --> 00:06:58,290
Else return minus one, it means that position was invalid, so we don't have data there.

100
00:06:59,560 --> 00:07:02,830
That's all will the speak function, so we don't need this.

101
00:07:03,250 --> 00:07:05,790
I have already declared initially, but we don't need it.

102
00:07:06,520 --> 00:07:09,710
So you have directly on the value instead of taking an X and sending it.

103
00:07:09,760 --> 00:07:10,970
I have that on the Web.

104
00:07:11,200 --> 00:07:15,980
So this is a big operation that is finding an element that given position.

105
00:07:17,460 --> 00:07:21,750
No remaining small operations out there that is stacked up is full and is empty.

106
00:07:21,990 --> 00:07:25,300
I will directly write on the record and just quickly explain.

107
00:07:26,550 --> 00:07:31,650
So here I have a stack of function stacked up, if at all.

108
00:07:31,650 --> 00:07:37,430
If Topman stop is not equal to null, then return the date of present that top surgery on Steadicam.

109
00:07:37,920 --> 00:07:44,010
Otherwise it will come to this statement and return minus once since data is not it and the stack is

110
00:07:44,010 --> 00:07:44,370
empty.

111
00:07:45,720 --> 00:07:49,990
The EMT function, if top is not that it is empty.

112
00:07:50,010 --> 00:07:52,010
Otherwise it's not empty.

113
00:07:52,320 --> 00:07:59,760
If top Onestop is not no, not Ullman's return, zero else means it is not return one zero means false

114
00:07:59,770 --> 00:08:00,260
wonderments.

115
00:08:00,300 --> 00:08:04,760
True, but if top is not null, then return false or otherwise return true.

116
00:08:04,950 --> 00:08:08,430
That is full function for checking whether it's full or not.

117
00:08:08,430 --> 00:08:09,950
Create a node and check.

118
00:08:10,530 --> 00:08:14,430
So create a node and check if that is not null.

119
00:08:14,430 --> 00:08:20,600
If it is not assign one to R otherwise assign zero to one minstrel's false means.

120
00:08:20,680 --> 00:08:27,720
If it is not null assign to otherwise assign false and after that delete the node and return not so

121
00:08:27,720 --> 00:08:30,150
whatever the value was stored in all that will be returned.

122
00:08:30,330 --> 00:08:31,390
True or false.

123
00:08:31,950 --> 00:08:34,880
So that's all these other functions for Stack.

124
00:08:35,429 --> 00:08:39,360
So all the operations you have seen and every operation take constant time.

125
00:08:39,390 --> 00:08:41,110
All are simple functions.

126
00:08:41,700 --> 00:08:48,860
So whenever we need a stack we may be using either side using array or we may be using stock using legalists.

127
00:08:49,170 --> 00:08:51,560
Whichever one you want, you can use that one.

