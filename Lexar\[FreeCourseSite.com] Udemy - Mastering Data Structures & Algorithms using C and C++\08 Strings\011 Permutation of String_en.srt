1
00:00:00,620 --> 00:00:03,790
The topic is permutations of a given strain.

2
00:00:04,520 --> 00:00:08,780
So first of all, we understand what does it mean by pigmentation, then we will see them, they're

3
00:00:08,790 --> 00:00:11,370
told, how to find permutations.

4
00:00:11,810 --> 00:00:13,020
There are more than one method.

5
00:00:13,040 --> 00:00:14,910
So in this video, I'll show you one method.

6
00:00:14,910 --> 00:00:19,610
Then in the coming video, you can find the second method for finding permutations.

7
00:00:20,420 --> 00:00:23,490
So, first of all, let us understand, what does it mean by permutations?

8
00:00:24,350 --> 00:00:28,100
See, there is a string here, alphabets, ABC are there.

9
00:00:28,580 --> 00:00:33,370
Then we want all permutations with all arrangements of ABC.

10
00:00:33,710 --> 00:00:42,290
So like if ABC is given, then one of the permutation, one of the arrangement is ABC, then a CB is

11
00:00:42,290 --> 00:00:53,690
the first one is the second one, Terminus B AC 41 BCE and fifth one is starting with C, then first

12
00:00:53,690 --> 00:00:55,640
aid and B then six.

13
00:00:55,640 --> 00:00:58,160
One is starting with C, first B, then eight.

14
00:00:58,550 --> 00:01:03,450
So there are total six possible arrangement of those alphabets of a string.

15
00:01:04,099 --> 00:01:09,920
So how many if there are three characters in a string then how many permutations are possible.

16
00:01:10,340 --> 00:01:16,050
Three factorial possible it for and how many possible and factorial are possible.

17
00:01:16,820 --> 00:01:19,700
So we want all those arrangements.

18
00:01:21,010 --> 00:01:27,250
Now, how to get those arrangements so far that we will devise some procedure or we will devise some

19
00:01:27,250 --> 00:01:31,000
logic for generating all those permutations.

20
00:01:31,300 --> 00:01:36,640
So first, let us do some border work to get the idea how we can get those permutations.

21
00:01:36,670 --> 00:01:38,080
So here, I'll do the boardwalk.

22
00:01:38,590 --> 00:01:40,500
See, let us say this is a starting point.

23
00:01:41,350 --> 00:01:49,240
And next, the first alphabet, I'll say first alphabet eight, the next alphabet, B, the next alphabet

24
00:01:49,240 --> 00:01:49,570
S..

25
00:01:49,820 --> 00:01:53,770
So here I got the result, A, B, C, if you come along this one.

26
00:01:53,770 --> 00:01:54,120
Right.

27
00:01:54,880 --> 00:01:56,830
So this is I'm forming a tree.

28
00:01:57,220 --> 00:01:57,510
Right.

29
00:01:57,850 --> 00:02:00,050
So from here, go back.

30
00:02:00,400 --> 00:02:03,160
So after that B, there was only one alphabet.

31
00:02:03,430 --> 00:02:10,620
OK, go back to A when I'm on A I can go on B as well as I can go on C so we already have taken.

32
00:02:10,840 --> 00:02:15,550
So let us go on C then from E I have gone on C..

33
00:02:15,550 --> 00:02:17,700
So what is remaining B's remaining.

34
00:02:17,980 --> 00:02:20,470
So then what is the B.C..

35
00:02:20,560 --> 00:02:22,630
So this is B C a.

36
00:02:23,990 --> 00:02:30,980
So I got two different arrangements so I can show these arrangements in the form of a three, yes.

37
00:02:31,220 --> 00:02:36,390
Then after there was only one letter B, then after A, B and C, both are over.

38
00:02:36,680 --> 00:02:44,450
So let us go to the next letter B if I start with B, next letter should be either A or C, so first

39
00:02:44,450 --> 00:02:46,970
I will take it, then I will take C.

40
00:02:47,120 --> 00:02:53,510
So this is another permutation that is B, A, C, so that is B, C, then go back.

41
00:02:53,540 --> 00:03:02,810
After all, if he was left after B, C is possible then next is A for B, C, E this is B, C under

42
00:03:02,810 --> 00:03:07,460
the permutation so far, B and C both we have tried.

43
00:03:07,640 --> 00:03:13,310
So let us go back to the main starting point and take the next letter C so now I am starting with C,

44
00:03:13,310 --> 00:03:21,500
so what are remaining in B, so first I'll go to A, then B then it is C, A, B or else I will go back

45
00:03:21,920 --> 00:03:29,660
and from here I will take first B, then A, then it is C, B, A that's all.

46
00:03:29,810 --> 00:03:32,360
One, two, three, four, five, six.

47
00:03:32,630 --> 00:03:34,480
These are six permutations.

48
00:03:34,670 --> 00:03:35,060
Yeah.

49
00:03:35,150 --> 00:03:38,860
Here I have to write a c b yes.

50
00:03:38,870 --> 00:03:45,980
This is A, C, B so I have total six permutations that are what we want at six permutations.

51
00:03:46,400 --> 00:03:48,580
So this is like forming like a three.

52
00:03:49,580 --> 00:03:54,050
So this tree is representing all various possible solutions.

53
00:03:54,350 --> 00:03:59,840
So this three we can call it as state space three.

54
00:04:01,360 --> 00:04:08,830
States based tree where the leaves are showing us the results, then one more thing we will observe

55
00:04:08,830 --> 00:04:09,550
about the tree.

56
00:04:10,450 --> 00:04:11,390
Start from here.

57
00:04:11,620 --> 00:04:13,510
Go to a go to be.

58
00:04:13,840 --> 00:04:14,650
Go to see.

59
00:04:15,130 --> 00:04:15,700
Go back.

60
00:04:16,480 --> 00:04:17,140
Go back.

61
00:04:17,709 --> 00:04:19,750
So again from there.

62
00:04:19,990 --> 00:04:20,890
Going to see.

63
00:04:21,320 --> 00:04:25,690
So this going back and again taking another route this weekend.

64
00:04:25,690 --> 00:04:28,070
Call it guys back tracking.

65
00:04:28,390 --> 00:04:32,170
So this is representing back tracking.

66
00:04:35,580 --> 00:04:40,960
So we have learned one more time for two terms, the state's history and this is backtracking, the

67
00:04:40,980 --> 00:04:42,150
approach is backtracking.

68
00:04:42,630 --> 00:04:44,280
And one more thing, one more thing.

69
00:04:44,280 --> 00:04:51,600
The line here see from this ABC, what all possible permutations, all are shown here.

70
00:04:51,600 --> 00:04:54,400
All are everything is explored here.

71
00:04:54,720 --> 00:04:58,620
So this is called us brute force.

72
00:05:01,350 --> 00:05:02,210
Brute force.

73
00:05:02,700 --> 00:05:04,280
See, these are different tones.

74
00:05:04,560 --> 00:05:05,220
I'll explain.

75
00:05:05,220 --> 00:05:09,210
You brute force means finding out all possible permutations.

76
00:05:09,390 --> 00:05:11,100
That is the meaning of brute force.

77
00:05:11,460 --> 00:05:12,360
How you do.

78
00:05:12,540 --> 00:05:13,290
It's up to you.

79
00:05:13,560 --> 00:05:16,110
Here we are using back tracking.

80
00:05:16,590 --> 00:05:17,640
So backtracking.

81
00:05:17,640 --> 00:05:23,280
If you observe in this country we have gone on to A, then B, then C, then we got the result and we

82
00:05:23,280 --> 00:05:24,360
went back then.

83
00:05:24,360 --> 00:05:26,340
Back then we took another route.

84
00:05:26,610 --> 00:05:29,280
We have went back and taken another possible route.

85
00:05:29,760 --> 00:05:31,980
That's what is back tracking.

86
00:05:33,390 --> 00:05:34,700
These are the two things you learn.

87
00:05:35,590 --> 00:05:43,290
Now, the last and a very important thing is see, whenever you have a procedure in the procedure,

88
00:05:43,290 --> 00:05:50,510
if you have to go back and take another route, then those procedures can be implemented using the recursion.

89
00:05:51,030 --> 00:05:52,740
Yes, recursion.

90
00:05:55,710 --> 00:06:01,860
See, we have to use recursion to achieve backtracking, and with the help of backtracking, we are

91
00:06:01,860 --> 00:06:03,590
performing brute force.

92
00:06:04,140 --> 00:06:06,270
So this will look like all the same thing.

93
00:06:06,270 --> 00:06:09,260
But these are different recursion, is that procedure?

94
00:06:09,510 --> 00:06:10,440
It can do anything.

95
00:06:11,370 --> 00:06:12,250
It can do anything.

96
00:06:12,270 --> 00:06:18,030
There are various forms of recursive procedure, but a recursive procedure which is trying to explore

97
00:06:18,030 --> 00:06:20,640
everything as a backtracking procedure.

98
00:06:21,460 --> 00:06:28,210
Right, and if a procedure is backtracking and find out, finding out everything, then that procedure

99
00:06:28,210 --> 00:06:30,120
is called brute force.

100
00:06:30,880 --> 00:06:32,630
So this can be done using loops.

101
00:06:32,650 --> 00:06:36,810
Also, if you want, but you have to use the stock for using glue.

102
00:06:37,030 --> 00:06:40,230
So this is mostly implemented using recursion.

103
00:06:40,540 --> 00:06:47,780
So backtracking is implemented using recursion and backtracking is used for achieving brute force or

104
00:06:47,800 --> 00:06:49,560
picking up brute force approach.

105
00:06:50,650 --> 00:06:51,020
Right.

106
00:06:51,090 --> 00:06:55,340
So these are the terms that we have learned from this example, right?

107
00:06:55,530 --> 00:06:57,920
So the example is to find all permutations.

108
00:06:58,860 --> 00:07:00,720
Now we will work on the example.

109
00:07:00,720 --> 00:07:02,610
We will study this state's history.

110
00:07:02,820 --> 00:07:07,850
Then we will try to devise a recursive function which will generate all these things.

111
00:07:08,700 --> 00:07:11,250
So let us look on recursive function.

112
00:07:13,760 --> 00:07:20,360
For writing a recursive function, let us observe the state's Space Street, see, it is starting from

113
00:07:20,360 --> 00:07:22,130
here and we know the answers.

114
00:07:22,130 --> 00:07:24,110
We are getting the answers at the leaf nodes.

115
00:07:24,140 --> 00:07:24,470
Yes.

116
00:07:24,830 --> 00:07:31,100
This ABCP one, we are getting the answers at the leaf as it is recursive, let us say, is starting

117
00:07:31,100 --> 00:07:31,580
from here.

118
00:07:31,820 --> 00:07:33,130
So this is the first call.

119
00:07:33,320 --> 00:07:34,110
This is the first call.

120
00:07:34,130 --> 00:07:35,300
This is within the first call.

121
00:07:35,540 --> 00:07:36,350
Within the first call.

122
00:07:36,350 --> 00:07:37,630
Really taking first letter.

123
00:07:38,150 --> 00:07:41,150
Then it is calling itself in second call again.

124
00:07:41,150 --> 00:07:42,470
It is calling in second call.

125
00:07:42,480 --> 00:07:46,190
We take a letter B, then the next call will take a letter, see.

126
00:07:47,030 --> 00:07:50,060
And then it will go back to the previous call, go back to the previous call.

127
00:07:50,420 --> 00:07:56,240
Then in this call again it will call itself so it means being monitored is calling on B also C also

128
00:07:56,720 --> 00:07:57,640
bought it is calling.

129
00:07:58,010 --> 00:08:02,180
So when we have taken C then we will take the missing one.

130
00:08:02,400 --> 00:08:03,620
Remember this missing one.

131
00:08:03,620 --> 00:08:06,340
So we should know which is missing, which is not yet taken.

132
00:08:06,590 --> 00:08:11,410
So after a we are directly on CS of these missing we should know which is taken, which is missing,

133
00:08:11,660 --> 00:08:14,650
it will take B and I believe it will print HCB.

134
00:08:14,690 --> 00:08:16,280
These are the three letters DaVita's.

135
00:08:17,550 --> 00:08:18,010
Right.

136
00:08:18,350 --> 00:08:21,260
So if you observe in the first call it is going on for slitter.

137
00:08:21,260 --> 00:08:27,050
Also second letter also totally droutsas going on on then if I dialed on to the next level and the next

138
00:08:27,050 --> 00:08:31,340
level leaving the first one which is already selected, it is selecting board B and C.

139
00:08:32,270 --> 00:08:40,130
Yes, so it means it is what's interesting in each call and picking those which are not yet selected

140
00:08:40,760 --> 00:08:44,580
and I believe it is preparing a single string and displaying a string.

141
00:08:44,990 --> 00:08:47,800
So we need a few data structures here already.

142
00:08:47,810 --> 00:08:50,790
We have a string, but we need to prepare the result.

143
00:08:51,420 --> 00:08:54,110
So let us take a result so we take an array.

144
00:08:54,110 --> 00:08:59,780
As a result, they should be of the same size zero one, two, three.

145
00:09:00,020 --> 00:09:02,830
So in this we will be storing this alphabet's ABC.

146
00:09:03,320 --> 00:09:06,350
Then we should also know which one is included, which is not included.

147
00:09:06,650 --> 00:09:09,080
So for that we will maintain a flag array.

148
00:09:09,110 --> 00:09:12,710
So I will call it as a right and this is zero.

149
00:09:12,710 --> 00:09:13,660
One, two, three.

150
00:09:14,540 --> 00:09:18,830
And initially all these values are zero means nothing is selected.

151
00:09:20,350 --> 00:09:26,530
Now, I will trace this history and prepare a few strings and give you the idea then based on that,

152
00:09:26,650 --> 00:09:29,410
it's a really very small function over on the function.

153
00:09:29,410 --> 00:09:33,400
Also here, let us first observe how it is working.

154
00:09:33,670 --> 00:09:34,930
Now, let us trace this one.

155
00:09:34,960 --> 00:09:42,280
So for this, we need to start with the key, that key as zero that is in this call key is zero.

156
00:09:43,450 --> 00:09:49,520
All right, so zero four that we will take A, B and C. So we should know which one is taken, which

157
00:09:49,520 --> 00:09:54,240
one is not taken, and we have to scan through the string for that we will take in here.

158
00:09:54,920 --> 00:10:00,140
So in the first call, case zero and here I is zero right now.

159
00:10:00,860 --> 00:10:05,270
Now, in the first call, we will check whether this aof is a zero or not.

160
00:10:05,270 --> 00:10:06,130
Is it a zero?

161
00:10:06,410 --> 00:10:11,270
So we'll make this one right and write it here.

162
00:10:11,870 --> 00:10:18,020
And the call itself again, call it again recursively so that it is calling again.

163
00:10:18,020 --> 00:10:20,210
It will call for the next level.

164
00:10:20,360 --> 00:10:23,760
So key should be on next location.

165
00:10:24,140 --> 00:10:32,270
So at this level, K is at 1:00 then what about I'm not at this level.

166
00:10:32,270 --> 00:10:41,110
Also I should scan for the entire string for again start i.e. from zero and lead starky from zero I

167
00:10:41,120 --> 00:10:41,660
zero.

168
00:10:41,660 --> 00:10:43,000
Is that alphabet available.

169
00:10:43,220 --> 00:10:46,100
No, we have already taken that eight plus plus.

170
00:10:46,580 --> 00:10:48,060
Is this alphabet available.

171
00:10:48,410 --> 00:10:49,600
Yes it is available.

172
00:10:49,820 --> 00:10:56,470
Someone on this one are displaced place there in the string so right to be right.

173
00:10:56,840 --> 00:11:03,560
So I is now at one then call itself again for the next level.

174
00:11:03,560 --> 00:11:07,640
Whenever you got a letter call level that is Cabelas one.

175
00:11:07,820 --> 00:11:11,270
So it has to call for the next level that is for this alphabet.

176
00:11:11,270 --> 00:11:11,580
Right.

177
00:11:11,840 --> 00:11:15,290
So now is to at this level is to.

178
00:11:16,610 --> 00:11:21,920
Now, here also it should scan through the entire string and take those alphabets, which are missing

179
00:11:22,160 --> 00:11:24,470
fish or take an alphabet that is missing so far.

180
00:11:24,770 --> 00:11:30,490
So, again, I start from zero check as if there were no next one.

181
00:11:30,600 --> 00:11:31,580
I placeless.

182
00:11:31,580 --> 00:11:33,990
Is it available nor is it available.

183
00:11:34,010 --> 00:11:34,600
Yes.

184
00:11:34,610 --> 00:11:40,610
So I use here marketed as one and that this place to the literacy so to see.

185
00:11:41,600 --> 00:11:46,860
Right, so right now, eyes are to their eyes that too White has got the character.

186
00:11:47,030 --> 00:11:52,240
So, again, it should call itself so this time again, case starts from next.

187
00:11:52,640 --> 00:11:53,750
That is Kate plus one.

188
00:11:53,750 --> 00:11:55,810
So it will call itself the next alphabet.

189
00:11:56,370 --> 00:12:00,260
Now, again, they should scan the string, but no check here.

190
00:12:00,890 --> 00:12:06,110
Key has reach index three and in the string main string it is zero.

191
00:12:06,380 --> 00:12:11,900
Minsky has reach at the end of a string no doubt gave representing the string.

192
00:12:11,900 --> 00:12:18,050
But at the same place, if you see matching place that is another character means which could not go

193
00:12:18,050 --> 00:12:20,450
further or we should not process anything here.

194
00:12:20,630 --> 00:12:22,650
We have reached the end and we got the result.

195
00:12:22,790 --> 00:12:27,240
So at this place again, waterslides zero and bring this one.

196
00:12:27,450 --> 00:12:29,570
So the next call printing is done.

197
00:12:30,770 --> 00:12:36,360
And the next call key is equal to three, printing is done and we will not scan for this string.

198
00:12:36,650 --> 00:12:38,710
So this is one step I have shown you.

199
00:12:38,720 --> 00:12:40,950
We have reached the result for the first time.

200
00:12:41,360 --> 00:12:46,580
Now, if you have understood this, then you can understand all other, though I have to continue.

201
00:12:46,580 --> 00:12:47,150
I love you.

202
00:12:47,300 --> 00:12:49,520
I'll show you a few more than how it is working.

203
00:12:49,520 --> 00:12:50,780
You can understand it clearly.

204
00:12:51,260 --> 00:12:51,970
Not till here.

205
00:12:51,980 --> 00:12:53,770
It should be clear what all has happened.

206
00:12:54,050 --> 00:12:55,060
This was the first call.

207
00:12:55,070 --> 00:12:56,400
First time cable was at zero.

208
00:12:56,420 --> 00:12:57,260
This is the result.

209
00:12:57,560 --> 00:12:57,780
Right?

210
00:12:58,070 --> 00:13:01,730
Then we got A, then we got B, then we got C, then we got reached the end.

211
00:13:01,790 --> 00:13:03,140
So we have been told ABC.

212
00:13:04,470 --> 00:13:08,470
Right now, once you have printed ABC, don't go further.

213
00:13:08,790 --> 00:13:11,070
Go back, go back to the previous call.

214
00:13:11,310 --> 00:13:17,760
So go back to the previous columns where the mosque, where was I let us know gave was at two and I

215
00:13:17,760 --> 00:13:20,190
was also asked to sooky was that too.

216
00:13:20,520 --> 00:13:27,150
I was also asked to learn what to do here, what we were doing, scanning the screen continuously,

217
00:13:27,150 --> 00:13:29,250
scanning the screen, continuous scanning.

218
00:13:29,580 --> 00:13:33,840
So before going to the next make this are zero and proceed.

219
00:13:34,530 --> 00:13:36,720
If you reach here this is none.

220
00:13:37,320 --> 00:13:42,500
So finish scanning, finish scanning in this light.

221
00:13:42,870 --> 00:13:44,480
So finish scanning this again.

222
00:13:44,500 --> 00:13:45,830
Go back to the previous call.

223
00:13:46,140 --> 00:13:50,070
So in the previous call their K was and where I was.

224
00:13:51,080 --> 00:13:54,270
Give that one I was at also one.

225
00:13:54,620 --> 00:14:00,230
So what you are doing here, scanning through the stream for continuous scanning, but before that,

226
00:14:00,350 --> 00:14:02,280
make this a zero and proceed.

227
00:14:02,840 --> 00:14:07,840
So when you go back, make that I said, all right, make it a zero, then continue scanning.

228
00:14:08,060 --> 00:14:13,220
OK, continuous scanning means next E-Plus plus is it available?

229
00:14:13,220 --> 00:14:16,410
If that is zero, that is zero.

230
00:14:16,610 --> 00:14:18,020
Yes, it has to be taken.

231
00:14:18,380 --> 00:14:21,110
So at this place it comes to this one.

232
00:14:21,260 --> 00:14:23,810
Right is one and I use one.

233
00:14:23,810 --> 00:14:25,160
Here is one.

234
00:14:25,430 --> 00:14:27,290
But I is it do.

235
00:14:27,290 --> 00:14:31,100
Yes is one and is two and this is available.

236
00:14:31,280 --> 00:14:37,040
So whenever the letter is available, market as one, take that letter calling itself again for the

237
00:14:37,040 --> 00:14:37,760
next letter.

238
00:14:39,070 --> 00:14:46,720
Call it self again, so key is now to then again start scanning the things, every call we are scanning

239
00:14:46,720 --> 00:14:47,540
for the whole thing.

240
00:14:47,900 --> 00:14:49,280
I start from here.

241
00:14:49,570 --> 00:14:51,100
This is a development.

242
00:14:51,250 --> 00:14:52,190
No, not available.

243
00:14:52,660 --> 00:14:53,740
Next, a plus.

244
00:14:53,740 --> 00:14:54,910
Plus, is it available?

245
00:14:54,940 --> 00:14:55,320
Yes.

246
00:14:55,330 --> 00:14:56,250
That is not taken.

247
00:14:56,590 --> 00:15:02,290
So make it as one and the right to be wherever this case is pointing because this is me.

248
00:15:02,650 --> 00:15:03,130
So right.

249
00:15:03,130 --> 00:15:09,790
Be here if we got that letter B, so where I is pointing right now is pointing at one.

250
00:15:10,360 --> 00:15:10,840
Yes.

251
00:15:12,400 --> 00:15:18,680
Then call again itself again, so if you have call again, it's health care has regional so has regional

252
00:15:18,710 --> 00:15:22,630
elements in this call bring this is printed now after printing.

253
00:15:22,630 --> 00:15:25,510
Go back to the previous call where Kim was in the previous call.

254
00:15:25,720 --> 00:15:27,610
Previous call came was at 2:00.

255
00:15:27,800 --> 00:15:30,220
Then where I was I was at one.

256
00:15:31,600 --> 00:15:33,820
Then what to do continuous scanning.

257
00:15:34,030 --> 00:15:38,910
So what before that make this as zero and continue as it can available.

258
00:15:39,220 --> 00:15:40,970
No, it's not available next.

259
00:15:41,020 --> 00:15:43,290
Is it available or you have regional.

260
00:15:43,630 --> 00:15:45,270
So you have finished with this level.

261
00:15:46,330 --> 00:15:47,530
Go back to the previous.

262
00:15:47,530 --> 00:15:49,370
Once you have finished, go back to previous work.

263
00:15:49,370 --> 00:15:55,510
It was at 1:00 where I was at two keyboards was at one and I was at two.

264
00:15:56,230 --> 00:15:56,910
What to do?

265
00:15:57,460 --> 00:16:00,250
Whenever you are going back, you have to mark that at zero.

266
00:16:00,400 --> 00:16:02,080
Yes, Mark, this is zero.

267
00:16:02,350 --> 00:16:04,470
Then continue scanning next.

268
00:16:05,020 --> 00:16:05,740
That is number.

269
00:16:06,250 --> 00:16:09,970
So we have regional then go back, go back and switch.

270
00:16:09,970 --> 00:16:11,530
Call now this one.

271
00:16:11,830 --> 00:16:12,940
Right, this one.

272
00:16:13,180 --> 00:16:15,430
So you have finish this and finish this and finish this.

273
00:16:15,700 --> 00:16:19,840
So work was at zero where I was at zero, so key was at zero.

274
00:16:20,020 --> 00:16:21,760
I was also at zero.

275
00:16:22,030 --> 00:16:22,810
Then what to do.

276
00:16:23,080 --> 00:16:25,780
Make it to zero and continue next.

277
00:16:25,930 --> 00:16:33,280
So make this at zero and continue next for I is here now I will show you one step that will be sufficient

278
00:16:33,280 --> 00:16:34,720
for you to understand everything.

279
00:16:35,380 --> 00:16:37,960
Not in this step we are scanning through next.

280
00:16:37,960 --> 00:16:39,490
So we are here now.

281
00:16:39,700 --> 00:16:41,350
So there is a key here.

282
00:16:41,350 --> 00:16:43,990
Is that zero where I is now?

283
00:16:44,020 --> 00:16:47,020
I use that one and is inevitable.

284
00:16:47,020 --> 00:16:48,460
Yes, that's why I'm writing it.

285
00:16:48,670 --> 00:16:50,860
So make it as one.

286
00:16:50,860 --> 00:16:51,610
And the copy.

287
00:16:51,610 --> 00:16:52,810
This had a key.

288
00:16:52,810 --> 00:16:54,370
So this becomes B.

289
00:16:56,130 --> 00:17:00,900
Then call it self again, so gay becomes one, gay becomes one.

290
00:17:00,930 --> 00:17:02,390
I start from again.

291
00:17:02,760 --> 00:17:03,750
So is it available?

292
00:17:03,800 --> 00:17:04,329
Yes.

293
00:17:04,349 --> 00:17:06,990
Make it as one copy here.

294
00:17:08,550 --> 00:17:09,630
Make it as one.

295
00:17:09,630 --> 00:17:14,630
So Izak zero right now and copy it here, then call it self again.

296
00:17:15,240 --> 00:17:16,859
Will be on next and again.

297
00:17:16,859 --> 00:17:19,369
Starky from zero is inevitable.

298
00:17:19,530 --> 00:17:20,150
Is inevitable.

299
00:17:21,030 --> 00:17:26,579
Yes, I live here so make it as one copy that like that us see.

300
00:17:28,400 --> 00:17:36,320
So gave that to an IED attack to faceprint BFE, so here you can see that it is BFE.

301
00:17:36,350 --> 00:17:40,690
Yes, we got that permutation now as we have this is nulls.

302
00:17:40,730 --> 00:17:42,410
We have been ordered not to go back.

303
00:17:42,720 --> 00:17:43,970
Go back with Givaudan.

304
00:17:43,970 --> 00:17:46,390
I was can I go to Iraq, too?

305
00:17:46,400 --> 00:17:51,420
So this is to then what to do whenever you're going back, make it a zero and continue.

306
00:17:51,530 --> 00:17:52,410
So there is nothing.

307
00:17:53,360 --> 00:17:59,540
So again, go back to that one and I zero so gave that one and I leave that to zero.

308
00:17:59,540 --> 00:18:00,140
What to do.

309
00:18:00,140 --> 00:18:01,720
Make it a zero and continue.

310
00:18:02,030 --> 00:18:02,770
So continue.

311
00:18:02,780 --> 00:18:03,750
This has already taken.

312
00:18:03,770 --> 00:18:05,000
This is not taken.

313
00:18:05,390 --> 00:18:05,900
So yes.

314
00:18:05,900 --> 00:18:07,670
Now we get to see.

315
00:18:07,670 --> 00:18:08,310
So here.

316
00:18:08,690 --> 00:18:15,410
So the second step in this call, so case that one only but this will be selected and see will be done

317
00:18:15,410 --> 00:18:17,950
here then obviously A will come next.

318
00:18:17,960 --> 00:18:19,320
So that's how it got it all.

319
00:18:19,610 --> 00:18:23,060
Now let us write a function for performing this permutation.

320
00:18:23,060 --> 00:18:25,040
So here I will write on that function.

321
00:18:25,340 --> 00:18:28,400
So let us start writing the function word.

322
00:18:28,640 --> 00:18:36,980
I will write on the function name as BRM for presentation and it should take array of characters that

323
00:18:37,160 --> 00:18:40,670
string it should pick and also it should take key.

324
00:18:40,880 --> 00:18:43,580
That is the goal number, that is for others.

325
00:18:43,790 --> 00:18:45,470
You remember gave zero.

326
00:18:45,470 --> 00:18:51,620
Here is one case to say every call it will be incrementing can calling itself that.

327
00:18:52,010 --> 00:18:58,670
Inside this function we need these stories which must be permanently there in every call.

328
00:18:58,670 --> 00:18:59,410
They must be present.

329
00:18:59,420 --> 00:19:00,940
So I should declare them as static.

330
00:19:01,340 --> 00:19:06,020
So if you declare anything as static, then it will be available for all function calls.

331
00:19:06,290 --> 00:19:10,610
So I will take an array of size and a and that is initialized with the zero.

332
00:19:10,850 --> 00:19:15,940
And also I will take a static character type array as a result.

333
00:19:16,100 --> 00:19:18,970
And this also I'll call it as size ten.

334
00:19:19,190 --> 00:19:20,420
So these two have become.

335
00:19:22,270 --> 00:19:29,230
Now, next, what I have to do, I have to scan through a string and print it, so for scanning through

336
00:19:29,230 --> 00:19:32,100
a string, I need a variable, I will take it.

337
00:19:32,110 --> 00:19:39,370
And here, using a for loop, I will scan through for ISIS zero as long as as of eye is not equal to

338
00:19:39,370 --> 00:19:40,450
null character.

339
00:19:40,930 --> 00:19:46,930
I plus plus scan for the string using a folder which can scan for string.

340
00:19:47,410 --> 00:19:52,190
Then while scanning what we have to do every time, check whether this alphabet is available or not.

341
00:19:52,210 --> 00:19:58,330
So I have to check if any of i.e. if it is zero alphabet is available.

342
00:19:58,660 --> 00:20:00,610
If it is available then what I have to do.

343
00:20:00,850 --> 00:20:03,140
Copy an alphabet from asset to rest.

344
00:20:03,510 --> 00:20:03,790
Right.

345
00:20:04,090 --> 00:20:10,390
So from where I'm from I have to copy wherever case pointing like suppose if I'm copying this alphabet.

346
00:20:10,390 --> 00:20:10,590
Right.

347
00:20:10,840 --> 00:20:12,230
So see I have to copy here.

348
00:20:12,610 --> 00:20:13,990
So from.

349
00:20:15,100 --> 00:20:24,520
As of a copy and two arrests of key, yes, arrests of key, copy that Alphabet wrote after copying

350
00:20:24,520 --> 00:20:30,250
that alphabet inside of a market as one of a market as one.

351
00:20:31,800 --> 00:20:38,310
The next step is what work call itself again with the next value Ofakim call itself again, that is,

352
00:20:38,350 --> 00:20:43,020
I'm calling the function names bomb BRM permutation.

353
00:20:43,260 --> 00:20:46,290
So ECES a string is as it is, but K plus one.

354
00:20:46,800 --> 00:20:47,290
Yes.

355
00:20:47,670 --> 00:20:48,990
So this will call itself.

356
00:20:49,260 --> 00:20:52,280
So this is for loop and this all happening inside if.

357
00:20:53,660 --> 00:21:02,840
And when the function call is returning, I should make this I as zero back again, so aof I as zero

358
00:21:02,840 --> 00:21:09,800
back again, there's the stuff I was doing and this all happening if I use the Seydou that's on the

359
00:21:09,800 --> 00:21:10,810
end of the for loop.

360
00:21:11,090 --> 00:21:15,860
So these are the things that are inside F and that's all happening and for loop.

361
00:21:17,030 --> 00:21:24,260
Then one last thing, when we have reesha care, the last that is, it is null at this place, it is

362
00:21:24,260 --> 00:21:25,890
no sort of blueprint.

363
00:21:26,180 --> 00:21:28,010
So that I will right here if.

364
00:21:28,770 --> 00:21:40,620
As Off-Key is equal to none character, then printf reserved printf percentile as Brender isn't right,

365
00:21:40,950 --> 00:21:49,860
then in a result of key Mogk slash zero and also bring the results.

366
00:21:49,860 --> 00:21:51,600
So bring their result.

367
00:21:51,630 --> 00:21:53,160
I'm just writing it as print.

368
00:21:54,000 --> 00:21:54,990
So this I have to do.

369
00:21:54,990 --> 00:22:02,940
If otherwise, if it is not enough then this whole thing should be done for this procedure is in a spot

370
00:22:03,090 --> 00:22:06,810
right then we have not reached nullifier that's not regional.

371
00:22:06,820 --> 00:22:11,880
Then do all this thing, have to have regional then printed so that I have written it in the beginning.

372
00:22:12,040 --> 00:22:13,290
So this is the procedure.

373
00:22:13,500 --> 00:22:18,000
And if you want to call this function from mean function, so I'll just write on here.

374
00:22:18,360 --> 00:22:25,350
If inside a mean function, if you have a character string s and it is having letters A, B, C in that,

375
00:22:25,650 --> 00:22:33,870
then call this function BRM by sending S and starting value of K should be zero, it will print all

376
00:22:33,870 --> 00:22:37,650
the permutations that song C, this is the procedure.

377
00:22:37,650 --> 00:22:40,020
I have shown you the detailed working of this one.

378
00:22:40,590 --> 00:22:44,540
Thus you have to do it by yourself, by using pen and paper.

379
00:22:44,850 --> 00:22:51,810
If you do it by yourself using pen and paper, then you can understand clearly like C what I was discussing

380
00:22:51,810 --> 00:22:52,020
this.

381
00:22:52,020 --> 00:22:57,900
I was saying, okay, I, I saw this once you put it on paper, you can understand clearly keep this

382
00:22:58,110 --> 00:23:03,180
on the screen, just pause and take a pen and paper and work out by yourself.

383
00:23:03,450 --> 00:23:06,000
Then you should be able to devise this fancy.

384
00:23:06,000 --> 00:23:09,810
I have devised the whole function by discussing each and every team.

385
00:23:10,020 --> 00:23:10,380
Right.

386
00:23:10,680 --> 00:23:12,540
So you should be able to be like this.

387
00:23:12,540 --> 00:23:16,500
Now, this will help you to devise an algorithm for any problem.

388
00:23:17,100 --> 00:23:23,040
I no doubt initially would take some extra time for you, much time for you, but slowly as you are

389
00:23:23,040 --> 00:23:26,000
in practice, then you can quickly answer these things.

390
00:23:26,700 --> 00:23:29,520
So this needs your practice that is more important.

391
00:23:29,790 --> 00:23:31,500
So perform all these things.

392
00:23:31,500 --> 00:23:37,410
What I have explained here, the complete problem, you do it by yourself once writing a function,

393
00:23:37,410 --> 00:23:39,660
coding it and seeing the output is not important.

394
00:23:39,930 --> 00:23:42,330
Framing a logic and procedure is more important.

395
00:23:42,510 --> 00:23:48,390
So you will concentrate on reframing this procedure by yourself that followed this procedure.

396
00:23:48,750 --> 00:23:53,730
Let us look at the second method for generating permutations of a given string.

397
00:23:54,060 --> 00:23:58,110
Second method is not much different at the same as the first method.

398
00:23:58,110 --> 00:23:59,250
Most of the things are same.

399
00:23:59,580 --> 00:24:01,590
Just a few differences are there.

400
00:24:01,860 --> 00:24:05,640
So let us discuss those differences already each and everything in detail.

401
00:24:05,640 --> 00:24:07,200
I have explained in the previous video.

402
00:24:07,470 --> 00:24:11,640
Just quickly, I will show you the working and also I will write on the function based on the working

403
00:24:12,270 --> 00:24:12,690
right.

404
00:24:12,690 --> 00:24:14,010
So here is a given a string.

405
00:24:14,010 --> 00:24:15,300
We want the permutations.

406
00:24:15,300 --> 00:24:15,540
Right.

407
00:24:15,840 --> 00:24:17,580
So you know well about the permutations.

408
00:24:17,590 --> 00:24:19,580
I don't have to explain what the permutations.

409
00:24:19,890 --> 00:24:21,630
Now, let us look at the procedure quickly.

410
00:24:22,080 --> 00:24:26,580
This procedure will be using, swapping, swapping of the letters.

411
00:24:26,580 --> 00:24:26,810
Right.

412
00:24:27,000 --> 00:24:28,600
So we don't need extra.

413
00:24:28,890 --> 00:24:32,330
We don't need to know which is included or not included.

414
00:24:32,730 --> 00:24:35,220
So this is just by using swopping.

415
00:24:35,760 --> 00:24:37,470
So let us see the procedure directly.

416
00:24:38,040 --> 00:24:44,420
See the first starting arrays is given so we know it is having A, B, C and null.

417
00:24:44,760 --> 00:24:47,440
So we need the starting index and ending index.

418
00:24:47,440 --> 00:24:48,960
So that is the length of string.

419
00:24:49,170 --> 00:24:51,300
So we take this as and this is high.

420
00:24:51,510 --> 00:24:51,740
Right.

421
00:24:52,110 --> 00:25:01,280
So let's start with this and s first call and the first call, what we will check reveals can throw

422
00:25:01,290 --> 00:25:02,520
a string in each call.

423
00:25:02,520 --> 00:25:02,890
Right.

424
00:25:02,910 --> 00:25:03,760
We will scan for it.

425
00:25:03,780 --> 00:25:06,500
So for that I will be able to facilitate.

426
00:25:06,750 --> 00:25:08,820
So when I is that the first letter only.

427
00:25:08,820 --> 00:25:15,660
So I will not show null now A, B, C and the law is here and heighth here I is here.

428
00:25:15,960 --> 00:25:21,240
So serve the letter at L and I so disrupted itself and called again.

429
00:25:21,960 --> 00:25:24,780
Called again then ABC.

430
00:25:24,900 --> 00:25:26,430
ABC no.

431
00:25:26,430 --> 00:25:30,150
Then you're calling again next time and this should be on the next letter.

432
00:25:30,750 --> 00:25:33,750
See Alison one and she's on to only.

433
00:25:33,750 --> 00:25:34,050
Right.

434
00:25:34,290 --> 00:25:39,210
So call this one then start scanning from where I start from here on the right.

435
00:25:39,210 --> 00:25:44,850
You don't have to pick this one so far I and L so B is a swap but it says nothing happened.

436
00:25:45,090 --> 00:25:49,020
So you don't see any changes in this first pass, then call again itself.

437
00:25:49,020 --> 00:25:52,200
So here it is in here is me in here to see Al.

438
00:25:52,210 --> 00:26:00,480
And now I know right now Alan, Angelo and I are equal so don't swap anything, bring this result.

439
00:26:00,690 --> 00:26:06,360
So we will be printing here, printing print this one right now.

440
00:26:06,360 --> 00:26:07,830
Go back to previous call.

441
00:26:07,980 --> 00:26:10,290
So previous call I was here.

442
00:26:10,440 --> 00:26:10,800
Right.

443
00:26:11,010 --> 00:26:17,330
So next call in the previous call where A, B, C low was here.

444
00:26:17,400 --> 00:26:19,560
I was here, I was on display.

445
00:26:19,560 --> 00:26:20,970
So I will move to next.

446
00:26:22,000 --> 00:26:28,930
I saw what we were doing flapping IANAL, so Ali's here is here, right?

447
00:26:29,050 --> 00:26:34,140
So, like, if I say zero one to Ali, that one and I have to interchange.

448
00:26:34,150 --> 00:26:37,720
So this will become C and this will become B.

449
00:26:39,340 --> 00:26:51,070
Right, then call again, call again, next call again, a CB right now, this ACB is not A.B.C., same

450
00:26:51,070 --> 00:26:52,380
string, right, same thing.

451
00:26:52,570 --> 00:26:54,640
And where L will be at the next place.

452
00:26:54,850 --> 00:26:55,660
At the next place.

453
00:26:55,660 --> 00:27:00,910
So I'll ask both of them are here now when they are equal then to what, Brent.

454
00:27:03,510 --> 00:27:09,980
Go back to the previous caller, right, and slap them back to their original places.

455
00:27:10,970 --> 00:27:13,610
All right, now, can I continue further?

456
00:27:13,640 --> 00:27:15,770
No, it is ended because it has reached.

457
00:27:16,160 --> 00:27:17,020
It is going beyond.

458
00:27:17,060 --> 00:27:23,960
Let's go back to the previous call here where I was at this one at the zero facility.

459
00:27:23,960 --> 00:27:29,180
And then the next call, I live here only.

460
00:27:29,180 --> 00:27:30,200
It is here only.

461
00:27:30,200 --> 00:27:34,040
And A, B, C, C, the Coliseum, actually Coliseum.

462
00:27:34,190 --> 00:27:38,540
But I will be on the next leg it here eyes on the next letter.

463
00:27:38,840 --> 00:27:43,650
So in each call and each call, we have to scan through the entire string.

464
00:27:43,880 --> 00:27:48,380
So I was in the beginning not eyes on one right then interchange.

465
00:27:48,380 --> 00:27:54,460
IANAL so this will come from B and this becomes A right then call itself.

466
00:27:54,620 --> 00:27:59,510
So then it is calling itself this is B C and will be on the next letter.

467
00:27:59,720 --> 00:28:01,130
Start from here.

468
00:28:01,250 --> 00:28:10,130
Eyes on the same place, no scanning is done, then call again itself then a b b a c this is then L

469
00:28:10,130 --> 00:28:11,210
will be on the next one.

470
00:28:11,220 --> 00:28:17,120
So as these are equal, don't do anything, just print f print stuff and go back.

471
00:28:17,250 --> 00:28:24,860
So we have done it when I was here from the next is B A C then I will be on the next letter.

472
00:28:25,500 --> 00:28:26,350
Here it is here.

473
00:28:26,600 --> 00:28:27,410
So interchange.

474
00:28:27,410 --> 00:28:30,890
So this will become C and this becomes a right.

475
00:28:31,130 --> 00:28:40,160
Then the next call here B C A L and seem so just print this one and go back when it is going back.

476
00:28:40,340 --> 00:28:43,870
It should copy the back, copy that things back again in the same place.

477
00:28:43,950 --> 00:28:46,580
So it should be a and there should be C..

478
00:28:46,760 --> 00:28:48,860
So this is AC and it's going back.

479
00:28:48,860 --> 00:28:56,540
So it should copy the things back again right in here and be here and it should continue on the next

480
00:28:56,540 --> 00:28:56,930
comp.

481
00:28:57,890 --> 00:28:58,670
Next, please.

482
00:28:59,420 --> 00:29:05,300
So this is how it works, because I will write on the procedure, then you look at the tree once again

483
00:29:05,840 --> 00:29:13,130
while I will call the function names BRM, and it should take a string and it should take Lo as well

484
00:29:13,130 --> 00:29:14,090
as high.

485
00:29:16,330 --> 00:29:23,230
Next, I need a variable that now here I will get on a follow for scanning eye from low eye is less

486
00:29:23,230 --> 00:29:35,890
than equal to high I plus plus then inside this slap string from law to I remember we will in the alphabets

487
00:29:35,890 --> 00:29:38,320
wherever I are pointing.

488
00:29:38,320 --> 00:29:40,930
If they are different place we can see the clopping.

489
00:29:41,140 --> 00:29:43,510
If they are the same place, no effect of swapping.

490
00:29:43,510 --> 00:29:44,860
Swapping will be definitely done.

491
00:29:45,400 --> 00:29:53,920
Then after that call itself again by VRM, same string then passing plus one and hire's it is.

492
00:29:55,420 --> 00:30:02,270
Call again for the next level, let alone right l here, the next level is here than it live here.

493
00:30:02,620 --> 00:30:07,950
So when it is coming back, it should slap Alphabet's at SFL.

494
00:30:08,320 --> 00:30:14,920
And as of I, we are cutting back the letters as it is right before B then going to be OK maybe for

495
00:30:14,920 --> 00:30:16,300
the next pass.

496
00:30:16,300 --> 00:30:17,600
Right next pass.

497
00:30:18,040 --> 00:30:20,430
That's all we have to do is follow up on this.

498
00:30:20,440 --> 00:30:25,510
All we have to do folo is not equal to high, if low is equal to high, but in the string.

499
00:30:25,990 --> 00:30:34,510
So here if low is equal to high print of our string s right.

500
00:30:34,870 --> 00:30:39,700
And all these things should be done else, all these things should be done.

501
00:30:40,450 --> 00:30:42,080
So here I write it myself.

502
00:30:43,300 --> 00:30:44,710
So this is inside and.

503
00:30:46,490 --> 00:30:52,190
And this is the end of the fund so that there's a simple function so you can raise this fund and again

504
00:30:52,200 --> 00:30:57,310
generate tree by by yourself and again, your work paper work is more important.

505
00:30:57,470 --> 00:31:01,790
And if you spend time on this one, then the problem is your solution is yours.

506
00:31:02,860 --> 00:31:08,390
Either way, it's just you have heard it once, that's it, and the coding is not much important here,

507
00:31:08,900 --> 00:31:11,740
think the program and looking at the result is not important.

508
00:31:12,080 --> 00:31:16,820
This content you can find anywhere, just you Google it, you can find the contents, but understanding

509
00:31:16,820 --> 00:31:23,810
it and pressing it and reframing it or making changes in this one by yourself, that is even more important.

510
00:31:24,170 --> 00:31:24,490
Right.

511
00:31:24,740 --> 00:31:30,140
So if you work on this one, then you can find a solution for any other problem.

512
00:31:30,830 --> 00:31:31,570
So that's all.

