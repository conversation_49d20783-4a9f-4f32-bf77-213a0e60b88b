1
00:00:00,430 --> 00:00:05,770
Let us see how to install a new node in a sorted list, a solid position.

2
00:00:06,340 --> 00:00:08,770
So what does it mean if you look at this link?

3
00:00:09,070 --> 00:00:11,570
It is sorted three seven nine, 15, 20.

4
00:00:11,590 --> 00:00:18,010
So all these keys are for the elements are I suppose I want to insert a new element that is 18, that

5
00:00:18,010 --> 00:00:22,540
where should come this 18 should come after 15 or before 20.

6
00:00:22,760 --> 00:00:24,160
So it should be after 15.

7
00:00:24,460 --> 00:00:29,000
So inserting a new element in a sorted list at a solid position.

8
00:00:29,020 --> 00:00:34,330
So this should be the position if suppose I want to insert then when it should come, it should come

9
00:00:34,330 --> 00:00:35,980
in between nine and 15 minutes.

10
00:00:35,980 --> 00:00:36,670
After nine.

11
00:00:36,880 --> 00:00:37,810
Before 15.

12
00:00:38,910 --> 00:00:41,860
Let us see how to do this, what should be the procedure?

13
00:00:42,240 --> 00:00:47,090
So let us take an example of cutting and try to insert it and this sorted list.

14
00:00:47,850 --> 00:00:52,950
So the first thing we will try to find out the position where this aid should be inserted so far that

15
00:00:52,950 --> 00:00:59,070
will take up help of a point and let us take be upon this node and start finding the position for this

16
00:00:59,070 --> 00:00:59,980
particular element.

17
00:01:00,360 --> 00:01:02,280
So how to find the position?

18
00:01:02,580 --> 00:01:04,700
The element here is three and this is 18.

19
00:01:04,709 --> 00:01:06,630
So three is a smaller than 18.

20
00:01:07,110 --> 00:01:08,850
So move to the next node.

21
00:01:09,600 --> 00:01:10,620
Come on to this one.

22
00:01:11,070 --> 00:01:13,160
And the seven is a smaller than 18.

23
00:01:13,170 --> 00:01:14,680
So it will come after this one.

24
00:01:14,700 --> 00:01:17,000
So just move to the next node.

25
00:01:17,520 --> 00:01:18,690
And this is nine.

26
00:01:18,690 --> 00:01:21,860
Nine is a smaller than the 18 eight inch after nine.

27
00:01:22,170 --> 00:01:24,720
So move point be the next node.

28
00:01:25,470 --> 00:01:28,380
And this is 15 and this is 18.

29
00:01:28,590 --> 00:01:30,180
15 is a smaller than the 18.

30
00:01:30,390 --> 00:01:32,360
So it should come after that one.

31
00:01:33,640 --> 00:01:34,180
Move.

32
00:01:36,040 --> 00:01:44,500
This is 20, so it should come before that 20, so we found an element which is greater than 18.

33
00:01:44,800 --> 00:01:49,230
So it means we found a position where we have to insert this 18.

34
00:01:49,240 --> 00:01:51,110
It should come before that node.

35
00:01:51,370 --> 00:01:56,140
So this key should be inserted after node 15, before P..

36
00:01:56,140 --> 00:01:57,010
Before P..

37
00:01:57,490 --> 00:02:00,520
Before inserting, before P, before this node.

38
00:02:00,730 --> 00:02:05,660
I have to modify this more, but I don't have any pointer on that front than what I should do.

39
00:02:06,460 --> 00:02:10,050
So let us talk again for bringing a pointer here.

40
00:02:10,090 --> 00:02:17,350
What I should do, start P again from here and we will take our taling pointer or following pointer.

41
00:02:17,350 --> 00:02:19,200
One pointer will be following P.

42
00:02:19,450 --> 00:02:20,800
So let us take one more point.

43
00:02:20,800 --> 00:02:24,630
A cue that is not I'm restarting the steps once again.

44
00:02:25,180 --> 00:02:28,650
Check this volume three and then three smaller.

45
00:02:28,900 --> 00:02:37,480
So if three the smaller bring you here and move P to next node then seven is a smaller than eighteen.

46
00:02:37,600 --> 00:02:39,870
So it will come after this one.

47
00:02:39,880 --> 00:02:48,490
So move Q up on B and move to next nor the nine that is smaller than 18.

48
00:02:48,730 --> 00:02:50,270
So it should be after this one.

49
00:02:50,290 --> 00:02:52,210
So still we did not find a position.

50
00:02:52,600 --> 00:03:03,010
OK, move Q move B then again move Q and move B so yes, we got an element which is greater than 18.

51
00:03:03,010 --> 00:03:05,300
So this means it should come before that.

52
00:03:05,470 --> 00:03:10,190
Indeed, that is B, so who will help this 18 to join in this at this place.

53
00:03:10,540 --> 00:03:11,530
Q will help.

54
00:03:12,190 --> 00:03:16,840
So first of all, you have to find out the position where this key has to be inserted so that we can

55
00:03:16,840 --> 00:03:20,170
take the help of good point where it can be done using single point also.

56
00:03:20,440 --> 00:03:22,090
But I'm using two pointers here.

57
00:03:22,300 --> 00:03:25,240
So Q will finally help this new node to get inserted.

58
00:03:25,290 --> 00:03:29,070
So what are the steps that are required for creating a new node and inserting it there?

59
00:03:29,380 --> 00:03:36,550
So create a new node with the T and insert evaluating then make a point on D next should point on Qs

60
00:03:36,550 --> 00:03:39,070
next and the Kucinich should pointier.

61
00:03:40,570 --> 00:03:45,670
That's one these are the steps let me I on the code for doing all these things, I would not like the

62
00:03:45,670 --> 00:03:51,310
complete executable function, just I would write on the steps that are used for inserting this note.

63
00:03:52,030 --> 00:03:55,120
First of all, we need a pointer P upon first node.

64
00:03:55,390 --> 00:03:59,410
I'm not declaring B and we need a cube, which is null.

65
00:04:00,280 --> 00:04:05,980
Then we should start searching for an element that is greater than X.

66
00:04:08,360 --> 00:04:18,380
While P and also PS not final and also BP's data is less than X.

67
00:04:19,519 --> 00:04:26,090
Go on searching for an element until you get this one or B becomes null, that each step what is happening,

68
00:04:26,570 --> 00:04:30,840
you will come up on B and B will move on to the next node.

69
00:04:31,760 --> 00:04:34,420
So this is how bank you are moving further.

70
00:04:34,430 --> 00:04:35,960
So we will stop.

71
00:04:35,960 --> 00:04:40,520
Either it finds an element greater than this one or B becomes null.

72
00:04:41,780 --> 00:04:48,200
So if he becomes null, it will go to the U.S. and Cuba will be on the last note after this, create

73
00:04:48,200 --> 00:04:50,840
a new node, insert the data and perform linking.

74
00:04:50,840 --> 00:04:55,560
So to assign new node, create a new node.

75
00:04:55,850 --> 00:04:57,680
And these data are psionics.

76
00:04:58,250 --> 00:05:01,310
So new mode is created and the data is written in this one.

77
00:05:01,550 --> 00:05:06,200
Now we have to perform two links that the city's next should point on kills.

78
00:05:06,200 --> 00:05:09,190
Next, Silicio was pointing them right here.

79
00:05:09,590 --> 00:05:12,870
So Besnik should be CU's next and then kills next.

80
00:05:12,870 --> 00:05:16,340
It should be T so t's next.

81
00:05:16,340 --> 00:05:19,010
Excuse next, then tules next to Steve.

82
00:05:19,520 --> 00:05:22,640
So these are the steps for inserting a node in a list.

83
00:05:24,480 --> 00:05:28,700
Little good analysis, how many extra pointers required, three.

84
00:05:29,940 --> 00:05:32,220
Can we do it using just two pointers?

85
00:05:32,250 --> 00:05:32,700
Yes.

86
00:05:33,270 --> 00:05:35,840
For new node and with the P, we can do it.

87
00:05:36,120 --> 00:05:40,500
So be sure to stand here and check the next node instead of checking the current node.

88
00:05:41,040 --> 00:05:42,300
With that, we can do it.

89
00:05:42,300 --> 00:05:43,530
So you can try that one.

90
00:05:43,890 --> 00:05:46,110
I'm taking three pointers smoothly.

91
00:05:46,110 --> 00:05:47,540
Everything is working now.

92
00:05:47,550 --> 00:05:48,690
How much time it is taking.

93
00:05:49,110 --> 00:05:53,010
So the time spent for creating a new node and performing lynxes constant.

94
00:05:53,430 --> 00:05:56,190
But bringing Pintu are these positions.

95
00:05:56,310 --> 00:05:58,320
It will take order of time.

96
00:05:58,560 --> 00:06:05,930
They have to traverse that traversing maybe as a minimum, as one as maximum.

97
00:06:06,330 --> 00:06:11,580
And so if I am inserting supposed five, that will get inserted here.

98
00:06:12,420 --> 00:06:15,690
If I'm inserting twenty five, then they will move till the end.

99
00:06:16,890 --> 00:06:18,060
So minimum times.

100
00:06:18,060 --> 00:06:19,020
Order of one.

101
00:06:20,670 --> 00:06:23,290
And the maximum time is order of an.

102
00:06:25,770 --> 00:06:32,850
No one thing is missing in this court, if I am giving a key that is X value that is a smaller than

103
00:06:32,850 --> 00:06:34,430
first, then what?

104
00:06:35,250 --> 00:06:41,910
So I should first check if the value is smaller than first, if so inserted on the left hand side,

105
00:06:42,210 --> 00:06:46,050
so that the gold I did not write and those conditions we did not check here.

106
00:06:46,740 --> 00:06:50,430
If suppose it is smaller, we should be inserted before first.

107
00:06:50,850 --> 00:06:54,490
So that piece of code I will write on when I write the program.

108
00:06:55,230 --> 00:06:57,660
So that's all about inserting in assorted polish.

