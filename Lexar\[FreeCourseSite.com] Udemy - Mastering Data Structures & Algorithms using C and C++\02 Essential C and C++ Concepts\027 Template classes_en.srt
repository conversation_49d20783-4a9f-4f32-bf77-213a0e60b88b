1
00:00:00,350 --> 00:00:08,670
In this video, I'll explain about template classes. C++ programming supports generic functions and generic

2
00:00:08,790 --> 00:00:16,860
classes. Generic functions are template functions, and classes are template classes. Generic functions I'm not

3
00:00:17,220 --> 00:00:21,580
explaining them separately, I'm explaining generic classes that includes generic function also.

4
00:00:22,200 --> 00:00:24,880
So already I have an example class written here,

5
00:00:24,880 --> 00:00:27,030
First I will explain that example class.

6
00:00:27,180 --> 00:00:32,110
Then I'll show you, What does it mean by generic, and I will convert it into a generic class.

7
00:00:32,159 --> 00:00:34,280
Let us look at the class.

8
00:00:34,410 --> 00:00:42,930
Here is a class called Arithmetic and 2 data members, a and b, integer type. Then, this is a constructor, constructor

9
00:00:43,030 --> 00:00:47,290
is taking 2 parameters, again I have taken parameters with the same names a and b.

10
00:00:47,610 --> 00:00:51,570
So these are the class members and these are the parameters.

11
00:00:51,600 --> 00:00:58,110
So we call this as, THIS a, or THIS b and these are parameters, a b, then I have two simple functions

12
00:00:58,110 --> 00:01:03,570
for adding those 2 numbers and returning the result, and subtracting those 2 numbers and returning the result.

13
00:01:03,900 --> 00:01:10,640
So this is a simple arithmetic class, right, so that we can focus on the template.

14
00:01:10,640 --> 00:01:16,730
Now next, here I have implemented all these functions outside the class using scope resolution operator,

15
00:01:16,770 --> 00:01:17,780
this is the end of class.

16
00:01:17,820 --> 00:01:24,570
Now, if you look at this, first as a constructor, arithmetic integer a b, so a b are parameters and

17
00:01:24,660 --> 00:01:32,550
this a b are these a b. So, to differentiate between them, now the names are same, so we have to say this caps

18
00:01:32,820 --> 00:01:38,550
a and this caps b, because this is a pointer to the current object.

19
00:01:38,550 --> 00:01:39,740
The same object.

20
00:01:39,870 --> 00:01:42,700
So -> we use, so, this -> a;

21
00:01:42,750 --> 00:01:44,520
and this -> b;

22
00:01:44,580 --> 00:01:47,580
This is a and this is b, and these 2

23
00:01:47,590 --> 00:01:55,440
are parameters. Now next, this add function, this subtract function, using class name and scope

24
00:01:55,460 --> 00:01:55,980
resolution.

25
00:01:56,040 --> 00:01:57,480
So just look at the class once.

26
00:01:57,480 --> 00:02:02,310
So you must be familiar with this already in the previous video I have explained you how to write a class,

27
00:02:02,400 --> 00:02:08,759
everything I've explained you. this is adding, and returning, this is subtracting a and b and returning. Both these

28
00:02:08,759 --> 00:02:11,860
functions are directly accessing the members.

29
00:02:11,860 --> 00:02:18,080
Now let us talk about generic classes. For understanding that, first of all let us look at class,

30
00:02:18,090 --> 00:02:24,240
This arithmetic class is performing arithmetic operation, addition and subtraction on integer type of data.

31
00:02:24,900 --> 00:02:27,780
What about float type of data?

32
00:02:28,350 --> 00:02:32,290
If I want to use long int or if I want to use a double, then what?

33
00:02:33,300 --> 00:02:38,490
So, for them this class will not work, I have to write a separate class for it.

34
00:02:39,150 --> 00:02:40,010
Yes.

35
00:02:40,200 --> 00:02:47,020
So I should write a separate class for again floating point type of data and performing arithmetic operations.

36
00:02:47,720 --> 00:02:48,550
So, shall I write

37
00:02:48,570 --> 00:02:54,600
2 different classes just for a change of data type? Though all operations are same we'll performing same

38
00:02:54,600 --> 00:02:56,770
thing for addition and subtraction.

39
00:02:56,960 --> 00:03:05,400
Shall I write another class? No. C++ says that you can use the same class for multiple data types.

40
00:03:05,410 --> 00:03:09,210
At a time, you can use only one data type, so it works for any type of data.

41
00:03:09,630 --> 00:03:15,570
So for any type of data it is called as generic class and that is defined as a template.

42
00:03:15,690 --> 00:03:21,760
So now, let us convert this class as a generic class using template. So I will just write down the code

43
00:03:21,780 --> 00:03:24,910
it is just like sprinkling some code inside this one.

44
00:03:25,080 --> 00:03:28,030
Adding something more extra to make it as generic.

45
00:03:28,080 --> 00:03:32,340
So how to change an existing class into a generic class?

46
00:03:32,340 --> 00:03:36,260
I'm explaining that one and this is what I have followed in my course.

47
00:03:36,420 --> 00:03:40,790
I have written a class, then I have made it as a generic class. Let us convert this into generic.

48
00:03:41,370 --> 00:03:48,240
So first of all, this class, the body of the class starts here and ends here, so write down template on the

49
00:03:48,240 --> 00:03:48,800
top.

50
00:03:48,810 --> 00:03:56,100
So say, template class T

51
00:03:56,310 --> 00:04:02,310
This is complete now, effect of that template will start from here and ends here.

52
00:04:03,180 --> 00:04:11,190
So the class became template, and the variable, the generic variable is T, right. Now, which part you want

53
00:04:11,190 --> 00:04:18,250
to make it as generic? These data members, so call them as T, means no data type, genetic data type

54
00:04:18,260 --> 00:04:23,930
whatever the data type you want, you can make that data type, so T can become integer, T

55
00:04:23,940 --> 00:04:27,260
can become float, or T can become double, anything.

56
00:04:27,360 --> 00:04:31,710
Now coming to this, arithmetic, parameters, What are these?

57
00:04:31,710 --> 00:04:32,910
This value will store here,

58
00:04:32,910 --> 00:04:34,040
This value will store here.

59
00:04:34,290 --> 00:04:36,090
So what is the data type of this? T.

60
00:04:36,180 --> 00:04:38,740
So what should be the data type of these? That also T,

61
00:04:38,750 --> 00:04:46,590
OK? Change it to T, this is also T, so you have to check it carefully which one will be a template. Don't

62
00:04:46,590 --> 00:04:51,420
write everywhere, wherever you find int as T. Whichever should become, you have to find out that and you have to

63
00:04:51,420 --> 00:05:01,190
change it. Then, add, it will add two numbers, integers? or float? Depending on these type, so what will it return?

64
00:05:01,460 --> 00:05:05,950
The result. If they are integer, result will be integer, if they are float, it will be float.

65
00:05:06,170 --> 00:05:08,480
So this should also be T.

66
00:05:08,540 --> 00:05:10,840
And similarly this should also be T.

67
00:05:11,180 --> 00:05:15,790
So I have made all changes that are required for making it as a template.

68
00:05:17,000 --> 00:05:21,200
Now next, the effect of template ends here.

69
00:05:21,290 --> 00:05:23,220
Now, this is the body of a function.

70
00:05:23,320 --> 00:05:24,730
What about this?

71
00:05:24,820 --> 00:05:27,860
This is a constructor belonging to this class.

72
00:05:27,860 --> 00:05:31,080
So class is a template for function also becomes template.

73
00:05:31,120 --> 00:05:33,740
So yes, write down template once again here.

74
00:05:33,750 --> 00:05:34,300
Why?

75
00:05:34,420 --> 00:05:35,980
Because this has finished here.

76
00:05:36,220 --> 00:05:37,240
So again write down,

77
00:05:37,240 --> 00:05:41,190
So that it can start end here, start from this line and end here.

78
00:05:41,200 --> 00:05:48,340
So write down, template class T.

79
00:05:49,060 --> 00:05:53,560
So if you observe here, these I've made them as T, so these will also be T.

80
00:05:53,560 --> 00:05:57,160
These are template type.

81
00:05:57,270 --> 00:06:04,870
Now one more thing, this class is a template class, so remember whenever you use the class name you should

82
00:06:04,870 --> 00:06:06,790
pass template parameter.

83
00:06:07,120 --> 00:06:10,360
So here I am using the class name as a part of scope resolution.

84
00:06:10,360 --> 00:06:19,860
So here I should write down T in brackets, then write scope resolution. This is the change you have to do, everywhere

85
00:06:19,990 --> 00:06:20,310
.

86
00:06:20,320 --> 00:06:21,550
Wherever you are using

87
00:06:21,550 --> 00:06:25,030
That class name, you must pass template as a parameter.

88
00:06:25,360 --> 00:06:29,740
Otherwise you pass data type, then, inside the function,

89
00:06:29,740 --> 00:06:30,760
There are no more changes.

90
00:06:30,790 --> 00:06:32,070
No changes at all.

91
00:06:32,200 --> 00:06:34,060
Now, coming to this.

92
00:06:34,060 --> 00:06:37,660
See this template has finished here, then again I should define a template here.

93
00:06:37,960 --> 00:06:49,230
So again, write template class T, then, this class name, arithmetic, is a template.

94
00:06:49,290 --> 00:06:56,610
So it must be written like this, then there are no parameters, leave it. What is the return type? I've changed

95
00:06:56,660 --> 00:06:57,260
it to T.

96
00:06:57,350 --> 00:07:02,760
So change it to T, then, these 2 are added.

97
00:07:02,900 --> 00:07:05,700
These 2 are added, result is stored in C.

98
00:07:05,810 --> 00:07:07,600
So what should be the type of C?

99
00:07:07,610 --> 00:07:09,040
Same as their type.

100
00:07:09,200 --> 00:07:11,300
So C should also be T.

101
00:07:11,540 --> 00:07:18,840
Yes, this is also T. See, in my code almost wherever I have declared data type, variable name,

102
00:07:18,890 --> 00:07:23,080
Everything is changing to T, but it may not be true always, remember this one.

103
00:07:23,110 --> 00:07:29,330
So you have to observe and change it, whichever should be changed, only that you should change. Now, this

104
00:07:29,390 --> 00:07:30,070
ends.

105
00:07:30,200 --> 00:07:33,660
Again for this I should write on a template class,

106
00:07:33,680 --> 00:07:34,790
template class T

107
00:07:37,790 --> 00:07:45,890
then, here for the class I should write T here and scope resolution, and this also I should change. So that all.

108
00:07:45,890 --> 00:07:50,500
This class has a change to a template class.

109
00:07:50,510 --> 00:07:52,190
No I will write down on the main function,

110
00:07:52,190 --> 00:07:53,720
I will use this template class.

111
00:07:53,730 --> 00:07:56,690
So just let us write a main function,

112
00:07:56,690 --> 00:08:01,170
So here, int main function. Now, for the main function,

113
00:08:01,200 --> 00:08:04,910
I'm creating an object of arithmetic

114
00:08:07,550 --> 00:08:13,280
I will create an object name as 'a'. Now, arithmetic as a template.

115
00:08:13,610 --> 00:08:17,070
So which type of data members you want? integer, float, double?

116
00:08:17,120 --> 00:08:17,900
Which one?

117
00:08:17,900 --> 00:08:22,570
So here along with the class name I should write on the date type, int.

118
00:08:23,150 --> 00:08:29,360
So now this object, I will call it as ar and I will pass the values 10, 5.

119
00:08:30,020 --> 00:08:35,020
Now this arithmetic class int, everywhere, wherever I have written T,

120
00:08:35,030 --> 00:08:36,789
That will be replaced by int,

121
00:08:36,880 --> 00:08:43,460
and this class becomes a pure integer class. So this works for integer type data, these variables become

122
00:08:43,460 --> 00:08:45,450
integer, that return types become integer,

123
00:08:45,460 --> 00:08:46,730
These parameters, integer.

124
00:08:46,730 --> 00:08:48,590
Everything becomes integer.

125
00:08:48,770 --> 00:08:51,910
Now these, 10 and 5 are integer values.

126
00:08:51,980 --> 00:08:58,610
If I want the result, I can say cout and I can call that function add, it will give me the integer

127
00:08:58,700 --> 00:09:00,740
addition of these 2 numbers.

128
00:09:00,740 --> 00:09:08,480
Now if I want the float class, then again make an object of this and give a parameter as

129
00:09:08,480 --> 00:09:09,490
float.

130
00:09:09,680 --> 00:09:17,360
And here I will give it as ar1, and pass the parameters, like 1.5 and 1.2

131
00:09:17,450 --> 00:09:18,220
.

132
00:09:18,230 --> 00:09:19,920
These are the float values.

133
00:09:19,970 --> 00:09:24,410
Now, cout<< ar1.add();

134
00:09:24,710 --> 00:09:31,020
This will give me addition of these 2 float numbers, right, because, the parameter is float type,

135
00:09:32,000 --> 00:09:34,370
and here the parameter is integer type.

136
00:09:34,670 --> 00:09:38,920
So here the object will be integer, and here it will be float for this one.

137
00:09:38,930 --> 00:09:41,480
Everything will become float.

138
00:09:41,620 --> 00:09:43,940
Wherever I have written T, that will become float.

139
00:09:44,210 --> 00:09:48,920
So it means the same class is working as float also, integer also.

140
00:09:48,980 --> 00:09:51,330
So I don't have to write on two different classes.

141
00:09:51,370 --> 00:09:53,660
It works for any data type.

142
00:09:54,230 --> 00:10:01,510
So whichever data type you want, you have to mention it as a template. So that's all about the Template Class.

143
00:10:01,570 --> 00:10:03,170
So you can practice this one.

144
00:10:03,250 --> 00:10:09,770
So I have covered all the required concepts of C and C++ programming that I'm using in my course.

145
00:10:09,890 --> 00:10:13,330
So once you have finished this you can start learning the course.

146
00:10:13,330 --> 00:10:20,590
You can start the topics of data structures. And, the next section is about installation or set up required

147
00:10:20,620 --> 00:10:24,590
for writing C C++ programs for data structures.

148
00:10:24,670 --> 00:10:29,710
So, if you already have the set up and you are already familiar, you can skip that section and you can start

149
00:10:29,710 --> 00:10:36,280
with the actual topics of data structures. So that's all, I have covered all the topics that are used in

150
00:10:36,280 --> 00:10:37,580
my course.

151
00:10:37,600 --> 00:10:41,560
So if you are familiar with this then you can smoothly understand the course.

152
00:10:41,590 --> 00:10:44,160
So hope you will get a lot of benefit from the course.

153
00:10:44,200 --> 00:10:49,100
Spend enough time on each topic and practice the programs. That's all in this video.

