1
00:00:00,720 --> 00:00:09,210
Our next topic is try diagonal matrix, so here I have an example of a five cross five matrix, and

2
00:00:09,240 --> 00:00:10,680
it's a tri diplomatic.

3
00:00:10,710 --> 00:00:12,390
So what does it mean by a triangle?

4
00:00:13,320 --> 00:00:20,240
So if you look at the elements, the elements are there and mean diagonal then in the lower diagonal

5
00:00:20,580 --> 00:00:22,200
than an upward angle.

6
00:00:22,560 --> 00:00:25,350
And rest of the elements are all zeroes.

7
00:00:25,680 --> 00:00:31,910
So let us define the properties of this matrix based on indices, rule number and column number.

8
00:00:31,920 --> 00:00:33,130
That is engy.

9
00:00:33,870 --> 00:00:39,660
So if you observe the indices of elements, the elements in the data they have are equal to G.

10
00:00:39,780 --> 00:00:40,670
Equal to G.

11
00:00:41,800 --> 00:00:49,640
And the elements in <PERSON>, two, one, three, two, so if I subtract I and I minus G.

12
00:00:49,660 --> 00:00:50,560
This gives me one.

13
00:00:50,560 --> 00:00:52,210
This gives me one and one.

14
00:00:52,390 --> 00:00:54,870
One, yes, I minus G.

15
00:00:54,910 --> 00:00:56,230
This gives C minus one.

16
00:00:56,500 --> 00:00:57,140
Minus one.

17
00:00:57,490 --> 00:01:05,260
So the elements in the main diagonal dissatisfied this property that is minus G, C close to zero and

18
00:01:05,260 --> 00:01:06,100
I equals today.

19
00:01:06,130 --> 00:01:14,200
So if you subtract to zero then lower diagonal dissatisfy the property that is minus G is equal to one

20
00:01:15,100 --> 00:01:22,030
and upper diagonal that satisfy the condition that is minus G is equal to minus one.

21
00:01:22,180 --> 00:01:24,220
If you subtract this you get minus one.

22
00:01:25,060 --> 00:01:30,520
So the elements whose rule number and column number that is in essence are satisfying these conditions,

23
00:01:30,850 --> 00:01:34,300
they must be non zero and all other elements must be.

24
00:01:35,860 --> 00:01:44,020
So I can combine these three conditions together and write them as I minus G absolute value that is

25
00:01:44,020 --> 00:01:46,000
mod as.

26
00:01:48,020 --> 00:01:53,990
Less than or equal to one, so if you subtract and if you are getting a value less than or equal to

27
00:01:53,990 --> 00:01:58,430
one that is an absolute value, then those elements must be non-zero.

28
00:01:59,450 --> 00:02:01,340
So finally, I can say that.

29
00:02:02,760 --> 00:02:06,090
I'm of Ikoma, and she is non-zero.

30
00:02:08,300 --> 00:02:08,900
If.

31
00:02:10,570 --> 00:02:22,030
I minus G is less than equal to one, and I'm off, I g must be zero if the absolute value of my energy

32
00:02:22,030 --> 00:02:23,080
is greater than one.

33
00:02:24,130 --> 00:02:28,690
So these elements, if you take the indices, if I try to write the index for this one, this is for

34
00:02:28,700 --> 00:02:31,990
come one, subtract for one, you get three.

35
00:02:32,230 --> 00:02:35,660
So subtraction of energy is greater than one.

36
00:02:35,680 --> 00:02:37,140
So these elements are all zero.

37
00:02:37,480 --> 00:02:42,790
So this is the definition of a diagonal metrics then total.

38
00:02:42,790 --> 00:02:44,980
How many non-zero elements are there?

39
00:02:46,710 --> 00:02:54,150
Seat five, five elements here, and one, two, three, four, one, less than five, one, two, three,

40
00:02:54,150 --> 00:02:55,890
four, one of them five.

41
00:02:56,220 --> 00:02:58,270
So five plus four plus four.

42
00:02:58,290 --> 00:03:06,570
So here I have five plus four plus four elements in terms of emphasis and plus and minus one.

43
00:03:06,570 --> 00:03:07,950
Plus and minus one.

44
00:03:08,070 --> 00:03:14,360
So this is three and minus two number of non-zero elements are there.

45
00:03:14,790 --> 00:03:18,470
So we have been trying to avoid scoring zero element.

46
00:03:18,480 --> 00:03:20,220
So here also we go for something.

47
00:03:20,580 --> 00:03:22,260
We will avoid studying zero.

48
00:03:22,260 --> 00:03:24,090
So will store only non-zero element.

49
00:03:24,600 --> 00:03:27,630
Then we will not be taking a two dimensional array for the semantics.

50
00:03:27,630 --> 00:03:29,240
We will take a single dimensionally.

51
00:03:29,640 --> 00:03:30,180
What should we?

52
00:03:30,180 --> 00:03:35,940
The size of that single dimension only depends on a number of non-zero elements that is trying to end

53
00:03:35,940 --> 00:03:36,590
minus two.

54
00:03:36,600 --> 00:03:38,610
So what is and in our example, five.

55
00:03:38,940 --> 00:03:40,350
So 15 minus two.

56
00:03:40,350 --> 00:03:41,450
That is 13.

57
00:03:41,820 --> 00:03:44,390
So we need 13 spaces for storing these elements.

58
00:03:44,760 --> 00:03:49,890
So let us see how to represent this tri diagonal matrix and a single dimensionality.

59
00:03:49,920 --> 00:03:52,080
I will take an array of size 13.

60
00:03:52,770 --> 00:03:57,720
So here I have an array of size 13 for storing all non-zero elements.

61
00:03:58,620 --> 00:04:02,010
Then how to map these elements in a single dimensional.

62
00:04:03,640 --> 00:04:10,200
We have stalled over triangle and upper triangle of mitosis by following rule by road, column by column.

63
00:04:11,170 --> 00:04:16,839
If we try here, Robledo, then first of all, is having to eliminate the next three three three then

64
00:04:16,839 --> 00:04:17,140
too.

65
00:04:17,829 --> 00:04:20,579
So those are not having uniform number of elements.

66
00:04:20,589 --> 00:04:24,700
Likewise, columns are also not having uniform number of elements.

67
00:04:26,060 --> 00:04:31,510
Then one option we have is we can represent them diagonal by diagonal.

68
00:04:31,970 --> 00:04:35,460
So let us store the elements diagonal, Nickman, which are diagonal.

69
00:04:35,480 --> 00:04:36,370
We should take first.

70
00:04:36,380 --> 00:04:43,370
We can pick up either the upper diagonal and mean the lower or lower the mean and then upper.

71
00:04:43,400 --> 00:04:44,590
So we have good options.

72
00:04:45,290 --> 00:04:48,840
So let us a store first lower diagonal elements.

73
00:04:49,400 --> 00:04:56,540
I will store lower diagonal elements to one, three, two, four, three, five, four.

74
00:04:56,570 --> 00:04:58,460
So first I will store this lower diagonal.

75
00:04:59,410 --> 00:05:04,970
Now, likewise, I will store main diagonal elements and upper diagonal elements followed by that.

76
00:05:05,950 --> 00:05:08,050
So I have stored all three diagonals.

77
00:05:08,620 --> 00:05:10,090
Now how to map these?

78
00:05:10,120 --> 00:05:15,850
There must be some formula so we cannot come up with a single formula for all the diagnosis.

79
00:05:15,850 --> 00:05:17,860
We have to handle them separately.

80
00:05:18,010 --> 00:05:20,260
So simply, we will go diagonal by diagonal.

81
00:05:20,260 --> 00:05:30,340
So first, if I want the index of any element that is aof Ikoma G, then.

82
00:05:31,490 --> 00:05:41,690
First case one, if I minus G is equal to one, so it means these are lower than elements, if I minus

83
00:05:41,690 --> 00:05:48,490
GS equals to one Lord Agnel elements, then if I is given as a tool, then it is our zero.

84
00:05:48,500 --> 00:05:50,090
If it is three, then to one.

85
00:05:50,360 --> 00:05:53,270
So it's a difference of a minus two.

86
00:05:53,430 --> 00:05:55,270
The elements are at minus two.

87
00:05:55,520 --> 00:05:58,520
So the indexes a minus two.

88
00:06:04,230 --> 00:06:11,190
So if I is to decide zero, if I is three, then decide one if I is a for the two, so I am minus two

89
00:06:11,370 --> 00:06:14,850
or else we can also take minus one also.

90
00:06:14,910 --> 00:06:26,430
What better people introducing I that case two if I am minus G, if I minus G is equal to zero means

91
00:06:26,430 --> 00:06:29,040
we are looking for mean diagonal elements.

92
00:06:29,370 --> 00:06:33,060
If it is a main diagonal element then first of all I should crossover like.

93
00:06:33,670 --> 00:06:36,340
So how many elements are there in the lower diagonal.

94
00:06:36,750 --> 00:06:39,870
We have and the minus one elements in lower diagonal.

95
00:06:40,380 --> 00:06:45,300
Once we cross these N minus one elements, we are here in our example and there's a fight.

96
00:06:45,300 --> 00:06:46,550
So five minus one four.

97
00:06:46,560 --> 00:06:47,340
So we are here.

98
00:06:48,430 --> 00:06:54,370
So if suppose you want one, then don't move at all, if you want second element I used to and then

99
00:06:54,370 --> 00:06:57,830
move on is the three, then move to spaces, to spaces.

100
00:06:58,150 --> 00:06:59,450
So I minus one.

101
00:07:00,480 --> 00:07:03,790
And minus one, plus I minus one.

102
00:07:04,440 --> 00:07:10,320
This will be the formula for elements of main diagonal, then case three.

103
00:07:12,790 --> 00:07:19,870
If I win, those GS equals two minus one, that is the elements of upper diagonal, then I should skip

104
00:07:19,870 --> 00:07:23,800
these elements and these elements and the minus one and an element.

105
00:07:23,800 --> 00:07:25,360
So index will be.

106
00:07:28,390 --> 00:07:34,440
Two and minus one, so I have to skip all these elements, then I'll be here and that location can be

107
00:07:34,790 --> 00:07:35,220
zero.

108
00:07:36,040 --> 00:07:39,040
So if I want one or to, then don't move.

109
00:07:39,070 --> 00:07:39,940
That is the element.

110
00:07:40,240 --> 00:07:43,560
If I want to commentary, then move by one place.

111
00:07:44,710 --> 00:07:47,460
So if it is three, four, move by two places.

112
00:07:47,800 --> 00:07:49,580
So that is a minus one.

113
00:07:49,600 --> 00:07:51,760
So if you want to then move by two places.

114
00:07:52,210 --> 00:07:54,520
So again, this is also a minus one.

115
00:07:58,040 --> 00:07:59,180
Plus, I might have.

116
00:08:02,290 --> 00:08:09,890
So we have three different formulas for mapping, tried diagonal matrix in a single dimension.

117
00:08:10,750 --> 00:08:12,250
So that's all about protecting.

118
00:08:12,250 --> 00:08:15,720
Mattocks will write a program for TitleMax.

119
00:08:16,240 --> 00:08:17,950
No, I will also show.

120
00:08:19,510 --> 00:08:27,490
What is a square band matrix, a square band, Mattocks Square band Matrix is similar to try and try

121
00:08:27,490 --> 00:08:28,060
diagonal.

122
00:08:28,420 --> 00:08:31,720
Only three diagonals will be their main diagonal lower number.

123
00:08:32,590 --> 00:08:37,610
But in square, mathematics doesn't mean diagonal and the below mean diagonal.

124
00:08:37,929 --> 00:08:45,160
There are more than one diagonals like one, two, three, and there can be more and I mean diagonal.

125
00:08:45,160 --> 00:08:50,980
Also same number of diagonals in search forming like a band of diagonal.

126
00:08:51,010 --> 00:08:56,800
So when there are more than one diagonals below mean diagonal and we have equal number of diagonals

127
00:08:56,800 --> 00:09:02,920
on both the sides, then it is called Square Band Mattocks and the Square Band Matrix.

128
00:09:02,980 --> 00:09:08,050
If it's a very big matrix, then again, you may have lot of zero elements depending on the size of

129
00:09:08,050 --> 00:09:08,520
a matrix.

130
00:09:08,530 --> 00:09:13,750
I have taken an example of eight by eight here suppose is hundred one hundred or two by two hundred.

131
00:09:13,990 --> 00:09:19,960
But I have only three bands below Main Diagonal that is the main diagonal and three bands below this

132
00:09:19,960 --> 00:09:20,920
one or three months ago.

133
00:09:20,920 --> 00:09:24,360
That one, then a lot of elements will be zero.

134
00:09:24,370 --> 00:09:26,890
So then we prefer storing only non-zero elements.

135
00:09:27,290 --> 00:09:32,200
Then same way you can take a single dimensionality and restore diagonal diagonal.

136
00:09:32,410 --> 00:09:34,860
So just I have given the introduction about this one.

137
00:09:35,170 --> 00:09:37,840
So this is square biomimetics.

138
00:09:38,320 --> 00:09:40,960
So next, Maddux's Toeplitz Mattocks.

