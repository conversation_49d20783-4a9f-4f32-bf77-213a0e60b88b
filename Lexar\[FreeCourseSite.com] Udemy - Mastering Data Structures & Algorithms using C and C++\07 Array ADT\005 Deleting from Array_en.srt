1
00:00:00,820 --> 00:00:04,630
Delayed operation is removing an element from a given index.

2
00:00:04,780 --> 00:00:09,010
Obviously, when we want to dilute something, then we should mention which element we want to delete

3
00:00:09,310 --> 00:00:11,320
so we can mention index.

4
00:00:11,680 --> 00:00:16,260
So as an example, I have taken the lead element three, index three.

5
00:00:16,390 --> 00:00:19,960
So this element I want to delete so for deleting this element.

6
00:00:19,970 --> 00:00:22,110
So first of all, you take out the element from there.

7
00:00:22,390 --> 00:00:31,120
So picking out an element so I can take out X, assign it of this index, whatever the index is given.

8
00:00:34,180 --> 00:00:40,480
Now we have an element that we want to delete that is present here in X, so I'm going to see 12 percent

9
00:00:40,480 --> 00:00:40,950
index.

10
00:00:41,560 --> 00:00:43,670
So we have taken out the copy of an element.

11
00:00:44,320 --> 00:00:50,120
Now, when we say this element is not there in the early, then this space will be blank.

12
00:00:50,920 --> 00:00:51,850
So remember this.

13
00:00:51,850 --> 00:00:55,610
We don't leave blank spaces in between the elements in another.

14
00:00:56,170 --> 00:01:01,030
So if you are saying that that element is not there, then we don't know what all the other elements

15
00:01:01,030 --> 00:01:02,120
are there or not there.

16
00:01:02,560 --> 00:01:07,740
So then it becomes difficult for us to find out whether there are elements there or not that.

17
00:01:09,000 --> 00:01:15,960
When we delete the elements from another, then that space, we don't leave it vacant, if we are leaving

18
00:01:15,960 --> 00:01:24,140
it vacant and assuming that the element is not there, then for every location we have to check the

19
00:01:24,150 --> 00:01:26,080
elements are there or not there.

20
00:01:26,550 --> 00:01:28,190
So it increases over work.

21
00:01:28,500 --> 00:01:33,680
So instead of keeping that space blank, we will shift all the elements.

22
00:01:34,110 --> 00:01:40,500
So whenever we delete an element from another, we have to shift the element to occupy that blank space.

23
00:01:40,860 --> 00:01:43,600
So what I have to do is shift the elements from here.

24
00:01:44,010 --> 00:01:52,770
So being here, let us again take aim, which will shift the element, then move I and shift the element,

25
00:01:53,130 --> 00:01:55,680
then move I and shift the element.

26
00:01:55,680 --> 00:02:00,420
So it's moving in forward direction and move I then shift the element.

27
00:02:00,600 --> 00:02:06,780
I should not move further because this is the last index where I have an element that was left to minus

28
00:02:06,780 --> 00:02:07,020
one.

29
00:02:07,030 --> 00:02:09,600
So I should stop before linta minus one.

30
00:02:09,900 --> 00:02:17,370
So I should shift all the elements one by one, starting from a given index and reach Atlanta minus

31
00:02:17,370 --> 00:02:19,300
one location or before minus one.

32
00:02:19,650 --> 00:02:23,260
So so the shifting of these elements can be done using follow.

33
00:02:23,580 --> 00:02:30,650
So let us start II from the given index and being I here, it can copy the element from the next index.

34
00:02:30,960 --> 00:02:37,600
So let us try to follow up for I start from given index, which is the index index is three.

35
00:02:37,860 --> 00:02:41,160
So from index and.

36
00:02:44,020 --> 00:02:49,900
Well, I should stop it should stop at six, being at six, it can copy the element from index seven,

37
00:02:49,900 --> 00:02:54,970
so I should be less than lente feet actually.

38
00:02:54,970 --> 00:02:58,560
Lantis eight less than means seven.

39
00:02:58,810 --> 00:03:01,390
No, it should stop at six, Salento minus one.

40
00:03:01,750 --> 00:03:03,750
And I is moving forward every time.

41
00:03:03,760 --> 00:03:07,390
So I know what I have to do being here.

42
00:03:07,390 --> 00:03:09,230
It should copy the element from next index.

43
00:03:09,230 --> 00:03:20,800
So at aof I it should copy the element from aof I plus one and the same loop over this loop I here at

44
00:03:20,800 --> 00:03:27,550
index three it will copy fifteen then eight plus plus eight plus plus then being here it will copy six

45
00:03:27,550 --> 00:03:34,870
that is plus one is copied here then I plus plus now I use here so it will go daleman from this index.

46
00:03:34,870 --> 00:03:35,860
So it is at six.

47
00:03:37,930 --> 00:03:43,630
So it is adding that five surgical copy nine then eight plus plus it should copy ten.

48
00:03:44,050 --> 00:03:45,700
Then after that I should stop.

49
00:03:45,700 --> 00:03:50,800
Yes, I use less than minus one and minus one is eight minus one.

50
00:03:51,070 --> 00:03:51,880
That is seven.

51
00:03:52,150 --> 00:03:53,710
So I have six now.

52
00:03:53,710 --> 00:03:55,060
Six is less than seven.

53
00:03:55,780 --> 00:03:58,170
So if it becomes seven it will stop.

54
00:03:58,690 --> 00:04:02,630
So this has moved all the elements or shifted all the elements.

55
00:04:02,630 --> 00:04:08,230
So this element is removed now as the number of elements has reduced.

56
00:04:08,230 --> 00:04:10,270
So this should become seven.

57
00:04:10,480 --> 00:04:16,329
So here I should decrease in the value of land land minus minus.

58
00:04:18,130 --> 00:04:22,390
So this is the pseudocode for deleting an element from a given index.

59
00:04:22,990 --> 00:04:23,620
Now one more thing.

60
00:04:23,620 --> 00:04:27,550
We should take care that the indexes should not be out of the range.

61
00:04:27,730 --> 00:04:29,580
Like I have only eight elements.

62
00:04:29,610 --> 00:04:33,900
I'm giving index has a 10 or to it beyond the length of a net.

63
00:04:34,060 --> 00:04:36,720
It should not go beyond the limit so that you have to pick.

64
00:04:37,180 --> 00:04:39,370
So that will be doing it when we write the program.

65
00:04:40,180 --> 00:04:43,310
Now let us analyze how much work is done here.

66
00:04:43,660 --> 00:04:49,240
What is the time complexity see gawping the elements from a given index.

67
00:04:49,240 --> 00:04:51,860
It takes one unit of time and degremont.

68
00:04:51,880 --> 00:04:53,500
Glenda takes one unit of time.

69
00:04:53,950 --> 00:04:59,350
And this loop, the statement that is shifting the elements, shifting the elements, how many elements

70
00:04:59,350 --> 00:05:00,060
are shifted?

71
00:05:00,640 --> 00:05:02,940
So depends what index I have given.

72
00:05:03,460 --> 00:05:07,140
So minimum zero elements are shifted.

73
00:05:07,630 --> 00:05:11,110
If I was deleting the last element and then nothing has to be shifted.

74
00:05:11,410 --> 00:05:15,660
If I'm deleting the first element that is index zero, then all have to be shifted.

75
00:05:15,970 --> 00:05:21,550
So minimum shifting is zero, maximum shifting as in so many of them shifting is zero order maximum

76
00:05:21,550 --> 00:05:21,950
16.

77
00:05:22,120 --> 00:05:27,610
And so again, the minimum time is a constant and the maximum time.

78
00:05:27,850 --> 00:05:32,470
And so if I write minimum time, minimum time is.

79
00:05:33,880 --> 00:05:42,220
One plus one, that is true, and the maximum time is one plus one, two as well as and so and plus

80
00:05:42,220 --> 00:05:42,520
two.

81
00:05:43,920 --> 00:05:50,960
So the best time as to that is constant both times, and that is maximum.

82
00:05:51,360 --> 00:05:54,240
So best case is what if I'm deleting the last element?

83
00:05:54,370 --> 00:05:56,140
No shifting required now.

84
00:05:56,160 --> 00:06:02,630
Right now, if I delete 10 from here, if I want to delete the 10 from here, then directly ten is deleted.

85
00:06:02,910 --> 00:06:04,410
No elements beyond ten.

86
00:06:04,710 --> 00:06:06,270
So no shifting required.

87
00:06:08,870 --> 00:06:16,760
So domesticates time is order of one worst case time as outdraw and.

88
00:06:18,240 --> 00:06:22,350
So that's all about religion, so we have finished deletion.

89
00:06:25,290 --> 00:06:28,560
Next will be search, so we learn about search.

