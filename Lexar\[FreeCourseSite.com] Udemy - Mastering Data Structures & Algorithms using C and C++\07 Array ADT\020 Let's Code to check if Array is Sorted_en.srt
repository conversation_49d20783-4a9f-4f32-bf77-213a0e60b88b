1
00:00:00,180 --> 00:00:06,930
And the city will have the demonstration for inserting an element in assorted uhry and checking whether

2
00:00:06,930 --> 00:00:12,750
or not that is all already is or not, and arranging all positives and negatives on either side of al-Nouri

3
00:00:13,440 --> 00:00:13,650
agree.

4
00:00:13,780 --> 00:00:15,780
We have discussed this topic on Lightwood.

5
00:00:16,200 --> 00:00:18,650
Neither return functions for them and I will show you.

6
00:00:19,410 --> 00:00:23,840
So I'm using the same program which we have already used in the previous topics.

7
00:00:24,180 --> 00:00:27,810
So all those functions are present in the same project now, same program.

8
00:00:28,860 --> 00:00:34,030
And here I have another which is initialized with some number and these numbers are already sorted.

9
00:00:35,100 --> 00:00:38,460
Now let us write a function for inserting an element in the solid position.

10
00:00:41,850 --> 00:00:49,410
I will call the function Ima's insert sort, and it should take as a parameter.

11
00:00:50,610 --> 00:00:56,760
And it should be Bio-Reference and next to the value that we want to insert, so for inserting an element

12
00:00:57,060 --> 00:01:02,070
in a solid position, I should start shifting the larger elements from the right hand side of an area

13
00:01:02,070 --> 00:01:04,290
and make a free space for X and inserted.

14
00:01:06,700 --> 00:01:12,130
So I relied on the code, what we have already seen on board, so before insertion, first of all,

15
00:01:12,130 --> 00:01:15,290
I should check whether there is any free space in an area or not.

16
00:01:15,790 --> 00:01:18,940
So if I is lente.

17
00:01:19,880 --> 00:01:22,700
Is equal to race size.

18
00:01:24,740 --> 00:01:26,720
Then there is no free space.

19
00:01:27,960 --> 00:01:33,900
So I should stop the function, so I will simply say Flint is equal to size, then there is no free

20
00:01:33,900 --> 00:01:36,420
space otherwise I can insert.

21
00:01:36,600 --> 00:01:39,240
So for insertion I have to shift the elements of that.

22
00:01:39,240 --> 00:01:46,700
I need a variable eye and that should be initialized with arrays lente minus one.

23
00:01:47,320 --> 00:01:53,610
Then, as we have already seen that I have to go on shifting elements, so I should do it as long as

24
00:01:53,610 --> 00:01:59,280
line of i.e. is greater than X.

25
00:02:00,360 --> 00:02:09,330
If so, then I should shift an element at AOF I plus one, I should trade on the value from aof I.

26
00:02:14,800 --> 00:02:16,240
And I should departement I'm.

27
00:02:17,390 --> 00:02:23,780
And one more thing I should take care of that I should not cross zeroth index, so one more condition,

28
00:02:24,170 --> 00:02:31,040
I should be greater than or equal to zero as well as this condition should be satisfied.

29
00:02:31,850 --> 00:02:35,200
If I'm inserting a smallest element, then it should come at index zero.

30
00:02:35,210 --> 00:02:38,640
Beyond that and the below index zero, there is no valid index.

31
00:02:38,640 --> 00:02:42,000
So I should stop when it has reached zero.

32
00:02:42,500 --> 00:02:44,720
So if it is becoming minus one, it should stop.

33
00:02:45,720 --> 00:02:51,030
Then after coming out of the loop at AOF I plus one.

34
00:02:52,110 --> 00:02:58,740
I should store the element as we have inserted one more element, so the length of Uhry should also

35
00:02:58,740 --> 00:03:00,210
be incremented.

36
00:03:00,220 --> 00:03:07,020
So Lentulus plus, so that's all the function is ready, which can insert an element in a solid position.

37
00:03:07,030 --> 00:03:10,800
So let us try it in main function in mean functionality.

38
00:03:10,800 --> 00:03:13,000
I haven't added the largest element of stuffing.

39
00:03:13,480 --> 00:03:22,200
Now let us insert an element by calling this function insert sort and it takes address of the object

40
00:03:22,320 --> 00:03:24,580
and the value that I want to insert is 20.

41
00:03:25,290 --> 00:03:26,790
So let us insert 20.

42
00:03:26,790 --> 00:03:31,920
20 should mean so that at the end of the day that is after 15 because this will be a great number.

43
00:03:32,820 --> 00:03:36,000
So display should display all the values along with 20.

44
00:03:36,030 --> 00:03:37,260
So this is a display function.

45
00:03:37,260 --> 00:03:38,400
Will it be all elements?

46
00:03:41,260 --> 00:03:44,920
Yes, now it is displaying six elements, including Pándy.

47
00:03:47,930 --> 00:03:49,070
Let us change the value.

48
00:03:49,250 --> 00:03:54,770
I will try to insert something between the 10 and 15, so I'll give the element as to and so it should

49
00:03:54,770 --> 00:03:58,070
be so that after 10, let us see that it works.

50
00:04:00,690 --> 00:04:06,330
Yes, two, three, five, 10, 12 and 15 that is inserted in the sorted position.

51
00:04:07,310 --> 00:04:11,240
If I give the value as a four, so it should be inserted in between three and five.

52
00:04:14,370 --> 00:04:16,589
Yes, two, three, four, five, 10, 15.

53
00:04:18,220 --> 00:04:23,030
If I give a value as one, then it should be considered before because it's the smallest element.

54
00:04:23,950 --> 00:04:28,320
Yes, one, two, three, five and then 15, it is inserted.

55
00:04:29,260 --> 00:04:32,440
So that's all the insert function in sorting in assorted other.

56
00:04:33,600 --> 00:04:38,820
Next, I will write a function to check if an array is sorted or not, that is, is it sorted?

57
00:04:39,880 --> 00:04:41,250
So written by percentages.

58
00:04:41,950 --> 00:04:47,850
It will return, whether arrested or not, so it will return true or false two, it will return one,

59
00:04:47,860 --> 00:04:49,300
otherwise it will unsettle.

60
00:04:50,810 --> 00:04:53,990
Is assaulted, so it should take an area so.

61
00:04:55,030 --> 00:05:04,110
Struct Uhry Iare, if you take the value by value, we don't need to buy a dress because it has to check,

62
00:05:04,140 --> 00:05:05,870
it doesn't have to modify an update.

63
00:05:07,460 --> 00:05:13,510
Then to avoid an error here, I right return zero, otherwise the compiler will show error here, as

64
00:05:13,510 --> 00:05:15,620
we have already seen the procedure on whiteboard.

65
00:05:15,850 --> 00:05:18,970
So we need to use follow for scanning for this.

66
00:05:19,360 --> 00:05:22,060
So for that, I will take I then follow up.

67
00:05:22,060 --> 00:05:23,380
I start from zero.

68
00:05:23,380 --> 00:05:31,240
I should be less than Odie's land minus one because we should stop one element before.

69
00:05:32,330 --> 00:05:37,520
As we are comparing to Elliman, so every time check what, if any, I.

70
00:05:39,070 --> 00:05:41,380
If AOF is.

71
00:05:42,970 --> 00:05:52,390
Greater than Eaarth I plus one, then it means they are not sodded any two elements are such that first

72
00:05:52,390 --> 00:05:54,960
one is greater than the second one, then they are not certain.

73
00:05:54,970 --> 00:05:57,190
And so we will return false Salsman Zettl.

74
00:05:58,770 --> 00:06:05,370
Otherwise, when we come out of the loop, we will return one means if return zero was not done, we

75
00:06:05,370 --> 00:06:09,010
have checked the complete uhry and there was no such condition.

76
00:06:09,030 --> 00:06:11,610
That is the first element was greater than the second element.

77
00:06:12,120 --> 00:06:13,510
So Uta's distorted.

78
00:06:13,650 --> 00:06:17,370
So this discussion already we have done now I will use that function.

79
00:06:17,370 --> 00:06:18,870
I will call that function here.

80
00:06:23,020 --> 00:06:28,170
So I will directly bring the value print that person, Tildy Slash and.

81
00:06:29,740 --> 00:06:31,540
Is it thought that I will pass an.

82
00:06:34,530 --> 00:06:39,630
So I'm passing this array to this function to check whether distorted or not, we can see that this

83
00:06:39,850 --> 00:06:42,510
is already sorted, so it should return one.

84
00:06:42,810 --> 00:06:44,730
So it should bring the value one.

85
00:06:48,810 --> 00:06:55,650
Yes, it is printing one, I will modify this area and make it unsorted, so I will write twenty five

86
00:06:55,650 --> 00:06:55,910
here.

87
00:06:56,700 --> 00:06:59,680
So two, three, twenty five, then 10, so it is not sorted.

88
00:07:00,060 --> 00:07:02,700
See, one of the elements that is greater than the next element.

89
00:07:05,320 --> 00:07:08,070
Let us run and see what the output, the output.

90
00:07:08,110 --> 00:07:12,590
Zero means, it says that is not certain is correct.

91
00:07:13,030 --> 00:07:18,370
So it is returning for false or whatever you need that you can use it to check that it is sorted or

92
00:07:18,370 --> 00:07:20,280
not sorted all the function.

93
00:07:20,470 --> 00:07:25,210
It will help you in taking a decision by sending true or false, that is zero or one.

94
00:07:27,730 --> 00:07:32,920
Well, the next function remaining is rearranging positive and negatives, so for that, I will write

95
00:07:32,920 --> 00:07:33,550
a function.

96
00:07:37,250 --> 00:07:45,800
Rearrange and it should take a struck by study and it's going to modify an array, so it should be called

97
00:07:45,800 --> 00:07:46,550
the address.

98
00:07:52,960 --> 00:07:55,030
I'm calling the function Ima's rearrange.

99
00:07:57,500 --> 00:08:03,230
As we have already seen the procedure, so I will declare the variables and that we need I should start

100
00:08:03,230 --> 00:08:08,990
from Index zero and G should start from index arrays LENTE.

101
00:08:10,590 --> 00:08:18,150
Minus one, that is the index of last element then using why, look, we have to trade as long as I

102
00:08:18,150 --> 00:08:18,870
use less than.

103
00:08:20,670 --> 00:08:28,100
Then each time incrementally, so I will use do I look here each time, increment I as well as a diclemente

104
00:08:28,230 --> 00:08:29,310
so I will use Vion.

105
00:08:36,710 --> 00:08:37,940
If itis.

106
00:08:39,370 --> 00:08:43,900
Less than zero minutes, if it is negative, then increment, i.e..

107
00:08:45,190 --> 00:08:48,220
Unseemly, I should diclemente if.

108
00:08:49,330 --> 00:08:53,940
If I is greater than equal to Zettl.

109
00:08:56,810 --> 00:08:58,040
G minus minus.

110
00:08:58,790 --> 00:09:04,880
So this procedure already I have shown you, I'm not explaining it here, if I is less than G, then

111
00:09:04,880 --> 00:09:05,270
swab.

112
00:09:05,270 --> 00:09:08,240
So already we have a function for swab.

113
00:09:08,240 --> 00:09:13,520
So I will call that function swab by sending addresses off it off.

114
00:09:15,620 --> 00:09:23,390
If I and I read off E of G, these two elements are willing to change.

115
00:09:23,750 --> 00:09:24,560
So that's all.

116
00:09:24,590 --> 00:09:30,290
So if I have some positive or negative elements scattered in the 30, then it will arrange all negatives

117
00:09:30,290 --> 00:09:32,540
on the left hand side and positive on the right hand side.

118
00:09:32,550 --> 00:09:34,740
So I will try to write some negative numbers.

119
00:09:34,760 --> 00:09:39,100
Also, it is minus 15 and this is minus three.

120
00:09:39,440 --> 00:09:43,350
So then I will add one more number here, minus seven.

121
00:09:43,700 --> 00:09:47,240
So this minus three, minus 15 and minus seven should be on the left hand side.

122
00:09:47,690 --> 00:09:49,180
So let us call the function.

123
00:09:49,190 --> 00:09:50,280
I will remove this.

124
00:09:56,350 --> 00:10:04,900
Rearrange, send the address of Uhry, not all elements are sick, so I should modify this land also

125
00:10:04,900 --> 00:10:08,850
to six letters from the program and see what happens.

126
00:10:10,400 --> 00:10:15,640
Yes, all negative side on one side and positive are on the right hand side, so you can see that two

127
00:10:15,650 --> 00:10:18,890
is slapped with minus seven then.

128
00:10:18,920 --> 00:10:20,900
Twenty five is five minus fifteen.

129
00:10:20,900 --> 00:10:21,640
So twenty five.

130
00:10:21,680 --> 00:10:22,990
And in a place of 15.

131
00:10:24,320 --> 00:10:25,130
So that's it.

132
00:10:25,250 --> 00:10:26,060
This is working.

133
00:10:27,200 --> 00:10:32,810
We have seen three different functions that is inserting an element in a solid position and checking

134
00:10:32,810 --> 00:10:36,320
whether or at a certain or not rearranging the elements.

