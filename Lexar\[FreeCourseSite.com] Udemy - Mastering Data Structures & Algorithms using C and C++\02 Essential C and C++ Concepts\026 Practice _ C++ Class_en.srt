1
00:00:00,540 --> 00:00:09,180
Let us look at the demonstration for writing class in C++ and also I will explain about constructor's.

2
00:00:09,750 --> 00:00:14,460
So I will write the same C++ class for Rectangle right from the beginning.

3
00:00:15,300 --> 00:00:18,840
Class rectangle, right.

4
00:00:19,170 --> 00:00:25,650
Then it should have members length and breadth.

5
00:00:27,980 --> 00:00:33,020
And these members should be declared as private by default, they are private only.

6
00:00:33,290 --> 00:00:37,740
But again, I'm declaring then the rest of the things should be public.

7
00:00:38,420 --> 00:00:42,900
Not every C++ class should have a constructor.

8
00:00:43,160 --> 00:00:47,440
It's not mandatory, but it's a good style of programming to define constructors.

9
00:00:47,930 --> 00:00:51,440
So first constructor is a non parametrized constructor.

10
00:00:51,470 --> 00:00:58,280
This is also called as default constructor, which is making London than zero means.

11
00:00:58,280 --> 00:01:03,890
If you're creating an object of a rectangle without passing any arguments, then it will make that has

12
00:01:03,950 --> 00:01:04,390
zero.

13
00:01:05,000 --> 00:01:11,390
Then one more rectangle constructor I will write on, which will take length and breadth as a parameter

14
00:01:11,720 --> 00:01:19,370
and it will set given land as a length of rectangle and the given but as part of a rectangle.

15
00:01:20,180 --> 00:01:22,130
So we have constructors.

16
00:01:23,060 --> 00:01:29,960
Then the next type of methods which we have already seen are Radiometer and that will return land into

17
00:01:29,960 --> 00:01:30,350
bed.

18
00:01:37,190 --> 00:01:39,140
Then one more Metrowest perimeter.

19
00:01:39,500 --> 00:01:46,790
OK, then perimeter, this will return to multiplied by lente.

20
00:01:48,050 --> 00:01:49,370
Plus, Brett.

21
00:01:51,960 --> 00:01:59,310
Then apart from this, we also tried the methods that are used for reading and writing the properties

22
00:01:59,310 --> 00:02:01,260
of a rectangle.

23
00:02:01,650 --> 00:02:08,789
So I relied on the methods void set settlement, which will change their land.

24
00:02:09,520 --> 00:02:14,820
OK, Linda, sign then later on, if you want to change the length of a rectangle, you can call this

25
00:02:14,820 --> 00:02:16,440
method same way.

26
00:02:16,590 --> 00:02:19,170
Set bread later on.

27
00:02:19,170 --> 00:02:25,030
If you want to change the breadth of a rectangle, you can change the Brett.

28
00:02:26,780 --> 00:02:31,970
All right, then, if you want to know what is the lente, then get method must be there.

29
00:02:32,420 --> 00:02:35,060
This is the style of C++, right?

30
00:02:35,480 --> 00:02:37,240
This object rendition style.

31
00:02:37,640 --> 00:02:45,440
So I'm not teaching object or rendition here, but I expect that you already know object oriented programming,

32
00:02:45,710 --> 00:02:50,270
that is C++ programming, just revising the concepts.

33
00:02:50,690 --> 00:02:50,990
Right.

34
00:02:51,120 --> 00:02:54,460
See, that's all I have written almost all the methods.

35
00:02:54,860 --> 00:03:00,890
Let us look at all the methods once and then finish the class C classes having private members land.

36
00:03:00,890 --> 00:03:05,240
And right then this is the default constructor.

37
00:03:05,240 --> 00:03:12,080
Does the parametrized constructor and does these methods area and parameter are the actual methods that

38
00:03:12,080 --> 00:03:12,770
we want it.

39
00:03:13,040 --> 00:03:15,260
Then the rest of the things are all extra.

40
00:03:15,410 --> 00:03:21,020
Yes, in C++ we write extra code, the code that we are not using.

41
00:03:21,020 --> 00:03:26,510
Also we rewrite it so that when we write the class for something, it should be a complete class.

42
00:03:27,050 --> 00:03:27,250
Right?

43
00:03:27,350 --> 00:03:29,420
If you think that way I should write so much.

44
00:03:29,420 --> 00:03:33,230
Only lending to Bredar I have to do then you don't use C++.

45
00:03:33,740 --> 00:03:40,160
But see, we have redundant rectangle class that is useful at any place in any program, not just this

46
00:03:40,160 --> 00:03:40,610
program.

47
00:03:41,150 --> 00:03:48,760
Then the rest of the matter's settled and to celebrate our class mutator functions getlin get protocols,

48
00:03:48,800 --> 00:03:53,090
accessor functions which are used for reading the properties lanterne.

49
00:03:53,090 --> 00:03:56,030
But these are used for writing the properties content.

50
00:03:56,030 --> 00:04:00,650
But and lastly, a class should have distracter.

51
00:04:01,540 --> 00:04:07,150
And here we don't have to destroy anything if there isn't any dynamic memory allocation, then you can

52
00:04:07,360 --> 00:04:12,010
look at it, but I don't have anything to do, so I will just print out print.

53
00:04:12,610 --> 00:04:14,150
So this is a destructor.

54
00:04:14,170 --> 00:04:19,290
So just I'm printing message destructor right now inside the main function.

55
00:04:19,300 --> 00:04:25,350
I should do the same thing, rectangle object I should create and I will directly pass the values here.

56
00:04:25,360 --> 00:04:27,490
I'm not taking user input, ok.

57
00:04:27,540 --> 00:04:32,710
Already we have seen in previous programs so you can read on scene, see old and take the input from

58
00:04:32,710 --> 00:04:33,280
the user.

59
00:04:34,000 --> 00:04:44,810
Then directly I will print area area right then area as our dart area then sealed nexus.

60
00:04:45,160 --> 00:04:48,760
Very mean to our dart parameter.

61
00:04:50,430 --> 00:04:51,120
That's it.

62
00:04:51,310 --> 00:04:58,370
And one more thing, I will write and I'll here for each statement after printout, it should come in

63
00:04:58,370 --> 00:04:58,980
the next line.

64
00:04:59,010 --> 00:05:01,490
Let us on the program and see a.D.A.

65
00:05:01,490 --> 00:05:08,630
The 50 perimeter is 30 and districted is also displayed because once the main function and object is

66
00:05:08,630 --> 00:05:12,070
automatically destroyed, the structure function is clear.

67
00:05:12,380 --> 00:05:13,040
That's it.

68
00:05:13,490 --> 00:05:14,940
There's a C++ code.

69
00:05:15,170 --> 00:05:15,620
All right.

70
00:05:15,830 --> 00:05:22,940
So the next topic is for templates or I have given a whiteboard lecture for template and I suggest you

71
00:05:22,940 --> 00:05:24,610
write on the program by yourself.

72
00:05:24,620 --> 00:05:29,000
Okay, you can convert any data structure class into a template.

73
00:05:29,030 --> 00:05:30,240
So that is helpful for you.

74
00:05:30,440 --> 00:05:34,240
So that's all in this video practice, this one and also practice template.

75
00:05:34,430 --> 00:05:35,410
That's all in this video.

