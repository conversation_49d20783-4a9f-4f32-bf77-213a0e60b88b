1
00:00:00,570 --> 00:00:06,120
In this video, we will try to see language program for Binary Search Tree.

2
00:00:07,860 --> 00:00:13,550
And this program, I will show you how to insert an element in a mind of the first three, how to perform

3
00:00:13,560 --> 00:00:19,860
search, and also we will see how to perform in order traversal that already we have seen simply in

4
00:00:19,860 --> 00:00:21,210
order to perform.

5
00:00:22,860 --> 00:00:31,320
Let us give the Project Nightmares BSD, that is by street and the language is slanguage, the project

6
00:00:31,320 --> 00:00:34,910
is ready and clear of all the comments here.

7
00:00:34,920 --> 00:00:41,370
First of all, I should define our structure for binary search street structure for a node of the research.

8
00:00:41,970 --> 00:00:49,260
So I will say struct node and inside this I need three members that is left child.

9
00:00:50,580 --> 00:00:56,790
Data and digitized data and digging and struck Naude.

10
00:00:57,670 --> 00:01:05,620
Right, child, then here globally, I will declare a root that is pointer of type <PERSON><PERSON> and I will

11
00:01:05,620 --> 00:01:07,450
also initialize it as null.

12
00:01:07,930 --> 00:01:10,540
So initially nothing is there in binary search tree.

13
00:01:11,750 --> 00:01:16,330
Now let us write a function for insert void insert.

14
00:01:17,430 --> 00:01:20,100
I think the key value that has to be inserted.

15
00:01:24,270 --> 00:01:28,140
All the is globally accessible, so I will directly access this route here.

16
00:01:29,520 --> 00:01:33,750
Inside this inside function, I made it glass some temporary variables, so the first variable that

17
00:01:33,750 --> 00:01:44,400
I will take that is Naude e that will point upon root and then also I will take a node pointer that

18
00:01:44,400 --> 00:01:48,510
is R that will be following E and also I will create one more point.

19
00:01:48,610 --> 00:01:55,830
That is B that is used for creating a new node using this P, I will create a new node on this function.

20
00:01:55,830 --> 00:01:59,130
First of all, I will check whether a root is null.

21
00:01:59,220 --> 00:02:03,120
If Rudy's null, then this is the first node.

22
00:02:07,370 --> 00:02:11,250
So simply, I have to create a new node and make it as a root node.

23
00:02:11,690 --> 00:02:15,590
So by using Pointer B, I will create a new node for creating a new node.

24
00:02:16,370 --> 00:02:18,320
All my log functions for that.

25
00:02:18,320 --> 00:02:20,690
First of all, I cost.

26
00:02:22,830 --> 00:02:23,610
Lord.

27
00:02:25,740 --> 00:02:26,490
STOD.

28
00:02:29,480 --> 00:02:33,950
Then mellark function and here size of.

29
00:02:35,380 --> 00:02:36,820
Struck north.

30
00:02:39,010 --> 00:02:48,900
And then these data, I will set it ASCII value that I want to insert and these left child as well as

31
00:02:48,900 --> 00:02:50,550
a PS right child.

32
00:02:53,290 --> 00:02:54,730
I will assign them as no.

33
00:02:55,360 --> 00:03:02,050
So this has to be done when route is not very far snored, otherwise we will run the procedure on what

34
00:03:02,050 --> 00:03:03,600
we have seen on board.

35
00:03:03,970 --> 00:03:08,740
So unlike what we have seen, that wildy is not equal to null.

36
00:03:09,340 --> 00:03:16,380
We will continue searching for a key value and I will be following E and here we right on the procedure,

37
00:03:16,390 --> 00:03:21,970
same as searching that if key is lasdun.

38
00:03:24,710 --> 00:03:29,660
Is data then moved upon left child?

39
00:03:31,630 --> 00:03:32,920
As if.

40
00:03:35,000 --> 00:03:35,510
If.

41
00:03:37,110 --> 00:03:40,230
G is greater than these data.

42
00:03:41,760 --> 00:03:46,240
Then the move is upon, right child ELT's.

43
00:03:48,230 --> 00:03:53,270
Key element is found, so if key elements are found, we are not going to insert anything, simply we

44
00:03:53,270 --> 00:03:54,410
will return.

45
00:03:55,370 --> 00:04:01,540
M.S., start the procedure now once we have came out of the loop, so.

46
00:04:02,670 --> 00:04:08,040
He will become an and will be pointing on some location where we have to insert a new node.

47
00:04:08,340 --> 00:04:10,750
So for that, first of all, I will create a new norm.

48
00:04:10,860 --> 00:04:15,690
So E as I knew so already, we have written this code for creating a new node.

49
00:04:15,990 --> 00:04:18,899
I will copy that ocean and paste it here.

50
00:04:21,000 --> 00:04:27,990
So it's the same code for creating a new node and once the new node is created by should link it with

51
00:04:27,990 --> 00:04:29,660
the binary search tree.

52
00:04:29,670 --> 00:04:37,170
So I have to again check using R if key is less than Rs.

53
00:04:38,390 --> 00:04:47,360
Data means no new node should come on the left hand side, so that is right on here are alkyl should

54
00:04:47,360 --> 00:04:50,420
be sign that the piece that is new node else.

55
00:04:53,300 --> 00:04:57,500
All right, child should be assigned to that new A.P..

56
00:04:59,950 --> 00:05:08,350
That's it, this is all a new law will be inserted in a binary search tree, so the goal is a little

57
00:05:08,350 --> 00:05:08,740
lendee.

58
00:05:09,310 --> 00:05:12,010
This is the portion which we have already seen on Leibold.

59
00:05:12,020 --> 00:05:17,740
So I have added extra code here that if this is the first known that this route is already null, then

60
00:05:17,740 --> 00:05:18,370
we'll do this.

61
00:05:18,950 --> 00:05:23,800
Then after creating the first node, I should stop the procedure so I can see right return.

62
00:05:24,730 --> 00:05:31,260
So here I don't have to write else because once it has inserted first to note the very first node,

63
00:05:31,270 --> 00:05:32,190
then it will stop.

64
00:05:32,950 --> 00:05:38,860
Otherwise, if it is not null, then it will enter here, then it will enter here and insert a new node

65
00:05:38,860 --> 00:05:39,970
at a given position.

66
00:05:41,290 --> 00:05:43,120
This is perfect now.

67
00:05:43,240 --> 00:05:48,790
Next, I will write on a function for performing in order traversal so that I can confirm the creation

68
00:05:48,790 --> 00:05:53,850
of a binary search tree in order traversal of a by necessity gives sodded order of elements.

69
00:05:53,920 --> 00:05:55,830
Let us try this in other function.

70
00:05:56,740 --> 00:06:04,060
I will write on a recursive function so it will take struct nor pointer.

71
00:06:04,600 --> 00:06:05,990
Let us call it as be.

72
00:06:06,430 --> 00:06:07,240
And that is for.

73
00:06:08,140 --> 00:06:10,000
Rupanyup and.

74
00:06:11,090 --> 00:06:18,500
Here means peace, not equal Donel, although, you know, the procedure for in order traversal, just

75
00:06:18,800 --> 00:06:22,970
writing it here, go on these left Shil.

76
00:06:25,590 --> 00:06:31,020
Then bring INDEF, but in the data percentile, the then.

77
00:06:33,850 --> 00:06:34,960
These data.

78
00:06:37,130 --> 00:06:38,720
Then in order.

79
00:06:41,070 --> 00:06:42,200
These are.

80
00:06:44,940 --> 00:06:45,600
NetSol.

81
00:06:47,370 --> 00:06:53,820
I have two functions already that is inserting an element and performing in order so that I can see

82
00:06:53,820 --> 00:07:00,520
the values that are inserted in binary search tree knowledge is right on the main function and the test

83
00:07:00,520 --> 00:07:02,560
of these incertain in order function.

84
00:07:02,560 --> 00:07:06,690
Then later on I will write on such a function from main function.

85
00:07:06,690 --> 00:07:15,630
I will perform insert operations, I will insert a key and there should be a root, then insert five.

86
00:07:15,720 --> 00:07:22,530
This should go on the left hand side, then insert into it should be on the right hand side.

87
00:07:23,130 --> 00:07:24,270
Then insert.

88
00:07:25,960 --> 00:07:28,780
Eight, so it should be on the right hand side of five.

89
00:07:29,560 --> 00:07:30,760
Then insert.

90
00:07:32,590 --> 00:07:39,340
Today, it should be on the right hand side of 20, then I will perform in order by performing a passing

91
00:07:39,340 --> 00:07:40,700
route of a tree.

92
00:07:41,980 --> 00:07:43,390
So then I perform in order.

93
00:07:43,420 --> 00:07:49,270
I should get the elements in the sorted order because in order to get sorted order, then here also

94
00:07:49,270 --> 00:07:52,510
I will give a new line so that I can get a gap.

95
00:07:53,830 --> 00:07:55,140
Now let us run this one.

96
00:07:55,780 --> 00:07:58,540
If there are any errors, we will remove those errors.

97
00:08:01,360 --> 00:08:06,170
Nothing is displayed, so there may be some error in this one.

98
00:08:06,190 --> 00:08:08,630
It's not a syntax error, but maybe a logical error.

99
00:08:09,060 --> 00:08:10,900
Let's see where I have missed it.

100
00:08:11,200 --> 00:08:13,760
So it means it's not displaying in order.

101
00:08:14,140 --> 00:08:16,290
These elements are not inserted properly.

102
00:08:17,280 --> 00:08:23,180
So it could be even a first value is not displayed, so it means the problem with the root.

103
00:08:23,220 --> 00:08:25,880
So let us check that root root canal.

104
00:08:25,960 --> 00:08:26,370
Yes.

105
00:08:26,930 --> 00:08:33,030
Then when I'm creating first northern route as equal to Nullum inserting a new node with the pointer

106
00:08:33,030 --> 00:08:33,510
p.

107
00:08:33,659 --> 00:08:40,740
Oh, I missed one thing you ruled should be pointing on this new node P so even the first node was not

108
00:08:40,740 --> 00:08:42,960
created since the problem was with the root.

109
00:08:43,409 --> 00:08:48,870
So here, yes, I missed this one then ruled as null and I am creating a new node.

110
00:08:49,100 --> 00:08:51,480
Then the rule should point on that new node.

111
00:08:52,050 --> 00:08:53,930
Now let us run the program once again.

112
00:08:56,320 --> 00:09:01,120
Yes, I got the elements in the sorted order, five, eight and 20 and 30.

113
00:09:01,960 --> 00:09:02,490
Perfect.

114
00:09:03,280 --> 00:09:08,350
Now, one thing I would like to show you before performing in order traversal, I'll just put a break

115
00:09:08,350 --> 00:09:10,720
point here and run the program.

116
00:09:14,410 --> 00:09:22,600
Now, here you can see in the dbag area, Rootes, there, all this having data that and the left child

117
00:09:22,600 --> 00:09:23,400
and the child.

118
00:09:23,410 --> 00:09:29,320
So if I expand left child left child is having data five and if I expand its left child, then it is

119
00:09:29,320 --> 00:09:32,130
having data, nothing on the left hand side.

120
00:09:32,500 --> 00:09:36,900
Then if I expand the right child of five, then you can find it here.

121
00:09:38,370 --> 00:09:41,430
Then let us go on the right child of this group.

122
00:09:41,710 --> 00:09:44,080
I close this one to the right of Ruth.

123
00:09:44,500 --> 00:09:47,170
Rachel is 20 and it's Rachel.

124
00:09:47,170 --> 00:09:47,740
It's 30.

125
00:09:48,870 --> 00:09:51,330
And this left child is nothing, nothing is there.

126
00:09:52,210 --> 00:09:58,470
So you can see the complete structure that is prepared in the heap, so the tree that I wanted to create

127
00:09:58,480 --> 00:09:58,720
is.

128
00:09:59,860 --> 00:10:00,870
Can be seen here.

129
00:10:04,740 --> 00:10:06,660
So that's it, we have finished with this.

130
00:10:07,960 --> 00:10:14,290
Inside and in order traversal removed, the breakpoint novelette on the function for search.

131
00:10:15,510 --> 00:10:21,420
So the search function we have seen and heard on that function, so the logic for search is same as

132
00:10:21,600 --> 00:10:29,460
what we have used all of and insert function, but I have a little search function should return a node

133
00:10:29,460 --> 00:10:31,380
in which the key value is found.

134
00:10:32,100 --> 00:10:33,480
So there's a struct node.

135
00:10:35,730 --> 00:10:37,980
Search, search for key.

136
00:10:41,490 --> 00:10:49,590
The forces searching, I need a pointer that should start from group, so I will take a pointer E that

137
00:10:49,590 --> 00:10:53,040
will start from rule and this will be searching for the key.

138
00:10:53,700 --> 00:10:54,900
And the procedure is simple.

139
00:10:54,900 --> 00:10:58,500
While he is not equal to null.

140
00:11:00,020 --> 00:11:00,500
Chick.

141
00:11:01,530 --> 00:11:08,880
If Disvalue is equal to these data, if it is equal to data, then rate on.

142
00:11:10,530 --> 00:11:11,490
Itself.

143
00:11:12,600 --> 00:11:17,990
Else check if key value is less than these data.

144
00:11:18,940 --> 00:11:24,100
Then it goes up upon left child scientists, a child.

145
00:11:25,710 --> 00:11:34,110
Else move upon Rachel here saying he's our child, that's it, and if you come out of the loop without

146
00:11:34,110 --> 00:11:37,130
any key found and simply written, none.

147
00:11:37,950 --> 00:11:38,820
So that's it.

148
00:11:38,850 --> 00:11:42,900
There's the search functionality we have seen and iterative version.

149
00:11:45,210 --> 00:11:50,280
Now, here, inside the main function, already, you have inserted a few elements that are also performing

150
00:11:50,280 --> 00:11:58,100
in order traversal, then after this I will perform search and search returns or node structure.

151
00:11:58,110 --> 00:11:58,460
Right.

152
00:11:58,470 --> 00:12:02,140
It will return the structure of a node in which the value is found.

153
00:12:02,550 --> 00:12:08,970
So I will take a pointer that is structure pointer on the top inside the main function and declare it.

154
00:12:10,590 --> 00:12:18,660
Struck Naude, I'll take a temporary pointer for taking the result from search, then the stamp assign

155
00:12:18,990 --> 00:12:22,980
all function search and I want to search for a key that is 20.

156
00:12:26,230 --> 00:12:33,760
After this, I will check if it is returning anything, if it is not equal to the element is found so

157
00:12:33,760 --> 00:12:37,210
simply, I will bring the message that element.

158
00:12:40,540 --> 00:12:45,790
Percentile D is found and from that Naude.

159
00:12:47,790 --> 00:12:53,670
I'll take the data that is Ms data, the value that is the note that is written from there, I will

160
00:12:53,670 --> 00:12:56,910
bring the data, otherwise I can print the message.

161
00:12:56,910 --> 00:12:58,830
That element is not found.

162
00:13:00,940 --> 00:13:02,190
Element is not foam.

163
00:13:03,640 --> 00:13:11,200
Again, that said, I'm searching for a chirality, just remember this, I'll run the program and show

164
00:13:11,200 --> 00:13:13,810
you I should get the result that the liberals found.

165
00:13:15,040 --> 00:13:16,540
And it should be in this data.

166
00:13:19,510 --> 00:13:21,580
Yes, element 20 is found.

167
00:13:22,400 --> 00:13:22,990
Yes.

168
00:13:24,380 --> 00:13:35,240
Then I'll give the numbers to Nancy, who is not there in our binary search tree element is not found.

169
00:13:35,480 --> 00:13:39,410
Yes, here you can see the message element is not found.

170
00:13:39,410 --> 00:13:40,490
I mistyped here.

171
00:13:41,450 --> 00:13:48,130
So there's a typing mistake element is not found, so that's all the search function and insert function,

172
00:13:48,140 --> 00:13:53,240
but the function we have seen and all of you have shown you recursive version of search and recursive

173
00:13:53,240 --> 00:13:54,280
version of insert.

174
00:13:54,650 --> 00:13:57,380
So you have to do it as an exercice.

175
00:13:58,430 --> 00:13:59,660
That's all in this video.

