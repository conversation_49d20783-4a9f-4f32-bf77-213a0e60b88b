1
00:00:00,150 --> 00:00:06,510
And this video, we will develop our C language program for implementation of polynomial evaluation

2
00:00:06,510 --> 00:00:07,800
using Lincolnesque.

3
00:00:08,010 --> 00:00:10,170
So already we have done this using Uhry.

4
00:00:10,600 --> 00:00:15,900
Now I will show you how we can represent the polynomial and also how to evaluate this one.

5
00:00:15,930 --> 00:00:16,560
And one more thing.

6
00:00:16,600 --> 00:00:19,380
Addition of two polynomials will be a student exercice.

7
00:00:19,380 --> 00:00:20,170
You have to do it.

8
00:00:20,280 --> 00:00:23,610
So let us start a new project and call it as polynomial.

9
00:00:23,850 --> 00:00:28,710
Linklaters so poorly I have given and the language is slanguage.

10
00:00:28,830 --> 00:00:35,730
Create a project that is the main function all-Clear of these comments for representation of polynomial.

11
00:00:35,760 --> 00:00:41,790
We have already seen the node structure, so I will define node structure for a polynomial.

12
00:00:42,090 --> 00:00:48,070
It needs a coefficient so I can take a coefficient as integer or float, whatever it is.

13
00:00:48,090 --> 00:00:50,720
So I have to get an integer here and exponent.

14
00:00:50,940 --> 00:00:54,060
The next is a pointer to next node.

15
00:00:54,210 --> 00:00:59,540
Then for representing a polynomial I should have a pointer of type polynomial, so I will call the size

16
00:00:59,550 --> 00:01:01,260
only instead of first.

17
00:01:01,260 --> 00:01:04,500
I'm taking a pointer as poly and let it be initially null.

18
00:01:04,739 --> 00:01:06,120
Then how to create this one.

19
00:01:06,120 --> 00:01:07,300
I should be able to create it.

20
00:01:07,320 --> 00:01:13,010
So here, let us write one function for creating a polynomial by taking input from the keyboard.

21
00:01:13,020 --> 00:01:19,720
So I will call the function Ima's create this functional axis of polynomial, this pointer globally.

22
00:01:19,830 --> 00:01:26,370
So here we will take the Thoms Eastern, we'll take a quick feature and exponent and create a column

23
00:01:26,640 --> 00:01:30,090
and each time we will be adding a new term at the end of all.

24
00:01:30,660 --> 00:01:35,580
So for that, I need so far that I need some temporary variable that is pointers.

25
00:01:35,640 --> 00:01:43,740
So I will take pointers struct node pointer for creating a new node and a pointer to point on last node.

26
00:01:43,740 --> 00:01:49,410
And also I have to take integer variables like the number of times for a number of times I declare the

27
00:01:49,410 --> 00:01:50,210
meeting anyway.

28
00:01:50,490 --> 00:01:52,160
We will use these variables.

29
00:01:52,170 --> 00:01:54,420
You can understand why I have declared these variables.

30
00:01:54,530 --> 00:01:59,990
Now, first of all, we should know how many times are there and the number of times.

31
00:02:00,150 --> 00:02:02,670
So I'll give a message and ask how many times are there?

32
00:02:03,000 --> 00:02:08,850
Then whatever the number of times are there, we will take it in a variable num that is number will

33
00:02:08,850 --> 00:02:09,530
have the number of.

34
00:02:10,139 --> 00:02:14,980
So as the number of items are given here, I should create those many times and prepare a polynomial.

35
00:02:15,000 --> 00:02:18,150
Now I have to do this in a loop so I can use the for loop.

36
00:02:18,190 --> 00:02:22,500
For that I have declared a variable I then next I'll give a message.

37
00:02:22,650 --> 00:02:24,510
Print f enter.

38
00:02:25,430 --> 00:02:26,600
Each, Tom.

39
00:02:28,320 --> 00:02:28,710
That.

40
00:02:29,790 --> 00:02:32,450
Coefficient an exponent.

41
00:02:33,450 --> 00:02:39,180
So be taking all the items by taking their coefficients and exponent and using followup, I'll do that

42
00:02:39,180 --> 00:02:46,650
for I assign zero and I use the number of items and I plus plus for each item, I should create an order

43
00:02:46,650 --> 00:02:49,310
and store the coefficient and exponent in that note.

44
00:02:49,320 --> 00:02:52,080
So for that I will first create a new node.

45
00:02:52,320 --> 00:02:57,360
Now node is created, then I should read the data that is efficient and exponent.

46
00:02:57,370 --> 00:03:03,200
So using caniff I will read the two values that is coefficient an exponent.

47
00:03:03,510 --> 00:03:06,840
So inside that is coefficient.

48
00:03:07,780 --> 00:03:13,780
And these explain then I have to link the snowed in.

49
00:03:14,210 --> 00:03:20,590
So, first of all, I will make these next as null because it's going to be a last note, then I have

50
00:03:20,590 --> 00:03:21,150
to link it.

51
00:03:21,280 --> 00:03:25,330
So either it may be a footnote or I have to insert it after last.

52
00:03:25,360 --> 00:03:31,360
So first of all, I will check that if a poly is equal to null means it's the first node, it was the

53
00:03:31,360 --> 00:03:34,090
first node, then only should point on.

54
00:03:34,090 --> 00:03:38,590
That is the first point I should point on as the last last point I should also point on.

55
00:03:39,190 --> 00:03:42,570
So I'll make both of them point on that first node.

56
00:03:43,090 --> 00:03:45,840
Otherwise it has to be inserted as a last known.

57
00:03:46,210 --> 00:03:52,720
So for last node I should say last the next assigned D and last should point on the.

58
00:03:53,650 --> 00:04:00,450
That's enough, so this will create a polynomial, so this will create a new node and inserted at the

59
00:04:00,820 --> 00:04:06,550
last known, then I should have a function for displaying polynomial.

60
00:04:06,580 --> 00:04:14,140
So I will write function here for displaying polynomial void display struct nor pointer B, it will

61
00:04:14,140 --> 00:04:18,490
take a pointer, then it will print all of the items in a polynomial.

62
00:04:18,490 --> 00:04:20,890
So will be print all of them.

63
00:04:21,040 --> 00:04:27,220
So printf percentile D then X thanin percentile D.

64
00:04:28,890 --> 00:04:36,540
Then plus for showing the next Tom Alpha male spaces in between them, then here I should display these

65
00:04:36,690 --> 00:04:41,420
coefficient as well as these exponent, that's all.

66
00:04:41,460 --> 00:04:44,000
Then after this speech, should move on to the next.

67
00:04:44,010 --> 00:04:45,690
No, that's all at the end.

68
00:04:45,840 --> 00:04:48,150
I should give a line gap.

69
00:04:48,880 --> 00:04:51,680
So for a proper format, a display.

70
00:04:51,700 --> 00:04:52,390
So that's it.

71
00:04:52,630 --> 00:04:59,890
I have a function for creating a polynomial by adding Dunbarton and then displaying all the tones.

72
00:05:00,250 --> 00:05:05,620
So just inside the main function, I should call a function create, which doesn't make any arguments,

73
00:05:06,010 --> 00:05:08,050
and I should write a function display.

74
00:05:08,320 --> 00:05:11,230
We should take a polynomial as a parameter.

75
00:05:12,280 --> 00:05:18,880
Let us run this program here is asking for Tom, so I would say for Tom's, I should enter coefficient

76
00:05:18,880 --> 00:05:19,510
and exponent.

77
00:05:19,510 --> 00:05:25,120
So efficient as to an export industry and efficient is to explain this.

78
00:05:25,120 --> 00:05:29,660
To be efficient as to an exponent is one coefficient, to an exponent to zero.

79
00:05:29,680 --> 00:05:31,750
So these are the four times.

80
00:05:32,350 --> 00:05:39,120
So I got two extra reports of three plus two extra reports of two plus two extra of one and two extra

81
00:05:39,120 --> 00:05:39,790
reports of zero.

82
00:05:39,910 --> 00:05:40,950
Yes, it's perfect.

83
00:05:40,990 --> 00:05:46,840
Here I got two warnings because of my log function and the last which may be not initialized.

84
00:05:46,840 --> 00:05:51,610
So I should declare it as a sign that I am initializing it.

85
00:05:52,100 --> 00:05:57,280
Then also I should include header file that is still live.

86
00:05:58,810 --> 00:06:04,810
Now let us write a function for evaluation of Polynomial, so I will write a function called eval.

87
00:06:05,170 --> 00:06:13,690
So return type is integer or I can make it as long because the value may be a little bigger struct nor

88
00:06:13,690 --> 00:06:15,410
the pointer to a polynomial.

89
00:06:15,610 --> 00:06:20,590
This will take apart parameter and also I should pass a value for which I want to evaluate.

90
00:06:20,600 --> 00:06:22,850
That is the value of X of our evaluation.

91
00:06:22,870 --> 00:06:26,320
I may be getting a longer value that is larger and I will declare a variable.

92
00:06:27,280 --> 00:06:34,500
Well, as long as the result of a polynomial in this variable now for scanning through a polynomial

93
00:06:34,510 --> 00:06:40,960
inkless, I should have a while loop and while I should evaluate the terms and I should store them or

94
00:06:41,110 --> 00:06:43,350
add them to this value variable.

95
00:06:43,750 --> 00:06:50,980
So Fortnum's, these coefficient is multiplied that extra the power of exponent.

96
00:06:51,160 --> 00:06:59,080
So I should call the function for that X comma E exponent and B should move to the next node.

97
00:07:00,610 --> 00:07:06,700
So instead of displaying, I'm evaluating, demonstrating that other than Val then finally it should

98
00:07:06,700 --> 00:07:08,960
return the result that is one nine.

99
00:07:09,030 --> 00:07:10,030
That means function.

100
00:07:10,030 --> 00:07:11,620
I will call this well function.

101
00:07:11,650 --> 00:07:16,330
Then here I will call eval function and directly I will print its results or print F.

102
00:07:17,250 --> 00:07:23,460
Percentile early because he was going to return a long value and also I'll give a new line the next

103
00:07:23,580 --> 00:07:29,490
I should call eval function for that, I will send a pointer to a polynomial that is to a first node

104
00:07:29,820 --> 00:07:31,820
and the value of X, I will pass it as one.

105
00:07:32,760 --> 00:07:37,650
So whatever the coefficients I'm giving, I should get the some of the coefficient of the value of X

106
00:07:37,650 --> 00:07:38,880
is one that is run.

107
00:07:39,210 --> 00:07:45,000
I will enter the number of times that is Fortnum's now coefficient is three and the power is five coefficients.

108
00:07:45,240 --> 00:07:52,030
Two of what is four coefficients, two hours, three coefficients two and the power is zero.

109
00:07:52,210 --> 00:07:59,160
Now if you see we are evaluating it based on the value of X as one value of X as one the Nexus One,

110
00:07:59,340 --> 00:08:00,690
then I should get the result.

111
00:08:00,690 --> 00:08:04,070
That is some of the more efficient that is three plus two plus two plus two.

112
00:08:04,080 --> 00:08:05,040
So that is nine.

113
00:08:05,400 --> 00:08:06,360
I should get the result.

114
00:08:06,360 --> 00:08:06,820
That's nine.

115
00:08:06,840 --> 00:08:07,580
So let us check.

116
00:08:08,100 --> 00:08:09,290
Yes, I got there.

117
00:08:09,330 --> 00:08:10,610
That's nine is perfect.

118
00:08:10,630 --> 00:08:12,740
Then you can try with different values and check it.

119
00:08:13,050 --> 00:08:16,230
So that's all with this evaluation of polynomial.

120
00:08:16,230 --> 00:08:17,910
So we have seen various functions here.

121
00:08:17,910 --> 00:08:19,470
That is a representation of polynomial.

122
00:08:19,470 --> 00:08:28,020
We have seen then creation of a polynomial by taking the inputs and display function and evaluation

123
00:08:28,020 --> 00:08:31,020
function and this value should be initialized to zero.

124
00:08:31,110 --> 00:08:31,650
That's all.

125
00:08:31,920 --> 00:08:33,299
So pectus this program.

