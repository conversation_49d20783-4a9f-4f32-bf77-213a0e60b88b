1
00:00:00,480 --> 00:00:07,300
The topic is selection thought, and this is the end square algorithm that comes under competition based

2
00:00:07,320 --> 00:00:07,790
sorting.

3
00:00:09,040 --> 00:00:12,340
I have taken a list of elements I have to fight them.

4
00:00:13,310 --> 00:00:19,490
Why the name selection is taken, we have to understand that so fine explaining the procedure, I will

5
00:00:19,490 --> 00:00:21,630
tell you what is the meaning of selection.

6
00:00:22,070 --> 00:00:28,340
So let us start the procedure, how this will go to the mall, perform this algorithm.

7
00:00:28,340 --> 00:00:35,570
Also sort of the element in process in each one element will be selected just like bubbles, an insertion

8
00:00:35,570 --> 00:00:39,340
sort for any elements and minus one quag.

9
00:00:40,280 --> 00:00:45,070
So let us perform first pass from that, we will learn everything about selection.

10
00:00:45,810 --> 00:00:47,390
So let us perform first pass.

11
00:00:47,390 --> 00:00:50,550
From that, we will understand the workings of selections.

12
00:00:50,580 --> 00:00:53,990
Hard for sorting these elements for the first pass.

13
00:00:54,020 --> 00:00:58,430
I will take this list vertically, so I will write the elements.

14
00:00:58,970 --> 00:01:00,950
So I have a list ready here.

15
00:01:01,340 --> 00:01:04,970
Let us perform first pass in first pass.

16
00:01:04,970 --> 00:01:07,400
We will select first position.

17
00:01:08,580 --> 00:01:12,090
To find out an element for that position.

18
00:01:13,660 --> 00:01:22,460
So I repeat, I stress from the point we select our position, yes, so that is I here select a position.

19
00:01:23,440 --> 00:01:30,070
I was pointing on a position that is index zero now, who should come at this place, the smallest element

20
00:01:30,070 --> 00:01:31,950
in the list should come at that place.

21
00:01:32,230 --> 00:01:36,330
So find out minimum element, find out which of it element is minimum.

22
00:01:36,940 --> 00:01:41,230
So for finding out minimum element, we will take two pointers.

23
00:01:42,380 --> 00:01:51,230
Key, as well as G to both of them will be on the same position we is pointing no, let us see how to

24
00:01:51,230 --> 00:01:59,050
find out minimum move to the next element and check whether this is smaller than their case pointing.

25
00:01:59,060 --> 00:01:59,420
Yes.

26
00:01:59,570 --> 00:02:01,460
So brincat this place.

27
00:02:02,000 --> 00:02:04,250
So they will find out a minimum element.

28
00:02:04,250 --> 00:02:10,759
And if any element is found, it will break their continued moves to the next location.

29
00:02:11,390 --> 00:02:13,160
J is pointing on the Suleiman.

30
00:02:13,160 --> 00:02:15,340
Is it Ballymun smaller than their case.

31
00:02:15,350 --> 00:02:16,090
Pointing, yes.

32
00:02:16,400 --> 00:02:17,780
So kolka here.

33
00:02:17,800 --> 00:02:19,300
Ask it to stand here.

34
00:02:19,310 --> 00:02:21,050
This is the smaller element we found.

35
00:02:22,460 --> 00:02:24,650
Then the move to the next element.

36
00:02:26,990 --> 00:02:27,830
Jazzier.

37
00:02:28,740 --> 00:02:31,170
Is it smaller than the element of our case pointing?

38
00:02:31,240 --> 00:02:34,810
Yes, so-called Kay here, Oscar, to stand here.

39
00:02:35,250 --> 00:02:39,270
We got a still smaller element than where you are pointing.

40
00:02:41,120 --> 00:02:49,040
Now move to the next element, is it smaller than the element of our case pointing no move to the next

41
00:02:49,040 --> 00:02:49,660
location?

42
00:02:51,730 --> 00:02:54,140
Is it smaller than the the case pointing?

43
00:02:54,160 --> 00:02:55,830
No, so don't move.

44
00:02:56,650 --> 00:02:57,290
Continue.

45
00:02:57,370 --> 00:03:01,260
Next element now, is that index six.

46
00:03:01,270 --> 00:03:03,090
So it is out of the list.

47
00:03:03,610 --> 00:03:10,060
So we have finished the entire list and found out our smallest element that keeps pointing.

48
00:03:10,720 --> 00:03:13,270
So just observe one thing.

49
00:03:13,270 --> 00:03:17,500
Just we found out which is the minimum element and we got this index.

50
00:03:17,870 --> 00:03:19,840
We have not swapped any elements.

51
00:03:19,840 --> 00:03:21,340
We did not swap any element.

52
00:03:21,860 --> 00:03:22,350
Right.

53
00:03:22,900 --> 00:03:33,300
Then interchange the element from i.e. to K, then the new list will be two six three eight five four.

54
00:03:33,700 --> 00:03:39,500
So eight is brought in place of two and two is sent there to an eight hour interchange.

55
00:03:39,850 --> 00:03:44,080
Now this is the result of the first pass and this element is sorted.

56
00:03:44,710 --> 00:03:46,020
First element is sorted.

57
00:03:46,750 --> 00:03:53,290
So in one pass we go to one element sorted and that is the smallest element in the list got sorted.

58
00:03:54,100 --> 00:03:55,890
We got the smallest element of the list.

59
00:03:55,960 --> 00:04:02,980
So what we did is we selected a position that is first position that is integral and found out an element

60
00:04:02,980 --> 00:04:04,690
for that particular position.

61
00:04:04,690 --> 00:04:07,590
So we got the smallest element for that position.

62
00:04:08,470 --> 00:04:12,220
Now, this was the first step for the same thing.

63
00:04:12,460 --> 00:04:16,600
Perform a second pass so quickly, I will perform remaining passes.

64
00:04:16,600 --> 00:04:17,769
This is second pass.

65
00:04:18,100 --> 00:04:19,720
First element is already sorted.

66
00:04:19,870 --> 00:04:21,640
Now select the next position.

67
00:04:21,790 --> 00:04:28,600
So I will be pointing here now take a G as well as a K, also here on Leane.

68
00:04:29,830 --> 00:04:38,920
Jan, they're only right then using you find out the element that is smaller than the six.

69
00:04:40,240 --> 00:04:43,150
Is it smaller than that six indexer, too, right?

70
00:04:43,180 --> 00:04:44,170
We have unindexed, too.

71
00:04:44,530 --> 00:04:44,980
Yes.

72
00:04:45,220 --> 00:04:46,810
So Bringuier also here.

73
00:04:48,350 --> 00:04:54,140
The Muji is it smaller than six, nor is it smaller than six, nor is it smaller than six.

74
00:04:54,140 --> 00:04:59,130
No G has finished the list so finish the list once we got the smallest element.

75
00:04:59,150 --> 00:04:59,710
What is that.

76
00:05:00,320 --> 00:05:04,670
OK, that is indexed to that is elementary interchange.

77
00:05:04,790 --> 00:05:10,220
So these two elements that we interchange and the new list will be two, three, six.

78
00:05:10,230 --> 00:05:13,350
These are interchange and a five four as it is.

79
00:05:14,090 --> 00:05:15,920
So these two elements are solid.

80
00:05:16,310 --> 00:05:18,630
Nonnatus perform third parties.

81
00:05:19,130 --> 00:05:22,090
Did I on Indexer two.

82
00:05:22,100 --> 00:05:23,210
That is third element.

83
00:05:23,510 --> 00:05:29,310
We go for the two elements of socket I use here and make a G as well as a K.

84
00:05:29,330 --> 00:05:30,380
Both of them pointed.

85
00:05:30,380 --> 00:05:36,440
They're only now start using G to find out the element that is smaller than this.

86
00:05:37,190 --> 00:05:38,600
Is it smaller than that?

87
00:05:38,810 --> 00:05:43,100
Nor is it smaller than that is five is smaller than six.

88
00:05:43,310 --> 00:05:44,540
So bring here.

89
00:05:45,940 --> 00:05:50,750
Then continue, gee, is it smaller than Workday's case, pointing his foot is less than fine.

90
00:05:50,770 --> 00:05:51,940
So bring it here.

91
00:05:52,900 --> 00:05:59,110
So end of list and we got a minimum element again next five, so interchange these two.

92
00:06:01,530 --> 00:06:04,180
Then the new list will be two, three.

93
00:06:04,530 --> 00:06:09,150
This will interchange so here for then eight, five, six.

94
00:06:09,540 --> 00:06:11,460
Now three elements are sorted.

95
00:06:11,520 --> 00:06:15,900
Next, we have to select a fourth element, that is index three.

96
00:06:16,360 --> 00:06:17,750
We have to solve this one.

97
00:06:18,690 --> 00:06:20,940
We have to find an element for this position.

98
00:06:21,660 --> 00:06:26,460
So KMG will be pointing there, only find out a smaller element.

99
00:06:26,550 --> 00:06:27,740
Is it smaller than that?

100
00:06:27,760 --> 00:06:28,160
Yes.

101
00:06:28,170 --> 00:06:29,340
So bring it here.

102
00:06:30,500 --> 00:06:32,000
Is it smaller than five?

103
00:06:32,030 --> 00:06:37,530
No so list and so this is the smallest element in this list in between these two.

104
00:06:37,940 --> 00:06:41,060
So the list will be two, three, four.

105
00:06:42,580 --> 00:06:50,050
Five, eight desiring to change and six, so these four elements are already sorted and only two elements

106
00:06:50,050 --> 00:06:50,590
are remaining.

107
00:06:51,400 --> 00:06:56,190
So that makes index of four and find out the smallest element for that position.

108
00:06:58,130 --> 00:07:02,450
So start the case as well as a G then Jacir, is it smaller?

109
00:07:02,480 --> 00:07:02,770
Yes.

110
00:07:02,780 --> 00:07:04,000
So bring it here.

111
00:07:05,690 --> 00:07:08,540
An end of the list, then interchange.

112
00:07:10,340 --> 00:07:17,080
Case here, so it is interchange, so the new list is two, three, four, five, distributing the change.

113
00:07:17,090 --> 00:07:18,440
So six, eight.

114
00:07:19,190 --> 00:07:23,690
This was fourth pass and this was fifth pass.

115
00:07:23,930 --> 00:07:26,960
And this is the result of the fifth pass, all elements.

116
00:07:27,740 --> 00:07:29,660
These are the total five elements.

117
00:07:29,660 --> 00:07:34,420
But the sixth element will automatically get further separated so that you can see it clearly.

118
00:07:34,820 --> 00:07:37,220
So total five buses the last 10 years.

119
00:07:38,570 --> 00:07:46,040
So once again, we were selecting our position and finding the element for that position, but is just

120
00:07:46,040 --> 00:07:50,560
like imagine there are some students in a class and is the first to sit in the class.

121
00:07:50,930 --> 00:07:53,140
I have to find out who should sit here.

122
00:07:53,360 --> 00:07:56,660
So I selected the seat and finding out who should sit here.

123
00:07:56,970 --> 00:08:03,050
OK, I'll ask the student to come and sit here, maybe based on height, based on the marks or whatever

124
00:08:03,050 --> 00:08:07,420
it is, and select the seat and find a student who should sit here.

125
00:08:08,240 --> 00:08:15,200
So we selected a position that is first position, that is index zero and find out which element should

126
00:08:15,200 --> 00:08:15,960
come there.

127
00:08:16,190 --> 00:08:18,430
So in the entire list, the two was minimum.

128
00:08:18,450 --> 00:08:19,940
So two was brought there.

129
00:08:20,630 --> 00:08:21,080
That's it.

130
00:08:21,890 --> 00:08:24,280
So selecting a position, finding an element.

131
00:08:24,950 --> 00:08:26,630
No, let us do some analysis.

132
00:08:26,630 --> 00:08:29,930
Find a number of comparisons and number of slots.

133
00:08:31,050 --> 00:08:36,330
See, InfoSpace FastPass, we compared almost all elements, so total six elements out there to five

134
00:08:36,330 --> 00:08:42,750
competitions we have done so competitions with five, a number of the swabs, how many swabs we have

135
00:08:42,750 --> 00:08:43,049
done.

136
00:08:43,380 --> 00:08:46,740
We have performed just one swab, remember this one swab.

137
00:08:47,070 --> 00:08:50,250
And here at most four competitions and just one swab.

138
00:08:50,790 --> 00:08:53,730
And here are three competitions and just one swab.

139
00:08:54,780 --> 00:08:57,870
Two competitions, one side won competition, one swam.

140
00:08:59,120 --> 00:09:04,750
So if you observe the competition, five, four, three, two, one, so total number of competitions

141
00:09:04,760 --> 00:09:07,730
I will write down here, number of competitions.

142
00:09:10,450 --> 00:09:16,780
One plus two plus three plus goes on for any elements, how many competitions will be there and the

143
00:09:16,780 --> 00:09:17,530
minus one.

144
00:09:17,560 --> 00:09:23,500
So this will be an end to end, minus one by two and the order of and square.

145
00:09:25,350 --> 00:09:31,740
So we have already done this type of analysis in bubble sort and insertion thought here also the same

146
00:09:32,910 --> 00:09:34,650
number of competitions are in square.

147
00:09:34,660 --> 00:09:39,420
So the Times and Square then how many slots in each bar?

148
00:09:39,420 --> 00:09:41,630
Just we're performing just one swap.

149
00:09:42,210 --> 00:09:44,130
So a number of swaps are.

150
00:09:45,690 --> 00:09:49,230
Foreign embassies and minus one swap, so it is order.

151
00:09:49,560 --> 00:09:59,170
And yes, this is more important about selection selections for an element only and the minus one slap

152
00:09:59,220 --> 00:10:00,900
for outdraw and slaps.

153
00:10:01,350 --> 00:10:08,190
So this is the only algorithm, which is sort of the elements with the minimum number of swaps to avoid

154
00:10:08,190 --> 00:10:11,340
swaps we have taken as well as a key.

155
00:10:12,060 --> 00:10:13,200
We find the index.

156
00:10:13,200 --> 00:10:18,780
Once we found the index, then we interchange will not be interchanging every time so we can avoid the

157
00:10:18,780 --> 00:10:21,720
changing of elements with the help of extra variable.

158
00:10:22,050 --> 00:10:27,000
So selections are as good for less number of swaps.

159
00:10:28,060 --> 00:10:35,650
Next, one more important thing about Selection's heart, Siefert perform only one pass than what I

160
00:10:35,650 --> 00:10:35,920
get.

161
00:10:36,040 --> 00:10:41,790
Smallest element if I perform just two passes than what I get to smallest element.

162
00:10:42,220 --> 00:10:47,640
So it means if I perform Kapos number of passes, then I get smaller elements.

163
00:10:48,430 --> 00:10:52,280
So yes, just like bubble sort, we were getting killed.

164
00:10:52,280 --> 00:10:54,430
Largest element in selection.

165
00:10:54,430 --> 00:10:56,650
So we get smaller elements.

166
00:10:57,750 --> 00:11:05,820
So intermediate results of selections that are useful now next year, we have to check whether it is

167
00:11:05,820 --> 00:11:08,120
adaptive and stable or not.

