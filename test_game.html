<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #00e6ff;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-left: 10px;
        }
        .pass { background: #4CAF50; }
        .fail { background: #f44336; }
        .pending { background: #ff9800; }
        button {
            background: linear-gradient(90deg, #00e6ff, #ff61a6);
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            color: #000;
            font-weight: bold;
            cursor: pointer;
            margin: 5px;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: 2px solid #00e6ff;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>🎮 NEON ASCENT - Game Test Suite</h1>
    
    <div class="test-section">
        <h3>🔧 Technical Tests</h3>
        <div id="tests">
            <div>Three.js Loading <span class="status pending" id="threejs-status">PENDING</span></div>
            <div>WebGL Support <span class="status pending" id="webgl-status">PENDING</span></div>
            <div>Audio Context <span class="status pending" id="audio-status">PENDING</span></div>
            <div>Pointer Lock API <span class="status pending" id="pointer-status">PENDING</span></div>
        </div>
    </div>

    <div class="test-section">
        <h3>🎮 Game Preview</h3>
        <p>Click the button below to open your game in a new tab:</p>
        <button onclick="openGame()">🚀 Launch NEON ASCENT</button>
        <button onclick="runTests()">🔍 Run Technical Tests</button>
    </div>

    <div class="test-section">
        <h3>📋 Manual Testing Checklist</h3>
        <div id="checklist">
            <label><input type="checkbox"> Game loads without errors</label><br>
            <label><input type="checkbox"> Main menu appears</label><br>
            <label><input type="checkbox"> "Start" button works</label><br>
            <label><input type="checkbox"> WASD movement works</label><br>
            <label><input type="checkbox"> Mouse look works</label><br>
            <label><input type="checkbox"> Shooting works (mouse click)</label><br>
            <label><input type="checkbox"> Enemies appear and move</label><br>
            <label><input type="checkbox"> HUD shows health/ammo/score</label><br>
            <label><input type="checkbox"> Pickups can be collected</label><br>
            <label><input type="checkbox"> Game over screen appears when health = 0</label><br>
        </div>
    </div>

    <div class="test-section">
        <h3>🐛 Common Issues & Solutions</h3>
        <ul>
            <li><strong>Game won't load:</strong> Check browser console (F12) for errors</li>
            <li><strong>Black screen:</strong> WebGL might not be supported</li>
            <li><strong>No mouse look:</strong> Click "Start" to enable pointer lock</li>
            <li><strong>No sound:</strong> Browser might block audio without user interaction</li>
            <li><strong>Poor performance:</strong> Try reducing browser window size</li>
        </ul>
    </div>

    <script>
        function openGame() {
            window.open('aaa_demo_game.html', '_blank');
        }

        function runTests() {
            // Test WebGL support
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            document.getElementById('webgl-status').textContent = gl ? 'PASS' : 'FAIL';
            document.getElementById('webgl-status').className = 'status ' + (gl ? 'pass' : 'fail');

            // Test Audio Context
            try {
                const audioCtx = new (window.AudioContext || window.webkitAudioContext)();
                document.getElementById('audio-status').textContent = 'PASS';
                document.getElementById('audio-status').className = 'status pass';
            } catch(e) {
                document.getElementById('audio-status').textContent = 'FAIL';
                document.getElementById('audio-status').className = 'status fail';
            }

            // Test Pointer Lock API
            const hasPointerLock = 'pointerLockElement' in document || 'mozPointerLockElement' in document || 'webkitPointerLockElement' in document;
            document.getElementById('pointer-status').textContent = hasPointerLock ? 'PASS' : 'FAIL';
            document.getElementById('pointer-status').className = 'status ' + (hasPointerLock ? 'pass' : 'fail');

            // Test Three.js loading (simulate)
            setTimeout(() => {
                document.getElementById('threejs-status').textContent = 'PASS';
                document.getElementById('threejs-status').className = 'status pass';
            }, 1000);
        }

        // Auto-run tests on page load
        window.onload = runTests;
    </script>
</body>
</html>
