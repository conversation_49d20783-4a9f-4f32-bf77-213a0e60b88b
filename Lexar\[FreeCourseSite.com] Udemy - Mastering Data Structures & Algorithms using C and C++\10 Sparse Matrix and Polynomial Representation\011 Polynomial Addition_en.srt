1
00:00:00,540 --> 00:00:05,560
Let us look at how we can add a polynomials and generate the third polynomial.

2
00:00:05,910 --> 00:00:12,210
I have two polynomials here, P1 this is a polynomial and its representation, P2, this is the polynomial

3
00:00:12,210 --> 00:00:13,790
and its representation.

4
00:00:14,700 --> 00:00:16,740
So all of you have taken those storms here.

5
00:00:16,860 --> 00:00:19,660
Now by adding these two, we want to get this one.

6
00:00:20,070 --> 00:00:24,960
So actually we want to add these two polynomial, but our program is having this representation.

7
00:00:25,320 --> 00:00:28,890
So should at least two and get the total representation.

8
00:00:28,980 --> 00:00:30,310
Now let us see how Gragg.

9
00:00:30,480 --> 00:00:32,460
So the first thing is total.

10
00:00:32,460 --> 00:00:35,430
How many items will be there in this third polynomial?

11
00:00:36,710 --> 00:00:44,120
How many times here, three, how many times here, five so common terms that the common power terms

12
00:00:44,120 --> 00:00:45,050
will be added.

13
00:00:45,050 --> 00:00:48,800
If nothing is common, then maximum, how many times will it be there?

14
00:00:49,190 --> 00:00:50,180
Three plus five.

15
00:00:50,180 --> 00:00:50,570
Eight.

16
00:00:50,610 --> 00:00:51,560
Tom Filippetti.

17
00:00:52,670 --> 00:00:59,630
Maximum terms are possible, if some bombs are common power, then they will be some number of items,

18
00:00:59,630 --> 00:01:03,010
maybe less than or equal to eight.

19
00:01:03,500 --> 00:01:06,530
So any way that I can only after adding.

20
00:01:06,770 --> 00:01:09,140
So I need an array here, Offsiders.

21
00:01:09,140 --> 00:01:09,890
Homicides.

22
00:01:10,250 --> 00:01:11,800
Again, maximum eight.

23
00:01:12,230 --> 00:01:16,370
So I should take an array of size eight here, but I already know what will be done.

24
00:01:16,390 --> 00:01:17,950
So I have taken only five here.

25
00:01:18,200 --> 00:01:24,290
So you have to take an array equal to the number of items here, plus number of items here, because

26
00:01:24,290 --> 00:01:26,210
the maximum that many items are possible.

27
00:01:26,630 --> 00:01:35,570
Now let's see how to add these two so far, adding these two, we need a pointer index pointer, not

28
00:01:35,650 --> 00:01:43,500
a pointer eye on zero of this one G on zero of this one and the key on zero.

29
00:01:44,420 --> 00:01:51,130
Like when you add these two polynomials, first item as export for this is also export four.

30
00:01:51,290 --> 00:01:52,730
So these two will be added.

31
00:01:53,450 --> 00:01:55,580
So here what is the exponent for?

32
00:01:55,610 --> 00:01:56,960
What's the explanation for.

33
00:01:57,140 --> 00:01:59,990
So these two exponents are same, powers are same.

34
00:02:00,230 --> 00:02:03,370
So add these coefficients of five plus six 11.

35
00:02:03,680 --> 00:02:06,560
So this is 11 and the power is four.

36
00:02:06,860 --> 00:02:08,940
So now the terms are added.

37
00:02:09,259 --> 00:02:10,490
So move.

38
00:02:10,490 --> 00:02:11,670
I do next.

39
00:02:11,690 --> 00:02:21,010
Don't move G two next time and move on to the next one, then continue the same thing.

40
00:02:22,180 --> 00:02:30,880
Check the power to check power three know that power is three, so this polynomial is not having any

41
00:02:30,880 --> 00:02:35,080
time with the power tree, this is having it done with the power tree.

42
00:02:35,260 --> 00:02:36,310
So that is greater.

43
00:02:36,550 --> 00:02:37,610
So take that one.

44
00:02:37,870 --> 00:02:43,750
So it means if the power of the storm is greater than copper, this one, if the power of the system

45
00:02:43,750 --> 00:02:45,190
is greater than copper, it is fun.

46
00:02:45,460 --> 00:02:49,180
If the powers are equal, then are there coefficients?

47
00:02:49,360 --> 00:02:50,360
This is the procedure.

48
00:02:50,770 --> 00:02:52,900
So now this power is greater.

49
00:02:53,080 --> 00:03:00,910
So copy simply five three moved to next atom and move to Mexico.

50
00:03:03,130 --> 00:03:06,860
Compared to ours, they are equal, they are equal.

51
00:03:07,330 --> 00:03:10,560
So had the coefficients and take that on.

52
00:03:10,610 --> 00:03:11,740
So what are the coefficients?

53
00:03:11,740 --> 00:03:12,900
Two and nine.

54
00:03:13,180 --> 00:03:15,450
So this is 11 and this is two.

55
00:03:15,820 --> 00:03:20,530
Then when we have added we should move and both eyes also moved.

56
00:03:20,900 --> 00:03:25,480
These also moved and keys also moved them again.

57
00:03:25,480 --> 00:03:26,230
Check the power.

58
00:03:26,410 --> 00:03:29,140
This is zero point zero Bovary's one.

59
00:03:29,350 --> 00:03:30,470
So that is greater.

60
00:03:30,640 --> 00:03:31,200
Copy that.

61
00:03:31,210 --> 00:03:33,250
One, two, one.

62
00:03:35,120 --> 00:03:36,080
Move to.

63
00:03:37,360 --> 00:03:38,380
Mooky.

64
00:03:39,390 --> 00:03:47,130
Now, check the powers, both are equal to zero zero, then are the coefficients five plus three eight

65
00:03:47,820 --> 00:03:49,290
eight zero.

66
00:03:50,670 --> 00:03:52,770
That followed the erratic.

67
00:03:54,260 --> 00:03:57,200
Then let us write a piece of code for doing this one.

68
00:03:58,330 --> 00:04:00,330
I is initially zero.

69
00:04:01,970 --> 00:04:10,430
Jay's also zero and Kayes also zero on our starting from zero, then we don't know total how many times

70
00:04:10,430 --> 00:04:17,920
we get so we can't use over for if we know the number of times then we can say for these many times.

71
00:04:18,320 --> 00:04:18,950
So we don't know.

72
00:04:18,950 --> 00:04:21,190
We have to use Vilo loop line.

73
00:04:23,030 --> 00:04:25,730
How long until I reach this.

74
00:04:25,730 --> 00:04:34,100
One month less then and be once and and the reach is this one that is Speedo's and.

75
00:04:36,250 --> 00:04:44,120
I is less than Beerenberg and and just less than be dog.

76
00:04:44,380 --> 00:04:49,270
And so if any one of them finishes, then it should stop.

77
00:04:50,260 --> 00:04:53,940
And while iterating through these, we should compare them.

78
00:04:54,070 --> 00:04:55,900
Whoever's power is greater.

79
00:04:56,290 --> 00:04:57,150
Copy that one.

80
00:04:58,970 --> 00:05:07,530
If it's over, we have to see exponent, exponent, so be one surtees of eyes exponent.

81
00:05:07,550 --> 00:05:12,780
So if I is here then be one of the awful eyes exponent.

82
00:05:12,890 --> 00:05:28,760
So if B once D of eyes exponent is greater than paedos, be off like Gevo starting from here g dot exponent.

83
00:05:30,170 --> 00:05:40,340
Beatles t g got exponent, if this is greater than that one, this is greater than this one, if Suppos

84
00:05:40,760 --> 00:05:52,670
then copied this one as it is in the city of A K, so B 3s, B of K plus plus also afterwards they should

85
00:05:52,670 --> 00:05:55,220
move a key assignment.

86
00:05:56,410 --> 00:05:58,990
We will not be off.

87
00:05:59,020 --> 00:06:02,570
I was also here also after copping, we should move.

88
00:06:02,590 --> 00:06:08,770
I see for copying I should copy coefficient and exponent coefficient and exponent but I'm not saying

89
00:06:09,160 --> 00:06:12,920
the coefficient or dot exponent we don't have to build.

90
00:06:12,990 --> 00:06:18,440
The values will be copied when you are assigning a structure to another structure.

91
00:06:18,760 --> 00:06:21,340
All the members will be copied and c language.

92
00:06:21,370 --> 00:06:25,480
So you don't have to worry about copying each one individually.

93
00:06:25,720 --> 00:06:26,920
So it will be copied.

94
00:06:27,670 --> 00:06:29,860
If this was greater, we copied this one.

95
00:06:30,910 --> 00:06:33,410
If that one is greater, will copy that one.

96
00:06:33,430 --> 00:06:43,070
So I should write on the condition here or else if we do dot the of JS exponent is a greater than the

97
00:06:43,270 --> 00:06:52,990
one p one city of I don't exponent if that one is greater than copy this term in this one.

98
00:06:53,020 --> 00:06:58,560
So just like the same statement instead of a P1, I should say P2 angioplasties.

99
00:06:59,290 --> 00:07:06,270
So here I have written that is in P three or four of Cabelas plus copy B to go to object.

100
00:07:06,640 --> 00:07:08,180
So this term will be copied here.

101
00:07:08,920 --> 00:07:12,180
The lasting final ls final elz.

102
00:07:12,810 --> 00:07:15,780
And so what we should do so in finals wins.

103
00:07:15,790 --> 00:07:17,880
Both are equal, both are equal means.

104
00:07:18,220 --> 00:07:24,040
Exponent should be taken as it is, but the coefficients must be added like five plus six we were adding,

105
00:07:24,400 --> 00:07:26,160
so coefficients must be added.

106
00:07:26,350 --> 00:07:30,120
So I should prepare two terms of this one exponent and coefficient.

107
00:07:30,460 --> 00:07:33,610
So let me write on this one so I have to write two statements here.

108
00:07:34,000 --> 00:07:39,340
So B three Dorte of K Dot Exponent.

109
00:07:40,000 --> 00:07:46,360
I can copy this exponent from here also or from here also either P1 or P2.

110
00:07:46,360 --> 00:07:50,740
So I would like p1 spe one daughter T of idot exponent.

111
00:07:50,980 --> 00:07:52,810
So this is one statement.

112
00:07:52,810 --> 00:07:53,950
So I will uslaw record.

113
00:07:54,520 --> 00:07:56,890
So this is copied then.

114
00:07:57,430 --> 00:07:59,230
Coefficients I should add them.

115
00:07:59,410 --> 00:08:11,320
So P three daughters D all four K plus plus dot coefficient should be equal to be one city of EI plus

116
00:08:11,320 --> 00:08:16,610
plus dot coefficient plus there is more space.

117
00:08:16,610 --> 00:08:27,640
So I'll continue the next line plus B do not deal G plus plus a dot coefficient.

118
00:08:28,720 --> 00:08:38,400
So in P of decay also coefficient I'm adding coefficient Olfert P1 and people to coefficient samadhi.

119
00:08:39,070 --> 00:08:44,290
So here I have explained with example and also how the program code should look like.

120
00:08:44,800 --> 00:08:49,450
I have written the code also so this code will use it in the program.

121
00:08:50,320 --> 00:08:57,280
So that's all about polynomials and the representation, then we will see the complete program on polynomials.

