1
00:00:00,720 --> 00:00:05,230
Let us look at hydrolysis notes for that already have taken some example.

2
00:00:05,700 --> 00:00:11,730
Let us look at the examples here I have taken height to so in height to minimum, these many notes are

3
00:00:11,730 --> 00:00:13,670
possible maximum D that seven.

4
00:00:14,400 --> 00:00:21,090
And here, <PERSON>, three minimum nor seven, maximum 15 and height is for minimum nine and maximum of

5
00:00:21,090 --> 00:00:21,540
31.

6
00:00:21,550 --> 00:00:23,020
I did not draw the completely there.

7
00:00:23,160 --> 00:00:30,750
If you observe the maximum number of nodes, maximum number of nodes are the same as a binary tree formula.

8
00:00:31,020 --> 00:00:36,290
Maximum number of nodes are seen, but it is just a binary or a stigman entry.

9
00:00:36,720 --> 00:00:37,890
Then what is different here?

10
00:00:38,190 --> 00:00:39,500
Minimum number of nodes.

11
00:00:39,750 --> 00:00:43,080
See if it is just above the tree, then these nodes are not required.

12
00:00:43,470 --> 00:00:47,800
Minimum three nodes are sufficient, but here five nodes because it has to be strict.

13
00:00:48,090 --> 00:00:51,360
So for being strict, every node must have either zero and two children.

14
00:00:51,600 --> 00:00:54,470
So five nodes, seven and nine.

15
00:00:55,050 --> 00:00:58,430
So what is the Formula five seven nine.

16
00:00:58,670 --> 00:01:00,720
Next, it may be 11 and 13.

17
00:01:01,050 --> 00:01:02,590
So it's going back or no.

18
00:01:02,910 --> 00:01:04,590
So we need the formula for minimum.

19
00:01:06,160 --> 00:01:15,340
If height is two, then two plus two plus one, so two in the two plus one, if height is three to two

20
00:01:15,340 --> 00:01:18,210
to four, three times to then rule one.

21
00:01:18,550 --> 00:01:21,670
So two, two, three plus one and the height is four.

22
00:01:22,060 --> 00:01:25,240
So this is two to two students.

23
00:01:25,250 --> 00:01:27,340
So four times two plus one.

24
00:01:27,730 --> 00:01:35,170
So what is the formula now so that we can frame the formula, whatever the hardest to and go hide.

25
00:01:35,170 --> 00:01:42,130
Plus one minimum number of nodes are doing to height plus one, what is the formula for maximum nodes.

26
00:01:42,490 --> 00:01:44,040
Two bodies plus one, minus one.

27
00:01:44,050 --> 00:01:45,040
We already know that.

28
00:01:45,430 --> 00:01:47,820
So that we write on the formula here.

29
00:01:47,980 --> 00:01:52,950
If height of a stick by entry is given, then minimum, how many nodes are required.

30
00:01:53,200 --> 00:01:59,380
Just what we have found, the formula that is two and two each plus one then maximum.

31
00:01:59,380 --> 00:02:01,270
How many nodes are possible maximum.

32
00:02:01,660 --> 00:02:04,450
This is same formula to for X plus one.

33
00:02:04,750 --> 00:02:05,540
Minus one.

34
00:02:06,910 --> 00:02:08,000
These are the formulas.

35
00:02:08,050 --> 00:02:15,310
If height is given and you have to find nodes, then what are the formulas if nodes are given and we

36
00:02:15,310 --> 00:02:17,820
have to find out the minimum and maximum height.

37
00:02:18,130 --> 00:02:21,150
So those four plus we can obtain them from here.

38
00:02:21,250 --> 00:02:26,550
Already we have learned in binary three that if you know these two formulas, just convert them.

39
00:02:26,800 --> 00:02:29,300
We can know the formula for height.

40
00:02:29,530 --> 00:02:31,700
So let me write on the formula here.

41
00:02:32,140 --> 00:02:38,860
Here, if the nodes are given, then what is the minimum height and what is the maximum height for minimum

42
00:02:38,860 --> 00:02:39,190
height?

43
00:02:39,200 --> 00:02:41,500
We can get it from this maximum formula.

44
00:02:41,800 --> 00:02:49,270
This is an equal to so and so in terms of H, we should convert it at equal to whatever the formula

45
00:02:49,270 --> 00:02:53,310
is in terms of N, so we have already converted this in the previous video.

46
00:02:53,410 --> 00:02:56,520
So this is Log-in plus one base to minus one.

47
00:02:56,890 --> 00:03:01,120
Yes, this is log base to end plus one.

48
00:03:01,480 --> 00:03:02,320
Minus one.

49
00:03:03,670 --> 00:03:09,030
Then whatever the maximum height formula that we can get it from anyone, no formula for any quota to

50
00:03:09,040 --> 00:03:10,080
achieve plus one.

51
00:03:11,200 --> 00:03:20,360
So what I did in terms of N so H is equal to and minus one by two and minus one by two.

52
00:03:21,100 --> 00:03:22,860
So that said, these are the formulas.

53
00:03:23,870 --> 00:03:27,930
Now, here, the formula for height is important, so height.

54
00:03:28,370 --> 00:03:36,350
If you observe minimum Heidi's log in base to log in plus one, base to minus one and the maximum hardness

55
00:03:36,350 --> 00:03:37,730
and the minus one by two.

56
00:03:38,210 --> 00:03:40,950
So if you observe this one, this is logarithmic.

57
00:03:41,180 --> 00:03:42,620
And this is linear.

58
00:03:43,040 --> 00:03:46,370
So height is ranging from log to.

59
00:03:46,700 --> 00:03:53,180
And so the height properties similar to binary tree only, that is maximum height can be in terms of

60
00:03:53,180 --> 00:03:53,510
an.

