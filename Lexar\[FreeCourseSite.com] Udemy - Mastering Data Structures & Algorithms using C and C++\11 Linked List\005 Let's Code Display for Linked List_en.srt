1
00:00:00,450 --> 00:00:08,250
This video will write a C language program for creating and displaying linked list, so already on white

2
00:00:08,280 --> 00:00:14,340
board, I have shown you how to display a limitless but also I will write on a piece of function for

3
00:00:14,820 --> 00:00:15,800
creating a linguist.

4
00:00:15,810 --> 00:00:16,740
Then I will display.

5
00:00:20,580 --> 00:00:28,860
I will give the function names and display that is for displaying the list and it's going to be a C

6
00:00:28,860 --> 00:00:32,640
language programs will select the language of C language the next.

7
00:00:33,830 --> 00:00:38,800
So it has created a project and the main function is this one I'll trade of these comments.

8
00:00:39,170 --> 00:00:40,410
Yeah, project is ready.

9
00:00:41,570 --> 00:00:48,830
So as we have discussed for linguists, we define a structure for North Slope and defined structure

10
00:00:48,830 --> 00:00:49,520
at this node.

11
00:00:50,400 --> 00:00:59,050
A note contains the number and a pointer to its self-referential pointer, so I will take data, either

12
00:00:59,100 --> 00:01:02,240
integer type and the pointer is off type A..

13
00:01:02,430 --> 00:01:10,310
So struct normed pointer and the pointer name, we have taken this next A..

14
00:01:10,320 --> 00:01:13,470
Structure is ready then for implementing a linked list.

15
00:01:13,470 --> 00:01:15,060
I need a pointer called Fuso.

16
00:01:15,060 --> 00:01:20,910
I will declare a pointer itself here only this becomes a global pointer.

17
00:01:21,060 --> 00:01:28,290
It can be directly accessed or even I can posit other parameter than here for making display function

18
00:01:28,290 --> 00:01:28,620
work.

19
00:01:28,620 --> 00:01:33,090
First of all, I will create a link for creating a list inside the main function.

20
00:01:33,510 --> 00:01:36,180
I will create an array with some numbers.

21
00:01:36,960 --> 00:01:37,920
Three, five.

22
00:01:37,920 --> 00:01:39,750
Seven, ten.

23
00:01:41,670 --> 00:01:47,850
15, I have taken five elements and I want to create a link list using these elements.

24
00:01:48,830 --> 00:01:52,280
So here I will write a function for creating a link list.

25
00:01:53,380 --> 00:02:01,930
Wide function and the function name is create, it will take area as a parameter and the number of elements

26
00:02:01,930 --> 00:02:05,640
in an array, then using those elements, it will create a list.

27
00:02:07,330 --> 00:02:13,200
So I have already declared a first point here, let us initialize that first point to as null now in

28
00:02:13,210 --> 00:02:18,700
this create function, I will scan through this early and I will take one element at a time and I will

29
00:02:18,700 --> 00:02:20,590
create a node and former list.

30
00:02:21,040 --> 00:02:24,900
So for forming a linguist, I need some variables.

31
00:02:24,910 --> 00:02:31,420
So I will take one variable, i.e. that is useful for scanning to that area and also I will take a temporary

32
00:02:31,420 --> 00:02:37,360
pointer naude e this will help creating a new node.

33
00:02:37,840 --> 00:02:41,530
Then also the pointer class on the last node.

34
00:02:41,530 --> 00:02:45,640
It will help me to add a new node at the end of a list.

35
00:02:46,330 --> 00:02:49,940
Then as the Lingle's is empty right now, I will create the first node.

36
00:02:50,260 --> 00:02:59,140
So first assign I'll create a new node using my log function so I will use type casting then mellark

37
00:02:59,140 --> 00:02:59,790
function.

38
00:03:00,310 --> 00:03:02,380
It will take size of.

39
00:03:03,850 --> 00:03:10,780
Struck north, it will create a new normal and he'll be pointing on that one, then this foster data,

40
00:03:11,470 --> 00:03:14,400
I will assign it does it of Zettl.

41
00:03:14,620 --> 00:03:15,940
That is the very first element.

42
00:03:15,940 --> 00:03:16,530
I will give it.

43
00:03:16,960 --> 00:03:18,220
And first.

44
00:03:19,560 --> 00:03:24,420
Next should be a fine as null, because there's the Fastenal, there is nothing beyond this one.

45
00:03:24,940 --> 00:03:28,260
And I also I will make the last point on Washtenaw.

46
00:03:29,010 --> 00:03:31,550
Now Fastenal is really the rest of the north.

47
00:03:31,560 --> 00:03:35,820
I will create them using follow it, reading through the follow and scanning for this.

48
00:03:36,690 --> 00:03:40,950
So the first element that is the of zero I have created in Fastenal and first in the last two point

49
00:03:40,950 --> 00:03:45,180
disappointing that now insightful look for integer.

50
00:03:45,180 --> 00:03:51,830
I assign one onwards list the number of elements that I am starting from one on what is because already

51
00:03:52,230 --> 00:03:53,670
the zero element is created.

52
00:03:54,510 --> 00:03:58,190
Then every time I will create a new node by using my log function.

53
00:03:58,200 --> 00:04:09,620
So same code I will write on starting point and then Mallott function and size of struct normal.

54
00:04:10,590 --> 00:04:13,590
Then inside this D gabs data.

55
00:04:13,710 --> 00:04:17,910
I will fill up the date of aof I then that is next.

56
00:04:17,970 --> 00:04:21,959
There is no next node yet so it should be made as null then.

57
00:04:21,959 --> 00:04:24,780
The last node should point on E.

58
00:04:27,170 --> 00:04:34,190
Last and should be deep and large, should move on national TV, and that is become something not.

59
00:04:35,440 --> 00:04:42,500
There's all the follow when is it reading through all the elements of an idea it will create and Darlinghurst

60
00:04:43,000 --> 00:04:47,940
this great function, I'll be using it every time, whenever I have to process apana Linklaters.

61
00:04:47,950 --> 00:04:53,200
So whenever I want to create a list of some elements, I will create an array and I will pass it to

62
00:04:53,200 --> 00:04:53,920
dysfunction.

63
00:04:53,920 --> 00:04:55,660
It will create the entire list.

64
00:04:56,720 --> 00:05:03,440
So right now, don't focus on how this creation logic is working, just we will focus on display function.

65
00:05:05,060 --> 00:05:11,240
So come back to main function here I have created and then I will call this function create it will

66
00:05:11,240 --> 00:05:16,150
create a link list, I will pass it and the number of elements are five.

67
00:05:16,970 --> 00:05:18,350
So Linklaters created.

68
00:05:19,650 --> 00:05:25,710
Now, I'll give some gap here, then here I will try to display function, so as we have discussed,

69
00:05:25,710 --> 00:05:34,770
I will write on a display function that is iterative function, white display, and to this I will pass

70
00:05:34,770 --> 00:05:35,610
this first point.

71
00:05:35,610 --> 00:05:40,610
I have no doubt it can directly access it, but I will pass a pointer to this.

72
00:05:41,070 --> 00:05:43,650
So I will take a pointer B then.

73
00:05:44,910 --> 00:05:49,580
The logic was simple, that is why P is not equal to null.

74
00:05:51,200 --> 00:05:53,930
Go on blending the elements, so printf.

75
00:05:56,850 --> 00:06:01,530
But some Tildy, because they data type is integer BP's data.

76
00:06:03,670 --> 00:06:11,140
Then after that move, be to next normed, that said, this is the only thing that we have to focus

77
00:06:11,140 --> 00:06:13,150
on, but already we should have a linguist.

78
00:06:13,150 --> 00:06:14,830
So I have already created a linguist.

79
00:06:17,960 --> 00:06:25,880
Now, after this, I will call a function display by passing first as a pointer, so that first pointer

80
00:06:25,880 --> 00:06:27,680
becomes a pointer here.

81
00:06:28,130 --> 00:06:33,080
So people pointing on the first Nalden, the Spearville scandal, Scantlin talentless and display all

82
00:06:33,080 --> 00:06:33,710
the elements.

83
00:06:34,040 --> 00:06:37,370
It will display the data and move to next to node.

84
00:06:39,700 --> 00:06:43,510
Let us run the program and see if there are any errors, I believe, of Debtors'.

85
00:06:45,910 --> 00:06:52,750
There's a warning here that Mellark function needs ahead of time, so I will declare a header file here.

86
00:06:53,860 --> 00:06:59,440
So I will include a five year include study dot at.

87
00:07:02,960 --> 00:07:04,010
Now, let us run it.

88
00:07:05,320 --> 00:07:09,730
This three, five, seven, 10, 15, as displayed by this display function.

89
00:07:10,740 --> 00:07:13,780
Let us debunk this display functions inside the main function.

90
00:07:13,800 --> 00:07:16,680
I'll put a breakpoint here and I will run it.

91
00:07:17,680 --> 00:07:23,860
You can watch here inside the window, that is watch window or debug area, the Saudis having these

92
00:07:23,860 --> 00:07:30,310
values and links it has already created and the first to know what is having value that is displayed

93
00:07:30,310 --> 00:07:33,290
here and next is this one and the next value of the five.

94
00:07:33,610 --> 00:07:37,420
And again, see the addresses of the first N last of three.

95
00:07:38,800 --> 00:07:42,730
Last four figures are four eight nine zero.

96
00:07:44,290 --> 00:07:51,010
The next is force is Eedle, a nexus to F nine zero, and if I say next, then it is having one seven

97
00:07:51,310 --> 00:07:58,150
the next to the point to the next node and that node is having value 10 and its addresses next US AFB

98
00:07:58,150 --> 00:08:00,330
zero and the last one is null.

99
00:08:00,340 --> 00:08:02,370
So you can see this null also at the bottom.

100
00:08:02,740 --> 00:08:08,800
So you can see that entire link lists the nodes, their addresses and the data presented in them.

101
00:08:08,800 --> 00:08:16,270
And that can be seen in the city, but will not be if I continue debugging this display function to

102
00:08:16,600 --> 00:08:17,860
display all these values.

103
00:08:17,860 --> 00:08:20,220
So P will be scanning for the center links.

104
00:08:20,620 --> 00:08:22,210
So they'll be taking these values.

105
00:08:22,220 --> 00:08:24,250
That is forty nine zero.

106
00:08:24,260 --> 00:08:27,850
Next will be for C a zero.

107
00:08:27,850 --> 00:08:29,930
Next will be to F nine zero.

108
00:08:30,250 --> 00:08:31,020
Let us check it.

109
00:08:31,220 --> 00:08:32,799
I will continue execution.

110
00:08:33,070 --> 00:08:35,679
So you just watch how B will be changing.

111
00:08:36,020 --> 00:08:39,090
IP will also appear inside this window ledge.

112
00:08:39,789 --> 00:08:40,220
Yes.

113
00:08:40,240 --> 00:08:44,140
B is now at first node so it's going to print three.

114
00:08:44,620 --> 00:08:46,750
So it is inside of a loop right now.

115
00:08:47,230 --> 00:08:49,470
So it's going to enter inside of a loop right now.

116
00:08:52,090 --> 00:08:52,660
Next.

117
00:08:54,080 --> 00:08:59,780
It prints the data, then next line is going to move to the next node, you can see that the next node

118
00:08:59,780 --> 00:09:01,450
is forces way.

119
00:09:01,490 --> 00:09:04,420
Right now, p value is four eight nine zero.

120
00:09:04,460 --> 00:09:06,440
Then it will change to foresee a zero.

121
00:09:08,930 --> 00:09:16,040
Yes, pregame for 08 is on the next note now and there the data is five and the next Noles addresses

122
00:09:16,040 --> 00:09:18,570
two five nine zero four.

123
00:09:19,010 --> 00:09:21,200
And the next note addresses two four nine zero.

124
00:09:21,290 --> 00:09:22,290
Just watch this one.

125
00:09:22,310 --> 00:09:29,420
I don't have to read it out, then print the data and be moved to do a nine to.

126
00:09:32,040 --> 00:09:38,610
Now it is on good nine zero and the note is containing seven seven is printed, then be moved through

127
00:09:38,610 --> 00:09:41,200
to a physical Espy's on two occasions.

128
00:09:42,000 --> 00:09:46,580
The data is ten that is printed not be moved through to be zero.

129
00:09:47,340 --> 00:09:49,110
He is on to a visual.

130
00:09:49,290 --> 00:09:56,370
So the data is at 15 and the next is not going to be the next, Isma'il said.

131
00:09:57,200 --> 00:10:00,540
Now, Bismullah, it will stop and come out of this function.

132
00:10:00,810 --> 00:10:05,720
So this live on CNN and on this function.

133
00:10:05,940 --> 00:10:12,480
So I have shown you the debugger also how this feels, the values and moving to Nixonland every time.

134
00:10:12,660 --> 00:10:15,790
And you can see the entire link with Inside the Heat available here.

135
00:10:16,590 --> 00:10:17,420
So that's part of it.

136
00:10:17,740 --> 00:10:18,390
The function.

