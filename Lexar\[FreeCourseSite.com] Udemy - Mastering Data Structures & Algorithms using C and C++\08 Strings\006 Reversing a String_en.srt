1
00:00:00,060 --> 00:00:06,450
Now, the topic is reversing a string so far, reversing our string already have taken an example,

2
00:00:06,670 --> 00:00:09,900
a string of the spitefulness written in that string.

3
00:00:10,200 --> 00:00:11,430
I want to reverse it.

4
00:00:12,000 --> 00:00:13,180
So for reversing.

5
00:00:13,200 --> 00:00:14,760
There are more than one method.

6
00:00:15,030 --> 00:00:19,700
So I will show you our first method for reversing a string line.

7
00:00:19,720 --> 00:00:26,010
First method, we will take another array and copy the reverse the form of that original string.

8
00:00:26,010 --> 00:00:27,030
And this a.

9
00:00:27,760 --> 00:00:33,270
So that will remain as it is and we will get the reverse version of that string in another.

10
00:00:33,790 --> 00:00:36,990
So the character will make it out of the string.

11
00:00:37,010 --> 00:00:41,760
So the difference is that we will agree that that sort of becomes a strength.

12
00:00:42,590 --> 00:00:48,950
So let us see how we can reverse it so far, reversing, I should, first of all, reach at the end

13
00:00:48,950 --> 00:00:52,300
of our string that is, I believe, alphabet.

14
00:00:52,700 --> 00:00:57,780
So once that is there, I can start copying those elements in this area in reverse.

15
00:00:57,800 --> 00:01:02,540
So first, trying to copy and then all then edge in this way I can copy.

16
00:01:02,540 --> 00:01:05,750
All these alphabets are not done at zero.

17
00:01:06,230 --> 00:01:08,970
So it is just like a reverse copying a string.

18
00:01:09,560 --> 00:01:13,520
So first of all, I should reach the end of a string.

19
00:01:13,640 --> 00:01:14,620
So let us do that.

20
00:01:14,960 --> 00:01:22,880
I will take I as an index upon and Ernie and I will go on moving this until we reach zero.

21
00:01:23,780 --> 00:01:25,550
So we know very well how to do this.

22
00:01:26,870 --> 00:01:36,020
I'll take some variable I and four I assign zero and eight of I is not equal to zero.

23
00:01:37,900 --> 00:01:38,950
I plus, plus.

24
00:01:40,200 --> 00:01:46,910
No need of anything in sight, just empty brackets, so this I will be stopping at last and look at

25
00:01:46,930 --> 00:01:47,230
it.

26
00:01:47,980 --> 00:01:51,640
No, I should start copying the elements from this place.

27
00:01:52,030 --> 00:01:55,900
That is I minus one on words from this place onwards.

28
00:01:56,230 --> 00:02:01,140
So, OK, I assign a minus one, I introduce one step.

29
00:02:01,750 --> 00:02:05,550
No, I should copy those elements here and should be copied here.

30
00:02:05,770 --> 00:02:08,139
So for this I should have G also here.

31
00:02:08,650 --> 00:02:20,680
Right so now and I will copy that degremont I increment G then or I will copy Degremont I and increment

32
00:02:20,680 --> 00:02:22,310
G then each.

33
00:02:22,390 --> 00:02:31,620
I will copy here then Degremont I and increment G in this way I will caputi then why the net loss went

34
00:02:31,630 --> 00:02:31,960
into that.

35
00:02:32,020 --> 00:02:33,790
Be here then.

36
00:02:35,500 --> 00:02:45,370
Cobwebby, right, degremont I so I becomes minus one and increment G nonstop because we have seneschal

37
00:02:45,370 --> 00:02:46,400
for all the alphabets.

38
00:02:46,420 --> 00:02:47,200
How do you say that.

39
00:02:47,440 --> 00:02:49,570
Because I has minus one.

40
00:02:50,680 --> 00:02:52,510
So the same thing I will write on here.

41
00:02:52,990 --> 00:03:00,010
So I need one more thing that is a G that should start from zero onwards and how long I should continue,

42
00:03:00,010 --> 00:03:03,520
as long as I is greater than or equal to zero.

43
00:03:03,820 --> 00:03:12,400
And every time I should do a minus minus comma G plus plus, then what I was doing, copying an alphabet

44
00:03:12,400 --> 00:03:15,480
from A to a B are equally B right.

45
00:03:15,790 --> 00:03:21,070
So inside the B of a G I should copy if I.

46
00:03:22,570 --> 00:03:28,240
See, initially when I was here, if you look at this, Jimbo's was here, copy the element of AOF I

47
00:03:28,540 --> 00:03:29,730
in the biology.

48
00:03:30,070 --> 00:03:32,080
So biology assign yafai.

49
00:03:33,110 --> 00:03:39,620
So after I finished copying all the elements, when the jihadis reach here, I should add zero here

50
00:03:39,620 --> 00:03:46,850
and be so after the for loop, once I finish everything, then the B of a G should be assigned with

51
00:03:47,390 --> 00:03:48,530
slash zettl.

52
00:03:49,910 --> 00:03:51,240
Now, this has become so strange.

53
00:03:51,680 --> 00:03:55,740
It was array of characters along with the zero, it has became a string.

54
00:03:56,060 --> 00:03:59,740
Now I can bring this reverse form of this string.

55
00:04:00,110 --> 00:04:04,140
So simply saying print deaf person die as.

56
00:04:04,990 --> 00:04:09,100
Maybe this will bring reverse form of a string.

57
00:04:10,430 --> 00:04:17,029
So this is first method and this method we have reversed copied a string and another.

58
00:04:18,870 --> 00:04:21,430
That's all so you can try this program.

59
00:04:21,450 --> 00:04:27,610
You can take a string and one more character study and you can write on this code and check it by yourself.

60
00:04:28,140 --> 00:04:34,520
Now, second, I will show you how to reverse a string using second method to remove this and pretty.

61
00:04:36,120 --> 00:04:40,260
Now, let us look at the second method for reversing a string and this method.

62
00:04:40,260 --> 00:04:43,950
We don't require any extra same string we will modify.

63
00:04:44,580 --> 00:04:45,840
And one more thing.

64
00:04:45,840 --> 00:04:52,200
You should know that in some C C++ compilers mentioned, the recent compilers latest compiler strings

65
00:04:52,200 --> 00:04:54,020
are not mutable.

66
00:04:54,030 --> 00:04:55,100
They cannot be changed.

67
00:04:55,500 --> 00:04:56,700
Instead of Uhry.

68
00:04:56,790 --> 00:05:00,870
If you take it as a pointer, then those strings cannot be modified.

69
00:05:01,230 --> 00:05:03,790
So make sure that you are taking a mutable string.

70
00:05:03,810 --> 00:05:10,440
So in C C++ declaring a string, that array of characters will make it as though it is immutable.

71
00:05:11,040 --> 00:05:13,230
So we will modify the same string.

72
00:05:14,190 --> 00:05:17,150
Now let us come to the procedure how to do this one.

73
00:05:17,940 --> 00:05:23,770
So the procedure is we will exceed the character for the character, for fostering the first letter

74
00:05:23,770 --> 00:05:27,530
with the last letter, second letter with the second letter and this letter.

75
00:05:27,660 --> 00:05:31,680
In this way we will exchange the characters of a string.

76
00:05:32,490 --> 00:05:32,780
Right.

77
00:05:33,030 --> 00:05:37,920
So for that, we have to scan from the left hand side as well as from the right hand side.

78
00:05:38,880 --> 00:05:42,700
So far, the scanning, we should know the length to start from the right hand side.

79
00:05:42,990 --> 00:05:45,550
So already we know how to find the land.

80
00:05:45,660 --> 00:05:48,860
So this is the code I have written already is there.

81
00:05:48,870 --> 00:05:52,550
So I have used and making it stand here.

82
00:05:52,800 --> 00:05:56,370
So let us do one thing and I'll just make it as a G.

83
00:05:57,330 --> 00:05:58,750
So let us say this is G.

84
00:05:58,950 --> 00:06:02,780
J's pointing at the last position that is at null character.

85
00:06:03,570 --> 00:06:04,410
Not from here.

86
00:06:04,410 --> 00:06:10,380
I will use I and let us exchange the characters from starting from this character onwards.

87
00:06:10,860 --> 00:06:11,340
Right.

88
00:06:11,430 --> 00:06:17,330
As long as I is less than she was demeaned or once they cross each other, let us stop.

89
00:06:17,760 --> 00:06:23,660
So let us do this and write on the code for that one so they should start from one step less.

90
00:06:23,820 --> 00:06:28,970
So let us G geoscience here minus one Norgay's here.

91
00:06:29,520 --> 00:06:34,670
So using a follow up we can make them move and copy the element.

92
00:06:34,680 --> 00:06:38,850
So let us say and comes here and the piece goes there.

93
00:06:38,850 --> 00:06:46,590
So we have to set the alphabets so this can be done in the for loop for I start from zero as long as

94
00:06:46,590 --> 00:06:53,910
I use less than G and I is plus plus and G is minus minus.

95
00:06:53,920 --> 00:07:00,900
So we have to increment and decrement and each time what we should do is to change the characters from

96
00:07:00,900 --> 00:07:02,580
there, so forth swapping.

97
00:07:02,790 --> 00:07:03,950
I need some variable.

98
00:07:04,230 --> 00:07:12,180
So let us take a character type variable B and with the help of the city we will exchange Elfy values

99
00:07:12,180 --> 00:07:22,140
exchange with the chance of first of all taken in d then elfy assign of G then E of a g assign that

100
00:07:22,590 --> 00:07:31,940
B so this for local exchange c exchange the two alphabets and move I increment and decrement chain.

101
00:07:32,130 --> 00:07:33,030
So let us do it.

102
00:07:33,420 --> 00:07:36,600
Next I will increment and then she will degremont.

103
00:07:37,200 --> 00:07:38,610
So all comes here.

104
00:07:38,810 --> 00:07:45,840
Lt goes there then I increments and J decrements so indigene.

105
00:07:45,840 --> 00:07:49,050
This s comes here and T goes there.

106
00:07:49,440 --> 00:07:56,500
Then I increments and J decrements now stop because I have become greater than J.

107
00:07:56,820 --> 00:07:57,900
So this will continue.

108
00:07:57,900 --> 00:08:03,540
As long as I is less than G then I don't have to write it so I'll leave it there.

109
00:08:03,540 --> 00:08:04,860
We have not disturbed it.

110
00:08:05,230 --> 00:08:06,480
It is a different place.

111
00:08:06,720 --> 00:08:14,100
So simply if I print this one I get the reverse string print deaf person dialis.

112
00:08:14,280 --> 00:08:20,220
That is a let's see we have not used this unordinary.

113
00:08:20,220 --> 00:08:24,170
Five I remove that we have used just to add the character.

114
00:08:24,990 --> 00:08:29,880
So this is the method for reversing a string without using another.

115
00:08:30,570 --> 00:08:35,220
So we have simple methods, even this method as a student exercise.

116
00:08:35,220 --> 00:08:39,330
So you have to write on this code and try it, that's all.

117
00:08:39,330 --> 00:08:43,799
In this video, we look at a few more options on string and next video.

