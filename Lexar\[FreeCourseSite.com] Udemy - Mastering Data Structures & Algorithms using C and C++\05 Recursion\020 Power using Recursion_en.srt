1
00:00:00,600 --> 00:00:09,090
In this video, we'll talk about explain function that is empower and so raising and for and times that

2
00:00:09,090 --> 00:00:11,610
is multiplying M for and times.

3
00:00:12,510 --> 00:00:16,620
So for this problem, we will try to define the recursive function for that.

4
00:00:16,620 --> 00:00:19,110
First of all, I will explain what the problem is.

5
00:00:19,620 --> 00:00:21,660
She suppose I have to find out.

6
00:00:22,840 --> 00:00:30,910
Booba five, so this means two into two multiplied by two, and this is multiplied by itself four or

7
00:00:30,910 --> 00:00:37,180
five times, so it means embar and means and should be multiplied for.

8
00:00:39,990 --> 00:00:43,620
And The Times four and The Times.

9
00:00:46,070 --> 00:00:54,350
And should be multiplied four times now, this, if I write it as a power function that is par, I'm

10
00:00:54,350 --> 00:00:55,430
gonna end.

11
00:00:55,730 --> 00:01:10,530
Then it means I'm into, I'm into and that goes on to end the minus one times in two, one more time.

12
00:01:11,600 --> 00:01:17,690
This will continue for and minus one times and one more time, for example, 2.5.

13
00:01:17,930 --> 00:01:19,720
So this is four times.

14
00:01:19,730 --> 00:01:23,300
This is four times and one more time.

15
00:01:23,540 --> 00:01:30,860
So Simbi mPower and memes should be multiplied four and minus one times then again multiplied by one

16
00:01:30,860 --> 00:01:31,310
more time.

17
00:01:31,310 --> 00:01:33,090
So it becomes total end times.

18
00:01:34,220 --> 00:01:36,090
Now this I can define it recursively.

19
00:01:36,110 --> 00:01:48,060
I can say part of M m is part of Ankomah and minus one times into M.

20
00:01:49,250 --> 00:01:53,510
So yes, this can be defined as a recurrence relation.

21
00:01:56,090 --> 00:02:07,130
Part of Ankomah and minus one in two and then and as greater than zero one and has become zero, then

22
00:02:07,150 --> 00:02:09,440
one is equal to zero as it is a product.

23
00:02:09,440 --> 00:02:10,690
So it should have done one.

24
00:02:10,699 --> 00:02:12,080
So minimum value is one.

25
00:02:12,680 --> 00:02:13,490
So that's it.

26
00:02:13,520 --> 00:02:19,930
So for this mathematical definition, I can write a recursive function using C or C++.

27
00:02:20,270 --> 00:02:21,860
So let me write a function here.

28
00:02:22,550 --> 00:02:29,780
Integer bar in M come on in and.

29
00:02:32,330 --> 00:02:47,630
If and is equal to zero of it on one otherwise Ripton, part of Ankomah and minus one.

30
00:02:47,650 --> 00:02:52,080
So that's all it is, just a two three line functions, so simple function.

31
00:02:52,120 --> 00:02:55,140
So this is converted into a recursive function.

32
00:02:56,180 --> 00:02:59,540
I would take one example and show you how it works.

33
00:02:59,960 --> 00:03:06,090
Let us stress this one for part of two leads to two nine.

34
00:03:07,040 --> 00:03:10,940
So two days to nine, embeddedness two and introduce nine.

35
00:03:11,150 --> 00:03:13,770
So nine, that is not equal to zero.

36
00:03:14,060 --> 00:03:18,230
So it should perform this operation that is part of.

37
00:03:20,020 --> 00:03:29,540
Bukola is one that is nine minus one that is eight, so I will rewrite it as eight multiplied by M..

38
00:03:29,640 --> 00:03:31,350
So what is and this is true.

39
00:03:32,910 --> 00:03:34,800
So let's continue now.

40
00:03:34,800 --> 00:03:38,020
This call has to be finished and only multiplication will be done.

41
00:03:38,250 --> 00:03:42,600
So what the scholars bugger off two comma as this is eight.

42
00:03:42,600 --> 00:03:43,560
It is not zeros.

43
00:03:43,590 --> 00:03:45,050
Every time this has to be done.

44
00:03:45,330 --> 00:03:47,870
So this will become seven multiplied by two.

45
00:03:48,150 --> 00:03:57,390
Then again, part of two comma six multiplied by two and part of two comma five multiplied by two.

46
00:03:59,480 --> 00:04:02,660
Bottles to come out for multiplied by two.

47
00:04:04,770 --> 00:04:11,480
Unfinished business, so that's all I have made all the calls and the last call is to come on zero zero

48
00:04:11,880 --> 00:04:13,880
when necessary, three times one.

49
00:04:14,130 --> 00:04:15,870
So this will be one.

50
00:04:17,589 --> 00:04:26,040
Multiplied by two thousand two hundred here and multiplied by two again, it is too square to square

51
00:04:26,060 --> 00:04:32,260
returns here, so this is multiplied by two, so this becomes two cube and so on.

52
00:04:32,830 --> 00:04:34,840
So I don't have to work out on this one.

53
00:04:35,110 --> 00:04:39,070
So you can see that one, two, three, four, five, six, seven, eight, nine.

54
00:04:39,490 --> 00:04:41,320
So nine times two is multiplied.

55
00:04:41,330 --> 00:04:51,010
So the result is two point nine so far value and as nine it has made a total ten columns, nine two

56
00:04:51,010 --> 00:04:52,470
zero Lincoln.

57
00:04:52,510 --> 00:04:54,020
So unless one counts.

58
00:04:54,760 --> 00:04:59,890
So now you know how much time it is taking and how much memory space it is consuming, as there are

59
00:04:59,890 --> 00:05:05,230
nine plus one calls for it and plus one and is out of times.

60
00:05:05,310 --> 00:05:05,840
Out of often.

61
00:05:06,130 --> 00:05:09,160
And what is the size of the stack that is also out of ten.

62
00:05:10,620 --> 00:05:16,230
Because the number of activation records created inside the stack are dependent on the number of calls.

63
00:05:16,260 --> 00:05:21,080
That is, unless one counts, not one more thing we should analyze here.

64
00:05:21,090 --> 00:05:25,350
We should observe here total how many multiplications is performing.

65
00:05:25,860 --> 00:05:29,370
So one, two, three, four, five, six, seven, eight, nine.

66
00:05:29,720 --> 00:05:31,890
Sertraline multiplications outperform.

67
00:05:32,340 --> 00:05:38,300
So is it possible to compute this one with less number of multiplications?

68
00:05:38,880 --> 00:05:39,660
Let us check.

69
00:05:39,780 --> 00:05:41,210
Is it possible to do that?

70
00:05:41,910 --> 00:05:51,210
See here if suppose it is dupa eight so two eight can be done, has to power to and perform.

71
00:05:51,480 --> 00:05:52,980
So this is to enter.

72
00:05:52,980 --> 00:05:56,120
To perform.

73
00:05:56,400 --> 00:06:04,610
So it means if I perform one multiplication on the same value, then power will reduce by half over

74
00:06:04,620 --> 00:06:05,830
the learning by half.

75
00:06:06,870 --> 00:06:13,290
And suppose it is to perform nine point nine then this can return as to into.

76
00:06:14,920 --> 00:06:16,400
To square perform.

77
00:06:16,690 --> 00:06:23,680
So because it is, ah, we can't directly make it half so first one, to have taken it out and decide

78
00:06:23,800 --> 00:06:30,490
it has to power it again so I can make it tough so that the power is even then I can directly have it

79
00:06:30,490 --> 00:06:30,810
out.

80
00:06:31,420 --> 00:06:33,460
So if the power is even no.

81
00:06:33,460 --> 00:06:35,860
Then I can directly take half of the power.

82
00:06:36,100 --> 00:06:41,450
And if the numbers are done I can take one multiplication extra and then I can take half of the power.

83
00:06:41,710 --> 00:06:47,390
So if I multiply the number directly, then the power is getting reduced by half.

84
00:06:48,040 --> 00:06:53,410
So with this observation, we can write a power function faster than this one.

85
00:06:53,680 --> 00:06:56,910
So let us rewrite that power function here.

86
00:06:56,920 --> 00:06:59,610
I will write on power function integer.

87
00:07:00,400 --> 00:07:03,640
I'll give the same name power lured from New Numeration.

88
00:07:04,360 --> 00:07:06,520
And I am.

89
00:07:08,740 --> 00:07:15,640
First of all, if N is equal to zero, then return one next check.

90
00:07:15,640 --> 00:07:24,910
If it is even if one is more than two, if it is equal to zero if and the maldito is equal to zero means

91
00:07:24,910 --> 00:07:31,560
number is even if power is even then I can reduce the power by dividing it by two.

92
00:07:31,900 --> 00:07:33,570
So let us write that code here.

93
00:07:33,790 --> 00:07:46,150
So if it is even number then ripton called power function and multiply M into M, so one multiplication

94
00:07:46,150 --> 00:07:53,520
that is Emin's multiplied by itself for one time, then this will be invited to next group.

95
00:07:53,950 --> 00:08:02,140
So the number of multiplication will reduce further than Ellesmere's number is not even power is not

96
00:08:02,140 --> 00:08:06,850
even poverties ared if power is already one multiplication extra.

97
00:08:07,060 --> 00:08:20,110
So I will write on ripton mental power off into M. comma and minus one divided by two.

98
00:08:21,220 --> 00:08:26,610
If it is our number then one multiplication we are taking here directly and then and then minus one

99
00:08:26,620 --> 00:08:27,100
by two.

100
00:08:27,370 --> 00:08:30,970
So in minus one it will become even numbered and divided by two.

101
00:08:31,960 --> 00:08:37,539
So let us take an example and trace this function and seeing how many multiplication we can get done.

102
00:08:37,539 --> 00:08:42,760
So so I take the same example that is part of Boogerman nine.

103
00:08:43,390 --> 00:08:48,130
So we are looking at this function there, a new version of a function which is having less number of

104
00:08:48,130 --> 00:08:49,000
multiplications.

105
00:08:49,510 --> 00:08:52,770
So let us try this power of Bookham.

106
00:08:52,790 --> 00:08:56,290
One name, this is two and this is nine.

107
00:08:56,290 --> 00:08:57,910
So edness zero.

108
00:08:57,910 --> 00:08:59,110
No, and it's not zero.

109
00:08:59,320 --> 00:09:00,500
So N is even.

110
00:09:00,500 --> 00:09:02,260
No, it is not even so.

111
00:09:02,260 --> 00:09:02,980
It is odd.

112
00:09:03,040 --> 00:09:11,320
So the letter here so m and also and so I will write on those things directly here to in the power of.

113
00:09:13,130 --> 00:09:21,170
Two into to do so, I'll try to square citizens and to em, so two square comma and the minus one by

114
00:09:21,170 --> 00:09:25,850
two, so that is nine minus one by two, nine minus one by two.

115
00:09:26,360 --> 00:09:27,440
That is eight by two.

116
00:09:27,450 --> 00:09:28,340
So it will be four.

117
00:09:28,370 --> 00:09:29,870
So I really like it at for.

118
00:09:31,280 --> 00:09:34,250
So to square, that is for coming forward.

119
00:09:34,970 --> 00:09:39,030
So this is the call, not again, a call is made this time.

120
00:09:39,050 --> 00:09:44,410
This is for that is two squares, two square and this is four so far.

121
00:09:44,410 --> 00:09:46,670
It is not zero for evil.

122
00:09:46,670 --> 00:09:48,170
No evil.

123
00:09:48,170 --> 00:09:54,340
No, just there is a call that multiplying into and end by two.

124
00:09:54,650 --> 00:09:56,810
So let us take our next call.

125
00:09:56,960 --> 00:09:58,760
That is part of.

126
00:10:00,130 --> 00:10:08,170
I mean, m m is now to square, so it becomes too square into two square and this should be bitou for

127
00:10:08,170 --> 00:10:11,940
four by two, four by two, that's what we have to do.

128
00:10:12,160 --> 00:10:13,480
So you can see this line.

129
00:10:14,170 --> 00:10:15,460
So this I will rewrite.

130
00:10:15,460 --> 00:10:20,890
So to square and square, this becomes two part four and four by two.

131
00:10:20,890 --> 00:10:22,090
So this becomes two.

132
00:10:23,700 --> 00:10:25,030
The new call again.

133
00:10:27,230 --> 00:10:37,310
Then let us continue to pause for and this has to do is not zero and two is even so it will enter into

134
00:10:37,310 --> 00:10:37,870
the spot.

135
00:10:38,330 --> 00:10:48,780
So next column that is aiming to end the power of Super four into two powerful comma and the two.

136
00:10:48,920 --> 00:10:50,890
So this is too divided by two.

137
00:10:51,470 --> 00:10:53,020
So I will write this one.

138
00:10:53,030 --> 00:10:55,580
So this becomes topower eight.

139
00:10:57,110 --> 00:10:59,270
And this becomes two by two as one.

140
00:11:03,120 --> 00:11:11,820
Now, this discon, this is to our aid, and this is one this time, and it's not zero but even and

141
00:11:13,260 --> 00:11:17,440
this time and is not zero and and it's not even number.

142
00:11:17,580 --> 00:11:20,790
It is one third and I think arge or Depok.

143
00:11:21,060 --> 00:11:24,970
So in that I'm in the power of so-and-so.

144
00:11:25,110 --> 00:11:31,740
So Emmas, how much to eight into power of Emmental.

145
00:11:31,740 --> 00:11:40,320
And that is to put it into Dupere that is topower 16 Gunma and the minus one by two one minus one by

146
00:11:40,800 --> 00:11:41,340
zero.

147
00:11:43,410 --> 00:11:47,370
So that's the last call now enhanced became zero.

148
00:11:47,460 --> 00:11:49,350
So four zero it becomes one.

149
00:11:49,860 --> 00:11:57,780
So this four zero value the return result, this one, this is one known what it will become.

150
00:11:58,650 --> 00:12:02,340
Dupo read in Q1 to pour aid into one.

151
00:12:02,850 --> 00:12:06,830
So this is to support aid and support aid goes here.

152
00:12:07,020 --> 00:12:12,090
So this becomes what each and every year this becomes Tobor aid.

153
00:12:12,300 --> 00:12:13,860
There is nothing, no multiplication.

154
00:12:13,860 --> 00:12:16,980
Is there just a return and to power it goes here.

155
00:12:17,310 --> 00:12:21,420
So this is going to be borate to into.

156
00:12:22,740 --> 00:12:26,130
So this is 2009 and 2009 online here.

157
00:12:28,030 --> 00:12:33,880
So whenever the call was for our number, there is one multiplication even then, but there is no multiplication

158
00:12:33,880 --> 00:12:35,110
simply it returns the value.

159
00:12:35,530 --> 00:12:36,730
So finally we got Don.

160
00:12:36,730 --> 00:12:44,330
So that is Drupa nine and total how many multiplications we perform one multiplication.

161
00:12:44,350 --> 00:12:47,810
And here we have taken square this this was 202, actually.

162
00:12:48,100 --> 00:12:53,670
So one, two, then three, four and five here.

163
00:12:53,740 --> 00:12:55,200
So one multiplication six.

164
00:12:55,750 --> 00:13:03,610
So just in six multiplication, we got to power nine in the previous version for this one, it has taken

165
00:13:04,120 --> 00:13:06,490
through the line multiplication for getting done.

166
00:13:06,520 --> 00:13:09,220
So when the power was nine, but No.

167
00:13:09,220 --> 00:13:15,210
Four over nine, this has worked faster and we got the result than just a six multiplications.

168
00:13:16,640 --> 00:13:22,820
So that's all about this power function, so we have a faster version of Exponent, and that is power

169
00:13:22,820 --> 00:13:29,520
function, also the same function can also be done using iteration that is using loop.

170
00:13:29,750 --> 00:13:31,930
So that's, again, a student exercise.

171
00:13:32,270 --> 00:13:33,320
So you have to do it.

172
00:13:34,950 --> 00:13:39,930
So we will continue looking at more example of recursions, incoming videos.

