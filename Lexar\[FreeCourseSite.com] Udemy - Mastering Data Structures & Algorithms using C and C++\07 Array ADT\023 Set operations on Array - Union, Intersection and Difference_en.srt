1
00:00:00,390 --> 00:00:07,770
Now, let us look at said operations, if the assets are stored in another, then how we can perform

2
00:00:07,770 --> 00:00:08,820
those operations.

3
00:00:09,120 --> 00:00:15,870
So the operations are performing union of two sets, intersection and different set membership.

4
00:00:16,200 --> 00:00:23,790
So here I have a example of a two sets that are stored in arrays and beat from five five elements I

5
00:00:23,790 --> 00:00:25,530
have taken and I have taken.

6
00:00:25,530 --> 00:00:26,690
One more example here.

7
00:00:27,150 --> 00:00:31,750
The difference is these elements are not sorted, but these are already sorted.

8
00:00:32,340 --> 00:00:38,720
So let us see how we can perform these operations if the elements are not sorted and if they are socket.

9
00:00:39,120 --> 00:00:41,530
So let us look at the union operation.

10
00:00:42,120 --> 00:00:43,780
Let us see how to perform union.

11
00:00:43,800 --> 00:00:48,780
So for that, I will say this is having amendments and this is having amendments.

12
00:00:49,740 --> 00:00:50,910
Now, what is the procedure?

13
00:00:51,270 --> 00:00:55,540
Copy all the elements of three, five, ten, four, six.

14
00:00:55,560 --> 00:00:57,060
So the first set is complete.

15
00:00:59,360 --> 00:01:01,610
How much time it has taken and.

16
00:01:03,250 --> 00:01:05,560
Then I should copy second set.

17
00:01:07,920 --> 00:01:10,200
Twelve, Shelley Capito not.

18
00:01:11,190 --> 00:01:13,680
See, I cannot blindly copy second set.

19
00:01:14,700 --> 00:01:21,240
If all they need is present here, I should not copy it to help search for it, is it they're not there,

20
00:01:21,390 --> 00:01:25,540
then copy it to a for search for it.

21
00:01:25,560 --> 00:01:27,460
Is it there guys there?

22
00:01:27,780 --> 00:01:30,030
So don't copy it then.

23
00:01:30,040 --> 00:01:31,400
Seven search for it.

24
00:01:31,410 --> 00:01:31,700
Is it.

25
00:01:31,710 --> 00:01:33,780
They're not there so copy it.

26
00:01:34,800 --> 00:01:41,520
So every element I'm searching here, searching takes how much time depends on the number of elements

27
00:01:41,520 --> 00:01:42,190
present here.

28
00:01:42,480 --> 00:01:44,490
So I'll you have copied those elements here.

29
00:01:44,490 --> 00:01:45,820
So M elements are there.

30
00:01:46,080 --> 00:01:47,140
So it is sticking out.

31
00:01:48,240 --> 00:01:50,390
So it is taking em time for searching.

32
00:01:51,120 --> 00:01:56,360
So let us continue to assist them, not the copy five then.

33
00:01:56,460 --> 00:01:57,360
So dumb compared.

34
00:01:57,660 --> 00:02:00,850
So four and five were already present here so do not copy.

35
00:02:01,140 --> 00:02:03,420
So every element we have to search.

36
00:02:05,240 --> 00:02:09,930
So how much time it is taking and is the time taken for searching?

37
00:02:10,400 --> 00:02:16,430
No doubt the elements are increasing after work, but let's say the time for searching how many times

38
00:02:16,430 --> 00:02:18,650
we have done this one for end times.

39
00:02:18,650 --> 00:02:28,070
So into and so the total time is as a photocopying foster elements, then second one is copying secondary

40
00:02:28,070 --> 00:02:29,900
elements of that, the second sex element.

41
00:02:30,140 --> 00:02:32,030
But every element has to be compared.

42
00:02:32,720 --> 00:02:34,100
So this is a plus.

43
00:02:34,260 --> 00:02:37,340
And in doing so, this is the highest degree.

44
00:02:37,340 --> 00:02:40,220
And in doing so we will not use the two variables.

45
00:02:40,220 --> 00:02:41,860
We will be a single variable only.

46
00:02:42,020 --> 00:02:44,240
So let us call it a size and plus.

47
00:02:44,600 --> 00:02:48,380
And in the end that is endless and square.

48
00:02:48,620 --> 00:02:51,550
So this is order of and squared time.

49
00:02:52,080 --> 00:02:55,290
So for performing union, it has taken in square time.

50
00:02:55,970 --> 00:02:57,050
This is very slow.

51
00:02:58,320 --> 00:03:00,700
This is Quadratic time-Consuming.

52
00:03:01,920 --> 00:03:09,000
Now let's see if we have assorted elements in the search, then how we can perform union on those sets.

53
00:03:09,610 --> 00:03:10,100
Let us.

54
00:03:13,800 --> 00:03:20,160
Let us say these are elements and these are elements and these are sorted, I have to combine them and

55
00:03:20,160 --> 00:03:22,800
store them and see without duplication that this union.

56
00:03:24,030 --> 00:03:27,390
So I can use the merger procedure here.

57
00:03:28,500 --> 00:03:32,370
We have already done what is merging that same merging procedure.

58
00:03:32,400 --> 00:03:33,180
We will perform.

59
00:03:33,540 --> 00:03:41,520
So what we will do this here is I am here is Jan Hirschi, company of I would be of GE biology's small

60
00:03:41,520 --> 00:03:47,310
companies to then move GE and move quickly if I were to be of change.

61
00:03:47,520 --> 00:03:56,430
So of a small so three kopit then move this one if I can be eligible tahseen so copy that forward and

62
00:03:56,700 --> 00:03:57,470
move.

63
00:03:57,870 --> 00:04:05,370
I also and also in case here if they're equal to this one nagaina off I would be ok.

64
00:04:05,440 --> 00:04:07,290
Both are equal corporate ones.

65
00:04:07,650 --> 00:04:15,840
Move I also and Johnson and case here Komaroff I would be of G six this morning.

66
00:04:15,840 --> 00:04:26,520
So concretised one move I if I would be of G seven is small seven Escorpion move gene aof I would be

67
00:04:26,930 --> 00:04:32,250
ten is a small capitán then leftovers l so copy that to it.

68
00:04:35,330 --> 00:04:42,030
So this was only a slight change, is that if they are same copy only once and move both?

69
00:04:42,050 --> 00:04:43,760
I think so.

70
00:04:43,760 --> 00:04:46,640
It is merge process or less and less.

71
00:04:46,640 --> 00:04:51,050
And we can say this is take off and plus and.

72
00:04:52,980 --> 00:04:59,580
If you want to represent us in a single variable, then you can see tea towels and listen, this is

73
00:04:59,580 --> 00:05:04,130
nothing but doing so too is efficient for it.

74
00:05:04,620 --> 00:05:06,660
And so this out of energy.

75
00:05:07,740 --> 00:05:13,380
So if the list is not sorted and squared, if they are sorted, then.

76
00:05:13,560 --> 00:05:17,340
And so that's all this about Union Mixed-Use.

77
00:05:18,160 --> 00:05:23,640
Intersection and difference, so we don't have to learn much of their knowledge.

78
00:05:23,680 --> 00:05:25,200
We know how to perform union.

79
00:05:25,210 --> 00:05:27,380
They are similar with some changes in them.

80
00:05:27,820 --> 00:05:32,270
Now, let us look at intersection, intersection of two sets.

81
00:05:33,130 --> 00:05:38,110
We have to take common elements of A and B and store them and see.

82
00:05:39,570 --> 00:05:47,730
So the procedure is start checking the elements of a copy them and see what before copying check if

83
00:05:47,730 --> 00:05:50,890
this element is already present, then it's not there.

84
00:05:51,000 --> 00:05:52,410
So move to the next element.

85
00:05:52,860 --> 00:05:53,880
Is it present here?

86
00:05:53,910 --> 00:05:54,870
Yes, it is present.

87
00:05:54,900 --> 00:05:55,380
Copy that.

88
00:05:55,380 --> 00:05:55,920
Five.

89
00:05:57,680 --> 00:06:02,340
So I'm checking this element in this one, so I'm searching for that element here.

90
00:06:02,630 --> 00:06:07,670
How much time it takes to search for any element here, like five am searching for the stick and buying.

91
00:06:09,650 --> 00:06:12,270
Then search for 10, also search for four.

92
00:06:12,290 --> 00:06:15,230
Also, how much time it takes for all and.

93
00:06:15,920 --> 00:06:23,030
So this is aiming to end or end into and then take the next 10.

94
00:06:23,240 --> 00:06:23,690
It there.

95
00:06:23,720 --> 00:06:27,140
It's not bad move to next fall, is it there?

96
00:06:27,140 --> 00:06:31,370
If it's there before then move to next fix.

97
00:06:31,370 --> 00:06:32,100
Is it there.

98
00:06:32,120 --> 00:06:34,100
It's not that bone cockpit.

99
00:06:35,120 --> 00:06:42,890
So why are you copying the elements from A into C check if they are also present in B or not?

100
00:06:43,400 --> 00:06:44,030
That said.

101
00:06:44,820 --> 00:06:52,700
So every element I have to search here and then elements and I have to search all elements of in Glenn,

102
00:06:52,950 --> 00:06:58,640
so if we write it in single variable, then it is an intrusion and it is outdraw and square.

103
00:07:00,090 --> 00:07:01,860
So it is also time consuming.

104
00:07:02,190 --> 00:07:05,190
Now let us see how they perform intersection if they are already sorted.

105
00:07:05,550 --> 00:07:13,590
So here also we use the same procedure of merging so far, merging let us start indices from zero of

106
00:07:13,590 --> 00:07:14,390
each list.

107
00:07:14,400 --> 00:07:22,030
That is in an emerging Wiecek, which is a smaller AOF or B of which is smaller.

108
00:07:22,050 --> 00:07:22,920
This is smaller.

109
00:07:23,400 --> 00:07:24,320
Don't copy it.

110
00:07:24,330 --> 00:07:27,340
We don't want that element moved next.

111
00:07:28,530 --> 00:07:32,100
Then compare ÁLFHEIÐUR to be of J that smaller.

112
00:07:32,280 --> 00:07:33,180
Don't pop it.

113
00:07:33,790 --> 00:07:34,680
Move next.

114
00:07:35,820 --> 00:07:38,960
No comparing your favorite biology but are equal.

115
00:07:39,270 --> 00:07:40,680
We want that element.

116
00:07:41,040 --> 00:07:45,950
So copy that element four move I as the last move g and move K.

117
00:07:47,520 --> 00:07:49,980
This is not merging but similar to merging.

118
00:07:50,340 --> 00:07:52,020
So we are using that procedure.

119
00:07:53,080 --> 00:07:55,770
The company of hybrid biology, they are the same.

120
00:07:56,250 --> 00:08:01,650
So copy that five move I and move G and move K.

121
00:08:04,060 --> 00:08:12,610
Then compare your favorite biology six, a smaller move next door, cooperate, then Beyoglu, smaller

122
00:08:12,610 --> 00:08:13,380
move next.

123
00:08:14,200 --> 00:08:18,550
Then if I use a smaller move next, finished.

124
00:08:20,300 --> 00:08:28,100
That full stop don't have to copy anything, so the procedure is as much procedure and the time as paid

125
00:08:28,100 --> 00:08:31,710
off and plus and in a single variable it is.

126
00:08:31,730 --> 00:08:38,630
And so in unison, if you remember, we were copying all the elements and duplicates one time, but

127
00:08:38,630 --> 00:08:44,179
in intersection we are copying only duplicates, not the next operation is finding difference.

128
00:08:44,600 --> 00:08:46,700
Next operation is a subtle difference.

129
00:08:46,700 --> 00:08:47,950
That is a difference operation.

130
00:08:48,470 --> 00:08:53,780
We have two sets, then we say minus B, that is subtraction of two sets.

131
00:08:54,170 --> 00:09:02,540
A minus B means we want all those elements of A which are not there, and B so subtract the common elements

132
00:09:02,930 --> 00:09:09,140
between A and B and take only those elements which are only in A, not in B at all.

133
00:09:09,530 --> 00:09:11,840
So this is a different operation.

134
00:09:12,110 --> 00:09:13,670
How to get those elements.

135
00:09:14,210 --> 00:09:15,470
So what are those elements?

136
00:09:15,470 --> 00:09:22,310
Letter C C three is not present in B threes and the result five is that one take it then it's not there.

137
00:09:22,310 --> 00:09:24,910
Take it forward as they don't take it six.

138
00:09:24,920 --> 00:09:26,320
So three to nine six.

139
00:09:26,330 --> 00:09:32,530
These are the three elements that comes after A minus B, so how to perform this operation.

140
00:09:32,900 --> 00:09:36,730
So we have to copy all the elements of a body copying every element.

141
00:09:36,980 --> 00:09:37,970
Is it present here?

142
00:09:38,120 --> 00:09:39,200
Three is not brilliant.

143
00:09:39,200 --> 00:09:39,770
Copy it.

144
00:09:40,400 --> 00:09:41,960
Five is present here.

145
00:09:42,200 --> 00:09:43,550
Don't take it then.

146
00:09:43,850 --> 00:09:47,140
It's not been it for it's there.

147
00:09:47,240 --> 00:09:47,960
Don't take it.

148
00:09:48,080 --> 00:09:49,620
Six is not there.

149
00:09:49,760 --> 00:09:50,290
Take it.

150
00:09:50,900 --> 00:09:56,900
So every element of is a compared with all the elements of B and if it is not there then we are copying

151
00:09:56,900 --> 00:09:57,050
it.

152
00:09:57,050 --> 00:09:58,280
So we are searching here.

153
00:09:58,760 --> 00:10:03,830
So M elements are copied and every time they are search in an element.

154
00:10:03,830 --> 00:10:05,270
So this is Aminta.

155
00:10:05,270 --> 00:10:09,440
And so in a single variable, this is an intrusion and this is in square.

156
00:10:09,710 --> 00:10:15,290
So the Times and Square now how it has to be done in sorted sets.

157
00:10:15,290 --> 00:10:17,990
If the list are sorted then how to do that.

158
00:10:17,990 --> 00:10:18,560
Let us see.

159
00:10:19,070 --> 00:10:25,040
We can again use the merge procedure so we will take in this IGY and key.

160
00:10:27,130 --> 00:10:32,800
Now we will learn, now we will run the process similar to merging, let us start.

161
00:10:34,540 --> 00:10:43,960
Álfheiður, Bhaiyyaji, this is Swon, so we don't want to eliminate from being on Motörhead, compare

162
00:10:43,990 --> 00:10:47,820
your favorite biology trees, the small Kopit then move ahead.

163
00:10:48,610 --> 00:10:51,870
They are saying don't copy anything, just move ahead.

164
00:10:52,570 --> 00:10:55,420
They are saying don't copy anything, just move ahead.

165
00:10:56,380 --> 00:11:00,280
That small kopit move ahead then.

166
00:11:00,280 --> 00:11:04,810
This is one move ahead then that is small kopit move ahead.

167
00:11:05,350 --> 00:11:07,990
Fossilised has finish so stop.

168
00:11:08,880 --> 00:11:15,720
We are not copying from B. We are copying only from A and we are running the same procedure just like

169
00:11:15,720 --> 00:11:24,030
much so the time is endless and Tetum and in single variable it is N and there is one more last operation

170
00:11:24,030 --> 00:11:28,710
set membership operation that is to know whether an element belongs to a set or not.

171
00:11:29,070 --> 00:11:31,400
Like 10 belongs to set or not.

172
00:11:31,950 --> 00:11:34,480
So search and find out if it's there.

173
00:11:35,220 --> 00:11:38,310
So said membership the same as searchingly.

174
00:11:39,390 --> 00:11:42,690
So that's all we have learned about set operations.

175
00:11:42,690 --> 00:11:44,670
Mostly they are binary operations.

176
00:11:46,760 --> 00:11:50,600
So that's all about operations on A.I., that is ADT.

177
00:11:51,260 --> 00:11:58,430
So that's all we have seen various operations on an array that is on a single tree as well as Binay

178
00:11:58,430 --> 00:11:59,060
operations.

179
00:11:59,090 --> 00:12:01,460
That is more than one area that we have seen.

