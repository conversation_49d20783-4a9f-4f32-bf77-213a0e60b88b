1
00:00:00,420 --> 00:00:03,400
We look at deleting from binary search tree.

2
00:00:03,630 --> 00:00:10,110
So here is an example, binary is so strange to see how to delete any key, so egregious examples can

3
00:00:10,110 --> 00:00:10,540
show you.

4
00:00:10,860 --> 00:00:13,290
So first, <PERSON>, I want to delete this.

5
00:00:13,890 --> 00:00:14,820
Thirty five.

6
00:00:15,950 --> 00:00:17,230
So what is the procedure?

7
00:00:18,630 --> 00:00:24,750
So the procedure is search for the key, if found, then deleted that simple.

8
00:00:26,040 --> 00:00:31,020
So for twenty five, start from here, twenty five smaller GooYa, twenty five is greater than this

9
00:00:31,020 --> 00:00:37,500
Google right is found, then delete this note if you believe this Naude and INOR, then you have to

10
00:00:37,500 --> 00:00:40,380
make it apparent to link as another type.

11
00:00:40,650 --> 00:00:43,650
If it is linked representation, we have to make this as null.

12
00:00:44,190 --> 00:00:46,910
Yes, we can make it as note and this note is deleted.

13
00:00:47,520 --> 00:00:48,570
So this was simple.

14
00:00:49,580 --> 00:00:51,270
And put back to the new realities.

15
00:00:51,980 --> 00:00:58,100
Now, let me take another example, I want to delete a key for the three search for 43.

16
00:00:58,100 --> 00:01:03,940
43 is greater than this and greater than this one, less than discount to left and greater than just

17
00:01:03,960 --> 00:01:04,660
your phone.

18
00:01:05,000 --> 00:01:08,350
So delete the note then make this right.

19
00:01:08,450 --> 00:01:13,520
I guess none that said so not just delete it and put it back.

20
00:01:14,500 --> 00:01:16,480
So these were simple examples.

21
00:01:17,230 --> 00:01:24,430
Next, I want to delete a key for you, search for 42, 42 is the greater, greater and the smaller

22
00:01:24,430 --> 00:01:24,940
than this one.

23
00:01:24,940 --> 00:01:25,660
Yes, found.

24
00:01:27,000 --> 00:01:35,070
Now, the problem is that what is that if I delete 42, then I have to modify the link of parent, but

25
00:01:35,400 --> 00:01:41,180
42 is also having a child, then, OK, it is having just one child.

26
00:01:41,190 --> 00:01:41,850
No problem.

27
00:01:42,120 --> 00:01:43,940
It's a child will take its position.

28
00:01:43,950 --> 00:01:50,400
So if what you do goes away from here, then this will be pointing towards China that this becomes far

29
00:01:50,670 --> 00:01:51,000
left.

30
00:01:51,000 --> 00:01:51,370
China.

31
00:01:51,400 --> 00:01:52,230
Forty five years old.

32
00:01:52,380 --> 00:01:58,080
We saw one more thing now that if the north is getting deleted and if it is having one child, then

33
00:01:58,080 --> 00:01:59,770
that child will take its place.

34
00:02:00,030 --> 00:02:04,860
So again, back to the same tree for the procedure we have learned so far is if you want to delete,

35
00:02:04,950 --> 00:02:05,940
search for the key.

36
00:02:06,450 --> 00:02:09,150
If you're found, then delete the key.

37
00:02:09,810 --> 00:02:12,030
If it is not having any children, no problem.

38
00:02:12,030 --> 00:02:15,690
If it is having one child, then let the child take its place.

39
00:02:15,970 --> 00:02:19,110
Now what if I want to delete key?

40
00:02:20,770 --> 00:02:23,470
Thirty, I want to delete this.

41
00:02:24,980 --> 00:02:25,340
That.

42
00:02:26,370 --> 00:02:30,960
After the election, who will take its place, you may be seeing 20 then who will take the place of

43
00:02:30,960 --> 00:02:34,020
twenty twenty five or ten anyone?

44
00:02:35,390 --> 00:02:38,190
Or else I say that no, no, 40 will take its place.

45
00:02:38,210 --> 00:02:44,240
Then who will take the place of 40 45 who will take the place of forty for so many changes we have to

46
00:02:44,240 --> 00:02:46,940
make instead of going to see the other is?

47
00:02:48,020 --> 00:02:57,560
For Turkey, find out in order if you perform in order which the comes before to Turkey so qualify comfortably

48
00:02:57,560 --> 00:02:58,170
before Turkey.

49
00:02:58,910 --> 00:03:00,560
This is in order.

50
00:03:01,620 --> 00:03:02,430
Predecessor.

51
00:03:02,760 --> 00:03:08,600
And who is in order, a successor of Turkey, who comes after 30 in order traversal of the street,

52
00:03:08,820 --> 00:03:09,360
this one.

53
00:03:09,540 --> 00:03:12,620
So this is in order successor.

54
00:03:12,690 --> 00:03:14,060
There is not a predecessor.

55
00:03:14,070 --> 00:03:15,870
There is another successor.

56
00:03:15,900 --> 00:03:20,570
So for Naude, this is in order, but this is in order successor.

57
00:03:21,030 --> 00:03:27,630
If you are the leading today, today's going out, then either in order for this you will take its place

58
00:03:27,900 --> 00:03:29,970
or in order six or seven decades.

59
00:03:30,330 --> 00:03:36,450
So in order for this to system, maybe a leaf and in order successor, we assume it is a leaf.

60
00:03:36,480 --> 00:03:37,500
Yes, they are leaf.

61
00:03:37,770 --> 00:03:42,330
So leaf node will come into this place so we don't have to disturb so many nodes.

62
00:03:42,600 --> 00:03:48,220
So the result of this tree can be 20 times the root and branch done on the site.

63
00:03:48,900 --> 00:03:50,550
Forty, thirty five.

64
00:03:51,000 --> 00:03:54,510
Forty five, forty two and forty three.

65
00:03:54,750 --> 00:03:55,770
It can be like this.

66
00:03:55,770 --> 00:03:59,610
So twenty four is in the root or it can be terrifying.

67
00:03:59,610 --> 00:04:04,490
The root codifies in the root either twenty five in the root or defendable.

68
00:04:04,830 --> 00:04:10,460
See here from here the node is the leader and here from here the note is deleted.

69
00:04:10,500 --> 00:04:12,300
So this is the actual method.

70
00:04:13,020 --> 00:04:18,920
Then you are deleting any more than who should take its place in order or in order.

71
00:04:18,930 --> 00:04:21,029
Successor should take its place.

72
00:04:21,480 --> 00:04:21,829
Right.

73
00:04:22,260 --> 00:04:30,150
So how to find in order predecessor or successor for in order predecessor for deleting a node go to

74
00:04:30,150 --> 00:04:32,190
its left subtree.

75
00:04:32,190 --> 00:04:34,650
Left that right, right, right.

76
00:04:34,650 --> 00:04:43,740
So try to maushart child of left subtlely in order a successor is left mauls a child of right subtree

77
00:04:43,740 --> 00:04:45,030
so you can easily find in order.

78
00:04:45,030 --> 00:04:46,350
But this is not a successor.

79
00:04:46,380 --> 00:04:48,360
You don't have to perform in order traversal.

80
00:04:48,750 --> 00:04:50,520
You can go to less than.

81
00:04:50,550 --> 00:04:51,180
Right, right.

82
00:04:51,180 --> 00:04:51,630
Right, right.

83
00:04:51,630 --> 00:04:55,500
Until you find the last note or else go go right.

84
00:04:55,500 --> 00:04:58,590
Separate and left, left, left, left until you get the.

85
00:05:00,030 --> 00:05:00,720
Last month.

86
00:05:01,900 --> 00:05:05,990
Then so that's all you can find in order for this sort of successor.

87
00:05:06,550 --> 00:05:11,810
Now, one more question, women, shall I take in order for this or shall I take another successor?

88
00:05:12,340 --> 00:05:13,360
You can take any of them.

89
00:05:13,750 --> 00:05:14,800
You can take any of them.

90
00:05:15,520 --> 00:05:16,440
You see a choice.

91
00:05:16,780 --> 00:05:20,540
Suppose you want to say that no right hand side is having more elements.

92
00:05:20,560 --> 00:05:21,830
So from there, I will bring.

93
00:05:22,100 --> 00:05:27,730
OK, so you can count the number of nodes here and you can down the notes here.

94
00:05:28,120 --> 00:05:34,060
Whoever whichever side is having more number of notes from there, you can get it or else you say load

95
00:05:34,090 --> 00:05:38,380
the height of this right up to this portion is larger than this one.

96
00:05:38,590 --> 00:05:44,070
Then if you want to take this to find out how you can select that one so otherwise randomly you can

97
00:05:44,080 --> 00:05:44,950
select anyone.

98
00:05:45,250 --> 00:05:50,410
So if you have seen various examples and when we took 30, then we understood that we have to replace

99
00:05:50,410 --> 00:05:51,080
it with an order.

100
00:05:51,100 --> 00:05:52,200
But this is all in order.

101
00:05:52,210 --> 00:05:55,520
Successor then what about twenty five and twenty five.

102
00:05:55,870 --> 00:06:00,420
There was no way this is the successor so directly related deleted the norm.

103
00:06:00,850 --> 00:06:03,370
Then what happened when I was deleting forty three.

104
00:06:03,700 --> 00:06:04,360
Forty three.

105
00:06:04,600 --> 00:06:07,390
There was no predecessor's successor so directly I deleted it.

106
00:06:08,140 --> 00:06:08,920
What happened then.

107
00:06:08,920 --> 00:06:10,030
Forty two was deleted.

108
00:06:10,300 --> 00:06:11,530
Well forty two was deleted.

109
00:06:11,800 --> 00:06:16,210
There was only one child only successor so it has taken its place.

110
00:06:16,360 --> 00:06:18,420
So actually I was following scene Murtada.

111
00:06:18,940 --> 00:06:19,280
Right.

112
00:06:19,300 --> 00:06:25,000
Always we should follow Symantec, not remove this and I'll show you one more example.

113
00:06:25,480 --> 00:06:27,460
Suppose I want to delete Fati.

114
00:06:29,020 --> 00:06:35,140
Then after reading 40, who will take his place in order predecessor, who is in order predecessor this

115
00:06:35,140 --> 00:06:36,730
one see the notes below 40.

116
00:06:36,760 --> 00:06:38,990
Only then who is in a successor?

117
00:06:39,090 --> 00:06:39,610
This one.

118
00:06:40,160 --> 00:06:47,440
This is not a predecessor in order of success, I suppose after deleting 40, I want to bring a successor

119
00:06:47,440 --> 00:06:47,800
here.

120
00:06:48,900 --> 00:06:54,830
Forty two will come here, this 42 will come here, but 42 is still having some child.

121
00:06:55,170 --> 00:07:01,650
It is not a leaf nor it's not a leaf, nor then I should fill up despoliation also.

122
00:07:01,920 --> 00:07:04,320
But it's in order for this a successor.

123
00:07:04,350 --> 00:07:06,710
So there's only one node that will come in this place.

124
00:07:06,930 --> 00:07:07,980
So listen carefully.

125
00:07:08,900 --> 00:07:15,530
We were taking in order, but this is a real success because we wanted to avoid multiple modifications

126
00:07:15,530 --> 00:07:16,130
in the three.

127
00:07:17,340 --> 00:07:21,830
But this is the example where we are taking in order success.

128
00:07:22,060 --> 00:07:24,240
We have to make multiple modifications.

129
00:07:24,570 --> 00:07:25,320
This is gone.

130
00:07:25,320 --> 00:07:28,050
42 has taken its place, then 43 has to come here.

131
00:07:28,650 --> 00:07:36,450
So if the in order for this is successor is not a leaf node, then you may have to make multiple modifications

132
00:07:37,260 --> 00:07:37,860
so quickly.

133
00:07:37,860 --> 00:07:39,270
I will show you one more example.

134
00:07:39,930 --> 00:07:41,670
Suppose this is a binary stream.

135
00:07:43,530 --> 00:07:51,560
I want to do it and then there is no predecessor, successor as there, who is in order successor grindy,

136
00:07:51,930 --> 00:07:55,710
so be sure to go there in place of 10, then this will be vacant.

137
00:07:55,860 --> 00:08:01,510
So this is not a leaf nor the who will take its place today is in order a successor of this one.

138
00:08:01,530 --> 00:08:03,180
So today should come in place again.

139
00:08:03,180 --> 00:08:03,850
This is vacant.

140
00:08:04,200 --> 00:08:06,110
This is not at least not against having a child.

141
00:08:06,120 --> 00:08:07,310
So somebody should come here.

142
00:08:08,330 --> 00:08:14,290
So in this example, we have to make more than one modifications, if you are deleting some more energy

143
00:08:14,570 --> 00:08:17,700
analysis, what is the timetable for deletion?

144
00:08:18,080 --> 00:08:19,710
It depends on the height of the tree.

145
00:08:20,570 --> 00:08:22,480
We are searching and modifying it.

146
00:08:22,850 --> 00:08:24,760
So it depends on the height of a tree.

147
00:08:25,010 --> 00:08:27,010
We say height is Logan, right?

148
00:08:27,140 --> 00:08:33,000
We are assuming that it is log and then after deleting in order, how many modifications required if

149
00:08:33,169 --> 00:08:37,049
this is so successful, is the leaf more than just one modification?

150
00:08:37,429 --> 00:08:40,309
Otherwise, there may be more than one modifications.

151
00:08:40,549 --> 00:08:41,679
How many at the most?

152
00:08:41,690 --> 00:08:44,870
That depends on the height again, so that depends on the height.

153
00:08:44,870 --> 00:08:47,040
In this example you can see that depends on the height.

154
00:08:47,270 --> 00:08:50,900
So a number of modifications are also log-in.

155
00:08:52,330 --> 00:08:57,670
Time is logon, and the number of modifications may be maximum login, so that's all.

156
00:08:58,120 --> 00:08:59,840
Let us summarize all the things.

157
00:08:59,840 --> 00:09:02,450
Then once again, I will show you what all we have done.

158
00:09:02,590 --> 00:09:06,810
Let us look at these cases, see if you are deleting 10.

159
00:09:07,570 --> 00:09:09,460
So there is nothing below 10 there.

160
00:09:09,580 --> 00:09:09,910
A leaf.

161
00:09:09,910 --> 00:09:11,580
Not simply delete an.

162
00:09:12,760 --> 00:09:19,000
Second one, if you are deleting 40, then 40 is having one child, so let that child take its place

163
00:09:19,570 --> 00:09:20,500
and delete 40.

164
00:09:21,660 --> 00:09:27,900
Then third, if you are living today and there are bored left subtree as well, that's right subtree.

165
00:09:29,120 --> 00:09:34,480
Right, then left in order to go there or in order successful, go to its place.

166
00:09:35,600 --> 00:09:41,070
Now to one, if you are deleting an order and if you want in order successor, then this will go in

167
00:09:41,090 --> 00:09:42,110
its place again.

168
00:09:42,110 --> 00:09:44,630
If it is having children, then it's in order.

169
00:09:44,640 --> 00:09:48,530
But his successor will come to its place more than one modifications required.

170
00:09:48,560 --> 00:09:55,270
Well, actually, there is only one network, this one, when you delete bringing order for a successor,

171
00:09:55,290 --> 00:09:57,740
anyone, it's not there.

172
00:09:57,740 --> 00:09:59,320
Then these are the two cases.

173
00:09:59,960 --> 00:10:05,020
And if it is also having its children then modified, that calls that SoloPower deletion.

174
00:10:05,030 --> 00:10:06,830
We will look at the program, how to delete.

