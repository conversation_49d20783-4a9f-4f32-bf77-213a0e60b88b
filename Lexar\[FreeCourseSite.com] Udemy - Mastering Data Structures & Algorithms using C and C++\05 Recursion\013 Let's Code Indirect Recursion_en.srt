1
00:00:00,990 --> 00:00:07,260
In this video, we will write a program for indirect regulation that we have already discussed in previous

2
00:00:07,260 --> 00:00:14,880
video, so we will have a demonstration of that in that agriculture and so forth, that agriculture

3
00:00:14,880 --> 00:00:16,410
and I will create a project.

4
00:00:18,020 --> 00:00:24,290
But this time, I'll give the project nightmares, recursion, all of the demos I will do in the same

5
00:00:24,290 --> 00:00:26,870
project, I will not be creating project every time.

6
00:00:27,140 --> 00:00:32,030
I will just delete and write on the fresh code, but the project will remain the same.

7
00:00:32,060 --> 00:00:38,930
So the name I have given as recursion already, you have seen how to create a project, so I will utilize

8
00:00:38,930 --> 00:00:44,350
the same project for all other demos, create a project for recursion.

9
00:00:45,530 --> 00:00:46,790
Here is the main function.

10
00:00:48,110 --> 00:00:49,160
I'll remove this.

11
00:00:50,810 --> 00:00:53,120
So here I will write on two functions.

12
00:00:58,180 --> 00:01:00,790
Function, a wish to experiment and.

13
00:01:06,350 --> 00:01:08,060
If N is greater than zero.

14
00:01:16,140 --> 00:01:23,200
But in the end, Ancol function be for value and minus one.

15
00:01:25,440 --> 00:01:30,960
Next, I should write on function B, function B, it takes parameter and.

16
00:01:33,330 --> 00:01:38,820
If anything greater than one, then predictive value often.

17
00:01:43,100 --> 00:01:48,350
And then call function a buck passing and buy two.

18
00:01:51,050 --> 00:01:55,430
Now here I'm getting an error, saying that the function B is not defined.

19
00:01:57,000 --> 00:02:00,170
Before it is used, it is used here in Function A..

20
00:02:00,510 --> 00:02:06,330
So if you read the code from here line by line, then first we are coming across A call to function

21
00:02:06,330 --> 00:02:12,870
B, then later on we are having a definition of function B, so this function B should be defined before

22
00:02:12,870 --> 00:02:13,500
it is used.

23
00:02:13,560 --> 00:02:16,290
So here I will write on the prototype of function B.

24
00:02:24,390 --> 00:02:30,130
So it is gone, so before using a function, it must be either declared or defined.

25
00:02:30,150 --> 00:02:31,850
So definition we have written here.

26
00:02:32,490 --> 00:02:36,600
So that's why we have to write declaration so that we can call it a display.

27
00:02:37,290 --> 00:02:41,430
Now, these functions, we have called them by passing value 20.

28
00:02:44,580 --> 00:02:47,460
Let us on the program and see what output we get.

29
00:02:51,630 --> 00:02:57,310
If you remember, the output output was 20 in 1998 for three and one.

30
00:02:58,320 --> 00:02:59,980
So we have already seen this working.

31
00:03:00,420 --> 00:03:01,660
I have explained you everything.

32
00:03:01,700 --> 00:03:05,670
So just for the demonstration, I have it on the program, so I got the result.

33
00:03:06,750 --> 00:03:07,770
So that's all of it.

34
00:03:08,040 --> 00:03:09,150
Indirect recursion.

