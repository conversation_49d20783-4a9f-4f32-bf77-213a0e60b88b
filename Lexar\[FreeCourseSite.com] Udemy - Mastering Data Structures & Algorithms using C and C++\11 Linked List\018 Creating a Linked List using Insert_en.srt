1
00:00:00,420 --> 00:00:03,090
Let us create a link with <PERSON><PERSON> as null.

2
00:00:03,270 --> 00:00:06,090
There is nothing, so I want to insert for Snorter.

3
00:00:06,420 --> 00:00:13,380
So say insert, I want to insert it again, insert only at index zero because there are no nodes in

4
00:00:13,380 --> 00:00:13,950
Eliquis.

5
00:00:13,980 --> 00:00:18,090
So the first round of that will be inserted in the link will be eight and there's none.

6
00:00:18,660 --> 00:00:23,190
So this function insert function will create a node like this the next.

7
00:00:23,700 --> 00:00:25,240
I will insert one more.

8
00:00:25,560 --> 00:00:26,540
Now what are the options?

9
00:00:26,550 --> 00:00:27,870
I have two options.

10
00:00:27,870 --> 00:00:29,940
I can say zero in order will be inserted here.

11
00:00:29,940 --> 00:00:36,010
Wonderments inserted here because this is one node I can insert at zero, also one on some final insert

12
00:00:36,010 --> 00:00:38,330
at one, index one and the value is three.

13
00:00:38,520 --> 00:00:43,890
So a new law three will be inserted here and this will be linked with this one.

14
00:00:44,280 --> 00:00:49,290
Now I have two nodes, so I have three places to insert zero one and two.

15
00:00:49,560 --> 00:00:55,410
So I didn't start a new node six at index zero one two.

16
00:00:55,410 --> 00:00:56,730
So I went inside two.

17
00:00:57,000 --> 00:00:59,100
So six will be inserted here.

18
00:01:01,750 --> 00:01:10,420
This is not not an insult or a. insult at index zero and insert five, so this five will be inserted

19
00:01:10,420 --> 00:01:13,510
here, literally pointing on this one.

20
00:01:13,540 --> 00:01:14,980
So first, we'll move on to this.

21
00:01:14,990 --> 00:01:15,610
No, no.

22
00:01:17,820 --> 00:01:24,150
So so far, us finding here not this big game for Snowden, this is second and third and fourth.

23
00:01:25,230 --> 00:01:35,130
Now, what are the possible illnesses, zero one two three four nine insert a new element and index

24
00:01:35,190 --> 00:01:36,720
three value nine.

25
00:01:36,990 --> 00:01:40,810
So it means I want to insert here so nine will be inserted here.

26
00:01:41,250 --> 00:01:44,250
This one point on this one and this one point here.

27
00:01:44,820 --> 00:01:50,420
So by using this function, we can create a link list of how I have shown you.

28
00:01:50,760 --> 00:01:53,040
You have to mention the first one must be zero.

29
00:01:53,250 --> 00:01:56,790
Then always make sure you are giving valid emesis.

30
00:01:57,150 --> 00:02:02,250
So by using this operation, once we have created a link list, then we can perform all other operations

31
00:02:02,250 --> 00:02:04,350
on a link, just like counting.

32
00:02:04,350 --> 00:02:10,620
The new ones are adding all the element searching, reversing all those things that we can do once we

33
00:02:10,620 --> 00:02:11,470
have a link list.

34
00:02:11,820 --> 00:02:15,170
So this is the basic operation that we need, creating a linguist.

35
00:02:15,750 --> 00:02:18,840
Now I have one more method for creating a linguist.

36
00:02:18,900 --> 00:02:21,140
So first I'll show you how we can create a link.

37
00:02:21,140 --> 00:02:22,680
Lindqvist, then I will write on the.

