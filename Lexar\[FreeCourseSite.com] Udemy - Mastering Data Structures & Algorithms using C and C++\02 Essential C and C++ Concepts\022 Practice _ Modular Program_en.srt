1
00:00:00,330 --> 00:00:06,870
This is a monolithic program which we have seen in the previous demo lecture and everything written

2
00:00:06,870 --> 00:00:09,540
inside main function, so it is monolithic.

3
00:00:09,840 --> 00:00:16,950
Now, if a program is larger, a bigger program, then you will go on adding statement inside same main

4
00:00:16,950 --> 00:00:17,400
function.

5
00:00:17,400 --> 00:00:22,360
So the program becomes very lendee, very vast and difficult to manage.

6
00:00:22,830 --> 00:00:24,870
What does it mean by difficult to manage?

7
00:00:25,200 --> 00:00:28,890
If you are getting over any errors, then it will be difficult to remove them.

8
00:00:29,220 --> 00:00:34,140
And later, if you want to make any changes, then it will take a lot of time for understanding the

9
00:00:34,140 --> 00:00:35,640
program and making changes.

10
00:00:35,640 --> 00:00:39,880
Then what we do break this main function into smaller functions.

11
00:00:40,200 --> 00:00:45,150
So that's what we will make model of programming and also it is called procedural programming.

12
00:00:45,450 --> 00:00:47,350
We will write functions.

13
00:00:47,940 --> 00:00:49,920
So what is the work that we are doing here?

14
00:00:50,310 --> 00:00:52,350
Taking input from the user.

15
00:00:52,740 --> 00:01:00,240
Right, calculating area and perimeter, displaying the result again and showing the result to the user.

16
00:01:00,690 --> 00:01:08,420
So this first part is user interaction and this last part is also user interaction and this is processing.

17
00:01:08,730 --> 00:01:15,060
So you do processing in a separate function, let the user interaction be done by main function itself.

18
00:01:15,420 --> 00:01:17,600
So here I will write on functions.

19
00:01:17,760 --> 00:01:24,750
I will write on function called area, it will find area, but it should know length and breadth, length

20
00:01:24,750 --> 00:01:26,460
and breadth.

21
00:01:26,640 --> 00:01:29,040
Yes, we will pass that as parameter.

22
00:01:29,040 --> 00:01:29,730
No problem.

23
00:01:30,030 --> 00:01:32,610
After finding data, it should return the result.

24
00:01:32,860 --> 00:01:34,620
Certain types integer.

25
00:01:35,280 --> 00:01:39,840
Then inside this function I will do processing.

26
00:01:40,030 --> 00:01:44,940
That is, I will return land into bread.

27
00:01:45,420 --> 00:01:49,800
She no longer has a single line, but it is done by some other function.

28
00:01:50,130 --> 00:01:52,130
Processing is done by some other function.

29
00:01:52,560 --> 00:01:55,080
Similarly, I will find out parameter also.

30
00:01:55,080 --> 00:01:59,430
So parameter is also integer type and the function name is perimeter.

31
00:01:59,700 --> 00:02:04,740
It should also know what is the length and what is the breadth of a rectangle.

32
00:02:04,740 --> 00:02:05,020
Right.

33
00:02:05,040 --> 00:02:06,830
So we will pass that as parameter.

34
00:02:06,840 --> 00:02:15,240
Then we will find out the result of people assigned to into Blende plus Brett.

35
00:02:16,430 --> 00:02:24,530
All right, then return B, I have written in multiple lines, so digging a variable and initializing

36
00:02:24,530 --> 00:02:26,900
that this formula, then returning the result.

37
00:02:26,930 --> 00:02:29,030
OK, that's all I have done here.

38
00:02:29,030 --> 00:02:34,260
I have directly written the result of this formula LINTANG, but that's it.

39
00:02:34,280 --> 00:02:35,510
Now we have two functions.

40
00:02:36,140 --> 00:02:41,720
Now this main function will not be finding the area of a rectangle by itself, but it will be calling

41
00:02:41,720 --> 00:02:46,610
a function area, bypassing the length and breadth.

42
00:02:47,480 --> 00:02:52,700
OK, then I have to change the name of this variable because the function name and variable neomycin

43
00:02:52,970 --> 00:03:00,350
then main function will not be finding the perimeter by itself, but it will be calling function perimeter

44
00:03:00,860 --> 00:03:02,990
bypassing to come.

45
00:03:03,500 --> 00:03:06,550
Brett and the here assignment.

46
00:03:06,580 --> 00:03:09,820
This missing assignment is given not what I should print here.

47
00:03:10,400 --> 00:03:15,100
Adrian Perry, a four area and Perry four perimeter.

48
00:03:15,560 --> 00:03:16,760
This is perimeter.

49
00:03:16,760 --> 00:03:17,870
It is giving some idea.

50
00:03:18,650 --> 00:03:19,010
Yeah.

51
00:03:19,310 --> 00:03:21,050
This E is missing here.

52
00:03:21,080 --> 00:03:21,860
Very meta.

53
00:03:21,860 --> 00:03:22,550
Yes.

54
00:03:23,060 --> 00:03:24,770
No, you can see the program here.

55
00:03:24,890 --> 00:03:31,040
Main function is interacting with the user and taking land and that, and it is sticking the help of

56
00:03:31,050 --> 00:03:35,300
area function to find out Dariya perimeter function to find out the perimeter.

57
00:03:35,600 --> 00:03:41,750
Then it is displaying perimeter, an area same program, but the style of writing a program is different.

58
00:03:42,020 --> 00:03:43,040
So what is the style?

59
00:03:43,310 --> 00:03:44,960
We have written functions.

60
00:03:45,620 --> 00:03:50,840
They are functions for area and perimeter and maintenance using them.

61
00:03:51,140 --> 00:03:56,600
So main itself is not calculatingly restricting the help of area and perimeter functions.

62
00:03:56,780 --> 00:03:57,230
Right.

63
00:03:57,440 --> 00:03:58,880
So you can see the complete program.

64
00:03:58,880 --> 00:03:59,760
No olate.

65
00:04:00,080 --> 00:04:02,330
If I run this I will get the same result.

66
00:04:02,570 --> 00:04:05,080
Lintas ten and Brittas five.

67
00:04:05,110 --> 00:04:07,730
So fifty and thirty same result I'm getting.

68
00:04:07,940 --> 00:04:13,880
So this is a program using function that is modular procedural programming note.

69
00:04:13,880 --> 00:04:19,670
Next thing see here, processing was done and that was multiplied.

70
00:04:19,970 --> 00:04:26,600
But now we have written to the function and here also processing was done in perimeter was found but

71
00:04:26,600 --> 00:04:31,880
now we have redone the function so we have separated the important processing part.

72
00:04:32,180 --> 00:04:33,410
Right operations.

73
00:04:33,740 --> 00:04:35,180
Now what about this lanterne?

74
00:04:35,180 --> 00:04:37,880
But these Landen better together.

75
00:04:38,150 --> 00:04:40,670
They are defining a rectangle.

76
00:04:40,850 --> 00:04:43,730
So why can't we combine these two together?

77
00:04:44,300 --> 00:04:51,150
Yes, we can combine them together and define it as a structure so that Nixa style, we will see it

78
00:04:51,170 --> 00:04:52,130
in the next lecture.

79
00:04:52,130 --> 00:04:52,910
Next video.

80
00:04:53,210 --> 00:04:54,500
We will finish this code.

81
00:04:54,770 --> 00:04:59,720
Then in the next few days I will combine these two Lindenwood, then I will use structures.

82
00:05:00,140 --> 00:05:03,380
So next lecture is on structure and function.

83
00:05:03,710 --> 00:05:04,790
So first you write on this.

84
00:05:04,790 --> 00:05:08,090
Could be ready with that I will modify the same program.

85
00:05:08,090 --> 00:05:09,800
Alright, so that's all in this video.

