1
00:00:00,790 --> 00:00:04,860
The four circular Q I will discuss the basic idea by taking the example.

2
00:00:05,760 --> 00:00:09,130
See, the problem that we are getting here is because we are moving from point.

3
00:00:10,460 --> 00:00:12,980
Instead of shifting the elements, we are moving from point out.

4
00:00:13,010 --> 00:00:16,460
So if you remember the example I took, the people are standing in the queue.

5
00:00:16,490 --> 00:00:17,570
This person is handling a.

6
00:00:18,070 --> 00:00:19,370
So I have not shown that box.

7
00:00:20,310 --> 00:00:25,560
So what we did is if this person is leaving the queue instead of shifting all these people forward,

8
00:00:25,770 --> 00:00:28,580
what we thought, let us move this person ahead.

9
00:00:29,310 --> 00:00:34,470
OK, so in this way, only one step will be taken and the deletion of a person will be done just in

10
00:00:34,470 --> 00:00:35,310
constant time.

11
00:00:35,310 --> 00:00:38,400
Only one step will be taken if one person is going out.

12
00:00:38,420 --> 00:00:40,300
Then again, he will move just one step.

13
00:00:41,070 --> 00:00:42,240
This person will move.

14
00:00:42,240 --> 00:00:43,210
This person is moving.

15
00:00:43,500 --> 00:00:50,160
So in this way, what is happening is so in this method, we are getting a lot of free space here and

16
00:00:50,190 --> 00:00:51,200
this space is full.

17
00:00:51,210 --> 00:00:52,900
There is no space for a new person.

18
00:00:53,190 --> 00:00:56,340
So if a new person wants to join the queue, he cannot change.

19
00:00:56,370 --> 00:00:57,390
There is no space here.

20
00:00:57,960 --> 00:01:05,459
Now, the idea we would ask this person to come and stand here, this is the backside of the counter

21
00:01:05,459 --> 00:01:06,270
of the counter.

22
00:01:06,270 --> 00:01:10,290
This person is the front person counter to the back side, so nobody would stand at the back.

23
00:01:10,590 --> 00:01:17,010
Now, if this person says that once I finish with all these people, when I have finished with the last

24
00:01:17,010 --> 00:01:20,640
person again, I will commence time here again.

25
00:01:20,640 --> 00:01:21,930
I will start from this place.

26
00:01:22,200 --> 00:01:29,800
If you say that I will be using this space circularly, then people can start forming a queue here also.

27
00:01:29,940 --> 00:01:32,420
Yes, that's what Didas is.

28
00:01:32,610 --> 00:01:35,390
The person who is serving, who is handling a counter.

29
00:01:35,580 --> 00:01:40,230
He can come back again here so he can move circularly.

30
00:01:40,440 --> 00:01:45,070
So same thing we want to implement here also who are inserting and deleting front of it.

31
00:01:45,300 --> 00:01:47,700
Let the front entrance move circularly.

32
00:01:47,700 --> 00:01:52,380
Once they have reached six, let them come back again on Siedel then again continue like this.

33
00:01:52,590 --> 00:01:53,670
So this is the idea of.

34
00:01:54,180 --> 00:01:55,740
So this is the idea of circular queue.

35
00:01:55,950 --> 00:01:58,580
I will take a few elements and show you how it is done.

36
00:01:58,830 --> 00:02:01,500
Now let us learn about circular queue from the beginning.

37
00:02:01,980 --> 00:02:06,030
See, initially front and rear will be at zero.

38
00:02:06,240 --> 00:02:11,580
We will start from minus four now we will start from zero will understand why we are starting from zero.

39
00:02:12,990 --> 00:02:16,310
Next using the we will insert using front we will delete.

40
00:02:16,500 --> 00:02:21,950
So I will insert some element of an insert one element so that we will move ahead and instead of under

41
00:02:21,990 --> 00:02:23,980
ten for the procedure the same.

42
00:02:24,790 --> 00:02:25,200
Yes.

43
00:02:25,470 --> 00:02:26,700
What about this location.

44
00:02:26,910 --> 00:02:28,770
That location should be left empty.

45
00:02:28,800 --> 00:02:30,750
We will not use that location.

46
00:02:30,930 --> 00:02:34,100
Wherever front is pointing, that location must be left empty.

47
00:02:34,860 --> 00:02:37,560
Let me fill all the elements until the front reaches here.

48
00:02:37,560 --> 00:02:38,700
I will fill all the elements.

49
00:02:38,940 --> 00:02:45,300
Now, Kurzel, I have heard all the elements radius that size minus one and front is still at zero.

50
00:02:45,480 --> 00:02:48,300
So let me delete fuel.

51
00:02:48,570 --> 00:02:55,380
So first element I will leave for that move to the next location and remove the element so that element

52
00:02:55,380 --> 00:02:55,890
is deleted.

53
00:02:56,100 --> 00:03:00,720
I want to delete one more element, so move on to the next location and delete Ballymun.

54
00:03:00,720 --> 00:03:01,830
So this element is gone.

55
00:03:02,370 --> 00:03:04,020
Now I have three spaces.

56
00:03:05,060 --> 00:03:13,550
I want to insert can I answer, yes, bring here at zero and insert the element here so you can see

57
00:03:13,550 --> 00:03:18,780
that the new element is inserted at the back side of Poynton and you can see that it was a six.

58
00:03:18,800 --> 00:03:19,850
It became Zettl.

59
00:03:20,510 --> 00:03:24,770
And I want to insert one more element this time really will move in the normal fashion.

60
00:03:24,980 --> 00:03:30,060
Plus plus just move one step really will come here and insert the element.

61
00:03:30,320 --> 00:03:33,430
So yes, that is it can insert one more element.

62
00:03:33,920 --> 00:03:35,900
No, there's a freespace.

63
00:03:35,900 --> 00:03:41,580
Can't I use don't use that space very well from this point in that space must be left empty.

64
00:03:42,200 --> 00:03:44,270
What happens if I want to use that space.

65
00:03:44,450 --> 00:03:47,020
So if you bring the rear there then the rear ends run.

66
00:03:47,030 --> 00:03:49,210
Both are equal right in front.

67
00:03:49,220 --> 00:03:50,390
Both are equal, equal amounts.

68
00:03:50,390 --> 00:03:53,750
We know the collision when they are at the same place as empty.

69
00:03:54,230 --> 00:03:54,950
Let me show you.

70
00:03:55,280 --> 00:03:56,000
I will inside.

71
00:03:56,000 --> 00:03:59,930
One more element to not in front is equal to there.

72
00:04:00,380 --> 00:04:03,760
Is it the full or empty C actually the same condition.

73
00:04:04,040 --> 00:04:07,130
So that is the reason we will not use that place.

74
00:04:07,140 --> 00:04:08,420
Very disappointing.

75
00:04:08,750 --> 00:04:13,550
So there is at this place, so is the size of an array is seven how many elements I have stored.

76
00:04:13,550 --> 00:04:15,020
Only six elements have store.

77
00:04:15,290 --> 00:04:17,089
We cannot use all seven pieces.

78
00:04:17,600 --> 00:04:21,430
Then if I am using front then I'll be deleting all the elements.

79
00:04:21,450 --> 00:04:28,550
The Negin front also moves like this, so we will implement circular Q over an array by moving front

80
00:04:28,550 --> 00:04:30,320
and rear circularly.

81
00:04:30,830 --> 00:04:32,600
So array is not circular here.

82
00:04:32,840 --> 00:04:35,000
Front and rear are moving circularly.

83
00:04:35,030 --> 00:04:38,120
So let me draw diagram how we usually represent it.

84
00:04:38,120 --> 00:04:38,650
I'll show you.

85
00:04:39,020 --> 00:04:40,340
So this is all we represent.

86
00:04:40,340 --> 00:04:41,180
A circular cube.

87
00:04:41,780 --> 00:04:49,390
We show Arreaza circular arrays north circular front arm will move circular then how to move front and

88
00:04:49,400 --> 00:04:49,970
rear circular.

89
00:04:50,100 --> 00:04:53,320
We want a mechanism such that they automatically move circular.

90
00:04:53,510 --> 00:04:56,270
So what we want let me show you one front is on two.

91
00:04:56,270 --> 00:04:57,800
If I say next, it should go on three.

92
00:04:57,800 --> 00:04:59,930
The next minute it should go on for next month.

93
00:04:59,930 --> 00:05:00,770
It should go on five.

94
00:05:00,770 --> 00:05:02,060
Next month it should go on six.

95
00:05:02,570 --> 00:05:05,390
Next means it should come on Zettl.

96
00:05:07,470 --> 00:05:13,740
So I'll be moving mics, but after 6:00, it should come on WSDL, so circler behavior or the circle

97
00:05:13,740 --> 00:05:16,610
or values like zero, one, two, three, four, five, six.

98
00:05:16,620 --> 00:05:23,180
Then again, 051, this can be obtained using more operation so we can take the help of more operation.

99
00:05:23,190 --> 00:05:30,030
I will show you let us see how the operation can be used for obtaining circular values.

100
00:05:31,270 --> 00:05:33,820
I'll explain it with the help of just three pointer.

101
00:05:33,850 --> 00:05:39,310
Same thing applies on FrontPoint and also so let us start initially rate is zero for one.

102
00:05:39,310 --> 00:05:42,200
It gives zero plus one more to size.

103
00:05:42,200 --> 00:05:43,340
That size is seven times.

104
00:05:43,370 --> 00:05:44,370
But our example.

105
00:05:44,680 --> 00:05:49,300
So the result of this one is one more size, one more size.

106
00:05:49,300 --> 00:05:50,750
Mormon's remainder.

107
00:05:51,310 --> 00:05:55,930
It means when one is divided by seven so it cannot divide.

108
00:05:55,940 --> 00:05:58,680
So seven zero Zeitels remainder is one.

109
00:05:59,080 --> 00:06:03,250
So that number, when it is smaller than seven, that same thing is the remainder.

110
00:06:03,520 --> 00:06:04,510
So this is one.

111
00:06:05,750 --> 00:06:13,160
So that one is the new value of freedom that makes this one plus one Mod seven, so this is two more

112
00:06:13,160 --> 00:06:13,610
seven.

113
00:06:14,030 --> 00:06:15,850
So the result is only.

114
00:06:16,040 --> 00:06:22,140
So that is the new value of real rate is modified for assigned rear plus one modified sort of modified

115
00:06:22,400 --> 00:06:23,380
rate is two now.

116
00:06:23,540 --> 00:06:27,460
So two plus one more size seven.

117
00:06:27,470 --> 00:06:28,560
So two plus one three.

118
00:06:28,730 --> 00:06:29,710
So this is three.

119
00:06:30,650 --> 00:06:33,770
It gives six from six if we say next.

120
00:06:34,920 --> 00:06:46,100
Six plus one, one size seven seven seven seven, if seven more seven means the seven ones seven remain,

121
00:06:46,140 --> 00:06:48,220
there is zero sodium and there is zero.

122
00:06:48,390 --> 00:06:50,300
So again, Ray is back on zero.

123
00:06:50,580 --> 00:06:55,280
So this statement will give us circler values from zero.

124
00:06:55,290 --> 00:06:58,250
It has started again, reached at zero.

125
00:06:58,410 --> 00:07:01,950
So by using more operator, we will be moving front and rear.

126
00:07:02,880 --> 00:07:09,750
So now the previous example, what we saw in the queue using UTI, we will modify in queue and cooperation

127
00:07:09,750 --> 00:07:14,640
and we will modify the moment of another to point out in that one, it becomes a circular queue.

128
00:07:14,850 --> 00:07:17,950
I'm going to guarantee is always those spaces will be reused.

129
00:07:18,240 --> 00:07:20,510
So this is the best method of implementing a queue.

130
00:07:20,520 --> 00:07:23,070
Using it must be circular.

131
00:07:23,850 --> 00:07:25,790
Let us see NQ in the queue functions.

132
00:07:25,800 --> 00:07:27,300
I invite the function and explain.

133
00:07:28,230 --> 00:07:36,210
Let us see and function how it works for inserting an element and you function takes on queue as a pointer

134
00:07:37,170 --> 00:07:41,610
as the pointer to a same structure which we have already seen, and it is taking a value that could

135
00:07:41,610 --> 00:07:42,280
be inserted.

136
00:07:43,470 --> 00:07:48,350
Then before insertion, I should check whether there is any space or not can insert any value or not

137
00:07:48,810 --> 00:07:50,810
if it is fully corroded entertainment.

138
00:07:50,820 --> 00:07:52,890
So I should check for the condition.

139
00:07:54,090 --> 00:07:56,790
So I should check for Kilfoyle condition.

140
00:07:56,820 --> 00:08:00,260
So what is skillfull condition for explaining Keuchel condition?

141
00:08:00,270 --> 00:08:01,950
I have taken two examples here.

142
00:08:01,980 --> 00:08:03,170
These are the two examples.

143
00:08:03,180 --> 00:08:05,340
So let us look at the second example here.

144
00:08:05,350 --> 00:08:10,800
You can see that is the place where the friend is pointing is left empty and all other places are filled

145
00:08:10,800 --> 00:08:16,890
and it is here Nazzal and this one front is pointing here.

146
00:08:16,890 --> 00:08:20,580
That place is left empty and this portion is full and even this portion is full.

147
00:08:20,880 --> 00:08:23,670
So I'm really standing just at the back of the front.

148
00:08:23,970 --> 00:08:26,340
Next location to there is a front only.

149
00:08:26,640 --> 00:08:27,570
So what about this?

150
00:08:28,050 --> 00:08:33,440
If you see circularly, then what is the next place for Freyr after six comes to zero?

151
00:08:33,690 --> 00:08:35,610
So this is also the same situation.

152
00:08:35,820 --> 00:08:40,270
So next place over here is a front, then the is full.

153
00:08:40,380 --> 00:08:43,260
So here I am checking that same condition over here.

154
00:08:43,289 --> 00:08:49,440
Plus one more the size rear plus one more size as because both are in this structure.

155
00:08:49,440 --> 00:08:58,190
So I have to write cuz rare and kuzu size real plus one more size if it is equal to front.

156
00:08:59,010 --> 00:09:02,440
So I'm not modifying it, just calculating that is an explicit front.

157
00:09:02,460 --> 00:09:04,350
If so then full.

158
00:09:05,430 --> 00:09:10,510
Otherwise, move your how to move the rear is modified by rail.

159
00:09:10,530 --> 00:09:16,920
Plus one more size then at that same place, Q3, Q3 values return.

160
00:09:17,550 --> 00:09:18,240
So that's it.

161
00:09:18,330 --> 00:09:19,410
This is in Q4.

162
00:09:19,710 --> 00:09:21,870
Circular Q using.

163
00:09:23,940 --> 00:09:31,530
Next, we will see did you let us look at dequeue operation dequeue function is taking a cue off same

164
00:09:31,530 --> 00:09:34,130
type structure, which we have already seen earlier.

165
00:09:34,140 --> 00:09:35,360
That is huge structure.

166
00:09:36,150 --> 00:09:41,400
Then I have taken one variable in which we will be deleting the value which condition I should check

167
00:09:41,400 --> 00:09:42,450
for deletion.

168
00:09:42,450 --> 00:09:43,500
Excuse me.

169
00:09:43,530 --> 00:09:45,060
So what is the current condition?

170
00:09:45,090 --> 00:09:46,520
Same front as equals today.

171
00:09:46,860 --> 00:09:52,920
So initially you saw that one for both friend and they were at zero, was empty front as equals to here

172
00:09:53,340 --> 00:09:53,920
was empty.

173
00:09:54,380 --> 00:09:56,160
Here the messages skews empty.

174
00:09:56,580 --> 00:10:01,240
If it is not empty then I can delete the elements for deletion which point that we use front pointer.

175
00:10:01,260 --> 00:10:05,570
So I should move on to the next location and that moment should be circular.

176
00:10:05,580 --> 00:10:08,370
So this is the code for circular movement of front.

177
00:10:08,700 --> 00:10:13,290
I read out front this is modified with the friend plus one mod size.

178
00:10:13,980 --> 00:10:19,890
So Q is coming everywhere because all these are members of a structure, cues from the signals to cues

179
00:10:19,890 --> 00:10:26,520
from plus one module the size then from that place cuz front plays that excuse for and take the value

180
00:10:26,910 --> 00:10:28,580
and then return the value.

181
00:10:28,710 --> 00:10:30,240
So that's all about circular.

182
00:10:30,240 --> 00:10:33,900
Q We have seen and cooperation as well as cooperation.

183
00:10:34,170 --> 00:10:35,030
And one more thing.

184
00:10:35,070 --> 00:10:40,260
Both the functions are having just simple statements, so the time for this function is constant.

185
00:10:41,250 --> 00:10:46,320
So the conclusion is the best method for implementing a Q using Uhry you should make it circular.

186
00:10:46,500 --> 00:10:52,890
Dunolly you can reduce the spaces, otherwise the spaces can be useful only once we cannot reuse.

