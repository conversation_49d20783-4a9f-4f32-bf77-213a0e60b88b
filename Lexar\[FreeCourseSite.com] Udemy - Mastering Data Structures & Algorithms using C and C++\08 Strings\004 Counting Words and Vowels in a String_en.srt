1
00:00:00,750 --> 00:00:07,490
And this year, you will see a few more operations on string, like counting number of words in a sentence,

2
00:00:07,530 --> 00:00:09,330
suppose a string is a single sentence.

3
00:00:09,360 --> 00:00:11,510
We want to know how many words are there.

4
00:00:11,850 --> 00:00:17,520
And also we want to count the number of balls, a number of consonants in a string.

5
00:00:18,540 --> 00:00:24,870
So first, let me show you Vogelsang conference here already I have a string, that is how are you?

6
00:00:25,280 --> 00:00:25,490
Right.

7
00:00:25,770 --> 00:00:26,940
So this is string.

8
00:00:27,070 --> 00:00:28,390
How many more are there?

9
00:00:28,410 --> 00:00:29,480
I want to find out.

10
00:00:29,910 --> 00:00:34,890
See, the models are o e e o.

11
00:00:35,190 --> 00:00:43,230
You saw one, two, three, four, five, five model for their remaining consonants.

12
00:00:43,860 --> 00:00:51,840
So if I draw that string here, so here I have taken a string in the form of an array, not counting

13
00:00:51,840 --> 00:00:52,510
models.

14
00:00:52,680 --> 00:00:55,080
So what are those eight or you.

15
00:00:55,410 --> 00:01:00,480
So you if it is an alphabet, is an IOU then Elizabeth Warren.

16
00:01:00,750 --> 00:01:06,610
So I will take a counter here for and if any alphabet is a model, I will increment a counter so far

17
00:01:06,630 --> 00:01:08,760
that I have to scan for this string.

18
00:01:09,090 --> 00:01:11,190
So already I have some program code here.

19
00:01:11,200 --> 00:01:14,560
So here I will write on the code for counting models literacy.

20
00:01:14,560 --> 00:01:18,060
See, I will take one variable that is vacant.

21
00:01:18,090 --> 00:01:22,290
I will call the test model and let it be eventually zero.

22
00:01:22,900 --> 00:01:29,130
Now while scanning to is starting from zero, the alphabet that is first position to the last position.

23
00:01:29,550 --> 00:01:31,090
I should check if it is a moment.

24
00:01:31,440 --> 00:01:36,570
So how to check whether it is A or E or I or you then.

25
00:01:36,990 --> 00:01:40,680
Also it is uppercase and lowercase.

26
00:01:41,190 --> 00:01:42,780
So I have to write so many conditions.

27
00:01:43,020 --> 00:01:44,340
So I will write a few here.

28
00:01:44,640 --> 00:01:59,700
Like if A of i.e. is equal to lowercase E or if Elfy is equal to lowercase E, same way I should write

29
00:01:59,700 --> 00:02:01,170
for all that.

30
00:02:02,130 --> 00:02:12,750
In between these two conditions I'm using are so be careful of this one, so R is a, r, e or I might

31
00:02:12,990 --> 00:02:20,700
throw up to you, you can write in capitals and for capital letter also I should take it like gabbed

32
00:02:20,850 --> 00:02:26,310
A, R, E, or so on.

33
00:02:26,670 --> 00:02:30,890
So this is for lower cases and upper gives us so many conditions I have to write.

34
00:02:31,200 --> 00:02:37,620
So definitely I have to write all those things so it can be any one of those alphabets, either lowercase

35
00:02:37,620 --> 00:02:38,430
or uppercase.

36
00:02:38,730 --> 00:02:43,790
If this condition is true, then I should take the second plus plus.

37
00:02:44,220 --> 00:02:45,510
So count that.

38
00:02:46,430 --> 00:02:52,250
Alphabeat by incrementing the counter, so that's all so at the end of the follow up, if I bring the

39
00:02:52,250 --> 00:02:56,950
value of recount, I will get the number of Vogels present in the strength.

40
00:02:58,110 --> 00:03:05,430
This is one thing, the next supples, I want to count confidence also that inside the same program,

41
00:03:05,640 --> 00:03:09,460
I can include one more counter that is for confidence.

42
00:03:09,480 --> 00:03:14,910
So second, that is consonance abolitionists, as you then hear.

43
00:03:15,880 --> 00:03:24,490
If it is not available, then it may be a consonant, but it must be within the range of alphabet's.

44
00:03:25,410 --> 00:03:28,590
Otherwise, see if it is not available, right?

45
00:03:28,660 --> 00:03:30,060
This is not available, this is a moment.

46
00:03:30,090 --> 00:03:30,900
This is not a woman.

47
00:03:31,440 --> 00:03:33,020
This is not available.

48
00:03:33,060 --> 00:03:35,540
So spaces will also be counted.

49
00:03:35,850 --> 00:03:38,890
So I should not take any special characters or spaces.

50
00:03:38,910 --> 00:03:40,740
I should take only Alphabet's.

51
00:03:40,740 --> 00:03:43,580
So make sure that it is within the range of Alphabet's.

52
00:03:43,860 --> 00:03:46,660
So here I will continue writing else.

53
00:03:47,520 --> 00:03:47,910
If.

54
00:03:48,980 --> 00:03:57,290
I should make sure that it is within range, so if I should be greater than equal to sixty five and

55
00:03:57,710 --> 00:04:04,020
if I should be less than or equal to 90, right.

56
00:04:04,100 --> 00:04:05,480
It should be within this range.

57
00:04:07,280 --> 00:04:07,970
Ah.

58
00:04:09,090 --> 00:04:13,330
This is for capital letters or it should be in lowercase.

59
00:04:13,560 --> 00:04:17,050
So, again, I should continue here, no space.

60
00:04:17,050 --> 00:04:18,060
So I will write on here.

61
00:04:18,480 --> 00:04:27,930
So if I should be given equal to ninety seven and aof, I should be less than equal to 122, so it should

62
00:04:27,930 --> 00:04:28,880
be 122.

63
00:04:29,280 --> 00:04:35,130
So if it is within this range that is either lowercase or uppercase C already we have check.

64
00:04:35,130 --> 00:04:41,250
It is not among you then other than that all of the characters are within that range only.

65
00:04:41,550 --> 00:04:42,950
So I should check the range.

66
00:04:42,960 --> 00:04:47,380
If so, then I will say second that is consonant count.

67
00:04:48,530 --> 00:04:49,340
Plus, plus.

68
00:04:51,020 --> 00:04:51,440
That.

69
00:04:52,480 --> 00:04:58,510
After the end of the for loop, you know very well that you have to print the value off and that is

70
00:04:58,510 --> 00:05:00,910
what will counter and console and counter.

71
00:05:01,790 --> 00:05:06,050
So this is how you can count vowels and consonants now.

72
00:05:06,080 --> 00:05:13,670
Next, let us see how the common number of words in a string now see how to count words.

73
00:05:13,910 --> 00:05:16,270
See, and this is how many words are there?

74
00:05:16,640 --> 00:05:19,740
One, two, three, three words are there.

75
00:05:20,420 --> 00:05:22,180
So how do you know there are three words?

76
00:05:22,190 --> 00:05:24,320
Because there are two spaces.

77
00:05:24,320 --> 00:05:25,130
One, two.

78
00:05:26,000 --> 00:05:29,820
So it means the spaces will help us identify words.

79
00:05:30,560 --> 00:05:31,990
So what is the matter?

80
00:05:32,210 --> 00:05:37,330
Simply count the spaces as many spaces plus one word for that.

81
00:05:37,730 --> 00:05:42,980
So we know very well how to scan a string and just we have to check for the spaces.

82
00:05:43,190 --> 00:05:46,760
So every word of the code here, this follow will take me through the entire string.

83
00:05:47,000 --> 00:05:56,920
Then every time I check that if eight of I if it is equal to a space every space, then word plus plus.

84
00:05:56,960 --> 00:06:02,270
So already I have taken a variable called word and that word plus plus it will count the number of spaces

85
00:06:02,510 --> 00:06:07,310
and after this for loop I can print print deaf person D.

86
00:06:07,670 --> 00:06:11,280
That is a number of words that we are finding out.

87
00:06:11,300 --> 00:06:18,460
So that should be a word plus one because word has given me a number of spaces actually have contact

88
00:06:18,470 --> 00:06:25,100
spaces, then that plus one will give me a number of words or else if you don't want to add it afterwards,

89
00:06:25,100 --> 00:06:26,900
then you can start from one here.

90
00:06:27,410 --> 00:06:31,610
Now add as many spaces we have that many times this word will increment.

91
00:06:31,640 --> 00:06:33,260
So there are two spaces for good time.

92
00:06:33,260 --> 00:06:34,820
It will increment every one.

93
00:06:34,820 --> 00:06:37,640
We have four total three words there.

94
00:06:38,330 --> 00:06:39,710
Now, one more thing in this one.

95
00:06:40,550 --> 00:06:49,550
If supports more than one species out there, how it is possible, see how species are spazz suppose

96
00:06:49,550 --> 00:06:50,570
this is not their.

97
00:06:51,510 --> 00:06:59,280
The news that so there are three spaces then you so if that you I count it as a word, then total,

98
00:06:59,280 --> 00:07:00,360
how many words are there?

99
00:07:00,750 --> 00:07:02,130
Only three words are there.

100
00:07:02,460 --> 00:07:09,960
But in my program, if I count the spaces one, two, three, four and plus one, I am doing four,

101
00:07:09,960 --> 00:07:11,960
five words out there.

102
00:07:11,970 --> 00:07:12,840
So that is wrong.

103
00:07:13,860 --> 00:07:21,840
Then how to deal with these excess spaces, so when you have a continuous set of spaces, it is actually

104
00:07:21,840 --> 00:07:25,750
called as white space, it's called as white space.

105
00:07:26,070 --> 00:07:28,900
So if there are any white spaces, I should check that also.

106
00:07:29,610 --> 00:07:31,210
So let us see how to do that.

107
00:07:31,620 --> 00:07:33,840
See, this is one space I have contact.

108
00:07:34,110 --> 00:07:37,100
This is one space I have gone the next space.

109
00:07:37,110 --> 00:07:42,540
Don't count it so hard to know that this space should be counted or not counted because the previous

110
00:07:42,540 --> 00:07:45,220
one was also a space right then.

111
00:07:45,250 --> 00:07:46,090
What about this one?

112
00:07:46,320 --> 00:07:48,030
Previous one was the space.

113
00:07:48,030 --> 00:07:48,770
Don't count it.

114
00:07:48,990 --> 00:07:53,020
So whenever you are having a space, check that the previous one is also a space.

115
00:07:53,040 --> 00:07:54,170
If so, then count it.

116
00:07:54,450 --> 00:07:55,800
So let us do it once again.

117
00:07:56,070 --> 00:07:56,850
This is space.

118
00:07:56,850 --> 00:07:58,340
Previous one is not a space.

119
00:07:58,680 --> 00:07:59,460
This is space.

120
00:07:59,460 --> 00:08:01,340
Previous one is not a space account counted.

121
00:08:01,650 --> 00:08:04,830
This one previous for the previous one space don't.

122
00:08:05,370 --> 00:08:14,760
So here, along with this condition, I should check that if I is equal to space and eight of eye minus

123
00:08:14,760 --> 00:08:18,870
one that is previous alphabet should not be a space.

124
00:08:18,870 --> 00:08:19,130
Right.

125
00:08:19,140 --> 00:08:20,710
It's not a space.

126
00:08:21,000 --> 00:08:22,470
If so, then what.

127
00:08:22,470 --> 00:08:22,770
Plus.

128
00:08:22,770 --> 00:08:23,250
Plus.

129
00:08:23,930 --> 00:08:27,830
The extra condition, so it will take care of white also.

130
00:08:28,130 --> 00:08:30,330
That's it then bring the number of wards here.

131
00:08:30,350 --> 00:08:34,570
So this is the procedure for considering which this is also.

132
00:08:34,940 --> 00:08:39,179
So you have seen models, confidence and a number of words in a string.

133
00:08:39,200 --> 00:08:45,250
This is a simple exercise, simplest thing, but I have explained it so you can do this one by yourself.

134
00:08:45,260 --> 00:08:48,650
So you have to write this program as a stalling exercise.

135
00:08:48,650 --> 00:08:49,520
So try it on.

136
00:08:50,180 --> 00:08:51,500
Let's all try this program.

137
00:08:51,830 --> 00:08:55,180
We will see more operations on stream incoming videos.

