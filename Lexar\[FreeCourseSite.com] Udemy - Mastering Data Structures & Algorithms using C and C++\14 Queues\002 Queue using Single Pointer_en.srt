1
00:00:00,180 --> 00:00:06,600
Implementation of QE using, we will learn these things, we will implement a cue using just one single

2
00:00:06,600 --> 00:00:07,110
pointer.

3
00:00:07,380 --> 00:00:08,130
Then I'll explain.

4
00:00:08,200 --> 00:00:09,450
What are the problems in that one?

5
00:00:09,480 --> 00:00:10,380
What are the drawbacks?

6
00:00:10,770 --> 00:00:16,680
Then I'll explain how we can implement it using 2.0 so that are front and rear pointers.

7
00:00:16,830 --> 00:00:22,740
Then I'll explain what are the drawbacks of implementing using <PERSON><PERSON>, even if you are using to Windex.

8
00:00:24,190 --> 00:00:30,310
So let us start with implementation of using single point for implementing it, using Uhry.

9
00:00:30,760 --> 00:00:37,070
I have to take an real of to aside so I have an idea of size seven here, and this is out from zero

10
00:00:37,070 --> 00:00:37,600
to six.

11
00:00:38,320 --> 00:00:39,430
And I need one point.

12
00:00:39,610 --> 00:00:44,650
So I have to point out that is right to the point that initially there is nothing in the queue so disappointing

13
00:00:44,650 --> 00:00:45,700
on minus one.

14
00:00:46,870 --> 00:00:53,320
So right now is empty because our area is equal to minus one, minus one is not the index in and this

15
00:00:53,320 --> 00:00:54,670
is starts from zero onward.

16
00:00:54,700 --> 00:00:59,100
So it is stunning before that location, that is before first location.

17
00:00:59,860 --> 00:01:03,410
So I don't have to point out here, I have just one single point that is relevant.

18
00:01:03,970 --> 00:01:07,240
Let us see what happens when I try to insert and delete.

19
00:01:07,240 --> 00:01:08,610
Just using it here.

20
00:01:09,130 --> 00:01:11,170
Let us first insert a few elements.

21
00:01:11,620 --> 00:01:19,570
If I want to insert any element in the queue, then the procedure is moving to next location.

22
00:01:20,020 --> 00:01:20,860
So I'll just write.

23
00:01:20,860 --> 00:01:27,880
Ah, instead of writing Complete World, where an insurgent element, a new element is inserted, just

24
00:01:27,880 --> 00:01:28,610
move red.

25
00:01:28,630 --> 00:01:31,600
And in a certain element, let me add a few more elements.

26
00:01:32,020 --> 00:01:40,030
Just increment to the next location and in certain element likewise just move to our next location and

27
00:01:40,030 --> 00:01:41,260
in a certain element.

28
00:01:42,010 --> 00:01:43,880
So this is how intuition can be done.

29
00:01:44,590 --> 00:01:46,870
So how much time it is ticking for insertion?

30
00:01:47,130 --> 00:01:48,400
See, the steps are simple.

31
00:01:48,820 --> 00:01:54,120
Move to the next location and write down the value there so the time is constant.

32
00:01:54,430 --> 00:01:58,270
So insert operations taking just constant time.

33
00:01:58,510 --> 00:02:00,580
I will fulfill more values in this one.

34
00:02:01,030 --> 00:02:05,400
I want to delete the element from the Q So which element can be deleted?

35
00:02:05,650 --> 00:02:08,289
So from the Q we can delete only this element.

36
00:02:08,289 --> 00:02:13,900
That is the very first element because it is FIFO first in, first out which came first.

37
00:02:14,110 --> 00:02:16,990
This element came first, so only that element can go.

38
00:02:17,290 --> 00:02:21,820
So for deleting that element, I will just take out that element and assume that it is removed from

39
00:02:21,820 --> 00:02:26,290
the Q So five is here to delete it after deleting the element.

40
00:02:26,290 --> 00:02:27,750
This location is empty.

41
00:02:27,760 --> 00:02:34,900
So if we don't leave blank spaces or we will not have any empty spaces, I have to fill that space.

42
00:02:34,900 --> 00:02:39,080
So for filling that space, I must move all these elements.

43
00:02:39,080 --> 00:02:42,430
So shift all these elements on the left hand side.

44
00:02:43,210 --> 00:02:47,290
So seven comfier 15 and all these comfier and two will be coming here.

45
00:02:47,290 --> 00:02:51,850
So I should also move to a previous location, softer deletion.

46
00:02:51,850 --> 00:02:56,500
When this space got vacant, our actual key was starting from this location.

47
00:02:56,800 --> 00:02:59,090
We should not have blank spaces in an array.

48
00:02:59,410 --> 00:03:02,230
If we have, then we have to check every time.

49
00:03:02,230 --> 00:03:07,090
But there is an element of this is blank, so we have to do some extra work.

50
00:03:07,090 --> 00:03:07,930
That is every time.

51
00:03:07,930 --> 00:03:09,550
Is it blank or element is there.

52
00:03:09,760 --> 00:03:13,390
So we want to avoid blank spaces whenever we are using Urte.

53
00:03:13,750 --> 00:03:19,210
So to occupy that blank space, all these elements should be shifted and for shifting the elements,

54
00:03:19,210 --> 00:03:24,850
how much time it takes for deleting an element order and as many elements out there, you have to shift

55
00:03:24,850 --> 00:03:25,480
all of them.

56
00:03:25,780 --> 00:03:28,330
So delete operation, take order of ending.

57
00:03:28,600 --> 00:03:33,380
So insertion takes order of one time deletion, take order and time.

58
00:03:33,410 --> 00:03:38,770
So this is the draw back and we want to remove this drawback and we want to delete operations should

59
00:03:38,770 --> 00:03:40,360
also take constant time.

60
00:03:42,180 --> 00:03:46,200
So let us see next method of implementation by using two Boynton's.

