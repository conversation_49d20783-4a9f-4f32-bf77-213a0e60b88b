1
00:00:00,240 --> 00:00:06,780
In this video, we'll look at a demonstration of insert function in our class, so all of you have started

2
00:00:06,780 --> 00:00:09,140
a project for the in the previous video.

3
00:00:09,150 --> 00:00:11,600
So I'm using the same project here already.

4
00:00:11,610 --> 00:00:16,320
I have structure defined and all the other functions like create and lend and display functions at every

5
00:00:16,320 --> 00:00:16,590
level.

6
00:00:16,620 --> 00:00:22,350
Now, a new function we will write here about the main function that is insert solidified function,

7
00:00:22,350 --> 00:00:29,850
insert insert will take nought point to the first node and it should take the index where we want to

8
00:00:29,850 --> 00:00:32,040
insert and element that we want to insert.

9
00:00:32,070 --> 00:00:35,190
Now I may require a few variables, so I will declare them.

10
00:00:35,220 --> 00:00:38,880
That is nought point thirty four new node.

11
00:00:39,060 --> 00:00:41,000
Then they need some variable like I.

12
00:00:41,370 --> 00:00:43,620
So if any more variable is required I will declare them.

13
00:00:44,100 --> 00:00:45,760
So this has become a common thing for us.

14
00:00:45,780 --> 00:00:52,650
Now we have written the same function upon to Linkous and it's going to be similar with minor changes.

15
00:00:53,490 --> 00:00:56,910
So first of all, I should check whether the index is valid or not.

16
00:00:56,940 --> 00:01:05,040
So if index is less than zero or if index is greater than the length of a list for length, I should

17
00:01:05,040 --> 00:01:09,360
send first or I can send the also, then we will simply return.

18
00:01:09,390 --> 00:01:10,350
That means we will stop.

19
00:01:10,350 --> 00:01:11,880
The function will not do anything.

20
00:01:11,970 --> 00:01:14,930
If it is valid, then we should check if the index is zero.

21
00:01:14,940 --> 00:01:17,010
If the index is zero, then it's a special case.

22
00:01:17,350 --> 00:01:20,380
We have to insert a new note before first node.

23
00:01:20,700 --> 00:01:26,390
So first of all, I will create a new node using my log function so I already have the code.

24
00:01:26,400 --> 00:01:28,680
So I will copy that code here.

25
00:01:28,710 --> 00:01:36,390
So this will be the new node then I should set the data is data should be set as X and the previous

26
00:01:36,390 --> 00:01:42,870
should be binding upon null because there is no known before this node and these next should be pointing

27
00:01:42,870 --> 00:01:47,850
upon first node because we are inserting it before first node, then first node.

28
00:01:47,970 --> 00:01:53,730
Previous pointer should point on this new node that this team, because this is linked before first

29
00:01:53,730 --> 00:01:56,580
node and Foster should be brought upon P.

30
00:01:57,120 --> 00:01:59,250
Yes, these are the steps already we have seen.

31
00:01:59,250 --> 00:01:59,940
So that's all.

32
00:02:00,030 --> 00:02:06,690
This is the way we can insert a new node at index zero that is before fussin or else we have to insert

33
00:02:06,690 --> 00:02:08,009
it at any other position.

34
00:02:08,280 --> 00:02:14,520
So for inserting at any other position, first of all, first of all, we should move B on to that index

35
00:02:14,520 --> 00:02:23,250
so that I can take a follow and for assign zero is less than index minus one and I plus plus and every

36
00:02:23,250 --> 00:02:26,040
time we should we move on to the next node.

37
00:02:26,040 --> 00:02:33,090
So we will go and stop at a node after which we want insert now create a new node that the function

38
00:02:33,090 --> 00:02:34,280
but a node is created.

39
00:02:34,440 --> 00:02:37,780
I will set the data for this node set data sine X.

40
00:02:37,800 --> 00:02:42,900
Now I have to provide proper links to insert this new node at a given index.

41
00:02:42,930 --> 00:02:51,050
So first thing I will set the previous pointer of this node as E and next pointer of this node is Siemens

42
00:02:51,270 --> 00:02:52,360
is next.

43
00:02:52,500 --> 00:02:55,590
Then I have to make two more changes in the links.

44
00:02:55,920 --> 00:03:02,010
Like if the next note is that if it is not null, then I should meet.

45
00:03:03,390 --> 00:03:07,650
These next previous as dean.

46
00:03:08,860 --> 00:03:16,540
Then I should make these next also point on the so four links are modified and this one of the longest

47
00:03:16,540 --> 00:03:17,390
conditional.

48
00:03:17,920 --> 00:03:20,620
That is if next notice there that only we can do it.

49
00:03:20,620 --> 00:03:23,340
If it is not there, then only Trilling's will be modified.

50
00:03:23,350 --> 00:03:25,370
Other flight fallings will be modified.

51
00:03:25,540 --> 00:03:26,110
That's all.

52
00:03:26,410 --> 00:03:31,300
This code will insert a new node at a given index in a link list.

53
00:03:31,330 --> 00:03:34,210
So already we have a link here created for these elements.

54
00:03:34,220 --> 00:03:41,280
So let us try to insert a new element at some given index so I will try to insert after second.

55
00:03:41,620 --> 00:03:45,360
So I will give index as a tool and the value is twenty five.

56
00:03:45,490 --> 00:03:48,250
So after twenty, twenty five should be inserted.

57
00:03:48,430 --> 00:03:52,750
Yes it is working 10, 20, 25, 30, 40 and 50.

58
00:03:52,840 --> 00:03:56,610
Let us insert this element after index five.

59
00:03:57,010 --> 00:04:00,230
So let us see whether it works after fifty twenty five should be inserted.

60
00:04:00,280 --> 00:04:01,810
Yes it is getting inserted.

61
00:04:02,500 --> 00:04:04,240
Let us use the index to zero.

62
00:04:04,270 --> 00:04:04,530
Yes.

63
00:04:04,570 --> 00:04:05,770
Twenty five in the beginning.

64
00:04:05,770 --> 00:04:07,470
So it's working perfectly.

65
00:04:07,510 --> 00:04:08,260
So that's it.

66
00:04:08,530 --> 00:04:10,120
You have to practice these functions.

67
00:04:10,120 --> 00:04:14,590
This will improve your programming skills and improve your logical thinking.

