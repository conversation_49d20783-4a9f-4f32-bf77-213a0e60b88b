1
00:00:00,470 --> 00:00:04,010
Let us look at some operations on a list.

2
00:00:05,070 --> 00:00:11,490
Displaying a double dealing with this, we will learn how to traverse linguist's the next, we will

3
00:00:11,490 --> 00:00:14,250
also learn how to reverse a double interest.

4
00:00:14,400 --> 00:00:20,940
So first, let me show you the procedure for displaying a linguist, for displaying a linguist.

5
00:00:20,940 --> 00:00:24,150
I should traverse all leave notes for traversing all these modes.

6
00:00:24,150 --> 00:00:29,900
I should start from for Snoad use then for traversing the north.

7
00:00:29,910 --> 00:00:31,340
I should move to next node.

8
00:00:31,350 --> 00:00:38,070
How to move to next naughties next then go to piece and extend these next peaceniks and Uncle B becomes

9
00:00:38,070 --> 00:00:38,540
null.

10
00:00:38,580 --> 00:00:39,730
I can continue this.

11
00:00:40,350 --> 00:00:45,570
So this is the same as linear <PERSON> as there is no change at all because I am moving or lean forward

12
00:00:45,570 --> 00:00:46,170
direction.

13
00:00:47,040 --> 00:00:53,520
If you're not traveling all n but you are moving in between the north as per the requirement then you

14
00:00:53,520 --> 00:00:54,890
can use modelling's.

15
00:00:55,290 --> 00:00:59,000
But suppose you want to scan for the link list, then you have to go to next.

16
00:00:59,030 --> 00:01:05,610
Only so far this lingling with the goal will be exactly the same as the code for single inkless.

17
00:01:05,940 --> 00:01:07,130
So let us see this.

18
00:01:07,140 --> 00:01:10,120
First of all, we have one for snood Pierpoint for snood.

19
00:01:10,880 --> 00:01:12,230
OK then.

20
00:01:12,360 --> 00:01:13,490
Want to bring this one.

21
00:01:13,500 --> 00:01:23,580
So bring F daytimes printed then move to make small move to next node then same we go on repeating this

22
00:01:23,580 --> 00:01:28,050
print and move to make small print and move to make small until you reach nullable.

23
00:01:29,620 --> 00:01:38,320
So this can be done using my loop, while BP's not equal to none, that's all these steps will display

24
00:01:38,320 --> 00:01:39,320
and talentless.

25
00:01:39,760 --> 00:01:42,190
So the court will be same as a single interest.

26
00:01:42,430 --> 00:01:45,640
And for all the operations of the court will be seemingly.

27
00:01:46,780 --> 00:01:53,170
Like finding maximum funding minimum and searching for everything, the same code works here also just

28
00:01:53,170 --> 00:01:54,670
the node structure is a different.

29
00:01:56,980 --> 00:02:00,220
Now, let us see how to reverse Tillinghast.

30
00:02:01,300 --> 00:02:08,530
For reversing a double interest, if I am reversing this morning, if I'm reversing this note, then

31
00:02:08,699 --> 00:02:10,780
the previous should become nine.

32
00:02:11,960 --> 00:02:13,490
And the next should become No.

33
00:02:14,030 --> 00:02:20,290
So I will modify this this should point on nine and this should become null.

34
00:02:22,350 --> 00:02:29,210
So I have swabbed the pointers, interchange, the pointers of six now.

35
00:02:29,280 --> 00:02:35,870
Next was nine, but the previous night on previous and go here, not surprise here again.

36
00:02:37,640 --> 00:02:43,850
So let us continue and reverse a few laws, then interchange the pointers, so see, this is pointing

37
00:02:43,850 --> 00:02:47,840
on six, so they should point on three because this is pointing on three.

38
00:02:47,840 --> 00:02:48,110
Right.

39
00:02:48,140 --> 00:02:52,910
So they should point out three, that this one on three, let it point on six.

40
00:02:53,970 --> 00:02:54,720
Interchanged.

41
00:02:56,090 --> 00:02:57,590
Then move to the snow.

42
00:02:57,620 --> 00:03:05,300
Where is the point of the snow is not in next, it is in previous, so OK, previous and there then

43
00:03:05,300 --> 00:03:06,360
again in catchiness.

44
00:03:07,310 --> 00:03:09,350
So in this way, if find the change on.

45
00:03:11,480 --> 00:03:18,260
So I have to change all of them now if I start from here, I have to say previous to this previous so

46
00:03:18,260 --> 00:03:19,400
if I start from P.

47
00:03:19,580 --> 00:03:22,400
S Nexus, this one, this is next is this one.

48
00:03:23,180 --> 00:03:25,040
So that has become the first node.

49
00:03:25,430 --> 00:03:30,350
So I should move first to that node because that has became the first NORK.

50
00:03:30,980 --> 00:03:35,880
So the procedure is very simple, just a scan for the North and South Duplantis.

51
00:03:36,070 --> 00:03:37,400
So I would vote on the procedure.

52
00:03:38,750 --> 00:03:46,160
B should start from Fassnacht, B should start from first node and I should repeat this procedure until

53
00:03:46,160 --> 00:03:51,950
P becomes null y b and what I should do every time sub.

54
00:03:52,940 --> 00:03:55,330
So force typing I have to take them three pointer.

55
00:03:55,340 --> 00:03:58,030
So this is a temp assigned peaceniks then.

56
00:03:58,040 --> 00:04:01,490
B's next to find B the previous.

57
00:04:04,620 --> 00:04:08,720
And these previous assign them.

58
00:04:10,940 --> 00:04:17,180
After swiping the pointers, I should always move to next mode, so next, not so next has already changed.

59
00:04:17,180 --> 00:04:19,100
So I should take previous note and go there.

60
00:04:19,800 --> 00:04:24,560
So we should move by digging previous more because links are already interchanged.

61
00:04:25,310 --> 00:04:29,600
So this procedure will mean that changing the pointers and it will be reversing the links.

62
00:04:30,080 --> 00:04:30,800
Then one more thing.

63
00:04:30,800 --> 00:04:35,570
I should take that up on the last node first should be pointing up on the last note.

64
00:04:35,870 --> 00:04:38,830
So how do you say it's the last or its nexus null?

65
00:04:39,140 --> 00:04:44,120
So if any node for Nexus null when you have reached on that node first time and for that Nexus null,

66
00:04:44,720 --> 00:04:45,760
that is the last note.

67
00:04:46,040 --> 00:04:47,180
Bring first there.

68
00:04:47,180 --> 00:04:48,730
So I should continuously check that.

69
00:04:48,740 --> 00:04:53,060
Also, I will write on the code here if PS next.

70
00:04:55,340 --> 00:05:00,920
Is equal to none then being forced upon B.

71
00:05:03,790 --> 00:05:04,370
That's it.

72
00:05:04,510 --> 00:05:06,970
So this all goes inside loop only.

73
00:05:08,000 --> 00:05:12,200
So this is the procedure for reversing a DoubleLine plus.

74
00:05:12,860 --> 00:05:18,920
So that's all I have shown only a few operations on the Lincolnesque because the rest of them are similar

75
00:05:18,920 --> 00:05:20,200
to singlehood, interest.

76
00:05:21,480 --> 00:05:26,580
That's all about the operations, so we will see everything inside the program.

