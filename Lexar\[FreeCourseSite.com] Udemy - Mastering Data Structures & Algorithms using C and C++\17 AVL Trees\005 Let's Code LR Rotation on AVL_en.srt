1
00:00:01,770 --> 00:00:04,540
Now, let us implement a law rotation function.

2
00:00:04,560 --> 00:00:07,800
I'll expand this function here, I relied on the required code.

3
00:00:08,830 --> 00:00:09,430
See, for.

4
00:00:11,030 --> 00:00:13,410
Performing an audition, what are the things that we require?

5
00:00:13,440 --> 00:00:14,260
Let us look at it.

6
00:00:14,300 --> 00:00:20,270
I have an example she supports this is a tree and this is B upon which we have to perform a rotation.

7
00:00:20,570 --> 00:00:28,240
So we need this node that is left child and also we need these left child's right chain.

8
00:00:28,490 --> 00:00:29,570
So this is BLR.

9
00:00:31,020 --> 00:00:35,630
And using this thing also, I have to make some necessary changes, what other changes have to do?

10
00:00:35,880 --> 00:00:37,800
So this route will be a lot.

11
00:00:39,350 --> 00:00:46,640
<PERSON><PERSON> will become group, these left, right, right on his left child remains, that is peon.

12
00:00:48,440 --> 00:00:49,730
It will be a lonely.

13
00:00:51,620 --> 00:00:55,220
And child will be compe, right, childhood compi.

14
00:00:56,850 --> 00:01:06,270
Then the left and right shell of a will be given to the right of P and the left shell of being so total

15
00:01:06,480 --> 00:01:08,130
for assignments I have to make.

16
00:01:12,510 --> 00:01:13,830
So let us perform this.

17
00:01:14,810 --> 00:01:19,520
So for this, I will pick three pointers that is struck Naude.

18
00:01:21,720 --> 00:01:24,180
Feel that Espy's left child.

19
00:01:24,450 --> 00:01:26,310
So let us take these live child.

20
00:01:27,640 --> 00:01:28,930
Then struck.

21
00:01:30,800 --> 00:01:31,520
Naude.

22
00:01:34,050 --> 00:01:39,850
A of that is being left child by child, so here Eells, our child, I will take.

23
00:01:40,910 --> 00:01:43,850
And I have to perform necessary changes.

24
00:01:46,200 --> 00:01:49,080
Feels our child should become.

25
00:01:51,180 --> 00:01:53,520
Byelaws left child.

26
00:01:56,030 --> 00:01:56,720
He's.

27
00:01:59,470 --> 00:02:02,500
Every child should become fearless.

28
00:02:04,230 --> 00:02:05,060
Archbold.

29
00:02:06,700 --> 00:02:07,090
Then.

30
00:02:08,789 --> 00:02:12,420
BLR left child should be made as.

31
00:02:15,630 --> 00:02:16,170
Ian.

32
00:02:17,150 --> 00:02:18,670
In-laws, right?

33
00:02:18,710 --> 00:02:28,130
Child should become so little for changes I have done, then let us see whose hide will change.

34
00:02:32,630 --> 00:02:38,690
So this appeals, right, I should calculate and also height, I should find out and then I can also

35
00:02:38,690 --> 00:02:44,600
find out Alars height, so I have to find out the height of appeal.

36
00:02:45,080 --> 00:02:46,640
So Beal's.

37
00:02:48,290 --> 00:02:51,710
Hyde is known height.

38
00:02:52,660 --> 00:02:53,230
Of.

39
00:02:54,480 --> 00:02:55,050
Pian.

40
00:02:56,390 --> 00:02:57,020
Then same.

41
00:03:00,050 --> 00:03:02,030
He's height also, I should find out.

42
00:03:06,980 --> 00:03:08,150
No right of P.

43
00:03:10,970 --> 00:03:13,310
Then they can find out people outside.

44
00:03:20,530 --> 00:03:25,710
That's all all the heads are better than I should report on BLR.

45
00:03:28,350 --> 00:03:31,350
This is a new group, so this I will remove it.

46
00:03:32,880 --> 00:03:39,780
And at last, I should check one more thing, that if B is equal to rudiments, it was actually equal

47
00:03:39,780 --> 00:03:40,310
to pee.

48
00:03:40,710 --> 00:03:43,560
So if Ruth was Beemans, now it has become a new rule.

49
00:03:43,710 --> 00:03:46,200
So rule should be what, BLR?

50
00:03:48,710 --> 00:03:49,300
That's it.

51
00:03:50,900 --> 00:03:55,760
So how we should construct a tree, I'll show you an example diagram then based on that, we will construct

52
00:03:55,760 --> 00:04:03,550
a tree so far that I will take some values like here I will take the value that is 50 root is 50 that

53
00:04:03,550 --> 00:04:06,420
let's take it as 50, then left Charleston.

54
00:04:07,430 --> 00:04:08,510
OK, then.

55
00:04:08,510 --> 00:04:10,370
Right, Charles 20 I will take.

56
00:04:11,950 --> 00:04:18,040
So the result should be to start doing this in the road and the left side is only on the right side,

57
00:04:18,040 --> 00:04:18,990
should become 50.

58
00:04:19,720 --> 00:04:22,000
So let us give these values and check.

59
00:04:24,770 --> 00:04:29,880
Here already I have a breakpoint, so here I'll give the root value that is 50 and the left child is

60
00:04:29,880 --> 00:04:32,210
the end and the right child is 20.

61
00:04:33,330 --> 00:04:34,770
Now, if I were on this program.

62
00:04:36,390 --> 00:04:38,700
And the debug mode, let us see what happens.

63
00:04:40,670 --> 00:04:48,770
Yeah, Rudi's 20 and it's left chinless 10 and right side it's 50, yes, perfect rotation is performed.

64
00:04:49,220 --> 00:04:49,970
That is a lot.

65
00:04:49,980 --> 00:04:51,050
Auditioners perform.

66
00:04:51,140 --> 00:04:55,190
So we are getting the same thing that is rooted in Banty and the left Charleston and the right will

67
00:04:55,190 --> 00:04:55,880
become 50.

68
00:04:56,570 --> 00:04:59,000
So a lot of is also working perfect.

69
00:04:59,480 --> 00:05:01,250
Then remaining that fall.

70
00:05:01,250 --> 00:05:02,110
I'll stop here.

71
00:05:02,120 --> 00:05:06,620
You have to ride on other two rotations and check them just off left and right.

72
00:05:06,620 --> 00:05:08,090
You have to just switch their names.

73
00:05:08,090 --> 00:05:08,630
That is left.

74
00:05:08,630 --> 00:05:09,020
Should we come.

75
00:05:09,020 --> 00:05:09,300
Right.

76
00:05:09,300 --> 00:05:09,470
Right.

77
00:05:09,490 --> 00:05:10,220
Should we come left.

78
00:05:11,120 --> 00:05:16,580
And already I have written the code for checking balance factors and calling the respective functions

79
00:05:16,910 --> 00:05:17,990
so you can use this one.

80
00:05:20,280 --> 00:05:21,840
That's all and this video.

