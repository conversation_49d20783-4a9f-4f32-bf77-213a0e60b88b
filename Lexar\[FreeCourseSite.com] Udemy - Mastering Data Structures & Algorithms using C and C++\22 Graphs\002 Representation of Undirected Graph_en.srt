1
00:00:00,180 --> 00:00:06,210
This video, a look at the representation of graph, the data structures used for representing Graph

2
00:00:06,870 --> 00:00:14,450
C for presenting a graph, we have to do things what are the set of notices and also set of edges so

3
00:00:14,520 --> 00:00:15,570
far representing them.

4
00:00:15,840 --> 00:00:21,310
There are more than one methods that are just in cymatics existentialist and compact list.

5
00:00:21,960 --> 00:00:24,100
Let us look all of them one by one.

6
00:00:24,240 --> 00:00:24,480
Yeah.

7
00:00:24,570 --> 00:00:26,460
Here first one is are just Jason.

8
00:00:26,460 --> 00:00:29,190
Cymatics, I want to take on the metrics.

9
00:00:29,550 --> 00:00:36,780
The graph is represented as a set of words and pictures and the number of what is this which is represented

10
00:00:36,780 --> 00:00:44,330
as and are five and the number of images that are represented with the symbol E are seven.

11
00:00:46,020 --> 00:00:50,820
So I have to represent all those words, the synergies for representing in the form of a matrix.

12
00:00:50,850 --> 00:00:55,200
I have to get a map of size five cross five, five by five.

13
00:00:56,990 --> 00:01:02,210
The what is this hour, starting from one on, Wortzel here also have taken matics or starting from

14
00:01:02,210 --> 00:01:09,800
a longwalls, but really when you use a program C C++, but really when you use a C C++ program, it

15
00:01:09,800 --> 00:01:11,110
will be starting from zero.

16
00:01:11,120 --> 00:01:16,280
So you can start from zero and you can level the words from zero on words, not let us see how to represent

17
00:01:16,280 --> 00:01:20,530
that graph in the form of a magnox C for any towards us.

18
00:01:20,570 --> 00:01:21,610
I and G.

19
00:01:21,710 --> 00:01:29,450
If there is an edge then we should mark one inside the matics at that same location and let me shoot.

20
00:01:29,540 --> 00:01:37,520
It means if there is an edge, I put that mark in the matrix I g as one.

21
00:01:37,940 --> 00:01:39,710
Otherwise it should be taken.

22
00:01:39,710 --> 00:01:41,450
As you mentioned, there is no.

23
00:01:42,230 --> 00:01:47,720
So let me show you how come Mark this one there to start from Vertex one one next one is having a disconnected

24
00:01:47,720 --> 00:01:49,480
two, two, three and four.

25
00:01:49,850 --> 00:01:55,910
So from one there is an extra two and that's two, three and four remaining are Zeitels.

26
00:01:56,690 --> 00:01:58,070
Zero means there is no edge.

27
00:01:58,370 --> 00:01:59,690
Then let us look at second.

28
00:02:00,850 --> 00:02:06,940
From to there are just two, one and three from two, there are just two, one and three, and these

29
00:02:06,940 --> 00:02:10,300
are all Ziegel like I will fill up for all.

30
00:02:11,740 --> 00:02:19,990
From three to one to four and five, one, two, four, five, then from four, it is connecting one

31
00:02:19,990 --> 00:02:23,830
and three and five, one, three and five.

32
00:02:24,580 --> 00:02:27,150
Then from five it is connecting to three and four.

33
00:02:28,450 --> 00:02:30,630
So from five to four on three and four.

34
00:02:31,150 --> 00:02:33,070
So that's it seems there is no change.

35
00:02:33,070 --> 00:02:40,840
I have my gazettal so even you can market as infinity in absence of edge and one wanting diagonals are

36
00:02:40,840 --> 00:02:44,120
representing self loop like one, two, one, two, two, two.

37
00:02:44,140 --> 00:02:45,490
There are no self loop here.

38
00:02:45,730 --> 00:02:48,910
So something you put infinity there in self loops.

39
00:02:49,240 --> 00:02:52,180
So it depends on your application, depends on your requirement.

40
00:02:52,180 --> 00:02:58,750
Either you can put the value as a zero or you can put a value as infinity or any other marker that you

41
00:02:58,750 --> 00:03:00,220
want to show.

42
00:03:00,250 --> 00:03:01,410
Absence of an X.

43
00:03:01,420 --> 00:03:09,070
The next let us look at are just on the list for representing a graph in the form of an to list will

44
00:03:09,070 --> 00:03:11,620
take an array of link lists.

45
00:03:12,040 --> 00:03:14,200
So I taken at the offices of five.

46
00:03:15,430 --> 00:03:18,630
This is an area of size five, this list.

47
00:03:18,850 --> 00:03:23,320
So actually this is array of linked list for each location.

48
00:03:23,620 --> 00:03:27,190
It's representing a vertex so far that location.

49
00:03:27,190 --> 00:03:31,240
We will have the set of Addison Water System, for example.

50
00:03:31,240 --> 00:03:38,110
If we take one, if I take vertex one from one, there are what this is connected to it are two, three

51
00:03:38,110 --> 00:03:38,740
and four.

52
00:03:38,830 --> 00:03:48,370
So here we will draw our link with the those nodes representing two and three and four.

53
00:03:50,880 --> 00:03:57,640
This is not now similarly, who are connected two to one and three, so I should have the list of nodes

54
00:03:57,640 --> 00:04:01,680
are showing one and three.

55
00:04:03,650 --> 00:04:09,560
And not singularly for three, the connected world is a sad one to four and five.

56
00:04:09,650 --> 00:04:10,430
I would draw them.

57
00:04:11,980 --> 00:04:17,410
Now, ForFour Connected or DSR one, three and five, so I will draw one, three and five year.

58
00:04:18,940 --> 00:04:23,500
One, three and five, not four, five, three and four, three and four.

59
00:04:25,850 --> 00:04:33,180
So this is real for linguist's, showing the edges and the story itself is showing, what is this?

60
00:04:34,040 --> 00:04:35,570
So this representation.

61
00:04:37,600 --> 00:04:42,100
Now, I have to show you come back list, but before that, let us do some analysis of this.

62
00:04:42,130 --> 00:04:42,400
What?

63
00:04:44,250 --> 00:04:50,580
See, in this matrix, there are total five into five elements, are there some are zeros and some other

64
00:04:50,580 --> 00:04:50,950
ones.

65
00:04:51,240 --> 00:04:54,250
So unless we check, we can't know whether there's an edge or not.

66
00:04:54,570 --> 00:04:57,840
So for accessing at just, we may have to access all the elements.

67
00:04:58,140 --> 00:05:03,480
So total number of elements are depending on the number of what that is and square.

68
00:05:04,290 --> 00:05:12,960
So it means any algorithm which is accessing a graph in the form of a schematics has to access those

69
00:05:12,960 --> 00:05:15,120
many elements, process those many elements.

70
00:05:15,420 --> 00:05:19,700
So the time taken by an algorithm may be dependent on and square.

71
00:05:20,130 --> 00:05:26,270
Then if an algorithm is using a decentralised, then what is the memory consumed here?

72
00:05:26,520 --> 00:05:31,470
It is the set of what exists that is set off what why that is.

73
00:05:31,470 --> 00:05:36,370
And plus these are set of edges, what each edges represent to time.

74
00:05:36,410 --> 00:05:39,040
So two into E I can see.

75
00:05:39,330 --> 00:05:48,150
So this is two E so the time dependent on set of what is a set of edges there.

76
00:05:48,150 --> 00:05:50,450
It is dependent on squared off vertices.

77
00:05:50,760 --> 00:05:57,060
So the time taken by any of them depends on a number of what on edges and it depends which are the structure

78
00:05:57,060 --> 00:05:58,410
is used by an algorithm.

79
00:05:58,650 --> 00:06:03,990
It will be in square or it may be V plus E to Sofie's.

80
00:06:03,990 --> 00:06:07,390
Also, we can consider it as a sort of Volesky.

81
00:06:07,620 --> 00:06:14,240
So if you check most of the algorithms, they are analyzed based on V plus the number of edges.

82
00:06:15,210 --> 00:06:18,730
So that's the analysis of the space consumed by this data structure.

83
00:06:19,170 --> 00:06:21,150
Now, next, one more thing I will show you.

84
00:06:21,150 --> 00:06:22,950
That is VITIT graph.

85
00:06:23,400 --> 00:06:27,800
So here I have given way to the edges of a graph.

86
00:06:28,260 --> 00:06:34,680
If you wait for a given, then we want to store those, which also means the cost of this is nine.

87
00:06:34,740 --> 00:06:38,750
The cost of this are just six and the cost of this are just eight.

88
00:06:39,090 --> 00:06:41,680
So we want to store those weights also.

89
00:06:42,030 --> 00:06:43,590
So how to store that?

90
00:06:43,770 --> 00:06:50,670
So in the form of actual semantics, instead of marking this as one, this is just one we can market

91
00:06:50,670 --> 00:06:52,370
with it and that is nine.

92
00:06:52,740 --> 00:06:59,760
So X from one to two is nine and the cost of X from one, two, three is for the cost of an X from one

93
00:06:59,760 --> 00:07:01,140
to four is six.

94
00:07:02,490 --> 00:07:08,210
So in this way, all these ones can be change in the form of a village of those edges.

95
00:07:08,370 --> 00:07:09,220
I'll change them.

96
00:07:09,510 --> 00:07:11,180
So here I have change all of them.

97
00:07:11,370 --> 00:07:18,000
Now we can call this as cost are just in semantics instead of just calling it, as I just said, semantics

98
00:07:18,330 --> 00:07:25,350
we call it does cost are just semantics because the matics representing the cost of adjusts then how

99
00:07:25,350 --> 00:07:28,800
to represent those values in addition to the list.

100
00:07:29,880 --> 00:07:35,700
So here for the vertex one, I must have all these models which are just in along with this inside the

101
00:07:35,700 --> 00:07:36,770
node itself.

102
00:07:37,050 --> 00:07:39,690
I should also have it stored here.

103
00:07:40,920 --> 00:07:45,210
One, two, three, four, four, and one, two, four is six.

104
00:07:46,050 --> 00:07:47,780
Likewise, you can fill up all these.

105
00:07:47,910 --> 00:07:55,490
So along with the vertex number and the weight of an X is also stored inside the list itself.

106
00:07:57,690 --> 00:08:01,470
So now it will be called cost are just simply list.

107
00:08:02,870 --> 00:08:05,540
So that's all about these two representations now.

108
00:08:05,570 --> 00:08:08,980
Next, let us look at compassionless representation.

109
00:08:10,080 --> 00:08:15,990
Compact misrepresentation, we will have a single image of a 40 presenting a graph, what should be

110
00:08:15,990 --> 00:08:18,550
the size of that single image of every single one?

111
00:08:18,600 --> 00:08:19,420
This is our fight.

112
00:08:19,440 --> 00:08:20,970
We should have a number of artists.

113
00:08:20,970 --> 00:08:26,980
Plus, as each edge is represented in two way, that is one to two and two to one.

114
00:08:27,570 --> 00:08:28,020
So.

115
00:08:30,010 --> 00:08:34,059
Two dimes of ages, and I should also have an extra space.

116
00:08:34,600 --> 00:08:40,190
So how much it is five plus two, seven plus one.

117
00:08:40,750 --> 00:08:42,340
This will be Brandee.

118
00:08:43,840 --> 00:08:49,550
Then one more thing, I'm starting and this is from one onwards, I'm not using index WSDL, so for

119
00:08:49,550 --> 00:08:54,360
that I need one more extra space that is twenty plus one twenty one.

120
00:08:55,730 --> 00:08:58,860
So here I will draw on a real Forsys grindy one.

121
00:08:59,390 --> 00:09:04,010
So here I have an idea of size twenty one and this is starting from zero to 20.

122
00:09:04,250 --> 00:09:08,220
So total 21 spaces then how do you present this.

123
00:09:08,780 --> 00:09:12,310
See this force to five locations representing.

124
00:09:12,680 --> 00:09:13,520
What is this.

125
00:09:16,290 --> 00:09:17,170
There are 2.5.

126
00:09:18,030 --> 00:09:24,660
So first five spaces, I'm not using this index zero, so from one to five, they are representing what

127
00:09:24,670 --> 00:09:29,550
is this total Birleffi versus other out there then for each word?

128
00:09:29,790 --> 00:09:36,450
I should have just since a list means who are just sent word like four vertex one are just one word,

129
00:09:36,850 --> 00:09:38,700
two, three and four.

130
00:09:39,120 --> 00:09:40,800
But no, I will not be using weights.

131
00:09:40,800 --> 00:09:42,090
I will remove these weights.

132
00:09:43,460 --> 00:09:49,300
So for fun, I just thought, what is this are two, three and four, so this I have to write down so

133
00:09:49,460 --> 00:09:55,070
to write on this see here leading this location and I will not use that location.

134
00:09:55,070 --> 00:09:56,720
I will show you why I'm living at.

135
00:09:57,470 --> 00:09:58,400
So from here.

136
00:10:00,050 --> 00:10:02,400
Two, three and four.

137
00:10:02,760 --> 00:10:09,080
These are just on versus off vortex one, see the addition where the source of Vertex one, three,

138
00:10:09,080 --> 00:10:13,900
two, three and four, they are starting from Indexer seven and ending at nine.

139
00:10:14,630 --> 00:10:20,350
So I will write on that seven here, just seven that this means that they are just in what is this of

140
00:10:20,360 --> 00:10:26,210
what one are stopping for the next seven that are two, three, four, then where they are ending,

141
00:10:26,390 --> 00:10:27,460
they are ending at nine.

142
00:10:27,710 --> 00:10:29,090
So I did not mention that.

143
00:10:29,210 --> 00:10:35,690
I have just mentioned the starting point, no logistic, other purposes for Vertex or to determine what

144
00:10:35,690 --> 00:10:36,910
is this are one entry.

145
00:10:37,130 --> 00:10:40,010
So Almog them one and three.

146
00:10:40,280 --> 00:10:44,470
So these are from 10 to 11, only just two places.

147
00:10:44,720 --> 00:10:46,010
So that is starting from ten.

148
00:10:46,220 --> 00:10:48,110
So that ten in Lexington.

149
00:10:48,170 --> 00:10:49,010
I will right here.

150
00:10:49,310 --> 00:10:54,220
This means that Anderson, what is the software next to understanding from Unix then onwards.

151
00:10:54,560 --> 00:10:55,460
So 10 and 11.

152
00:10:55,460 --> 00:11:05,100
They are at 11 now as we have the seven and ten not observe this, the largest convergence of one hour

153
00:11:05,130 --> 00:11:06,620
starting from seven onwards.

154
00:11:06,650 --> 00:11:10,670
OK, seven, eight, nine, stop.

155
00:11:11,880 --> 00:11:12,250
Why?

156
00:11:12,510 --> 00:11:19,560
Because the next location belongs to two, so seven and 10 together gives the stabbing an ending point

157
00:11:19,560 --> 00:11:20,620
forward next one.

158
00:11:21,120 --> 00:11:26,640
So that's all just we keep starting index of a different list of what is next.

159
00:11:26,650 --> 00:11:28,650
Let us write on the rest of those.

160
00:11:28,950 --> 00:11:30,340
That is for three words.

161
00:11:30,390 --> 00:11:35,100
And what is this are one, two, four, five, one, two, four, five.

162
00:11:35,100 --> 00:11:36,530
Starting in Texas, 12.

163
00:11:36,810 --> 00:11:39,240
And this belongs to Vortex three.

164
00:11:39,560 --> 00:11:44,680
This belongs to them four four one three five four one three five.

165
00:11:45,630 --> 00:11:46,830
These are four four.

166
00:11:47,190 --> 00:11:50,250
And the starting point of this one is sixteen.

167
00:11:51,000 --> 00:11:54,110
Then four five are just in words, as are three and four.

168
00:11:54,420 --> 00:11:55,800
So these are three and four.

169
00:11:56,010 --> 00:11:57,670
And the starting point is nineteen.

170
00:11:57,870 --> 00:12:00,800
This is starting point nineteen and this belongs to five.

171
00:12:02,490 --> 00:12:12,830
I have read all of them now from seven to ten minus one list of what is this one from ten to twelve,

172
00:12:12,840 --> 00:12:22,530
minus one eleven list of what is a form of text to these two, then four for 16 to 18 is 16 to 18,

173
00:12:22,860 --> 00:12:23,370
then four.

174
00:12:23,370 --> 00:12:24,360
Five.

175
00:12:24,540 --> 00:12:25,800
Nineteen to what.

176
00:12:26,400 --> 00:12:27,330
Nineteen to what.

177
00:12:27,630 --> 00:12:28,200
Ninety two.

178
00:12:28,200 --> 00:12:29,370
Twenty minutes.

179
00:12:29,400 --> 00:12:30,680
Twenty one minus one.

180
00:12:31,050 --> 00:12:33,810
So here I should try it on twenty one.

181
00:12:34,020 --> 00:12:37,020
So give us the ending point for this vertex liquor store.

182
00:12:37,380 --> 00:12:37,980
Fine.

183
00:12:39,120 --> 00:12:45,420
That's why you have kept this place blank so that we can store the index that is also the size of an

184
00:12:45,420 --> 00:12:49,170
Audi that is thirty one nineteen to twenty minus one twenty.

185
00:12:49,740 --> 00:12:50,430
So that's it.

186
00:12:50,820 --> 00:12:52,590
Does the compact misrepresentation.

187
00:12:52,920 --> 00:12:54,000
I have no store.

188
00:12:54,270 --> 00:12:57,930
Wait, if you want to store weights then you should have one more area.

189
00:12:58,050 --> 00:13:03,780
It means two dimensional every two rows and twenty one columns and each location.

190
00:13:03,780 --> 00:13:05,610
You can have the vertex number below that.

191
00:13:05,610 --> 00:13:06,950
You can also have its straight.

192
00:13:07,140 --> 00:13:09,390
Then what is the space consumed by this one.

193
00:13:09,840 --> 00:13:14,400
This is a number of bodices and number of adjusts to time.

194
00:13:14,430 --> 00:13:17,700
So plus two e see this.

195
00:13:17,760 --> 00:13:24,690
We call it guys and and this is to e if you say is also equal to end, this is to win so total it is

196
00:13:24,690 --> 00:13:26,140
three in this order of.

197
00:13:26,200 --> 00:13:34,950
And so Selenia, if we assume that an E equal if we assume they're not equal but if we assume so why

198
00:13:34,950 --> 00:13:39,900
we're assuming is any linear value is also leading value.

199
00:13:40,350 --> 00:13:41,550
It's not a square.

200
00:13:41,970 --> 00:13:43,850
Right is square quadratic.

201
00:13:43,860 --> 00:13:46,500
It is not n square and square isn't quite ready.

202
00:13:46,710 --> 00:13:48,510
So this is also linear, this is also linear.

203
00:13:48,510 --> 00:13:54,120
So both are linear and the means this is not variable and this means linear.

204
00:13:55,480 --> 00:14:03,670
So this is consumed by compatibilities Línea, that's all so in the coming videos we will see Algorithm's

205
00:14:03,670 --> 00:14:04,330
on Graff's.

