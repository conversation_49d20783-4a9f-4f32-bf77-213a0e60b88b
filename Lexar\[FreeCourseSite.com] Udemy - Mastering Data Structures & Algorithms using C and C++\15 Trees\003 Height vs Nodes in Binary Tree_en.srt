1
00:00:01,040 --> 00:00:09,650
This topic is about to formalize height versus nold formulas for what is this if we know the height

2
00:00:09,650 --> 00:00:15,920
of a binary tree, what could be the minimum number of norms and what could be the maximum number of

3
00:00:15,920 --> 00:00:22,450
norms and vice versa if nodes are given what could be minimum height, what could be the maximum height?

4
00:00:22,550 --> 00:00:24,050
We are going to discuss this one.

5
00:00:24,740 --> 00:00:26,330
So first, let us learn.

6
00:00:26,660 --> 00:00:32,630
If height of a binary tree is given, then what could be the minimum number of nodes?

7
00:00:32,630 --> 00:00:35,000
What could be the maximum number of nodes?

8
00:00:35,240 --> 00:00:37,220
So I'll let you have taken a few examples.

9
00:00:37,220 --> 00:00:38,780
Let us study these examples.

10
00:00:39,200 --> 00:00:43,250
Height of a binary trees given height as one side starts from zero.

11
00:00:43,250 --> 00:00:43,490
Right?

12
00:00:43,760 --> 00:00:44,920
So this is zero and one.

13
00:00:45,200 --> 00:00:46,110
So how does one.

14
00:00:46,430 --> 00:00:51,490
So in height one minimum, how many nodes are required to reach height.

15
00:00:51,500 --> 00:00:52,840
One two nodes.

16
00:00:53,450 --> 00:00:56,780
If I remove or not the only one node it will not be height one.

17
00:00:57,380 --> 00:01:00,740
So many one two nodes are needed to make a tree of height one.

18
00:01:01,330 --> 00:01:03,290
Then in that height, one maximum.

19
00:01:03,290 --> 00:01:04,510
How many nodes are possible.

20
00:01:04,730 --> 00:01:05,480
One, two, three.

21
00:01:05,480 --> 00:01:12,800
Those are possible then likewise if height is two then for making a tree of Hideko minimum.

22
00:01:12,800 --> 00:01:13,940
How many nodes required.

23
00:01:14,160 --> 00:01:17,570
One, two, three maximum seven nodes.

24
00:01:17,930 --> 00:01:20,660
Likewise height is three minimum nodes.

25
00:01:20,720 --> 00:01:22,670
These many maximum nodes.

26
00:01:22,760 --> 00:01:26,630
This many we don't want to count, we want a formula.

27
00:01:27,080 --> 00:01:30,850
So, so from observation we can generate the formula.

28
00:01:31,220 --> 00:01:33,590
So first let us take minimum number of nodes.

29
00:01:33,980 --> 00:01:40,880
If height is one minimum two nodes high this to minimum three nodes, height is three minimum four nodes.

30
00:01:41,690 --> 00:01:44,900
So minimum nodes form that will be height plus one.

31
00:01:45,170 --> 00:01:46,400
C height plus one.

32
00:01:48,340 --> 00:01:53,710
So minimum notes and equal to each plus one.

33
00:01:54,370 --> 00:01:55,900
This is one formula we've got.

34
00:01:57,270 --> 00:02:00,210
No, you want the formula for maximum N.

35
00:02:01,490 --> 00:02:04,330
Three, seven, 15.

36
00:02:05,480 --> 00:02:07,040
We don't find any pattern.

37
00:02:08,220 --> 00:02:18,180
Like there was a pattern here, two or three, three or four plus one, but three, seven, 15, let

38
00:02:18,180 --> 00:02:24,570
us observe from the street how we got 15, we got 15, because this is one note.

39
00:02:24,870 --> 00:02:27,990
And in this level there are two notes.

40
00:02:28,410 --> 00:02:33,030
And this level, one, two, three, four, four, Nawzad, that is two square miles.

41
00:02:33,420 --> 00:02:38,570
And in this level, one, two, three, four, five, six, seven, eight, two Cubanos are there.

42
00:02:39,780 --> 00:02:45,150
So this is one plus two plus two squared plus two.

43
00:02:45,150 --> 00:02:48,200
Q This answer is 15.

44
00:02:48,930 --> 00:02:52,110
So all these Norsemen, when they are added, it is becoming 15.

45
00:02:52,860 --> 00:02:56,100
So one plus two plus two squared plus two Cube is giving 15.

46
00:02:56,970 --> 00:02:58,780
So is there any formula for this one.

47
00:03:00,330 --> 00:03:01,620
Let me show you the formula.

48
00:03:02,070 --> 00:03:08,070
Say this is GPCRs a a r r square yard cube.

49
00:03:08,190 --> 00:03:15,650
So on to a R power K so this is a series and all the terms of GPCRs are added.

50
00:03:16,170 --> 00:03:19,110
So some of these towns are of GPCRs.

51
00:03:20,080 --> 00:03:27,910
That is a job that is geometric progression, some of the terms of Christmas geometric progression,

52
00:03:27,920 --> 00:03:30,700
Chris, so there is a known formula for this one.

53
00:03:31,030 --> 00:03:40,960
This is a nod to the power of K plus one, minus one by R, minus one decimal one formula.

54
00:03:41,860 --> 00:03:44,050
Now, this looks like Chris only.

55
00:03:44,440 --> 00:03:45,680
Let me right on that one.

56
00:03:46,420 --> 00:03:51,610
So you have written this one one plus two plus two squared plus two cubed goes on to power.

57
00:03:53,440 --> 00:03:56,340
Then what is in this one is one.

58
00:03:57,970 --> 00:04:03,510
Then what is odd in this one odd is to see very strong coefficient.

59
00:04:03,520 --> 00:04:06,000
It is not, they're not say this one.

60
00:04:06,340 --> 00:04:08,140
So A is one, an R is two.

61
00:04:08,410 --> 00:04:12,860
So I will use this formula and I will write on it as one and are as two.

62
00:04:13,180 --> 00:04:17,339
So this is is one of to power.

63
00:04:17,620 --> 00:04:24,940
What is the maximum term I have taken Hetch Hetchy plus one minus one by artist two.

64
00:04:24,940 --> 00:04:26,030
So two minus one.

65
00:04:26,530 --> 00:04:33,450
So if I simplify this, this is two part S plus one minus one divided by two, minus one is one.

66
00:04:34,000 --> 00:04:35,020
So that is the answer.

67
00:04:36,430 --> 00:04:42,890
So if you take the sum of these stones up to each, then this is two points plus or minus one.

68
00:04:44,230 --> 00:04:45,850
Let us come back to this example.

69
00:04:46,150 --> 00:04:52,080
It was a three, so one plus two plus two squared plus two cube up to three higher to three.

70
00:04:52,420 --> 00:04:56,740
So this is nothing but two power plus one minus one, two or three.

71
00:04:56,740 --> 00:04:57,830
Plus one, minus one.

72
00:04:58,240 --> 00:04:59,610
Let us put the values here.

73
00:04:59,740 --> 00:05:01,270
So two point three plus one.

74
00:05:01,270 --> 00:05:01,970
Minus one.

75
00:05:02,320 --> 00:05:03,790
This is too powerful.

76
00:05:03,790 --> 00:05:06,160
Minus one and this is sixteen.

77
00:05:06,160 --> 00:05:06,860
Minus one.

78
00:05:07,150 --> 00:05:08,210
So this is 15.

79
00:05:08,230 --> 00:05:09,400
Yes, it is correct.

80
00:05:10,900 --> 00:05:11,630
That is correct.

81
00:05:12,040 --> 00:05:16,450
So that value 15 is some of the terms of the.

82
00:05:17,170 --> 00:05:19,710
So these are some of the columns of GPCRs.

83
00:05:20,080 --> 00:05:24,070
So these are these terms if we take maximum nought.

84
00:05:24,550 --> 00:05:26,200
So that is similar to this one.

85
00:05:26,200 --> 00:05:27,400
We already know the formula.

86
00:05:27,400 --> 00:05:28,800
We have used the same formula.

87
00:05:29,050 --> 00:05:31,090
Now we got the formula for maximum N.

88
00:05:33,010 --> 00:05:34,970
So we already have four minimum nodes.

89
00:05:35,290 --> 00:05:45,430
I will remove this, I will it on for maximum nodes, so formula for maximum nodes and equal to Dupa

90
00:05:45,490 --> 00:05:46,480
H plus one.

91
00:05:46,630 --> 00:05:47,470
Minus one.

92
00:05:48,370 --> 00:05:49,870
So now we know the formula.

93
00:05:50,080 --> 00:05:59,590
If height of a binary tree is given, then within that height minimum we need X plus one N maximum dupa

94
00:05:59,590 --> 00:06:02,070
x plus one minus one number of nodes are possible.

95
00:06:02,420 --> 00:06:02,830
Right.

96
00:06:03,130 --> 00:06:07,070
So these formulas are very important for binary tree.

97
00:06:07,730 --> 00:06:09,700
Now I need the formulas in other way.

98
00:06:10,150 --> 00:06:13,390
If nodes are given, what will be the minimum height?

99
00:06:13,410 --> 00:06:16,330
What will be the maximum right here with no height.

100
00:06:16,420 --> 00:06:22,720
So we found OK n now next we will be doing n we want height fighting.

101
00:06:22,730 --> 00:06:23,400
Same formula.

102
00:06:23,460 --> 00:06:24,100
We can use.

103
00:06:24,430 --> 00:06:25,440
Let us see first.

104
00:06:25,450 --> 00:06:28,030
I will take some examples, then we will discuss this.

105
00:06:28,970 --> 00:06:34,640
Now, if the number of nodes are given, we have to find out what could be the minimum height, what

106
00:06:34,640 --> 00:06:37,730
could be the maximum height for those number of nodes.

107
00:06:38,210 --> 00:06:41,480
So here I have already taken some examples.

108
00:06:42,320 --> 00:06:48,920
Using three nodes, I can generate a binary tree of minimum height, one zero one.

109
00:06:49,820 --> 00:06:56,540
And with that same tree node, I can stretch the height up to maximum to zero one, two, four, three

110
00:06:56,540 --> 00:06:56,960
nodes.

111
00:06:57,500 --> 00:06:59,840
Next, I have to get an example of seven nodes.

112
00:06:59,870 --> 00:07:03,320
So for seven nodes, one, two, three, four, five, six, seven.

113
00:07:03,350 --> 00:07:04,280
This is the minimum height.

114
00:07:04,280 --> 00:07:09,460
Height is to that zero one, two and these seven nodes, maximum height of six.

115
00:07:10,850 --> 00:07:13,850
Then for 15 nodes, minimum height is three.

116
00:07:14,180 --> 00:07:15,680
Maximum height is 14.

117
00:07:16,580 --> 00:07:18,050
Now we want the formula.

118
00:07:19,680 --> 00:07:26,490
For a minimum and maximum, first of all, we will find the formula for maximum, let us set maximum

119
00:07:26,880 --> 00:07:34,170
maximum height for three high to four seven N Hida six four 15.

120
00:07:34,170 --> 00:07:35,710
N height is 14.

121
00:07:36,450 --> 00:07:44,340
So this pattern, whatever, the nodes are high, this one less than that nodes are seven to six nodes

122
00:07:44,340 --> 00:07:44,730
are three.

123
00:07:44,730 --> 00:07:45,630
So high is two.

124
00:07:46,020 --> 00:07:49,020
So high, this one less than the number of nodes.

125
00:07:49,410 --> 00:07:52,530
So yes, we got the formula for maximum height.

126
00:07:52,770 --> 00:07:57,970
So maximum height, Formula X equals two and minus one.

127
00:07:58,800 --> 00:07:59,830
We got the formula.

128
00:08:00,180 --> 00:08:01,900
Now what is the minimum height formula?

129
00:08:01,920 --> 00:08:06,010
Now if I check the minimum height number of nodes are three.

130
00:08:06,150 --> 00:08:09,460
So minimum height is one, number of nodes are seven.

131
00:08:09,480 --> 00:08:12,700
So minimum, the stool number of nodes are 15.

132
00:08:12,870 --> 00:08:13,780
So how does three?

133
00:08:14,190 --> 00:08:15,800
So I don't find any pattern here.

134
00:08:16,830 --> 00:08:18,150
So I'll show you something here.

135
00:08:18,600 --> 00:08:24,510
See, these are the formulas just now we have found out if height is given, then minimum Norns and

136
00:08:24,510 --> 00:08:25,440
maximum Norns.

137
00:08:25,540 --> 00:08:30,240
We have all the formula, minimum nonsensical stretch plus one, maximum noses equals two.

138
00:08:30,540 --> 00:08:31,860
And these two bodies plus one.

139
00:08:31,860 --> 00:08:32,460
Minus one.

140
00:08:32,490 --> 00:08:33,620
We have seen this formula.

141
00:08:35,260 --> 00:08:42,360
Now we are finding out now if new orders are given, then find out minimum and maximum height, if no

142
00:08:42,370 --> 00:08:45,520
laws are given so far that maximum height.

143
00:08:45,550 --> 00:08:47,410
We got to go through in minus one.

144
00:08:47,410 --> 00:08:53,290
So that's the supreme law to this one that is equal to and minus one X equals to end this plus one,

145
00:08:53,290 --> 00:08:54,000
you send it there.

146
00:08:54,250 --> 00:08:55,010
So minus one.

147
00:08:55,270 --> 00:08:57,690
So this was the minimum formula.

148
00:08:57,700 --> 00:08:59,860
It became maximum height formula.

149
00:09:00,190 --> 00:09:05,880
So it means maximum load formula can be converted into minimum height formula.

150
00:09:06,280 --> 00:09:08,740
So I don't have to observe from the crease.

151
00:09:08,980 --> 00:09:11,230
Let us use this formula and can like it.

152
00:09:11,630 --> 00:09:13,150
OK, so what is the formula.

153
00:09:13,150 --> 00:09:14,410
And is equal to two parties.

154
00:09:14,410 --> 00:09:14,830
Plus one.

155
00:09:14,830 --> 00:09:15,450
Minus one.

156
00:09:15,730 --> 00:09:16,990
So I will find out here.

157
00:09:18,920 --> 00:09:23,330
And as we go through two borders, plus one, minus one, this is the formula, right?

158
00:09:23,630 --> 00:09:25,120
This is for maximum notes.

159
00:09:25,490 --> 00:09:29,080
So this I can read it as and plus one as equals.

160
00:09:29,180 --> 00:09:31,160
Two to par plus one.

161
00:09:33,490 --> 00:09:40,120
That is when I brought it here, then I will change the site, so two parties plus one equals two and

162
00:09:40,120 --> 00:09:48,110
plus one, not X plus one is log base two and plus one.

163
00:09:48,520 --> 00:09:54,580
So if you send this base to that site, it becomes log X, plus one equals to log based.

164
00:09:54,620 --> 00:10:01,870
And finally, this X is equal to log base two and plus one.

165
00:10:02,290 --> 00:10:04,990
And this plus one if I send it there, minus one.

166
00:10:05,770 --> 00:10:09,790
So I got the formula for each as equals to log base two and plus one.

167
00:10:09,790 --> 00:10:14,510
Minus one from where I got this one from this one C from this formula.

168
00:10:14,710 --> 00:10:21,370
This was in terms of X to find out and I converted it in terms of end to find out which.

169
00:10:23,050 --> 00:10:32,710
This has done so for minimum height, minimum height, so if animals are given the minimum height will

170
00:10:32,710 --> 00:10:34,110
be based on this formula.

171
00:10:34,110 --> 00:10:35,960
Now let us verify this one.

172
00:10:36,370 --> 00:10:39,660
See, I'm having 50 knows what could be the minimum height.

173
00:10:39,670 --> 00:10:41,660
We know down to three that is secured.

174
00:10:42,160 --> 00:10:47,560
That is equal to log 15 plus one minus one.

175
00:10:48,190 --> 00:10:54,070
So this is Loga 16 base to log 16 base to minus one log.

176
00:10:54,100 --> 00:10:55,060
16 is how much?

177
00:10:55,060 --> 00:10:57,000
Four minus one.

178
00:10:57,010 --> 00:10:59,020
This minus one as it is this minus one.

179
00:10:59,320 --> 00:11:00,110
This is three.

180
00:11:00,130 --> 00:11:00,460
Yes.

181
00:11:00,460 --> 00:11:01,570
We are getting the answer.

182
00:11:02,140 --> 00:11:06,100
So from these formulas, we can derive other formulas.

183
00:11:06,340 --> 00:11:06,650
Right.

184
00:11:07,000 --> 00:11:13,210
So if you know the formula for height, you can get the formula for N, so I will write on those formulas,

185
00:11:13,570 --> 00:11:19,440
which is equal to and the minus one is equal to minimum formula.

186
00:11:19,450 --> 00:11:27,860
That is log base two and plus one minus one maximum formula and the minus one.

187
00:11:28,390 --> 00:11:29,550
So you got the formula.

188
00:11:30,430 --> 00:11:34,540
So I will remove this and finally show you two formulas, these formulas.

189
00:11:34,540 --> 00:11:35,360
I'll show them here.

190
00:11:35,830 --> 00:11:39,580
Finally, from the observation, we got a range of values.

191
00:11:40,600 --> 00:11:47,110
The first formula besides this one number of nodes in a binary tree, number of nodes.

192
00:11:48,110 --> 00:11:56,540
Minimum wage plus one maximum two parties, plus one minus this is based on height, if you know the

193
00:11:56,540 --> 00:12:02,990
height of a binary tree, the line that by the tree minimum number of nodes can be this many maximum

194
00:12:02,990 --> 00:12:04,740
number of nodes can be these many.

195
00:12:05,410 --> 00:12:08,990
Next, we saw this formula height of a binary tree.

196
00:12:09,410 --> 00:12:10,660
This is very important.

197
00:12:11,340 --> 00:12:15,620
A pine tree can range from logging to any.

198
00:12:17,200 --> 00:12:23,320
Logarithmic to linear, then this is outdraw log-in.

199
00:12:24,900 --> 00:12:26,880
And this is outdraw and.

200
00:12:28,220 --> 00:12:35,240
So minimum is logging maximum and the height of a binary tree can range from logging to it.

201
00:12:35,320 --> 00:12:39,100
Remember this one always so binary treatments logging, Gwen.

202
00:12:40,020 --> 00:12:44,990
And whatever the border look I have done, you do it once, then you will always remember this farmers,

203
00:12:45,020 --> 00:12:46,580
these farmers are very important.

