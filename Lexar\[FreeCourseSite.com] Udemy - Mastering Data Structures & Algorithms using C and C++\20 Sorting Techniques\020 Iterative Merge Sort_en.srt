1
00:00:00,320 --> 00:00:09,420
Another topic is a version of <PERSON><PERSON> thought, this is to blame us, sort through them so it can be implemented

2
00:00:09,810 --> 00:00:15,090
at relatively also recursively also so far we learn it, Reconversion.

3
00:00:16,010 --> 00:00:20,370
So for explaining my thought, I have already taken an example of.

4
00:00:22,800 --> 00:00:24,040
So let us see the problem.

5
00:00:25,140 --> 00:00:33,720
I have an Audi containing a list of eight elements, one list of eight elements, and we have to solve

6
00:00:33,720 --> 00:00:33,960
them.

7
00:00:34,620 --> 00:00:36,300
I'll repeat the problem statement.

8
00:00:36,690 --> 00:00:42,660
There is an array containing one list of eight elements we have decided not to apply.

9
00:00:42,660 --> 00:00:44,820
Most thought we change the statement.

10
00:00:45,720 --> 00:00:51,000
We say that there is an array containing eight lists.

11
00:00:52,400 --> 00:00:53,320
Eight list.

12
00:00:54,370 --> 00:00:57,890
How I see this aid itself is one list.

13
00:00:57,910 --> 00:01:00,750
Three is one list, seven is another list.

14
00:01:00,970 --> 00:01:02,170
Food is another list.

15
00:01:02,440 --> 00:01:03,550
Nine is one of the list.

16
00:01:03,550 --> 00:01:04,930
Two is four like this.

17
00:01:04,930 --> 00:01:07,960
We say that each element is our list.

18
00:01:08,260 --> 00:01:08,890
For now.

19
00:01:08,890 --> 00:01:10,120
I repeat the statement.

20
00:01:10,630 --> 00:01:17,260
There is an array containing eight lists where each of us contain one element.

21
00:01:18,600 --> 00:01:19,950
Problem statement is change.

22
00:01:21,180 --> 00:01:25,830
No, you tell me if a list is having just one element, is it sorted or not?

23
00:01:26,110 --> 00:01:32,670
Yes, if you take just the sentiment as one list, it is certain this element as a list, it is sorted,

24
00:01:32,670 --> 00:01:34,260
seven sorted for sorted.

25
00:01:34,590 --> 00:01:36,260
So each list is sorted.

26
00:01:36,540 --> 00:01:41,250
So then they are so that we can directly merge them and merging is sorted.

27
00:01:41,250 --> 00:01:41,820
Resolved.

28
00:01:42,580 --> 00:01:43,010
Yes.

29
00:01:43,410 --> 00:01:48,180
So now the task is we have to merge eight lists.

30
00:01:50,360 --> 00:01:56,400
For how to march, at least for two emerging markets will pick up to this tragic time and merge them.

31
00:01:57,140 --> 00:01:58,140
So let us start.

32
00:01:58,880 --> 00:02:01,070
So first of all, march these two list.

33
00:02:01,160 --> 00:02:04,600
This list is having one eliminate this list as having one element three.

34
00:02:04,880 --> 00:02:10,220
So we will merge these two list and make it as a single list so that there will be three.

35
00:02:10,220 --> 00:02:11,870
The smaller eight is greater.

36
00:02:11,900 --> 00:02:16,500
So we get two elements and the new merged list.

37
00:02:16,520 --> 00:02:17,510
So this is sorted.

38
00:02:18,890 --> 00:02:24,930
Then we select the next pair of list, so we have to list, no one is having seven, one is having foremast

39
00:02:24,950 --> 00:02:25,550
these two.

40
00:02:26,860 --> 00:02:33,720
Result is four to seven, because for smaller now, this is sudden, then we select next pan.

41
00:02:36,360 --> 00:02:42,270
Nine and go, when you march, it will become two and nine, because two is smaller to comes first novel

42
00:02:42,270 --> 00:02:44,020
March disappear off list.

43
00:02:44,280 --> 00:02:46,770
So five is a smaller feigen force than six.

44
00:02:47,070 --> 00:02:48,930
So this is March.

45
00:02:49,290 --> 00:02:55,290
So we got four lists now out of eight lists than we have March for bears.

46
00:02:55,530 --> 00:02:56,770
Then we got four list.

47
00:02:56,800 --> 00:03:01,280
Now, if you observe earlier, what was the size of list, just one element.

48
00:03:01,800 --> 00:03:06,220
What is the size of list to two elements each to two elements each.

49
00:03:07,110 --> 00:03:08,400
Again, we have to continue.

50
00:03:08,580 --> 00:03:09,960
We have to continue merging.

51
00:03:10,350 --> 00:03:14,870
Not again, go through this entire area by merging list.

52
00:03:14,880 --> 00:03:15,920
So what are those list?

53
00:03:17,850 --> 00:03:24,180
More to these two list of two elements, each, the result will be smallest number is three, then four,

54
00:03:24,450 --> 00:03:26,780
then seven and eight.

55
00:03:27,240 --> 00:03:29,210
We got one list of eight elements.

56
00:03:29,550 --> 00:03:36,290
Then the more these two list to the smallest, then five, then six, the nine.

57
00:03:36,690 --> 00:03:38,010
So we got another list.

58
00:03:40,380 --> 00:03:46,950
Now for less we have more trouble taking paid at the time, so we got to list now we have to march at

59
00:03:46,950 --> 00:03:52,830
least five was one element sidewalls to Alemanno Sivas for Element.

60
00:03:52,840 --> 00:03:57,550
So sizes are getting multiplied by two sides of the list is growing by two more.

61
00:03:57,570 --> 00:04:04,290
We have to merge these two, merge these two to the smaller than trees.

62
00:04:04,290 --> 00:04:06,050
Next, then Ford is next.

63
00:04:06,370 --> 00:04:07,620
Then five is smaller.

64
00:04:07,890 --> 00:04:12,440
Then compare these two so six and then compare these to seven comes next.

65
00:04:12,780 --> 00:04:14,220
Then it comes, then nine.

66
00:04:14,670 --> 00:04:16,269
And that's on this subject.

67
00:04:16,980 --> 00:04:19,470
So this is how it looks.

68
00:04:20,970 --> 00:04:26,040
For eight elements, how many passersby have performed so means we have gone through the entire list

69
00:04:26,040 --> 00:04:26,730
at once.

70
00:04:27,000 --> 00:04:29,770
So this we have gone through and we got this result.

71
00:04:30,420 --> 00:04:31,890
So this is FastPass.

72
00:04:33,710 --> 00:04:41,050
Result of first pass, then this was the result of second pass, then this, then this is the result

73
00:04:41,050 --> 00:04:41,970
of a third pass.

74
00:04:42,340 --> 00:04:48,570
So if you perform only three passes and we got the result so total, how many elements were there to

75
00:04:48,590 --> 00:04:48,970
the lead?

76
00:04:48,970 --> 00:04:53,910
Elements were there for four that we have performed three passes in this past year.

77
00:04:54,360 --> 00:05:01,690
One one element of the magic bullet for the five was to that next time it was to the elements together.

78
00:05:01,690 --> 00:05:05,500
The size was for the next time for four elements were together.

79
00:05:05,500 --> 00:05:06,760
The size was eight.

80
00:05:07,890 --> 00:05:14,460
So let us do analysis, see, this is a great diversion and it is having process assessments, we are

81
00:05:14,460 --> 00:05:18,890
going through the entire list at once, not partially at once.

82
00:05:18,900 --> 00:05:23,060
We are going to check if it is recursive, it will not have passes.

83
00:05:23,230 --> 00:05:24,080
It is iterative.

84
00:05:24,090 --> 00:05:25,500
So that's why it's having process.

85
00:05:25,890 --> 00:05:28,760
So in one box, how many elements are.

86
00:05:30,940 --> 00:05:32,770
So in one past, what is the work done?

87
00:05:33,070 --> 00:05:38,710
We are doing nothing but merging the elements, how many elements are merged and elements are much.

88
00:05:39,100 --> 00:05:44,680
Then again from this, how many elements are much here from there to here and elements are much then

89
00:05:44,680 --> 00:05:45,690
again, from there to here.

90
00:05:45,700 --> 00:05:46,330
How many elements?

91
00:05:46,330 --> 00:05:48,100
Harmoush and elements are much.

92
00:05:48,370 --> 00:05:49,080
So this is done.

93
00:05:49,080 --> 00:05:49,970
How many times?

94
00:05:50,380 --> 00:05:53,870
So if you turn this upside down, you can see it as a tree.

95
00:05:54,700 --> 00:05:58,270
So this is the root left and right and it's left here.

96
00:05:58,270 --> 00:05:58,510
Right.

97
00:05:58,930 --> 00:06:01,300
And these are left LHR of this one.

98
00:06:01,570 --> 00:06:04,020
And these are the left of each of these notes.

99
00:06:04,150 --> 00:06:05,220
So it looks like a tree.

100
00:06:05,530 --> 00:06:07,330
So it is the same as height of a tree.

101
00:06:08,470 --> 00:06:10,720
So height of the tree is nothing but logging.

102
00:06:10,960 --> 00:06:12,880
So this is done for how many times?

103
00:06:12,880 --> 00:06:13,960
Log in times.

104
00:06:15,590 --> 00:06:21,670
Others, there are totally elements, eight log, eight babies, two is how much, three.

105
00:06:21,860 --> 00:06:24,720
So it is done four, three times three passes are required.

106
00:06:25,160 --> 00:06:26,480
So that's how it is login.

107
00:06:26,780 --> 00:06:31,100
So total times and that is done for login times.

108
00:06:31,610 --> 00:06:35,600
So the time complexities, order of analogue and.

109
00:06:37,880 --> 00:06:47,270
So the time, complexity of a trade off to emerge, authorities and the logjam now let us look at algorithm

110
00:06:47,270 --> 00:06:50,090
or function for iterative version of MOSSHART.

111
00:06:50,420 --> 00:06:53,000
So I would write on the function, then I will explain you.

112
00:06:53,870 --> 00:06:57,340
So here is an iterative version of much thought.

113
00:06:57,920 --> 00:07:01,340
It is taking Horia as a parameter and NP.

114
00:07:01,580 --> 00:07:02,750
That is a number of elements.

115
00:07:02,780 --> 00:07:05,790
So here number of elements are eight.

116
00:07:06,110 --> 00:07:08,140
So this is eight.

117
00:07:08,840 --> 00:07:11,030
Then this loop is four passes.

118
00:07:11,060 --> 00:07:14,830
So how many passes we need for eight elements, three passes.

119
00:07:15,140 --> 00:07:17,040
That is log-in passes.

120
00:07:17,390 --> 00:07:24,640
So here I have two can be assigned to unappeased, getting multiplied by two and reach up to.

121
00:07:24,690 --> 00:07:31,050
And so if I write P values first time P will be two, then the P is getting multiplied by two.

122
00:07:31,070 --> 00:07:37,700
So next time P becomes four, then again multiplied by two, so P becomes eight and it will continue

123
00:07:37,700 --> 00:07:39,480
as long as it is less than equal to.

124
00:07:39,510 --> 00:07:41,120
And so now it is equal to.

125
00:07:41,120 --> 00:07:42,050
And so it will continue.

126
00:07:42,470 --> 00:07:44,540
So total three passes.

127
00:07:44,540 --> 00:07:45,320
It will perform.

128
00:07:45,350 --> 00:07:46,620
We will take three values.

129
00:07:47,000 --> 00:07:53,390
So in the first passes perform on the original must be value will be to the next part B level before

130
00:07:53,600 --> 00:07:54,320
the next pass.

131
00:07:54,350 --> 00:07:55,640
P value will be eight.

132
00:07:57,840 --> 00:07:58,660
So what is this?

133
00:07:58,680 --> 00:08:05,970
I will explain you if I take from the last list P values eight minutes total eight elements are much

134
00:08:05,970 --> 00:08:07,450
in the schools today.

135
00:08:07,770 --> 00:08:09,210
One is with the four elements.

136
00:08:09,220 --> 00:08:11,160
The other one is also the four elements.

137
00:08:11,430 --> 00:08:13,140
So totally the elements are most.

138
00:08:13,170 --> 00:08:14,800
That's why P values eight.

139
00:08:14,820 --> 00:08:21,810
If you remember much function or algorithm that we have seen in the previous video, it takes low,

140
00:08:21,810 --> 00:08:27,940
middle and high of a single lurrie and the merge them with the help of Auxillary.

141
00:08:29,070 --> 00:08:33,390
If you have not seen previous video, just watch previous video upon merging.

142
00:08:33,390 --> 00:08:39,690
They don't have explain you how to live in the same or they can be much so far that low, middle and

143
00:08:39,690 --> 00:08:41,080
high three words are required.

144
00:08:41,340 --> 00:08:48,000
So when PS eight low should be this one and this should be made and this should be high.

145
00:08:48,360 --> 00:08:53,560
So it will mar a list from low to mid and second list is from plus one to high.

146
00:08:54,120 --> 00:08:55,430
So that's all four eight.

147
00:08:55,440 --> 00:08:57,500
It works perfectly within this band.

148
00:08:57,580 --> 00:08:59,360
This is not one.

149
00:08:59,370 --> 00:09:02,220
This is for when this is four.

150
00:09:02,430 --> 00:09:06,020
So this is low, this is mid and this is high.

151
00:09:07,020 --> 00:09:10,740
So just to this time, again, it has to be continued for this one.

152
00:09:11,250 --> 00:09:12,960
For this one, all six should be continued.

153
00:09:13,140 --> 00:09:14,250
Again, this is low.

154
00:09:14,430 --> 00:09:18,690
This is and this is high with the total elements are four.

155
00:09:19,230 --> 00:09:21,300
So similarly, even P is a two.

156
00:09:21,510 --> 00:09:29,040
Then more than one paid off list has to be much so far that I have taken one more for loop I I is taking

157
00:09:29,040 --> 00:09:32,130
a span off to list that again to list.

158
00:09:32,340 --> 00:09:32,620
Right.

159
00:09:32,940 --> 00:09:40,230
So it is moving by P C moving by P so using I calculate low, high.

160
00:09:40,350 --> 00:09:47,400
And so as we have to merge more than one pair of list so far that we have this iteration I.

161
00:09:48,580 --> 00:09:52,180
So if I show it on first list, first time I is here.

162
00:09:52,360 --> 00:09:54,190
So these two elements are much.

163
00:09:54,340 --> 00:09:57,850
Next time I will be here, then these two elements will be much.

164
00:09:57,850 --> 00:10:00,550
Next time I will be here, then these two elements will be merged.

165
00:10:00,850 --> 00:10:02,260
Next I will be here.

166
00:10:02,470 --> 00:10:04,090
And these two elements will be much.

167
00:10:04,210 --> 00:10:09,010
And each time this is Lord, this is high and this is this is low and this is high.

168
00:10:09,010 --> 00:10:11,770
And this will be this is low and this is high.

169
00:10:11,770 --> 00:10:16,230
And this will remain so that so all are calculated and multi-function scored.

170
00:10:16,450 --> 00:10:22,510
So if you want to understand it by taking values, then you have to substitute, then you have to prepare

171
00:10:22,510 --> 00:10:25,260
a table and understand how these values are changing.

172
00:10:26,470 --> 00:10:31,750
Then one last thing in this function, I have taken exactly eight elements.

173
00:10:32,110 --> 00:10:34,090
So log eight.

174
00:10:35,690 --> 00:10:42,260
Base two is three, so I get exact answer if these are not eight elements, so you suppose this is nine

175
00:10:42,530 --> 00:10:45,100
didn't want the extra element will be there somewhere.

176
00:10:45,230 --> 00:10:50,180
I will take one more element, one here that will not be paired with anyone that does not match that

177
00:10:50,180 --> 00:10:50,720
same one.

178
00:10:50,730 --> 00:10:51,540
Carry on here.

179
00:10:51,710 --> 00:10:53,800
Again, this through this to this is left over.

180
00:10:54,030 --> 00:10:55,570
Then again, one carried on here.

181
00:10:55,760 --> 00:10:57,080
So these two will be much.

182
00:10:57,080 --> 00:10:58,430
Then again, one is left over.

183
00:10:58,640 --> 00:10:59,930
Then it comes as it is.

184
00:11:00,140 --> 00:11:02,960
Then this list should be more on this one.

185
00:11:03,470 --> 00:11:07,840
So one, the more pass has to be performed so far that this is there.

186
00:11:08,180 --> 00:11:13,190
So then you have an odd number of elements in the list of the list sizes, not in powers of two.

187
00:11:13,550 --> 00:11:17,570
Then some elements will be left over so far, merging them dysfunctional state.

188
00:11:18,990 --> 00:11:25,380
So that's all about it with the version of Mosshart, which takes any longer in time, I will write

189
00:11:25,380 --> 00:11:27,110
the program and run it and show you.

