1
00:00:00,330 --> 00:00:04,650
In this video, we will look at a demonstration for marching to raise.

2
00:00:05,880 --> 00:00:12,180
So first of all, I will create stories, I will directly initialize them structurally, I will call

3
00:00:12,210 --> 00:00:15,630
first one as Aarav one and I will initialize it.

4
00:00:15,890 --> 00:00:19,890
The list of elements are two, six and.

5
00:00:21,200 --> 00:00:28,880
15 and 25 is the first list, so that I put on five elements and the size of the array I can give any

6
00:00:28,880 --> 00:00:30,440
size, so I will give a larger size.

7
00:00:30,440 --> 00:00:33,450
And the number of elements that is Lantis five.

8
00:00:34,370 --> 00:00:35,300
This is the first study.

9
00:00:37,630 --> 00:00:42,550
Then second, they struck three eight to.

10
00:00:44,610 --> 00:00:50,250
I will initialize the array first, so the elements are three and four.

11
00:00:51,800 --> 00:00:52,550
Seven.

12
00:00:54,780 --> 00:01:01,650
18 and 20 now, the size I have to give size, it can be greater than or equal to five because I have

13
00:01:01,650 --> 00:01:02,400
only five elements.

14
00:01:02,400 --> 00:01:04,890
So let us give the size of 10 and then.

15
00:01:06,120 --> 00:01:11,510
Number of elements are five, so I am taking the same number of elements in both the others, then the

16
00:01:11,510 --> 00:01:20,240
third area, one so far that I want a function march to create today so far that I will take appointer.

17
00:01:22,320 --> 00:01:22,980
Iara.

18
00:01:24,000 --> 00:01:24,420
Three.

19
00:01:25,940 --> 00:01:27,210
I will not created any.

20
00:01:28,530 --> 00:01:33,540
Now, these three variables, it is giving a warning that these three variables are not in use.

21
00:01:33,570 --> 00:01:35,070
Yes, we have not yet used them.

22
00:01:36,720 --> 00:01:39,670
This is the same program or the project that we have been using.

23
00:01:39,690 --> 00:01:43,590
So inside this, I will try to function for much.

24
00:01:45,450 --> 00:01:48,420
March, March should take to address.

25
00:01:50,250 --> 00:01:52,830
Much should take to address.

26
00:01:54,640 --> 00:02:01,660
It should take my address, so first one is Ardavan, I live the same name, Struct Uhry.

27
00:02:02,920 --> 00:02:05,060
And second one is Iraq, too.

28
00:02:06,670 --> 00:02:09,699
Then what are the things required in this function?

29
00:02:09,710 --> 00:02:19,130
We need I and G that will help us facing a even a ripple that I need to for putting the the material.

30
00:02:19,420 --> 00:02:24,130
And it's a race I should be and I will create an army from heap.

31
00:02:24,370 --> 00:02:26,350
So for that I will take appointer.

32
00:02:27,830 --> 00:02:31,220
Start at three, then here, I will say.

33
00:02:33,900 --> 00:02:36,390
Struck a three pointer.

34
00:02:37,810 --> 00:02:38,590
Malaak.

35
00:02:41,980 --> 00:02:51,610
Size of struct three, so this object will be created in here so that I can return it and I can use

36
00:02:51,610 --> 00:02:56,770
it inside mean function the Monterrosa, I should be able to use it in the main function.

37
00:02:56,790 --> 00:02:59,230
So that's why I'm creating this on heap.

38
00:03:01,980 --> 00:03:09,580
I will read on the position that we have already discussed on Lightford, while I is less than they

39
00:03:09,610 --> 00:03:11,090
are once lente.

40
00:03:12,750 --> 00:03:13,260
And.

41
00:03:14,580 --> 00:03:16,200
Jay is less than.

42
00:03:18,650 --> 00:03:27,230
Eight hours to land, that second is land, as long as I eat less than Forslund and Jesus lived in second

43
00:03:27,230 --> 00:03:27,600
land.

44
00:03:28,580 --> 00:03:30,140
We have to compare the elements.

45
00:03:35,240 --> 00:03:38,990
Now, if you have to compare unpopular elements, so I will initialize these.

46
00:03:40,820 --> 00:03:49,420
Variable, so I need Kelsall, so let us initialize these three variables I assigned and assign assigned

47
00:03:49,460 --> 00:03:56,810
Zettl, all these three are initialized and now here I will compare and copied elements if everyone's.

48
00:03:58,150 --> 00:04:03,610
Element s less than a retools element.

49
00:04:04,800 --> 00:04:05,220
Then.

50
00:04:06,920 --> 00:04:09,830
I read these elements should be.

51
00:04:10,950 --> 00:04:11,880
Set to.

52
00:04:15,940 --> 00:04:17,500
Everyone's element.

53
00:04:22,890 --> 00:04:30,180
And this I and the key bullshot should increment in agreement I as well as Kate.

54
00:04:32,630 --> 00:04:46,580
Else in 83, we should copy the elements of secondary C++ should be equal to a sign that Oracle's element.

55
00:04:49,580 --> 00:04:54,030
That's it, so this will be all the elements by comparing them one by one.

56
00:04:57,110 --> 00:04:59,390
And it will stop and one of the area has finished.

57
00:05:00,020 --> 00:05:05,040
Now I have to copy the remaining elements from any one of the three, so I should start from wherever

58
00:05:05,040 --> 00:05:05,780
it is.

59
00:05:05,780 --> 00:05:06,200
So.

60
00:05:07,350 --> 00:05:10,080
I is for first relend.

61
00:05:11,750 --> 00:05:14,970
Then I was then in third.

62
00:05:16,280 --> 00:05:17,840
I should copy the elements.

63
00:05:19,250 --> 00:05:21,280
From First Lady.

64
00:05:25,560 --> 00:05:27,930
And similarly, I will copy and paste this one.

65
00:05:29,860 --> 00:05:35,150
I will copy and paste this one, and this should be 4G and start typing again.

66
00:05:35,830 --> 00:05:37,180
I will make it four G.

67
00:05:37,480 --> 00:05:43,210
This G is less than a retools length and G plus plus.

68
00:05:44,320 --> 00:05:52,740
And he they should be copied in key and this should come from a little NetSol, this will match both

69
00:05:52,750 --> 00:05:57,280
the arrays and store the result in a three or four, three, three.

70
00:05:57,430 --> 00:05:58,870
I should set its length.

71
00:06:00,130 --> 00:06:01,180
Length should be.

72
00:06:03,870 --> 00:06:05,100
Everyone's lent.

73
00:06:07,520 --> 00:06:07,970
Plus.

74
00:06:08,890 --> 00:06:10,140
Attitudes lent.

75
00:06:12,550 --> 00:06:19,300
This, I should said and added resources is not set, I should said the site also an.

76
00:06:21,280 --> 00:06:28,350
That's not as this three is created in the heap, I can return this to the main function so I can set

77
00:06:28,360 --> 00:06:34,000
it on a tree and here the function should have the return type as.

78
00:06:35,760 --> 00:06:38,340
Struct a pointer.

79
00:06:41,080 --> 00:06:45,430
He has the function, this little lyndy, but it is ready for merging, this is a function for emerging

80
00:06:46,870 --> 00:06:48,370
from inside main function.

81
00:06:48,400 --> 00:06:51,400
I will call Marje bypassing.

82
00:06:52,690 --> 00:07:03,680
Addressed of First Street and address our second agree and the result, I will take it in a three then.

83
00:07:03,760 --> 00:07:04,960
Whatever the result I got.

84
00:07:07,700 --> 00:07:08,720
I will display that.

85
00:07:10,960 --> 00:07:17,290
So let us call the function display, and it is called value, so I should send star Aarav three.

86
00:07:22,190 --> 00:07:27,590
That's all these elements should be merged and they should get all sorted elements in added three.

87
00:07:31,870 --> 00:07:40,420
Two, three, four, six, seven, 10, 15, 18, 20 and 25, yes, perfect and so perfect, so much

88
00:07:40,420 --> 00:07:42,340
functions, merging all elements.

89
00:07:43,570 --> 00:07:46,690
So this multi-function we may be using at various places.

90
00:07:48,510 --> 00:07:51,470
So that's all we have seen multi-function, that's all in this video.

