1
00:00:00,620 --> 00:00:03,740
In this video, we learn details about <PERSON><PERSON><PERSON><PERSON>.

2
00:00:05,750 --> 00:00:06,750
What is the link list?

3
00:00:08,029 --> 00:00:12,830
What is the node like nodes already I have shown you in the previous video?

4
00:00:13,580 --> 00:00:20,120
Then how do we find the structure of the node and how to create a node, then how to access the contents

5
00:00:20,120 --> 00:00:20,750
of a node?

6
00:00:21,260 --> 00:00:23,980
We learn basic things about the list.

7
00:00:24,770 --> 00:00:27,620
So let us start with the definition of a linguist.

8
00:00:27,620 --> 00:00:28,460
Let us define it.

9
00:00:28,460 --> 00:00:38,060
What it is linguist is a collection of nodes where each and also contain data and pointer to next node

10
00:00:39,290 --> 00:00:41,380
data and point to next node.

11
00:00:41,630 --> 00:00:46,810
So linking is a collection of nodes with each node contained data and point at the next node.

12
00:00:47,150 --> 00:00:53,340
If you see this node, its address is 200 and it is having Data eight and address to ten.

13
00:00:53,360 --> 00:00:55,860
So this student is the areas of DNA.

14
00:00:56,660 --> 00:00:58,100
This is the status of this node.

15
00:00:58,340 --> 00:01:02,880
So two, that this node is having data tree and address 270.

16
00:01:02,990 --> 00:01:06,020
So this is the node who said this is 270.

17
00:01:06,350 --> 00:01:10,810
So every node is having the data as well as linked to next normal.

18
00:01:11,750 --> 00:01:15,740
And first is a pointer which points on first node.

19
00:01:15,950 --> 00:01:23,000
We call this pointer name as a first or also we call it as her head is a pointer pointing to first node

20
00:01:23,000 --> 00:01:24,140
or headed node.

21
00:01:24,590 --> 00:01:26,550
So this atlas's 200.

22
00:01:26,570 --> 00:01:31,030
So this two hundred and the address of Pointer Exercice Henpecked.

23
00:01:31,850 --> 00:01:35,480
Now this is what I have shown you as similar to the one already.

24
00:01:35,480 --> 00:01:41,900
We have discussed in the previous video that link was created inside Ahepe, these notes and this force

25
00:01:41,920 --> 00:01:44,290
to the point that pointing to that first node or head node.

26
00:01:44,570 --> 00:01:46,730
So the address of this point, that is one hundred.

27
00:01:47,420 --> 00:01:53,690
And these are just I have taken them randomly in real you create some memory in here that maybe that

28
00:01:53,780 --> 00:01:55,100
this is maybe in thousands.

29
00:01:55,490 --> 00:01:58,540
But for making it simple, I have taken their decision.

30
00:01:58,910 --> 00:02:00,490
So these are just random addresses.

31
00:02:01,280 --> 00:02:06,140
Now, one thing you can observe in an array, all the addresses are contiguous.

32
00:02:06,170 --> 00:02:07,360
They are side by side.

33
00:02:07,940 --> 00:02:13,760
If this is two hundred and this is not to like that, but here you can see that this are this is 200.

34
00:02:13,760 --> 00:02:18,990
So this a student and this is somewhere else that is 270 and this is somewhere else at 300.

35
00:02:19,400 --> 00:02:22,010
So all those nodes will not be side by side.

36
00:02:23,610 --> 00:02:30,860
Then how the continue is maintained through links, that is addresses, this is about the link Linklaters

37
00:02:30,870 --> 00:02:36,600
created in Heap, so I will remove this, then we'll talk about how to define the node, how to create

38
00:02:36,600 --> 00:02:36,800
a new.

39
00:02:37,320 --> 00:02:41,250
Now let us see how we can define a node for defining a node.

40
00:02:41,550 --> 00:02:51,000
We have to define two things data and the pointer data may be any type like integer floatable or any

41
00:02:51,000 --> 00:02:53,910
other type you can take then pointers.

42
00:02:53,910 --> 00:02:58,500
Which type pointer is pointing to a node that is next node.

43
00:02:58,920 --> 00:02:59,760
This is a node.

44
00:02:59,790 --> 00:03:00,690
So what is this then?

45
00:03:00,870 --> 00:03:01,830
There's also node.

46
00:03:02,160 --> 00:03:06,960
So point that is often all the type node is having a pointer off node type.

47
00:03:08,490 --> 00:03:16,290
So it's a pointer of its own type now that has defined the structure of a. A. is containing two members.

48
00:03:18,870 --> 00:03:25,060
Here is data and here is a pointer to next normal, let us call it.

49
00:03:25,060 --> 00:03:25,830
That's next.

50
00:03:26,100 --> 00:03:28,290
So this is node structure.

51
00:03:28,890 --> 00:03:37,970
Node is having data and pointer to next normal then in programming, how we can define this structure

52
00:03:38,310 --> 00:03:44,640
so far that we can use of C language structure struct nor.

53
00:03:46,950 --> 00:03:55,380
Nord, what are the members, Daytop This of which you type, it can be any type integer floatable or

54
00:03:55,380 --> 00:04:01,160
any other structure, you can take anything you want, but usually for learning purposes we use integer.

55
00:04:01,560 --> 00:04:04,910
So I will call it as integer data.

56
00:04:07,730 --> 00:04:13,700
Then what is this pointer pointer toward pointer to a known, then what is this?

57
00:04:13,730 --> 00:04:21,060
This is also known so this pointer is a pointer of a type A. means the pointer of a type.

58
00:04:21,079 --> 00:04:21,890
Same note.

59
00:04:22,220 --> 00:04:34,890
So here struct Naude pointer names next on this late in the structure.

60
00:04:35,390 --> 00:04:42,680
Now you can see that a structure is having data as well as pointer of type structure.

61
00:04:42,680 --> 00:04:47,200
Not only so it's having a pointer of its own type.

62
00:04:47,930 --> 00:04:54,980
So such a structure is called self-referential structure, self-referential structure.

63
00:04:55,640 --> 00:05:00,010
Self-referential structures are famous that are they are used in Linklaters.

64
00:05:00,020 --> 00:05:04,610
So yes, we can see that they are useful in Lincolnesque C language programming.

65
00:05:04,610 --> 00:05:07,640
We use a structure for C++ programming.

66
00:05:07,640 --> 00:05:09,650
Also we can use a structure.

67
00:05:09,890 --> 00:05:11,950
C++ also support structure.

68
00:05:12,500 --> 00:05:15,680
What is the structure in C++ are the same as class.

69
00:05:16,040 --> 00:05:19,900
Then what is the difference between class and structure in class?

70
00:05:19,910 --> 00:05:23,420
Everything by default is appropriate and in structure.

71
00:05:23,420 --> 00:05:25,300
Everything by default is public.

72
00:05:25,880 --> 00:05:32,790
So if you are doing C++, if we are writing C++ program, then there also we can define a node with

73
00:05:32,790 --> 00:05:36,400
the structure or else we can use the class also.

74
00:05:37,370 --> 00:05:39,370
Next thing, what is the size of the structure?

75
00:05:39,380 --> 00:05:42,200
So on this site I will write on C, this is integer.

76
00:05:42,200 --> 00:05:45,230
So we are assuming every time that integer takes two bytes.

77
00:05:45,230 --> 00:05:47,030
So let us say this is taking two whites.

78
00:05:47,450 --> 00:05:51,650
And what about pointer C in any compiler?

79
00:05:51,660 --> 00:05:54,800
If Integer is taking to white pointer, it will also take the whites.

80
00:05:55,070 --> 00:05:58,190
If religion is taken for by the point, it will also take four bytes.

81
00:05:58,430 --> 00:06:01,160
So this is also taking two points total.

82
00:06:01,160 --> 00:06:01,970
How many bytes?

83
00:06:01,970 --> 00:06:03,830
These are four bytes.

84
00:06:04,760 --> 00:06:11,090
So the size of a N for whites it means this node is occupying four points.

85
00:06:11,360 --> 00:06:16,610
So the first this is two hundred and the next byte is to not one to know two and two or three.

86
00:06:17,000 --> 00:06:22,940
These are for whites and this node is also consuming memory to 10, 11 and two hundred and twelve and

87
00:06:22,940 --> 00:06:23,780
two hundred and thirteen.

88
00:06:23,780 --> 00:06:28,720
So everything for this country for four bytes but for a pointer just we need Fassbinder.

89
00:06:28,960 --> 00:06:33,440
So we are writing only white addresses this the size of a node.

90
00:06:34,030 --> 00:06:39,910
Now, next, let legacy how to create such nodes, how these nodes are created and the link there.

91
00:06:40,160 --> 00:06:43,280
So linking part to creating a link will see afterwards.

92
00:06:43,280 --> 00:06:48,170
First, let us see a basic thing how unnoticed created for creating a node.

93
00:06:48,380 --> 00:06:52,940
First of all, we need a pointer because nodes have to be created inside heap.

94
00:06:53,210 --> 00:06:55,760
So for that we must already have a point there.

95
00:06:55,760 --> 00:06:56,840
So I'll take a pointer.

96
00:06:58,480 --> 00:07:01,450
Struck northern.

97
00:07:03,110 --> 00:07:09,470
Star B. I'm calling the point, and they must be so when you declare any variable, this is a variable

98
00:07:09,470 --> 00:07:11,860
pointer variable, so whether memory will be looking at it.

99
00:07:12,020 --> 00:07:14,480
So we know that this pointer is isn't stack.

100
00:07:15,260 --> 00:07:17,630
So anyway, I will just show it as a pointer.

101
00:07:18,770 --> 00:07:19,520
The pointer.

102
00:07:20,990 --> 00:07:33,290
Now I have to create Naude in Hebrew so far that function in C languages mellark function mellark how

103
00:07:33,290 --> 00:07:36,980
many bytes I need total two plus two, four bytes I need.

104
00:07:37,700 --> 00:07:47,300
Instead of giving that four bytes, I can even see what size of struct nor struct nor

105
00:07:50,240 --> 00:07:52,190
this Mallott function returns.

106
00:07:52,250 --> 00:07:54,020
Why the type of pointer.

107
00:07:54,020 --> 00:07:56,720
So we have to type Kostic of this type.

108
00:07:57,050 --> 00:08:03,300
So here I should write on Struck North Star.

109
00:08:03,800 --> 00:08:04,930
This is typecasting.

110
00:08:06,140 --> 00:08:07,790
So this is very low in the code.

111
00:08:08,150 --> 00:08:09,170
This is a very lonely call.

112
00:08:09,470 --> 00:08:11,510
This is how we do it and see language.

113
00:08:12,590 --> 00:08:14,510
But in C++ it's very simple.

114
00:08:14,510 --> 00:08:24,920
Sapirstein new node, the small node will be created when you said new it is created and heap.

115
00:08:26,700 --> 00:08:33,330
So we will avoid writing this lengthy quote and just remove it and I will keep this fun while writing

116
00:08:33,330 --> 00:08:36,559
the program, I may be typing this code so it'll follow.

117
00:08:36,570 --> 00:08:38,880
This could be assigned new Naude.

118
00:08:39,870 --> 00:08:42,000
A new node will be created.

119
00:08:42,000 --> 00:08:45,510
A node structure is defined with the data and next that.

120
00:08:45,550 --> 00:08:52,240
So this is a data and this is an X point that suppose that this of this node is for example, five hundred

121
00:08:52,260 --> 00:08:57,540
and five hundred is there inside B so it means a piece pointing on this normal.

122
00:08:59,270 --> 00:09:04,730
This whole world is created, so we have seen how to create and then how to access the members of an

123
00:09:04,750 --> 00:09:05,190
order.

124
00:09:05,570 --> 00:09:09,520
I want to fill in some data and here I want to write address as zero.

125
00:09:09,830 --> 00:09:17,780
So I read the data here for writing data because the data be a raw data, like a pointer to a structure.

126
00:09:17,780 --> 00:09:22,670
So the structure members are access using arrow symbol operator.

127
00:09:22,970 --> 00:09:31,460
So I should say these data that is this one I sign with the value some 10.

128
00:09:31,940 --> 00:09:33,710
So that is stored here.

129
00:09:35,740 --> 00:09:36,070
Then.

130
00:09:37,150 --> 00:09:45,830
These next assignment, zero, so zero means null, null is the story here.

131
00:09:46,000 --> 00:09:47,440
So that is not pointing anywhere.

132
00:09:48,010 --> 00:09:50,770
So this is how we can access the members of a structure.

133
00:09:50,800 --> 00:09:57,460
This is how no one can be created, creating an order and accessing the members of a normal.

