1
00:00:00,390 --> 00:00:06,150
And this video will have a demonstration for tomorrow on a problem, so I will ride on the function

2
00:00:06,150 --> 00:00:07,890
recursive function, which we have seen.

3
00:00:10,800 --> 00:00:18,360
Wide be edge funny, <PERSON>'s number of disc, and next is our number.

4
00:00:18,510 --> 00:00:28,620
So first our number will be a the next over number is B and next to see the first US F number of disc

5
00:00:28,620 --> 00:00:33,150
are greater than zero, then only perform these steps.

6
00:00:35,480 --> 00:00:45,740
So the first step is over a phone, I call it cell four and minus one desk and see this tree cover names

7
00:00:46,520 --> 00:00:49,400
eight to see, we have to move using B.

8
00:00:49,850 --> 00:00:56,460
So here first move from A to B, but using C, C, C should come in between.

9
00:00:58,070 --> 00:01:02,320
So this is the first recursive call then printf.

10
00:01:03,320 --> 00:01:10,130
So here a statement move C instead of writing move, I'll do just one thing, I'll just write the other

11
00:01:10,460 --> 00:01:16,710
percentile the comma percentile d d it adorable is that we have generated at last.

12
00:01:16,730 --> 00:01:31,760
So again this one now here the tower S E and destination C, this will print other parts then that is

13
00:01:32,120 --> 00:01:34,370
from cover to cutover.

14
00:01:34,370 --> 00:01:36,110
That is sort of our true destination.

15
00:01:36,120 --> 00:01:44,660
Tougher then tower of now is recursively call for and the minus one desk and here we have to move the

16
00:01:44,660 --> 00:01:52,290
disk from Tower B, put our C and utilize our aid so it comes in between.

17
00:01:52,490 --> 00:01:56,300
So that's all is a very simple function inside the main function.

18
00:01:56,300 --> 00:02:02,300
I will call tomorrow honey, by passing three desk and the tower numbers, I will give the minus one

19
00:02:02,600 --> 00:02:03,580
to three.

20
00:02:04,460 --> 00:02:06,050
So first values number of this.

21
00:02:06,380 --> 00:02:08,900
The remaining three values are our numbers.

22
00:02:09,830 --> 00:02:12,530
Let us on the program and see what output we get.

23
00:02:16,790 --> 00:02:17,210
S..

24
00:02:19,890 --> 00:02:23,930
Seven steps they got for moving three days from today to cover.

25
00:02:23,940 --> 00:02:28,270
See, these are the steps which we have already saw them in the previous video.

26
00:02:28,290 --> 00:02:33,030
Also, I have used these steps for moving the disc from cover to cover.

27
00:02:33,060 --> 00:02:38,940
See, so the program is just giving the steps to be taken for moving the disc.

28
00:02:38,940 --> 00:02:41,360
And it's not moving that it is giving us a step.

29
00:02:41,670 --> 00:02:42,990
So following was a step.

30
00:02:42,990 --> 00:02:45,860
We should move the disc to solve this problem.

31
00:02:46,560 --> 00:02:50,150
If I change the number of disc to to let us see what happens.

32
00:02:53,190 --> 00:02:58,710
See more of this from Tower want to talk to than one to three than two to three, that's all.

33
00:02:59,040 --> 00:03:00,900
These are the steps we have seen for two days.

34
00:03:01,590 --> 00:03:04,170
If I give the number of these guys one, then what happens?

35
00:03:04,230 --> 00:03:05,550
Directly from one to three.

36
00:03:08,630 --> 00:03:12,950
There is no single step forward move orders from government, but our three.

37
00:03:16,200 --> 00:03:20,280
So if I get four, then let us see how many steps I'll get.

38
00:03:24,370 --> 00:03:32,560
Yes, these many steps are there, I guess these are 15 steps total, so you can count them, you can

39
00:03:32,560 --> 00:03:37,420
check it, that's all about our of any problem.

