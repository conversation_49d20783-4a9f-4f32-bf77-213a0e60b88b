1
00:00:00,720 --> 00:00:07,640
Let us look at the demo for <PERSON><PERSON>'s algorithm for finding minimum cost spanning three, four identical

2
00:00:07,650 --> 00:00:08,830
schools algorithm already.

3
00:00:08,830 --> 00:00:10,200
I have written a few things here.

4
00:00:10,590 --> 00:00:15,150
See, the first thing instead of just this is all to be shown on whiteboard instead of just out there

5
00:00:15,510 --> 00:00:18,420
three rows and nine column because there are total line.

6
00:00:18,420 --> 00:00:21,780
And just in our example graph, the graphics have shown on whiteboard.

7
00:00:22,080 --> 00:00:25,890
If you have not seen that video, I suggest you go back and watch that video.

8
00:00:26,300 --> 00:00:34,050
Then the demonstration of that video here as fast as the rule of law takes, then raw for second vertex,

9
00:00:34,050 --> 00:00:37,800
then this is the roll of cost of the ACC just stood in line and just decided.

10
00:00:38,490 --> 00:00:44,430
Then, as we are using disjoint subsets of that, there is an area of size eight here because there

11
00:00:44,430 --> 00:00:45,740
are seven words.

12
00:00:45,850 --> 00:00:50,700
So zero to seven, that is one to seven I'll be using zero.

13
00:00:50,770 --> 00:00:55,410
One is not used then included whether energy is included in the solution or not.

14
00:00:55,410 --> 00:00:59,250
So for that we are meeting in an array and that is all initialized to zero.

15
00:00:59,250 --> 00:01:01,890
Whenever we are including an entry, we are going to make it as one.

16
00:01:02,610 --> 00:01:06,550
Then the history of Tucumán six is a solution that is spanning.

17
00:01:06,550 --> 00:01:10,530
Three will be storing in this one that is strudels and six columns.

18
00:01:10,530 --> 00:01:11,550
That is zero to five.

19
00:01:12,540 --> 00:01:21,100
Then for performing centre operations we have joint that is union, I should call it as union and next

20
00:01:21,150 --> 00:01:23,400
US Fine Union and find operationalised.

21
00:01:23,400 --> 00:01:24,540
We have all to discuss.

22
00:01:24,870 --> 00:01:30,480
So I will call it as my union, OK, my union and that is you.

23
00:01:30,480 --> 00:01:32,820
And we will perform Union, the Soviet Union.

24
00:01:33,330 --> 00:01:36,570
And I was discussing about the collapsing find the find.

25
00:01:37,010 --> 00:01:41,920
This is the code which I have shown on whiteboard and this is the extra thing I said that once you found

26
00:01:41,920 --> 00:01:44,520
the period of any vertex, you can directly connect to it.

27
00:01:44,820 --> 00:01:51,420
So do the code for connecting all those words directly to the parent that is coming along the path.

28
00:01:51,630 --> 00:01:54,930
So the extra code and this is Corless collapsing.

29
00:01:54,930 --> 00:01:55,380
Fine.

30
00:01:56,880 --> 00:02:00,300
Now, next, we have everything already.

31
00:02:00,300 --> 00:02:02,160
I have shown you these things to discuss these things.

32
00:02:02,160 --> 00:02:06,060
My union is there and find and adjust and set everything.

33
00:02:06,420 --> 00:02:08,220
Now let us write on the mean function.

34
00:02:08,220 --> 00:02:13,230
That is the algorithm for Kruskal algorithm for algorithm is very simple.

35
00:02:13,230 --> 00:02:14,960
So what are the variables required?

36
00:02:14,970 --> 00:02:22,590
I will declare them IG and you as well as V and minimum for that.

37
00:02:22,590 --> 00:02:24,210
Let us initialize minimum to I.

38
00:02:24,300 --> 00:02:27,330
That is infinity as a maximum integer.

39
00:02:27,330 --> 00:02:28,620
I'm taking it as infinity.

40
00:02:28,620 --> 00:02:30,270
So it is initialized to eight.

41
00:02:30,900 --> 00:02:36,330
And also the number of what is this are seven and the number of edges.

42
00:02:36,330 --> 00:02:39,390
We have number of areas that are nine and just out there.

43
00:02:39,390 --> 00:02:43,620
So that is nine Eastern right now.

44
00:02:43,620 --> 00:02:49,890
This comes inside one Y loop because there are no initialization parts here.

45
00:02:50,220 --> 00:02:51,810
Everything is repeating step.

46
00:02:51,810 --> 00:02:54,000
So we have to write on and starting in a loop.

47
00:02:54,330 --> 00:02:57,780
And for that I will take I have to take total and minus one.

48
00:02:57,780 --> 00:03:01,050
I just so I is less than and minus one I will write on.

49
00:03:01,050 --> 00:03:02,330
So let us initialize.

50
00:03:02,340 --> 00:03:04,080
I do zero starting on what.

51
00:03:04,440 --> 00:03:08,840
So I'll be getting and minus one it just so inside this white look will get all the edges.

52
00:03:09,390 --> 00:03:14,930
Now here we have to write in the logic now inside this outer loop I have to find out a minimum cost

53
00:03:14,940 --> 00:03:17,520
edge so far that I have to scan through the edges.

54
00:03:17,520 --> 00:03:24,440
So I will use G and discharge from zero ages less than E that is number of edges and C++.

55
00:03:24,960 --> 00:03:26,220
This is G plus plus.

56
00:03:26,490 --> 00:03:31,050
And every time I have to find out the minimum cost edge and the min, I should initialize every time,

57
00:03:31,050 --> 00:03:32,610
whenever I'm selecting a minimum.

58
00:03:32,610 --> 00:03:33,630
So let us do it here.

59
00:03:33,640 --> 00:03:38,250
This is the right place for setting minimum value that is minimum as infinity.

60
00:03:38,940 --> 00:03:43,050
Inside this loop I will find out minimum if cost of an edge.

61
00:03:43,050 --> 00:03:49,470
So that is inside Uhry that is edges and inside third row.

62
00:03:49,650 --> 00:03:51,510
So that is indexed to the zeros.

63
00:03:51,520 --> 00:03:56,880
The first right one is the second, but also so to the third row and column s g.

64
00:03:56,880 --> 00:03:58,530
Whatever the G value we are scanning.

65
00:03:58,860 --> 00:04:02,610
If that is less than minimum then let us take you also.

66
00:04:02,610 --> 00:04:04,020
That is for First Vertex.

67
00:04:04,020 --> 00:04:11,880
We will take it from the edge first and second vertex in you and the so that is edges of that is force

68
00:04:11,880 --> 00:04:23,370
edge first vertex is in zero four zero of G and then V is inside edges of one of G whatever the minimum

69
00:04:23,370 --> 00:04:24,000
that we are getting.

70
00:04:24,270 --> 00:04:30,720
And also we should record this one, whichever is giving minimum levels recorded in key, because once

71
00:04:30,720 --> 00:04:35,270
that edge is included, we are going to make it as one means, once we have considered it, whether

72
00:04:35,280 --> 00:04:39,030
it is included in the solution or not, we are going to market as one No.

73
00:04:39,030 --> 00:04:40,560
One zero said this for loop.

74
00:04:40,950 --> 00:04:42,700
We have a minimum edge.

75
00:04:43,080 --> 00:04:45,840
Now, the question is whether we should include it in the solution or not.

76
00:04:45,840 --> 00:04:49,380
So for that we have to check whether it is forming a cycle or not.

77
00:04:49,380 --> 00:04:56,940
So for that we will call a fine function, find that set function, find you as well as we will find

78
00:04:56,940 --> 00:04:57,270
out.

79
00:04:57,270 --> 00:04:59,220
We both of values will find out.

80
00:04:59,550 --> 00:04:59,900
And the.

81
00:05:00,090 --> 00:05:06,330
Values should not be equal if they're equal means they already belong to the same sex or by use, including

82
00:05:06,330 --> 00:05:07,750
this age, it will form a cycle.

83
00:05:08,160 --> 00:05:10,930
So if they are not equal, then we will include in the solution.

84
00:05:11,430 --> 00:05:21,420
So the solution is a T that is binding treaty of zero of I assign you and that deal for one of I as

85
00:05:21,420 --> 00:05:28,680
a sign that v as we have included this edge, then we should perform union on this Fonso Union.

86
00:05:28,680 --> 00:05:30,450
I have named it as my union.

87
00:05:31,020 --> 00:05:39,120
OK, this is my union function and in this I should find a find of you and find also V..

88
00:05:39,120 --> 00:05:40,680
I should join the parents.

89
00:05:40,680 --> 00:05:40,890
Right.

90
00:05:40,920 --> 00:05:44,820
I should not just the words as I should find join their parents.

91
00:05:44,820 --> 00:05:51,300
So I have given kind of you and kind of the the support from union and also we should move on to next,

92
00:05:51,300 --> 00:05:54,990
i.e. because we have got one edge in the spanning three.

93
00:05:54,990 --> 00:05:57,090
We got the result right inside that is it.

94
00:05:58,290 --> 00:06:00,240
So one edge is included in the solution.

95
00:06:00,240 --> 00:06:05,880
So move to the next index for the next edge then and then outside this.

96
00:06:05,880 --> 00:06:14,340
If any of this we have already completed so included should be made as included of K should be made

97
00:06:14,340 --> 00:06:14,880
as one.

98
00:06:15,150 --> 00:06:15,470
Right.

99
00:06:15,750 --> 00:06:17,670
So here one more thing I missed here.

100
00:06:18,000 --> 00:06:24,650
Whatever that you are taking, it should be included of that one should be equal to zero.

101
00:06:24,750 --> 00:06:26,330
Right then only consider that one.

102
00:06:26,340 --> 00:06:32,400
Otherwise don't make that attachments don't repeatedly take the same and just as a minimum wage, otherwise

103
00:06:32,400 --> 00:06:34,830
it will always be finding the same are just minimum wage.

104
00:06:35,160 --> 00:06:37,200
So if included is zero then only take it.

105
00:06:37,410 --> 00:06:42,360
And once it is done, once you have only the minimum, then at the end you make it as one.

106
00:06:43,140 --> 00:06:44,940
So this is the entire way loop.

107
00:06:44,940 --> 00:06:49,170
So does the simplest code of cross-class algorithm.

108
00:06:49,920 --> 00:06:51,480
That's all we will get.

109
00:06:51,480 --> 00:06:54,000
All minimum cost are just included in the solution.

110
00:06:54,420 --> 00:06:56,820
Now, once it is done, we have a solution that does so.

111
00:06:56,820 --> 00:07:04,830
Let us traverse to this solution and display all the edges, that is all the edges that are included

112
00:07:04,830 --> 00:07:05,470
in the spanning.

113
00:07:05,730 --> 00:07:06,900
So we have added minus one.

114
00:07:06,900 --> 00:07:08,430
I just I will paint all of them.

115
00:07:08,430 --> 00:07:18,420
So I you see out here and they will be split among other pair that is T of zero comma i.e. and comma

116
00:07:18,510 --> 00:07:22,500
next to comma then D of one comma.

117
00:07:23,250 --> 00:07:31,170
I then close the bracket for the bracket then after that and then in a new line.

118
00:07:34,330 --> 00:07:42,430
That's all so that's all over this algorithm for preschool, let us run it, and if there are any errors,

119
00:07:42,430 --> 00:07:43,270
I will remove them.

120
00:07:43,270 --> 00:07:49,870
If any logical or syntax errors, let us remove them here or there, because that is a party that is

121
00:07:49,870 --> 00:07:51,630
declared after the function call.

122
00:07:51,640 --> 00:07:53,100
So let us change this one.

123
00:07:53,500 --> 00:07:56,320
I'm hoping this one cuts from here.

124
00:07:56,710 --> 00:08:01,500
I should declare it before the function calls because the study is not visible for them.

125
00:08:02,320 --> 00:08:05,080
So I have declared that in the beginning.

126
00:08:05,080 --> 00:08:06,130
Let us run it now.

127
00:08:07,520 --> 00:08:09,160
OK, are there any more errors?

128
00:08:09,970 --> 00:08:10,300
Yes.

129
00:08:10,300 --> 00:08:11,380
Semicolon is missing.

130
00:08:12,220 --> 00:08:12,790
Let us see.

131
00:08:13,920 --> 00:08:15,160
We are successful.

132
00:08:23,910 --> 00:08:28,260
Yeah, one six, three, four to seven and two, three, four, five, five, six.

133
00:08:28,290 --> 00:08:30,180
This was the result we have seen on Whitewall.

134
00:08:30,480 --> 00:08:31,590
So the same another record.

135
00:08:31,590 --> 00:08:35,840
And you can see the order in which they're just adding to that fosters one six, then three four.

136
00:08:35,850 --> 00:08:41,280
That is the next minimum eight discoursed of these are just five and the cost of the surge is eight.

137
00:08:41,610 --> 00:08:45,960
And I guess this is 10 and this is 12 something.

138
00:08:46,260 --> 00:08:48,000
OK, and this is.

139
00:08:50,190 --> 00:08:57,340
16 and then this is 20, so these are all there are included in the solution, so we got the result.

140
00:08:58,590 --> 00:08:59,900
So that's all in this video.

141
00:08:59,910 --> 00:09:03,810
You can try this program by yourself if you do it by yourself once.

142
00:09:03,860 --> 00:09:05,640
Don't try to just for an examination.

143
00:09:05,640 --> 00:09:08,850
If you are preparing, does it don't make it up good by yourself.

144
00:09:08,850 --> 00:09:11,240
And if there are any variations possible, you can do that.

145
00:09:11,790 --> 00:09:14,250
You can take a data structure in different forms.

146
00:09:14,760 --> 00:09:17,550
Like I have taken this ad just in the form of rules.

147
00:09:17,550 --> 00:09:21,010
You can take them in the form of columns or you can use the structures.

148
00:09:21,030 --> 00:09:25,510
There are many possibilities or in places you can make it as a class edge, as a class.

149
00:09:25,510 --> 00:09:26,880
So a lot of things you can do.

150
00:09:27,330 --> 00:09:30,750
So you can try this program and write the program by yourself.

151
00:09:30,970 --> 00:09:33,380
And there is a PDF available along with this video.

152
00:09:34,110 --> 00:09:35,340
So that's all in this video.

