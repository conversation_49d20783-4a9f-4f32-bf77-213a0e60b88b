1
00:00:00,670 --> 00:00:10,270
In this video, we look at a demonstration for C++ class for Q using every so confusing array in C++.

2
00:00:11,490 --> 00:00:19,500
I'll call this project Annemasse Que Sepi and a language type I will select as C++.

3
00:00:21,440 --> 00:00:23,600
Create the Project Project Authority.

4
00:00:24,880 --> 00:00:30,760
Yeah, so let us right on the program now, let us try to class, so first of all, I will say using

5
00:00:31,270 --> 00:00:36,030
namespace study so that I can you see <PERSON> out very, very quick.

6
00:00:36,790 --> 00:00:41,110
Now, I've learned the class for you as it is a class.

7
00:00:41,110 --> 00:00:43,180
I should have private data member.

8
00:00:43,180 --> 00:00:48,670
So in private members, I should have a front pointer and a real pointer.

9
00:00:49,740 --> 00:00:58,550
And the size of a Q and also I need a pointer for Q for storing the elements, so for an area have taken

10
00:00:58,560 --> 00:00:58,940
a point.

11
00:01:00,020 --> 00:01:04,940
Then public functions, I should have first of all, I should have a constructor, so for that I will

12
00:01:04,940 --> 00:01:12,130
take a non parameter constructor, which will initialize and assign are assigned minus one.

13
00:01:13,010 --> 00:01:17,110
And next thing, let us say default size is ten.

14
00:01:17,120 --> 00:01:21,410
So I will excise ten then you should be initialize.

15
00:01:21,430 --> 00:01:26,740
So Ariki was created with no end of size, whatever the sizes.

16
00:01:27,590 --> 00:01:32,120
So I have written everything in a single line so that you can see the complete class at once next to

17
00:01:32,120 --> 00:01:33,710
the parametrized constructor.

18
00:01:33,710 --> 00:01:41,060
We should take the size, so let us take a bottom interface and from here I will copy everything and

19
00:01:41,060 --> 00:01:42,520
I'll modify them accordingly.

20
00:01:44,030 --> 00:01:52,110
So Fernleigh Gold already goes to minus one, then sizes not and now the sizes size only as this is

21
00:01:52,220 --> 00:01:52,790
ambiguity.

22
00:01:52,800 --> 00:01:59,330
So I should say this size and here also and creating an area, I should say, this size.

23
00:01:59,340 --> 00:02:06,350
So if you have already watched this C++ class for Stack, so you know that how I'm writing and everything

24
00:02:06,350 --> 00:02:10,490
in a single line, then I should have a function for NQ.

25
00:02:10,509 --> 00:02:18,710
Does a member function for entry, which will take an element then I should have a function for dequeue.

26
00:02:21,450 --> 00:02:26,220
Which will delete an element then I should have a functional display for displaying a.

27
00:02:27,820 --> 00:02:33,860
That's all all the members I have defined now I will implement these methods outside the class, if

28
00:02:33,860 --> 00:02:38,030
you want more functions, like you want to know the first element or the last element, you can add

29
00:02:38,030 --> 00:02:39,620
more function is empty is a.

30
00:02:40,370 --> 00:02:43,450
So as you like, you can add more function to this data structure.

31
00:02:43,470 --> 00:02:46,640
You must make sure that it's working on fee for.

32
00:02:47,870 --> 00:02:53,960
Then outside the class, I will implement other functions for Fosters NQ, so first of all, a class

33
00:02:53,960 --> 00:03:00,740
named in the school resolution, then NQ, which takes a parameter X, no other you know, the procedure.

34
00:03:00,740 --> 00:03:03,350
And what I have to do, I have to check for Keuchel or not.

35
00:03:03,740 --> 00:03:07,760
If there is equals to size minus one then.

36
00:03:08,700 --> 00:03:09,450
BNF.

37
00:03:10,400 --> 00:03:11,630
You full.

38
00:03:14,440 --> 00:03:24,670
Otherwise, I should move a real plus plus then due of rare, a sign that the value that they want to

39
00:03:24,670 --> 00:03:27,490
insult the Zankou.

40
00:03:29,250 --> 00:03:35,520
Then for dequeue forces, a class named Scott Resolution then function NamUs dequeue.

41
00:03:37,150 --> 00:03:45,520
And before the election, I should check in front as equals to rare if Sowden cures empty, so I should

42
00:03:45,520 --> 00:03:50,650
give a message, cure's empty and slashing, stop it.

43
00:03:51,430 --> 00:03:56,200
Otherwise I can delete an element so far that I need a temporary variable.

44
00:03:56,200 --> 00:03:59,830
So integer X assigned minus one as we were doing it.

45
00:04:02,020 --> 00:04:02,740
Then here.

46
00:04:04,340 --> 00:04:06,830
For the nation, I should say, it's two of.

47
00:04:07,910 --> 00:04:14,840
Front plus one, I should take the element first, then thrown plus plus second increments, and then

48
00:04:14,840 --> 00:04:17,240
afterwards return an element X.

49
00:04:20,260 --> 00:04:25,060
That display function plus name and the function name is display.

50
00:04:26,360 --> 00:04:35,390
And I have to use a loop for displaying this for integer, I assign front plus one, and as long as

51
00:04:35,390 --> 00:04:42,130
I use less than equal to Rare and I plus plus then every time print the elements.

52
00:04:42,130 --> 00:04:47,630
So person tildy with the space I should print an element from Q off I.

53
00:04:49,580 --> 00:04:56,800
The end, I should give a new line for better output, so I have given a new line, NetSol, the complete

54
00:04:56,800 --> 00:04:59,280
classes ready, you can call this class by yourself.

55
00:05:00,610 --> 00:05:06,280
Now, here, inside the main function, I will create a two object queue of size five.

56
00:05:07,220 --> 00:05:16,460
Then you got into I will insert some element, like I said, and then I'll copy this and use the same

57
00:05:16,460 --> 00:05:17,180
code for.

58
00:05:18,680 --> 00:05:24,260
Inserting more elements, then I will say Coudert display.

59
00:05:26,550 --> 00:05:29,280
This is trendy and this is 30 Randers.

60
00:05:30,330 --> 00:05:33,960
Yes, it is displaying ten, twenty, thirty three elements are displayed.

61
00:05:36,040 --> 00:05:40,730
I guess this is sufficient, so you can read on the glass and you can test all the functions by yourself.

62
00:05:41,110 --> 00:05:43,900
I have tested only and display function.

63
00:05:45,470 --> 00:05:50,720
And one more thing, this glass can be converted using a template, you can make it as a generic glass.

64
00:05:50,720 --> 00:05:55,130
Already I have shown you quite a few times how to convert a glass into a generic glass.

