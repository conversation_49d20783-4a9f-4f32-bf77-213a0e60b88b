1
00:00:00,440 --> 00:00:07,700
Let us look at algorithm for selection thought so if you have already seen selection start in each pass,

2
00:00:07,700 --> 00:00:13,850
it will select a particular position or index and find out the smallest element that should fit in that

3
00:00:13,850 --> 00:00:14,390
index.

4
00:00:14,790 --> 00:00:17,650
So for writing an algorithm, I have picked an example.

5
00:00:17,690 --> 00:00:22,040
We will trace the algorithm here and simultaneously I will write on the instructions there.

6
00:00:22,670 --> 00:00:25,240
So here you have a number of elements that are 10.

7
00:00:25,940 --> 00:00:31,610
Now let start see foreign elements as we need and minus one passes in each passing will select a particular

8
00:00:31,610 --> 00:00:36,080
index so far that we have taken, i.e. that will be selecting an index.

9
00:00:36,320 --> 00:00:39,050
First time will find the smallest element for this index zero.

10
00:00:39,050 --> 00:00:41,180
The next one, the next four, three, so on.

11
00:00:41,480 --> 00:00:43,710
So we have to do it four and minus one time.

12
00:00:43,970 --> 00:00:45,670
So this is done using for loop.

13
00:00:45,720 --> 00:00:49,030
So this is four passes, so I will write to four loopier.

14
00:00:50,060 --> 00:00:52,910
This all of four passes for assigned Z.

15
00:00:52,910 --> 00:00:54,930
Always less than ten minus one plus plus.

16
00:00:54,950 --> 00:01:01,070
So this will be starting from zero and continuing not being more, two more integer pointers that are

17
00:01:01,280 --> 00:01:06,890
key and that should also start from here and should be initialized.

18
00:01:06,890 --> 00:01:13,850
That I and this a G should scan through the list of all the elements up to N to find out a smaller element

19
00:01:14,120 --> 00:01:17,870
if any smaller element is found in case brought to that location.

20
00:01:18,170 --> 00:01:24,220
So far as getting to all these elements to find a smaller element, I have to use again of for look

21
00:01:24,470 --> 00:01:25,670
for G also.

22
00:01:25,850 --> 00:01:32,150
So I will write on one more follow look for G, C for G and keyboard that is starting from like on the

23
00:01:32,150 --> 00:01:38,000
G incrementing every time and it's going to elements less than N so it will be stopping at nine.

24
00:01:38,030 --> 00:01:41,120
Now in this loop I have to check for the element.

25
00:01:41,360 --> 00:01:48,800
If the element wherever J is pointing suppose to GS right now here I have to check if this element is

26
00:01:48,800 --> 00:01:51,020
a smaller than their case pointing.

27
00:01:51,020 --> 00:01:53,150
If so K should be brought that G.

28
00:01:53,540 --> 00:01:54,950
So that's what I mean right here.

29
00:01:55,430 --> 00:02:02,020
If ALJ is less than half then bring K upon G make a key also point there.

30
00:02:02,270 --> 00:02:04,310
So K will also move to this location.

31
00:02:05,970 --> 00:02:12,700
So this process should continue, and once this fall ends, Kate will be pointing on the smallest element.

32
00:02:12,990 --> 00:02:18,090
So right now, another example gave a point on this, the smallest element.

33
00:02:18,090 --> 00:02:18,440
Right.

34
00:02:18,720 --> 00:02:20,630
And the GAO has finished early.

35
00:02:21,000 --> 00:02:26,730
So this is the smallest elements of this element should be swap the element that I so I will simply

36
00:02:26,730 --> 00:02:35,310
write a function swap here, swap with a key ally in the swap after this volume that's on this the end

37
00:02:35,310 --> 00:02:37,690
of this volume and end of this function.

38
00:02:38,100 --> 00:02:40,980
So that's solves the algorithm for selection for.

