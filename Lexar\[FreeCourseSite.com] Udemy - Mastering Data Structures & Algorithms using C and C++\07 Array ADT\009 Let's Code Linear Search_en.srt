1
00:00:00,740 --> 00:00:07,939
And this video, we will implement linear surge and also improvement of linear surge by applying various

2
00:00:07,939 --> 00:00:08,570
methods.

3
00:00:11,190 --> 00:00:17,460
So already I have an array here with a few values that are five values I'm having let us use in values

4
00:00:17,460 --> 00:00:18,230
for the program.

5
00:00:18,240 --> 00:00:19,800
So here are the main function.

6
00:00:19,800 --> 00:00:22,140
I will write a function for linear such.

7
00:00:24,270 --> 00:00:31,740
Linear search should search for an element and return its index, so function NamUs Linear Search.

8
00:00:33,420 --> 00:00:35,790
It should take an uhry, so.

9
00:00:37,770 --> 00:00:45,060
Struck, they are still not passing it by address because just by values enough because it has to only

10
00:00:45,060 --> 00:00:48,810
search for the value then the key that we want to search.

11
00:00:50,400 --> 00:00:51,000
That's all.

12
00:00:52,240 --> 00:00:58,240
Now, the program is very simple, I need a variable and I am using for loop, we can check for the

13
00:00:58,240 --> 00:01:03,610
element I assign zero and I is less than lente.

14
00:01:03,700 --> 00:01:05,480
So error total.

15
00:01:05,500 --> 00:01:10,290
And I can see because this is coming polymer value, not Culburra.

16
00:01:10,330 --> 00:01:14,110
So I don't have to use arole then I placeless.

17
00:01:15,330 --> 00:01:22,950
Every time I should check whether that key given key is equal to element in an array at index, i.e.

18
00:01:23,820 --> 00:01:26,520
if it is found, then written Index I.

19
00:01:28,100 --> 00:01:34,430
Otherwise, after the fall, we can create an index minus one, seeing that element is not found.

20
00:01:34,910 --> 00:01:36,550
So logic is very simple.

21
00:01:36,560 --> 00:01:39,070
We have already seen this not here.

22
00:01:39,080 --> 00:01:43,820
I will call linear search function and the result I will directly predict.

23
00:01:43,850 --> 00:01:47,840
So I will use printf percentile slash and.

24
00:01:48,890 --> 00:01:52,610
And I will Paul, Leanyer search function.

25
00:01:55,270 --> 00:01:59,530
Parameters are uhry, and the key value that I want to search is for.

26
00:02:01,250 --> 00:02:03,870
Now, formants I should get the index has to run.

27
00:02:06,820 --> 00:02:11,080
Yes, indexes took the keys following that index to.

28
00:02:15,480 --> 00:02:17,760
Next, I will search for six.

29
00:02:21,690 --> 00:02:24,120
Yes, he's found at Anex for.

30
00:02:25,910 --> 00:02:30,320
Let us give the value 15, it is not their index is minus one.

31
00:02:31,510 --> 00:02:33,160
This element is not found.

32
00:02:35,220 --> 00:02:41,460
So that's the function for Línea, such we have also seen how to improve linear surge by using transposition

33
00:02:41,460 --> 00:02:42,580
or move to front.

34
00:02:43,170 --> 00:02:44,730
So let us adopt those techniques.

35
00:02:44,740 --> 00:02:50,110
So in that just we will swap an element with the previous element or with the first element.

36
00:02:50,700 --> 00:02:57,350
So for that, I will write one function called swap, which will swap two elements of type integers.

37
00:02:57,390 --> 00:03:03,090
So first, this Pointer X and second is Pointer Y, I will die dysfunction.

38
00:03:03,090 --> 00:03:05,760
So wherever I need to swap the elements, I'll use this one.

39
00:03:06,870 --> 00:03:12,120
So a temporary variable, then temporary variable assigned the value from X.

40
00:03:13,470 --> 00:03:22,440
And the value of access modified by value of Y and value of Y will change to EMK next all these three

41
00:03:22,440 --> 00:03:24,290
lines for sublingual events.

42
00:03:24,300 --> 00:03:25,890
I'll be using it whenever required.

43
00:03:25,920 --> 00:03:27,090
This is called Berglas.

44
00:03:29,020 --> 00:03:34,420
No, let us improve linear search for improving linear search, linear search is going to modify this

45
00:03:34,420 --> 00:03:35,350
actual area.

46
00:03:35,740 --> 00:03:39,700
So for modifying this, actually this should be addressed.

47
00:03:39,710 --> 00:03:41,070
So it should be a pointer.

48
00:03:41,350 --> 00:03:44,320
And here while calling, it should be an address.

49
00:03:45,800 --> 00:03:49,880
Then when I'm accessing the members, I should use Atle instead of DOT.

50
00:03:52,160 --> 00:03:55,860
Now, these changes I have made so that it can modify the actual URTE.

51
00:03:58,810 --> 00:04:05,920
Now, before returning the element, we should swap the element, so first of all, let us see transpositions

52
00:04:05,950 --> 00:04:09,900
or transpositional subelement with the previous element.

53
00:04:10,210 --> 00:04:13,660
So let us call our function here by passing.

54
00:04:15,010 --> 00:04:15,880
Address of.

55
00:04:17,720 --> 00:04:18,680
If I.

56
00:04:22,510 --> 00:04:32,130
That address of AOF I minus one, so it will interchange the element found at andexanet element that

57
00:04:32,140 --> 00:04:33,100
I minus one.

58
00:04:35,450 --> 00:04:41,900
So here I will debate the function, then we can clearly see how it will perform transposition and I

59
00:04:41,900 --> 00:04:46,230
will search for a key element that is five searching for five.

60
00:04:46,580 --> 00:04:48,830
So five should be the with for.

61
00:04:50,090 --> 00:04:50,870
Let us run.

62
00:04:52,200 --> 00:04:55,260
I'll put a break point here on PRINTF and Ron.

63
00:04:58,520 --> 00:05:00,950
It has dropped by the function, let us continue.

64
00:05:02,190 --> 00:05:03,720
Naidus entered into.

65
00:05:05,000 --> 00:05:05,900
Linear search.

66
00:05:05,930 --> 00:05:10,640
Let me expand this at the searching for the key is going on the next index.

67
00:05:13,100 --> 00:05:19,880
The next index I used to know at the bottom, you can see, is three, then the animal is found sort

68
00:05:19,880 --> 00:05:21,110
of swapping the elements.

69
00:05:22,850 --> 00:05:27,170
Back to dysfunction now you can see that four and five has interchangeable.

70
00:05:27,410 --> 00:05:28,340
So I became.

71
00:05:29,420 --> 00:05:37,220
So if I came before for so the swapping is done and return the element, so that's all back to the main

72
00:05:37,220 --> 00:05:37,820
function.

73
00:05:40,560 --> 00:05:49,100
I'll remove the break point and continue normal execution index, the street and the elements are changed,

74
00:05:49,110 --> 00:05:49,750
modified.

75
00:05:49,770 --> 00:05:51,570
You can see that the elements are modified.

76
00:05:51,570 --> 00:05:53,190
Two, three, five, four, six.

77
00:05:53,240 --> 00:05:53,540
Now.

78
00:05:54,540 --> 00:05:56,820
Now, next, we will see move to head.

79
00:05:56,910 --> 00:06:00,230
So I have to just make a small change in this linear sales function.

80
00:06:00,540 --> 00:06:05,000
I have to say swap of Ivied of zero.

81
00:06:05,220 --> 00:06:06,270
Just say zero.

82
00:06:06,450 --> 00:06:09,110
Daleville element will be brought at index zero.

83
00:06:09,300 --> 00:06:11,480
Let us run and see Fischer.

84
00:06:11,490 --> 00:06:14,830
We brought that index is zero and two percent in place of five.

85
00:06:14,850 --> 00:06:19,080
Yes, you can see this change in the list of elements.

86
00:06:22,970 --> 00:06:24,920
So that's all about the search.

