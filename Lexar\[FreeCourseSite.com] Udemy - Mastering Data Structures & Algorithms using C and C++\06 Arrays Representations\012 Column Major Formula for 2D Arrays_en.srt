1
00:00:00,330 --> 00:00:06,680
For color, major mapping, the elements of this two dimensional array are mapped on a single dimensionality

2
00:00:07,050 --> 00:00:10,760
column by column, so I will fill these elements column by column.

3
00:00:11,250 --> 00:00:15,310
These elements will the first column are zero zero one zero and two zero.

4
00:00:15,660 --> 00:00:18,690
So first column elements are eight zero zero.

5
00:00:19,940 --> 00:00:20,780
One zero.

6
00:00:23,090 --> 00:00:32,810
This is Golomb Index Zettl, then this is Collum Index one, these elements, these element, so I finish

7
00:00:32,810 --> 00:00:42,430
with these elements now these elements, the next column of zero two, this is column two, then eight

8
00:00:42,500 --> 00:00:44,810
of this column three.

9
00:00:46,450 --> 00:00:50,530
So the elements of Two-dimensional, <PERSON>, are, ma'am, the column, my column.

10
00:00:52,360 --> 00:00:59,920
Now we need a formula for obtaining an address of any element, so this address I have taken there,

11
00:00:59,930 --> 00:01:03,730
this is starting from 200 and as integer, every integer takes two to white.

12
00:01:03,760 --> 00:01:07,870
So this is 200 and do not want to not point to not three to not four and five.

13
00:01:07,870 --> 00:01:08,320
So on.

14
00:01:08,860 --> 00:01:09,610
Then let us call.

15
00:01:09,620 --> 00:01:13,290
This is starting a process and not that is bishoprics location.

16
00:01:15,550 --> 00:01:19,780
Now let us take some example element from there and frame a formula.

17
00:01:21,440 --> 00:01:29,720
So I want this element one comma to see where this one comes to here in column number two.

18
00:01:30,320 --> 00:01:43,820
So address of it of one comma to this is this address that is 200 plus not before reaching the settlement.

19
00:01:43,820 --> 00:01:47,150
I should skip first column and the second column, two columns I should skip.

20
00:01:47,480 --> 00:01:50,650
So to include how many elements are there in each column.

21
00:01:50,660 --> 00:01:52,220
One, two, three, one, two, three.

22
00:01:52,520 --> 00:01:54,170
So columns are having three elements.

23
00:01:54,170 --> 00:01:57,190
So that is equal to the number of rules and that is three.

24
00:01:57,860 --> 00:02:02,310
So three then plus one five skip column zero.

25
00:02:02,360 --> 00:02:03,020
I'm here.

26
00:02:03,200 --> 00:02:04,640
Then I have skip column one.

27
00:02:04,640 --> 00:02:06,270
I'm here now from here.

28
00:02:06,290 --> 00:02:07,130
How much I should move.

29
00:02:07,130 --> 00:02:09,050
Just one element, one element.

30
00:02:09,350 --> 00:02:11,860
So you can see that this has to come on.

31
00:02:12,110 --> 00:02:19,370
So this is to come along and this has to be multiplied with a number of elements that are w so this

32
00:02:19,370 --> 00:02:23,620
will be three to six plus one, seven and 14.

33
00:02:23,630 --> 00:02:25,340
So this is two hundred and fourteen.

34
00:02:25,640 --> 00:02:29,810
And you can see that the index of of one comment was two hundred and fourteen.

35
00:02:30,200 --> 00:02:34,170
So that's all we got the address of a given element.

36
00:02:34,610 --> 00:02:38,150
Now let's take one more element that is aof to commit to.

37
00:02:39,280 --> 00:02:46,440
Now let's take one more element that is aof one committee, one commentary's here so how to get the

38
00:02:46,760 --> 00:02:47,310
of that one.

39
00:02:47,840 --> 00:02:54,070
So the address of a of one comma three is 200 plus.

40
00:02:54,890 --> 00:02:58,900
Now I have to skip how many columns I should go into a column.

41
00:02:59,180 --> 00:03:01,640
So skip zero column, first column and second column.

42
00:03:01,650 --> 00:03:04,460
So I have to skip the three columns and in each column.

43
00:03:04,460 --> 00:03:05,560
How many elements are there.

44
00:03:05,960 --> 00:03:08,060
These are three elements.

45
00:03:08,300 --> 00:03:12,820
So this is three and two, three plus not one side each year.

46
00:03:12,830 --> 00:03:14,330
How many elements I should move forward?

47
00:03:14,330 --> 00:03:15,280
Just one element.

48
00:03:15,740 --> 00:03:21,640
So this is multiplied by two and this is three to nine plus one and two.

49
00:03:21,950 --> 00:03:25,160
That is twenty two hundred and twenty two hundred and twenty.

50
00:03:25,450 --> 00:03:27,400
The status is 217.

51
00:03:27,530 --> 00:03:28,490
The element is here.

52
00:03:29,780 --> 00:03:31,880
This is of commentary is here.

53
00:03:33,710 --> 00:03:42,230
So now we have two examples, we can prepare a formula, so let us prepare a formula for any index of

54
00:03:42,860 --> 00:03:44,210
I g.

55
00:03:46,540 --> 00:03:51,520
This is location zero, that is visitors and not plus.

56
00:03:53,110 --> 00:04:00,240
The three and one or two and one three, two, one three one are used, so this means faster.

57
00:04:00,720 --> 00:04:05,130
Then I saw this object into a number of elements in the room.

58
00:04:05,280 --> 00:04:06,150
That is three.

59
00:04:07,680 --> 00:04:14,600
Number of elements in the column that is equal to number of rules three, so this is M plus one is taken

60
00:04:14,600 --> 00:04:15,200
at the last.

61
00:04:15,230 --> 00:04:16,339
So this is I.

62
00:04:16,519 --> 00:04:18,170
That is rule number.

63
00:04:19,550 --> 00:04:21,110
This is multiplied by revenue.

64
00:04:21,980 --> 00:04:26,820
So this is the formula for column media representation or column media mapping.

65
00:04:27,350 --> 00:04:33,530
I will write on Romijn mapping formula also just below this fund so that we can see the difference between

66
00:04:33,530 --> 00:04:33,840
them.

67
00:04:34,220 --> 00:04:47,240
This was the formula El Norte plus I into N plus G multiplied by W, so in Romelio I was used first,

68
00:04:47,240 --> 00:04:50,350
then G, but in columns judges used first.

69
00:04:50,360 --> 00:04:57,650
Then I say, you can say that in the measure here we go from left to right and in column measure we

70
00:04:57,650 --> 00:04:59,340
go from right to left.

71
00:04:59,370 --> 00:05:05,000
So faster than I saw first g then I n roll measure first then G.

72
00:05:05,270 --> 00:05:09,500
So this is for real measure and this is for column.

73
00:05:09,660 --> 00:05:12,560
You know, let is observe one more thing here.

74
00:05:12,950 --> 00:05:17,120
See these two formulas, how many operations they are having.

75
00:05:17,120 --> 00:05:21,130
Plus, plus multiply multiplied plus plus multiply multiplied.

76
00:05:21,470 --> 00:05:25,310
So both the formulas are having the same number of operations.

77
00:05:25,320 --> 00:05:29,420
So in terms of time, both are equally efficient.

78
00:05:29,690 --> 00:05:35,290
So a compiler may follow Romijn mapping or it may follow columns on mapping.

79
00:05:35,630 --> 00:05:40,330
So if you are designing your own compiler, then you have option to select anyone.

80
00:05:40,340 --> 00:05:42,140
Both are equally efficient.

81
00:05:42,170 --> 00:05:43,400
There is no difference in them.

82
00:05:44,530 --> 00:05:52,890
We can't say one formula is better than another in any situation, but C C++ follows Romelio formula,

83
00:05:53,050 --> 00:05:55,600
this one, the four major formula.

84
00:05:58,660 --> 00:06:00,880
So that's all about to them.

85
00:06:01,410 --> 00:06:08,890
So so that's all about two dimensionality we have discussed how the media and the media mapping is done

86
00:06:09,130 --> 00:06:12,630
and we have seen the formula for index starting from zero.

87
00:06:12,640 --> 00:06:17,470
And also we have seen the formula for before, starting from one also for Romijn.

88
00:06:17,590 --> 00:06:20,410
Now for columns that you can devise the formula.

89
00:06:20,710 --> 00:06:25,090
So you know that only a minus one and minus one minus one has to be taken.

90
00:06:25,100 --> 00:06:27,040
Then we get the formula for Indy.

91
00:06:27,160 --> 00:06:28,580
So starting from one onwards.

92
00:06:28,810 --> 00:06:30,150
So that's all in this topic.

