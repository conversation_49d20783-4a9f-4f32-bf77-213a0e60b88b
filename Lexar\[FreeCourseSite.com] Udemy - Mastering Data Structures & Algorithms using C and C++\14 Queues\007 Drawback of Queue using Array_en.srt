1
00:00:00,270 --> 00:00:04,720
Our previous topic was implementation of using two points we have used front and rear.

2
00:00:05,100 --> 00:00:06,910
There are some drawbacks in that matter.

3
00:00:06,930 --> 00:00:10,500
So we learn more of those drawbacks and also we'll find some solution.

4
00:00:10,540 --> 00:00:14,790
So there are two types of solutions that is resetting pointers and circular.

5
00:00:14,790 --> 00:00:21,510
Q So in this we will discuss what are the drawbacks and the solutions, how to remove those drawbacks?

6
00:00:22,200 --> 00:00:23,400
Let us look at drawback.

7
00:00:24,000 --> 00:00:26,310
Here is a queue of size seven.

8
00:00:26,310 --> 00:00:32,250
I have an array of size seven and already it is filled with some elements already responding at six

9
00:00:32,250 --> 00:00:34,020
indexer that the size minus one.

10
00:00:34,350 --> 00:00:35,940
So I can see that queue is full.

11
00:00:35,970 --> 00:00:38,940
Yes, there is no space for inserting any more element.

12
00:00:39,210 --> 00:00:42,960
And this is the first elements of this morning just before that element.

13
00:00:43,650 --> 00:00:44,710
Now, what is the drawback?

14
00:00:45,090 --> 00:00:48,120
I want to insert a new element in the queue.

15
00:00:48,930 --> 00:00:55,530
So any queue operational that we saw, if I tried to insert Raila's equals to size minus one.

16
00:00:55,830 --> 00:00:57,330
I get a message full.

17
00:00:58,020 --> 00:00:59,400
I can only answer the element.

18
00:00:59,580 --> 00:01:04,560
But if you see already few elements are deleted from the Q So these spaces are free.

19
00:01:04,560 --> 00:01:05,510
These are vacant.

20
00:01:05,970 --> 00:01:11,370
I cannot use them because the insertion is done from rear end and there is no space at rear end.

21
00:01:11,730 --> 00:01:13,430
This is at the front end.

22
00:01:14,490 --> 00:01:16,600
So these are spaces we cannot utilize.

23
00:01:17,160 --> 00:01:21,900
So the first point is we cannot reuse those pieces of deleted element.

24
00:01:23,020 --> 00:01:30,250
Let me lead a few more elements and show you if I'm deleting this one and this one also and this one

25
00:01:30,250 --> 00:01:32,290
also, so front will be pointing.

26
00:01:34,120 --> 00:01:35,680
Now, so many elements are related.

27
00:01:35,980 --> 00:01:37,480
There is only one element in the queue.

28
00:01:38,080 --> 00:01:44,710
Still, I want to insert and queue so foreign queue conditions, checked rate is equal to size minus

29
00:01:44,710 --> 00:01:45,010
one.

30
00:01:45,010 --> 00:01:47,640
That is seven minus one here, six Güzel.

31
00:01:47,890 --> 00:01:50,050
I cannot in certain element though.

32
00:01:50,050 --> 00:01:51,460
There is a lot of space for free.

33
00:01:52,000 --> 00:01:53,800
So it means second point.

34
00:01:54,310 --> 00:01:56,810
Every location can be used only once.

35
00:01:56,980 --> 00:01:59,410
I cannot reuse them then third of them.

36
00:01:59,920 --> 00:02:01,750
I will delete this element also.

37
00:02:03,590 --> 00:02:10,850
A friend will point here, so front and rear are pointing at the same place, Sacu is empty for the

38
00:02:10,850 --> 00:02:20,320
second straight empty game, certainly no full because red is equal to size minus one full rate is that

39
00:02:20,340 --> 00:02:21,140
classification.

40
00:02:21,950 --> 00:02:26,540
Hotpoint is a situation where the queue is empty, also follows.

41
00:02:27,930 --> 00:02:34,920
So these are the three points we saw that we cannot reuse the pieces of the second point, we can use

42
00:02:34,920 --> 00:02:36,510
each space only one time.

43
00:02:36,750 --> 00:02:37,710
That is a similar point.

44
00:02:37,710 --> 00:02:43,500
On the third thing is there may be a situation even to the full level as empty spaces can be used only

45
00:02:43,500 --> 00:02:45,710
once we want to reuse the space.

46
00:02:46,110 --> 00:02:48,720
How do we use the space for solution?

47
00:02:48,720 --> 00:02:51,570
Is resetting point for solution?

48
00:03:01,940 --> 00:03:03,870
Solution is resetting pointers.

49
00:03:04,220 --> 00:03:12,740
So what I should do at any time, if it is becoming empty at any place, bring front and rear pointer

50
00:03:13,190 --> 00:03:19,010
at the beginning, that is, reinitialize them to minus one so that they can again start from the beginning.

51
00:03:19,460 --> 00:03:22,800
So in this way, we can reduce those places.

52
00:03:23,630 --> 00:03:26,010
Let me show you a little bit by inserting three elements.

53
00:03:26,270 --> 00:03:28,300
Suppose I have inserted three elements.

54
00:03:28,310 --> 00:03:29,630
So I have three elements.

55
00:03:29,790 --> 00:03:31,520
Now I want to believe the two elements.

56
00:03:33,080 --> 00:03:35,020
After admitting to Elements Fundación.

57
00:03:36,080 --> 00:03:41,020
I want to delete one more element when we delete one more element, the front will move to the next

58
00:03:41,020 --> 00:03:45,780
location and this element is gone and both the front and rear are that same place.

59
00:03:46,000 --> 00:03:48,600
So it is empty, so empty.

60
00:03:48,790 --> 00:03:52,810
So reset them and bring front and rear at minus one.

61
00:03:53,740 --> 00:03:55,800
This is what we will do in this setting point to.

62
00:03:57,830 --> 00:04:03,380
Not just a band anywhere in between when they were and they are becoming equal, could become somebody,

63
00:04:03,680 --> 00:04:07,640
bring them quickly on minus one so that we can reduce the places.

64
00:04:08,420 --> 00:04:12,110
But this method doesn't guarantee that that these pieces will be reused.

65
00:04:12,110 --> 00:04:18,680
Definitely, if AQ is not becoming empty at any time, then we cannot reset them.

66
00:04:18,980 --> 00:04:21,850
So and we cannot even insert more elements.

67
00:04:22,040 --> 00:04:28,220
So so this method doesn't guarantee reusing of spaces then one second solution.

68
00:04:28,610 --> 00:04:30,130
So let us look at Circular Quay.

