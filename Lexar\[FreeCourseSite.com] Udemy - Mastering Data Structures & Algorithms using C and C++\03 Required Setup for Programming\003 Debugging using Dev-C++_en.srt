1
00:00:00,210 --> 00:00:04,860
In this video, we will see how to use a debugger and C++.

2
00:00:06,180 --> 00:00:13,650
Debugger is used for tracing the program line by line, we can watch the program and see how it is executing

3
00:00:13,650 --> 00:00:14,490
line by line.

4
00:00:16,970 --> 00:00:24,830
Sometimes if the program is not giving expected results or it is giving wrong output, then we may check

5
00:00:24,830 --> 00:00:29,350
the program line by line to check to find out where we have gone wrong.

6
00:00:31,870 --> 00:00:37,450
We'll be learning more about the usage of debugger in the coming videos while giving the demonstration

7
00:00:37,450 --> 00:00:43,050
of programs, I have used debugger sometimes, so even you can do it in Dev C++.

8
00:00:43,570 --> 00:00:46,900
So let us see how to use a debugger so far for that.

9
00:00:47,110 --> 00:00:50,980
First of all, I will create a project and I will write one simple program.

10
00:00:51,290 --> 00:00:55,690
Then I will show you the options that you have to use for using a debugger.

11
00:00:57,210 --> 00:00:58,200
New project.

12
00:00:59,210 --> 00:01:04,099
And they canceled application and it's a C++ project, let us limit numerous.

13
00:01:06,310 --> 00:01:07,270
And say, OK.

14
00:01:11,960 --> 00:01:16,550
Now, here, the skeleton of programs, really, I'll replace it with my program.

15
00:01:18,440 --> 00:01:20,590
Yeah, here is the program right now.

16
00:01:20,630 --> 00:01:26,820
Don't go into the details of the program, this program is to find the sum of all these elements, additional

17
00:01:26,870 --> 00:01:27,670
these elements.

18
00:01:27,680 --> 00:01:29,600
That is one two five eight nine.

19
00:01:30,050 --> 00:01:32,360
So it will give the sum of all these elements.

20
00:01:32,960 --> 00:01:34,560
That's going to be 25.

21
00:01:35,190 --> 00:01:40,250
Let us first compile and build a program to go to execute and see.

22
00:01:42,530 --> 00:01:46,650
Compile and run, she's asking me to save the file.

23
00:01:46,670 --> 00:01:48,680
Yes, may not seem to be saved.

24
00:01:50,090 --> 00:01:50,840
Yes.

25
00:01:55,170 --> 00:01:57,150
So it's giving the that it's 25.

26
00:01:58,450 --> 00:02:05,500
Now, this is a normal execution I have done, not one I have compiled, so the program is ready to

27
00:02:05,500 --> 00:02:06,050
run now.

28
00:02:06,160 --> 00:02:09,220
So instead of running, I will perform debugging.

29
00:02:11,450 --> 00:02:17,600
So for debugging, first of all, we have to put a breakpoint upon any statements on the first statement

30
00:02:17,600 --> 00:02:24,510
itself and put a breakpoint so far, putting a breakpoint in C++, just click on the statement here,

31
00:02:24,530 --> 00:02:29,320
have clicked on the number right on the left hand side, leftmost corner have just clicked here.

32
00:02:29,660 --> 00:02:31,230
If you're going to click, it will be gone.

33
00:02:31,250 --> 00:02:34,910
So for toggling, you can just click and click once again.

34
00:02:34,910 --> 00:02:35,450
It's gone.

35
00:02:35,480 --> 00:02:36,700
So it's like on and off.

36
00:02:37,400 --> 00:02:38,510
So now it is on.

37
00:02:39,470 --> 00:02:39,810
Right.

38
00:02:40,220 --> 00:02:42,260
So this statement is having breakpoint.

39
00:02:42,270 --> 00:02:48,530
So when I generally will come and stop here so that someday I can carry on by executing the statements

40
00:02:48,530 --> 00:02:49,470
one by one.

41
00:02:50,510 --> 00:02:53,270
So let us start the debuggers for starting a debugger again.

42
00:02:53,270 --> 00:02:54,090
Go to execute.

43
00:02:54,440 --> 00:03:00,800
Now, this time, instead of calling run or compiler run, just go down at the in the menu options and

44
00:03:00,800 --> 00:03:11,180
select Depok so you can see that even they can do it using a five so you can even press a five for starting

45
00:03:11,180 --> 00:03:11,720
debugger.

46
00:03:13,100 --> 00:03:14,930
Now here it is at this line.

47
00:03:14,930 --> 00:03:16,760
It has not yet executed the line.

48
00:03:17,740 --> 00:03:19,910
I want to know what are the values in these variables?

49
00:03:19,910 --> 00:03:23,120
Are the variables that I have are some and A and X.

50
00:03:23,840 --> 00:03:24,970
These are some variables.

51
00:03:25,670 --> 00:03:28,950
So let us add these variables in a watch.

52
00:03:29,480 --> 00:03:33,590
So for this, I will select the sum and right click.

53
00:03:34,160 --> 00:03:36,740
And here is an option I watch.

54
00:03:37,520 --> 00:03:42,440
So I decided to watch and the some variable is zero right now and a.

55
00:03:44,540 --> 00:03:51,530
And right click, the ad watch is accurate and it is having some garbage values, it is not having these

56
00:03:51,530 --> 00:03:55,820
values one, two, five, eight, nine, because this line is not yet executed.

57
00:03:56,990 --> 00:04:01,190
And I have one more variable X, I will select this and I'd watch.

58
00:04:03,350 --> 00:04:05,950
So execute to evaluate.

59
00:04:05,960 --> 00:04:08,120
So what is the result of this?

60
00:04:08,120 --> 00:04:14,800
X is not yet known because it has not yet reached my my program has executed the last line.

61
00:04:14,810 --> 00:04:17,120
So the line we just highlighted it is here.

62
00:04:18,000 --> 00:04:23,630
Now, let us execute it step by step and see how these values in the world are changing.

63
00:04:24,030 --> 00:04:31,260
So you have to observe here this area and here I'll be executing the statements line by line and here

64
00:04:31,260 --> 00:04:32,060
there's a loop.

65
00:04:32,310 --> 00:04:37,950
So this will be repeatedly executing and the value of some will also be changing because it will be

66
00:04:37,950 --> 00:04:40,260
keep on adding the value of X into something.

67
00:04:41,010 --> 00:04:42,110
So let us see it now.

68
00:04:42,540 --> 00:04:47,270
So for executing next line here at the bottom, we have option like next line.

69
00:04:47,280 --> 00:04:50,600
So you can press this or even you can press seven.

70
00:04:50,610 --> 00:04:57,320
You can see there is a body popping up showing that for next line press F7.

71
00:04:57,990 --> 00:04:59,580
So Elbrus next line.

72
00:04:59,960 --> 00:05:01,040
It is on the next line.

73
00:05:01,620 --> 00:05:04,180
No, I'm pressing at seven for condemnation.

74
00:05:04,950 --> 00:05:10,470
Now, after this line you can see that the array values these values will be changing like it is on

75
00:05:10,470 --> 00:05:10,910
this line.

76
00:05:10,920 --> 00:05:12,100
It has not yet executed.

77
00:05:12,510 --> 00:05:18,510
So this values will be changed only a little bit on some and all of this somewhat having zero so that

78
00:05:18,510 --> 00:05:20,070
actually the value was garbage.

79
00:05:20,220 --> 00:05:23,100
Then again, zero is a sign for garbage.

80
00:05:23,100 --> 00:05:24,240
Value can be even zero.

81
00:05:24,320 --> 00:05:26,520
You can see inside each one of the value zero.

82
00:05:27,690 --> 00:05:35,010
So it has not yet executed the slight suppressive seven is now other is initialized with these values.

83
00:05:35,010 --> 00:05:39,160
Here you can see this on the left hand side and the back window.

84
00:05:39,210 --> 00:05:43,520
You can see this on this watch watching the contents of the variable site.

85
00:05:43,950 --> 00:05:47,690
Now Press F7 not ascender insightful.

86
00:05:47,700 --> 00:05:53,340
Look, you can track this blue color line now.

87
00:05:53,340 --> 00:06:00,390
Sometimes we can run an X value was one, not X games to now it will be added to some and some becomes

88
00:06:00,390 --> 00:06:00,720
three.

89
00:06:01,500 --> 00:06:04,950
Now X became five, it will be added to some and some becomes eight.

90
00:06:04,950 --> 00:06:08,790
So you can watch here some became eight and X values fine.

91
00:06:09,150 --> 00:06:11,550
So the next after five it is eight.

92
00:06:11,760 --> 00:06:15,780
So let's see, next year X became eight.

93
00:06:15,780 --> 00:06:17,820
So some should become sixteen.

94
00:06:17,820 --> 00:06:21,630
Yes, some became sixteen, then X is becoming nine.

95
00:06:21,990 --> 00:06:26,520
So then some became twenty five for end of this loop and this was printed.

96
00:06:28,270 --> 00:06:33,160
So if you see the window here, you can watch here the value 25 splinted.

97
00:06:38,340 --> 00:06:43,980
So this all we can trace, how the program is working and closely observe how the values of the variables

98
00:06:43,980 --> 00:06:49,410
are changing, and if anything unexpected found, then you can make corrections in your code.

99
00:06:50,340 --> 00:06:51,750
So that's all about debugging.

100
00:06:51,930 --> 00:06:58,500
So you remember the options that you have to go ahead and execute and start the debugger and then afterwards

101
00:06:58,500 --> 00:07:05,380
you have to just keep on pressing next line or F7 and for watching the variables, you have to select

102
00:07:05,380 --> 00:07:10,500
the variable and say add, watch, or even you can click on this add watch button and here you get into

103
00:07:10,500 --> 00:07:11,340
the variable in.

104
00:07:13,120 --> 00:07:17,980
So that's all about whether I may be showing it again in some of the programs, how to use the debugger

105
00:07:17,980 --> 00:07:19,140
for that particular program.

