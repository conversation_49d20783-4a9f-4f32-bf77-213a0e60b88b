1
00:00:00,470 --> 00:00:03,680
Let us see what is the drawback of binary search tree?

2
00:00:05,730 --> 00:00:07,170
I have a set of keys here.

3
00:00:08,400 --> 00:00:14,690
Using these keys, I have to create a binary search tree and I have one more set of keys for this.

4
00:00:14,700 --> 00:00:16,680
Also, I have to create a binary tree.

5
00:00:17,810 --> 00:00:25,490
If you observe keys 42, indeed, 30, 60, 50, then 70, so 10, 20, 30, 40, 50.

6
00:00:26,330 --> 00:00:29,720
So keys are seen, but the order is different.

7
00:00:30,680 --> 00:00:33,650
So, first of all, I will create a tree for this one also.

8
00:00:33,710 --> 00:00:36,800
That will also then we'll see what is the drawback.

9
00:00:38,030 --> 00:00:39,560
Let us talk first.

10
00:00:39,560 --> 00:00:41,950
Element 40, create a. 20.

11
00:00:41,980 --> 00:00:47,240
The smaller it comes on left side, 30 is smaller than 40 and greater than 20.

12
00:00:47,270 --> 00:00:48,610
So it comes on the right side.

13
00:00:49,280 --> 00:00:51,900
Then 60 is greater than 40 schools on the right side.

14
00:00:52,340 --> 00:00:55,670
50 is greater than 40, but less than 60 comes on left side.

15
00:00:56,120 --> 00:00:58,810
Then 10 is a smaller than 40 and smaller than 20.

16
00:00:58,820 --> 00:01:00,110
So it comes on left side.

17
00:01:00,560 --> 00:01:02,900
Then 70 is greater than 40, greater than 60.

18
00:01:02,910 --> 00:01:04,400
So it comes on the right side.

19
00:01:05,030 --> 00:01:06,080
So I got the tree.

20
00:01:06,500 --> 00:01:07,930
So this is the root of a tree.

21
00:01:08,840 --> 00:01:10,280
And what is the height of a tree?

22
00:01:10,550 --> 00:01:11,980
Zero one two.

23
00:01:12,080 --> 00:01:13,460
So the height is to.

24
00:01:17,150 --> 00:01:25,150
Next, let us pray for those keys, let us start first keys have been OK, this is road, then Brandy

25
00:01:25,160 --> 00:01:26,060
is better than this one.

26
00:01:26,210 --> 00:01:28,490
Insert on the right hand side today is greater than ten.

27
00:01:28,490 --> 00:01:30,580
And then twenty three is on the right hand side.

28
00:01:31,010 --> 00:01:32,410
Forty is great.

29
00:01:32,450 --> 00:01:34,010
And so it is on the right side.

30
00:01:34,430 --> 00:01:37,880
50 is going to get us on the right side.

31
00:01:38,270 --> 00:01:39,920
60 is greater than all these.

32
00:01:40,040 --> 00:01:43,230
It comes here and 70 is better than all these.

33
00:01:43,230 --> 00:01:44,570
So it comes on this side.

34
00:01:45,020 --> 00:01:46,640
So what is the height.

35
00:01:46,680 --> 00:01:47,090
Zero.

36
00:01:47,090 --> 00:01:50,870
One, two, three, four, five, six.

37
00:01:51,110 --> 00:01:54,230
So high gas six for the same set of keys.

38
00:01:54,650 --> 00:01:57,350
I got the string of height too.

39
00:01:57,590 --> 00:01:59,870
I got the string of height six.

40
00:02:01,190 --> 00:02:08,919
So far, seven north, these are seven north, so Heidi, Stallman's hiders log and plus one, B is

41
00:02:08,930 --> 00:02:10,650
two, minus one.

42
00:02:10,940 --> 00:02:12,680
So this log in.

43
00:02:15,820 --> 00:02:25,000
And for endnotes, hide here is in the one so it's outdraw and so height of disability, so it's logarithmic

44
00:02:25,240 --> 00:02:29,080
height of that binary search trees, Lenie and Log-in.

45
00:02:29,560 --> 00:02:31,840
Now, let's talk about Trubek.

46
00:02:32,440 --> 00:02:36,730
The height of a binary search street can be as minimum as Log-in.

47
00:02:37,520 --> 00:02:45,440
As maximum as now, it depends how you are inserting the keys, how you're inserting the keys.

48
00:02:45,470 --> 00:02:46,490
It depends on that.

49
00:02:47,150 --> 00:02:50,750
So there is no control over the height of a binary search tree.

50
00:02:51,020 --> 00:02:54,110
It all depends on order of insertion.

51
00:02:55,600 --> 00:02:57,820
Can you control order of insertion?

52
00:02:58,860 --> 00:03:00,390
No way.

53
00:03:01,260 --> 00:03:07,920
See, I am writing a program for my native forestry and that application, that program, you are going

54
00:03:07,920 --> 00:03:08,630
to use it.

55
00:03:08,850 --> 00:03:09,840
You are a user.

56
00:03:11,170 --> 00:03:11,540
Like.

57
00:03:12,670 --> 00:03:19,180
Now, how I can control you to give the values properly so that a minimum height is formed, you are

58
00:03:19,180 --> 00:03:25,000
using my application, you will be giving the keys as you have or as you are getting, then my program

59
00:03:25,000 --> 00:03:26,210
will be generating the tree.

60
00:03:26,620 --> 00:03:30,220
So if you have given the values like this, then it'll be like this.

61
00:03:31,320 --> 00:03:38,790
So it created a situation to make you understand that we cannot control the order of inflation, so

62
00:03:38,790 --> 00:03:40,820
we cannot control the height of any disaster.

63
00:03:41,580 --> 00:03:46,440
So we need some method to control the height of a binary search tree.

64
00:03:47,390 --> 00:03:50,820
So binary search tree itself should control its height.

65
00:03:51,410 --> 00:03:55,990
So that's yes, that's what we call them as Aviel trees.

66
00:03:56,060 --> 00:04:00,810
So Aviel trees are high to balance the binary search trees.

67
00:04:01,550 --> 00:04:03,620
So how they control their height.

68
00:04:03,650 --> 00:04:06,380
We will learn about the real trees in the next topic.

69
00:04:07,610 --> 00:04:14,800
So conclusion here is that higer of a binary search, trees, not logging, always it's not always logging,

70
00:04:15,200 --> 00:04:17,570
but we were assuming that it is logging every time.

71
00:04:17,570 --> 00:04:21,970
Whenever I was analyzing, I was saying, hey, this log, we are expecting this.

72
00:04:22,700 --> 00:04:27,440
So we're using binary search trees, expecting that their height will be minimum.

73
00:04:29,110 --> 00:04:33,660
But the reality is that it may not be always minimum, it can be lenient on some.

74
00:04:35,950 --> 00:04:37,940
So that's all about drawback of money.

