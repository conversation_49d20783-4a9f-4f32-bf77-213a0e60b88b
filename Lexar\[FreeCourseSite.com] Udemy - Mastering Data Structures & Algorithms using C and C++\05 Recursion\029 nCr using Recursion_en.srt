1
00:00:00,480 --> 00:00:07,110
Now, the topic is a combination formula that is also called a selection formula, if the set of objects

2
00:00:07,110 --> 00:00:14,080
are given to us, then in how many ways we can select the subset of those objects.

3
00:00:14,700 --> 00:00:19,020
This formula is used for finding the number of ways we can select the subset.

4
00:00:19,530 --> 00:00:20,730
For example, if.

5
00:00:22,500 --> 00:00:25,680
Alphabets are given to us, a, b, c, d, e.

6
00:00:30,020 --> 00:00:37,280
Seven objects to give up, and they want to select only three in how many ways I can select any three

7
00:00:37,280 --> 00:00:44,900
of them, like I can select ABC or ABC, the ECB.

8
00:00:45,020 --> 00:00:45,690
This is wrong.

9
00:00:45,860 --> 00:00:51,180
This is the same selection in the changing of position will not give a new selection.

10
00:00:51,200 --> 00:00:54,610
It's called a permutation in the unique position is called permutation.

11
00:00:54,950 --> 00:01:01,910
So there's another formal and informal call and B are also what this is and C are the combination that

12
00:01:01,910 --> 00:01:02,670
is selection.

13
00:01:03,290 --> 00:01:04,620
So there's a known formula.

14
00:01:05,660 --> 00:01:14,630
Now how we can write a procedure or a function for evaluation of and C are not important.

15
00:01:14,630 --> 00:01:23,300
Thing about this formula is if the value of an ambiguous five then C then are articulating the values

16
00:01:23,300 --> 00:01:25,400
from zero to five.

17
00:01:25,760 --> 00:01:32,190
So it means I can find the values like five six zero five seven five C two.

18
00:01:32,630 --> 00:01:35,800
So on up to five C five.

19
00:01:36,230 --> 00:01:42,860
So the value of R can start from zero and the last number is same as any.

20
00:01:43,430 --> 00:01:50,190
So I cannot have are greater than five that is itself or less than zero.

21
00:01:50,960 --> 00:01:56,630
The next thing, how to write a function for the so I can write on a simple function for evaluation

22
00:01:56,630 --> 00:01:57,640
of this formula.

23
00:01:57,950 --> 00:02:04,850
I'll write a function here and type of functions in and let us call the function name as a C and it

24
00:02:04,850 --> 00:02:11,810
needs to parameters that are and and are integer and an integer are.

25
00:02:14,970 --> 00:02:21,390
Not for evaluation of this form, I should know, factored in of an R and and minus half, so these

26
00:02:21,390 --> 00:02:27,480
factors I can find out by calling function fact that is already if you have done a function for finding

27
00:02:27,480 --> 00:02:31,680
the factorial, we have written iterative version, also recursive version also.

28
00:02:31,950 --> 00:02:39,080
So we can use any of those versions and we can find out the factor of this so far, finding the factorial.

29
00:02:39,240 --> 00:02:41,040
I'll take some temporary variables.

30
00:02:42,000 --> 00:03:01,380
One B2 and deeply even as factorial of an A. two is factorial of R d trees factorial off and minus on.

31
00:03:02,010 --> 00:03:03,740
So you got all three factorial?

32
00:03:04,200 --> 00:03:06,120
No, I have the result.

33
00:03:06,270 --> 00:03:14,940
Just I can return the one divided by B2 into the three.

34
00:03:15,330 --> 00:03:20,160
Must be in the brackets because both are the denominator that nitzan.

35
00:03:21,480 --> 00:03:27,060
As a simple function for finding NCAR value for any given and are.

36
00:03:28,650 --> 00:03:32,530
How much time with the city taking factories function?

37
00:03:32,550 --> 00:03:39,280
We have already analyzed and it is taking out of End-Time and this all sort of end and the end and the

38
00:03:39,300 --> 00:03:43,140
simple statements which Konstantine so we can ignore that.

39
00:03:43,320 --> 00:03:44,390
So it is a trehan.

40
00:03:44,670 --> 00:03:49,540
So three, it is order of any degree of polynomials.

41
00:03:49,590 --> 00:03:55,530
And so the time taken by this function is and it's a simple function, that's all we can write a simple

42
00:03:55,530 --> 00:04:01,860
function for finding a combination value of given and then are now next.

43
00:04:01,950 --> 00:04:08,340
Can we write a recursive function for this one too, for getting the idea of a recursive function?

44
00:04:08,610 --> 00:04:13,980
First of all, we look at Pascal's triangle, so let us see Pascal's Triangle.

45
00:04:14,490 --> 00:04:22,360
Pascall Triangle shows that the values of NCAR can be obtained by performing addition recursively how.

46
00:04:22,380 --> 00:04:23,280
Let us see this.

47
00:04:24,090 --> 00:04:31,680
First of all, on the top the values one then on the site extreme's are taken as one, then this extremeness

48
00:04:31,680 --> 00:04:35,700
one, then these two are added to get to and this is one.

49
00:04:36,450 --> 00:04:40,470
Then this extreme value is one and this is one place to.

50
00:04:40,620 --> 00:04:43,020
This is three, two plus one.

51
00:04:43,200 --> 00:04:45,600
This is three and this one is one.

52
00:04:47,000 --> 00:04:53,330
The site, it's one and one plus three, this is for these two are added to get six.

53
00:04:53,770 --> 00:04:57,780
These two are added to get food and extreme value.

54
00:04:58,030 --> 00:04:58,420
One.

55
00:05:01,140 --> 00:05:11,610
So what these values are, let us see this topmost values zero, and this is one C0 and one see one

56
00:05:11,910 --> 00:05:15,540
see the value of and as one so I can be from zero to one.

57
00:05:16,380 --> 00:05:23,070
Now this room is four to four to see zero to see one to see two.

58
00:05:24,370 --> 00:05:33,810
Then destroyed four three three six zero three three one three three two three C three.

59
00:05:34,920 --> 00:05:40,470
And lastly as four three zero four seven four three two.

60
00:05:41,680 --> 00:05:45,190
For C three for C for.

61
00:05:47,320 --> 00:05:55,090
So if you want to find out the combination values for the four, for various values of God, you can

62
00:05:55,090 --> 00:05:57,970
take it from this rule of Pascall strangled.

63
00:05:58,330 --> 00:06:04,680
So from Pascal's Triangle, we can find out the values of NCAR or else we can use this formula also.

64
00:06:05,110 --> 00:06:11,830
So already we have seen a function using the formula, but from the Pascal's Triangle can be right on

65
00:06:11,830 --> 00:06:14,550
the function for fighting in sheer value.

66
00:06:15,250 --> 00:06:15,850
Let us see.

67
00:06:16,120 --> 00:06:18,100
Let us observe Pascal's Triangle.

68
00:06:18,400 --> 00:06:20,770
See any value like example.

69
00:06:20,770 --> 00:06:30,370
Falsey two is obtained by adding three C1 and three C2 and how that one is obtained by adding to C0

70
00:06:30,370 --> 00:06:34,180
and to see one and how that to see zero is obtained.

71
00:06:34,180 --> 00:06:38,340
If a direct answer to see one is obtained by adding one zero.

72
00:06:38,360 --> 00:06:47,140
And once you answer this recursive recursive if I call this value as ency are this value is obtained

73
00:06:47,140 --> 00:06:56,320
by adding these two, if any, for an artist to then what is this three one Solis's and the minus one

74
00:06:56,860 --> 00:06:58,690
see Rs2.

75
00:06:58,690 --> 00:06:59,530
So this one here.

76
00:06:59,530 --> 00:07:04,150
So this is R minus one and other value is this one.

77
00:07:04,150 --> 00:07:09,460
So N so this is and minus one C R is RJD.

78
00:07:10,420 --> 00:07:20,830
So any ncar value can be obtained by adding and minus one, C or minus one and minus one C are so based

79
00:07:20,830 --> 00:07:24,100
on that I can define a recursive function here.

80
00:07:25,030 --> 00:07:35,680
Let the function name B C and it takes N and R then here for the recursion I should define that small

81
00:07:35,680 --> 00:07:39,340
problem means to zero zero what I should do.

82
00:07:39,670 --> 00:07:48,460
So for this you can see here when R is zero, return one then and is equal to our return one.

83
00:07:48,700 --> 00:07:50,890
So extreme points.

84
00:07:50,890 --> 00:08:03,580
Those are one always when one is equal to zero or and is equal to are written one for small values.

85
00:08:03,730 --> 00:08:10,270
These are the small values, one are the small values and one it is one if out of zero or anything close

86
00:08:10,270 --> 00:08:10,630
to what.

87
00:08:10,900 --> 00:08:14,170
Otherwise we will add these two terms and get the result.

88
00:08:14,500 --> 00:08:29,220
So else written written C off and the minus one gomaa minus one plus C of and minus one comma are.

89
00:08:31,680 --> 00:08:32,289
That's all.

90
00:08:33,510 --> 00:08:40,799
So this is a recursive function for finding NCAR value that this combination formula and this recursive

91
00:08:40,799 --> 00:08:43,860
function follows actually Pascal's triangle.

92
00:08:44,130 --> 00:08:50,430
So it means if I am trying to find 42, it will call Tracy one and Tracy two.

93
00:08:50,640 --> 00:08:51,600
So far, Tracy one.

94
00:08:51,600 --> 00:08:57,870
It will call to see zero to see one for to see what it will call one zero one one two one and one zero

95
00:08:57,870 --> 00:09:00,240
one seven are directly one four three zero.

96
00:09:00,240 --> 00:09:02,130
It will call this one and this one again.

97
00:09:02,460 --> 00:09:06,630
So it will make the calls from bottom towards top.

98
00:09:06,780 --> 00:09:07,650
That is bottom up.

99
00:09:07,650 --> 00:09:14,430
Calls are made compared to the Pascal's Triangle and whenever it reaches one, it terminates.

100
00:09:14,460 --> 00:09:15,570
That is the last point.

101
00:09:15,870 --> 00:09:20,840
Because when and is equal to or is equal to zero, we are returning one.

102
00:09:21,660 --> 00:09:24,120
So that's all about combination formula.

