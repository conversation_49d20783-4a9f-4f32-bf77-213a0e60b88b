1
00:00:00,210 --> 00:00:09,030
Let us look at Mr<PERSON> in an introduction, a recursive function will pass parameter as a recursive

2
00:00:09,030 --> 00:00:09,510
call.

3
00:00:10,830 --> 00:00:14,760
Here is a recursive function and it is calling itself so.

4
00:00:14,760 --> 00:00:17,240
It is recursive, it is calling itself here.

5
00:00:17,700 --> 00:00:24,990
But what the parameters parameter itself is a recursive call means unless the result of this recursive

6
00:00:24,990 --> 00:00:28,290
call is up in, this call cannot be made.

7
00:00:29,380 --> 00:00:33,780
So a recursive call is taking a recursive call as a barometer.

8
00:00:33,850 --> 00:00:39,700
So this is recursion inside recursion, so it is called as nested recursion.

9
00:00:40,120 --> 00:00:43,200
So far as an example, I have one function here.

10
00:00:43,210 --> 00:00:45,220
I will trace that function and show you.

11
00:00:46,240 --> 00:00:48,950
Here is an Mr. Recursive function example.

12
00:00:49,300 --> 00:00:55,750
Let me read it out first, a function which is taking parameter end and if anything greater than 100,

13
00:00:55,750 --> 00:00:58,440
then directly it is returning some value.

14
00:00:58,990 --> 00:01:03,280
If not, then it is making a nested recursive call.

15
00:01:03,280 --> 00:01:09,670
So function call is again taking a parameter as a function call, but some increase the value of and

16
00:01:10,510 --> 00:01:10,940
so forth.

17
00:01:10,960 --> 00:01:16,720
Tracing this one, I will take the value that is ninety five and let us try this ninety five on this

18
00:01:16,720 --> 00:01:16,970
one.

19
00:01:17,380 --> 00:01:19,460
So I will place this function for this value.

20
00:01:19,990 --> 00:01:24,550
So let us start first function gonne one of nine defines.

21
00:01:26,910 --> 00:01:33,810
Ninety five is greater than Hendrickx, no, then it will enter into El Spok, so else it will call

22
00:01:33,810 --> 00:01:39,030
itself again by passing fun and again fun of.

23
00:01:40,290 --> 00:01:42,450
Ninety five plus 11.

24
00:01:43,340 --> 00:01:44,800
That is one, not six.

25
00:01:45,630 --> 00:01:52,830
So first of all, I should know the result of this one, then I can use that value as a parameter for

26
00:01:52,830 --> 00:01:53,910
this function called.

27
00:01:54,670 --> 00:01:56,530
Let me evaluate this one first.

28
00:01:56,860 --> 00:02:02,770
See, some of this is one, not six, so full of one, not six.

29
00:02:05,090 --> 00:02:10,460
Let us take this call for one or six one, not six, is greater than Hendershott, so it returns one,

30
00:02:10,460 --> 00:02:11,680
not six minus 10.

31
00:02:11,690 --> 00:02:13,400
That is 96.

32
00:02:13,610 --> 00:02:16,300
So the result of this function is 96.

33
00:02:16,710 --> 00:02:19,360
So this 96 is the result of this function call.

34
00:02:19,610 --> 00:02:25,670
So this has to be placed here so it becomes functional 96.

35
00:02:26,390 --> 00:02:28,670
So actually we started with ninety five.

36
00:02:28,860 --> 00:02:34,910
Then the next call is nothing but 96 a for 96 96.

37
00:02:34,940 --> 00:02:36,000
Let's not get rid of that.

38
00:02:36,040 --> 00:02:43,190
Hendershott enters into this spot then it will make a function call that again nested function call.

39
00:02:43,490 --> 00:02:46,760
But what is the value of end plus level Sirtis.

40
00:02:46,770 --> 00:02:50,720
So 96 96 plus eleven as one not seven.

41
00:02:51,410 --> 00:02:55,680
So Buttiglione making the rack so I should know what is the result of this one.

42
00:02:55,700 --> 00:02:57,760
All for one, not seven.

43
00:02:57,920 --> 00:03:00,290
So let us find out for Northbourne or seven here.

44
00:03:00,740 --> 00:03:03,350
Fun of one, not seven.

45
00:03:04,310 --> 00:03:08,180
This is fast and this will not seven for one, not seven is better than 100.

46
00:03:08,180 --> 00:03:11,420
So it returns one, not seven minus 10.

47
00:03:11,420 --> 00:03:13,300
So this becomes 97.

48
00:03:13,700 --> 00:03:16,640
So this is the result of this function call.

49
00:03:17,630 --> 00:03:22,240
So actually this function becomes no fun of ninety seven.

50
00:03:22,970 --> 00:03:25,110
Now let us solve this one off ninety seven.

51
00:03:25,700 --> 00:03:31,070
Ninety seven is not greater than a hundred so it will enter into the L spot and it will make the function

52
00:03:31,070 --> 00:03:31,700
calls.

53
00:03:31,730 --> 00:03:33,080
That is fun of.

54
00:03:33,650 --> 00:03:34,760
Fun of.

55
00:03:36,530 --> 00:03:42,430
And plus, Levin, that is ninety seven plus 11, so this becomes one, not eight.

56
00:03:44,060 --> 00:03:48,650
Now as we have already seen these two girls say this was one, not six.

57
00:03:48,650 --> 00:03:52,070
So it becomes 96, but not seven became seven.

58
00:03:52,070 --> 00:03:54,860
So one day it will become 98 and stop it.

59
00:03:55,130 --> 00:03:56,390
I will get a good grade on this.

60
00:03:56,990 --> 00:03:58,880
So this is 98.

61
00:03:59,120 --> 00:04:03,440
So fun of one, not eight is 98.

62
00:04:05,090 --> 00:04:11,990
So this function call becomes actually fun of ninety eight now full of ninety, ninety eight is not

63
00:04:11,990 --> 00:04:12,790
greater than hundreds.

64
00:04:12,790 --> 00:04:14,330
So it enters into the L spot.

65
00:04:14,570 --> 00:04:20,779
Then it become full of fun of 98 plus 11.

66
00:04:22,220 --> 00:04:24,110
So this becomes Bhanot nine.

67
00:04:25,900 --> 00:04:34,360
And dysfunction, one of nine, this becomes one of one, not nine becomes ninety nine, so this is

68
00:04:34,360 --> 00:04:36,100
one of ninety nine.

69
00:04:38,700 --> 00:04:46,800
Four ninety nine is not greater than a hundred, so it will go into the El Paso Fund of fund of.

70
00:04:49,700 --> 00:04:54,140
Ninety nine 11, so this becomes one 110.

71
00:04:55,930 --> 00:04:57,760
Now, what is the result of 110?

72
00:05:05,290 --> 00:05:08,890
Full of 110 will be 100.

73
00:05:10,010 --> 00:05:14,520
Here, doesn't the if block and returns minus 10, so there's a 100.

74
00:05:14,900 --> 00:05:17,420
So this call becomes fun of.

75
00:05:20,000 --> 00:05:20,600
Hendrik.

76
00:05:23,510 --> 00:05:28,120
Now, full of 100 hundred is not greater than that, so it will enter into this spot.

77
00:05:28,370 --> 00:05:31,150
So I have to continue here and continue here.

78
00:05:31,520 --> 00:05:33,560
So this becomes fun of.

79
00:05:34,520 --> 00:05:35,480
Fund of.

80
00:05:36,310 --> 00:05:39,940
One hundred plus eleven, so this is one 111.

81
00:05:41,050 --> 00:05:42,340
The four of 111.

82
00:05:43,510 --> 00:05:50,610
This is greater than and so this is 111 minus 10, so this becomes one, not one.

83
00:05:51,160 --> 00:05:57,330
So this function call, finally it becomes fun of one, not one, final four, not one.

84
00:05:57,340 --> 00:05:58,760
Again, it is greater than 100.

85
00:05:58,790 --> 00:06:00,760
So this will be one, not one minus 10.

86
00:06:00,760 --> 00:06:04,640
So this becomes 91 now.

87
00:06:04,750 --> 00:06:09,240
This time it will not enter into the elsberg and it will return 91.

88
00:06:09,430 --> 00:06:12,520
It goes back to this call and goes back to this one.

89
00:06:12,520 --> 00:06:13,000
And this one.

90
00:06:13,000 --> 00:06:13,450
And this one.

91
00:06:13,450 --> 00:06:13,930
And this one.

92
00:06:14,230 --> 00:06:17,110
Finally, the result of this one is 91.

93
00:06:19,250 --> 00:06:24,860
So that's all I have taken one simple example to show misattribution, usually Néstor percussion, if

94
00:06:24,860 --> 00:06:28,880
you want them to expand like anything, it will become a bigger tree.

95
00:06:29,750 --> 00:06:33,260
So here you have observed that for every nested call.

96
00:06:33,260 --> 00:06:38,060
First of all, we should note the result of this one and that result is less of a dysfunction like here

97
00:06:38,060 --> 00:06:39,490
I have written off 96.

98
00:06:40,040 --> 00:06:45,500
Then again, doesn't this that call then I should know the result of this one that is 97 and I use that

99
00:06:45,500 --> 00:06:47,820
ninety seven here in front of ninety seven.

100
00:06:48,650 --> 00:06:55,420
So every time you have to find out the result of nested or Interpol, then you can make an altar call,

101
00:06:55,430 --> 00:06:57,350
you can find Otakon.

102
00:06:58,130 --> 00:06:59,250
So that's all that.

103
00:06:59,270 --> 00:07:05,420
Mr. Recursion, we will see more examples on recursions in coming menials.

