1
00:00:00,440 --> 00:00:06,980
Now, let us look at the major formula and Stalder elements column by column and this single dimension

2
00:00:08,090 --> 00:00:09,300
column media formula.

3
00:00:10,100 --> 00:00:15,140
So I'll fill the elements column by column for first column elements are one one two one three one four

4
00:00:15,140 --> 00:00:17,840
one five one zero two one.

5
00:00:19,530 --> 00:00:25,690
Three one four one five one, so this is column one.

6
00:00:26,820 --> 00:00:29,010
Likewise, I'll fill up the second column.

7
00:00:29,010 --> 00:00:31,670
That is two to three to four to five to.

8
00:00:32,310 --> 00:00:33,780
So I'll fill all the columns.

9
00:00:34,790 --> 00:00:40,490
So I have filled all the elements column by column, so simply we have filled elements column by column,

10
00:00:40,490 --> 00:00:47,900
but there must be some formula that is mapping these two dimensional elements or a matrix element in

11
00:00:47,900 --> 00:00:48,950
a single dimension.

12
00:00:49,790 --> 00:00:51,930
So we need to come up with some formula.

13
00:00:52,340 --> 00:00:57,500
So for that, I'll take some examples and we will do some observation and then we will prepare the formula

14
00:00:57,500 --> 00:00:57,840
for it.

15
00:00:58,280 --> 00:01:01,610
Let us take an example of Element A four by four.

16
00:01:01,620 --> 00:01:03,240
So this is about four by four.

17
00:01:03,560 --> 00:01:06,950
And here in this area, it is present at index 12.

18
00:01:08,010 --> 00:01:15,930
So let us get the next 12 by observation, then we will prepare the formula so the index of an element

19
00:01:16,740 --> 00:01:19,370
is off for coming forward,

20
00:01:22,350 --> 00:01:23,490
how to get the index.

21
00:01:24,060 --> 00:01:28,530
So first column check for the column column is forward.

22
00:01:28,540 --> 00:01:30,420
So I have to go in fourth column.

23
00:01:30,750 --> 00:01:33,330
So fourth column and I should skip how many columns?

24
00:01:33,330 --> 00:01:36,270
Three columns, three columns I should skip.

25
00:01:36,480 --> 00:01:38,580
The first column is having how many elements.

26
00:01:38,580 --> 00:01:39,540
Five elements.

27
00:01:40,500 --> 00:01:45,450
Then second column is having four elements and then the third column is having three elements.

28
00:01:45,450 --> 00:01:52,270
I should skip all these columns five, then four, then three, then afterwards when I have skipped

29
00:01:52,290 --> 00:01:56,150
first, second and third column, then I'm here in the beginning of this one.

30
00:01:56,430 --> 00:01:59,540
So do I have to move ahead to get on the element node?

31
00:01:59,550 --> 00:02:00,810
I'm on the element only.

32
00:02:01,020 --> 00:02:01,970
So this is zero.

33
00:02:02,400 --> 00:02:03,390
So total.

34
00:02:03,400 --> 00:02:07,440
This is 12 five plus four nine nine plus these two.

35
00:02:07,740 --> 00:02:09,900
So this is two in next.

36
00:02:10,020 --> 00:02:12,390
I want the element that is five coming forward.

37
00:02:12,640 --> 00:02:15,240
That is in the same column, column four.

38
00:02:15,570 --> 00:02:23,070
So let us get the index for that one index of eight of five comma four.

39
00:02:23,850 --> 00:02:27,450
So this is again in fourth column so far reaching forward column.

40
00:02:27,450 --> 00:02:28,710
This is column number four.

41
00:02:28,740 --> 00:02:32,130
I should skip the elements, five elements, four elements of the three elements.

42
00:02:32,140 --> 00:02:36,420
So this is five plus four plus three then.

43
00:02:37,290 --> 00:02:39,690
But I have skipped first column that I'm here.

44
00:02:39,690 --> 00:02:42,930
Second column I skip, I'm here, third column I skip, I'm here.

45
00:02:42,930 --> 00:02:44,250
And how much I should move ahead.

46
00:02:44,580 --> 00:02:46,440
Just one element I should move forward.

47
00:02:46,620 --> 00:02:47,580
So this is one.

48
00:02:47,700 --> 00:02:51,390
So this is five plus four nine nine plus the 12 and 13.

49
00:02:51,630 --> 00:02:57,510
And they got the index to let us take one more example and then we will try to prepare the formula.

50
00:02:57,690 --> 00:03:00,510
So let us take the example of five commentary.

51
00:03:00,690 --> 00:03:01,980
This is the third column.

52
00:03:02,160 --> 00:03:10,260
So what is the index of five comma three five commentary as a present here in the third column?

53
00:03:10,500 --> 00:03:12,360
So for that I should skip two columns.

54
00:03:12,360 --> 00:03:14,400
So the first column is having five elements.

55
00:03:14,400 --> 00:03:19,520
Second column is having four elements then plus one I have skipped.

56
00:03:19,560 --> 00:03:22,770
First column I'm here, second column I skip, I'm here.

57
00:03:22,980 --> 00:03:25,790
How much I should move ahead to reach that five commentary.

58
00:03:25,800 --> 00:03:27,600
One to two places.

59
00:03:28,110 --> 00:03:28,680
Places.

60
00:03:29,130 --> 00:03:33,130
So this is five four nine nine plus two living index indexes.

61
00:03:33,130 --> 00:03:33,540
Sliman.

62
00:03:33,720 --> 00:03:34,830
Yes, I'm getting it.

63
00:03:35,190 --> 00:03:40,650
Then how this can be converted into the formula for any index icon Maji.

64
00:03:41,520 --> 00:03:44,040
So I will try to observe these values and write down.

65
00:03:44,130 --> 00:03:49,290
So for example, if I take this one, the column, the model for forward and here also column number

66
00:03:49,290 --> 00:03:49,920
was four.

67
00:03:50,160 --> 00:03:53,460
So this is five of five is nothing but NP cross.

68
00:03:53,460 --> 00:04:01,560
And so first one is and then plus and minus one then plus and minus two.

69
00:04:03,740 --> 00:04:11,060
For for this was and then in the minus one, then in the minus two, then for three, how much it is

70
00:04:11,060 --> 00:04:13,070
and then then minus one only.

71
00:04:14,880 --> 00:04:20,810
So for four, it was up to two for three, and this is just and the minus one, only two out there,

72
00:04:21,060 --> 00:04:27,660
so it means for four it was two times less and for three also it is a two time less.

73
00:04:27,930 --> 00:04:37,410
So for any general form, this will go on till and the minus, whatever the value is G minus two, it

74
00:04:37,410 --> 00:04:39,660
will go up to G minus two times.

75
00:04:39,810 --> 00:04:45,520
So N and minus one and minus two go on up to end the minus solfege minus two.

76
00:04:45,900 --> 00:04:49,080
So this has finished within the bracket then.

77
00:04:49,080 --> 00:04:51,830
Plus what is this value.

78
00:04:52,200 --> 00:04:53,180
What is this value.

79
00:04:53,670 --> 00:04:54,160
See this.

80
00:04:54,180 --> 00:04:59,160
We got zero because it was four for this was one because five four.

81
00:05:00,030 --> 00:05:02,630
And this we got two because five three.

82
00:05:02,910 --> 00:05:07,200
So if you observe this is fine, minus three is two five minutes for this one.

83
00:05:07,390 --> 00:05:09,320
So this is four minus four zero.

84
00:05:09,480 --> 00:05:13,050
So yes, it is a minus G.

85
00:05:15,380 --> 00:05:27,000
The formula, I can simplify this one and say that this is G minus one times so and into G minus one.

86
00:05:27,230 --> 00:05:31,820
So from here, if you count then it is one, two, three, up to minus two time.

87
00:05:32,090 --> 00:05:33,440
Then this one is extra.

88
00:05:33,590 --> 00:05:42,770
So G minus one times and then this minus one and two and three goes on to J minus two.

89
00:05:42,800 --> 00:05:44,560
So this is inside the bracket.

90
00:05:44,810 --> 00:05:47,380
Then afterwards it's a minus G.

91
00:05:48,530 --> 00:05:54,380
I can simplify it further and say anything to G minus one than minus.

92
00:05:54,680 --> 00:05:58,360
This is some off and on minus two.

93
00:05:58,670 --> 00:06:10,620
So this is G minus two and two, minus one by two and this is within the bracket plus minus G.

94
00:06:11,240 --> 00:06:13,880
So this is the formula for column representation.

95
00:06:14,120 --> 00:06:18,170
And later we will see how to write a program for this one.

96
00:06:18,830 --> 00:06:22,350
We have seen the Romijn formula as the last column is a formula.

97
00:06:22,580 --> 00:06:27,090
These two formulas are useful in Upper Triangle of Mattocks.

98
00:06:27,120 --> 00:06:28,580
That will be our next topic.

99
00:06:28,580 --> 00:06:34,280
An upper triangle of matter to the formula will be similar, but instead of I gave, they will change.

100
00:06:34,280 --> 00:06:35,030
So I will become.

101
00:06:35,030 --> 00:06:36,140
GMG will become.

102
00:06:36,140 --> 00:06:37,970
I will see that in the next video.

