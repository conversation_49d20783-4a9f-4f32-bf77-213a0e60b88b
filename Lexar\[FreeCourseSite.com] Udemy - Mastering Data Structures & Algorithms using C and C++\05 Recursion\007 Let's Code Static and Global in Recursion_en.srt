1
00:00:00,150 --> 00:00:09,540
And this is will look at static and global variables and recursion, I'll start a new project, a command

2
00:00:09,540 --> 00:00:15,720
line tool, I'll give the project name as Static Global.

3
00:00:21,200 --> 00:00:27,260
Create a project, a project is ready, and here is the main function.

4
00:00:28,030 --> 00:00:34,040
I will write on the function that I have shown you integers, the return type of a function and the

5
00:00:34,040 --> 00:00:35,120
parameter it takes.

6
00:00:35,120 --> 00:00:41,630
And now inside this, if any, is greater than Zettl.

7
00:00:45,880 --> 00:00:50,290
Return phone calls and minus one.

8
00:00:52,730 --> 00:00:53,570
Plus and.

9
00:00:55,620 --> 00:01:03,750
Otherwise, return zero, this is the function we have seen, which gives the some of the national numbers.

10
00:01:05,880 --> 00:01:12,090
Let us take a variable R and inside this R, let us get the result of a function by passing parameter

11
00:01:12,090 --> 00:01:17,220
five and I will print the result obtained by that function.

12
00:01:18,900 --> 00:01:26,100
Person Tildy, only one value that is, ah, I have to point, this is the functionality we have seen

13
00:01:26,550 --> 00:01:34,440
the value if we pass the value as a five eight, return 15 every delegate in the program and let us

14
00:01:34,440 --> 00:01:36,270
see what is the output given by this program.

15
00:01:39,160 --> 00:01:46,900
His output is 15, yes, now will modify the function, I will introduce a static variable inside the

16
00:01:46,940 --> 00:01:56,020
function static integer X initially zero and before calling itself let us do X plus plus and return

17
00:01:56,890 --> 00:01:57,610
plus X..

18
00:01:58,330 --> 00:02:00,060
This is what I have explained you on the board.

19
00:02:00,430 --> 00:02:02,050
So the same thing I have written here.

20
00:02:02,230 --> 00:02:06,390
And when we have press that function, the result was twenty five.

21
00:02:06,850 --> 00:02:14,560
Let us run and check what the result will be here after person daily here and said print after person

22
00:02:14,560 --> 00:02:21,640
daily I will give slash and so it gets a new line after pending the result that is on the program.

23
00:02:24,460 --> 00:02:32,380
See, the result is 25, let us debug this program and see so for debugging, I will take.

24
00:02:35,130 --> 00:02:38,460
Breakpoint, then I run the program.

25
00:02:40,820 --> 00:02:47,840
It will stop here at the break point inside the burger window, you can see that just it's in the main

26
00:02:47,840 --> 00:02:50,620
function and it has stopped at this function call.

27
00:02:52,760 --> 00:02:54,890
I will continue running next line.

28
00:02:55,340 --> 00:03:01,460
Another function is called you can check the value of S five and X is right now Zettl.

29
00:03:05,150 --> 00:03:09,230
The next statement, it will do X plus plus, then it will call itself.

30
00:03:09,410 --> 00:03:15,040
So now you can see that the value of X has became one and this is the second call here in the debugging.

31
00:03:15,080 --> 00:03:17,600
And also, you can see that there are two functions called.

32
00:03:19,900 --> 00:03:21,370
But the value of X is one.

33
00:03:24,270 --> 00:03:33,030
Let us continue now, plus value of X becomes two, you can check it here, see X value is to.

34
00:03:34,050 --> 00:03:35,850
Then again, it will call itself.

35
00:03:37,640 --> 00:03:46,480
Again, it will call itself again, it will call itself right now, and value is one and became four.

36
00:03:46,930 --> 00:03:56,180
And then the next statement X becomes five, non-value value is zero and X is five four zero.

37
00:03:56,180 --> 00:03:57,200
It will not execute.

38
00:03:57,200 --> 00:04:04,140
It will not enter inside if block and it will return a zero, return zero.

39
00:04:04,160 --> 00:04:07,250
So it goes back to the previous caller known the previous call.

40
00:04:07,250 --> 00:04:11,090
The value of end was one noise about to add this value.

41
00:04:11,330 --> 00:04:12,980
See, the value of X is five.

42
00:04:13,340 --> 00:04:15,950
Five is added with this one because X is a static.

43
00:04:15,950 --> 00:04:23,350
There is only one copy of variable X and here inside the swash you can see that X is five and as one

44
00:04:23,970 --> 00:04:24,500
the bottom.

45
00:04:25,430 --> 00:04:31,580
If we continue, it will go back to the previous call with the value of and was two and X is still five.

46
00:04:33,030 --> 00:04:41,460
Then continue, it will go back to the previous call with any value as a three and X's five because.

47
00:04:44,400 --> 00:04:50,010
Because when it has reached an equal to zero, by that time, X became five and X remained five while

48
00:04:50,010 --> 00:04:56,160
returning time also because there is only one copy of variable X if I finish this.

49
00:04:58,390 --> 00:05:02,560
Then here in the next line, the value is twenty five.

50
00:05:03,850 --> 00:05:05,380
Yes, we got the same result.

51
00:05:05,890 --> 00:05:08,910
So we have seen how the function is working now.

52
00:05:08,920 --> 00:05:10,270
I will modify this function.

53
00:05:11,590 --> 00:05:17,470
I will remove the breakpoint, let us modify this and install a static variable.

54
00:05:17,470 --> 00:05:22,320
I will remove this and I will make it as a global variable out.

55
00:05:24,730 --> 00:05:25,850
Xangsane zero.

56
00:05:25,870 --> 00:05:28,140
So this is a global variable, all said all the functions.

57
00:05:28,170 --> 00:05:33,820
It's accessible to all the functions will run this ancestrally five.

58
00:05:34,450 --> 00:05:35,260
So it's working.

59
00:05:35,270 --> 00:05:37,980
Same now.

60
00:05:38,080 --> 00:05:38,920
I'll show you one thing.

61
00:05:39,220 --> 00:05:43,990
See here, I have called the function for five one time and I have a display that it's resod.

62
00:05:46,180 --> 00:05:47,260
I will do it again.

63
00:05:47,740 --> 00:05:51,620
So again, I will call the function for value five and I can print the result.

64
00:05:52,120 --> 00:05:58,420
So if you check this variable X, that will become five in the first call, then the second call it

65
00:05:58,420 --> 00:06:04,020
will become ten, then the second call, it will start from the same value five.

66
00:06:04,450 --> 00:06:07,540
So let us even take all the same function for two times.

67
00:06:07,540 --> 00:06:09,430
Then what will be the result are two times.

68
00:06:09,430 --> 00:06:11,380
It will not be twenty five the times.

69
00:06:15,390 --> 00:06:18,580
See, first time at twenty five and the second time it is 50.

70
00:06:18,930 --> 00:06:24,980
The reason is when the first call is made, it has made the value X as a five.

71
00:06:25,260 --> 00:06:32,350
Then the second call is again trying to increment X four, five times here, X plus plus, then become

72
00:06:32,790 --> 00:06:35,180
ten and five times and is added.

73
00:06:35,180 --> 00:06:35,970
So it is 50.

74
00:06:38,330 --> 00:06:43,490
So that's all we have seen, the effect of static variables and global variables sometimes that are

75
00:06:43,490 --> 00:06:47,990
very useful, some type of problems, we cannot write them in the form of.

76
00:06:48,890 --> 00:06:49,210
Hmm.

77
00:06:51,010 --> 00:06:56,050
Some functions we cannot write them recursive, we may have to use a static variable, so we will see

78
00:06:56,050 --> 00:06:58,930
them when we come across such topics.

