1
00:00:00,900 --> 00:00:07,860
Let us look at a procedure for finding whether a given Linklaters sorted or not, so the example what

2
00:00:07,860 --> 00:00:11,210
I have here, the list has all the elements are sorted.

3
00:00:11,610 --> 00:00:14,790
That's what you want to detect, whether the sort of not socket.

4
00:00:15,090 --> 00:00:17,850
So let us see the example of sorted list first.

5
00:00:18,690 --> 00:00:20,220
Then what should be the procedure?

6
00:00:20,520 --> 00:00:23,810
See, the procedure is I should start from the first note.

7
00:00:23,940 --> 00:00:24,800
There is nothing here.

8
00:00:24,900 --> 00:00:26,700
So let's go to the next node.

9
00:00:27,150 --> 00:00:30,690
This node value should be greater than this value.

10
00:00:31,380 --> 00:00:34,710
Then it is increasing rather then go to the next node.

11
00:00:34,710 --> 00:00:36,260
This value should be greater than this one.

12
00:00:36,270 --> 00:00:37,760
Yes, it is an increasing order.

13
00:00:38,100 --> 00:00:41,830
Go to the next node and check whether this value is greater than this one.

14
00:00:42,180 --> 00:00:47,580
Yes, it's a good idea to continue in the same way if any point suppose instead of two.

15
00:00:47,580 --> 00:00:52,380
Well, I have four here then let us start once again.

16
00:00:52,830 --> 00:00:54,310
Three then come to five.

17
00:00:54,310 --> 00:00:55,190
First three.

18
00:00:55,200 --> 00:00:56,200
There is nothing to compare.

19
00:00:56,220 --> 00:00:59,280
So five, this is greater than this one percent.

20
00:00:59,280 --> 00:01:00,510
Eight is greater than this one.

21
00:01:00,510 --> 00:01:00,960
Perfect.

22
00:01:01,410 --> 00:01:04,590
Four is a smaller than the previous node mix.

23
00:01:04,590 --> 00:01:05,550
It is not Sakic.

24
00:01:06,730 --> 00:01:11,360
So just check the condition, whether you are getting any value that is smaller than the previous value.

25
00:01:11,380 --> 00:01:12,690
If so, it is not solid.

26
00:01:13,510 --> 00:01:16,120
If not, you have reached the end of the list.

27
00:01:16,150 --> 00:01:18,520
You have checked all that at the socket.

28
00:01:18,930 --> 00:01:20,280
The procedure is very simple.

29
00:01:20,500 --> 00:01:22,240
So let us see your procedure for this one.

30
00:01:22,660 --> 00:01:25,090
See, what I need is being on every node.

31
00:01:25,090 --> 00:01:27,500
I need a value of a previous node.

32
00:01:27,850 --> 00:01:35,230
So what I'll do is I'll take one variable X and let it be a minimum integer, minus three, two, seven,

33
00:01:35,230 --> 00:01:36,390
six, eight.

34
00:01:36,640 --> 00:01:39,810
So this is the minimum value for gobshite integer.

35
00:01:40,390 --> 00:01:42,100
So let it be a minimum value.

36
00:01:42,490 --> 00:01:46,360
Now take a pointer on Fust nor offer Linkous here.

37
00:01:47,410 --> 00:01:48,380
Now, the procedure.

38
00:01:48,520 --> 00:01:52,520
So what is the procedure, check three, is it greater than this one?

39
00:01:52,550 --> 00:01:58,230
Yes, Greta, so modify this to three and move to the next node seven am.

40
00:01:58,240 --> 00:02:03,820
Presently, on this note, I need the value of the previous node so that the value I'm keeping an X

41
00:02:04,300 --> 00:02:05,850
five, is it greater than three?

42
00:02:05,860 --> 00:02:06,260
Yes.

43
00:02:06,370 --> 00:02:09,460
So modify this as five and move to next normal.

44
00:02:11,440 --> 00:02:14,140
Now this is values present in X, right?

45
00:02:14,410 --> 00:02:15,960
I'm on that note eight.

46
00:02:16,120 --> 00:02:18,300
So this eight, is it greater than five.

47
00:02:18,310 --> 00:02:18,750
Yes.

48
00:02:19,000 --> 00:02:21,790
So modify this and move to the next level.

49
00:02:21,800 --> 00:02:27,630
So I'm storing the current value and moving to the next node to is it greater than eight.

50
00:02:27,850 --> 00:02:28,300
Yes.

51
00:02:28,450 --> 00:02:30,970
Modify this and go to the next node.

52
00:02:32,890 --> 00:02:34,780
16, is it greater than 12?

53
00:02:34,810 --> 00:02:39,380
Yes, modify this and go to the next node, so then we go to the next node.

54
00:02:39,400 --> 00:02:40,140
It is null.

55
00:02:41,540 --> 00:02:44,730
So if have reached annulments, we have thoroughly checked all the laws.

56
00:02:45,080 --> 00:02:47,090
There is no contradiction found.

57
00:02:47,210 --> 00:02:49,520
There is no failure so linguist's.

58
00:02:51,740 --> 00:02:59,580
If suppose I modify it once again, see, when I was on eight, this value was eight right now, suppose

59
00:02:59,600 --> 00:03:05,260
this is for that I'm here for that is greater than eight more.

60
00:03:05,870 --> 00:03:10,660
So I got a value which is not an increasing order after eight I should have next.

61
00:03:10,820 --> 00:03:11,660
But that is increasing.

62
00:03:11,660 --> 00:03:12,680
But this is decreasing.

63
00:03:13,010 --> 00:03:15,830
So stop and say Linklaters not socket.

64
00:03:17,070 --> 00:03:19,720
So the procedure is simple, quickly evident on the procedure.

65
00:03:20,130 --> 00:03:23,070
I need a variable X with the minimum value.

66
00:03:26,410 --> 00:03:33,490
I need a pointer on Fastenal, I should scan through the whole list, comparing the value with X and

67
00:03:33,490 --> 00:03:42,250
also modifying X. So what I have to do is if that value, this value is greater than this initial value

68
00:03:42,250 --> 00:03:48,560
was minus three to seven, minus three, two, seven, six, eight.

69
00:03:48,670 --> 00:03:49,740
This was the initial value.

70
00:03:50,110 --> 00:03:51,250
Is this greater?

71
00:03:51,550 --> 00:03:53,290
If it is greater, I will continue.

72
00:03:53,440 --> 00:03:57,250
If it is a smaller, then it is not sorted.

73
00:03:57,250 --> 00:03:58,570
I should stop suddenly.

74
00:03:59,650 --> 00:04:02,710
If it is smaller than it is not sorted, I should stop.

75
00:04:03,130 --> 00:04:07,480
So I should check which condition success condition or failure condition.

76
00:04:08,520 --> 00:04:11,100
We should check for senior coalition if.

77
00:04:12,860 --> 00:04:23,930
These data, if it is smaller than X, then wrecked on false means, let's just not sodic.

78
00:04:26,680 --> 00:04:33,040
So I should check the condition for failure, not for success, if anywhere, if the contract is found,

79
00:04:33,040 --> 00:04:35,110
then stop, otherwise continue.

80
00:04:35,590 --> 00:04:38,710
So I'm writing the condition for failure then.

81
00:04:39,430 --> 00:04:43,780
If it is not less, then modify X with the piece of data.

82
00:04:45,280 --> 00:04:49,040
And move be to next NORDO Pitou next.

83
00:04:51,130 --> 00:04:58,960
This should be repeated how long will be not equal to null until you reach the last node and the end

84
00:04:58,960 --> 00:05:02,410
of this one, say Rickon through.

85
00:05:04,530 --> 00:05:08,700
So this is the procedure for checking whether the Linklaters sorted or not.

86
00:05:08,820 --> 00:05:11,500
Let me try this one for some examples.

87
00:05:12,300 --> 00:05:18,540
I think the data here, I will take after five, I will take two here.

88
00:05:19,200 --> 00:05:23,610
So this is not four, three, five, two, 12, 16, two.

89
00:05:23,620 --> 00:05:25,900
So this is not shorter because of who they are not solid.

90
00:05:26,280 --> 00:05:27,450
So let us check this one.

91
00:05:27,450 --> 00:05:29,310
Let us stress this and how it works.

92
00:05:29,970 --> 00:05:31,470
Piece on first note.

93
00:05:31,470 --> 00:05:33,180
And this is minus eight or six, seven.

94
00:05:33,480 --> 00:05:37,860
The first one, the data, is it less than expeditor three?

95
00:05:37,860 --> 00:05:38,700
Is it leathernecks?

96
00:05:38,700 --> 00:05:39,120
No.

97
00:05:39,330 --> 00:05:40,680
So don't bet on anything.

98
00:05:41,040 --> 00:05:46,780
Take data in X, so three in X and the move to next normal move.

99
00:05:46,820 --> 00:05:50,700
P2 makes more than continue to be data.

100
00:05:50,700 --> 00:05:52,020
Is it less the next five.

101
00:05:52,020 --> 00:05:52,920
Is it like three.

102
00:05:53,550 --> 00:06:02,010
So modify this X to five and move P to next node, move P2 next node then continue.

103
00:06:02,310 --> 00:06:06,180
If this data is less than X two, is it less then?

104
00:06:06,630 --> 00:06:09,750
Yes, it is less those less than five.

105
00:06:10,170 --> 00:06:11,840
So a return false.

106
00:06:12,390 --> 00:06:17,610
So if this procedure is running false means list is not something solid return false.

107
00:06:17,610 --> 00:06:22,140
If the list is not sorted I will change that one and I will continue from there.

108
00:06:22,140 --> 00:06:24,150
Only I'll make it as eight.

109
00:06:25,410 --> 00:06:33,000
Now, at this place, BP data, is it less than eight, is it less than five know modify this X and

110
00:06:33,000 --> 00:06:38,160
make it as eight and move P2 next, nor B will move to next small.

111
00:06:40,390 --> 00:06:42,990
Continue to be the next.

112
00:06:43,030 --> 00:06:44,120
Well, is it less than eight?

113
00:06:44,290 --> 00:06:44,740
No.

114
00:06:45,100 --> 00:06:51,840
So modify this to 12 and move people next door and continue to data.

115
00:06:51,850 --> 00:06:53,110
Is it less than 16?

116
00:06:53,320 --> 00:06:54,400
Is it less than 12?

117
00:06:54,610 --> 00:06:55,170
No.

118
00:06:55,690 --> 00:07:00,640
Then modify data, then modify X with the data.

119
00:07:00,640 --> 00:07:08,260
That is this becomes 16 and move B to make small beams to the next node so people null continue.

120
00:07:08,560 --> 00:07:11,350
No P became null, he became null.

121
00:07:11,650 --> 00:07:14,020
So come out of the slope and return to.

122
00:07:14,620 --> 00:07:15,700
So this is working.

123
00:07:16,210 --> 00:07:20,460
So inside the loop we have check for the false condition, not for the true condition.

124
00:07:20,470 --> 00:07:25,750
So we believe that the link is already sorted, if not sorted, then when it is not certain if this

125
00:07:25,750 --> 00:07:27,600
condition is true, then it is not correct.

126
00:07:28,690 --> 00:07:37,660
So that's all about checking whether or not the time taken by this procedure is there is a loop.

127
00:07:38,880 --> 00:07:39,720
So an.

128
00:07:40,770 --> 00:07:48,540
Out of em, but if the list is sorted, it is and if it is not sorted, it can be minimum one.

129
00:07:48,540 --> 00:07:53,850
Also, see, this is three and if this is true, then in second, not only it will stop.

130
00:07:54,120 --> 00:08:00,060
So the time maybe order of one subminimum time taken is one maximum time on.

131
00:08:00,450 --> 00:08:07,800
And so the time is minimum, constant, maximum and.

132
00:08:11,620 --> 00:08:13,170
So that's all about this procedure.

