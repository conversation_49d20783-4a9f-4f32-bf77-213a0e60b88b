1
00:00:00,360 --> 00:00:04,080
Now, let us look at the procedure once again with some minor changes.

2
00:00:04,110 --> 00:00:05,100
What is the change?

3
00:00:05,100 --> 00:00:05,790
Look at here.

4
00:00:06,570 --> 00:00:11,520
See, the president's table was having addition, subtraction, multiplication, division now have included

5
00:00:11,520 --> 00:00:13,130
variables also operating.

6
00:00:13,260 --> 00:00:16,970
Also, I'm assuming that all parents have higher precedence.

7
00:00:16,980 --> 00:00:19,120
That is three options are also there.

8
00:00:19,140 --> 00:00:23,480
So it means I'm going to push all parents also into the stack.

9
00:00:24,330 --> 00:00:25,050
Yes.

10
00:00:25,350 --> 00:00:29,070
Let us see how the procedure works and the procedure.

11
00:00:29,100 --> 00:00:33,210
Every symbol from infix expression will go into the stack every single.

12
00:00:34,020 --> 00:00:39,060
Previously we were sending operands phonetically to postfix, but now everything goes into the stack

13
00:00:39,630 --> 00:00:41,150
and still it works.

14
00:00:41,280 --> 00:00:43,950
Let us see how fast is a.

15
00:00:45,060 --> 00:00:45,930
It's a no brainer.

16
00:00:46,640 --> 00:00:48,360
What is the president's three?

17
00:00:48,570 --> 00:00:50,190
Is there anything inside the stack?

18
00:00:50,220 --> 00:00:53,190
No Bush, that one in the stack, so I like it.

19
00:00:53,220 --> 00:00:56,070
Presidents also get along with the next.

20
00:00:56,310 --> 00:00:58,290
Plus, what is the president's one?

21
00:00:58,650 --> 00:01:00,240
What is the president in the stack?

22
00:01:00,510 --> 00:01:03,050
The stack top is having a president's three.

23
00:01:03,390 --> 00:01:04,379
This is higher.

24
00:01:04,739 --> 00:01:10,350
So you cannot push any symbol in the stack if already higher or equal president symbol is dead in the

25
00:01:10,350 --> 00:01:10,790
stack.

26
00:01:10,800 --> 00:01:14,610
So if is something is higher or equal in the stack, you cannot push.

27
00:01:14,970 --> 00:01:16,080
So popular this one.

28
00:01:16,090 --> 00:01:21,270
So the conditions are seem so popular this and send it to postfix.

29
00:01:22,750 --> 00:01:31,180
Then Bush plus into this time Plus's alone, then be precedences three, push it into the stack, the

30
00:01:31,180 --> 00:01:34,010
next as ex-president is too.

31
00:01:34,420 --> 00:01:39,550
That is true but this is three so great that one pop up and send it to postfix.

32
00:01:40,600 --> 00:01:41,320
That is true.

33
00:01:41,330 --> 00:01:42,060
This is one.

34
00:01:42,580 --> 00:01:44,020
So this is smaller than that one.

35
00:01:44,020 --> 00:01:44,710
So push it.

36
00:01:47,440 --> 00:01:53,440
The next US, see, this is three, that is three, this is two that is greater, so push it then minus

37
00:01:53,440 --> 00:01:57,070
its pursuit of this one, this is greater pop out.

38
00:01:59,140 --> 00:02:02,620
Minus one, and this is greater star for both.

39
00:02:05,260 --> 00:02:07,510
That is minus one and this is equal.

40
00:02:08,560 --> 00:02:12,740
So all the symbols in the stack, which are greater or equal, we should support them.

41
00:02:13,060 --> 00:02:13,950
Yes, we did it.

42
00:02:14,470 --> 00:02:16,390
No, there is nothing now we can push it.

43
00:02:16,990 --> 00:02:20,700
So you can see in the procedure that every symbol is going to stack.

44
00:02:20,710 --> 00:02:22,680
It's getting pushed definitely in the stack.

45
00:02:22,960 --> 00:02:27,620
Nobody is directly going to postfix continue these procedures.

46
00:02:27,640 --> 00:02:32,170
History, and especially this is one push it slash precedences two.

47
00:02:32,470 --> 00:02:33,310
This is three.

48
00:02:33,460 --> 00:02:35,380
This is great for property.

49
00:02:36,760 --> 00:02:37,870
Slash two.

50
00:02:37,870 --> 00:02:39,610
This is one that is good.

51
00:02:39,710 --> 00:02:40,600
So you can push it.

52
00:02:42,290 --> 00:02:45,160
E precedent's three three, and this is true.

53
00:02:45,470 --> 00:02:47,030
That is great, so we can push it.

54
00:02:48,210 --> 00:02:54,930
And of expression for all the symbols and append them to postfix e goes out first.

55
00:02:56,830 --> 00:02:58,180
Then goes out.

56
00:03:00,570 --> 00:03:01,890
Then minus goes out.

57
00:03:03,310 --> 00:03:10,780
Same reason we got even if you are pushing operations on this track, it works, but the operators must

58
00:03:10,780 --> 00:03:12,560
have higher precedence.

59
00:03:13,000 --> 00:03:17,630
But most presidents you must give, so everything will be going to stack.

60
00:03:18,070 --> 00:03:20,380
So that's what this was the variation.

