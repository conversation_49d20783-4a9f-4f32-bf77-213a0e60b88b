1
00:00:00,300 --> 00:00:08,970
Let us look at in order traversal up on a train, so already I have a train available here and a recursive

2
00:00:08,970 --> 00:00:10,840
in order traversal function.

3
00:00:11,460 --> 00:00:14,660
This is a recursive function similar to preorder.

4
00:00:14,970 --> 00:00:16,050
What is the difference?

5
00:00:16,530 --> 00:00:17,700
Function name is different.

6
00:00:17,740 --> 00:00:18,240
Yes, it is.

7
00:00:18,240 --> 00:00:19,170
In order for function.

8
00:00:19,170 --> 00:00:21,000
Name is different than three steps.

9
00:00:21,030 --> 00:00:22,350
Yes, it is having three steps.

10
00:00:22,500 --> 00:00:29,370
But if you observe in preorder that force in the beginning first step was predictive, then preorder

11
00:00:29,370 --> 00:00:30,080
calls for that.

12
00:00:30,100 --> 00:00:35,420
Two calls but now two calls are these calls and the parentheses in between them.

13
00:00:36,060 --> 00:00:40,130
So it is in order to guess how to rate both of print.

14
00:00:40,390 --> 00:00:41,560
That should be at the end.

15
00:00:41,850 --> 00:00:42,350
Yes.

16
00:00:42,360 --> 00:00:44,520
Print should be adding that spool start up.

17
00:00:45,120 --> 00:00:51,180
Now let me show you how this function works up on the street, so I will just show you the tracing of

18
00:00:51,330 --> 00:00:52,340
this function.

19
00:00:52,350 --> 00:00:54,270
I will not show this tag already.

20
00:00:54,300 --> 00:01:01,200
You have seen detail in preorder, so let us just generate tracing tree and understand how in order

21
00:01:01,200 --> 00:01:08,280
is working here, calibrating the output first time when the function is called in order, first time.

22
00:01:08,280 --> 00:01:11,610
The address of this route is past that that is 200.

23
00:01:11,790 --> 00:01:12,170
Right.

24
00:01:12,480 --> 00:01:17,570
So first time when we call, we call it like this in Onaga and we postlude.

25
00:01:19,320 --> 00:01:20,570
So two hundred is past.

26
00:01:21,670 --> 00:01:24,310
So it will take didas no, wait a minute.

27
00:01:25,320 --> 00:01:30,250
Function takes addresses, but addresses may confuse us from that.

28
00:01:30,390 --> 00:01:34,020
If I ask you to read, you will try to read volumes only date only.

29
00:01:34,560 --> 00:01:37,560
So we prefer data so Artosis may confuse.

30
00:01:37,590 --> 00:01:44,340
So instead of writing addresses, I relied on data, if at all, and even required I relied on letters.

31
00:01:45,150 --> 00:01:48,390
We know this is our path, but I will try to write data.

32
00:01:48,660 --> 00:01:51,210
So instead of 200, let us see.

33
00:01:51,330 --> 00:01:54,950
It is called upon a node in which data is eight.

34
00:01:55,140 --> 00:01:56,810
So that makes sense to us.

35
00:01:57,800 --> 00:01:58,070
Right.

36
00:01:58,610 --> 00:02:00,240
So let us continue.

37
00:02:00,410 --> 00:02:03,030
This is not so it has to perform three steps.

38
00:02:03,030 --> 00:02:05,360
The first step is call itself a child.

39
00:02:05,660 --> 00:02:09,650
Second is bring the data and tolis itself up on the chain.

40
00:02:09,919 --> 00:02:14,990
So the first step happens that is calling itself upon left chain left China Street.

41
00:02:15,260 --> 00:02:17,540
So it will call itself upon left child.

42
00:02:17,540 --> 00:02:18,220
That is three.

43
00:02:18,500 --> 00:02:20,980
And the second step printing it is done afterwards.

44
00:02:20,990 --> 00:02:22,490
Let us finish this then.

45
00:02:22,730 --> 00:02:28,160
Calling upon the right child will be done after what facilities finish this function that is in order

46
00:02:28,160 --> 00:02:28,760
upon order.

47
00:02:28,760 --> 00:02:30,040
Three four No.

48
00:02:30,050 --> 00:02:30,800
Three is not.

49
00:02:31,130 --> 00:02:37,860
Yes again three steps call upon left child print and call upon right chain call upon left child print

50
00:02:37,940 --> 00:02:39,110
and call upon the child.

51
00:02:39,320 --> 00:02:47,180
So first step is called upon left child in order of left child in order for first it will call itself

52
00:02:47,180 --> 00:02:49,190
up on left then other two steps.

53
00:02:49,190 --> 00:02:50,380
First let us finish this.

54
00:02:50,390 --> 00:02:52,400
Then we will perform other two steps.

55
00:02:53,580 --> 00:03:00,070
Then what about this rally for it is not known the again performed three steps, so go on left side.

56
00:03:00,360 --> 00:03:04,710
So this time it will be done in order of no.

57
00:03:06,210 --> 00:03:08,010
In order of not, nothing will happen.

58
00:03:09,350 --> 00:03:11,030
Then what is the second step?

59
00:03:12,610 --> 00:03:20,080
Rental data for this time, it will bring the data value for this printed, then what is the next step?

60
00:03:20,320 --> 00:03:24,500
Go on the right side for what is there on the right side, off for null.

61
00:03:24,790 --> 00:03:30,070
So again, for the right side also, it is in order of null, then nothing will happen.

62
00:03:30,790 --> 00:03:32,740
See, this is not a value zero.

63
00:03:32,920 --> 00:03:39,420
Actually, I'm showing values here, but here I have to show some zero to show that this is not next.

64
00:03:39,490 --> 00:03:40,390
This has finished.

65
00:03:40,750 --> 00:03:47,830
It will go back to this node that is third nor this one for this first step is completed upon left side.

66
00:03:47,830 --> 00:03:48,850
Everything is completed.

67
00:03:49,080 --> 00:03:52,660
Now the next step is print circle printed data.

68
00:03:52,840 --> 00:03:54,280
The value is three right here.

69
00:03:54,280 --> 00:03:55,480
Already we have the value three.

70
00:03:55,810 --> 00:04:02,020
So frustrated that speaker was found on the next value is the three then to distribute has to perform.

71
00:04:02,230 --> 00:04:05,920
That is go on the right hand side of the three nor the three.

72
00:04:06,160 --> 00:04:06,790
What is there?

73
00:04:07,000 --> 00:04:10,540
It will call upon node nine again.

74
00:04:10,540 --> 00:04:15,460
Three steps go to left in the value go right for left.

75
00:04:15,460 --> 00:04:17,040
It is null nothing.

76
00:04:17,410 --> 00:04:21,670
So print nine nine then go on the right side.

77
00:04:21,880 --> 00:04:22,650
There is nothing.

78
00:04:23,080 --> 00:04:24,190
This is completed.

79
00:04:24,430 --> 00:04:25,840
So this is also Kongregate.

80
00:04:25,840 --> 00:04:31,870
All three steps go back then here eight does the step remaining.

81
00:04:31,870 --> 00:04:36,010
See this portion is completed totally completed eight eight.

82
00:04:36,250 --> 00:04:37,960
Then go to the right side so far.

83
00:04:37,960 --> 00:04:39,460
Right side, right side.

84
00:04:39,470 --> 00:04:49,870
Note this five in order of five for this go to left side in order of seven other two steps remaining

85
00:04:49,870 --> 00:04:53,110
meaning four in order of seven left side in order of zero.

86
00:04:53,110 --> 00:04:53,920
Nothing is there.

87
00:04:54,190 --> 00:04:57,600
Then print seven in order of zero, nothing is done.

88
00:04:57,880 --> 00:04:59,700
So seven is printed.

89
00:05:00,100 --> 00:05:01,120
This is completed.

90
00:05:01,120 --> 00:05:05,260
Go back to this one print five call upon the right side.

91
00:05:05,320 --> 00:05:09,670
The right side is to do for this upper left side.

92
00:05:09,670 --> 00:05:14,560
Nothing is there printed to then up on the right side nothing is ten.

93
00:05:14,680 --> 00:05:15,550
So there's nothing.

94
00:05:15,550 --> 00:05:16,490
The Old Dominion.

95
00:05:16,510 --> 00:05:16,810
Right.

96
00:05:18,370 --> 00:05:21,850
So the values that were printed after this was five and two.

97
00:05:22,180 --> 00:05:27,090
So the output is four three nine eight seven five two.

98
00:05:27,730 --> 00:05:30,850
Let us do it directly by moving finger around it.

99
00:05:31,060 --> 00:05:34,620
I said that four in order to keep your finger on the top of this node.

100
00:05:34,630 --> 00:05:34,960
Right.

101
00:05:35,170 --> 00:05:37,900
Put it in this direction right up upward direction.

102
00:05:38,320 --> 00:05:39,400
So let us move around.

103
00:05:41,270 --> 00:05:46,610
We reached the bottom of four four, then get inside, we reached the bottom of three, yes, three,

104
00:05:47,270 --> 00:05:55,370
then bottom of nine, yes, nine, then get inside the board room of eight eight, bottommost seven

105
00:05:55,370 --> 00:06:00,710
seven, Watmough five five and bottom of two to finished.

106
00:06:00,890 --> 00:06:01,970
So answer is correct.

107
00:06:02,840 --> 00:06:07,590
I have shown you three methods in altering the cards, I was taking the bottom of a order.

108
00:06:08,120 --> 00:06:14,720
So if you observe here in order to see printing is done in between them, in between printing, this

109
00:06:14,720 --> 00:06:17,410
is done in between left cycle and right.

110
00:06:18,080 --> 00:06:20,930
So between left and right, printing is done.

111
00:06:21,290 --> 00:06:24,320
So that's how you say move your finger around like this.

112
00:06:25,510 --> 00:06:35,290
See, four, then three, then nine, then eight is what I have shown you in preorder, if you observe

113
00:06:35,320 --> 00:06:39,900
or if you go back to the previous video and watch, preorder printing was done first.

114
00:06:39,910 --> 00:06:41,410
So it was on the left hand side.

115
00:06:41,650 --> 00:06:45,180
What happens to the post starter printing will be done afterwards.

116
00:06:45,220 --> 00:06:45,820
Yes.

117
00:06:47,690 --> 00:06:51,990
So if you want to modify this function right on parenticide, then they're supposed to.

118
00:06:52,730 --> 00:06:56,290
So I don't have to show you Strata is a student exercise.

119
00:06:56,300 --> 00:06:57,010
You can do it.

120
00:06:57,230 --> 00:06:58,370
It's a small function.

121
00:06:58,730 --> 00:07:00,010
You can write on that function.

122
00:07:00,800 --> 00:07:02,420
So bending down afterwards.

123
00:07:03,590 --> 00:07:06,630
No, let us do some analysis total, how many calls?

124
00:07:07,070 --> 00:07:14,180
One, two, three, four, five, six, seven, eight, nine, 10, 11, 12, 13, 14, 15, 15 calls.

125
00:07:14,180 --> 00:07:16,840
We saw them in preordering for 15 calls for their.

126
00:07:17,060 --> 00:07:19,020
How many nodes are data and nodes.

127
00:07:19,040 --> 00:07:20,240
Seven nodes are there.

128
00:07:20,630 --> 00:07:21,000
Right.

129
00:07:21,050 --> 00:07:26,060
Then how many pointers and plus one point for each node a call is made.

130
00:07:26,240 --> 00:07:28,400
And also final point as calls are made.

131
00:07:28,610 --> 00:07:30,470
So doodle calls are two plus one.

132
00:07:31,190 --> 00:07:33,070
So total calls are two plus one.

133
00:07:33,710 --> 00:07:36,860
Now, one thing I have to tell you, I did not say that I didn't preorder.

134
00:07:37,310 --> 00:07:39,870
What is the time complexity of this traversal?

135
00:07:40,700 --> 00:07:46,100
It depends on how much work it is doing what the work it is doing, it is calling and every call it

136
00:07:46,100 --> 00:07:53,420
is trying to print, if possible, just printing one step only how many times it is printing depends

137
00:07:53,420 --> 00:07:54,440
on the number of calls.

138
00:07:54,440 --> 00:07:56,530
So how many calls are there going plus one.

139
00:07:56,750 --> 00:07:58,640
So what is the degree of this polynomial.

140
00:07:58,760 --> 00:08:06,640
And so the time complexity of traversal is and remember this, the time, complexity of all the transactions

141
00:08:06,920 --> 00:08:11,780
or whatever you do up on a binary tree most of the time it is now.

142
00:08:11,890 --> 00:08:14,990
Next, let us look at the order in which the calls are made.

143
00:08:15,560 --> 00:08:18,620
Since the first call was the second call.

144
00:08:19,070 --> 00:08:28,160
Third, fourth, fifth column, go back sixth column, then come the side seven on, go back and come

145
00:08:28,160 --> 00:08:28,790
the site.

146
00:08:28,790 --> 00:08:34,280
It can go back and same nine call and call Leinwand.

147
00:08:36,210 --> 00:08:46,650
Go back and call the site to Worldcon 13, 14, gone 15, you can observe that the order in which the

148
00:08:46,650 --> 00:08:50,520
calls are made is the same as preorder in preorder.

149
00:08:50,520 --> 00:08:56,160
Also, same thing was happening because the function is calling itself two times upon the same tree.

150
00:08:58,440 --> 00:09:00,540
Then same amount of calls should be done.

151
00:09:01,110 --> 00:09:05,040
But the difference in preordering in this one is printing was done before.

152
00:09:05,370 --> 00:09:07,490
Printing is done in between two calls.

153
00:09:07,500 --> 00:09:08,630
That's only the difference.

154
00:09:09,000 --> 00:09:10,240
So the calls will be similar.

155
00:09:10,410 --> 00:09:10,910
What is the limit?

156
00:09:10,920 --> 00:09:12,030
The size of the stack?

157
00:09:13,280 --> 00:09:19,670
Size of the stack that we see, that depends on the height of a tree height plus two, we analyzed that

158
00:09:19,670 --> 00:09:22,540
time, so the size of the stack will be high school.

159
00:09:23,030 --> 00:09:25,700
So you can see all of that height of a tree.

160
00:09:26,030 --> 00:09:32,860
If you remember, height of a tree can range from logging to end well.

161
00:09:32,900 --> 00:09:34,690
And here is the number of nodes.

162
00:09:34,940 --> 00:09:37,330
So, you know, for example, the this log in.

163
00:09:37,970 --> 00:09:41,250
But if it is a skill tree, then the height will be enough.

164
00:09:41,630 --> 00:09:42,960
So it depends on the height.

165
00:09:43,280 --> 00:09:46,150
So usually we don't write out of any other variable.

166
00:09:46,160 --> 00:09:47,750
We don't use, we use and only.

167
00:09:48,110 --> 00:09:50,720
So that means either can be logging or.

168
00:09:50,780 --> 00:09:53,620
And so many logging maximums.

169
00:09:53,880 --> 00:09:56,800
And so that's all about in order and postal order.

170
00:09:56,840 --> 00:10:00,510
Also give you the idea you have to write on the function for post traumatic.

