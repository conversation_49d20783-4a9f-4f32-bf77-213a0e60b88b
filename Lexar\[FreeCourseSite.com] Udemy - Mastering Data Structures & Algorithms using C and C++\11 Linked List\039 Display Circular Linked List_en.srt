1
00:00:00,150 --> 00:00:06,810
In this video, we learn about how to display a circular link linguist so we already know how to display

2
00:00:07,290 --> 00:00:11,330
linear linguists, so the procedure will be seen, definitely.

3
00:00:12,090 --> 00:00:15,900
But the differences in linear linguists last night was having none of that.

4
00:00:15,910 --> 00:00:18,690
So we were stopping there, but now they are circular.

5
00:00:18,720 --> 00:00:23,500
So if you start from this more than if you go on accessing, you may be reaching back on the same note.

6
00:00:23,520 --> 00:00:24,900
So here we have to stop.

7
00:00:25,200 --> 00:00:29,630
When you are reaching on the starting point again, then the list has finished.

8
00:00:30,180 --> 00:00:33,360
So let us see how we can do this one sidelight on the code.

9
00:00:34,080 --> 00:00:40,610
So I will write on a function for displaying our circle of Lincolnesque display function.

10
00:00:41,130 --> 00:00:45,210
It takes a pointer to had not been appointed to head node.

11
00:00:45,510 --> 00:00:53,910
So when we call this function, we should call it like this display and pass the pointer hand.

12
00:00:55,350 --> 00:01:01,600
So if you pass the pointer had done this function is called let us write the function soapies here or

13
00:01:01,600 --> 00:01:02,510
Skippy's here.

14
00:01:02,730 --> 00:01:05,700
So let us see the procedure, how to display it.

15
00:01:05,700 --> 00:01:08,460
And simultaneously I relied on instructions here.

16
00:01:09,970 --> 00:01:15,490
I have a point that people are counting on, her leadership is pointing on her nor Brindusa data.

17
00:01:15,700 --> 00:01:19,440
So so Breytenbach percentile beebees data.

18
00:01:19,660 --> 00:01:25,520
So please do this then move Pitou next, not be assigned piece next.

19
00:01:25,630 --> 00:01:29,590
So we will move to the next node or get people to Nixonland.

20
00:01:30,580 --> 00:01:36,310
Now, next, what I should do, repeat the same thing and move next trend and move next.

21
00:01:36,610 --> 00:01:38,740
So I will ride on it in a loop.

22
00:01:38,980 --> 00:01:39,790
Why loop.

23
00:01:41,030 --> 00:01:50,180
So what should be the condition here, let us see this imprint and move next footprint and move next.

24
00:01:50,720 --> 00:01:53,870
Suppose Bezier print this and move next.

25
00:01:54,950 --> 00:01:57,110
Bring this and move next.

26
00:01:58,080 --> 00:02:01,740
Apprentice and move next, we are back on hand.

27
00:02:02,190 --> 00:02:04,830
So, no, should not break not we should not.

28
00:02:05,400 --> 00:02:08,979
We should stop when it has reached head once again.

29
00:02:09,449 --> 00:02:10,940
So what condition I should take?

30
00:02:12,320 --> 00:02:16,210
So I should continue when she's not equal to her.

31
00:02:17,640 --> 00:02:20,310
While he is not equal to her.

32
00:02:21,540 --> 00:02:23,320
So while fees not equal to her.

33
00:02:24,360 --> 00:02:31,520
If I write this condition, the problem is first time only equal to Head Start will not enter at all.

34
00:02:31,530 --> 00:02:32,870
It will not print anything.

35
00:02:33,300 --> 00:02:35,630
So this cannot be done using Willerton.

36
00:02:35,820 --> 00:02:36,980
This cannot be done using.

37
00:02:36,990 --> 00:02:41,660
Why do I have to do this using do I look, do I look?

38
00:02:42,360 --> 00:02:45,640
Because first time only is equal to her so it will not enter in.

39
00:02:46,500 --> 00:02:50,910
We are talking about second time when it is coming on harder then we should stop.

40
00:02:51,070 --> 00:02:52,740
So better use.

41
00:02:52,790 --> 00:02:56,340
Do I look so first of all, let it print and move next.

42
00:02:56,340 --> 00:02:58,830
Then we will see if it is coming back on head.

43
00:02:59,070 --> 00:03:03,900
So this has to be written as to why do line be not equal to head.

44
00:03:04,200 --> 00:03:06,150
So this do while will work perfectly.

45
00:03:06,660 --> 00:03:11,030
So this function is for printing or circular linked list.

46
00:03:12,000 --> 00:03:16,380
Now let me write on a display function using recursion.

47
00:03:17,230 --> 00:03:18,370
It is a little difficult.

48
00:03:18,580 --> 00:03:21,720
It's not so easy like the other recursions we have seen.

49
00:03:22,270 --> 00:03:26,330
So let me read the function and explain to our display function.

50
00:03:26,400 --> 00:03:28,010
I'm going to make it to Kotcheff.

51
00:03:28,540 --> 00:03:35,980
This is taking parameter Naude, so let us call it by passing had had an order that is the beginning

52
00:03:36,370 --> 00:03:37,960
of a polygamist.

53
00:03:38,920 --> 00:03:46,270
Now, here in the previous recursion for a linear linguist, we were writing the commission, if we

54
00:03:46,390 --> 00:03:48,630
is not equal to none.

55
00:03:49,570 --> 00:03:52,780
This was a condition where we can only use this condition.

56
00:03:53,050 --> 00:03:56,520
We never get this condition satisfied because there is no novel here.

57
00:03:57,070 --> 00:04:03,420
Then what I have to say, be not equal to head, I should say, not equal to her.

58
00:04:03,460 --> 00:04:08,830
If I said then already you just had the first time and you possibly will be on this header and it will

59
00:04:08,830 --> 00:04:17,829
never enter inside, then what I mean to say here is that first time B and it will be seen and then

60
00:04:17,829 --> 00:04:19,800
the P moved around all the rules.

61
00:04:19,810 --> 00:04:24,430
Then again, back on the same note again, it will be on the same note for second time.

62
00:04:25,000 --> 00:04:28,030
If it is coming for the second time, then stop.

63
00:04:28,270 --> 00:04:35,170
So how do I know this is the first time or second time so far that I have to take some variable flag,

64
00:04:35,500 --> 00:04:36,760
so let us take a flag.

65
00:04:37,190 --> 00:04:43,110
So here I will declare a variable that is integer flag and this flag value is zero.

66
00:04:45,390 --> 00:04:53,070
So it's value zero means this is first time as flag values oneman, second time, so by the 011 value

67
00:04:53,080 --> 00:04:59,220
we can identify whether this is the first time that Panhead are on the same note or the second time.

68
00:05:00,240 --> 00:05:01,500
So that is right on the call.

69
00:05:01,890 --> 00:05:02,910
Let us right on this.

70
00:05:03,570 --> 00:05:08,280
If peace not equal to had our flag equal to zero.

71
00:05:09,120 --> 00:05:10,270
So this will continue.

72
00:05:10,590 --> 00:05:13,570
See first time peace equal to head.

73
00:05:13,740 --> 00:05:16,440
So this is false, but it's like a zero.

74
00:05:16,510 --> 00:05:17,340
Yes, this is true.

75
00:05:18,060 --> 00:05:24,270
Then I will print that piece of data is printed and call it self again.

76
00:05:24,540 --> 00:05:27,710
Call itself again for the next person.

77
00:05:28,800 --> 00:05:33,750
But once it has a print, enter inside, then flag should become one.

78
00:05:34,560 --> 00:05:36,170
I should make a flag as one.

79
00:05:36,180 --> 00:05:39,530
So here inside I will make a flag as one.

80
00:05:39,870 --> 00:05:41,220
Let us praise this one.

81
00:05:41,460 --> 00:05:44,280
Let us stress this first time please.

82
00:05:44,280 --> 00:05:46,070
Here be not equal to head.

83
00:05:46,320 --> 00:05:47,520
No, no it's equal.

84
00:05:47,760 --> 00:05:50,310
False flag is a zero yes.

85
00:05:50,310 --> 00:06:00,180
Flag and zero then enter inside make flag as one flag will become no one flag will become one then print

86
00:06:00,180 --> 00:06:02,370
the value and call it for the next node.

87
00:06:02,550 --> 00:06:04,910
So be will be called upon next node.

88
00:06:05,580 --> 00:06:06,420
Not this time.

89
00:06:07,590 --> 00:06:11,730
B not equal to Espy's not equal to had this condition is true.

90
00:06:12,120 --> 00:06:13,830
Our flag equals to zero.

91
00:06:14,010 --> 00:06:15,480
No, no flag is equal to one.

92
00:06:15,720 --> 00:06:16,710
So this is false.

93
00:06:17,070 --> 00:06:17,820
This is true.

94
00:06:18,000 --> 00:06:19,750
But our condition is used here.

95
00:06:19,770 --> 00:06:21,510
So if any one is a true it is true.

96
00:06:21,750 --> 00:06:22,540
So this is true.

97
00:06:22,860 --> 00:06:23,490
Will continue.

98
00:06:23,730 --> 00:06:25,860
So it will print this one than this one.

99
00:06:25,860 --> 00:06:26,640
Than this one.

100
00:06:26,640 --> 00:06:27,390
Than this one.

101
00:06:27,800 --> 00:06:29,070
Then again, it comes here.

102
00:06:30,700 --> 00:06:36,460
So like it is calling itself again and again now, this time be not equal to no, no, it's equal to

103
00:06:36,460 --> 00:06:37,750
her false.

104
00:06:39,250 --> 00:06:40,480
Flag equals to zero.

105
00:06:40,510 --> 00:06:46,210
No, no flag is equal to one, so but the conditions are false, so it will come out of this one.

106
00:06:47,060 --> 00:06:54,550
And they will not enter inside, so when it is exiting, when it is exiting, I should make a flag again,

107
00:06:54,560 --> 00:06:56,060
zettl an exit.

108
00:06:56,980 --> 00:07:04,240
Now, one more thing is missing, see this flag variable for every call, a new variable will be created

109
00:07:04,990 --> 00:07:07,450
right inside the stack activation record.

110
00:07:07,840 --> 00:07:11,880
Two variables are used by this function node, pointer and flag.

111
00:07:12,100 --> 00:07:15,800
So every time two new variables will be created inside the stack.

112
00:07:16,130 --> 00:07:17,860
I'll let you know how Stack works.

113
00:07:17,890 --> 00:07:19,380
I'm not going to trace this one now.

114
00:07:19,660 --> 00:07:23,050
So every time flag becomes a zero, so it's not the same flag.

115
00:07:23,500 --> 00:07:29,980
What I want is for all recursive calls, then what I want is for all recursive call.

116
00:07:29,980 --> 00:07:31,360
There should be only one flag.

117
00:07:32,080 --> 00:07:36,040
Then I should declare the flag outside as a global variable.

118
00:07:36,550 --> 00:07:42,090
Or if I want to keep it only inside this function, then I should make it as static variable.

119
00:07:43,300 --> 00:07:49,300
So static variable will be just like a global variable only, but limited to a function that is accessible

120
00:07:49,300 --> 00:07:50,550
only inside a function.

121
00:07:50,950 --> 00:07:56,590
So there will be just one single copy of Flag Variable for all the function calls.

122
00:07:56,800 --> 00:08:00,930
So how many times a function will call itself one, two, three, four, five?

123
00:08:01,240 --> 00:08:07,150
Then again here, six, four, six calls it will make and at the sixth call make the flag at zero or

124
00:08:07,150 --> 00:08:07,860
determinates.

125
00:08:08,980 --> 00:08:18,450
So finally, for writing a recursive function upon linked list, we have to use static variables.

126
00:08:18,610 --> 00:08:19,420
So this is static.

127
00:08:19,420 --> 00:08:25,870
Variable will help us know whether this is for the first time being heard or seen, or is it the second

128
00:08:25,870 --> 00:08:26,830
time they seen.

129
00:08:27,220 --> 00:08:29,140
So I'm using flag for 011.

130
00:08:29,350 --> 00:08:29,680
That's.

