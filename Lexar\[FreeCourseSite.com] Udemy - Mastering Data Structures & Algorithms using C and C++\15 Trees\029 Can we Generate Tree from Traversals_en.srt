1
00:00:00,300 --> 00:00:07,650
So the topic is, can we generate a tree from Tattersall's so from traversal we want to generate the

2
00:00:07,660 --> 00:00:10,380
tree actually from the tree we find traverses.

3
00:00:10,710 --> 00:00:13,240
So you already know traversal can insulate the tree.

4
00:00:13,740 --> 00:00:15,070
There are three diversions.

5
00:00:15,120 --> 00:00:20,730
So we want to know that they're just preorder will help us to generate the tree or not or we just bought

6
00:00:20,730 --> 00:00:25,990
started can find or not or preorder a postcard or if they are given can we generate a tree.

7
00:00:26,280 --> 00:00:33,810
So finally we'll find out which travel zones are compulsory so that we can generate a tree from those.

8
00:00:33,840 --> 00:00:38,540
Traviss So that's what our discussion is, which travel sales are compulsory.

9
00:00:38,550 --> 00:00:39,410
So let us start.

10
00:00:40,200 --> 00:00:41,820
I have taken three notes here.

11
00:00:41,820 --> 00:00:45,390
These are three notes, three known alongside that.

12
00:00:46,470 --> 00:00:54,940
Now, suppose a tree with a tree in the world is having a preorder traversal as ABC.

13
00:00:55,290 --> 00:00:58,590
Now I have just a preorder traversal for the tree with the tree.

14
00:00:59,430 --> 00:01:04,220
Now the question is from the preorder, can we generate a tree or not?

15
00:01:04,860 --> 00:01:07,130
Let us check for that traversal.

16
00:01:07,440 --> 00:01:09,180
I will draw a tree with the tree.

17
00:01:09,180 --> 00:01:12,810
N e b.

18
00:01:13,560 --> 00:01:15,450
C yes.

19
00:01:15,450 --> 00:01:17,790
What is the preorder of this first aid?

20
00:01:17,790 --> 00:01:19,650
And B then C yes, I got a tree.

21
00:01:21,240 --> 00:01:31,290
I'm able to generate no, let us check one more if I draw a tree like this A, B, C, because the preorder,

22
00:01:32,040 --> 00:01:34,620
a, B, C, I'm getting some preorder.

23
00:01:35,960 --> 00:01:37,970
For this also, I'm getting some preorder.

24
00:01:39,300 --> 00:01:45,540
I'll try this A, B, C for this also same preorder.

25
00:01:46,710 --> 00:01:48,960
A, B, then C.

26
00:01:51,420 --> 00:01:59,840
Then it's fine preorder A, B, C, I'm going to simply order ABC, simply order from the preorder if

27
00:01:59,840 --> 00:02:05,590
we try to generate a three five trees are possible for three elements.

28
00:02:05,590 --> 00:02:07,100
Five PS are possible.

29
00:02:07,570 --> 00:02:10,330
Actually, we want one tree, one unique tree.

30
00:02:10,539 --> 00:02:12,130
So we cannot generate a tree.

31
00:02:12,130 --> 00:02:18,010
We cannot find out because there are multiple trees are possible which are giving same preorder for

32
00:02:18,010 --> 00:02:18,720
three elements.

33
00:02:18,730 --> 00:02:23,260
So conclusiveness if only preorder is given, we can articulate claim.

34
00:02:24,310 --> 00:02:30,540
But one observation is that the same preorder how many different trees can be generated.

35
00:02:30,550 --> 00:02:32,080
One, two, three, four, five.

36
00:02:32,560 --> 00:02:35,120
If you remember, these are five different shapes.

37
00:02:35,270 --> 00:02:38,890
Yes, if preorder is given, only priority is given.

38
00:02:38,890 --> 00:02:46,920
Then how many trees we can generate which are having same preorder two and C and by and plus one year

39
00:02:46,930 --> 00:02:49,200
catalog number of trees we can generate.

40
00:02:49,930 --> 00:02:52,060
Not one actually we wanted one.

41
00:02:52,600 --> 00:02:54,860
Not possible next.

42
00:02:56,470 --> 00:02:58,220
What about if I give you an order.

43
00:02:58,690 --> 00:02:59,350
Same time.

44
00:02:59,830 --> 00:03:01,550
Same thing for the trees I can give it.

45
00:03:03,010 --> 00:03:08,790
What about we'll start sending five different trees I can generate which will give me a simple start.

46
00:03:09,340 --> 00:03:11,170
So this is true.

47
00:03:11,410 --> 00:03:13,550
This is true for all travellers.

48
00:03:13,630 --> 00:03:16,810
If you give any traversal more than one, trees are possible.

49
00:03:16,840 --> 00:03:19,300
So how many trees are possible which are satisfying.

50
00:03:19,300 --> 00:03:23,380
Seem traversal 20 plus one next.

51
00:03:24,630 --> 00:03:31,220
Shall I give you two traversal, can you generate a tree from two traversal, I have already given preorder.

52
00:03:31,530 --> 00:03:34,780
I'll give you post starter also post order.

53
00:03:34,970 --> 00:03:38,680
Post order is C, B, a.

54
00:03:39,750 --> 00:03:44,010
So I am giving to traversal this one and this one to travel since.

55
00:03:44,340 --> 00:03:47,070
Can you generate a tree for those two traversal.

56
00:03:48,850 --> 00:03:53,660
What is the preorder ABC preorders ABC, right?

57
00:03:54,820 --> 00:03:58,480
What is special starter of this one, CBA, CBA?

58
00:03:59,560 --> 00:04:00,580
What is the preorder?

59
00:04:00,610 --> 00:04:03,690
ABC preorders ABC.

60
00:04:04,960 --> 00:04:05,880
What is postpartum?

61
00:04:07,900 --> 00:04:10,300
What is post-mortem from this site, right?

62
00:04:11,390 --> 00:04:20,430
C, B, A, for starters, C, B, E, C, more than one trees are having the same preordering.

63
00:04:20,430 --> 00:04:28,460
Infostrada So if you give preorder and post are also more than one, trees can have same preorder and

64
00:04:28,460 --> 00:04:29,120
post order.

65
00:04:29,420 --> 00:04:30,650
So which tree you want.

66
00:04:30,800 --> 00:04:32,480
Actually we want a unique tree.

67
00:04:32,480 --> 00:04:35,320
Just one single tree, one single tree.

68
00:04:35,870 --> 00:04:38,010
So we try to preorder and post.

69
00:04:38,540 --> 00:04:39,460
It's not possible.

70
00:04:39,470 --> 00:04:44,870
So that's all is if you just give preorder.

71
00:04:47,030 --> 00:04:48,620
Or BORSTAR the.

72
00:04:50,950 --> 00:05:01,300
Or in order, you cannot generate just one tree total to NCM by and plus one, trees are possible,

73
00:05:01,300 --> 00:05:02,210
not one tree.

74
00:05:02,260 --> 00:05:05,380
You cannot generate if this is given this one or this one.

75
00:05:05,800 --> 00:05:06,550
Or this one.

76
00:05:07,930 --> 00:05:16,160
If I give preorder and boustani, then one plus I hope that is more than one.

77
00:05:16,180 --> 00:05:20,980
There is no formula for that because there are many possibilities in this one, many cases out there.

78
00:05:21,100 --> 00:05:22,900
So there is no formula for this.

79
00:05:22,930 --> 00:05:24,490
I cannot tell you this.

80
00:05:24,490 --> 00:05:25,400
Many are sure.

81
00:05:25,420 --> 00:05:26,790
No, not possible.

82
00:05:27,250 --> 00:05:29,060
So more than one that is sufficient.

83
00:05:29,350 --> 00:05:34,750
So if only preorders for starters, given more than one batteries are possible, one tree is not possible,

84
00:05:34,930 --> 00:05:37,120
then Richard Conversos can give me three.

85
00:05:37,450 --> 00:05:38,560
So I'll write them here.

86
00:05:38,860 --> 00:05:39,490
Yes.

87
00:05:39,910 --> 00:05:41,470
Preorder plus order.

88
00:05:42,650 --> 00:05:51,310
Either this one or second one, for starters, plus in order anyone via in order, because in order

89
00:05:51,460 --> 00:05:55,360
to take the route in the middle so fast, it gives the left that route then.

90
00:05:55,390 --> 00:05:55,710
Right.

91
00:05:55,910 --> 00:06:01,040
So in order can help you, if you know the route, then what should go on left side, what should go

92
00:06:01,040 --> 00:06:01,810
on right side.

93
00:06:02,300 --> 00:06:06,610
It can help you decide the splitting of north in the left subtree.

94
00:06:06,620 --> 00:06:07,030
All right.

95
00:06:08,500 --> 00:06:14,830
That's why in order is mandatory, alone in order will not help you alone, pre or post order will not

96
00:06:14,830 --> 00:06:16,180
help you together.

97
00:06:16,180 --> 00:06:20,700
Preorder for starter also will not help you then in order is must.

98
00:06:20,890 --> 00:06:27,400
So conclusion is that one of the traversal must be in order and the other traversal can be either preorder

99
00:06:27,400 --> 00:06:27,960
or post on.

