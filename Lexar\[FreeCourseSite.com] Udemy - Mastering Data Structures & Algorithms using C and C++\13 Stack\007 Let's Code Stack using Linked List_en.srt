1
00:00:00,360 --> 00:00:05,400
And this video, we will try to see language program for start using lingo.

2
00:00:08,060 --> 00:00:08,300
Link.

3
00:00:11,800 --> 00:00:14,050
And the language is see language.

4
00:00:16,309 --> 00:00:17,320
Project is ready.

5
00:00:19,130 --> 00:00:22,370
Now for implementing a stock using <PERSON><PERSON>'s first of all, I should have a.

6
00:00:22,730 --> 00:00:28,820
So even I can use the existing program for lintels already we have it on the program for Linklaters,

7
00:00:28,820 --> 00:00:29,810
that same functions.

8
00:00:29,810 --> 00:00:32,840
I can use them here, but I will write them again here.

9
00:00:33,440 --> 00:00:35,150
<PERSON><PERSON>ck Naude.

10
00:00:35,240 --> 00:00:40,850
First of all, I should have a structure for node which should have some data and also a pointer to

11
00:00:41,360 --> 00:00:42,380
next Naut<PERSON>.

12
00:00:42,380 --> 00:00:45,710
So a structure node and the pointer name is next.

13
00:00:46,550 --> 00:00:50,660
So this is our definition of node of a Linkous.

14
00:00:52,020 --> 00:00:56,720
Then here are two points that I need sort of point that I will take it as a global variable and I will

15
00:00:56,720 --> 00:00:57,860
initialize it as null.

16
00:00:57,890 --> 00:01:03,010
So even I can declare it inside main function and pass it as parameter to the functions, otherwise

17
00:01:03,010 --> 00:01:04,129
sticking it as global.

18
00:01:04,129 --> 00:01:08,860
If I have only one stock in my program, then I can go for this method.

19
00:01:09,530 --> 00:01:10,810
But this is not recommended.

20
00:01:10,820 --> 00:01:17,090
We should have a pointer inside the main function and it should pass it as parameter to the other functions.

21
00:01:18,580 --> 00:01:23,680
Now, the functions that I should have are Bush and Pop, so I will ride on only three functions, that

22
00:01:23,680 --> 00:01:25,540
is Bush pop and it functions.

23
00:01:26,460 --> 00:01:32,010
Wide Bush function, this push function should take a value to be inserted and it should put it into

24
00:01:32,010 --> 00:01:34,520
the stack, so let us implement this function.

25
00:01:34,920 --> 00:01:41,760
So this function should, first of all, create a new node struck node using a temporary point at the.

26
00:01:43,170 --> 00:01:47,760
And that is a sign I should created using my log function.

27
00:01:49,290 --> 00:01:55,530
I should create created using my log function, struttin all the time, posting and call a log function

28
00:01:56,040 --> 00:02:00,750
and how much I say they need, I should mention, a structure name.

29
00:02:02,620 --> 00:02:06,030
So this will be a normal then these data should.

30
00:02:08,020 --> 00:02:13,440
Then before pushing, I should check whether the stack is full here, the stack is not having any size,

31
00:02:13,750 --> 00:02:18,700
so if it is not created, it means the state is full, actually heap is full.

32
00:02:18,710 --> 00:02:25,420
So I can give a message so I can give a message that stack is full of stack as full.

33
00:02:27,010 --> 00:02:28,510
And also, I'll give a new line.

34
00:02:31,590 --> 00:02:36,180
Otherwise, I can otherwise I can insert an old.

35
00:02:37,180 --> 00:02:42,130
So for inserting a. all that, you have created a new one, then I should set the date for this known

36
00:02:42,430 --> 00:02:47,110
as X and said that the next pointer as top.

37
00:02:49,960 --> 00:02:52,300
And OP should be pointing to this, Norb.

38
00:02:55,040 --> 00:03:01,370
Pete, you know one thing, I should do all these things inside and spot that is.

39
00:03:03,030 --> 00:03:08,320
If stock is not full, then I should do all these things that should be enclosed inside else.

40
00:03:09,150 --> 00:03:10,350
Yes, I have done it.

41
00:03:13,070 --> 00:03:17,690
Now, the next function is both function, which will delete the topmost element from the stack and

42
00:03:17,690 --> 00:03:19,310
it will return that element.

43
00:03:19,850 --> 00:03:23,630
So for deletion, first of all, it should check whether the stack is empty.

44
00:03:23,780 --> 00:03:27,740
If OP is equals to null, then the stack is empty.

45
00:03:28,920 --> 00:03:30,680
We cannot delete element.

46
00:03:30,680 --> 00:03:32,510
So you can print a message that.

47
00:03:34,530 --> 00:03:36,140
Stack is empty.

48
00:03:39,000 --> 00:03:39,940
A new line.

49
00:03:41,560 --> 00:03:46,170
Else you can delete an element, so for deleting an element, I need a temporary pointer, so I will

50
00:03:46,170 --> 00:03:52,860
take a temporary pointer stuck Naude Starky as a temporary pointer then.

51
00:03:54,000 --> 00:04:01,230
He should be pointing on the top note that is topmost node, then OP should move to the next node sign

52
00:04:01,240 --> 00:04:01,980
pops next.

53
00:04:03,000 --> 00:04:09,870
Then echoed the value that whatever we want to believe that is then echoed the value from the topmost

54
00:04:09,870 --> 00:04:18,240
NORAD data is pointing out that more than three, that in order to delete that note and for this, I

55
00:04:18,240 --> 00:04:21,790
need one more variable integer X and which is initialized to minus one.

56
00:04:21,810 --> 00:04:24,290
This will take the deleted element and it will return it.

57
00:04:24,960 --> 00:04:28,890
So at the end, I should say return X value X is return.

58
00:04:30,880 --> 00:04:36,460
That's sort of two functions I have written, not the last function as a display functions avoid display

59
00:04:36,460 --> 00:04:37,000
function.

60
00:04:38,200 --> 00:04:41,550
This will display all the elements so far that I have to scan through.

61
00:04:42,630 --> 00:04:47,760
So for that, I have to scan through a list of for that I have taken the number three pointer P for

62
00:04:47,760 --> 00:04:51,150
scanning through a link list, then be assigned top.

63
00:04:53,000 --> 00:04:55,200
Then why BP's not equal to none?

64
00:04:55,220 --> 00:05:00,360
That is not equal to none, I should say, controlling and display each and every element.

65
00:05:00,360 --> 00:05:01,310
So printf.

66
00:05:02,420 --> 00:05:05,720
Percentile to give some space.

67
00:05:06,640 --> 00:05:10,960
And I should display the elements using Pointer P. B's data.

68
00:05:12,720 --> 00:05:18,570
And after displaying all the elements, I should give a new line so for formatted output, I'll give

69
00:05:18,570 --> 00:05:19,110
a new line.

70
00:05:20,110 --> 00:05:21,670
That's all all these functions are.

71
00:05:22,100 --> 00:05:27,880
Now I can use main function, yeah, and one more thing, while it is scanning through a link less than

72
00:05:27,880 --> 00:05:29,850
we should move to the next NORK.

73
00:05:30,920 --> 00:05:36,380
Inside the main function, I will play a few functions for pushing and popping the elements and also

74
00:05:36,380 --> 00:05:42,910
display so I don't have to create a stack because already I have taken a top pointer as a global variable

75
00:05:43,520 --> 00:05:49,940
if I'm not going to appear as a global variable and all these functions as a barometer, as a stack.

76
00:05:51,020 --> 00:05:58,580
So I made it available so that I can call Bush and function, so let us call Bush function and Bush

77
00:05:58,580 --> 00:06:06,560
fuel values and there is no parliament, so there is no chance of getting fell because we have taken

78
00:06:06,560 --> 00:06:13,100
the limit as he signs, so that maybe taking a number of elements and pushing three elements, then

79
00:06:13,100 --> 00:06:17,150
I will call display function that should display all the elements.

80
00:06:17,600 --> 00:06:23,150
Then inside the printer I will call of function and displays.

81
00:06:24,440 --> 00:06:25,020
They dadon.

82
00:06:25,920 --> 00:06:26,940
Bob function.

83
00:06:28,660 --> 00:06:35,770
So that's one of the main function things here at Appointer, I have declared, so this is accessible

84
00:06:35,770 --> 00:06:37,350
for all the function disappointments.

85
00:06:37,420 --> 00:06:38,680
I should make it that star.

86
00:06:39,740 --> 00:06:42,070
I must be a star and.

87
00:06:43,250 --> 00:06:44,390
Let us run the program.

88
00:06:45,200 --> 00:06:49,940
Yes, these are the elements when I discipline them and the topmost element is 30, so I deleted that

89
00:06:49,940 --> 00:06:50,190
one.

90
00:06:50,600 --> 00:06:52,290
So Bob has given me the element.

91
00:06:53,180 --> 00:06:54,020
So that's all.

92
00:06:54,390 --> 00:07:01,610
And for utilizing this stock wherever if I need in my application, I need then I need this node structure

93
00:07:01,610 --> 00:07:05,810
and the appointer that is declared and the top of the global.

94
00:07:06,140 --> 00:07:08,150
Then I need Bush and pull functions.

95
00:07:08,330 --> 00:07:13,040
If I need any other function, I can write down that function for the stock I can use.

96
00:07:13,040 --> 00:07:16,880
This functions inside an application very Steiger's used.

97
00:07:19,910 --> 00:07:20,480
That's it.

