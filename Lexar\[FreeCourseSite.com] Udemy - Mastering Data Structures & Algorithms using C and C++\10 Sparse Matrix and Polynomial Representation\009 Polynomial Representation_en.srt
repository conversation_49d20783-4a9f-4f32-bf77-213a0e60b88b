1
00:00:00,480 --> 00:00:03,230
The topic is polynomial representation.

2
00:00:04,660 --> 00:00:12,330
And this topic, Villone, representation of polynomial evaluation, of polynomial addition of two polynomials,

3
00:00:12,910 --> 00:00:20,590
see, this is a polynomial univariate polynomial as a collection of atoms with a single variable X.

4
00:00:21,810 --> 00:00:28,800
How to represent that body and how to evaluate if you know the value of X and if there are more than

5
00:00:28,800 --> 00:00:34,590
one polynomials, how we can perform polynomial addition, we will Londis once you know, addition,

6
00:00:34,590 --> 00:00:39,490
we can also learn how to perform subtraction and multiplication of polynomials.

7
00:00:39,510 --> 00:00:43,290
Also, let us look at a representation of polynomial.

8
00:00:43,320 --> 00:00:45,160
Let us learn how to represent this one.

9
00:00:45,970 --> 00:00:49,080
See, this is the polynomial that we write using pen and paper.

10
00:00:49,740 --> 00:00:52,380
We use polynomial for solving various problems.

11
00:00:52,770 --> 00:00:59,310
Various things are represented in the formulas that a single variable formulas that univariate formulas.

12
00:00:59,460 --> 00:01:02,370
We use these type of expressions in problem solving.

13
00:01:02,910 --> 00:01:07,620
Not a problem is we want our programs to use these type of polynomial.

14
00:01:07,620 --> 00:01:09,420
Then what programs should be able to do?

15
00:01:09,720 --> 00:01:14,670
Our program should be able to evaluate the value of this polynomial.

16
00:01:14,670 --> 00:01:20,040
If the value of X is given or if there are more than one polynomial, they should be able to add or

17
00:01:20,040 --> 00:01:21,510
subtract or multiply them.

18
00:01:22,260 --> 00:01:28,560
So first of all, how our program should represent this polynomial, how we will store this polynomial

19
00:01:28,560 --> 00:01:29,400
in our program.

20
00:01:29,760 --> 00:01:32,520
So for representation, let us observe.

21
00:01:33,030 --> 00:01:36,170
See, a polynomial is nothing but a collection of atones.

22
00:01:36,330 --> 00:01:43,960
So there are total five doms here and in each item this value is equally efficient.

23
00:01:44,130 --> 00:01:49,830
So too is a good, efficient five years ago, efficient like that every day having coefficient.

24
00:01:50,070 --> 00:01:55,140
If there is no coefficient then it means that this one coefficient is one the next.

25
00:01:55,320 --> 00:02:00,600
This X is a variable, but this is a power that is exponential.

26
00:02:00,930 --> 00:02:03,590
So there is one more thing that is exponent.

27
00:02:04,500 --> 00:02:11,440
So this having exponent five and four and two one there is no X here means exponent zero.

28
00:02:11,580 --> 00:02:15,990
So now I can say that a polynomial is a collection of photons.

29
00:02:16,200 --> 00:02:24,300
Each term is having coefficient and exponent of X power of X so we can represent the polynomial as a

30
00:02:24,300 --> 00:02:30,480
collection of thumb very strongly is higher coefficient an exponent so I can represent this other list

31
00:02:30,480 --> 00:02:34,330
of garms having coefficients, an exponent.

32
00:02:34,650 --> 00:02:39,630
So let us prepare it as a list of two things.

33
00:02:39,960 --> 00:02:47,460
That is coefficients and exponent total five other.

34
00:02:50,210 --> 00:02:57,740
Now, first coefficient history three and Exponent is five coefficient is to explain this four coefficient

35
00:02:57,740 --> 00:03:04,970
is five and Exponent is two two one seven zero.

36
00:03:06,380 --> 00:03:09,240
So here I have a set of tones.

37
00:03:10,460 --> 00:03:16,550
This data is sufficient to represent a polynomial is then one more thing.

38
00:03:16,550 --> 00:03:20,180
We want to know that how many non zero terms hard data supports.

39
00:03:20,190 --> 00:03:25,760
If we want to know, then you can have that as a length of this one or the size of polynomial that is

40
00:03:25,760 --> 00:03:27,560
five zero five times data.

41
00:03:29,400 --> 00:03:37,710
So this is how polynomials represented now programmatically, how to represent this one in a program,

42
00:03:37,710 --> 00:03:43,410
how to represent this one so far C program, first of all, we will define that dumb dumb as having

43
00:03:43,420 --> 00:03:44,540
coefficient and exponent.

44
00:03:44,760 --> 00:03:47,280
So I will define it as a structure.

45
00:03:48,820 --> 00:03:58,210
Don, the storm should have integer coefficient C, this coefficient value three to five, they can

46
00:03:58,210 --> 00:04:00,520
be integer also four float also double.

47
00:04:00,520 --> 00:04:03,480
Also depending on the requirement, you can change the data type.

48
00:04:03,820 --> 00:04:08,110
And this exponent exponent is always integer type.

49
00:04:09,670 --> 00:04:15,400
So I have taken two members and this is a term I'm calling it a stone.

50
00:04:16,510 --> 00:04:22,000
Then what is a polynomial polynomial array of atoms or collection of atoms.

51
00:04:22,240 --> 00:04:26,160
So now let us define struck polynomial.

52
00:04:26,470 --> 00:04:27,880
So I write in short poly.

53
00:04:28,180 --> 00:04:36,880
Then a polynomial should have a number of non-zero terms, that is, and an array of these terms before

54
00:04:36,880 --> 00:04:38,350
the array of photons.

55
00:04:38,350 --> 00:04:45,070
Either I can declare an array of terms or else I can take a pointer and dynamically create an array

56
00:04:45,070 --> 00:04:45,880
of atoms.

57
00:04:46,980 --> 00:04:48,390
So I will take a point on here.

58
00:04:49,770 --> 00:04:50,490
Strutt.

59
00:04:52,260 --> 00:04:52,770
Tom.

60
00:04:54,600 --> 00:05:01,770
Point B, so this is a structure for polynomial polynomials using tongs.

61
00:05:02,040 --> 00:05:09,330
Now let us learn how to write the program code for creating the representation of any polynomial for

62
00:05:09,330 --> 00:05:11,300
creating the representation of this one.

63
00:05:11,520 --> 00:05:12,930
I will be writing the code.

64
00:05:12,990 --> 00:05:14,640
I will not write any function.

65
00:05:14,820 --> 00:05:15,220
Right.

66
00:05:15,270 --> 00:05:18,360
So in whichever function is suitable, we will put the code there.

67
00:05:18,660 --> 00:05:23,650
Just we are how the code looks like for creation or representation of this polynomial.

68
00:05:24,210 --> 00:05:27,610
So let us create C for creating this polynomial.

69
00:05:27,840 --> 00:05:29,630
I need a variable of type.

70
00:05:29,660 --> 00:05:31,780
This is structure that is poly structure.

71
00:05:31,800 --> 00:05:33,840
So let us say structure.

72
00:05:35,950 --> 00:05:36,580
Pauline.

73
00:05:38,090 --> 00:05:38,510
B.

74
00:05:40,200 --> 00:05:51,090
Now, this AP is having two members and an don't so be is having two members, one is and and second

75
00:05:51,090 --> 00:05:55,950
one is point A. This is for number of non-zero elements.

76
00:05:55,950 --> 00:06:00,930
And this is a pointer for the collection of Tormes or array of atoms.

77
00:06:01,680 --> 00:06:03,110
So I have this polynomial.

78
00:06:03,390 --> 00:06:07,140
The first thing that I should know is how many non-zero are there?

79
00:06:08,950 --> 00:06:14,110
I will take the number of non-zero elements from keyboard's, I will say printf and the number of non

80
00:06:14,110 --> 00:06:20,380
zero terms and total number of non zero bones, I should know how many drones are there and I should

81
00:06:20,380 --> 00:06:27,070
read it in this one so it can be taught and be or 10 percent daily.

82
00:06:27,160 --> 00:06:31,270
I personally don't see this as an object, so it's not a pointer.

83
00:06:31,270 --> 00:06:35,190
So P dot, I can access this number using dot operator dot.

84
00:06:35,200 --> 00:06:42,680
And so here I, I'm taking the value and the value given it's five so that us say this is five.

85
00:06:43,240 --> 00:06:50,650
Now I need an Audi of five Thoms so let us create this array dynamically.

86
00:06:50,950 --> 00:06:56,030
So here I write the code for creating that array dynamically using new operator.

87
00:06:56,470 --> 00:07:02,860
So B daughter T will assign array of dorm's of size five.

88
00:07:02,950 --> 00:07:08,140
So here are the code B daughter to assign betore to assign.

89
00:07:08,170 --> 00:07:09,930
So I will say new.

90
00:07:10,780 --> 00:07:14,530
So new audio for Thom's are four times here.

91
00:07:15,040 --> 00:07:19,200
So it is created in heap and instead this is taken in property, in property.

92
00:07:19,390 --> 00:07:22,930
And what should be the size of the study period in this size.

93
00:07:23,170 --> 00:07:27,970
So here an array of size of five will be created.

94
00:07:28,690 --> 00:07:32,050
So this is an array of size five coefficients, an exponent.

95
00:07:32,560 --> 00:07:35,450
So this is how it will look like inside me main memory.

96
00:07:35,620 --> 00:07:36,950
So polynomial is created.

97
00:07:37,180 --> 00:07:42,760
So one thing I want to show you here, see, when I say district only, just be so this will be created

98
00:07:42,760 --> 00:07:43,660
inside stack.

99
00:07:43,930 --> 00:07:47,100
And when you say a new, then this will be created in a heap.

100
00:07:47,590 --> 00:07:55,240
If we want this also in here, then I should say new here and I must take this as a pointer and then

101
00:07:55,240 --> 00:07:55,810
say new.

102
00:07:56,350 --> 00:08:04,330
So this is directly inside the stack and this array of atoms isn't heap now memories ready for storing

103
00:08:04,330 --> 00:08:05,050
that polynomial.

104
00:08:05,440 --> 00:08:08,200
Now let us write on the code for filling these values.

105
00:08:08,230 --> 00:08:14,440
So for filling that, we will take the input from the keyboard and ask the user to enter coefficient

106
00:08:14,950 --> 00:08:18,410
an exponent of each atom one by one.

107
00:08:18,940 --> 00:08:20,910
So let me know five times out there.

108
00:08:20,920 --> 00:08:24,290
Let us call this the first, second and third, fourth, fifth column.

109
00:08:24,640 --> 00:08:30,610
So one by one we ask the user to enter Coefficient and exponent, so I should share this coefficient.

110
00:08:31,090 --> 00:08:37,120
So the next coefficient is kind of exponent so repeatedly for five times.

111
00:08:37,330 --> 00:08:39,190
So using a for loop, I can do that.

112
00:08:39,190 --> 00:08:42,789
So I will write on the code here for reading all those values one by one.

113
00:08:43,390 --> 00:08:44,710
So here I give a message.

114
00:08:44,710 --> 00:08:52,270
Enter polynomial terms now using followed by the values for I define zero Islas then Teagarden that

115
00:08:52,270 --> 00:08:54,820
is or in an eight plus plus.

116
00:08:56,110 --> 00:09:02,300
Then here I should read ganef coefficient and exponent but before that I will display the total number.

117
00:09:02,950 --> 00:09:06,070
So here I am printing print and Darktown number.

118
00:09:06,340 --> 00:09:08,860
So total number I plus one I zero.

119
00:09:08,860 --> 00:09:10,900
So first it will be one, the next two.

120
00:09:11,080 --> 00:09:13,030
So it will be showing them what someone on what.

121
00:09:13,030 --> 00:09:17,700
So the user who is entering the data will know Vitit on his entry.

122
00:09:18,040 --> 00:09:23,010
So to help him I am printing number no no scantest those two values.

123
00:09:23,020 --> 00:09:24,580
So how to read the values.

124
00:09:24,910 --> 00:09:37,150
B daughter to be Dorte of zero zero bad coefficient dot exponent the next door to T also one dot coefficient

125
00:09:37,300 --> 00:09:38,290
dot exponent.

126
00:09:39,360 --> 00:09:53,250
So here I might be Dawid D of I don't coefficient Guriev, and this should be unperson then comma unperson

127
00:09:54,180 --> 00:09:59,050
b dot the of a dot exponent.

128
00:09:59,580 --> 00:10:03,540
So this for a loop will read all the values in that area.

129
00:10:04,140 --> 00:10:08,710
So here I know the non zero terms and here I have created this array.

130
00:10:09,720 --> 00:10:13,170
Then here I'm reading all the values in this.

131
00:10:14,760 --> 00:10:19,590
So from the keyboard, the values are entered and the values will be like the three five to for all

132
00:10:19,590 --> 00:10:20,750
these values reinterred.

133
00:10:21,360 --> 00:10:23,820
So this is how it is represented.

134
00:10:23,820 --> 00:10:25,550
And does the program called.

