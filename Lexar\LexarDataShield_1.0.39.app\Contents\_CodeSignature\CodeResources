<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/AppIcon.icns</key>
		<data>
		4vSm/BzXOnGRfGEztZjgAn7gRoU=
		</data>
		<key>Resources/Assets.car</key>
		<data>
		rWYTupDm9gQKBbXXY76H+LOpNc4=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/2cp-pQ-CzH-view-BVD-Tw-20T.nib</key>
		<data>
		jtuy0LhJYZHU9I9ZVr5GoPlvSVQ=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/4CM-FY-6Uq-view-4dg-y1-QOH.nib</key>
		<data>
		YkT7VuOKcVJniVoUPUHaki7zDkM=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/74I-xu-cgo-view-7KX-rf-5Ss.nib</key>
		<data>
		8RuCowO5n6O9CRLf8gRilYTUuko=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/ARc-Ej-hMx-view-aqB-n5-tG9.nib</key>
		<data>
		Uaz6rAhUMGa+4C7IQHyzlHINOl4=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/E3O-Ou-ZJh-view-cax-Yk-5z2.nib</key>
		<data>
		+6a4hzzPYR3VYazoL4ihtUAnemQ=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/H9x-Ch-kii-view-d5s-qM-iFz.nib</key>
		<data>
		cMJhsqn4R4OWaJ/qTWOzx4dCiI0=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/Hu9-el-o0D-view-QGd-uK-CpK.nib</key>
		<data>
		N4qQxEeeD1cQSJRkQMwZF6k5vbI=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		OPH+FrmF5R9mZy/LHtKTr7y8EU8=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/MainMenu.nib</key>
		<data>
		jYBSGVYTad5wq1/0xg4m9zlzLtQ=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/NSTabViewController-hwP-ca-1We.nib</key>
		<data>
		F1DVEPTGHWAcYE3dx+MfKeIvAgM=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/NSViewController-ZP2-6Z-1e9.nib</key>
		<data>
		cQgFs0GOS5DDy46zllZhhXVoVsQ=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/XXAgreeProtocalWindow.nib</key>
		<data>
		ucy+88xMumCrSSymTrLhC71H9ag=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/XXChangePWWindowController.nib</key>
		<data>
		3byLjmdrGjdF387rzrX7CVFLDIU=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/XXHelloWindowController.nib</key>
		<data>
		wLhLmB/QSzpD+jigP+GlXxo6DoI=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/XXMainViewController.nib</key>
		<data>
		FlpcClMp7ZNBxJZDbzD7XzwFXo4=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/XXMainWindowController.nib</key>
		<data>
		4FJiN/LR2WrQF5NmoAQQvhlKQO4=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/XXRegistWindowController.nib</key>
		<data>
		uzR+q6yOVivc1mbRWr/85L0cKo0=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/XfG-lQ-9wD-view-m2S-Jp-Qdl.nib</key>
		<data>
		j2LbvR3c6i4NM/gu89RA7RghHAw=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/ZP2-6Z-1e9-view-cI8-ec-blU.nib</key>
		<data>
		OK1fxgKeRtvIPoIeZgbH/zPq4wE=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/aboutUs.nib</key>
		<data>
		L9G8hEpwm11POPfcDYMc55r/p1g=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/checkVersion.nib</key>
		<data>
		XPUvrwyRyZDTwEqfrr4H7MgXEaQ=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/gmf-oM-26k-view-hrc-8A-njs.nib</key>
		<data>
		N6xhBZAGTMnbxknHYEFe8eTpT0g=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/protocalWindow.nib</key>
		<data>
		w7ypmvY6V1747m9daj293jpTbLc=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/sOX-2d-O7g-view-STb-Pb-ggO.nib</key>
		<data>
		Q/YYGPzuFRKBIlTvTJ7mKx0m2k0=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/tAf-7D-gQu-view-ILu-vr-dmw.nib</key>
		<data>
		ItV3ARB4kb65HdYTNPVEKRc9WIk=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/vvG-H9-N8c-view-ioK-D7-hao.nib</key>
		<data>
		mQpZsgD23HG2D1uT3SIcCmvbY8w=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/xTj-i5-hgw-view-E49-TW-JQi.nib</key>
		<data>
		pchqvc1phtNoMEq1DiuIPQ2sZBc=
		</data>
		<key>Resources/XXCollectionViewItem.nib</key>
		<data>
		pAp3NZ0Vwf8eGfqbxeom01JVH8E=
		</data>
		<key>Resources/XXFolderDetailInfoView.nib</key>
		<data>
		xUPdLGrPg2DG1sgxkYADoah9vdY=
		</data>
		<key>Resources/XXLocationSettingView.nib</key>
		<data>
		x5R9hLeepCmTLgQ8LwvJeY1tdGs=
		</data>
		<key>Resources/XXProgressAlertView.nib</key>
		<data>
		x7Q8b7DVhyKQWObJPXIMMiDzWyI=
		</data>
		<key>Resources/XXReminderView.nib</key>
		<data>
		gAHyzUaTzIMfe+emCI1Zu275Q1k=
		</data>
		<key>Resources/XXRsumeDataView.nib</key>
		<data>
		lxtMLisjKEP1e2t82NeMyyiG7lU=
		</data>
		<key>Resources/XXSelectedProtectFolderView.nib</key>
		<data>
		16DB7gyID/RNMQxmoJdfZqUjiZc=
		</data>
		<key>Resources/XXSingleInputAlertView.nib</key>
		<data>
		KdsjPNh91bnlNCFt1RMgrTyrTrE=
		</data>
		<key>Resources/XXTimeoutSettingView.nib</key>
		<data>
		a6wQ5gH1b50fYgR71oiMn5yKhBo=
		</data>
		<key>Resources/XXTransmissionErrorView.nib</key>
		<data>
		cpSL6iK+JsFjXPhH715eFUjNx5E=
		</data>
		<key>Resources/XXTwoBtnReminderView.nib</key>
		<data>
		PWii8B3togZDpgGR7j5iYijLacM=
		</data>
		<key>Resources/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9rDz6FTt12crAks7Dt7tr8oPAyM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			z2+z2qsKIEUwPvyev1MdukYwrdE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/AppIcon.icns</key>
		<dict>
			<key>hash</key>
			<data>
			4vSm/BzXOnGRfGEztZjgAn7gRoU=
			</data>
			<key>hash2</key>
			<data>
			1WOgwrL7j/7hne8b4VT8sqC0Ae7Qk4XNxTGjgRRUJ7E=
			</data>
		</dict>
		<key>Resources/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			rWYTupDm9gQKBbXXY76H+LOpNc4=
			</data>
			<key>hash2</key>
			<data>
			azV4VzMwWKkGXVGffKviJ1SMriaItcd5mIGlkWsZmTo=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/2cp-pQ-CzH-view-BVD-Tw-20T.nib</key>
		<dict>
			<key>hash</key>
			<data>
			jtuy0LhJYZHU9I9ZVr5GoPlvSVQ=
			</data>
			<key>hash2</key>
			<data>
			I2eZK5qixxa1qGDWntVJGbclgQI1LPEvVnWLagDNusM=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/4CM-FY-6Uq-view-4dg-y1-QOH.nib</key>
		<dict>
			<key>hash</key>
			<data>
			YkT7VuOKcVJniVoUPUHaki7zDkM=
			</data>
			<key>hash2</key>
			<data>
			480gZrUOrQ1iGSyfQPrcLbbbmRNUM8H9Sjh8jIH9vUc=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/74I-xu-cgo-view-7KX-rf-5Ss.nib</key>
		<dict>
			<key>hash</key>
			<data>
			8RuCowO5n6O9CRLf8gRilYTUuko=
			</data>
			<key>hash2</key>
			<data>
			roHZff7lhphwt5PMkF97hMgICMwoIWI4PIePQduxvk8=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/ARc-Ej-hMx-view-aqB-n5-tG9.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Uaz6rAhUMGa+4C7IQHyzlHINOl4=
			</data>
			<key>hash2</key>
			<data>
			nItC3L9Eh/SkPdZJF2JuHZZApKYT7wi4VfqbkhDrrpY=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/E3O-Ou-ZJh-view-cax-Yk-5z2.nib</key>
		<dict>
			<key>hash</key>
			<data>
			+6a4hzzPYR3VYazoL4ihtUAnemQ=
			</data>
			<key>hash2</key>
			<data>
			Vvg/iprcx2Sxxc+XaaPSRgbyiQwhBIlJHCyvnpLvgUs=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/H9x-Ch-kii-view-d5s-qM-iFz.nib</key>
		<dict>
			<key>hash</key>
			<data>
			cMJhsqn4R4OWaJ/qTWOzx4dCiI0=
			</data>
			<key>hash2</key>
			<data>
			FpUXhg5TGO4v+0l0U96NndQio8Xsi8Kq/+mEOfhQ6mE=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/Hu9-el-o0D-view-QGd-uK-CpK.nib</key>
		<dict>
			<key>hash</key>
			<data>
			N4qQxEeeD1cQSJRkQMwZF6k5vbI=
			</data>
			<key>hash2</key>
			<data>
			2RhVEASxp2rnlPGTH+fdBF7u2pNrEBaVBILCuyoYEzE=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			OPH+FrmF5R9mZy/LHtKTr7y8EU8=
			</data>
			<key>hash2</key>
			<data>
			MJZAZpkCWmLEeJc1ej3d9iCuJT+IxhJLt9DStpuT7YI=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/MainMenu.nib</key>
		<dict>
			<key>hash</key>
			<data>
			jYBSGVYTad5wq1/0xg4m9zlzLtQ=
			</data>
			<key>hash2</key>
			<data>
			IKzi2VzxGgfFiMgiOT2LPurlQG2jKBaJgvWVWSuBn0M=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/NSTabViewController-hwP-ca-1We.nib</key>
		<dict>
			<key>hash</key>
			<data>
			F1DVEPTGHWAcYE3dx+MfKeIvAgM=
			</data>
			<key>hash2</key>
			<data>
			nCNKwq1HfI8KRd4II6Y1yYDV8onLUjlpqFRflBFNgbE=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/NSViewController-ZP2-6Z-1e9.nib</key>
		<dict>
			<key>hash</key>
			<data>
			cQgFs0GOS5DDy46zllZhhXVoVsQ=
			</data>
			<key>hash2</key>
			<data>
			UvbohvlPNI95Gc0kPDdwFWKorjoUshMVkQqbAV6yf70=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/XXAgreeProtocalWindow.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ucy+88xMumCrSSymTrLhC71H9ag=
			</data>
			<key>hash2</key>
			<data>
			zbsKS11Q5/ZE0yQ6e7DqOQx4kla2ATrhRNHBY82MTYg=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/XXChangePWWindowController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			3byLjmdrGjdF387rzrX7CVFLDIU=
			</data>
			<key>hash2</key>
			<data>
			kr+6quujq3yBEnCL7vBLMuMq7zvLnurjPiOnWe6y4rU=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/XXHelloWindowController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			wLhLmB/QSzpD+jigP+GlXxo6DoI=
			</data>
			<key>hash2</key>
			<data>
			kA9L1deNurWnadL9UDzp3YV/AsBLMW5Y3g23pc7Rdnw=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/XXMainViewController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			FlpcClMp7ZNBxJZDbzD7XzwFXo4=
			</data>
			<key>hash2</key>
			<data>
			w5k4h3t2cjPok6k9H3NpoJFQ4UeSnr0cOSPm/+T+FyI=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/XXMainWindowController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			4FJiN/LR2WrQF5NmoAQQvhlKQO4=
			</data>
			<key>hash2</key>
			<data>
			SXOMVZxAPu8UdxQ1vbOP0/Vc3QzGATeH6Y+kzk1BrcY=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/XXRegistWindowController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			uzR+q6yOVivc1mbRWr/85L0cKo0=
			</data>
			<key>hash2</key>
			<data>
			u541AxFXSC3hgVtZoW211KY+42NTYqP85jw5B5Gqah8=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/XfG-lQ-9wD-view-m2S-Jp-Qdl.nib</key>
		<dict>
			<key>hash</key>
			<data>
			j2LbvR3c6i4NM/gu89RA7RghHAw=
			</data>
			<key>hash2</key>
			<data>
			qGopuRrepPuOSnwcdz9JOt1x558/TpBtmk9/oclFYEA=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/ZP2-6Z-1e9-view-cI8-ec-blU.nib</key>
		<dict>
			<key>hash</key>
			<data>
			OK1fxgKeRtvIPoIeZgbH/zPq4wE=
			</data>
			<key>hash2</key>
			<data>
			2FRy/Ew6HCzt4YfU1Ki893X4yVP57siXpC+Z3XjVbGg=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/aboutUs.nib</key>
		<dict>
			<key>hash</key>
			<data>
			L9G8hEpwm11POPfcDYMc55r/p1g=
			</data>
			<key>hash2</key>
			<data>
			PXsd7SiaVId38m8FFfAS6dmSBa1JlMLeXazXEWOwY0Y=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/checkVersion.nib</key>
		<dict>
			<key>hash</key>
			<data>
			XPUvrwyRyZDTwEqfrr4H7MgXEaQ=
			</data>
			<key>hash2</key>
			<data>
			ByP51qbfPxbY2ZJgMpV8F1cVIXcSj8aPt6zJeDDMT7Y=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/gmf-oM-26k-view-hrc-8A-njs.nib</key>
		<dict>
			<key>hash</key>
			<data>
			N6xhBZAGTMnbxknHYEFe8eTpT0g=
			</data>
			<key>hash2</key>
			<data>
			DbIk6JxSSv8SpuxByMLnXtg1gSDePpbGOp8DxaS98fE=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/protocalWindow.nib</key>
		<dict>
			<key>hash</key>
			<data>
			w7ypmvY6V1747m9daj293jpTbLc=
			</data>
			<key>hash2</key>
			<data>
			akwWalEyK9BqTYXDAJlDdXoxi23t515cdZmEfQyHFX8=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/sOX-2d-O7g-view-STb-Pb-ggO.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Q/YYGPzuFRKBIlTvTJ7mKx0m2k0=
			</data>
			<key>hash2</key>
			<data>
			zUkA+e5fDpbVrBVGvppg9H+BF5Q7fsjB6y2QwAXIJcs=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/tAf-7D-gQu-view-ILu-vr-dmw.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ItV3ARB4kb65HdYTNPVEKRc9WIk=
			</data>
			<key>hash2</key>
			<data>
			qTHTYOp1GXmI+jYFc+oVmatbeEuDU4gM5PcWOyOIRio=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/vvG-H9-N8c-view-ioK-D7-hao.nib</key>
		<dict>
			<key>hash</key>
			<data>
			mQpZsgD23HG2D1uT3SIcCmvbY8w=
			</data>
			<key>hash2</key>
			<data>
			SdPCekQQY2Q7FmQbbt1UghwbIg8a9hc8YbymmNLUEeQ=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/xTj-i5-hgw-view-E49-TW-JQi.nib</key>
		<dict>
			<key>hash</key>
			<data>
			pchqvc1phtNoMEq1DiuIPQ2sZBc=
			</data>
			<key>hash2</key>
			<data>
			k5+ykl81FF+Z+p1bMQ8cEVKxF9JD75uZxqKJAVDCnqs=
			</data>
		</dict>
		<key>Resources/XXCollectionViewItem.nib</key>
		<dict>
			<key>hash</key>
			<data>
			pAp3NZ0Vwf8eGfqbxeom01JVH8E=
			</data>
			<key>hash2</key>
			<data>
			wu5hfLuNLipSYRk7agdR+Be/a5D4MP52W5bipGQ4FO4=
			</data>
		</dict>
		<key>Resources/XXFolderDetailInfoView.nib</key>
		<dict>
			<key>hash</key>
			<data>
			xUPdLGrPg2DG1sgxkYADoah9vdY=
			</data>
			<key>hash2</key>
			<data>
			pjQrltT0geO/+sSIHl/7gGDERAQ5tFVdMrWSF8yBa4c=
			</data>
		</dict>
		<key>Resources/XXLocationSettingView.nib</key>
		<dict>
			<key>hash</key>
			<data>
			x5R9hLeepCmTLgQ8LwvJeY1tdGs=
			</data>
			<key>hash2</key>
			<data>
			I58vL4a3xwjarqySx2S8UgTZCPv2OhKhzeXW2tkHOC4=
			</data>
		</dict>
		<key>Resources/XXProgressAlertView.nib</key>
		<dict>
			<key>hash</key>
			<data>
			x7Q8b7DVhyKQWObJPXIMMiDzWyI=
			</data>
			<key>hash2</key>
			<data>
			iEqY9bu2iEwSH8euQNNy0NgwBzB8c31YZyTGjmzpoEk=
			</data>
		</dict>
		<key>Resources/XXReminderView.nib</key>
		<dict>
			<key>hash</key>
			<data>
			gAHyzUaTzIMfe+emCI1Zu275Q1k=
			</data>
			<key>hash2</key>
			<data>
			LmWzNcFls3wjA+McQaAzxs7NoH0iNFifX0etrKz9e8Q=
			</data>
		</dict>
		<key>Resources/XXRsumeDataView.nib</key>
		<dict>
			<key>hash</key>
			<data>
			lxtMLisjKEP1e2t82NeMyyiG7lU=
			</data>
			<key>hash2</key>
			<data>
			SsCOdR9kozUx/+KQpn1bdHkUQUyuZKXNM1xseeHvsBE=
			</data>
		</dict>
		<key>Resources/XXSelectedProtectFolderView.nib</key>
		<dict>
			<key>hash</key>
			<data>
			16DB7gyID/RNMQxmoJdfZqUjiZc=
			</data>
			<key>hash2</key>
			<data>
			b7Mu3bDZJK7llSrNyd4+t3xv2F5hKJM0H9hqK1NXRt0=
			</data>
		</dict>
		<key>Resources/XXSingleInputAlertView.nib</key>
		<dict>
			<key>hash</key>
			<data>
			KdsjPNh91bnlNCFt1RMgrTyrTrE=
			</data>
			<key>hash2</key>
			<data>
			aanQIGidcQ12oc6P2BovUF0GN57MAseI6FppqtWCN9Y=
			</data>
		</dict>
		<key>Resources/XXTimeoutSettingView.nib</key>
		<dict>
			<key>hash</key>
			<data>
			a6wQ5gH1b50fYgR71oiMn5yKhBo=
			</data>
			<key>hash2</key>
			<data>
			yGvAI29IbXDsr2xVQgAN7Gb/T1jjcg/IjvTrVm/XNUs=
			</data>
		</dict>
		<key>Resources/XXTransmissionErrorView.nib</key>
		<dict>
			<key>hash</key>
			<data>
			cpSL6iK+JsFjXPhH715eFUjNx5E=
			</data>
			<key>hash2</key>
			<data>
			TIrKSMA74rsxFOoOnoRti8s+1WoeD+OVC07z4E5R2RQ=
			</data>
		</dict>
		<key>Resources/XXTwoBtnReminderView.nib</key>
		<dict>
			<key>hash</key>
			<data>
			PWii8B3togZDpgGR7j5iYijLacM=
			</data>
			<key>hash2</key>
			<data>
			xB9F1uHg77ehUwb9XRMwFwXtCUpaZbw0FzFHTDSkl4g=
			</data>
		</dict>
		<key>Resources/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9rDz6FTt12crAks7Dt7tr8oPAyM=
			</data>
			<key>hash2</key>
			<data>
			J/0Wcd9q5gGCsNEatMO1XT32MGkVtV5L87ZVULodPNk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			z2+z2qsKIEUwPvyev1MdukYwrdE=
			</data>
			<key>hash2</key>
			<data>
			iht/gkat5oDwGEeYbtxYlmt/jdW85LcJkf9jYjemw5c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
