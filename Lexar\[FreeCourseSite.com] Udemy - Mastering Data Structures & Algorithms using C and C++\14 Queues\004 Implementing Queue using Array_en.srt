1
00:00:00,590 --> 00:00:08,390
For implementing a cure, using a required following things that are fixed to size and the size.

2
00:00:08,430 --> 00:00:10,700
Also, we should know what is the size of a UTI or a.

3
00:00:11,540 --> 00:00:14,640
Then also we need to point out that our front and rear.

4
00:00:15,170 --> 00:00:19,820
So all these things, we can combine them together under a structure because all these are related to

5
00:00:19,820 --> 00:00:23,690
a Q So let me define a structure for a Q.

6
00:00:25,630 --> 00:00:26,340
Structure.

7
00:00:26,350 --> 00:00:27,860
Q So what are the members?

8
00:00:27,880 --> 00:00:36,250
I need I need the size what is the size of a cube that the size of Nuti that I need from point A to

9
00:00:36,250 --> 00:00:42,280
point up one first element and real point to point upon last element.

10
00:00:42,280 --> 00:00:47,020
This is used for insertion and this is used for deletion front for deletion.

11
00:00:47,200 --> 00:00:49,180
Then also I need an arizpe.

12
00:00:49,210 --> 00:00:50,650
So what size it should be.

13
00:00:50,650 --> 00:00:56,200
It can be done only at runtime when really programming is running, we can decide the fight and create

14
00:00:56,200 --> 00:00:58,750
an array dynamically from Hape.

15
00:00:59,110 --> 00:01:02,440
So for creating array dynamically I should take a pointer.

16
00:01:02,440 --> 00:01:07,810
So I will declare an array but I will just take a pointer to four.

17
00:01:07,810 --> 00:01:10,700
And so this I'll be creating it dynamically.

18
00:01:11,410 --> 00:01:16,500
Now let us see how to create the variable of that type structure and how to initialize it.

19
00:01:17,170 --> 00:01:19,630
So I relied on the program code and show you this code.

20
00:01:19,630 --> 00:01:22,420
We will be using it wherever required whenever we are using.

21
00:01:22,420 --> 00:01:28,150
Q Let us assume that this is a main function right inside main function.

22
00:01:28,480 --> 00:01:31,150
I'm creating a variable of type that structure.

23
00:01:31,150 --> 00:01:32,250
Q This.

24
00:01:32,260 --> 00:01:34,570
Q is an object of that type structure.

25
00:01:34,580 --> 00:01:40,480
So once I create this object, the Q are variable of Q I can get following members.

26
00:01:40,600 --> 00:01:51,550
First one is size and second one as the front pointer and the next is a real pointer and Q is appointed

27
00:01:51,550 --> 00:01:54,580
to an area so I'm calling them a pointer.

28
00:01:54,590 --> 00:01:57,640
These are not actually pointers, these are indexed pointers.

29
00:01:57,640 --> 00:02:02,950
Right for index pointers does not address pointers but they're pointing on the element.

30
00:02:02,970 --> 00:02:04,600
So we are calling them as pointers.

31
00:02:04,840 --> 00:02:07,300
Don't confuse them with address pointers.

32
00:02:07,480 --> 00:02:10,840
So a variable is created then we have to initialize all this.

33
00:02:11,140 --> 00:02:13,570
So first of all, I will find out what is the size.

34
00:02:13,570 --> 00:02:15,670
So I will take the size of from the keyboard.

35
00:02:15,670 --> 00:02:19,810
I will give a message, enter the size of a Q and then take the size.

36
00:02:20,200 --> 00:02:22,180
So here the messages and the size.

37
00:02:22,180 --> 00:02:24,940
Then Scarff, I have to pick the size.

38
00:02:24,940 --> 00:02:28,840
So sizes, fortitude or size I have to say cusa size.

39
00:02:28,840 --> 00:02:30,010
So kudo's size.

40
00:02:30,490 --> 00:02:32,260
Q dot size.

41
00:02:32,620 --> 00:02:38,710
So this, whatever the size is given that size would be fairly small, the size is seven, so seven

42
00:02:38,710 --> 00:02:39,520
is the size.

43
00:02:39,790 --> 00:02:45,520
Then based on that size I should create an array of a size seven and fix it at this point.

44
00:02:45,520 --> 00:02:50,380
A Q So I should create this study, I should create that study.

45
00:02:50,620 --> 00:02:53,340
So for creating that area I should say kudos to.

46
00:02:53,350 --> 00:02:59,590
Q So Q is a variable name and it is having no Q as a pointer of type in didgeridoos.

47
00:02:59,590 --> 00:03:06,220
And at this point then to this I should dynamically look at the memory from Hape and assign it so far

48
00:03:06,240 --> 00:03:07,840
dynamically memory allocation.

49
00:03:07,840 --> 00:03:14,530
We use mellark function or else we can use a new summerlong will create an end to that.

50
00:03:14,530 --> 00:03:15,640
Q will be pointing.

51
00:03:16,800 --> 00:03:17,100
Right.

52
00:03:17,520 --> 00:03:23,390
So instead of writing lengthy code, I was referring writing new and while typing the program I value,

53
00:03:23,400 --> 00:03:25,080
I'm using my log function.

54
00:03:25,950 --> 00:03:31,530
Another last lasting I have to initialised front and rear initially, both are minus one, so I have

55
00:03:31,530 --> 00:03:34,910
to set this as a minus one and this also as minus one.

56
00:03:35,190 --> 00:03:43,110
So say killed out front, as well as a good order, both assigned minus and so we can initialize them

57
00:03:43,110 --> 00:03:44,770
together in a single statement.

58
00:03:45,240 --> 00:03:48,210
So these are the steps required for initializing at CU.

59
00:03:48,750 --> 00:03:52,410
Now let us look at functions, operations on a cue.

60
00:03:52,680 --> 00:04:01,500
That is to look at NQ and dequeue Operation I right on the function for NQ and BQ for NQ function and

61
00:04:01,500 --> 00:04:07,860
key function is taking a value that we want to insert in the Q and also in which you want to insert.

62
00:04:07,870 --> 00:04:12,120
So it is taking to my address or reference.

63
00:04:12,390 --> 00:04:17,220
So if suppose this is the object created in the main function or some other function, whatever it is

64
00:04:17,220 --> 00:04:18,480
we want to insert in that.

65
00:04:18,480 --> 00:04:21,630
Q Then this is a pointer to this one.

66
00:04:23,170 --> 00:04:24,940
Pointed to that particular object.

67
00:04:26,160 --> 00:04:31,440
Now, let us write the function for insertion, first of all, we should know, is there any space or

68
00:04:31,440 --> 00:04:31,790
not?

69
00:04:31,980 --> 00:04:36,240
If there is no space we cannot insert, if there is no space is full.

70
00:04:36,570 --> 00:04:38,620
So first, check that that is full.

71
00:04:39,000 --> 00:04:41,160
So I read on the condition for Q4.

72
00:04:41,790 --> 00:04:46,650
If this is the case, then cure's a full I cannot insert in the elements.

73
00:04:46,650 --> 00:04:51,630
So here simply I'll give a message that the queue is full or queue overflow.

74
00:04:53,550 --> 00:04:56,040
If it is not full, then we can insert that element.

75
00:04:56,460 --> 00:04:59,080
So else how to insert the element.

76
00:04:59,340 --> 00:05:05,780
So for inserting an element, we should move Redpoint one step and then insert the element.

77
00:05:06,570 --> 00:05:10,730
Let's step a simple mover and insert element.

78
00:05:10,980 --> 00:05:14,700
So this really should move this rare so of error.

79
00:05:15,090 --> 00:05:23,250
Plus plus so Q3 is moved, so Q3 plus plus so rare becomes now zero six pointing here.

80
00:05:23,520 --> 00:05:30,000
So here in this data structure, if you see the zero statements, it's pointing on a Negrito right then

81
00:05:30,150 --> 00:05:32,200
and cusa zero position.

82
00:05:32,200 --> 00:05:33,510
Excuse their position.

83
00:05:33,510 --> 00:05:41,250
I should insert the value so the so Valetta and whatever the value you are inserting, it will be inserted

84
00:05:41,250 --> 00:05:41,580
there.

85
00:05:42,330 --> 00:05:45,570
But if you are visualizing it like this, this is how it happens inside.

86
00:05:47,470 --> 00:05:51,970
So that's all end of dysfunction and offals part and a lot of NQ.

87
00:05:52,830 --> 00:05:59,220
If you look at the steps, the steps are simple steps, either say print it is full or else two steps,

88
00:05:59,340 --> 00:06:00,700
so the time is constant.

89
00:06:00,990 --> 00:06:04,160
So from the court also, you can see that the time is constant.

90
00:06:05,190 --> 00:06:12,660
Now, next, let us look at dequeue operation that is a delayed operation now delayed operation function

91
00:06:12,660 --> 00:06:19,660
name is a dequeue that is for deletion and this is having a pointer to this variable or this object.

92
00:06:20,130 --> 00:06:23,430
So when you call this function, you have to pass that as parameter.

93
00:06:23,430 --> 00:06:26,930
And this is called address or reference before deleting.

94
00:06:26,940 --> 00:06:30,270
I have taken some variable X and it is initialized to minus one.

95
00:06:30,720 --> 00:06:35,300
Now let us read on the steps before deletion what I have to do before division.

96
00:06:35,340 --> 00:06:38,990
Check that there are some elements or not if there are no elements we cannot delete.

97
00:06:39,540 --> 00:06:41,590
So when there are no elements at the same time.

98
00:06:42,420 --> 00:06:44,080
So what is the condition for me?

99
00:06:44,160 --> 00:06:47,100
If front and rear are equal, we cannot delete them.

100
00:06:47,110 --> 00:06:48,600
So they are equal.

101
00:06:48,780 --> 00:06:49,520
It is empty.

102
00:06:49,830 --> 00:06:55,920
So first I should write on the condition that if front and rear are equal, so who's front and Qs?

103
00:06:55,920 --> 00:07:01,590
So I can access this using an operator to the front and Qs there if they are equal.

104
00:07:01,750 --> 00:07:09,680
So see if it kills the front is equal to cuz of error then I should just display a message that was

105
00:07:09,690 --> 00:07:10,140
full.

106
00:07:10,470 --> 00:07:13,530
Then here I just display a message that Q is empty.

107
00:07:13,530 --> 00:07:14,190
So print.

108
00:07:14,190 --> 00:07:15,060
Excuse me.

109
00:07:15,670 --> 00:07:16,860
So Q is empty.

110
00:07:16,860 --> 00:07:22,530
If there are equal is empty, I cannot delete any element else how to delete an element.

111
00:07:22,530 --> 00:07:29,550
So for deleting an element just moving from point to an next location and take out the element that's

112
00:07:29,550 --> 00:07:29,810
all.

113
00:07:30,510 --> 00:07:32,460
So how to move from the front.

114
00:07:32,460 --> 00:07:40,350
I have to move so that the skills front plus plus first of all sulcus front is moved cuz front is moved

115
00:07:40,710 --> 00:07:42,780
them from that place.

116
00:07:42,780 --> 00:07:48,120
I should take all the elements so I can take out that element in variable X, so inside X I am picking

117
00:07:48,120 --> 00:07:54,420
out the value from cuz the Q that excuse a Q that is from that area, from which place I'm picking out

118
00:07:54,420 --> 00:08:01,740
the value, secure the front, I'm picking out the value that is taken in X, I finally return X Sauceda

119
00:08:01,740 --> 00:08:03,930
else I'm writing return X.

120
00:08:03,930 --> 00:08:06,540
So the value that is deleted is written.

121
00:08:07,290 --> 00:08:14,550
What happens if the Q was empty so that X will have minus one and nothing is assigned to X so it will

122
00:08:14,550 --> 00:08:15,540
return minus one.

123
00:08:16,110 --> 00:08:19,830
So if this function is running minus one means Q is empty.

124
00:08:20,640 --> 00:08:24,990
There are no elements in the Q, so that's all decided to function.

125
00:08:25,320 --> 00:08:30,240
And if you look at the function, the steps are very simple, condition is checked and the printing

126
00:08:30,240 --> 00:08:30,720
is done.

127
00:08:31,020 --> 00:08:32,880
Otherwise just two simple steps.

128
00:08:33,000 --> 00:08:36,240
So the time taken by the Q is constant.

129
00:08:36,690 --> 00:08:39,750
So from the code also you can see that it is constant.

