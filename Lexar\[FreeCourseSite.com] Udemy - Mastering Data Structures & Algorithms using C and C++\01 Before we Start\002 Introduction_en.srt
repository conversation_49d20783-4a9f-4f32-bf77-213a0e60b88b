1
00:00:00,300 --> 00:00:06,000
And this video, let us have the basic idea about the subject and the course that is data structures,

2
00:00:06,600 --> 00:00:08,560
so I discuss in the form of question answer.

3
00:00:08,610 --> 00:00:12,050
So let us start with the first question.

4
00:00:12,450 --> 00:00:14,550
What are the contents of the course?

5
00:00:14,850 --> 00:00:21,660
The course contains these topics like <PERSON><PERSON>'s Makkah sociolinguistics docu series, <PERSON><PERSON>'s Harshing

6
00:00:21,960 --> 00:00:23,850
Recursion and Sorting.

7
00:00:24,690 --> 00:00:25,810
These are the topics.

8
00:00:26,430 --> 00:00:29,310
Next question is what are data structures?

9
00:00:30,120 --> 00:00:35,900
CI A. program is a set of instructions which performs operations on data.

10
00:00:36,390 --> 00:00:39,840
So without data, instructions cannot be performed.

11
00:00:39,960 --> 00:00:43,240
So data is an important part of a program.

12
00:00:43,770 --> 00:00:50,790
So when a program is dealing with the data, how it will organize the data in the mean memory, that's

13
00:00:50,790 --> 00:00:57,760
what its data structures, the way you organize the data in the mean memory during execution time of

14
00:00:57,760 --> 00:00:59,870
a program that is data structure.

15
00:01:00,150 --> 00:01:04,800
See if you see the components of a computer system, if you will, which executes the program.

16
00:01:04,959 --> 00:01:07,890
And this is the main memory and inside the main memory.

17
00:01:07,920 --> 00:01:08,850
This is our program.

18
00:01:08,850 --> 00:01:09,240
Good.

19
00:01:09,510 --> 00:01:11,110
And this is data.

20
00:01:11,310 --> 00:01:11,790
Right.

21
00:01:11,820 --> 00:01:14,370
So this is mumbly this work for showing memory.

22
00:01:14,670 --> 00:01:16,880
So program code and data distributing.

23
00:01:16,890 --> 00:01:19,650
So this program called will perform operations on the data.

24
00:01:19,920 --> 00:01:26,040
So how you keep the data here so that it can be best utilized by the program, efficiently utilized

25
00:01:26,040 --> 00:01:26,710
by the program.

26
00:01:27,690 --> 00:01:31,020
So that arrangement of data is structured.

27
00:01:31,030 --> 00:01:35,640
So depending on the requirements of the program, the type of procedures it is performing, we have

28
00:01:35,640 --> 00:01:36,930
various data structures.

29
00:01:37,410 --> 00:01:42,570
So depending on your requirements, you can choose a suitable data structure.

30
00:01:42,990 --> 00:01:46,430
Now, next question is, what are the list of data structures?

31
00:01:46,740 --> 00:01:48,910
So here I have the list of data structures.

32
00:01:48,930 --> 00:01:53,820
These are the data structures, only three mattresses and linked.

33
00:01:55,170 --> 00:02:01,550
These are usually called as physical data structure that define how the data is arranged in the memory.

34
00:02:02,040 --> 00:02:04,970
Then these are logical data structures.

35
00:02:05,220 --> 00:02:12,420
So this defines how the data can be utilized, how the arrangement is and how it should be utilized.

36
00:02:12,810 --> 00:02:16,000
So this refines arrangement and this defines utilization.

37
00:02:16,020 --> 00:02:20,420
So there is one variable physical data structure versus logical data structure.

38
00:02:20,430 --> 00:02:21,360
So you can watch that.

39
00:02:21,360 --> 00:02:22,760
I have discussed that in detail.

40
00:02:23,610 --> 00:02:29,160
If you take physically Dustan, just like UTI and the MATTATHIAS, these are provided by almost every

41
00:02:29,160 --> 00:02:34,280
programming language like C, C++, Java, in every language you have a built in audience.

42
00:02:34,290 --> 00:02:39,570
There's a part of a compiler only that you can declare an array like this and you can start using it

43
00:02:39,840 --> 00:02:40,650
in C language.

44
00:02:40,650 --> 00:02:47,010
You can declare an array of single dimensionality like this or a two dimensional array of some size

45
00:02:47,010 --> 00:02:48,060
for implementing.

46
00:02:49,110 --> 00:02:55,140
So this is built in as a part of compiler, not a linked list is not there in C language, so we have

47
00:02:55,140 --> 00:02:55,830
to create it.

48
00:02:56,070 --> 00:03:00,550
Then we can implement this, we can use it like a stack of Q and all.

49
00:03:00,810 --> 00:03:06,030
So there are sections for each topic and there are lots of variables under each section.

50
00:03:06,420 --> 00:03:10,320
So you'll be learning in detail about these divestitures now.

51
00:03:10,350 --> 00:03:11,070
Next question.

52
00:03:11,310 --> 00:03:17,850
Why eventually study data structures, key data structures, the core subject for programmers or I.T.

53
00:03:17,850 --> 00:03:18,450
professionals?

54
00:03:18,840 --> 00:03:24,960
So for computer science graduates, undergraduates who are pursuing the course engineering goals, they

55
00:03:24,960 --> 00:03:26,220
have this as a subject.

56
00:03:26,220 --> 00:03:29,100
So they have to study as a part of academics.

57
00:03:29,610 --> 00:03:35,220
And the second thing in the industry, if you're working as a programmer, then you have to use data

58
00:03:35,220 --> 00:03:37,470
structures in your application development.

59
00:03:37,480 --> 00:03:41,030
So without data structures, you cannot develop applications at all.

60
00:03:41,970 --> 00:03:44,460
So you should have good understanding of data structures.

61
00:03:44,520 --> 00:03:46,410
Now, next question at what level?

62
00:03:46,410 --> 00:03:48,330
One should study data structures.

63
00:03:48,670 --> 00:03:50,640
So I will define it as level C.

64
00:03:50,640 --> 00:03:58,350
The first level is, you know, what these data structures are and how they work, that this is a basic

65
00:03:58,350 --> 00:03:58,650
thing.

66
00:03:59,160 --> 00:04:03,600
So most of the programmers will be doing this one, even though many programmers out there who are not

67
00:04:03,600 --> 00:04:07,020
computer science graduates, but they still do programming.

68
00:04:07,020 --> 00:04:09,080
So they have no idea how they work.

69
00:04:09,090 --> 00:04:09,660
That's it.

70
00:04:10,470 --> 00:04:11,670
And where to use them.

71
00:04:12,420 --> 00:04:18,089
Now, next level, you know how they work in detail and you are able to do the analysis.

72
00:04:18,089 --> 00:04:24,720
So you understand in depth what are the operations that are performed by them and you know how those

73
00:04:24,720 --> 00:04:26,370
operations are performed also.

74
00:04:26,640 --> 00:04:32,970
So you know the internal detail and you are able to analyze them based on the time and the space complexities.

75
00:04:33,240 --> 00:04:39,810
So you understand them thoroughly for doing analysis is also involved in this one.

76
00:04:40,050 --> 00:04:45,540
So you know the internal details, how the operations works and also, you know, also the next level,

77
00:04:46,230 --> 00:04:50,070
then one more next level, let's say level three.

78
00:04:50,310 --> 00:04:57,630
So unless you know everything in detail and also you know how to code them, you can develop your own

79
00:04:57,630 --> 00:04:58,470
data structure.

80
00:04:58,740 --> 00:04:59,850
So that means you can.

81
00:04:59,960 --> 00:05:01,700
Programmed them by yourself.

82
00:05:02,000 --> 00:05:04,670
So for this, you need programming practice.

83
00:05:05,780 --> 00:05:14,030
Now, next question to what level the court covers the data structure, so this course covers data structure

84
00:05:14,180 --> 00:05:16,970
in much detail, I can say, at level three.

85
00:05:17,120 --> 00:05:20,870
So, well, you know how they work and you know the internal details.

86
00:05:20,870 --> 00:05:24,290
You will be able to do analysis and in-depth analysis.

87
00:05:24,500 --> 00:05:27,650
And also the scores make you program.

88
00:05:27,770 --> 00:05:30,940
Right, the data structure from the scratch by yourself.

89
00:05:32,000 --> 00:05:37,340
Next question, which programming language is suitable for data structures?

90
00:05:37,580 --> 00:05:41,430
You can select any language, whichever language you know, you can select that language.

91
00:05:41,750 --> 00:05:45,830
The next question, do I have to develop these data structures?

92
00:05:45,830 --> 00:05:46,240
Right.

93
00:05:46,620 --> 00:05:52,550
Create them by myself in every programming language, know all these languages.

94
00:05:52,550 --> 00:05:58,250
They have their built in their dust, which is available for these languages, provide something or

95
00:05:58,250 --> 00:06:04,310
the other or some languages, provide everything building available so they have the things available

96
00:06:04,310 --> 00:06:04,730
already.

97
00:06:05,060 --> 00:06:10,250
So you just you have to use them and you don't have to develop your own data structure.

98
00:06:10,250 --> 00:06:12,500
You have to use them for using them.

99
00:06:12,500 --> 00:06:14,810
You should know how they work and where to use them.

100
00:06:15,170 --> 00:06:17,140
So the level of knowledge is sufficient.

101
00:06:17,750 --> 00:06:23,780
All these languages provide stability data structures like in C++ there are Steeles and in Java there

102
00:06:23,780 --> 00:06:27,020
are collection classes right in C Sharp.

103
00:06:27,020 --> 00:06:32,960
Also collection classes are there in Python, also containers are there and in JavaScript also collections

104
00:06:32,960 --> 00:06:36,380
are there or DOM objects are also there in JavaScript.

105
00:06:36,680 --> 00:06:39,320
So languages have a built in data structure.

106
00:06:39,320 --> 00:06:45,260
So mostly people study about those data structures and they want to understand how to use them, just

107
00:06:45,260 --> 00:06:46,880
how they work and how to use them.

108
00:06:47,680 --> 00:06:49,460
That is sufficient for most of the people.

109
00:06:49,700 --> 00:06:53,330
But this course goes way beyond that one.

110
00:06:53,420 --> 00:06:56,290
If you want to learn about these, these are language specific.

111
00:06:56,300 --> 00:07:01,760
So if you are studying about the language, you can learn about them, then what about the C language,

112
00:07:01,940 --> 00:07:06,550
C languages, not having building data structures, if you will.

113
00:07:06,560 --> 00:07:10,740
If you want any one of these data structures, then you should develop your own code.

114
00:07:11,060 --> 00:07:11,930
Next question.

115
00:07:12,020 --> 00:07:14,240
Which language is used in discourse?

116
00:07:14,360 --> 00:07:17,330
C C language is not having brilliant data structures.

117
00:07:17,360 --> 00:07:25,100
I have used that language for creating or implementing these data structures and also I have given sufficient

118
00:07:25,100 --> 00:07:28,070
idea for using C++.

119
00:07:29,150 --> 00:07:32,500
Even if you want to implement Java and C Sharp, you can do that.

120
00:07:32,510 --> 00:07:35,300
That's your choice and that will be your responsibility.

121
00:07:35,630 --> 00:07:39,320
How to implement this core code in C Sharp or Java.

122
00:07:39,530 --> 00:07:42,260
I'll be showing in C and that is also same.

123
00:07:42,260 --> 00:07:49,820
And C++ also whatever I'm showing and C that is nothing but C++ also because see the subset of C++,

124
00:07:50,300 --> 00:07:50,630
right.

125
00:07:50,870 --> 00:07:52,130
So it is coming.

126
00:07:52,130 --> 00:07:53,810
I said C and C++.

127
00:07:53,810 --> 00:07:54,000
Right.

128
00:07:54,290 --> 00:07:57,260
But a C++ having some object oriented features.

129
00:07:57,500 --> 00:07:59,420
So how do you use those features.

130
00:07:59,420 --> 00:08:00,430
I have discussed that.

131
00:08:00,860 --> 00:08:04,220
So whatever is done in C language, you can easily convert into C++.

132
00:08:04,220 --> 00:08:08,850
If you know C++, if you don't know C++, then you will not be able to do it.

133
00:08:09,020 --> 00:08:12,320
The next question why I have selected C language.

134
00:08:12,320 --> 00:08:14,060
Why not any other language?

135
00:08:14,510 --> 00:08:20,060
C When you are understanding the operations on these data structures in detail and you are observing

136
00:08:20,060 --> 00:08:26,580
them very closely, then CS are best suitable language for doing programming on that.

137
00:08:26,990 --> 00:08:33,549
So for every operation we write functions that are procedures, we write procedures, standard procedures

138
00:08:33,559 --> 00:08:36,230
are the functions are available in every language.

139
00:08:36,260 --> 00:08:39,200
They are available, every language, C, C++, Java, everywhere.

140
00:08:39,500 --> 00:08:41,960
Only the difference is in C++ and Java.

141
00:08:42,200 --> 00:08:44,930
We put them inside class in C they are open.

142
00:08:45,080 --> 00:08:45,590
That's it.

143
00:08:46,280 --> 00:08:51,050
They are global functions, but they remember functions in Java and C++.

144
00:08:51,380 --> 00:08:52,700
So have you see language?

145
00:08:52,700 --> 00:08:59,240
Because I am showing you how to implement the operations and I see as not having anything building and

146
00:08:59,240 --> 00:09:02,720
it's more suitable and more perfect language for learning data structures.

147
00:09:02,780 --> 00:09:06,920
The another reason is other languages are already having their own building data structures.

148
00:09:06,920 --> 00:09:10,940
Then why do you make your own sources not having it so you make your own.

149
00:09:11,180 --> 00:09:13,250
So that will help you understand all these.

150
00:09:13,250 --> 00:09:16,910
Also, if you know a little bit about the language, you can understand them easily.

151
00:09:17,150 --> 00:09:20,150
The next question, how the contents are organized.

152
00:09:20,400 --> 00:09:27,590
Since the beginning section, I have discussed the required features of C and C++ that are used in the

153
00:09:27,590 --> 00:09:28,100
course.

154
00:09:28,100 --> 00:09:34,070
So you should be doing C C++ and if you up these concepts, then you can start with the course.

155
00:09:34,070 --> 00:09:36,890
I have not directly started with the course for BRUSHER.

156
00:09:36,920 --> 00:09:43,370
I have some section, so I have the concept of C++ like structure functions, classes, templates,

157
00:09:43,760 --> 00:09:49,600
parameter passing that are useful for programming and discourse implementing these data structures.

158
00:09:49,760 --> 00:09:51,860
The next question, sorting example.

159
00:09:51,860 --> 00:09:52,700
What in discourse.

160
00:09:52,700 --> 00:09:57,050
Yes, almost all the sorting techniques are covered here and there.

161
00:09:57,050 --> 00:10:02,890
Implementation also shown in this one, like bubbles for selection sort incisions are various or things

162
00:10:02,910 --> 00:10:03,710
are shown here.

163
00:10:03,950 --> 00:10:05,300
So sorting is covered in.

164
00:10:05,670 --> 00:10:12,660
And the analysis also done in that than the last and the important question as a precaution, I have

165
00:10:12,660 --> 00:10:18,000
a first T.S.A. recursion right before starting these data structures.

166
00:10:18,000 --> 00:10:20,570
I have used recursion, first as recursion.

167
00:10:20,820 --> 00:10:21,300
Why?

168
00:10:21,330 --> 00:10:22,350
What is the importance?

169
00:10:22,750 --> 00:10:26,520
So see, recursion is a difficult topic for the students.

170
00:10:26,520 --> 00:10:28,790
Mostly students struggle in understanding it.

171
00:10:29,070 --> 00:10:31,560
So I have got to make it simple.

172
00:10:31,800 --> 00:10:37,260
And you have the good understanding of recursion and most of the operations that are implemented on

173
00:10:37,260 --> 00:10:43,830
these data structures are basically defining recursion and they are also implemented without recursion

174
00:10:43,830 --> 00:10:44,490
using loops.

175
00:10:44,490 --> 00:10:52,500
Also, the next recursion is usually felt as inefficient because it uses static internally.

176
00:10:52,770 --> 00:10:56,130
It is not much useful for solving large problems.

177
00:10:56,130 --> 00:10:57,870
Then still we study recursion.

178
00:10:57,870 --> 00:10:58,760
What is the reason?

179
00:10:59,070 --> 00:11:03,170
So there is something called problem solving and programming.

180
00:11:03,510 --> 00:11:05,180
So these two are two different things.

181
00:11:05,280 --> 00:11:10,440
Problem solving is a different programming is different mostly to try to mix them.

182
00:11:10,440 --> 00:11:15,240
The thing that if they understand programming, they learn problem solving, no problem solving is different

183
00:11:15,630 --> 00:11:17,170
and programming is different.

184
00:11:17,550 --> 00:11:19,770
So programming can be learned in just a few days.

185
00:11:19,920 --> 00:11:22,410
But problem solving is a lifetime.

186
00:11:22,410 --> 00:11:27,690
Work programming means the syntax, languages, anyone of the language.

187
00:11:28,020 --> 00:11:31,710
If you know the features that are sufficient, then problem solving.

188
00:11:31,860 --> 00:11:36,390
You should be good at mathematics than if you're solving a problem.

189
00:11:36,390 --> 00:11:39,660
Solution you will be giving in mathematics and mathematics.

190
00:11:39,660 --> 00:11:42,010
Have recursion, mathematics.

191
00:11:42,010 --> 00:11:45,060
It doesn't have Lupe's mathematics have recursion.

192
00:11:45,300 --> 00:11:48,780
So your solution will be mostly in the form of recursion.

193
00:11:49,750 --> 00:11:57,040
So every programming language supports recursion, no doubt it is less used, but it supports in case

194
00:11:57,040 --> 00:11:59,650
if you're writing a recursive function, you can write it.

195
00:11:59,980 --> 00:12:05,810
Otherwise as a programmer, you can learn how to convert recursion into a loop.

196
00:12:06,190 --> 00:12:10,520
So mostly problems are solved in recursion, then they are converted into a loop.

197
00:12:11,140 --> 00:12:15,820
So understanding the recursion is a very important thing for understanding problem solving.

198
00:12:16,150 --> 00:12:16,900
So that's all.

199
00:12:16,900 --> 00:12:17,730
These are the questions.

200
00:12:17,740 --> 00:12:23,980
And one more students believe that the topic is data structures and algorithm algorithms.

201
00:12:23,980 --> 00:12:30,130
Are those algorithms that are used on this radar structure algorithm is a very vast topic, even Google

202
00:12:30,130 --> 00:12:31,690
and Facebook users algorithms.

203
00:12:31,700 --> 00:12:37,660
So look them doesn't mean that I'll be teaching Google's algorithm or face recognition algorithm or

204
00:12:38,470 --> 00:12:40,660
vehicle tracking algorithm for everything.

205
00:12:40,690 --> 00:12:44,380
Algorithms are the algorithm is a separate subject for that.

206
00:12:44,410 --> 00:12:45,520
There is a YouTube channel.

207
00:12:45,520 --> 00:12:49,930
You can search for algorithm and YouTube and you can find my lot of free videos.

208
00:12:50,170 --> 00:12:52,210
So I cannot include those free videos here.

209
00:12:52,540 --> 00:12:59,680
So if you are studying want to study algorithm, you can watch YouTube, channel it as a subject mostly

210
00:12:59,680 --> 00:13:06,700
for academics discourses about data structures and algorithms that are applied on data structures that

211
00:13:06,790 --> 00:13:07,600
all in this video.

212
00:13:07,750 --> 00:13:14,290
And the next video is up on the essential features of C and C++ that you need for completing.

213
00:13:14,290 --> 00:13:15,940
The scores are following this course.

214
00:13:16,270 --> 00:13:19,450
If you're familiar with that, you can skip and you start again.

215
00:13:19,450 --> 00:13:22,570
Start with the next section, NetSol.

