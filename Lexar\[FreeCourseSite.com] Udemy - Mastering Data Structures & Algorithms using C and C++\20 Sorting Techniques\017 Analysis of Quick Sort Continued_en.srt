1
00:00:01,000 --> 00:00:07,490
For the best case, assume that I have a list of 15 elements, I'm starting index from one on works

2
00:00:07,550 --> 00:00:09,060
that is easy for understanding.

3
00:00:09,520 --> 00:00:11,380
We know what is a stock from zero.

4
00:00:11,620 --> 00:00:13,710
OK, don't get confused with that.

5
00:00:14,200 --> 00:00:17,050
I'm taking from one on one, so it's easy for understanding.

6
00:00:17,260 --> 00:00:20,290
So it's starting from one and ending at 15.

7
00:00:20,620 --> 00:00:21,700
A lost is infinity.

8
00:00:21,710 --> 00:00:28,810
That is not a part of let's just ignore it so that it's counter but just ignoring the whole quicksort.

9
00:00:29,730 --> 00:00:34,170
Works when you give a list of elements from.

10
00:00:36,180 --> 00:00:38,850
One to 15, then.

11
00:00:40,110 --> 00:00:45,960
It will perform partition and it will find the partition position and before it is brought there and

12
00:00:45,960 --> 00:00:46,920
the list is split.

13
00:00:47,540 --> 00:00:55,110
So assume that list is split in the middle, in the middle, assuming this this is best case, it is

14
00:00:55,110 --> 00:00:56,130
split right in the middle.

15
00:00:56,490 --> 00:00:59,900
If it is in the middle, then that will be position eight.

16
00:01:00,060 --> 00:01:02,050
So seven elements on this side.

17
00:01:02,460 --> 00:01:07,710
That is one, two, seven, then seven elements on the side that are nine to 15.

18
00:01:08,250 --> 00:01:11,670
Felicitas divided into two equal halves.

19
00:01:12,270 --> 00:01:14,280
Then how many elements on the side?

20
00:01:15,060 --> 00:01:19,880
OK, then if quicksort is performed here, I assume that again in this partitioning in the middle,

21
00:01:20,520 --> 00:01:21,630
this is assumption.

22
00:01:21,630 --> 00:01:23,190
It may or may not happen.

23
00:01:23,200 --> 00:01:29,250
This assumption, if it is in the middle, then elements from one to three on the site then.

24
00:01:30,860 --> 00:01:34,090
Five to seven on the side, three elements here, three elements there.

25
00:01:35,060 --> 00:01:37,820
And on that side, also assume that same thing is happening.

26
00:01:39,370 --> 00:01:42,520
So this is nine to 11 and this is from.

27
00:01:43,520 --> 00:01:48,170
13 to 15, so this is actual, so partitioning is done at 12.

28
00:01:50,420 --> 00:01:55,640
Then here, partitioning is done at to so element, that is one to one on the side and three to three

29
00:01:55,640 --> 00:01:56,310
on the side.

30
00:01:56,600 --> 00:01:58,190
Now these are one one element only.

31
00:01:58,430 --> 00:02:00,020
And here partitioning is at six.

32
00:02:00,020 --> 00:02:05,590
So five to five on the side, then seven to seven on the side, then partitioning is at ten.

33
00:02:05,600 --> 00:02:08,789
So nine to nine on the side and 11 to 11.

34
00:02:08,810 --> 00:02:10,160
These are all single elements.

35
00:02:10,639 --> 00:02:12,270
Then partitioning is at 14.

36
00:02:12,620 --> 00:02:17,210
So today, butadiene on the side and 15 to 15 on the side.

37
00:02:17,780 --> 00:02:23,990
So as quicksort is recursive, the order in which these calls will be made as this is the first call

38
00:02:24,020 --> 00:02:30,650
and the second call, third call and fourth call, which will not do anything, fifth call, then six

39
00:02:30,680 --> 00:02:32,550
to seven, eight.

40
00:02:32,840 --> 00:02:40,970
Then this will be a nine content 11, 12, 13 call, 14 call and 15 call.

41
00:02:42,990 --> 00:02:48,150
So as far as recursive, I have written the order in which these calls will be made like this, the

42
00:02:48,150 --> 00:02:50,650
first call, second call, third call forth and so on.

43
00:02:51,330 --> 00:02:54,460
So I'll let you know about the recursion so how the calls are made.

44
00:02:55,200 --> 00:03:00,870
So this is how we are representing our tree based on the working of the quicksort.

45
00:03:01,350 --> 00:03:05,120
And we are assuming that partitioning is always done in the middle of a list.

46
00:03:05,730 --> 00:03:09,010
So half of the element left side, half of the elements on the right side.

47
00:03:09,510 --> 00:03:12,630
Then how much time it will take now level by level.

48
00:03:12,630 --> 00:03:18,210
If I compare here, how many comparisons are done and the elements are different, combinations are

49
00:03:18,210 --> 00:03:21,780
done and then the next level, this part is different and this part is different.

50
00:03:21,780 --> 00:03:22,660
Goals are different.

51
00:03:22,830 --> 00:03:24,130
A second call that is nine called.

52
00:03:24,150 --> 00:03:29,640
But at this level here, half of the elements here, half of the elements total.

53
00:03:29,640 --> 00:03:31,340
How many elements are getting elements.

54
00:03:31,740 --> 00:03:36,650
But actually, if you see combined together, one element is less because eight is already sorted.

55
00:03:36,810 --> 00:03:42,000
So anyway, I will not write at minus one approximately and only then in this level.

56
00:03:42,000 --> 00:03:47,970
Also, if I take up all those elements, three, three, three, three, so that all elements are there.

57
00:03:48,750 --> 00:03:51,460
So approximately 15 elements on this are ridiculous.

58
00:03:51,480 --> 00:03:54,780
And so that's all in this last level.

59
00:03:54,810 --> 00:03:57,000
No work is done so total.

60
00:03:57,000 --> 00:04:03,150
How many competitions are done in this partitioning elements are compared to using and so total.

61
00:04:03,150 --> 00:04:04,460
How many competitions are done?

62
00:04:04,470 --> 00:04:06,650
First time 15 next time here.

63
00:04:06,660 --> 00:04:08,040
One lesson here, one less.

64
00:04:08,040 --> 00:04:09,300
Then also they are losing.

65
00:04:09,600 --> 00:04:14,430
But I am taking them approximately and only because reduction is not a huge reduction.

66
00:04:14,920 --> 00:04:18,300
Every level and competitions are done so total.

67
00:04:18,300 --> 00:04:19,500
How many levels are there?

68
00:04:19,829 --> 00:04:21,350
C 15 elements.

69
00:04:21,360 --> 00:04:30,570
So you assume approximately 16 elements divided by two eight eight eight again divided by two.

70
00:04:30,750 --> 00:04:31,680
So four four.

71
00:04:31,680 --> 00:04:34,040
Actually we are getting three three because this is 15.

72
00:04:34,060 --> 00:04:34,340
Right.

73
00:04:34,740 --> 00:04:38,460
Then again, divided by two to two elements.

74
00:04:38,490 --> 00:04:43,370
So finally, actually, we are getting one one element, two divided by two, finally one one element.

75
00:04:43,770 --> 00:04:51,330
So this successive division by two is nothing but log base two for a number of elements.

76
00:04:51,670 --> 00:04:53,790
So approximately log base two.

77
00:04:55,120 --> 00:05:04,600
Approximately log base to log off a 16 don't Dec. 15, 16, as for but how many levels we got only three.

78
00:05:04,720 --> 00:05:07,950
So log and the minus one you can see.

79
00:05:09,470 --> 00:05:12,030
So, Logan, you saw Logan minus one.

80
00:05:12,050 --> 00:05:17,300
You can see so basically you see the behavior elements are divided into two, then again divided into

81
00:05:17,300 --> 00:05:17,540
two.

82
00:05:17,570 --> 00:05:21,010
So this is log so and competitions.

83
00:05:21,410 --> 00:05:25,490
How many times and competitions for how many times.

84
00:05:25,880 --> 00:05:26,900
Log in times.

85
00:05:28,130 --> 00:05:36,230
So the time complexity of quicksort is analog, and so this is the best case analysis of quicksort.

86
00:05:37,330 --> 00:05:42,100
Now, finally, we will conclude the topic for that, I will remove this and I'll vote on something

87
00:05:42,100 --> 00:05:42,310
here.

88
00:05:43,510 --> 00:05:48,910
In conclusion, we will see what is the best case of quicksort, best cases.

89
00:05:49,210 --> 00:05:56,320
If partition is happening only in the middle, if partitioning is done in the middle, then what is

90
00:05:56,320 --> 00:05:57,670
the best case of time?

91
00:05:57,940 --> 00:06:01,480
Baskis Steinman's outdraw and the LOG-IN.

92
00:06:03,780 --> 00:06:09,300
So this case is partitioning is always happening in the middle and the time taken in the best cases,

93
00:06:09,420 --> 00:06:12,060
and then what is the worst case?

94
00:06:12,360 --> 00:06:20,810
Partitioning is any and often that is either left hand side or the right hand side, right and left

95
00:06:21,060 --> 00:06:23,220
or right of a list.

96
00:06:23,580 --> 00:06:26,730
Then what is the time in that case then?

97
00:06:26,730 --> 00:06:27,780
What is the time taken?

98
00:06:27,780 --> 00:06:30,360
In that case, it's outdraw and square.

99
00:06:31,750 --> 00:06:40,780
So those cases and square these cases and log and then what is the average case time, average case

100
00:06:40,780 --> 00:06:43,930
time of Sardis and log in?

101
00:06:45,660 --> 00:06:52,920
So best case is an log-in average citizen, Logan, but those cases and Square and worst case will happen

102
00:06:52,920 --> 00:06:57,190
if the list is already sorted out, either in ascending order or descending order.

103
00:06:57,810 --> 00:06:59,160
So that's all about the time.

104
00:06:59,610 --> 00:07:04,590
See what is worst case if the list is already sorted?

105
00:07:05,280 --> 00:07:07,680
Just the definition of worst case.

106
00:07:08,490 --> 00:07:09,720
What is best case?

107
00:07:11,360 --> 00:07:16,970
We don't know, but partitioning is in the middle, how all the elements are sorted, not sorted, what

108
00:07:17,420 --> 00:07:23,000
we can't tell, what is the arrangement of elements, but for some arrangement, if partitioning is

109
00:07:23,000 --> 00:07:23,630
in the middle.

110
00:07:23,990 --> 00:07:27,510
So their best guess is defined in terms of partitioning position.

111
00:07:27,950 --> 00:07:32,840
Worst case is defined in terms of arrangement of element and also partitioning.

112
00:07:32,870 --> 00:07:33,960
What arrangement of element?

113
00:07:33,980 --> 00:07:35,800
Also, we know here we do not involve.

114
00:07:37,820 --> 00:07:38,840
The next.

115
00:07:39,910 --> 00:07:40,360
S..

116
00:07:41,870 --> 00:07:48,260
In partitioning, we are selecting first elements that support first element has broad support from

117
00:07:48,260 --> 00:07:51,410
the list if we select middle elements, Spearwood.

118
00:07:52,850 --> 00:07:58,160
We can change, yes, you can select any element of support, so you can bring this element as the first

119
00:07:58,160 --> 00:08:01,810
element in the change with the first element and select this element of sport.

120
00:08:01,820 --> 00:08:03,080
So the element will be this fun.

121
00:08:04,220 --> 00:08:05,440
This element is brought here.

122
00:08:05,660 --> 00:08:13,070
We can select this one so we can also select the sport by bringing it at the first position of a list.

123
00:08:14,570 --> 00:08:15,540
Then what happens?

124
00:08:15,560 --> 00:08:20,450
What is the benefit if you are doing this, if the list is already sorted, then.

125
00:08:21,670 --> 00:08:27,020
Then the partitioning will be done, it will be done in the middle only because we brought the element

126
00:08:27,100 --> 00:08:29,770
as by what way it will go and sit in the sorted list.

127
00:08:29,770 --> 00:08:30,990
It will go considerably.

128
00:08:31,690 --> 00:08:33,850
So partitioning will happen always in the middle.

129
00:08:35,250 --> 00:08:42,059
So instead of selecting foster element, what if we select element as pivot, if we modify this, then

130
00:08:42,600 --> 00:08:43,919
best case will be.

131
00:08:45,600 --> 00:08:53,700
Sorted list, then the partitioning is always in the middle, then the time will be and login, so sorted

132
00:08:53,700 --> 00:08:57,630
lists become so biscuits, then what is worst case?

133
00:08:58,040 --> 00:09:00,840
Worst case will be partitioning at any end.

134
00:09:01,560 --> 00:09:02,870
So worst case remain same.

135
00:09:03,240 --> 00:09:04,360
But can you define it?

136
00:09:04,410 --> 00:09:05,730
No, it is not sorted.

137
00:09:05,730 --> 00:09:12,600
List some of the list, some list for which partitioning is always in the end of a list either left

138
00:09:12,810 --> 00:09:13,920
or right then of a list.

139
00:09:14,160 --> 00:09:20,880
That time Times and Square first element has pivot, then partitioning in the middle happens.

140
00:09:20,880 --> 00:09:24,150
Then in Log-in how the list looks like we don't know.

141
00:09:25,390 --> 00:09:31,540
First element people, people passing at any end, then worst case and square how the list looked like

142
00:09:31,540 --> 00:09:39,040
already started, then change, which element is selected as people are selected and we would then best

143
00:09:39,040 --> 00:09:42,220
case and Logan really find the list sodic.

144
00:09:44,100 --> 00:09:49,540
Muskets and square, where the partitioning is done at any one end, can really find the list.

145
00:09:49,670 --> 00:09:50,410
No, we don't know.

146
00:09:50,580 --> 00:09:56,380
So that's all by selecting middle elements before we convert that that worst case into the best case.

147
00:09:56,420 --> 00:10:00,180
Now, it has become a best case, but still the worst case.

148
00:10:00,180 --> 00:10:03,950
Time of quicksort is now square for some order of elements.

149
00:10:04,500 --> 00:10:09,600
So we try to improve quicksort by changing the P word.

150
00:10:09,600 --> 00:10:11,640
But still there are Times and Square.

151
00:10:12,880 --> 00:10:16,420
And lastly, there is one more idea about quicksort.

152
00:10:17,290 --> 00:10:23,380
That is of selecting the element of sport, we can randomly select some element of sport, so that is

153
00:10:23,380 --> 00:10:25,760
called, as randomise, quicksort.

154
00:10:25,990 --> 00:10:27,700
So here are some changes we have to do.

155
00:10:27,740 --> 00:10:27,990
Right.

156
00:10:28,040 --> 00:10:33,880
We are selecting first element, otherwise we can select model element or we can select the random element.

157
00:10:33,890 --> 00:10:37,810
So only this place, if you make some changes and quicksort will change.

158
00:10:37,810 --> 00:10:42,880
If you are selecting randomly some element of sport, then it is randomise quicksort.

159
00:10:43,060 --> 00:10:47,260
So for that also Vásquez, Steinman's and Logan was Guesstimation Square.

160
00:10:47,270 --> 00:10:51,180
We cannot define how it looks like, but partitioning will be in the middle.

161
00:10:51,230 --> 00:10:52,600
Partitioning will be at the end.

162
00:10:54,910 --> 00:11:01,210
These are the two ways of defining best given, worst case, however, that look like then one last

163
00:11:01,210 --> 00:11:09,050
thing I have to show you one interesting comparison here between selections for time quicksort, selection

164
00:11:09,060 --> 00:11:15,670
selections already we have studied, let me show its behavior once again in selection, we select a

165
00:11:15,670 --> 00:11:17,980
position like this is index zero.

166
00:11:17,980 --> 00:11:19,540
We select the position.

167
00:11:19,900 --> 00:11:22,420
Then at this place who should come.

168
00:11:22,420 --> 00:11:25,300
We find out the minimum element and bring it then.

169
00:11:26,200 --> 00:11:31,030
So we select a position and find out element for that position.

170
00:11:31,030 --> 00:11:32,220
The statement is very important.

171
00:11:32,230 --> 00:11:34,750
Again, I'm going to repeat in selection.

172
00:11:34,750 --> 00:11:39,760
So we select our position and we find out an element for that position.

173
00:11:40,330 --> 00:11:47,590
But in quicksort we select an element and find out the position for that element where it should be

174
00:11:47,590 --> 00:11:48,190
in the list.

175
00:11:50,340 --> 00:11:57,960
Selecting a position, finding an element, selecting an element, finding a position, so the similarity

176
00:11:57,960 --> 00:12:05,790
between selection sort and quicksort, this selection of what index element?

177
00:12:07,490 --> 00:12:15,170
So this quicksort is also called selection exchange of sort, and this quicksort algorithm uses partitioning,

178
00:12:15,170 --> 00:12:19,310
that is the reason it is also called partition exchange.

179
00:12:19,970 --> 00:12:26,180
So three names for quicksort, selection, exchange for partition exchange for or quicksort.

180
00:12:28,070 --> 00:12:31,280
So that's all about the analysis of quicksort.

