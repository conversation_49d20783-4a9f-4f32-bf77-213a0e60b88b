1
00:00:00,150 --> 00:00:03,900
Now, let's compare <PERSON><PERSON> thought, an insertion fork.

2
00:00:05,200 --> 00:00:12,040
We have already finished these to let us compare them, see, the first thing is minimum number of comparisons

3
00:00:12,040 --> 00:00:12,940
in bubble thought.

4
00:00:13,450 --> 00:00:17,430
If the list is already shorter than the minimum number of comparisons are any.

5
00:00:18,690 --> 00:00:25,980
Then what about an insertion thought if the list is already sorted, then again, it also performs only

6
00:00:25,980 --> 00:00:28,230
and comparisons actually year and minus one.

7
00:00:28,710 --> 00:00:34,920
So let me write a disorder off and the maximum number of comparisons and squared comparisons.

8
00:00:34,950 --> 00:00:37,100
This also and square comparisons.

9
00:00:37,260 --> 00:00:40,520
If the list is already in descending order, then maximum comparison.

10
00:00:40,530 --> 00:00:43,930
So cinches gives us all the same time as all the same.

11
00:00:44,160 --> 00:00:47,340
See, let me write the case here already.

12
00:00:48,860 --> 00:00:57,320
In ascending and descending order, descending order than minimum time and descending order, maximum

13
00:00:57,320 --> 00:01:06,200
time, then minimum slaps, zero swaps actually so constant zero swaps then if it is in ascending the

14
00:01:06,200 --> 00:01:06,950
case.

15
00:01:06,950 --> 00:01:07,420
Right.

16
00:01:08,900 --> 00:01:16,970
The case, and this does not stop this, the case, the time, the maximum slaps and square and also

17
00:01:16,970 --> 00:01:21,450
in square and when this is in descending, then adoptive.

18
00:01:21,500 --> 00:01:23,090
Yes, adoptive dissolves.

19
00:01:23,100 --> 00:01:25,240
Adoptive stable.

20
00:01:25,250 --> 00:01:25,460
Yes.

21
00:01:25,460 --> 00:01:26,480
There's also stable.

22
00:01:26,480 --> 00:01:27,500
There's also stable.

23
00:01:28,670 --> 00:01:32,340
She almost all the properties are matching, they are similar.

24
00:01:32,570 --> 00:01:34,670
Yes, they are similar.

25
00:01:35,030 --> 00:01:36,600
Their properties are similar.

26
00:01:36,920 --> 00:01:40,400
These are only two algorithms which are adaptive.

27
00:01:40,640 --> 00:01:43,100
No other sorting algorithm is adaptive.

28
00:01:43,970 --> 00:01:47,720
These are adaptive and these are stable also.

29
00:01:47,870 --> 00:01:51,440
But there is one more algorithm, like much thought that is also stable.

30
00:01:52,280 --> 00:01:54,200
So only two algorithms are adaptive.

31
00:01:54,560 --> 00:01:57,460
Only three algorithms are stable out of those.

32
00:01:57,710 --> 00:02:01,950
These are two which are stable next linguist's.

33
00:02:02,840 --> 00:02:04,640
Is it suitable for Lincolnesque?

34
00:02:04,850 --> 00:02:08,030
Nor is it suitable for linguist.

35
00:02:08,270 --> 00:02:09,020
Yes.

36
00:02:09,690 --> 00:02:15,350
Here you don't have to shift the elements or create a new linkers, but here you may have to shift the

37
00:02:15,350 --> 00:02:16,040
elements.

38
00:02:17,280 --> 00:02:26,820
Then keep passes are the useful yes gives you for elements here are the useful no kapos are of no use.

39
00:02:27,270 --> 00:02:30,330
If you perform to possess you, you get to the largest element here.

40
00:02:30,330 --> 00:02:31,170
You don't get it.

41
00:02:32,160 --> 00:02:35,550
That is a comparison of bubbles or that inflation soared.

42
00:02:35,560 --> 00:02:38,570
So this comparison also very useful.

43
00:02:40,490 --> 00:02:41,860
That's all about competition.

