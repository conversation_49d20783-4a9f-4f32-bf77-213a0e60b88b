1
00:00:00,490 --> 00:00:09,010
And this video will look at a program for double, and we have you have to see more sorting techniques

2
00:00:09,010 --> 00:00:11,670
and all those sorts of things right on in a single program.

3
00:00:12,010 --> 00:00:15,390
So I'll be using the same project for showing you all the sorting methods.

4
00:00:16,090 --> 00:00:17,380
So let us start with bubbles.

5
00:00:18,490 --> 00:00:21,400
So I will give the project as Sots.

6
00:00:21,730 --> 00:00:24,220
And this is using C language.

7
00:00:25,940 --> 00:00:31,800
Here, inside the main function, I will declare an audit having few elements, so I will read on a

8
00:00:31,820 --> 00:00:32,810
few elements.

9
00:00:34,630 --> 00:00:41,350
So here I have taken an element, so also I will have one variable and whose value is and so that's

10
00:00:41,350 --> 00:00:42,700
it, I'm having an element.

11
00:00:43,330 --> 00:00:51,100
Now let us write a function for performing bubble sort function name is bubble x integer type array

12
00:00:51,430 --> 00:00:53,410
and also its size.

13
00:00:55,870 --> 00:01:00,250
Already we have discussed the logic of the same thing I have to write down here, I would take some

14
00:01:00,250 --> 00:01:04,269
temporary variables like I and J, which I'll be using it for followups.

15
00:01:05,600 --> 00:01:06,680
So the procedure is.

16
00:01:08,110 --> 00:01:12,610
For ISIS, zero is less than and minus one.

17
00:01:15,560 --> 00:01:16,580
I placeless.

18
00:01:18,750 --> 00:01:28,320
Then follow up, Jay should start from zero and G should stop at end minus a minus one, that should

19
00:01:28,320 --> 00:01:30,840
stop one element less always.

20
00:01:31,840 --> 00:01:39,250
So it should go on reducing the jobless, plus that inside this, I should check the condition if.

21
00:01:41,530 --> 00:01:49,030
E of G is greater than E of G plus one, if so, then I should swap the elements.

22
00:01:49,570 --> 00:01:54,310
So for swapping swap is a common function so far that I relied on a function just about this bubble

23
00:01:54,310 --> 00:01:56,740
chart, so I will directly use it here.

24
00:01:57,010 --> 00:02:00,760
Swap address of E of G.

25
00:02:01,930 --> 00:02:05,470
The address of E of G plus one.

26
00:02:07,410 --> 00:02:11,210
That said, there's a bubble for sort function next.

27
00:02:11,870 --> 00:02:15,810
OK, then that's the end of a loop.

28
00:02:16,840 --> 00:02:23,260
That said, this is the end of burbles heart function that I will write on such a function of what this

29
00:02:23,260 --> 00:02:25,180
means about this bubble sort function.

30
00:02:26,930 --> 00:02:32,870
That's all in bubble sort, and here I will ride on one function called swipe, so swipe function,

31
00:02:32,870 --> 00:02:44,540
which take two pointers like say X and integer Y addresses of two values then here, and it should assign

32
00:02:45,020 --> 00:02:52,410
value of X and the value of X should be changed to the value of Y and value of Y should be assigned

33
00:02:52,430 --> 00:02:53,160
with the M.

34
00:02:55,340 --> 00:03:02,030
Next, what there's a SWAT function and bubbles is a very small function, it's completed now inside

35
00:03:02,030 --> 00:03:04,250
the main function I will call bubble.

36
00:03:08,370 --> 00:03:14,190
Bubbles are that personality and a number of elements are taken after this, I will display that so

37
00:03:14,190 --> 00:03:16,050
far that I will take a look for.

38
00:03:16,080 --> 00:03:18,570
I assign zero, I use less than.

39
00:03:21,580 --> 00:03:23,470
And I plus, plus.

40
00:03:25,380 --> 00:03:27,900
They're printing an element percentile D.

41
00:03:29,670 --> 00:03:37,290
If I saw this film displaying all the elements after that, I will give a new line to get a proper output

42
00:03:38,130 --> 00:03:40,410
is not for this.

43
00:03:40,410 --> 00:03:44,550
I should also declare a lot more variable after and I will declare one more variable I.

44
00:03:46,400 --> 00:03:47,950
Later on the program, Nancy.

45
00:03:50,440 --> 00:03:52,340
Yes, it has sorted all the elements.

46
00:03:52,360 --> 00:03:57,690
That is two, three, four, five, seven, six, seven, nine, 10, 11, 12.

47
00:03:57,700 --> 00:03:59,620
I got all the elements and the sorted order.

48
00:04:00,910 --> 00:04:02,500
That's all in the bubble chart.

49
00:04:04,000 --> 00:04:09,040
We have already seen that we can improve bubble sort and we can make it adaptive by introducing a flag.

50
00:04:09,040 --> 00:04:11,030
So let us introduce a flag inside bubble.

51
00:04:11,950 --> 00:04:14,020
So here I am, jollity we have.

52
00:04:14,020 --> 00:04:20,589
So I will take one more available flag that this flag is initially zero and before the start of every

53
00:04:20,589 --> 00:04:23,260
pass I will make flag as zettl.

54
00:04:23,980 --> 00:04:25,000
And inside this.

55
00:04:26,260 --> 00:04:31,260
Whenever it is swopping, I will make the flag as one that is a flag.

56
00:04:31,570 --> 00:04:34,360
Is a slap flag as one.

57
00:04:35,280 --> 00:04:37,800
So as many times it has done, it will become one only.

58
00:04:37,810 --> 00:04:40,820
So at least one time a redundance like becomes one.

59
00:04:41,370 --> 00:04:47,580
Then after the first follow up, that is after the pass, I should check that if a flag is still zero,

60
00:04:47,760 --> 00:04:50,100
it means there was no shopping done.

61
00:04:50,100 --> 00:04:52,500
So it means that reason only the target Swarbrick.

62
00:04:54,690 --> 00:04:58,380
So that's all the improvement in bubble sort, so now it has became.

63
00:04:59,380 --> 00:05:04,720
And if so, I will change the numbers and see how the bubble thoughtworks.

64
00:05:06,740 --> 00:05:09,030
Now, up on these numbers, let's run bubbles.

65
00:05:09,240 --> 00:05:14,330
So I will not take so many numbers, I will reduce the size now I will take on the phone numbers and

66
00:05:14,330 --> 00:05:19,100
inciters also for now, let us run by will start on this and see how it is working.

67
00:05:19,100 --> 00:05:21,440
Then we will give the elements in the.

68
00:05:24,830 --> 00:05:30,080
So now let us run bubbles and see these are all sorted elements, how bubbles are working, I'll put

69
00:05:30,080 --> 00:05:32,630
a break point on bubbles and not run.

70
00:05:33,720 --> 00:05:34,940
I will enter into bubbles.

71
00:05:36,000 --> 00:05:36,630
Yes.

72
00:05:38,880 --> 00:05:43,410
Now you can see that the elements are all in disorder, that is three seven, nine, 10, and there

73
00:05:43,410 --> 00:05:44,370
are four elements here.

74
00:05:44,370 --> 00:05:51,510
And that Bulgaria, if you see if I press at seven and go line by line now it is entering into the first

75
00:05:51,510 --> 00:05:58,380
four loop, then flight zero, then it has came out of the first inner for loop.

76
00:06:00,710 --> 00:06:03,700
Is false, condition is false.

77
00:06:04,770 --> 00:06:11,190
Then next time, also conditions, false flag is still zero because it has come up, so it has performed

78
00:06:11,190 --> 00:06:11,890
only one part.

79
00:06:11,910 --> 00:06:14,340
It has gone through all the elements only once.

80
00:06:16,390 --> 00:06:22,300
So that's all about a well thought, we have improved it also.

81
00:06:23,820 --> 00:06:29,120
And made it adaptive so you can practice this program, that's all in this video.

