1
00:00:00,590 --> 00:00:07,340
Let us look at the recursive procedure for reversing a linguist so that you have learned recursion in

2
00:00:07,340 --> 00:00:14,870
many topics, so we know that recursion will have to fix this one is calling phase and then returning

3
00:00:14,870 --> 00:00:15,350
phase.

4
00:00:16,010 --> 00:00:24,350
Now, let us decide, shall we reverse the link of each node while going or calling out while returning?

5
00:00:25,280 --> 00:00:28,130
We should do it in returning if we are doing it while going.

6
00:00:28,130 --> 00:00:31,340
If you point on this one, then how we can get there to solve this one.

7
00:00:32,360 --> 00:00:37,730
But while returning, we can make it point on this and come back on this node and make it point on this

8
00:00:37,730 --> 00:00:38,030
node.

9
00:00:38,210 --> 00:00:39,770
And this is still the repointing here.

10
00:00:40,010 --> 00:00:43,240
To come back on this node, we can make it point here while they're done it.

11
00:00:43,260 --> 00:00:44,010
We should do that.

12
00:00:44,510 --> 00:00:46,140
OK, let us see how we can do.

13
00:00:46,580 --> 00:00:53,600
Let us take a pointer p one being here and it will move in the next call here, the next one here,

14
00:00:53,600 --> 00:00:54,590
the next call here.

15
00:00:54,800 --> 00:01:00,380
The next call becomes null not to come back while returning null.

16
00:01:00,530 --> 00:01:03,370
PS on that node it has to point on this node.

17
00:01:03,680 --> 00:01:09,860
So we need one tail pointer also because this node should point on this node means we should have data

18
00:01:09,860 --> 00:01:10,610
for this node.

19
00:01:11,180 --> 00:01:15,740
So we should also have one more pointer which is following B, so we need to Blanca's.

20
00:01:16,220 --> 00:01:19,060
So let me show you how this can be written.

21
00:01:19,400 --> 00:01:23,150
So then I have a pointer p up on this node, then I need one pointer.

22
00:01:23,150 --> 00:01:26,420
Q here, which is another novelty's moving.

23
00:01:26,420 --> 00:01:28,820
Q Should come here when he comes here.

24
00:01:28,850 --> 00:01:30,560
Q Should be here when he comes here.

25
00:01:30,610 --> 00:01:34,460
You should be here then while returning P can point on this node.

26
00:01:34,460 --> 00:01:36,710
Q Then again, return begin point on.

27
00:01:36,710 --> 00:01:38,870
Q Return to while returning.

28
00:01:38,870 --> 00:01:40,250
We can reverse the links.

29
00:01:42,170 --> 00:01:43,730
So let me write a function.

30
00:01:45,160 --> 00:01:53,890
Rewards, it should take two pointers, first one is cue, second one is speed for function, reward

31
00:01:53,900 --> 00:01:55,090
for taking two pointers.

32
00:01:55,090 --> 00:02:01,540
First one is cue that previous one and next, this one is A B for two point at sticking.

33
00:02:02,170 --> 00:02:05,020
Then what we should do calling and just go forward.

34
00:02:05,170 --> 00:02:17,290
So if B is not equal to none, then do not call it self again by making first one as B means you should

35
00:02:17,290 --> 00:02:18,340
come on B.

36
00:02:20,180 --> 00:02:25,400
Then they should move to the next node, so next pointer be next.

37
00:02:27,390 --> 00:02:28,280
So first point that is.

38
00:02:28,280 --> 00:02:32,990
Q That will be complete and B will become next, calling itself again and again.

39
00:02:33,200 --> 00:02:39,720
So recursively when it is calling at the end, B becomes null cuz here.

40
00:02:40,040 --> 00:02:45,710
So at that time when he became null, what I should do c now I'll be returning back.

41
00:02:45,770 --> 00:02:46,810
So that is first.

42
00:02:47,450 --> 00:02:48,290
This is first.

43
00:02:48,410 --> 00:02:50,360
So I should make first point on that.

44
00:02:50,360 --> 00:02:53,720
Q So first should point on.

45
00:02:53,720 --> 00:02:57,020
Q Then I should do this when be became null.

46
00:02:57,170 --> 00:03:03,140
If P is not null, it was calling Welby's null first became Q now it is returning.

47
00:03:06,050 --> 00:03:12,950
While returning first time, we will be here and you will be here, so while returning, what I should

48
00:03:12,950 --> 00:03:15,740
do be next should point on cue.

49
00:03:16,040 --> 00:03:23,570
So please next should point on cue that from the beginning it will return.

50
00:03:23,570 --> 00:03:27,070
So vitamins A B will be here and you will be here.

51
00:03:27,320 --> 00:03:29,030
So next will point on.

52
00:03:29,030 --> 00:03:33,020
Q So you can see that this is a recursive call.

53
00:03:33,020 --> 00:03:34,940
So it is calling itself again and again.

54
00:03:35,270 --> 00:03:36,400
Whatever is thereafter.

55
00:03:36,410 --> 00:03:42,070
Recursive call will be executed at returning time, so attriting time links will be reversed.

56
00:03:42,950 --> 00:03:49,310
So this is a simple function for reversing Lincolnesque using recursion for which really quite good

57
00:03:49,310 --> 00:03:57,720
pointers because I had an Q is A tail of that B and then P became null.

58
00:03:57,770 --> 00:04:03,530
Also I have written in Elsipogtog to bring first up on that note, even Q Was he that time only fust

59
00:04:03,530 --> 00:04:04,570
was brought here.

60
00:04:04,790 --> 00:04:06,140
So this becomes the first node.

61
00:04:06,140 --> 00:04:07,550
The next, the next, the next.

62
00:04:07,850 --> 00:04:11,660
Atlast this will be null and this will be pointing on this one.

63
00:04:16,149 --> 00:04:17,959
So that's all about reversing a linguist.

