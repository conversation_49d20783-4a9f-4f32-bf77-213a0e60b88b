1
00:00:02,100 --> 00:00:08,090
In this video, we'll look at a program for diagonal matrix using C++, so I will be defining a class

2
00:00:08,100 --> 00:00:09,300
for diplomatics.

3
00:00:09,960 --> 00:00:11,490
I'll create a new project here.

4
00:00:12,000 --> 00:00:16,260
I'll call the project name as Matisses CPB.

5
00:00:17,910 --> 00:00:24,780
Now, the project team selected as matters of CPB and the language of C++, next, let us create the

6
00:00:24,780 --> 00:00:25,260
project.

7
00:00:26,230 --> 00:00:29,980
Project is created and this is the main function.

8
00:00:31,320 --> 00:00:32,130
No reality.

9
00:00:33,810 --> 00:00:35,390
I have expanding one light boat.

10
00:00:35,410 --> 00:00:38,710
I will define a class for diagonal matrix.

11
00:00:40,070 --> 00:00:46,460
Now, inside the diagonal matrix, the data numbers that we need are an update for that, I will take

12
00:00:46,460 --> 00:00:48,560
a pointer and the dimensions.

13
00:00:50,780 --> 00:00:54,890
Now, in public, the very first thing that we will ride on is a constructor.

14
00:00:58,010 --> 00:01:03,470
Bagnall constructor's, first of all, I will define a non parametrized constructor, so here I will

15
00:01:03,470 --> 00:01:04,280
create a.

16
00:01:05,750 --> 00:01:13,520
So here I will take minimum dimensions as a tool for diagrammatic that it's two by two matrix and I

17
00:01:13,520 --> 00:01:17,690
will create a size two in both size to.

18
00:01:23,630 --> 00:01:31,340
The is not given the minimum wage will create an area of size to an otherwise, we will have an parametrized

19
00:01:31,340 --> 00:01:32,150
constructor.

20
00:01:35,480 --> 00:01:42,070
And inside this little first set that I mentioned and a sign and what is this?

21
00:01:42,080 --> 00:01:45,610
And the first one is this one and the second and is this one.

22
00:01:45,620 --> 00:01:46,900
So how to differentiate that?

23
00:01:46,910 --> 00:01:51,110
So we have to use this pointer and we can differentiate.

24
00:01:51,140 --> 00:01:52,160
So this means.

25
00:01:53,760 --> 00:01:59,730
Dissent and on the right hand side, what end is given simply and is given, so that is a local parameter.

26
00:02:03,150 --> 00:02:06,330
Then we should create an array of size and.

27
00:02:10,580 --> 00:02:10,820
These.

28
00:02:13,390 --> 00:02:17,480
Now the constructor is ready as we are dynamically creating an array from here.

29
00:02:17,660 --> 00:02:19,280
We should also have a destructor.

30
00:02:22,350 --> 00:02:25,180
And this is distracter should delete that uhry.

31
00:02:26,180 --> 00:02:28,160
An object is not in use.

32
00:02:30,320 --> 00:02:36,710
So the first function we will write is set, which will not take a day because it's directly inside

33
00:02:36,710 --> 00:02:37,440
the same class.

34
00:02:38,210 --> 00:02:43,250
It means I and G and the element that we want to set.

35
00:02:45,110 --> 00:02:50,560
Then I should have a function forget, which will return an element from a given index.

36
00:02:50,590 --> 00:02:55,610
I should pass in and then I should also have a function for display.

37
00:02:59,130 --> 00:03:04,770
Now these functions have to implement them outside the class using scope resolution, so first function

38
00:03:04,770 --> 00:03:05,470
is set.

39
00:03:06,060 --> 00:03:07,470
So this is.

40
00:03:12,810 --> 00:03:15,390
Diagonal matrix and the function name is set.

41
00:03:23,530 --> 00:03:30,810
It's taking three parameters now, the procedure the same, if I is equal to G, then only we will set

42
00:03:30,810 --> 00:03:35,610
an element in a hurry at a minus one location.

43
00:03:38,630 --> 00:03:40,550
Otherwise, they will not said anything.

44
00:03:41,620 --> 00:03:42,940
The next function is.

45
00:03:45,570 --> 00:03:46,620
Get function.

46
00:03:48,370 --> 00:03:51,010
Which will take two parameters, I and J.

47
00:03:53,320 --> 00:04:00,220
And this will also check, if I is equal to G, then it will return an element from Uhry at index A

48
00:04:00,220 --> 00:04:00,950
minus one.

49
00:04:01,300 --> 00:04:03,340
Otherwise, it will return Zettl.

50
00:04:06,440 --> 00:04:09,020
Then we should have a function for display.

51
00:04:14,060 --> 00:04:20,720
Display function, which doesn't take any parameters it needs to for loops, for displaying all elements

52
00:04:20,720 --> 00:04:25,520
for ISIS zero, i.e less than an eight plus plus.

53
00:04:27,570 --> 00:04:28,380
Then for.

54
00:04:29,920 --> 00:04:37,930
Jails are in zero G's, less than and and jail plus plus down here, if I is equal to Jaiden only,

55
00:04:37,930 --> 00:04:41,200
we have an element in a diagonal, so I should display C out.

56
00:04:42,690 --> 00:04:44,260
Element from Afie.

57
00:04:45,530 --> 00:04:47,360
And I should give some space.

58
00:04:49,630 --> 00:04:52,030
Otherwise, I should print Zettl.

59
00:04:55,980 --> 00:04:57,300
And also interspace.

60
00:04:58,270 --> 00:05:04,300
And at the end of for look, I should say Siao and then for the new line.

61
00:05:06,310 --> 00:05:08,560
That followed the display function.

62
00:05:10,380 --> 00:05:11,870
Here are your streamers included.

63
00:05:11,890 --> 00:05:15,540
So we should use namespace, so using namespace.

64
00:05:17,400 --> 00:05:18,030
S.T..

65
00:05:19,070 --> 00:05:27,290
Now, inside the main function, I will create an object of diagonal matrix, B of dimension for.

66
00:05:29,770 --> 00:05:35,860
I will call September 3rd and I will set a few elements, that is first is one, comma one and the values

67
00:05:35,860 --> 00:05:38,800
of five inside the same line I will write on.

68
00:05:40,020 --> 00:05:45,660
Likewise, I will set other elements, a detailed set to come to as a.

69
00:05:47,230 --> 00:05:48,340
Then did Dot.

70
00:05:49,330 --> 00:05:50,020
Said.

71
00:05:52,370 --> 00:05:54,290
Recommendatory is nine.

72
00:05:57,760 --> 00:06:06,080
They need all set for comfort, as we have given the dimensions are for values, too, and then did

73
00:06:06,090 --> 00:06:07,650
not display.

74
00:06:10,880 --> 00:06:12,230
Now, let us run the program.

75
00:06:16,180 --> 00:06:20,980
Yes, we got a diagonal matrix display function is displaying all the diagonal elements of the elements

76
00:06:20,980 --> 00:06:24,940
are Zeitels, and the values that we have set are five, eight, nine, 12.

77
00:06:24,940 --> 00:06:26,440
So they are appearing in the diagonal.

78
00:06:28,920 --> 00:06:35,640
So that's all the C++ program for Bagnall Mattocks, I have written a complete class for diagrammatic,

79
00:06:35,730 --> 00:06:36,420
not all of them.

80
00:06:36,510 --> 00:06:37,860
This is also look similar.

81
00:06:38,280 --> 00:06:44,910
Only the name will change and the size, depending on their required size, they should clear the area

82
00:06:44,910 --> 00:06:49,110
of that size and also get and set function will change.

83
00:06:50,760 --> 00:06:56,250
I think display function will look similar or only the difference may be the condition may change.

84
00:06:57,680 --> 00:06:59,870
So the rest of the programs will also be similar.

85
00:07:00,950 --> 00:07:02,080
That's all we stop here.

