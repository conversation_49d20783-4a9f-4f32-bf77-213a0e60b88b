1
00:00:00,690 --> 00:00:08,119
In this video, we will learn how to write a creative play travel, since we have already seen a recursive

2
00:00:08,119 --> 00:00:15,570
phratry traversal, we have a recursive functions now we will write iterative functions for preorder

3
00:00:15,570 --> 00:00:15,930
in order.

4
00:00:15,930 --> 00:00:18,730
Infostrada recursive function Fiordaliso.

5
00:00:19,230 --> 00:00:25,260
So if you want to convert a recursive function into iterative function that is a function of the loop,

6
00:00:25,860 --> 00:00:30,210
then you may have to use your own stack.

7
00:00:30,600 --> 00:00:33,720
It's not necessary always in some procedure.

8
00:00:34,290 --> 00:00:35,440
That depends on the procedure.

9
00:00:35,820 --> 00:00:39,750
So in some procedure you may need to have your own statements.

10
00:00:39,750 --> 00:00:46,740
You have to use your own <PERSON><PERSON><PERSON> iteration so that, for example, in this example we are going to

11
00:00:46,740 --> 00:00:48,270
use a stack without STAC.

12
00:00:48,270 --> 00:00:51,060
We cannot convert recursion to iteration.

13
00:00:51,970 --> 00:00:55,780
So first of all, I will explain the procedure then after that, I will try to function also.

14
00:00:56,170 --> 00:00:57,910
So let us start with the pre order.

15
00:00:58,570 --> 00:01:03,690
Let me show you how we can perform preorder iteratively without recursion.

16
00:01:04,569 --> 00:01:06,650
Let us perform it up on the street.

17
00:01:07,480 --> 00:01:13,970
As I said, I may require a stock so beforehand I will draw up stock and keep it ready.

18
00:01:14,260 --> 00:01:15,480
Maybe it is useful.

19
00:01:16,990 --> 00:01:23,140
And here I'm going to write a notebook and one more thing is preorder steps, our first print, then

20
00:01:23,140 --> 00:01:29,700
perform preorder on left show and perform preorder on Rachell that is ruled left-to-right.

21
00:01:30,370 --> 00:01:31,450
So Luttmann spread.

22
00:01:31,540 --> 00:01:35,290
So Faceprint then go to the site, then we'll do Rightside.

23
00:01:35,470 --> 00:01:37,670
So let us start and follow these steps.

24
00:01:38,110 --> 00:01:41,790
OK, so let us start from Rup Naughties here.

25
00:01:42,880 --> 00:01:46,630
What is the first thing I have to do with the data so I will print that data.

26
00:01:46,750 --> 00:01:49,850
It is printed then move on to left.

27
00:01:49,870 --> 00:01:51,550
OK, I'm moving on to left.

28
00:01:52,220 --> 00:02:00,340
OK then on this again what I have to do printed it up will get printed three then do not go to left

29
00:02:00,340 --> 00:02:00,670
site.

30
00:02:00,970 --> 00:02:05,320
OK, I'll go on the left side then print the data.

31
00:02:05,320 --> 00:02:09,160
First step here is print the data for this node, then go to the left side.

32
00:02:09,160 --> 00:02:13,360
OK, I will go on left side not became null.

33
00:02:13,780 --> 00:02:15,910
No, do not go to right site.

34
00:02:17,230 --> 00:02:18,050
Watch carefully.

35
00:02:19,000 --> 00:02:22,630
Go to Rightside, whose right side, this north right side.

36
00:02:22,960 --> 00:02:24,290
Do you have the evidence of that?

37
00:02:24,310 --> 00:02:25,690
Nope, no peace there.

38
00:02:25,720 --> 00:02:27,070
So what is this?

39
00:02:28,710 --> 00:02:35,640
None of them said this nowhere on the three, so to begin, how do you get out of this nor the address

40
00:02:35,640 --> 00:02:40,370
of this, nor is it this node and this address of this notice that this one and that is not true.

41
00:02:40,650 --> 00:02:42,090
So I should again confirm Ruth.

42
00:02:43,110 --> 00:02:44,820
That's not that far.

43
00:02:45,330 --> 00:02:51,360
This is the problem, so I should go back to this and again, remember, I should go back to this note

44
00:02:51,360 --> 00:02:53,100
also to go on the right side.

45
00:02:53,220 --> 00:02:57,150
So I need those addresses then how to all those addresses?

46
00:02:57,450 --> 00:02:59,910
I should have pushed them in the stack.

47
00:03:00,470 --> 00:03:02,170
Yes, that's what I missed.

48
00:03:02,460 --> 00:03:06,330
So let me start over once again and remove this.

49
00:03:07,050 --> 00:03:15,650
Let us not be forced to step in the data, but indeed, then what to do, going left, going on left,

50
00:03:15,660 --> 00:03:18,690
but are going to have to come back on this note so that I can go on.

51
00:03:18,690 --> 00:03:19,060
Right.

52
00:03:19,320 --> 00:03:21,030
So I need the address of this law.

53
00:03:21,270 --> 00:03:23,250
So I have not done their addresses here.

54
00:03:23,250 --> 00:03:29,160
So I will write down address off that note eight, whatever that address maybe, I don't know, right.

55
00:03:29,700 --> 00:03:30,620
Then go to left.

56
00:03:31,260 --> 00:03:32,370
OK, now move.

57
00:03:32,370 --> 00:03:33,110
Be here.

58
00:03:34,680 --> 00:03:40,230
Now, what to do with the small print data or print data three then you have to go on left.

59
00:03:40,380 --> 00:03:46,980
But before going on left of that note containing data tree address of that note into the stack.

60
00:03:48,270 --> 00:03:48,990
That's perfect.

61
00:03:49,260 --> 00:03:52,170
Then go to decide what to do here.

62
00:03:52,620 --> 00:04:01,320
Print for Bush diaries of this one into the stack after pushing Google left chain not became null became.

63
00:04:02,780 --> 00:04:06,990
C, d, another stack is not empty, stack is not empty, right?

64
00:04:07,370 --> 00:04:09,180
OK, continue what they want.

65
00:04:09,200 --> 00:04:10,250
I want the address of this.

66
00:04:10,250 --> 00:04:18,140
More paper bags of food, go to the Snork so people can give it to be so Tinos that as of this morning

67
00:04:18,190 --> 00:04:19,190
I'm standing here.

68
00:04:19,440 --> 00:04:19,750
Right.

69
00:04:20,060 --> 00:04:29,150
So when standing here, go on the right side for the steps for Pope, go to that note and move to its

70
00:04:29,150 --> 00:04:31,530
side check, then continue.

71
00:04:32,090 --> 00:04:35,750
Again, no cities, none, but the state is not empty.

72
00:04:36,830 --> 00:04:39,150
Address, that's what address of three.

73
00:04:39,590 --> 00:04:43,480
OK, go to the Snoad then move to Rightside.

74
00:04:43,820 --> 00:04:45,620
Not continue for this.

75
00:04:45,620 --> 00:04:46,970
Nor what do you have to do.

76
00:04:47,450 --> 00:04:48,790
Print Pusztai.

77
00:04:48,800 --> 00:04:49,860
Listen to the statue.

78
00:04:49,970 --> 00:04:50,280
Only one.

79
00:04:50,370 --> 00:04:51,410
This is different this time.

80
00:04:51,470 --> 00:04:53,410
So here it will be inserted.

81
00:04:53,960 --> 00:04:55,390
Then we'll be left side.

82
00:04:57,630 --> 00:05:04,530
It is island, but this is not what we are doing, pop out an address, take this upon that address

83
00:05:04,830 --> 00:05:07,150
and then move to a -- site.

84
00:05:08,280 --> 00:05:10,580
So right now, so does the smell.

85
00:05:10,830 --> 00:05:13,890
And the stack is not empty of this address.

86
00:05:14,160 --> 00:05:16,780
Eight biggity over there.

87
00:05:17,250 --> 00:05:19,740
OK, then move to the right site.

88
00:05:21,870 --> 00:05:24,710
So whenever it is not, we are doing this right.

89
00:05:25,410 --> 00:05:26,970
Continue is not.

90
00:05:27,300 --> 00:05:27,780
Yes.

91
00:05:27,810 --> 00:05:28,490
So what to do?

92
00:05:28,860 --> 00:05:29,910
But in the five.

93
00:05:30,870 --> 00:05:31,210
Right.

94
00:05:31,560 --> 00:05:37,000
And the of that file into the stack at five into the stack, this is a free space.

95
00:05:37,020 --> 00:05:40,720
Now then move to the left side.

96
00:05:41,340 --> 00:05:49,100
Again, this is not null doowop, but in seven Goodhew left before going to left chain insert the address

97
00:05:49,110 --> 00:05:53,320
of seven into the stack and go to left China these null annulments.

98
00:05:53,380 --> 00:05:59,100
What we are doing that does go to that node and move to its right chain.

99
00:06:00,750 --> 00:06:03,990
So whenever these not we are doing this again.

100
00:06:03,990 --> 00:06:06,300
This is not to address.

101
00:06:07,960 --> 00:06:12,640
Go to that north and move to write these nocturnal.

102
00:06:14,180 --> 00:06:22,970
They're not printed to go to left side for this child again, push a dinosaur that Naude then you are

103
00:06:22,970 --> 00:06:26,300
going on the left child predators and Goodhew left child.

104
00:06:29,140 --> 00:06:39,970
These people don't address go to that north and move to write Child Surtees Aguinaldo naughties null.

105
00:06:40,420 --> 00:06:44,230
And there is nothing in the stack, there's nothing in the stack.

106
00:06:44,680 --> 00:06:47,570
So we have finished the procedure and we got better.

107
00:06:48,160 --> 00:06:49,180
This is preorder.

108
00:06:50,580 --> 00:06:52,440
So this is how the procedure works.

109
00:06:53,360 --> 00:06:59,970
So when to stop this procedure, then these none as well as the stomach is empty now let us verify our

110
00:06:59,970 --> 00:07:05,120
resod, preorder and just a trace with the finger until you preorder.

111
00:07:05,120 --> 00:07:07,900
I just said we should point the finger in this direction.

112
00:07:08,560 --> 00:07:09,710
The left hand side of eight.

113
00:07:09,710 --> 00:07:10,550
Yes, eight.

114
00:07:10,820 --> 00:07:13,310
The next three years for that.

115
00:07:13,310 --> 00:07:15,370
Didn't get inside nine.

116
00:07:15,680 --> 00:07:18,930
Then get inside five, then seven, then two.

117
00:07:18,980 --> 00:07:20,300
Yes, it is perfect.

118
00:07:20,750 --> 00:07:22,370
No, I have to right on the program.

119
00:07:22,610 --> 00:07:24,320
So already I get a few things there.

120
00:07:24,620 --> 00:07:29,180
A function preorder taking point deep en route and a stack is also available.

121
00:07:29,180 --> 00:07:31,130
Assume that stack is initialized.

122
00:07:32,460 --> 00:07:33,450
What are the steps?

123
00:07:35,970 --> 00:07:37,470
We were repeating the steps.

124
00:07:39,640 --> 00:07:43,300
How long is not known and Stack is not empty.

125
00:07:43,570 --> 00:07:48,160
OK, so repeating the statements by loop while.

126
00:07:50,710 --> 00:07:56,800
Be not equal to none of it's not and are.

127
00:07:58,000 --> 00:08:04,450
Not is empty as steam stack is not empty.

128
00:08:05,140 --> 00:08:06,910
So while there is not No.

129
00:08:07,820 --> 00:08:15,680
And the stack is not empty if it is another false strike is ultimately false, both are false, it will

130
00:08:15,680 --> 00:08:16,090
stop.

131
00:08:16,250 --> 00:08:20,930
So I should use order that the steps, the steps are very simple.

132
00:08:21,320 --> 00:08:29,120
If it is not null, let us see this step again from here to get the idea if it is not null.

133
00:08:29,450 --> 00:08:33,080
Print data Bush address.

134
00:08:33,650 --> 00:08:35,169
Go to left China.

135
00:08:37,380 --> 00:08:45,950
Right, if it is not so here, if it is not null, then three things printed data, motion detectors

136
00:08:46,080 --> 00:08:48,190
in the stack and move to left China.

137
00:08:48,480 --> 00:08:53,570
President Bush to the stack and move to left China.

138
00:08:54,060 --> 00:08:55,220
If it is not known.

139
00:08:56,130 --> 00:09:00,780
If it is nothing else, what we should do and what we should do.

140
00:09:01,180 --> 00:09:07,460
See when it was null, what we were doing here, what we did at that time when it wasn't Republican

141
00:09:07,560 --> 00:09:15,000
alternatives and go to China so and address from the start of opportunities from the start and go to

142
00:09:15,000 --> 00:09:15,160
right.

143
00:09:15,210 --> 00:09:18,240
China, that's on and off loop.

144
00:09:18,240 --> 00:09:25,290
And the function that's all is the iterative function for preorder traversal, which is you utilizing

145
00:09:25,290 --> 00:09:25,950
a stack.

146
00:09:26,160 --> 00:09:31,680
And one more important thing is the stack should be of each type integer type character type, which

147
00:09:31,680 --> 00:09:33,630
type it is stored in the addresses.

148
00:09:33,780 --> 00:09:37,320
So it should be of a type address of this node.

149
00:09:38,130 --> 00:09:40,170
So while writing the program, I will show you that.

