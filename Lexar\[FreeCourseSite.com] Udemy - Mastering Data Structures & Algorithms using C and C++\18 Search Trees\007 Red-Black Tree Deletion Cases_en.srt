1
00:00:00,440 --> 00:00:04,320
In this video, we'll look at deletion from the BlackBerry.

2
00:00:05,300 --> 00:00:08,780
This is a little complex topic, but I will make it very, very, very simple.

3
00:00:10,130 --> 00:00:18,590
This is similar to deletion from binary search tree and involving rotations or reconnoitering of.

4
00:00:19,970 --> 00:00:24,020
So let us look at the method for deletion from binary search.

5
00:00:24,260 --> 00:00:29,810
So first of all, understand it perfectly then deletion from that practice, very easy.

6
00:00:30,560 --> 00:00:31,640
So let us understand.

7
00:00:31,670 --> 00:00:37,430
So already I have taken an example, disarrayed black thing, but don't see it as a red flag to look

8
00:00:37,430 --> 00:00:39,440
at it as a binary search tree.

9
00:00:40,460 --> 00:00:43,160
Now, how deletion happens in binary search tree.

10
00:00:43,730 --> 00:00:47,850
As an example, it doesn't delete some nodes and see the cases.

11
00:00:48,470 --> 00:00:55,910
So in this whatever we understand here that will help us understanding the from either the black tree.

12
00:00:56,150 --> 00:00:58,700
So focus on this procedure for deletion from binary.

13
00:00:58,700 --> 00:01:00,470
So let us take example.

14
00:01:00,890 --> 00:01:03,040
Suppose I want to delete 90.

15
00:01:03,380 --> 00:01:08,570
So the method for deletion from binary search is, first of all, search for 1990s.

16
00:01:08,570 --> 00:01:10,160
Greater than this.

17
00:01:10,160 --> 00:01:10,790
Greater than this.

18
00:01:11,120 --> 00:01:11,660
Yes, it is.

19
00:01:12,530 --> 00:01:15,440
So we deleted this a leaf node.

20
00:01:15,560 --> 00:01:17,990
So we have to simply delete it now.

21
00:01:18,080 --> 00:01:24,520
Next second example, I want to delete Banty search for 2020 slenderness.

22
00:01:24,670 --> 00:01:25,660
Yes, it is found.

23
00:01:26,090 --> 00:01:29,000
Then we delete this node actually in binary.

24
00:01:29,000 --> 00:01:30,890
So we don't believe that node.

25
00:01:31,100 --> 00:01:36,950
We delete the value then that nodes capacity's some other value will take its place.

26
00:01:36,990 --> 00:01:42,920
Who will take the place either in order purchaser or in order successor.

27
00:01:43,190 --> 00:01:51,200
So who comes before this node in order to Lavasan this one and who comes after this one today.

28
00:01:51,920 --> 00:01:55,780
So either 10 will take its place or 30 will take its place.

29
00:01:56,820 --> 00:02:04,010
I don't know, you can observe one thing, the note that is deleted is 30, and that is a leaf NORDER.

30
00:02:04,230 --> 00:02:08,710
So they will come here and this note, we will delete physically today the leaf node.

31
00:02:08,940 --> 00:02:15,610
So one thing we understand that whenever you delete some from binary search tree or leaf nodes, delete

32
00:02:16,230 --> 00:02:23,460
that next isec then will be deleted then it's not a leaf note, but it is having only one child.

33
00:02:23,460 --> 00:02:24,970
It is not having a child.

34
00:02:25,500 --> 00:02:26,720
It is having left child.

35
00:02:27,270 --> 00:02:27,800
Yes.

36
00:02:28,050 --> 00:02:33,900
So when you are deleting an order from the binary search tree, either it may be a leaf node or it will

37
00:02:33,900 --> 00:02:35,430
have exactly one child.

38
00:02:35,430 --> 00:02:36,670
It will not have two children.

39
00:02:37,440 --> 00:02:39,090
This is the very important point.

40
00:02:39,450 --> 00:02:42,050
Nonetheless, observe two some more examples.

41
00:02:42,330 --> 00:02:45,840
So I want to delete 50 who will take its place.

42
00:02:46,770 --> 00:02:50,040
First of all, will remove 50 50 is the first cell phone.

43
00:02:50,050 --> 00:02:50,960
So remove 50.

44
00:02:50,970 --> 00:02:54,910
And it's a place we will bring some other node and we will delete that node.

45
00:02:55,200 --> 00:03:00,570
So who will come here in order predecessor who is in order to serve, which comes before fifty.

46
00:03:00,930 --> 00:03:02,520
Forty comes before fifty.

47
00:03:03,060 --> 00:03:03,390
Right.

48
00:03:05,360 --> 00:03:11,990
Or else, in order successor who will come after 50, 60 will come, so either 40 will go in its place

49
00:03:11,990 --> 00:03:13,740
and or 60 will go its place.

50
00:03:14,000 --> 00:03:20,090
So 40 goes to their place and we will delete 40 or 60 go to its place and we will delete sixty nine

51
00:03:20,090 --> 00:03:23,590
few of in this example, 60 is the least Norder.

52
00:03:23,900 --> 00:03:24,200
Right.

53
00:03:24,500 --> 00:03:26,770
And 40 is not the least more.

54
00:03:26,960 --> 00:03:29,620
It is having one child that is left child.

55
00:03:30,140 --> 00:03:34,610
It will not have an change if suppose there is one child here, there is one child here.

56
00:03:35,300 --> 00:03:36,140
What it could be.

57
00:03:36,140 --> 00:03:37,160
It is greater than 40.

58
00:03:37,160 --> 00:03:37,940
So it is a 40.

59
00:03:37,940 --> 00:03:40,700
Fine then if I delete 50 who will take its place.

60
00:03:40,700 --> 00:03:43,920
40 will not get Fliss 45 and take its place.

61
00:03:43,940 --> 00:03:44,770
So that's what.

62
00:03:45,020 --> 00:03:46,850
So this will not be there.

63
00:03:47,030 --> 00:03:50,380
So then what is taking place means it is having this child.

64
00:03:51,500 --> 00:03:57,650
So finally we can say that when we are deleting from behind the research team, it will be leaf node

65
00:03:57,800 --> 00:04:00,090
or it will have exactly one child.

66
00:04:00,420 --> 00:04:01,520
I remember this thing.

67
00:04:02,130 --> 00:04:05,000
The deletion from BlackBerry depends on this.

68
00:04:05,000 --> 00:04:12,170
Only standard BlackBerry deletion have many cases if you have certain textbooks, some authors say there

69
00:04:12,170 --> 00:04:12,860
are six cases.

70
00:04:12,920 --> 00:04:14,930
Some authors say there are eight cases.

71
00:04:15,410 --> 00:04:17,230
I will say there are no cases, though.

72
00:04:17,240 --> 00:04:22,820
I have read on some cases here, but don't see it as a case that's not based on the deletion procedure

73
00:04:22,820 --> 00:04:28,000
from the research which will try to delete from the BlackBerry.

74
00:04:28,970 --> 00:04:31,420
No, let us come to the examples before that.

75
00:04:31,700 --> 00:04:32,540
See again.

76
00:04:32,540 --> 00:04:33,080
Once again.

77
00:04:33,110 --> 00:04:33,860
Remember this.

78
00:04:34,100 --> 00:04:36,800
We will be working at the least level.

79
00:04:36,800 --> 00:04:39,570
That is the last level of three.

80
00:04:39,800 --> 00:04:40,100
Right.

81
00:04:40,510 --> 00:04:45,140
Will not be working towards the road, will be working towards leaving because this node goes there

82
00:04:45,410 --> 00:04:47,390
and this means we have to adjust.

83
00:04:47,480 --> 00:04:49,870
So that's why I have these examples.

84
00:04:49,880 --> 00:04:50,060
Right.

85
00:04:50,360 --> 00:04:52,950
So we are working on the smaller level, like lowest level.

86
00:04:53,960 --> 00:04:54,830
Now let us see.

87
00:04:55,340 --> 00:04:57,670
Suppose, for example, the BlackBerry.

88
00:04:57,690 --> 00:04:59,600
Imagine there are other models are also there.

89
00:04:59,900 --> 00:05:00,760
Not from this.

90
00:05:00,770 --> 00:05:02,810
I want to delete this note.

91
00:05:02,810 --> 00:05:04,580
I want to delete this node.

92
00:05:04,700 --> 00:05:11,570
Whatever the values data in that one, I want to believe that node then search for the key node is found

93
00:05:12,090 --> 00:05:12,830
deleted.

94
00:05:13,610 --> 00:05:19,540
And you don't have to do anything because it is the node and it is not having any children.

95
00:05:20,340 --> 00:05:21,400
So the first thing.

96
00:05:21,620 --> 00:05:27,440
So whenever you are deleting anything from red BlackBerry and that in order are simply deleted, you

97
00:05:27,440 --> 00:05:28,610
don't have to do anything.

98
00:05:28,910 --> 00:05:29,510
Legacy.

99
00:05:29,930 --> 00:05:32,360
I want to delete 30 search for 30.

100
00:05:32,360 --> 00:05:34,850
Tadeus phone today is deleted.

101
00:05:35,690 --> 00:05:37,910
I want to delete seventy five.

102
00:05:37,910 --> 00:05:39,020
Search for seventy five.

103
00:05:39,020 --> 00:05:41,150
Seventy five found deleted.

104
00:05:41,150 --> 00:05:41,930
Don't do anything.

105
00:05:41,930 --> 00:05:44,510
You don't have to do any changes in the red BlackBerry.

106
00:05:44,540 --> 00:05:45,760
Just delete that note.

107
00:05:46,040 --> 00:05:47,060
Now one more example.

108
00:05:47,330 --> 00:05:55,490
I want to delete 70 who will take its place either in order predecessor or in order successor.

109
00:05:55,670 --> 00:05:58,190
Either 60 will take its place or 70 will.

110
00:05:58,190 --> 00:05:59,520
Seventy five will take its place.

111
00:05:59,520 --> 00:06:03,980
I suppose if you are sending seventy five there then you have to delete this node.

112
00:06:04,160 --> 00:06:06,170
So what is the color of this small right now.

113
00:06:06,410 --> 00:06:08,270
Delete it, simply delete it.

114
00:06:08,510 --> 00:06:11,440
Seventy five goes there and you don't have to do anything.

115
00:06:11,460 --> 00:06:13,670
Don't have to do any adjustment at all.

116
00:06:14,030 --> 00:06:19,730
OK, so if the note that you are deleting is rather than blindly delete that note.

117
00:06:20,240 --> 00:06:21,980
Now let us look at another example.

118
00:06:21,990 --> 00:06:22,510
Same thing.

119
00:06:23,750 --> 00:06:28,190
So I want to delete this node right node then simply delete it.

120
00:06:28,670 --> 00:06:29,420
If I had not.

121
00:06:29,630 --> 00:06:30,670
I have shown it here.

122
00:06:30,690 --> 00:06:32,150
Now I'm showing it on the site.

123
00:06:32,150 --> 00:06:32,510
Right.

124
00:06:32,810 --> 00:06:34,610
So delete it as this one.

125
00:06:35,090 --> 00:06:35,810
Now, next one.

126
00:06:35,990 --> 00:06:37,310
I want to delete the node.

127
00:06:37,310 --> 00:06:42,980
So although I have to delete this note and it is having one child, it is having just one child and

128
00:06:42,980 --> 00:06:48,330
that child definitely black because red color mode will not have red color child, it will have black.

129
00:06:48,920 --> 00:06:50,450
So this is black.

130
00:06:51,080 --> 00:06:57,510
So if this note is deleted, red color is going away, then simply bring this node here and it becomes

131
00:06:57,510 --> 00:06:58,220
a black node.

132
00:06:58,700 --> 00:06:59,570
This node is gone.

133
00:06:59,810 --> 00:07:00,620
This node is gone.

134
00:07:00,890 --> 00:07:03,590
See, remember, this was having only one left child.

135
00:07:03,830 --> 00:07:05,960
Rachel was not the only one child was dead.

136
00:07:06,350 --> 00:07:12,650
So if you're deleting in the world that is a red node, then it will simply delete and its child will

137
00:07:12,650 --> 00:07:14,600
take its place if there is any child.

138
00:07:14,840 --> 00:07:16,760
And definitely that child will be black child.

139
00:07:17,120 --> 00:07:23,060
So from these three examples, we can understand that if there is a red node, then search it for it

140
00:07:23,270 --> 00:07:25,130
and it is red, then delete it.

141
00:07:25,250 --> 00:07:26,270
There are no issues.

142
00:07:26,810 --> 00:07:27,920
Then there is the issue.

143
00:07:28,160 --> 00:07:30,590
If the notice black, there is a problem.

144
00:07:31,900 --> 00:07:37,910
Now, you are coming to the actual complex part of red BlackBerry, let us make it simple and understand

145
00:07:37,910 --> 00:07:38,080
it.

146
00:07:38,260 --> 00:07:38,880
Let us see.

147
00:07:39,160 --> 00:07:40,780
I have another example here.

148
00:07:41,610 --> 00:07:44,190
So for deletion, we consider some notes.

149
00:07:44,200 --> 00:07:44,860
Let us see.

150
00:07:45,550 --> 00:07:47,980
This is the law that is getting deleted.

151
00:07:47,990 --> 00:07:51,380
So I wrote it as a D and it is having a child.

152
00:07:52,110 --> 00:07:55,000
OK, this is the child and this is the parent.

153
00:07:55,270 --> 00:08:00,670
And this is sibling of a node that is getting deleted, sibling of a node that is getting deleted.

154
00:08:01,420 --> 00:08:01,810
No.

155
00:08:02,200 --> 00:08:04,520
One, the note that you are deleting is black.

156
00:08:04,540 --> 00:08:06,930
There is a complexity and it's not that complex.

157
00:08:06,940 --> 00:08:07,820
Let us understand.

158
00:08:08,200 --> 00:08:08,580
Simple.

159
00:08:08,590 --> 00:08:11,860
They want to delete the all that is black, right.

160
00:08:12,310 --> 00:08:21,100
Then check the sibling sibling effect, then simply delete the snored, delete the node and perform

161
00:08:21,190 --> 00:08:23,490
probation, perform rotation.

162
00:08:23,800 --> 00:08:35,260
So here comes S and here comes B and this child is present so that we can black and two children can

163
00:08:35,260 --> 00:08:36,909
write perform rotation.

164
00:08:37,179 --> 00:08:38,280
That's simple.

165
00:08:39,460 --> 00:08:41,020
So what was the situation here.

166
00:08:41,409 --> 00:08:43,539
The note that we are deleting is black.

167
00:08:43,780 --> 00:08:44,130
Right.

168
00:08:44,380 --> 00:08:50,780
And if the child will take its place then after that its sibling, its a sibling, a parent to perform

169
00:08:50,800 --> 00:08:51,340
rotation.

170
00:08:51,670 --> 00:08:52,150
Simple.

171
00:08:53,280 --> 00:08:58,970
So we should audition, we perform, we have perform a zigzag rotation that is our auscultation.

172
00:08:59,220 --> 00:09:04,050
If it was, this side will perform a little rotation, OK, this is something that is simple.

173
00:09:04,420 --> 00:09:07,950
And now comes the complex Kitsis complex part.

174
00:09:08,100 --> 00:09:11,340
Soudas, the note we are is black, right.

175
00:09:11,340 --> 00:09:16,290
And it is having some child, black child that is not on its sibling.

176
00:09:16,680 --> 00:09:17,730
S black.

177
00:09:18,510 --> 00:09:20,130
See if it was well performed rotation.

178
00:09:20,210 --> 00:09:20,820
No problem.

179
00:09:21,240 --> 00:09:24,170
If it is black, then we have multiple charges here.

180
00:09:24,540 --> 00:09:26,580
What are we have to charge this.

181
00:09:26,940 --> 00:09:27,630
What is that?

182
00:09:28,050 --> 00:09:32,040
If both the children are black, then simply change the color.

183
00:09:32,190 --> 00:09:34,510
So hope to change the color sibling.

184
00:09:34,510 --> 00:09:43,140
You make it isolated and let these children be black only and the make the parent as black, white and

185
00:09:43,140 --> 00:09:44,180
its deleted lord.

186
00:09:44,310 --> 00:09:47,470
This has come here that no one has gone its child.

187
00:09:47,470 --> 00:09:48,750
That is, none has come here.

188
00:09:49,710 --> 00:09:55,830
This whole chain becomes siblings black and its children are also black.

189
00:09:55,830 --> 00:10:00,780
So do re coloring change a sibling to read and parent as black.

190
00:10:01,110 --> 00:10:03,110
No parent was already read.

191
00:10:03,360 --> 00:10:05,160
It became black so there is no issue.

192
00:10:05,430 --> 00:10:08,070
Otherwise this will continue in the pattern.

193
00:10:08,080 --> 00:10:10,870
So that coloring part, the rotation part will continue.

194
00:10:10,870 --> 00:10:12,870
We don't have to go to that much complexity.

195
00:10:13,260 --> 00:10:14,040
So that's it.

196
00:10:14,490 --> 00:10:16,790
Not one more possibility in this one.

197
00:10:17,190 --> 00:10:20,850
See the note that we are deleting as this one and it is having none.

198
00:10:20,850 --> 00:10:21,980
Nothing is there on the site.

199
00:10:21,990 --> 00:10:27,090
OK, this note we are deleting and it is black and the dissembling is also black.

200
00:10:27,210 --> 00:10:27,660
Yes.

201
00:10:28,020 --> 00:10:34,410
Then what are these children, if it is having one child or two children, whatever it is having, if

202
00:10:34,410 --> 00:10:40,110
they are aware that they are not black, then they can perform repetition again, perform rotation.

203
00:10:40,350 --> 00:10:42,810
So I have taken read the child.

204
00:10:42,810 --> 00:10:44,330
So which rotation?

205
00:10:44,370 --> 00:10:46,770
We have to perform zigzaggy rotation.

206
00:10:46,770 --> 00:10:53,580
We have to perform that is this ready goes up and the parent comes here that is dear and sibling comes

207
00:10:53,580 --> 00:10:58,590
here that is black for the parent and sibling are black and that red goes down.

208
00:10:59,130 --> 00:11:03,000
So this whole you have to perform the rotation, this exact rotation.

209
00:11:03,240 --> 00:11:05,910
If it is having right child then Zick rotation.

210
00:11:05,910 --> 00:11:13,080
If both the children you have the option, so better perform Zagazig rotation that is out of probation.

211
00:11:13,960 --> 00:11:15,480
Which obligation is performed?

212
00:11:15,480 --> 00:11:17,510
It depends on the child development.

213
00:11:17,550 --> 00:11:17,790
Right.

214
00:11:18,030 --> 00:11:21,420
If for this, Maldive left child still performs exactly right.

215
00:11:21,430 --> 00:11:24,300
Challenge the if both are there done also.

216
00:11:24,690 --> 00:11:27,180
So again, if you break them, these are cases again.

217
00:11:27,360 --> 00:11:28,170
These are cases.

218
00:11:28,470 --> 00:11:29,580
So I'm not breaking them.

219
00:11:30,510 --> 00:11:31,650
Not one more thing.

220
00:11:31,980 --> 00:11:34,310
There is something called blackness.

221
00:11:34,320 --> 00:11:35,460
Double blackness.

222
00:11:35,490 --> 00:11:36,240
What is that?

223
00:11:36,240 --> 00:11:37,590
I did not explain anything.

224
00:11:38,130 --> 00:11:39,630
I, I will come to it.

225
00:11:39,930 --> 00:11:43,830
No, let us repeat the topic again and see all the things quickly.

226
00:11:44,520 --> 00:11:49,200
Let us see if I am deleting a node that is red alert node then deleted.

227
00:11:49,200 --> 00:11:50,480
Simply don't do anything.

228
00:11:50,970 --> 00:11:54,150
If I am deleting a note that is red color Nordon deleted.

229
00:11:54,150 --> 00:11:58,770
Simply don't do anything if I'm deleting and all that is red color node.

230
00:11:58,950 --> 00:12:02,070
And it was having a black child then also deleted.

231
00:12:02,070 --> 00:12:05,310
And if black children take its place there was no problem.

232
00:12:05,610 --> 00:12:11,940
Why there was no problem because in Red Black Tree the number of black nodes along any pod should be

233
00:12:11,940 --> 00:12:12,420
the same.

234
00:12:12,930 --> 00:12:17,460
So if you are deleting it red color mode, it's not going to affect a very black tree at all.

235
00:12:17,580 --> 00:12:20,070
That property is not affected at all.

236
00:12:20,430 --> 00:12:23,790
So that is the reason if the notice had been simply deleted.

237
00:12:24,390 --> 00:12:29,760
So we are bothered about the number of black nodes along every part that is black site.

238
00:12:29,760 --> 00:12:30,930
We are bothered about that.

239
00:12:31,020 --> 00:12:34,260
And the problem is when the node is black, so that's all.

240
00:12:34,500 --> 00:12:39,330
If the node that you are deleting is black and then there are other situations, what other situation?

241
00:12:39,960 --> 00:12:46,740
If a node is black, then if this is deleted, then this is all is black.

242
00:12:47,040 --> 00:12:48,720
So no comes here.

243
00:12:48,750 --> 00:12:49,110
Right.

244
00:12:49,380 --> 00:12:52,770
And that the node that was deleted, it was black.

245
00:12:52,770 --> 00:12:55,950
So black hiders of in this direction for runtime.

246
00:12:56,220 --> 00:12:59,130
So we make this nodes say double black.

247
00:12:59,250 --> 00:13:05,400
It is having more blackness that was null, that is its own blackness and the node that is deleted,

248
00:13:05,400 --> 00:13:06,540
that blackness also.

249
00:13:06,540 --> 00:13:08,760
So we have to adjust that blackness.

250
00:13:09,090 --> 00:13:10,770
So we have to adjust black height.

251
00:13:10,800 --> 00:13:12,030
So that is a problem here.

252
00:13:12,030 --> 00:13:15,310
If the note that you are deleting is black, then it will become double black.

253
00:13:15,690 --> 00:13:19,980
Each child will become double black, then what are the possibilities and how to handle.

254
00:13:20,370 --> 00:13:28,740
So the Black Sea, that sibling, if it is already performed, rotation next allegorically jump here.

255
00:13:29,040 --> 00:13:31,920
If the note is deleted, then this was none.

256
00:13:32,160 --> 00:13:37,070
So it will be not only definitely because the moons are lymph nodes, the notes that are deleted are

257
00:13:37,080 --> 00:13:37,650
left notes.

258
00:13:37,660 --> 00:13:42,090
Remember, this sibling is definitely of black rotation.

259
00:13:42,540 --> 00:13:46,350
Siblings, black children over rotation.

260
00:13:47,810 --> 00:13:54,740
Then on another case, siblings, black children are also black, then three color, so simple, let

261
00:13:54,740 --> 00:13:56,300
us do it more simple once again.

262
00:13:56,480 --> 00:14:02,530
If the Lord is ready, deleted is deleted nor destroyed, and the child is black, definitely black,

263
00:14:02,540 --> 00:14:05,090
it will take its place so it is deleted.

264
00:14:05,540 --> 00:14:08,330
If the Lord is black, then it will become double black.

265
00:14:08,450 --> 00:14:09,470
Then see the sibling.

266
00:14:09,890 --> 00:14:11,450
If it is, read it.

267
00:14:11,810 --> 00:14:14,510
If it is black and children not read it.

268
00:14:15,800 --> 00:14:16,730
If it is black.

269
00:14:16,730 --> 00:14:19,000
Children are also black recolour.

270
00:14:19,700 --> 00:14:20,840
So this is the procedure.

271
00:14:20,850 --> 00:14:23,660
So if you want to break, you can make it in many cases.

272
00:14:23,660 --> 00:14:23,950
Right.

273
00:14:24,230 --> 00:14:29,090
This case, case court, case fourth, case fifth and again, this is sixth and seventh.

274
00:14:29,210 --> 00:14:30,560
You can have many cases.

275
00:14:31,160 --> 00:14:36,740
So I have not shown them in the form of a case, as I said, that the note that you are leading is red

276
00:14:36,740 --> 00:14:37,340
or black.

277
00:14:37,550 --> 00:14:38,090
That's hot.

278
00:14:38,810 --> 00:14:44,930
So these are the these are notes can be adjusted after leading from a regulatory novel.

279
00:14:44,930 --> 00:14:48,950
Take an example of a BlackBerry and delete a few notes from it.

280
00:14:48,950 --> 00:14:49,730
In the next video.

281
00:14:49,730 --> 00:14:50,600
You can watch it.

282
00:14:50,900 --> 00:14:53,660
So let us take out a BlackBerry and delete some notes from.

