1
00:00:00,970 --> 00:00:07,180
In the previous whiteboard lecture, we have learned about functions, so let us look at the demonstration

2
00:00:07,180 --> 00:00:08,060
for the function.

3
00:00:08,650 --> 00:00:16,000
So here in Google, I will search for online GDP, so I will select C++.

4
00:00:16,000 --> 00:00:23,290
C++ program is really the first of all, I will write on a function for adding two numbers and I will

5
00:00:23,290 --> 00:00:26,020
explain a few things using that function.

6
00:00:26,890 --> 00:00:34,150
So here about the main function, I will write one function, add function name is ad hominem, which

7
00:00:34,150 --> 00:00:39,340
is going to add to numbers numbers, all of which type integer type or float type.

8
00:00:39,610 --> 00:00:44,890
I want integer type integer A an integer B two parameters that is sticking.

9
00:00:45,280 --> 00:00:51,230
Then after adding what it should do, it should return the result and the result is of type integer.

10
00:00:51,550 --> 00:00:54,540
So this is the return type does the name of a function.

11
00:00:54,970 --> 00:00:59,020
So this is the name of function and these are the parameters.

12
00:00:59,710 --> 00:01:00,130
Right.

13
00:01:00,610 --> 00:01:06,610
Then inside this, what it should do for adding it should take one more temporary variable, then C

14
00:01:06,610 --> 00:01:07,270
assign.

15
00:01:08,210 --> 00:01:12,360
A plus B then returned the result.

16
00:01:12,560 --> 00:01:19,520
See, this is a function of the function, I have to let us go to main function and here inside main

17
00:01:19,520 --> 00:01:27,160
function, I will declare two variables integer number one, and in this value is ten.

18
00:01:27,560 --> 00:01:31,070
And also number two, and this value is 15.

19
00:01:31,400 --> 00:01:34,870
And also I will declare one more variable and that is some.

20
00:01:35,510 --> 00:01:39,680
Now, I'll explain these things inside the main function.

21
00:01:39,680 --> 00:01:40,910
I have three variables.

22
00:01:40,910 --> 00:01:43,700
Number one, number two and some number one.

23
00:01:43,700 --> 00:01:45,350
And number two are having values.

24
00:01:45,590 --> 00:01:47,450
And I want that result certain sum.

25
00:01:47,960 --> 00:01:51,880
So for adding I will call a function add.

26
00:01:52,460 --> 00:01:54,230
OK, add this function.

27
00:01:54,230 --> 00:01:54,740
I'm calling.

28
00:01:55,070 --> 00:02:00,650
And whenever we call this function, we should pass two parameters because it is sticking to parameters.

29
00:02:00,980 --> 00:02:07,760
So I will pass number one, this value of number that is ten will be copied in a pick.

30
00:02:08,210 --> 00:02:12,380
Then number two, the value of number two will be copied.

31
00:02:12,380 --> 00:02:14,880
And B, so then we call this function.

32
00:02:14,900 --> 00:02:16,460
This is called function calling.

33
00:02:16,730 --> 00:02:19,540
When I call this function, this will execute.

34
00:02:19,910 --> 00:02:24,770
So it will take the value of number one, we will take the value of number two, then it will perform

35
00:02:24,770 --> 00:02:27,580
all these things and it will return the result.

36
00:02:28,130 --> 00:02:29,950
Nor do I need the result.

37
00:02:30,290 --> 00:02:32,480
Yes, I need it where I need it.

38
00:02:32,540 --> 00:02:34,100
I need it in some variable.

39
00:02:34,310 --> 00:02:38,490
So I should try it on some before the function call and then assign.

40
00:02:39,020 --> 00:02:43,850
So what happens here is the result of this function will be copied in sum.

41
00:02:44,210 --> 00:02:50,000
So the return value of dysfunction that is C will be copied and some are right.

42
00:02:50,480 --> 00:02:51,290
And one more thing.

43
00:02:51,590 --> 00:02:53,630
This function is returning integer type.

44
00:02:53,850 --> 00:03:00,080
Then you must write a written statement here, you must write it down and you should return in digital

45
00:03:00,080 --> 00:03:00,560
value.

46
00:03:01,130 --> 00:03:04,360
If you don't want a function, return anything, then make it void.

47
00:03:05,000 --> 00:03:11,090
But here we want a function to return something so int and we should return the value so that will be

48
00:03:11,090 --> 00:03:11,450
copied.

49
00:03:11,450 --> 00:03:13,850
In sum, that's all in the statement.

50
00:03:13,850 --> 00:03:15,580
I will close this one now.

51
00:03:15,640 --> 00:03:17,090
I will explain a few more things.

52
00:03:17,390 --> 00:03:24,680
See, this is a function call and these parameters, No one, no actual parameters because from here

53
00:03:24,680 --> 00:03:32,750
we are calling a function and parsing these two values and this is a function definition, this is function

54
00:03:32,750 --> 00:03:35,920
definition and it is taking two parameters.

55
00:03:35,930 --> 00:03:43,200
These are called formal parameters means these are temporary for the function when the function is called,

56
00:03:43,200 --> 00:03:45,830
these are used and the function and these are destroyed.

57
00:03:46,340 --> 00:03:50,990
So this no one will be copied in a and no will be copied.

58
00:03:50,990 --> 00:03:52,790
It'll be aligned.

59
00:03:53,210 --> 00:03:59,810
So the values of actual parameters are copied in formal parameters and the function returns the result

60
00:04:00,020 --> 00:04:05,960
that will be copied in the sum if I don't write some assign, then it will return the value.

61
00:04:05,960 --> 00:04:07,880
But I am not taking in any variable.

62
00:04:08,120 --> 00:04:11,200
If I don't have this, I'm not taking any variable.

63
00:04:11,660 --> 00:04:13,160
OK, that's it.

64
00:04:13,460 --> 00:04:16,459
Now I will display some code.

65
00:04:17,060 --> 00:04:18,230
I will give a message.

66
00:04:18,620 --> 00:04:24,980
Some s then some literature on the program take out the result.

67
00:04:25,130 --> 00:04:26,810
Some is twenty five.

68
00:04:27,290 --> 00:04:31,190
Right lt fifteen added we got the result.

69
00:04:31,190 --> 00:04:31,850
Twenty five.

70
00:04:32,180 --> 00:04:34,370
So who has added these two numbers.

71
00:04:34,760 --> 00:04:42,500
This add a function add function C the question arises some here that is it not easy to just write here.

72
00:04:42,740 --> 00:04:46,640
Some assign number one plus number two.

73
00:04:47,590 --> 00:04:50,830
Yes, I can and I don't need this some function.

74
00:04:51,880 --> 00:04:58,900
Then why we're writing this, see, are reducing the workload from the main function and defining separate

75
00:04:58,900 --> 00:05:03,200
functions so that they do the job or they help women function.

76
00:05:03,910 --> 00:05:04,150
Right.

77
00:05:04,540 --> 00:05:07,600
So for adding numbers, this function is helping main function.

78
00:05:07,990 --> 00:05:11,380
Otherwise you can write everything inside, main function also.

79
00:05:11,800 --> 00:05:12,650
So that's it.

80
00:05:12,760 --> 00:05:13,790
That's all in this video.

81
00:05:14,260 --> 00:05:20,950
See, the function that I have written this for are same for C language as well as C++.

82
00:05:21,850 --> 00:05:28,360
Right, and here there is no difference just in of code, you can use pretty much everything the same.

83
00:05:29,170 --> 00:05:34,930
Later, it will become a CBC program, so practice this thing by yourself and the next lecture we will

84
00:05:34,930 --> 00:05:36,640
see barometer passing methods.

