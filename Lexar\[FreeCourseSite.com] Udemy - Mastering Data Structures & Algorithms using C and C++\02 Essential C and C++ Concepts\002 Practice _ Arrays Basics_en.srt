1
00:00:00,640 --> 00:00:06,010
In the previous whiteboard lecture I have explained you about <PERSON>, let us have the demonstration

2
00:00:06,010 --> 00:00:07,560
of ideas in this video.

3
00:00:08,200 --> 00:00:15,160
See, the idea I'm using here is Xcode on MacBook, that is maquilas.

4
00:00:15,760 --> 00:00:22,450
If you want to practice these demonstrations on your machine, then you have to install C or C++ compiler

5
00:00:22,450 --> 00:00:23,340
on your machine.

6
00:00:23,650 --> 00:00:30,730
So how to set up C C++ compiler and how to install an ID is explain in the next section.

7
00:00:31,120 --> 00:00:37,480
I assume that you already have C or C++ installed on your machine and you are learning this course and

8
00:00:37,480 --> 00:00:41,690
if you have not done it so you can go to the next section and install IDs.

9
00:00:41,980 --> 00:00:45,850
And one more thing even you can practice online on an online compiler.

10
00:00:45,850 --> 00:00:47,500
I will show you where to practice.

11
00:00:47,710 --> 00:00:55,180
Open any browser, Chrome or Safari or Mozilla, Firefox, any browser inside the browser.

12
00:00:55,480 --> 00:00:58,350
Just search for online gdb.

13
00:00:59,410 --> 00:01:06,040
This is online GDP and you can select the compiler, either C compiler or C++ compiler, Java, whichever

14
00:01:06,040 --> 00:01:10,470
compiler you want you can select here can practice for almost all the languages.

15
00:01:10,750 --> 00:01:15,790
So here I will select C++ compiler because our courses in C and C++.

16
00:01:16,240 --> 00:01:18,640
So you can see that ready-Made function is given here.

17
00:01:19,280 --> 00:01:26,590
Right, main function is given with datastream and using namespace is also included here, already written

18
00:01:26,590 --> 00:01:27,190
paradiddle.

19
00:01:27,640 --> 00:01:33,820
So you can select C++ and you can write on C as well as C++ programs, but the programs again write.

20
00:01:34,270 --> 00:01:36,750
So I suggest you you can practice here online.

21
00:01:36,770 --> 00:01:40,000
You can start practicing here right in the next section.

22
00:01:40,000 --> 00:01:46,450
I am showing how to install Idiz and if you like any idea you can start working there or even you can

23
00:01:46,450 --> 00:01:47,170
practice here.

24
00:01:48,100 --> 00:01:49,600
So let us go back to dite.

25
00:01:50,710 --> 00:01:56,350
Now, the next very important thing I should tell you see some student loans, C++, they don't know

26
00:01:56,350 --> 00:02:03,370
C language C, I should tell you that if you know C++, you already know C, language C, I have adopted

27
00:02:03,370 --> 00:02:06,750
both C and C++ style of programming in my course.

28
00:02:07,660 --> 00:02:12,290
So the C style program you can write on in C++ or C++ program also.

29
00:02:12,880 --> 00:02:15,160
Don't worry about it, you can easily write it.

30
00:02:15,160 --> 00:02:21,710
And I'm also explaining the basic syntax that are required for practicing in this course.

31
00:02:22,450 --> 00:02:26,390
So let us start and see the demonstration for array and structure.

32
00:02:26,930 --> 00:02:31,900
See, I have already taken a C++ project in my Xcode Idy.

33
00:02:32,170 --> 00:02:33,760
I have taken C++ project.

34
00:02:33,760 --> 00:02:41,200
You can see that has included your stream using namespace bastardy methods that now same thing.

35
00:02:41,260 --> 00:02:44,400
If you have a C language project also you can do the same thing.

36
00:02:44,410 --> 00:02:45,890
What I am going to show you now.

37
00:02:46,120 --> 00:02:47,860
So let us create an array.

38
00:02:47,950 --> 00:02:50,830
See this array of size of five.

39
00:02:51,150 --> 00:02:52,300
The array declaration.

40
00:02:52,750 --> 00:02:59,590
It will create an array of size five to five integers and each integer can be accessed using its index

41
00:02:59,980 --> 00:03:01,290
like aof zero.

42
00:03:01,300 --> 00:03:04,630
I can store some value that is twelve and AF1.

43
00:03:04,630 --> 00:03:11,160
I can store the value, let us say 15 and of two I can store some value.

44
00:03:11,170 --> 00:03:12,900
Let us take it as 25.

45
00:03:13,330 --> 00:03:17,800
So this all weekend toward the elements at each index in an edit.

46
00:03:17,830 --> 00:03:22,810
So total in the sense that I will be getting at zero to four cities are five integers.

47
00:03:22,810 --> 00:03:24,700
So how much memory this array will take.

48
00:03:24,940 --> 00:03:30,610
It depends on the size of integer C Lidice compilers take for whites for integer.

49
00:03:31,030 --> 00:03:34,960
Seeing my whiteboard sessions, I'm assuming integer taking two bytes.

50
00:03:35,230 --> 00:03:42,550
Right for making my explanation easy, but in real integer takes four bytes.

51
00:03:42,940 --> 00:03:45,970
So let us see the size of this one so I can see it.

52
00:03:45,970 --> 00:03:50,860
See out size of a let us on the program.

53
00:03:52,290 --> 00:03:55,400
I should get the say for and go five 20.

54
00:03:55,440 --> 00:03:58,830
Yes, say the serenity side of a restaurant.

55
00:03:59,280 --> 00:04:00,820
Yes, it is spending 20.

56
00:04:01,230 --> 00:04:03,330
Now, let us bring some element.

57
00:04:03,330 --> 00:04:04,990
See out your fun.

58
00:04:05,010 --> 00:04:05,970
So what is the value?

59
00:04:06,980 --> 00:04:08,880
He often is 15 and it.

60
00:04:11,060 --> 00:04:17,300
Yes, I got 12, 15, but there should be some space between them, so let us use Andell here.

61
00:04:17,300 --> 00:04:23,210
That is for New Line Softer Building 20, Garcelle will move to the next line and 15 will be printed

62
00:04:23,210 --> 00:04:24,020
in the next line.

63
00:04:24,650 --> 00:04:29,600
Yes, 20 and then 15 light and I will give a here also.

64
00:04:29,810 --> 00:04:30,530
So that.

65
00:04:31,600 --> 00:04:37,750
This last message, this is coming from Xcode, it will appear in the next line.

66
00:04:38,980 --> 00:04:42,810
Yes, 20 and 15 and then program under the zip code zero.

67
00:04:43,150 --> 00:04:43,760
That's it.

68
00:04:44,260 --> 00:04:46,230
Now, can I use printf here?

69
00:04:46,900 --> 00:04:48,650
I want to print it off for two.

70
00:04:48,670 --> 00:04:53,950
Yes, I can use printer also print deaf person Tildy and also slash and.

71
00:04:55,310 --> 00:04:58,000
Right, he have to run the program.

72
00:05:00,100 --> 00:05:01,270
Yes, it is working.

73
00:05:01,430 --> 00:05:02,460
They're spending 35.

74
00:05:03,270 --> 00:05:07,210
It may not work in your it in my deeds, not giving any error.

75
00:05:07,420 --> 00:05:15,850
If it is giving any error, then you can include study dot edge seen side of C++ program.

76
00:05:16,180 --> 00:05:18,130
I'm writing C language code.

77
00:05:18,700 --> 00:05:19,060
Right.

78
00:05:19,300 --> 00:05:24,220
Socialist's for C++ and preface for C language.

79
00:05:24,520 --> 00:05:24,940
Right.

80
00:05:25,090 --> 00:05:27,760
So you can include extra research and write.

81
00:05:27,760 --> 00:05:29,750
C++ makes the code also right.

82
00:05:30,250 --> 00:05:32,830
See all these things are Sieminski and C++.

83
00:05:32,830 --> 00:05:35,400
Only the difference is coming in C out and Bendorf.

84
00:05:35,530 --> 00:05:35,970
Right.

85
00:05:36,460 --> 00:05:37,710
So it's not a big difference.

86
00:05:38,050 --> 00:05:39,190
So so far.

87
00:05:39,610 --> 00:05:40,580
Now let us learn.

88
00:05:40,600 --> 00:05:43,340
Next thing, can I initialize an array.

89
00:05:43,420 --> 00:05:43,870
Yes.

90
00:05:43,870 --> 00:05:50,650
You can initialize NATO by giving some elements directly like I will give the values like are two,

91
00:05:50,650 --> 00:05:52,870
four, six, eight and 10.

92
00:05:52,900 --> 00:05:53,800
These are the values.

93
00:05:54,250 --> 00:06:01,690
If I run here, I should get four and here I should get six, you know, sizes twenty four and this

94
00:06:01,690 --> 00:06:02,230
is six.

95
00:06:02,800 --> 00:06:04,210
The wanting one.

96
00:06:04,210 --> 00:06:05,480
I am initializing an array.

97
00:06:05,530 --> 00:06:06,870
I can skip the size.

98
00:06:06,880 --> 00:06:10,930
So what will be the size of the three as many elements you are mentioning here.

99
00:06:10,930 --> 00:06:12,220
That will be the size of Fanelli.

100
00:06:12,580 --> 00:06:14,740
So the size of an array will be still five.

101
00:06:15,280 --> 00:06:16,960
Yes, five four twenty.

102
00:06:17,440 --> 00:06:20,340
I will include a few more elements here, 12 and 14.

103
00:06:20,350 --> 00:06:22,570
So now the size of the array is a seven.

104
00:06:22,960 --> 00:06:26,710
So the size should be seven for 28.

105
00:06:26,920 --> 00:06:29,720
Yes, sizes seven for 28.

106
00:06:29,720 --> 00:06:31,270
So I'm getting the size of 28.

107
00:06:31,270 --> 00:06:37,550
So it means the array sizes seven because I have initialized with seven elements.

108
00:06:38,020 --> 00:06:39,730
Now let us look at next thing.

109
00:06:40,240 --> 00:06:43,030
I will get the size, I'll give the size ten.

110
00:06:43,480 --> 00:06:47,020
But there are only seven elements 026.

111
00:06:47,020 --> 00:06:49,710
I have the elements at the Seventh Andexanet Index.

112
00:06:49,720 --> 00:06:52,540
There is no element what will be present there.

113
00:06:52,810 --> 00:06:59,280
So I will try to pin the element from index eight and at index nine, let us see what it will print.

114
00:06:59,560 --> 00:07:00,700
See the size esten.

115
00:07:00,700 --> 00:07:02,640
So then this will be 029.

116
00:07:02,650 --> 00:07:02,890
Right.

117
00:07:03,220 --> 00:07:04,960
So I'm the last twenty six.

118
00:07:04,960 --> 00:07:07,030
Let us see what is there at that location.

119
00:07:08,210 --> 00:07:16,130
Novellas are zeroes, so it means, if you have initialise, a few values from Manetti, then the rest

120
00:07:16,130 --> 00:07:19,360
of the locations will be having different values zeroes.

121
00:07:19,790 --> 00:07:21,380
So I will try to remove this.

122
00:07:22,480 --> 00:07:24,370
So I have only two elements, right?

123
00:07:24,790 --> 00:07:31,440
I will try to bring the next element that is at the next to right and at the next three.

124
00:07:31,630 --> 00:07:33,950
So I have the elements that index 011.

125
00:07:34,390 --> 00:07:34,960
So what is it?

126
00:07:34,960 --> 00:07:35,910
There are two and three.

127
00:07:35,920 --> 00:07:37,680
Let us check it at zero.

128
00:07:38,170 --> 00:07:44,590
So it means if you have an idea of some size and initialize few elements, then the rest of the elements

129
00:07:44,590 --> 00:07:46,400
will by default become zero.

130
00:07:46,810 --> 00:07:48,460
So can I just zero here?

131
00:07:48,820 --> 00:07:50,820
What will happen if I just zero.

132
00:07:51,370 --> 00:07:56,120
The only one element is initialized that is zero and rest of all the elements will be zero.

133
00:07:56,290 --> 00:07:59,970
So the entire area of size 10 will be initialized with zero.

134
00:08:00,730 --> 00:08:05,990
So this is all you can initialize the entirety with to zero if you want all the elements to be zero.

135
00:08:06,370 --> 00:08:11,470
Now, the next thing I have shown you, Hovick and displaying array for the slingin that we can use

136
00:08:11,680 --> 00:08:19,290
for loop for integer I assign zero is less than we know the size of ten a plus plus.

137
00:08:19,660 --> 00:08:21,840
Right, so we can print all the elements.

138
00:08:21,850 --> 00:08:28,240
That is, if I am also I will give endl so I will remove the rest of the printing.

139
00:08:29,570 --> 00:08:32,610
So follow up used for accessing all the elements in an array.

140
00:08:32,900 --> 00:08:35,360
So here I'm just printing all the elements.

141
00:08:36,020 --> 00:08:39,159
So all the elements are 016, elements are Zeitels.

142
00:08:39,740 --> 00:08:45,770
I will try to initialize the elements with some values two, four, six, eight, 10 and 12.

143
00:08:46,130 --> 00:08:48,740
And the rest of the elements will be definitely zeroes.

144
00:08:49,010 --> 00:08:55,130
So you can see all the elements two, four, six, eight, 10, 12 and rest of the elements and zeros.

145
00:08:55,550 --> 00:08:57,410
See, I mentioned the size Ostende.

146
00:08:57,410 --> 00:08:59,720
So inside the fold up again directly use 10.

147
00:08:59,990 --> 00:09:00,340
Right.

148
00:09:00,650 --> 00:09:02,440
So you have to give some constant value.

149
00:09:02,450 --> 00:09:08,390
It's a better idea to give a constant value added the size of Analeigh now next.

150
00:09:08,810 --> 00:09:12,190
Is there any other method for accessing all these elements in an array?

151
00:09:12,320 --> 00:09:15,480
Yes, in C++ that is for each loop.

152
00:09:15,500 --> 00:09:16,610
So let us use that.

153
00:09:16,610 --> 00:09:17,310
How to write it.

154
00:09:17,720 --> 00:09:20,710
I can take some element X from a.

155
00:09:21,680 --> 00:09:24,880
So I am assuming that you already know C and C++.

156
00:09:24,890 --> 00:09:26,060
I'm just revising.

157
00:09:26,060 --> 00:09:27,700
Don't ask questions on this one.

158
00:09:28,010 --> 00:09:29,510
See what is foreach loop in all.

159
00:09:29,840 --> 00:09:31,880
So I'm assuming you should know about that.

160
00:09:31,880 --> 00:09:32,190
Right.

161
00:09:32,450 --> 00:09:36,040
So if you are new to it, I suggest you do a study about it.

162
00:09:36,440 --> 00:09:39,720
I'm just using a for each loop here index.

163
00:09:39,770 --> 00:09:41,150
So what will happen to X.

164
00:09:41,450 --> 00:09:46,060
X will take the value to then four, then six, then eight and all.

165
00:09:46,250 --> 00:09:50,170
So it will be taking all the elements from an array one by one, all elements.

166
00:09:50,170 --> 00:09:51,870
So how many elements are the then elements.

167
00:09:51,920 --> 00:09:57,530
It will take all 10 elements, I say to take the number of elements depending on the side of finally

168
00:09:58,100 --> 00:09:59,030
let us run it.

169
00:10:00,390 --> 00:10:01,400
Yes, sir.

170
00:10:01,470 --> 00:10:04,740
Two, four, six, eight, 10, 12, and the rest of the elements are zeroes.

171
00:10:04,920 --> 00:10:07,020
So, yes, you can also use the foreach loop.

172
00:10:07,470 --> 00:10:09,510
No more on ATI's.

173
00:10:09,810 --> 00:10:14,760
Let us take the size of Hannity as input as possible.

174
00:10:15,150 --> 00:10:18,230
Can create an array of our desired sites.

175
00:10:18,600 --> 00:10:19,270
Let us check.

176
00:10:19,680 --> 00:10:22,440
I want to take input from the user.

177
00:10:22,470 --> 00:10:24,270
I will write C++ syntax here.

178
00:10:24,810 --> 00:10:26,670
Enter sites.

179
00:10:27,180 --> 00:10:27,560
Right.

180
00:10:27,870 --> 00:10:33,930
So homicide of another you want seen and then is it possible to mention.

181
00:10:33,930 --> 00:10:41,040
And here, see what I want is I will take the input from the user that is size of a then I am reading

182
00:10:41,040 --> 00:10:47,780
the size then I, I'm creating an area of size and as a callout see I'm getting an error here.

183
00:10:48,090 --> 00:10:53,010
Actually this often is allowed and it is called a variable sized Hoddy.

184
00:10:53,430 --> 00:10:56,640
This variable size array is allowed in C language.

185
00:10:56,640 --> 00:11:00,060
Also this compiler of betsie language in all compilers.

186
00:11:00,060 --> 00:11:04,050
It was not available then in C++ also it is available.

187
00:11:04,260 --> 00:11:04,590
Right.

188
00:11:04,830 --> 00:11:07,530
It is used at a very real places in my class.

189
00:11:07,690 --> 00:11:10,020
I don't know what is the error I'm getting.

190
00:11:10,200 --> 00:11:16,470
It is saying that you cannot initialize this variable size array like this, so you cannot initialize

191
00:11:16,710 --> 00:11:17,970
the drawback of this one.

192
00:11:18,330 --> 00:11:20,670
You can create it, but you cannot initialize it.

193
00:11:20,880 --> 00:11:22,710
Then what will be the values in this one?

194
00:11:22,710 --> 00:11:24,290
It will have all Goderich values.

195
00:11:24,300 --> 00:11:25,710
They cannot initialize it.

196
00:11:26,040 --> 00:11:33,210
Then how I can assign the values, you can take them from keyboard or you have to use the NDIS and write

197
00:11:33,210 --> 00:11:35,190
on the values like this.

198
00:11:36,120 --> 00:11:41,800
Or write them or fill them using Leupp by taking input from the keyboard.

199
00:11:42,240 --> 00:11:47,070
So let us run Nancy into the size of Reciters seven.

200
00:11:47,250 --> 00:11:48,450
I'll give the five to seven.

201
00:11:48,750 --> 00:11:49,530
So let us see.

202
00:11:49,860 --> 00:11:51,690
First element is two that I have.

203
00:11:51,690 --> 00:11:52,530
Initialize it.

204
00:11:52,760 --> 00:11:53,910
That's what the elements are.

205
00:11:53,910 --> 00:11:55,560
Garbage so total.

206
00:11:55,560 --> 00:11:56,540
How many elements are there?

207
00:11:57,490 --> 00:12:02,770
One, two, three, four, five, six, seven.

208
00:12:03,850 --> 00:12:10,650
All right, so total seven elements, I have given the side a seven to the side of another seven, right.

209
00:12:10,870 --> 00:12:17,110
And here foreach Loop is working on this variable size and in some compilers it may not work.

210
00:12:17,560 --> 00:12:18,000
Right.

211
00:12:18,160 --> 00:12:20,770
So just make sure your compiler is supporting or not.

212
00:12:21,100 --> 00:12:25,540
And in some compiler, if you try to initialize it, will not give you error, but it will not work

213
00:12:25,540 --> 00:12:26,110
properly.

214
00:12:26,530 --> 00:12:31,900
Here in my compiler, it is giving an error at asking, saying that you cannot cannot initialize it.

215
00:12:32,260 --> 00:12:36,190
But some compilers don't give any error, but they don't work properly.

216
00:12:36,190 --> 00:12:38,030
They will not initialize it either.

217
00:12:38,680 --> 00:12:42,300
So remember that variable Sirees cannot be initialized.

218
00:12:42,340 --> 00:12:42,840
All right.

219
00:12:43,030 --> 00:12:46,620
So at most of the places in my course, I need initialized arrays.

220
00:12:46,900 --> 00:12:51,280
So that is the reason I have not preferred using variable size arrays.

221
00:12:51,580 --> 00:12:52,440
Now, one more thing.

222
00:12:52,450 --> 00:12:56,800
Some students may get confused here, not one of the values initialized with zero.

223
00:12:56,980 --> 00:13:02,920
I'm seeing that all our garbage then one of the value zero hard is Spottswood C zero is also garbage.

224
00:13:03,340 --> 00:13:05,110
We did not keep disvalue.

225
00:13:05,320 --> 00:13:07,240
I have kept only one value to.

226
00:13:07,450 --> 00:13:12,520
So all of the values, whatever it may be, it is a garbage garbage bins.

227
00:13:12,520 --> 00:13:13,750
It doesn't belong to you.

228
00:13:14,140 --> 00:13:16,590
You have not assigned it right.

229
00:13:16,780 --> 00:13:18,460
So all these values are garbage.

230
00:13:18,800 --> 00:13:19,600
These are values.

231
00:13:19,600 --> 00:13:20,920
Garbage are also values.

232
00:13:20,920 --> 00:13:22,860
But that is not your value.

233
00:13:23,230 --> 00:13:23,750
That's it.

234
00:13:24,120 --> 00:13:24,610
All right.

235
00:13:24,790 --> 00:13:25,740
So that's it.

236
00:13:25,980 --> 00:13:27,300
The demonstration of an array.

237
00:13:27,490 --> 00:13:33,520
So I suggest you just practice this and try out all these things on your machine once so that it becomes

238
00:13:33,520 --> 00:13:36,970
a revision and it will be helpful in further topics.

