1
00:00:00,420 --> 00:00:07,870
Now, the next challenge is finding duplicate elements in an area that is duplicate elements in a list.

2
00:00:08,730 --> 00:00:16,440
So in this, we will be looking at finding duplicates and sorted out it and also how to count those

3
00:00:16,440 --> 00:00:17,490
duplicate elements.

4
00:00:18,480 --> 00:00:20,880
So if you look at the example, what I have taken here.

5
00:00:21,980 --> 00:00:31,070
Three, six, eight eight is two times it is duplicated and 10, 12, 15, 15, 15, 15 is duplicated

6
00:00:31,070 --> 00:00:36,290
and that is three times so fast, I will show you how to find the duplicates then.

7
00:00:36,290 --> 00:00:38,270
Also, I will show you how to count them.

8
00:00:39,080 --> 00:00:44,450
So let us develop some procedures for finding duplicates and counting duplicates.

9
00:00:45,380 --> 00:00:50,750
Let us develop a procedure for finding duplicate elements in a list.

10
00:00:51,620 --> 00:00:54,920
So for finding duplicates, I should scan through this list.

11
00:00:55,430 --> 00:00:57,910
So the total number of elements in the list are ten.

12
00:00:57,920 --> 00:01:01,090
So let's say I have a number of elements of are and.

13
00:01:03,190 --> 00:01:09,100
Now, let us see what we have to do while scanning through August, so I'll take account that I and

14
00:01:09,100 --> 00:01:10,260
I will scan for the list.

15
00:01:10,390 --> 00:01:13,150
So let us take I, I is Zettl.

16
00:01:14,410 --> 00:01:18,280
So being here, I should check whether the next element is the same as this one.

17
00:01:18,730 --> 00:01:21,360
So at Elfy you should check your five plus one.

18
00:01:21,370 --> 00:01:22,100
Is it same.

19
00:01:22,120 --> 00:01:25,600
These are some not so quietly move back.

20
00:01:26,020 --> 00:01:28,810
That is it same the next to the same.

21
00:01:28,810 --> 00:01:31,490
No I use that one to check the next location.

22
00:01:31,510 --> 00:01:33,010
No it's not same.

23
00:01:33,250 --> 00:01:35,110
So move ahead now.

24
00:01:35,620 --> 00:01:36,400
Is it same.

25
00:01:36,410 --> 00:01:36,990
Yes.

26
00:01:37,150 --> 00:01:38,520
So a duplicate is found.

27
00:01:38,740 --> 00:01:44,170
So wherever I is pointing right now, there is a duplicate element that is that element itself as duplicate.

28
00:01:44,330 --> 00:01:49,150
So OK, displayed then move on to the next element.

29
00:01:49,330 --> 00:01:52,270
So now here again, check the next element.

30
00:01:52,270 --> 00:01:54,630
See being on one element, we are checking the next element.

31
00:01:54,850 --> 00:01:55,850
Is it the same element?

32
00:01:55,990 --> 00:01:57,790
No, it's not duplicate then.

33
00:01:57,800 --> 00:01:58,810
Is it the same element?

34
00:01:58,930 --> 00:02:00,120
No, it's not a duplicate.

35
00:02:00,460 --> 00:02:01,570
Is it the same element?

36
00:02:01,720 --> 00:02:03,550
Nor is it the same element.

37
00:02:03,910 --> 00:02:04,380
Yes.

38
00:02:04,540 --> 00:02:07,150
So fluffiness phone so we can print 15.

39
00:02:07,450 --> 00:02:10,150
That is a duplicate element and move next.

40
00:02:10,750 --> 00:02:16,540
No, see the difference here if we move next and again, check the next element being here.

41
00:02:16,540 --> 00:02:22,620
If I check the next element or the same source again, 15 socially, then 15 once more.

42
00:02:23,020 --> 00:02:23,320
No.

43
00:02:23,800 --> 00:02:25,780
So I should print 15 only one time.

44
00:02:27,050 --> 00:02:27,430
Right.

45
00:02:27,710 --> 00:02:34,130
So this I have to take care if you if a same number is duplicated more than two times, then I have

46
00:02:34,130 --> 00:02:36,020
to take care of that situation also.

47
00:02:36,290 --> 00:02:39,400
So here it is, duplicated more than two times.

48
00:02:39,770 --> 00:02:41,430
So let us see what I should do.

49
00:02:41,690 --> 00:02:46,400
So, again, start the procedure and see how we can proceed for this one.

50
00:02:46,430 --> 00:02:47,770
I'll take one variable here.

51
00:02:47,900 --> 00:02:53,530
Let us call us last duplicate, last duplicate initially.

52
00:02:53,540 --> 00:02:57,280
Let it be something that says, you know, let us talk once more.

53
00:02:57,530 --> 00:03:02,360
See, for this problem handling this problem, we are using this one, not same procedure.

54
00:03:02,900 --> 00:03:03,620
Next element.

55
00:03:03,620 --> 00:03:04,160
Is it failed?

56
00:03:04,190 --> 00:03:06,290
No, it's not a duplicate mixed element, is it?

57
00:03:06,580 --> 00:03:07,670
No, it's not a duplicate.

58
00:03:07,910 --> 00:03:09,220
Next element, is it same?

59
00:03:09,530 --> 00:03:10,130
Yes.

60
00:03:10,130 --> 00:03:11,150
We got a duplicate.

61
00:03:11,570 --> 00:03:12,530
Last duplicate.

62
00:03:12,680 --> 00:03:15,530
I'll remove this zero and keep it as eight.

63
00:03:18,080 --> 00:03:20,960
So check the last duplicate, last duplicate, is it same?

64
00:03:20,970 --> 00:03:22,390
No, this is zero.

65
00:03:22,640 --> 00:03:24,440
So this is a new duplicate record.

66
00:03:24,680 --> 00:03:29,900
So bring this eight and change it to eight and move to the next element.

67
00:03:30,380 --> 00:03:31,640
Now check the next element.

68
00:03:31,640 --> 00:03:32,260
Is it same?

69
00:03:32,300 --> 00:03:34,460
No, it's not a duplicate mix.

70
00:03:34,460 --> 00:03:35,780
Not a duplicate next.

71
00:03:35,780 --> 00:03:36,590
Not a duplicate.

72
00:03:36,830 --> 00:03:38,410
Not being here 15.

73
00:03:38,810 --> 00:03:39,770
So next element.

74
00:03:39,770 --> 00:03:40,670
Yes, a 15.

75
00:03:40,680 --> 00:03:41,460
It's a duplicate.

76
00:03:41,690 --> 00:03:48,830
Is it the same one that we found it at the last note, so put into that 15 and change it to 15 now,

77
00:03:49,010 --> 00:03:51,710
changed it to 15 and move to the next element.

78
00:03:53,180 --> 00:03:55,210
Now again, next element is duplicate.

79
00:03:55,490 --> 00:03:58,240
But is it the same element, last duplicate we found?

80
00:03:58,640 --> 00:03:59,220
Yes.

81
00:03:59,390 --> 00:04:00,470
No need to print.

82
00:04:00,740 --> 00:04:04,800
So if that same duplicate is they're gonna print it for that.

83
00:04:04,820 --> 00:04:11,630
So just you can maintain one variable to keep a record of last a duplicate phone that will help you

84
00:04:11,750 --> 00:04:14,720
if there are multiple occurrences of duplicate element.

85
00:04:14,870 --> 00:04:16,459
So that's all this is the procedure.

86
00:04:17,300 --> 00:04:18,079
So that's it.

87
00:04:18,120 --> 00:04:19,000
This is the procedure.

88
00:04:19,220 --> 00:04:21,140
Not let us write the program code for it.

89
00:04:22,220 --> 00:04:31,730
I will take one variable call, last duplicate and let us set it as zero, not have to scan through

90
00:04:31,730 --> 00:04:34,280
this area so that I can do it using Falu.

91
00:04:40,140 --> 00:04:45,180
So the follow up will take me through all these elements in an area, then every time what I should

92
00:04:45,180 --> 00:04:49,740
do being on one index, I should check whether the next element is same.

93
00:04:50,100 --> 00:04:52,310
If it is the same, then I got a duplicate.

94
00:04:52,560 --> 00:05:00,190
So check if if I is equal to aof I plus one.

95
00:05:01,470 --> 00:05:04,380
So if these are the same I should check that.

96
00:05:04,710 --> 00:05:15,300
And also I should check that it should not be equal to that duplicate and each eye is not equal to last

97
00:05:15,300 --> 00:05:16,050
duplicate.

98
00:05:17,130 --> 00:05:22,180
So if these two conditions are true, then I got a new duplicate so I will print this one.

99
00:05:22,410 --> 00:05:23,580
So pretty def.

100
00:05:29,460 --> 00:05:35,520
Print an element of time and also modify last duplicate to this fine.

101
00:05:38,560 --> 00:05:40,700
So that's all this is the procedure.

102
00:05:40,930 --> 00:05:47,890
So if the next element is same and it is not the same as last duplicate, then it will be printed and

103
00:05:47,890 --> 00:05:54,050
we will record this new duplicate so that we don't bring the same thing next time.

104
00:05:55,000 --> 00:05:57,070
So that's all it is, a student exercise.

105
00:05:57,070 --> 00:06:00,040
So you can read on this program just as a part of main function.

106
00:06:00,040 --> 00:06:00,790
You can do it.

107
00:06:01,470 --> 00:06:06,790
You can take an array of these elements and you can read the rest of the call and you can check what

108
00:06:06,790 --> 00:06:07,630
the results you get.

109
00:06:07,870 --> 00:06:09,650
So it should be the duplicate elements.

110
00:06:10,560 --> 00:06:17,260
Now, next, I will show you how to count the number of duplicates like eight disappear in good times

111
00:06:17,260 --> 00:06:19,320
and 15 is appearing three times.

112
00:06:19,630 --> 00:06:21,490
So let us see how we can this.

113
00:06:22,790 --> 00:06:28,640
Now, let us see how to count the procedure will be same as finding duplicate just before the procedure,

114
00:06:28,940 --> 00:06:33,380
but only the difference is we also have to count how many times disappearing.

115
00:06:34,100 --> 00:06:35,920
So let me devise a procedure.

116
00:06:36,140 --> 00:06:41,990
Let us check so I will scan through this list of elements one by one and the procedure the same.

117
00:06:41,990 --> 00:06:44,600
I will be checking whether the mixed signals is CNN.

118
00:06:44,840 --> 00:06:46,880
So it's not a duplicate next element.

119
00:06:46,880 --> 00:06:47,490
Is it same?

120
00:06:47,900 --> 00:06:48,560
No, it's not.

121
00:06:48,890 --> 00:06:50,990
So it's not a duplicate mixed element?

122
00:06:51,020 --> 00:06:51,910
Yes, it is seen.

123
00:06:52,250 --> 00:06:53,010
Yes, it is same.

124
00:06:53,330 --> 00:06:59,600
So once you got a new element, so once you got a duplicate element, what we can do is we can count

125
00:06:59,600 --> 00:07:03,060
how many times disappearing so we can continue counting from there.

126
00:07:03,380 --> 00:07:05,690
So what I will do is next element.

127
00:07:05,690 --> 00:07:12,580
I will keep it as G that is under the counter, that is for counting and keeping it as G.

128
00:07:13,040 --> 00:07:15,440
I will check whether this is the same as this one or not.

129
00:07:15,440 --> 00:07:17,720
Is it the same then C++.

130
00:07:17,720 --> 00:07:19,680
Is it same as this element.

131
00:07:19,690 --> 00:07:21,600
I do not know.

132
00:07:21,650 --> 00:07:23,450
This is not saying this was different.

133
00:07:23,750 --> 00:07:24,500
So stop.

134
00:07:24,660 --> 00:07:26,210
We got the number of duplicates.

135
00:07:26,510 --> 00:07:28,700
How many G is the for.

136
00:07:28,880 --> 00:07:32,350
And I use it to four four minus two as two.

137
00:07:32,510 --> 00:07:33,960
So we've got two duplicates.

138
00:07:34,130 --> 00:07:36,020
Not once we have finished discounting.

139
00:07:36,020 --> 00:07:38,450
I should move to this element.

140
00:07:38,750 --> 00:07:42,860
That is I should move to this location that is minus one.

141
00:07:42,870 --> 00:07:47,270
So from here I can continue smoothly because Nick's in the loop.

142
00:07:47,570 --> 00:07:51,350
It will be a plus plus and it will automatically go to the next element.

143
00:07:51,830 --> 00:07:53,440
So let us continue the procedure.

144
00:07:53,480 --> 00:07:55,460
This is over right now.

145
00:07:55,490 --> 00:07:57,350
Again, continue as it same.

146
00:07:57,620 --> 00:07:59,300
No, not seem next.

147
00:07:59,300 --> 00:08:00,020
Is it same.

148
00:08:00,020 --> 00:08:01,730
No, not the same as it same.

149
00:08:02,060 --> 00:08:03,230
No not same.

150
00:08:03,800 --> 00:08:04,610
Is it same.

151
00:08:04,910 --> 00:08:05,480
Yes.

152
00:08:05,780 --> 00:08:10,220
So when it is same again you can start from here and go on incrementing G.

153
00:08:10,580 --> 00:08:11,850
Is it same as the offer.

154
00:08:12,260 --> 00:08:15,680
Is it same as the of a yes or the same for C++.

155
00:08:15,680 --> 00:08:18,080
Is it the same as if I know.

156
00:08:18,740 --> 00:08:21,890
So from I to J we have the duplicate element.

157
00:08:21,900 --> 00:08:23,510
So what is the element that is 15.

158
00:08:23,750 --> 00:08:24,620
How many times.

159
00:08:24,770 --> 00:08:26,180
So GS nine.

160
00:08:26,180 --> 00:08:28,690
Nine minus six of that is three.

161
00:08:28,940 --> 00:08:37,120
So fifteen is about getting three times then bring I here and check if mixed element is same.

162
00:08:37,130 --> 00:08:38,000
No it's not same.

163
00:08:38,240 --> 00:08:41,390
So I when it reaches the last index it should stop.

164
00:08:41,570 --> 00:08:47,780
So in the follow up I should say and minus one I should not go up to end here using two in the first

165
00:08:47,780 --> 00:08:53,000
I as well as G I'm using simple names that is and G and G is helping us to count.

166
00:08:53,420 --> 00:09:00,320
So let us write on the code for counting so the code will be similar to the duplicates code.

167
00:09:00,500 --> 00:09:04,310
Just here we are counting with the help of sheet, so I will write the code.

168
00:09:04,850 --> 00:09:08,660
So for scanning through the list of elements, I need a follow up so far.

169
00:09:08,660 --> 00:09:10,520
It will start, I will start from zero.

170
00:09:10,520 --> 00:09:12,860
I should let them and the minus one.

171
00:09:12,860 --> 00:09:13,160
Right.

172
00:09:13,490 --> 00:09:16,440
So in the previous example I wrote in here, it should be in minus one.

173
00:09:16,490 --> 00:09:18,880
Since the last solution is here, I should stop here.

174
00:09:19,190 --> 00:09:22,060
Beyond this, there are no elements that I love.

175
00:09:22,070 --> 00:09:22,470
Plus.

176
00:09:23,380 --> 00:09:25,160
That's what I should do.

177
00:09:25,630 --> 00:09:30,120
See, when I is here, I should see that, if that makes it seem so, yes.

178
00:09:30,240 --> 00:09:30,750
Same thing.

179
00:09:30,760 --> 00:09:35,830
If if I is equal to the next element, that is a plus one.

180
00:09:36,340 --> 00:09:40,270
If it is same, then what I should do, I should start from here.

181
00:09:40,390 --> 00:09:45,460
I can start from here and make it move until it finds a different element.

182
00:09:45,460 --> 00:09:46,650
So you should stop here.

183
00:09:47,050 --> 00:09:52,130
So inside this I can say get assigned I or I plus one.

184
00:09:52,150 --> 00:09:52,960
Also I can write.

185
00:09:56,370 --> 00:10:06,990
And I should move, Jay, while eight of G is equal to eight of I, as long as it is equal, I should

186
00:10:06,990 --> 00:10:07,980
go on moving.

187
00:10:08,220 --> 00:10:10,140
So Seijas C++.

188
00:10:10,560 --> 00:10:16,080
So here I have not written any brackets or anything which is a part of my loop in a single line.

189
00:10:16,090 --> 00:10:16,770
I am writing it.

190
00:10:17,340 --> 00:10:23,610
Then once j stop at some place like here, then the difference between these two is the number of times

191
00:10:23,790 --> 00:10:24,990
eight is duplicated.

192
00:10:25,260 --> 00:10:31,920
So I can print this one, put it in def, I will print the duplicate element and the number of times.

193
00:10:33,590 --> 00:10:38,210
Seat person Daly is appearing percentile the time, so who is appearing?

194
00:10:38,240 --> 00:10:41,720
How many times do you minus eight times.

195
00:10:42,930 --> 00:10:50,430
G minus eight times, then after this, I should make I come on to this location, not not after this,

196
00:10:50,430 --> 00:10:57,390
I should move I to a location just before G because Innaloo again, I plus plus I will be definitely

197
00:10:57,390 --> 00:10:57,780
moving.

198
00:10:58,080 --> 00:11:03,720
So I should be J minus one gatsas.

199
00:11:05,680 --> 00:11:09,370
I is used in the follow loop, can be modified inside the loop.

200
00:11:09,390 --> 00:11:10,580
Yes, you can modify it.

201
00:11:11,110 --> 00:11:13,890
So now already these two elements are finished.

202
00:11:13,900 --> 00:11:16,050
So I was here so I can bring it here.

203
00:11:16,330 --> 00:11:20,220
So next time I placeless again, I start from this point.

204
00:11:20,620 --> 00:11:21,160
Now I'll get it.

205
00:11:21,160 --> 00:11:23,310
Will check this element with this one and this is different.

206
00:11:23,350 --> 00:11:23,830
So on.

207
00:11:24,830 --> 00:11:25,500
So that's all.

208
00:11:26,360 --> 00:11:31,100
So just by using a variable am counting the number of elements.

209
00:11:32,290 --> 00:11:37,820
Now, next thing is analysis, I want to know how much time this procedure is taking.

210
00:11:38,470 --> 00:11:42,540
So what we are doing in this one, we are scanning through all the list of elements.

211
00:11:42,700 --> 00:11:43,630
How many times?

212
00:11:43,790 --> 00:11:44,650
Only one time.

213
00:11:45,730 --> 00:11:51,670
We are scanning from left to right, say this for loop, even in the duplicates displaying the duplicates

214
00:11:51,970 --> 00:11:54,460
as well as counting duplicates, the logic is the same.

215
00:11:55,000 --> 00:11:58,140
Only difference is we also have one more way insight then.

216
00:11:59,020 --> 00:12:00,580
Do we consider this like loop?

217
00:12:01,010 --> 00:12:01,690
No need.

218
00:12:01,930 --> 00:12:02,440
No need.

219
00:12:02,620 --> 00:12:04,230
Is it covering the entirety?

220
00:12:04,480 --> 00:12:05,880
No, just a part of a three.

221
00:12:06,040 --> 00:12:06,930
Part of an array.

222
00:12:07,210 --> 00:12:08,230
So it's negligible.

223
00:12:08,410 --> 00:12:15,610
And even when we have moved to some location, we are also shifting, i.e. so those elements, whichever

224
00:12:15,610 --> 00:12:16,600
elements we have got.

225
00:12:17,200 --> 00:12:22,260
So the elements covered by J we are skipping it, i.e. so we don't have to count it.

226
00:12:22,540 --> 00:12:28,660
So this loop is negligible though, while loop is there, it's not doing extra effort.

227
00:12:28,870 --> 00:12:32,050
So the time is equal to the number of elements.

228
00:12:32,080 --> 00:12:33,160
That is a number of elements.

229
00:12:33,160 --> 00:12:35,680
We can see an order of an.

230
00:12:36,840 --> 00:12:42,300
So the time is outdraw, and so this is the analysis for finding duplicates and counting duplicates

231
00:12:42,810 --> 00:12:43,020
now.

232
00:12:43,050 --> 00:12:46,940
Next, I will show you one more method that is using hash table.

233
00:12:47,820 --> 00:12:50,900
We can find duplicates and also count duplicates.

234
00:12:51,090 --> 00:12:52,770
So let us see another method.

