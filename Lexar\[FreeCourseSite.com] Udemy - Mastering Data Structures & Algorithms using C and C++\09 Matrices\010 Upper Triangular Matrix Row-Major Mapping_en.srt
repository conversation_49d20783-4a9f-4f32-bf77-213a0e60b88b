1
00:00:00,210 --> 00:00:05,939
Now, the topic is a triangular mattocks, as you are already familiar with the lower triangular metrics

2
00:00:05,939 --> 00:00:07,450
we have seen in every presentation.

3
00:00:07,800 --> 00:00:09,570
So let us finish this quickly.

4
00:00:10,110 --> 00:00:12,370
This is an upper triangle, triangular metrics.

5
00:00:12,370 --> 00:00:18,180
So non-zero elements are in the upper triangular part of this square matrix.

6
00:00:18,510 --> 00:00:21,660
So these are non-zero elements.

7
00:00:22,110 --> 00:00:23,130
These are nonlegal.

8
00:00:24,400 --> 00:00:31,590
So if I observe in this house, I that is low numbers and gee, that are column numbers, then which

9
00:00:31,630 --> 00:00:32,880
elements are non-zero?

10
00:00:33,220 --> 00:00:42,630
If I observe this is two, three to four, three, four, I and I is listening for five, I listening

11
00:00:43,490 --> 00:00:44,140
for four.

12
00:00:44,290 --> 00:00:45,190
I is equal.

13
00:00:45,220 --> 00:00:46,160
I is equal.

14
00:00:46,630 --> 00:00:52,840
So one tree is less than G so I is less than or equal to two elements are non-zero.

15
00:00:53,230 --> 00:00:54,670
The which elements are zero.

16
00:00:54,880 --> 00:01:03,700
If I is greater than G so I'm of that is a maximum of Ikoma G is a zero F.

17
00:01:04,890 --> 00:01:12,570
I is greater than Gene and am of IG is nonzero.

18
00:01:15,480 --> 00:01:22,950
If I is less than or equal to gee, this is all we can define, our upper triangle aromatics.

19
00:01:24,370 --> 00:01:31,480
Next, how many non-zero elements are there in this, Max Foster, who is having five elements, the

20
00:01:31,480 --> 00:01:34,280
next four is having four, then three, then two, then one.

21
00:01:34,570 --> 00:01:39,240
So there's is five plus four plus three plus two plus one.

22
00:01:39,610 --> 00:01:46,420
So for any end, it will be end plus and minus one plus goes on up to three to one.

23
00:01:46,450 --> 00:01:54,330
This is nothing but an end to end plus one by to these many non-zero elements, are there not.

24
00:01:54,340 --> 00:01:55,870
How many zero elements are there.

25
00:01:56,380 --> 00:02:01,420
Zero elements will be an end to end, minus one by do so.

26
00:02:01,420 --> 00:02:04,880
Already we have seen this lower triangular matrix.

27
00:02:04,880 --> 00:02:08,020
So if you have not watch that one, you can go back and watch it.

28
00:02:08,580 --> 00:02:14,430
Next, how to represent this one in a single dimensionally so we will not be taking a two dimensional

29
00:02:14,440 --> 00:02:16,680
idea and filling with these many zeros.

30
00:02:16,930 --> 00:02:22,140
So we want to avoid storage of zero so that we can save some space as well as processing time.

31
00:02:22,900 --> 00:02:24,590
So let us take a single dimensionality.

32
00:02:24,850 --> 00:02:27,060
So what should be the size of a array?

33
00:02:27,880 --> 00:02:29,990
Depends on a number of non-zero elements.

34
00:02:30,050 --> 00:02:31,620
That is an even better one by two.

35
00:02:31,840 --> 00:02:34,540
So here the example side is five.

36
00:02:34,840 --> 00:02:38,260
So five in the five plus one by two that is 15.

37
00:02:38,260 --> 00:02:42,480
So I need an array of 15 for storing these non-zero elements.

38
00:02:43,180 --> 00:02:45,270
I will take an array of size 15 here.

39
00:02:45,820 --> 00:02:53,710
So here I have an array of the size 15, the this aside from zero to 14 and we will map this upward

40
00:02:53,710 --> 00:02:56,700
triangular matrix in a single dimension adding.

41
00:02:57,770 --> 00:03:05,390
So what are the methods, again, to today that is a real major and major matter, so let us fill the

42
00:03:05,390 --> 00:03:06,950
elements role by all.

43
00:03:07,280 --> 00:03:13,340
So first we will see the major and I will remove this one so that I can work out and displace Romijn.

44
00:03:13,640 --> 00:03:15,020
I will fill these elements.

45
00:03:15,800 --> 00:03:22,670
So if you observe us through even one one two one three one four one and one five, this is rule one.

46
00:03:24,400 --> 00:03:31,610
The next rule to do so is not from two one on what to do on the next rule is a three three onwards.

47
00:03:31,610 --> 00:03:34,290
So like that, I will fill up all those rules.

48
00:03:34,960 --> 00:03:39,310
So this is how I have felt rule one or two or three or four or five.

49
00:03:39,580 --> 00:03:41,560
And the last rule is having just one element.

50
00:03:41,560 --> 00:03:46,510
For one element is the first rule is having five elements or five elements are there for industry.

51
00:03:46,690 --> 00:03:48,550
All these elements are stored there.

52
00:03:48,750 --> 00:03:52,330
Rule no, we need a formula for formula.

53
00:03:52,330 --> 00:03:58,720
Let us take one example and observe it as we all did it all in the lower triangular matrix video that

54
00:03:58,930 --> 00:04:00,750
those formulas will be useful here.

55
00:04:01,030 --> 00:04:02,830
So let us take one example and see.

56
00:04:03,340 --> 00:04:07,180
We will take the element of four or five.

57
00:04:07,720 --> 00:04:10,930
So index of A4 comma five.

58
00:04:15,190 --> 00:04:22,870
Four to five, as the president here in Fort Rule, I should skip first or second or third row, then

59
00:04:22,870 --> 00:04:23,790
I can reach there.

60
00:04:24,190 --> 00:04:28,440
So let us keep these elements, then I can reach in the beginning of the fourth rule.

61
00:04:28,750 --> 00:04:31,780
So first rule is having five elements one, two, three, four, five.

62
00:04:32,620 --> 00:04:35,890
The next rule is having four elements, four elements.

63
00:04:36,250 --> 00:04:38,020
Next is having three elements.

64
00:04:38,590 --> 00:04:44,140
Then after skipping these elements, I'm here in the beginning of the fourth rule, then how much I

65
00:04:44,140 --> 00:04:44,740
should move.

66
00:04:44,740 --> 00:04:46,750
I had to read this for Gomo five.

67
00:04:46,750 --> 00:04:52,420
Just one element then total is four plus five nine nine plus three, 12 and 13.

68
00:04:52,780 --> 00:04:54,220
Yes, index is 13.

69
00:04:54,220 --> 00:04:55,450
So I'm getting the index.

70
00:04:55,780 --> 00:04:59,860
So this looks similar to column in formula of lower triangle of Matics.

71
00:04:59,860 --> 00:05:04,600
If you have not seen the video go back and see that video, I'll directly use that formula here.

72
00:05:05,110 --> 00:05:09,550
So a formula for any element at index igy.

73
00:05:10,240 --> 00:05:11,080
This can be done.

74
00:05:11,080 --> 00:05:18,150
As I see this, the five is nothing but and this is a dimension and Crosson or in my end.

75
00:05:18,550 --> 00:05:24,520
So this is N plus and minus one and plus and minus two.

76
00:05:24,520 --> 00:05:25,210
This is go on.

77
00:05:25,210 --> 00:05:33,940
Reducing up to what c this was before this was four, so this was up to five minus two five minus two

78
00:05:34,150 --> 00:05:36,220
five five minus one five minus two.

79
00:05:36,460 --> 00:05:43,600
So this should be a minus two, up to minus two and minus of minus two.

80
00:05:44,500 --> 00:05:47,410
Then outside this C this is one.

81
00:05:47,710 --> 00:05:53,170
So this is five minus four minus I, G minus eight.

82
00:05:54,280 --> 00:06:02,170
So if you do remember that formula, this is the same as that formula only I n g has interchange.

83
00:06:02,170 --> 00:06:04,150
I became and became eight.

84
00:06:04,450 --> 00:06:14,560
So the final simplified formula can be I minus one times and minus eight, minus two into a minus one

85
00:06:14,860 --> 00:06:15,790
by two.

86
00:06:16,450 --> 00:06:20,740
And finally J minus eight at last this one.

87
00:06:21,070 --> 00:06:28,570
So this is the formula for a roll, major representation of upper triangular max.

88
00:06:29,140 --> 00:06:33,090
So these formulas, I'll be using them in the program afterwards.

89
00:06:33,100 --> 00:06:36,160
So once we finish the mattresses, I will show you the program.

90
00:06:36,820 --> 00:06:39,850
Now, let us look at the major mapping of this fund.

91
00:06:40,120 --> 00:06:44,260
So we'll store the elements column by column in a single dimensionality.

92
00:06:44,470 --> 00:06:46,180
Then we come up with some formula.

