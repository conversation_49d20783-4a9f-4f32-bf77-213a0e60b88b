1
00:00:00,610 --> 00:00:06,939
In this video, I will write a recursive function for inserting in a binary search tree, as well as

2
00:00:06,939 --> 00:00:13,630
a recursive function for deleting from a binary search tree, already we have redundant program for

3
00:00:13,630 --> 00:00:20,950
creating a binary choice tree, and the insert function was a repeating function that is iterative function.

4
00:00:20,950 --> 00:00:23,800
Already we have seen it in the same program.

5
00:00:23,810 --> 00:00:27,820
I will add a function for recursive insert.

6
00:00:29,620 --> 00:00:37,390
Let us write a function that returns Structure A. Pointer and the function name I will call you guys

7
00:00:37,750 --> 00:00:46,210
are inside because already I've used a name insert for iterative insert function, so it should be pointed

8
00:00:46,210 --> 00:00:47,300
to a root node.

9
00:00:47,350 --> 00:00:52,450
So let us call the size of B and that is the root node and the key that we want to insert.

10
00:00:54,780 --> 00:01:01,010
Now, the concert was just like a recursive search function, which we have already discussed on Lightbourne,

11
00:01:01,620 --> 00:01:03,180
so the same method will follow.

12
00:01:04,160 --> 00:01:08,450
Let's start writing the code, if any variables are glad, I will declare them afterwards.

13
00:01:09,930 --> 00:01:16,180
First thing, if the key is less, then these data, and that is route data.

14
00:01:17,400 --> 00:01:22,490
If it is less than route data, then we should try to insert on the left hand side.

15
00:01:22,740 --> 00:01:29,160
So call our insert upon the left hand side of B that is alkyl.

16
00:01:30,870 --> 00:01:36,470
And the keys to the perimeter, these are the two parameters of this function is going to return the

17
00:01:36,930 --> 00:01:40,790
and all that, I will take it and left child of P.

18
00:01:43,410 --> 00:01:44,430
Then elf's.

19
00:01:46,110 --> 00:01:50,100
If a key is greater than these data.

20
00:01:51,800 --> 00:01:57,820
That is a huge data on the key data then I should call upon recursive function should be called upon

21
00:01:57,820 --> 00:02:01,780
Rightside and its address should be taken in our child.

22
00:02:12,180 --> 00:02:18,900
Oltmanns, the key is equal to all 80 percent, so we don't have to do anything simply on be and even

23
00:02:19,440 --> 00:02:20,620
after insertion.

24
00:02:20,640 --> 00:02:24,480
I have to return only, so I'll just write P in every case.

25
00:02:24,780 --> 00:02:28,230
So it means or else if they are equal, I don't have to do anything.

26
00:02:29,440 --> 00:02:36,610
Then went to insert this one, see, while searching, when we reach a when it becomes null that time,

27
00:02:36,610 --> 00:02:38,410
we should insert a new node.

28
00:02:38,410 --> 00:02:42,330
So in the beginning of it, I don't know if A has became null.

29
00:02:42,330 --> 00:02:43,930
That means we have reached the last.

30
00:02:43,960 --> 00:02:47,530
That is leave node, then create a new node.

31
00:02:47,530 --> 00:02:54,880
So I'll take a temporary pointer t and with that I will create a new node struct node pointer and I

32
00:02:54,880 --> 00:02:59,710
should call my log function for creating a new node size of.

33
00:03:02,270 --> 00:03:03,850
Struck Norbert.

34
00:03:05,630 --> 00:03:12,440
So nobody's created inside this and all the data I should set the value that is key and also I should

35
00:03:12,440 --> 00:03:15,620
say it's left child as well as Rachel axonal.

36
00:03:15,950 --> 00:03:19,230
He's left and these are as null.

37
00:03:22,210 --> 00:03:30,590
And right on the so that is said to the parent A. as a love child or right child, it depends on how

38
00:03:30,590 --> 00:03:31,440
we were searching.

39
00:03:33,100 --> 00:03:33,640
That's it.

40
00:03:33,850 --> 00:03:35,500
So for this, I need a.

41
00:03:36,460 --> 00:03:38,530
Pointer that is struck Naude.

42
00:03:41,260 --> 00:03:44,770
Pointed to the city, I can keep it initialise to nul.

43
00:03:46,370 --> 00:03:50,690
That's all the recursive procedure for inserting in a binary search tree.

44
00:03:50,810 --> 00:03:55,430
Now let us go to the main function and let us use that are insert function.

45
00:03:56,030 --> 00:04:00,500
See, when I call or insert function for the first time that I'm creating first node, then I should

46
00:04:00,740 --> 00:04:02,070
assign this to root.

47
00:04:02,540 --> 00:04:05,870
So this should be a root assigned or insert.

48
00:04:05,870 --> 00:04:14,000
First time I'm inserting N then R insert and also I should pass this root as parameter in every call,

49
00:04:14,300 --> 00:04:15,470
then r insert.

50
00:04:15,470 --> 00:04:17,420
I should pass through that parameter.

51
00:04:18,940 --> 00:04:19,470
Rude.

52
00:04:19,959 --> 00:04:21,200
Because it's repulsive.

53
00:04:21,220 --> 00:04:24,750
It should take the parameter, the pointer off route itself.

54
00:04:29,290 --> 00:04:32,730
And all these functions are so I will modify them.

55
00:04:34,770 --> 00:04:38,890
Only the first one to have to take it in road, because when I'm creating fussin order, when it returns

56
00:04:38,890 --> 00:04:40,980
to the point that that should be taken in route.

57
00:04:42,660 --> 00:04:49,110
That's all remaining already, I have a function for checking in order traversal Gelperin in order traversal

58
00:04:49,110 --> 00:04:53,120
and also checking a key element whether to present or not.

59
00:04:53,130 --> 00:04:57,990
So this time we will check for 20 minutes, but then do not let us run this program.

60
00:04:59,230 --> 00:05:05,780
Yes, it's working perfectly, binary search trees created five, eight, 10, 20, 30 years in order

61
00:05:05,800 --> 00:05:07,630
traversal and 20.

62
00:05:07,900 --> 00:05:12,850
He's also found let us debug this and see how this tree is created.

63
00:05:12,850 --> 00:05:19,570
For this all to stand on its left child will be five, then ritcher of 10 will be 20, and then eight

64
00:05:19,570 --> 00:05:23,600
will be on the right hand side of five and 30 will be on the right hand side of five.

65
00:05:23,920 --> 00:05:29,950
So actually the structure that we should get is so here I will draw and show you just a rough sketch.

66
00:05:29,960 --> 00:05:36,340
I'm doing it here and should be in the route and five should be on its left hand side.

67
00:05:37,900 --> 00:05:41,440
There should be five friends on the right hand side of 10.

68
00:05:44,290 --> 00:05:51,700
There should be 20 or 30 should be created like this and age should be on the right hand side of five.

69
00:05:53,290 --> 00:05:54,310
This is eight.

70
00:05:55,490 --> 00:06:01,370
And today should be on the right hand side of eternity, so the kids should be insulted like this.

71
00:06:02,660 --> 00:06:08,660
They should be Turkey, let us debug over the program and check whether the trees constructed like this

72
00:06:08,660 --> 00:06:09,200
or not.

73
00:06:11,400 --> 00:06:16,710
I'll put a break point on in order traversal before performing, in order to some, all the notes will

74
00:06:16,710 --> 00:06:17,270
be inserted.

75
00:06:17,280 --> 00:06:19,080
So let us see how the structure looks like.

76
00:06:21,160 --> 00:06:27,290
Let us look at the area Rudi's there, all this having value and yes, perfect, it's live chat is fine.

77
00:06:27,310 --> 00:06:27,850
Yes.

78
00:06:28,420 --> 00:06:30,520
And the files left Charles null here.

79
00:06:30,760 --> 00:06:31,600
This null here.

80
00:06:32,650 --> 00:06:35,050
And face right child is eight.

81
00:06:35,080 --> 00:06:40,020
Yes, it is eight and it's left and right, Charilaos, that annuls.

82
00:06:40,820 --> 00:06:42,490
Then let us close this.

83
00:06:44,110 --> 00:06:49,190
Then and right child, it's 20 years and it's left Charla's another year.

84
00:06:49,240 --> 00:06:52,260
You can see that it is null and ends right.

85
00:06:52,300 --> 00:06:54,820
Child is 30 years perfect.

86
00:06:56,450 --> 00:07:03,520
So that's it, that is perfectly inserting and creating a binary so sorry, I'll stop this one.

87
00:07:05,240 --> 00:07:10,370
No legislator, delete, delete, function, delete function will be a recursive function, just like

88
00:07:10,370 --> 00:07:15,260
a recursive search or the insert we saw, so let us start writing.

89
00:07:15,260 --> 00:07:17,480
It struck Naude.

90
00:07:18,380 --> 00:07:20,570
It will return a pointer to an old.

91
00:07:23,310 --> 00:07:29,680
Delete function, so it should take a parameter as struct no pointer, let us call it speed.

92
00:07:29,700 --> 00:07:33,570
This is going to be ruled an integer key that we want to delete.

93
00:07:35,220 --> 00:07:45,300
Then inside the function, I will write on the similar code at first, if key is less than B's data,

94
00:07:45,840 --> 00:07:49,000
then we will perform search upon left hand side.

95
00:07:49,020 --> 00:07:58,020
So these alkyl as assigned with the lead function, which will call itself again on, is alkyl.

96
00:07:59,430 --> 00:07:59,820
Then.

97
00:08:00,910 --> 00:08:02,900
Or else, if.

98
00:08:04,010 --> 00:08:12,030
He is greater than these data than it is on the right hand side, so we should search and key element

99
00:08:12,050 --> 00:08:12,970
on the right hand side.

100
00:08:12,980 --> 00:08:20,390
So that will continue on the right hand side by assigning it to our child of P or delete function by

101
00:08:20,390 --> 00:08:20,930
passing.

102
00:08:21,350 --> 00:08:26,930
These are child as well as key also sticking around here.

103
00:08:27,230 --> 00:08:28,340
And the previous one also.

104
00:08:28,340 --> 00:08:29,320
I should try key.

105
00:08:29,900 --> 00:08:30,510
Yes.

106
00:08:30,530 --> 00:08:31,370
No, it's OK.

107
00:08:32,299 --> 00:08:34,490
Else elements, both are equal.

108
00:08:34,490 --> 00:08:38,120
Minsky's found and this time we have to delete a key.

109
00:08:38,510 --> 00:08:44,360
So for deleting a key already I have shown you how element is deleted from binary search tree.

110
00:08:44,990 --> 00:08:49,430
We can delete an element by replacing it with the preorder.

111
00:08:49,850 --> 00:08:55,430
We can delete an element and replace it with the in order processor or in order successor.

112
00:08:55,520 --> 00:08:56,880
So we have two options here.

113
00:08:57,710 --> 00:09:01,270
So here I would like to decide which one I should take.

114
00:09:01,910 --> 00:09:10,040
I will not blindly follow predecessor or blindly follow successor, but I will decide based on the height

115
00:09:10,040 --> 00:09:12,080
of subtree and the height of subtree.

116
00:09:12,380 --> 00:09:16,670
If the height of left subtree is more than we were deleting element from the left hand side.

117
00:09:16,890 --> 00:09:19,280
Otherwise we will need an element from right hand side.

118
00:09:19,850 --> 00:09:21,950
If they are equal, we can delete from any side.

119
00:09:21,970 --> 00:09:28,600
So let us check the height and based on the height, we will decide whether to delete from left hand

120
00:09:28,610 --> 00:09:29,760
side or right hand side.

121
00:09:30,170 --> 00:09:35,780
So first of all, I will find height of piece left China.

122
00:09:37,570 --> 00:09:39,040
If this is greater than.

123
00:09:40,380 --> 00:09:47,880
Height of these are child, yeah, I have to ride on this function height, I'll ride it afterwards.

124
00:09:48,810 --> 00:09:51,120
So if peace left, child is greater.

125
00:09:52,710 --> 00:09:58,920
So if bees left shoulder height is greater than the right hand side, then we should delete an element

126
00:09:58,920 --> 00:09:59,820
from left hand side.

127
00:10:00,060 --> 00:10:02,030
So for deleting an element from left hand side.

128
00:10:02,250 --> 00:10:04,310
Actually, we need in order.

129
00:10:04,320 --> 00:10:09,050
But it's also so for that, I would write a function which you'll find out in order predecessor.

130
00:10:09,240 --> 00:10:16,250
So that function I will write on directly here, I will call that function in place of left subtree

131
00:10:16,470 --> 00:10:18,620
so that Espy's alkyl.

132
00:10:19,230 --> 00:10:21,470
So how to find the in order processor.

133
00:10:21,960 --> 00:10:24,980
It should be a rightmost child of left subtree.

134
00:10:24,990 --> 00:10:26,220
Yes, we will get that one.

135
00:10:26,640 --> 00:10:34,890
And this I will they point out the secu then after finding that element I should copy the data off a

136
00:10:34,890 --> 00:10:35,550
Acuil.

137
00:10:35,730 --> 00:10:40,290
I should copy the date of that in order processor in the piece of data.

138
00:10:40,300 --> 00:10:45,570
So these data should be changed to use data.

139
00:10:45,600 --> 00:10:46,680
It should be replaced.

140
00:10:47,710 --> 00:10:50,350
Then after replacing I should delete that an order.

141
00:10:50,370 --> 00:10:58,980
Q So is alkyl should be assigned that again for delete recursively upon.

142
00:11:00,010 --> 00:11:00,670
These.

143
00:11:02,940 --> 00:11:09,700
Alkyl, Newquay, we have to delete this key that is due to data we have to delete.

144
00:11:10,040 --> 00:11:13,890
Yes, this will recursively call itself means.

145
00:11:14,040 --> 00:11:19,140
We have already seen that while deleting, we may have to modify more than one keys or more than one

146
00:11:19,140 --> 00:11:19,560
note.

147
00:11:19,920 --> 00:11:20,950
So how does it work?

148
00:11:20,970 --> 00:11:23,040
I will show you for this.

149
00:11:23,040 --> 00:11:24,460
I will draw a diagram and show you.

150
00:11:25,080 --> 00:11:27,240
So here is an example by necessity.

151
00:11:27,270 --> 00:11:30,890
Suppose I want to delete 50 and it is not having any right child.

152
00:11:30,900 --> 00:11:35,340
So I should get Elliman from left subtree that is in order processor.

153
00:11:35,490 --> 00:11:37,750
So in order processor 450 is 40.

154
00:11:38,070 --> 00:11:40,530
So this value should be copied here.

155
00:11:41,430 --> 00:11:42,950
Then this note should be deleted.

156
00:11:43,290 --> 00:11:47,700
But again, this node is having its left suppy, so it's in order.

157
00:11:47,820 --> 00:11:52,560
This is a study, so it should be copied here and we should delete the node 30.

158
00:11:52,860 --> 00:11:55,610
So this is how it is calling recursively itself.

159
00:11:55,890 --> 00:11:59,040
Then finally we delete nor the 30 because it's a leaf node.

160
00:11:59,250 --> 00:12:03,840
So physically this node will be deleted and value today is copied here.

161
00:12:04,260 --> 00:12:08,510
And the value for this couple here in this way, furphies deleted.

162
00:12:08,850 --> 00:12:10,560
So the physical node deleted.

163
00:12:10,560 --> 00:12:11,930
Is this one the last one?

164
00:12:11,940 --> 00:12:12,710
Alief one.

165
00:12:13,230 --> 00:12:15,690
So that's all we are calling it recursively.

166
00:12:16,770 --> 00:12:21,810
So back on this one, if the height of love child was more than we have performed on the left hand side,

167
00:12:22,090 --> 00:12:25,130
otherwise we will perform the same thing on the right hand side.

168
00:12:25,140 --> 00:12:29,850
So I will it in else and I will post it in the same.

169
00:12:30,540 --> 00:12:37,170
So this time I have to find in order successor upon right child.

170
00:12:38,550 --> 00:12:40,560
And this right child.

171
00:12:42,530 --> 00:12:51,140
And this also should be Rachel, that's so recursively perform deletion on the left side or the right

172
00:12:51,140 --> 00:12:52,430
hand side, depending on the height.

173
00:12:53,030 --> 00:12:54,670
Finally, when I should delete a..

174
00:12:54,950 --> 00:12:56,590
So here I should delete a..

175
00:12:57,350 --> 00:12:58,550
Hitesh or delete a..

176
00:12:59,030 --> 00:13:03,680
If E has became null means it has become null.

177
00:13:03,890 --> 00:13:06,620
Actually, nothing we have to delete or simply null.

178
00:13:07,820 --> 00:13:12,730
And second thing, if a B is a leaf node so easily not left.

179
00:13:13,110 --> 00:13:13,910
Is not the.

180
00:13:16,840 --> 00:13:22,480
Is equal to NUL as well as is right Charla's equal to none.

181
00:13:26,430 --> 00:13:31,170
If both are annulments, it's a leaf node, and when it is at least not directly, we have to delete

182
00:13:31,170 --> 00:13:31,680
this note.

183
00:13:31,980 --> 00:13:42,660
So here I will write on the code for freeing that node free b B should be freed and before free freeing

184
00:13:42,660 --> 00:13:45,090
B, let us check if a B is.

185
00:13:47,840 --> 00:13:54,620
It is a root node, if a B is equal to root, n E is equal to root, then.

186
00:13:55,750 --> 00:13:59,020
Rules should be made as not, so you should delete that note.

187
00:13:59,500 --> 00:14:04,350
Yes, then after that we should return null because the note is deleted.

188
00:14:06,500 --> 00:14:12,860
That's all this is the code for deleting see, this portion is like the search process only and when

189
00:14:12,860 --> 00:14:13,920
the keys are found.

190
00:14:13,970 --> 00:14:19,370
So we have to replace the element by finding in order for this sort of successor.

191
00:14:19,790 --> 00:14:26,060
So I have an option depending on the height and then we have reached the least not we should delete

192
00:14:26,060 --> 00:14:26,560
that note.

193
00:14:26,840 --> 00:14:31,760
And we should also make sure that if this was the last node that is equal to route only, then the root

194
00:14:31,760 --> 00:14:32,720
should be made as null.

195
00:14:33,450 --> 00:14:37,100
And then here the required variable.

196
00:14:37,100 --> 00:14:38,940
I will declare that is struct node.

197
00:14:38,960 --> 00:14:40,540
I have taken a pointer that is.

198
00:14:40,550 --> 00:14:42,140
Q So I will declare it.

199
00:14:45,550 --> 00:14:50,870
The nub of this, I should have functions for finding hiden in order, processor in order successor.

200
00:14:51,300 --> 00:15:01,650
So the function for finding height, it should take struck normed point to be an FBI equals to null,

201
00:15:02,550 --> 00:15:04,080
then return zero.

202
00:15:07,170 --> 00:15:10,770
Otherwise, I should find out its value and I value.

203
00:15:13,420 --> 00:15:17,800
Excess height BS as a child.

204
00:15:19,620 --> 00:15:22,070
White is height.

205
00:15:23,150 --> 00:15:24,590
These are child.

206
00:15:28,240 --> 00:15:30,400
Then return whichever greater solid.

207
00:15:30,430 --> 00:15:36,160
We have seen this quote, so I don't have to explain this, I'm typing it, I've done X plus one or

208
00:15:36,160 --> 00:15:37,930
Alfredton Y plus one.

209
00:15:38,180 --> 00:15:39,130
This is for the call.

210
00:15:40,840 --> 00:15:41,770
This for the height.

211
00:15:42,400 --> 00:15:51,340
Then in order preserve, it should return Naude structure that is in pre in order predecessor and it

212
00:15:51,340 --> 00:15:53,680
should take the parameter also as node structure.

213
00:15:53,690 --> 00:15:54,880
Let us take it as being.

214
00:15:57,340 --> 00:16:01,780
Now, from the point that is known to be, we should find out in our.

215
00:16:03,270 --> 00:16:13,650
So it's a rightmost child survival E as well as Mitzpe should not be null while our child is not equal

216
00:16:13,650 --> 00:16:14,420
to null.

217
00:16:15,510 --> 00:16:21,510
Means it will be going on, moving on the right hand side, so rightmost child, we will tape off live

218
00:16:21,510 --> 00:16:26,470
subtree and finally should return E!

219
00:16:28,290 --> 00:16:29,220
Now, the simbi.

220
00:16:32,760 --> 00:16:38,550
I will copy this and I will write down the code for in order for success, so I will just modify this,

221
00:16:38,550 --> 00:16:42,270
I'll just give some line gaps so that you can clearly see the code that I'm typing.

222
00:16:43,680 --> 00:16:47,290
This is for in order successor in order successor.

223
00:16:47,760 --> 00:16:50,730
So it should be going on the left hand side.

224
00:16:50,740 --> 00:16:53,610
Now that is left left most child of right subtree.

225
00:16:54,500 --> 00:17:00,200
NetSol, I finish all the function, I have just typed all of them and without taking any errors, if

226
00:17:00,230 --> 00:17:02,620
we compile if there are any errors, I remove them.

227
00:17:05,880 --> 00:17:06,660
There is another.

228
00:17:10,290 --> 00:17:18,619
Yes, end of the function, yes, inside this recursive delete at the end, I should return be.

229
00:17:19,550 --> 00:17:23,750
Written as same as what I was doing in insert function.

230
00:17:25,089 --> 00:17:26,000
Let us run this.

231
00:17:26,020 --> 00:17:29,090
It should run at her feet, then we will use the lead function.

232
00:17:29,710 --> 00:17:33,080
Yes, it's working perfectly normal that it's called lead function.

233
00:17:33,370 --> 00:17:36,060
So from these notes, I will delete one element.

234
00:17:36,370 --> 00:17:39,860
So before performing in order traversal, let us call delete function.

235
00:17:39,880 --> 00:17:45,220
I will call function Ima's delete, delete and we should pass through.

236
00:17:45,670 --> 00:17:47,730
And the key that I want to delete is 20.

237
00:17:47,740 --> 00:17:49,210
So 20 should be deleted from.

238
00:17:50,570 --> 00:17:52,970
Binary St. Run.

239
00:17:54,990 --> 00:17:57,810
Yes, five, eight and then 30.

240
00:17:59,710 --> 00:18:00,430
It's working.

241
00:18:02,330 --> 00:18:04,190
Let us delete Rudek Sulston.

242
00:18:07,660 --> 00:18:15,970
Five, eight, 20, 30, let us create this extreme case that is 50, then 40, 20 and 30.

243
00:18:15,970 --> 00:18:18,880
If I saw them in this order, I'll get the structure.

244
00:18:19,140 --> 00:18:23,290
Then we will delete 50 through the safety that is left.

245
00:18:23,290 --> 00:18:25,450
Charleston and right.

246
00:18:25,450 --> 00:18:27,280
Charlie is 40, then left.

247
00:18:27,280 --> 00:18:31,740
Charlie is 20 and right, Charlie 30 and we will delete 50.

248
00:18:31,750 --> 00:18:33,940
Let us check whether it will delete 50 or not.

249
00:18:36,700 --> 00:18:41,560
Yes, delete it if I delete any other element, like 30, I want to delete.

250
00:18:43,520 --> 00:18:46,010
Your study is deleted, you can check your results here.

251
00:18:47,230 --> 00:18:47,610
Right.

252
00:18:48,550 --> 00:18:55,030
So today is deleted, so it's working perfect, so that's all these programs are little lendee and you

253
00:18:55,030 --> 00:19:01,870
have to work carefully and whenever you require, you can do some paperwork also to check how your results

254
00:19:01,870 --> 00:19:02,380
are working.

255
00:19:02,620 --> 00:19:07,990
And debugging the program is more important if you're getting any errors and always you have the solution

256
00:19:07,990 --> 00:19:09,880
available as part of the program.

257
00:19:09,880 --> 00:19:15,090
So you can download that PDF and you can compare your program that the PDF.

258
00:19:16,660 --> 00:19:17,710
That's all in this video.

