1
00:00:00,780 --> 00:00:06,550
Now, let us check with the selection as adaptive and stable or not.

2
00:00:06,660 --> 00:00:10,220
So first we will look at adaptive adaptive means.

3
00:00:10,230 --> 00:00:15,260
If the list is already sorted, then the algorithm should take a minimum time.

4
00:00:15,720 --> 00:00:17,820
I have taken all the target list.

5
00:00:18,970 --> 00:00:26,440
Now, what is the procedure of selection thought it will select the first position and find out an element.

6
00:00:27,650 --> 00:00:32,810
So what is the smallest element that seem so if I use a K and the G.

7
00:00:32,810 --> 00:00:38,450
G will be going on checking all the elements but remains there only because no element is smaller than

8
00:00:38,900 --> 00:00:39,290
two.

9
00:00:40,100 --> 00:00:42,150
So then it will interchange with itself.

10
00:00:42,620 --> 00:00:46,350
Is there any way to find out that list is already sorted?

11
00:00:46,700 --> 00:00:53,060
Is there any way if I say that if the element is swapping with itself, it's already sorted.

12
00:00:53,780 --> 00:00:56,020
So I suppose this is not sorted.

13
00:00:56,360 --> 00:00:57,620
This is fine.

14
00:00:58,050 --> 00:01:02,990
Then if that is getting so, I figured since then how I can say that the rest of the elements are already

15
00:01:02,990 --> 00:01:03,400
sorted.

16
00:01:03,860 --> 00:01:10,150
We cannot judge sealed bubble sort of identify that no swaptions it for the socket.

17
00:01:10,670 --> 00:01:16,820
So that same logic we cannot apply here and even we don't find any method to check whether it is for

18
00:01:16,820 --> 00:01:17,410
that or not.

19
00:01:18,020 --> 00:01:20,940
So selection does not add up.

20
00:01:20,980 --> 00:01:28,700
If it's not adopted, if the list is sorted or not sorted, it's going to take always and in square

21
00:01:28,700 --> 00:01:32,780
time then second one, whether it is stable or not.

22
00:01:32,930 --> 00:01:35,930
So for stable example, I have taken a list.

23
00:01:36,290 --> 00:01:43,760
I have duplicate values eight and one moderate forces blackgate next year and at eight, let us perform

24
00:01:43,760 --> 00:01:46,130
one path of selection thought.

25
00:01:46,700 --> 00:01:51,320
We select this as an index, then we mark a key also here.

26
00:01:51,530 --> 00:01:57,200
Then we search and find out the smallest element and whatever the smallest element is found, K will

27
00:01:57,200 --> 00:01:58,040
be pointing here.

28
00:01:58,070 --> 00:02:00,740
So here we have a smallest element in the entire list.

29
00:02:01,130 --> 00:02:02,000
That is two.

30
00:02:02,180 --> 00:02:03,650
For once the element is found.

31
00:02:03,650 --> 00:02:04,880
It is in the change.

32
00:02:05,210 --> 00:02:06,470
Now let us see the list.

33
00:02:07,460 --> 00:02:12,860
The store will come here, then three, then five, then red color eight.

34
00:02:14,770 --> 00:02:18,320
Then four, then black, then seven.

35
00:02:18,760 --> 00:02:22,070
Now you can see that this aid is first and the city's next.

36
00:02:22,420 --> 00:02:29,390
So this black was first, then was red nonreaders first, red is first, then black is next.

37
00:02:30,250 --> 00:02:32,170
So they have changed their order.

38
00:02:32,620 --> 00:02:34,380
So original order is lost.

39
00:02:35,130 --> 00:02:38,140
Just one example I have shown you in this example, it is lost.

40
00:02:38,590 --> 00:02:42,480
So we cannot trust selection thought to be stable.

41
00:02:42,820 --> 00:02:44,610
So yes, it is not stable.

42
00:02:46,210 --> 00:02:52,270
I do not preserve the order, so that's all our selection for two strong points of selection.

43
00:02:52,960 --> 00:03:00,630
First one is it performs minimum number of swaps and second is the intermediate reserves of selection.

44
00:03:00,650 --> 00:03:01,450
So it gives.

45
00:03:02,230 --> 00:03:08,200
Youthful researchers that escape as gives gaze smallest elements.

