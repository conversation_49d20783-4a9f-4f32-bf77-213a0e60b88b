1
00:00:00,420 --> 00:00:06,000
So let us write a menu driven program for the markets.

2
00:00:06,060 --> 00:00:11,760
So first I will show a program for one of the markets, then I will show you how changes can be made

3
00:00:11,760 --> 00:00:15,420
in the same program for making a program for other markets.

4
00:00:16,530 --> 00:00:20,740
So what my program is going to do is it's a menu driven program.

5
00:00:20,760 --> 00:00:23,630
So first option is creating a matics.

6
00:00:24,480 --> 00:00:30,750
Second one is getting a particular element of a given index, then set aside for changing an element

7
00:00:30,760 --> 00:00:34,180
of a given index, then displaying that metrics.

8
00:00:34,200 --> 00:00:37,740
So let us write the program first for diagonal metrics.

9
00:00:37,740 --> 00:00:40,270
So I will write a complete program for diagonal metrics.

10
00:00:40,590 --> 00:00:42,930
So here is the starting point.

11
00:00:43,200 --> 00:00:44,550
That is main function.

12
00:00:45,560 --> 00:00:47,040
What are the variables I need?

13
00:00:47,230 --> 00:00:49,590
So initially I will take serializable later on.

14
00:00:49,590 --> 00:00:51,330
If required, I will try to motivate him.

15
00:00:51,870 --> 00:00:56,840
So first of all, I need an area for storing the elements.

16
00:00:56,850 --> 00:01:05,069
So for that I will take a pointer integer, start a because I don't know how many elements are there.

17
00:01:05,099 --> 00:01:08,390
So I think the dimension then I will create an array dynamically.

18
00:01:08,400 --> 00:01:15,140
So I'm picking a pointer then and is the dimension so that this is a square matrix.

19
00:01:15,150 --> 00:01:22,260
So we need entries and so just one value and is sufficient then for choice I may need some variable

20
00:01:22,410 --> 00:01:26,070
C for choice digging choice one, two or three or four.

21
00:01:28,200 --> 00:01:34,530
Then I need some variable maybe for reading the volume, so I'll take one more extra variable X if at

22
00:01:34,530 --> 00:01:41,460
all required, the first thing program should ask what is the dimension and create an array.

23
00:01:42,330 --> 00:01:45,780
So let us write a message print.

24
00:01:47,220 --> 00:01:47,870
Enter.

25
00:01:49,600 --> 00:01:58,540
Dimension and a.k.a. the dimension in an ampersand, and so here we go, the dimension.

26
00:01:59,410 --> 00:02:09,789
Next thing, I should create an array in a heap and assigned to a so a sign how to create an array heap

27
00:02:10,150 --> 00:02:12,340
Mallott function Minnoch.

28
00:02:12,940 --> 00:02:18,100
So what should be the size of an array size of an array for a diagonal maddux's.

29
00:02:18,430 --> 00:02:25,690
And whatever the dimension is that many elements are there, so any elements are there only and elements

30
00:02:25,690 --> 00:02:35,960
in a diagonal so and into size of integer because this is an integer array so size of eight.

31
00:02:36,400 --> 00:02:40,030
So this may be a two bit integer or forward integer.

32
00:02:40,030 --> 00:02:48,520
Whatever the size of an integer is, that size is taken and I should typecast the pointer of type int.

33
00:02:49,830 --> 00:02:58,180
So this will create a rift in heap for this size, and so using Mellark, we can look at the memory

34
00:02:58,250 --> 00:02:58,860
in a heap.

35
00:02:59,670 --> 00:03:05,970
This is an easy language, but in C++, a simple operator is given that is new for creating an array

36
00:03:05,970 --> 00:03:13,390
and heap like we can simply say, ursine new indigeneity offices.

37
00:03:13,410 --> 00:03:20,150
And so instead of this little syntax, we can look at hape memory like this.

38
00:03:20,580 --> 00:03:25,290
So next time onwards, I may not be using log function for creating anything in the heap.

39
00:03:25,290 --> 00:03:26,530
I'll be using new.

40
00:03:26,580 --> 00:03:27,810
This is simple though.

41
00:03:27,810 --> 00:03:32,670
While writing the program I will be using my log function, but for explanation on board, I will be

42
00:03:32,670 --> 00:03:40,420
writing new nonexisting programs for display aminul and that Menal should be repeatedly available.

43
00:03:40,710 --> 00:03:44,460
So let us write on this whole code in a loop that is do while loop.

44
00:03:44,730 --> 00:03:46,780
In the beginning it should display the minimum.

45
00:03:46,800 --> 00:03:51,720
So I will say display Menom so using can display.

46
00:03:52,020 --> 00:03:55,170
So five to six statements are required for displaying them.

47
00:03:55,170 --> 00:03:56,790
And so I'm not writing that code.

48
00:03:58,240 --> 00:04:04,750
Then after taking the menu option, the menu option, maybe one toward anything so that can be processed,

49
00:04:04,750 --> 00:04:06,550
in which case switch.

50
00:04:07,620 --> 00:04:12,550
Based on the choice that is given the choice, whatever the choice supports you are taking in S.H.,

51
00:04:13,020 --> 00:04:21,990
then if case one, case one is created and so creating an arraignment's, I should take all the elements

52
00:04:21,990 --> 00:04:24,000
and fill those elements and then only.

53
00:04:24,090 --> 00:04:32,010
So if I show you diagrammatically we have a pointer a and to which an array is created and attached

54
00:04:32,010 --> 00:04:34,190
to it, suppose that I mentioned is five.

55
00:04:34,200 --> 00:04:38,480
So we have an array of a size five hold by appointer.

56
00:04:38,850 --> 00:04:43,200
So this array has to be filled so it doesn't matter how many elements.

57
00:04:43,200 --> 00:04:45,350
Are there just any elements out there.

58
00:04:45,600 --> 00:04:49,860
So I have to take all the elements one by one and fill them in.

59
00:04:51,120 --> 00:04:53,640
So shall we take those two elements also?

60
00:04:53,730 --> 00:04:54,900
Only non-zero elements.

61
00:04:55,060 --> 00:04:59,840
We should take only non-zero elements and it is obvious that we have to enter them at all.

62
00:04:59,880 --> 00:05:01,830
Wirral means all the elements and their diagonal.

63
00:05:02,010 --> 00:05:03,090
That is the first element.

64
00:05:03,090 --> 00:05:03,840
The next element.

65
00:05:03,840 --> 00:05:04,860
The next, the next.

66
00:05:04,890 --> 00:05:10,110
So in the same order, it should be said so for simply reading and values.

67
00:05:10,110 --> 00:05:14,280
So I will take a starting from one I is less than equal to.

68
00:05:14,280 --> 00:05:21,030
And I see in the mattresses we are saying that this is all starting from one onwards.

69
00:05:21,030 --> 00:05:23,440
So that's why I'm taking from one to end.

70
00:05:23,460 --> 00:05:29,790
So then this is going to be one two five zero numbers will be one to five column number three from one

71
00:05:29,790 --> 00:05:30,230
to five.

72
00:05:30,570 --> 00:05:33,890
But the other and this is just from zero onwards anyway.

73
00:05:34,170 --> 00:05:36,360
We know this formula that is minus one.

74
00:05:36,360 --> 00:05:46,330
Did we have to Sivak so we can say ganef percentile D ampersand E of i.e. minus one.

75
00:05:47,490 --> 00:05:51,580
So the first element will be filled in this one, the next rule here, next from here.

76
00:05:51,600 --> 00:05:53,480
So all these elements will be here.

77
00:05:53,730 --> 00:05:59,210
So it will be reading five elements and storing them in a single dimensionality of site five.

78
00:06:00,180 --> 00:06:02,410
This is for creating an array.

79
00:06:03,270 --> 00:06:04,920
I did not write any message here.

80
00:06:04,920 --> 00:06:09,930
I can write a message into the elements that overall or I can say enter all the elements in a diagonal

81
00:06:09,930 --> 00:06:12,520
one by one, then I can read all these ones.

82
00:06:12,960 --> 00:06:16,330
So this is Fuscus that is taking the values.

83
00:06:16,620 --> 00:06:22,310
No second case, getting a value from a given index.

84
00:06:22,440 --> 00:06:25,050
So here I can say print F.

85
00:06:26,980 --> 00:06:27,690
Enter.

86
00:06:28,650 --> 00:06:34,380
And this is then Percentile Tildy.

87
00:06:35,580 --> 00:06:47,400
Personally, I should read I am g a wrong number and column number, but I should read that after reading

88
00:06:47,400 --> 00:06:54,950
this one, if I is equal to G men for this, there is a non-zero element available.

89
00:06:55,530 --> 00:07:03,120
So in the same line I will say printf percentile D which element I should print.

90
00:07:03,210 --> 00:07:07,500
I should build an element for my minus one that said.

91
00:07:09,240 --> 00:07:11,770
Else that element is zero.

92
00:07:11,790 --> 00:07:20,670
So I will print just zero, so whatever the hell you want to retrieve from that two dimensional matrix

93
00:07:20,880 --> 00:07:25,650
you can mention in this, if it is a diagonal index, then we get a non-zero elements.

94
00:07:25,650 --> 00:07:29,730
If it is not a diagonal index, then we will get the elements equal.

95
00:07:30,420 --> 00:07:31,480
Two cases are over.

96
00:07:32,730 --> 00:07:35,040
There is more space cell continuum here.

97
00:07:35,490 --> 00:07:40,340
Case three, that is four Forcett setting an element at the given index.

98
00:07:40,350 --> 00:07:43,740
So I have to take three things wrong.

99
00:07:43,740 --> 00:07:46,890
Number, column number and element also.

100
00:07:47,250 --> 00:07:49,500
So printf.

101
00:07:51,560 --> 00:07:51,920
And.

102
00:07:54,180 --> 00:07:54,780
Raul.

103
00:07:56,070 --> 00:08:02,110
Column, an element I will read three things Scholem's.

104
00:08:03,790 --> 00:08:12,970
Percentile, the percentile, percentile ampersand, I am percent G ampersand X, so I get it in three

105
00:08:13,000 --> 00:08:14,740
values then.

106
00:08:16,180 --> 00:08:21,310
If the indices are for diagonal, then you change that, otherwise don't do anything.

107
00:08:21,640 --> 00:08:28,690
So here I would write on the condition if I is equal to G, then in the same line I will continue to

108
00:08:28,960 --> 00:08:33,610
set the element that I minus one as the given Element X..

109
00:08:34,480 --> 00:08:36,850
So suppose they want to change this element of this element.

110
00:08:36,860 --> 00:08:41,230
So whatever the element is already present, they're suppose six was there and I want to change it to

111
00:08:41,230 --> 00:08:42,429
10 so you can change it.

112
00:08:43,390 --> 00:08:45,840
So this is a rule one, rule two to three.

113
00:08:46,630 --> 00:08:47,920
So I have to give it all three.

114
00:08:48,160 --> 00:08:49,880
So it is a minus one.

115
00:08:49,900 --> 00:08:51,280
So attitude will change.

116
00:08:53,400 --> 00:09:01,890
This is for K Street, then break, then the last option display displaying the complete diagonal marks,

117
00:09:02,280 --> 00:09:05,830
I want to display it as a mattocks only that is two dimensional.

118
00:09:06,300 --> 00:09:07,890
So far, two dimensional.

119
00:09:07,890 --> 00:09:12,030
I have to use a two nested for loops, so let us write.

120
00:09:12,840 --> 00:09:18,690
Case for the first loop four, I assign one.

121
00:09:19,940 --> 00:09:24,020
I is less than equal to and I placeless.

122
00:09:24,990 --> 00:09:26,520
And for.

123
00:09:28,420 --> 00:09:35,980
Assign one G is less than or equal to n g plus plus.

124
00:09:37,650 --> 00:09:38,540
So I have to follow.

125
00:09:40,990 --> 00:09:48,270
Then inside this, I should build an element only for the Degnan element, otherwise I should print

126
00:09:48,280 --> 00:09:48,740
a zero.

127
00:09:49,480 --> 00:09:51,980
So here I will write on the code for that one.

128
00:09:52,840 --> 00:09:58,450
So I have written a complete code for displaying displaying an element if I is equal to G, otherwise

129
00:09:58,450 --> 00:09:59,430
I'm zero.

130
00:09:59,950 --> 00:10:07,390
Then after I read all this, is that the new line then the next thing is I have started with to do then

131
00:10:07,390 --> 00:10:11,120
I should close it here with the vial so there is no space.

132
00:10:11,140 --> 00:10:13,740
So we will see this when we write the program.

133
00:10:14,080 --> 00:10:19,450
So here we are getting the idea how we should write the program explanation of a program I'm doing here.

134
00:10:19,480 --> 00:10:22,570
So this is for diagonal mattocks.

135
00:10:22,930 --> 00:10:24,100
So the diagonal matrix.

136
00:10:24,100 --> 00:10:32,920
We have created an array and reading an array and getting the value and setting the value and printing

137
00:10:32,920 --> 00:10:33,910
up Max.

138
00:10:34,780 --> 00:10:38,560
Now we have finished four diagonal matrix.

139
00:10:39,640 --> 00:10:42,110
What about lower triangular marks?

140
00:10:42,130 --> 00:10:47,770
We have different formulas for Romijn representation in column representation, different formulas out

141
00:10:47,770 --> 00:10:48,010
there.

142
00:10:49,420 --> 00:10:51,580
Now the program will be similar only.

143
00:10:51,790 --> 00:10:59,740
We want to create an array of required size and then program will be having these four options, creating

144
00:10:59,740 --> 00:11:06,690
an array and filling the elements, then getting elements, adding an element and displaying a matrix.

145
00:11:06,880 --> 00:11:08,270
So same options.

146
00:11:08,290 --> 00:11:11,380
I want to provide nofollow triangular also.

147
00:11:11,410 --> 00:11:14,350
I want to provide same options in the program.

148
00:11:14,350 --> 00:11:18,580
So the working of a program will be the same, but it will be for lower triangle of Mattocks.

149
00:11:19,670 --> 00:11:22,690
So in the triangle of markets, there are two formulas from a different color.

150
00:11:23,060 --> 00:11:26,850
So if you take Romeijn formula, then here the formula will change.

151
00:11:27,380 --> 00:11:32,060
So in the same program as we have already seen the program, what are the changes I should make in this

152
00:11:32,060 --> 00:11:38,670
one so that it becomes a program for lower triangular aromatics using ROMELIO formula.

153
00:11:39,380 --> 00:11:43,490
So I'll simply make the changes in the program main function as it is.

154
00:11:43,490 --> 00:11:49,460
And also I was using variables like G, so I will declare them now that I mentioned.

155
00:11:49,460 --> 00:11:55,120
And for a lower triangular matrix, how many non-zero elements will be there?

156
00:11:55,790 --> 00:12:00,740
If you remember, it will contain and to an equal number of elements.

157
00:12:01,100 --> 00:12:05,170
So the size of an array which is created and remove this one.

158
00:12:05,300 --> 00:12:18,640
And I will write a C++ code here, new end of size and in the end, plus one by two, that's fine.

159
00:12:19,430 --> 00:12:21,950
So the sizes, different sizes different.

160
00:12:21,950 --> 00:12:28,220
That is an interesting way to then hear a menu is displayed on the switch case.

161
00:12:28,220 --> 00:12:32,570
First choice, creating a arraignment's entering all the elements.

162
00:12:32,660 --> 00:12:38,240
So after creating an array that inside the middle here, I have to read the elements.

163
00:12:39,140 --> 00:12:45,200
So for reading the elements, I can read all the elements of zero as well as non-zero and stored only

164
00:12:45,200 --> 00:12:46,160
non-zero elements.

165
00:12:46,170 --> 00:12:48,210
So for that I have to write on the code.

166
00:12:48,500 --> 00:12:50,710
So this I'll be showing while writing the program.

167
00:12:50,780 --> 00:12:53,630
So I have to write two for loops and take the elements.

168
00:12:53,630 --> 00:12:58,640
If the element is non-zero, then using the formula I should store the element and then for the zero

169
00:12:58,640 --> 00:13:06,230
interest I should skip it, then get the function, get I want to get the element so that I can give

170
00:13:06,230 --> 00:13:14,050
an element if I is greater than or equal to G then at this lower triangular element that is non-zero

171
00:13:14,060 --> 00:13:17,990
element if I use it and then we have an element.

172
00:13:18,000 --> 00:13:27,830
So from where I should take an element, I should take an element from AI into AI minus one by two plus

173
00:13:28,040 --> 00:13:29,240
G minus one.

174
00:13:31,000 --> 00:13:37,150
I'll remove this one, so from this place that is I'm using the formula by using the formula, we can

175
00:13:37,150 --> 00:13:40,180
retrieve an element, get an element from a given index.

176
00:13:40,610 --> 00:13:44,440
Then if you want to change an element at the given index, then this is Forcett.

177
00:13:44,830 --> 00:13:51,490
Then here also if I is critical and then from there I should change the value, so I should change the

178
00:13:51,490 --> 00:13:51,820
value.

179
00:13:51,820 --> 00:13:55,360
Add I into am minus one by two.

180
00:13:56,510 --> 00:14:00,410
Plus minus one, a sign X.

181
00:14:01,390 --> 00:14:07,580
That's all I can change the venue then for display whenever I is greater than equal to J.

182
00:14:07,600 --> 00:14:15,160
We will get an element and again here the formula will be I into I minus one by two.

183
00:14:16,350 --> 00:14:26,970
Plus minus one, that same formula, so you can see that by changing by changing the formulas at few

184
00:14:26,970 --> 00:14:31,820
places, the program changes for lower triangular metrics.

185
00:14:32,130 --> 00:14:39,420
So I have shown only for Romeijn the same way we can implement for column measure and also upper angular

186
00:14:39,450 --> 00:14:41,190
and two methods.

187
00:14:41,220 --> 00:14:47,040
And finally, for symmetric metrics and other matters, assault's try diagonal and problematic.

188
00:14:47,050 --> 00:14:50,350
So for all the Matisses, we will follow the same approach.

189
00:14:50,370 --> 00:14:56,760
So I have given you the idea how our program should work, and based on the same idea, I will be writing

190
00:14:56,760 --> 00:14:57,300
the program.

