1
00:00:00,150 --> 00:00:06,320
In this video, we look at a demonstration for a function which removes duplicate elements from a socket

2
00:00:06,380 --> 00:00:06,980
linguist.

3
00:00:06,990 --> 00:00:13,410
So here in the main function already I have was created for these seven elements and the elements are

4
00:00:13,410 --> 00:00:16,440
sorted and the value is duplicated.

5
00:00:16,470 --> 00:00:22,120
Let us write a function which we have already discussed on whiteboard and try on this link list.

6
00:00:22,140 --> 00:00:28,050
So here I will write on a function function name is remove duplicate and it should take a pointer to

7
00:00:28,050 --> 00:00:28,840
Fassnacht.

8
00:00:28,860 --> 00:00:29,700
So let us call.

9
00:00:29,700 --> 00:00:33,360
It must be as a point to go first Fastenal then inside the function.

10
00:00:33,370 --> 00:00:34,500
We need one more point.

11
00:00:34,710 --> 00:00:39,840
That is skill which will be before B, so let us take a cue and that is on piece next.

12
00:00:39,870 --> 00:00:46,890
So in this function we have taken P as a tail pointer for Q so Q is ahead and B is following.

13
00:00:46,890 --> 00:00:49,680
Q Now we have to scan through a linguist using pointer.

14
00:00:49,680 --> 00:00:57,090
Q So while Q is not equal to null, and every time we check if data is matching BS and Q data is matching,

15
00:00:57,090 --> 00:01:01,350
if BP's data is not equal to Qs, data means they are not the same.

16
00:01:01,680 --> 00:01:03,150
It means there is no duplicate.

17
00:01:03,150 --> 00:01:04,590
Just we should move ahead.

18
00:01:04,590 --> 00:01:08,910
So B should come up on Q and Q should move to the next node.

19
00:01:08,910 --> 00:01:10,010
That's what we have written.

20
00:01:10,020 --> 00:01:11,360
We have seen it on the whiteboard.

21
00:01:11,370 --> 00:01:15,060
Ls it means they are equal so not Q should be deleted.

22
00:01:15,390 --> 00:01:19,020
So ps next should point on Qs next.

23
00:01:19,020 --> 00:01:24,500
So Q will be logically removed from the link list, then we can free another, that is delete A..

24
00:01:24,540 --> 00:01:29,670
So in C language we have to use a free as a function instead of delete keyword.

25
00:01:29,670 --> 00:01:34,550
So delete an old Q and make a Q point on next node off beat that's all.

26
00:01:34,560 --> 00:01:35,850
So there's a simple function.

27
00:01:35,880 --> 00:01:37,770
Let us run it up on this link.

28
00:01:37,980 --> 00:01:45,240
So I will call a function here, remove duplicate upon first node of a link list and after that I will

29
00:01:45,240 --> 00:01:52,590
also display a link list by passing pointer first and also I will give a line gap after printing.

30
00:01:52,620 --> 00:01:54,120
Yes, let us run.

31
00:01:54,120 --> 00:01:55,460
The study should be removed.

32
00:01:55,500 --> 00:01:59,910
Yesterday's removal it was three times so it is taking only one time.

33
00:01:59,910 --> 00:02:02,130
So 10, 20, 30, 40 and 50.

34
00:02:02,160 --> 00:02:03,480
Let us try it once more.

35
00:02:03,810 --> 00:02:05,160
I will add more.

36
00:02:05,160 --> 00:02:07,200
Forty two more for this I will insert.

37
00:02:07,200 --> 00:02:09,330
So now total number of elements are nine.

38
00:02:09,330 --> 00:02:13,310
So it should delete Pándy as well as forty years for are also removed.

39
00:02:13,320 --> 00:02:14,580
Let us try it once more.

40
00:02:14,580 --> 00:02:16,200
I will have initial ten.

41
00:02:16,200 --> 00:02:20,510
So if the duplicates are in the beginning itself now I have 11 elements.

42
00:02:20,520 --> 00:02:22,290
Yes it is removing ten also.

43
00:02:22,290 --> 00:02:22,860
Perfect.

44
00:02:22,860 --> 00:02:24,030
I'll do one more test.

45
00:02:24,030 --> 00:02:25,560
I'll remove all these elements.

46
00:02:25,560 --> 00:02:27,450
Only I will have three tenths.

47
00:02:27,660 --> 00:02:29,820
So it should take just one tenth.

48
00:02:29,850 --> 00:02:35,100
Yes, it's working just one tent as their display is spending just one element.

49
00:02:35,550 --> 00:02:37,020
So that's all.

50
00:02:37,020 --> 00:02:40,350
The function is perfectly working in all possible cases.

51
00:02:40,350 --> 00:02:41,010
So that's all.

52
00:02:41,010 --> 00:02:42,870
You can try this function by yourself.

53
00:02:42,870 --> 00:02:45,720
You can write this program by yourself and practice this one.

