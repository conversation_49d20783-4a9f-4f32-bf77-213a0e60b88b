1
00:00:00,180 --> 00:00:07,710
Let us look at addition, offer two spots, mattresses in coordinate list representation.

2
00:00:08,780 --> 00:00:16,820
Suppose I have spiked my trousers like a bee and I want to add these two and get the residency.

3
00:00:18,160 --> 00:00:19,210
So this is an.

4
00:00:20,650 --> 00:00:26,440
Mathematical form, but whether it is represented in the program in the form of a coordinate list or

5
00:00:26,440 --> 00:00:33,130
three column method, whatever it is for that, how we perform operations like addition operation,

6
00:00:33,140 --> 00:00:33,670
we will see.

7
00:00:34,360 --> 00:00:37,330
So first of all, I will solve it manually.

8
00:00:37,360 --> 00:00:38,690
That is using pen and paper.

9
00:00:38,740 --> 00:00:43,330
Think that using pen and paper, how I can add these two and what should be the result.

10
00:00:43,750 --> 00:00:44,600
So let us do it.

11
00:00:45,340 --> 00:00:51,810
The dimension of <PERSON><PERSON><PERSON>'s five, six, seven matrix is also five by six.

12
00:00:52,150 --> 00:00:56,540
So yes, they can be added so for adding to their dimensions must be seen.

13
00:00:57,180 --> 00:01:00,550
Then the third man thinks I have already taken Handyside five by six.

14
00:01:01,700 --> 00:01:08,600
Now, what a non-zero elements in and first Matrix six, seven, two, five and four and Annunzio elements

15
00:01:08,600 --> 00:01:11,420
are three five to seven, nine, eight.

16
00:01:13,010 --> 00:01:20,540
Now, a normal procedure for adding to this, the same method that is every element of is ordered with

17
00:01:20,540 --> 00:01:22,890
the corresponding element of being mattocks.

18
00:01:23,330 --> 00:01:24,380
So let us add them.

19
00:01:25,220 --> 00:01:33,650
So let us start with first one first element, this one common one one one zero zero zero zero, then

20
00:01:33,650 --> 00:01:37,010
one common to that one common to this is also zero.

21
00:01:37,910 --> 00:01:38,960
So I will do the rest.

22
00:01:39,200 --> 00:01:41,440
This is zero and zero.

23
00:01:41,450 --> 00:01:42,250
So this is zero.

24
00:01:42,620 --> 00:01:44,500
There's a six and here to zero.

25
00:01:44,690 --> 00:01:46,670
So six plus zero zero.

26
00:01:46,940 --> 00:01:49,520
Then the remaining two are zero remaining towards zero zero.

27
00:01:50,570 --> 00:01:56,420
Now second row, second row here we have non-zero element, Fuscus zero and Nexxus Mondial and here

28
00:01:56,420 --> 00:01:57,650
we have zero and nonlegal.

29
00:01:57,860 --> 00:01:59,120
So this is zero.

30
00:01:59,330 --> 00:02:02,540
And this corresponding element, they are the same index.

31
00:02:02,540 --> 00:02:08,919
Anji So seven plus three this becomes ten and the next element of zero zero.

32
00:02:08,930 --> 00:02:12,160
And here we have one more zero for which there is zero here.

33
00:02:12,470 --> 00:02:13,660
So it will be just fine.

34
00:02:13,940 --> 00:02:19,970
So zero zero five zero then total total to five.

35
00:02:20,120 --> 00:02:21,810
And here there is two here.

36
00:02:22,220 --> 00:02:30,470
So this is zero zero two plus zero two, only zero plus two to only five plus zero five only then zero,

37
00:02:30,680 --> 00:02:31,340
then seven.

38
00:02:31,820 --> 00:02:39,320
So if I take them first one is zero, then two as it is to us it is five, then zero, then seven,

39
00:02:40,490 --> 00:02:45,290
then four through all the zero zero four zero order with these will get the same thing.

40
00:02:45,290 --> 00:02:53,630
So we'll get the same row zero zero zero nine zero zero and the LASO four and then here eight.

41
00:02:53,750 --> 00:02:59,570
So this is 12 and the rest of all zeros here on Sarastro zero two zero zero.

42
00:03:01,430 --> 00:03:07,700
So this is what the addition of two spots, Matisses and B and I got the result C, so she's obtained

43
00:03:07,700 --> 00:03:11,420
by adding A plus B, how many non-zero elements I got.

44
00:03:11,660 --> 00:03:16,680
One, two, three, four, five, six, seven, eight, nine.

45
00:03:17,390 --> 00:03:19,610
So nine non-zero elements.

46
00:03:19,610 --> 00:03:26,420
I have now let us do the same thing using coordinate list representation.

47
00:03:26,810 --> 00:03:31,790
Let us represent this other coordinate less so in this past matrix.

48
00:03:31,790 --> 00:03:33,890
Let us count the number of non-zero elements.

49
00:03:34,220 --> 00:03:38,180
One, two, three, four, five, five.

50
00:03:38,180 --> 00:03:39,470
Non-zero elements are there.

51
00:03:39,740 --> 00:03:42,260
So I will take a horizontal table.

52
00:03:43,550 --> 00:03:52,850
Of size six, let us call this aid, so it may be a two dimensional three or four times how to implement

53
00:03:52,850 --> 00:03:55,340
it in programming, we will discuss afterwards.

54
00:03:56,930 --> 00:04:03,170
So I at a zero index, I will write on the dimensions, that is five and six columns, five rows and

55
00:04:03,170 --> 00:04:09,140
six columns and the number of elements one, two, three, four, five, five non-zero elements.

56
00:04:10,520 --> 00:04:14,250
Now, let me fill this table with the non-zero elements.

57
00:04:14,390 --> 00:04:23,780
So, first of all, there is an element six at column for first row, fourth column values six second

58
00:04:23,780 --> 00:04:23,960
row.

59
00:04:23,960 --> 00:04:24,740
Second column.

60
00:04:24,740 --> 00:04:26,300
Seven second row.

61
00:04:26,300 --> 00:04:27,680
Second column seven.

62
00:04:29,000 --> 00:04:33,530
Third or fourth column, five, third or fourth, column five.

63
00:04:34,860 --> 00:04:42,990
For truth, there is nothing third or second column to third row, second column to then third or fourth

64
00:04:42,990 --> 00:04:51,060
column, five, third row, fourth column five, then photo, there is nothing fifth row, first column

65
00:04:51,060 --> 00:04:53,340
for fifth row.

66
00:04:53,340 --> 00:04:54,840
First column for.

67
00:04:56,160 --> 00:04:57,700
This is how it is represented.

68
00:04:58,200 --> 00:05:05,850
Now, second one, I will do it quickly so I have a representation for Bemax also, which is having

69
00:05:05,850 --> 00:05:09,390
five by six dimension and six non-zero elements.

70
00:05:09,390 --> 00:05:10,760
And all those elements are here.

71
00:05:11,100 --> 00:05:12,850
Let us see how our mission can be done.

72
00:05:13,470 --> 00:05:17,040
We have to add these two Matisses from this representation.

73
00:05:17,460 --> 00:05:19,560
So we need one more array of coordinates.

74
00:05:19,590 --> 00:05:26,880
That is the same type of for C, but we don't know how many elements it might be getting for what should

75
00:05:26,880 --> 00:05:27,660
be the sites.

76
00:05:28,320 --> 00:05:29,580
C How many elements here.

77
00:05:29,580 --> 00:05:30,120
Five.

78
00:05:30,450 --> 00:05:31,560
How many elements here.

79
00:05:31,560 --> 00:05:33,660
Six total leverne.

80
00:05:34,050 --> 00:05:41,340
If none of them is matching and none of them is added, then at the most leverne elements sodic Wagg.

81
00:05:42,420 --> 00:05:48,160
So I should have space for everyone, but I will not take Leverne we know very well I'm we know.

82
00:05:48,180 --> 00:05:49,970
So how many non-zero elements are there?

83
00:05:49,980 --> 00:05:54,180
One, two, three, four, five, six, seven, eight, nine, four.

84
00:05:54,180 --> 00:05:55,250
Nine is the five.

85
00:05:55,410 --> 00:05:58,260
So I will take the size nine, then I will add it from here.

86
00:05:58,290 --> 00:06:00,920
Now I have a UTI ready here.

87
00:06:01,170 --> 00:06:02,940
Now let us start adding them.

88
00:06:03,150 --> 00:06:05,010
The dimensions are five and six.

89
00:06:05,010 --> 00:06:06,810
I don't know how many elements I may be getting.

90
00:06:06,810 --> 00:06:09,390
Once I get the number of elements, I will update this one.

91
00:06:09,780 --> 00:06:13,590
So let us start adding so far, adding I will go on the first term of this one.

92
00:06:13,590 --> 00:06:17,660
And let's say I and this first term of this one that fidge.

93
00:06:18,060 --> 00:06:19,030
See, this is zero.

94
00:06:19,030 --> 00:06:20,340
What index is having that?

95
00:06:20,340 --> 00:06:21,760
I dimension a number of elements.

96
00:06:22,620 --> 00:06:27,690
Now let's start how to add them first to check the number here and number here.

97
00:06:27,720 --> 00:06:29,250
That one number is a smaller.

98
00:06:29,580 --> 00:06:32,460
So that element comes before this element.

99
00:06:32,910 --> 00:06:34,500
They are not in the same room.

100
00:06:34,800 --> 00:06:37,920
So that first role, one element comes here.

101
00:06:37,980 --> 00:06:40,430
So we'll include that element here directly.

102
00:06:40,470 --> 00:06:41,340
It is included.

103
00:06:41,700 --> 00:06:46,740
So you can see that here we are automatically six six directly came from here.

104
00:06:46,750 --> 00:06:47,400
So same thing.

105
00:06:47,400 --> 00:06:54,690
It's not added with anything then as we have copied this element, move to the next element compared

106
00:06:54,690 --> 00:06:55,320
rules.

107
00:06:55,470 --> 00:06:59,040
Roles are seen compared columns, columns are also seem.

108
00:06:59,250 --> 00:07:00,720
So these are matching.

109
00:07:00,870 --> 00:07:03,000
So add seven plus three.

110
00:07:03,210 --> 00:07:07,590
So rule number is two, column number is two and add them and a move.

111
00:07:07,800 --> 00:07:11,250
I also and move Jeonju.

112
00:07:13,110 --> 00:07:19,980
So this is what we got here because of the seven entry we're matching now, next element rule number

113
00:07:19,980 --> 00:07:23,570
three, rule number two, this element come first.

114
00:07:24,000 --> 00:07:28,380
There is no matching of rules so that we take the smaller roll.

115
00:07:28,380 --> 00:07:31,410
No copy it here, then move to next.

116
00:07:31,410 --> 00:07:32,370
Not on the military.

117
00:07:32,680 --> 00:07:36,380
Rule number three, they are matching column two, column three.

118
00:07:36,540 --> 00:07:38,100
So this element come first.

119
00:07:38,490 --> 00:07:40,700
So they are not matching, but this element come first.

120
00:07:40,710 --> 00:07:42,140
So take this three to two.

121
00:07:42,480 --> 00:07:52,080
So three, two to see the first element and the fourth element, then move to the next element, i.e.

122
00:07:53,430 --> 00:07:56,480
we are here three three run numbers are matching again.

123
00:07:56,850 --> 00:07:58,140
Now check the column number.

124
00:07:58,140 --> 00:08:00,650
Column number here is four and here is three.

125
00:08:00,660 --> 00:08:01,960
So this element come first.

126
00:08:01,980 --> 00:08:06,150
So three three to the second element in that role.

127
00:08:07,650 --> 00:08:09,840
Then move to the next element.

128
00:08:11,310 --> 00:08:13,730
Now compare rule number three, rule number three.

129
00:08:13,740 --> 00:08:16,880
So same rule number, but column number, this element come first.

130
00:08:16,880 --> 00:08:19,830
So three, four, five, four, three, four, five.

131
00:08:20,740 --> 00:08:21,680
Three, four, five.

132
00:08:21,730 --> 00:08:28,030
This one, then, as we have copied this one, move to the next element, rule number five, rule number

133
00:08:28,030 --> 00:08:28,460
three.

134
00:08:28,810 --> 00:08:30,210
So this element smaller.

135
00:08:30,580 --> 00:08:32,059
So this will come as it is.

136
00:08:32,289 --> 00:08:38,490
So three, six or seven, then move next rule five, four.

137
00:08:38,500 --> 00:08:45,910
So this is smaller, four, four, nine, then move next rule matching column also matching or they

138
00:08:45,910 --> 00:08:47,000
are in the same place.

139
00:08:47,290 --> 00:08:48,700
So are these two elements.

140
00:08:48,850 --> 00:08:52,230
So this is five one and four plus eight two.

141
00:08:52,240 --> 00:08:58,690
And so this all we can add by scanning through array and scanning through a and if the rules are matching,

142
00:08:58,690 --> 00:08:59,560
check for the column.

143
00:08:59,560 --> 00:09:05,860
If that is also matching the Nadhum, if at all, is more directly copied, if column is more directly

144
00:09:05,860 --> 00:09:06,230
copied.

145
00:09:06,460 --> 00:09:10,750
So that's all about sports metrics and the operation like addition I have shown you.

146
00:09:10,750 --> 00:09:13,010
So similarly you can do subtraction.

147
00:09:13,240 --> 00:09:15,790
So how it can be done through a program.

148
00:09:16,120 --> 00:09:17,080
I will show you that.

