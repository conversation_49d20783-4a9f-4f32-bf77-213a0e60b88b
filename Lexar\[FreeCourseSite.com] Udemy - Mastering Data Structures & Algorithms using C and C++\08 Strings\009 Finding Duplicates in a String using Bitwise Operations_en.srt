1
00:00:01,180 --> 00:00:07,860
In this video, we will see how to find duplicates in a string if any alphabets are repeating.

2
00:00:07,870 --> 00:00:10,840
So we have already seen the same thing in previous video.

3
00:00:10,850 --> 00:00:15,340
We have learned two methods, not the third method that is using bits.

4
00:00:15,850 --> 00:00:19,720
So in this video, we will learn how we can perform the same thing using bits.

5
00:00:20,260 --> 00:00:26,230
This method is not just for a string, but it is useful for indigenous birds also, but it is more favorable

6
00:00:26,230 --> 00:00:27,900
for a string.

7
00:00:28,150 --> 00:00:30,030
So let us see this method.

8
00:00:30,040 --> 00:00:33,080
So for learning this method, we should have some concepts.

9
00:00:33,550 --> 00:00:35,410
So let us revise those concepts.

10
00:00:35,740 --> 00:00:39,260
Then we will find the duplicate elements in a string.

11
00:00:40,300 --> 00:00:45,520
See, the concept that we should know is bitwise operations, what bitwise operations we should not

12
00:00:46,030 --> 00:00:53,540
left shift and mix hording, which is also called merging and bits, which is also called masking.

13
00:00:54,100 --> 00:00:55,510
So we should know these things.

14
00:00:55,960 --> 00:00:57,830
Then we will see how to find duplicates.

15
00:00:58,150 --> 00:01:01,100
So let me explain you about all these things one by one.

16
00:01:01,600 --> 00:01:05,519
So here is the white C for understanding between the operation.

17
00:01:05,530 --> 00:01:08,810
We should know how the data is stored in the memory in the form of objects.

18
00:01:09,160 --> 00:01:14,950
So for understanding that, I have taken just one byte so it is sufficient to understand by using just

19
00:01:14,950 --> 00:01:18,730
one byte, suppose I have a variable which is taking only one byte.

20
00:01:18,730 --> 00:01:20,190
Let us character type variable.

21
00:01:20,430 --> 00:01:26,890
So I'm calling that variable and it is taking one byte, so one byte as eight bits eight which makes

22
00:01:27,120 --> 00:01:31,810
it so I have Takamine bits and this is called at least significant bit and this is more significant.

23
00:01:31,830 --> 00:01:34,610
But this is a zero usually in an array.

24
00:01:34,630 --> 00:01:39,300
We start indexing from zero one, two, three on word that as we go from left to right, but this is

25
00:01:39,310 --> 00:01:42,190
indexed from zero to seven on this site.

26
00:01:42,430 --> 00:01:43,970
We started from right hand side.

27
00:01:44,350 --> 00:01:48,570
So this is significant and this is most significant.

28
00:01:48,580 --> 00:01:53,930
But now any number is stored as stored in the form of binary zeros and ones.

29
00:01:54,250 --> 00:01:56,600
So for this, you should be doing binary numbers.

30
00:01:56,980 --> 00:01:59,290
I'm not going to explain you binary numbers system.

31
00:02:00,010 --> 00:02:02,370
Now we will understand bitwise operations.

32
00:02:02,650 --> 00:02:08,919
So for that, let us assume this is initially zero, say as zero.

33
00:02:09,160 --> 00:02:15,590
If I declare a variable of type character and assign zero, net zero will be stored for how zero is

34
00:02:15,620 --> 00:02:16,090
stored.

35
00:02:17,600 --> 00:02:19,310
All these will be zeros.

36
00:02:20,900 --> 00:02:22,930
Yes, all these will be zeros.

37
00:02:23,770 --> 00:02:28,540
Then if suppose if I store one then this will be one.

38
00:02:28,780 --> 00:02:32,390
So it looks like in the memory zero zero zero zero one.

39
00:02:32,410 --> 00:02:36,290
If I read it from the left hand side because we usually read from left hand side.

40
00:02:36,610 --> 00:02:39,290
So this is zero zero zero zero zeros the last one.

41
00:02:39,830 --> 00:02:46,900
Then if I store two here, then binary form offered to us one zero.

42
00:02:47,680 --> 00:02:50,550
So this is zero zero zero zero one zero.

43
00:02:50,740 --> 00:02:51,310
Yes.

44
00:02:51,910 --> 00:02:56,570
Then if I store for then the binary format for this one zero zero.

45
00:02:56,830 --> 00:03:00,310
So this will be one zero zero.

46
00:03:01,820 --> 00:03:06,300
If it is five, then the binary form is one, not one, single one, not one.

47
00:03:06,920 --> 00:03:07,880
Let us make it back.

48
00:03:07,890 --> 00:03:10,160
As for now.

49
00:03:10,190 --> 00:03:12,640
Next I will skip six and seven.

50
00:03:12,650 --> 00:03:13,670
Let us take eight.

51
00:03:13,790 --> 00:03:18,170
If it is eight, then binary form of eight is one zero zero three zero.

52
00:03:18,320 --> 00:03:20,180
So this looks like this.

53
00:03:21,880 --> 00:03:28,540
So this means that this minority Brits represent one, and this represents to them the support for Anderson's

54
00:03:28,570 --> 00:03:36,340
eight double, it is getting double every time, right, 16, 32 versus 64 under this 138.

55
00:03:37,440 --> 00:03:41,230
So, yes, this is how the binary value of each digit.

56
00:03:41,850 --> 00:03:50,040
Right, so if suppose we want to store ten, then eight and two, these two will be one one, right.

57
00:03:50,100 --> 00:03:50,970
This becomes ten.

58
00:03:52,440 --> 00:03:53,460
Just one date.

59
00:03:53,680 --> 00:04:01,110
Then this is one and all zeroes, although suppose we want to store 20, the 16 will be one.

60
00:04:01,620 --> 00:04:03,300
So nearest number is 16, right?

61
00:04:03,750 --> 00:04:05,340
It will be greater than Bernie Sanders.

62
00:04:05,340 --> 00:04:07,410
Number 16, then what is remaining?

63
00:04:07,410 --> 00:04:07,880
Four.

64
00:04:08,230 --> 00:04:09,830
So this one.

65
00:04:10,080 --> 00:04:11,280
So these two will be one.

66
00:04:11,280 --> 00:04:18,680
So it will become 20 in binary form by no system like if only this bydesign means 16.

67
00:04:19,140 --> 00:04:25,920
So if you have seen how binary form of a number is stored in the memory right now, next we will see

68
00:04:25,920 --> 00:04:28,100
what does it mean by a shift operation.

69
00:04:28,440 --> 00:04:29,750
So I remove this and explain.

70
00:04:29,760 --> 00:04:33,730
You see right now is having zero zero zero one.

71
00:04:33,750 --> 00:04:35,300
So right now it's one.

72
00:04:36,120 --> 00:04:38,940
OK, now let us see what you mean by shift.

73
00:04:39,720 --> 00:04:45,330
Each left shift one and the resulting edge only.

74
00:04:45,870 --> 00:04:46,160
Right.

75
00:04:46,560 --> 00:04:54,330
So whatever the value of X, C here is one bit or whatever the bits are, whatever it may be, all bits

76
00:04:54,330 --> 00:04:59,010
will shift by one place on the left hand side, if you mention one all this.

77
00:04:59,190 --> 00:05:01,540
So we have all which says only that one is there.

78
00:05:01,710 --> 00:05:03,440
So it moved by one place.

79
00:05:03,660 --> 00:05:06,620
So this one comes here, then this place will be vacant.

80
00:05:06,630 --> 00:05:08,370
No, this will be filled with zero.

81
00:05:08,520 --> 00:05:12,210
If the bits are shifting on this side, then you get some blankets here.

82
00:05:12,210 --> 00:05:15,330
So that will be said as zeros right now.

83
00:05:15,330 --> 00:05:16,180
What is this number?

84
00:05:16,770 --> 00:05:19,130
It has became two, yes.

85
00:05:19,800 --> 00:05:28,290
Then let us put it back this size one only and not if I left shift by two places, edge left shift by

86
00:05:28,290 --> 00:05:28,970
two places.

87
00:05:29,220 --> 00:05:32,130
So this will be shifted by Publicis one two.

88
00:05:32,400 --> 00:05:33,930
So that one comes here.

89
00:05:35,590 --> 00:05:36,470
Then Necesito.

90
00:05:37,640 --> 00:05:39,420
So now how much it is for?

91
00:05:39,710 --> 00:05:41,990
So, as I said, left shift in the weather.

92
00:05:41,990 --> 00:05:48,660
Storen estimates this will modify and it will become for so only this operation will not modify at night.

93
00:05:48,710 --> 00:05:56,610
We have to store it not it means by shifting the number on the left hand side, we are able to increase

94
00:05:56,610 --> 00:06:01,520
that multiples of two and also the digits shifting.

95
00:06:01,970 --> 00:06:02,350
Right.

96
00:06:02,630 --> 00:06:08,480
Suppose here I see edge left shift by five right now at this one here by five.

97
00:06:08,840 --> 00:06:11,130
So one, two, three, four, five.

98
00:06:11,210 --> 00:06:12,170
So it will come here.

99
00:06:12,590 --> 00:06:13,750
So what is the number now?

100
00:06:14,270 --> 00:06:15,500
These are all zeros only.

101
00:06:15,500 --> 00:06:16,080
This is one.

102
00:06:16,310 --> 00:06:17,390
So this is 32.

103
00:06:18,910 --> 00:06:25,540
Right, so we can shift the digits so we can shift all the digits, but we are interested in only one

104
00:06:25,540 --> 00:06:26,490
digit, that is one.

105
00:06:26,770 --> 00:06:29,180
So we are looking at, for example, one.

106
00:06:30,070 --> 00:06:32,700
So I have explained you shifting now.

107
00:06:32,710 --> 00:06:35,920
I will explain you and and Audie.

108
00:06:36,340 --> 00:06:44,230
So first and then I'll explain you oring so far explaining and I have picked an example, I have taken

109
00:06:44,230 --> 00:06:45,280
two variables here.

110
00:06:45,280 --> 00:06:47,890
Two variables, A 10 and B, the six.

111
00:06:48,160 --> 00:06:53,320
So I have taken just a four four digit for those number because these rest of the digits on this side

112
00:06:53,320 --> 00:06:57,430
are zeros only if I write all eight, but also they are all zeros.

113
00:06:57,610 --> 00:06:59,860
So I'm showing only the useful bits.

114
00:06:59,980 --> 00:07:00,270
Right.

115
00:07:00,610 --> 00:07:03,070
So there is a lot forward and these are large forwards.

116
00:07:03,070 --> 00:07:09,180
So it is just like one zero one zero for A and for B it is one one zero.

117
00:07:09,220 --> 00:07:13,120
So I have used this one also because that makes for Forbert from each side.

118
00:07:13,600 --> 00:07:23,440
Now what is and if I see A and B, then Bitzer and zero unsettlement, false and false as a false only

119
00:07:23,860 --> 00:07:24,670
one and one.

120
00:07:25,060 --> 00:07:25,870
True and true.

121
00:07:25,870 --> 00:07:26,110
Yes.

122
00:07:26,300 --> 00:07:36,120
Only so 011 false and true as a false then one and zellman true and false as false.

123
00:07:37,300 --> 00:07:41,130
She may be doing this if you have a B and you are performing.

124
00:07:41,140 --> 00:07:49,880
And so this is one one as one and one zero zero and zero one is also zero zero zero zero zero.

125
00:07:49,900 --> 00:07:53,110
So when you perform an operation then this is the result.

126
00:07:53,260 --> 00:07:59,320
So what we are performing not logical and we are performing bitwise and single Lamberson.

127
00:07:59,590 --> 00:08:03,940
So the ending will be done up on the bits of numbers.

128
00:08:04,970 --> 00:08:06,120
Bricks in the memory.

129
00:08:06,410 --> 00:08:09,760
So what is the result we got this is a binary form, one, two.

130
00:08:09,980 --> 00:08:13,940
So it means 10 and six is to.

131
00:08:15,060 --> 00:08:18,660
Ten and six is two, so this is an operation.

132
00:08:19,330 --> 00:08:23,340
Next, I'll show you our operation, then we'll move ahead.

133
00:08:23,790 --> 00:08:27,540
Let us see our operation, see same number and remove this.

134
00:08:27,900 --> 00:08:36,059
Now, instead of ending, we are performing our operations on the then Armin's what, zero zero zero.

135
00:08:36,059 --> 00:08:42,659
Only one on one one zero zero one is also one one or zero is also one.

136
00:08:43,200 --> 00:08:44,640
That how much this is.

137
00:08:44,910 --> 00:08:53,830
This is 14 in the decimal number 180 formatters one one one zero and it is 14 and decimal system.

138
00:08:54,240 --> 00:08:58,560
See this is audin oring means what one on one.

139
00:08:58,560 --> 00:09:03,730
This one one zero is also one and zero one is also one zero zero zero.

140
00:09:04,050 --> 00:09:06,650
So if any one of the buttons one then it is one.

141
00:09:06,840 --> 00:09:13,580
So therefore you have perform here and ten or six is 14.

142
00:09:14,070 --> 00:09:15,210
So this is interesting.

143
00:09:15,540 --> 00:09:16,860
Bitwise operations.

144
00:09:18,070 --> 00:09:23,550
Now, let us understand, what does it mean by merging and masking this, I have explained you and then

145
00:09:23,560 --> 00:09:30,640
our operations now merging and masking, I will explain, you know, let us understand masking for explaining,

146
00:09:30,640 --> 00:09:31,150
masking.

147
00:09:31,150 --> 00:09:36,490
I have taken two variables, such as having all zeros except this one.

148
00:09:36,490 --> 00:09:38,630
So the value inside that is 16.

149
00:09:38,950 --> 00:09:40,550
So there's one byte long, right.

150
00:09:40,840 --> 00:09:45,730
So Etchells having rather 16, then there is one more variable A and it is having zero.

151
00:09:46,480 --> 00:09:48,190
Now, what does it mean by masking?

152
00:09:48,710 --> 00:09:57,760
See, I want to know whether out of this place that is better to zero one to right at this, but is

153
00:09:57,760 --> 00:09:59,720
it one or zero?

154
00:09:59,740 --> 00:10:00,850
I want to find out at.

155
00:10:01,750 --> 00:10:02,120
Right.

156
00:10:02,350 --> 00:10:03,970
How to find out inside.

157
00:10:04,270 --> 00:10:11,120
I want to know whether this is on or not, that 012 second or if I count one, two, three, Turbit

158
00:10:11,230 --> 00:10:12,870
Soliris called us two.

159
00:10:13,120 --> 00:10:17,060
I want to know whether that vehicle was on or not.

160
00:10:17,500 --> 00:10:18,480
So how to know that?

161
00:10:18,670 --> 00:10:22,490
So I will take the help of a source inside a I will write on one.

162
00:10:22,990 --> 00:10:27,310
So there's this ursine one that I want to check this one.

163
00:10:27,310 --> 00:10:27,610
Right.

164
00:10:27,820 --> 00:10:29,730
So one, two, three, one, two, three.

165
00:10:29,950 --> 00:10:35,440
That is too significant to so move this one two times.

166
00:10:35,440 --> 00:10:38,660
So let's shift a by to places.

167
00:10:38,800 --> 00:10:41,350
So this one will move here and move here.

168
00:10:41,470 --> 00:10:43,720
So it will move here and it will become zero.

169
00:10:44,230 --> 00:10:47,820
Now we have a bit of one move by googlies.

170
00:10:47,900 --> 00:10:51,780
So what is the value in a now that is for value in these for.

171
00:10:52,120 --> 00:10:53,470
So remember that happens only on these.

172
00:10:53,500 --> 00:10:53,740
Right.

173
00:10:54,040 --> 00:10:55,530
So this is for this is 16.

174
00:10:55,790 --> 00:10:56,650
It is 16 here.

175
00:10:56,650 --> 00:10:57,580
And it is four here.

176
00:10:58,760 --> 00:11:07,310
Now, how to know whether this is on or not, and this one perform, and if you perform ending, then

177
00:11:07,640 --> 00:11:09,680
zero zero zero, this is also zero.

178
00:11:09,710 --> 00:11:12,200
This is also zero zero is also zero.

179
00:11:12,560 --> 00:11:13,990
All last on me.

180
00:11:14,450 --> 00:11:22,490
So if you watch the settlements better off, if you got non-zero multivolume means on.

181
00:11:22,790 --> 00:11:30,480
So by performing and between eight and ATCH, I can know whether that bet is on or not.

182
00:11:30,590 --> 00:11:38,420
So knowing a particular bit inside a memory, whether it's on and off, escalus masking not let us do

183
00:11:38,420 --> 00:11:39,190
it for this bit.

184
00:11:39,200 --> 00:11:40,760
See, all of this is on.

185
00:11:40,970 --> 00:11:42,170
Let us check for this one.

186
00:11:42,560 --> 00:11:46,430
So first of all, set as one as one.

187
00:11:46,610 --> 00:11:48,020
So decide which place.

188
00:11:48,020 --> 00:11:48,800
Fourth place.

189
00:11:49,130 --> 00:11:50,910
So moved by Fort Bliss.

190
00:11:51,020 --> 00:11:52,650
So it will move one by Fort Bliss.

191
00:11:52,650 --> 00:11:53,330
A left shift.

192
00:11:53,660 --> 00:11:55,430
One, two, three, four.

193
00:11:55,430 --> 00:11:56,930
So it will move here.

194
00:11:57,200 --> 00:11:58,220
One will move here.

195
00:11:58,370 --> 00:12:00,260
Actually, all the others also will move here.

196
00:12:00,260 --> 00:12:00,530
Right.

197
00:12:00,530 --> 00:12:03,290
It's not crossing over this one side just like this.

198
00:12:03,290 --> 00:12:08,150
Then you are shifting mix this zero zero zero zero comes here and so on.

199
00:12:08,360 --> 00:12:09,830
And one will also move like that.

200
00:12:09,830 --> 00:12:11,110
It will happen four times.

201
00:12:11,540 --> 00:12:11,960
All right.

202
00:12:12,290 --> 00:12:15,080
So this is the is here now and these are on.

203
00:12:16,340 --> 00:12:24,400
New rules that came there, so it is left shifted and it is here, MIFA perform an edge and get through.

204
00:12:24,720 --> 00:12:26,000
Yes, this is true.

205
00:12:27,260 --> 00:12:30,890
So that means that we're just on this perfect.

206
00:12:32,040 --> 00:12:38,880
Now, next time, I'll show what does it mean by merging, right, see all this?

207
00:12:38,890 --> 00:12:40,810
Because on this one, right.

208
00:12:41,130 --> 00:12:44,610
And they want to set this bitties on inside edge.

209
00:12:44,790 --> 00:12:45,480
Inside Edge.

210
00:12:45,750 --> 00:12:47,450
I want to set this business on.

211
00:12:47,760 --> 00:12:53,040
So for sharing that with us on, I will take help of E eight as one.

212
00:12:53,040 --> 00:12:53,520
Yes.

213
00:12:54,090 --> 00:12:56,070
Left shift Abi Duplessis.

214
00:12:56,070 --> 00:12:57,380
So we will move here.

215
00:12:57,600 --> 00:12:58,510
He will come here.

216
00:12:58,680 --> 00:13:01,050
This will be that one will come here and that remains.

217
00:13:01,060 --> 00:13:11,930
Siedel then perform are installed the result in only so zero zero zero zero zero zero zero or one as

218
00:13:11,940 --> 00:13:12,390
one.

219
00:13:12,720 --> 00:13:14,580
Right and this is zero.

220
00:13:14,580 --> 00:13:16,530
This one or zero is one.

221
00:13:16,740 --> 00:13:17,510
So this is zero.

222
00:13:17,640 --> 00:13:24,240
So the result of this one will be zero zero zero one zero one zero zero.

223
00:13:24,600 --> 00:13:25,010
Right.

224
00:13:25,440 --> 00:13:28,680
So this will be the result and that is the story edge.

225
00:13:28,800 --> 00:13:30,230
So this becomes one.

226
00:13:31,080 --> 00:13:37,020
So already some big switched on and we have other better on that is called marching setting the bit

227
00:13:37,020 --> 00:13:43,320
on in-memory memory called us marching, checking whether it is on and off escalus masking.

228
00:13:43,770 --> 00:13:50,940
So these two operations, we have seen bitwise operations left shift masking and merging, all these

229
00:13:50,940 --> 00:13:55,770
things we will use now for finding duplicates in a string.

230
00:13:57,030 --> 00:13:57,420
Right.

231
00:13:58,140 --> 00:14:03,350
So finding obligate, we will see it in the next video, that is the video, so otherwise it will be

232
00:14:03,360 --> 00:14:04,180
very big video.

233
00:14:04,380 --> 00:14:05,380
So let us break it.

234
00:14:05,730 --> 00:14:10,560
So what's the next video for finding duplicates, using bitwise operations?

235
00:14:10,890 --> 00:14:12,710
This is a continuation of previous video.

236
00:14:12,720 --> 00:14:18,870
If you have not seen previous video go back to previous video, then I have to explain how we can use

237
00:14:18,870 --> 00:14:24,090
bitwise operations to manipulate the bits of a bite in the memory.

238
00:14:25,320 --> 00:14:33,090
We have seen masking and marching, now we will use that masking and merging to find out are there any

239
00:14:33,090 --> 00:14:34,890
duplicates in a string?

240
00:14:36,060 --> 00:14:42,000
So already we have an example where I use repeating and repeating, so just we can find out, are there

241
00:14:42,000 --> 00:14:43,200
any duplicates or not?

242
00:14:43,270 --> 00:14:50,230
I cannot count how many times that element or a character is repeating by using bits.

243
00:14:50,790 --> 00:14:54,660
So just to find out whether the bits are already there or not, that.

244
00:14:55,910 --> 00:15:02,210
So let us see what we need for this procedure, see, similar to harshing, we need some space.

245
00:15:02,420 --> 00:15:06,280
So in harshing, we have taken an array of during this exercise.

246
00:15:06,620 --> 00:15:13,940
So now we need thirty six bits, but we can only get 26 bits we get in terms of bytes.

247
00:15:13,940 --> 00:15:18,210
So eight, which makes a bite so we can get 32.

248
00:15:18,230 --> 00:15:20,330
But that is larger than this one.

249
00:15:20,480 --> 00:15:20,730
Right.

250
00:15:20,930 --> 00:15:22,250
Otherwise we get 16.

251
00:15:22,260 --> 00:15:24,290
But that is too white that it will be less than this one.

252
00:15:24,620 --> 00:15:26,850
So together we can take.

253
00:15:26,870 --> 00:15:27,620
So what is it have to do?

254
00:15:28,160 --> 00:15:29,660
So that is for whites.

255
00:15:29,840 --> 00:15:36,320
So four white means for either it can be an integer or it can be a long integer.

256
00:15:36,530 --> 00:15:38,840
So long integer takes four bites.

257
00:15:38,960 --> 00:15:42,680
C We have been assuming that integer takes to a white rate.

258
00:15:42,710 --> 00:15:43,980
I was assuming this one.

259
00:15:44,510 --> 00:15:52,760
So if you take two bytes long big four white but in some compilers of C C++ integer itself takes four

260
00:15:52,760 --> 00:15:53,150
bytes.

261
00:15:53,420 --> 00:15:55,010
So just integer is sufficient.

262
00:15:55,020 --> 00:15:57,350
You don't have to take long for it.

263
00:15:57,620 --> 00:16:01,710
So tiny little bits are for which you can get just using an integer.

264
00:16:02,060 --> 00:16:07,430
So I am assuming as per my assumptions, I'm taking long and.

265
00:16:07,700 --> 00:16:08,110
Right.

266
00:16:08,390 --> 00:16:12,910
So that is of four bytes now what to do with that fomites.

267
00:16:12,950 --> 00:16:14,810
So first of all, I will draw for whites.

268
00:16:14,810 --> 00:16:17,120
Then we will discuss here.

269
00:16:17,120 --> 00:16:18,290
I have four bytes.

270
00:16:18,290 --> 00:16:18,630
Right.

271
00:16:19,010 --> 00:16:27,320
So the least significant byte and this is significant, but zero to seven, the next bite is eight to

272
00:16:27,350 --> 00:16:28,680
15 and so on.

273
00:16:29,480 --> 00:16:35,060
So this is just like a hash table in the hash table we have begun and a hash table of size twenty six

274
00:16:35,060 --> 00:16:36,320
in the previous video.

275
00:16:36,530 --> 00:16:36,820
Right.

276
00:16:37,160 --> 00:16:41,520
So here we are having total 30 two zero to thirty one.

277
00:16:41,810 --> 00:16:45,720
So actually we need up to zero 2.5 right up to here.

278
00:16:45,730 --> 00:16:47,770
It is useful foubert extra.

279
00:16:47,900 --> 00:16:48,950
That is not an issue.

280
00:16:49,400 --> 00:16:52,980
Then all these bits assume that they are set to zero.

281
00:16:53,120 --> 00:16:54,880
Yes, they are set to zero.

282
00:16:55,160 --> 00:17:03,110
So for this I will take one variable, then I will take one variable loan and each and this is initialized

283
00:17:03,110 --> 00:17:03,770
with the zero.

284
00:17:04,760 --> 00:17:06,680
Yes, so all will be Zeitels.

285
00:17:08,069 --> 00:17:17,579
Now this I will use it for finding duplicate, how I will scan through a string for every alphabet I

286
00:17:17,579 --> 00:17:23,740
will set a bit on here, but the ASCII code of this is, well, not two, right.

287
00:17:24,000 --> 00:17:28,910
So actually, from the not to reveal subtract ninety seven and we get five.

288
00:17:29,220 --> 00:17:31,590
So we'll settle this fifth debate on.

289
00:17:32,490 --> 00:17:32,790
Right.

290
00:17:33,090 --> 00:17:36,620
You remember in The Harshing, we have done the same thing at the index.

291
00:17:36,630 --> 00:17:40,530
We were incrementing it, but now we will set this bit on.

292
00:17:40,860 --> 00:17:47,310
And for this I this will not five so not five minus ninety seven gives.

293
00:17:48,510 --> 00:17:57,440
Eight will set this eight bit on, right, so next time, when we come on this, I then I give you this

294
00:17:57,470 --> 00:17:59,770
aid, so we will go and check it out.

295
00:18:00,160 --> 00:18:01,530
It's already on.

296
00:18:01,530 --> 00:18:03,090
So I duplicated.

297
00:18:04,270 --> 00:18:08,700
So before setting it on, we will check whether it's already on or not.

298
00:18:09,520 --> 00:18:12,370
How do you check whether that bet is already on or not?

299
00:18:12,580 --> 00:18:14,040
By performing, masking.

300
00:18:14,050 --> 00:18:16,720
And then, yes, we know that that is already on.

301
00:18:17,190 --> 00:18:18,430
It is already on.

302
00:18:18,930 --> 00:18:22,210
Then we say it's a duplicate, not B of A.

303
00:18:22,360 --> 00:18:23,920
S F letter.

304
00:18:23,930 --> 00:18:25,890
If it is a one, not two.

305
00:18:26,290 --> 00:18:26,650
Right.

306
00:18:26,830 --> 00:18:34,490
So as it is will not I will subtract ninety seven so of i.e. minus ninety seven.

307
00:18:35,110 --> 00:18:37,550
So for every alphabet I will do it.

308
00:18:37,810 --> 00:18:39,490
So for this I will get to five.

309
00:18:39,490 --> 00:18:40,940
For this I will get eight.

310
00:18:41,440 --> 00:18:43,460
So now what to do with this five.

311
00:18:43,810 --> 00:18:50,740
I have to check whether this debate is on or off of at least 50 because on or off but zero.

312
00:18:50,740 --> 00:18:51,730
One, two, three, four, five.

313
00:18:51,970 --> 00:18:55,210
If you count a six, but a Nexus five.

314
00:18:55,540 --> 00:18:57,220
So that part is on or not.

315
00:18:57,550 --> 00:18:59,080
So how do you know that?

316
00:18:59,320 --> 00:19:01,480
By performing masking.

317
00:19:01,480 --> 00:19:02,320
I can do that.

318
00:19:02,330 --> 00:19:06,260
So I should have one more variable, one more variable there.

319
00:19:06,280 --> 00:19:09,130
This bit to set on and I should perform asking.

320
00:19:09,340 --> 00:19:13,270
So for that I will take one more variable that is set to zero.

321
00:19:13,510 --> 00:19:17,500
And here before this I will set X as one.

322
00:19:18,070 --> 00:19:18,420
Right.

323
00:19:18,760 --> 00:19:22,030
I will set that X as one then that one.

324
00:19:22,270 --> 00:19:26,860
I have to make this X shifted for five Plesser so that it comes here.

325
00:19:27,340 --> 00:19:34,240
So this difference, whatever the differences I should to shift this X so X left to shift to for these

326
00:19:34,240 --> 00:19:37,210
many times and assign it to X file.

327
00:19:37,220 --> 00:19:39,130
Right X here next assign one.

328
00:19:40,600 --> 00:19:42,630
Now this is a little tricky statement.

329
00:19:42,820 --> 00:19:46,600
Check this X as one might see, this is another one.

330
00:19:46,600 --> 00:19:48,810
I am not bringing boxes here right already.

331
00:19:48,810 --> 00:19:55,660
I have boxes for variable X so similarly assume that these are the boxes for variable X

332
00:19:58,300 --> 00:20:01,330
zero one, two, three, four, five, six, seven.

333
00:20:01,640 --> 00:20:03,070
But these are the boxes.

334
00:20:03,490 --> 00:20:06,940
So I'm showing only for the one variable like otherwise I have to draw all of them.

335
00:20:07,240 --> 00:20:15,770
This is variable rate which is zero now for it has one then left this X by these many times.

336
00:20:15,770 --> 00:20:18,760
So how much it is right now if one or two.

337
00:20:18,970 --> 00:20:21,970
So one not to two minus ninety seven is a five.

338
00:20:22,210 --> 00:20:25,350
So it will shift this value one by five.

339
00:20:25,780 --> 00:20:27,540
One, two, three, four, five.

340
00:20:27,560 --> 00:20:30,460
So one comes here and all these are zeros.

341
00:20:30,760 --> 00:20:31,810
All these are zeros.

342
00:20:31,810 --> 00:20:32,140
Right.

343
00:20:32,620 --> 00:20:33,540
All the zeros.

344
00:20:34,300 --> 00:20:35,410
So I got one here.

345
00:20:37,400 --> 00:20:45,860
Now, check inside, is that it is on or not, if it is already on means it's a duplicate letter.

346
00:20:46,140 --> 00:20:50,230
Once it's there, if it is off limits, set it on.

347
00:20:50,870 --> 00:21:02,840
So I will check here how to check whether this is on or not, but masking so X and H if X and etch the

348
00:21:02,840 --> 00:21:09,170
result by ending as is the greater than zero minutes, we got some non-zero value.

349
00:21:09,260 --> 00:21:09,660
Right.

350
00:21:09,950 --> 00:21:14,090
If that is off, once we get to zero, if that is on, we get non-zero value.

351
00:21:14,420 --> 00:21:18,380
If it is so, then put into that swendson that there is a duplicate.

352
00:21:18,650 --> 00:21:22,330
So printf percentile C.

353
00:21:22,370 --> 00:21:26,020
S a duplicate percentile C is duplicate.

354
00:21:27,680 --> 00:21:29,330
So who is duplicate here.

355
00:21:29,660 --> 00:21:31,420
That is of i.e. four.

356
00:21:31,460 --> 00:21:34,580
If it is not duplicate for an M you will get duplicate.

357
00:21:34,910 --> 00:21:37,280
So if I whatever I is.

358
00:21:39,420 --> 00:21:47,040
Right, it's already there if it is not there, if it is not one, then set it as one.

359
00:21:47,600 --> 00:21:51,060
We got that digit, we got that alphabet, so set it as one.

360
00:21:51,300 --> 00:21:54,720
How to set it by performing, merging.

361
00:21:54,720 --> 00:21:59,320
So X are each still the result only.

362
00:21:59,700 --> 00:22:01,640
So this will be set as one.

363
00:22:02,610 --> 00:22:04,860
So that phone does the close of loop.

364
00:22:06,680 --> 00:22:12,610
So wherever there is a duplicate, it will display that alphabet if any duplicate alphabet is there,

365
00:22:12,620 --> 00:22:13,480
it will displayed.

366
00:22:14,210 --> 00:22:20,260
We have used the shifting for preparing a number so that we can perform masking and marching.

367
00:22:20,420 --> 00:22:20,690
Right.

368
00:22:21,200 --> 00:22:24,830
So far, this worked for us, but comfier.

369
00:22:25,160 --> 00:22:28,070
And for AI eight, which will be on.

370
00:22:28,460 --> 00:22:29,210
This will be on.

371
00:22:29,480 --> 00:22:30,530
So we check for this one.

372
00:22:30,530 --> 00:22:31,560
Is it present or not?

373
00:22:32,150 --> 00:22:38,540
So in this way, the bits of four or five this will be set on and four eight it will be set on.

374
00:22:39,380 --> 00:22:39,740
Right.

375
00:22:40,100 --> 00:22:42,830
And the for this one will not treat.

376
00:22:42,890 --> 00:22:45,870
This is one, not three, but is also set on.

377
00:22:46,880 --> 00:22:50,270
So this is not to and this is one, not a three.

378
00:22:50,750 --> 00:22:52,490
And this is a hundred.

379
00:22:52,620 --> 00:22:57,690
So this is also one four hundred then will not five is already over.

380
00:22:58,130 --> 00:23:02,270
Then one ten, five, six, seven, eight, nine, ten.

381
00:23:02,300 --> 00:23:03,520
So this is also on.

382
00:23:03,860 --> 00:23:08,360
So the rest of them all will be Zeitels and these bits will be set on.

383
00:23:08,570 --> 00:23:11,390
And then again it finds that it's already there.

384
00:23:11,540 --> 00:23:12,950
So I will not fight.

385
00:23:13,220 --> 00:23:14,110
So I will not fight.

386
00:23:14,120 --> 00:23:15,020
It is already there.

387
00:23:15,380 --> 00:23:22,430
So this is duplicated then four and one the first time this will be set on second time it will check

388
00:23:22,430 --> 00:23:22,720
here.

389
00:23:22,910 --> 00:23:23,890
This is already there.

390
00:23:24,200 --> 00:23:26,220
So this is duplicate.

391
00:23:26,480 --> 00:23:26,760
Right.

392
00:23:27,050 --> 00:23:32,300
So for checking this one, we need one here in X, so we are getting that one initially.

393
00:23:32,300 --> 00:23:36,200
We are sitting this as one by this and shifting it that many times.

394
00:23:36,600 --> 00:23:36,940
Right.

395
00:23:37,220 --> 00:23:38,660
Shifting it that many times.

396
00:23:38,660 --> 00:23:39,500
How much times.

397
00:23:39,800 --> 00:23:42,640
C one and minus ninety seven.

398
00:23:42,650 --> 00:23:46,130
So this is 13, so it should be moved by 13 times from here.

399
00:23:46,140 --> 00:23:51,460
So I'll show you one, two, three, four, five, six, seven, eight, nine, 10, 11, 12, 13.

400
00:23:51,470 --> 00:23:56,960
So it will come here then will perform masking the number four billion.

401
00:23:57,320 --> 00:23:59,560
If not, we will set it by much.

402
00:24:01,130 --> 00:24:07,410
So this is all you can use, bitwise operation to know the duplicates inside the sting, but we cannot

403
00:24:07,410 --> 00:24:12,720
tell just we can know whether there is a duplicate or not and we can know what is that look that is

404
00:24:12,740 --> 00:24:13,370
duplicated.

405
00:24:14,540 --> 00:24:19,370
So that's all there is a simple program, so you can write down and check it right.

406
00:24:19,610 --> 00:24:25,070
And if you face any difficulty, you can pose the question like if you want me to give you the program,

407
00:24:25,100 --> 00:24:27,290
I will give the program the full.

