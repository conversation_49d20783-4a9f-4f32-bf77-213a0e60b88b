1
00:00:00,480 --> 00:00:06,590
The topic is sorting technique, there are various sorting techniques here, I have a list of sorting

2
00:00:06,600 --> 00:00:08,360
techniques that I'm going to discuss.

3
00:00:08,580 --> 00:00:13,050
So for many sorting techniques out there, each has its own method of working.

4
00:00:13,080 --> 00:00:17,280
We cannot say one of the sorting technique is best a sorting technique.

5
00:00:17,670 --> 00:00:19,680
There is some criteria based on that.

6
00:00:19,680 --> 00:00:23,360
We compare the sorting techniques and suitable for your requirement.

7
00:00:23,370 --> 00:00:25,430
You can select a particular sorting technique.

8
00:00:26,870 --> 00:00:30,500
So what is the criteria for comparing those of certain techniques?

9
00:00:30,800 --> 00:00:36,500
So, first of all, we should know the criteria of comparison of those assorting techniques based on

10
00:00:36,500 --> 00:00:37,280
the criteria.

11
00:00:37,280 --> 00:00:39,750
We will be studying them and analyzing them.

12
00:00:40,040 --> 00:00:42,290
So, first of all, let us discuss criteria.

13
00:00:43,690 --> 00:00:51,640
See, certain techniques are analyzed based on a number of competitions or starting the elements or

14
00:00:51,790 --> 00:00:57,450
sorting technique, maybe comparing the elements, and if it is comparing then total, how many competitions

15
00:00:57,460 --> 00:00:58,270
it is performing.

16
00:00:59,620 --> 00:01:04,290
So actually, total time taken by a sorting technique depends on the number of competitions.

17
00:01:05,260 --> 00:01:11,100
So this number of competitions decides the time, complexity of any sorting technique.

18
00:01:12,540 --> 00:01:19,290
Then the second one is how many slaps C for arranging them in the order, increasing or decreasing or

19
00:01:19,320 --> 00:01:23,670
under the most has to change their places, so it has to swap the elements.

20
00:01:24,000 --> 00:01:28,330
So subbing is a common process is done in almost all the following techniques.

21
00:01:28,590 --> 00:01:30,370
So how many swaps are this performing?

22
00:01:30,660 --> 00:01:33,520
We will analyze how many swaps are sorting techniques.

23
00:01:33,520 --> 00:01:36,980
Is performing then total one is adaptive.

24
00:01:37,860 --> 00:01:42,690
See, when you are trying to keep the elements in the sorted order, what happens if they are already

25
00:01:42,690 --> 00:01:44,460
in the solid order already?

26
00:01:44,460 --> 00:01:48,030
They are in increasing order and I'm trying to raise them again in increasing order.

27
00:01:49,350 --> 00:01:55,290
So if they're already in increasing order, then the sorting method must take a minimum time or less

28
00:01:55,290 --> 00:01:59,090
amount of time because it doesn't have to rearrange the elements.

29
00:01:59,580 --> 00:02:06,690
So if any sorting method is taking less time or minimum time over all the sorted list, then we call

30
00:02:06,690 --> 00:02:09,090
that algorithm as adoptive.

31
00:02:09,360 --> 00:02:15,510
So sorting procedure or algorithm, because it has adopted the algorithm that this will have to explain

32
00:02:15,510 --> 00:02:16,760
you using an example.

33
00:02:16,770 --> 00:02:18,030
What does it mean by stable?

34
00:02:18,750 --> 00:02:23,630
So I will finish this extra memory, all of these sorting algorithms.

35
00:02:23,640 --> 00:02:29,400
There are some sorting algorithms which requires extra memory space for sorting the elements apart from

36
00:02:29,400 --> 00:02:35,670
the space consumer, the element they need extra spaces, so not all fuel required.

37
00:02:35,820 --> 00:02:40,890
So we will analyze for every algorithm whether it requires extra space or not.

38
00:02:42,140 --> 00:02:43,280
Then I have to explain you.

39
00:02:43,970 --> 00:02:49,800
So I'll remove this and take an example to show you what is the meaning of stable and important.

40
00:02:50,570 --> 00:02:51,470
Now, let me explain.

41
00:02:51,530 --> 00:02:54,410
What does it mean by Steben for explanation?

42
00:02:54,440 --> 00:02:57,860
I have a list of names and marks.

43
00:02:57,860 --> 00:03:00,320
Actually, these are records of students.

44
00:03:00,560 --> 00:03:02,000
Some of these are student names.

45
00:03:02,000 --> 00:03:05,720
I have taken just alphabet's names of the student and their marks.

46
00:03:07,030 --> 00:03:10,940
Right now, this list is all that is sorted based on names.

47
00:03:11,140 --> 00:03:13,520
Now I want to start based on marks.

48
00:03:13,630 --> 00:03:23,440
So if you look into the marks, two students are having same marks, first as C, the next is E. So

49
00:03:23,440 --> 00:03:24,760
there are duplicate elements.

50
00:03:25,710 --> 00:03:32,460
When I saw this based on Moggs, what I want is there their there should not change that.

51
00:03:32,730 --> 00:03:35,560
This is six should come first, then this six.

52
00:03:35,880 --> 00:03:36,760
What is the difference.

53
00:03:36,780 --> 00:03:37,900
What are six, Olina?

54
00:03:38,200 --> 00:03:40,980
No, that is the reason I have kept other values.

55
00:03:40,980 --> 00:03:47,780
Also here, this six belongs to see this six belongs to E so that C should come first, then comes E

56
00:03:48,060 --> 00:03:49,590
the order should be preserved.

57
00:03:50,490 --> 00:03:56,760
If a sorting algorithm is preserving the order of duplicate elements in the sorted list, then that

58
00:03:56,760 --> 00:03:58,630
algorithm is stable.

59
00:03:59,250 --> 00:04:03,640
Let me sort the essential you Lewis the smallest monisha forward.

60
00:04:03,810 --> 00:04:09,960
So be for the next is a five, the Nexus six.

61
00:04:10,290 --> 00:04:11,130
So which is six.

62
00:04:11,130 --> 00:04:13,530
I should take first this one only.

63
00:04:13,540 --> 00:04:20,430
So if I am getting C six of them E six, then we say that the sorting procedure is stable.

64
00:04:20,430 --> 00:04:27,960
If any of these sardis preserving that order as it is, then we call that sorting procedure as stable

65
00:04:27,960 --> 00:04:30,690
socket or stable algorithm for sorting.

66
00:04:31,710 --> 00:04:33,900
I'll finish this, I'll write the rest of the numbers.

67
00:04:33,900 --> 00:04:40,470
Also, so stable means if there are any duplicate elements in the original list, then in the sorted

68
00:04:40,470 --> 00:04:42,480
list there are there must be preserved.

69
00:04:43,510 --> 00:04:47,300
Now, next question, why why we need this reason.

70
00:04:47,490 --> 00:04:52,030
Listen, see, already this list was sorted based on names.

71
00:04:53,260 --> 00:05:00,130
Now I am sorting them based on Mock's if I saw them based on marks, it will not be sorted up on names

72
00:05:00,520 --> 00:05:01,120
no more.

73
00:05:01,270 --> 00:05:01,900
Definitely.

74
00:05:02,320 --> 00:05:04,030
See, the names are different.

75
00:05:04,030 --> 00:05:06,780
B So they have changed their order.

76
00:05:07,570 --> 00:05:14,560
But if there are duplicate marks, then at least their names must be in the sorted order.

77
00:05:14,560 --> 00:05:16,200
First to C, then E.

78
00:05:16,750 --> 00:05:18,070
This is the requirement.

79
00:05:18,760 --> 00:05:26,260
If you are sorting a record up on multiple columns named and all numbered and marks like this, if you

80
00:05:26,260 --> 00:05:30,330
are sorting them, then this is the requirement for sorting.

81
00:05:30,340 --> 00:05:37,840
Was this one not only sort of one marks that is lost, but it will be preserved if duplicates are there.

82
00:05:38,290 --> 00:05:41,320
So this is commonly used in databases.

83
00:05:41,710 --> 00:05:48,250
So we want our sorting algorithm and database such that if we are sorting upon multiple columns, then

84
00:05:48,250 --> 00:05:50,230
it should preserve the of the facade.

85
00:05:50,320 --> 00:05:54,940
In the second part means if there are duplicates then there are the should be preserved.

86
00:05:55,330 --> 00:05:56,400
There's the requirement.

87
00:05:56,770 --> 00:05:59,500
So not all algorithms are stable, few are stable.

88
00:05:59,500 --> 00:06:00,150
I would show them.

89
00:06:00,880 --> 00:06:06,340
So that's all we have finished with the criteria for analysing sorting algorithms.

90
00:06:06,970 --> 00:06:09,850
Now briefly give you the idea of all these things.

91
00:06:10,240 --> 00:06:11,230
I have listed them.

92
00:06:11,650 --> 00:06:13,680
Then briefly, I will talk about them.

93
00:06:14,260 --> 00:06:17,260
We are going to learn about all these things one by one.

94
00:06:17,560 --> 00:06:21,280
But just let us get a broader picture of all these together.

95
00:06:21,820 --> 00:06:23,800
See all these sorting?

96
00:06:23,800 --> 00:06:26,710
Al Gore comes from one to eight.

97
00:06:26,920 --> 00:06:28,170
These are called us.

98
00:06:38,120 --> 00:06:45,590
Competition based sorts, all these are competition based sorts, the sort of elements by competing

99
00:06:45,590 --> 00:06:46,590
with other elements.

100
00:06:46,910 --> 00:06:50,780
So one element is compared with other elements and the arrangement this is done.

101
00:06:51,980 --> 00:06:58,070
Then what about these sorts, these resorts are called us index, be the thoughts.

102
00:06:59,260 --> 00:07:04,180
So these are index based and these are comparison based, next.

103
00:07:05,890 --> 00:07:14,350
These are things, these three things, they all big and square time, so we commonly fair and square

104
00:07:14,350 --> 00:07:16,870
sorting algorithm means we are referring to those three.

105
00:07:17,970 --> 00:07:22,400
They all did, and we will analyze this one when we study, we will analyze this just I am giving the

106
00:07:22,400 --> 00:07:23,480
overview of what this.

107
00:07:25,940 --> 00:07:27,650
Then diesel gorgons takes.

108
00:07:30,330 --> 00:07:31,350
And Laurentine.

109
00:07:34,100 --> 00:07:40,880
And login for any of these, he thought MassArt quixotry thought and login swotting means any of these

110
00:07:41,960 --> 00:07:44,290
and means any one of those.

111
00:07:45,200 --> 00:07:46,520
Now these things are.

112
00:07:47,770 --> 00:07:53,160
So these are often all multiple often, but they consume a lot of space.

113
00:07:54,700 --> 00:07:57,500
So they are faster, but space consumption is small.

114
00:07:58,060 --> 00:08:05,650
This is all to use and three by two, so it is faster than this one, much slower than in Log-in Shelford

115
00:08:05,920 --> 00:08:09,240
three one point five and one point five.

116
00:08:09,880 --> 00:08:13,120
Anyway, about which one is adaptive, which one is stable.

117
00:08:13,360 --> 00:08:18,950
I am going to explain it along with the algorithm when I discuss it and analyze it thoroughly.

118
00:08:19,310 --> 00:08:24,940
This I wanted to show you that some are comparison based swordfight, some are index based and some

119
00:08:24,940 --> 00:08:26,710
are Red Square and some are analog.

120
00:08:26,710 --> 00:08:29,560
And this categorization you must be familiar with.

121
00:08:29,950 --> 00:08:31,450
So that's all we will do.

122
00:08:31,460 --> 00:08:32,890
Starting from bubbles sort.

