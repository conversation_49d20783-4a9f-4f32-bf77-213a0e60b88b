1
00:00:00,150 --> 00:00:09,570
The topic is finding my lord of a linguist we want to reach on the North Sea as an example, I have

2
00:00:09,570 --> 00:00:16,020
to count seven notes in a single linguist here, and this is the middle of all the three nodes on this

3
00:00:16,020 --> 00:00:16,360
site.

4
00:00:16,400 --> 00:00:22,130
Even worse on that site, if suppose there are only six nodes like this node is not there.

5
00:00:22,560 --> 00:00:25,970
I this is not the only six northern region of the Lord.

6
00:00:26,160 --> 00:00:33,390
Now, either I can stop on third or fourth, not like anyone note I can stop one because if there are

7
00:00:33,390 --> 00:00:35,450
even number of nodes there is nothing like it.

8
00:00:36,480 --> 00:00:40,530
So either third or fourth, if in six nodes, if you have to find no log.

9
00:00:41,610 --> 00:00:45,220
Now, what should be the procedure for reaching the north?

10
00:00:45,480 --> 00:00:48,900
So there can be more than one solutions first solution.

11
00:00:50,490 --> 00:00:51,360
Find Alan.

12
00:00:53,570 --> 00:00:55,920
We already know how to find the land of a linguist.

13
00:00:56,780 --> 00:01:02,270
We have already seen the procedure once you know, the land is supposed to land this seven, then ridge

14
00:01:02,300 --> 00:01:04,069
on the north right.

15
00:01:05,720 --> 00:01:07,400
Ridge to the north.

16
00:01:10,430 --> 00:01:16,640
So which is a middle load, seven by two, so this will be three point five, so we can take it as a

17
00:01:16,640 --> 00:01:24,890
seal of seven point to the fourth node so I can take a point B upon the first node and move it for four

18
00:01:24,890 --> 00:01:25,960
minus one times.

19
00:01:26,270 --> 00:01:29,100
So one, two, three.

20
00:01:30,110 --> 00:01:31,890
So four minus one is three.

21
00:01:32,150 --> 00:01:33,200
So he's still here.

22
00:01:33,560 --> 00:01:35,340
So actually I have to reach on food and order.

23
00:01:35,400 --> 00:01:36,320
So this is the food node.

24
00:01:36,330 --> 00:01:38,600
Food not be before just three times.

25
00:01:38,960 --> 00:01:40,370
So four minus one time.

26
00:01:40,610 --> 00:01:43,980
If I move the pointer for four minus one time then it will restart.

27
00:01:44,900 --> 00:01:48,720
This is one method, so we're not sure how to do this one.

28
00:01:48,740 --> 00:01:49,700
You know it very well.

29
00:01:49,700 --> 00:01:53,780
Just I'm discussing the method C in this method.

30
00:01:53,780 --> 00:01:55,490
First of all, I have to find the length.

31
00:01:56,180 --> 00:02:01,460
Then afterwards I have to take a pointer and move it until it reaches the middle node.

32
00:02:01,550 --> 00:02:04,490
So I have to scan the link two times.

33
00:02:05,790 --> 00:02:11,680
No, I want to do it in a single scan, single scan, how to do it.

34
00:02:12,170 --> 00:02:17,980
So let's see the second method for finding a middle node in a single scan.

35
00:02:17,990 --> 00:02:19,210
We will take two pointers.

36
00:02:19,760 --> 00:02:24,620
So already we have learned the concept of building point that one pointer is there and behind that we

37
00:02:24,620 --> 00:02:25,580
will have one more pointer.

38
00:02:25,850 --> 00:02:27,650
So for that I will take two pointers.

39
00:02:27,650 --> 00:02:30,740
Let us call first point B and the second point rescue.

40
00:02:31,730 --> 00:02:35,300
Not one point will move it one or two at a time.

41
00:02:35,600 --> 00:02:39,550
And second point that we will move it to N at that time.

42
00:02:39,950 --> 00:02:44,180
So let us say P we will move by one shot and killed by two nodes.

43
00:02:44,390 --> 00:02:50,140
So in this way, by the time you reaches the last node people in the middle.

44
00:02:50,420 --> 00:02:57,310
So because the distance travelled by P will be half of Q, so let us do it by taking two pointers.

45
00:02:57,650 --> 00:02:59,630
So already I have taken two Usborne.

46
00:02:59,630 --> 00:03:01,130
Q How to do it.

47
00:03:01,460 --> 00:03:03,380
Let us do so first move.

48
00:03:03,380 --> 00:03:11,810
Q for to n one to two n Q is not null so move p also one node.

49
00:03:13,000 --> 00:03:20,140
Then I didn't move too far to north one to four queues here now queue is not known.

50
00:03:20,410 --> 00:03:31,310
So it has not reached and so move me also then moved Q4 to north one to sulcus here, then move by one

51
00:03:31,320 --> 00:03:31,630
north.

52
00:03:33,440 --> 00:03:41,360
Then move Cuba to north, so first node itself, Cuba becomes null, so we cannot move further, then

53
00:03:41,750 --> 00:03:44,120
move be shall we move be?

54
00:03:44,330 --> 00:03:46,590
No, Cuba became null, don't move.

55
00:03:47,060 --> 00:03:49,870
So it means we are on the middle of a linked list.

56
00:03:50,840 --> 00:03:52,960
So by using to point us, we can do this.

57
00:03:54,080 --> 00:03:56,150
So here I have seven nodes.

58
00:03:56,510 --> 00:03:58,750
I'll do it again by taking eight north.

59
00:03:58,760 --> 00:04:00,530
Just watch it not.

60
00:04:00,530 --> 00:04:04,160
I have added one more in order to uplink less so total.

61
00:04:04,160 --> 00:04:06,980
There are eight north so ornaments.

62
00:04:07,100 --> 00:04:10,520
I should start back for Naude so let us see.

63
00:04:10,520 --> 00:04:12,160
Will it stop at Ford or not.

64
00:04:12,500 --> 00:04:13,850
So one, two, three, four.

65
00:04:13,850 --> 00:04:14,960
Naughtie then.

66
00:04:14,960 --> 00:04:16,100
One, two, three, four.

67
00:04:16,100 --> 00:04:17,110
Faunus that site.

68
00:04:17,390 --> 00:04:26,750
Let us run the procedure move Kubi to N We'll get you is here then move P by one normal move Kubi to

69
00:04:26,750 --> 00:04:27,920
north one to.

70
00:04:29,530 --> 00:04:35,170
Then moved by one known move, two by two north, one to.

71
00:04:36,690 --> 00:04:47,010
Move by the north, right, then move Kubi to north one, too, so Cuba becomes known as Cuba has became

72
00:04:47,010 --> 00:04:47,410
null.

73
00:04:47,610 --> 00:04:48,980
We are not moving B..

74
00:04:48,990 --> 00:04:50,600
I already told you so.

75
00:04:50,610 --> 00:04:56,310
Don't be so Cuba null and stop BS on the middle of a linguist.

76
00:04:57,030 --> 00:05:01,380
So this is the procedure for finding a middle node in a single scan.

77
00:05:01,380 --> 00:05:05,500
So this can be done by using two pointers right now.

78
00:05:05,520 --> 00:05:10,140
One thing I would like to show you that in the previous method, first method, we were first finding

79
00:05:10,140 --> 00:05:10,500
land.

80
00:05:11,100 --> 00:05:18,660
So just like you then we were reaching the middle of a link list by using the same amount of work is

81
00:05:18,660 --> 00:05:18,960
done.

82
00:05:19,290 --> 00:05:23,700
So amount of pointer movement the same, but only the references.

83
00:05:23,700 --> 00:05:26,050
Previous method was not in a single scan.

84
00:05:26,970 --> 00:05:28,350
This isn't a single scan.

85
00:05:28,800 --> 00:05:29,130
Right?

86
00:05:29,340 --> 00:05:36,150
In the previous method we perform LENTE one scan, then the second half of us can, but here two are

87
00:05:36,150 --> 00:05:39,390
done simultaneously that that is the difference.

88
00:05:39,780 --> 00:05:46,380
So if I write the program code for this one, this will be in a single loop like B as well as the Q,

89
00:05:46,390 --> 00:05:48,330
they will be starting at the first.

90
00:05:48,510 --> 00:05:55,440
Then why cube like is not null because we will become null for school will become null first, then

91
00:05:55,440 --> 00:05:56,280
what to do first.

92
00:05:56,280 --> 00:05:57,960
Q or Syracuse next.

93
00:05:58,380 --> 00:06:04,350
Right Cube will move to one node then if a Q Maskew is not null then.

94
00:06:04,350 --> 00:06:06,000
Q Assign thankyous next.

95
00:06:06,420 --> 00:06:08,310
So does the second movement.

96
00:06:08,670 --> 00:06:13,500
Then if a Q Maskew has not became null, then be a fine piece next.

97
00:06:14,910 --> 00:06:19,760
So Q will move for two times, people move for one time and the B will move.

98
00:06:19,860 --> 00:06:26,280
Q has not became null, so this loop will make a point on the in of for linguist.

99
00:06:27,130 --> 00:06:33,090
So there's a simple call, next analysis, how much time it takes, it takes lot of time.

100
00:06:33,220 --> 00:06:36,460
Why we are just a scanning tool linguist once.

101
00:06:37,730 --> 00:06:43,400
That said, so the program called You Have to write on, I will not show you the demonstration of this

102
00:06:43,400 --> 00:06:44,840
one, you can take a link.

103
00:06:44,900 --> 00:06:47,060
Listen, we have programs for creating a link.

104
00:06:47,060 --> 00:06:50,140
Let's just you have to write on this piece of code as a function.

105
00:06:50,150 --> 00:06:54,270
You can write as well as you can write the same thing inside main function and check it.

106
00:06:54,980 --> 00:06:57,190
So after this, you can display a node, right.

107
00:06:57,200 --> 00:07:03,110
So you can display a note to confirm whether P has reached the middle of a link list or not.

108
00:07:03,380 --> 00:07:09,470
So you can print a piece of data so you can know where she's pointing as a default node of your list

109
00:07:09,470 --> 00:07:09,870
or not.

110
00:07:10,250 --> 00:07:11,120
So we're printing this.

111
00:07:11,120 --> 00:07:15,470
You can also one more method using a stack and remove this.

112
00:07:17,020 --> 00:07:22,570
Now, third method, I will make a point to be upon for Fastenal and I will take a stack.

113
00:07:26,150 --> 00:07:27,440
Now, why are you going?

114
00:07:27,740 --> 00:07:33,710
I will scan for this linguists and each node, I will put it inside this tag, so I will put the address

115
00:07:33,710 --> 00:07:34,880
of an order that is central.

116
00:07:35,240 --> 00:07:40,040
Then we will move to the next node so one can then we will move to the next node.

117
00:07:40,040 --> 00:07:43,220
It's 130, then be moved here, right here.

118
00:07:43,220 --> 00:07:46,450
So be 150 is inserted then please.

119
00:07:46,460 --> 00:07:46,770
Here.

120
00:07:47,090 --> 00:07:48,500
So 200 is inserted in.

121
00:07:48,500 --> 00:07:50,690
This can then be moved to next node.

122
00:07:50,690 --> 00:07:54,890
So 220 is inserted into the stack, then be moved to make small.

123
00:07:55,220 --> 00:08:01,550
So 240 is inserted into the stack, then be moved to this next node.

124
00:08:01,820 --> 00:08:04,280
So 260 is inserted into the stack.

125
00:08:04,370 --> 00:08:04,700
Right.

126
00:08:05,000 --> 00:08:06,230
Then P becomes null.

127
00:08:06,320 --> 00:08:07,010
So stop.

128
00:08:07,250 --> 00:08:12,670
So we have scans for this link list and every address I have pushed it into the stack.

129
00:08:12,680 --> 00:08:13,840
I'll complete this one.

130
00:08:14,360 --> 00:08:14,630
Right.

131
00:08:15,380 --> 00:08:17,090
So what is the size of the stack?

132
00:08:18,250 --> 00:08:22,030
We can know the size of the stack, one, two, three, four, five, six, seven, eight.

133
00:08:23,690 --> 00:08:24,090
Right.

134
00:08:24,230 --> 00:08:26,040
So eight, eight by two.

135
00:08:26,330 --> 00:08:32,880
So we want them to load so eight by two, if I take single value, then I get four.

136
00:08:33,169 --> 00:08:35,270
So simply about four.

137
00:08:35,280 --> 00:08:38,760
And also from the stack so forward, one, two, three, four.

138
00:08:39,080 --> 00:08:41,120
So this is the address of the middle north.

139
00:08:42,750 --> 00:08:48,570
So let us try for seven more if seven nodes are dead and I should pop out a three nodes from this one,

140
00:08:48,810 --> 00:08:53,360
then fourth node will be yes, I should take it to the floor, not a seal.

141
00:08:53,610 --> 00:08:55,320
So this also it should be floor.

142
00:08:55,440 --> 00:08:58,060
So for eight floor seal, the same thing.

143
00:08:58,080 --> 00:09:04,200
So how many nodes I should pop out as floor number of that is eight by two or seven by two.

144
00:09:04,440 --> 00:09:08,030
So if it's about three nodes then footnote that will be a middle ground.

145
00:09:08,760 --> 00:09:10,140
So this is another matter.

146
00:09:10,290 --> 00:09:16,770
So take all these addresses inside the stack, then pop out half of the stack, then middle node will

147
00:09:16,770 --> 00:09:17,160
be there.

148
00:09:17,430 --> 00:09:23,220
So again, this is not using single scan, but we are scanning a link list only one time.

149
00:09:23,690 --> 00:09:24,060
Right.

150
00:09:24,450 --> 00:09:28,860
But the rest of the process is also the linguist's and it is inside the stack.

151
00:09:29,800 --> 00:09:34,130
So we can see we are scanning a list only one time, right?

152
00:09:34,510 --> 00:09:35,320
So we are getting there.

153
00:09:35,350 --> 00:09:36,380
This is from this stack.

154
00:09:37,570 --> 00:09:39,010
So there's another solution.

155
00:09:39,590 --> 00:09:41,970
So what is the time taken by this one time?

156
00:09:41,980 --> 00:09:46,750
Take on the same thing and then half of the elements are from the stack.

157
00:09:46,760 --> 00:09:49,660
So that falls in right around bitou.

158
00:09:49,660 --> 00:09:57,130
So it is so total amount of work done this and plus and by doing so, if you take this and that is Lenie.

159
00:09:58,780 --> 00:10:06,100
And one more thing for writing the program for this one, we should have a stack of Linklaters that

160
00:10:06,100 --> 00:10:08,370
is N n type.

161
00:10:08,590 --> 00:10:14,170
So these are all pointers to N, so you should take a stack of notes and you can fill the addresses

162
00:10:14,170 --> 00:10:15,550
of the nodes in the stack.

163
00:10:16,120 --> 00:10:17,740
So typewrite on the program.

164
00:10:17,980 --> 00:10:22,510
Anyway, in the coming sections I may be showing a stack of addresses.

165
00:10:23,510 --> 00:10:24,200
So that's it.

166
00:10:24,290 --> 00:10:27,430
So you have to implement this one, you have to get a program for this one.

