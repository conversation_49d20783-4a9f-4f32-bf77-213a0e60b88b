1
00:00:00,500 --> 00:00:08,480
Now, let us do the analysis of binary search, so for the same list, if you observe when we have search

2
00:00:08,480 --> 00:00:17,000
for various keys, every time middle value was seven, obviously zero to 14, mirrorless seven only

3
00:00:17,660 --> 00:00:22,310
initially law was here and he was here and we were targeting America.

4
00:00:22,520 --> 00:00:23,380
This is America.

5
00:00:24,110 --> 00:00:26,320
So always we were getting married at seven.

6
00:00:27,260 --> 00:00:33,780
So if suppose we are searching for a key thirty seven, then in foster competition, only our search

7
00:00:33,780 --> 00:00:34,790
should terminate.

8
00:00:34,880 --> 00:00:36,260
That key is found.

9
00:00:37,320 --> 00:00:45,630
So it means that the key value at index seven, the key value twenty 27, can be found in just one single

10
00:00:45,630 --> 00:00:46,240
competition.

11
00:00:47,590 --> 00:00:54,660
And next, we were searching for the element on the left hand side, so he was here and murder was just

12
00:00:54,670 --> 00:00:54,940
one.

13
00:00:56,760 --> 00:01:02,440
So for the left hand side, when I was 15, the index was always a three.

14
00:01:02,730 --> 00:01:09,120
So on the left hand side, middle one index is three and the element is 15.

15
00:01:10,380 --> 00:01:16,230
Then on that side, if you check, if we are checking for an element on the right hand side, then the

16
00:01:16,230 --> 00:01:20,790
law will be this one and I will be this one and this will get to the middle.

17
00:01:21,040 --> 00:01:24,360
That is index level and the limit is 37.

18
00:01:26,670 --> 00:01:36,050
And next 11 and 37, so it means if you're searching for 37, then it will be following the to competition.

19
00:01:36,330 --> 00:01:39,510
First time we will check for index seven that started seven.

20
00:01:39,510 --> 00:01:40,410
Element is great.

21
00:01:40,530 --> 00:01:43,480
Thirty seven, go on the right side, take the mud.

22
00:01:43,560 --> 00:01:46,400
We get dismissed and women will fall into competition.

23
00:01:47,280 --> 00:01:53,460
So from this list, this will be found in one competition and this will be found in two competition

24
00:01:53,460 --> 00:01:55,070
and this also in competition.

25
00:01:55,620 --> 00:01:56,760
What about remaining?

26
00:01:56,760 --> 00:01:58,980
So I will quickly write on about the remaining one.

27
00:01:59,730 --> 00:02:03,060
If I take the left hand side, that is from zero to six.

28
00:02:03,570 --> 00:02:05,010
The middle element was three.

29
00:02:05,610 --> 00:02:10,560
Then on the left side, if I take this list, the number will be eight.

30
00:02:10,560 --> 00:02:11,490
That is the next one.

31
00:02:11,850 --> 00:02:15,840
So this will be index one and element eight.

32
00:02:16,530 --> 00:02:21,470
Then on the right hand side, this list, this is the middle one, index five.

33
00:02:21,750 --> 00:02:28,290
So here right hand side is index of five and the element is 21 for this eight.

34
00:02:28,290 --> 00:02:33,400
Left hand side is zero and 11 for the right hand side is index to element this one.

35
00:02:34,200 --> 00:02:37,320
So if I remove this, you can see that this is on the left hand side.

36
00:02:37,650 --> 00:02:39,240
So this is on the left hand side.

37
00:02:39,240 --> 00:02:40,370
This is on the right hand side.

38
00:02:40,560 --> 00:02:44,060
So for this one, this index one, let's say to zero, right side is two.

39
00:02:44,340 --> 00:02:49,080
So this will be zero and this will be two.

40
00:02:49,320 --> 00:02:54,330
And the element here is for an element here is ten now this side.

41
00:02:56,390 --> 00:03:02,910
Left-Hand side is for an element, is it in the right hand side, a six element is straight forward.

42
00:03:03,200 --> 00:03:05,720
So this is 18 and this is going to.

43
00:03:07,250 --> 00:03:09,480
Similarly and prepare for the right hand side also.

44
00:03:09,860 --> 00:03:14,830
So for right hand side list leaving this middle, this is the right hand side and this is done with

45
00:03:14,840 --> 00:03:18,080
one Sullivan then for these three elements.

46
00:03:18,110 --> 00:03:19,040
There's no one.

47
00:03:19,310 --> 00:03:27,340
So left hand side is index nine and it's left this index eight and it's right this index then.

48
00:03:28,010 --> 00:03:33,330
So the elements are thirty three, twenty nine and thirty four.

49
00:03:34,130 --> 00:03:39,830
And on his right hand side, these are the three elements, middle one is 13, so this is 13.

50
00:03:40,550 --> 00:03:48,080
And on its left hand side is 12, right hand side is 14 and the elements are thirty nine.

51
00:03:48,110 --> 00:03:49,370
And this is forty one.

52
00:03:49,820 --> 00:03:51,230
And this is forty three.

53
00:03:53,010 --> 00:03:53,970
So this is the.

54
00:03:55,400 --> 00:04:03,130
This is outracing tree we got based on the working of binary search, so we will discuss from this tree.

55
00:04:04,190 --> 00:04:12,350
Now, if you observe twenty one seven can be found just in one competition, then 15 and 37 can be found.

56
00:04:12,350 --> 00:04:18,890
And two competitions means if the element is 15, we go on the left hand side of the elements, 37 on

57
00:04:18,890 --> 00:04:19,640
the right hand side.

58
00:04:21,660 --> 00:04:29,100
These elements, which are at level three, these will be found in three competitions and these elements

59
00:04:29,100 --> 00:04:31,860
will be found in four competitions.

60
00:04:33,070 --> 00:04:40,300
So maximum how many competitions are required for competitions are required for searching, not more

61
00:04:40,300 --> 00:04:45,100
than four, whether the limit is in the beginning of a list or at the end part of a list, wherever

62
00:04:45,100 --> 00:04:49,140
that limit is, maximum competition required for searching for.

63
00:04:49,150 --> 00:04:54,630
So from the three, you can clearly see that at most for competition to reach any element.

64
00:04:55,660 --> 00:04:59,320
So the number of competition depends on height of a tree.

65
00:04:59,770 --> 00:05:03,700
This is a tracing tree, so the height of the stretching tree is log.

66
00:05:03,700 --> 00:05:09,910
And so the time taken will be dependent on the number of competitions and the number of competitions

67
00:05:09,910 --> 00:05:11,830
are at the most to log.

68
00:05:11,830 --> 00:05:20,050
And so the time taken for searching in binary searches, log and so minimum time, as if you are searching

69
00:05:20,050 --> 00:05:22,140
for an element which is present in the middle.

70
00:05:22,390 --> 00:05:24,650
So the minimum time is outdraw one.

71
00:05:24,670 --> 00:05:26,460
So you can see this is the best case.

72
00:05:26,950 --> 00:05:29,230
And what is the maximum time taken?

73
00:05:29,440 --> 00:05:34,810
If the element is present here, according to this tree, if they are present in the leaf, are those

74
00:05:34,810 --> 00:05:36,160
elements which are reachable?

75
00:05:36,160 --> 00:05:38,110
They are found at the forward comparison.

76
00:05:38,110 --> 00:05:41,660
So the maximum time is outdraw log in.

77
00:05:41,980 --> 00:05:43,320
This is the best case time.

78
00:05:46,210 --> 00:05:47,750
This is the worst case time.

79
00:05:48,730 --> 00:05:53,020
So Becka's time of binary search is order of one and the worst case.

80
00:05:53,020 --> 00:05:54,730
Time of binary searches.

81
00:05:54,970 --> 00:05:55,600
Log in.

82
00:05:56,560 --> 00:05:58,510
So this was the successful search.

83
00:05:58,810 --> 00:06:00,610
What about unsuccessful search?

84
00:06:00,640 --> 00:06:05,360
Let us see if I'm searching for any element that is smaller than four.

85
00:06:06,010 --> 00:06:07,020
Then what happens?

86
00:06:07,510 --> 00:06:09,850
I'll take care for the smaller than this.

87
00:06:09,880 --> 00:06:11,140
Four is smaller than this.

88
00:06:11,140 --> 00:06:12,250
For the smaller than this.

89
00:06:12,250 --> 00:06:13,840
For the smaller than this also.

90
00:06:14,140 --> 00:06:16,690
So it will end up on the left hand side.

91
00:06:18,070 --> 00:06:25,090
If I'm searching for any element which is present in between four and eight, then what happens?

92
00:06:25,100 --> 00:06:25,660
Let us see.

93
00:06:25,990 --> 00:06:26,990
Four and eight.

94
00:06:27,010 --> 00:06:30,000
Let us take one element seven and see what happens.

95
00:06:30,430 --> 00:06:32,020
Seven is smaller than this.

96
00:06:32,020 --> 00:06:34,300
Seven and smaller than the seven is smaller than this.

97
00:06:34,630 --> 00:06:35,850
Seven is greater than that.

98
00:06:35,860 --> 00:06:37,080
So it will end up here.

99
00:06:37,480 --> 00:06:44,980
So it means this and the one which is a squared and all that I am showing are representing unsuccessful

100
00:06:44,980 --> 00:06:45,880
searches.

101
00:06:46,450 --> 00:06:50,950
So this node is representing all those elements which are smaller than four, which is not there in

102
00:06:50,950 --> 00:06:51,370
the list.

103
00:06:51,550 --> 00:06:55,090
And this represent all the elements that are in between four and eight.

104
00:06:55,270 --> 00:07:01,920
So those are five, six and seven, these three elements that is representing.

105
00:07:02,410 --> 00:07:09,570
Similarly, we can represent the nodes here for all those elements which are not there in the list.

106
00:07:10,090 --> 00:07:16,480
So if you are searching for any element, then we say that the search will terminate at these nodes.

107
00:07:16,690 --> 00:07:18,370
So far, unsuccessful search.

108
00:07:18,700 --> 00:07:22,270
This shows that it reaches the end of a tree always.

109
00:07:22,420 --> 00:07:26,680
So the time taken in unsuccessful search is always under of logic.

110
00:07:27,070 --> 00:07:30,400
So successful search minimum is one maximum.

111
00:07:30,520 --> 00:07:34,690
Logging for unsuccessful search is always order of log.

112
00:07:35,210 --> 00:07:41,580
Next, we have also seen a recursive procedure so that recursive procedure also behaves in the same

113
00:07:41,590 --> 00:07:41,820
way.

114
00:07:42,530 --> 00:07:43,920
First, it will go in the middle.

115
00:07:44,170 --> 00:07:48,460
If there is a small, then it will search on the left hand side that is in the left subtree.

116
00:07:48,700 --> 00:07:50,500
If it is greater, it will search in the right.

117
00:07:50,680 --> 00:07:56,040
But then again, it will call itself recursively on the left sublist, the right sublist.

118
00:07:56,170 --> 00:08:02,080
So the maximum number of comparisons it may be making, the maximum calls it will be making will be

119
00:08:02,080 --> 00:08:02,920
at most for.

120
00:08:03,850 --> 00:08:06,820
And if anyone is not found, then it will be a fifth column.

121
00:08:06,940 --> 00:08:10,570
It will stop at fifth column if the element is not found.

122
00:08:12,040 --> 00:08:17,170
So the number of calls are recursive algorithm will make depends on the height of a tree, so that is

123
00:08:17,170 --> 00:08:17,590
logging.

124
00:08:17,830 --> 00:08:22,810
So whether you write it with the procedure or recursive procedure, the time this log in.

125
00:08:24,300 --> 00:08:30,570
So how binary search is logging, I have shown, you know, to show you using some other method how

126
00:08:30,570 --> 00:08:35,520
binary searches login, see if you have total 15 elements here.

127
00:08:35,669 --> 00:08:40,350
Zero to 14, 15, 15 is not in powers of two.

128
00:08:40,380 --> 00:08:43,740
So I will just make it round as 16.

129
00:08:44,390 --> 00:08:52,620
OK, to explain you how this login I'll take it, the powers of two that is 16 assume that the number

130
00:08:52,620 --> 00:08:54,050
of elements are 16.

131
00:08:55,560 --> 00:08:57,200
Number of elements are 16.

132
00:08:57,510 --> 00:09:00,550
So how many researchers working 16.

133
00:09:00,570 --> 00:09:01,910
It has divided by two.

134
00:09:02,370 --> 00:09:05,190
So eight elements of the site, eight elements of that site.

135
00:09:05,850 --> 00:09:06,260
This fun.

136
00:09:06,270 --> 00:09:10,830
So added elements we are getting actually here, we're getting five, seven, seven elements.

137
00:09:11,130 --> 00:09:13,070
But as we have taken it has 16.

138
00:09:13,290 --> 00:09:15,090
So eight on the site, eight on this site.

139
00:09:15,600 --> 00:09:20,640
If you are searching on the left side, also on the right hand side, again, divided by two.

140
00:09:22,590 --> 00:09:24,130
Then again, divided by two.

141
00:09:24,210 --> 00:09:30,520
So how long this can get divided until you are left with just one number, if there is only one number.

142
00:09:30,540 --> 00:09:33,470
Beyond that, you cannot proceed because there are no numbers.

143
00:09:33,750 --> 00:09:36,090
So last one, it should stop that one.

144
00:09:36,450 --> 00:09:39,640
So 16 divided by two, divided by two and divided by two.

145
00:09:39,780 --> 00:09:42,190
So how many times I should divide 16 by two.

146
00:09:42,220 --> 00:09:43,590
Such that would be just one.

147
00:09:43,890 --> 00:09:48,800
So I should divide 16 by two over four so that it was just one.

148
00:09:49,950 --> 00:09:50,740
So that's all.

149
00:09:50,910 --> 00:09:57,900
So it means this list will be divided four times to reach at last one single elements.

150
00:09:58,500 --> 00:09:58,940
See this?

151
00:09:59,040 --> 00:10:00,510
A total 16 elements.

152
00:10:00,530 --> 00:10:03,900
Assume this half, then this half, then this half.

153
00:10:03,910 --> 00:10:09,300
Then we got just one element, either all sorts of stuff here or at last it will stop here.

154
00:10:11,220 --> 00:10:19,710
So this is for the answer we got it test for, so how do you calculate that 16 how many times it should

155
00:10:19,710 --> 00:10:21,690
be divided by two to get the answer?

156
00:10:21,690 --> 00:10:21,990
One.

157
00:10:22,290 --> 00:10:26,080
It's nothing but sixteen sixty two point four.

158
00:10:26,370 --> 00:10:27,150
So for.

159
00:10:30,980 --> 00:10:38,290
There's nothing but two powerful is equal to 16 and for as long, 16 beats two.

160
00:10:38,660 --> 00:10:45,530
So actually the of power as log in words of power as log.

161
00:10:45,980 --> 00:10:47,640
So to power for power.

162
00:10:47,640 --> 00:10:51,270
A 16, then how much is for this log.

163
00:10:51,290 --> 00:10:53,840
Sixteen base to so log.

164
00:10:53,840 --> 00:10:54,840
And so.

165
00:10:55,050 --> 00:10:59,180
So this means the time taken is log and base two.

166
00:10:59,390 --> 00:11:05,950
So when I have made it and powers of two so I can explain you hear otherwise 15 minutes 15 by seven.

167
00:11:05,960 --> 00:11:08,470
So we will not get exactly the powers of two.

168
00:11:08,780 --> 00:11:10,840
So I have taken an example at 16.

169
00:11:11,690 --> 00:11:14,460
So from 16 we can observe that log in base two.

170
00:11:14,900 --> 00:11:22,820
So here we can say that whatever the number of elements you have N n plus one, the log base to exact

171
00:11:22,820 --> 00:11:23,090
opposite.

172
00:11:23,130 --> 00:11:28,520
We got this one 15 elements rather than in that you add one 16 to log in base two.

173
00:11:28,700 --> 00:11:36,800
So exact formula is a log in base to an exact formalized log and plus one base to support roughly we

174
00:11:36,800 --> 00:11:37,790
take it as a log.

175
00:11:37,790 --> 00:11:44,000
And so because the polynomial degrees log in, so we take it as logging, then one more thing.

176
00:11:44,840 --> 00:11:51,680
If there are more than 15 elements, then definitely the number of competition will increase by one

177
00:11:52,130 --> 00:11:56,450
because that is the maximum number for which we will require for competitions.

178
00:11:56,600 --> 00:12:03,260
Otherwise, if the number is greater than it will be five competitions so that I can show you here,

179
00:12:03,500 --> 00:12:10,900
that is, if you have 16 elements, then 16 plus one Log Vista, it's nothing but log 17, base two.

180
00:12:11,090 --> 00:12:13,160
So this will be four points something.

181
00:12:13,460 --> 00:12:16,600
So four point two, then they should be taken at five.

182
00:12:16,880 --> 00:12:19,250
So this value should be taken as sealed.

183
00:12:20,240 --> 00:12:27,260
Log base two and plus one, should we take a seat so I have shown you from the tracing tree and also

184
00:12:27,260 --> 00:12:31,070
I have given you the idea, just the idea there's not a method or an approach.

185
00:12:31,070 --> 00:12:32,750
Just I have given you the idea.

186
00:12:32,900 --> 00:12:39,290
How does Logan because every time getting divided by two, so when something is successive division,

187
00:12:39,290 --> 00:12:46,330
it's nothing but log successive multiplication is power that's solvable by research and analysis.

