1
00:00:00,850 --> 00:00:08,560
In this video we will talk about representation of our spots matrix and also we will see how to display

2
00:00:08,670 --> 00:00:12,990
our spots matrix from that representation.

3
00:00:13,390 --> 00:00:16,900
This topic we have already studied in arrays.

4
00:00:16,930 --> 00:00:19,920
Now again we are learning it and linguist.

5
00:00:20,100 --> 00:00:27,640
I have an example of a sparse matrix of size five cross six that is five rather than six columns lot

6
00:00:27,640 --> 00:00:29,000
of elements are zeros.

7
00:00:29,050 --> 00:00:38,590
Only a few elements are known zeros 8 7 5 9 3 and 6 and 4 just 7 elements are non-zero.

8
00:00:38,590 --> 00:00:41,800
Now how do we prevent this so far representing a sparse matrix.

9
00:00:41,800 --> 00:00:43,970
We want to avoid storing zeros.

10
00:00:43,990 --> 00:00:46,480
We want to store only non-zero elements.

11
00:00:46,480 --> 00:00:52,190
For example if I take this element there's a non-zero so 40 presenting it we should know what is that

12
00:00:52,190 --> 00:00:52,720
element.

13
00:00:52,720 --> 00:00:55,770
And also we should know it is in the Mattox.

14
00:00:55,840 --> 00:01:02,740
So for that I should know the rule number and column number so zero coming forward is eight and this

15
00:01:02,950 --> 00:01:05,760
seven is one commentary fossil.

16
00:01:05,830 --> 00:01:07,910
Third Column values 7.

17
00:01:08,020 --> 00:01:12,430
So for every nonzero tone we should know or number in column number.

18
00:01:12,820 --> 00:01:14,620
And then the nonzero thumb.

19
00:01:14,890 --> 00:01:20,830
So we have to represent this one such that we should be able to regenerate the same spots metrics.

20
00:01:20,920 --> 00:01:26,860
So let us see how we can represent this one by just taking non-zero elements.

21
00:01:26,890 --> 00:01:31,240
So I will draw the structure and show you for representation.

22
00:01:31,240 --> 00:01:39,040
I have taken an early idea of size 5 these arrays represent [removed] that are 5 rows so the indices in

23
00:01:39,040 --> 00:01:45,380
an array represents row number 4 representing non-zero element like example 8.

24
00:01:45,400 --> 00:01:48,160
I need to know column number and the element itself.

25
00:01:48,250 --> 00:01:53,830
So column number is four and that element is eight so that I was represented as a node

26
00:01:57,440 --> 00:02:08,320
fourth column element is eight and this is not so there's a node containing column number and a non-zero

27
00:02:08,380 --> 00:02:09,550
element.

28
00:02:09,970 --> 00:02:14,320
Then next 0 0 1 there is 1 non-zero element at column 3.

29
00:02:14,650 --> 00:02:20,540
So here we have a non-zero element out column 3 so column 3 element is 7.

30
00:02:20,620 --> 00:02:31,930
And this is now then in a row 2 we have two non-zero elements and index 0 and 4 0 the values 5 and next

31
00:02:33,860 --> 00:02:44,380
add column 4 values 9 the next row we have an element at column finds that in this rule we have 2 non-zero

32
00:02:44,380 --> 00:02:46,840
elements that column 0 in column 3

33
00:02:50,560 --> 00:02:51,670
for this the structure.

34
00:02:51,940 --> 00:02:53,560
So this is a linked list.

35
00:02:53,690 --> 00:02:55,800
Is another linked list does under the link list.

36
00:02:55,930 --> 00:02:57,170
So all these are limitless.

37
00:02:57,190 --> 00:03:01,210
So it's an array of linked lists and the name of this array.

38
00:03:01,210 --> 00:03:08,420
Suppose it is a then this is also a you can call it as a as an array of linked list.

39
00:03:08,440 --> 00:03:13,430
Now how we can form this a structure this is structure is having collection of linguists or linguists

40
00:03:13,510 --> 00:03:14,730
as a collection of naught.

41
00:03:14,740 --> 00:03:19,620
So the law structure this contains what each node contains column number a non-zero element.

42
00:03:19,870 --> 00:03:27,310
So if I define a node structure node structure contains three things column number and the value and

43
00:03:27,400 --> 00:03:28,630
next pointer.

44
00:03:29,080 --> 00:03:35,550
So using C language if I define that structure this is structure Maude will contain three elements.

45
00:03:35,560 --> 00:03:42,050
One is column number and value and an exploiter

46
00:03:47,010 --> 00:03:51,900
does nor does a structure know how to create the structure for creating this structure.

47
00:03:51,900 --> 00:04:00,390
I need an army of number of rules of size if this is M grows and then I should have an array of size

48
00:04:00,540 --> 00:04:01,030
m.

49
00:04:01,260 --> 00:04:06,910
So let me create this area of size m so array is of type.

50
00:04:07,030 --> 00:04:11,310
Node pointers e office size M.

51
00:04:13,410 --> 00:04:17,459
So this is an ideal for pointers of size m whatever the sizes.

52
00:04:17,579 --> 00:04:27,720
So this will be an array then at each location let the C E 0 4 0 assign this new lord new norm.

53
00:04:28,920 --> 00:04:31,200
So I'm just assigning just one node like this.

54
00:04:31,230 --> 00:04:35,810
I have to create all these linguists so you can write a program.

55
00:04:35,820 --> 00:04:39,720
You can write a procedure for creating this type of structure.

56
00:04:39,720 --> 00:04:46,470
I have just given the idea here how we have to create this one create an idea of pointers then at each

57
00:04:46,470 --> 00:04:51,810
location create a linked list of non-zero elements so you can write a program that is taking input from

58
00:04:51,810 --> 00:04:56,070
the keyboard and asking what are the non-zero elements from keyboard you can take.

59
00:04:56,130 --> 00:05:01,260
Number column number and the non-zero element so for a given the wrong number in that particular role

60
00:05:01,320 --> 00:05:07,620
that is in that particular index you can create an order having column number and a non-zero element

61
00:05:08,280 --> 00:05:17,460
and you can form a linguist so this is a student exercise for creating a sports matrix adding to spice

62
00:05:17,470 --> 00:05:20,280
mitosis or displaying spice markets.

63
00:05:20,290 --> 00:05:21,850
These are the things that you have to do.

64
00:05:21,850 --> 00:05:22,900
So I'll just list out.

65
00:05:25,060 --> 00:05:26,740
So these are the three things you have to do.

66
00:05:26,770 --> 00:05:28,300
Create spice mitosis.

67
00:05:28,300 --> 00:05:33,940
Then you should be able to create more than one then display how to display the spice matrix and how

68
00:05:33,940 --> 00:05:38,350
to add two spice mitosis out of this.

69
00:05:38,350 --> 00:05:41,350
I will just share the idea how to display this fun.

70
00:05:41,360 --> 00:05:45,620
So I will write on the piece of code for displaying this sparks.

71
00:05:45,630 --> 00:05:52,590
Max let us see how to display so far displaying I should display all these rules.

72
00:05:52,750 --> 00:06:00,040
So for all these rules for I assign zero I is less than M M rules out there.

73
00:06:00,980 --> 00:06:04,380
I love this.

74
00:06:04,710 --> 00:06:13,500
This loop will cover all the rows by each at all what I should do I have to scan through a linguist.

75
00:06:13,500 --> 00:06:20,600
Let us take this example it is having more than one more so scan for this fun so take a pointer at the

76
00:06:20,600 --> 00:06:21,120
beginning.

77
00:06:21,140 --> 00:06:22,790
So I need a pointer.

78
00:06:23,070 --> 00:06:31,320
So let us take a plane to be let us take a pointer B for example I'm treating it here for this linguist

79
00:06:31,380 --> 00:06:34,000
right pointing here so that I can get it from me.

80
00:06:34,000 --> 00:06:34,380
Fine.

81
00:06:34,770 --> 00:06:42,250
So I you are finding some piece pointing in let me show it here.

82
00:06:42,440 --> 00:06:43,370
This is on site.

83
00:06:43,400 --> 00:06:45,300
So we have to do it for all.

84
00:06:45,560 --> 00:06:51,020
But here you can see it fly I'm showing it here is so far displaying all the elements in a column I

85
00:06:51,020 --> 00:06:52,900
should repeated four times.

86
00:06:52,910 --> 00:06:55,040
So I want to display zeros also.

87
00:06:55,040 --> 00:07:01,670
So for that take one more look for G assign Zero G is less than N N G plus plus.

88
00:07:01,670 --> 00:07:05,470
So this will repeat four total times.

89
00:07:05,480 --> 00:07:07,640
That is equal to number of columns.

90
00:07:07,640 --> 00:07:12,740
Then at each column I should see that if that is non-zero or zero element.

91
00:07:13,340 --> 00:07:17,550
So if it is non-zero I should print element if it is not I should print Ziegel.

92
00:07:17,600 --> 00:07:19,910
So how you can know is it non-zero or not.

93
00:07:19,910 --> 00:07:23,010
So this piece pointing up on some note that will show me.

94
00:07:23,060 --> 00:07:24,290
So that column number.

95
00:07:24,290 --> 00:07:32,090
So here what I should do is if J is matching with the column number so that is B's column B is a column

96
00:07:32,120 --> 00:07:38,350
this is column if it is matching with the peace column then printed their data on here non-zero element

97
00:07:40,170 --> 00:07:49,010
suspend the value non-zero value and also move B to next move moved so printing the value and moving

98
00:07:49,040 --> 00:07:49,980
P2 next snored.

99
00:07:50,030 --> 00:07:57,530
This I have to do if this is a true if the column number is matching if it is not matching else I should

100
00:07:57,530 --> 00:08:05,970
print zero as frenzied all that's on this is simple fiber trace this one little bit.

101
00:08:06,030 --> 00:08:07,190
Just watch it.

102
00:08:07,190 --> 00:08:08,480
I is starting from zero.

103
00:08:08,520 --> 00:08:12,140
So I is here then be assigning or fine.

104
00:08:12,150 --> 00:08:21,160
So people pointing here first one then forward here saying zero go and 0 2 and then 0 5 it will go J

105
00:08:21,160 --> 00:08:24,400
will repeat from 0 to less than that then so zero less than six.

106
00:08:24,400 --> 00:08:25,140
That is five.

107
00:08:25,240 --> 00:08:30,620
So it will go up to five then what it is doing first time J zero yes G zero.

108
00:08:31,360 --> 00:08:33,370
So is it matching with the peace column.

109
00:08:33,400 --> 00:08:34,760
Is it matching with peace column.

110
00:08:34,760 --> 00:08:44,080
No it is for so print 0 0 is printed the next the jobless plus a J becomes 1 then check if JS equals

111
00:08:44,080 --> 00:08:51,040
the peace column J is one is it equal to peace column no print zero then J becomes two Gibler surplus

112
00:08:51,220 --> 00:08:57,520
then J is equal to peace column these columns for up to 40 to log in matching so it's not matching then

113
00:08:57,520 --> 00:09:07,570
print 0 then J becomes 3 print 0 j becomes for its matching now print date and moved to next more so

114
00:09:07,580 --> 00:09:17,050
people move to next note and b became known B became known then next this becomes 5 is it matching no

115
00:09:17,080 --> 00:09:23,610
print 0 so that so it will print all these things that so it can be displayed.

116
00:09:24,620 --> 00:09:30,750
So I have just given the idea how to display this a sparse matrix complete Matrix 0 0 0 8 0 all the

117
00:09:30,750 --> 00:09:33,660
values of printed zeros as well as on tables.

118
00:09:33,870 --> 00:09:40,140
So that sort of thing is an exercise for you write a complete program and also write a program for adding

119
00:09:40,140 --> 00:09:41,760
two spots mitosis.

